# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.10.4_20241224
- **舊版本**: release_5.8.10.4
- **生成時間**: 2025-07-18 10:39:47
- **新增 Commit 數量**: 6

## 變更摘要

### kmin (1 commits)

- **2024-12-18 14:50:52**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]
  - 變更檔案: 1 個

### 張詠威 (1 commits)

- **2024-10-17 14:03:53**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
  - 變更檔案: 1 個

### 周权 (4 commits)

- **2024-12-20 14:11:20**: [資安]Q00-20241217004 調整個人訊息頁欄位安全性問題
  - 變更檔案: 2 個
- **2024-12-19 10:43:41**: [資安]Q00-20241217003 調整查詢欄位可以輸入查詢條件支持查詢的問題，防止SQL注入
  - 變更檔案: 1 個
- **2024-12-19 10:23:42**: [資安]Q00-20241217002 调整登入错误讯息[補]
  - 變更檔案: 3 個
- **2024-12-18 16:10:44**: [資安]Q00-20241217002 调整登入错误讯息
  - 變更檔案: 4 個

## 詳細變更記錄

### 1. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]
- **Commit ID**: `9ada8bfcd645ad277e6b269ba063e136c95fa4a1`
- **作者**: kmin
- **日期**: 2024-12-18 14:50:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 2. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
- **Commit ID**: `99f505c932f7a7aaad751b5288746d64d8c914cf`
- **作者**: 張詠威
- **日期**: 2024-10-17 14:03:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 3. [資安]Q00-20241217004 調整個人訊息頁欄位安全性問題
- **Commit ID**: `41af72642d65968e8b55b08d83b5141104e0622c`
- **作者**: 周权
- **日期**: 2024-12-20 14:11:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 4. [資安]Q00-20241217003 調整查詢欄位可以輸入查詢條件支持查詢的問題，防止SQL注入
- **Commit ID**: `9287c65390def0fc662156d80d63c15409f86a92`
- **作者**: 周权
- **日期**: 2024-12-19 10:43:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 5. [資安]Q00-20241217002 调整登入错误讯息[補]
- **Commit ID**: `b936fce4c7cf078b8d73edb70008982606709af1`
- **作者**: 周权
- **日期**: 2024-12-19 10:23:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 6. [資安]Q00-20241217002 调整登入错误讯息
- **Commit ID**: `bf1ec218ad7e5cdfa1e1d216b5b9235afc764ce1`
- **作者**: 周权
- **日期**: 2024-12-18 16:10:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

