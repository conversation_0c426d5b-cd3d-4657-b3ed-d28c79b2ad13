{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.6.5.3_1", "date": "tag 5.6.5.3_1\nTagger: 施翔耀 <jose<PERSON><PERSON><PERSON>@digiwin.biz>\n\n2018/04/17 14:30 last build2018-04-17 10:42:57", "message": "修正鼎捷移動session already invalided 問題", "author": "ChinRong"}, "舊分支": {"branch_name": "5.6.5.3", "date": "tag 5.6.5.3\nTagger: 張容倫 <<EMAIL>>\n\n2018-03-14 18:54:07", "message": "修正formBuilder開表單與發表單錯誤問題", "author": "pinchi_lin"}, "比較時間": "2025-07-28 18:12:06", "新增commit數量": 33, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "1d0663b1ed460bb27b69d632d24c40dc7703ceb1", "commit_訊息": "修正鼎捷移動session already invalided 問題", "提交日期": "2018-04-17 10:42:57", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6b322e4eebf2471cd5310bbfec242741d0579554", "commit_訊息": "修正form-Builder中，產生BPMAPP表單元件缺少Password類型", "提交日期": "2018-04-17 09:14:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b985b83950c2916f95dee48b9b3f6d974d035e6d", "commit_訊息": "A00-20180409001 修正客製開窗在字數過多時會產生重疊問題", "提交日期": "2018-04-16 17:25:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "103b376309ff0d09bce60f7fef4d719af204b85c", "commit_訊息": "[A00-20180410003]修正流程簽核掛外部連結畫面,使用Chrome瀏覽器無法正常派送流程的問題。", "提交日期": "2018-04-16 17:15:22", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a1094f9c95ddbfcecfa25447bcc99d27124d264", "commit_訊息": "A00-20180410001 修正自帶小數點保留功能時,表單沒有顯示問題", "提交日期": "2018-04-13 18:11:57", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "807326b09ed19391e305f317d2e1f550ff4bcada", "commit_訊息": "[A00-20180410003]此議題先還原 ->修正外部連結畫面使用Chrome瀏覽器無法正常派送流程的問題。", "提交日期": "2018-04-13 16:22:46", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2eb6a080cdcb6cfc5ad50820d8cdd11680a041af", "commit_訊息": "[A00-20180410003]修正外部連結畫面使用Chrome瀏覽器無法正常派送流程的問題。", "提交日期": "2018-04-13 10:03:25", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6afec66cc4098dcd860cd7bae4de67fce5d9960", "commit_訊息": "A00-20180412009 協助commit 修正在退回重辦需要逐級通知的前提下當流程關卡包含核決關卡", "提交日期": "2018-04-12 18:15:17", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "15831eda74ae21e65ef5a8964df9bbc9e0461663", "commit_訊息": "修正升版後統計元件的顏色還是彩色的問題", "提交日期": "2018-04-11 15:59:45", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0f3f012a94a7f4c2cc237c0c3731f97e302dd1aa", "commit_訊息": "修正將流程設定為不支援行動版，在行動版的常用發起仍然可以看到該流程問題", "提交日期": "2018-04-11 14:15:36", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "10f09fe003bf775d5cd7599e1c31c74edfbd2f81", "commit_訊息": "修正表單畫面多語系問題", "提交日期": "2018-04-11 11:07:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "d296bd5cfc9fdf96fdc871f4e0dfc3d26e59791a", "commit_訊息": "C01-*********** 微信帳號的搜索功能失效", "提交日期": "2018-04-10 17:22:31", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "14b6e477459223f532b6ad2e8ddf5335b0d54b8a", "commit_訊息": "取消開窗限制500筆資料的限制(因為會導致客戶原有的script異常)-V57統一調整", "提交日期": "2018-04-10 16:26:35", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f509a864305aa14a390d6319c30f0efa1c2ec8ce", "commit_訊息": "Q00-20180410001 修正檔案總管新增文件異常 及 ISO檔案下載失敗", "提交日期": "2018-04-10 16:19:09", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOInv001.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "88b47c2e572544a760eb184fc216e32d3aca3b05", "commit_訊息": "C01-20180409002 修正BPMAPP向後加簽多語系錯誤", "提交日期": "2018-04-10 15:03:58", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5653.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2f573b59ccd0cbb481acc1bc9c053f8bf41902fa", "commit_訊息": "Q00-20180330003 修正測試模擬使用者開窗異常議題", "提交日期": "2018-03-31 15:18:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cd80f13831f4a482117f12e33efcfda3309c36df", "commit_訊息": "C01-20180320001 修正app發起流程畫面最後一行顯示一半", "提交日期": "2018-03-31 13:46:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2d5d1f16b0fb715717d3dc9e76ad96177701e147", "commit_訊息": "調整入口平台設定頁面jsp，增加class類別", "提交日期": "2018-03-31 11:40:11", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeploy.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "47b81e2b5cd7ac97629e6c11924d09e44f023d38", "commit_訊息": "Q00-*********** 修正入口平台頁面鼎捷使用者與微信使用者頁面取得的資料異常。", "提交日期": "2018-03-31 10:23:38", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "32ec7b46761bb03b6bb0393cdebe4bfaf279e99e", "commit_訊息": "A00-20180329002 新增行動版Grid reload方法", "提交日期": "2018-03-30 14:32:20", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34076ed722d3627336af6900331b8f2147e35a0e", "commit_訊息": "A00-20180320001 修正TT流程再中間層簽核時會派送失敗問題", "提交日期": "2018-03-29 14:55:31", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6034dacf4e693f646d285f9920bbdbea997278db", "commit_訊息": "廷緯 修正進階查詢及表單設計師Time開窗偏移問題。", "提交日期": "2018-03-29 08:39:45", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/popup.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f25617312591475d4570a6619d2ef440d0bff4b1", "commit_訊息": "修正表單中間層錯誤", "提交日期": "2018-03-28 18:18:48", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c8c71e35e9e88b2d14c3a3d80e1f99e72651b2e6", "commit_訊息": "修正表單中間層在表單有多欄位時只會截取右邊的欄位的問題", "提交日期": "2018-03-28 15:24:10", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc06cb9104f63e903364a8da850b3dfbf8d8b430", "commit_訊息": "[A00-***********]修正組織設計師部門主管無法正常顯示的問題。", "提交日期": "2018-03-28 09:32:28", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/AddUserToUnitDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/table/UserTableController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "524c0ccb7fdb824f5fe52226de3ea4019dfff29e", "commit_訊息": "修正APP議題", "提交日期": "2018-03-26 10:32:37", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "be736049b01b0600c6760fd095b0391f75f949ba", "commit_訊息": "修正進階查詢搜尋流程發起人失效問題(程式錯誤修正)", "提交日期": "2018-03-26 09:42:28", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "68f868fced7850091ace7ffece099cdd8e81dae7", "commit_訊息": "2018/03/23 廷緯 修改進階查詢開窗會偏移問題。", "提交日期": "2018-03-23 16:22:16", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/popup.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b28b898630d7699441311e7253e49cb6378df275", "commit_訊息": "修正鼎捷移動多組織簽核失敗問題", "提交日期": "2018-03-23 14:20:33", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c057c1e423ffe48beca660b80fb2da46198156f9", "commit_訊息": "補上鼎捷移動電話簿SQL語法", "提交日期": "2018-03-23 12:40:18", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "67fd38dc5081fcff86f82bb3865e7df9f7814856", "commit_訊息": "修正絕對位置grid表單元件亂跑問題", "提交日期": "2018-03-23 10:20:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7afe3a3217486dcbe675b9c6d217f3932624e733", "commit_訊息": "修正議題 1.入口平台微信無法編輯 2.行動表單開啟異常 3.加簽畫面跑版 4.絕對位置表單日期時間元件樣式變一條線 5.行動表單閒置15分鐘不會登出 6.微信推播網址錯誤 7.sql語法預設資料庫代號統一為BPMSQL", "提交日期": "2018-03-22 23:43:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 17}, {"commit_hash": "d6eb44619cdfabd50e1baf2417e45b095faa0564", "commit_訊息": "20180322 廷緯 調整(模糊查詢)追蹤流程頁面的進階查詢流程發起人無法正確搜尋的問題。", "提交日期": "2018-03-22 16:35:38", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}]}