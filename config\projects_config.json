{"projects": {"BPM": {"name": "BPM 主專案", "repo_path": "D:\\IDEA_workspace\\BPM", "branch_patterns": {"include_starts_with": ["release_", "hotfix_"], "include_ends_with": [], "exclude_branches": ["develop_v58"]}, "description": "BPM 主要專案"}, "BPM-ISO": {"name": "BPM ISO 專案", "repo_path": "D:\\IDEA_workspace\\BPM-ISO", "branch_patterns": {"include_starts_with": ["release_", "hotfix_"], "include_ends_with": [], "exclude_branches": ["develop_v58"]}, "description": "BPM ISO 相關專案"}, "NaNaXWeb": {"name": "NaNaX Web 專案", "repo_path": "D:\\IDEA_workspace\\NaNaXWeb", "branch_patterns": {"include_starts_with": ["release_"], "include_ends_with": ["_hotfix"], "exclude_branches": ["develop", "main", "master"]}, "description": "NaNaX Web 應用專案 - 包含 release_ 開頭和 _hotfix 結尾的分支"}}, "settings": {"output_prefix": "smart_diff", "date_format": "%Y%m%d_%H%M%S"}, "deployment": {"package_name": "bpm_easy_tools_deployment", "exclude_patterns": ["venv/", "venv\\", "__pycache__/", "__pycache__\\", "*.pyc", "*.pyo", "*.pyd", ".git/", ".git\\", ".giti<PERSON>re", ".pytest_cache/", ".pytest_cache\\", "test_all_functions.bat", "show_ip.bat", "run_branch_diff.bat", "run_deployment_index.bat", "*.log", "*.tmp", ".DS_Store", "Thumbs.db"], "include_files": ["streamlit_home.py", "requirements.txt", "setup_environment.cmd", "start_application.cmd", "deploy_to_target.py", "README.md"], "include_directories": ["pages/", "tools/", "config/", "data_output/", ".streamlit/"]}}