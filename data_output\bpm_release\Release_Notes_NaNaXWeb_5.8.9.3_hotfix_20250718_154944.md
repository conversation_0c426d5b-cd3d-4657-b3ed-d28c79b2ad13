# Release Notes - NaNaXWeb

## 版本資訊
- **新版本**: 5.8.9.3_hotfix
- **舊版本**: 5.8.9.3
- **生成時間**: 2025-07-18 15:49:44
- **新增 Commit 數量**: 46

## 變更摘要

### yamiyeh10 (7 commits)

- **2024-04-17 09:33:23**: [PRODT]Q00-20240417001 修正Web流程管理工具中流程定義進版後不會更新作者資訊問題
  - 變更檔案: 2 個
- **2024-09-26 08:21:33**: [PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況
  - 變更檔案: 1 個
- **2024-06-20 17:22:29**: [PRODT]C01-20240619001 修正Web流程管理工具中服務關卡的參數功能切換到第二頁後存在實際參數卻出現未指定參數的訊息
  - 變更檔案: 2 個
- **2023-11-07 09:10:01**: [DT]Q00-20231107001 修正發起權限設定屬性在新增使用者後變成解析錯誤問題
  - 變更檔案: 1 個
- **2023-11-07 09:07:51**: Revert "[DT]Q00-20231106003 修正發起權限設定屬性在新增使用者後變成解析錯誤問題"
  - 變更檔案: 1 個
- **2023-11-07 09:07:10**: [DT]Q00-20231106003 修正發起權限設定屬性在新增使用者後變成解析錯誤問題
  - 變更檔案: 1 個
- **2023-11-06 17:31:38**: [DT]Q00-20231106003 修正發起權限設定屬性沒有資料下在編輯狀態進行新增設定時儲存沒有效果問題
  - 變更檔案: 1 個

### pinchi_lin (39 commits)

- **2024-01-31 15:50:36**: [PRODT]Q00-20240131001 修正Web流程管理工具中元件放到連接線上會自動生成或合併線的問題
  - 變更檔案: 1 個
- **2024-01-25 16:05:50**: [PRODT]Q00-20240125003 修正Web流程管理工具中流程模型屬性設定無法點確定的問題
  - 變更檔案: 1 個
- **2024-01-19 10:08:18**: [PRODT]Q00-20231222001 調整流程管理工具中流程圖連接線允許編輯的邏輯
  - 變更檔案: 1 個
- **2024-01-10 10:51:24**: [PRODT]Q00-20240110001 修正Web流程管理工具中儲存進版後的建立日期非當前日期的問題
  - 變更檔案: 4 個
- **2023-12-21 17:01:34**: [PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題
  - 變更檔案: 1 個
- **2023-12-21 17:00:27**: Revert "[PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題"
  - 變更檔案: 1 個
- **2023-12-21 16:58:20**: [PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題
  - 變更檔案: 1 個
- **2023-12-20 10:19:36**: [PRODT]Q00-20231220002 修正Web流程管理工具中核決活動的核決規則其層級選擇異常的問題
  - 變更檔案: 1 個
- **2023-12-14 18:35:51**: [PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題
  - 變更檔案: 6 個
- **2023-12-05 19:08:21**: [PRODT]Q00-20231205006 修正Web流程管理工具中流程圖有缺少連接線時仍可儲存的問題
  - 變更檔案: 2 個
- **2023-12-01 17:07:06**: [ORGDT]Q00-20231201005 修正組織管理工具從首頁使用者清單調離並轉調其他單位時不會帶入原單位任職資料的問題
  - 變更檔案: 3 個
- **2023-12-01 10:51:50**: [ORGDT]Q00-20231201003 修正組織管理工具從搜尋修改使用者資料後返回首頁使用者清單未更新的問題
  - 變更檔案: 1 個
- **2023-12-01 10:28:18**: [ORGDT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題
  - 變更檔案: 1 個
- **2023-12-01 10:26:31**: Revert "[PRODT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題"
  - 變更檔案: 1 個
- **2023-12-01 09:34:59**: [PRODT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題
  - 變更檔案: 1 個
- **2023-12-01 09:34:23**: [PRODT]Q00-20231201001 修正流程管理工具進階按鈕禁用選項點擊後仍會觸發問題
  - 變更檔案: 1 個
- **2023-11-28 12:01:08**: [PRODT]Q00-20231128002 修正流程樹節點禁用選項點擊後仍會觸發問題
  - 變更檔案: 1 個
- **2023-11-21 18:58:22**: [DT]Q00-20231121008 修正核決層級中選擇參考活動的一般活動無資料問題
  - 變更檔案: 1 個
- **2023-11-21 18:56:50**: [DT]Q00-20231117005 修正流程管理工具中應用程式管理員修改或刪除session bean中的參數後仍是舊資料的問題
  - 變更檔案: 2 個
- **2023-11-16 15:54:43**: [DT]Q00-20231116003 修正流程管理工具中活動關卡的不可退回活動設定存在髒資料導致雙擊無法開啟編輯頁面的問題
  - 變更檔案: 1 個
- **2023-11-07 14:43:13**: [DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題
  - 變更檔案: 1 個
- **2023-11-07 14:42:03**: Revert "[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題[補]"
  - 變更檔案: 1 個
- **2023-11-07 14:16:24**: [DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題[補]
  - 變更檔案: 1 個
- **2023-11-07 14:14:13**: Revert "[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題"
  - 變更檔案: 1 個
- **2023-11-07 14:06:54**: [DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題
  - 變更檔案: 1 個
- **2023-10-31 17:26:14**: [DT]Q00-20231031003 修正組織管理工具中組織樹根節點下無組織資料時展開會有後端接口調用失敗的問題
  - 變更檔案: 1 個
- **2023-10-23 15:59:25**: [DT]Q00-20231023005 修正組織管理工具中首頁使用者表格的換頁按鈕被隱藏的問題
  - 變更檔案: 2 個
- **2023-10-17 20:38:26**: [DT]Q00-20230918003 修正開起流程後活動與連接線消失的問題[補]
  - 變更檔案: 3 個
- **2023-10-17 13:47:00**: [DT]Q00-20231017001 修正資料使用權限管理中新增時啟用包含子目錄儲存後會還原的問題
  - 變更檔案: 3 個
- **2023-10-12 17:59:56**: [DT]Q00-20231012005 修正使用套索元件選取多個流程圖元件刪除後流程模型定義會異常的問題
  - 變更檔案: 2 個
- **2023-10-12 14:13:48**: [DT]Q00-20231012001 修正匯出流程不會取當前流程圖內容的問題
  - 變更檔案: 1 個
- **2023-10-03 14:01:12**: [DT]Q00-20231003002 修正流程管理工具中因註解元件固定長寬導致顯示異常的問題
  - 變更檔案: 3 個
- **2023-09-25 17:29:43**: [DT]Q00-20230925003 修正通知編輯範本中變數清單缺少問題
  - 變更檔案: 1 個
- **2023-09-23 10:57:14**: [DT]Q00-20230923001 修正活動定義編輯器進階屬性中限制欄位的單位顯示異常問題
  - 變更檔案: 4 個
- **2023-09-21 11:23:47**: [DT]Q00-20230918003 修正開起流程後活動與連接線消失的問題
  - 變更檔案: 437 個
- **2023-09-18 17:00:51**: [DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題
  - 變更檔案: 1 個
- **2023-09-01 19:42:00**: [DT]Q00-20230901002 修正流程管理工具中主流程屬性設定的可重定義屬性中關係人選擇表單無法單選人員的問題
  - 變更檔案: 4 個
- **2023-08-29 11:06:42**: [DT]A00-20230828001 修正組織管理工具中設定代理人資訊在第二頁點修改開啟頁面資料會取錯的問題
  - 變更檔案: 2 個
- **2023-08-28 11:13:43**: [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. [PRODT]Q00-20240417001 修正Web流程管理工具中流程定義進版後不會更新作者資訊問題
- **Commit ID**: `dc4b81723a6b1c20281714318184a09a057e4e07`
- **作者**: yamiyeh10
- **日期**: 2024-04-17 09:33:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 2. [PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況
- **Commit ID**: `8887fd1dbb61889c2ac618557a3789b20bd7b724`
- **作者**: yamiyeh10
- **日期**: 2024-09-26 08:21:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 3. [PRODT]C01-20240619001 修正Web流程管理工具中服務關卡的參數功能切換到第二頁後存在實際參數卻出現未指定參數的訊息
- **Commit ID**: `805acf0d5bf4291c97945abfc47c19adfe2efda7`
- **作者**: yamiyeh10
- **日期**: 2024-06-20 17:22:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/formal-parameter/formal-parameter.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/formal-parameter/formal-parameter.component.ts`

### 4. [PRODT]Q00-20240131001 修正Web流程管理工具中元件放到連接線上會自動生成或合併線的問題
- **Commit ID**: `5e3eea66c0d267770cf1739258c6aa6e6dc8356c`
- **作者**: pinchi_lin
- **日期**: 2024-01-31 15:50:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DropOnFlowBehavior.js`

### 5. [PRODT]Q00-20240125003 修正Web流程管理工具中流程模型屬性設定無法點確定的問題
- **Commit ID**: `f780ed76dfbe69ecb2a9d6c2b20a114dac755cde`
- **作者**: pinchi_lin
- **日期**: 2024-01-25 16:05:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`

### 6. [PRODT]Q00-20231222001 調整流程管理工具中流程圖連接線允許編輯的邏輯
- **Commit ID**: `1e415fdaeea62d59c6d7d8d434145d4b14fdd31c`
- **作者**: pinchi_lin
- **日期**: 2024-01-19 10:08:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 7. [PRODT]Q00-20240110001 修正Web流程管理工具中儲存進版後的建立日期非當前日期的問題
- **Commit ID**: `e0e3bb64a44bf656039cf839632d02e927cdb8f3`
- **作者**: pinchi_lin
- **日期**: 2024-01-10 10:51:24
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/save-process-package-drawer/save-process-package-drawer.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/save-process-package-drawer/save-process-package-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 8. [PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題
- **Commit ID**: `05d8b95adad5e03bef0fec307423801c7ccbe4c2`
- **作者**: pinchi_lin
- **日期**: 2023-12-21 17:01:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 9. Revert "[PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題"
- **Commit ID**: `ed4170ccd742d1b62ce83d8caedca16d6e23d5e7`
- **作者**: pinchi_lin
- **日期**: 2023-12-21 17:00:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 10. [PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題
- **Commit ID**: `3d755617ef9aeeee369b774bedfc7f9bd3155011`
- **作者**: pinchi_lin
- **日期**: 2023-12-21 16:58:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 11. [PRODT]Q00-20231220002 修正Web流程管理工具中核決活動的核決規則其層級選擇異常的問題
- **Commit ID**: `85ad4220acb45dd40de3d901a43088b45bd43c16`
- **作者**: pinchi_lin
- **日期**: 2023-12-20 10:19:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-rule-list-editor/decision-rule-list-editor.component.ts`

### 12. [PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題
- **Commit ID**: `774fe99d0da8edadb5a33a5ec508953e5899fb4d`
- **作者**: pinchi_lin
- **日期**: 2023-12-14 18:35:51
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/services/home-button-event-emitter-collection.service.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/services/process-package-manage.service.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/tabs/tabs.component.ts`
  - 📝 **修改**: `src/com/digiwin/bpm/DTModule/services/ProcessDesignMgr.java`

### 13. [PRODT]Q00-20231205006 修正Web流程管理工具中流程圖有缺少連接線時仍可儲存的問題
- **Commit ID**: `13131ce8599aea64f5349835ac9445da6ca02cbf`
- **作者**: pinchi_lin
- **日期**: 2023-12-05 19:08:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 14. [ORGDT]Q00-20231201005 修正組織管理工具從首頁使用者清單調離並轉調其他單位時不會帶入原單位任職資料的問題
- **Commit ID**: `86c0f3b6fab1c94cdd2963536d2943909b5141e5`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 17:07:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/hosting-department-openwin/hosting-department-openwin.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/hosting-project-openwin/hosting-project-openwin.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/tables/user-table/user-table.component.ts`

### 15. [ORGDT]Q00-20231201003 修正組織管理工具從搜尋修改使用者資料後返回首頁使用者清單未更新的問題
- **Commit ID**: `99d9a703ffbbbb0efced70ed356fdcb7eaaeaefc`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 10:51:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>

### 16. [ORGDT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題
- **Commit ID**: `8fd244ac63c8c2b76dde0acc81d220349e9f3d94`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 10:28:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>

### 17. Revert "[PRODT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題"
- **Commit ID**: `9a1efaa5c85a6503add0a1e8bc59d1f99f51ce69`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 10:26:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>

### 18. [PRODT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題
- **Commit ID**: `9431cf4182b53592ee55f48859464be66052da3b`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 09:34:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>

### 19. [PRODT]Q00-20231201001 修正流程管理工具進階按鈕禁用選項點擊後仍會觸發問題
- **Commit ID**: `975a2f1c95f797d34ddc9c968f52194fcb8d3e2e`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 09:34:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>

### 20. [PRODT]Q00-20231128002 修正流程樹節點禁用選項點擊後仍會觸發問題
- **Commit ID**: `4efa8d2828c1a8656fc6be451510d0615cf3a2d1`
- **作者**: pinchi_lin
- **日期**: 2023-11-28 12:01:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 21. [DT]Q00-20231121008 修正核決層級中選擇參考活動的一般活動無資料問題
- **Commit ID**: `36e196910a58873800615fc91f88e0976d5bda10`
- **作者**: pinchi_lin
- **日期**: 2023-11-21 18:58:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-chooser/activity-chooser.component.ts`

### 22. [DT]Q00-20231117005 修正流程管理工具中應用程式管理員修改或刪除session bean中的參數後仍是舊資料的問題
- **Commit ID**: `71bf03571183a770c9773f0a81c3961d07a43cbd`
- **作者**: pinchi_lin
- **日期**: 2023-11-21 18:56:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/basic-type-inspector/basic-type-inspector.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/formal-parameter/formal-parameter.component.ts`

### 23. [DT]Q00-20231116003 修正流程管理工具中活動關卡的不可退回活動設定存在髒資料導致雙擊無法開啟編輯頁面的問題
- **Commit ID**: `6e00be5194b13a8ffdbe391d17c9a2978e16eda1`
- **作者**: pinchi_lin
- **日期**: 2023-11-16 15:54:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/unreexecute-attributes/unreexecute-attributes.component.ts`

### 24. [DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題
- **Commit ID**: `8df6b3481d678884b867fbc197e6bacbacb78ee0`
- **作者**: pinchi_lin
- **日期**: 2023-11-07 14:43:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 25. Revert "[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題[補]"
- **Commit ID**: `6fa10b29c823950f5d4cf9dd1f25cee7063ba76d`
- **作者**: pinchi_lin
- **日期**: 2023-11-07 14:42:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 26. [DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題[補]
- **Commit ID**: `067d7610ec2a963f705f6e1c7562cefd03d81718`
- **作者**: pinchi_lin
- **日期**: 2023-11-07 14:16:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 27. Revert "[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題"
- **Commit ID**: `017af50d12d5ceca60556f0eac1ad7f43066e574`
- **作者**: pinchi_lin
- **日期**: 2023-11-07 14:14:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 28. [DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題
- **Commit ID**: `f29840e9df75c7d3619001046f3f85f873e57808`
- **作者**: pinchi_lin
- **日期**: 2023-11-07 14:06:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 29. [DT]Q00-20231107001 修正發起權限設定屬性在新增使用者後變成解析錯誤問題
- **Commit ID**: `1ed269fc5c197b363d9ef16622e30146bc254f26`
- **作者**: yamiyeh10
- **日期**: 2023-11-07 09:10:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/add-invoke-aurhority-openwin/add-invoke-aurhority-openwin.component.ts`

### 30. Revert "[DT]Q00-20231106003 修正發起權限設定屬性在新增使用者後變成解析錯誤問題"
- **Commit ID**: `d542d33ca71a915fb7171a6d065a5ff77e00197d`
- **作者**: yamiyeh10
- **日期**: 2023-11-07 09:07:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/add-invoke-aurhority-openwin/add-invoke-aurhority-openwin.component.ts`

### 31. [DT]Q00-20231106003 修正發起權限設定屬性在新增使用者後變成解析錯誤問題
- **Commit ID**: `e5d5fedf93f87fe24fefa35d470eb05165456271`
- **作者**: yamiyeh10
- **日期**: 2023-11-07 09:07:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/add-invoke-aurhority-openwin/add-invoke-aurhority-openwin.component.ts`

### 32. [DT]Q00-20231106003 修正發起權限設定屬性沒有資料下在編輯狀態進行新增設定時儲存沒有效果問題
- **Commit ID**: `be178bf5945df9ca7ba1cfb2cc8fe966ce24bc29`
- **作者**: yamiyeh10
- **日期**: 2023-11-06 17:31:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`

### 33. [DT]Q00-20231031003 修正組織管理工具中組織樹根節點下無組織資料時展開會有後端接口調用失敗的問題
- **Commit ID**: `1b00c405d1c765e8cef467a1017c90f6c9da1515`
- **作者**: pinchi_lin
- **日期**: 2023-10-31 17:26:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/shared/components/organization-tree/organization-tree.component.ts`

### 34. [DT]Q00-20231023005 修正組織管理工具中首頁使用者表格的換頁按鈕被隱藏的問題
- **Commit ID**: `b7aee9f11e4720153d48dd8a92799faad41fa75f`
- **作者**: pinchi_lin
- **日期**: 2023-10-23 15:59:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/tables/user-table/user-table.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/tables/user-table/user-table.component.ts`

### 35. [DT]Q00-20230918003 修正開起流程後活動與連接線消失的問題[補]
- **Commit ID**: `4d30c532249f2c0ff70f7b885ab23057526afb1a`
- **作者**: pinchi_lin
- **日期**: 2023-10-17 20:38:26
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/bpmn-js/package.json`
  - 📝 **修改**: `AngularProjects/DTModule/bpmn-moddle/package.json`
  - 📝 **修改**: `AngularProjects/DTModule/package-lock.json`

### 36. [DT]Q00-20231017001 修正資料使用權限管理中新增時啟用包含子目錄儲存後會還原的問題
- **Commit ID**: `c2c25c1532a31309b32dd6a5c160db34e4a48f8d`
- **作者**: pinchi_lin
- **日期**: 2023-10-17 13:47:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/access-control/access-contorl-table/access-contorl-table.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/access-control/access-control-create/access-control-create.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/access-control/access-control-create/access-control-create.component.ts`

### 37. [DT]Q00-20231012005 修正使用套索元件選取多個流程圖元件刪除後流程模型定義會異常的問題
- **Commit ID**: `3a202a59c42140da91b386c79141565f8559bb31`
- **作者**: pinchi_lin
- **日期**: 2023-10-12 17:59:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-custom/customContextPad.js`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 38. [DT]Q00-20231012001 修正匯出流程不會取當前流程圖內容的問題
- **Commit ID**: `eaa8357557292e4558e7064aa4a2b0171b1866f4`
- **作者**: pinchi_lin
- **日期**: 2023-10-12 14:13:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/buttons/view-edit-page-button/view-edit-page-button.component.ts`

### 39. [DT]Q00-20231003002 修正流程管理工具中因註解元件固定長寬導致顯示異常的問題
- **Commit ID**: `07e66ea71c15a1d469749bcb34b992712827e8ac`
- **作者**: pinchi_lin
- **日期**: 2023-10-03 14:01:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/ElementFactory.js`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-custom/customRenderer.js`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 40. [DT]Q00-20230925003 修正通知編輯範本中變數清單缺少問題
- **Commit ID**: `4a8d51ee42182450d37dbf63e18f33a230e19bca`
- **作者**: pinchi_lin
- **日期**: 2023-09-25 17:29:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/pattern-editor/pattern-editor.component.html`

### 41. [DT]Q00-20230923001 修正活動定義編輯器進階屬性中限制欄位的單位顯示異常問題
- **Commit ID**: `552990b16aed4ed092f1ea3e6b838625f1d13025`
- **作者**: pinchi_lin
- **日期**: 2023-09-23 10:57:14
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/activity-definition-manager/activity-definition-manager.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/activity-definition-manager/activity-definition-manager.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-advanced-attributes/activity-advanced-attributes.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-advanced-attributes/activity-advanced-attributes.component.ts`

### 42. [DT]Q00-20230918003 修正開起流程後活動與連接線消失的問題
- **Commit ID**: `d71236f51acd254f55e135df77da5d45246f35fb`
- **作者**: pinchi_lin
- **日期**: 2023-09-21 11:23:47
- **變更檔案數量**: 437
- **檔案變更詳細**:
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/LICENSE`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/README.md`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/css/bpmn.css`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.eot`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.ttf`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.woff`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.woff2`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-js.css`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/assets/diagram-js.css`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/bpmn-modeler.development.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/bpmn-modeler.production.min.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/bpmn-navigated-viewer.development.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/bpmn-navigated-viewer.production.min.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/bpmn-viewer.development.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/dist/bpmn-viewer.production.min.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/BaseModeler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/BaseViewer.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/Modeler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/NavigatedViewer.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/Viewer.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/core/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/draw/BpmnRenderUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/draw/BpmnRenderer.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/draw/PathMap.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/draw/TextRenderer.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/draw/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/AlignElementsContextPadProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/AlignElementsIcons.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/AlignElementsMenuProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/BpmnAlignElements.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-bottom-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-horizontal-center-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-left-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-right-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-top-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-vertical-center-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/auto-place/BpmnAutoPlace.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/auto-place/BpmnAutoPlaceUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/auto-place/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/auto-resize/BpmnAutoResize.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/auto-resize/BpmnAutoResizeProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/auto-resize/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/context-pad/ContextPadProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/context-pad/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/copy-paste/BpmnCopyPaste.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/copy-paste/ModdleCopy.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/copy-paste/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/di-ordering/BpmnDiOrdering.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/di-ordering/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/BpmnDistributeElements.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/DistributeElementsIcons.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/DistributeElementsMenuProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/resources/distribute-horizontally-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/resources/distribute-vertically-tool.svg`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/drilldown/DrilldownBreadcrumbs.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/drilldown/DrilldownCentering.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/drilldown/DrilldownOverlayBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/drilldown/SubprocessCompatibility.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/drilldown/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/editor-actions/BpmnEditorActions.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/editor-actions/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/BpmnGridSnapping.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/behavior/GridSnappingAutoPlaceBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/behavior/GridSnappingLayoutConnectionBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/behavior/GridSnappingParticipantBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/behavior/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/interaction-events/BpmnInteractionEvents.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/interaction-events/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/keyboard/BpmnKeyboardBindings.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/keyboard/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/label-editing/LabelEditingPreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/label-editing/LabelEditingProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/label-editing/LabelUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/label-editing/cmd/UpdateLabelHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/label-editing/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/BpmnFactory.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/BpmnLayouter.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/BpmnUpdater.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/ElementFactory.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/Modeling.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/AdaptiveLabelPositioningBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/AppendBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/AssociationBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/AttachEventBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/BoundaryEventBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/CreateBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/CreateDataObjectBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/CreateParticipantBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DataInputAssociationBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DataStoreBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DeleteLaneBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DetachEventBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DropOnFlowBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/EventBasedGatewayBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/FixHoverBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/GroupBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ImportDockingFix.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/IsHorizontalFix.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/LabelBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/LayoutConnectionBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/MessageFlowBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ModelingFeedback.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/RemoveElementBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/RemoveEmbeddedLabelBoundsBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/RemoveParticipantBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ReplaceConnectionBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ReplaceElementBehaviour.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ResizeBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ResizeLaneBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/RootElementReferenceBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/SpaceToolBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/SubProcessPlaneBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/SubProcessStartEventBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ToggleCollapseConnectionBehaviour.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ToggleElementCollapseBehaviour.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/UnclaimIdBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/UnsetDefaultFlowBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/UpdateFlowNodeRefsBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/CategoryUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/ConnectionLayoutUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/GeometricUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/LabelLayoutUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/LayoutUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/LineAttachmentUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/LineIntersect.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/ResizeUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/AddLaneHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/IdClaimHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/ResizeLaneHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/SetColorHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/SplitLaneHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdateCanvasRootHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdateFlowNodeRefsHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdateModdlePropertiesHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdatePropertiesHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdateSemanticParentHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/util/LaneUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/util/ModelingUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/ordering/BpmnOrderingProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/ordering/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/palette/PaletteProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/palette/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/popup-menu/ReplaceMenuProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/popup-menu/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/popup-menu/util/TypeUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/replace-preview/BpmnReplacePreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/replace-preview/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/replace/BpmnReplace.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/replace/ReplaceOptions.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/replace/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/rules/BpmnRules.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/rules/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/search/BpmnSearchProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/search/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/snapping/BpmnConnectSnapping.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/snapping/BpmnCreateMoveSnapping.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/snapping/BpmnSnappingUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/snapping/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/space-tool/BpmnSpaceTool.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/features/space-tool/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/import/BpmnImporter.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/import/BpmnTreeWalker.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/import/Importer.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/import/Util.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/import/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/util/CompatibilityUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/util/DiUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/util/DrilldownUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/util/LabelUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/util/ModelUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/lib/util/PoweredByUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/package.json`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/helper/TranslationCollector.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/helper/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/matchers/BoundsMatchers.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/matchers/ConnectionMatchers.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/matchers/JSONMatcher.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/util/KeyEvents.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/util/MockEvents.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/util/custom-rules/CustomRules.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-js/test/util/custom-rules/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/LICENSE`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/README.md`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/dist/bpmn-moddle.umd.cjs`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/dist/bpmn-moddle.umd.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/dist/bpmn-moddle.umd.prod.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/dist/index.cjs`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/dist/index.esm.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/dist/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/package.json`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn-io/json/bioc.json`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/json/bpmn.json`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/json/bpmndi.json`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/json/dc.json`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/json/di.json`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/BPMN20.xsd`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/BPMNDI.xsd`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/DC.xsd`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/DI.xsd`
  - ➕ **新增**: `AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/Semantic.xsd`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/LICENSE`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/README.md`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/assets/diagram-js.css`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/Diagram.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/command/CommandHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/command/CommandInterceptor.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/command/CommandStack.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/command/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/core/Canvas.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/core/ElementFactory.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/core/ElementRegistry.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/core/EventBus.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/core/GraphicsFactory.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/core/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/draw/BaseRenderer.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/draw/DefaultRenderer.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/draw/Styles.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/draw/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/align-elements/AlignElements.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/align-elements/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/attach-support/AttachSupport.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/attach-support/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-place/AutoPlace.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-place/AutoPlaceSelectionBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-place/AutoPlaceUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-place/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-resize/AutoResize.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-resize/AutoResizeProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-resize/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-scroll/AutoScroll.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/auto-scroll/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/bendpoints/BendpointMove.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/bendpoints/BendpointMovePreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/bendpoints/BendpointSnapping.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/bendpoints/BendpointUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/bendpoints/Bendpoints.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/bendpoints/ConnectionSegmentMove.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/bendpoints/GeometricUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/bendpoints/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/change-support/ChangeSupport.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/change-support/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/clipboard/Clipboard.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/clipboard/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/connect/Connect.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/connect/ConnectPreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/connect/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/connection-preview/ConnectionPreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/connection-preview/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/context-pad/ContextPad.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/context-pad/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/copy-paste/CopyPaste.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/copy-paste/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/create/Create.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/create/CreateConnectPreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/create/CreatePreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/create/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/distribute-elements/DistributeElements.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/distribute-elements/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/dragging/Dragging.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/dragging/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/editor-actions/EditorActions.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/editor-actions/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/global-connect/GlobalConnect.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/global-connect/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/GridSnapping.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/GridUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/behavior/ResizeBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/behavior/SpaceToolBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/behavior/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/visuals/Grid.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/visuals/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/hand-tool/HandTool.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/hand-tool/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/hover-fix/HoverFix.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/hover-fix/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/interaction-events/InteractionEvents.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/interaction-events/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/keyboard-move-selection/KeyboardMoveSelection.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/keyboard-move-selection/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/keyboard/Keyboard.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/keyboard/KeyboardBindings.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/keyboard/KeyboardUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/keyboard/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/label-support/LabelSupport.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/label-support/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/lasso-tool/LassoTool.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/lasso-tool/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/Modeling.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/AlignElementsHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/AppendShapeHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/CreateConnectionHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/CreateElementsHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/CreateLabelHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/CreateShapeHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/DeleteConnectionHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/DeleteElementsHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/DeleteShapeHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/DistributeElementsHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/LayoutConnectionHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/MoveConnectionHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/MoveElementsHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/MoveShapeHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/NoopHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/ReconnectConnectionHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/ReplaceShapeHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/ResizeShapeHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/SpaceToolHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/ToggleShapeCollapseHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/UpdateAttachmentHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/UpdateWaypointsHandler.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/helper/AnchorsHelper.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/helper/MoveClosure.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/helper/MoveHelper.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/modeling/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/mouse/Mouse.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/mouse/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/move/Move.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/move/MovePreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/move/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/ordering/OrderingProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/outline/Outline.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/outline/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/overlays/Overlays.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/overlays/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/palette/Palette.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/palette/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenu.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenuComponent.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenuItem.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenuList.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenuProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/popup-menu/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/preview-support/PreviewSupport.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/preview-support/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/replace/Replace.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/replace/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/resize/Resize.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/resize/ResizeHandles.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/resize/ResizePreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/resize/ResizeUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/resize/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/root-elements/RootElementsBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/root-elements/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/rules/RuleProvider.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/rules/Rules.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/rules/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/search-pad/SearchPad.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/search-pad/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/selection/Selection.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/selection/SelectionBehavior.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/selection/SelectionVisuals.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/selection/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/snapping/CreateMoveSnapping.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/snapping/ResizeSnapping.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/snapping/SnapContext.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/snapping/SnapUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/snapping/Snapping.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/snapping/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/space-tool/SpaceTool.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/space-tool/SpaceToolPreview.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/space-tool/SpaceUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/space-tool/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/tool-manager/ToolManager.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/tool-manager/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/tooltips/Tooltips.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/tooltips/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/touch/TouchFix.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/touch/TouchInteractionEvents.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/features/touch/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/i18n/I18N.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/i18n/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/i18n/translate/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/i18n/translate/translate.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/layout/BaseLayouter.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/layout/ConnectionDocking.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/layout/CroppingConnectionDocking.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/layout/LayoutUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/layout/ManhattanLayout.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/model/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/navigation/keyboard-move/KeyboardMove.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/navigation/keyboard-move/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/navigation/movecanvas/MoveCanvas.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/navigation/movecanvas/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/navigation/touch/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/navigation/zoomscroll/ZoomScroll.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/navigation/zoomscroll/ZoomUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/navigation/zoomscroll/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/ui/index.d.ts`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/ui/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/AttachUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/ClickTrap.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Collections.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Cursor.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Elements.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/EscapeUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Event.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Geometry.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/GraphicsUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/IdGenerator.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/LineIntersection.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Math.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Mouse.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Platform.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/PositionUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Removal.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/RenderUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/SvgTransformUtil.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/lib/util/Text.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/package.json`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/test/helper/index.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/test/matchers/BoundsMatchers.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/test/matchers/BoundsMatchersSpec.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/test/matchers/ConnectionMatchers.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/test/matchers/ConnectionMatchersSpec.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/test/util/KeyEvents.js`
  - ➕ **新增**: `AngularProjects/DTModule/diagram-js/test/util/MockEvents.js`
  - ➕ **新增**: `AngularProjects/DTModule/moddle-xml/LICENSE`
  - ➕ **新增**: `AngularProjects/DTModule/moddle-xml/README.md`
  - ➕ **新增**: `AngularProjects/DTModule/moddle-xml/dist/index.cjs`
  - ➕ **新增**: `AngularProjects/DTModule/moddle-xml/dist/index.cjs.map`
  - ➕ **新增**: `AngularProjects/DTModule/moddle-xml/dist/index.esm.js`
  - ➕ **新增**: `AngularProjects/DTModule/moddle-xml/dist/index.esm.js.map`
  - ➕ **新增**: `AngularProjects/DTModule/moddle-xml/dist/moddle-xml.umd.cjs`
  - ➕ **新增**: `AngularProjects/DTModule/moddle-xml/package.json`
  - 📝 **修改**: `AngularProjects/DTModule/package.json`

### 43. [DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題
- **Commit ID**: `826078283913aa189c5ea0e14c7b7df73088d791`
- **作者**: pinchi_lin
- **日期**: 2023-09-18 17:00:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/models/process-definition-header.model.ts`

### 44. [DT]Q00-20230901002 修正流程管理工具中主流程屬性設定的可重定義屬性中關係人選擇表單無法單選人員的問題
- **Commit ID**: `32bbb8229af2523386b81b4678bba6b0a45f5566`
- **作者**: pinchi_lin
- **日期**: 2023-09-01 19:42:00
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/main-process-definition-drawer/main-process-definition-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-element/form-element.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-definition-redefinable-header/process-definition-redefinable-header.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-definition-redefinable-header/process-definition-redefinable-header.component.ts`

### 45. [DT]A00-20230828001 修正組織管理工具中設定代理人資訊在第二頁點修改開啟頁面資料會取錯的問題
- **Commit ID**: `81f0ae649bc8b1a566da5eb45b9605812478d20e`
- **作者**: pinchi_lin
- **日期**: 2023-08-29 11:06:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/operations/substitute-operation/substitute-operation.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/operations/substitute-operation/substitute-operation.component.ts`

### 46. [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
- **Commit ID**: `0c743951e57b7f611b2534bf7edd3770f91ed7e0`
- **作者**: pinchi_lin
- **日期**: 2023-08-28 11:13:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/print-openwin/print-openwin.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/shared/models/print.model.ts`

