{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.8.1_20220803", "date": "2022-06-26 20:48:05", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.8.1", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.8.1", "date": "2022-06-26 20:48:05", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.8.1", "author": "lorenchang"}, "比較時間": "2025-07-18 11:18:08", "新增commit數量": 127, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "7c29f10f690daa80875a3d294e1f9729f33b6a12", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.8.1", "提交日期": "2022-06-26 20:48:05", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "da5b5c4220450b2b46cb6dd8b71d38d7657b021e", "commit_訊息": "[Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失 RWD不會發生的原因是就算前端直是空的傳進來也是[] 移動防呆位置", "提交日期": "2022-08-03 16:04:39", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c209cc1f152d305f4021c45adce456bdbc068b06", "commit_訊息": "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。", "提交日期": "2022-07-29 16:30:05", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "678cbe117d658fd527a5e169087b8e501a670d5b", "commit_訊息": "[流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送", "提交日期": "2022-07-21 15:36:43", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a056660157cae186b4044ac1cdeaed29ddc34641", "commit_訊息": "[流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料", "提交日期": "2022-08-03 15:53:36", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "756afc159b46123d50e00f9b17762758843086ea", "commit_訊息": "Revert \"[流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料\"", "提交日期": "2022-08-03 15:38:59", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2c62d2a67f740dba4c4b9de5278842d290f305b5", "commit_訊息": "[流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料", "提交日期": "2022-06-29 14:31:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a4b215c54e1cfea008defbe1ee0b564cf87595c1", "commit_訊息": "[WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]", "提交日期": "2022-07-29 14:34:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "754970492b7e6e5dd673fa321a62ed80f4de16cd", "commit_訊息": "[Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題", "提交日期": "2022-07-27 17:51:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cbacd775df77172323d6129dbc1433953edef27", "commit_訊息": "[Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]", "提交日期": "2022-07-28 14:39:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e743ffd22d13e7c2ccdb92e89040325bf5f8609c", "commit_訊息": "[Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。", "提交日期": "2022-07-27 12:07:05", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c710bda537773a3d8b1fb37aebfcf355279c257c", "commit_訊息": "[Web]A00-20220802001 修正無法開啟SAP維護作業", "提交日期": "2022-08-02 11:27:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5caf081cb7708b9408fee161ab60057746a5832a", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]", "提交日期": "2022-07-29 14:02:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ca6439b52e14e25dd1f36a8cc1f0707d45d00df2", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock", "提交日期": "2022-07-28 15:20:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b3023f7146ee8bdbe55d5fc64c9b987d9b3003d5", "commit_訊息": "Revert \"[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock\"", "提交日期": "2022-08-01 16:38:12", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2b34ffdd46218c2bb2b3693738fc5e50439c00d9", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock", "提交日期": "2022-07-28 15:20:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c6484f3ab16b220889e23161032bda07d9b139c2", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]", "提交日期": "2022-07-29 14:20:21", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6a34311627ddb878ac7890ff54129650d37c6dcb", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況", "提交日期": "2022-07-29 00:04:37", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "170861ad2b4d77efec6f1e396ec67cc5f902f34a", "commit_訊息": "Revert \"[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況\"", "提交日期": "2022-08-01 13:51:55", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b7ead7ae38f728c9e726e4e6b37fa13e441c3964", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況", "提交日期": "2022-07-29 00:04:37", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8a24c043da8c40dcbd5078f6eb778ab0c01b9671", "commit_訊息": "Revert \"[MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]\"", "提交日期": "2022-08-01 13:50:19", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4fae0e3c2582cfa92d1a7a6203506a845567fb87", "commit_訊息": "[MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]", "提交日期": "2022-07-12 19:29:30", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "bcd9da43d09d4664ca0a0f80a77c49583f2a5a47", "commit_訊息": "[WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法", "提交日期": "2022-07-27 10:41:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b46a273694ad0a7d9c6f7c47b6b75dfe90b59c64", "commit_訊息": "[內部]Log調整", "提交日期": "2022-07-04 08:58:18", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "685568764d2c4310fa42b5f4b2f591bdcfdc200a", "commit_訊息": "[Web]Q00-20220720002 修正列印模式下附件與簽核歷程的右邊邊線會不見問題", "提交日期": "2022-07-20 18:23:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "992c075955e3b09290b0b18065d57f3477aca6d8", "commit_訊息": "Revert \"[Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部\"", "提交日期": "2022-07-25 17:12:47", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "979c526610ee3c35a2bf43776edf8ed59927e040", "commit_訊息": "[Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部", "提交日期": "2022-07-20 15:26:09", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "630b34ec1f5aa0c718d05b97d7dadfa068dc4f8f", "commit_訊息": "[Web]A00-20220718001 修正Gird元件在某關卡隱藏時開啟表單會出現該物件沒有定義的問題", "提交日期": "2022-07-20 11:38:23", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8a869815b15a74613153802bee16a9f13d438ce2", "commit_訊息": "[登入]Q00-20220719002 修正DB為Oracle時，使用者登出登入紀錄作業中使用操作時間為查詢條件會查不到結果的問題", "提交日期": "2022-07-19 18:01:01", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa30564db8e130af2c0fd839e564c5c7ea0202f3", "commit_訊息": "[Web]Q00-20220714004 修正使用safari瀏覽器時，點選在線閱讀附件沒有反應", "提交日期": "2022-07-14 18:28:53", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c5a72b06c2a0b21af2089c49cf16a8ee04256618", "commit_訊息": "[Web]Q00-20220713003 修正在行動版面中，在表單內向下滑動時，右下角的浮動按鈕會隱藏而無法後續操作", "提交日期": "2022-07-13 16:33:02", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8e15006b589d43fe9b77150a07c11164481a7cc", "commit_訊息": "[Web]Q00-20220714003 修正Dialog元件的txt屬性如果被FormScript或其他非預期方式刪除，在產生表單畫面時報錯 如果屬性被改成null或是遺失將其防呆為空字串 內部測試將元件隱藏一關或是連續兩關以上都無法重現，應該是客戶的Script有改到元件內容", "提交日期": "2022-07-18 10:06:16", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23a28bdefa5016de43ad7ba9f6bde2fa3e484682", "commit_訊息": "[流程引擎]Q00-20220713005 修正核決關卡設定自動簽核，取/退回後再次簽核進核決層級時會報錯無法派送", "提交日期": "2022-07-14 11:32:09", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3a615817f444bcb70b1096a71bf3abd98bf5150", "commit_訊息": "[Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法 語法: FormUtil.setScrollBarHeight(\"0\");", "提交日期": "2022-07-15 08:37:59", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c21f1f4d10ad95fdd39b3bd295bdaf0768ac5f19", "commit_訊息": "[表單設計師]Q00-20220711002 修正絕對表單元件不存在某些屬性而取用該屬性導致無法開啟表單，增加防呆", "提交日期": "2022-07-11 15:22:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a5152db54620f4ecbc0aa77803a0f5eb7e875444", "commit_訊息": "[流程引擎]Q00-20220706005 修正資料庫為MSSQL，且流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能", "提交日期": "2022-07-06 17:43:27", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9327480cbf619bcdde10a79f5aa2b1fa7da06eb5", "commit_訊息": "[Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久", "提交日期": "2022-06-28 18:06:01", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9d309c72dd36cc6448ac6ed7bb5ab0b904675dd", "commit_訊息": "[流程引擎]Q00-20220622001 修正當RWD表單的RadioButton元件與CheckBox元件選項內容太長時會斷行 相關議題單：C01-20220613009。", "提交日期": "2022-07-14 17:29:49", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "92368e872c7d106b815248361a8fc8e934ef0786", "commit_訊息": "[Web]Q00-20220621001 修正非CheckBox或RadioButton的選擇元件執行到額外輸入框邏輯導致出現非預期異常", "提交日期": "2022-07-14 17:27:26", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bdd4c6865b8c76cc3e3952aad15e2a8ebde55e7e", "commit_訊息": "[Web]Q00-20220713004 修正移動消息訂閱管理頁面無法開啟", "提交日期": "2022-07-13 16:21:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a566e9feb6ac3307b6192f9603977d986fcc61c2", "commit_訊息": "[流程引擎]Q00-20220711003 修正在表單上將Excel匯入單身後，開窗畫面變成空白", "提交日期": "2022-07-11 17:38:00", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "91b05ba89cabea5201cbfc5faa894ec859f03e5a", "commit_訊息": "[Web]Q00-20220701001 調整時間元件如果輸入不是數字直接換成00", "提交日期": "2022-07-01 11:54:21", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9179954f3708f8d659b0986294af383bbe8ea8a", "commit_訊息": "[資安]Q00-20220629001 修正/NaNaWeb/webservice/servlet/AxisServlet要加入為WebService白名單控管範圍", "提交日期": "2022-06-29 11:34:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e6d5832a2b9539fcf61afc755e9a4056598c44c2", "commit_訊息": "[Web]A00-20220622002 修正流程新增關卡頁面輸入簽核意見在新增向前or向後關卡按下確定後，簽核意見內容被清除", "提交日期": "2022-06-23 14:10:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc1b44733c0006ee17a2c088dde79b29a1a831e5", "commit_訊息": "[Web]Q00-20220523001 修正同瀏覽器有二次登入時，登入頁「記住我」的功能會失效", "提交日期": "2022-06-22 15:47:30", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6027984f5f20c23d39bf6d1cf12e20910800ab39", "commit_訊息": "[Web]A00-20220616003 修正SerialNumber元件的字體設25px以上，在流程中該元件的顯示會有部分被遮蔽到", "提交日期": "2022-06-21 16:55:38", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "24b41f87171d72661bf64fd8f1441f30d1bae8dd", "commit_訊息": "[Web]Q00-20220621003修正發起流程-查詢流程清單搜尋純數字的流程名稱會報錯", "提交日期": "2022-06-21 13:42:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f96b0529d219879fe2679c40b6603e8b7309ee6", "commit_訊息": "[Web]A00-20220616001 退回重辦頁面增加退回重辦方式的說明", "提交日期": "2022-06-20 14:09:46", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6f724526dcdf1eea047294858cfecf9bd0661df9", "commit_訊息": "[Web]Q00-20220609001 修正行動裝置在加簽關卡選擇參與者人員後，選擇參與者的下方欄位會顯示已選取0個項目", "提交日期": "2022-06-09 11:50:53", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b379956f95cf06d57f991b653dc6480c3f7ae7c0", "commit_訊息": "[報表設計器]Q00-20220607003 修正欄位字串如果含as會辨識錯欄位名稱", "提交日期": "2022-06-08 09:21:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7b2d36dee04fd8cc047a4ce0a3f26325d62440a", "commit_訊息": "[Web]Q00-20220607002 修正首頁待辦清單第三頁以上的流程進行派送時會報錯", "提交日期": "2022-06-07 14:20:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c19c7517a4280f2ea190a5ebb78b6d3b0d96a8f1", "commit_訊息": "[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼 1.移動端原本就會把中文字轉換成Unicode字符代碼作呈現,如果再轉一次就會把&轉換掉 2.關聯紀錄:A00-20220511001", "提交日期": "2022-07-14 16:39:42", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ff9754ebb34842d9a7b2c570f845fc1277289125", "commit_訊息": "Revert \"[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼\"", "提交日期": "2022-07-14 16:37:51", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 1}, {"commit_hash": "d75c994b9ebf5e12ffd5a75b93f4433a24850191", "commit_訊息": "[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼", "提交日期": "2022-07-12 20:01:24", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "c969b52102ecb4def4a86fff53097d8966931a97", "commit_訊息": "[流程引擎]Q00-20220628001 調整流程關係人與關係部門解析邏輯，若流程中沒有變更流程關係人或關係部門資料，系統在流程往下派送時就不會再解析流程關係人與關係部門", "提交日期": "2022-06-28 17:17:20", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0202ed19fb05f4ef5caced212eaf59732c4afe40", "commit_訊息": "[流程引擎]Q00-20220707002 修正表單日期元件預設值計算錯誤 相關議題單：C01-20220701004。", "提交日期": "2022-07-14 16:34:56", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d8ca71f47ea6d2662f690b837cbda0c4e18358f8", "commit_訊息": "[BPM APP]Q00-20220711001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟移動端表單會報錯的問題", "提交日期": "2022-07-12 11:15:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a7839f6112ce0f9793dc88d13827b7169f70b99", "commit_訊息": "[表單設計師]A00-20220704001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟表單會報錯", "提交日期": "2022-07-08 18:24:42", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "43126117cd2f58ca7ebb13c58093b4894adae349", "commit_訊息": "[表單設計師]C01-20220706003 修正當變更表單的對齊方式並儲存後會將已設計過的行動版表單設計欄位清空問題", "提交日期": "2022-07-08 11:01:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a91321f3fe9f0c7fe064f7c6b1867aa4406c2709", "commit_訊息": "[流程引擎]Q00-20220707003 修正DialogInput元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容", "提交日期": "2022-07-07 16:10:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "17d2f439eed1efb31126c52c678ac5a09292b906", "commit_訊息": "[Web]A00-20220628001 修正已簽核過的關卡，從Mail的待辦連結進入該表單時可以移除附件", "提交日期": "2022-07-14 16:28:34", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b351b7ac7de092698624bb3fb5339ede8495a92e", "commit_訊息": "[Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾", "提交日期": "2022-06-13 16:14:20", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "40b4ab1d5f46cc66c43a675819b363df40c62218", "commit_訊息": "[流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯[補修正]", "提交日期": "2022-06-14 11:52:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a54aee90cc354946b0b0ecc2d6c6af223527b0a3", "commit_訊息": "[流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯", "提交日期": "2022-06-13 16:07:01", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "38f5c4331370bb8b639116d0a47e783d2e124193", "commit_訊息": "[內部]Q00-20220610001修正WorkFlow拋單log會顯示[Fatal Error] :1:1的錯誤訊息", "提交日期": "2022-06-10 11:42:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7f0adcd722b72637ea0e2ce96e77fd7cd6848873", "commit_訊息": "[Web]A00-20220608003 修正進入追蹤流程畫面時未清除「撤銷理由」欄位內容", "提交日期": "2022-06-09 17:18:03", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "865774fa9b179a00f322eaff4163c2676a7a84e0", "commit_訊息": "[流程引擎]Q00-20220609003 修正使用者操作個人預設代理人設定時，代理人可能有多筆相同人員的問題", "提交日期": "2022-06-09 16:32:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/user_profile/MultiDefaultSubstituteForManaging.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18e25acb3b7f0414ddf505687e96c5cc66c0e3ed", "commit_訊息": "[內部]Q00-20220609002 調整DWR設定讓Log不要一直出現轉換ProcessInstanceStateType的錯誤", "提交日期": "2022-06-09 15:28:14", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c6ccf3fee3d00c3a620ba7c2db247a20c9cff9de", "commit_訊息": "[Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]", "提交日期": "2022-06-10 18:10:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eeb41f2b2ec36cbca2e5989c272bf8b90886920d", "commit_訊息": "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開", "提交日期": "2022-07-14 16:20:49", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a2411fdd14302b4f3cfbb506efe248d9d30d460", "commit_訊息": "Revert \"[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開\"", "提交日期": "2022-07-14 16:19:46", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 1}, {"commit_hash": "36869fb976f50f659b1bdd104cef8f7bc8fa0a7c", "commit_訊息": "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開", "提交日期": "2022-06-08 15:56:57", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "5a89e296bee6eef02313fc33a1c5e9da8778d7fb", "commit_訊息": "[WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯", "提交日期": "2022-06-08 11:49:23", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "46257b441af7fa32405dbe75555f7b915460b7e1", "commit_訊息": "[Web]Q00-20220606001 修正第二關之後的關卡預解析，流程線的條件式採用表單欄位時，預解析的關卡與派送的關卡不符合", "提交日期": "2022-06-06 14:26:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a61981f8b3b11ef21fe916b79f542cbf2c15f720", "commit_訊息": "[Web]A00-20220526001 修正如果DB是Oralce在線閱讀浮水印管理出現無法取得EJB所提供的服務", "提交日期": "2022-06-01 13:57:59", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5ee178664e6b6e9b0a45d94f6255f2f860e05de", "commit_訊息": "[內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方", "提交日期": "2022-05-30 15:34:30", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbf6cdaba99d733ce9fe7f8213e5eb67e2b27759", "commit_訊息": "[BPM APP]C01-20220523004 修正移動端subTab元件使用formScript在單獨顯示時會顯示其他頁籤內容的問題", "提交日期": "2022-05-27 19:22:55", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileSubTab.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "72b64ec91f0f44e402f870bccf743f5bb820d042", "commit_訊息": "[Web]Q00-20220527003 修正使用者使用監控流程的最大筆數沒有根據process.default.show.records的設定", "提交日期": "2022-05-27 17:34:09", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a609d8da256c68226cee6c806df3edff3854069c", "commit_訊息": "[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘", "提交日期": "2022-07-14 16:14:21", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "609cdce934fcef062a77186802a1131ffba5ef0d", "commit_訊息": "Revert \"[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘\"", "提交日期": "2022-07-14 16:13:16", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 1}, {"commit_hash": "6f10ef8d256aaf3cbae2014d7ae6712710b19dd6", "commit_訊息": "[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘", "提交日期": "2022-05-27 17:15:41", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "17062d606905ce9eb3c09a6d27037a1d5538e339", "commit_訊息": "[BPM APP]C01-20220524002 修正改派通知設定整張表單時Line推播內容不會呈現表單訊息格式的問題", "提交日期": "2022-05-26 10:06:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "376f93870331f3b05488b75f674136eac7c976ee", "commit_訊息": "[Web]A00-20220519001 修正IE加簽會加成兩次的問題", "提交日期": "2022-05-25 18:07:49", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76db9c269d9c579b4d4f27d9909c284c8328a5e5", "commit_訊息": "[表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出", "提交日期": "2022-05-25 17:01:11", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "45407b00ec35e0dac4a3ce528e7b0d04805210cf", "commit_訊息": "[Web]Q00-20220525004 修正輸入單身資料有&#加任意數字，被轉成特殊符號，會與輸入資料不符", "提交日期": "2022-05-25 16:48:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d3a9fd788e9e1e16e58d81f9e19de67fe4daab79", "commit_訊息": "[TIPTOP]Q00-20220525003 修正拋單的單身資料有中刮號會被轉成小括號，導致資料與TIPTOP不符合", "提交日期": "2022-05-25 16:32:13", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbfeb44c4307ea6400f09ce9d4780ac9c80497c2", "commit_訊息": "[系統管理工具]C01-20220524002 修正進階功能>檢查密碼如果User裡有關聯有異常的會全部撈不出來", "提交日期": "2022-05-24 19:04:27", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/util/CheckPassDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f00f82cb6b0be171a0174cfbd03708d88ebae3b9", "commit_訊息": "[Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]", "提交日期": "2022-05-25 14:01:03", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a8f914520555ca73234fbf0c7fead761f49bd875", "commit_訊息": "[Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]", "提交日期": "2022-05-25 10:57:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "92d2f06ff00910ede22c220a303bb88dfaa46d6b", "commit_訊息": "[Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤", "提交日期": "2022-05-24 17:45:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f7abe4bb2ddbd60043acc83d567a426828ab2ce3", "commit_訊息": "[內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)", "提交日期": "2022-05-23 18:19:20", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c736e90c69c8718d13d0d4ed667e37eb6cc66433", "commit_訊息": "[流程引擎]Q00-20220520003 修正流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能", "提交日期": "2022-05-20 15:42:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e867113bc82fba87d0155404b131c6579e495721", "commit_訊息": "[BPM APP]C01-20220516002 修正行動端FormUtil.disable為true時Dropdown元件顯示異常問題", "提交日期": "2022-05-18 18:47:47", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1d1f78987f576456c88b36bf6fcc7f22af35cd48", "commit_訊息": "[Web]Q00-20220518001 修正退件表單資訊與開啟的表單關連錯誤", "提交日期": "2022-05-18 10:39:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "308d711ac70660457534c193ad6b766ecfca5421", "commit_訊息": "[BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題[補]", "提交日期": "2022-05-17 15:12:44", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "111da87c3aab7f8bef5481d6b2302367813faa76", "commit_訊息": "[BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題", "提交日期": "2022-05-16 15:10:33", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6f7a2a2671881d90f6465235cc44fbbe470add9", "commit_訊息": "[流程引擎]A00-20220511001 修正使用者輸入到表單TextArea的內容在儲存表單後變成亂碼的問題", "提交日期": "2022-05-12 18:19:18", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8e3c11ac3da316033d2f492b08f924a2b75a70f3", "commit_訊息": "[Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」", "提交日期": "2022-05-12 16:55:22", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "90fcad606b409e4042b1bef7deedac699c81a377", "commit_訊息": "[流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差", "提交日期": "2022-05-12 14:36:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "491da6dbb2e32308231f696260e999bccc245e0b", "commit_訊息": "[Web]Q00-20220411005 修正使用者在絕對位置表單進行簽核時遭遇產品程式錯誤", "提交日期": "2022-05-12 14:12:31", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "feca23c9bbd4d4fa19731df8d118eaa6119f321c", "commit_訊息": "[內部]Q00-20220512001 bootstrap-table-1.18.3.js更換檔案名稱", "提交日期": "2022-05-12 11:47:55", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 4}, {"commit_hash": "e857eee9d14d7900dac6823a0c3d0e80875b2810", "commit_訊息": "[Web]Q00-20220511005 修正T100拋轉單據中有舊值的單身內容沒有顯示為紅色", "提交日期": "2022-05-11 15:03:45", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "362595d986a914d1d73bd29a134efdbac19a0843", "commit_訊息": "[內部]Q00-20220511003 調整BCL8轉檔Timeout從預設2分鐘拉長到5分鐘", "提交日期": "2022-05-11 14:44:02", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8cdb96c57f96594b3875ade082de583f64626fa3", "commit_訊息": "[流程引擎]Q00-20220506001 調整使用者「授權的流程」，流程清單筆數改為設定檔設定", "提交日期": "2022-05-11 12:07:20", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "27e790b2aac3873472de9abaeaffd2dc73806941", "commit_訊息": "[流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料", "提交日期": "2022-05-11 11:23:52", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cb108e61e86af69550310d90255b150951786861", "commit_訊息": "[流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知", "提交日期": "2022-05-11 11:10:44", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee4d7001d1002b9eb3ce8c39c6ed9b186ebabbc1", "commit_訊息": "[Web]Q00-20220511001 調整列印表單畫面簽核歷程，移除「資料代號」、「通知者」欄位", "提交日期": "2022-05-11 10:54:24", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b5e6b24502de1f68fa32e7b5a8c18577599e2e0e", "commit_訊息": "[Web]Q00-20220510001 修正IE瀏覽器開啟產品授權註冊頁面時，畫面跑版呈現異常", "提交日期": "2022-05-10 15:59:21", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "94ed24fd530b825fd409ddf9be9980e440c9f68c", "commit_訊息": "[BPM APP]C01-20220509009 修正行動端在Grid元件存在沒有繫結元件情況下點擊編輯按鈕會失敗問題", "提交日期": "2022-05-10 10:49:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4a56b5209e3ae4e8b658eb18dfcb56fa7bc2e19", "commit_訊息": "[Web]Q00-20220509002 修正授權的流程使用closeTime(結案時間)排序會報錯的問題", "提交日期": "2022-05-09 16:41:13", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b32f722e9b9f876c594e612079f917c99549eb3", "commit_訊息": "[Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果[補]", "提交日期": "2022-05-05 17:52:51", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f0ac924b4b31ad4e2cd50ed2318ed58e8e9c30b9", "commit_訊息": "[Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果", "提交日期": "2022-05-05 15:24:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e43780e2adce65ebb4eb223e1162da0309ee205", "commit_訊息": "[Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢", "提交日期": "2022-05-05 11:00:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f7bc2ccbe0e53fcce61966ae4652c9af75b0869c", "commit_訊息": "[流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]", "提交日期": "2022-06-20 16:44:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3e92541da2e6f767fafd2b39762b71fabca29cc7", "commit_訊息": "[Web]Q00-20220616001 調整待辦通知信中簽核歷程的處理者欄位移除粗體樣式", "提交日期": "2022-06-16 13:59:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e8c290af7d49313599bc9d2c487f1a1b0f0be7c", "commit_訊息": "[BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]", "提交日期": "2022-05-26 09:50:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ecab37c0ba800aff656e15697922a7905333a38", "commit_訊息": "[BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]", "提交日期": "2022-05-16 15:15:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c83c05c6362fbafaf767d8fbae44c8f085e1d43d", "commit_訊息": "[BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題", "提交日期": "2022-05-10 18:03:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5241a7266e699eae9ce7df37a8318f455eba5260", "commit_訊息": "[流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題", "提交日期": "2022-05-04 14:02:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cbb1f75cdd68334a25f6bd6afaacff11663fed80", "commit_訊息": "[BPM APP]C01-20220419001 修正移動端選項元件使用動態塞值且隱藏標籤會顯示異常問題", "提交日期": "2022-04-28 12:23:28", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99882ef3e28f0fe40d995613bfc079c3cebef6f9", "commit_訊息": "[Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色", "提交日期": "2022-04-27 16:50:57", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e21554874ff8f1a19e7913cd46373a366085b4d5", "commit_訊息": "[內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤", "提交日期": "2022-04-27 15:00:41", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b6ce7ebaa673892bb38fbbc04eb1bb7883c9bc30", "commit_訊息": "[流程引擎]A00-20220519003 修正終止前置流程時，後置流程完成流程撤銷後，前置流程出現「派送失敗」的錯誤訊息，無法正常終止", "提交日期": "2022-05-20 14:08:18", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d2588982d05156ee7d73b94fd6796fb2c74ec775", "commit_訊息": "[Web]A00-20220517004 修正調離職人員帳號更新排程預設出貨端口改成8086", "提交日期": "2022-05-19 17:04:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaJobs.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d8c4aa3f68bc813e3f9153809738802f6159904e", "commit_訊息": "[內部]Q00-20220509001修正設定檔ESS內網IP敘述的Oracle指令有誤", "提交日期": "2022-05-09 08:55:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cabb6a193ce4d7930008bd9863bf345c2e22b34", "commit_訊息": "[流程引擎]A00-20220513001 修正ProcessMapping在Oracle遺漏attachInfo欄位導致拋單失敗", "提交日期": "2022-05-13 17:23:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36049cb4b8d31b5684bcc014bc16f8b2c192f97f", "commit_訊息": "[內部]A00-20220517002 修正DB為 Oracle時，產品授權畫面無顯示模組名稱", "提交日期": "2022-05-17 11:32:25", "作者": "wayne", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}