"""
Release 查詢路由
"""
from fastapi import APIRouter, Request, Query, Form
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from pathlib import Path
import json
import re
from datetime import datetime
from typing import Optional, List, Dict, Any
import sys

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.config import BPM_RELEASE_DIR, load_projects_config

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/", response_class=HTMLResponse)
async def release_query_page(request: Request):
    """Release 查詢頁面"""
    # 載入專案設定
    projects_config = load_projects_config()
    
    # 取得可用的專案列表
    projects = list(projects_config.get("projects", {}).keys())
    
    # 取得可用的 release 檔案
    release_files = []
    if BPM_RELEASE_DIR.exists():
        for file in BPM_RELEASE_DIR.glob("*.json"):
            release_files.append({
                "name": file.stem,
                "path": str(file),
                "modified": datetime.fromtimestamp(file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            })
    
    context = {
        "request": request,
        "page_title": "產品Release記錄查詢工具",
        "projects": projects,
        "release_files": release_files
    }
    
    return templates.TemplateResponse("releases/index.html", context)

@router.post("/search", response_class=JSONResponse)
async def search_releases(
    project: str = Form(...),
    branch_filter: Optional[str] = Form(None),
    keyword_search: Optional[str] = Form(None),
    file_search: Optional[str] = Form(None),
    sort_by: str = Form("date"),
    sort_order: str = Form("desc")
):
    """搜尋 Release 記錄"""
    try:
        # 載入專案的 release 資料
        release_file = BPM_RELEASE_DIR / f"{project}_releases.json"
        
        if not release_file.exists():
            return {"error": f"找不到專案 {project} 的 release 資料"}
        
        with open(release_file, 'r', encoding='utf-8') as f:
            releases_data = json.load(f)
        
        # 過濾資料
        filtered_releases = filter_releases(
            releases_data,
            branch_filter,
            keyword_search,
            file_search
        )
        
        # 排序資料
        sorted_releases = sort_releases(filtered_releases, sort_by, sort_order)
        
        return {
            "success": True,
            "data": sorted_releases,
            "total": len(sorted_releases)
        }
        
    except Exception as e:
        return {"error": f"搜尋時發生錯誤: {str(e)}"}

def filter_releases(releases: List[Dict], branch_filter: str, keyword_search: str, file_search: str) -> List[Dict]:
    """過濾 release 資料"""
    filtered = releases
    
    # 分支過濾
    if branch_filter:
        filtered = [r for r in filtered if branch_filter.lower() in r.get("branch", "").lower()]
    
    # 關鍵字搜尋
    if keyword_search:
        keyword_lower = keyword_search.lower()
        filtered = [
            r for r in filtered 
            if keyword_lower in r.get("message", "").lower() or 
               keyword_lower in r.get("author", "").lower()
        ]
    
    # 檔案名稱搜尋
    if file_search:
        file_lower = file_search.lower()
        filtered = [
            r for r in filtered 
            if any(file_lower in f.lower() for f in r.get("files", []))
        ]
    
    return filtered

def sort_releases(releases: List[Dict], sort_by: str, sort_order: str) -> List[Dict]:
    """排序 release 資料"""
    reverse = sort_order == "desc"
    
    if sort_by == "date":
        return sorted(releases, key=lambda x: x.get("date", ""), reverse=reverse)
    elif sort_by == "branch":
        return sorted(releases, key=lambda x: x.get("branch", ""), reverse=reverse)
    elif sort_by == "author":
        return sorted(releases, key=lambda x: x.get("author", ""), reverse=reverse)
    else:
        return releases

@router.get("/branches/{project}")
async def get_project_branches(project: str):
    """取得專案的分支列表"""
    try:
        release_file = BPM_RELEASE_DIR / f"{project}_releases.json"
        
        if not release_file.exists():
            return {"error": f"找不到專案 {project} 的資料"}
        
        with open(release_file, 'r', encoding='utf-8') as f:
            releases_data = json.load(f)
        
        # 提取所有分支名稱
        branches = list(set(r.get("branch", "") for r in releases_data if r.get("branch")))
        branches.sort()
        
        return {"branches": branches}
        
    except Exception as e:
        return {"error": f"取得分支列表時發生錯誤: {str(e)}"}

@router.get("/detail/{project}/{commit_hash}")
async def get_release_detail(project: str, commit_hash: str):
    """取得特定 commit 的詳細資訊"""
    try:
        release_file = BPM_RELEASE_DIR / f"{project}_releases.json"
        
        if not release_file.exists():
            return {"error": f"找不到專案 {project} 的資料"}
        
        with open(release_file, 'r', encoding='utf-8') as f:
            releases_data = json.load(f)
        
        # 尋找指定的 commit
        for release in releases_data:
            if release.get("hash") == commit_hash:
                return {"success": True, "data": release}
        
        return {"error": "找不到指定的 commit"}
        
    except Exception as e:
        return {"error": f"取得詳細資訊時發生錯誤: {str(e)}"}
