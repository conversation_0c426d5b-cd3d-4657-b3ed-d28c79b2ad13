{"company_id": "16101660", "company_name": "穎宸", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\16101660_穎宸\\01客戶基本資料", "files": [{"filename": "[穎宸實業]連線資訊.txt", "raw_content": "正式機 ************* (00-50-56-B4-74-48)\r\nDomainName :  bpm.yin-chen.com\r\nhttps://bpm.yin-chen.com:8086/NaNaWeb\r\nadministrator/asdd53471839AA\r\nNAS IP：*************\r\nBPM/aa53471839\r\n\r\n測試機 ************* (00-50-56-B4-29-87)\r\nDomainName :  BPMtest.yin-chen.com\r\nhttps://bpmtest.yin-chen.com:8086/NaNaWeb\r\nESS *************\r\n\r\nDB *************\t\r\nsa/sa16899\r\n\r\nGuardService ************* 6666\r\n\r\n穎宸 LINE  ID\r\n帳號:<EMAIL>\r\n密碼:aa53471839AA\r\n基本ID @452ndvbw\r\n\r\n\r\n劉品妤小姐 \r\n0956-383567   \r\n<EMAIL>\r\n", "structured_data": {"nas ip": "*************", "domainname": "BPMtest.yin-chen.com", "https": "//bpmtest.yin-chen.com:8086/NaNaWeb", "username": "<EMAIL>", "password": "aa53471839AA", "host": "*************"}, "source_path": "C1.客戶維護相關\\16101660_穎宸\\01客戶基本資料\\[穎宸實業]連線資訊.txt", "file_size": 552, "encoding_used": "utf-8", "processed_at": "2025-08-26T10:46:31.186072"}], "total_files": 1, "processed_at": "2025-08-26T10:46:31.186080"}