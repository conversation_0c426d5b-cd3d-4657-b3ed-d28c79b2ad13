{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.4.2", "date": "2022-06-26 22:32:36", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.2", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.4.1", "date": "2022-06-26 22:37:36", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.1", "author": "lorenchang"}, "比較時間": "2025-07-18 11:43:42", "新增commit數量": 58, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "b61b72e8e1758e101c76a294d0e2c5510515939b", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.2", "提交日期": "2022-06-26 22:32:36", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "d43a3e2369dc397c9d1a23a1cc24a74a1dce649a", "commit_訊息": "[BPM APP]Q00-20200923006修改IMG中間層附件可依照流程設定顯示", "提交日期": "2020-09-25 09:55:30", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "81fed9be5c260e64c5bf6f3b115191c57747083e", "commit_訊息": "[BPM APP]Q00-20200923003 修正IMG待辦工作若已被處理或找不到工作時詳情表單顯示資訊錯誤的問題", "提交日期": "2020-09-24 17:48:18", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9ad810265e0aad45dc6b665c44c053256bab2e66", "commit_訊息": "Revert \"[Web]A00-20200518001 修正當部門名稱有雙引號時會導致進入發起流程和待辦流程中會無法發起或派送\"", "提交日期": "2020-09-24 15:04:49", "作者": "peng_cheng_wang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbd6aef2ab1214267f88f90fddcc9f43c044d34e", "commit_訊息": "[流程引擎]Q00-20200923002 調整Log", "提交日期": "2020-09-23 14:51:32", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22dff79a0e8e4ccced92651771a5a6e9154fcfd5", "commit_訊息": "[Web]S00-20200110001 調整登入頁語系名稱的轉換機制[補]", "提交日期": "2020-09-22 16:09:31", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "25e1b43aba0a06b46c9956671ab238ae6399e9b3", "commit_訊息": "[流程引擎]Q00-20200922001 調整排版", "提交日期": "2020-09-22 14:06:56", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "661e94cf94f59bb4eed2b9a38846dad0376f4c97", "commit_訊息": "[流程引擎]A00-20200910002 修正問題: 流程負責人監控流程以\"目前處理者\"為查詢條件，查詢結果異常", "提交日期": "2020-09-22 11:36:13", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "42b706446e80ec52faee50174e1f7ad52b427cd0", "commit_訊息": "[BPM APP]Q00-20200828002 修正絕對位置表單行動版相對位置ESS欄位為空時Grid欄位值順序異常問題[補]", "提交日期": "2020-09-21 18:37:49", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a8b0962b3fc4265040fe391d74e3b0bc115e499b", "commit_訊息": "[BPM APP]Q00-20200828002 修正絕對位置-行動相對位置表單ESS欄位為空時Grid欄位值順序異常問題", "提交日期": "2020-09-21 17:43:13", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppGridTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "f1da27edea82e12e8b67ea893fdcf418aa9d8f04", "commit_訊息": "[易飛]整合程式入版", "提交日期": "2020-09-21 11:43:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/integration/model/SysIntegrationXmlTag.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/yife/IYFGPIntegration.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/yife/IYFGPIntegrationProxy.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/yife/IYFGPIntegrationbindingStub.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/yife/IYFGPIntegrationservice.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/yife/IYFGPIntegrationserviceLocator.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@yife/conf/Process_Mapping.prsmapping", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@yife/form-default/PURI09.form", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@yife/process-default/bpmn/\\351\\200\\262\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(WorkFlowERP_PURI09).bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@yife/create/InitYiFe_ORACLE.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@yife/create/InitYiFe_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 14}, {"commit_hash": "265d6b627352787be6451183b8d773e3257af0a1", "commit_訊息": "[流程引擎]Q00-20200921001 調整排版", "提交日期": "2020-09-21 11:10:18", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe3916dbe960cad5789adc6b9d10307e2a7f3f89", "commit_訊息": "[流程引擎]A00-20200916002 修正問題: 客戶資料庫為Oracle，於追蹤流程選擇流程發起人後查詢報錯", "提交日期": "2020-09-21 09:48:37", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe1437c07461b2ec24f03c6cfd03824efd2c7edc", "commit_訊息": "[Web]A00-20200818001 調整URL登入連結ExtraLogin登入頁面顯示人員Id邏輯[補]", "提交日期": "2020-09-18 16:55:48", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "421d93f4b85e42933f334385a69434abdd7153a4", "commit_訊息": "[Web]S00-20200713001 調整功能清單中掛載模組的排序方式", "提交日期": "2020-09-18 13:51:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7244ed615b806841a411c5c91363587d242c8a14", "commit_訊息": "[Web]A00-20200902003修正Select元件有勾選必填狀況下，若有勾選最後一個TextBox編輯框，也需要驗證TextBox內容是否有值", "提交日期": "2020-09-17 18:50:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c9007cb56a55c0030b159771f9499bcc6dabec5", "commit_訊息": "[Web]A00-20200902003修正Select類型元件在有勾選最後一個TextBox編輯框的狀況，追蹤流程還是能編輯TextBox裡的內容", "提交日期": "2020-09-17 18:47:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/SelectElementTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ad8b38982b7ff149dda0b587ab88369c79d61888", "commit_訊息": "[流程引擎]A00-*********** 修正使用者透過追蹤流程切換重要流程及授權的流程會報錯", "提交日期": "2020-09-17 16:59:58", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "187b278966ec65f292028af42768a98ea74930c7", "commit_訊息": "[Web]S00-20200110001 調整登入頁語系名稱的轉換機制", "提交日期": "2020-09-17 16:33:51", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f8104cf1585f0cbcabe8d9680c854c7332aeb511", "commit_訊息": "[流程引擎]A00-20200902002 修正Invoke關卡若透過客製sessionBean加簽關卡時，流程無法解析到新增的關卡", "提交日期": "2020-09-17 11:58:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "228a17405336f19d446d7766408ff87ee758a384", "commit_訊息": "更新MSSQL 5841 updateSQL[補]", "提交日期": "2020-09-17 11:28:44", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7efb1bdc4c8866e9bf683836062c36bf15b2e5a5", "commit_訊息": "更新Oracle 5841 updateSQL[補]", "提交日期": "2020-09-17 11:24:19", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e951d476a0dd060f71433592323dbbe4df2d0c26", "commit_訊息": "修改5841 updateSQL", "提交日期": "2020-09-17 10:47:11", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.4.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b4fe390e6941cdba2e389796f46582b0177ec546", "commit_訊息": "[Web]A00-20200518001 修正當部門名稱有雙引號時會導致進入發起流程和待辦流程中會無法發起或派送", "提交日期": "2020-09-16 18:36:35", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "996f2a4d5052556eaef502899aff1e72d45f0864", "commit_訊息": "[BPM APP]C01-20200911003 修正行動端使用舊語法的客製開窗時篩選條件無法以簡體字搜尋問題", "提交日期": "2020-09-16 14:12:56", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileDatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "14e1a8d1c65e27003cc1b808b2d2f555bf77f95c", "commit_訊息": "[Web]S00-20200810002 調整首頁及待辦清單可用時間顯示機制", "提交日期": "2020-09-15 18:08:02", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "990e5c813406201d4a3a31b79f0a4b1bc6b60f1e", "commit_訊息": "[BPM APP]S00-20200914001 企業微信流程追蹤頁面UI優化", "提交日期": "2020-09-14 11:08:49", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "898a1c2acaa1f283a43d4dce06d0a28303fa5c1d", "commit_訊息": "[流程引擎]A00-20200910002 修正問題: 流程負責人監控流程以\"目前處理者\"為搜尋條件，查詢後清單上\"執行中的活動\"與實際狀況不符", "提交日期": "2020-09-14 10:40:24", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ebfa07beff1258077592a390b9bef3eccb8d4e43", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2020-09-14 10:39:17", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "81e89e1b01b68f2b5446ed92f903275c8f41cb1d", "commit_訊息": "[流程引擎]A00-20200909001 修正流程引擎的getUserDTOByLdapId出現服務異常", "提交日期": "2020-09-14 10:33:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b25a0f8fc3d1ab17db402e4c7c2fe331e865cd8e", "commit_訊息": "Revert \"[流程引擎]A00-20200910002 修正問題: 流程附則人監控流程以\"目前處理者\"下搜尋條件，進行中的關卡顯示錯誤\"", "提交日期": "2020-09-14 10:33:08", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "98898c519d0ad8c29d38386920ddd583d4becfee", "commit_訊息": "[流程引擎]A00-20200910002 修正問題: 流程附則人監控流程以\"目前處理者\"下搜尋條件，進行中的關卡顯示錯誤", "提交日期": "2020-09-11 17:49:12", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7be886d7c6d2b3884f3e05de8c8c8051e00be144", "commit_訊息": "[流程引擎]A00-20200910001 修正問題: create BamWorkAssignmentData失敗導致無法派送至下一關", "提交日期": "2020-09-11 11:36:39", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7095dd0add5b7553851451e2fe18c501f3ebf749", "commit_訊息": "[Web]Q00-20200910002 修正維護樣板產生的維護作業，若是由外部URL進入時，功能鈕會與上方title畫面重疊，無法操作", "提交日期": "2020-09-10 17:30:20", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a40b6ebf8b804b0acffbc7ca6b6eb22a3443709d", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2020-09-10 15:18:28", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "f1117cf7df039efe37549cd0c3d4f7bcb8cad76a", "commit_訊息": "[Web]Q00-20200910001 修正問題: 使用URL開啟表單畫面，重整頁面時JavaScript報錯", "提交日期": "2020-09-10 15:17:24", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "39a7505ff11b8c65341a019fedf2e97320056b1b", "commit_訊息": "[BPM APP]S00-20200910001 調整企業微信的待辦推播若已被處理過則直接導向追蹤已處理的表單畫面", "提交日期": "2020-09-10 14:56:10", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmInvokeWorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "aa34f4ba3e00432395f460e95e6a00e4e06b3ae7", "commit_訊息": "[Web]A00-20200908001 修正問題: 在小螢幕使用外部URL開啟表單時，無法顯示簽核意見", "提交日期": "2020-09-10 10:56:17", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e115f238837f428b143b04f47bdd3f393eb1d422", "commit_訊息": "[Web]A00-20200908001 修正問題: IE11使用URL開啟RWD表單畫面發生錯誤\"不正確的引數\"", "提交日期": "2020-09-09 17:25:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "79f81a5de2ac43a3ad5940ce97e1ada1c0227242", "commit_訊息": "[流程引擎]A00-20200813003 核決層級關卡參考之前一關處理人員為最高級，附件上傳後派送到核決層級關卡會異常", "提交日期": "2020-09-08 18:05:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6a40dcb99067aeb33e161da85f4b7c994bed86c9", "commit_訊息": "[流程引擎]C01-20200828003 修正: 流程負責人系統通知沒有逾時通知的內容", "提交日期": "2020-09-08 16:59:32", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c91b39f610d84430e7d6af6f3e4e22993faa5834", "commit_訊息": "[BPM APP]Q00-20200903001 修正微信在待辦詳情畫面在錯誤頁面時不應顯示浮動按鈕議題", "提交日期": "2020-09-04 16:28:54", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d451b52fc6c80bc45a3a8b5d952012a3a47600e", "commit_訊息": "[流程引擎]A00-20200901002 修正: 使用URL開啟表單時，Dropdown元件無法顯示以SQL註冊器產生的選項", "提交日期": "2020-09-04 13:52:49", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dde1652c0e6ce1b2d6a36bcd4d3d03bf0ebf6ae4", "commit_訊息": "[流程引擎]A00-20200828002 修正核決關卡參考關卡的人員滿足條件時，流程無法往後派送", "提交日期": "2020-09-04 10:30:15", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7fef0df98cbde78059776729be820a5c21d5688", "commit_訊息": "[流程引擎]A00-20200827001 修正: 在TT單後續簽核關卡刪除附件後，附件顯示異常", "提交日期": "2020-09-03 11:04:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f34178fabcf382a778b063687e91cd9400b1b63c", "commit_訊息": "[流程引擎]A00-20200803002 修正: 按流程定義退回給被代理簽核關卡功能異常", "提交日期": "2020-09-03 10:44:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36b8bf6eee4d62c7c6929ffa5c28ce868dcfd444", "commit_訊息": "[流程引擎]A00-20200825001 修正: ajax_ExtOrgAccessor.findOrgUnitsByEmpId 無法正常運作", "提交日期": "2020-08-31 14:57:26", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/UserVo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d1ee25e14c3b44cdb3d82cc370d18547da38ffe7", "commit_訊息": "[流程引擎]A00-20200731001 修正V58當取回重辦若有轉派紀錄時，會報錯無法取回", "提交日期": "2020-08-31 14:02:57", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "316a44e317d1b2f5caf5f667cfcca7d25e232e7a", "commit_訊息": "[Web]A00-20200828001 調整: 於\"已轉派工作\"顯示\"批次取回工作\"按鈕", "提交日期": "2020-08-28 15:03:39", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c19b1ed70a4c9e65580103520b9f63aea922424f", "commit_訊息": "[表單設計師]A00-20200824001 修正: 舊表單無法開啟表單資訊", "提交日期": "2020-08-28 13:41:54", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb7c1d81cc90f537aa518dfc848388f5d1465af3", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2020-08-27 16:19:54", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "8aa2ff1096869cb48ccae65f191d20d549b157e5", "commit_訊息": "[Web]Q00-20200827004 修正: 流程負責人監控流程清單以\"流程建立時間\"排序異常", "提交日期": "2020-08-27 16:19:25", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e0f33a5af9025106efa76079383d1a4e92dccd5d", "commit_訊息": "[Web]A00-20200814003 修正從關注指標重要性維護頁面進入多語系設定會出現錯誤", "提交日期": "2020-08-27 16:16:05", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "57c11f62836ba3e08059171f8cb0b499deed95a5", "commit_訊息": "[流程引擎]Q00-20200826001 修正: 流程負責人監控流程清單所呈現的\"執行中的活動\"與實際資料不符", "提交日期": "2020-08-27 16:11:57", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fcdef7b3a693015623146efba11a60f66b57fc3c", "commit_訊息": "[流程引擎]A00-20200826001 修正: 若關卡接收者為多人，流程圖詳細資料顯示異常[補]", "提交日期": "2020-08-27 14:55:57", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8cbd3c30cf1fde6402a467a12d67ab6cfc0d0ec1", "commit_訊息": "[流程引擎]A00-20200826001 修正: 若關卡接收者為多人，流程圖詳細資料顯示異常", "提交日期": "2020-08-27 13:54:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c2876b6c4bca1b469fb47aefa446955e09f9c543", "commit_訊息": "[Web]C01-20200825003 移除ModalDialog.js的openDialog的遮罩loading圖", "提交日期": "2020-08-27 10:50:56", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ModalDialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1fb8bc57816c028bacfa958cc678405ad52e07ca", "commit_訊息": "[Web]A00-20200818001 調整URL登入連結ExtraLogin登入頁面顯示人員Id邏輯", "提交日期": "2020-08-27 10:13:53", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}