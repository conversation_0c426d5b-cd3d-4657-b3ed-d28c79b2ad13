{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.4_yankey", "date": "2023-12-20 15:26:13", "message": "[流程封存]修正若特製流程存在條件時，會出現ConditionDefinition PK 重覆導致無法封存的異常", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.9.4", "date": "2024-11-22 11:40:05", "message": "Revert \"[ORGDT]C01-20240517013 調整Web化設計工具在打開後隔一段時間會發生操作錯誤問題\"", "author": "lorenchang"}, "比較時間": "2025-07-18 10:45:46", "新增commit數量": 4, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "227dfc4224c94f5dc8f2db10f5ef14d68ff3c918", "commit_訊息": "[流程封存]修正若特製流程存在條件時，會出現ConditionDefinition PK 重覆導致無法封存的異常", "提交日期": "2023-12-20 15:26:13", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34334373f9e501315f1886b7bdf798db1567569f", "commit_訊息": "[Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊", "提交日期": "2023-11-27 16:31:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e49f53ed4ff5cec61d54b3f82dc348058bf29511", "commit_訊息": "[附件擴充] 在線閱讀新增發布檔下載接口", "提交日期": "2023-11-28 17:29:23", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "797cba4e79d6e1b63d3832481dc257dc929cf8c2", "commit_訊息": "[Web] V00-20231204001 修正工作轉派使用者，原處理者無法查閱已轉派的工作清單問題", "提交日期": "2023-12-04 18:08:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}