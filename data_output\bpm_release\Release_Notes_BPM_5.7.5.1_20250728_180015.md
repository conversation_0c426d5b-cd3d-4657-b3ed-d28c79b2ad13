# Release Notes - BPM

## 版本資訊
- **新版本**: 5.7.5.1
- **舊版本**: 5.7.4.2
- **生成時間**: 2025-07-28 18:00:15
- **新增 Commit 數量**: 189

## 變更摘要

### jerry1218 (15 commits)

- **2019-03-27 10:38:43**: 調回Linux啟動conf檔
  - 變更檔案: 1 個
- **2019-03-26 15:30:10**: 調整設計工具登入頁排版
  - 變更檔案: 4 個
- **2019-03-26 14:14:13**: Q00-20190326001 A00-20190326001 修正組織設計師-工作行事曆-新增無回應問題
  - 變更檔案: 7 個
- **2019-03-20 14:33:54**: 修正流程設計師-一般關卡-處理者新增視窗遺漏的多語系
  - 變更檔案: 5 個
- **2019-03-14 17:24:30**: Q00-20190314011 openJDK議題,barcode元件在openJDK環境下無法使用,故修改第三方套件barbecue.jar改為可支援的寫法
  - 變更檔案: 1 個
- **2019-03-11 10:14:35**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-03-11 10:13:11**: 調整5751 Oracle update SQL錯誤
  - 變更檔案: 1 個
- **2019-03-08 15:06:19**: 1.移除安裝憑證連結 2.微調設計工具多語系
  - 變更檔案: 9 個
- **2019-03-07 17:34:08**: 微調build的說明語句
  - 變更檔案: 1 個
- **2019-03-05 14:52:13**: Jerry Merge : 新增設定檔開啟流程設計器 , 其他語系選項
  - 變更檔案: 1 個
- **2019-03-05 14:21:40**: Jerry Merge : 修正設計器應用程式多開(3個以上)失敗問題
  - 變更檔案: 8 個
- **2019-02-27 16:53:20**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-02-27 16:51:45**: Jerry Merge : 修正launch4j錯誤
  - 變更檔案: 9 個
- **2019-02-27 14:21:05**: Jerry Merge - 取消build efgp-pdfViewer
  - 變更檔案: 2 個
- **2019-02-27 10:55:51**: Jerry Merge : OpenJDK merge
  - 變更檔案: 898 個

### waynechang (16 commits)

- **2019-03-27 09:48:24**: Q00-20190321005 調整update TIPTOP restful的語法
  - 變更檔案: 6 個
- **2019-03-26 16:08:28**: Q00-20190321005 調整TT出貨的流程將sessionBean調整為restful
  - 變更檔案: 1 個
- **2019-03-26 15:38:22**: Q00-20190321005 提供回寫TT的restful接口的updateSQL
  - 變更檔案: 2 個
- **2019-03-26 15:28:58**: Q00-20190321005 因應TT多主機sessionBean回寫，增加回寫TT的restful接口
  - 變更檔案: 2 個
- **2019-03-26 15:20:17**: S00-20190213001 增加log-QRCode簽核時，參考系統參數設定驗證時間
  - 變更檔案: 1 個
- **2019-03-14 14:13:18**: Q00-20190314006 修正SYNC_ISO 文件匯入後，透過文管首頁閱讀檔案開啟時報錯，無法閱讀檔案
  - 變更檔案: 1 個
- **2019-03-13 11:46:26**: C01-20181220005 修正通知信夾帶附件若檔名超過10字會亂碼
  - 變更檔案: 1 個
- **2019-03-08 15:10:47**: 修正ISO文件變更單選擇文件沒有ISOType時會報錯
  - 變更檔案: 1 個
- **2019-03-07 10:34:42**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-03-07 10:34:15**: Q00-20190307003 調整樹狀開窗時，須將父視窗的資料一併帶回至開窗頁面
  - 變更檔案: 1 個
- **2019-02-26 14:26:57**: A00-20190215002 修正T100送簽單據後，關卡解析失敗回傳失敗的XML，但流程仍然產生
  - 變更檔案: 1 個
- **2019-02-26 14:12:07**: A00-20190219002 修正T100發起RWD表單有SubTab元件時會報錯-增加其他整合的例外修正
  - 變更檔案: 2 個
- **2019-02-25 17:25:48**: A00-20190219002 修正T100發起RWD表單有SubTab元件時會報錯
  - 變更檔案: 1 個
- **2019-02-25 14:15:18**: S00-20190213001 增加QRCode簽核時，參考系統參數設定驗證時間
  - 變更檔案: 10 個
- **2019-02-22 16:09:49**: C01-20190123004 ISO文件一覽表的ISO文件階層無法顯示
  - 變更檔案: 4 個
- **2019-01-30 09:32:17**: C01-20190123004 修正關卡退回重辦後，後續派送關卡新增為兩個代辦
  - 變更檔案: 1 個

### ChinRong (47 commits)

- **2019-03-26 09:07:12**: Q00-20190322002 <二次修正> 還原誤簽上去的程式碼
  - 變更檔案: 1 個
- **2019-03-22 20:31:39**: Q00-20190322002 推播中間層簽核的待辦事項打開來都會是"找不到工作項目"
  - 變更檔案: 4 個
- **2019-03-22 20:09:58**: Q00-20190322001 同意派送RESTful服務如果有多個關卡活動中且都為同處理人，呼叫接口後會將任一關卡往下派送。
  - 變更檔案: 2 個
- **2019-03-22 18:29:58**: Q00-20190321003 調整取消訂閱多語系
  - 變更檔案: 6 個
- **2019-03-22 16:12:04**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-03-22 16:09:54**: Q00-20190320002 修正表單的FormOpen階段載入資料並更新Grid,會因為Grid還沒加載完成而導致錯誤。
  - 變更檔案: 2 個
- **2019-03-22 11:44:02**: Q00-20190315008 <二次修正>修正因調整listreader造成IMG處理的流程過濾功能無法使用的問題
  - 變更檔案: 1 個
- **2019-03-22 10:02:35**: Q00-20190321007 修正響應式表單設計器使用複製元件，行動版設計器沒有效果
  - 變更檔案: 1 個
- **2019-03-22 09:38:41**: Q00-20190321006 修正響應式表單設計器復原上一動後，行動版相對位置設計器的欄位模版會無法新增。
  - 變更檔案: 1 個
- **2019-03-22 09:36:30**: Q00-20190321008 修正一般表單設計器使用Ctrl+z復原上一動，行動版相對位置設計器的欄位模版會出現好幾個
  - 變更檔案: 1 個
- **2019-03-21 20:13:37**: Q00-20190321002 調整腳本樣本行動版"外部網址開窗"
  - 變更檔案: 6 個
- **2019-03-12 19:46:47**: Q00-20190312005 調整IMG首頁統計元件智能示警多語系
  - 變更檔案: 1 個
- **2019-03-12 19:42:51**: Q00-20190307002 鼎捷移動整合頁面的統記元件設定中，新增"智能快簽"選項。
  - 變更檔案: 3 個
- **2019-03-12 17:10:21**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-03-12 17:08:08**: Q00-20190225004 詳情表單中顯示流程頁面內的簡易流程圖中的任何連結都不應該有任何動作
  - 變更檔案: 1 個
- **2019-03-12 16:39:43**: Q00-20190221002 修正IMG推播如果表單設定詳情簽核時，直連表單畫面一片空白
  - 變更檔案: 1 個
- **2019-03-12 11:14:49**: Q00-20190222002 修正統計元件設定再新增第四個類型時會無法新增，並提示"統計組件類型不能重複"的問題
  - 變更檔案: 1 個
- **2019-03-12 10:15:52**: Q00-20190311001 將行動版"我的關注"統計筆數調整成與PC一致
  - 變更檔案: 1 個
- **2019-03-11 13:58:04**: C01-20190305004 修正IOS日期元件顯示時間後，在中間層會多顯示秒數且日期跟時間中間會多一個T
  - 變更檔案: 3 個
- **2019-03-08 14:22:10**: 補上因Git Merge異常遺失的多語系
  - 變更檔案: 2 個
- **2019-03-08 14:19:25**: 新增管理員移動消息訂閱管理頁面
  - 變更檔案: 11 個
- **2019-03-08 14:17:20**: 微調build的說明語句
  - 變更檔案: 1 個
- **2019-03-08 14:16:22**: 調整管理員維護行動流程消息訂閱的功能
  - 變更檔案: 3 個
- **2019-03-08 14:15:25**: 新增管理員維護行動流程消息訂閱的功能
  - 變更檔案: 10 個
- **2019-03-07 20:06:14**: 新增管理員移動消息訂閱管理頁面
  - 變更檔案: 11 個
- **2019-03-07 10:19:41**: 調整IMG統計元件"我的關注"筆數計算邏輯
  - 變更檔案: 1 個
- **2019-03-06 17:42:04**: 調整行動版"我的關注"多語系
  - 變更檔案: 3 個
- **2019-03-06 17:10:50**: 新增行動版"我的關注"功能
  - 變更檔案: 8 個
- **2019-03-06 10:35:41**: 註解上個記錄中，關於行動版"我的關注"功能的程式碼
  - 變更檔案: 1 個
- **2019-03-06 10:18:48**: 調整IMG重要流程可用字段接口
  - 變更檔案: 7 個
- **2019-02-27 10:39:57**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-02-27 10:39:27**: 新增img草稿清單二期接口
  - 變更檔案: 5 個
- **2019-02-26 13:49:11**: 隱藏IMG追蹤流程快速簽核進階按鈕
  - 變更檔案: 1 個
- **2019-02-26 13:47:44**: Q00-20190226001 修正表單設計中間層標記多欄位時，最右邊標記的欄位在IMG中間層上不會顯示
  - 變更檔案: 1 個
- **2019-02-22 17:21:29**: 修正鼎捷移動快速簽核同意按鈕點擊沒反應的問題
  - 變更檔案: 1 個
- **2019-02-22 17:02:13**: 新增預測關卡、上一關卡撥電話的進階按鈕與提示訊息
  - 變更檔案: 8 個
- **2019-02-22 17:01:17**: 修正恢復訂閱管理如果清單中有"只支援行動簽核的流程"流程名稱會顯示為空
  - 變更檔案: 1 個
- **2019-02-21 17:47:48**: 修正鼎捷移動快速簽核預計關卡聯絡人Label沒有多語系問題
  - 變更檔案: 1 個
- **2019-02-21 16:52:02**: 補上表單取消訂閱畫面的多語系
  - 變更檔案: 2 個
- **2019-02-21 16:48:24**: 調整表單取消訂閱畫面的樣式
  - 變更檔案: 12 個
- **2019-02-21 09:42:48**: 新增行動流程恢復訂閱管理頁面
  - 變更檔案: 10 個
- **2019-02-20 18:15:55**: C01-20190218003 修正產品開窗預設值過濾組織名稱沒效果的問題
  - 變更檔案: 1 個
- **2019-02-19 11:16:20**: 補上鼎捷移動快速簽核預計關卡資訊的多語系
  - 變更檔案: 3 個
- **2019-02-19 10:03:44**: 將01/18註解的功能還原(遺漏部分)
  - 變更檔案: 1 個
- **2019-02-13 15:58:49**: 新增IMG行動表單上取消訂閱推播功能
  - 變更檔案: 16 個
- **2019-02-13 15:47:28**: 將01/18註解的功能還原
  - 變更檔案: 12 個
- **2019-02-12 14:24:38**: C01-20190212001 修正附件管理按鈕在IPhone XR,IPhone XS上跑版問題
  - 變更檔案: 1 個

### yamiyeh10 (39 commits)

- **2019-03-25 18:52:27**: Q00-20190325002 IMG的智能示警在有處理記錄時的彈出視窗提示內容調整多語系
  - 變更檔案: 2 個
- **2019-03-25 18:46:26**: Q00-20190325005 IMG的加入重要流程功能註解
  - 變更檔案: 2 個
- **2019-03-21 20:09:21**: Q00-20190315007 二次修正因IMG傳遞的processId有引號導致異常
  - 變更檔案: 1 個
- **2019-03-21 18:41:10**: Q00-20190320001 修正App在Grid資料欄位是純數字時會有呼叫失敗問題
  - 變更檔案: 2 個
- **2019-03-21 16:17:40**: Q00-20190321004 修正IMG加入重要流程重複加入時的提示訊息多語系異常
  - 變更檔案: 2 個
- **2019-03-21 15:53:53**: Q00-20190221001 調整發送郵件內容到行動版裝置的機制
  - 變更檔案: 2 個
- **2019-03-20 18:02:17**: Q00-20190315007 修正追蹤流程RESTful服務(取清單接口、統計接口、總計接口)服務異常
  - 變更檔案: 3 個
- **2019-03-20 11:19:20**: 移除多餘的log
  - 變更檔案: 1 個
- **2019-03-20 11:12:29**: Q00-20190315006 修正待辦清單RESTful服務取得的行動版直連表單網址打開來一片空白
  - 變更檔案: 1 個
- **2019-03-19 15:31:30**: Q00-20190315002 修正同意派送RESTFul在參數資料內容有誤時不是預期的回應格式
  - 變更檔案: 1 個
- **2019-03-19 10:27:57**: Q00-20190125002 修正App表單元件點擊清除按鈕會觸發取消焦點功能
  - 變更檔案: 1 個
- **2019-03-18 17:30:12**: Q00-20190315008 修正已轉派流程總計RESTful接口platform參數傳入"mobile"沒效果
  - 變更檔案: 1 個
- **2019-03-18 17:27:16**: Q00-20190314016 調整IMG快速簽核與詳情的預計關卡過濾服務與通知關卡
  - 變更檔案: 1 個
- **2019-03-15 17:37:19**: Q00-20190314010 將IMG預測關卡的流程有設定條件運算式註解打開
  - 變更檔案: 1 個
- **2019-03-15 10:58:21**: Q00-20190314005 調整移動消息訂閱管理的多語系
  - 變更檔案: 2 個
- **2019-03-14 19:24:55**: Q00-20190221005 調整App在服務重啟後若沒有先登入過BPM，直接登入IMG的APP會有403錯誤
  - 變更檔案: 3 個
- **2019-03-14 17:46:37**: Q00-20190225002 IMG快速簽核畫面調整若欄位無資料時不顯示其區塊
  - 變更檔案: 1 個
- **2019-03-14 15:24:33**: Q00-20190314005 調整個人移動消息訂閱管理的多語系
  - 變更檔案: 3 個
- **2019-03-13 19:34:02**: Q00-*********** 調整IMG快速簽核畫面的上一關資訊過濾通知與服務關卡
  - 變更檔案: 1 個
- **2019-03-12 17:05:20**: Q00-20190312003 修正IMG簽核歷程在代處理時人員名稱的圓圈會跑版
  - 變更檔案: 1 個
- **2019-03-12 16:04:03**: Q00-20190311003 調整移動端的JSP移掉未使用的js
  - 變更檔案: 16 個
- **2019-03-12 11:12:11**: Q00-20190225003 增加IMG撥電話的直連畫面標題的多語系
  - 變更檔案: 2 個
- **2019-03-12 11:10:05**: Q00-20190225003 調整IMG撥電話的直連畫面內容符合多語系
  - 變更檔案: 4 個
- **2019-03-11 15:50:07**: Q00-20190225006 調整行動版樣版中的初始化Dropdown與Listbox元件方法
  - 變更檔案: 2 個
- **2019-03-11 11:47:08**: Q00-20190221004 調整App<57>Grid明細欄位與接收資料欄位數對不上時顯示空值
  - 變更檔案: 3 個
- **2019-03-11 10:05:55**: Q00-20190308001 補上調整的多語系
  - 變更檔案: 2 個
- **2019-03-11 10:02:58**: Q00-20190308001 修正IMG智能示警的處理記錄在英文語系時彈出視窗會跑版
  - 變更檔案: 1 個
- **2019-03-11 09:57:31**: Q00-20190308002 修正IMG智能示警的處理紀錄頁面無法完整顯示內容
  - 變更檔案: 2 個
- **2019-03-11 09:51:11**: Q00-20190308003 智能示警的處理紀錄資訊排序方式改為降冪
  - 變更檔案: 1 個
- **2019-03-07 20:32:09**: IMG的快速簽核增加智能示警歷史處理記錄畫面
  - 變更檔案: 9 個
- **2019-03-07 09:29:25**: 調整IMG可發起流程可用字段接口
  - 變更檔案: 2 個
- **2019-03-06 18:26:50**: 新增IMG智能示警歷史處理記錄畫面
  - 變更檔案: 11 個
- **2019-03-06 09:45:45**: 調整IMG所有可發起流程增加流程名稱蒐尋
  - 變更檔案: 1 個
- **2019-03-05 10:25:30**: C01-20190225001 修正企業微信在直接進入表單畫面時會因使用者尚未登入而導致畫面異常問題
  - 變更檔案: 7 個
- **2019-02-22 10:15:54**: 新增BPM撥號畫面
  - 變更檔案: 5 個
- **2019-02-22 09:16:51**: 調整IMG查看附件方式 --改使用鼎捷移動提供的開窗方式預覽附件檔案
  - 變更檔案: 6 個
- **2019-02-20 14:54:51**: 調整最常處理流程的功能 1.調整篩選API增加處理工作欄位 2.常處理流程增加優先處理工作欄位 3.判斷用戶是否為部門主管功能改用public 4.增加常處理流程使用的多語系
  - 變更檔案: 10 個
- **2019-02-14 09:52:44**: 將01/19註解的功能還原 1.IMG待辦詳情表單畫面轉由他人處理功能 2.IMG待辦詳情表單畫面簽核歷程上一關卡與當前關卡功能
  - 變更檔案: 4 個
- **2019-02-12 14:15:32**: 更新ESS表單 --HR同仁協助調整表單內容 --將單身明細的欄位名稱補上與表單上相同的欄位名稱
  - 變更檔案: 28 個

### walter_wu (15 commits)

- **2019-03-25 15:43:56**: Q00-20190325006 修正離職人員工作轉派 如果未選接收者 就按下批次轉派 開出來的選人窗會請洽系統管理員
  - 變更檔案: 1 個
- **2019-03-15 18:23:49**: Q00-20190315010 修正資料庫是Oracle的時候  使用流程監控資訊封存  不管是封存資料或是解封存資料都會報錯
  - 變更檔案: 1 個
- **2019-03-13 17:45:32**: S00-20171031001 補更新多語系
  - 變更檔案: 1 個
- **2019-03-13 11:30:36**: C01-20190311006 修正因為prepare Statement沒關導致cursor超過限制
  - 變更檔案: 1 個
- **2019-03-12 17:45:25**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-03-12 17:39:34**: Q00-20190312004 修正C01-20190102002修改後Oracle會報錯
  - 變更檔案: 1 個
- **2019-02-27 18:13:44**: A00-20190111001 修正使用WorkflowService.invokeProcess發起流程內含附件 清單上不會有附件的迴紋針
  - 變更檔案: 1 個
- **2019-02-27 15:20:28**: S00-20171031001 修正上筆修改後 Script沒有成功更新
  - 變更檔案: 1 個
- **2019-02-18 19:17:23**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-02-18 18:17:13**: A00-20190128001 修正TextArea設定高度與必填 高度會失效
  - 變更檔案: 1 個
- **2019-02-15 15:01:46**: A00-20190129004 修正TextBox的唯讀，只要設定顯示小數點後N位數就會發生  顏色無法正確顯示
  - 變更檔案: 1 個
- **2019-02-14 10:45:49**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-02-13 18:38:14**: 補修正C01-20190107001 C01-20190109005
  - 變更檔案: 2 個
- **2019-01-30 18:36:46**: C01-20190107002 補上如果是自動簽核 列印時 狀態已處理之後面沒有(自動)兩字
  - 變更檔案: 2 個
- **2019-01-29 19:00:19**: C01-20190107001 修正Grid資料過多會蓋到簽核意見的問題
  - 變更檔案: 2 個

### 施翔耀 (12 commits)

- **2019-03-22 16:08:58**: <V57> C01-20181113002 修正 :ESS單上按儲存草稿，會跳出呼叫AppForm網路服務失敗的錯誤訊息
  - 變更檔案: 2 個
- **2019-03-20 14:55:38**: C01-20170809003 修正:組織同步執行順序異常,導致新增部門時會找不到部門核決層級
  - 變更檔案: 1 個
- **2019-03-20 14:20:53**: <V57>A00-20190130003 調整:新增組織同步時,因DB資料被刪時可以識別的錯誤訊息
  - 變更檔案: 1 個
- **2019-03-18 17:04:37**: C01-20190123004 二次修正 : ISO文件一覽表的ISO文件階層無法顯示
  - 變更檔案: 1 個
- **2019-03-15 17:26:42**: 補上缺少的 QRCode多語系
  - 變更檔案: 1 個
- **2019-03-15 17:00:11**: 修正 : E10表單同步異常
  - 變更檔案: 1 個
- **2019-03-08 09:42:10**: <V57>S00-20180908001 修正 :流程預先解析人員 如果是離職加上符號註記,但當連續是兩個服務關卡時會有異常
  - 變更檔案: 1 個
- **2019-02-25 15:19:32**: <V57>Q00-20190223001 修正 :文件制作索引失敗,因缺少引用的jar檔
  - 變更檔案: 1 個
- **2019-02-20 15:24:57**: 二次修正 <V57>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤
  - 變更檔案: 1 個
- **2019-02-20 15:14:53**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-02-20 15:14:27**: <V57>A00-20180725001 修正:ESS流程只能加簽通知關卡，但修改模式下確可以選到會辦
  - 變更檔案: 1 個
- **2019-02-20 14:39:32**: <V57>C01-20190213001  調整 :移除多表單自動儲存功能 ，該功能不符合應用場景，且後端容易發生物件修改時報出 CannotLock的錯誤
  - 變更檔案: 1 個

### Catherine (7 commits)

- **2019-03-22 11:50:19**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-03-22 11:49:39**: A00-20190215005 修正千分位顯示異常問題(2)
  - 變更檔案: 2 個
- **2019-03-08 16:59:03**: 加上 (e) 變成 catch (e) {}
  - 變更檔案: 1 個
- **2019-03-08 12:51:24**: C01-20190128001 內部主機現有.form壓縮檔 放入出貨光碟only
  - 變更檔案: 2 個
- **2019-02-27 18:25:56**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-02-27 18:24:48**: 刪除無意義檔案only
  - 變更檔案: 2 個
- **2019-02-27 18:02:25**: A00-20190226002 doResize導致錯誤修正
  - 變更檔案: 1 個

### pinchi_lin (19 commits)

- **2019-03-22 10:39:37**: Q00-20190215002 修正web表單設計師響應式表單在APP未啟用時一樣會作行動版表單初始化問題
  - 變更檔案: 3 個
- **2019-03-22 10:37:22**: Q00-20190215001 修正web表單設計師絕對位置表單在APP未啟用時一樣會作行動版表單初始化問題
  - 變更檔案: 3 個
- **2019-03-20 11:43:24**: 調整IMG智能快簽與重要流程功能
  - 變更檔案: 4 個
- **2019-03-18 17:51:44**: Q00-20190307001 新增取未結案處理的流程的接口
  - 變更檔案: 2 個
- **2019-03-15 18:48:57**: Q00-20190215004 調整簽核流設計師中行動版表單權限控管會卡控APP序號是否註冊或過期
  - 變更檔案: 2 個
- **2019-03-15 16:21:05**: Q00-20190215003 調整簽核流設計師中的支援手持裝置選項卡控APP序號註冊或過期
  - 變更檔案: 1 個
- **2019-03-14 12:09:18**: Q00-20190312001 停用管理員的管理消息訂閱頁面的全選功能
  - 變更檔案: 1 個
- **2019-03-14 10:09:49**: Q00-*********** 調整取最近含有示警訊息的工作清單功能效能問題
  - 變更檔案: 8 個
- **2019-03-11 18:47:43**: 調整快速簽核中加入重要流程會重複問題
  - 變更檔案: 3 個
- **2019-03-11 11:41:16**: 調整IMG詳情表單中可作加入重要流程功能
  - 變更檔案: 2 個
- **2019-03-08 19:06:32**: 新增IMG詳情表單中可作加入重要流程功能
  - 變更檔案: 8 個
- **2019-03-07 17:13:34**: 調整管理員維護行動流程消息訂閱的功能
  - 變更檔案: 3 個
- **2019-03-07 15:17:53**: 新增管理員維護行動流程消息訂閱的功能
  - 變更檔案: 10 個
- **2019-03-05 16:32:44**: 新增取最近含有示警訊息的工作清單功能
  - 變更檔案: 5 個
- **2019-02-21 17:20:35**: 調整最常處理流程的功能
  - 變更檔案: 5 個
- **2019-02-19 18:44:09**: 調整最常處理流程的功能
  - 變更檔案: 8 個
- **2019-02-19 09:58:26**: 將01/19註解的功能還原(遺漏部分)
  - 變更檔案: 1 個
- **2019-02-13 14:00:46**: 新增IMG推播消息取消訂閱功能
  - 變更檔案: 16 個
- **2019-02-13 10:36:29**: 將01/19註解的功能還原
  - 變更檔案: 9 個

### 劉建德 (9 commits)

- **2019-03-18 17:13:44**: Q0020190314007 智能快簽功能的進階篩選功能沒效果。
  - 變更檔案: 1 個
- **2019-03-15 16:55:24**: Q0020190314008 智能快簽功能的進階搜尋功能沒效果。
  - 變更檔案: 1 個
- **2019-03-15 16:53:41**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-03-15 16:00:14**: Q00-20190314013 鼎捷移動快速簽核依照工作權限動態產生的按鈕中，沒有"表單詳情"按鈕。
  - 變更檔案: 3 個
- **2019-03-07 20:16:06**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 1 個
- **2019-03-07 20:14:34**: 新增加入重要流程多語系
  - 變更檔案: 2 個
- **2019-03-07 20:12:52**: 新增加入重要流程 RESTful 服務API
- **2019-03-07 20:12:52**: 新增加入重要流程 RESTful 服務API
  - 變更檔案: 5 個
- **2019-03-07 15:08:35**: 使用者可以在流程簽核時把流程加入重要流程
  - 變更檔案: 5 個

### yanann_chen (6 commits)

- **2019-03-11 09:37:41**: A00-20190215005 修正千分位顯示異常問題
  - 變更檔案: 1 個
- **2019-03-08 15:44:22**: C01-20180807001 更新WfERP form-default
  - 變更檔案: 80 個
- **2019-03-06 16:46:38**: C01-20190214003 第三次修正
  - 變更檔案: 1 個
- **2019-03-06 09:28:22**: C01-20190214003 補修正
  - 變更檔案: 1 個
- **2019-02-21 11:21:39**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-02-21 11:05:42**: C01-20190214003 修正在 追蹤流程=>已轉派的工作 中，查看表單資料右上方沒有"顯示流程"的按鈕
  - 變更檔案: 1 個

### BPM (4 commits)

- **2019-03-06 15:12:08**: [在移動App上,可以從待辦中把流程加入重要流程] Ajax服務及RESTful服務
  - 變更檔案: 5 個
- **2019-02-22 09:52:11**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 1 個
- **2019-02-22 09:50:11**: 修正過濾中間層按鈕的錯誤
  - 變更檔案: 1 個
- **2019-02-18 17:00:32**: 用戶可以透過智能快簽的簽核歷程撥打電話給關卡處理人,中間層可以透過參數控制不撈出表單資料
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. 調回Linux啟動conf檔
- **Commit ID**: `83920ffc7438a1397d485e04bba1e780a8cda2be`
- **作者**: jerry1218
- **日期**: 2019-03-27 10:38:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf`

### 2. Q00-20190321005 調整update TIPTOP restful的語法
- **Commit ID**: `3721ed24f54410ee29a984479ba8d9f57678a8c5`
- **作者**: waynechang
- **日期**: 2019-03-27 09:48:24
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_1_Check.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_2.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_1_Check.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_2.sql`

### 3. Q00-20190321005 調整TT出貨的流程將sessionBean調整為restful
- **Commit ID**: `a58ba23760e61099ee089954c75d8a0ced464c08`
- **作者**: waynechang
- **日期**: 2019-03-26 16:08:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/bpmn/5.25/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.bpmn"`

### 4. Q00-20190321005 提供回寫TT的restful接口的updateSQL
- **Commit ID**: `f0bcf95b78c59de3730160ff21ffecc60a6cd3cf`
- **作者**: waynechang
- **日期**: 2019-03-26 15:38:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_1.sql`

### 5. 調整設計工具登入頁排版
- **Commit ID**: `005983824e8db3f5a6a9963257e6882d1fb635d9`
- **作者**: jerry1218
- **日期**: 2019-03-26 15:30:10
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_vi_VN.properties`

### 6. Q00-20190321005 因應TT多主機sessionBean回寫，增加回寫TT的restful接口
- **Commit ID**: `3e6890ebf5259c7c961470c8c6671c27c1767518`
- **作者**: waynechang
- **日期**: 2019-03-26 15:28:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/TIPTOP.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/TIPTOPMgr.java`

### 7. S00-20190213001 增加log-QRCode簽核時，參考系統參數設定驗證時間
- **Commit ID**: `e58dedd8ad5c8a7ffea38cd805844890f3525c40`
- **作者**: waynechang
- **日期**: 2019-03-26 15:20:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/QRCodeLoginCache.java`

### 8. Q00-20190326001 A00-20190326001 修正組織設計師-工作行事曆-新增無回應問題
- **Commit ID**: `e95e3ed3fcc0d765a18bcbae1c8ac90f584774a7`
- **作者**: jerry1218
- **日期**: 2019-03-26 14:14:13
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/JCalendar/jcalendar-1.3.2.jar`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/lib/JCalendar/jcalendar.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/main/CMPanel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-tool-entry/lib/JCalendar/jcalendar.jar`

### 9. Q00-20190322002 <二次修正> 還原誤簽上去的程式碼
- **Commit ID**: `1e139bdd1664ba2b9ab551ea0a844b7ef16326cc`
- **作者**: ChinRong
- **日期**: 2019-03-26 09:07:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 10. Q00-20190325002 IMG的智能示警在有處理記錄時的彈出視窗提示內容調整多語系
- **Commit ID**: `d24596c9d4d81db957c7d2fe39225b785e4674b2`
- **作者**: yamiyeh10
- **日期**: 2019-03-25 18:52:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 11. Q00-20190325005 IMG的加入重要流程功能註解
- **Commit ID**: `18cb3a1a9259be1abf7d91ebf4a4dc245d12012d`
- **作者**: yamiyeh10
- **日期**: 2019-03-25 18:46:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`

### 12. Q00-20190325006 修正離職人員工作轉派 如果未選接收者 就按下批次轉派 開出來的選人窗會請洽系統管理員
- **Commit ID**: `fea7ca61d9e1380117c1a71e06cb4aff813cc339`
- **作者**: walter_wu
- **日期**: 2019-03-25 15:43:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java`

### 13. Q00-20190322002 推播中間層簽核的待辦事項打開來都會是"找不到工作項目"
- **Commit ID**: `00243f9e0c898fa31ca04d0ea19e36cb129c6538`
- **作者**: ChinRong
- **日期**: 2019-03-22 20:31:39
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 14. Q00-20190322001 同意派送RESTful服務如果有多個關卡活動中且都為同處理人，呼叫接口後會將任一關卡往下派送。
- **Commit ID**: `16b2be13e3814640b61352c42196872c5c0a20b9`
- **作者**: ChinRong
- **日期**: 2019-03-22 20:09:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`

### 15. Q00-20190321003 調整取消訂閱多語系
- **Commit ID**: `7d7c5424a40fde403675f2d1733b4084ad6ffb54`
- **作者**: ChinRong
- **日期**: 2019-03-22 18:29:58
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 16. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `6742f3a45e0ee2c616d6b65f01ec98d4c2f2870d`
- **作者**: ChinRong
- **日期**: 2019-03-22 16:12:04
- **變更檔案數量**: 0

### 17. Q00-20190320002 修正表單的FormOpen階段載入資料並更新Grid,會因為Grid還沒加載完成而導致錯誤。
- **Commit ID**: `8cef50ea9a4f4167293f7d90e15b384f98c6fe77`
- **作者**: ChinRong
- **日期**: 2019-03-22 16:09:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 18. <V57> C01-20181113002 修正 :ESS單上按儲存草稿，會跳出呼叫AppForm網路服務失敗的錯誤訊息
- **Commit ID**: `5b1fc32f37db407f45c0c7d0670c81ac926a06bf`
- **作者**: 施翔耀
- **日期**: 2019-03-22 16:08:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 19. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `4a30e75131b0afe9b0410aba837aa4243b94f54a`
- **作者**: Catherine
- **日期**: 2019-03-22 11:50:19
- **變更檔案數量**: 0

### 20. A00-20190215005 修正千分位顯示異常問題(2)
- **Commit ID**: `54311feb9d78a8d07754016cb68839770b130c74`
- **作者**: Catherine
- **日期**: 2019-03-22 11:49:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 21. Q00-20190315008 <二次修正>修正因調整listreader造成IMG處理的流程過濾功能無法使用的問題
- **Commit ID**: `13e5aab9e73fd8b511b9ac40bc6edbfefb855760`
- **作者**: ChinRong
- **日期**: 2019-03-22 11:44:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 22. Q00-20190215002 修正web表單設計師響應式表單在APP未啟用時一樣會作行動版表單初始化問題
- **Commit ID**: `315fabb888a99acf01f1f6b8aba7d3f1ee0ed931`
- **作者**: pinchi_lin
- **日期**: 2019-03-22 10:39:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 23. Q00-20190215001 修正web表單設計師絕對位置表單在APP未啟用時一樣會作行動版表單初始化問題
- **Commit ID**: `8596097b8c1a2196821cafe4e6e0ce10bd32d92c`
- **作者**: pinchi_lin
- **日期**: 2019-03-22 10:37:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 24. Q00-20190321007 修正響應式表單設計器使用複製元件，行動版設計器沒有效果
- **Commit ID**: `23de70e0b12609c522d5d53e8a52589d8b878ce6`
- **作者**: ChinRong
- **日期**: 2019-03-22 10:02:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 25. Q00-20190321006 修正響應式表單設計器復原上一動後，行動版相對位置設計器的欄位模版會無法新增。
- **Commit ID**: `b3ca4bd0d67df3033d6a236a467766b36e376d48`
- **作者**: ChinRong
- **日期**: 2019-03-22 09:38:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 26. Q00-20190321008 修正一般表單設計器使用Ctrl+z復原上一動，行動版相對位置設計器的欄位模版會出現好幾個
- **Commit ID**: `f88c084575bb7c410eab6ebc84d9003df57fb356`
- **作者**: ChinRong
- **日期**: 2019-03-22 09:36:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 27. Q00-20190321002 調整腳本樣本行動版"外部網址開窗"
- **Commit ID**: `87a287d8cc8921fb4c1c26de3ddff3665506f288`
- **作者**: ChinRong
- **日期**: 2019-03-21 20:13:37
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql`

### 28. Q00-20190315007 二次修正因IMG傳遞的processId有引號導致異常
- **Commit ID**: `b1a15c0a0977722cf7ffcce12d435776087de7e0`
- **作者**: yamiyeh10
- **日期**: 2019-03-21 20:09:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

### 29. Q00-20190320001 修正App在Grid資料欄位是純數字時會有呼叫失敗問題
- **Commit ID**: `4388fef62bc892877418089ce146bb947a3054df`
- **作者**: yamiyeh10
- **日期**: 2019-03-21 18:41:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 30. Q00-20190321004 修正IMG加入重要流程重複加入時的提示訊息多語系異常
- **Commit ID**: `df8a1cbfb968e1da7021c51d4d2e771fa8ef0ea6`
- **作者**: yamiyeh10
- **日期**: 2019-03-21 16:17:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 31. Q00-20190221001 調整發送郵件內容到行動版裝置的機制
- **Commit ID**: `776ebaf8711bdd7a6efaa5cba8cd48cec9eb5471`
- **作者**: yamiyeh10
- **日期**: 2019-03-21 15:53:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`

### 32. Q00-20190315007 修正追蹤流程RESTful服務(取清單接口、統計接口、總計接口)服務異常
- **Commit ID**: `34f0f29650c03d9eb17b6dc7630cf35b7e0f0c98`
- **作者**: yamiyeh10
- **日期**: 2019-03-20 18:02:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`

### 33. C01-20170809003 修正:組織同步執行順序異常,導致新增部門時會找不到部門核決層級
- **Commit ID**: `e4e0410da2b2b838d1a236cc5a0832dcfdb7e5f4`
- **作者**: 施翔耀
- **日期**: 2019-03-20 14:55:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ws/ImportOrgDocBuilder.java`

### 34. 修正流程設計師-一般關卡-處理者新增視窗遺漏的多語系
- **Commit ID**: `e27c713379f573088d8d0d830d87f5c0799bc99a`
- **作者**: jerry1218
- **日期**: 2019-03-20 14:33:54
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_zh_TW.properties`

### 35. <V57>A00-20190130003 調整:新增組織同步時,因DB資料被刪時可以識別的錯誤訊息
- **Commit ID**: `6325423233053aad47ef732adf8a0c9d5035173a`
- **作者**: 施翔耀
- **日期**: 2019-03-20 14:20:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 36. 調整IMG智能快簽與重要流程功能
- **Commit ID**: `c7add5ee7cf23c51729d64cdaf08171b0d16db61`
- **作者**: pinchi_lin
- **日期**: 2019-03-20 11:43:24
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 37. 移除多餘的log
- **Commit ID**: `80368cd719c3664a188f9f38cb9c04afc9d4edfd`
- **作者**: yamiyeh10
- **日期**: 2019-03-20 11:19:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 38. Q00-20190315006 修正待辦清單RESTful服務取得的行動版直連表單網址打開來一片空白
- **Commit ID**: `2cf6f1ece69b76af58a19de28e4a4452bbdaf896`
- **作者**: yamiyeh10
- **日期**: 2019-03-20 11:12:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`

### 39. Q00-20190315002 修正同意派送RESTFul在參數資料內容有誤時不是預期的回應格式
- **Commit ID**: `d9daf03963cdee880608858c513c6e1403138e2b`
- **作者**: yamiyeh10
- **日期**: 2019-03-19 15:31:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java`

### 40. Q00-20190125002 修正App表單元件點擊清除按鈕會觸發取消焦點功能
- **Commit ID**: `c7ac35cc6fc0a6200db055dddd9b561ab0e2c1ea`
- **作者**: yamiyeh10
- **日期**: 2019-03-19 10:27:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 41. Q00-20190307001 新增取未結案處理的流程的接口
- **Commit ID**: `c9dbe07ae5719ea8173aed14b817e1731e78f666`
- **作者**: pinchi_lin
- **日期**: 2019-03-18 17:51:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 42. Q00-20190315008 修正已轉派流程總計RESTful接口platform參數傳入"mobile"沒效果
- **Commit ID**: `bc6064cdfe386479e046027c091dfcd1fea9e375`
- **作者**: yamiyeh10
- **日期**: 2019-03-18 17:30:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 43. Q00-20190314016 調整IMG快速簽核與詳情的預計關卡過濾服務與通知關卡
- **Commit ID**: `4c9a6fc3fba139a00623910af97434f15ca8373e`
- **作者**: yamiyeh10
- **日期**: 2019-03-18 17:27:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`

### 44. Q0020190314007 智能快簽功能的進階篩選功能沒效果。
- **Commit ID**: `bb4c06c8f4a22f638f5367c5e9887ff32da228dc`
- **作者**: 劉建德
- **日期**: 2019-03-18 17:13:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 45. C01-20190123004 二次修正 : ISO文件一覽表的ISO文件階層無法顯示
- **Commit ID**: `026d5ed5329de0a398e93defbd56c06470f67e74`
- **作者**: 施翔耀
- **日期**: 2019-03-18 17:04:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`

### 46. Q00-20190215004 調整簽核流設計師中行動版表單權限控管會卡控APP序號是否註冊或過期
- **Commit ID**: `53ce5be5078e446170225d310ae58e61d20709aa`
- **作者**: pinchi_lin
- **日期**: 2019-03-15 18:48:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/FormSelectDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`

### 47. Q00-20190315010 修正資料庫是Oracle的時候  使用流程監控資訊封存  不管是封存資料或是解封存資料都會報錯
- **Commit ID**: `838d2ee7eabcb39e3a0864d693646cd36649e9a6`
- **作者**: walter_wu
- **日期**: 2019-03-15 18:23:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`

### 48. Q00-20190314010 將IMG預測關卡的流程有設定條件運算式註解打開
- **Commit ID**: `901117fbe90b5ea6c11634624c7ed1a2320a185a`
- **作者**: yamiyeh10
- **日期**: 2019-03-15 17:37:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 49. 補上缺少的 QRCode多語系
- **Commit ID**: `16e6574eb562252849e130d557d3eda47aaf24ce`
- **作者**: 施翔耀
- **日期**: 2019-03-15 17:26:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 50. 修正 : E10表單同步異常
- **Commit ID**: `87eb1f20f032216075c686a8d0adad3fa36d138a`
- **作者**: 施翔耀
- **日期**: 2019-03-15 17:00:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`

### 51. Q0020190314008 智能快簽功能的進階搜尋功能沒效果。
- **Commit ID**: `156a326b1e3c9eb8d635ddb46bb3e82378fe37c8`
- **作者**: 劉建德
- **日期**: 2019-03-15 16:55:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 52. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `5092e6b47d59390448e68fdf6f5c3b481474aaed`
- **作者**: 劉建德
- **日期**: 2019-03-15 16:53:41
- **變更檔案數量**: 0

### 53. Q00-20190215003 調整簽核流設計師中的支援手持裝置選項卡控APP序號註冊或過期
- **Commit ID**: `5962d9856170f99fc1b0b977aad1274578d17ce3`
- **作者**: pinchi_lin
- **日期**: 2019-03-15 16:21:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`

### 54. Q00-20190314013 鼎捷移動快速簽核依照工作權限動態產生的按鈕中，沒有"表單詳情"按鈕。
- **Commit ID**: `c1da504a8aa48ca1dfac7070973ff72501b3899c`
- **作者**: 劉建德
- **日期**: 2019-03-15 16:00:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`

### 55. Q00-20190314005 調整移動消息訂閱管理的多語系
- **Commit ID**: `1708e28028e70c2951089d9e78c38bb0b12597d1`
- **作者**: yamiyeh10
- **日期**: 2019-03-15 10:58:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 56. Q00-20190221005 調整App在服務重啟後若沒有先登入過BPM，直接登入IMG的APP會有403錯誤
- **Commit ID**: `ae270af1a72632764d2ddba620a0b6c3b173d0cb`
- **作者**: yamiyeh10
- **日期**: 2019-03-14 19:24:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 57. Q00-20190225002 IMG快速簽核畫面調整若欄位無資料時不顯示其區塊
- **Commit ID**: `b913c017f26507af0c403f8a8f551061d219dd65`
- **作者**: yamiyeh10
- **日期**: 2019-03-14 17:46:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 58. Q00-20190314011 openJDK議題,barcode元件在openJDK環境下無法使用,故修改第三方套件barbecue.jar改為可支援的寫法
- **Commit ID**: `85c83736a164f999f845394cdc1a505695723a38`
- **作者**: jerry1218
- **日期**: 2019-03-14 17:24:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Barbecue/barbecue.jar`

### 59. Q00-20190314005 調整個人移動消息訂閱管理的多語系
- **Commit ID**: `b2f800f8f5c66a0c08889c7c7c1ed487384146cc`
- **作者**: yamiyeh10
- **日期**: 2019-03-14 15:24:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 60. Q00-20190314006 修正SYNC_ISO 文件匯入後，透過文管首頁閱讀檔案開啟時報錯，無法閱讀檔案
- **Commit ID**: `9160d23631ae30c7959c55c0ab0351b073bf3add`
- **作者**: waynechang
- **日期**: 2019-03-14 14:13:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 61. Q00-20190312001 停用管理員的管理消息訂閱頁面的全選功能
- **Commit ID**: `47e302f7298680c7b748622e34746042eb091640`
- **作者**: pinchi_lin
- **日期**: 2019-03-14 12:09:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`

### 62. Q00-*********** 調整取最近含有示警訊息的工作清單功能效能問題
- **Commit ID**: `d69becd30bc56d8fcdb631000feae3efbeff5d7d`
- **作者**: pinchi_lin
- **日期**: 2019-03-14 10:09:49
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessInstanceDTOFactoryDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 63. Q00-*********** 調整IMG快速簽核畫面的上一關資訊過濾通知與服務關卡
- **Commit ID**: `eada466947730ed4d6b82a38996004723d29c7a2`
- **作者**: yamiyeh10
- **日期**: 2019-03-13 19:34:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 64. S00-20171031001 補更新多語系
- **Commit ID**: `f749538ece277e7de60c60723d3261c87889fd1f`
- **作者**: walter_wu
- **日期**: 2019-03-13 17:45:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 65. C01-20181220005 修正通知信夾帶附件若檔名超過10字會亂碼
- **Commit ID**: `a4fdc20026f09f2a5e04b11dd76f6d2d42551905`
- **作者**: waynechang
- **日期**: 2019-03-13 11:46:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`

### 66. C01-20190311006 修正因為prepare Statement沒關導致cursor超過限制
- **Commit ID**: `d7b60b9b31a9d0a8364e05cebeeb04dd445e2abe`
- **作者**: walter_wu
- **日期**: 2019-03-13 11:30:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPkgCategoryListReader.java`

### 67. Q00-20190312005 調整IMG首頁統計元件智能示警多語系
- **Commit ID**: `5e8ff57acc3dcca0f2c228fc077998c16c018b82`
- **作者**: ChinRong
- **日期**: 2019-03-12 19:46:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 68. Q00-20190307002 鼎捷移動整合頁面的統記元件設定中，新增"智能快簽"選項。
- **Commit ID**: `53a4f2e2bebe2bd88dd7757a0f77edf7a84a2082`
- **作者**: ChinRong
- **日期**: 2019-03-12 19:42:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 69. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `8975ed97bd0c40e4929b3f5b009dc16420698c63`
- **作者**: walter_wu
- **日期**: 2019-03-12 17:45:25
- **變更檔案數量**: 0

### 70. Q00-20190312004 修正C01-20190102002修改後Oracle會報錯
- **Commit ID**: `d648bf90d6274f7f7829207f8676a0d4f5f0b3ce`
- **作者**: walter_wu
- **日期**: 2019-03-12 17:39:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`

### 71. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `7e71dbcc5b84bb5e175caf7b127a0d04d73a35de`
- **作者**: ChinRong
- **日期**: 2019-03-12 17:10:21
- **變更檔案數量**: 0

### 72. Q00-20190225004 詳情表單中顯示流程頁面內的簡易流程圖中的任何連結都不應該有任何動作
- **Commit ID**: `070836659fce5bd60e8f48a4d2bb5c5fdef703ed`
- **作者**: ChinRong
- **日期**: 2019-03-12 17:08:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp`

### 73. Q00-20190312003 修正IMG簽核歷程在代處理時人員名稱的圓圈會跑版
- **Commit ID**: `1d60ec379d82eebbcd235fcd6767784804266757`
- **作者**: yamiyeh10
- **日期**: 2019-03-12 17:05:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 74. Q00-20190221002 修正IMG推播如果表單設定詳情簽核時，直連表單畫面一片空白
- **Commit ID**: `5339d41c50ba7616ec55ae2135e297df435cb0cf`
- **作者**: ChinRong
- **日期**: 2019-03-12 16:39:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 75. Q00-20190311003 調整移動端的JSP移掉未使用的js
- **Commit ID**: `96d0a4ef9c0aa2e368a5fe409450fbc07af983dd`
- **作者**: yamiyeh10
- **日期**: 2019-03-12 16:04:03
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`

### 76. Q00-20190222002 修正統計元件設定再新增第四個類型時會無法新增，並提示"統計組件類型不能重複"的問題
- **Commit ID**: `ebfc95a515441fab4d14dc7775966bef868a8a71`
- **作者**: ChinRong
- **日期**: 2019-03-12 11:14:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`

### 77. Q00-20190225003 增加IMG撥電話的直連畫面標題的多語系
- **Commit ID**: `15162461926eb61772bdea58714caeaec22d89c9`
- **作者**: yamiyeh10
- **日期**: 2019-03-12 11:12:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 78. Q00-20190225003 調整IMG撥電話的直連畫面內容符合多語系
- **Commit ID**: `eba06b61450d5100ea608158a7d8d49659377f89`
- **作者**: yamiyeh10
- **日期**: 2019-03-12 11:10:05
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp`

### 79. Q00-20190311001 將行動版"我的關注"統計筆數調整成與PC一致
- **Commit ID**: `398b5649a290489074167e406dbc0eecc4d6849e`
- **作者**: ChinRong
- **日期**: 2019-03-12 10:15:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 80. 調整快速簽核中加入重要流程會重複問題
- **Commit ID**: `4adfbdd7675341d2e5fd84daf86cff05bbe4b972`
- **作者**: pinchi_lin
- **日期**: 2019-03-11 18:47:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 81. Q00-20190225006 調整行動版樣版中的初始化Dropdown與Listbox元件方法
- **Commit ID**: `1d2e85a06516097233ce695f849c4f1caf8646ce`
- **作者**: yamiyeh10
- **日期**: 2019-03-11 15:50:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql`

### 82. C01-20190305004 修正IOS日期元件顯示時間後，在中間層會多顯示秒數且日期跟時間中間會多一個T
- **Commit ID**: `90ecfa47eb3db75565bbfcdb5f69b3fb6dcc2c1a`
- **作者**: ChinRong
- **日期**: 2019-03-11 13:58:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 83. Q00-20190221004 調整App<57>Grid明細欄位與接收資料欄位數對不上時顯示空值
- **Commit ID**: `0e147ec17bec39b9f855898724d6f2da25fc3ef4`
- **作者**: yamiyeh10
- **日期**: 2019-03-11 11:47:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 84. 調整IMG詳情表單中可作加入重要流程功能
- **Commit ID**: `dd9bf8cce7c3c559b58fef23b8f59f62e0853760`
- **作者**: pinchi_lin
- **日期**: 2019-03-11 11:41:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 85. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `ded2427e4b7f37ef8a795e73312f8efc81e5d653`
- **作者**: jerry1218
- **日期**: 2019-03-11 10:14:35
- **變更檔案數量**: 0

### 86. 調整5751 Oracle update SQL錯誤
- **Commit ID**: `7c85f8945a4a06e01d3f7e428031399d9191ee17`
- **作者**: jerry1218
- **日期**: 2019-03-11 10:13:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql`

### 87. Q00-20190308001 補上調整的多語系
- **Commit ID**: `0850e741eeecd040d5c22b4edc6297a4d7c5fa7a`
- **作者**: yamiyeh10
- **日期**: 2019-03-11 10:05:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 88. Q00-20190308001 修正IMG智能示警的處理記錄在英文語系時彈出視窗會跑版
- **Commit ID**: `309be3230ebece6fe7ebd95341cbf3c38f96f569`
- **作者**: yamiyeh10
- **日期**: 2019-03-11 10:02:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 89. Q00-20190308002 修正IMG智能示警的處理紀錄頁面無法完整顯示內容
- **Commit ID**: `b8783cabe7e407a8ac0c783491ab33e4122bd708`
- **作者**: yamiyeh10
- **日期**: 2019-03-11 09:57:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 90. Q00-20190308003 智能示警的處理紀錄資訊排序方式改為降冪
- **Commit ID**: `8cd834d7b959550195e78d628def18030a26b31a`
- **作者**: yamiyeh10
- **日期**: 2019-03-11 09:51:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 91. A00-20190215005 修正千分位顯示異常問題
- **Commit ID**: `7e49ff12945d9bde1f3d3fbbdd1c046e7927d4dd`
- **作者**: yanann_chen
- **日期**: 2019-03-11 09:37:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 92. 新增IMG詳情表單中可作加入重要流程功能
- **Commit ID**: `12cee2ca30636ed3ad940fa84fc037c6e4abedbe`
- **作者**: pinchi_lin
- **日期**: 2019-03-08 19:06:32
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 93. 加上 (e) 變成 catch (e) {}
- **Commit ID**: `92b9cf91aefcd278b995b70ed33dcb90b4dd5db6`
- **作者**: Catherine
- **日期**: 2019-03-08 16:59:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 94. C01-20180807001 更新WfERP form-default
- **Commit ID**: `1383e8aced75fa34c286db682b4e7a1b8cb814bc`
- **作者**: yanann_chen
- **日期**: 2019-03-08 15:44:22
- **變更檔案數量**: 80
- **檔案變更詳細**:
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/BOM \350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI04).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/BOM\347\224\250\351\207\217\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(BOMI02).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/E-BOM\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI12).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/PACKING LIST \345\273\272\347\253\213\344\275\234\346\245\255(EPSI06).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/PACKING LIST\345\273\272\347\253\213\344\275\234\346\245\255(IDLI43).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/SI\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IPSI04).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/WAFER BANK\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IDLI11).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/WAFER \350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IDLI15).form"`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\344\273\230\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACPI03).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\207\272\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(BCSI17).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\223\201\351\201\213\345\233\236\351\200\262\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(BCSI18).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\344\277\235\347\250\205\346\251\237\345\231\250\350\250\255\345\202\231\351\200\262\345\207\272\345\217\243\347\225\260\345\213\225\345\273\272\347\253\213\344\275\234\346\245\255(BCHI14).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(BCHI08).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(BCSI05).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\344\277\235\347\250\205\350\262\250\345\223\201\345\207\272\345\273\240\344\277\256\347\220\206\346\252\242\346\270\254\346\210\226\346\240\270\346\250\243\345\273\272\347\253\213\344\275\234\346\245\255(BCSI15).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\344\277\241\347\224\250\347\213\200\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(EPSI11).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI19).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(INVI11)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI20).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(INVI12)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\205\266\344\273\226\345\207\272\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI13).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\207\272\345\217\243\350\262\273\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(EPSI10).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BCHI09).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BCSI12).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI05).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI62).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\212\240\345\267\245\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI10).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI19).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI58).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI20).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI59).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\223\201\350\231\237\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(INVI24).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI05).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\244\232\345\270\263\346\234\254\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI62).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\247\224\345\244\226\345\267\245\345\226\256\351\226\213\347\253\213(IDLI33).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI06).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\256\242\346\210\266\350\263\207\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(COPI15).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\345\272\253\345\255\230\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(INVI05)[GPSD260030].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\207\211\344\273\230\346\206\221\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACPI02).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\213\206\350\247\243\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI06).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\216\241\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI07)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI08)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\224\266\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACRI03).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\240\261\345\220\215\344\275\234\346\245\255(HRSI34).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\226\260\345\256\242\346\210\266\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(COPI21).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI10).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI03).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\346\264\276\350\273\212\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI14).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\347\265\204\345\220\210\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI05).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\347\265\220\345\270\263\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACRI02).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI07).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\250\223\347\267\264\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(HRSI23).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\251\242\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI14)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\252\277\346\225\264\346\262\226\351\212\267\345\210\206\351\214\204\345\273\272\347\253\213\344\275\234\346\245\255(FCSI04).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI05)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\253\213\350\263\274\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(PURI16)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\262\250\351\201\213\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI07).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\345\240\261\345\273\242\345\273\272\347\253\213\344\275\234\346\245\255(ASTI08).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\345\244\226\351\200\201\345\273\272\347\253\213\344\275\234\346\245\255(ASTI13).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\212\230\350\210\212\345\273\272\347\253\213\344\275\234\346\245\255(ASTI11).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\216\241\350\263\274\350\256\212\346\233\264\344\275\234\346\245\255(ASTI24).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\216\241\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI22).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\224\266\345\233\236\345\273\272\347\253\213\344\275\234\346\245\255(ASTI14).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\224\271\350\211\257\345\273\272\347\253\213\344\275\234\346\245\255(ASTI06).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\270\233\346\220\215\345\273\272\347\253\213\344\275\234\346\245\255(ASTI25).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\347\247\273\350\275\211\345\273\272\347\253\213\344\275\234\346\245\255(ASTI12).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\350\251\242\345\203\271\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI20).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\350\252\277\346\225\264\345\273\272\347\253\213\344\275\234\346\245\255(ASTI10).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI19).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI02).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ASTI23).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\263\207\347\224\242\351\207\215\344\274\260\345\273\272\347\253\213\344\275\234\346\245\255(ASTI07).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\350\275\211\346\222\245\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(INVI08)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\351\200\200\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI11)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI09)[GP25(PR)].form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\351\212\267\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI08).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\351\212\267\351\200\200\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI09).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\351\240\220\347\256\227\346\214\252\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI23).form"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/\351\240\220\347\256\227\350\277\275\345\212\240\345\273\272\347\253\213\344\275\234\346\245\255(ACTI22).form"`

### 95. 修正ISO文件變更單選擇文件沒有ISOType時會報錯
- **Commit ID**: `00150372056338e09e1536f6d43b5b941dcdc871`
- **作者**: waynechang
- **日期**: 2019-03-08 15:10:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocCmItemVo.java`

### 96. 1.移除安裝憑證連結 2.微調設計工具多語系
- **Commit ID**: `eb125cad95834eb2799548be8f3c4c2eb730b331`
- **作者**: jerry1218
- **日期**: 2019-03-08 15:06:19
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_zh_TW.properties`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql`

### 97. 補上因Git Merge異常遺失的多語系
- **Commit ID**: `d7e8b87bc8edf1628235f8d71767a9cf5d49d8e0`
- **作者**: ChinRong
- **日期**: 2019-03-08 14:22:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 98. 新增管理員移動消息訂閱管理頁面
- **Commit ID**: `98a7944ecc8f5c38ba29e3a8a2136009bf81242c`
- **作者**: ChinRong
- **日期**: 2019-03-08 14:19:25
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql`

### 99. 微調build的說明語句
- **Commit ID**: `a1fed2558672e2c725b7c3dc25f563d42cd66231`
- **作者**: ChinRong
- **日期**: 2019-03-08 14:17:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/build.xml`

### 100. 調整管理員維護行動流程消息訂閱的功能
- **Commit ID**: `f33031b140b357e6bdd8ebeeda60c12e6866f77b`
- **作者**: ChinRong
- **日期**: 2019-03-08 14:16:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`

### 101. 新增管理員維護行動流程消息訂閱的功能
- **Commit ID**: `983350a341596fa53e7ae4d14307af7e0a2de4f3`
- **作者**: ChinRong
- **日期**: 2019-03-08 14:15:25
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAllProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 102. C01-20190128001 內部主機現有.form壓縮檔 放入出貨光碟only
- **Commit ID**: `4a5e57cac0c2742bb91774329244a1eff77a0ae4`
- **作者**: Catherine
- **日期**: 2019-03-08 12:51:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form-default-t100.zip`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form_\346\226\274\345\205\247\351\203\250\344\270\273\346\251\237\344\270\213\350\274\211\346\234\200\346\226\260\347\211\210.txt"`

### 103. <V57>S00-20180908001 修正 :流程預先解析人員 如果是離職加上符號註記,但當連續是兩個服務關卡時會有異常
- **Commit ID**: `64cae4a036941948e78c5904e072ac14ee610bd9`
- **作者**: 施翔耀
- **日期**: 2019-03-08 09:42:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 104. IMG的快速簽核增加智能示警歷史處理記錄畫面
- **Commit ID**: `94d6071cd2f641e02553a8329310749a55d77e4e`
- **作者**: yamiyeh10
- **日期**: 2019-03-07 20:32:09
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 105. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `8c47063ebf442f7361e61e2e5a77c1109996c8ce`
- **作者**: 劉建德
- **日期**: 2019-03-07 20:16:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 106. 新增加入重要流程多語系
- **Commit ID**: `4bbcb68a39a54720c3b32a35754e057c576c1f63`
- **作者**: 劉建德
- **日期**: 2019-03-07 20:14:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 107. 新增加入重要流程 RESTful 服務API
- **Commit ID**: `87c2acfb8029074313bce9320b78ed3b119fc79e`
- **作者**: 劉建德
- **日期**: 2019-03-07 20:12:52
- **變更檔案數量**: 0

### 108. 新增加入重要流程 RESTful 服務API
- **Commit ID**: `972b8aaac81c883cb12cea90883e9e8bdd2574dc`
- **作者**: 劉建德
- **日期**: 2019-03-07 20:12:52
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java`

### 109. 新增管理員移動消息訂閱管理頁面
- **Commit ID**: `65decd1b5e86c55506300d7ad0b75a740d417c36`
- **作者**: ChinRong
- **日期**: 2019-03-07 20:06:14
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql`

### 110. 微調build的說明語句
- **Commit ID**: `e3d374f3676e131a9017e4ee3ac079047380be7d`
- **作者**: jerry1218
- **日期**: 2019-03-07 17:34:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/build.xml`

### 111. 調整管理員維護行動流程消息訂閱的功能
- **Commit ID**: `75915685760179fe7c12eaab2dae20c458cb8903`
- **作者**: pinchi_lin
- **日期**: 2019-03-07 17:13:34
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`

### 112. 新增管理員維護行動流程消息訂閱的功能
- **Commit ID**: `34eb3aa778920b48e801833145222784529fef68`
- **作者**: pinchi_lin
- **日期**: 2019-03-07 15:17:53
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAllProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 113. 使用者可以在流程簽核時把流程加入重要流程
- **Commit ID**: `9137488e6c1333b92e029d47f39ba48f0bdf741d`
- **作者**: 劉建德
- **日期**: 2019-03-07 15:08:35
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 114. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `5fd36a78bdbd17c106a438dd06ac63b1798c09fb`
- **作者**: waynechang
- **日期**: 2019-03-07 10:34:42
- **變更檔案數量**: 0

### 115. Q00-20190307003 調整樹狀開窗時，須將父視窗的資料一併帶回至開窗頁面
- **Commit ID**: `905cd24f60012a0aebb771ce4a836c2733dc7f5f`
- **作者**: waynechang
- **日期**: 2019-03-07 10:34:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`

### 116. 調整IMG統計元件"我的關注"筆數計算邏輯
- **Commit ID**: `e5701021d1f8e203d9837a53cb021369746a9afe`
- **作者**: ChinRong
- **日期**: 2019-03-07 10:19:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 117. 調整IMG可發起流程可用字段接口
- **Commit ID**: `8e38744e12995e8f0438622655441cbe4705ae3e`
- **作者**: yamiyeh10
- **日期**: 2019-03-07 09:29:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 118. 新增IMG智能示警歷史處理記錄畫面
- **Commit ID**: `01e8862f7f74fd6ce05c631b399b16cf647efce2`
- **作者**: yamiyeh10
- **日期**: 2019-03-06 18:26:50
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 119. 調整行動版"我的關注"多語系
- **Commit ID**: `a9757115829b2e9fa4443696731a757b9ea9d9e4`
- **作者**: ChinRong
- **日期**: 2019-03-06 17:42:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 120. 新增行動版"我的關注"功能
- **Commit ID**: `4d05e9473f9203cfc1458e71d69b91ab63edbd9f`
- **作者**: ChinRong
- **日期**: 2019-03-06 17:10:50
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 121. C01-20190214003 第三次修正
- **Commit ID**: `d2c9bcabb137137e08c6d20fa44a7563bbb8623d`
- **作者**: yanann_chen
- **日期**: 2019-03-06 16:46:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 122. [在移動App上,可以從待辦中把流程加入重要流程] Ajax服務及RESTful服務
- **Commit ID**: `a8b0ef45b09350b24d5e3bb65d4bf5ddc78f37e5`
- **作者**: BPM
- **日期**: 2019-03-06 15:12:08
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java`

### 123. 註解上個記錄中，關於行動版"我的關注"功能的程式碼
- **Commit ID**: `4b7113a246610ba4bad7726dfe8e0011050dab9b`
- **作者**: ChinRong
- **日期**: 2019-03-06 10:35:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`

### 124. 調整IMG重要流程可用字段接口
- **Commit ID**: `f07a8b3cb1f5adf69d63c5946389a830b0ce5d8e`
- **作者**: ChinRong
- **日期**: 2019-03-06 10:18:48
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDataset.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDetailNew.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 125. 調整IMG所有可發起流程增加流程名稱蒐尋
- **Commit ID**: `950cf0af46dcae77ce0bdca8ddf5753cdfd29038`
- **作者**: yamiyeh10
- **日期**: 2019-03-06 09:45:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 126. C01-20190214003 補修正
- **Commit ID**: `3decbe80fd3eccc0b75861454b7dab3bf27b1548`
- **作者**: yanann_chen
- **日期**: 2019-03-06 09:28:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 127. 新增取最近含有示警訊息的工作清單功能
- **Commit ID**: `a9ba40591eeb9730691c9a4c691d3072a2b28b65`
- **作者**: pinchi_lin
- **日期**: 2019-03-05 16:32:44
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessInstanceDTOFactoryDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/CriticalProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 128. Jerry Merge : 新增設定檔開啟流程設計器 , 其他語系選項
- **Commit ID**: `c3e023102cb3b024632a376140f9c533af494654`
- **作者**: jerry1218
- **日期**: 2019-03-05 14:52:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@designer/NaNaTools.properties`

### 129. Jerry Merge : 修正設計器應用程式多開(3個以上)失敗問題
- **Commit ID**: `51e287008af0dc7f5a6f5e16f147000adf0f43f2`
- **作者**: jerry1218
- **日期**: 2019-03-05 14:21:40
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/lib/NaNaSimple/bpmToolEntrySimple.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/DesignerMainApp.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/LoginCache.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/LoginDesigner.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/lib/NaNaSimple/bpmToolEntrySimple.jar`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/lib/NaNaSimple/bpmToolEntrySimple.jar`

### 130. C01-20190225001 修正企業微信在直接進入表單畫面時會因使用者尚未登入而導致畫面異常問題
- **Commit ID**: `cb53fcfcbf776ec643dbc555201b36a18824795f`
- **作者**: yamiyeh10
- **日期**: 2019-03-05 10:25:30
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 131. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `74e2ba14a4aa0cc2e8572ba5c05c7dcdaa58e8d0`
- **作者**: Catherine
- **日期**: 2019-02-27 18:25:56
- **變更檔案數量**: 0

### 132. 刪除無意義檔案only
- **Commit ID**: `fa516dd05e3158fd44c5b97288cb181110a052f9`
- **作者**: Catherine
- **日期**: 2019-02-27 18:24:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ❌ **刪除**: `"3.Implementation/subproject/bpm-designer/\345\267\262\347\237\245\345\225\217\351\241\214.doc"`
  - ❌ **刪除**: `"3.Implementation/subproject/process-designer/\345\267\262\347\237\245\345\225\217\351\241\214.doc"`

### 133. A00-20190111001 修正使用WorkflowService.invokeProcess發起流程內含附件 清單上不會有附件的迴紋針
- **Commit ID**: `c11075ff97ca2327002c8ac7646cbbde7a7cafba`
- **作者**: walter_wu
- **日期**: 2019-02-27 18:13:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceController.java`

### 134. A00-20190226002 doResize導致錯誤修正
- **Commit ID**: `2164d7240a2d39273120392eeb122e05f1e1235a`
- **作者**: Catherine
- **日期**: 2019-02-27 18:02:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 135. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `aa123cddc9308d805e2cafbfadf8780e8aa8efdc`
- **作者**: jerry1218
- **日期**: 2019-02-27 16:53:20
- **變更檔案數量**: 0

### 136. Jerry Merge : 修正launch4j錯誤
- **Commit ID**: `eb9bb68e1a8564d609ae90197dee7089a910db68`
- **作者**: jerry1218
- **日期**: 2019-02-27 16:51:45
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/.gitignore`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/bin/COPYING`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/bin/ld.exe`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/bin/windres.exe`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/bin/COPYING`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/bin/ld.exe`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/bin/windres.exe`

### 137. S00-20171031001 修正上筆修改後 Script沒有成功更新
- **Commit ID**: `102d2880be52a66f52a27f325a2ca65eb52e49ec`
- **作者**: walter_wu
- **日期**: 2019-02-27 15:20:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 138. Jerry Merge - 取消build efgp-pdfViewer
- **Commit ID**: `63d22baa7eef5d52f9356133721d5ce7e7edf05e`
- **作者**: jerry1218
- **日期**: 2019-02-27 14:21:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/build.xml`
  - 📝 **修改**: `3.Implementation/properties.xml`

### 139. Jerry Merge : OpenJDK merge
- **Commit ID**: `54edf3a723693612adc287a92c380858b3c74b13`
- **作者**: jerry1218
- **日期**: 2019-02-27 10:55:51
- **變更檔案數量**: 898
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/build.bat`
  - 📝 **修改**: `3.Implementation/build.xml`
  - 📝 **修改**: `3.Implementation/properties.xml`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/jar-in-jar-loader.zip`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/NaNaSimple/bpmToolEntrySimple.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/ProcessDesignerApp.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/action/OpenProcessPackageAction.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ActionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/CMManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/DesignerSecurityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessViewController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/SecurityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/jgoext/view/ProcessViewListener.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BaseEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/JDiagram.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/main/DesignerIFrame.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/main/DesignerMainFrame.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/popmenu/ObjectPopupMenu.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/popmenu/ProcessViewPopupMenu.java`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/AccessRightController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_zh_TW.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_en_US.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_vi_VN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_zh_CN.properties`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/.project`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/.project`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/.settings/org.eclipse.core.resources.prefs`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/.settings/org.eclipse.core.runtime.prefs`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/Launch4j.url`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/ConsoleApp.exe`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/ConsoleApp.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/l4j/ConsoleApp.ico`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/lib/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ConsoleApp/src/net/sf/launch4j/example/ConsoleApp.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ExitCodeApp/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ExitCodeApp/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ExitCodeApp/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/ExitCodeApp/src/net/sf/launch4j/example/ExitCodeApp.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/SimpleApp.exe`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/SimpleApp.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/l4j/SimpleApp.ico`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/l4j/SimpleApp.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/l4j/splash.bmp`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/lib/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/SimpleApp/src/net/sf/launch4j/example/SimpleApp.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/demo/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head/consolehead.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head/guihead.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head/head.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/head.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/jniconsolehead.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/jniguihead.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_jni_BETA/jnihead.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/consolehead/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/consolehead/Makefile.win`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/consolehead/consolehead.c`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/consolehead/consolehead.dev`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/Makefile.win`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/guihead.c`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/guihead.dev`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/guihead/guihead.h`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/head.c`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/head.h`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniconsolehead_BETA/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniconsolehead_BETA/Makefile.win`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniconsolehead_BETA/jniconsolehead.c`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniconsolehead_BETA/jniconsolehead.dev`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/Makefile.win`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/jniguihead.c`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/jniguihead.dev`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jniguihead_BETA/jniguihead.h`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jnihead.c`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/jnihead.h`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/head_src/resource.h`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/launch4j.exe`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/launch4j.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/launch4j.jfpr`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/launch4jc.exe`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/JGoodies.Forms.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/JGoodies.Looks.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/Nuvola.Icon.Theme.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/XStream.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/ant.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/ant.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/commons-beanutils.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/commons-logging.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/commons.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/formsrt.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/foxtrot.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/foxtrot.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/jgoodies-common.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/jgoodies-forms.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/jgoodies-looks.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/lib/xstream.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/manifest/uac.exe.manifest`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/.project`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/.settings/org.eclipse.m2e.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-dist.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-linux.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-linux64.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-mac.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/assemble-win32.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/assembly/src.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/maven-readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/maven/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/sign4j/README.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/sign4j/sign4j.c`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/sign4j/sign4j.exe`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/asterix-o.gif`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/asterix.gif`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/build.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/button_ok.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/cancel16.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/down16.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/edit_add16.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/info.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/new.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/new16.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/ok16.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/open.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/open16.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/run.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/save.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/images/up16.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/launch4j.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Builder.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/BuilderException.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ExecException.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/FileChooserFilter.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/KanjiEscapeOutputStream.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Log.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Main.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/RcBuilder.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/Util.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/AntClassPath.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/AntConfig.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/AntJre.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/Launch4jTask.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/StringWrapper.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/ant/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/Binding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/BindingException.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/Bindings.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/IValidatable.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/InvariantViolationException.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JComboBoxBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JListBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JRadioButtonBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JTextAreaBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JTextComponentBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/JToggleButtonBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/OptComponentBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/OptJTextAreaBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/Validator.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/binding/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/CharsetID.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/ClassPath.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Config.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/ConfigPersister.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/ConfigPersisterException.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Describable.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Jre.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/JreVersion.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/LanguageID.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/LdDefaults.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Msg.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/SingleInstance.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/Splash.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/VersionInfo.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/config/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/BasicForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/BasicForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/ClassPathForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/ClassPathForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/ConfigForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/ConfigForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/EnvironmentVarsForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/EnvironmentVarsForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/HeaderForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/HeaderForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/JreForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/JreForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/MessagesForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/MessagesForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/SingleInstanceForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/SingleInstanceForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/SplashForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/SplashForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/VersionInfoForm.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/VersionInfoForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/form/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/AbstractAcceptListener.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/BasicFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/BrowseActionListener.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/ClassPathFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/ConfigFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/EnvironmentVarsFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/FileChooser.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/GlassPane.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/HeaderFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/JreFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/MainFrame.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/MessagesFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/SingleInstanceFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/SplashFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/VersionInfoFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/formimpl/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/src/net/sf/launch4j/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/uninst.exe`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/MinGW.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/crt2.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libadvapi32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libgcc.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libkernel32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libmingw32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libmsvcrt.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libshell32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api/libuser32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/MinGW.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/crt2.o`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libadvapi32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libgcc.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libkernel32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libmingw32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libmingwex.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libmoldname.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libmsvcrt.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libshell32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/w32api_jni/libuser32.a`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/web/bullet.gif`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/web/changelog.html`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/web/docs.html`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/web/index.html`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/web/launch4j-use.gif`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/web/launch4j.gif`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/web/links.html`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/Launch4j/web/style.css`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/cp.bat`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/jar-in-jar-loader.zip`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Axis/activation.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Axis/axis.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Axis/commons-discovery.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Axis/jaxrpc.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Axis/saaj.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Axis/wsdl4j.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/BrowserLauncher/BrowserLauncher.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/Bsf/bsf.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/Bsf/bsh.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/Bsf/js.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/Bsf/jython.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Dom4J/dom4j-1.6.1-changed_serialization.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Forms/forms-1.1.0.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JCalendar/jcalendar-1.3.2.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JCalendar/jcalendar.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JCalendar/kunststoff.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JDiagram/JDiagram-4.1.4.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JGo/JGo.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JGo/JGoLayout.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/JGo/JGoSVG.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/icons.zip`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/optional/Filters.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/optional/MultipleGradientPaint.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/optional/swing-layout.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/optional/swing-worker.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/JTaskpane/swingx-0.9.1.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Jag/jag.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-beanutils.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-collections.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-lang.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-logging.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/JakartaOJB/antlr-2.7.6.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/JakartaOJB/db-ojb.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Jython/jython.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/Log4J/log4j.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/LookAndFeel/looks-2.1.4.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/LookAndFeel/panel-skin.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Mail/activation.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Mail/mail.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Msv/isorelax.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Msv/msv.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Msv/relaxngDatatype.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Msv/xmlgen.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Msv/xsdlib.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/Tyrex/tyrex.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/XStream/xpp3_min.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/XStream/xstream.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Xerces/resolver.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Xalan/serializer.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/app/lib/Xerces/xercesImpl.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/lib/Xerces/xml-apis.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/DesignerMainApp.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/LoginCache.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/DesignerChooseController.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/DesignerChooseDialog.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginController.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_en_US.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_vi_VN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_zh_CN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/main/DesignerMainApp_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_en_US.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_vi_VN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_zh_CN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_vi_VN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_CN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/tool-icon.ico`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SystemConfigManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/SystemConfigManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_side_util/RemoteCallConnection.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/remote_call/Administrator.java`
  - 📝 **修改**: `3.Implementation/subproject/crm-configure/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/crm-configure/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/crm-configure/src/com/dsc/nana/user_interface/apps/crmcfg/controller/CrmCfgMainFrameController.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/control/AbstractDesignerSystemManager.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/AbstractDesignerDialog.java`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/model/ToolEntryLoginTemp.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/subdesigner/AbsSubDesignerController.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/view/dialog/DesignerLoginController.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/view/dialog/DesignerLoginDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/domain/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/dto/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/dto/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/FormDesigner.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/ServerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/SystemController.java`
  - 📝 **修改**: `3.Implementation/subproject/form-importer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/form-importer/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/jar-in-jar-loader.zip`
  - ❌ **刪除**: `3.Implementation/subproject/org-designer-blink/lib/NaNa/conf/NaNaLog.properties`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/NaNaSimple/bpmToolEntrySimple.jar`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/OrgDesigner.java`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/ToolAction.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/mainframe/MainMenuBar.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/mainframe/OrgMainFrame.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-importer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/org-importer/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/org-importer/src/com/dsc/nana/user_interface/apps/org_importer/control/ImporterMainController.java`
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/.project`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/.settings/org.eclipse.core.resources.prefs`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/.settings/org.eclipse.core.runtime.prefs`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/Launch4j.url`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/ConsoleApp.exe`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/ConsoleApp.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/l4j/ConsoleApp.ico`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/lib/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ConsoleApp/src/net/sf/launch4j/example/ConsoleApp.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ExitCodeApp/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ExitCodeApp/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ExitCodeApp/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/ExitCodeApp/src/net/sf/launch4j/example/ExitCodeApp.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/SimpleApp.exe`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/SimpleApp.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/l4j/SimpleApp.ico`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/l4j/SimpleApp.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/l4j/splash.bmp`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/lib/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/SimpleApp/src/net/sf/launch4j/example/SimpleApp.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/demo/readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head/consolehead.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head/guihead.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head/head.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/head.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/jniconsolehead.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/jniguihead.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_jni_BETA/jnihead.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/consolehead/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/consolehead/Makefile.win`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/consolehead/consolehead.c`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/consolehead/consolehead.dev`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/Makefile.win`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/guihead.c`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/guihead.dev`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/guihead/guihead.h`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/head.c`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/head.h`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniconsolehead_BETA/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniconsolehead_BETA/Makefile.win`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniconsolehead_BETA/jniconsolehead.c`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniconsolehead_BETA/jniconsolehead.dev`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/Makefile.win`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/jniguihead.c`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/jniguihead.dev`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jniguihead_BETA/jniguihead.h`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jnihead.c`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/jnihead.h`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/head_src/resource.h`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/launch4j.exe`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/launch4j.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/launch4j.jfpr`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/launch4jc.exe`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/JGoodies.Forms.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/JGoodies.Looks.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/Nuvola.Icon.Theme.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/XStream.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/ant.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/ant.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/commons-beanutils.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/commons-logging.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/commons.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/formsrt.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/foxtrot.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/foxtrot.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/jgoodies-common.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/jgoodies-forms.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/jgoodies-looks.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/lib/xstream.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/manifest/uac.exe.manifest`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/.project`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/.settings/org.eclipse.m2e.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-dist.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-linux.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-linux64.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-mac.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/assembly/assemble-win32.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/assembly/src.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/maven-readme.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/maven/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/sign4j/README.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/sign4j/sign4j.c`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/sign4j/sign4j.exe`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/asterix-o.gif`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/asterix.gif`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/build.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/button_ok.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/cancel16.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/down16.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/edit_add16.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/info.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/new.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/new16.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/ok16.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/open.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/open16.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/run.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/save.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/images/up16.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/launch4j.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Builder.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/BuilderException.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ExecException.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/FileChooserFilter.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/KanjiEscapeOutputStream.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Log.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Main.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/RcBuilder.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/Util.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/AntClassPath.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/AntConfig.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/AntJre.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/Launch4jTask.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/StringWrapper.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/ant/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/Binding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/BindingException.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/Bindings.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/IValidatable.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/InvariantViolationException.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JComboBoxBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JListBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JRadioButtonBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JTextAreaBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JTextComponentBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/JToggleButtonBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/OptComponentBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/OptJTextAreaBinding.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/Validator.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/binding/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/CharsetID.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/ClassPath.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Config.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/ConfigPersister.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/ConfigPersisterException.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Describable.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Jre.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/JreVersion.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/LanguageID.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/LdDefaults.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Msg.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/SingleInstance.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/Splash.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/VersionInfo.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/config/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/BasicForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/BasicForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/ClassPathForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/ClassPathForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/ConfigForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/ConfigForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/EnvironmentVarsForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/EnvironmentVarsForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/HeaderForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/HeaderForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/JreForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/JreForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/MessagesForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/MessagesForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/SingleInstanceForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/SingleInstanceForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/SplashForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/SplashForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/VersionInfoForm.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/VersionInfoForm.jfrm`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/form/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/AbstractAcceptListener.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/BasicFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/BrowseActionListener.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/ClassPathFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/ConfigFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/EnvironmentVarsFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/FileChooser.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/GlassPane.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/HeaderFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/JreFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/MainFrame.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/Messages.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/MessagesFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/SingleInstanceFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/SplashFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/VersionInfoFormImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/formimpl/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/messages.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/src/net/sf/launch4j/messages_es.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/uninst.exe`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/MinGW.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/crt2.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/libadvapi32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/libgcc.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/libkernel32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/libmingw32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/libmsvcrt.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/libshell32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api/libuser32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/MinGW.LICENSE.txt`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/crt2.o`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libadvapi32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libgcc.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libkernel32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libmingw32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libmingwex.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libmoldname.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libmsvcrt.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libshell32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/w32api_jni/libuser32.a`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/web/bullet.gif`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/web/changelog.html`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/web/docs.html`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/web/index.html`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/web/launch4j-use.gif`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/web/launch4j.gif`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/web/links.html`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/Launch4j/web/style.css`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/build-exe.xml`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/jar-in-jar-loader.zip`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/NaNaSimple/bpmToolEntrySimple.jar`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/ADMApp.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/AdmSignOnManager.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/main/ADMMainFrame.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/ProcessDesignerApp.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/CMManager.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/DesignerController.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/DesignerSecurityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/SecurityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/main/DesignerIFrame.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/main/DesignerMainFrame.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/tool-icon.ico`
  - 📝 **修改**: `3.Implementation/subproject/service/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/sys-authority/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/sys-authority/src/com/dsc/nana/user_interface/apps/authority/controller/SysAuthorityController.java`
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/src/com/dsc/nana/user_interface/apps/rsrcbundle/controller/RsrcBundleMainController.java`
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/src/com/dsc/nana/user_interface/apps/syscfg/controller/SysCfgMainFrameController.java`
  - 📝 **修改**: `3.Implementation/subproject/system/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/system/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/webapp/.gitignore`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DesignerDownloadAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-designerDownload-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/DesignerDownload/DesignerDownloadMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/DesignerDownload/java-1.8.0-openjdk-1.8.0.201-1.b09.ojdkbuild.windows.x86_64.msi`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/ToolSuite.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-awt-util.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-dom.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-svg-dom.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-svggen.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-util.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-xml.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-core.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-extra.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-pro-intl.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-pro.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-viewer.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/levigo-jbig2-imageio.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/J2EE/j2ee.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/JIntellitype/JIntellitype.dll`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/JIntellitype/JIntellitype64.dll`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/JIntellitype/jintellitype-1.3.9.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/JTaskpane/swingx.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-codec.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-httpclient.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-io.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-pool.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/Jaxen/jaxen.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/Log4J/log4j.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/Sdo/sdo2_1.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/Trinity/TrinityServiceEJB.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/Xalan/xalan.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/app/lib/Xerces/xml-apis.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/bin/jconsole.exe`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/jre/lib/security/US_export_policy.jar`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/jre/lib/security/local_policy.jar`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf.bat`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_1.sql`

### 140. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `507fcb8c779ece8e4673a2957a5a71885c524c8a`
- **作者**: ChinRong
- **日期**: 2019-02-27 10:39:57
- **變更檔案數量**: 0

### 141. 新增img草稿清單二期接口
- **Commit ID**: `099bb0511bab99e4948771d3f1a1e5bb030effe5`
- **作者**: ChinRong
- **日期**: 2019-02-27 10:39:27
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/AdvancedSearch.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 142. A00-20190215002 修正T100送簽單據後，關卡解析失敗回傳失敗的XML，但流程仍然產生
- **Commit ID**: `efadf3df1fb8969356470a52e3cb9221618e7d0d`
- **作者**: waynechang
- **日期**: 2019-02-26 14:26:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/AbstractNewTiptopMethod.java`

### 143. A00-20190219002 修正T100發起RWD表單有SubTab元件時會報錯-增加其他整合的例外修正
- **Commit ID**: `73a37d292e2caa908150a60f332b156385674e07`
- **作者**: waynechang
- **日期**: 2019-02-26 14:12:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 144. 隱藏IMG追蹤流程快速簽核進階按鈕
- **Commit ID**: `0605b17824c44e9da1050cad3d675fb97ad8d04b`
- **作者**: ChinRong
- **日期**: 2019-02-26 13:49:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 145. Q00-20190226001 修正表單設計中間層標記多欄位時，最右邊標記的欄位在IMG中間層上不會顯示
- **Commit ID**: `f0523d7adf62267540cca890499fcd75ad0c5487`
- **作者**: ChinRong
- **日期**: 2019-02-26 13:47:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 146. A00-20190219002 修正T100發起RWD表單有SubTab元件時會報錯
- **Commit ID**: `9706d4197d5b86e7fa9b95c23a1881b2d6d18f3a`
- **作者**: waynechang
- **日期**: 2019-02-25 17:25:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 147. <V57>Q00-20190223001 修正 :文件制作索引失敗,因缺少引用的jar檔
- **Commit ID**: `409af830a1840f32da0cabb0f817a626fc34cf6b`
- **作者**: 施翔耀
- **日期**: 2019-02-25 15:19:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`

### 148. S00-20190213001 增加QRCode簽核時，參考系統參數設定驗證時間
- **Commit ID**: `8889900667da3c3f91df9a265578afa58567432d`
- **作者**: waynechang
- **日期**: 2019-02-25 14:15:18
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/QRCodeLoginCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_1.sql`

### 149. 修正鼎捷移動快速簽核同意按鈕點擊沒反應的問題
- **Commit ID**: `ee66ebc8b64f78b7df008d7c7130b13642a8b386`
- **作者**: ChinRong
- **日期**: 2019-02-22 17:21:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`

### 150. 新增預測關卡、上一關卡撥電話的進階按鈕與提示訊息
- **Commit ID**: `88138af26e0a35ba39f875a7e8a6da9f783e2cfc`
- **作者**: ChinRong
- **日期**: 2019-02-22 17:02:13
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 151. 修正恢復訂閱管理如果清單中有"只支援行動簽核的流程"流程名稱會顯示為空
- **Commit ID**: `a3db69915a4513c55c541acc3629da0fb019eb26`
- **作者**: ChinRong
- **日期**: 2019-02-22 17:01:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java`

### 152. C01-20190123004 ISO文件一覽表的ISO文件階層無法顯示
- **Commit ID**: `7515948ca8cc7afa0c02ed1cb60a53b39d395ced`
- **作者**: waynechang
- **日期**: 2019-02-22 16:09:49
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/ISODocLevel.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/ISODocType.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocForReportViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`

### 153. 新增BPM撥號畫面
- **Commit ID**: `bde12de6ab3c17bc698644be688545355f5bf678`
- **作者**: yamiyeh10
- **日期**: 2019-02-22 10:15:54
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/phonecall.gif`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 154. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `063c8f950f5f0f883cc258c1fe3fb3727a07d1f8`
- **作者**: BPM
- **日期**: 2019-02-22 09:52:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 155. 修正過濾中間層按鈕的錯誤
- **Commit ID**: `62fe16dabd636265335e38b1316a4817d4da8fe6`
- **作者**: BPM
- **日期**: 2019-02-22 09:50:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 156. 調整IMG查看附件方式 --改使用鼎捷移動提供的開窗方式預覽附件檔案
- **Commit ID**: `784053e608c9fc11bd41e5a2bbe6b6c4453ba207`
- **作者**: yamiyeh10
- **日期**: 2019-02-22 09:16:51
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 157. 修正鼎捷移動快速簽核預計關卡聯絡人Label沒有多語系問題
- **Commit ID**: `ccf0986f2bf11b1f9b753f3a96f36a0ff9c6e147`
- **作者**: ChinRong
- **日期**: 2019-02-21 17:47:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 158. 調整最常處理流程的功能
- **Commit ID**: `442e59dda887266f1a5a111593f52e0149618a5d`
- **作者**: pinchi_lin
- **日期**: 2019-02-21 17:20:35
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 159. 補上表單取消訂閱畫面的多語系
- **Commit ID**: `ba1943174176295878719590b130602c12d7aa42`
- **作者**: ChinRong
- **日期**: 2019-02-21 16:52:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 160. 調整表單取消訂閱畫面的樣式
- **Commit ID**: `9c5198ee03f27357118f7ea913a05548ab178d8e`
- **作者**: ChinRong
- **日期**: 2019-02-21 16:48:24
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 161. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `b841bfe739c33a345d00c32801d3664f208f5110`
- **作者**: yanann_chen
- **日期**: 2019-02-21 11:21:39
- **變更檔案數量**: 0

### 162. C01-20190214003 修正在 追蹤流程=>已轉派的工作 中，查看表單資料右上方沒有"顯示流程"的按鈕
- **Commit ID**: `72637c097599259210cde9acecd5dfd284bf4045`
- **作者**: yanann_chen
- **日期**: 2019-02-21 11:05:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 163. 新增行動流程恢復訂閱管理頁面
- **Commit ID**: `8f4e70d6a470d2ddb065fe762afb0a660e8715c9`
- **作者**: ChinRong
- **日期**: 2019-02-21 09:42:48
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 164. C01-20190218003 修正產品開窗預設值過濾組織名稱沒效果的問題
- **Commit ID**: `ddc2f7349bace9398a00308b5f22479bb03a3f3a`
- **作者**: ChinRong
- **日期**: 2019-02-20 18:15:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 165. 二次修正 <V57>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤
- **Commit ID**: `ccafc43b6913731595ba8c8c1ceb91995e9c9645`
- **作者**: 施翔耀
- **日期**: 2019-02-20 15:24:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 166. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `af29d5473bc94942b03edc271cfa853232b03dd7`
- **作者**: 施翔耀
- **日期**: 2019-02-20 15:14:53
- **變更檔案數量**: 0

### 167. <V57>A00-20180725001 修正:ESS流程只能加簽通知關卡，但修改模式下確可以選到會辦
- **Commit ID**: `3193517c50ad2ea690da5dc80943c99bf52cbefa`
- **作者**: 施翔耀
- **日期**: 2019-02-20 15:14:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 168. 調整最常處理流程的功能 1.調整篩選API增加處理工作欄位 2.常處理流程增加優先處理工作欄位 3.判斷用戶是否為部門主管功能改用public 4.增加常處理流程使用的多語系
- **Commit ID**: `ee90b4e4f0742e74a330b321080c5b39779a179e`
- **作者**: yamiyeh10
- **日期**: 2019-02-20 14:54:51
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 169. <V57>C01-20190213001  調整 :移除多表單自動儲存功能 ，該功能不符合應用場景，且後端容易發生物件修改時報出 CannotLock的錯誤
- **Commit ID**: `7e7dc90aa322532de0d52ff13d6bdf59e7e99696`
- **作者**: 施翔耀
- **日期**: 2019-02-20 14:39:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 170. 調整最常處理流程的功能
- **Commit ID**: `60d98b84c12711e9b5d720f006abfe5d8102c430`
- **作者**: pinchi_lin
- **日期**: 2019-02-19 18:44:09
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbstractPageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListResultsTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 171. 補上鼎捷移動快速簽核預計關卡資訊的多語系
- **Commit ID**: `4808fde9b20b9fa8445d27035885c33323e09c9f`
- **作者**: ChinRong
- **日期**: 2019-02-19 11:16:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5751.xls`

### 172. 將01/18註解的功能還原(遺漏部分)
- **Commit ID**: `e12470f5c2e6345f260a655aab183281ab6c2d71`
- **作者**: ChinRong
- **日期**: 2019-02-19 10:03:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 173. 將01/19註解的功能還原(遺漏部分)
- **Commit ID**: `0aed4fb0c87fe939f9ebea4fca777654ece98fac`
- **作者**: pinchi_lin
- **日期**: 2019-02-19 09:58:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 174. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `edce2208962cf117eb7f41aa47fa86835f87d51c`
- **作者**: walter_wu
- **日期**: 2019-02-18 19:17:23
- **變更檔案數量**: 0

### 175. A00-20190128001 修正TextArea設定高度與必填 高度會失效
- **Commit ID**: `da807c77d0badb4d43f104dbac4d1b75090386d9`
- **作者**: walter_wu
- **日期**: 2019-02-18 18:17:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 176. 用戶可以透過智能快簽的簽核歷程撥打電話給關卡處理人,中間層可以透過參數控制不撈出表單資料
- **Commit ID**: `768d58a8c031f94d3a88556e4c5cc3f28000c41a`
- **作者**: BPM
- **日期**: 2019-02-18 17:00:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 177. A00-20190129004 修正TextBox的唯讀，只要設定顯示小數點後N位數就會發生  顏色無法正確顯示
- **Commit ID**: `006d38654be1175c305e1d70ad5250a2f362f07b`
- **作者**: walter_wu
- **日期**: 2019-02-15 15:01:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 178. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `0191e9b4df0031d3f50768c3dcc2c38b192ec80f`
- **作者**: walter_wu
- **日期**: 2019-02-14 10:45:49
- **變更檔案數量**: 0

### 179. 將01/19註解的功能還原 1.IMG待辦詳情表單畫面轉由他人處理功能 2.IMG待辦詳情表單畫面簽核歷程上一關卡與當前關卡功能
- **Commit ID**: `b3117aaf5fbf7c1077ee74221a8c8812d892c8c7`
- **作者**: yamiyeh10
- **日期**: 2019-02-14 09:52:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`

### 180. 補修正C01-20190107001 C01-20190109005
- **Commit ID**: `43caba79c483ea235061efaccca6d14b4762fc77`
- **作者**: walter_wu
- **日期**: 2019-02-13 18:38:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 181. 新增IMG行動表單上取消訂閱推播功能
- **Commit ID**: `3ae6fc6f301854bba491bbcf0b51095e0020ce8c`
- **作者**: ChinRong
- **日期**: 2019-02-13 15:58:49
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmWorkItemDataVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 182. 將01/18註解的功能還原
- **Commit ID**: `61148c935d72d9128a4338faff232050d7d78bab`
- **作者**: ChinRong
- **日期**: 2019-02-13 15:47:28
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWinUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql`

### 183. 新增IMG推播消息取消訂閱功能
- **Commit ID**: `6d6e9c1ce50f1783e4b46f4fada0c3444360b237`
- **作者**: pinchi_lin
- **日期**: 2019-02-13 14:00:46
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileMessageSubscription.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileMessageSubscriptionDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.5.1_updateSQL_SQLServer.sql`

### 184. 將01/19註解的功能還原
- **Commit ID**: `c3bdca3b74b37c4ac4125686a8d428937a6afa6c`
- **作者**: pinchi_lin
- **日期**: 2019-02-13 10:36:29
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`

### 185. C01-20190212001 修正附件管理按鈕在IPhone XR,IPhone XS上跑版問題
- **Commit ID**: `27187d54b9a208a5ddec49aceb8130cfcf016619`
- **作者**: ChinRong
- **日期**: 2019-02-12 14:24:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 186. 更新ESS表單 --HR同仁協助調整表單內容 --將單身明細的欄位名稱補上與表單上相同的欄位名稱
- **Commit ID**: `ad857442f176485f167a3dc10ef620983614b8c9`
- **作者**: yamiyeh10
- **日期**: 2019-02-12 14:15:32
- **變更檔案數量**: 28
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF05\345\212\240\347\217\255\350\250\210\345\212\203\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF17\351\212\267\345\201\207\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF20\345\207\272\345\267\256\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF21\345\207\272\345\267\256\347\231\273\350\250\230.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF24\350\252\277\350\226\252\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF28\344\272\272\345\212\233\351\234\200\346\261\202\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF31\346\213\233\350\201\230\350\250\210\347\225\253.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF32\346\207\211\350\201\230\344\272\272\345\223\241\351\235\242\350\251\246.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF33\346\207\211\350\201\230\344\272\272\345\223\241\347\255\206\350\251\246.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF50\347\217\255\346\254\241\350\256\212\346\233\264\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF60\350\254\233\345\270\253\350\263\207\346\240\274\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF62\345\237\271\350\250\223\351\240\220\347\256\227\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF63\345\237\271\350\250\223\351\234\200\346\261\202\346\216\241\351\233\206.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF64\345\237\271\350\250\223\350\250\210\347\225\253\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF66\345\237\271\350\250\223\350\251\225\344\274\260.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF74\350\263\207\346\272\220\347\224\263\351\240\230.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF75\350\263\207\346\272\220\346\255\270\351\202\204.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF04B\345\212\240\347\217\255\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF23B\350\252\277\350\201\267\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF26B\347\215\216\346\207\262\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF30\350\243\234\345\210\267\345\215\241\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF71\350\253\213\345\201\207\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\346\227\227\350\211\246/ESSF77\350\226\252\350\263\207\347\265\220\346\236\234\345\257\251\346\240\270.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\346\227\227\350\211\246/ESSF80_\345\212\240\347\217\255\350\250\210\345\212\203\346\230\216\347\264\260\346\222\244\351\212\267.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF52C2\347\217\255\346\254\241\350\256\212\346\233\264.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF52\346\212\225\347\217\255\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF53\346\216\222\347\217\255\347\242\272\350\252\215.form"`

### 187. C01-20190107002 補上如果是自動簽核 列印時 狀態已處理之後面沒有(自動)兩字
- **Commit ID**: `cc840d8f7135e2c2c06111aa42bd48edbb2d275b`
- **作者**: walter_wu
- **日期**: 2019-01-30 18:36:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 188. C01-20190123004 修正關卡退回重辦後，後續派送關卡新增為兩個代辦
- **Commit ID**: `741a2ccdc4ade65bec8442a84386bf3efb919490`
- **作者**: waynechang
- **日期**: 2019-01-30 09:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 189. C01-20190107001 修正Grid資料過多會蓋到簽核意見的問題
- **Commit ID**: `05a260da6ad1065018a4846d609fd7018366ac35`
- **作者**: walter_wu
- **日期**: 2019-01-29 19:00:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

