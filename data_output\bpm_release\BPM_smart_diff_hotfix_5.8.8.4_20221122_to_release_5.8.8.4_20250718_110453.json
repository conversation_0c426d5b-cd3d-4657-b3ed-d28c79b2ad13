{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.8.4_20221122", "date": "2022-11-21 18:02:16", "message": "[流程引擎]Q00-20221121001 修正流程寄信內容是整張表單，且表單元件為浮點數且為空的狀況會派送失敗", "author": "林致帆"}, "舊分支": {"branch_name": "release_5.8.8.4", "date": "2022-11-16 14:27:02", "message": "[Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題", "author": "<PERSON><PERSON><PERSON>"}, "比較時間": "2025-07-18 11:04:53", "新增commit數量": 68, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "24ca6eb4cd593538f5111a31bb979d5713a49cbd", "commit_訊息": "[流程引擎]Q00-20221121001 修正流程寄信內容是整張表單，且表單元件為浮點數且為空的狀況會派送失敗", "提交日期": "2022-11-21 18:02:16", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a46c5a8ba4b041d20c5d3101b5be261e2549bb4", "commit_訊息": "[Web]V00-20221020001 修正切換頁面時，若讀取時間較長，會先呈現原畫面再跳轉的問題", "提交日期": "2022-11-19 19:39:49", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a64e321202c7b075c623cb1859541ceee7478961", "commit_訊息": "[Web]Q00-20221118002 修正附件太多導致往下派送失敗", "提交日期": "2022-11-18 14:57:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c55aa1fd3bff9285475b300011b3c11d9b864f9", "commit_訊息": "[Web]Q00-*********** 修正一般使用者簽核 T100 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊", "提交日期": "2022-11-18 14:25:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysNewTiptopToolDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopToolBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "a2a9ecf2fd9aa46548d535bf854365697186a2fa", "commit_訊息": "[Web]Q00-*********** 修正一般使用者簽核 TIPTOP 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊", "提交日期": "2022-11-17 17:41:12", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/IPrsMappingKeyDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/OJBPrsMappingKeyDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "f3b770bb2d3f6d91dae4437210050e52f997ae7e", "commit_訊息": "[DT]C01-*********** 修正Web化系統管理工具流程主機設定在編輯儲存後導致其他使用者登入BPM後顯示空白畫面問題", "提交日期": "2022-11-17 16:01:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "30a4f405b707ae4656df9ea6fcbc5f73ac99535f", "commit_訊息": "[在線閱覽]Q00-20221026004 修正在線閱覽開啟檔案的URL，當文件主機設置的WebAddress最後一碼為斜線時需過濾，避免開啟閱讀檔案後，點擊其他BPM功能會被導入登入頁", "提交日期": "2022-11-17 12:04:11", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fefd5c2cb34e9a17f16a64db8569b14b36cd8200", "commit_訊息": "[BPM APP]C01-20221109006 修正移動端Grid元件在不可新增但可編輯與刪除時能看到查看更多按鈕的問題", "提交日期": "2022-11-17 10:13:34", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5d8d71445a2f10293cbd79762f1fb91f51dac800", "commit_訊息": "[DT]C01-20221114005 修正Web化系統管理工具TIPTOP整合設定中對映索引修改後沒儲存問題", "提交日期": "2022-11-17 10:03:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36bdee1880be4a0f4b3018fb8a959d490447b216", "commit_訊息": "[流程引擎]Q00-20221117001 修正自動簽核在多人處理關卡上沒有效果", "提交日期": "2022-11-17 09:23:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8f4b4453541a2a9b28979b7b816b7848e97cf17", "commit_訊息": "[Web]Q00-20221116003 修正 Checkbox、RadioButton 元件，若文字過多造成換行時，勾選按鈕會有偏移的問題", "提交日期": "2022-11-16 17:52:27", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea0964c68f4dac4caac828eb364237b566d7ed28", "commit_訊息": "[BPM APP]C01-20221025006 修正企業微信未進入菜單前從推播進入表單畫面時空白問題", "提交日期": "2022-11-16 14:40:46", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b11c86b923037dc5c91666369313da06df79d16a", "commit_訊息": "[Web]Q00-20221116002 修正個人資訊頁面載入，因為雙因素認證沒資料導致報錯", "提交日期": "2022-11-16 14:15:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f39465ca13f15565f960fcbf8ee20b0ebd236b34", "commit_訊息": "[BPM APP]C01-20220922002 修正移動端主旨與表單內容重疊跟取不到簽核歷程報錯問題", "提交日期": "2022-11-15 18:20:46", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "551a8b4af4b76208a5aa93500949abdbd665f238", "commit_訊息": "[Web]Q00-20221115002 修正流程設計師/流程模型/進階的主旨範本若有換行，會導致流程資料/流程資料查詢的查詢畫面無法顯示的問題", "提交日期": "2022-11-15 15:04:02", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/searchFormData/FormInstResultForSearching.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "57c3456ff5bb5d2d46018050434aa93bc9450345", "commit_訊息": "[Web]Q00-20221111005 修正員工代號有大寫時，使用iReport的列印功能會發生異常問題", "提交日期": "2022-11-15 14:02:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a0b09ba6b5e4a52297ff25fd545f3b47b112ee7d", "commit_訊息": "[Web]Q00-20221114005絕對定位表單及RWD表單，統一可設定背景色設定。", "提交日期": "2022-11-14 18:21:16", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/LinkElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7bd8e1c6be8a4de2ab27479bd1137fce1fda873d", "commit_訊息": "[WEB]Q00-20221114003 修正5884版本絕對位置表單下載附件異常，無法下載檔案", "提交日期": "2022-11-14 14:31:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cace94c2fe820f003d2fd9b5bedded1fce08eda4", "commit_訊息": "[Web]Q00-20221114002修正表單設計師Barcode元件異常問題。", "提交日期": "2022-11-14 12:28:50", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c06a488ce947eef80574dd70859d14b452dac475", "commit_訊息": "[WorkFlow]Q00-20221114001 修正附件URL帶有空格導致拋單敗", "提交日期": "2022-11-14 10:42:08", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/TiptopUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa8dbbe7ef556ba95a7db183f5d4f03fa802a90d", "commit_訊息": "[BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題", "提交日期": "2022-11-11 17:34:54", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "5846a40d1a707fc319ab804069734d00d195d80b", "commit_訊息": "[Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息", "提交日期": "2022-11-11 11:07:05", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "acbc5a2795816ada5acaabcb14fd54828d38114a", "commit_訊息": "[Web]Q00-*********** 修正逾期授權的人數也算進總授權數裡", "提交日期": "2022-11-10 11:40:18", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ada799a6b11a39b30de631e1c3386a6300accb4", "commit_訊息": "[流程引擎]Q00-20221109001 調整流程圖點選核決權限關卡，核決關卡改以關卡建立時間排序", "提交日期": "2022-11-09 17:27:22", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0ea3e8f32a1f88233795dfe16d4108978e43d4fc", "commit_訊息": "[流程引擎]Q00-20221108003 修正流程引擎的加簽函式功能「addCustomParallelAndSerialActivity」，加簽出來的關卡的表單未依照「參考關卡」呈現對應的「表單元件顯示」狀態", "提交日期": "2022-11-08 16:22:44", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c63012432d0225424e4c3dbea899b7e44acb4ef2", "commit_訊息": "[Web]Q00-20221108001修正輸入元件設置必填後，沒勾選隱藏標籤原label標籤會出現undefined", "提交日期": "2022-11-08 15:25:39", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea49cc2c6bd451e4d6a33c4813cde057b00855c7", "commit_訊息": "[Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日(修正多語系)", "提交日期": "2022-11-07 10:21:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b638db52c710f0de3d7cdacae7035fbe2febc41", "commit_訊息": "[Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日", "提交日期": "2022-11-07 10:15:56", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f08d120064769a64acd928a602cb0a34b258564f", "commit_訊息": "[表單設計師]Q00-20221106001 修正表單設計師中設置輔助格線的貼齊刻度無法暫存修改後的參數", "提交日期": "2022-11-06 16:37:01", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f675ffb4677941cd5b8c95f1d495e3f30749d4f0", "commit_訊息": "[Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。(補修正)", "提交日期": "2022-11-04 17:32:12", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f9afd5c868a813901192d7164c405a4c56bbe63", "commit_訊息": "[Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。", "提交日期": "2022-11-04 17:06:47", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "ddc8286c4f162d300aca7a9361b7fccd27177010", "commit_訊息": "[Web]Q00-20221104004 修正通知關卡指定離職人員時，離職交接人沒有作用。", "提交日期": "2022-11-04 15:59:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "33ab9934d8e1849cf0c30642339c42c55e854354", "commit_訊息": "[內部]Q00-20221104002 調整觸發自動簽核時間點的log", "提交日期": "2022-11-04 11:40:39", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a54cfacb2f7eaa1f2754a5e706331a2607d62cbb", "commit_訊息": "[BPM APP]C01-20220927008 修正移動端Grid顯示畫面上按鈕重疊問題", "提交日期": "2022-11-03 18:57:05", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3d600a41013c5a14d84b3bc31779a85a6036c03", "commit_訊息": "[流程設計師]Q00-20221103003 修正流程定義/事件處理/流程完成/網頁應用程式，第一次點擊編輯時畫面空白的問題", "提交日期": "2022-11-03 18:09:04", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/FormalParametersCellEditorRenderer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bf5499db5c93ab7115b4605b5812a50007571a6a", "commit_訊息": "[流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信", "提交日期": "2022-11-03 17:41:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0c707a452bcc259eb0ecba0e1354f23348bee124", "commit_訊息": "[WEB]Q00-20221103001 使用者撤銷流程，理由填空白字串時不允許撤銷流程", "提交日期": "2022-11-03 11:24:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cc4ac54b5a45e3fa3122c13f456bec43a23716b4", "commit_訊息": "[Tiptop]Q00-20221031002 修正log沒有辦法正常換日的問題，全部jar替換。", "提交日期": "2022-11-03 10:37:55", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/lib/CruiseControl/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/Log4J/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Log4J/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/lib/Log4J/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/lib/Log4J/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/Log4J/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/Log4J/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/lib/Log4J/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/Log4J/log4j.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "6a4bb6bffd198b538d19fd0bf94357d3692ca3c9", "commit_訊息": "[流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]", "提交日期": "2022-11-02 18:07:03", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4d03c1e1ba992fdd7df784a6deba949bd8541a2", "commit_訊息": "[Web]Q00-20221102001 修正checkbox設計時，若有勾選「最後一個選項額外產生輸入框」，表單中checkbox呈現與列印不一致的問題", "提交日期": "2022-11-02 14:53:14", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d9388ed93e5018361c80c7a40ba7ed2ef35178a1", "commit_訊息": "[WEB]Q00-20221101005 修正在表單上設定運算規則時有參考單身加總的元件時不會自動觸發更新的問題", "提交日期": "2022-11-01 17:40:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6b159215c2fde8f7ed7c73b510678fc63a04da0f", "commit_訊息": "[WEB]Q00-20221028003 補修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)", "提交日期": "2022-11-01 16:41:19", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "246d1ec0f14148c933baa369488376d6c2ab6588", "commit_訊息": "[Web]Q00-20221101003 修正使用者若有離職作業維護，點選離職人員會跳到登入畫面的問題", "提交日期": "2022-11-01 15:24:25", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c90a418e46a8f7dd33c955aeb9179ab2802445a5", "commit_訊息": "[流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]", "提交日期": "2022-10-31 17:41:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "91fee61a0899a6dca7ad626d5dfe67c49cde6696", "commit_訊息": "[流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常", "提交日期": "2022-10-31 16:23:44", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c81a4596e44c081a30e19479bb697f5a9eb68cea", "commit_訊息": "[流程設計師]A00-20221026001 修正新增的預設關卡ID如果默認與已經存在的關卡ID一樣，儲存流程時不會異常導致開啟該流程直接報錯", "提交日期": "2022-10-31 15:24:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNFactory.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ac66e78c85cb8a29f55e41e77f0cdc8eb6611d70", "commit_訊息": "[BPM APP]C01-20220921001 修正移動端在簽核後兩條流程的表單內容會串單問題", "提交日期": "2022-10-31 11:47:13", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "b44b02abc330cfaf9b1292a6b653a1b1167d109e", "commit_訊息": "[WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)", "提交日期": "2022-10-28 17:47:45", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d7e4743d51b68d0ded7d90780745b9c0ca47fe28", "commit_訊息": "[WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>", "提交日期": "2022-10-28 17:26:12", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0eb2d3d8ae2224ea746588a8fa77ac499cc0c0ec", "commit_訊息": "[WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。(補修正)", "提交日期": "2022-10-28 15:59:37", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c334dcb4b409cce073207bcb31d09ac273e44ed0", "commit_訊息": "[流程引擎]Q00-20221028002 修正Oracle資料庫，若流程有設計執行服務任務並將回傳值回寫至流程變數時，服務任務會報錯的異常", "提交日期": "2022-10-28 15:21:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ab0485d9d9576cb5b04392e7164d5167b07ba8cb", "commit_訊息": "[WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。", "提交日期": "2022-10-28 15:20:12", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd219371af216d496a59f94bb90c04080f02dddd", "commit_訊息": "[系統管理工具]A00-*********** 若管理員將有組織設計師權限的人員離職，並且移除組織及部門，會導致使用權限設定沒有畫面的問題", "提交日期": "2022-10-27 18:59:08", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WizardAuthorityManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/WizardAuthorityManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/OrgWizardAuthorityScopeController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/toolauth/OrgAuthConfPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "921f9374b88cad164defc3643fb434fc67bff34e", "commit_訊息": "[內部]Q00-*********** 調整PDF8Convert轉檔機制由synchronized改為多執行序執行，並增加debuglog", "提交日期": "2022-10-27 15:27:51", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "58a242d861255d407187cd1e525b9d352f1cce7d", "commit_訊息": "[Web]S00-20220510001新增運算規則可以選取到hidden元件。", "提交日期": "2022-10-26 16:01:07", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a835acea7ef36e5938d9c96ecd638af797d26ce2", "commit_訊息": "[Web]Q00-20221019001 修正響應式表單Grid元件設定凍結欄位時縮放瀏覽器時會出現跑版的問題", "提交日期": "2022-10-26 11:14:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.18.3.js", "修改狀態": "重新命名", "狀態代碼": "R099"}], "變更檔案數量": 4}, {"commit_hash": "c2b231bb1f3e6671896ede09063354a8106983d0", "commit_訊息": "[Web]Q00-20221020004 修正 TextBox 元件進階功能的運算規則，若將已綁定的欄位值輸入後又刪除，會顯示 NaN 的問題", "提交日期": "2022-10-26 09:06:05", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75386735ef4e4aa14c63749eea9a40ba21a9a2f3", "commit_訊息": "[流程引擎]Q00-20221025003 調整當核決關卡解析時；若解析人員在同一個組織下有多個兼職部門，且兼職部門的職務核決層級的level都相同時，則以該人員的主部門作為解析部門", "提交日期": "2022-10-25 17:29:45", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34e1a626e5cdac6361bcaf053f5d9613b9aeea03", "commit_訊息": "[Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。", "提交日期": "2022-10-25 15:45:35", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fc3a5d88dae712787062c2018347e6d2d73e7034", "commit_訊息": "[Web]Q00-20221006004 上傳附件功能，優化使用者提示，且上傳過程不可點擊關閉按鈕。", "提交日期": "2022-10-25 15:25:10", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "927627b7b61f8622f56c59da50939a5a31b22e2f", "commit_訊息": "[Web]V00-20221019001修正流程管理/監控流程 選擇「已撤銷」流程，匯出Excel發現多了「執行中的關卡」跟「目前處理者」的欄位。", "提交日期": "2022-10-25 15:18:30", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16e41849a9f1ffad6ccef7fe9cb2231eff7c9205", "commit_訊息": "[系統管理工具]A00-20221117001 修正儲存流程因為Application Server位址沒有填上PORT導致失敗", "提交日期": "2022-11-21 15:55:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "304cbdb38e6e3103a55fc1b86794a25b5542a7ab", "commit_訊息": "[Web]Q00-20221026002 新增判斷二階快取應確認來源位置是否為本地端(localhost / 127.0.0.1)若是則不須額外清除。", "提交日期": "2022-10-26 14:13:48", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d1c01f6a8a387e635780e90b0b984ba092c86cb2", "commit_訊息": "[Web]Q00-20221121002 修正關卡設定附近在線閱覽按鈕不顯示，在流程草稿上傳附件還是會顯示在線閱覽按鈕", "提交日期": "2022-11-21 15:48:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "67df0bcb71552b497582364a54963bf575a51ed2", "commit_訊息": "[在線閱覽] Q00-20221111002 修正追蹤流程重發新流程，當第一關關卡有設定上傳附件不使用在線閱覽時，上傳附件仍會出現在線閱覽的選項", "提交日期": "2022-11-11 15:00:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c3d835633c7eaa3dbb96f0764cb611bbb2eee4ff", "commit_訊息": "[Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題", "提交日期": "2022-11-16 14:27:02", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6b431c5cc12359ecca76a910f7c08f85231508e3", "commit_訊息": "[WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定", "提交日期": "2022-11-01 12:00:20", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d35f4a1cd4ef39a9944bb61ef97566d601207b9", "commit_訊息": "[雙因素模組]Q00-20221026005 在未授權時，BPM首頁左側功能列會顯示雙因素模組功能", "提交日期": "2022-10-26 16:02:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}