{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.2_20230721", "date": "2023-07-17 09:25:32", "message": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]", "author": "周权"}, "舊分支": {"branch_name": "release_5.8.9.2", "date": "2023-05-26 10:36:32", "message": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 10:55:57", "新增commit數量": 82, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "8cce2261fc5f227f5106e398481c42cca377cea4", "commit_訊息": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]", "提交日期": "2023-07-17 09:25:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ac8ef6489b530b4906d10748851897418222fd1b", "commit_訊息": "[流程引擎]Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序[补修正]", "提交日期": "2023-07-18 12:40:40", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "386562baff8e1cfd0c6e853df74435fe0cd596d9", "commit_訊息": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。", "提交日期": "2023-07-14 17:36:50", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ec2aaa8a764e59ab64ccd11bf93b78bfb91fb6e6", "commit_訊息": "[流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序(補修正)", "提交日期": "2023-05-30 16:03:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActivityInstanceComparator.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "13cc2014085068c6433b880199df9daf4519b499", "commit_訊息": "[流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序", "提交日期": "2023-05-25 14:03:05", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79c9a925292248de488b5944292104a6ade61401", "commit_訊息": "[ESS]Q00-20230710001 調整log錯誤訊息的顯示", "提交日期": "2023-07-10 10:49:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c6a995677ed1a4d523e715c2685b88a7f62d59b", "commit_訊息": "[Web] Q00-20230713005 新增登入登出LOG印出SessionId，修正模擬為同一IP時取瀏覽器資訊異常問題", "提交日期": "2023-07-20 13:44:37", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e0e066dfdb1fa81d7dde0a12847582c19a056424", "commit_訊息": "[在地化] Q00-20230620003 增加驗證SSOkey時，時間間隔超過5分鐘，印出LOG訊息", "提交日期": "2023-06-20 17:03:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c2b454bdefe63b2353f5ef81b66dec59cea18c3", "commit_訊息": "[DT]C01-20230719005 修正設計師使用權限管理中的組織設計師清單其id與名稱顯示異常問題", "提交日期": "2023-07-20 12:13:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/ParticipantInfoAcquirer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/AccessCrtlMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "aafe83f9744a0522fa4b16467f8fc1f6b559336f", "commit_訊息": "[Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員 [補修正]", "提交日期": "2023-07-20 11:16:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "edabb9e0cb8722c57fd8ddd88d5d79c8bec2ed7a", "commit_訊息": "[流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException[补修正]", "提交日期": "2023-07-20 10:33:53", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc582f6da64668f93d0c6ffaa029deed2235e987", "commit_訊息": "[流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException", "提交日期": "2023-07-18 16:45:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f788d638ad67ac07d9b8d81d2a0e3d0f024c7a0", "commit_訊息": "[Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員", "提交日期": "2023-07-19 14:28:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c00c08cb53e93fa28864627f6e82c57ee87f3bb8", "commit_訊息": "[流程引擎] Q00-20230717003 修正終止流程時，偶發ProcessInstance與BamProInstData狀態不一致問題", "提交日期": "2023-07-17 17:55:45", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "2f528ac2e10384e8bc2909b3d893c6614bf50cdb", "commit_訊息": "[web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复", "提交日期": "2023-07-17 14:37:58", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8836d2fef061b8277612950109ca6c9d06d1915c", "commit_訊息": "[流程引擎] Q00-20230714001 修正服務任務關卡執行後，不會重新組成全文檢索欄位問題", "提交日期": "2023-07-14 14:48:48", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "62d8599eafb1afd676fc0759806242adb502d48a", "commit_訊息": "[Web] Q00-20230713003 優化使用者登入時異常時的LOG", "提交日期": "2023-07-13 11:44:10", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/PersistentObjectHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c3136f94185f1a6a7ae4cd4caed76991059cb0a", "commit_訊息": "[Web]Q00-20230713002 修正使用者為部門主管從Portal進入BPM點選首頁顯示內容不為主管首頁", "提交日期": "2023-07-13 10:23:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8460f5b46051afe0a1bf0d9ea9d70e36b25a4b41", "commit_訊息": "[web]Q00-20230712003 修正在转存表单时栏位原數值為小數點後兩位，轉存表單後僅剩小數點後一位", "提交日期": "2023-07-12 15:59:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1340937f84f9f2c661d156f10b560fdf7070e44d", "commit_訊息": "[Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白。", "提交日期": "2023-07-11 14:12:59", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4148e0147df5360584669f5a36c4c6d37b30af64", "commit_訊息": "[流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題(補)", "提交日期": "2023-07-11 10:24:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c2a731f0ba096ff654dd7ff1958831b92a28550b", "commit_訊息": "[流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題。", "提交日期": "2023-07-07 14:39:22", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5ae761b8897e3299c43924e6b126a748d53654ae", "commit_訊息": "[web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题", "提交日期": "2023-07-10 16:56:51", "作者": "liuxua", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7c967dc6e64de3e36dcfc2a6743fe64736beed0c", "commit_訊息": "[web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题", "提交日期": "2023-07-10 16:51:34", "作者": "liuxua", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c4a1138898a33d8aaf6fac7f8040c41c183d3d6", "commit_訊息": "[其他]Q00-20230704001 調整BCL8單個檔案轉檔逾時時間由預設2分鐘改為預設10分鐘", "提交日期": "2023-07-10 11:38:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3145c639005ba13adaeb4e0ee35d7716266bada", "commit_訊息": "[組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。", "提交日期": "2023-07-06 15:22:50", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9286e2062de27ff6f0ec71845f20857cb357f8d", "commit_訊息": "[Web]Q00-20230705002 修正表單在预览时，更換image后，显示图片不正确的问题", "提交日期": "2023-07-05 14:23:40", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ImageElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b8bf4fe66412e250d96f526e456fdf27cb66c531", "commit_訊息": "[Web]Q00-20230704003 調整formScript撰寫ajax加簽關卡後，需要更新session裡面的ProcessInst的相關屬性(Processpackage,ProcessDef等屬性)，避免預覽流程仍以加簽前的定義做解析", "提交日期": "2023-07-04 17:40:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e61a884d84b124afd3f9fe7ce225455f5494d141", "commit_訊息": "[Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級(補)", "提交日期": "2023-07-04 17:34:15", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f41906d8ea89b45960d13034fecd607eece5268", "commit_訊息": "[Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級", "提交日期": "2023-07-04 16:43:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b83a5ca0fc7355b63c5a0b7c0ae1b1ae2903d4c4", "commit_訊息": "[Web]Q00-20230704001 調整CommonAccessor.updateConnectedUserInfo()更新使用者時間的方法，當更新異常時，由前端畫面提示「更新線上時間失敗」改為後端serverlog記錄就好", "提交日期": "2023-07-04 17:02:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "80c003dc533af2a497d5589f2639c1220d34f0ea", "commit_訊息": "[Web] Q00-20230703004 修正表單列印畫面元件跑版問題，邊界調整為0", "提交日期": "2023-07-03 16:31:00", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "725e5ca7380785ed5c5b1c2eadfcf137301f99d1", "commit_訊息": "[Web]Q00-20230609001 調整待辦流程開啟列印表單時Grid數據沒有加載的問題", "提交日期": "2023-06-09 12:02:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "496c3900bcfcfa0ee21a76f9f83e3c67d2d93990", "commit_訊息": "[SAP]Q00-20230628003 修正SAP整合回寫呼叫SAP的invoke服務，當SAP回傳的訊息需存放在Grid時，若Grid內容為空時，可能會導致formInstance.fieldValues產生多組相同Grid代號的內容", "提交日期": "2023-06-28 18:02:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d4c925a54cd3b1935c5cef028033a6f1b9458e2", "commit_訊息": "[SAP]Q00-20230627003 SAP整合的invoke服務任務增加表單相關資訊log", "提交日期": "2023-06-27 15:09:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70284b28ae495eb228734f2f6683c402f9486448", "commit_訊息": "[TipTop] Q00-20230627002 調整TIPTOP的附件選擇txt時，上傳文件且未填說明欄位，轉檔異常問題。", "提交日期": "2023-06-27 14:41:06", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "66d963f1cf56945d586c84cc56cd7851d36d243c", "commit_訊息": "[T100]Q00-20230627001 修正關卡設置\"所有附件皆需開啟過\"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送", "提交日期": "2023-06-27 13:39:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc65303df2e29fd80e723ebd68da1b825cb3d0c8", "commit_訊息": "[Tiptop] Q00-20230626002 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。", "提交日期": "2023-06-26 16:28:03", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a98fbcd500a2f55192f5e8516bcb87d48cdd9ede", "commit_訊息": "[Web] Q00-20230621003 修正Rwd-Grid 設置必填時，Alert訊息異常問題", "提交日期": "2023-06-21 11:44:24", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f4e8cc1b1e5872edd97568885677d5f1389baa0f", "commit_訊息": "[Web] Q00-20230620001 調整絕對定位表單追蹤流程下列印表單畫面。", "提交日期": "2023-06-20 14:56:48", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "70df8e2b201aebca721e28c12cb9807900007148", "commit_訊息": "[Web]Q00-20230619001 修正Grid元件在多欄位時欄位寬度異常顯示問題", "提交日期": "2023-06-19 10:12:14", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "18e7381a2579f976079d29a9e616128cf3869280", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)", "提交日期": "2023-06-16 16:45:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "abeabeed156b7026be0dcfbc69fb6f59cc9bae48", "commit_訊息": "[Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題[補]", "提交日期": "2023-06-16 13:56:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b81ce47c61e8107c4ed254b8219768b4f077d4c1", "commit_訊息": "[Web]Q00-20230615002 修正離職維護作業無法開啟", "提交日期": "2023-06-15 17:18:15", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2bbe00d25531c762f9618270e49a9ad1e63d67fc", "commit_訊息": "[Web] Q00-20230615001 修正客制開窗order by轉小寫導致模糊查詢異常問題。", "提交日期": "2023-06-15 11:52:52", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c068bc54562590d6548eaa7e358fd9753bb0e5c0", "commit_訊息": "[Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補)", "提交日期": "2023-06-05 11:41:57", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6afb05dadf8a24d7c3a20b56d27ff507dececbd8", "commit_訊息": "[Web] Q00-20230613003 調整參與者型式的關卡頁面的「檢視轉派歷程」按鈕圖示。", "提交日期": "2023-06-13 14:18:28", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "02c0450d001b5267e58f30b51eca64ae478a4711", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)", "提交日期": "2023-06-13 11:36:01", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd71e74c850bdab523818fed0bd80ecbf8d8702e", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。", "提交日期": "2023-06-12 14:57:27", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe114df67129a5ea377789fdf8453eaeb686eb62", "commit_訊息": "[Web]Q00-20230612003 修正Script撰寫Grid的setColumnWith語法會跳出alert", "提交日期": "2023-06-12 12:02:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a8b4e25bc25a0d29efc9bf12c6d11dd0cee18818", "commit_訊息": "[在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕", "提交日期": "2023-06-12 11:27:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cd837fc6847cbc1581f23a344abdac9a2540f0f4", "commit_訊息": "[Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間[補修正]", "提交日期": "2023-05-26 10:45:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee073a7c97ffa26a05ca184b5f52f2d34395482c", "commit_訊息": "[Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間", "提交日期": "2023-05-25 15:33:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc98a848c088df53b51b0910d727ccc537891167", "commit_訊息": "[Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息(補修正)", "提交日期": "2023-06-12 10:45:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "152815aa6f63074961368e6c2682abce9b796aff", "commit_訊息": "[Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息", "提交日期": "2023-06-09 17:16:16", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "87231d6677524bca1103b0d5ea5d82c77947d78f", "commit_訊息": "[Web] Q00-20230609004 修正匯入Excel資料內有,會被替換成空白問題", "提交日期": "2023-06-09 15:13:05", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "54ade605839d6befe3b6720e28974a214591de53", "commit_訊息": "[Web]A00-20230605001 修正在待辦情況下將HandWriting元件透過Script設置disable時沒有作用問題", "提交日期": "2023-06-06 13:57:44", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c565b7e43091d7c353185f535ac2d264dcb5760", "commit_訊息": "[Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系", "提交日期": "2023-06-07 15:58:33", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8812df4c09c48db0f2d506641f0d4239796cdba6", "commit_訊息": "[Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題", "提交日期": "2023-06-07 14:51:50", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05a01ebc45a6add25c0f63a8346dd8b052a31834", "commit_訊息": "[ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持\"通知\"選項", "提交日期": "2023-06-06 14:44:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecc88a61dbc4ea0d5ae16956dfac7e9c842d04e7", "commit_訊息": "[流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求", "提交日期": "2023-06-05 17:14:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/WebApplicationAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "49a235dc520fd84652e53353c19fa8885e68c652", "commit_訊息": "[Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示", "提交日期": "2023-06-05 10:37:25", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8088ac8fef882d3642e763766e45acdc61474dd9", "commit_訊息": "[Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題", "提交日期": "2023-06-02 10:54:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f48fac77aa1f751759f325043641f43783dbc0e1", "commit_訊息": "[WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料", "提交日期": "2023-06-02 11:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "acdfd7b7d457a9d312a383bdfdcf3d542c86ff76", "commit_訊息": "[WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]", "提交日期": "2023-06-02 10:23:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "01f34b18f3cb2674099a28bbc63dd503bc57ceea", "commit_訊息": "[BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題", "提交日期": "2023-06-01 15:10:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8186af7b5bb50188f75c03ec114653c012364ad8", "commit_訊息": "[其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容", "提交日期": "2023-06-01 11:24:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dee1ff9a9f49a64f2036216c91549582832b47c5", "commit_訊息": "[Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常", "提交日期": "2023-06-01 10:56:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/lib/Dwr/dwr.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbba77b6293efa1c468c03bbea5681d634c91537", "commit_訊息": "[WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息", "提交日期": "2023-06-01 12:04:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7465c9e3fae294f55777e734f7262a6de032f0fd", "commit_訊息": "[WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤", "提交日期": "2023-05-31 17:47:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1654eb781f177acdabd651a99072bebbfb3464f", "commit_訊息": "[Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態", "提交日期": "2023-05-30 10:27:14", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "256583ba35be24e43684df0f48927bbe9ab01313", "commit_訊息": "[Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)", "提交日期": "2023-05-29 17:06:25", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f879995c811268ee03a7fa0f5bb44fb8b838acc1", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正", "提交日期": "2023-05-29 16:54:46", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "eb4fcda9bed15bec18820491e2c67e769a24a91d", "commit_訊息": "[WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起", "提交日期": "2023-05-26 16:38:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d72accd32ca2ac64d731380966e8b8f53529744d", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題", "提交日期": "2023-05-26 15:02:51", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ae609cd95bff0a2b8728345da221270ec06eafb", "commit_訊息": "[Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題", "提交日期": "2023-05-26 14:59:32", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0eabc66d8706bfec8d0f937d7f5ad593bfbc736b", "commit_訊息": "[WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]", "提交日期": "2023-05-26 10:10:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "64fa32e7bc15301766f00da2e16ea1dbe87fdc20", "commit_訊息": "[Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題", "提交日期": "2023-05-25 10:16:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ab7a426ee61294822f61d9d19fb01ace9ac934c4", "commit_訊息": "[流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常", "提交日期": "2023-05-24 17:36:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cb8973a1085888406960b5ae2c4373b226bd9b14", "commit_訊息": "[Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加", "提交日期": "2023-05-24 17:01:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed1a7b3d587b5d6d7a041912c9f502e537d2eff5", "commit_訊息": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "提交日期": "2023-05-26 10:36:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "798a76611aaaefd9e7ca40e01e571b75e5b99e7a", "commit_訊息": "[組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題", "提交日期": "2023-05-25 19:35:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}