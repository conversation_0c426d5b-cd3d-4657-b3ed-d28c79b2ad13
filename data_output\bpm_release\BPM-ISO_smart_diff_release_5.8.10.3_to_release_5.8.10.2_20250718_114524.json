{"比較資訊": {"專案ID": "BPM-ISO", "倉庫路徑": "D:\\IDEA_workspace\\BPM-ISO", "新分支": {"branch_name": "release_5.8.10.3", "date": "2024-09-02 14:52:35", "message": "[ISO]C01-20240829007 修正文件類別管理的回到查詢清單提示文字顯示為亂碼的異常", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.10.2", "date": "2024-06-25 15:50:24", "message": "[文件智能家]修正因流程主機位址不是127.0.0.1或localhost導致取得AccessToken失敗，間接導致不會觸發ChatFile接口", "author": "lorenchang"}, "比較時間": "2025-07-18 11:45:24", "新增commit數量": 20, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "f400f656c666dde5b1705c453bfb9d74ba6b0a5d", "commit_訊息": "[ISO]C01-20240829007 修正文件類別管理的回到查詢清單提示文字顯示為亂碼的異常", "提交日期": "2024-09-02 14:52:35", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ManageDocCategory.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c7da3640de4ce3d9317dbf05b296e72292d7d0b", "commit_訊息": "C01-20240815005 修正Oracle資料庫在ISO文管首頁模糊查詢會查詢很久的議題", "提交日期": "2024-08-20 14:14:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08025f7027fe58115e01fe46161415519576e0c6", "commit_訊息": "[Word套表] PDF浮水印属性管理，新增字段(type)", "提交日期": "2024-08-20 09:51:43", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.hbm.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/util/ConditionUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "3713e687e25b07c1b98cd3eed0fdf2c48e5c590e", "commit_訊息": "[Word套表] PDF浮水印管理作业隐藏不需要的栏位[补]", "提交日期": "2024-08-13 16:48:29", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9d58afacbd15b44ffb730106f3a06c89f2e9155", "commit_訊息": "[Word套表] 调整<#SystemDate>只显示为日期", "提交日期": "2024-08-13 09:04:58", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "26a5bde6c699ee44a9895bf64e697dc081824332", "commit_訊息": "[Word套表] 调整从设计工具进入\"PDF浮水印管理\"作业的[监视浮水印]逻辑", "提交日期": "2024-08-08 15:06:05", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9dc4afc6781c213eb325b9ce6eeae3db26329d19", "commit_訊息": "[Word套表] PDF浮水印管理作业隐藏不需要的栏位", "提交日期": "2024-08-07 14:48:45", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a2a448afdc813367c123069cd324590cd18cfc74", "commit_訊息": "[Word套表] 调整json传已经插入文字的图片", "提交日期": "2024-08-06 16:47:45", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "3d3654d31a22d384e5cc2cfb1dd3ee5af61c51fe", "commit_訊息": "[Word套表] 调整获取原有发行图章json", "提交日期": "2024-07-31 16:51:08", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "734473c9e77e2ec16bdd9998b0367c204a040f1d", "commit_訊息": "[ISO]C01-20240730001 修正建立新ISO文件時，若指定的浮水印不存在將導致服務任務出現NullPointerException，增加明確的Log", "提交日期": "2024-07-30 15:36:03", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5e213382a01144366e5f116a9488badf332ead8d", "commit_訊息": "[Word套表] 设计工具增加“PDF浮水印圖片管理”、“PDF浮水印屬性管理”两个作业", "提交日期": "2024-07-29 15:06:46", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/util/filter/FilterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dea5e1e9b9dd3d1489ab148223488ad56066e0de", "commit_訊息": "[資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js", "提交日期": "2024-07-22 11:33:45", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ManageDocFileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7082d889f293119e3b1fa1d5b0d5bd4c73e45237", "commit_訊息": "[Word套表] 新增通过OID取得圖片浮水印的逻辑", "提交日期": "2024-07-19 10:46:23", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "461c411e52e761c546bf9880d2c4677feca72e2d", "commit_訊息": "[ISO] C01-20240612001 調整由評審單發起的變更單流程，若中止或者撤銷，要將評審單狀態改為Close", "提交日期": "2024-07-10 15:48:21", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "126b8deb256c27c84d5b80cf673acbecf4051393", "commit_訊息": "[內部]更新 README.md", "提交日期": "2024-07-09 15:30:58", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "README.md", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0acccfe27682bb029942df13a022f3b111012a5f", "commit_訊息": "Revert \"[ISO] Q00-20240703001 修正ISO階層ID相同時，開窗頁面被濾掉問題\"", "提交日期": "2024-07-04 13:36:33", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/comparator/ISODocLevelComparator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2789aa861712f6ca1ff85421068b861003c4315f", "commit_訊息": "[ISO] Q00-20240703001 修正ISO階層ID相同時，開窗頁面被濾掉問題", "提交日期": "2024-07-03 14:13:33", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/comparator/ISODocLevelComparator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "431c11f99be358f3d55e8be1aea15ab048aa65c7", "commit_訊息": "[ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)", "提交日期": "2024-06-26 16:31:30", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b3bd70a78821ace68934fda1a71387a89db5223b", "commit_訊息": "[ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)", "提交日期": "2024-07-01 16:56:55", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFullTextSearch.hbm.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFullTextSearch.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/util/hibernate/module.hibernate.cfg.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e38f7deb695a2e0b5b97078c29ae839cec2d086e", "commit_訊息": "[ISO] C01-20240625002 新增全文檢索查詢結果上限功能", "提交日期": "2024-06-26 16:31:30", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOSearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/SearchCondiction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/DocListController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/IndexingHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ManageDocumentMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}]}