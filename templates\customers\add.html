{% extends "base.html" %}

{% block title %}新增客戶連線 - BPM服務部好用工具{% endblock %}

{% block content %}
<div class="container">
    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fas fa-plus me-3"></i>
                新增客戶連線
            </h1>
            <p class="page-subtitle">建立新的客戶連線資訊</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        客戶連線資訊
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="/customers/add" id="addCustomerForm">
                        <div class="row g-3">
                            <!-- 公司名稱 -->
                            <div class="col-md-6">
                                <label for="company_name" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    公司名稱 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="company_name" 
                                       name="company_name" required placeholder="請輸入公司名稱">
                            </div>

                            <!-- 產品類型 -->
                            <div class="col-md-6">
                                <label for="product_type" class="form-label">
                                    <i class="fas fa-cube me-1"></i>
                                    產品類型 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="product_type" name="product_type" required>
                                    <option value="">請選擇產品類型</option>
                                    {% for product in product_types %}
                                    <option value="{{ product }}">{{ product }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- 伺服器IP -->
                            <div class="col-md-6">
                                <label for="server_ip" class="form-label">
                                    <i class="fas fa-server me-1"></i>
                                    伺服器IP <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="server_ip" 
                                       name="server_ip" required placeholder="例：*************">
                            </div>

                            <!-- 埠號 -->
                            <div class="col-md-6">
                                <label for="port" class="form-label">
                                    <i class="fas fa-plug me-1"></i>
                                    埠號
                                </label>
                                <input type="text" class="form-control" id="port" 
                                       name="port" placeholder="預設：1433">
                            </div>

                            <!-- 資料庫名稱 -->
                            <div class="col-md-6">
                                <label for="database_name" class="form-label">
                                    <i class="fas fa-database me-1"></i>
                                    資料庫名稱 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="database_name" 
                                       name="database_name" required placeholder="請輸入資料庫名稱">
                            </div>

                            <!-- 使用者名稱 -->
                            <div class="col-md-6">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    使用者名稱 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" 
                                       name="username" required placeholder="請輸入使用者名稱">
                            </div>

                            <!-- 密碼 -->
                            <div class="col-12">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    密碼 <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" 
                                           name="password" required placeholder="請輸入密碼">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 說明 -->
                            <div class="col-12">
                                <label for="description" class="form-label">
                                    <i class="fas fa-comment me-1"></i>
                                    說明
                                </label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="3" placeholder="選填：其他相關說明或備註"></textarea>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- 按鈕區域 -->
                        <div class="d-flex justify-content-between">
                            <a href="/customers" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回列表
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-warning me-2" id="resetForm">
                                    <i class="fas fa-undo me-2"></i>重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>儲存
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 提示資訊 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        填寫說明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>必填欄位</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>公司名稱</li>
                                <li><i class="fas fa-check text-success me-2"></i>產品類型</li>
                                <li><i class="fas fa-check text-success me-2"></i>伺服器IP</li>
                                <li><i class="fas fa-check text-success me-2"></i>資料庫名稱</li>
                                <li><i class="fas fa-check text-success me-2"></i>使用者名稱</li>
                                <li><i class="fas fa-check text-success me-2"></i>密碼</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>注意事項</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>埠號預設為 1433</li>
                                <li><i class="fas fa-shield-alt text-info me-2"></i>密碼將安全儲存</li>
                                <li><i class="fas fa-edit text-primary me-2"></i>資料可隨時修改</li>
                                <li><i class="fas fa-trash text-danger me-2"></i>可隨時刪除記錄</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 密碼顯示/隱藏切換
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // 重置表單
    $('#resetForm').click(function() {
        if (confirm('確定要重置表單嗎？所有已填寫的資料將會清除。')) {
            $('#addCustomerForm')[0].reset();
            $('#password').attr('type', 'password');
            $('#togglePassword i').removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // 表單驗證
    $('#addCustomerForm').submit(function(e) {
        const serverIp = $('#server_ip').val();
        const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        
        if (serverIp && !ipPattern.test(serverIp)) {
            e.preventDefault();
            alert('請輸入有效的IP位址格式（例：*************）');
            $('#server_ip').focus();
            return false;
        }

        const port = $('#port').val();
        if (port && (isNaN(port) || port < 1 || port > 65535)) {
            e.preventDefault();
            alert('埠號必須是 1-65535 之間的數字');
            $('#port').focus();
            return false;
        }

        // 顯示載入狀態
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>儲存中...');
    });

    // IP 位址格式提示
    $('#server_ip').on('blur', function() {
        const value = $(this).val();
        if (value) {
            const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            if (!ipPattern.test(value)) {
                $(this).addClass('is-invalid');
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">請輸入有效的IP位址格式</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        }
    });

    // 埠號格式提示
    $('#port').on('blur', function() {
        const value = $(this).val();
        if (value) {
            if (isNaN(value) || value < 1 || value > 65535) {
                $(this).addClass('is-invalid');
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">埠號必須是 1-65535 之間的數字</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        }
    });
});
</script>
{% endblock %}
