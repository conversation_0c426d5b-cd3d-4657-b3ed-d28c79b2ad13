{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.9.3", "date": "2023-08-23 13:15:30", "message": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "author": "林致帆"}, "舊分支": {"branch_name": "release_5.8.9.2", "date": "2023-05-26 10:36:32", "message": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 11:32:15", "新增commit數量": 524, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "45e7700bb2c35b92f1a3a603c0724bc5dac19c76", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-23 13:15:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@ebg/process-default/\\351\\233\\273\\347\\260\\275\\346\\234\\203\\350\\276\\246\\345\\226\\256.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "95e562a355d7e8cacb8ac362f6921eb640dcd268", "commit_訊息": "[DT]V00-20230810009 修正更新使用者因密碼卡控功能會卡在loading畫面問題", "提交日期": "2023-08-23 10:46:40", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1984f6eb15010c74094f252e68338d7b1d011af6", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-23 09:13:16", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGCreateForm.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53f05e61657e8ce08da59dd78cdfc4ffd4ac2ab1", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-22 18:01:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fde689c609cf0c5a11897d081481acfed1adbf66", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-22 17:52:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb93df52f37767d6235128231a2bd0fd94aabe1f", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-22 17:49:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/EBGAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGFormManager.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGPropertise.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGSignerTemplate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "7f5fd25e19dfc8e761b2e9b3a967edfdf13e179c", "commit_訊息": "[BPM APP]新增釘釘集成多對一的機制[補]", "提交日期": "2023-08-22 13:53:42", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "af60cfc1172e3469ddbd84ada8f496411220145c", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-22 11:25:14", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "5d3fbfc22facb9024e47a48793856f2488fe53a1", "commit_訊息": "[Web]V00-20230817001 新增grid後無法把值代回繫結元件", "提交日期": "2023-08-22 11:24:06", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84d0d7e968861a66fe1da7121a2de0bdf84aecf7", "commit_訊息": "[web]V00-20230817002 grid高度明明還夠卻長出了滾軸问题修复", "提交日期": "2023-08-22 11:22:33", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f3912269f2a6d54c8ad078fa2877f12db61c7762", "commit_訊息": "[內部]調整流程設計師工具Web化改開窗顯示[補]", "提交日期": "2023-08-21 17:19:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74998e39fb6e268f7898895b721fce0b065e19e0", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-21 16:39:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ebgMofule/EbgDelegate.java", "修改狀態": "重新命名", "狀態代碼": "R099"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/EBGAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "44f817b3da9a28dd90fcbeb14325137ce510d7f5", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-21 14:05:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGCreateForm.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGPropertise.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGSignerTemplate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@ebg/form-default/EBGForm.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "ff7355e35e707f76c7a490676896d694a2e0830d", "commit_訊息": "[內部]流程設計師工具Web化多語系新增", "提交日期": "2023-08-18 20:16:45", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f4d56c6a16693aa59be23e0ce406d1c85b8c4eaa", "commit_訊息": "[DT]V00-20230817003 修正核決活動中設定核決規則簽入後無效果問題", "提交日期": "2023-08-18 18:25:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "760905e97edc1932c7c31d509e79f11330cc42be", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-18 17:44:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9cd38cf1eb974ecddcb52928d760ff77bc86fd05", "commit_訊息": "[DT]V00-20230817004 修正活動進階屬性設定簽入後無效果問題", "提交日期": "2023-08-18 11:52:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c9b003bb06b8d60a456ac7b7ada0ff4b194a539f", "commit_訊息": "[DT]V00-20230810008 修正移動流程模型錯誤問題", "提交日期": "2023-08-18 11:29:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "343a0979e4b563f34a4e6bee53be17323bbe269f", "commit_訊息": "[Web] Q00-20230817004 修改手机版追踪流程，取回重办、撤销流程显示两个时间", "提交日期": "2023-08-18 10:41:47", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4919713a09fc9c6a5b3952c78b155cf9224958e8", "commit_訊息": "[DT]V00-20230810003 修正儲存流程模型錯誤問題[補]", "提交日期": "2023-08-17 19:26:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "854cfb16dc3f5f7829712a69ccbf7ee3d9b370ac", "commit_訊息": "[Web]Q00-20230817001 修正有關卡設定為列印模式，從待辦事項點選列印時，格式會跑掉", "提交日期": "2023-08-17 11:05:11", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a63430159c467ab36356367e215db1e76a05ae5b", "commit_訊息": "[內部]更新5893patch", "提交日期": "2023-08-16 16:21:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3446e052941a8cde2ac830477c0b302a38f32b31", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-16 16:13:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f7ff308cc3a383f69c00737b82a9ddfe803e5db3", "commit_訊息": "[內部] 更新達夢 DM8 Create SQL", "提交日期": "2023-08-16 16:05:35", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "777ac883b6122a6a836d7999c8724bbc75082b3f", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-16 13:32:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/copyfiles/ebg/form-default/EBGForm.form", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/ebg/process-default/\\351\\233\\273\\347\\260\\275\\346\\234\\203\\350\\276\\246\\345\\226\\256.bpmn\"", "修改狀態": "重新命名", "狀態代碼": "R095"}], "變更檔案數量": 2}, {"commit_hash": "79d5a0f4c8000d3c6232e3961dabdb737efe6b15", "commit_訊息": "[Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常 [补修正]", "提交日期": "2023-08-16 12:08:59", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b21f74e479994b986d633d7ba609378b5b2c0e72", "commit_訊息": "[內部] 調整 PK 判斷語法，用來處理名稱可能不相同的問題", "提交日期": "2023-08-16 10:49:00", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "04ff60e222c1183bb97c29bead7a411a5badc76a", "commit_訊息": "[BPM APP]新增釘釘集成多對一的機制[補]", "提交日期": "2023-08-16 10:37:23", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "873e8a049a1cb5e09e18e306b53c2d5136c208a2", "commit_訊息": "[DT]V00-20230810002 調整bpm-tools點流程管理工具時提示已Web化訊息[補]", "提交日期": "2023-08-16 10:26:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/DesignerChooseDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "19fd4d0e8601094ce15a9bd68e56fbb9a6a4f034", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-16 10:03:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "732c5687750c6cb3f81de4ee2f90fea48e2fef9b", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-16 08:34:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "590899fc4f87b66ca8e8a086c87a0f892f036aa2", "commit_訊息": "[WorkFlow]Q00-20230815004 新增WorkFlow回寫增加時間訊息", "提交日期": "2023-08-15 17:13:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05b56c41550423997afda2434a0bb5f93a75a2fb", "commit_訊息": "[Web]V00-20230810019 转派离职人员的工作，点击流程会弹出是否追踪流程的alert框，此alert框不管点确定还是取消都无反应。", "提交日期": "2023-08-15 16:04:57", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8233275efd6e2c9c89f6f1476b8ee52fc88d67e", "commit_訊息": "[E10]Q00-20230815003 修正回寫E10單據遇到CROSS回傳錯誤不應該結案", "提交日期": "2023-08-15 15:54:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a38387eb18e66a5b517dfb3d5310a65aa117658", "commit_訊息": "[Web] V00-*********** 修正無上傳簽名圖檔時，後端拋錯異常，新增防呆。", "提交日期": "2023-08-15 15:10:34", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fa046642db7859756f242cf7c844ac485ead0167", "commit_訊息": "[Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常", "提交日期": "2023-08-15 13:57:44", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e986a60023bea2bd575850a9c5f1f18730ef4747", "commit_訊息": "[流程串接套件]Q00-*********** 移除服務任務參數\"前置表單代號\"", "提交日期": "2023-08-15 11:42:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/LocalDocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3858b85b0632bfa708f69fd34476f4d347737e4e", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-15 11:30:33", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/ebg/form-default/EBGForm.form", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/ebg/process-default/\\351\\233\\273\\347\\260\\275\\346\\234\\203\\350\\276\\246\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3692978b40f7dfa7e49533a503a2f50114c1b1dd", "commit_訊息": "[內部]Q00-20230714002 調整組織設計師中管理行事曆的適用日期刪除機制", "提交日期": "2023-08-14 21:30:56", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3cb222f7587939b1ce8dab2515b812aeb1e197f", "commit_訊息": "[DT]V00-20230811009 修正流程管理工具未依資料使用權限顯示或操作流程資料問題[補]", "提交日期": "2023-08-14 17:21:58", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "392c9f45c99acdbe45190fe8529a20bc53104e8b", "commit_訊息": "[DT]V00-20230811009 修正流程管理工具未依資料使用權限顯示或操作流程資料問題", "提交日期": "2023-08-14 17:15:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6513416c0b996b8cadd577678df5fd107ba93070", "commit_訊息": "[內部]Q00-20230710004 因一體化安裝，將ISO相關排程放入預設出貨", "提交日期": "2023-08-14 17:02:39", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/conf/NaNaJobs.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "945ae457c1e4b3e4850b8e3a7eb73f0d8c44fb66", "commit_訊息": "[流程引擎]Q00-20230814003 優化自動簽核機制，當同一個關卡有多個處理者，且都需自動簽核時，因執行時間相近有機率會導致部分處理者無法自動簽核，故調整機制避免此狀況", "提交日期": "2023-08-14 16:54:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "78b8967a7d51077c465d31ff869e27238e73d52f", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-14 16:08:38", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "89d12c87e63182b3df5e0f2081030782aded61f5", "commit_訊息": "[Web]V00-20230814001 元件在表單存取控管設為Invisible 绝对位置表单列印空白", "提交日期": "2023-08-14 16:08:20", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "79b70c07e9875ec04d65c841a104f5fece6209da", "commit_訊息": "[在線閱覽]Q00-20230814002 調整在線閱讀浮水印機制，當浮水印內容有特殊字導致無法添加浮水印時，改使用預設內容「userId+閱讀時間」作為浮水印內容，避免使用者無法順利閱讀檔案", "提交日期": "2023-08-14 15:59:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ffa9c871901d8d6e1e2956741ba694cd4d1f25f1", "commit_訊息": "[流程引擎] Q00-20230814001 设计工具>系统管理>系统邮箱 保存报错，增大SystemConfig表的MailServerAddress字段长度", "提交日期": "2023-08-14 15:37:15", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fd6d6745179ec8dbf62ddbd46dcbd36b19cf0308", "commit_訊息": "[內部]S00-20230717004 因一體化安裝，將SYN_ISO相關的表放入預設出貨", "提交日期": "2023-08-14 11:24:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d0bb481324aca824997425d422d2077c6381f888", "commit_訊息": "[DT]V00-20230810011 修正連接線條件運算式設定完儲存後會消失問題", "提交日期": "2023-08-11 16:42:41", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "61b4a4b4ec44aa722c10bbb9f294c67dd188c708", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-11 16:01:29", "作者": "liu<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "3b45b83577aa9744efe78df7793a17f5b93174ad", "commit_訊息": "[Web] Q00-20230811001 修正BPM首页手机端流程有两个时间显示", "提交日期": "2023-08-11 15:36:12", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cffa93fd7c0f6c9595665dd056cd2fedbd3ae228", "commit_訊息": "[Web] S00-20230811001 修正BPM首页手机端流程有两个时间显示", "提交日期": "2023-08-11 15:36:12", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b2ad1a0c339c01c951c367d5b48842a44752d0ad", "commit_訊息": "[DT]V00-20230810003 修正儲存流程模型錯誤問題", "提交日期": "2023-08-10 19:23:31", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a66fbdfbd4a278330a55f932de4d8c5e1dcc3123", "commit_訊息": "[DT]V00-20230810006 修正流程變數中新增表單的搜尋底字顯示錯誤", "提交日期": "2023-08-10 15:11:34", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "96d5f12faf1b4ffba4f6918f6d61c28ce351a108", "commit_訊息": "[web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复[补修正]", "提交日期": "2023-08-10 15:06:04", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e0746e8739bd72fba4caee6f9b1b0881af299796", "commit_訊息": "[BPM APP]新增釘釘集成多對一的機制[補]", "提交日期": "2023-08-10 14:24:27", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd8d8a552d742b5dd9c3ec0ea0294827b563e348", "commit_訊息": "[Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白[补修正]", "提交日期": "2023-08-10 13:22:36", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8245ab3a8422cb94202be65168616bbad09abdce", "commit_訊息": "[DT]V00-20230810002 調整bpm-tools點流程管理工具時提示已Web化訊息", "提交日期": "2023-08-10 12:20:31", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/DesignerChooseDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@designer/NaNaTools.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5b2bc72f8f5a0bd0b31bec86b9bc5b1622647a1c", "commit_訊息": "[Web]V00-20230810005 修正ORACLE使用者開窗在有離職人員的狀況下打開會異常", "提交日期": "2023-08-10 12:00:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9624b80d0925ec60b7be3fef49aa8735863fe11", "commit_訊息": "[DT]V00-20230810001 修正Web流程管理工具連結未加入授權卡控判斷的問題", "提交日期": "2023-08-10 11:37:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0117a2254bae6b4ece2ee859b8e154802bf49e1", "commit_訊息": "[web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复[补修正]", "提交日期": "2023-08-10 11:10:49", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2cab70bc19ed9c5b74d4b804959551708c28c70b", "commit_訊息": "[Web] S00-20230628001 调整待辦的簽核意見栏寬度比例（加宽） 【补修正】", "提交日期": "2023-08-10 10:45:50", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0357576aeb5230a8cd3a9f9c5262084595ba18d6", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-10 08:53:09", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "325e2444b5d2e42568080733cc2d48a753ac3668", "commit_訊息": "[內部]更新5893patch", "提交日期": "2023-08-09 14:32:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0636a6d4aec29c40f2a1d1a0362baa666a265ba4", "commit_訊息": "[Web]Q00-20230526002 修正關卡通知信設定以整張表單時，ListBox元件在信件上呈現應該要為顯示值", "提交日期": "2023-08-09 11:49:25", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7e764c7a9905f4f2aa681417070040ae9ec114b", "commit_訊息": "[內部] BPMDT.war 更名為 NaNaXWeb.war 之相應調整，由設計器專用擴充為新 Angular 模組共用", "提交日期": "2023-08-09 08:50:52", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6cf0917ab9c1518b4bd928ff9c56612d9b0389de", "commit_訊息": "[web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复[补修正]", "提交日期": "2023-08-09 08:45:00", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7d5e0bfbee872bc5add946a09c3a4424c76f7b76", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-09 08:45:19", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c184abae4d598eb342488ec79597717903c979ee", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "提交日期": "2023-08-09 08:42:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/EBGAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGFormManager.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "db75b01b7416c606a8a4a7ec800baebf730e4138", "commit_訊息": "[內部]調整流程設計師工具Web化改開窗顯示[補]", "提交日期": "2023-08-08 20:15:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b98333268642324695a1145826a5f7ad4e10b333", "commit_訊息": "[內部]調整流程設計師工具Web化改開窗顯示[補]", "提交日期": "2023-08-08 19:53:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "678af0281e4e4d2498d4ce173d2fdbbfc4ef2625", "commit_訊息": "[組織同步] S00-20230309004 新增Employee是否預設帶入工作行事曆(補修正)", "提交日期": "2023-08-08 16:18:00", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0d514b929d85883768b973daa50ed38d0e202d20", "commit_訊息": "[BPM APP]S00-20230524001 調整釘釘與IMG詳情簽核的待辦推播若已被處理過則直接導向追蹤已處理的表單畫面", "提交日期": "2023-08-08 15:24:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "8e0409d006423f92aefa8b8b7e5dbc434dfb1d32", "commit_訊息": "[Web] Q00-20230511002 优化原Q00-20230510004（修正關卡說明有換行，導致在流程圖檢視參與者型式關卡頁面工作列表無法顯示）", "提交日期": "2023-08-08 14:55:38", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/taglib/WriteTag.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ce1ea860e82e87f2c87063c320ac8822b8bed1ac", "commit_訊息": "[流程引擎] Q00-20230626005 新增索引(補)", "提交日期": "2023-08-08 14:31:46", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "eadf72e31edc2c095865b1ce4b68666910796bd6", "commit_訊息": "[Web] Q00-20230525002 調整匯出表單，日期格式異常問題", "提交日期": "2023-08-08 12:24:06", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "88fbd98bcc374fda177135c3cde82144b59120ad", "commit_訊息": "[EBG]S00-*********** 新增EBG電子簽章專案", "提交日期": "2023-08-08 11:39:28", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryInstanceManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/LocalDocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ebgMofule/EbgDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/ebgModule/EBGHistoricalSigner.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/ebgModule/EBGPropertise.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/ebgModule/EBGSignerTemplate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/ebgModule/EBGSignerTemplate_Users.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkStep.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Ebg.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/EbgMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/EBGAccessor.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGCreateForm.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGFormManager.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGPropertise.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGSignerTemplate.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/UserInfo.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/Folder-hover.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/Folder.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/download-hover.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/download.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 44}, {"commit_hash": "bdbfbc2dcd0f40b05fc767e4445dad349d4f0cb7", "commit_訊息": "[Web]S00-20230609001 表單附件下載機制 增加「是否允許透過瀏覽器直接預覽附件內容」及「在線閱讀是否允許下載附件」的系統變數設定[補]", "提交日期": "2023-08-07 17:10:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f207ac52e38ae1e9ec0994080ae915d06252987c", "commit_訊息": "[Web] V00-20230522001 修正formDispatch 回傳false時，沒有儲存表單", "提交日期": "2023-08-07 16:58:02", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36c6d6e6273a68a413e3b0b0bb7343b7e05b5534", "commit_訊息": "[流程引擎]Q00-20230706003 新增索引優化簽核緩慢[補修正]", "提交日期": "2023-08-07 10:37:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0feb178f41ce77f5f082820414bbb816d50b7159", "commit_訊息": "[流程引擎]Q00-20230609007 回收索引 [補修正]", "提交日期": "2023-08-07 10:35:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "360a127089228117f8538f7f4eec7004326f3c2c", "commit_訊息": "[Web]Q00-20230601001 [在地化]回收至標準產品 [補修正]", "提交日期": "2023-08-07 10:30:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "64fce35a165ad70b2bcd74ae1d3001ae82062155", "commit_訊息": "[組織同步] S00-20230309004 新增Employee是否預設帶入工作行事曆(補修正)", "提交日期": "2023-08-07 10:29:48", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/WorkCalendar.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "758c9a5bf4c84f27e35923934592bfb3cee6fbdc", "commit_訊息": "[流程引擎] Q00-20230727001 在后端卡控保存密码是否符合规则，密码只能为数字、字母和特殊符号(!@#$%^&*.()/=-+) [补修正]", "提交日期": "2023-08-07 10:01:58", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6309e55057b219ae74d8d0a72d7acd370a63b565", "commit_訊息": "[DM8] 新增達夢 DM8 支持(補)", "提交日期": "2023-08-06 20:54:36", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/conf/syncorg/SyncTable.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc4c5c0b7173877b8729ba924f3f68ab02736877", "commit_訊息": "[DM8] 新增達夢 DM8 支持(補)", "提交日期": "2023-08-06 20:01:20", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/conf/syncorg/SyncTable_Oracle.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/syncorg/sync-def_Oracle.xml", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/wildfly/standalone/configuration/standalone-full_Oracle.xml", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 3}, {"commit_hash": "8b835af445037007ab725b1e2fa4ea40d1ce6fcd", "commit_訊息": "[流程引擎] 新增 NanaDsUtil 工具程式", "提交日期": "2023-08-06 17:39:33", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "d532261b078179ace3788106d01464665335985d", "commit_訊息": "Revert \"[流程引擎] 新增 NaNaDS 工具程式\"", "提交日期": "2023-08-06 17:10:04", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 2}, {"commit_hash": "00816d2e6b565aecc5f0e4cd26fecdb1e9839b81", "commit_訊息": "[DM8] 新增達夢 DM8 支持", "提交日期": "2023-08-04 10:56:33", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/lib/Hibernate/DmDialect-for-hibernate5.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/persistence.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/form/DataBaseType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/util/jdbc/ColumnsFinder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/util/jdbc/DatabasesFinder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/util/jdbc/TablesFinder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/data/AnnouncementOADataManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DraftListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionSearchReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/GroupNoticeListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/GroupRejectableListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/GroupTraceProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/GroupWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudNoticeListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudTraceProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimpleExpenseAccountItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimplePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDraftListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/iwc/IndicatorCalculationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/archive/CopyOrgJdbcHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/SQLHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/resources/DatabaseDriver.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/Hibernate/DmDialect-for-hibernate5.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISODocumentsListExcelServlet.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISOListExcelServlet.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/custPLMAttachmentList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/custPLMSignCommentList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/jboss-deployment-structure.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOChangeFileList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISODocumentsList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOEffectInvalidList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOFileQueryList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOReleaseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/util.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DDL_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DDL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/bin/standalone.conf", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/bin/standalone.conf.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/bin/systemd/bpm.service", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/bin/systemd/launch.sh", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/dt/dt.hibernate.cfg.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/iso/iso.hibernate.cfg.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/mpt/mpt.hibernate.cfg.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/mts/mts.hibernate.cfg.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/syncorg/SyncTable.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/syncorg/sync-def.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/lib/jdbc/dm8/main/DmJdbcDriver18.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/lib/jdbc/dm8/main/module.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 86}, {"commit_hash": "a41c2d39bbb4d9b02cfa9f42c977501698ec8c02", "commit_訊息": "[流程引擎] 新增 NaNaDS 工具程式", "提交日期": "2023-08-04 10:37:53", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "cabceb979a27b2753769b528a484a5d4997ae145", "commit_訊息": "[WEB]S00-20230530002 調整RadioButton，checkbox輸入框binding到GRID为空时会呈现[]及回传显示undefined的问题", "提交日期": "2023-08-04 17:05:08", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "953c6fabc577391405b876a7961437d40916b68b", "commit_訊息": "[Web] S00-20230628001 调整待辦的簽核意見栏寬度比例（加宽）", "提交日期": "2023-08-04 10:59:54", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d45a84cc3bd0cb8fb4c4b27d3a898783cd6b83d1", "commit_訊息": "[BPM APP]S00-20230609001 表單附件下載機制 增加「是否允許透過瀏覽器直接預覽附件內容」及「在線閱讀是否允許下載附件」的系統變數設定[補]", "提交日期": "2023-08-03 20:24:16", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "4b9cc343e46d3f52e6b5b664db9d3f48edf6fbb3", "commit_訊息": "[BPM APP]新增釘釘集成多對一的機制[補]", "提交日期": "2023-08-03 18:31:58", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5afb3bd35baa72b33072052b99d91bf72dd98482", "commit_訊息": "[BPM APP]新增釘釘集成多對一的機制[補]", "提交日期": "2023-08-03 17:27:47", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "fa0765c03ab42a0e843d6832aa460d173fba02f3", "commit_訊息": "[流程引擎]Q00-20230802004 修正流程未設定\"被處理者終止時逐級通知\"應該只要發起人收到信件通知", "提交日期": "2023-08-02 18:54:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9d0b3852ba368d666749303ad02a9711800abfb5", "commit_訊息": "[BPM APP]Q00-20230802003 修正從企業微信菜單直接進入通知列表時操作閱讀狀態顯示undefined問題", "提交日期": "2023-08-02 17:57:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "514d767b45866f926e0720aef0e9b243fe3f18ff", "commit_訊息": "[Web] S00-20220401001 新增系統變數:「設定簽核完畢後的行為」套用在新增使用者的預設值", "提交日期": "2023-08-02 17:44:27", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "f38b9ef2613ec589046226a53bcd956ef94eca95", "commit_訊息": "[流程引擎]V00-20230802001 修正流程引擎在核決關卡展開第一關時向前加簽或核決關卡展開的最後一關向後加簽時，若PerformWorkItemHandlerBean的log層級設定info以上時，系統會出現錯誤訊息，實際上加簽關卡已成功的異常", "提交日期": "2023-08-02 16:07:19", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3f0f93c371a88fe1271e45d6839a74647323cf0", "commit_訊息": "[BPM APP]Q00-20230802002 修正DB為Oracle時IMG開啟所有流程、重要流程與我的關注應用列表時不會顯示篩選內容問題", "提交日期": "2023-08-02 15:11:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a1f6ef157f69889fb639c28ce318ee02c5e55ce", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-02 14:37:25", "作者": "liu<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "ca0fd3301932cd4df8a5a136e6822100e348c919", "commit_訊息": "[流程引擎] Q00-20230727001 在后端卡控保存密码是否符合规则，密码只能为数字、字母和特殊符号(!@#$%^&*.()/=-+) [补修正]", "提交日期": "2023-08-02 14:34:59", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/exception/VerificationFailedException.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "5ac9461cadb369e02d8a34b3da0b36eb71f3f59d", "commit_訊息": "[內部]調整流程設計師工具Web化改開窗顯示[補]", "提交日期": "2023-08-02 14:27:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "957ea2d2b67a4b070ae6d53c855278837add9787", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-02 14:02:37", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 3}, {"commit_hash": "6479ecefe326a37eab1a15da590c3170ca3198a0", "commit_訊息": "[在線閱覽]Q00-20230802001 修正在線閱覽管理-轉檔異常處理作業，當關閉「上傳PDF」的開窗畫面後，系統會彈跳服務錯誤的訊息", "提交日期": "2023-08-02 13:48:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4cb4df669e2b399001b8371db6e17cc1a588aa42", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-02 11:33:11", "作者": "邱郁晏", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "f739cd398e475218dcb1d1425c7b37130bad89e4", "commit_訊息": "[資安] S00-20211220001 S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信(補)", "提交日期": "2023-08-02 11:31:06", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0a321703a134ada1d599cb1399735b19d15f6faa", "commit_訊息": "[資安] Q00-20230801001 SonarQube安全性議題相關修正，統一補上防呆機制。", "提交日期": "2023-08-01 16:56:57", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/portal/util/codec/PortalPwdCodec.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "c49f05fb49f2b70a4b5807ba2c9d304bcdf82ea5", "commit_訊息": "[資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信", "提交日期": "2023-08-01 16:27:12", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "6d47c8bea8c3c964be3a29b7f653d704cab46e9f", "commit_訊息": "[資安] Q00-20230801001 SonarQube安全性議題相關修正，統一補上防呆機制。", "提交日期": "2023-08-01 16:56:57", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/portal/util/codec/PortalPwdCodec.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "54c3972646ea7677a63cd1503ea6cc46ebe28367", "commit_訊息": "[資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true為系統郵件帳號為寄件者，false為操作轉派使用者作為寄件者", "提交日期": "2023-08-01 16:27:12", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "63cbaf1888aa1d97992023344ba9d6f87b2684dc", "commit_訊息": "[Web]Q00-20230613001 登入頁面新增越南語系及回收越南多語系內容(補)", "提交日期": "2023-08-01 15:47:18", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f3fdcd9b311462328acd0b67f66369b20d5c2a97", "commit_訊息": "[Web]Q00-20230613001 登入頁面新增越南語系及回收越南多語系內容[補修正]", "提交日期": "2023-08-01 15:16:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bed295450ae2ade3584f4753b431c741653c7dd1", "commit_訊息": "[Web] S00-20230620001 增加系统参数控制是否啟用首頁模組就關閉原BPM首頁功能，true為啟用，false為關閉", "提交日期": "2023-08-01 14:41:45", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "4aa19b0a09f43f789ecb5cbdf3f478b2caf9336c", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58", "提交日期": "2023-08-01 14:01:06", "作者": "pinchi_lin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7b2097dfb2e4bc7ccb3194d1a2ef80000f118c24", "commit_訊息": "[流程引擎] Q00-20230727001 在后端卡控保存密码是否符合规则，密码只能为数字、字母和特殊符号(!@#$%^&*.()-+) 修改[補]", "提交日期": "2023-08-01 14:00:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "318f533e5352f1a506599b7d24acf84818ee3d74", "commit_訊息": "[組織同步] S00-20230309004 新增Employee是否預設帶入工作行事曆", "提交日期": "2023-08-01 13:58:32", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/WorkCalendar.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "159cf8c393077a8c7c7de522ca09e74fd6cf1bc4", "commit_訊息": "[BPM APP]新增釘釘集成多對一的機制[補]", "提交日期": "2023-07-03 18:25:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/mobile/AdapterOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 18}, {"commit_hash": "d090ee8287477e55fdbf6162d1e57998fa37096a", "commit_訊息": "[BPM APP]新增釘釘集成多對一的機制", "提交日期": "2023-07-03 17:01:56", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/AdapterVo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a0c85614b4d30dc434c68f616179033a30f23f6", "commit_訊息": "[表單設計師]Q00-20230731001 調整表單設計師縮小或切換頁簽後切回來操作沒立即更新使用者資訊的問題", "提交日期": "2023-07-31 15:59:38", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "51c500aa44ba71200d717086e641dbfbe66eaa35", "commit_訊息": "[Web] Q00-20230728002 栏位是grid的系结元件，绑定元件显示没问题，单身内容显示有误；修改为单身内容显示未绑定元件值", "提交日期": "2023-07-28 18:01:43", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23baa20b0a2a7b6b19ad1eaf0cb3ab7a44b6df0a", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-27 17:32:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a87007f4a52e7697b72cdc410553a93389ff22e1", "commit_訊息": "[流程引擎] Q00-20230727001 在后端卡控保存密码是否符合规则，密码只能为数字、字母和特殊符号(!@#$%^&*.()-+) 修改", "提交日期": "2023-07-27 13:51:26", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/exception/VerificationFailedException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "33f2708d5efeb3acc1237018d64c4660f28934ce", "commit_訊息": "[BPM APP]Q00-20230726002 修正移動端絕對位置表單中Grid元件單身數據有空值時組成格式不正確問題", "提交日期": "2023-07-26 16:31:24", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "024b6340cfe8b1835eea75e7191edb7d6d32a593", "commit_訊息": "[資安] Q00-20230726001 修正解析XML的JAR包衝突問題。", "提交日期": "2023-07-26 15:10:59", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/BPMDocumentBuilderFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/BPMDocumentBuilderFactory.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "817fe524452d0b1eedd4cb7097d945e64f6c852b", "commit_訊息": "[web]Q00-20230725003 Dialoginputlabel栏位，每个栏位查询sql是取前一个栏位_txt的值做查询条件，第二个栏位开窗选出的值带有“\\”，第三个栏位查询条件中的\\被去除，导致查询条件不对，第三个栏位查询出的结果为空的问题修复[补修正]", "提交日期": "2023-07-26 10:32:42", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/resources/html/CustomDataChooserTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cc41ff49998bf91e16eadac0fe7e37602e3038f6", "commit_訊息": "[WEB]Q00-20230725004 修正主旨樣板選擇\"允許修改主旨\"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨[補修正]", "提交日期": "2023-07-26 10:15:28", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05abd14144bc3c9ac15c69c8534f807b37ab775b", "commit_訊息": "[WEB]Q00-20230725004 修正主旨樣板選擇\"允許修改主旨\"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨", "提交日期": "2023-07-25 18:16:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f5e8061df239b225d672000f7f876b98ebe4ecf2", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-07-25 18:01:14", "作者": "刘旭", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "2d1b2abc78c682803b4c645058c3472fd6cb9041", "commit_訊息": "[web]Q00-20230725003 Dialoginputlabel栏位，每个栏位查询sql是取前一个栏位_txt的值做查询条件，第二个栏位开窗选出的值带有“\\”，第三个栏位查询条件中的\\被去除，导致查询条件不对，第三个栏位查询出的结果为空的问题修复[补修正]", "提交日期": "2023-07-25 18:00:14", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23a66634b66b72b97a44dbd2e2b7bb4b937e3a48", "commit_訊息": "[資安]V00-20230704008 SonarQube安全性議題修正：Cipher Block Chaining IVs should be unpredictable[補]", "提交日期": "2023-07-25 17:50:04", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/weixin/mp/aes/WXBizMsgCrypt.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a97f6f1c03c904a02aca623b0398fdf873c1ccfc", "commit_訊息": "[資安]V00-20230704008 SonarQube安全性議題修正：Cipher Block Chaining IVs should be unpredictable", "提交日期": "2023-07-25 17:45:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/weixin/mp/aes/WXBizMsgCrypt.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "26f63eadaf9a8971613c582c41cdd9fcd3633b90", "commit_訊息": "[web]Q00-20230725003 Dialoginputlabel栏位，每个栏位查询sql是取前一个栏位_txt的值做查询条件，第二个栏位开窗选出的值带有“\\”，第三个栏位查询条件中的\\被去除，导致查询条件不对，第三个栏位查询出的结果为空的问题修复", "提交日期": "2023-07-25 15:42:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/resources/html/CustomDataChooserTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7fcf8196d5842efe1a71169213d2fcfa55ea88eb", "commit_訊息": "[Web]Q00-20230725002 修正發起流程切換預解析流程時，當系統設定有設定該流程只以主部門發起(invoke.process.by.main.unit.process.package.ids)時，若使用者有多個兼職部門時，切換流程圖不應讓使用者選擇參考部門", "提交日期": "2023-07-25 15:27:07", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c46897e41524b1d45cd317403596279206b26a7f", "commit_訊息": "[Web]S00-20230609001 表單附件下載機制 增加「是否允許透過瀏覽器直接預覽附件內容」及「在線閱讀是否允許下載附件」的系統變數設定[補]", "提交日期": "2023-07-25 15:09:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7224a657943e54b8bc64250b6fe55dd8dfbeb94d", "commit_訊息": "[Web]S00-20230609001 表單附件下載機制 增加「是否允許透過瀏覽器直接預覽附件內容」及「在線閱讀是否允許下載附件」的系統變數設定[補]", "提交日期": "2023-07-25 14:35:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e70290dc4f742aaeb64c5e015f4d0b0815337d78", "commit_訊息": "[資安] V00-20230704003 SonarQube安全性議題修正：Encryption algorithms should be used with secure mode and padding scheme(補webapp)", "提交日期": "2023-07-25 14:10:43", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "816d3c13656476e716ddef340d0cf6305bfbb3c2", "commit_訊息": "[資安] V00-20230704003 SonarQube安全性議題修正：Encryption algorithms should be used with secure mode and padding scheme", "提交日期": "2023-07-25 11:47:26", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "ce95fc1b64a6a5dee8ac214e7d36455785c491a9", "commit_訊息": "[表單設計師]Q00-20230725001 調整選項元件進階設定選項開窗顯示值多語系有含單引號時確定按鈕異常的問題", "提交日期": "2023-07-25 11:17:31", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ea0dad8d4d8fb9fe13e62030363af44c16d940f7", "commit_訊息": "[Web]S00-20230609001 表單附件下載機制 增加「是否允許透過瀏覽器直接預覽附件內容」及「在線閱讀是否允許下載附件」的系統變數設定", "提交日期": "2023-07-25 11:03:04", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/FileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 20}, {"commit_hash": "6e838749f3d570462c7061fd7fd5faf3d4fef90e", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-25 10:53:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b12f1271ca319ae0a06339bcddcdd9c43543aefd", "commit_訊息": "[資安]V00-20230704007 SonarQube安全性議題修正：Server hostnames should be verified during SSL/TLS connections", "提交日期": "2023-07-24 18:24:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6c5a64104088ed6bb82d648e5ce4dce3a4f56ddf", "commit_訊息": "[資安] V00-20230704002 SonarQube安全性議題修正：Credentials should not be hard-coded", "提交日期": "2023-07-24 17:45:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/portal/util/codec/PortalPwdCodec.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "98a9c05858b7391cff18b87cbcd0417435de3620", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-24 17:27:19", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "52f7db9908c2235556adf052209ffe8d84f5edc7", "commit_訊息": "[資安] V00-20230704005 SonarQube安全性議題修正：Cipher algorithms should be robust", "提交日期": "2023-07-24 14:52:54", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/util/Crypto.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/portal/util/codec/PortalPwdCodec.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "453c4f34a9150bfd2f41422b044e4874029d7b52", "commit_訊息": "[資安] Q00-20230718001 修正發送通知者寄送Mail，預設以系統郵件寄件者代發郵件(補修正)", "提交日期": "2023-07-24 11:46:12", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3176d084dd00c9a54e4d7815320d6cd8ca93f48b", "commit_訊息": "[內部]V00-20230704006 修正SonarQube掃描安全性問題:XML parsers should not be vulnerable to XXE attacks", "提交日期": "2023-07-24 11:27:23", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/SchemaType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/EventDispatchConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/util/WebServiceForCrm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/WebServiceForTiptop.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/WebServiceToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/BPMDocumentBuilderFactory.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/BPMTransformerFactory.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/XmlHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "60e5d8ea9f6969c310968170c99c76ad70eb7928", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-07-24 11:03:26", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "67f334171441f098b4815f3d405589eed2f2ce58", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-07-24 11:02:29", "作者": "邱郁晏", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "3280a76390c0f40e41d1d4ee7b9b2d8f49a0df2f", "commit_訊息": "[資安]V00-20230704004 SonarQube安全性議題修正：Weak SSL/TLS protocols should not be used [補修正]", "提交日期": "2023-07-24 10:21:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ee785fd54e5cfd3c1680765debc675968c2c813f", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-21 18:44:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c43da4c7359b4a1c735ed034077a74a69a6b70ff", "commit_訊息": "[內部]V00-20230704006 修正SonarQube掃描安全性問題:Vulnerabilities", "提交日期": "2023-07-21 10:30:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d7883c9bfef5cbbad062d4bb8c675efc549ab4cc", "commit_訊息": "[資安]V00-20230704004 SonarQube安全性議題修正：Weak SSL/TLS protocols should not be used", "提交日期": "2023-07-21 10:23:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "c7e80924f4843be0e10982015176dc9b15e5f1ed", "commit_訊息": "[Web] Q00-20230713005 新增登入登出LOG印出SessionId，修正模擬為同一IP時取瀏覽器資訊異常問題", "提交日期": "2023-07-20 13:44:37", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d1a75b9e7a1446ff21c8a031f07b782066aa823c", "commit_訊息": "[DT]C01-20230719005 修正設計師使用權限管理中的組織設計師清單其id與名稱顯示異常問題", "提交日期": "2023-07-20 12:13:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/ParticipantInfoAcquirer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/AccessCrtlMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "89e01235d1274a11a030774c0ab6e5506a70529a", "commit_訊息": "[資安] Q00-20230718001 修正發送通知者寄送Mail，預設以系統郵件寄件者代發郵件", "提交日期": "2023-07-20 11:50:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "08dddde4f2c88714f3da8e5ade350f4de5a3fc3f", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58", "提交日期": "2023-07-24 11:01:08", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "89c4e6132237ed85588c8031019ae6d7bd22d23f", "commit_訊息": "[內部]V00-20230704006 修正SonarQube掃描安全性問題:XML parsers should not be vulnerable to XXE attacks", "提交日期": "2023-07-24 10:58:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/BuildFormReport.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/apps/form_importer/TiptopFormImportTest.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/BPMDocumentBuilderFactory.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/Sample.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/WeChatMessageRecUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/weixin/mp/aes/XMLParse.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "9cb726d91a287c936520735745f9219798846392", "commit_訊息": "[資安]V00-20230704004 SonarQube安全性議題修正：Weak SSL/TLS protocols should not be used [補修正]", "提交日期": "2023-07-24 10:21:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9b463f33e85468ecda9741c4e36300d38dd8a55c", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-21 18:44:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74afd37fa4a588211468c2713c338e1a4aeb2506", "commit_訊息": "[內部]V00-20230704006 修正SonarQube掃描安全性問題:Vulnerabilities", "提交日期": "2023-07-21 10:30:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8eaa092f78935ab55d7eb955b87596d273869955", "commit_訊息": "[資安]V00-20230704004 SonarQube安全性議題修正：Weak SSL/TLS protocols should not be used", "提交日期": "2023-07-21 10:23:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "663825edc6d7fb64a15761e4962173c14966f6c2", "commit_訊息": "[Web] Q00-20230713005 新增登入登出LOG印出SessionId，修正模擬為同一IP時取瀏覽器資訊異常問題", "提交日期": "2023-07-20 13:44:37", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e6e68d7fc91546907135cde31b939ea3aaa17748", "commit_訊息": "[DT]C01-20230719005 修正設計師使用權限管理中的組織設計師清單其id與名稱顯示異常問題", "提交日期": "2023-07-20 12:13:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/ParticipantInfoAcquirer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/AccessCrtlMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c7845237159492a52e812e49804f3c4a6ca6bf3a", "commit_訊息": "[資安] Q00-20230718001 修正發送通知者寄送Mail，不應副本給通知者的問題。", "提交日期": "2023-07-20 11:50:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "68a4fb1a324ee61eb66dc5620c50d05eaf87c975", "commit_訊息": "[Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員 [補修正]", "提交日期": "2023-07-20 11:16:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d104fbb85cca82a5bf8f4c1fd53f242be4accbcb", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58", "提交日期": "2023-07-20 10:38:19", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "561bd02477cebbbf504b6606e1b5d497908b7ddc", "commit_訊息": "[流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException[补修正]", "提交日期": "2023-07-20 10:33:53", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7d1d30d28c4b28cecad0bb5427b29ed3513cf65", "commit_訊息": "[其他]S00-20230717001 iReport報表-新增「iReport的資料庫連線是否只允許啟用TLS1.2」的參數設定，並根據參數設定判斷是否走新版iReport的報表", "提交日期": "2023-07-17 12:03:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "61311e0a6be69be884f18da14df65f2ac7a4f2c8", "commit_訊息": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]", "提交日期": "2023-07-17 09:25:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fb15f4005424023c61cfd42a6694809cb6d8acad", "commit_訊息": "[Web] Q00-20230706001 新增系統變數，統一設定ESS表單的最小高度，若表單高度超過此設定，則套用表單高度；若無超過則套用此設定(補)", "提交日期": "2023-07-14 16:41:25", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9d82c7a46d2eef8ee9c494a87c3c5fa2e96bbbbf", "commit_訊息": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。", "提交日期": "2023-07-14 17:36:50", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a0b474e0b5e13397246c718015605aea8eb3fe34", "commit_訊息": "[流程引擎] Q00-20230714001 修正服務任務關卡執行後，不會重新組成全文檢索欄位問題", "提交日期": "2023-07-14 14:48:48", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "af95fe313c6b2609e70335635eb595e7f01b17cd", "commit_訊息": "[Web]Q00-20230713004 優化主管首頁與企業流程監控會使用到的SQL", "提交日期": "2023-07-13 11:58:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2279f70b4e79317ff68b28afebe38c023d2399ab", "commit_訊息": "[Web] Q00-20230713003 優化使用者登入時異常時的LOG", "提交日期": "2023-07-13 11:44:10", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/PersistentObjectHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "693b46728f2187ee6ed083217a7e0c7850602854", "commit_訊息": "[Web]Q00-20230713002 修正使用者為部門主管從Portal進入BPM點選首頁顯示內容不為主管首頁", "提交日期": "2023-07-13 10:23:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df0fcf1878d0224bb631b1466f8ee855ff5d4136", "commit_訊息": "[E10]Q00-20230712002 修正E10單據開啟遇到setColumnWith異常訊息導致無法繼續派送 [補修正]", "提交日期": "2023-07-13 08:52:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/E10Form.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dbcc65948189a5978f11d43b29d5998a571f7261", "commit_訊息": "[web]Q00-20230712003 修正在转存表单时栏位原數值為小數點後兩位，轉存表單後僅剩小數點後一位", "提交日期": "2023-07-12 15:59:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0fe544afeded7664761faf31bd7976e737124b9", "commit_訊息": "[E10]Q00-20230712002 修正E10單據開啟遇到setColumnWith異常訊息導致無法繼續派送", "提交日期": "2023-07-12 15:07:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/E10Form.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52bdae34116cc27c642d3910f8d342996e26c5a7", "commit_訊息": "[流程引擎]Q00-20230706003 新增索引優化簽核緩慢[補修正]", "提交日期": "2023-07-12 13:47:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2e89327fadcb66e9472d67eeb543631c688a1589", "commit_訊息": "[Web]Q00-20230712001 修正从布景主题修改banner图片或还原预设图片情况下，简体语系修改或还原不成功问题", "提交日期": "2023-07-12 14:43:39", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a44d416d674c25e4d6f978535d6dc8db9e1d570", "commit_訊息": "[Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白。", "提交日期": "2023-07-11 14:12:59", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "19521b5cecc61752b987108357890273bac994e9", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-11 12:06:55", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a58f7eae2bf6e77517b57b46ac7dddc26ac176db", "commit_訊息": "[流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題(補)", "提交日期": "2023-07-11 10:24:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dfb79855a2ecffcd27561d44cf6fe347fc3c3770", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-10 19:18:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71bf010311d916d16586ce1047995d1390d2a4b3", "commit_訊息": "[web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题", "提交日期": "2023-07-10 16:56:51", "作者": "liuxua", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f742300525feca10618185ffa3207b0933d30784", "commit_訊息": "[web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题", "提交日期": "2023-07-10 16:51:34", "作者": "liuxua", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1e042c2edc3eed50b23ec2308e72f9789a2a7c83", "commit_訊息": "[T100]S00-20230421001 T100取簽核歷程關卡若有退回(取回)重辦，只取最新處理時間的關卡[補修正]", "提交日期": "2023-07-10 15:19:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "63fd2373acb860c0a0d6f7bb7a367381ac127489", "commit_訊息": "[T100]S00-20230421001 T100取簽核歷程關卡若有退回(取回)重辦，只取最新處理時間的關卡", "提交日期": "2023-07-10 15:13:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "96083a642af9ac20b29519bec2650e391bc07ec8", "commit_訊息": "[其他]Q00-20230704001 調整BCL8單個檔案轉檔逾時時間由預設2分鐘改為預設10分鐘", "提交日期": "2023-07-10 11:38:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9001afc943a41a2bfdcf49b090efa989082cd0b0", "commit_訊息": "[ESS]Q00-20230710001 調整log錯誤訊息的顯示", "提交日期": "2023-07-10 10:49:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12c726c73d3cd66941505b8eab462441e6c11d2c", "commit_訊息": "[流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題。", "提交日期": "2023-07-07 14:39:22", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9015f7ac6de2bd4c7930969f0940f17a7883d952", "commit_訊息": "[ESS]Q00-20230707001 新增ESS預設出貨\"招募\",\"培訓\",\"績效\"的管理類作業 [補修正]", "提交日期": "2023-07-07 12:24:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\345\\237\\271\\350\\250\\223\\351\\226\\213\\347\\231\\274\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\345\\237\\271\\350\\250\\223\\351\\226\\213\\347\\231\\274\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\346\\213\\233\\345\\213\\237\\347\\224\\204\\351\\201\\270\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\346\\213\\233\\345\\213\\237\\347\\224\\204\\351\\201\\270\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\346\\273\\277\\346\\204\\217\\345\\272\\246\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\346\\273\\277\\346\\204\\217\\345\\272\\246\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\347\\270\\276\\346\\225\\210\\350\\200\\203\\346\\240\\270\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\347\\270\\276\\346\\225\\210\\350\\200\\203\\346\\240\\270\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "4a4dee0bdd707bcab2c567df108546141e5abeae", "commit_訊息": "[ESS]Q00-20230707001 新增ESS預設出貨\"招募\",\"培訓\",\"績效\"的管理類作業", "提交日期": "2023-07-07 09:03:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "66e07193ce3c23771670b0474aeb201375eafd49", "commit_訊息": "[Web]Q00-20230706004 修改流程中，转由他人处理画面上“经常选取对象”里，不会带出离职人员。", "提交日期": "2023-07-06 17:28:46", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "135c63701155f2113d4a2474489678fafc4054cd", "commit_訊息": "[流程引擎]Q00-20230706003 新增索引優化簽核緩慢", "提交日期": "2023-07-06 15:42:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e39d6823460efff38b04c3254fe7b1982022acfb", "commit_訊息": "[組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。", "提交日期": "2023-07-06 15:22:50", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd1c4928c8a16a0cf78698d3f07118681da1668a", "commit_訊息": "[Web] Q00-20230706001 新增系統變數，統一設定ESS表單的最小高度，若表單高度超過此設定，則套用表單高度；若無超過則套用此設定。", "提交日期": "2023-07-06 11:40:29", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "cb10fe4729c2760a98a0675556e66ab54087a173", "commit_訊息": "[Web] Q00-20230628002 新增系統變數可自定義簽核歷程是否要出現按鈕，並調整為無須重啟BPM即生效(補)", "提交日期": "2023-07-05 18:19:53", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9583cf219750f6b24fe432f54054a0f326ffb2e2", "commit_訊息": "[Web]S00-20230504003 調整汇入表单，使用原本ID，若存在的ID，會提示是否要Overwrite", "提交日期": "2023-07-05 15:55:42", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "648f65cb63fed6e79cdb59ed076f290c06531678", "commit_訊息": "[Web]Q00-20230705002 修正表單在预览时，更換image后，显示图片不正确的问题", "提交日期": "2023-07-05 14:23:40", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ImageElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e7a62b18a00f01571aa34a59ae556e69f993c827", "commit_訊息": "[BPM APP]Q00-20230705001 修正行動表單的Title元件無法使用FormUtil.setLabel方法設定標籤內容", "提交日期": "2023-07-05 12:07:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6474edee86fc1520b67ac0cf094c8ddcd406145", "commit_訊息": "[MPT]S00-20230315001 首頁我的最愛模塊增加顯示常用功能[補]", "提交日期": "2023-07-04 17:40:55", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f5519f3f0e6d1795bfa0b669f7aa8cd225d6251", "commit_訊息": "[Web]Q00-20230704003 調整formScript撰寫ajax加簽關卡後，需要更新session裡面的ProcessInst的相關屬性(Processpackage,ProcessDef等屬性)，避免預覽流程仍以加簽前的定義做解析", "提交日期": "2023-07-04 17:40:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbf365e55fb9d6d5722fe8fceb686ad8b1b30bbc", "commit_訊息": "[Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級(補)", "提交日期": "2023-07-04 17:34:15", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "80688129e5f1084b78ea573d18b090d56a09bffb", "commit_訊息": "[Web]Q00-20230704001 調整CommonAccessor.updateConnectedUserInfo()更新使用者時間的方法，當更新異常時，由前端畫面提示「更新線上時間失敗」改為後端serverlog記錄就好", "提交日期": "2023-07-04 17:02:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "91eae685dcd654379462914ef05f9a9d60319bfd", "commit_訊息": "[Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級", "提交日期": "2023-07-04 16:43:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f5e99751114e59c22bd2c6ea14c8fd5fce64f42", "commit_訊息": "[內部]新增Web化流程管理工具程式定義與其SQL", "提交日期": "2023-07-04 15:19:47", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ca8e38c09fa23869661c13b713cae09612d46bac", "commit_訊息": "[MPT]S00-20230315001 首頁我的最愛模塊增加顯示常用功能", "提交日期": "2023-07-03 17:43:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "108426e9836cfc243529a7c0b38f3e26df84bca6", "commit_訊息": "[Web] Q00-20230703004 修正表單列印畫面元件跑版問題，邊界調整為0", "提交日期": "2023-07-03 16:31:00", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "574a293c68da291fa9c5aa4db4f9f31e5461336e", "commit_訊息": "[E10]Q00-20230703003 修正子單身內容未傳，造成E10回寫失敗", "提交日期": "2023-07-03 15:39:12", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormInstanceTransferJson.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "558673ecf9d59b25a1ff8272cf22f238f47784c0", "commit_訊息": "[E10]Q00-20230703001 修正E10因服務任務未放入表單實例參數造成回寫失敗", "提交日期": "2023-07-03 11:16:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213(\\344\\270\\215\\351\\251\\227\\350\\255\\211\\350\\241\\250\\345\\226\\256)\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254(E10\\346\\224\\257\\346\\214\\201\\347\\211\\210\\346\\234\\254\\357\\274\\232E10_6003.V5\\347\\211\\210\\345\\220\\253\\344\\273\\245\\344\\270\\212)/e10\\346\\265\\201\\347\\250\\213(\\344\\270\\215\\351\\251\\227\\350\\255\\211\\350\\241\\250\\345\\226\\256)\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "93cc7ee2c1237292432952e3aece4f2f34f03d1d", "commit_訊息": "[Web] Q00-20230628002 新增系統變數可自定義簽核歷程是否要出現按鈕，並調整為無須重啟BPM即生效(補)", "提交日期": "2023-06-30 17:11:27", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d83e76dfdab0a10dbbfcff4efdc8c232ea979647", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-28 19:12:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d9aed970cf1a160ddbac5d0fba487bef2cf36b82", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-28 18:50:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5cc4a65938228688c9e6b5c4c1d64c42099be4ef", "commit_訊息": "[SAP]Q00-20230628003 修正SAP整合回寫呼叫SAP的invoke服務，當SAP回傳的訊息需存放在Grid時，若Grid內容為空時，可能會導致formInstance.fieldValues產生多組相同Grid代號的內容", "提交日期": "2023-06-28 18:02:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7fd12a01a3e9e80b26d071835b4fdd6cc44917f", "commit_訊息": "[Web] Q00-20230628002 新增系統變數可自定義簽核歷程是否要出現按鈕，並調整為無須重啟BPM即生效", "提交日期": "2023-06-28 16:28:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 18}, {"commit_hash": "908329892fd774aa88a1f42a790c680177d960d8", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-28 15:57:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5519032c8bffad82302e426236760ac597d939a", "commit_訊息": "[Web]Q00-20230628001 调整绝对位置表单中的各元件border宽度在Chrome和edge显示问题", "提交日期": "2023-06-28 11:16:06", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/form/ElementDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4f4c5597d12c012dc7ad3d8f4721d3a218279def", "commit_訊息": "[SAP]Q00-20230627003 SAP整合的invoke服務任務增加表單相關資訊log", "提交日期": "2023-06-27 15:09:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb9793771edef6915290181d4666f1ef05c020e0", "commit_訊息": "[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件(補修正)", "提交日期": "2023-06-27 14:53:03", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e69cd23fc15e09923f10abeeb40cc5d80f2e2fa", "commit_訊息": "[TipTop] Q00-20230627002 調整TIPTOP的附件選擇txt時，上傳文件且未填說明欄位，轉檔異常問題。", "提交日期": "2023-06-27 14:41:06", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be7bd9f840713cda76b4a924ed6e2e20bdc3a76b", "commit_訊息": "[T100]Q00-20230627001 修正關卡設置\"所有附件皆需開啟過\"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送", "提交日期": "2023-06-27 13:39:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "38ade9669e6d0ffe78b448eb5dcdf5ab912ae7fe", "commit_訊息": "[MPT]Q00-20230626004 修正新增流程分類模塊流程未定義分類該選項會呈現空白問題", "提交日期": "2023-06-26 18:45:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "661a2be916b7225ea713a34089f37ac931a09161", "commit_訊息": "[流程引擎] Q00-20230626005 新增索引", "提交日期": "2023-06-26 17:30:59", "作者": "raven.917", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5ae98bc6480148830749032bdb473eabf9eec72e", "commit_訊息": "[Tiptop] Q00-20230626002 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。", "提交日期": "2023-06-26 16:28:03", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ff555a0dce7a4015c5b0ab6c532049cb05a7c58", "commit_訊息": "[E10]Q00-20230626001 新增E10發單log訊息", "提交日期": "2023-06-26 15:29:13", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b14c4c2e7dc08c0696f12e52efe13844615c6c26", "commit_訊息": "[BPM APP]C01-20230620004 調整企業微信推播內容計算邏輯錯誤問題", "提交日期": "2023-06-21 17:58:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8b3e22fe52a8191319e00b5f596dee22d631c45", "commit_訊息": "[內部]調整流程設計師工具Web化改開窗顯示", "提交日期": "2023-06-21 17:59:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9025ff0c0515068563c56f15a323df5e57720504", "commit_訊息": "[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件(補修正)", "提交日期": "2023-06-21 17:41:47", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ddda6fc12081f9e115df9802d394c870754993e", "commit_訊息": "Revert \"[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件(補修正)\"", "提交日期": "2023-06-21 16:40:43", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dad0119e7e4746fe23e4c807a2278393947e60c1", "commit_訊息": "[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件(補修正)", "提交日期": "2023-06-21 16:10:00", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d487fb32fa2a1bf63310746716001f8b45d6da15", "commit_訊息": "[BPM APP]S00-20230608001 優化BPMAPP詳情頁面不同意簽核意見增加彈窗機制[補]", "提交日期": "2023-06-21 14:42:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "0eb21f4ff7e4259d023afd8cc7659d897daae77e", "commit_訊息": "[Web] Q00-20230621003 修正Rwd-Grid 設置必填時，Alert訊息異常問題", "提交日期": "2023-06-21 11:44:24", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "242c6b851abd9ac425c01b839e4d70f13ca5e833", "commit_訊息": "[Web]Q00-20230621002 修正手機點選批次終止時，沒填值的狀況不會跳出alert訊息", "提交日期": "2023-06-21 11:30:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3fbc59c45fb012e579750eed7a461946ecb55332", "commit_訊息": "[在地化] Q00-20230620003 增加驗證SSOkey時，時間間隔超過5分鐘，印出LOG訊息", "提交日期": "2023-06-20 17:03:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c41e1cb9dc280c5f880075807ae97eecd59f8ec", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-20 16:19:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "86daead74a2c9ab26071ae8519006fb889b6c338", "commit_訊息": "[內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息", "提交日期": "2023-06-20 15:54:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f86ef4791a671c347bcd79d869c879ae8964a883", "commit_訊息": "[Web] Q00-20230620001 調整絕對定位表單追蹤流程下列印表單畫面。", "提交日期": "2023-06-20 14:56:48", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "716a981b740dab19c962b4876276cbc278801b98", "commit_訊息": "[ISO]S00-20230619001 文件攜出模組中的「文件攜出記錄報表」查詢報表新增匯出Excel功能", "提交日期": "2023-06-20 11:37:56", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a202cd38cd9a5f2fdcb061881ceaeb0bd540439e", "commit_訊息": "[Web]Q00-20230619001 修正Grid元件在多欄位時欄位寬度異常顯示問題", "提交日期": "2023-06-19 10:12:14", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7eef3e9f990b74cb6fc74271f61b60fe1a6d936c", "commit_訊息": "[流程設計師]Q00-20230619003 修正流程設計師的條件驗證在簡體的\"財\"字進行驗證被認為判斷錯誤", "提交日期": "2023-06-19 10:01:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/util/ConditionEvaluator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e9d4fb8639bed6fcd01a12cba062e0a6aa087cc4", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-17 15:16:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8b32da1aa6870f16f843b833c889845e91fbde73", "commit_訊息": "[表單設計師]Q00-20230617002 修正當行動版表單腳本編輯器為空時點擊儲存表單沒有效果問題", "提交日期": "2023-06-17 13:48:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "30a3903338613eb36d30c9d6ed77048d99852e5e", "commit_訊息": "[MPT]C01-20230612004 修正公告申請單的公告內文(富文本)在流程結案後仍可修改的問題", "提交日期": "2023-06-17 10:17:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/copyfiles/@mpt/default-form/MptAncApply.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "20734162647185c214f12026efaa8d54f94ee8f5", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-17 09:07:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "92b34e3e4d8ab61ef8d654ce775ca888023a9b3a", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)", "提交日期": "2023-06-16 16:45:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b96174d0bca0a9bb1451f7594dba450affe514b8", "commit_訊息": "[BPM APP]S00-20230608001 優化BPMAPP詳情頁面不同意簽核意見增加彈窗機制[補]", "提交日期": "2023-06-16 16:06:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cd26232b5c91071afa3001a919a749561e09c6f0", "commit_訊息": "[Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題[補]", "提交日期": "2023-06-16 13:56:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a182dbd9e5089c815265097d234a0443a795232", "commit_訊息": "[E10]Q00-20230616001 新增提示訊息在表單同步若上一版的元件為下拉選單，欄位同步成輸入元件就會提醒用戶請移除該下拉元件並重新同步", "提交日期": "2023-06-16 11:05:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6449c1d8708c407f7b02e8e7c8a3713d1e8a0c06", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-15 18:22:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cf6441f818ac4f1ac493f72078ff4915919137c0", "commit_訊息": "[BPM APP]S00-20230608001 優化BPMAPP詳情頁面不同意簽核意見增加彈窗機制", "提交日期": "2023-06-15 18:19:48", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "be3538d026d4bde1d5ea74d73bd1f3ec4177a57a", "commit_訊息": "[Web]Q00-20230615002 修正離職維護作業無法開啟", "提交日期": "2023-06-15 17:18:15", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "be8e52b587c0f294771243eb2bef3901b793dbab", "commit_訊息": "[Web] Q00-20230615001 修正客制開窗order by轉小寫導致模糊查詢異常問題。", "提交日期": "2023-06-15 11:52:52", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c22a42dd5ed34163e7100b9665d09c4582470ad5", "commit_訊息": "[Web]Q00-20230613001 登入頁面新增越南語系及回收越南多語系內容[補修正]", "提交日期": "2023-06-14 14:33:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "17a2a198f42fd93c6852002c00b4a01230627899", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-13 18:01:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c67edbc0dd308fb3032f5b3359a0378bb1e31ac", "commit_訊息": "[Web] Q00-20230613004 調整絕對定位表單，因瀏覽器機制改變，框線變粗體問題。", "提交日期": "2023-06-13 16:13:30", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ActiveWidgets264/styles/system/aw.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "50bbec79233412a10e21a0d7ef866c02e8f03169", "commit_訊息": "[Web] Q00-20230613003 調整參與者型式的關卡頁面的「檢視轉派歷程」按鈕圖示。", "提交日期": "2023-06-13 14:18:28", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "280239ac8bbae7fe8341f2d0d3859155f98d1738", "commit_訊息": "[流程引擎]Q00-20230613002 修正流程取回重辦時，當關閉取回重辦後面的關卡時，需一併將後面關卡直接退回的關卡記錄清除，避免關卡再次派送時，流程會直接回到直接退回的關卡，而非重新展開流程", "提交日期": "2023-06-13 13:48:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1be8e1c40b291a2bf6dc41c94229fd113e70ce05", "commit_訊息": "[Web]Q00-20230613001 登入頁面新增越南語系及回收越南多語系內容", "提交日期": "2023-06-13 12:00:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a6707c99c587ed297a3076c2e97ae2c537d36a10", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)", "提交日期": "2023-06-13 11:36:01", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1a3195db3e946509878cba0cedbf5c9c9686b76", "commit_訊息": "[Web] Q00-20230612001 優化單身欄位加總操作體驗，因新增修改刪除，會跳回原欄位(補)", "提交日期": "2023-06-13 10:41:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b202dedee32caedeef969adb3948cbf2e0c283fb", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。", "提交日期": "2023-06-12 14:57:27", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "faac909172143ed7a23c22d98cfb2d9ee6e975c0", "commit_訊息": "[Web] Q00-20230612004 修正絕對定位表單，上傳圖片異常問題。", "提交日期": "2023-06-12 13:57:42", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "118866a61560c4c4a083a1dcba2d517d50177c6f", "commit_訊息": "[Web]Q00-20230612003 修正Script撰寫Grid的setColumnWith語法會跳出alert", "提交日期": "2023-06-12 12:02:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "35c22b820af1c02793adb1f01b4f35e9aec36559", "commit_訊息": "Revert \"[Web] Q00-20230612001 優化單身欄位加總操作體驗，因新增修改刪除，會跳回原欄位\"", "提交日期": "2023-06-12 11:45:47", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c3ece3fec5c136565cc0dbd2e2e967dcfc19297", "commit_訊息": "[在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕", "提交日期": "2023-06-12 11:27:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e4ebcae78e26e9f167a5bd641725f9a6a78aeb6e", "commit_訊息": "[Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息(補修正)", "提交日期": "2023-06-12 10:45:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1b68e273f559cf5f1d4aff274754def104a38a0e", "commit_訊息": "[Web] Q00-20230612001 優化單身欄位加總操作體驗，因新增修改刪除，會跳回原欄位", "提交日期": "2023-06-12 09:21:40", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9436c72dc444fba825e8e712f92f4bb9a0d47dfa", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-12 09:08:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8b5cbc435d94b4f56cd987dd6b71adfe0d898772", "commit_訊息": "[流程引擎]Q00-20230609007 回收索引", "提交日期": "2023-06-09 18:08:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1323634d1a336bb9216802a0b37982d2938a788f", "commit_訊息": "[Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息", "提交日期": "2023-06-09 17:16:16", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dbdd2cdf9064843a90ebb50e8a2673ef36bfddfd", "commit_訊息": "[BPM APP]S00-20230509001 優化整合企業微信在不同裝置統一使用微信提供的閱讀器下載附件[補]", "提交日期": "2023-06-09 16:18:34", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "31ce73c67b970c89a8e151bd86d21526afbeb234", "commit_訊息": "[Web] Q00-20230609004 修正匯入Excel資料內有,會被替換成空白問題", "提交日期": "2023-06-09 15:13:05", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d01b4e07eb5587883942e4633cb1e604c5bcf6cc", "commit_訊息": "[WEB]Q00-20230609003 修正流程掛載多表單，且各表單的Grid內容都有值，且流程設計該關卡不允許修改表單內容時，自動載入Grid的功能會失效", "提交日期": "2023-06-09 14:04:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ad4e47475d23135f0ba2fb1eedb0aa31f961117", "commit_訊息": "[Web表單設計師] Q00-20230609002 修正轉存表單因元件Id大小寫被視作相同異常問題，卡控前端元件大小寫一樣也視為相同元件Id", "提交日期": "2023-06-09 13:59:23", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f8376acaffd6fd22f3c1e2833be5017ead7f6b1f", "commit_訊息": "[Web]Q00-20230609001 調整待辦流程開啟列印表單時Grid數據沒有加載的問題", "提交日期": "2023-06-09 12:02:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "adae5d3b79c3bb75c0e1f18f11e90809dbd36911", "commit_訊息": "[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件。", "提交日期": "2023-06-09 10:54:23", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "78ecd87753b74c176a5567f7a4bacb47cd413986", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-08 18:00:56", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ce4f9d2300da2cd49917bd2883ae299d1113937", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-08 16:57:10", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5886cdbe52d3303d01c9775d9189ab0e5b8498af", "commit_訊息": "[Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系", "提交日期": "2023-06-07 15:58:33", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c2606c6fe2f65bbc882e68b7989d06b4432e577", "commit_訊息": "[Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題", "提交日期": "2023-06-07 14:51:50", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "43d2bccada15dcf81953b3bbe2ca6859d6d391ea", "commit_訊息": "[BPM APP]Q00-20230607001 修正企業微信列表在iPad裝置下無法正常篩選流程狀態問題", "提交日期": "2023-06-07 11:42:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobileSelect.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b864c00be9786bae810b75413f482f87430b0b67", "commit_訊息": "[ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持\"通知\"選項", "提交日期": "2023-06-06 14:44:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76f913320c0359f4f693eb808de77c931b4f5167", "commit_訊息": "[Web]A00-20230605001 修正在待辦情況下將HandWriting元件透過Script設置disable時沒有作用問題", "提交日期": "2023-06-06 13:57:44", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c19e15f8ea339dfbc34403cd3d5915bf8dde9bf7", "commit_訊息": "[BPM APP]S00-20230509001 優化整合企業微信在不同裝置統一使用微信提供的閱讀器下載附件[補]", "提交日期": "2023-06-06 09:21:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6d5f4cae4cf358551204e0bc089819cc677bfbd9", "commit_訊息": "[Web]Q00-20230601001 [在地化]回收至標準產品 [補修正]", "提交日期": "2023-06-05 17:35:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1125b9532bcade8e0c1b9bab08051e3fec9f8faa", "commit_訊息": "[內部] Q00-20230605004 新增standalone.conf.bat 記憶體建議配置", "提交日期": "2023-06-05 17:31:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "Release/wildfly/bin/standalone.conf.bat", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3986bdf650858e74ccba4fc87c68c9cafc3d7cef", "commit_訊息": "[流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求", "提交日期": "2023-06-05 17:14:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/WebApplicationAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37e81f24dda5a36ac08182522a4dd5d2e9bf5c0a", "commit_訊息": "[WEB]S00-20220818004 新增系統參數在待辦關卡進行退回重辦或向前加簽關卡時，判斷是否要自動儲存表單或詢問是否儲存表單[補修正]", "提交日期": "2023-06-05 15:04:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93d6aae0bf9f85cd784b63915fb057b2d32d37d5", "commit_訊息": "[Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補)", "提交日期": "2023-06-05 11:41:57", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b3b9861de6332080f4ea573f7594425cac7a864", "commit_訊息": "[T100] Q00-20230605001 修正T100同步單別時，作業代號若有底線，造成同步異常問題", "提交日期": "2023-06-05 11:13:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e7da7371d1cb54271a2330cab0dca271c708da7", "commit_訊息": "[WEB]Q00-20230602004 修正流程重要性在下列情境時，無法正確顯示設定的重要性 1.關卡設定列印模式，並點擊列印表單後 2.多人關卡只需一人處理，並點擊「由我處理」後 3.關卡派送失敗，畫面提示派送失敗後", "提交日期": "2023-06-02 15:48:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "749e59aaf73a2b66c90685550554b84e013e176f", "commit_訊息": "[WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料", "提交日期": "2023-06-02 11:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1eaea2b1ab02ec3b16b23a0322159aed20bfa48c", "commit_訊息": "[Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題", "提交日期": "2023-06-02 10:54:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "cadf8027133203699c55f5821bff8f6044733897", "commit_訊息": "[WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]", "提交日期": "2023-06-02 10:23:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f004f67f16c873715ca5d520e6e91824d191d78e", "commit_訊息": "[Web]Q00-20230601007 修正流程資料查詢於點新增欄位為條件的按鈕後看不見Grid內容的問題", "提交日期": "2023-06-01 15:27:13", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2232a212371ac297cb4a012ab96ed4ca297c12df", "commit_訊息": "[BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題", "提交日期": "2023-06-01 15:10:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d64060f70c217103f66b7fff5c180ec8b4436ba3", "commit_訊息": "[ISO]Q00-20230601005 修正版更至5892後，發生系統設定的pdfconverter.bcl.easypdfversion被變為6.3，因為歸檔浮水印的參數會吃不到，導致大量文件歸檔後浮水印異常。", "提交日期": "2023-06-01 14:38:37", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/conf/NaNaISO.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c124528733949c8abe559a79b18a866606265e4e", "commit_訊息": "[WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息", "提交日期": "2023-06-01 12:04:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e6e7129fe3c482aaa2c3640150ed1dfbb9fd3872", "commit_訊息": "[組織同步] Q00-20230531001 E10組織同步，新增LOG，以判斷異常錯誤。(補修正)", "提交日期": "2023-06-01 11:40:22", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/exception/ServiceException.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fe1fa45c09adfe435661bfbde88c87960016168e", "commit_訊息": "[Web]Q00-20230601001 [在地化]回收至標準產品", "提交日期": "2023-06-01 11:16:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FavoritiesProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "4c2b04b5068e5d2c25f532fab408af3e7552feef", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-01 11:13:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb098aae3b580951f459bdc2a6533395b174857d", "commit_訊息": "[其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容", "提交日期": "2023-06-01 11:24:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1e315f4d1f3fab07e85963cadc5f5ec6f89c1adb", "commit_訊息": "[WEB]S00-20220818004 新增系統參數在待辦關卡進行退回重辦或向前加簽關卡時，判斷是否要自動儲存表單或詢問是否儲存表單", "提交日期": "2023-06-01 11:06:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "f0ec8dabaac7469c73d7f1a9f00bd168325b72e3", "commit_訊息": "[Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常", "提交日期": "2023-06-01 10:56:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/lib/Dwr/dwr.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed6d57e939502c5aea03dce1582dfe8200ebe5e8", "commit_訊息": "[WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤", "提交日期": "2023-05-31 17:47:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f1ffeb32f61f73612f73879d506d779edd7da93", "commit_訊息": "[組織同步] Q00-20230531001 E10組織同步，新增LOG，以判斷異常錯誤。", "提交日期": "2023-05-31 17:20:55", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c217264b8130e4323e22d09eb79453aa15092628", "commit_訊息": "[流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序(補修正)", "提交日期": "2023-05-30 16:03:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActivityInstanceComparator.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ff2f0bda83cc54e64b9149b8849be905a5d421c2", "commit_訊息": "[Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示", "提交日期": "2023-06-05 10:37:25", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6baba544d35b7a4b4b2bc30efd3e38a135a7228a", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-05-29 18:56:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d2fa13a6fb89c1e0825ace96a5803d7ca76be736", "commit_訊息": "[BPM APP]S00-20230509001 優化整合企業微信在不同裝置統一使用微信提供的閱讀器下載附件[補]", "提交日期": "2023-05-29 17:52:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "11844e8e4b3e9979866665a602f54c0baca59c41", "commit_訊息": "[Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態", "提交日期": "2023-05-30 10:27:14", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ec8147016cc14c5462420e3475c8497ead3d24b3", "commit_訊息": "[Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)", "提交日期": "2023-05-29 17:06:25", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ab4809592b23aca7bfb56e8a659665b4a24ea677", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正", "提交日期": "2023-05-29 16:54:46", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c013b0bcc9eb0cfac2ae2cef67596bbaa73d40eb", "commit_訊息": "[內部]補充OID有序的誤解說明", "提交日期": "2023-05-29 13:08:03", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/AbstractPersistentObject.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c43bd8c4dab48c280e6e8331a3817207cd5a294a", "commit_訊息": "[報表設計器]Q00-20230526005 調整新增報表選擇模組代號只顯示可在模組維護作業可新增修改的模組 [補修正]", "提交日期": "2023-05-26 17:30:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "274b093e5d38b1c75027e9bd504cf1e7298360f5", "commit_訊息": "[報表設計器]Q00-20230526005 調整新增報表選擇模組代號只顯示可在模組維護作業可新增修改的模組", "提交日期": "2023-05-26 17:27:51", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "523e1517c29f725f2dd73b27f21f8c1b32d8f320", "commit_訊息": "[WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起", "提交日期": "2023-05-26 16:38:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9c251eee480131ce2baf28b27d34459297a7edaa", "commit_訊息": "[BPM APP]Q00-20220621004 移動端FormUtil.setOption新增第三個參數可選擇移除RadioButton和CheckBox元件第一個空白選項與動態塞值在後續關卡沒顯示選中值的問題", "提交日期": "2023-05-26 15:36:11", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0e0c16000970bf0053f2617616dd63640679141b", "commit_訊息": "[Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題", "提交日期": "2023-05-26 14:59:32", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f7e348d583fc3970b71210eca16907fba6059826", "commit_訊息": "[Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間[補修正]", "提交日期": "2023-05-26 10:45:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "00ccc4c11df9379b041d82526f01bd7355a7c298", "commit_訊息": "[WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]", "提交日期": "2023-05-26 10:10:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fcba312f05ecc65f975acf1de0cf0636e1240ee2", "commit_訊息": "[BPM APP]Q00-20230525007 修正移動端ListBox元件使用動態塞值如果有多選會在簽核關卡只顯示第一個選中值的問題", "提交日期": "2023-05-25 18:15:54", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4609dd059f78697f0243dc5c557e54df49b8d238", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題", "提交日期": "2023-05-26 15:02:51", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "49550264f29fae9b627e01bc7946481e8d1f36f0", "commit_訊息": "[Web]Q00-20230215002 調整RWD表單設計器元件-HorizontalLine元件，樣式一、樣式二的屬性不統一", "提交日期": "2023-05-25 17:18:56", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "386f0c4d570dd0e226efd1d12141c1e2bf303dd9", "commit_訊息": "[Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間", "提交日期": "2023-05-25 15:33:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8548540899ab5908f1364ee88ba94e61fc51a8ec", "commit_訊息": "[WEB]Q00-20230525004 修正在線閱覽浮水印管理及SQL註冊器的表單多選開窗的資料總筆數與清單數量不一致的異常", "提交日期": "2023-05-25 14:51:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a358851f99e1a297b9b3a5408a1b10c80aa795ab", "commit_訊息": "[BPM APP]S00-20230509001 優化整合企業微信在不同裝置統一使用微信提供的閱讀器下載附件", "提交日期": "2023-05-25 14:26:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "bf8f4b6d525ee78f8e01614c3ef0d9fe3f08abd2", "commit_訊息": "[流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序", "提交日期": "2023-05-25 14:03:05", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "95a98038e674ec60e77d15c7bc750b218f20b88f", "commit_訊息": "[Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題", "提交日期": "2023-05-25 10:16:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c534a550fe1cf690d213be613ee39162f0a64901", "commit_訊息": "[流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常", "提交日期": "2023-05-24 17:36:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71f1b33f21e5d428e6233ecc4a614aaffbc921ee", "commit_訊息": "[Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加", "提交日期": "2023-05-24 17:01:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18ecda9fe2e6a3dd1a8e0eb2d55179e0245f3da7", "commit_訊息": "[WEB]Q00-20230524003 調整簡易流程圖，當進行中的關卡有轉發通知給其他使用者時，簡易流程圖由顯示通知人員為處理者改為不顯示通知人員為處理者", "提交日期": "2023-05-24 16:04:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4bb6d303b7b5e6b6283c254d0d3d77077f99484", "commit_訊息": "[Web]Q00-20230524002 調整當開窗取回的資料為null時會顯示&nbsp;而不是空白問題", "提交日期": "2023-05-24 15:27:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/ResultObjectForDataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "539fab20ff7da3cc3330a5243835eac37ea5641a", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-19 17:18:41", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e19fd43cb1bd95985f22d4a1daae18d9ab9a3219", "commit_訊息": "[Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員", "提交日期": "2023-07-19 14:28:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0afd2c70cdac6f546b74308fc1add7384b2dccd6", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-07-18 16:57:02", "作者": "刘旭", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "da196af8fc32e1676faafe67d31da9d43322b36f", "commit_訊息": "[web]Q00-20230718003 绝对位置表单的time元件在表单右侧边缘处,点击按钮出现的选择画面在旧版中显示完整，但是在新版中未显示完整的问题修复", "提交日期": "2023-07-18 16:55:23", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "14fb24591a2aec2be527d38f06b6d6e4db7d24ca", "commit_訊息": "[流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException", "提交日期": "2023-07-18 16:45:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8c6d4498bd8f54da8f705f0dde5a68f0573e6a3", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-18 14:51:36", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89ab23b12434243a80f5a236537f2d7acde6478a", "commit_訊息": "[流程引擎]Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序[补修正]", "提交日期": "2023-07-18 12:40:40", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e8d0fa750a7c29e742f132b689a74de31f54dcc", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-18 11:18:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ba3da444031b7f8c20fa015d6c918f6175b6bc60", "commit_訊息": "[流程引擎] Q00-20230717003 修正終止流程時，偶發ProcessInstance與BamProInstData狀態不一致問題", "提交日期": "2023-07-17 17:55:45", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "6ab735be34e82902833849f4c436f5e0be7909d2", "commit_訊息": "[web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复", "提交日期": "2023-07-17 14:37:58", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7407cc61293be416840446b65e71bb5fa024d699", "commit_訊息": "[其他]S00-20230717001 iReport報表-新增「iReport的資料庫連線是否只允許啟用TLS1.2」的參數設定，並根據參數設定判斷是否走新版iReport的報表", "提交日期": "2023-07-17 12:03:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "69d2029c2a37ab9b3a6ff3fbd899187e7fb1ebb2", "commit_訊息": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]", "提交日期": "2023-07-17 09:25:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b6501737a1b6cc0f12e0890d474bd12402d29010", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-07-14 17:37:12", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "eab35061dcd411cfa71d3851937e2c7511d2fcae", "commit_訊息": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。", "提交日期": "2023-07-14 17:36:50", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e34823c8e6f758e237307c5376772783a58f6562", "commit_訊息": "[Web] Q00-20230706001 新增系統變數，統一設定ESS表單的最小高度，若表單高度超過此設定，則套用表單高度；若無超過則套用此設定(補)", "提交日期": "2023-07-14 16:41:25", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "29e20772e44c4ac58783c81b2dad91d18e7077d3", "commit_訊息": "[流程引擎] Q00-20230714001 修正服務任務關卡執行後，不會重新組成全文檢索欄位問題", "提交日期": "2023-07-14 14:48:48", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2bab75b23c9b83dceb5f31afc566774bd7495076", "commit_訊息": "[Web]Q00-20230713004 優化主管首頁與企業流程監控會使用到的SQL", "提交日期": "2023-07-13 11:58:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a62043276a68267678abd2f1816e5ec97c54c455", "commit_訊息": "[Web] Q00-20230713003 優化使用者登入時異常時的LOG", "提交日期": "2023-07-13 11:44:10", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/PersistentObjectHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "42ee167397bc3b4420c4ffdc02a06f5b87fcf33d", "commit_訊息": "[Web]Q00-20230713002 修正使用者為部門主管從Portal進入BPM點選首頁顯示內容不為主管首頁", "提交日期": "2023-07-13 10:23:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3b43567a6c153e33bf0ab34a981baebdb8b078fd", "commit_訊息": "[E10]Q00-20230712002 修正E10單據開啟遇到setColumnWith異常訊息導致無法繼續派送 [補修正]", "提交日期": "2023-07-13 08:52:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/E10Form.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "edc08b8369136cb242840652bfce2b69076e69aa", "commit_訊息": "[web]Q00-20230712003 修正在转存表单时栏位原數值為小數點後兩位，轉存表單後僅剩小數點後一位", "提交日期": "2023-07-12 15:59:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c9a04b73d9bb867018b946c79ea2692b9fbf870", "commit_訊息": "[E10]Q00-20230712002 修正E10單據開啟遇到setColumnWith異常訊息導致無法繼續派送", "提交日期": "2023-07-12 15:07:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/E10Form.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8b0731a831be11107f588aee29ed6cd0cb91f74d", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58", "提交日期": "2023-07-12 14:44:24", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "cc2865edd8c4a2519c4a559e32157613d0cace99", "commit_訊息": "[Web]Q00-20230712001 修正从布景主题修改banner图片或还原预设图片情况下，简体语系修改或还原不成功问题", "提交日期": "2023-07-12 14:43:39", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d407d9ac1f1c1c901421f4e5adfd326c7c9b0e7", "commit_訊息": "[流程引擎]Q00-20230706003 新增索引優化簽核緩慢[補修正]", "提交日期": "2023-07-12 13:47:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "02013f7b5e6a5920286556277ac6e0149919270c", "commit_訊息": "[Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白。", "提交日期": "2023-07-11 14:12:59", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "058aecf4a1cbce40632964aed8899a0a8af71db1", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-11 12:06:55", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "68c2daf08479dc87674f1d2e9734534806fe6b32", "commit_訊息": "[流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題(補)", "提交日期": "2023-07-11 10:24:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dd4506d245bb8f419545f0c866905783cddfd299", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-07-10 19:18:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d3195855611e94bf59f82685c3daff65c92da90", "commit_訊息": "[web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题", "提交日期": "2023-07-10 16:56:51", "作者": "liuxua", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "af44a00fa2fa97b6c25b463e87e4d3fed03f246d", "commit_訊息": "[web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题", "提交日期": "2023-07-10 16:51:34", "作者": "liuxua", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a641feb230fbb4667073960e703e5814ad619c80", "commit_訊息": "[T100]S00-20230421001 T100取簽核歷程關卡若有退回(取回)重辦，只取最新處理時間的關卡[補修正]", "提交日期": "2023-07-10 15:19:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3f0982f8ae5c0e0d1c6ef291c1fc78fff09cbab8", "commit_訊息": "[T100]S00-20230421001 T100取簽核歷程關卡若有退回(取回)重辦，只取最新處理時間的關卡", "提交日期": "2023-07-10 15:13:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "0e4b6874b979de845fd8aba1a0df634e090c90b6", "commit_訊息": "[其他]Q00-20230704001 調整BCL8單個檔案轉檔逾時時間由預設2分鐘改為預設10分鐘", "提交日期": "2023-07-10 11:38:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6fea34add79ca9fcadaca5716bc8a35aef093481", "commit_訊息": "[ESS]Q00-20230710001 調整log錯誤訊息的顯示", "提交日期": "2023-07-10 10:49:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0613968e29bb55ca87e49656eceee210ec933fb9", "commit_訊息": "[流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題。", "提交日期": "2023-07-07 14:39:22", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4768ae3e76dfe43f02caae1d0244ec154352bf0d", "commit_訊息": "[ESS]Q00-20230707001 新增ESS預設出貨\"招募\",\"培訓\",\"績效\"的管理類作業 [補修正]", "提交日期": "2023-07-07 12:24:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\345\\237\\271\\350\\250\\223\\351\\226\\213\\347\\231\\274\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\345\\237\\271\\350\\250\\223\\351\\226\\213\\347\\231\\274\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\346\\213\\233\\345\\213\\237\\347\\224\\204\\351\\201\\270\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\346\\213\\233\\345\\213\\237\\347\\224\\204\\351\\201\\270\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\346\\273\\277\\346\\204\\217\\345\\272\\246\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\346\\273\\277\\346\\204\\217\\345\\272\\246\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\347\\270\\276\\346\\225\\210\\350\\200\\203\\346\\240\\270\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/(HRD)\\347\\270\\276\\346\\225\\210\\350\\200\\203\\346\\240\\270\\347\\256\\241\\347\\220\\206\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "a9b6c86e124bad0827eb912431174eb4325672ef", "commit_訊息": "[ESS]Q00-20230707001 新增ESS預設出貨\"招募\",\"培訓\",\"績效\"的管理類作業", "提交日期": "2023-07-07 09:03:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b0779b0539f424976fd0156aaf34529ac14a6265", "commit_訊息": "[Web]Q00-20230706004 修改流程中，转由他人处理画面上“经常选取对象”里，不会带出离职人员。", "提交日期": "2023-07-06 17:28:46", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8c7b230596e16b46f18674e0ce7caf4f1bd6655a", "commit_訊息": "[流程引擎]Q00-20230706003 新增索引優化簽核緩慢", "提交日期": "2023-07-06 15:42:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "9d3c6a62b097e4633033b01a0a9c0ee39245beb7", "commit_訊息": "[組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。", "提交日期": "2023-07-06 15:22:50", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "739d1c7dc1ec6c524ad2cae28b7076da91f8b223", "commit_訊息": "[Web] Q00-20230706001 新增系統變數，統一設定ESS表單的最小高度，若表單高度超過此設定，則套用表單高度；若無超過則套用此設定。", "提交日期": "2023-07-06 11:40:29", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "16b58e5f13266c23d6f7292f4c07a263092c8b8d", "commit_訊息": "[Web] Q00-20230628002 新增系統變數可自定義簽核歷程是否要出現按鈕，並調整為無須重啟BPM即生效(補)", "提交日期": "2023-07-05 18:19:53", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d08be8495025740b1e065d3e73bc96aee6ac75d4", "commit_訊息": "[Web]S00-20230504003 調整汇入表单，使用原本ID，若存在的ID，會提示是否要Overwrite", "提交日期": "2023-07-05 15:55:42", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1bf85f21bd86a161a4922ac73dd2cac256d58c8d", "commit_訊息": "[Web]Q00-20230705002 修正表單在预览时，更換image后，显示图片不正确的问题", "提交日期": "2023-07-05 14:23:40", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ImageElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ef2f01da000a1e5397d88eff9a39e82fe87aee2b", "commit_訊息": "[BPM APP]Q00-20230705001 修正行動表單的Title元件無法使用FormUtil.setLabel方法設定標籤內容", "提交日期": "2023-07-05 12:07:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c13d86dbc1d004b9b4e9eb7477aca28877b3d760", "commit_訊息": "[MPT]S00-20230315001 首頁我的最愛模塊增加顯示常用功能[補]", "提交日期": "2023-07-04 17:40:55", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ba68cb93a82c8bad38aba9f3d8626223535627f", "commit_訊息": "[Web]Q00-20230704003 調整formScript撰寫ajax加簽關卡後，需要更新session裡面的ProcessInst的相關屬性(Processpackage,ProcessDef等屬性)，避免預覽流程仍以加簽前的定義做解析", "提交日期": "2023-07-04 17:40:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "528c683cc4d600176f5f6510cca170bd4be2f786", "commit_訊息": "[Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級(補)", "提交日期": "2023-07-04 17:34:15", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65421f65532b4a36d723473300f0544d317f0358", "commit_訊息": "[Web]Q00-20230704001 調整CommonAccessor.updateConnectedUserInfo()更新使用者時間的方法，當更新異常時，由前端畫面提示「更新線上時間失敗」改為後端serverlog記錄就好", "提交日期": "2023-07-04 17:02:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "17eb2f9b83918a7a98ab5611089ced794ac201ad", "commit_訊息": "[Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級", "提交日期": "2023-07-04 16:43:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c2794725373dc98ef961f5004699f8eed6086ce", "commit_訊息": "[內部]新增Web化流程管理工具程式定義與其SQL", "提交日期": "2023-07-04 15:19:47", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "cef8d3a7abac8514e09794622d0c1be6eb744f0c", "commit_訊息": "[MPT]S00-20230315001 首頁我的最愛模塊增加顯示常用功能", "提交日期": "2023-07-03 17:43:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "2f3a5ae3d6a54aac1af2825f8dcc745e60edaf68", "commit_訊息": "[Web] Q00-20230703004 修正表單列印畫面元件跑版問題，邊界調整為0", "提交日期": "2023-07-03 16:31:00", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b87a40c84b19e4c7ef21d3283930dd2956ab8ae2", "commit_訊息": "[E10]Q00-20230703003 修正子單身內容未傳，造成E10回寫失敗", "提交日期": "2023-07-03 15:39:12", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormInstanceTransferJson.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a34193751d676ff3341768608be45ce7eefaa875", "commit_訊息": "[E10]Q00-20230703001 修正E10因服務任務未放入表單實例參數造成回寫失敗", "提交日期": "2023-07-03 11:16:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213(\\344\\270\\215\\351\\251\\227\\350\\255\\211\\350\\241\\250\\345\\226\\256)\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254(E10\\346\\224\\257\\346\\214\\201\\347\\211\\210\\346\\234\\254\\357\\274\\232E10_6003.V5\\347\\211\\210\\345\\220\\253\\344\\273\\245\\344\\270\\212)/e10\\346\\265\\201\\347\\250\\213(\\344\\270\\215\\351\\251\\227\\350\\255\\211\\350\\241\\250\\345\\226\\256)\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0d56063ad20ec93696fdb14253ad31c366f135f0", "commit_訊息": "[Web] Q00-20230628002 新增系統變數可自定義簽核歷程是否要出現按鈕，並調整為無須重啟BPM即生效(補)", "提交日期": "2023-06-30 17:11:27", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0e9f99f64a329874972d3620c6013db04f47e80", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-28 19:12:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a278063241ea193d4a4342cd2dbd364dfd4eed27", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-28 18:50:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "739901654f5e0dbcc8b62f217ce8408adfe9733a", "commit_訊息": "[SAP]Q00-20230628003 修正SAP整合回寫呼叫SAP的invoke服務，當SAP回傳的訊息需存放在Grid時，若Grid內容為空時，可能會導致formInstance.fieldValues產生多組相同Grid代號的內容", "提交日期": "2023-06-28 18:02:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f41211d316a6d26e63f0cb039a3e1c0f84bbee46", "commit_訊息": "[Web] Q00-20230628002 新增系統變數可自定義簽核歷程是否要出現按鈕，並調整為無須重啟BPM即生效", "提交日期": "2023-06-28 16:28:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 18}, {"commit_hash": "ac1537facdc28dbf1803ad36b3c2a59a9509c024", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-28 15:57:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5996737d7ca8fd0625ece8c63de524e8903cff8b", "commit_訊息": "[Web]Q00-20230628001 调整绝对位置表单中的各元件border宽度在Chrome和edge显示问题", "提交日期": "2023-06-28 11:16:06", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/form/ElementDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f677528d75064e702b94e82cdce3612b89cd362f", "commit_訊息": "[SAP]Q00-20230627003 SAP整合的invoke服務任務增加表單相關資訊log", "提交日期": "2023-06-27 15:09:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e23ff918562cf656287b5b734fd80e6651a82d27", "commit_訊息": "[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件(補修正)", "提交日期": "2023-06-27 14:53:03", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9ef23e2781ad710add665ec8029f36073fe9f9e", "commit_訊息": "[TipTop] Q00-20230627002 調整TIPTOP的附件選擇txt時，上傳文件且未填說明欄位，轉檔異常問題。", "提交日期": "2023-06-27 14:41:06", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3f17a55c906399cc2c688b9b59df61a2ee7bda6", "commit_訊息": "[T100]Q00-20230627001 修正關卡設置\"所有附件皆需開啟過\"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送", "提交日期": "2023-06-27 13:39:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09c7125679b73f326040d28c065161dc41f0f2af", "commit_訊息": "[MPT]Q00-20230626004 修正新增流程分類模塊流程未定義分類該選項會呈現空白問題", "提交日期": "2023-06-26 18:45:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.3_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "e5caa82ef512f72856532033637a3861aa535e72", "commit_訊息": "[流程引擎] Q00-20230626005 新增索引", "提交日期": "2023-06-26 17:30:59", "作者": "raven.917", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5d8334bc145795cbdafc021393248e43599e437e", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-06-26 16:28:58", "作者": "raven.917", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "9b2bd85744d31a1d549c232eb53f66c35f74feae", "commit_訊息": "[Tiptop] Q00-20230626002 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。", "提交日期": "2023-06-26 16:28:03", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ce0e866764537774a9a6570b5287eb949355fc6b", "commit_訊息": "[Tiptop] 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。", "提交日期": "2023-06-26 16:28:03", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "10ae04a32dad90403bce904b2365fc41fa06207e", "commit_訊息": "[E10]Q00-20230626001 新增E10發單log訊息", "提交日期": "2023-06-26 15:29:13", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe1d1109252cf0d50f38f89a0fa106f9410bbb8b", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-06-21 17:59:40", "作者": "pinchi_lin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a09608bec2e7abdc654fbf2933dce9c7eb3bf870", "commit_訊息": "[內部]調整流程設計師工具Web化改開窗顯示", "提交日期": "2023-06-21 17:59:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c5fa947f810665aaa31ab28271031220ceccc4a3", "commit_訊息": "[BPM APP]C01-20230620004 調整企業微信推播內容計算邏輯錯誤問題", "提交日期": "2023-06-21 17:58:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc7dbbecee1c7d3b411b3dd3141b401487256113", "commit_訊息": "[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件(補修正)", "提交日期": "2023-06-21 17:41:47", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "852d1b399445406cfe98263e2436223ad28d2884", "commit_訊息": "Revert \"[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件(補修正)\"", "提交日期": "2023-06-21 16:40:43", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ae6d6b9e3b9c1cd234198cea3691f913cb216b6", "commit_訊息": "[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件(補修正)", "提交日期": "2023-06-21 16:10:00", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1b69df60941d9b86feb5a8612b558ab7d20afc9", "commit_訊息": "[BPM APP]S00-20230608001 優化BPMAPP詳情頁面不同意簽核意見增加彈窗機制[補]", "提交日期": "2023-06-21 14:42:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "358b6aa117b25e5b9c88dea44a8a5b6d43cd43e0", "commit_訊息": "[Web] Q00-20230621003 修正Rwd-Grid 設置必填時，Alert訊息異常問題", "提交日期": "2023-06-21 11:44:24", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e0250f0f6049d80de2c7123106e5854058d49923", "commit_訊息": "[Web]Q00-20230621002 修正手機點選批次終止時，沒填值的狀況不會跳出alert訊息", "提交日期": "2023-06-21 11:30:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a93e9f0789166102dddf75057264aa8bf9f413b9", "commit_訊息": "[在地化] Q00-20230620003 增加驗證SSOkey時，時間間隔超過5分鐘，印出LOG訊息", "提交日期": "2023-06-20 17:03:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99aeda99465c95c1659d1f5e66c615c6fd9778cd", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-20 16:19:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "81d210e792f2f069022ccb4f2f945df24ee927b5", "commit_訊息": "[內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息", "提交日期": "2023-06-20 15:54:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa14e8eb344b7297ed91909db5b2cff7bebd66f6", "commit_訊息": "[Web] Q00-20230620001 調整絕對定位表單追蹤流程下列印表單畫面。", "提交日期": "2023-06-20 14:56:48", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e9fc7a163b4d62f2d4eee7bed5875e4f0215dcb4", "commit_訊息": "[ISO]S00-20230619001 文件攜出模組中的「文件攜出記錄報表」查詢報表新增匯出Excel功能", "提交日期": "2023-06-20 11:37:56", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0dfa9eb5a5093b9c646248e59c47c7245c9309c0", "commit_訊息": "[Web]Q00-20230619001 修正Grid元件在多欄位時欄位寬度異常顯示問題", "提交日期": "2023-06-19 10:12:14", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9f33104e9d5dff7a48f4377357ae4b328af07d03", "commit_訊息": "[流程設計師]Q00-20230619003 修正流程設計師的條件驗證在簡體的\"財\"字進行驗證被認為判斷錯誤", "提交日期": "2023-06-19 10:01:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/util/ConditionEvaluator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c40281a745aea9cde74ae1b56c8f07ef45fd6bb7", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-17 15:16:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a43d16a98c94c917fbcb79a0f7097bfa8eb64a3b", "commit_訊息": "[表單設計師]Q00-20230617002 修正當行動版表單腳本編輯器為空時點擊儲存表單沒有效果問題", "提交日期": "2023-06-17 13:48:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "97f68b4bd8c640cff1341ec0e0b42ac02cf3fb9b", "commit_訊息": "[MPT]C01-20230612004 修正公告申請單的公告內文(富文本)在流程結案後仍可修改的問題", "提交日期": "2023-06-17 10:17:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/copyfiles/@mpt/default-form/MptAncApply.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "56e6798798823f5e428cced8918eed2e968d97c0", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-17 09:07:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d12610ee15ffaf8ec12b382e136c0844a4bde50", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)", "提交日期": "2023-06-16 16:45:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7da91765488de886ebab4ffb012d42f85e371a7b", "commit_訊息": "[BPM APP]S00-20230608001 優化BPMAPP詳情頁面不同意簽核意見增加彈窗機制[補]", "提交日期": "2023-06-16 16:06:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fb6755958c7c12659d023ae316fd45991d8ad9bb", "commit_訊息": "[Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題[補]", "提交日期": "2023-06-16 13:56:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "60c7f06b2e17ac5c6571dde78651f960251142fd", "commit_訊息": "[E10]Q00-20230616001 新增提示訊息在表單同步若上一版的元件為下拉選單，欄位同步成輸入元件就會提醒用戶請移除該下拉元件並重新同步", "提交日期": "2023-06-16 11:05:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "cb84064c44bee327ec49f0c536b2e1b297d7e3d3", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-15 18:22:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6d49aecd9bad6dc9cd07181f53698fcd50988788", "commit_訊息": "[BPM APP]S00-20230608001 優化BPMAPP詳情頁面不同意簽核意見增加彈窗機制", "提交日期": "2023-06-15 18:19:48", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "c60739e1456a3657cc20fece216bee954224ac55", "commit_訊息": "[Web]Q00-20230615002 修正離職維護作業無法開啟", "提交日期": "2023-06-15 17:18:15", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "47acf0d05a384010a2274945b0560fbf171b10d8", "commit_訊息": "[Web] Q00-20230615001 修正客制開窗order by轉小寫導致模糊查詢異常問題。", "提交日期": "2023-06-15 11:52:52", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c4b1ea57aa5f95f83c49ddfad67b887ea41149ff", "commit_訊息": "[Web]Q00-20230613001 登入頁面新增越南語系及回收越南多語系內容[補修正]", "提交日期": "2023-06-14 14:33:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "19a778e55ebbf7b519704d5ea57dcb3a1c2588b1", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-13 18:01:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4298fcdb1021ba305becd0f518e5601522728c02", "commit_訊息": "[Web] Q00-20230613004 調整絕對定位表單，因瀏覽器機制改變，框線變粗體問題。", "提交日期": "2023-06-13 16:13:30", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ActiveWidgets264/styles/system/aw.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "958b649f4c22284a6a20676a17e02c03129ac13c", "commit_訊息": "[Web] Q00-20230613003 調整參與者型式的關卡頁面的「檢視轉派歷程」按鈕圖示。", "提交日期": "2023-06-13 14:18:28", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba910e5cfe804e49aad013b9d082ec849c21fef8", "commit_訊息": "[流程引擎]Q00-20230613002 修正流程取回重辦時，當關閉取回重辦後面的關卡時，需一併將後面關卡直接退回的關卡記錄清除，避免關卡再次派送時，流程會直接回到直接退回的關卡，而非重新展開流程", "提交日期": "2023-06-13 13:48:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bf4117e0f410670d5bb9b18febef76d875f5a11b", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-06-13 13:45:30", "作者": "raven.917", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "4e71fb93e84b61915cc0c11ad0d076037c744d60", "commit_訊息": "[Web]Q00-20230613001 登入頁面新增越南語系及回收越南多語系內容", "提交日期": "2023-06-13 12:00:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8eaa543d42415fc9301c34f26ec4d0a2ada6e1eb", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)", "提交日期": "2023-06-13 11:36:01", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "126ce90be1b2a5732d2a545ba084a8a4fd9017aa", "commit_訊息": "[Web] Q00-20230612001 優化單身欄位加總操作體驗，因新增修改刪除，會跳回原欄位(補)", "提交日期": "2023-06-13 10:41:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "511f72a3cb1c6c80c3772ae93763bcb20c1a858d", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。", "提交日期": "2023-06-12 14:57:27", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "03755ed19d5ea8a9909cb122f6ec234bd0e2053d", "commit_訊息": "[Web] Q00-20230612004 修正絕對定位表單，上傳圖片異常問題。", "提交日期": "2023-06-12 13:57:42", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29de8d1bb7bae1f8f1581abcf80708f4563090b2", "commit_訊息": "[Web]Q00-20230612003 修正Script撰寫Grid的setColumnWith語法會跳出alert", "提交日期": "2023-06-12 12:02:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "81d1e4171a45d984de512d85f8aa050ef069ecd3", "commit_訊息": "Revert \"[Web] Q00-20230612001 優化單身欄位加總操作體驗，因新增修改刪除，會跳回原欄位\"", "提交日期": "2023-06-12 11:45:47", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7606972c2344900f27544fe4c199891ecad4abf", "commit_訊息": "[在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕", "提交日期": "2023-06-12 11:27:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "094bbd62c0b4869bbf16ddc2f8e2ef7e84b2631c", "commit_訊息": "[Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息(補修正)", "提交日期": "2023-06-12 10:45:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "883430b36df01bbf247c70fc54d72f70a5f3dd19", "commit_訊息": "[Web] Q00-20230612001 優化單身欄位加總操作體驗，因新增修改刪除，會跳回原欄位", "提交日期": "2023-06-12 09:21:40", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "17cd8e473f7df0385a277e74c7f80fbf06f50cb1", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-12 09:08:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d71fdb72f819e2c59592fd51623094c28b4ea66c", "commit_訊息": "[流程引擎]Q00-20230609007 回收索引", "提交日期": "2023-06-09 18:08:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6ce7389fc50e090932853b3ce96457e747c78d70", "commit_訊息": "[Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息", "提交日期": "2023-06-09 17:16:16", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4efcaea6a3c6fee0e0042cadf074aac2dd8ae91b", "commit_訊息": "[BPM APP]S00-20230509001 優化整合企業微信在不同裝置統一使用微信提供的閱讀器下載附件[補]", "提交日期": "2023-06-09 16:18:34", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "0e40e6689da1ca34ef63f20b2745eed60f8547b2", "commit_訊息": "[Web] Q00-20230609004 修正匯入Excel資料內有,會被替換成空白問題", "提交日期": "2023-06-09 15:13:05", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad3fba6da6637a89975a37641365c2619f7ebcb5", "commit_訊息": "[WEB]Q00-20230609003 修正流程掛載多表單，且各表單的Grid內容都有值，且流程設計該關卡不允許修改表單內容時，自動載入Grid的功能會失效", "提交日期": "2023-06-09 14:04:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0f7a3d7243fb6a911853312bba3c7834d924bb0", "commit_訊息": "[Web表單設計師] Q00-20230609002 修正轉存表單因元件Id大小寫被視作相同異常問題，卡控前端元件大小寫一樣也視為相同元件Id", "提交日期": "2023-06-09 13:59:23", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5dfa259831091ae43ecc17a8bf6e84b82acd5f7f", "commit_訊息": "[Web]Q00-20230609001 調整待辦流程開啟列印表單時Grid數據沒有加載的問題", "提交日期": "2023-06-09 12:02:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbe4b612246f126b357a6edfe179a708fa9be90c", "commit_訊息": "[流程引擎] Q00-20230608001 調整退回重辦信件邏輯，被退回的關卡處理者應收到退回重辦信件。", "提交日期": "2023-06-09 10:54:23", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "5e4664283337e337c6fa5e11aa9764d30c47671f", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-08 18:00:56", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ca43abc8cdce5ac8cbe4fe7849fef54c0aed0b41", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-08 16:57:10", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "613f4a08ce8c4596ceb10539f9777647746a2f52", "commit_訊息": "[Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系", "提交日期": "2023-06-07 15:58:33", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3011dc512dda32942aaeec79d008573f8da1e0a4", "commit_訊息": "[Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題", "提交日期": "2023-06-07 14:51:50", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "48afeeed1f8ca6074101c5598e3c38bef6f38cc1", "commit_訊息": "[BPM APP]Q00-20230607001 修正企業微信列表在iPad裝置下無法正常篩選流程狀態問題", "提交日期": "2023-06-07 11:42:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobileSelect.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "51a0bcaf434551c9ab3660d3047b1c79e72b5fb9", "commit_訊息": "[ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持\"通知\"選項", "提交日期": "2023-06-06 14:44:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c29aa2de34452dfd1d76715739233dd9f9df0c2e", "commit_訊息": "[Web]A00-20230605001 修正在待辦情況下將HandWriting元件透過Script設置disable時沒有作用問題", "提交日期": "2023-06-06 13:57:44", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d9c226ff0a0566b9ae38c57386421254d26e3c5", "commit_訊息": "[BPM APP]S00-20230509001 優化整合企業微信在不同裝置統一使用微信提供的閱讀器下載附件[補]", "提交日期": "2023-06-06 09:21:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8f4ec384290713b4304f8af7c873faa4f9a18559", "commit_訊息": "[Web]Q00-20230601001 [在地化]回收至標準產品 [補修正]", "提交日期": "2023-06-05 17:35:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3a8da9604cd11986f86b170ca50bfee4752cc9cc", "commit_訊息": "[內部] Q00-20230605004 新增standalone.conf.bat 記憶體建議配置", "提交日期": "2023-06-05 17:31:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "Release/wildfly/bin/standalone.conf.bat", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32d777f5934dc42d016f9433dc1354dce9e7ecfc", "commit_訊息": "[流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求", "提交日期": "2023-06-05 17:14:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/WebApplicationAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e344b82c323d82aa2ed74b77bbfa1b69820a7dea", "commit_訊息": "[WEB]S00-20220818004 新增系統參數在待辦關卡進行退回重辦或向前加簽關卡時，判斷是否要自動儲存表單或詢問是否儲存表單[補修正]", "提交日期": "2023-06-05 15:04:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6a5f5b2f6838c184657ac834c8fd47408b3bcf0a", "commit_訊息": "[Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補)", "提交日期": "2023-06-05 11:41:57", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93ff26a6b3dc72d80ed4f111fca275857db361e9", "commit_訊息": "[T100] Q00-20230605001 修正T100同步單別時，作業代號若有底線，造成同步異常問題", "提交日期": "2023-06-05 11:13:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0787b8bb48b8104dc617d906305c610815eac7a7", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-06-05 10:37:49", "作者": "develop_20274", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "ea9560c6e0e7f037bf6bef30596699c14cace6ad", "commit_訊息": "[Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示", "提交日期": "2023-06-05 10:37:25", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63db1b9bd22830bfe40b03a5726f1927ed8ba9f6", "commit_訊息": "[WEB]Q00-20230602004 修正流程重要性在下列情境時，無法正確顯示設定的重要性 1.關卡設定列印模式，並點擊列印表單後 2.多人關卡只需一人處理，並點擊「由我處理」後 3.關卡派送失敗，畫面提示派送失敗後", "提交日期": "2023-06-02 15:48:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "da28cd7f9acc41b220b317fbfd57f9c97b68a46c", "commit_訊息": "[WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料", "提交日期": "2023-06-02 11:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "92b41dced8d881dcfbb21ac9cef1ba02a7612570", "commit_訊息": "[Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題", "提交日期": "2023-06-02 10:54:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0aab3dc8cd2456b5c73a3ac9927cdb53a7f81319", "commit_訊息": "[WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]", "提交日期": "2023-06-02 10:23:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2066ba2234cb35a1d751cc9bf7f9cc2c66b3dbd5", "commit_訊息": "[Web]Q00-20230601007 修正流程資料查詢於點新增欄位為條件的按鈕後看不見Grid內容的問題", "提交日期": "2023-06-01 15:27:13", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f29d9eec794cb9b2f7f084a41108dcf2024ec5d", "commit_訊息": "[BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題", "提交日期": "2023-06-01 15:10:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "025dadbc660d363b35860ad4502d8e5422259e2f", "commit_訊息": "[ISO]Q00-20230601005 修正版更至5892後，發生系統設定的pdfconverter.bcl.easypdfversion被變為6.3，因為歸檔浮水印的參數會吃不到，導致大量文件歸檔後浮水印異常。", "提交日期": "2023-06-01 14:38:37", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/conf/NaNaISO.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0956fe6716dedd6796f3b368d8dd86290020f9e", "commit_訊息": "[WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息", "提交日期": "2023-06-01 12:04:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5b600dcf9db488de3f4095ccdf041b2308fb4ab1", "commit_訊息": "[組織同步] Q00-20230531001 E10組織同步，新增LOG，以判斷異常錯誤。(補修正)", "提交日期": "2023-06-01 11:40:22", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/exception/ServiceException.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ad0f787f443d9c69bdcacd93eafefa85e697e3af", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-06-01 11:25:11", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "056b5761cff29cee4dc788a393821a00be81d6d6", "commit_訊息": "[其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容", "提交日期": "2023-06-01 11:24:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5e6b199adf8412e0d2e0c5ef0687964fb79b2038", "commit_訊息": "[Web]Q00-20230601001 [在地化]回收至標準產品", "提交日期": "2023-06-01 11:16:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FavoritiesProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "0460a09e5f3f49ef73fe3784ae7522ecc2a272d2", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-06-01 11:13:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0fda93e54cee264d8a602a0b2b7497d9ed10a431", "commit_訊息": "[WEB]S00-20220818004 新增系統參數在待辦關卡進行退回重辦或向前加簽關卡時，判斷是否要自動儲存表單或詢問是否儲存表單", "提交日期": "2023-06-01 11:06:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "d6718f80ce6e09bb07e1c5fa5829c86b105e1e02", "commit_訊息": "[Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常", "提交日期": "2023-06-01 10:56:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/lib/Dwr/dwr.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7d8f61e7b11c1738baf52fcefa2f1e01af324bd", "commit_訊息": "[WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤", "提交日期": "2023-05-31 17:47:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "17f58bcd80cbe65fa03e402f1f2a4f0a7750bd3c", "commit_訊息": "[組織同步] Q00-20230531001 E10組織同步，新增LOG，以判斷異常錯誤。", "提交日期": "2023-05-31 17:20:55", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22565620d76615d7a96bf7edbf44a4575b9188df", "commit_訊息": "[流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序(補修正)", "提交日期": "2023-05-30 16:03:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActivityInstanceComparator.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6582c0fe0df8ee1179ed8dc49fc004cc8c4998be", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-05-30 10:27:32", "作者": "develop_20274", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "783b1098ba7ea60a0d3a9d9f1abab0b06cd1d565", "commit_訊息": "[Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態", "提交日期": "2023-05-30 10:27:14", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1837c62f1f92a5cec5e87a3a3ca567f22a002376", "commit_訊息": "[內部]新增提供給流程設計師工具Web化使用的SessionBean[補]", "提交日期": "2023-05-29 18:56:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8f9cc46e0b57e6ab5027e15a6dc69ad6e338fcda", "commit_訊息": "[BPM APP]S00-20230509001 優化整合企業微信在不同裝置統一使用微信提供的閱讀器下載附件[補]", "提交日期": "2023-05-29 17:52:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4523d61ee4b28d4a1a5f25be6aff32d8c0363f0b", "commit_訊息": "[Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)", "提交日期": "2023-05-29 17:06:25", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ee63b3ebdada1ca13b70763c5b453850c0f4149", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正", "提交日期": "2023-05-29 16:54:46", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d4c5f9f35a5402c248599a8925e0c6427a99e50e", "commit_訊息": "[內部]補充OID有序的誤解說明", "提交日期": "2023-05-29 13:08:03", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/AbstractPersistentObject.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9823e47e33d687b59f61c500c785c91699dca4e4", "commit_訊息": "[報表設計器]Q00-20230526005 調整新增報表選擇模組代號只顯示可在模組維護作業可新增修改的模組 [補修正]", "提交日期": "2023-05-26 17:30:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f7d53ca58829013c109e5e6b2eb14710bf4001e", "commit_訊息": "[報表設計器]Q00-20230526005 調整新增報表選擇模組代號只顯示可在模組維護作業可新增修改的模組", "提交日期": "2023-05-26 17:27:51", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a001e6c34df2265acee03d3410ed51e83e463a59", "commit_訊息": "[WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起", "提交日期": "2023-05-26 16:38:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "18bcfc9fe73074c40820d3ce3c84c047b0753338", "commit_訊息": "[BPM APP]Q00-20220621004 移動端FormUtil.setOption新增第三個參數可選擇移除RadioButton和CheckBox元件第一個空白選項與動態塞值在後續關卡沒顯示選中值的問題", "提交日期": "2023-05-26 15:36:11", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4ae595ead7c340a7127e5469647142a0cfc5bb0a", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-05-26 15:03:26", "作者": "develop_20274", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "bb343920e87550217b3bfcd911ce0dc9aca2be48", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題", "提交日期": "2023-05-26 15:02:51", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e6628755a874c064f5c5a7d02bbf3bfbaffaf3fa", "commit_訊息": "[Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題", "提交日期": "2023-05-26 14:59:32", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e2d85f54fda4f1b79b3c17079fe58bda45dd36ac", "commit_訊息": "[Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間[補修正]", "提交日期": "2023-05-26 10:45:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9ac34e730b5629593c7ba164b3e8be5fe615474e", "commit_訊息": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "提交日期": "2023-05-26 10:36:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a29ea01e6bbfdb253618de1f32985d191caecc1a", "commit_訊息": "[WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]", "提交日期": "2023-05-26 10:10:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b69214018202a5bf4c1bc95bb97f0056489467b0", "commit_訊息": "[組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題", "提交日期": "2023-05-25 19:35:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79af6997639595be2a81f561dc46dc9de13e8172", "commit_訊息": "[BPM APP]Q00-20230525007 修正移動端ListBox元件使用動態塞值如果有多選會在簽核關卡只顯示第一個選中值的問題", "提交日期": "2023-05-25 18:15:54", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "26c5ee35c471514bd5b7ed5a432dc9958f9287f6", "commit_訊息": "[Web]Q00-20230215002 調整RWD表單設計器元件-HorizontalLine元件，樣式一、樣式二的屬性不統一", "提交日期": "2023-05-25 17:18:56", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dff7575955001a4c3b277153fecf7992d1d334f7", "commit_訊息": "[Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間", "提交日期": "2023-05-25 15:33:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e134e6512c7da63e1b40dd9a3f1982e19343a35", "commit_訊息": "[WEB]Q00-20230525004 修正在線閱覽浮水印管理及SQL註冊器的表單多選開窗的資料總筆數與清單數量不一致的異常", "提交日期": "2023-05-25 14:51:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6563f93f1c82bd2264ef08801c10eaa99b25eaa2", "commit_訊息": "[BPM APP]S00-20230509001 優化整合企業微信在不同裝置統一使用微信提供的閱讀器下載附件", "提交日期": "2023-05-25 14:26:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "5bdf1071ee0e610d287d663fb7dc8d43f9ce37e7", "commit_訊息": "[流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序", "提交日期": "2023-05-25 14:03:05", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cc13cc0e3901369d205ca19d26da4cf150c46ac3", "commit_訊息": "[Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題", "提交日期": "2023-05-25 10:16:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65486c3df70453a42849be7514cc840f6bca5655", "commit_訊息": "[流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常", "提交日期": "2023-05-24 17:36:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82e755eafe19ca6e6c0f027298cf0479f733b4e8", "commit_訊息": "[Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加", "提交日期": "2023-05-24 17:01:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f02886a2abd922427d5750eb67ba8c6b94eac329", "commit_訊息": "[WEB]Q00-20230524003 調整簡易流程圖，當進行中的關卡有轉發通知給其他使用者時，簡易流程圖由顯示通知人員為處理者改為不顯示通知人員為處理者", "提交日期": "2023-05-24 16:04:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa97b8ee137df6116cc8be362b0cf52a46f89a7a", "commit_訊息": "[Web]Q00-20230524002 調整當開窗取回的資料為null時會顯示&nbsp;而不是空白問題", "提交日期": "2023-05-24 15:27:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/ResultObjectForDataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}