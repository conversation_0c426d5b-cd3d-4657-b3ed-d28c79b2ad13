# Release Notes - BPM-ISO

## 版本資訊
- **新版本**: release_5.8.10.2
- **舊版本**: release_5.8.10.1
- **生成時間**: 2025-07-18 11:45:29
- **新增 Commit 數量**: 20

## 變更摘要

### lorenchang (7 commits)

- **2024-06-25 15:50:24**: [文件智能家]修正因流程主機位址不是127.0.0.1或localhost導致取得AccessToken失敗，間接導致不會觸發ChatFile接口
  - 變更檔案: 1 個
- **2024-06-20 19:56:54**: [文件智能家]更新表單卡控
  - 變更檔案: 6 個
- **2024-06-14 17:58:31**: [文件智能家]配合表單更新調整js
  - 變更檔案: 1 個
- **2024-06-14 10:27:09**: [文件智能家]調用 NaNaXWeb 關聯文件的單向接口增加傳入 docName(補))
  - 變更檔案: 1 個
- **2024-06-14 10:21:39**: [文件智能家]調用 NaNaXWeb 關聯文件的單向接口增加傳入 docName
  - 變更檔案: 2 個
- **2024-05-24 15:39:38**: [文件智能家]新增模組
  - 變更檔案: 13 個
- **2024-05-22 09:02:50**: [ChatFile]新增取得指定 DocNo 的生效文件接口
  - 變更檔案: 4 個

### 林致帆 (2 commits)

- **2024-06-18 15:59:39**: [ISO]V00-20240618001 修正文管首頁匯出EXCEL按鈕調整為多語系
  - 變更檔案: 1 個
- **2024-04-01 15:44:12**: [ISO]Q00-20240401003 修正ISO文件新增單儲存表單失敗
  - 變更檔案: 1 個

### 邱郁晏 (11 commits)

- **2024-06-12 14:11:42**: [ISO] V00-20240612006 修正文件類別管理查詢類別名稱異常問題
  - 變更檔案: 1 個
- **2024-06-07 17:15:36**: [ISO] C01-20240531005 修正ISO「文件階層」開窗顯示undefined問題(補)
  - 變更檔案: 2 個
- **2024-06-06 16:52:46**: [ISO] C01-20240531005 修正ISO「文件階層」開窗顯示undefined問題
  - 變更檔案: 1 個
- **2024-05-28 15:10:01**: [ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)
  - 變更檔案: 1 個
- **2024-05-28 11:52:40**: [ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)
  - 變更檔案: 1 個
- **2024-05-27 17:28:07**: [ISO] C01-20240520003 修正只有樹狀文管首頁時，ISO開啟文管首頁對應文件的URL會判斷沒有權限
  - 變更檔案: 1 個
- **2024-05-17 09:17:19**: [ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)
  - 變更檔案: 1 個
- **2024-05-16 15:27:33**: [ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件
  - 變更檔案: 4 個
- **2024-04-10 17:14:08**: [ISO] Q00-20240410001 修正取得未部署成功的文件清單時找不到資料，新增防呆(捕修正)
  - 變更檔案: 1 個
- **2024-04-10 16:44:31**: [ISO] Q00-20240410001 修正取得未部署成功的文件清單時找不到資料，新增防呆
  - 變更檔案: 1 個
- **2024-04-08 10:23:09**: [ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題(補)
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [文件智能家]修正因流程主機位址不是127.0.0.1或localhost導致取得AccessToken失敗，間接導致不會觸發ChatFile接口
- **Commit ID**: `d79c650cc586f0bfb1bf366392bca60a5f91c69e`
- **作者**: lorenchang
- **日期**: 2024-06-25 15:50:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`

### 2. [文件智能家]更新表單卡控
- **Commit ID**: `3e928dc38f230b7ce72ea244f6ea622eb72d189c`
- **作者**: lorenchang
- **日期**: 2024-06-20 19:56:54
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOMod.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOUtil.js`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/AccessRightEntityDaoImpl.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/AccessRightEntityController.java`

### 3. [ISO]V00-20240618001 修正文管首頁匯出EXCEL按鈕調整為多語系
- **Commit ID**: `04292476d6331047b49b93bec39070443b699a91`
- **作者**: 林致帆
- **日期**: 2024-06-18 15:59:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`

### 4. [文件智能家]配合表單更新調整js
- **Commit ID**: `45c1a381750fea8a49959b2f9f87e05760bdc557`
- **作者**: lorenchang
- **日期**: 2024-06-14 17:58:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOUtil.js`

### 5. [文件智能家]調用 NaNaXWeb 關聯文件的單向接口增加傳入 docName(補))
- **Commit ID**: `021e10e0606ab1e4eefa3d1d0bc9aba9aa7ae6b5`
- **作者**: lorenchang
- **日期**: 2024-06-14 10:27:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocCanceMgr.java`

### 6. [文件智能家]調用 NaNaXWeb 關聯文件的單向接口增加傳入 docName
- **Commit ID**: `67dc47790c18213777656241fad5c82b65e00f31`
- **作者**: lorenchang
- **日期**: 2024-06-14 10:21:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java`

### 7. [ISO] V00-20240612006 修正文件類別管理查詢類別名稱異常問題
- **Commit ID**: `2ef7ffe5dab88be955aad459cad5e65a9c3fa308`
- **作者**: 邱郁晏
- **日期**: 2024-06-12 14:11:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/DocCategoryDaoImpl.java`

### 8. [文件智能家]新增模組
- **Commit ID**: `030f91c67b8817cb362c1ad027295dfe1657ddd7`
- **作者**: lorenchang
- **日期**: 2024-05-24 15:39:38
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOMod.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOUtil.js`
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOSearchCondictionKey.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/SearchCondiction.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOAjaxController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocCanceMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java`

### 9. [ISO] C01-20240531005 修正ISO「文件階層」開窗顯示undefined問題(補)
- **Commit ID**: `9798d2a9a8addb3d991d531292d69424d9e62108`
- **作者**: 邱郁晏
- **日期**: 2024-06-07 17:15:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISODocLevel.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISODocType.java`

### 10. [ISO] C01-20240531005 修正ISO「文件階層」開窗顯示undefined問題
- **Commit ID**: `01aa968430e10ba56f7406f96442838c3ae39b00`
- **作者**: 邱郁晏
- **日期**: 2024-06-06 16:52:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISODocLevel.java`

### 11. [ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)
- **Commit ID**: `be4a32d8713d10c1873b2e022a696923f5f03fdf`
- **作者**: 邱郁晏
- **日期**: 2024-05-28 15:10:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java`

### 12. [ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)
- **Commit ID**: `18948469b6b40c5bf4fb915bdc462fcfafe393ba`
- **作者**: 邱郁晏
- **日期**: 2024-05-28 11:52:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java`

### 13. [ISO] C01-20240520003 修正只有樹狀文管首頁時，ISO開啟文管首頁對應文件的URL會判斷沒有權限
- **Commit ID**: `7fb03d605ed82f7ac43c2074a2f0d8fc496c5233`
- **作者**: 邱郁晏
- **日期**: 2024-05-27 17:28:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOUtil.js`

### 14. [ChatFile]新增取得指定 DocNo 的生效文件接口
- **Commit ID**: `c90f8698eb2a1398ecac0a3aedb7ffe9483b3b47`
- **作者**: lorenchang
- **日期**: 2024-05-22 09:02:50
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `ISOModule/WebContent/WEB-INF/lib/commons-lang3-3.14.0.jar`
  - 📝 **修改**: `ISOModule/pom.xml`
  - ➕ **新增**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/chatfile/ChatFileKnowledgeController.java`
  - ➕ **新增**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/chatfile/ChatFileKnowledgeService.java`

### 15. [ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件(補)
- **Commit ID**: `52da5fd0b46846e4e595c98d53b14d71f3efaf7e`
- **作者**: 邱郁晏
- **日期**: 2024-05-17 09:17:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java`

### 16. [ISO] C01-20240514003 修正ISO生失效排程沒有檢查需要作廢的文件
- **Commit ID**: `37bd1c97c106bf9ee7b697de07d0b46cfd46b20f`
- **作者**: 邱郁晏
- **日期**: 2024-05-16 15:27:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISODocManagerController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocCanceMgr.java`

### 17. [ISO] Q00-20240410001 修正取得未部署成功的文件清單時找不到資料，新增防呆(捕修正)
- **Commit ID**: `5399c89586441cbb796555b9825436891cbcb66f`
- **作者**: 邱郁晏
- **日期**: 2024-04-10 17:14:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java`

### 18. [ISO] Q00-20240410001 修正取得未部署成功的文件清單時找不到資料，新增防呆
- **Commit ID**: `e1bb187c48d14ead222c220511d18023b4aedce2`
- **作者**: 邱郁晏
- **日期**: 2024-04-10 16:44:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java`

### 19. [ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題(補)
- **Commit ID**: `4db47fff2bb415da99586bcdd020fb248844594f`
- **作者**: 邱郁晏
- **日期**: 2024-04-08 10:23:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/DocCategoryDaoImpl.java`

### 20. [ISO]Q00-20240401003 修正ISO文件新增單儲存表單失敗
- **Commit ID**: `a6809ec9fe2a687b73d1f2bbfefba472f8a41bd6`
- **作者**: 林致帆
- **日期**: 2024-04-01 15:44:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`

