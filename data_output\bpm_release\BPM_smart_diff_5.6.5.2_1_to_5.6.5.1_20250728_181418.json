{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.6.5.2_1", "date": "tag 5.6.5.2_1\nTagger: 張容倫 <<EMAIL>>\n\n2018-01-17 14:30:19", "message": "修正: 有設定下一關為關系人人主管,在解析人員時有誤", "author": "jose<PERSON>"}, "舊分支": {"branch_name": "5.6.5.1", "date": "tag 5.6.5.1\nTagger: 張詠威 <<EMAIL>>\n\n2017/10/25 09:30 last build2017-10-24 18:31:12", "message": "修正 : JSON資料開窗 ,查詢功能輸入數字，會查不到資料的問題", "author": "jose<PERSON>"}, "比較時間": "2025-07-28 18:14:18", "新增commit數量": 329, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "0605a60853a809597b3bd5b6896a59ca7fc65821", "commit_訊息": "修正: 有設定下一關為關系人人主管,在解析人員時有誤", "提交日期": "2018-01-17 14:30:19", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "215e62d32227af8bb811d458ef32c52dbe26ccf0", "commit_訊息": "修正 : gridRowClick 範例的內容", "提交日期": "2018-01-17 10:36:21", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "774615186dd4c7197aa39b14d4758076da3de94c", "commit_訊息": "修正鼎捷移動直連表單異常", "提交日期": "2018-01-16 19:07:00", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "9c57f91abbf7d3fc774bbb74c078a1000f66d725", "commit_訊息": "修正微信列表使用的彈出視窗樣式 修正草稿刪除樣式 修正我的最愛多語系", "提交日期": "2018-01-16 14:43:30", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6f370f310fd99262b2ae10bf66d0da977e1a3bb9", "commit_訊息": "修正微信發請流程列表編輯我的最愛時無反應問題", "提交日期": "2018-01-16 14:24:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "da292bb7d671b30eacd3726f7ec0d99a2f950931", "commit_訊息": "修正鼎捷移動Session already invalidated問題", "提交日期": "2018-01-16 13:15:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AbstractMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d8bfe9b71a01bdf9d4cc2ea78099481a56e955d8", "commit_訊息": "修正終止流程多語系", "提交日期": "2018-01-16 11:40:20", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b61f997b1f47667aafc3e2dc85344d4c8602909f", "commit_訊息": "修正多部門iphone手機下拉選項與彈出視窗跑版", "提交日期": "2018-01-16 11:11:27", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7bb5b8e29a6ec33f25349b17c44a88872f6487fd", "commit_訊息": "[S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱2018/01/16", "提交日期": "2018-01-16 11:07:51", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3da33a32730099efb44bd44d173f2db62aed1b52", "commit_訊息": "修正鼎捷移動發起時有ajax呼叫錯誤問題", "提交日期": "2018-01-16 11:05:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29d49e1bba9e9c95b1c97c20230f077d5ba391a3", "commit_訊息": "C01-20180108002 在ISO文件一覽表的申請狀態，新增全部狀態的選項", "提交日期": "2018-01-16 10:58:02", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dcb1e2fd33dc8c1807ad9f033cd3cdb5bcdff2f2", "commit_訊息": "A00-20180104002 在ISO文件一覽表中新增失效日期的欄位。", "提交日期": "2018-01-16 10:50:09", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b310672f388bd915b2d696bd50cb2bf187befcf3", "commit_訊息": "調整入口平台整合設定", "提交日期": "2018-01-16 10:19:25", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a416d71a21e60a1e93bb263b9f86356591911cf3", "commit_訊息": "修正鼎捷移動登入操作時會有Session already invalidated問題", "提交日期": "2018-01-16 10:14:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "50389fc3e62141f2cfd2b331fdaaeddbb79bde3d", "commit_訊息": "修正鼎捷移動行事曆提醒議題", "提交日期": "2018-01-15 20:25:34", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a79802a0dd27a8f3d8d12516b84205826c497436", "commit_訊息": "修正多語系錯誤", "提交日期": "2018-01-15 19:18:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "fa55484c92ec7c83ee66d01b871278f5133e1af9", "commit_訊息": "調整多部門顯示問題", "提交日期": "2018-01-15 19:15:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a40a7d9cd37f65ab694ed54a0b4f79330b3eabf0", "commit_訊息": "A00-20171003001 2次修正 : 當凍結時,原先已經有排序的欄位 ,也要更新凍結欄位", "提交日期": "2018-01-15 18:06:40", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "029e7fe986e45b1e0632db865c2598e0480971c2", "commit_訊息": "移除Log", "提交日期": "2018-01-15 18:04:02", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9d8d7a89ab9863c89c8c1b03b61a991d2e3cd0c8", "commit_訊息": "修正微信附件上傳後表單值消失問題", "提交日期": "2018-01-15 16:36:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5e9ef4da72c9767c856d7dc894ed9396eb23db54", "commit_訊息": "修正微信追蹤、通知畫面遺漏多語系", "提交日期": "2018-01-15 16:19:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "5220f5c802b15ec13bcd8ef38d4dea4c1a0e785b", "commit_訊息": "修正多語系錯誤", "提交日期": "2018-01-15 15:01:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fe3cf1351ba6361068b2fecb21e90cc1dccbc1d9", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-15 14:48:24", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "2153b2eb232291d0d745b0bc7b80b4000732ca74", "commit_訊息": "修正 : 有設定流程部門,在解析下一關時，派送的關卡人員有誤", "提交日期": "2018-01-15 14:47:59", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "238e34addab68e80eb3a716bd104bcf59b12d318", "commit_訊息": "修正微信簽核畫面遺漏多語系", "提交日期": "2018-01-15 14:29:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fbf4db922def7c48cc6ba3dfca56b38df867d338", "commit_訊息": "2次修正:從左邊Menu的取回工作重辦，進行取回重辦會報錯的問題", "提交日期": "2018-01-15 13:54:35", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52ab9810df37be9a4e81255d7d1eb8daeb277dd0", "commit_訊息": "調整Ajax DatabaseAccessor 範例內容", "提交日期": "2018-01-15 13:51:06", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d2535afdfb5ffafdc67725879edcb394514d004a", "commit_訊息": "修正鼎捷移動表單多語系異常", "提交日期": "2018-01-12 20:38:22", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9648302ff67553cf047f85ef54282e373c8aaa78", "commit_訊息": "修正鼎捷平台二期發起流程異常", "提交日期": "2018-01-12 17:34:20", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ac462acf75e59319357528279f7c05f4d249fb2", "commit_訊息": "修正鼎捷移動會出現session invaild問題與刪除沒用到的import", "提交日期": "2018-01-12 16:08:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "9adcda95fafd19206536b52dccd30ba78d29d65a", "commit_訊息": "修正:從左邊Menu的取回工作重辦，進行取回重辦會報錯的問題", "提交日期": "2018-01-12 16:05:11", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7c384445463e4757f9fb60fd8ef057901e957a1", "commit_訊息": "修正鼎捷平台二期發起流程異常", "提交日期": "2018-01-12 15:19:37", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a25e9cdaf5f1d5b3bc10ba1b4115ce7e96aa93f", "commit_訊息": "修正打版後測試微信遇到的問題", "提交日期": "2018-01-12 09:38:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "10e66ab6717e89653cf328279b317686c0f7d8f5", "commit_訊息": "修正打版後微信測試到的問題", "提交日期": "2018-01-11 21:08:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "312cd228b51ab61e03f79a9eb28568e9f26381ef", "commit_訊息": "調整行動版表單編輯器可標記附件元件為中間層", "提交日期": "2018-01-11 20:04:02", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "02ceacfd69f4de5e3c79b5e70a2ac7a3b0ec66ab", "commit_訊息": "[S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱2018/01/11", "提交日期": "2018-01-11 18:53:26", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c971a80058a97dc3b5561b5d17398ed45276cb93", "commit_訊息": "修正ESS取ManagerURLPatten問題", "提交日期": "2018-01-11 18:23:02", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2701fe83b148c0e2b90efc154fc0c2f973f045c5", "commit_訊息": "修正打版後測試微信的發現的問題", "提交日期": "2018-01-11 18:15:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListContact.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileTool.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenu.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenuExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 22}, {"commit_hash": "753a44f2fe4073ad6c881486e35eeab211e4149f", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-11 18:07:31", "作者": "施廷緯", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "46ed319bb47bc0067884fffd66fd0d71884a996e", "commit_訊息": "S00-20171218001修改多語系及調整administrator在ISO文件總管閱讀文件的權限問題。", "提交日期": "2018-01-11 18:01:47", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0f9d4f9478267acb79c91a062a84e1746dde4456", "commit_訊息": "行動簽核加簽的活動關卡名稱帶入預設值", "提交日期": "2018-01-11 17:13:07", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2ed4a3ec768e579ecbf5a1f09f1697781be5d38c", "commit_訊息": "C01-*********** 修正離職人員維護作業緩慢", "提交日期": "2018-01-11 15:38:32", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ResignedEmployeesManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "85f4e6c5803c5a109eed917371fb332d7ec1fa58", "commit_訊息": "修正BPMAPP元件多欄位樣式", "提交日期": "2018-01-11 14:59:02", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b03dd17e474100e1d63e9496a826e02ef322d435", "commit_訊息": "更換鼎捷移動直連表單網址", "提交日期": "2018-01-11 14:24:02", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f32b693365db414c7a2ba0761f16dd36a43cbbf8", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-11 14:18:21", "作者": "治傑", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "efe1cc8a34b162be91d85d286a29add3b3d9f0a4", "commit_訊息": "修正鼎捷平台無法取得授權", "提交日期": "2018-01-11 14:16:41", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1fe1a6d61d4a6bc31e66f77858f8c7b42cb00556", "commit_訊息": "將手持裝置使用的資料選取器改用openwin方式操作，不使用iframe方式嵌入", "提交日期": "2018-01-11 14:12:12", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "47414ed5dd7d60c2c38d92bd0b29238e92394132", "commit_訊息": "修正相對位置表單設計器問題", "提交日期": "2018-01-11 11:31:05", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c59bfe7d20b62776cfee0c89d8b9530955e70ca9", "commit_訊息": "Q00-*********** 修正 :掛雙表單的流程 ,A表單掛附件 , B表單沒有掛附件 , 但發起時用 B表單發起 會將附件刪除", "提交日期": "2018-01-11 10:36:53", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "41584c01e2da4e60bc4f9f9151a866d4fad6199d", "commit_訊息": "調整NaNaWebLog部份INFO項目為WARN", "提交日期": "2018-01-09 08:47:38", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/conf/NaNaWebLog.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f098d5e856d16933a2055a0c6a3e9710959ead56", "commit_訊息": "Hibernate SQL Log預設更改為false", "提交日期": "2018-01-09 08:44:28", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/hibernate/session-factory.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "47dded06d9f2a89c143e7a6f3d8ff529c3e63d91", "commit_訊息": "修正微信被動響應消息改為新UI連結", "提交日期": "2018-01-08 19:24:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "8fec00467f2f56a00bad9170f141233b5d8d5523", "commit_訊息": "[S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱", "提交日期": "2018-01-08 19:15:44", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6f5e44b55f968abf11a5d03f355dcad7ee8f4f56", "commit_訊息": "移除log", "提交日期": "2018-01-08 18:10:51", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5bb8e0506e1e214dbc261c6cf5eee94bbf26147d", "commit_訊息": "S00-20170930002 新增：簽核流程設計師流程關係部門的設定", "提交日期": "2018-01-08 18:07:47", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "0f9a22310b31afb3a7cff537dd2b11747f091967", "commit_訊息": "A00-20180102001 修正 :可以取回重辦狀態為暫停的Invoke關卡", "提交日期": "2018-01-08 17:58:51", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e5c452bbe98d610513aa846b34ad6b0535b01131", "commit_訊息": "修正企業微信新UI表單畫面部分錯誤", "提交日期": "2018-01-08 17:49:42", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenu.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "2fe5ec08eabbaec7857c9978617407ce2c2ee54c", "commit_訊息": "新增鼎捷移動直連表單", "提交日期": "2018-01-08 15:00:45", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 10}, {"commit_hash": "ca8b7f943fd6d7d2cbea86b7001d2c354082b9ce", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-08 14:44:34", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "cb1bc95cb6e04f0f3be1f86dc0e7c2e7476ea74d", "commit_訊息": "調整RESTful API說明文字", "提交日期": "2018-01-08 14:42:57", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e27b7312c40530e85f7bdc47a60938e1d6806675", "commit_訊息": "新增鼎捷移動直連發起表單，調整待辦表單提醒UI", "提交日期": "2018-01-08 13:51:58", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "1a44bdb220580061ca4f96fb3a498c0b3140d0df", "commit_訊息": "S00-20171218001修改ISO調閱功能，根據權限屬性來決定登入者有無權限看到檔案資料", "提交日期": "2018-01-08 11:56:36", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "798b3796564821b7e7e96ae39d6c4469ff0a62da", "commit_訊息": "S00-20171218001修改ISO調閱功能，根據權限屬性來決定登入者有無權限看到檔案資料", "提交日期": "2018-01-08 11:49:18", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d8bd9fb0becfd2012e9ff219160a0bbdc1c10fa8", "commit_訊息": "修正微信推播網址連結改連至新UI", "提交日期": "2018-01-08 11:30:14", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc2590dd8d0f02d744ab58bfac7a4b76649fb50d", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-08 10:55:27", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "26012ea84fa378521c71c8869c65fd445b151d4d", "commit_訊息": "新增鼎捷移動直連待辦表單", "提交日期": "2018-01-08 10:54:43", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "ac18f7e93127a506b42d94c3a2bfa206b72852b2", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-08 10:34:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 7}, {"commit_hash": "ae414d67269e9422fd7ca4fbde0270a111d2c120", "commit_訊息": "修正微信推播消息網址", "提交日期": "2018-01-08 10:15:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37a5acb62634aeb0075b939e4d09ac7fa156c0d9", "commit_訊息": "修正鼎捷待辦列表過濾名稱total_cnt為0的錯誤", "提交日期": "2018-01-08 09:57:38", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ca9fd6a2e5fda98c91663030833a39712206c20e", "commit_訊息": "新增鼎捷移動直連表單新畫面", "提交日期": "2018-01-05 17:46:28", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "1a9e2eedd51f2349aacc0841f99afb59a1e74fa0", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-05 17:08:23", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "7b3053f7de645dc7423e47606102a94f0a7f1646", "commit_訊息": "新增鼎捷移動直連表單新樣式", "提交日期": "2018-01-05 17:04:54", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormNoticeLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "74ac60871bec591c6bc1ae7171109c71f601ec07", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-05 16:53:25", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "84d35ba5c8722f497b0543b8c8783080245a957e", "commit_訊息": "新增鼎捷移動新樣式Action", "提交日期": "2018-01-05 16:52:48", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7dd078eb709946923bef520954f01c5e610b88bd", "commit_訊息": "S00-20180103001  派送永久代理人時，若發現代理人離職，發信通知系統管理員", "提交日期": "2018-01-05 16:47:22", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b5abd02e849ac4d4e3692cd925fb21835802722", "commit_訊息": "二次修正Lib異常，補上漏掉的action", "提交日期": "2018-01-05 14:40:31", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4cc621fe1044085ebadc618d54a6db2d7f588af8", "commit_訊息": "修正Lib異常", "提交日期": "2018-01-05 14:36:32", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a759b6203a922e6cdce9ff727f8c73f05e899e85", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-05 14:08:12", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "74bf9d553617da97546cd3cd5b3913c6082c5237", "commit_訊息": "更正Action名稱", "提交日期": "2018-01-05 13:55:09", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "04b55e244e5ee2b7d09cd6d5fe2386952543d153", "commit_訊息": "新增鼎捷移動新樣式表單", "提交日期": "2018-01-05 13:54:27", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormTodoLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleFormTodo.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "6ba8c9d1a09e95756fc796aca7d4ecddd4147dd1", "commit_訊息": "新增BPM二期服務API接口 新增表單附件操作API接口", "提交日期": "2018-01-05 13:44:58", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/FormV2.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "579fbaa132082628f2617a4d01057c46b52c04da", "commit_訊息": "A00-20171122001]調整webService的fetchToDoWorkItem的SQL語法", "提交日期": "2018-01-05 13:40:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "816d6fe0ff24bcc2a7868e2b7fa8bd35f48af35c", "commit_訊息": "修正Manager大量使用Web Session問題", "提交日期": "2018-01-05 11:29:40", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleDataChooseTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 17}, {"commit_hash": "c225834de013519e3f3a0f92f1f7118258ebe1ac", "commit_訊息": "S00-20170628001 新增 :ajax查詢更新接口且優化查詢方式", "提交日期": "2018-01-04 15:49:37", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0f79924767d896cd8f7d7817fffb635d105ba211", "commit_訊息": "C01-20180103004 修補表單序號重複議題", "提交日期": "2018-01-04 14:35:50", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SNGenerator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb91a9dbb2fe81d5b8d8ef7522e43b1d61195bae", "commit_訊息": "A00-20171227001 修正微信推播消息內容不完整問題", "提交日期": "2018-01-03 18:38:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e193fe32cbd5ab2d158fd858f64b47eaccd88cc", "commit_訊息": "C01-20171103005 回復之前版本", "提交日期": "2018-01-03 17:35:45", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ec42bb222a216300ca1c6bcfda733624a0d8c1be", "commit_訊息": "[A00-20171114001]修改Grid使用時，不執行moveCursorToEnd動作", "提交日期": "2018-01-03 16:49:22", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "57685af28aad522477f04fdf43cde0e21eec4462", "commit_訊息": "A00-20171206001 上傳附件限制類型，當上傳失敗後畫面顯示異常", "提交日期": "2018-01-02 17:46:31", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c84b8ad83d5116ec0ef8004caae2421815c9c7c", "commit_訊息": "A00-20171003001 修正:簽核流程設計師設定欄位中，凍結欄位後不會排序的問題", "提交日期": "2018-01-02 17:40:07", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7a8af01961d4e4020342ddc64736bc8456794062", "commit_訊息": "修改日期取月份的值。", "提交日期": "2018-01-02 15:26:53", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/statistics/StatisticianBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "db39dcbed2f405769b2bac829d15f27b2cd46416", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2018-01-02 14:15:02", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "783f452c7704eeb7677b361db8ee1cf373a6f3b8", "commit_訊息": "企業微信簽核效能改進", "提交日期": "2018-01-02 14:13:15", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BPMProcessTracing.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppCommon.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListContact.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppMenu.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmTaskManage.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkItem.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkItemShell.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkPublic.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileCustomOpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileGrid.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileLibrary.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileProductOpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTool.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/aw.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/cn.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/de.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/es.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/fr.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/it.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/nl.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/pt.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/ru.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lib/aw.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_tabs.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/aw.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/bg1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/bg2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g3.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/tabs.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/aw.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/checkbox1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/checkbox2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/radio1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/radio2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/aw.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-tabs.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-tabs.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-bg1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-bg2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g3.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-tabs.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aw.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-checkbox1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-checkbox2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-radio1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-radio2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g3.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g4.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tabs1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tabs2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-tabs.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/aw.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g3.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g4.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tabs1.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tabs2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_tabs.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/aw.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/checkbox.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/combo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/grid.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/icons.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/radio.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/tabs.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/tree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/AppModalDialog.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/Dialog.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ModalDialog.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/OpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/aw.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ds-grid-aw.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ds.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/dsMobile.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/form/popup.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/Map.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/MobileAppGrid.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/MobileProductOpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/ajax-loader.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/action-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/action-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/alert-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/alert-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-l-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-l-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-r-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-r-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-l-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-l-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-r-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-r-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-l-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-l-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-r-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-r-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/audio-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/audio-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/back-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/back-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bars-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bars-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bullets-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bullets-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/calendar-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/calendar-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/camera-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/camera-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-d-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-d-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-l-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-l-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-r-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-r-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-u-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-u-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/check-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/check-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/clock-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/clock-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/cloud-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/cloud-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/comment-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/comment-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/delete-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/delete-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/edit-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/edit-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/eye-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/eye-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forbidden-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forbidden-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forward-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forward-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/gear-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/gear-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/grid-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/grid-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/heart-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/heart-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/home-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/home-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/info-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/info-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/location-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/location-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/lock-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/lock-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/mail-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/mail-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/minus-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/minus-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/navigation-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/navigation-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/phone-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/phone-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/plus-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/plus-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/power-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/power-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/recycle-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/recycle-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/refresh-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/refresh-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/search-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/search-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/shop-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/shop-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/star-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/star-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/tag-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/tag-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/user-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/user-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/video-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/video-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/action-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/action-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/alert-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/alert-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-l-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-l-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-r-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-r-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-l-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-l-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-r-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-r-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-l-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-l-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-r-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-r-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/audio-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/audio-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/back-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/back-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bars-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bars-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bullets-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bullets-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/calendar-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/calendar-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/camera-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/camera-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-d-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-d-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-l-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-l-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-r-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-r-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-u-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-u-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/check-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/check-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/clock-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/clock-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/cloud-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/cloud-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/comment-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/comment-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/delete-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/delete-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/edit-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/edit-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/eye-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/eye-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forbidden-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forbidden-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forward-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forward-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/gear-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/gear-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/grid-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/grid-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/heart-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/heart-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/home-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/home-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/info-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/info-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/location-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/location-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/lock-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/lock-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/mail-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/mail-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/minus-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/minus-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/navigation-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/navigation-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/phone-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/phone-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/plus-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/plus-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/power-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/power-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/recycle-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/recycle-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/refresh-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/refresh-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/search-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/search-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/shop-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/shop-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/star-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/star-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/tag-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/tag-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/user-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/user-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/video-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/video-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.map", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.structure.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.structure.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.theme.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.theme.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.external-png-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.external-png-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.icons-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.icons-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-png-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-png-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-svg-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-svg-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.structure-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.structure-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.theme-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.theme-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jquery-1.8.3.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jweixin-1.0.0.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/snap.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobilePublic.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileTool.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/knockout-3.2.0.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/knockout.mapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/utab.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/ListToDo.cc", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 434}, {"commit_hash": "b369a6d897a94408724dea34346771217b312926", "commit_訊息": "修正BPM文件APP的JS範例增加版號與停用版號 修正BPMAPP多欄位時元件畫面跑版問題", "提交日期": "2018-01-02 11:48:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "044d5312f1df969ef3c5ccdbb1652a92a28417af", "commit_訊息": "調整RESTful說明文件功能", "提交日期": "2017-12-29 18:55:53", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/Reflection/reflections-0.9.10.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/config/JSONDocInteragtion.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "57b17c1ee6201fdb41850a9733ab0fa1fb191b05", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-29 18:53:49", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "e1ec3bf4c8f855b348ad2b2eddffb02206261055", "commit_訊息": "A00-20171114002 修正時間元件開啟後離按鈕元件太遠問題", "提交日期": "2017-12-29 17:10:34", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/popup.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a85d88c912a5694df8908657c9ceec9d5f96827e", "commit_訊息": "新增RESTful文件說明套件", "提交日期": "2017-12-29 14:23:33", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/GoogleGuava/guava-20.0.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-core-1.1.16.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-springmvc-1.1.16.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-ui-1.1.16.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-ui-webjar-1.1.16.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/Slf4J/slf4j-api-1.7.25.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-aop-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-aspects-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-beans-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-context-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-context-support-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-core-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-expression-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-instrument-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-instrument-tomcat-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-jdbc-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-jms-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-messaging-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-orm-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-oxm-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-test-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-tx-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-web-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-webmvc-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-webmvc-portlet-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/springframework/spring-websocket-4.3.7.RELEASE.jar", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/config/JSONDocInteragtion.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 32}, {"commit_hash": "d4ba4c8ed845ef13e88137b9c4a016c852a31d50", "commit_訊息": "C01-20171103005 取消TT流程回傳簽核結果失敗時，流程仍會結案議題", "提交日期": "2017-12-29 11:35:43", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "131e0548f6b6c86fa1ef1fd0939d6c8c050eb27b", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-28 17:40:18", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "783c1135f6bf53d4b4883e3b8180521a11bb71a9", "commit_訊息": "[A00-20170918001]修改Object轉為String的型態。", "提交日期": "2017-12-28 17:39:11", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/util/DBMessageResources.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b523bc4fb98271e35616fcbb25c465c640360a23", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-28 15:51:44", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "8376ec4931cbf936de6997ee25ca042f9eadac77", "commit_訊息": "C01-*********** 修正SSO登入當user已離職時，不允許登入", "提交日期": "2017-12-28 15:51:22", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ab2e35b8da20b0dac794caaa797000bd052c206d", "commit_訊息": "Q00-*********** 修改系統多語系未開放問題", "提交日期": "2017-12-28 10:30:29", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/Next_updateSQL_Oracle.sql", "修改狀態": "重新命名", "狀態代碼": "R089"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/Next_updateSQL_SQLServer.sql", "修改狀態": "重新命名", "狀態代碼": "R095"}], "變更檔案數量": 3}, {"commit_hash": "9bdb8ed5acb3a08c3753859176542c617bcea273", "commit_訊息": "[A00-20171027001]修改表單的開窗元件「Dialog Input Multi」", "提交日期": "2017-12-27 16:27:26", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7faafc1d0ff85a36536426c68c9df7befb827819", "commit_訊息": "[A00-20171027001]修改表單的開窗元件「Dialog Input Multi」", "提交日期": "2017-12-27 16:27:26", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "da93fa2460bf781abb8ff175a445ff659969e85d", "commit_訊息": "新增 OJB 的LockType", "提交日期": "2017-12-27 15:32:05", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/persistence/src/com/dsc/nana/persistence/LockType.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb16499c0ee2e0afb219d00f7d807b55a261bcd9", "commit_訊息": "調整文管ISOManager判斷方法", "提交日期": "2017-12-27 15:30:40", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4080b71b7c8dd1fe55d11fab74c67dd32683b0f1", "commit_訊息": "C01-20171214001 代理人取回重辦後，關卡解析處理者異常", "提交日期": "2017-12-27 15:16:04", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d978e463289a277bc4455c638839f74eb94f56fe", "commit_訊息": "A00-20171122003 漏簽入一支程式", "提交日期": "2017-12-27 14:36:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4b439571c716b476005d38aad39e0d091d3cf211", "commit_訊息": "A00-20171122003 修正微信推送的流程結案連結，會導到未結案的列表中，應導到已結案的列表", "提交日期": "2017-12-27 14:12:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "272598272f28d0c2549b08963030c2c28d920deb", "commit_訊息": "A00-20170919001 修正 :組織設計師列印組織圖時,應載入全部人員", "提交日期": "2017-12-27 09:57:59", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgTreeNode.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f3ac3deb1f0d68e144c37fbe6da61d2d6f8fd9af", "commit_訊息": "[C01-20171222002]修正行動簽核上傳附件後會清空表單的問題", "提交日期": "2017-12-26 18:08:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a2b453353b112a9cbe12c4bd0e028efc5a4dbd17", "commit_訊息": "當BPM App尚未註冊時,則鼎捷移動不能正確調用BPM App的相關服務", "提交日期": "2017-12-26 14:56:15", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinWhaleServiceAuthenticate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "8f203679a3d46a8babdec6fdbcbb41fc030469d1", "commit_訊息": "[A00-20171026002]總筆數取不到時,從session另外取待辦清單出來", "提交日期": "2017-12-22 19:36:20", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b3b51424ecd19ca1b070353740c2da77667a3d8", "commit_訊息": "更新bpm文件增加適用版本", "提交日期": "2017-12-22 17:59:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b9882eabcc1cbcffaad19b76c76149da88c3b27", "commit_訊息": "修正草稿進入畫面的restful無表單資料的問題", "提交日期": "2017-12-22 11:29:51", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/BuildFormBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7233693ccc0c548421a80d6eea5e8cb701afb38d", "commit_訊息": "A00-20171122002 修正BPMAPP加簽的搜尋人員，將搜尋條件預設人員姓名，搜尋方式預設包含", "提交日期": "2017-12-21 18:34:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a9cd79c74af499eba2c340e983bbc71dfa7ebcf1", "commit_訊息": "修正 鼎捷移動平台部屬工具頁面配置", "提交日期": "2017-12-21 16:21:17", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "cb818ab7c145b4a9456eae83b7852ee5e9d58605", "commit_訊息": "A00-20171206003 修改匯入ESS表單後WEB表單會出現行動版頁簽的問題", "提交日期": "2017-12-21 15:51:29", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ef1ae47a6e558a0b0d8c6a09a66e3b0c9f786a04", "commit_訊息": "修正發起流程restful在選擇多部門時錯誤問題", "提交日期": "2017-12-21 10:27:07", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82d50c088695a028a03599cc2064550d096efe8e", "commit_訊息": "修正通知表單restful簽核歷程沒有顯示問題", "提交日期": "2017-12-20 18:18:24", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "755b236e2f3f0422f832d0657a8cec115248a2d5", "commit_訊息": "修正待辦表單restful的流程主旨消失問題", "提交日期": "2017-12-20 17:28:39", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d471c2567a76f486246f330bb1640c9a64cf9bd4", "commit_訊息": "修正相對位置表單必填驗證訊息顯示樣式", "提交日期": "2017-12-20 14:51:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b66d5cc2f5ff48bdb9a9d02b8f23798aa74ba10", "commit_訊息": "修正相對位置表單若checkbox或radio元件設置必填，驗證會失效問題", "提交日期": "2017-12-19 18:55:58", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7c7aa066db563e28e8af731ef768b459d750e27", "commit_訊息": "修正多語系總表重覆部分", "提交日期": "2017-12-19 14:31:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "699160a843f7159a9676162a724c311e8d603953", "commit_訊息": "新增企業微信效能改進計畫調整", "提交日期": "2017-12-19 11:14:31", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/cn.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/de.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/es.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/fr.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/it.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/nl.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/pt.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/ru.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lib/aw.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_tabs.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/aw.css", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/bg1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/bg2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g3.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/tabs.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/aw.css", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/checkbox1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/checkbox2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/radio1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/radio2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/aw.css", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-tabs.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-tabs.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-bg1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-bg2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g3.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-tabs.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aw.css", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-checkbox1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-checkbox2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-radio1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-radio2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g3.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g4.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tabs1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tabs2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-tabs.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/aw.css", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g3.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g4.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tabs1.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tabs2.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_tabs.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/aw.css", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/button.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/checkbox.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/combo.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/grid.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/icons.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/radio.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/tabs.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/tree.png", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/AppModalDialog.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/Dialog.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ModalDialog.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/OpenWin.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/aw.min.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ds-grid-aw.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/ds.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/dsMobile.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/Form/popup.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 162}, {"commit_hash": "ae0015aa8afe282480af594d61c1cd669908223a", "commit_訊息": "修正BPMAPP的GRID編輯畫面，下拉元件帶回應為顯示值問題", "提交日期": "2017-12-18 17:33:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileAppGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d12ace19e8f2d8eb55b2405c685342473fceaf7c", "commit_訊息": "A00-20171124001 修改使用者個人資訊的基本資料，選項「簽核完畢後執行」沒有多語系功能", "提交日期": "2017-12-18 11:59:18", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d1630510e48e6d3b700bb2226565ab4c1373ce4f", "commit_訊息": "修正錯誤的部份", "提交日期": "2017-12-18 10:37:29", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71a106750838bdfdfa50b11c538e091b92fdd897", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-18 10:29:00", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "613764bf2716fdfc7cf95376357669ad8dad0e02", "commit_訊息": "補上草稿發起表單,待辦直接派送後回列表", "提交日期": "2017-12-18 10:25:50", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0bfd9fc94127d5ef9ee3d055aa1435826ab08643", "commit_訊息": "修正表單無法開啟問題", "提交日期": "2017-12-18 10:24:36", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "1648b29404d45aed0e39a0b75ce28f1e1de05a06", "commit_訊息": "新增工作首頁畫面", "提交日期": "2017-12-18 10:01:22", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1ad96d81b2e86e920819e54259be3212e1079bd6", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-18 09:48:37", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "7bc595638bc7991310a4ab468cabf171383a20b9", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-18 09:15:55", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/system/BpmMobileLibrary.js", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 8}, {"commit_hash": "8e07077390fa127ba987de0729f5d095170da80f", "commit_訊息": "調整彈出視窗、元件樣式", "提交日期": "2017-12-18 09:07:35", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "b644a264ff82e8374bc0e1720d5c5dd0fa21bd63", "commit_訊息": "修正微信认证问题", "提交日期": "2017-12-15 19:34:25", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobileLibrary.js", "修改狀態": "重新命名", "狀態代碼": "R095"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobilePublic.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/knockout-3.2.0.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/knockout.mapping.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/utab.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/ListToDo.cc", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 22}, {"commit_hash": "d7e09808cc14cb042e7bd15ffd6cb135cf6d7ada", "commit_訊息": "調整行動表單多語系機制，調整路徑", "提交日期": "2017-12-15 19:22:18", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/aw.min.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobileLibrary.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobilePublic.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileCustomOpenWin.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileGrid.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileLibrary.js", "修改狀態": "重新命名", "狀態代碼": "R094"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileProductOpenWin.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileTool.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/knockout-3.2.0.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/knockout.mapping.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/utab.js", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 18}, {"commit_hash": "3a32300cea73017021b62f695d7d9817b03c3fdc", "commit_訊息": "調整追蹤流程清單，補上跳轉頁面的箭頭", "提交日期": "2017-12-15 16:56:04", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTracePerformed.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "c3dad5da557bad513cd05f2a5b24cb85129166bd", "commit_訊息": "新增博眾輕量化通知表單,UI設計", "提交日期": "2017-12-15 16:37:55", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "792e57c1760cd98a3db1b44c407f3b9b8ffe451c", "commit_訊息": "新增Grid一鍵展開/收合功能", "提交日期": "2017-12-15 15:29:44", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "dae1fc988279e1cfbafa63d25af59e498a76dde8", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-15 14:47:22", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 3}, {"commit_hash": "a8ce095a3750d81097dab5e364659e22002faf64", "commit_訊息": "新增工作首頁 調整推播機制", "提交日期": "2017-12-15 14:40:36", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthClientUserDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "b58cc3ea50f140d1271d2d0aff66ae4a862928bc", "commit_訊息": "新增博眾輕量化表單", "提交日期": "2017-12-15 12:09:53", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppCustomDataChooserTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTraceInvoked.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTracePerformed.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileCustomOpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileGrid.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileLibrary.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileNotice.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileProductOpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileTool.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/aw.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 30}, {"commit_hash": "e9846572cc5973ebc783ed7c9a6ec993758d9491", "commit_訊息": "新增輕量化微信簽核、通知、聯絡人、追蹤頁面 調整微信認證機制", "提交日期": "2017-12-14 19:07:37", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListContact.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListNotice.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListToDo.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/ajax-loader.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/action-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/action-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/alert-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/alert-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-l-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-l-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-r-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-r-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-l-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-l-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-r-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-r-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-l-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-l-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-r-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-r-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/audio-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/audio-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/back-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/back-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bars-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bars-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bullets-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bullets-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/calendar-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/calendar-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/camera-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/camera-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-d-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-d-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-l-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-l-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-r-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-r-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-u-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-u-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/check-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/check-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/clock-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/clock-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/cloud-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/cloud-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/comment-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/comment-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/delete-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/delete-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/edit-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/edit-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/eye-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/eye-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forbidden-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forbidden-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forward-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forward-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/gear-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/gear-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/grid-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/grid-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/heart-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/heart-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/home-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/home-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/info-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/info-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/location-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/location-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/lock-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/lock-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/mail-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/mail-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/minus-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/minus-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/navigation-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/navigation-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/phone-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/phone-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/plus-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/plus-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/power-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/power-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/recycle-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/recycle-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/refresh-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/refresh-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/search-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/search-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/shop-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/shop-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/star-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/star-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/tag-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/tag-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/user-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/user-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/video-black.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/video-white.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/action-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/action-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/alert-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/alert-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-l-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-l-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-r-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-r-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-l-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-l-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-r-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-r-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-l-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-l-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-r-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-r-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/audio-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/audio-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/back-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/back-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bars-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bars-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bullets-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bullets-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/calendar-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/calendar-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/camera-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/camera-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-d-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-d-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-l-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-l-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-r-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-r-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-u-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-u-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/check-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/check-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/clock-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/clock-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/cloud-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/cloud-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/comment-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/comment-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/delete-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/delete-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/edit-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/edit-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/eye-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/eye-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forbidden-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forbidden-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forward-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forward-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/gear-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/gear-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/grid-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/grid-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/heart-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/heart-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/home-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/home-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/info-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/info-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/location-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/location-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/lock-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/lock-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/mail-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/mail-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/minus-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/minus-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/navigation-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/navigation-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/phone-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/phone-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/plus-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/plus-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/power-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/power-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/recycle-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/recycle-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/refresh-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/refresh-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/search-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/search-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/shop-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/shop-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/star-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/star-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/tag-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/tag-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/user-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/user-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/video-black.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/video-white.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.map", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.structure.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.structure.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.theme.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.theme.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.external-png-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.external-png-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.icons-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.icons-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-png-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-png-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-svg-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-svg-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.structure-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.structure-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.theme-1.4.5.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.theme-1.4.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/plugin/jquery-1.8.3.min.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 238}, {"commit_hash": "4236014d2cc3d3fabc0d891aaec032e0891847fb", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-14 18:51:46", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "55fc34f063805f564e67b5d568653e36e77f544c", "commit_訊息": "[A00-20171026001] 修改ISO文件查詢,簡易查詢where的SQL指令", "提交日期": "2017-12-14 18:26:22", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fadb4b5bbe7005d6910db4ae8dc9b1b4db279e36", "commit_訊息": "修正Oracle資料庫在取得聯絡人資訊時大小寫問題", "提交日期": "2017-12-13 19:30:37", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.2_updateSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "d2f5f12060d1d6f2ef7286d09b41f1535824e8d4", "commit_訊息": "A00-20171129001 修改使用者基本資料的編輯按鈕無法正常使用，及Oracle員工工號問題", "提交日期": "2017-12-13 17:11:27", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ff99d91ea7052cc0942f60d0dc09b1d098d62ddc", "commit_訊息": "增加T100整合測試主機連接多語系", "提交日期": "2017-12-13 10:00:31", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ab85a48d8ba7db8e634558bc8cb5982a0f859915", "commit_訊息": "C01-20171122007 退回重辦出現error，原因為多次點擊(調整退回重瓣按鈕顯示狀態)", "提交日期": "2017-12-12 16:16:42", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d685b14b207d8c6ee569ee8c23df22f30c80a8ae", "commit_訊息": "C01-20171205004-修正mcloud下載附件需顯示原始檔名", "提交日期": "2017-12-11 18:49:28", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d64e3ce7f06e89b0f9aae8428e7f38bc9d80e773", "commit_訊息": "[A00-***********]搖旗吶喊小助手新增userId+LDAP密碼登入", "提交日期": "2017-12-11 18:47:14", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/notifier/NotifierServlet.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "2c8c166c91aff35a954ec41723c31e729bc16b05", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-11 16:38:38", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "9c076a42e01d5d942e2dd73634a0035a82083b5c", "commit_訊息": "修正C01-***********衍生問題_附件關閉時找不到formType 修正通知RESTFul讀取後未增加viewTimes的bug", "提交日期": "2017-12-11 11:07:55", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "083704fdc7d62c4d93bcbb8924a36967544548e5", "commit_訊息": "修正鼎捷移動統計組件在編輯時會蓋掉原有顏色問題", "提交日期": "2017-12-11 10:02:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c3c1001b05ad61c7bea6dea4b9845a147eaf518", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-08 18:31:02", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "bec5db1f16489147f97652f23cb1964710abb0e7", "commit_訊息": "C01-20171115001_2 2次修正:於流程進行中將附件刪除後，待辦清單附件圖示仍然存在", "提交日期": "2017-12-08 18:30:18", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f7558a9dccd879bd9c47fde825428d3ed89a7df", "commit_訊息": "修正取得表單元件的javabean名稱錯誤", "提交日期": "2017-12-08 18:27:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ElementBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "aa5db5a504b5fbb0cd6f03049e2b92b191f01abd", "commit_訊息": "修正加簽restFul服務BUG", "提交日期": "2017-12-08 11:09:16", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0eb4cd92254f982bfb95034c957a1df3634f0bb4", "commit_訊息": "修正  依指定序號取得待辦 : 移除測試功能   新增多語系總表", "提交日期": "2017-12-08 10:06:38", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1418428685a14daa79c9aea674ccebe6f6cea327", "commit_訊息": "C01-20171115001 修正:於流程進行中將附件刪除後，待辦清單附件圖示仍然存在", "提交日期": "2017-12-07 17:47:00", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1cb2849e58f365ae250d7f9b7f451bf4e3afd614", "commit_訊息": "C01-20171205001 workflow結案未清除cache導致發單異常", "提交日期": "2017-12-07 16:20:48", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c2a35f8fb060917340dfa5ede34efc4d0a7d627", "commit_訊息": "修正 待辦流程依指定取得 測試用的序號  中間層取得附件 主機URL取得方式", "提交日期": "2017-12-07 14:28:55", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "68647a638fcc3f3623fde05b312d3bb431cd54df", "commit_訊息": "新增 取得系統中所有的組織資訊 RESTFul服務 修正取processSerialNumber時會是null 修正追蹤取詳細資料時漏寫FormCommentTypeValue資訊", "提交日期": "2017-12-07 14:22:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "d1ec8021b9582d393ba8fdd4af181f45710da28a", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-07 14:22:10", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7d88fb5182c80cc0795d2ed8d63c085fb65b1ad3", "commit_訊息": "新增微信認證架構", "提交日期": "2017-12-07 14:16:37", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f7483d1ac66264c08e2088b331ff72b3daea93ec", "commit_訊息": "調整中間層取簽核歷程寫法", "提交日期": "2017-12-07 10:47:32", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "047e491b20e0f788e815e9a1f5ec59ef08651560", "commit_訊息": "A00-20170808002 修改Oracle角色及員工工號不會顯示問題", "提交日期": "2017-12-07 10:41:49", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d33e3c7b53c444b04a973fb058f47a9530ed1297", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-07 10:34:09", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "4ad8988b1ef48ef3e20be4672bea99672e7fcbb3", "commit_訊息": "[C01-20171120001]修改上傳ISO表單資料用RequireNew獨立出來，切割跟文件佈署主機的交易", "提交日期": "2017-12-07 10:31:44", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2d1aead64360848cc28c3f84eea0e8cbc0313262", "commit_訊息": "新增加簽restful服務", "提交日期": "2017-12-07 10:27:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ActivityDefinitionForClientListBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AddCustomActivityBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ffe53406fbb743b223bdd02509350d60c89979eb", "commit_訊息": "修正 統計元件佈署工具配置欄位及CSS檔", "提交日期": "2017-12-07 08:57:53", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "feca9663e915388ae372a6b240b36086df14fc5c", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-07 08:54:45", "作者": "喬倫", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "3d11d702173e818ab7add9670af814db9da9fff1", "commit_訊息": "修改 鼎捷平台佈署工具JSP檔配置", "提交日期": "2017-12-06 19:10:27", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeploy.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentMenu.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/clipboard/clipboard.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 14}, {"commit_hash": "d350a8aa9be24ea8dce8c57deb3929dfea5c7c69", "commit_訊息": "修正入口整合平台,單一JSP過大會出錯誤問題", "提交日期": "2017-12-06 18:33:02", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeploy.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentMenu.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "21a3e8bd208e71980ac7999439929d13213726a1", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-06 18:31:28", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "12c07bf8e87919d46aea3d49ab4192491359d808", "commit_訊息": "A00-20171206002 建立index", "提交日期": "2017-12-06 16:49:38", "作者": "張詠威", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/Next_updateSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/Next_updateSQL_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "ca947c0228b3e912e2b3a3b65dcf3fd1ac9b08ff", "commit_訊息": "//20171129 waynechang C01-20171123003 調整 將 DEFAULT_VALUE 調整為空白", "提交日期": "2017-12-06 16:15:32", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "625ea8b2f4557c9063ae6f1da6b8f3207fdc1314", "commit_訊息": "C01-20171006002 修正workflow更新表單時，欄位會變黑", "提交日期": "2017-12-06 16:11:33", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4050848849cd76ceb4f27edd0684931fe4f49afe", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-06 15:41:15", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c7706cdd362ed6c84b72ed81d0047dd94ba0976e", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-06 15:28:08", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "7c6c1c1aada6943a3bbc25028d321a18611cc7f9", "commit_訊息": "修正取回重辦restful的OID大小問題", "提交日期": "2017-12-06 14:32:19", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "50f49c0cf0123b7357f9e35d6f58c6fdb3e4cbc2", "commit_訊息": "restful服務的關卡定義javaBean新增同意派送是否必填簽核意見開關", "提交日期": "2017-12-06 14:25:10", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityDefinitionBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c2651839a5fecad5e8bcad7b6c9031a35dcf8a15", "commit_訊息": "修正取得流程緊急度javabean的OID大小寫問題", "提交日期": "2017-12-06 14:03:35", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmProcessLevelBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a153267ee9775daf48e6e5165a90a550c6cf270a", "commit_訊息": "新增 鼎捷移動平台佈署工具的多語系", "提交日期": "2017-12-06 13:48:26", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3b2e7c8d7bfc28f9e1151eb6fb3dfac639ebc4f7", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-06 13:46:42", "作者": "喬倫", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "cb52e0fb439a83ef6aa643771792429108c03e74", "commit_訊息": "新增 鼎捷移動平台佈署工具頁面的多語系", "提交日期": "2017-12-06 13:45:25", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "97890cb0332931d9b9c32654684dbaabfd383478", "commit_訊息": "修正BPMAPP終止流程前端錯誤打錯部分", "提交日期": "2017-12-06 13:44:55", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63c678a6b5fba9de3fcbfe2eb6227547c7418d5d", "commit_訊息": "修正草稿取詳細的javabean少取草稿OID問題", "提交日期": "2017-12-06 12:55:41", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmWorkItemDataBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "946d5e347958b3f40ba95e469766a4f64f401cb1", "commit_訊息": "新增  鼎捷移動平台佈署工具頁面 : 發起流程、待辦簽核、流程追蹤、工作通知、其他工具", "提交日期": "2017-12-06 12:45:43", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap.4.0.0.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/clipboard/clipboard.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-3.2.1.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/bootstrap_3_3_7.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/bootstrap_4_0_0.min.css", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 8}, {"commit_hash": "68d209312f09814963b3bb1a3f4dd69217d56e5c", "commit_訊息": "修正退回重辦restful服務function名稱", "提交日期": "2017-12-06 11:01:14", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1641a325aff5220142860a9ff30fbc823f054d28", "commit_訊息": "修正表单中间层多选元件问题", "提交日期": "2017-12-06 10:59:43", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "af125353a440b45175bb5fde64040580bf898391", "commit_訊息": "BPMAPP增加簽核時若流程有設定必填簽核意見，在派送時會有提示訊息", "提交日期": "2017-12-06 10:04:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "1b41958512385a8d3687104543058db5d61b1aac", "commit_訊息": "修正formBuilder錯誤", "提交日期": "2017-12-06 09:48:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d9ece7618b70cea9815c0ae24f6f3a3323ba2b59", "commit_訊息": "S00-20171205001 - ESS流程發起後就結案", "提交日期": "2017-12-05 16:10:33", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/appform/AppFormKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "cb7bdfc310c4b2e39abe4d03062325a6ba7f298b", "commit_訊息": "修正取得組織資料javabean的OID大小寫問題", "提交日期": "2017-12-05 15:33:27", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OrgUnitBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dfc236ec216769213220fa34e8ef890f34dfaa87", "commit_訊息": "新增中間層單身長度與單頭長度判斷有無穩合", "提交日期": "2017-12-04 19:07:12", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "cdba097c9c7b9831c4d577c2e80b6a9034727329", "commit_訊息": "修正退回重辦javaBean的OID大小寫問題", "提交日期": "2017-12-04 16:53:55", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ReexecuteActivityBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "106f0266fbfe32559bf175ceda9ffcde3fe2d089", "commit_訊息": "修正取退回關卡清單javaBean的OID大小寫問題", "提交日期": "2017-12-04 15:31:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityInstanceBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a0a3bc7a1f167668b323a0fb6c0ab16734053b6d", "commit_訊息": "調整待辦的javabean參數OID改為oid", "提交日期": "2017-12-04 14:35:30", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForPerformBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmWorkItemDataBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0580e44baa99e0210de9d34335497d1ea3ff3420", "commit_訊息": "新增 待辦列表依指定流程、我的最愛、常用流程過濾", "提交日期": "2017-12-04 14:22:16", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "044af87fa999789fdc845d429277d8c911db728e", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-04 14:20:19", "作者": "喬倫", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "eadd9bbb61fddb76aadfbb30a41abd5c6e5b5abd", "commit_訊息": "新增 待辦列表依指定流程、我的最愛、常用流程過濾", "提交日期": "2017-12-04 14:18:21", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ceb49d1f5900c074af5c8a8274f78c71511e5f8e", "commit_訊息": "調整入口平台整合設定鼎捷移動平台的詞彙", "提交日期": "2017-12-04 13:38:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "bb63ca9f1e3da3072d7a336900e2bbedf76552da", "commit_訊息": "修正鼎捷移動一期列表BUG", "提交日期": "2017-12-01 20:34:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "50affd3f0d3b987283ad02925a66966d67155258", "commit_訊息": "[C01-***********] APP企业微信发单异常，点击发起后，提示发单失败，实际是有发起成功", "提交日期": "2017-12-01 15:52:51", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/StatefulProcessDispatcherDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "992b65b9c9a11c7d81a9aafefead627d032014b9", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-01 15:36:57", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "db3140e8d5d63534ff803db78c4ef1175700fc49", "commit_訊息": "修正表單中間層無法顯示多選元件內容問題", "提交日期": "2017-12-01 15:35:49", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "bac46d7ac58d7eb6e3faae7753f66471f809a2a9", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-01 11:26:39", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c6a639cc3f4bd0266a411db1851bbc102a0b72ab", "commit_訊息": "C00-20170927002 修正組織同步更新人員cache失敗時導致組織同步rollback", "提交日期": "2017-12-01 11:26:22", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e9a557dd01e42d3974b88df3a5a3b0ac038994b", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-12-01 11:20:52", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "b73552194c73e1e8dc11976fcacab4bde8424979", "commit_訊息": "調整多選元件中間層功能", "提交日期": "2017-12-01 11:20:07", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bdbb9563db0966842e44df66f38cf9714ea9b40e", "commit_訊息": "修正微信登入問題 修正二期列表問題 修正發起流程二期功能", "提交日期": "2017-12-01 11:11:16", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "417eb02db84e3eeb13aa3446f493b9aa1ab25f92", "commit_訊息": "C01-20170612002 修正追蹤信使用ldap無法登入", "提交日期": "2017-12-01 10:54:58", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d44d5eeae398832f09b131eff602275275484d2", "commit_訊息": "修正查詢待辦工作restFul服務bug", "提交日期": "2017-12-01 10:51:30", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PerformTypeBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0974ccb2cc7bda5a9a83595c60a8e4177aa87e96", "commit_訊息": "修正鼎捷移動一期列表流程狀態功能", "提交日期": "2017-11-30 19:08:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b7665aaf9f306284a3c56b2ac95678cffaeb5a1", "commit_訊息": "新增 多語系:流程狀態 工作來源 通知來源", "提交日期": "2017-11-30 15:54:10", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ff40002eedca20ed1b9b826550196bb5d138396", "commit_訊息": "新增 待辦列表取得工作來源  追蹤列表取得流程狀態  通知列表取得通知來源", "提交日期": "2017-11-30 14:56:25", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "911a5252193db5d68384b3c49a33dc6cd4f10ab2", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-30 14:52:26", "作者": "喬倫", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c95b201129126fc720440c473b55a6c8c4bb0af2", "commit_訊息": "新增 待辦列表取得工作來源  追蹤列表取得流程狀態  通知列表取得通知來源", "提交日期": "2017-11-30 14:51:15", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "972f59f867123b1942406a4017a014413c95f306", "commit_訊息": "新增設計行動版表單時，預設拖拉前20個元件為中間層", "提交日期": "2017-11-30 13:43:36", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "364b375ea16074966479a93c20a07a58015edf66", "commit_訊息": "新增行動版表單無標記中間層時，預設前20個元件為中間層", "提交日期": "2017-11-30 13:42:33", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "fe90754a4a8a051561717b47453f230117068a08", "commit_訊息": "調整草稿列表javabean", "提交日期": "2017-11-30 11:57:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DraftHeaderBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0a1fb3743c1c7742ad0c9cd942b0dabc32db3508", "commit_訊息": "修正取簽核歷程時，退回重辦關卡重覆取得錯誤", "提交日期": "2017-11-30 11:50:47", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "027288e756fb9610da8e91fcd0ff99049dd51964", "commit_訊息": "修正行動版相對位置textbox若設定資料型態為浮點數且有設定小數點後幾位會無法編輯問題", "提交日期": "2017-11-29 17:18:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "39dca528d61714fd73bc0803ebc567b91de14b5b", "commit_訊息": "C01-20171123003  取消T100表單欄位預設值", "提交日期": "2017-11-29 16:41:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "521958c6a4a50e76c89a70294c9fce55763f2c47", "commit_訊息": "修正行動版Grid欄位順序與表單設計器拉的不同的問題", "提交日期": "2017-11-29 15:27:22", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileAppGrid.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4a14191caa6664c0fb8a909afd4b6b11cc7e5dc2", "commit_訊息": "新增編輯我的最愛restful服務", "提交日期": "2017-11-29 13:02:44", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeFavoriteReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d7c333f4669e173b2ababa4f419248f750de1429", "commit_訊息": "修正鼎捷移動平台多語系導致無法簽核問題", "提交日期": "2017-11-28 15:26:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0c441ed8d152c29a775128896def769a4895cea", "commit_訊息": "查詢關卡步驟restful服務新增是否必填簽核意見", "提交日期": "2017-11-28 11:30:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityDefinitionBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc49a2f749466eecaab7fd40ef176b1b3d872b36", "commit_訊息": "修正中間層單身收合、中間層單身可顯示多個", "提交日期": "2017-11-27 18:06:29", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldContent.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "58a50a5761adc8ae16aa364d7c7496d3029e712e", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-27 13:39:24", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "9650ac9b3325652a18d0b57e61ccd1f74586dffd", "commit_訊息": "調整繼續派送RESTFul服務增加多部門功能 調整待辦jsp少冒號問題 調整取得組織人員清單javabean參數名稱", "提交日期": "2017-11-27 13:38:46", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "4ec53634c63cc7e6df1aa686e77b7e379e77e05f", "commit_訊息": "新增發起流程篩選功能", "提交日期": "2017-11-27 13:37:57", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ESSFileManager.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ESSFormHandler.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ESSPerformWorkItem.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "a8e09435174c98d987503b6f55ceb085f2b2b528", "commit_訊息": "Q00-20171127001 修改:連接ESS管理作業URL的Patten 改為設定檔設定", "提交日期": "2017-11-27 11:54:00", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "42581cdc314f729769857da533842bd1df47dae2", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-27 10:28:39", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 3}, {"commit_hash": "28f80f726dbb57c7253fc25fdcc68ac7bff7289f", "commit_訊息": "新增鼎捷移動二期發起流程服務", "提交日期": "2017-11-27 10:16:26", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/jdajia.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "7ac38181703e8731fa05b81d05abb2665848a428", "commit_訊息": "C01-20171122001 核決關卡參考通知關卡時，簡易流程圖顯示關卡異常", "提交日期": "2017-11-24 16:16:04", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6cba5fde1ac6bb37346b3767e76bea79ff2ec87c", "commit_訊息": "Q00-*********** BPM開啟後沒有人登入過時，第一次restful呼叫SQL註冊器無法使用", "提交日期": "2017-11-23 17:31:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c3ce5872b93350dea54bc79b7d7bf01149c8ecc", "commit_訊息": "新增取退回重辦關卡RESTful服務", "提交日期": "2017-11-23 16:37:45", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c097437428d1c663eb0b1c3d5027f981844e1885", "commit_訊息": "新增中間單身明細可用字段與中間層單身明細替換成Grid名稱", "提交日期": "2017-11-23 14:53:16", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e0a30db020616843acc3c417dc2c4793bb394eab", "commit_訊息": "修正中間層退回重辦服務錯誤", "提交日期": "2017-11-23 14:50:27", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityInstanceBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ffd1d740c92f1e0b88d4d7a8b7a6c0fd0ecac78b", "commit_訊息": "修正中間層同意、不同意服務取不到RemoteUser問題", "提交日期": "2017-11-23 14:47:19", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "58f428c63990b5f65e10d735428f32aa40ed6092", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-23 14:24:14", "作者": "治傑", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "4dc4173f5770f8cd687ac29207934d9bd8ac4692", "commit_訊息": "修正C01-20171121001漏掉'{'符號 調整追蹤詳細資料將systout註解", "提交日期": "2017-11-23 11:55:16", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "727c55cd0394cfddbe756b2fe30890b40057fe68", "commit_訊息": "新增取回重辦服務", "提交日期": "2017-11-23 09:09:09", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/RollbackActivityBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "494ced1b8b5968e7cd84879694eec79e1ad2bf13", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-22 18:19:38", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "0da9ee8db0167bddf55e7dd5438f4a72b4b32de4", "commit_訊息": "修正中間層單身", "提交日期": "2017-11-22 18:18:46", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4103414307c1babe6a3ee8f6a51cbdd55189d67d", "commit_訊息": "新增 中間層通知列表字段及資料來源 : 通知來源", "提交日期": "2017-11-22 18:05:21", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "478f3def9f4709bb979609d95403c00c04c13a9d", "commit_訊息": "[A00-20170717003] 修改點選部門出現系統錯誤", "提交日期": "2017-11-22 17:40:36", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99e2ff28639e1afc62fd97b101891ffb22c85097", "commit_訊息": "[A00-***********] 修正同部門人員可重複新增問題", "提交日期": "2017-11-22 17:21:35", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/AddUserToUnitDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "2eb5f053f40f5bf1dc7dfd756bc958ff4b49cc4e", "commit_訊息": "調整追蹤取回重辦的jsp少了冒號問題 C01-20171121003 修正APP版本在Grid無填寫表單內容時,value會顯示']'問題", "提交日期": "2017-11-22 17:08:16", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTraceLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "27b8e1ae075064c771029e0b25e8f05558241515", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-22 16:30:23", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "73386f59d84a243979905c578cec6054e7361fd6", "commit_訊息": "Q00-20171122003  調整SQL註冊器在回傳資料時(select A,B,C from table)，無法依照ABC傳回", "提交日期": "2017-11-22 16:27:03", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleDataChooseTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/LikedJSONObject.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "46f7740b2e88c116df2632bc71130e6a38ec76c7", "commit_訊息": "[A00-20170717003] 修改點選部門出現系統錯誤", "提交日期": "2017-11-22 16:24:00", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f41773fe6107f40318578cb719fc3be9e03a9770", "commit_訊息": "新增 中間層追蹤流程列表字段及資料來源 : 流程狀態", "提交日期": "2017-11-22 16:12:23", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fd34b9f089652df2d4e866d4d8cba03575c1d8be", "commit_訊息": "新增 中間層待辦列表字段及資料來源 : 工作來源", "提交日期": "2017-11-22 14:19:39", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "849b5357574c031f3d5740a8b5d2c145af2bc73b", "commit_訊息": "刪除log", "提交日期": "2017-11-22 10:57:18", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fbc537717ab075c5dd6a7d662fbf5fb8387e2e89", "commit_訊息": "[C01-20171121001] 修正ESS表單有附件時BPM App開啟表單時的異常", "提交日期": "2017-11-22 08:52:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFileManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "97ce75f8318cfbc2dfd2b86edb813550ade86e69", "commit_訊息": "修正BPMAPP取通知資料總數量不對問題", "提交日期": "2017-11-21 19:48:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd76172d9ef0cbadc28cf201ef160766a5653c6a", "commit_訊息": "流程設計師指定時限無法正常儲存值", "提交日期": "2017-11-21 17:52:42", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorCER.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fdb4062928d61bcb6cb4d1b2c8a8ebce1691b52", "commit_訊息": "修正 中間層簽核歷程狀態的多語系顯示", "提交日期": "2017-11-21 17:11:12", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "35807bd8d1d48db937f7b8073080a752470acaaf", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-21 17:07:34", "作者": "喬倫", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "8baa6c4d82a347023425d44cf6156e399fb7393a", "commit_訊息": "Revert \"修正 中間層簽核歷程狀態的多語系顯示\"", "提交日期": "2017-11-21 17:05:42", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c3fb547b1e48d7ad3d115c4c2b695ba1abd4aeac", "commit_訊息": "修正 中間層簽核歷程狀態的多語系顯示", "提交日期": "2017-11-21 16:50:16", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c0858de31a917f7398d4dbeee539f7412078466e", "commit_訊息": "[S00-20170928002]老版本流程设计师通知和审核都是同一个控件（客户需求就是有部分通知关卡不需要邮件提醒）。", "提交日期": "2017-11-21 16:53:03", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "abe9664b6b62e8f0f6ccedaa528e31c84b56e2fb", "commit_訊息": "流程設計師指定時限無法正常儲存值", "提交日期": "2017-11-21 16:49:20", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorCER.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8a5f09d44be37480b2086662dbaaca3d6fd8e1e", "commit_訊息": "[A00-20171003002]舊版設計師有流程多次逾時選項，但於簽核流程設計師不見了", "提交日期": "2017-11-21 16:37:37", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorCER.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorPanel.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateObject.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "46ad50d94f802350147484d05eacbd8a55b9e0a9", "commit_訊息": "調整取得聯繫人的參數(配合鼎捷移動)", "提交日期": "2017-11-20 17:30:36", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookData.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ContactBeanRes.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "f178adbd7843e1aa27aae5a29c19bad8873cbd01", "commit_訊息": "修正BPMAPP終止流程前端錯誤", "提交日期": "2017-11-20 12:02:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "13de34ab9dde013a825cf417925ca255697f5386", "commit_訊息": "調整取得待辦流程表單資料RESTFul服務", "提交日期": "2017-11-20 11:05:11", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForPerformBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormDefinitionBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f406dfcab9f6ce445ed9967825f2cc6f51276ef2", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-17 15:06:29", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "66bff5cdb606514556a1fdcd0cef3e715ca5ea70", "commit_訊息": "修正錯誤", "提交日期": "2017-11-17 15:01:30", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "90a27688fb919d4bcba48fff1b9a6b55bfc262b3", "commit_訊息": "修正ESS表單沒有簽核歷程問題", "提交日期": "2017-11-17 14:56:17", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "188b0399773c46bc8053ae4f02a98db4515f1c03", "commit_訊息": "修正中間層表單單身明細、中間層附件下載連結", "提交日期": "2017-11-17 14:08:35", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/RowDataSet.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "4a0b1d456a468e5accadd4cb4ba1034d7912a4d9", "commit_訊息": "調整取得流程分類的RESTFul", "提交日期": "2017-11-17 10:56:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ListReaderBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c04094f6bf70023108164514909b56b80a25195d", "commit_訊息": "修正ESS流程在BPMAPP中無法終止問題", "提交日期": "2017-11-16 20:30:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c6122c4fb56506230e702dd6892e2ec3384a2a47", "commit_訊息": "修正ESS表單沒有簽核歷程問題", "提交日期": "2017-11-16 17:35:04", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fb6db22a65c42561d3ec3783a2cad909aaa52a36", "commit_訊息": "修正簽核多語系 退簽關卡多語系 撤銷流程功能", "提交日期": "2017-11-16 10:51:37", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "81dced1a5b43e19f67e4d9825006880228f2b55b", "commit_訊息": "調整簽核和退簽關卡的RESTful URI", "提交日期": "2017-11-16 10:28:56", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "01bd18e3ba7f0939f87460df7a73bfa55c5005d3", "commit_訊息": "調整撤銷javabean的set、get名稱", "提交日期": "2017-11-15 15:53:23", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "11010db61ea6e363c8c5a3bb0c3d21b6f692f6a1", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-15 14:28:57", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "855a0c9aeb74dc7552184f08c2deae44f3df21cb", "commit_訊息": "修正鼎捷移動token過期無法刷新問題", "提交日期": "2017-11-15 13:46:57", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2392da107ffeb48a0e9f54abdb009f7e9328951c", "commit_訊息": "修正中間層簽核歷程、中間層表單單身單頭", "提交日期": "2017-11-15 11:47:12", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "266a8d7aa0f519251ca5952266c8c3dd41296546", "commit_訊息": "C01-20170831002 修改 簽核完一個待辦後，跳下一筆不簽核返回待辦清單,再次點選待辦會無法進入", "提交日期": "2017-11-14 18:16:49", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ddc4a6a1e596331913c84a4445a2d5b1f094439b", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-11-14 16:52:17", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "43ed068790423d24f27aed8219c2cfffc48ef9f8", "commit_訊息": "新增退回重瓣選擇關卡服務 新增鼎捷移動簽核、終止、退回重瓣服務", "提交日期": "2017-11-14 16:47:53", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchReexecuteActivityBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityInstanceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchReexecuteActivityBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "84eaf358eb15113f49586827d95a116d3fd39905", "commit_訊息": "新增利用SQL註冊器取得表格資料、取得中間層簽核意見、中間層表單取得附件以及單身單頭", "提交日期": "2017-11-14 14:53:23", "作者": "喬倫", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ColumnDataSet.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentArray.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentColor.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentImage.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentWidth.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDataset.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDetail.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/HeaderDataSet.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListFieldDataset.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/QueryCriteria.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/TableHeader.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 23}, {"commit_hash": "5c703d271a867d4e04b8eeeb71267a6f0b5af34a", "commit_訊息": "新增表單中間層可用字段", "提交日期": "2017-11-14 11:03:01", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulClientDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldContent.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "75c39618fccee3f8594acb4da23f6c603cbdbebf", "commit_訊息": "調整微信被動響應RESTful服務", "提交日期": "2017-11-14 09:21:30", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WeChatKeyBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "02c955f3ec0f7662ec3c444d172b9a44ee04a286", "commit_訊息": "新增取得流程分類的RESTFul服務", "提交日期": "2017-11-13 16:44:56", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SearchProcessListBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/SearchProcessListBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ProcessMgr.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "d2794f53f1ce6449c83ae5024d0d340a89478b8e", "commit_訊息": "調整追蹤javabean的get跟set名稱", "提交日期": "2017-11-10 18:45:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/TraceProcessBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ae4bd080592804fef9d848fce0b171602239b63d", "commit_訊息": "新增同意派送架構 新增不同意派送架構 新增退簽派送架構", "提交日期": "2017-11-10 14:37:50", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleOpReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOpReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataOpReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/RemoteUser.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "249d79112a78ca2dd63a4bd0537dd3bc2263e63c", "commit_訊息": "新增表單中間層單身顯示架構 新增表單中間層附件架構 調整WorkInfo", "提交日期": "2017-11-09 18:17:03", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/RsrcBundleCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AllRsrcbundleValueBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "77316807bcbbeebd7e57024573d91ebc75b19881", "commit_訊息": "補上javabean漏寫空的建構式", "提交日期": "2017-11-08 19:02:04", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentTypeBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemStateTypeBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "878f567aa35105e82b288651af3009c98c4e71c7", "commit_訊息": "Q00-20171107001 修正TT或T100流程在第一關收不到微信推播問題", "提交日期": "2017-11-07 18:46:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "26e5fede83aa6a880966018b1a24bb199c209fb2", "commit_訊息": "C01-20171103004 修正核決關卡結束後不允許取回重瓣", "提交日期": "2017-11-07 15:38:10", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2d942439cf78fd8124b06738cbc9e8bdf5c8edca", "commit_訊息": "Q00-20171027002", "提交日期": "2017-11-06 16:39:24", "作者": "張詠威", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@syncisodoc/InitSyncISO_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@syncisodoc/InitSyncISO_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "4d39a6885942676886de7376d33ccd3d26e3f2c8", "commit_訊息": "Q00-20171031001", "提交日期": "2017-11-06 16:28:46", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e4e5e89dd301b2188fa6cde27094d8f7d17ac4cd", "commit_訊息": "調整取得所有多語系的RESTful服務", "提交日期": "2017-11-03 17:29:14", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/GetRsrcbundleBeanReq.java", "修改狀態": "重新命名", "狀態代碼": "R080"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AllRsrcbundleValueBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GetAllRsrcbundleValueBeanRes.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GetRsrcbundleBeanRes.java", "修改狀態": "重新命名", "狀態代碼": "R080"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "ab399b10bae08da5a2bd1afff20cb15dcf25cd07", "commit_訊息": "C01-*********** 修正Android手機新增grid資料被按鈕擋住問題", "提交日期": "2017-11-03 16:28:39", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "690fcf5039a0e11623190e1268dffd084dcca07b", "commit_訊息": "A00-20171102001 修正 : ISO文件一覽表，文件類別展開畫面沒有scrollbar", "提交日期": "2017-11-03 11:34:29", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75391bce27e8f853e716cd0bd930808327bdb414", "commit_訊息": "Q00-20171102001", "提交日期": "2017-11-02 17:54:46", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89504a68b46ac966b3d362c7f98bf1681b84a9a8", "commit_訊息": "調整增加判斷工作通知類別 InputElement純marge", "提交日期": "2017-11-02 16:13:01", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0381c64b7b43037df5b5d00ad04b1f9e37a8a66d", "commit_訊息": "C01-20171027001 修正IOS上微信消息內容被切斷 導致內容異常問題", "提交日期": "2017-11-01 19:24:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f55fd838d4b2e10b8f843323f8c46fa1687aadf", "commit_訊息": "A00-20171101001", "提交日期": "2017-11-01 17:12:25", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0518635f679667c1d4e88da025ce35ce62be1db0", "commit_訊息": "A00-20170807002  修正客製開窗變更比數後資料重複議題", "提交日期": "2017-11-01 16:21:44", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ce4869c1d4a0b34fa71b1389e2d9bfc1ea490902", "commit_訊息": "A00-20170726004 加上防呆", "提交日期": "2017-11-01 16:15:37", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e456ed75ce94759da59379b58b2903adb823c79c", "commit_訊息": "A00-20170726004 修正通知樣版 代辦URL連結", "提交日期": "2017-11-01 15:33:31", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbd46d56e534c9d4c341cc7d97bdd9ed3ab34827", "commit_訊息": "A00-20171031001 修正ESS流程在通知關卡無法打開行動版相對位置表單問題", "提交日期": "2017-10-31 17:48:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "657e9d0677b04d8d577e3fcf35a50871be527dd2", "commit_訊息": "A00-20170822001 產品開窗使用SQL註冊器，到流程草稿會呈現空白", "提交日期": "2017-10-31 16:55:21", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cf82ec16fb44777a32221a22acea3d627267548c", "commit_訊息": "修正鼎捷移動表單中間層多語系無法顯示問題 修正鼎捷移動直連表單多語系無法顯示問題 修正列表過濾功能,流程重要性多語系無法顯示問題", "提交日期": "2017-10-31 11:49:22", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a4d9e76830313b45de7d5c9c507b05b7dfc46291", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-10-30 17:51:14", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "adafef365330ef0ae4485b62a96c2f6f05668e15", "commit_訊息": "修正鼎捷移動二期表單語系顯示問題", "提交日期": "2017-10-30 17:44:53", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "68542131953721a12049c5cb56d8f2575da52b0c", "commit_訊息": "Q00-20171030001 補上RTX程式註解", "提交日期": "2017-10-30 15:00:12", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b48fcd65ee50addbc23bc73b218d6e52eebb9a9b", "commit_訊息": "C01-20171018001 流程模型更新版本後，使用重發新流程畫面會一直轉 調整寫法", "提交日期": "2017-10-30 14:33:45", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7651b6bba911ffb7a0de990af9e11532bacc829", "commit_訊息": "Q00-20171027001 修正 :關注事件模組開窗問題", "提交日期": "2017-10-27 16:27:58", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c373201ef905c2b94582ae2b8cfd05292f35fccc", "commit_訊息": "A00-20170830001 修正 :ISO無法開啟參考文件", "提交日期": "2017-10-27 16:14:46", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "41821cc439bdead6ca37fa1e06df9438e3c61148", "commit_訊息": "A00-20170926001 修正 :產品客製開窗查詢功能及查詢樣板 ,輸入簡體文字無法查詢正確資料", "提交日期": "2017-10-27 10:28:32", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dab56a7a48719d7230dc647a9d4933fd5fd0d760", "commit_訊息": "修正鼎捷移動行事曆列表部份功能異常，增加直連表單的log", "提交日期": "2017-10-27 08:56:08", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8983124633e60a2ef55254fb56017cc3d60dcdaa", "commit_訊息": "調整簽錯部分", "提交日期": "2017-10-26 17:26:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4bb53056055eb3d5cb65963a5234332c0754ddd8", "commit_訊息": "調整鼎捷移動圖表邏輯", "提交日期": "2017-10-26 17:23:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "9b608fc5e835c4e706fbf3bb608dd3500fef58f8", "commit_訊息": "修正鼎捷移動追蹤已完成流程取不到流程資料的問題", "提交日期": "2017-10-26 14:51:16", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d029981060f75ad14f9cf041f3029d60167ac721", "commit_訊息": "新增取得全部多語系的RESTful服務", "提交日期": "2017-10-26 11:49:24", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GetAllRsrcbundleValueBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "77f47789a185e44d6dcb8cc1c6ff6fb06245b402", "commit_訊息": "修正取得待辦事項表單資料的RESTful異常", "提交日期": "2017-10-25 16:04:34", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a83051aef9dd4e5f126de2ed0a3e45d63d248bd", "commit_訊息": "C01-20171020001 修正ESS流程取語系時會有NullPointerException問題", "提交日期": "2017-10-25 14:52:59", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileUserAuthTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e6a4f0ca4549ca1b9d4f3277c6f220ccaadc93f1", "commit_訊息": "2次修正 關鍵事件開窗點選資料無法帶入欄位", "提交日期": "2017-10-25 12:07:42", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}