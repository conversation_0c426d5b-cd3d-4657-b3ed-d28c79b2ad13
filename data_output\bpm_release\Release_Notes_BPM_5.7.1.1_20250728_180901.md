# Release Notes - BPM

## 版本資訊
- **新版本**: 5.7.1.1
- **舊版本**: 5.7.0.1
- **生成時間**: 2025-07-28 18:09:01
- **新增 Commit 數量**: 133

## 變更摘要

### jerry1218 (35 commits)

- **2018-03-29 16:53:28**: 5711問題修正
  - 變更檔案: 11 個
- **2018-03-28 16:37:08**: 移除我的關注-備註功能
  - 變更檔案: 25 個
- **2018-03-27 15:46:56**: 轉派員工工作-員工名稱 多語系修改
  - 變更檔案: 2 個
- **2018-03-26 18:44:06**: 1.修正程式權限管理功能異常 2.修正一般使用者menu系統設定就算有項目可顯示但依然不會出現問題
  - 變更檔案: 4 個
- **2018-03-23 17:35:15**: 修正設定片語頁面alert忘了拿掉
  - 變更檔案: 1 個
- **2018-03-23 15:09:04**: 修正Dash Board圖片錯誤
  - 變更檔案: 1 個
- **2018-03-23 14:10:56**: 1.V57測試問題修改 2.智能示警及重要流程img置換
  - 變更檔案: 7 個
- **2018-03-22 17:23:17**: 1.關鍵流程label改為智能示警 2.檢視關卡資訊圖案更改
  - 變更檔案: 4 個
- **2018-03-22 16:18:27**: 5711發版測試異常修正
  - 變更檔案: 16 個
- **2018-03-20 11:04:50**: 我的關注功能修改
  - 變更檔案: 13 個
- **2018-03-16 14:39:13**: 新增dashboard-我的關注功能
  - 變更檔案: 1 個
- **2018-03-16 09:53:56**: 新增我的關注支援excel匯出(JSP)
  - 變更檔案: 1 個
- **2018-03-16 09:52:57**: 新增我的關注支援匯出excel
  - 變更檔案: 1 個
- **2018-03-15 17:18:14**: 新增關注流程功能.修正右上簽核相關icon大小螢幕切換顯示異常
  - 變更檔案: 15 個
- **2018-03-13 17:52:25**: 我的關注
  - 變更檔案: 1 個
- **2018-03-13 17:47:10**: 我的關注欣曾.圖片變更.T100簽核模板更新
  - 變更檔案: 58 個
- **2018-03-01 14:27:06**: 修正轉派員工工作FloatButton異常
  - 變更檔案: 1 個
- **2018-03-01 14:21:53**: 新增我的關注domain&Service 修正1.頁面清單流程名稱最多顯示行數 2.administrator監控流程關注&重要異常 3.轉派員工工作頁面調整
  - 變更檔案: 21 個
- **2018-02-26 09:40:46**: 1修正merge後Critical邏輯變更所導致的原程式問題 2.修正匯出excel可用時間空白問題 3.修正關注項目維護作業JSP merge後的異常
  - 變更檔案: 5 個
- **2018-02-22 17:10:14**: 修改關注流程merge到5652後邏輯的變更導致的異常
  - 變更檔案: 3 個
- **2018-02-22 16:10:56**: 1.修正個人化首頁-重要流程數量計算錯誤問題 2.流程內容-完整流程頁面修改 3.微調SQL註冊器頁面 4.修正Template.jsp對於個人化首頁的因應 5.修正重啟服務功能SQL的order by
  - 變更檔案: 8 個
- **2018-02-21 16:24:16**: 修改5701 updateSQL檔案名稱&移除表單多語系功能連結
  - 變更檔案: 6 個
- **2018-02-21 15:59:02**: 搬移Critical SQL&修正單選及多選窗分頁出現時機
  - 變更檔案: 6 個
- **2018-02-12 15:16:59**: 修改多語系文字
  - 變更檔案: 2 個
- **2018-02-12 12:02:17**: 修改多語系文字及畫面文字
  - 變更檔案: 2 個
- **2018-02-09 17:12:23**: 修正V5舊流程設計師拉出來的流程顯示流程內容異常
  - 變更檔案: 6 個
- **2018-02-09 10:54:53**: 1.修正排版 2.變更table min-windh改為windh
  - 變更檔案: 5 個
- **2018-02-07 18:03:23**: 修正-個人化首頁自訂圖表區間異常
  - 變更檔案: 1 個
- **2018-02-07 17:58:54**: 修改-個人資訊頁面排版 新增- 1.通知頁面支援關注流程&重要流程 2.個人化首頁自訂時間
  - 變更檔案: 5 個
- **2018-02-07 10:16:40**: 我的最愛main頁面修改
  - 變更檔案: 1 個
- **2018-02-06 15:09:40**: 修正-1.各功能頁面統一、2.service缺少commons-digester.jar(影響iReport開啟)
  - 變更檔案: 19 個
- **2018-02-01 16:33:05**: 修改個人化首頁畫面
  - 變更檔案: 1 個
- **2018-02-01 09:51:53**: 在個人化首頁時,右上角隱藏簽核區塊按鈕
  - 變更檔案: 1 個
- **2018-01-31 16:07:32**: 修正簽核及追蹤畫面 , 返回按鈕移至最左邊
  - 變更檔案: 2 個
- **2018-01-30 16:20:10**: 修改問題: 1.功能頁修改按鈕顏色與獨立模組不同 2.簽核中轉由他人處理頁-片語異常 3.模擬使用者頁面修改,支援快搜
  - 變更檔案: 10 個

### Gaspard (57 commits)

- **2018-03-29 11:06:01**: 修正未分類流程的顯示名稱
  - 變更檔案: 1 個
- **2018-03-23 09:38:58**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-03-23 09:38:34**: 修正SubTab可編輯的屬性、修正Title元件無圖片時直接給空的SRC
  - 變更檔案: 2 個
- **2018-03-22 18:02:21**: 開啟DoubleTextBox元件供使用
  - 變更檔案: 1 個
- **2018-03-22 09:11:27**: 將jBPM.toJSON統一改用JSON.stringify
  - 變更檔案: 9 個
- **2018-03-20 11:30:19**: 隱藏DoubleTextBox與TripleTextBox元件
  - 變更檔案: 1 個
- **2018-03-16 15:23:08**: 修正$.變成jBPM.
  - 變更檔案: 3 個
- **2018-03-14 12:07:20**: 縮小菜單ICON大小
  - 變更檔案: 2 個
- **2018-03-12 14:41:02**: 修正查詢維護樣版需要用引用到的程式路徑
  - 變更檔案: 260 個
- **2018-03-08 10:09:19**: 修正開窗後的查詢條件開窗樣式
  - 變更檔案: 1 個
- **2018-03-08 10:04:37**: 修正於發起流程清單頁面，點擊「顯示全部」功能失效的議題
  - 變更檔案: 1 個
- **2018-03-08 09:59:08**: 修正將TTTTTTTTTT取代成$$$$$$
  - 變更檔案: 26 個
- **2018-03-07 14:09:25**: 修改查詢維護樣版的錯誤訊息樣式與產品相同
  - 變更檔案: 12 個
- **2018-03-07 11:10:23**: 修改Grid自帶修改控制項的代號與方法統一為edit(非update)
- **2018-03-07 11:10:23**: 修改Grid自帶修改控制項的代號與方法統一為edit(非update)
  - 變更檔案: 3 個
- **2018-03-06 11:50:17**: 移除多餘alert訊息
- **2018-03-06 11:50:17**: 移除多餘alert訊息
  - 變更檔案: 4 個
- **2018-03-06 11:17:19**: 修正於表單中隱藏Gird元件後，造成綁定的隱藏欄位清空的異常
- **2018-03-06 11:17:19**: 修正於表單中隱藏Gird元件後，造成綁定的隱藏欄位清空的異常
  - 變更檔案: 5 個
- **2018-03-06 11:16:16**: 修正$用法變成jBPM
  - 變更檔案: 1 個
- **2018-03-02 16:34:28**: 將全系統jQuery的使用變成jBPM
  - 變更檔案: 376 個
- **2018-03-01 14:29:54**: 增加複合式元件的按鈕文字描述共用方法
  - 變更檔案: 1 個
- **2018-03-01 09:26:24**: 修正多表單時，於chrome無法簽核的異常
- **2018-03-01 09:26:24**: 修正多表單時，於chrome無法簽核的異常
  - 變更檔案: 1 個
- **2018-02-27 17:11:16**: 增加向下相容Grid元件的getRowIndex的多種寫法與移除toArrayString方法提示
  - 變更檔案: 1 個
- **2018-02-27 17:10:01**: 修正當有絕對位置APP表單時，轉換RWD表單後開啟異常的議題
  - 變更檔案: 1 個
- **2018-02-27 17:09:15**: 修正RWD表單設計器切換垂直式與水平式後，無法修改元件代號的議題
  - 變更檔案: 1 個
- **2018-02-26 11:44:46**: 移除登錄BPM後，ifmFucntionLocation2的預設src屬性，避免相同畫面載入兩次狀況發生
- **2018-02-26 11:44:46**: 移除登錄BPM後，ifmFucntionLocation2的預設src屬性，避免相同畫面載入兩次狀況發生
  - 變更檔案: 1 個
- **2018-02-23 17:40:22**: 表單設計器樹狀樣式調整
  - 變更檔案: 1 個
- **2018-02-23 17:39:04**: 安裝密碼註冊錯誤提示調整
  - 變更檔案: 1 個
- **2018-02-23 10:06:48**: 增加複合式元件的placeholder功能
  - 變更檔案: 7 個
- **2018-02-23 10:05:23**: 修正RWD表單無法儲存的異常
  - 變更檔案: 1 個
- **2018-02-22 17:33:58**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-02-22 17:33:07**: 修改表單設計器的「顯示名稱」屬性變成「提示文字」
  - 變更檔案: 4 個
- **2018-02-22 14:26:25**: 修正發起流程頁面中「常用流程」分類中不顯示T100的流程
  - 變更檔案: 1 個
- **2018-02-21 17:40:25**: 修改撤銷流程頁面，將換行方式改用bpm-horizontal-line
- **2018-02-21 17:40:25**: 修改撤銷流程頁面，將換行方式改用bpm-horizontal-line
  - 變更檔案: 1 個
- **2018-02-21 13:29:54**: 修正絕對位置表單無法正常發單的議題
  - 變更檔案: 1 個
- **2018-02-21 11:01:00**: 修正BAM相關議題
  - 變更檔案: 3 個
- **2018-02-21 09:41:32**: 修改佈景主題的CSS路徑CustomCssLib/bpm-global-style-custom.css
  - 變更檔案: 1 個
- **2018-02-21 09:33:39**: 增加查詢維護樣版的mobileTemplate屬性
  - 變更檔案: 3 個
- **2018-02-21 09:32:45**: 統一自訂義的佈景主題設定.css檔案至CustomCssLib下
- **2018-02-21 09:32:45**: 統一自訂義的佈景主題設定.css檔案至CustomCssLib下
  - 變更檔案: 1 個
- **2018-02-12 17:32:27**: 修改自定義佈景主題邏輯，統一更新設定後動態匯出.css檔，可供其他客製頁面使用
  - 變更檔案: 4 個
- **2018-02-12 15:42:38**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-02-12 15:42:14**: 修改自訂佈景主題配色時的CSS檔案路徑至CustomCssLib之下
  - 變更檔案: 194 個
- **2018-02-12 09:14:05**: 增加檢查已經是RWD的表單不可以再選擇「匯入並轉換成RWD表單」的模式
  - 變更檔案: 1 個
- **2018-02-12 08:59:11**: 修正於表單腳本中嵌入自行的jQuery版本時，無法刷新GRID的議題
  - 變更檔案: 1 個
- **2018-02-09 10:33:52**: 修正表單中日期與時間元件的OOO_onchange()事件失效
  - 變更檔案: 1 個
- **2018-02-08 16:18:23**: 修正於Web表單設計器檢視表單資訊時無法正常開啟的議題
  - 變更檔案: 3 個
- **2018-02-08 16:17:07**: 同步更新模組使用的bpm-bootstrap-util.js檔案
  - 變更檔案: 4 個
- **2018-02-08 16:14:56**: 修正該改系統文字大小時，Grid的排版異常
  - 變更檔案: 6 個
- **2018-02-08 16:11:54**: 修正時間選取器可編輯輸入框的屬性設定
  - 變更檔案: 5 個
- **2018-02-07 09:02:11**: 增加轉換T100表單變RWD表單時的特殊轉換邏輯
  - 變更檔案: 4 個
- **2018-02-07 09:00:09**: 增加日期時間選取器的多語系
  - 變更檔案: 1 個
- **2018-02-01 14:47:02**: 修正第一關以後Gird資料清空的議題
  - 變更檔案: 1 個

### ChinRong (11 commits)

- **2018-03-29 10:20:02**: 修正議題
  - 變更檔案: 2 個
- **2018-03-26 10:33:06**: 修正APP議題
  - 變更檔案: 4 個
- **2018-03-23 14:31:02**: 修正行動表單多部門簽核異常
  - 變更檔案: 2 個
- **2018-03-23 12:07:13**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-03-23 12:02:22**: 補上漏掉的SQL
  - 變更檔案: 2 個
- **2018-03-23 10:22:09**: 修正絕對位置grid表單元件亂跑問題
  - 變更檔案: 2 個
- **2018-03-23 09:12:57**: 修正議題
  - 變更檔案: 3 個
- **2018-03-22 18:52:41**: 修正議題
  - 變更檔案: 14 個
- **2018-02-08 11:55:28**: 補上入口平台頁面漏掉的css與多語系
  - 變更檔案: 2 個
- **2018-02-06 16:42:24**: 修正議題
  - 變更檔案: 14 個
- **2018-02-01 11:41:22**: 修正議題
  - 變更檔案: 7 個

### 施廷緯 (4 commits)

- **2018-03-29 08:43:34**: 廷緯 修正進階查詢及表單設計師Time開窗偏移問題。
  - 變更檔案: 1 個
- **2018-03-23 18:23:17**: 恢復為前一版本
  - 變更檔案: 3 個
- **2018-03-23 16:26:16**: 2018/03/23 廷緯 修改進階查詢開窗會偏移問題。
  - 變更檔案: 1 個
- **2018-03-22 16:46:22**: 20180322 廷緯 調整(模糊查詢)追蹤流程頁面的進階查詢流程發起人無法正確搜尋的問題。
  - 變更檔案: 3 個

### shenLu (1 commits)

- **2018-03-28 16:21:48**: [A00-***********]修正BPM57版,組織設計師部門主管無法正常顯示的問題。
  - 變更檔案: 8 個

### Loren (5 commits)

- **2018-03-28 14:35:50**: 修正56版舊流程匯入時，Session Bean的JNDI Name轉換錯誤的問題
  - 變更檔案: 1 個
- **2018-03-28 10:02:05**: 更新Patch
  - 變更檔案: 1 個
- **2018-03-28 09:12:31**: 更新Patch
  - 變更檔案: 1 個
- **2018-03-27 11:53:49**: 更新Patch
- **2018-03-27 11:53:35**: 更新Patch
  - 變更檔案: 1 個

### lorenchang (19 commits)

- **2018-03-26 15:37:31**: Release相關檔案路徑調整，移至git project:bpmApp
  - 變更檔案: 17 個
- **2018-03-26 11:01:07**: 修正進階查詢搜尋流程發起人失效問題(程式錯誤修正)，代廷緯將v56版程式Merge至v57
  - 變更檔案: 3 個
- **2018-03-23 14:37:16**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-03-23 14:36:46**: 更新Patch
  - 變更檔案: 1 個
- **2018-03-23 11:31:13**: 解決其它Client調用產品Session Bean時的異常
  - 變更檔案: 4 個
- **2018-03-20 16:59:07**: 修正Merge後多語系異常
  - 變更檔案: 1 個
- **2018-03-20 09:07:11**: v57Merge(5652-5653)
  - 變更檔案: 232 個
- **2018-03-16 19:04:02**: 更新為5.7.1.1版patch
  - 變更檔案: 1 個
- **2018-03-16 12:55:43**: 更改Update SQL命名規則：增加_Check
  - 變更檔案: 6 個
- **2018-03-16 09:29:51**: 更改Http Header使用的Server及X-Powered-By屬性內容
  - 變更檔案: 2 個
- **2018-03-16 09:22:37**: 增加以Windows服務啟動的相關設定及程式
  - 變更檔案: 5 個
- **2018-03-13 15:06:40**: 修正刪除BouncyCastle後導致開發環境異常的問題
  - 變更檔案: 3 個
- **2018-03-12 22:16:54**: 修正無法由BPM開啟T100作業的異常
  - 變更檔案: 4 個
- **2018-03-02 16:47:58**: 更新Patch
  - 變更檔案: 1 個
- **2018-02-26 09:36:20**: 移除用不到的RMI Method:lookup(Class<?> pClass)及相關設定
  - 變更檔案: 4 個
- **2018-02-22 14:49:12**: 調整update sql命名及備註
  - 變更檔案: 6 個
- **2018-02-12 15:09:21**: 更新多語系檔並刪除重覆項目
  - 變更檔案: 2 個
- **2018-02-12 12:19:15**: v57Merge(5641-5652)
  - 變更檔案: 1355 個
- **2018-02-01 16:23:36**: 修正SessionBeanAppliction的欄位調整導致SessionBean無法正常運作的問題
  - 變更檔案: 1 個

### 張詠威 (1 commits)

- **2018-02-26 15:39:10**: 新增獨立模組設定
  - 變更檔案: 6 個

## 詳細變更記錄

### 1. 5711問題修正
- **Commit ID**: `84df673757abebdc75216d43b3c5d2f07c6a48ab`
- **作者**: jerry1218
- **日期**: 2018-03-29 16:53:28
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/OpenWin.js`

### 2. 修正未分類流程的顯示名稱
- **Commit ID**: `190dffada5bbcc0e9c4fd0ac785b423e9d3a4cf0`
- **作者**: Gaspard
- **日期**: 2018-03-29 11:06:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 3. 修正議題
- **Commit ID**: `b9c579ccf6d91b74992d083388a77797d1c26a3f`
- **作者**: ChinRong
- **日期**: 2018-03-29 10:20:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 4. 廷緯 修正進階查詢及表單設計師Time開窗偏移問題。
- **Commit ID**: `293fa9184683d91ecd3196f791fc53b70f18aebd`
- **作者**: 施廷緯
- **日期**: 2018-03-29 08:43:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/popup.js`

### 5. 移除我的關注-備註功能
- **Commit ID**: `4c29b96aaae6352b83abdfcb9fd0fc7dba6f57e4`
- **作者**: jerry1218
- **日期**: 2018-03-28 16:37:08
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessUserFocus.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessInstanceForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcher.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_Oracle_1.sql`

### 6. [A00-***********]修正BPM57版,組織設計師部門主管無法正常顯示的問題。
- **Commit ID**: `1b657774d29325a5ef551d323f4d0e19e84da5ff`
- **作者**: shenLu
- **日期**: 2018-03-28 16:21:48
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/AddUserToUnitDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/table/UserTableController.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 7. 修正56版舊流程匯入時，Session Bean的JNDI Name轉換錯誤的問題
- **Commit ID**: `1fe9be9c1c60443b384fc35b21a7396bb9268a52`
- **作者**: Loren
- **日期**: 2018-03-28 14:35:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/action/OpenFromXMLAction.java`

### 8. 更新Patch
- **Commit ID**: `55fe910d0d145f37e276adfd3b0c4a8192e54cee`
- **作者**: Loren
- **日期**: 2018-03-28 10:02:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 9. 更新Patch
- **Commit ID**: `d59b2c54fbddec2d1263f6a753a4a91e2f49afe3`
- **作者**: Loren
- **日期**: 2018-03-28 09:12:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 10. 轉派員工工作-員工名稱 多語系修改
- **Commit ID**: `485696dce48cc14fa24e03cc14b029fd2b4f2396`
- **作者**: jerry1218
- **日期**: 2018-03-27 15:46:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`

### 11. 更新Patch
- **Commit ID**: `97c4d6b24ff675f5c272a1ccf4bdb2fb3df30b67`
- **作者**: Loren
- **日期**: 2018-03-27 11:53:49
- **變更檔案數量**: 0

### 12. 更新Patch
- **Commit ID**: `03023ea113cba40aaeca53cc6f77080a96099394`
- **作者**: Loren
- **日期**: 2018-03-27 11:53:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 13. 1.修正程式權限管理功能異常 2.修正一般使用者menu系統設定就算有項目可顯示但依然不會出現問題
- **Commit ID**: `b44d5c716bd3a2982e0ce9152315fda59275f24c`
- **作者**: jerry1218
- **日期**: 2018-03-26 18:44:06
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql`

### 14. Release相關檔案路徑調整，移至git project:bpmApp
- **Commit ID**: `3facc3583c09ce90ced8b23e80c07bf4734d07a4`
- **作者**: lorenchang
- **日期**: 2018-03-26 15:37:31
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_SQLServer.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.2.1_updateSQL_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.2.1_updateSQL_SQLServer.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_SQLServer.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_SQLServer.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.2_updateSQL_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_SQLServer.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.7.0.1_updateSQL_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.7.0.1_updateSQL_SQLServer.sql`

### 15. 修正進階查詢搜尋流程發起人失效問題(程式錯誤修正)，代廷緯將v56版程式Merge至v57
- **Commit ID**: `b248ddd937bf0e72efcf1fec0f1d83f214d78b85`
- **作者**: lorenchang
- **日期**: 2018-03-26 11:01:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 16. 修正APP議題
- **Commit ID**: `089688636791cd192cb385e3574d1495ad9d00fb`
- **作者**: ChinRong
- **日期**: 2018-03-26 10:33:06
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`

### 17. 恢復為前一版本
- **Commit ID**: `95fb2fb27c34556308c171bc044af20bb53d4b28`
- **作者**: 施廷緯
- **日期**: 2018-03-23 18:23:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 18. 修正設定片語頁面alert忘了拿掉
- **Commit ID**: `14bcaf3d31e6a1fd73416638c46134379b4a5458`
- **作者**: jerry1218
- **日期**: 2018-03-23 17:35:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`

### 19. 2018/03/23 廷緯 修改進階查詢開窗會偏移問題。
- **Commit ID**: `66c2f734febfe83f642430c8a47d11cc5b3ddb92`
- **作者**: 施廷緯
- **日期**: 2018-03-23 16:26:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/popup.js`

### 20. 修正Dash Board圖片錯誤
- **Commit ID**: `5bb03ff55ee18d0da5c37a99f011331a7dfa24d2`
- **作者**: jerry1218
- **日期**: 2018-03-23 15:09:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 21. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a5cc74649c83d21aee60cce995b0f119de0632d1`
- **作者**: lorenchang
- **日期**: 2018-03-23 14:37:16
- **變更檔案數量**: 0

### 22. 更新Patch
- **Commit ID**: `a3553926988948a1879dfb6c308b137b6ec13fb6`
- **作者**: lorenchang
- **日期**: 2018-03-23 14:36:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 23. 修正行動表單多部門簽核異常
- **Commit ID**: `8a22ef2b7db12499fa75611d13ab79cb26e4c06f`
- **作者**: ChinRong
- **日期**: 2018-03-23 14:31:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 24. 1.V57測試問題修改 2.智能示警及重要流程img置換
- **Commit ID**: `e5be782502066c42941dd3415e46316badd07862`
- **作者**: jerry1218
- **日期**: 2018-03-23 14:10:56
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-critical-Process-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-critical-Process.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-focus-Process-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-focus-Process.png`

### 25. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `4b2a41e2c3b9c3ca6543c73034fff8041ae9e42a`
- **作者**: ChinRong
- **日期**: 2018-03-23 12:07:13
- **變更檔案數量**: 0

### 26. 補上漏掉的SQL
- **Commit ID**: `01fb295c561cc54c1e13854f3b22d6769e660a38`
- **作者**: ChinRong
- **日期**: 2018-03-23 12:02:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`

### 27. 解決其它Client調用產品Session Bean時的異常
- **Commit ID**: `996be3a37342cb759771bb7b10177cb241b371a3`
- **作者**: lorenchang
- **日期**: 2018-03-23 11:31:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/domain/configuration/application-roles.properties`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/domain/configuration/application-users.properties`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/application-roles.properties`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/application-users.properties`

### 28. 修正絕對位置grid表單元件亂跑問題
- **Commit ID**: `8662ba848ff56042ef62f01338e90ee9c17ebbf3`
- **作者**: ChinRong
- **日期**: 2018-03-23 10:22:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 29. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a61b5d359040b55a6b50791f8dc4d9b8a1938b5b`
- **作者**: Gaspard
- **日期**: 2018-03-23 09:38:58
- **變更檔案數量**: 0

### 30. 修正SubTab可編輯的屬性、修正Title元件無圖片時直接給空的SRC
- **Commit ID**: `3a6c60b9b1f2002bf1ddaa039c8df79129876c91`
- **作者**: Gaspard
- **日期**: 2018-03-23 09:38:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 31. 修正議題
- **Commit ID**: `7b58b14b79668be6511d492e48abec1faf596192`
- **作者**: ChinRong
- **日期**: 2018-03-23 09:12:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_SQLServer.sql`

### 32. 修正議題
- **Commit ID**: `e844bb2617399204b89ef2c9e4cc7f00d2aaea28`
- **作者**: ChinRong
- **日期**: 2018-03-22 18:52:41
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 33. 開啟DoubleTextBox元件供使用
- **Commit ID**: `a35fd6f8ec8bb308195e336e6e32e57769862ca3`
- **作者**: Gaspard
- **日期**: 2018-03-22 18:02:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 34. 1.關鍵流程label改為智能示警 2.檢視關卡資訊圖案更改
- **Commit ID**: `11c957b7d6549dd82291cc6bd7135740172d5aac`
- **作者**: jerry1218
- **日期**: 2018-03-22 17:23:17
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`

### 35. 20180322 廷緯 調整(模糊查詢)追蹤流程頁面的進階查詢流程發起人無法正確搜尋的問題。
- **Commit ID**: `5bd5c9951503894576f10fd96e99588c36aa6f18`
- **作者**: 施廷緯
- **日期**: 2018-03-22 16:46:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 36. 5711發版測試異常修正
- **Commit ID**: `bd3682c2378af5485d912245961ecd127f1e64ef`
- **作者**: jerry1218
- **日期**: 2018-03-22 16:18:27
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_2_Check.sql`

### 37. 將jBPM.toJSON統一改用JSON.stringify
- **Commit ID**: `0f060928b8153026f3f645066990167f57b6f49b`
- **作者**: Gaspard
- **日期**: 2018-03-22 09:11:27
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CreateSysLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`

### 38. 修正Merge後多語系異常
- **Commit ID**: `a4fd0a62f6be8bb7bd530f29c4284817c102391b`
- **作者**: lorenchang
- **日期**: 2018-03-20 16:59:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 39. 隱藏DoubleTextBox與TripleTextBox元件
- **Commit ID**: `6b9e0ce92132e926a25f9c17cb686e51ab86b6d7`
- **作者**: Gaspard
- **日期**: 2018-03-20 11:30:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 40. 我的關注功能修改
- **Commit ID**: `da8bbc5085e2e706d860d4561a976fe50da43c40`
- **作者**: jerry1218
- **日期**: 2018-03-20 11:04:50
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/heart-check.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/heart-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/heart.png`

### 41. v57Merge(5652-5653)
- **Commit ID**: `686cf6df4dcc7e112bf3ac23317134a7ac5c57a4`
- **作者**: lorenchang
- **日期**: 2018-03-20 09:07:11
- **變更檔案數量**: 232
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/CMManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessViewController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/processpackage/ProcessPackageCategoryMultiZh.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/ProcessPackageCategoryManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISOPageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/access_control/ISODocCmItem.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/AbsDocument.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/DocCategory.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/DocCategory.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/Document.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/Attachment.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/BarcodeElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ImageElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/LinkElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/OutputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/OrmDaoSupport.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/ISODocRefListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/ListReaderFacade.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocRefListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IFormDefinitionDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/listreader/ISOPageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/listreader/ISOPageListReaderBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5653.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookDeptData.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkItemBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/UserFormValueBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/AttachPermissionBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/AttachmentBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/DeleteFileBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/DinwhaleUserBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/DownloadFileBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/OpenWinBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/UploadFileBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/UploadFileInfoBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/UserClientBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/AttachPermissionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/AttachmentBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/DeleteFileBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/DownloadFileBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormDefinitionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OpenWinBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/UploadFileBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/UserServiceBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/FormV2.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileFormV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystemV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocCmItemViewer.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/AttachmentContainer.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/FormContainer.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/FormElementContainer.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/SubElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BPMPerformRequestTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/TimeZoneManager.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileWorkItemServiceTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/FormElementUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/pdfJs/pdf-bpm.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/pdfJs/pdf.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/pdfJs/pdf.worker.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/light_sign_click.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF04B\345\212\240\347\217\255\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF05\345\212\240\347\217\255\350\250\210\345\212\203\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF17\351\212\267\345\201\207\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF20\345\207\272\345\267\256\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF21\345\207\272\345\267\256\347\231\273\350\250\230.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF24\350\252\277\350\226\252\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF28\344\272\272\345\212\233\351\234\200\346\261\202\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF31\346\213\233\350\201\230\350\250\210\347\225\253.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF32\346\207\211\350\201\230\344\272\272\345\223\241\351\235\242\350\251\246.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF33\346\207\211\350\201\230\344\272\272\345\223\241\347\255\206\350\251\246.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF50\347\217\255\346\254\241\350\256\212\346\233\264\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF60\350\254\233\345\270\253\350\263\207\346\240\274\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF62\345\237\271\350\250\223\351\240\220\347\256\227\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF63\345\237\271\350\250\223\351\234\200\346\261\202\346\216\241\351\233\206.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF64\345\237\271\350\250\223\350\250\210\347\225\253\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF66\345\237\271\350\250\223\350\251\225\344\274\260.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF74\350\263\207\346\272\220\347\224\263\351\240\230.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF75\350\263\207\346\272\220\346\255\270\351\202\204.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF04B\345\212\240\347\217\255\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF23B\350\252\277\350\201\267\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF26B\347\215\216\346\207\262\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF30\350\243\234\345\210\267\345\215\241\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF71\350\253\213\345\201\207\347\224\263\350\253\213(\346\211\271\351\207\217).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\346\226\260\345\242\236\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/process-default/template/\345\205\251\351\227\234\345\210\266\347\260\275\346\240\270\346\250\243\346\235\277.bpmn"`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.3_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.3_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_SQLServer.sql`

### 42. 更新為5.7.1.1版patch
- **Commit ID**: `52b3706eb3f178f1447c965af3e8b2b59a371fe4`
- **作者**: lorenchang
- **日期**: 2018-03-16 19:04:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 43. 修正$.變成jBPM.
- **Commit ID**: `d63dd7692b269d5b9b23c7e1e289dd034f0461d8`
- **作者**: Gaspard
- **日期**: 2018-03-16 15:23:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 44. 新增dashboard-我的關注功能
- **Commit ID**: `31384957dc5b54094f0efc5f58f0114900784a87`
- **作者**: jerry1218
- **日期**: 2018-03-16 14:39:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 45. 更改Update SQL命名規則：增加_Check
- **Commit ID**: `135acbb95060c0ea9740ef7bc7e9d6a3d50fd92e`
- **作者**: lorenchang
- **日期**: 2018-03-16 12:55:43
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_1.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_2.sql`

### 46. 新增我的關注支援excel匯出(JSP)
- **Commit ID**: `46d43df7a4b9c4b67bcf9d38f0b0cb9f4d401e68`
- **作者**: jerry1218
- **日期**: 2018-03-16 09:53:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`

### 47. 新增我的關注支援匯出excel
- **Commit ID**: `311b658e923afde666c97f4d034e6a2d434d0b79`
- **作者**: jerry1218
- **日期**: 2018-03-16 09:52:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 48. 更改Http Header使用的Server及X-Powered-By屬性內容
- **Commit ID**: `19270635fe9f4bbb8a0c4fcef71e8aa2ea79843d`
- **作者**: lorenchang
- **日期**: 2018-03-16 09:29:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 49. 增加以Windows服務啟動的相關設定及程式
- **Commit ID**: `f525352c3345534b717673b054234d5dcd6d6aef`
- **作者**: lorenchang
- **日期**: 2018-03-16 09:22:37
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/jboss-cli.bat`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/service/amd64/wildfly-service.exe`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/service/service.bat`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/service/wildfly-mgr.exe`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/service/wildfly-service.exe`

### 50. 新增關注流程功能.修正右上簽核相關icon大小螢幕切換顯示異常
- **Commit ID**: `55951940dda301ac76dbe4ba9176554398963f97`
- **作者**: jerry1218
- **日期**: 2018-03-15 17:18:14
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessInstanceForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`

### 51. 縮小菜單ICON大小
- **Commit ID**: `1039a76bb91de9df726642afa59d0c5463690e38`
- **作者**: Gaspard
- **日期**: 2018-03-14 12:07:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 52. 我的關注
- **Commit ID**: `f1ad4f3c1632ce2438a0c9014d3c8de8a4c1ee72`
- **作者**: jerry1218
- **日期**: 2018-03-13 17:52:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`

### 53. 我的關注欣曾.圖片變更.T100簽核模板更新
- **Commit ID**: `c945e15c446050855c2ae85cb2bc73b56cf7f99c`
- **作者**: jerry1218
- **日期**: 2018-03-13 17:47:10
- **變更檔案數量**: 58
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IProcessUserFocusDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBProcessUserFocusDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcher.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/abort-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/abort.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/add-activity-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/add-activity.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/arrow-left-o-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/arrow-left-o.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/arrow-right-o-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/arrow-right-o.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/check-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/check.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/invoke-refer-process-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/invoke-refer-process.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/invoked-process-data-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/invoked-process-data.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/notice-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/notice.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/print-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/print.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/reassign-activity-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/reassign-activity.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/reexecute-activity-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/reexecute-activity.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/reget-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/reget.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/save-draft-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/save-draft.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/save-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/save.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/save_hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/send-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/send.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/stop-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/stop.png`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/process-default/template/\345\205\251\351\227\234\345\210\266\347\260\275\346\240\270\346\250\243\346\235\277.bpmn"`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_1.sql`

### 54. 修正刪除BouncyCastle後導致開發環境異常的問題
- **Commit ID**: `5dee1380305cbe868837fd6c6b6d7d7a6b6ba224`
- **作者**: lorenchang
- **日期**: 2018-03-13 15:06:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/BouncyCastle/bcprov-jdk15on-1.56.jar`

### 55. 修正無法由BPM開啟T100作業的異常
- **Commit ID**: `c97c9f793251d173573dd98fce358cf85e242d37`
- **作者**: lorenchang
- **日期**: 2018-03-12 22:16:54
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/BouncyCastle/bcprov-jdk15on-150.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/jre/lib/security/US_export_policy.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/jre/lib/security/local_policy.jar`

### 56. 修正查詢維護樣版需要用引用到的程式路徑
- **Commit ID**: `8f8db39bf2e401a635be2be2f35e9113c8579536`
- **作者**: Gaspard
- **日期**: 2018-03-12 14:41:02
- **變更檔案數量**: 260
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/BpmTable.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/bootstrap/bootstrap-3.3.5.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/bpm-calendar.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/bpm-global-style.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/bpm-style.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/action-to-top.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/add-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/add.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/arrow-double-left.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/arrow-double-right.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/back-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/back.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/back_hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/calendar.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/end-left.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/end-right.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/error-page-image.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/error-page-oops.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/excel-export-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/excel-export.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/save-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/save.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/search.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/select.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/time.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/trashcan-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/image/trashcan.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/BpmCalendar.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/BpmTable.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/CustomDataChooser.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/ModalDialog.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/OpenWin.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/QueryTemplate.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrap-3.3.4.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/bpm-bootstrap-util.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/jquery-1.11.3.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/jqueryUI/jquery-ui-1.11.4.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/jqueryUI/jquery.ui-contextmenu.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/BPMModule/js/json2.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/CannotAccessWarnning.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CannotAccessWarnning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/BpmTable.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/QueryDesinger.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/bootstrap/bootstrap-3.3.5.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/bpm-calendar.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/bpm-global-style.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/bpm-style.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/action-to-top.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/add-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/add-sub.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/add.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/arrow-double-left.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/arrow-double-right.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/back-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/back.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/back_hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/calendar.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/edit.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/end-left.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/end-right.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/error-page-image.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/error-page-oops.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/excel-export-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/excel-export.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/save-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/save.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/search.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/select.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/time.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/trashcan-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/trashcan-sub.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/image/trashcan.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/AccessRight.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/BpmCalendar.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/BpmTable.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/CustomDataChooser.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/ModalDialog.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/OpenWin.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/QueryTemplate.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrap-3.3.4.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bpm-bootstrap-util.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/jquery-1.11.3.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/jqueryUI/jquery-ui-1.11.4.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/jqueryUI/jquery.ui-contextmenu.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/js/json2.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_en_US.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_zh_CN.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_zh_TW.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/CannotAccessWarnning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/BpmTable.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/QueryDesinger.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/bootstrap/bootstrap-3.3.5.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/bpm-calendar.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/bpm-global-style.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/bpm-style.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/action-to-top.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/add-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/add-sub.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/add.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/arrow-double-left.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/arrow-double-right.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/back-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/back.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/calendar.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/edit.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/end-left.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/end-right.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/error-page-image.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/error-page-oops.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/excel-export-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/excel-export.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/save-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/save.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/search.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/select.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/time.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/trashcan-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/trashcan-sub.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/image/trashcan.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/AccessRight.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/BpmCalendar.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/BpmTable.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/bootstrap/bootstrap-3.3.4.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/bpm-bootstrap-util.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/jquery-1.11.3.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/jqueryUI/jquery-ui-1.11.4.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/jqueryUI/jquery.ui-contextmenu.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/js/json2.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/rescBunble/QueryTemplate_en_US.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/rescBunble/QueryTemplate_zh_CN.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/CustomModule/rescBunble/QueryTemplate_zh_TW.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/CannotAccessWarnning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/BpmTable.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/QueryDesinger.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/bootstrap/bootstrap-3.3.5.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/bpm-calendar.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/bpm-global-style.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/bpm-style.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/jquery-ui.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/action-to-top.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/add-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/add-sub.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/add.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/arrow-double-left.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/arrow-double-right.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/back-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/back.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/calendar.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/edit.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/end-left.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/end-right.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/error-page-image.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/error-page-oops.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/excel-export-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/excel-export.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/save-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/save.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/search.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/select.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/time.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/trashcan-hover.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/trashcan-sub.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/image/trashcan.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/AccessRight.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/BpmCalendar.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/BpmTable.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/OpenWin.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/QueryTemplate.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/bootstrap/bootstrap-3.3.4.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/bpm-bootstrap-util.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/jquery-1.11.3.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/jqueryUI/jquery-ui-1.11.4.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/jqueryUI/jquery.ui-contextmenu.min.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/js/json2.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/rescBunble/QueryTemplate_en_US.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/rescBunble/QueryTemplate_zh_CN.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/ISOModule/rescBunble/QueryTemplate_zh_TW.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/css/QueryDesinger.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/js/AccessRight.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/CustomModule/js/QueryTemplate.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_en_US.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_zh_CN.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_zh_TW.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/image/add-sub.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/image/edit.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/BPMModule/image/trashcan-sub.png`

### 57. 修正開窗後的查詢條件開窗樣式
- **Commit ID**: `f504313beef0a3fa553757ca41f99e64def40ad1`
- **作者**: Gaspard
- **日期**: 2018-03-08 10:09:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 58. 修正於發起流程清單頁面，點擊「顯示全部」功能失效的議題
- **Commit ID**: `9f0bc74a249be97ab523c3dcd671d9410d4e74da`
- **作者**: Gaspard
- **日期**: 2018-03-08 10:04:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`

### 59. 修正將TTTTTTTTTT取代成$$$$$$
- **Commit ID**: `7b2c510ca5e853cd82c266442616798c92da053d`
- **作者**: Gaspard
- **日期**: 2018-03-08 09:59:08
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CreateSysLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExpenseAccountItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainCombinationService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainExternalService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 60. 修改查詢維護樣版的錯誤訊息樣式與產品相同
- **Commit ID**: `5a2b34ddb7899aaa5494bfd463f456ff87b26417`
- **作者**: Gaspard
- **日期**: 2018-03-07 14:09:25
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/CannotAccessWarnning.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/image/error-page-image.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/image/error-page-oops.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CannotAccessWarnning.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/image/error-page-image.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/image/error-page-oops.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/CannotAccessWarnning.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/image/error-page-image.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/image/error-page-oops.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/CannotAccessWarnning.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/ISOModule/image/error-page-image.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/ISOModule/image/error-page-oops.png`

### 61. 修改Grid自帶修改控制項的代號與方法統一為edit(非update)
- **Commit ID**: `f38ab490f4e86d5b6359c2c89bd00bba420702bc`
- **作者**: Gaspard
- **日期**: 2018-03-07 11:10:23
- **變更檔案數量**: 0

### 62. 修改Grid自帶修改控制項的代號與方法統一為edit(非update)
- **Commit ID**: `0cb7ccd87de8f10e2e22ce1d4ed09b8c54f9443a`
- **作者**: Gaspard
- **日期**: 2018-03-07 11:10:23
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 63. 移除多餘alert訊息
- **Commit ID**: `28a97a49463c977c6b46fccd206f0963b3d21d39`
- **作者**: Gaspard
- **日期**: 2018-03-06 11:50:17
- **變更檔案數量**: 0

### 64. 移除多餘alert訊息
- **Commit ID**: `e0546a7255d2dc7ec8b7ef56b64242f40f63b45f`
- **作者**: Gaspard
- **日期**: 2018-03-06 11:50:17
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/QueryTemplate.js`

### 65. 修正於表單中隱藏Gird元件後，造成綁定的隱藏欄位清空的異常
- **Commit ID**: `826f599ed99e30173d8f9a71c0e6e64cb03faf98`
- **作者**: Gaspard
- **日期**: 2018-03-06 11:17:19
- **變更檔案數量**: 0

### 66. 修正於表單中隱藏Gird元件後，造成綁定的隱藏欄位清空的異常
- **Commit ID**: `6e809f05d6ac25830dacf65fb059bfe0490d7389`
- **作者**: Gaspard
- **日期**: 2018-03-06 11:17:19
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 67. 修正$用法變成jBPM
- **Commit ID**: `59eca97b3e016130fc758bbaa9d1a706b825b0c0`
- **作者**: Gaspard
- **日期**: 2018-03-06 11:16:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 68. 更新Patch
- **Commit ID**: `7be299a511587b2fd49505e0ebe0fa819fd5d3c2`
- **作者**: lorenchang
- **日期**: 2018-03-02 16:47:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 69. 將全系統jQuery的使用變成jBPM
- **Commit ID**: `25f9cd1e754ab6e5d3d45cbee1b89ad36ba81d8a`
- **作者**: Gaspard
- **日期**: 2018-03-02 16:34:28
- **變更檔案數量**: 376
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/EMSI01.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/EMSProgram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CreateSysLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocCategory/ManageDocCategoryMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISODocumentsList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOEffectInvalidList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOReleaseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmApp.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracingLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPublicLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManageLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItemLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileIntegrate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/NotRegisterApp.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteReferProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExpenseAccountItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ViewReassignHistory.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewDecisionActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/CreateProcessModule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/ManageProcessModuleMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/InvokeProcessDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/PerformWorkItemDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/ProcessPerformanceMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchOrgData/ViewOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainAnalyzeService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainCombinationService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainExternalService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/ServiceRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ChooseDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/WebViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/AppModalDialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BPMProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTrace.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppSetting.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmWorkItem.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChatDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChateUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMWorkItem.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmAppGetResource.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmAppMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolWorkItem.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BPMProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTracePerformed.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmWorkItem.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmWorkPublic.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/form/AppModalDialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/system/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/system/BpmMobilePublic.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileTool.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAbsoluteNewStyle.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BPMProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkItem.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkPublic.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTool.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/AppModalDialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobilePublic.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileTool.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileMethodControl.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/NewTiptop/NewTiptop.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/doResize.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rsrcBundleManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-undoManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/jGrid/jgrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/jGrid/jqgrid43/js/i18n/grid.locale-hr1250.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/jGrid/jqgrid43/src/i18n/grid.locale-hr1250.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/materialize/materialize.min.js`

### 70. 增加複合式元件的按鈕文字描述共用方法
- **Commit ID**: `838f6f2f26b401a174163ef7194236f940fc1c4a`
- **作者**: Gaspard
- **日期**: 2018-03-01 14:29:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 71. 修正轉派員工工作FloatButton異常
- **Commit ID**: `f193d22a0956f59d7ecd76c389c32ce9a8ebc452`
- **作者**: jerry1218
- **日期**: 2018-03-01 14:27:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 72. 新增我的關注domain&Service 修正1.頁面清單流程名稱最多顯示行數 2.administrator監控流程關注&重要異常 3.轉派員工工作頁面調整
- **Commit ID**: `7536ca808efb337c83b1a17458cf733ecb44e01a`
- **作者**: jerry1218
- **日期**: 2018-03-01 14:21:53
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessDispatcherDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessUserFocus.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/DAOFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IProcessUserFocusDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/JDBCDAOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBDAOFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBProcessUserFocusDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcher.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_Oracle_1.sql`

### 73. 修正多表單時，於chrome無法簽核的異常
- **Commit ID**: `a4e109a473cebf991925469a1a84b4ed762309db`
- **作者**: Gaspard
- **日期**: 2018-03-01 09:26:24
- **變更檔案數量**: 0

### 74. 修正多表單時，於chrome無法簽核的異常
- **Commit ID**: `04ed044ab367b183759ae64fcac14d09f69e9c4f`
- **作者**: Gaspard
- **日期**: 2018-03-01 09:26:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 75. 增加向下相容Grid元件的getRowIndex的多種寫法與移除toArrayString方法提示
- **Commit ID**: `589b3398e5327b13c39bbdf5fa3058936d193481`
- **作者**: Gaspard
- **日期**: 2018-02-27 17:11:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 76. 修正當有絕對位置APP表單時，轉換RWD表單後開啟異常的議題
- **Commit ID**: `b1e8232ce9c5b19b3dffe4ca8cfefbc41ab364dd`
- **作者**: Gaspard
- **日期**: 2018-02-27 17:10:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 77. 修正RWD表單設計器切換垂直式與水平式後，無法修改元件代號的議題
- **Commit ID**: `0a4b3b7b457cdcb6cbf96136dc1b2cb4381eb41d`
- **作者**: Gaspard
- **日期**: 2018-02-27 17:09:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 78. 新增獨立模組設定
- **Commit ID**: `3d91a4ddcc346cd69e2b8ec05c3cdc8184e19253`
- **作者**: 張詠威
- **日期**: 2018-02-26 15:39:10
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WorkflowServerManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 79. 移除登錄BPM後，ifmFucntionLocation2的預設src屬性，避免相同畫面載入兩次狀況發生
- **Commit ID**: `3e42cb620d2c9d803a662b8fe398249a0900f6f5`
- **作者**: Gaspard
- **日期**: 2018-02-26 11:44:46
- **變更檔案數量**: 0

### 80. 移除登錄BPM後，ifmFucntionLocation2的預設src屬性，避免相同畫面載入兩次狀況發生
- **Commit ID**: `2a153a7730a776d3ea7c858630862f79dfa92034`
- **作者**: Gaspard
- **日期**: 2018-02-26 11:44:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 81. 1修正merge後Critical邏輯變更所導致的原程式問題 2.修正匯出excel可用時間空白問題 3.修正關注項目維護作業JSP merge後的異常
- **Commit ID**: `836a5f1ef47176d1514357f5e0cde5b2aa4028ef`
- **作者**: jerry1218
- **日期**: 2018-02-26 09:40:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/CriticalMessageViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`

### 82. 移除用不到的RMI Method:lookup(Class<?> pClass)及相關設定
- **Commit ID**: `12be3d32fc7bfc1bf83db484cb302484839a3f6e`
- **作者**: lorenchang
- **日期**: 2018-02-26 09:36:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/DocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RmiHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties`

### 83. 表單設計器樹狀樣式調整
- **Commit ID**: `cf702c9223adb28b9a483ebaace5dbd1a6ab2734`
- **作者**: Gaspard
- **日期**: 2018-02-23 17:40:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`

### 84. 安裝密碼註冊錯誤提示調整
- **Commit ID**: `25f2210951dd36b37523dbc332754cca562f95d2`
- **作者**: Gaspard
- **日期**: 2018-02-23 17:39:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java`

### 85. 增加複合式元件的placeholder功能
- **Commit ID**: `c5b09515208353928d5db0cab72c7c10576ded9f`
- **作者**: Gaspard
- **日期**: 2018-02-23 10:06:48
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ComplexElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 86. 修正RWD表單無法儲存的異常
- **Commit ID**: `58999f76018ee2b9b584d438ac18088fc67a96b0`
- **作者**: Gaspard
- **日期**: 2018-02-23 10:05:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`

### 87. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `89e7af63f0108c21083cb009b392494db55cfe83`
- **作者**: Gaspard
- **日期**: 2018-02-22 17:33:58
- **變更檔案數量**: 0

### 88. 修改表單設計器的「顯示名稱」屬性變成「提示文字」
- **Commit ID**: `90260d8e2fad82be0c5013bbe9ec99ca97352a00`
- **作者**: Gaspard
- **日期**: 2018-02-22 17:33:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 89. 修改關注流程merge到5652後邏輯的變更導致的異常
- **Commit ID**: `6c26c3e5ae0e6e916cfcba8c5faa938750136e0f`
- **作者**: jerry1218
- **日期**: 2018-02-22 17:10:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/CriticalMessageViewer.java`

### 90. 1.修正個人化首頁-重要流程數量計算錯誤問題 2.流程內容-完整流程頁面修改 3.微調SQL註冊器頁面 4.修正Template.jsp對於個人化首頁的因應 5.修正重啟服務功能SQL的order by
- **Commit ID**: `2ba55e7fd0ca919feae267deb5821353bf6accaf`
- **作者**: jerry1218
- **日期**: 2018-02-22 16:10:56
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SuspendedInvokeActListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`

### 91. 調整update sql命名及備註
- **Commit ID**: `9d99f171e81dea30cfdcfde5ce90cd3577b82522`
- **作者**: lorenchang
- **日期**: 2018-02-22 14:49:12
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_for_5.6.5.2_MSSQL.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_for_5.6.5.2_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_for_5.6.5.2_MSSQL.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_useForCritical_MSSQL.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_for_5.6.5.2_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_useForCritical_Oracle.sql`

### 92. 修正發起流程頁面中「常用流程」分類中不顯示T100的流程
- **Commit ID**: `e78aa2329768b20ca5fc350dcd54f79417dff645`
- **作者**: Gaspard
- **日期**: 2018-02-22 14:26:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/CommonProcessPkgListReader.java`

### 93. 修改撤銷流程頁面，將換行方式改用bpm-horizontal-line
- **Commit ID**: `b99fd1e080376c6b9832233615e8d48f555c24b5`
- **作者**: Gaspard
- **日期**: 2018-02-21 17:40:25
- **變更檔案數量**: 0

### 94. 修改撤銷流程頁面，將換行方式改用bpm-horizontal-line
- **Commit ID**: `e60ecf20d5b42a15522d068270359bc654bb1f96`
- **作者**: Gaspard
- **日期**: 2018-02-21 17:40:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`

### 95. 修改5701 updateSQL檔案名稱&移除表單多語系功能連結
- **Commit ID**: `8a137abfcb28c86e7cb17a3b374347968cda9a4a`
- **作者**: jerry1218
- **日期**: 2018-02-21 16:24:16
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.0.1_DDL_for_5.6.4.1_MSSQL.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.0.1_DDL_for_5.6.4.1_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.0.1_DML_for_5.6.4.1_MSSQL.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.0.1_DML_for_5.6.4.1_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.0.1_DML_useForCritical_MSSQL.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.0.1_DML_useForCritical_Oracle.sql`

### 96. 搬移Critical SQL&修正單選及多選窗分頁出現時機
- **Commit ID**: `19f4446c7d301446fa0e8697b7030054d963d214`
- **作者**: jerry1218
- **日期**: 2018-02-21 15:59:02
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@criticalModule/5.6.4.1_createSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@criticalModule/5.6.4.1_createSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@criticalModule/5.7.0.1_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@criticalModule/5.7.0.1_updateSQL_Oracle.sql`

### 97. 修正絕對位置表單無法正常發單的議題
- **Commit ID**: `8f08d22f8a4b196d3d5393338bf11cc80c03d21b`
- **作者**: Gaspard
- **日期**: 2018-02-21 13:29:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 98. 修正BAM相關議題
- **Commit ID**: `eb47be497619ebd38f57c0d7a9f3fa599b325b8b`
- **作者**: Gaspard
- **日期**: 2018-02-21 11:01:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_bpm.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jSignature.js`

### 99. 修改佈景主題的CSS路徑CustomCssLib/bpm-global-style-custom.css
- **Commit ID**: `da5cd2977231c70496f5d6f152fc31a9d4d1004f`
- **作者**: Gaspard
- **日期**: 2018-02-21 09:41:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`

### 100. 增加查詢維護樣版的mobileTemplate屬性
- **Commit ID**: `b570adbf335cc7359c64a5c67b9dbf8263c9f69d`
- **作者**: Gaspard
- **日期**: 2018-02-21 09:33:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/QueryTemplate.js`

### 101. 統一自訂義的佈景主題設定.css檔案至CustomCssLib下
- **Commit ID**: `7b01cd5af37a97ed960b0618fe1bac77bd1e9b30`
- **作者**: Gaspard
- **日期**: 2018-02-21 09:32:45
- **變更檔案數量**: 0

### 102. 統一自訂義的佈景主題設定.css檔案至CustomCssLib下
- **Commit ID**: `525fdcd6adae5f11f01e7c71f38463b2687c1f6c`
- **作者**: Gaspard
- **日期**: 2018-02-21 09:32:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`

### 103. 修改自定義佈景主題邏輯，統一更新設定後動態匯出.css檔，可供其他客製頁面使用
- **Commit ID**: `c85cbaa8ee5dbce6a323db250cb3ee4034b27e1a`
- **作者**: Gaspard
- **日期**: 2018-02-12 17:32:27
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/css/bpm-global-style-custom.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`

### 104. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `82ef652bef33349c12ab23bca0578b48ecc12267`
- **作者**: Gaspard
- **日期**: 2018-02-12 15:42:38
- **變更檔案數量**: 0

### 105. 修改自訂佈景主題配色時的CSS檔案路徑至CustomCssLib之下
- **Commit ID**: `8e97b308e3cc9ee42d7ddfbc79b5f6bde4a20e43`
- **作者**: Gaspard
- **日期**: 2018-02-12 15:42:14
- **變更檔案數量**: 194
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/AttachmentExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/ButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DialogExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/RadioButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TextboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TimeExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Index.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ProductManifest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocCategory/ManageDocCategoryMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/LogoImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ShowSignImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AdjustActivityOrder.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteReferProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ViewReassignHistory.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewDecisionActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/SubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/CreateProcessModule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/ManageProcessModuleMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ChooseDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/WebViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`

### 106. 修改多語系文字
- **Commit ID**: `238fcb603afddaa143f9c4362b7fe6a50b443049`
- **作者**: jerry1218
- **日期**: 2018-02-12 15:16:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`

### 107. 更新多語系檔並刪除重覆項目
- **Commit ID**: `1ed47400b9b450cde6662c019003e6f79c3d3ab3`
- **作者**: lorenchang
- **日期**: 2018-02-12 15:09:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`

### 108. 修改多語系文字及畫面文字
- **Commit ID**: `25aab5a2319563d26bdc7023b2e25df0ec7d910a`
- **作者**: jerry1218
- **日期**: 2018-02-12 12:02:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`

### 109. 增加檢查已經是RWD的表單不可以再選擇「匯入並轉換成RWD表單」的模式
- **Commit ID**: `8f157887303a0eae320da7019b7fac757b8d7d28`
- **作者**: Gaspard
- **日期**: 2018-02-12 09:14:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 110. 修正於表單腳本中嵌入自行的jQuery版本時，無法刷新GRID的議題
- **Commit ID**: `d4c30081184fac859ef5b6efc96fed050b3439ed`
- **作者**: Gaspard
- **日期**: 2018-02-12 08:59:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`

### 111. 修正V5舊流程設計師拉出來的流程顯示流程內容異常
- **Commit ID**: `484fafa5c2199c8e16c2a5d6bcd1b1a7f8c10b22`
- **作者**: jerry1218
- **日期**: 2018-02-09 17:12:23
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/SubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceParticipantActivity.jsp`

### 112. 1.修正排版 2.變更table min-windh改為windh
- **Commit ID**: `25fa6bc1fa686763606515280f13bce698bd0747`
- **作者**: jerry1218
- **日期**: 2018-02-09 10:54:53
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 113. 修正表單中日期與時間元件的OOO_onchange()事件失效
- **Commit ID**: `237eaca6acf577e688f4f394bad59b491ce2bf26`
- **作者**: Gaspard
- **日期**: 2018-02-09 10:33:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 114. 修正於Web表單設計器檢視表單資訊時無法正常開啟的議題
- **Commit ID**: `1dedc0c30cf6758b3b0e3e9cf2b0d17c2ea4c91e`
- **作者**: Gaspard
- **日期**: 2018-02-08 16:18:23
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/FormDefinitionInfoVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`

### 115. 同步更新模組使用的bpm-bootstrap-util.js檔案
- **Commit ID**: `106b52e339aaf9790669a41ccccd3b524ba8935e`
- **作者**: Gaspard
- **日期**: 2018-02-08 16:17:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/bpm-bootstrap-util.js`

### 116. 修正該改系統文字大小時，Grid的排版異常
- **Commit ID**: `d2f75de863319b0ba5fbbac87d5d21b9453799c9`
- **作者**: Gaspard
- **日期**: 2018-02-08 16:14:56
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 117. 修正時間選取器可編輯輸入框的屬性設定
- **Commit ID**: `5f6baac93b2ea1be63b5f7300bf8281459a47db3`
- **作者**: Gaspard
- **日期**: 2018-02-08 16:11:54
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 118. 補上入口平台頁面漏掉的css與多語系
- **Commit ID**: `6642dc5349eaeb96d67b655d4bcf9e70a46b440b`
- **作者**: ChinRong
- **日期**: 2018-02-08 11:55:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 119. 修正-個人化首頁自訂圖表區間異常
- **Commit ID**: `21a56c849752719cfc798d7a6e4bc4dce7fc5b7a`
- **作者**: jerry1218
- **日期**: 2018-02-07 18:03:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 120. 修改-個人資訊頁面排版 新增- 1.通知頁面支援關注流程&重要流程 2.個人化首頁自訂時間
- **Commit ID**: `b43879144d513bbf433cd11031ab69ce1d43e619`
- **作者**: jerry1218
- **日期**: 2018-02-07 17:58:54
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 121. 我的最愛main頁面修改
- **Commit ID**: `f61d6fed426109f23e9019ea60d95acd457dbde8`
- **作者**: jerry1218
- **日期**: 2018-02-07 10:16:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp`

### 122. 增加轉換T100表單變RWD表單時的特殊轉換邏輯
- **Commit ID**: `b275b15d1465d18f977f36892f9ead2e8c9b3cc3`
- **作者**: Gaspard
- **日期**: 2018-02-07 09:02:11
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 123. 增加日期時間選取器的多語系
- **Commit ID**: `5e574069a81e039517d927d09e8531270bf78769`
- **作者**: Gaspard
- **日期**: 2018-02-07 09:00:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 124. 修正議題
- **Commit ID**: `174a64b43380ba28941ed090919d032d198e170c`
- **作者**: ChinRong
- **日期**: 2018-02-06 16:42:24
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TreeViewDataChooserAjax.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/TreeViewDataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/zTreeStyle/img/zTreeStandard_complete.gif`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/zTreeStyle/img/zTreeStandard_complete.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChateUser.js`

### 125. 修正-1.各功能頁面統一、2.service缺少commons-digester.jar(影響iReport開啟)
- **Commit ID**: `7081fefecd969c79cad5b7c545732d17e0203fee`
- **作者**: jerry1218
- **日期**: 2018-02-06 15:09:40
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-digester.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 126. v57Merge(5641-5652)
- **Commit ID**: `a8ffb35c4b5d986840dedf664afc9c5ba131326b`
- **作者**: lorenchang
- **日期**: 2018-02-12 12:19:15
- **變更檔案數量**: 1355
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorCER.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorCER.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateObject.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ResignedEmployeesManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/StatefulProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TimerFacadeDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementStyle.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/appform/AppFormKey.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileGraphTemplates.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ConnectionFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthClientUserDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppCustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/AddUserToUnitDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgTreeNode.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/persistence/src/com/dsc/nana/persistence/LockType.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SNGenerator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/statistics/StatisticianBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/rtxpush/RTXMessagePusher.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormTransfer.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/RsrcBundleCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/dataformat/MobileHttpStatusCode.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5602.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5631.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5651.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle_5701.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWebLog.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/picName/process.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/AspectJ/aspectjrt-1.8.9.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/AspectJ/aspectjweaver-1.8.9.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/GoogleGuava/guava-20.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-core-1.1.16.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-springmvc-1.1.16.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-ui-1.1.16.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-ui-webjar-1.1.16.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JavaJWT/java-jwt-2.1.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/Reflection/reflections-0.9.10.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/Slf4J/slf4j-api-1.7.25.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-aop-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-aspects-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-beans-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-context-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-context-support-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-core-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-expression-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-instrument-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-instrument-tomcat-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-jdbc-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-jms-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-messaging-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-orm-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-oxm-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-test-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-tx-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-web-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-webmvc-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-webmvc-portlet-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-websocket-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerOrganization.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogAspect.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulClientDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ChartLegendConfig.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ChartXconfig.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ChartYconfig.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ColumnDataSet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentArray.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentColor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentImage.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentWidth.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DateTimeField.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DatetimeQueryCondition.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldContent.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDataset.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDatasetList.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDetail.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/HeaderDataSet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ImageDataset.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListDataset.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListFieldDataset.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleBatchReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleOperationRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageExecutionRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationButtonRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataBatchReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataOperationRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/QueryCriteria.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/RemoteUser.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/RowDataSet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ScreeningDataSet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ScreeningDetail.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ScreeningQueryCondition.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesConfig.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesConfigForLine.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesDetail.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/TableHeader.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AcceptWorkItemBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ActivityDefinitionForClientListBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AddCustomActivityBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/BAMBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkItemBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkStepBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ContactUserBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CriticalBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ElementBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchProcessCommentsBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchReexecuteActivityBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchWorkStepsBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FormDefinitionBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ListReaderBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ReexecuteActivityBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/RsrcbundleBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/TerminateProcessBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/UserFormValueBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/XmlBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/BuildFormBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeFavoriteReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeProcessBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeWorkItemReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/NoticeProcessBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/OAuthConfigBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/RollbackActivityBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SaveFormBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/ScheduleListBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SearchProcessListBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/TraceProcessBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/WeChatAuthBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/WeChatMsgBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityDefinitionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityInstanceBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AllRsrcbundleValueBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ApplicationDefinitionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AskActivityReexecuteTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AssignmentTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AttachmentDefinitionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AttachmentInstanceBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/BAMBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CriticalBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DocCmItemBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DocTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DraftHeaderBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ExternalReferenceBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchProcessCommentsBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchReexecuteActivityBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchWorkStepsBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormFieldAccessDefinitionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormInstanceBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormInstanceForPerformBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/InstanceCompleteTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/InvokeParametersBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ListReaderBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/MailingFrequencyTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ManualReassignTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/NoticeTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OrganizationUnitForInvokingListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PerformTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstanceForListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstanceStateTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessPackageForListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ReassignmentInfoForListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ReassignmentTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ReexecuteActivityTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/RsrcbundleBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SimpleUserBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/StringWorkflowRuntimeValueBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageViewerBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysintegrationServerBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/TracerRoleTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserDefineModeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserForListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserInputSubjectTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/VerifyPasswordTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForPerformBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemStateTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepForPerformBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepStateTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmFormBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmPhaseBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmProcessLevelBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmWorkItemDataBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormDefinitionBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/InvokeProcessBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/MailingFrequencyTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/NoticeProcessBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OAuthClientUserBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OAuthConfigBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OrgUnitBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OrganizationUnitForInvokingListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ScheduleBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ScheduleListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/SearchProcessListBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/TraceProcessBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/UserInputSubjectTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/VerifyPasswordTypeBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WeChatAccessTokenBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WeChatAccountBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WeChatKeyBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WorkStepViewerBeanRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/BpmServiceAuthenticate.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinWhaleServiceAuthenticate.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/FormV2.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileForm.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AbstractMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleDataChooseTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/UserClientBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserServiceBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/notifier/NotifierServlet.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/util/DBMessageResources.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BPMPerformRequestTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFileManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileUserAuthTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileOrganizationManagerTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/LikedJSONObject.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceLocator.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/TiptopMemo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTraceLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeploy.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/jdajia.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListContact.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTracePerformed.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleFormTodo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/cn.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/de.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/es.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/fr.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/it.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/nl.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/pt.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/ru.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lib/aw.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/bg1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/bg2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/checkbox1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/checkbox2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/radio1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/radio2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-bg1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-bg2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-checkbox1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-checkbox2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-radio1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-radio2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g4.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tabs1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tabs2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g4.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tabs1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tabs2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/AppModalDialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/Dialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ModalDialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/OpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/form/aw.min.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ds-grid-aw.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ds.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/dsMobile.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/popup.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/ajax-loader.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/action-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/action-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/alert-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/alert-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/audio-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/audio-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/back-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/back-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bars-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bars-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bullets-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bullets-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/calendar-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/calendar-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/camera-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/camera-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/check-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/check-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/clock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/clock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/cloud-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/cloud-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/comment-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/comment-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/delete-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/delete-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/edit-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/edit-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/eye-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/eye-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forbidden-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forbidden-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forward-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forward-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/gear-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/gear-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/grid-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/grid-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/heart-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/heart-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/home-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/home-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/info-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/info-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/location-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/location-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/lock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/lock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/mail-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/mail-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/minus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/minus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/navigation-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/navigation-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/phone-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/phone-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/plus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/plus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/power-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/power-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/recycle-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/recycle-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/refresh-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/refresh-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/search-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/search-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/shop-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/shop-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/star-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/star-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/tag-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/tag-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/user-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/user-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/video-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/video-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/action-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/action-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/alert-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/alert-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/audio-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/audio-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/back-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/back-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bars-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bars-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bullets-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bullets-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/calendar-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/calendar-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/camera-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/camera-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/check-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/check-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/clock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/clock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/cloud-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/cloud-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/comment-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/comment-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/delete-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/delete-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/edit-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/edit-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/eye-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/eye-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forbidden-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forbidden-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forward-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forward-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/gear-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/gear-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/grid-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/grid-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/heart-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/heart-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/home-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/home-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/info-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/info-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/location-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/location-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/lock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/lock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/mail-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/mail-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/minus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/minus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/navigation-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/navigation-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/phone-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/phone-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/plus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/plus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/power-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/power-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/recycle-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/recycle-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/refresh-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/refresh-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/search-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/search-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/shop-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/shop-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/star-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/star-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/tag-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/tag-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/user-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/user-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/video-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/video-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.map`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.structure.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.structure.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.theme.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.theme.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.external-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.external-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.icons-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.icons-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-svg-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-svg-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.structure-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.structure-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.theme-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.theme-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jquery-1.8.3.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/system/BpmMobileLibrary.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobilePublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileCustomOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/system/MobileTool.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/knockout-3.2.0.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/knockout.mapping.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/utab.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BPMProcessTracing.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListContact.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmTaskManage.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkItem.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkItemShell.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkPublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileCustomOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileGrid.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTool.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/aw.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/cn.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/de.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/es.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/fr.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/it.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/nl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/pt.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/ru.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lib/aw.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/bg1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/bg2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/checkbox1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/checkbox2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/radio1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/radio2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-bg1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-bg2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-checkbox1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-checkbox2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-radio1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-radio2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tabs1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tabs2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tabs1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tabs2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/AppModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/Dialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/OpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/aw.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ds-grid-aw.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ds.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/dsMobile.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/popup.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/Map.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/MobileAppGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/index.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/ajax-loader.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/action-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/action-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/alert-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/alert-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/audio-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/audio-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/back-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/back-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bars-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bars-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bullets-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bullets-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/calendar-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/calendar-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/camera-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/camera-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/check-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/check-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/clock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/clock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/cloud-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/cloud-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/comment-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/comment-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/delete-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/delete-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/edit-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/edit-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/eye-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/eye-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forbidden-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forbidden-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forward-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forward-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/gear-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/gear-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/grid-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/grid-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/heart-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/heart-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/home-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/home-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/info-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/info-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/location-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/location-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/lock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/lock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/mail-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/mail-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/minus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/minus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/navigation-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/navigation-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/phone-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/phone-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/plus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/plus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/power-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/power-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/recycle-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/recycle-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/refresh-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/refresh-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/search-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/search-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/shop-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/shop-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/star-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/star-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/tag-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/tag-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/user-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/user-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/video-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/video-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/action-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/action-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/alert-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/alert-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/audio-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/audio-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/back-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/back-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bars-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bars-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bullets-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bullets-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/calendar-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/calendar-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/camera-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/camera-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/check-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/check-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/clock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/clock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/cloud-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/cloud-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/comment-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/comment-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/delete-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/delete-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/edit-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/edit-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/eye-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/eye-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forbidden-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forbidden-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forward-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forward-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/gear-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/gear-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/grid-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/grid-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/heart-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/heart-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/home-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/home-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/info-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/info-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/location-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/location-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/lock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/lock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/mail-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/mail-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/minus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/minus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/navigation-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/navigation-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/phone-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/phone-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/plus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/plus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/power-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/power-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/recycle-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/recycle-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/refresh-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/refresh-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/search-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/search-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/shop-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/shop-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/star-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/star-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/tag-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/tag-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/user-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/user-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/video-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/video-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.map`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.structure.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.structure.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.theme.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.theme.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.external-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.external-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.icons-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.icons-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-svg-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-svg-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.structure-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.structure-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.theme-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.theme-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jquery-1.8.3.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jweixin-1.0.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/snap.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobilePublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileTool.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/knockout-3.2.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/knockout.mapping.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/utab.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap.4.0.0.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/popup.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/ListToDo.cc`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenuExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/bootstrap_3_3_7.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/bootstrap_4_0_0.min.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOInv001.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.2.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.2.1_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.2_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.2_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.2_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@syncisodoc/InitSyncISO_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@syncisodoc/InitSyncISO_Oracle.sql`

### 127. 修改個人化首頁畫面
- **Commit ID**: `6df435bc259b4c4fbffd77176f9d48f203d6c497`
- **作者**: jerry1218
- **日期**: 2018-02-01 16:33:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 128. 修正SessionBeanAppliction的欄位調整導致SessionBean無法正常運作的問題
- **Commit ID**: `acbfc17ab09a5aecf710eb0ab54039c2c32b96d4`
- **作者**: lorenchang
- **日期**: 2018-02-01 16:23:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/app/SessionBeanApplication.java`

### 129. 修正第一關以後Gird資料清空的議題
- **Commit ID**: `9eacff381bf619d1537b2ab505ca58953735fbaf`
- **作者**: Gaspard
- **日期**: 2018-02-01 14:47:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 130. 修正議題
- **Commit ID**: `7d986734ac4e39fbb03dcf9dea83327580448a65`
- **作者**: ChinRong
- **日期**: 2018-02-01 11:41:22
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle_5701.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 131. 在個人化首頁時,右上角隱藏簽核區塊按鈕
- **Commit ID**: `a54f108d0aa65cc50820bc94ee7cc431bb928698`
- **作者**: jerry1218
- **日期**: 2018-02-01 09:51:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 132. 修正簽核及追蹤畫面 , 返回按鈕移至最左邊
- **Commit ID**: `1d58566d97d5ff09447c8b47be830c055d48668b`
- **作者**: jerry1218
- **日期**: 2018-01-31 16:07:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 133. 修改問題: 1.功能頁修改按鈕顏色與獨立模組不同 2.簽核中轉由他人處理頁-片語異常 3.模擬使用者頁面修改,支援快搜
- **Commit ID**: `6afbad0ba7619b8c431ecb6d9afc0100c22a9716`
- **作者**: jerry1218
- **日期**: 2018-01-30 16:20:10
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`

