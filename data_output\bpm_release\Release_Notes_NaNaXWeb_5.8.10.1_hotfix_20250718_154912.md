# Release Notes - NaNaXWeb

## 版本資訊
- **新版本**: 5.8.10.1_hotfix
- **舊版本**: 5.8.10.1_202403261726_autobuild
- **生成時間**: 2025-07-18 15:49:12
- **新增 Commit 數量**: 32

## 變更摘要

### yamiyeh10 (24 commits)

- **2024-12-12 16:15:52**: [PRODT]C01-20241209004 修正Web流程管理工具中活動參與者為策略分配時無法儲存定義問題
  - 變更檔案: 2 個
- **2024-10-29 15:14:55**: [PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題
  - 變更檔案: 1 個
- **2024-10-09 14:06:59**: [PRODT]C01-20241008003 修正Web流程管理工具中連接線名稱在儲存後會消失的問題
  - 變更檔案: 1 個
- **2024-07-26 11:57:04**: [PRODT]Q00-20240710001 調整Web流程管理工具中儲存流程定義模型前增加檢核參與者機制
  - 變更檔案: 1 個
- **2024-07-10 11:57:42**: [PRODT]C01-20240614001 調整Web流程管理工具中主流程設定屬性的作為活動處理者代理人機制
  - 變更檔案: 2 個
- **2024-09-26 08:21:33**: [PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況
  - 變更檔案: 1 個
- **2024-09-20 09:03:16**: [PRODT]Q00-20240626002 修正Web流程管理工具中關卡與連接線存在髒資料卻無提示的問題
  - 變更檔案: 1 個
- **2024-07-17 14:51:14**: [PRODT]C01-20240716003 調整Web流程管理工具中發起權限設定屬性選擇使用者或組織時的畫面
  - 變更檔案: 3 個
- **2024-07-10 17:32:40**: [PRODT]C01-20240710001 修正Web流程管理工具在活動定義編輯器中將允許系統改派他人取消勾選後還是存在問題
  - 變更檔案: 1 個
- **2024-06-26 10:21:15**: [內部]A00-20240625001 NG-Zorro套件引入越南語系
  - 變更檔案: 3 個
- **2024-06-25 17:05:06**: [PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題
  - 變更檔案: 2 個
- **2024-06-12 11:57:45**: [PRODT] V00-20240612001 修正Web流程管理工具加入核決層級關卡後無法儲存會跳出未設定條件等提示訊息問題
  - 變更檔案: 1 個
- **2024-06-11 17:06:11**: [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
  - 變更檔案: 1 個
- **2024-06-20 11:20:39**: [行業表單庫]V00-20240620001 增加卡控使用的表單代號必須為英文字母開頭，並且由英文字母、數字或底線組成
  - 變更檔案: 2 個
- **2024-05-31 17:38:16**: [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
  - 變更檔案: 1 個
- **2024-05-31 16:31:30**: [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制[補]
  - 變更檔案: 1 個
- **2024-05-29 17:30:08**: [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息
  - 變更檔案: 3 個
- **2024-05-21 15:31:08**: [PRODT]C01-20240517015 修正Web流程管理工具中活動定義編輯器上編輯網路服務的應用程式時會發生找不到portName問題
  - 變更檔案: 2 個
- **2024-05-16 08:52:03**: [PRODT]C01-20240509001 修正Web流程管理工具中連接線條件名稱會覆蓋問題
  - 變更檔案: 1 個
- **2024-05-15 15:42:14**: [SYSDT]C01-20240514004 修正TIPTOP(WorkFlow)系統整合設定中網路服務設定一覽表有使用MethodColumnSet時導致畫面呈現loading問題
  - 變更檔案: 1 個
- **2024-04-30 14:55:28**: [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制
  - 變更檔案: 3 個
- **2024-04-24 13:53:44**: [PRODT]V00-20240407001 修正Web流程管理工具中發起權限設定屬性刪除所有資料後原先資料還是存在的問題
  - 變更檔案: 1 個
- **2024-04-16 08:56:26**: [PRODT]Q00-20240415003 調整流程屬於簽核樣版分類時不驗證流程ID命名規格必須是[營運中心_單據_單別]的機制
  - 變更檔案: 1 個
- **2024-03-26 18:45:07**: [ORGDT]Q00-20240326005 修正Web組織管理工具中列印組織圖異常問題
  - 變更檔案: 3 個

### lorenchang (3 commits)

- **2024-11-07 11:00:59**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)
  - 變更檔案: 2 個
- **2024-10-28 10:58:05**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)
  - 變更檔案: 2 個
- **2024-10-04 17:16:39**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log
  - 變更檔案: 3 個

### 邱郁晏 (1 commits)

- **2024-07-12 10:23:15**: [流程封存] C01-20240506005 調整流程封存維護作業日期儲存計算方式
  - 變更檔案: 1 個

### 刘旭 (1 commits)

- **2024-05-15 17:57:59**: [流程封存] C01-20240510006 已封存流程在監控流程仍查看的到问题修复
  - 變更檔案: 1 個

### 周权 (1 commits)

- **2024-04-09 11:01:00**: [Web]Q00-20240409001 修正依賴衝突導致XPDL流程圖無法正常呈現的问题
  - 變更檔案: 1 個

### pinchi_lin (2 commits)

- **2024-04-03 14:51:22**: [PRODT]Q00-20240403001 修正Web流程管理工具中建立核決活動時更換預設id會在發起流程後報錯的問題
  - 變更檔案: 1 個
- **2024-03-27 10:50:11**: [PRODT]Q00-20240327001 修正Web流程管理工具中匯入流程無法覆蓋流程進版的問題
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. [PRODT]C01-20241209004 修正Web流程管理工具中活動參與者為策略分配時無法儲存定義問題
- **Commit ID**: `3338825fae5287a560383c8d015841aab8acab95`
- **作者**: yamiyeh10
- **日期**: 2024-12-12 16:15:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/participant-chooser.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/strategy-assign/strategy-assign.component.ts`

### 2. [PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題
- **Commit ID**: `c48c0c47585155d983e30e90cfba46a68563941b`
- **作者**: yamiyeh10
- **日期**: 2024-10-29 15:14:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.ts`

### 3. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)
- **Commit ID**: `f722f2b792cedaaa5633b913fbee1786f74cc821`
- **作者**: lorenchang
- **日期**: 2024-11-07 11:00:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/util/init/TimeScheduleInitializer.java`

### 4. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)
- **Commit ID**: `0f0bccb36c4969acbbb02b720f4147dcee9cfc24`
- **作者**: lorenchang
- **日期**: 2024-10-28 10:58:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java`

### 5. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log
- **Commit ID**: `0d3dbcdd6f617c97f8e1044a4284c00a803ce5c1`
- **作者**: lorenchang
- **日期**: 2024-10-04 17:16:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java`

### 6. [流程封存] C01-20240506005 調整流程封存維護作業日期儲存計算方式
- **Commit ID**: `9a40c7270dfe11cf5f97d8e7cc31f3d15693cf00`
- **作者**: 邱郁晏
- **日期**: 2024-07-12 10:23:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java`

### 7. [PRODT]C01-20241008003 修正Web流程管理工具中連接線名稱在儲存後會消失的問題
- **Commit ID**: `0431f59294e45a961b802b6007217eca82337c76`
- **作者**: yamiyeh10
- **日期**: 2024-10-09 14:06:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 8. [PRODT]Q00-20240710001 調整Web流程管理工具中儲存流程定義模型前增加檢核參與者機制
- **Commit ID**: `9938cfdcb1a16dcb23a7ddb3e63e48df9519ae67`
- **作者**: yamiyeh10
- **日期**: 2024-07-26 11:57:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 9. [PRODT]C01-20240614001 調整Web流程管理工具中主流程設定屬性的作為活動處理者代理人機制
- **Commit ID**: `378754268241638ff788490a70f01b4ce97e50b5`
- **作者**: yamiyeh10
- **日期**: 2024-07-10 11:57:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-definition-general-attributes/process-definition-general-attributes.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-definition-general-attributes/process-definition-general-attributes.component.ts`

### 10. [PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況
- **Commit ID**: `f02fb7ce9cfd0b5a816fcb84f8eb2a85910f4b5f`
- **作者**: yamiyeh10
- **日期**: 2024-09-26 08:21:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 11. [PRODT]Q00-20240626002 修正Web流程管理工具中關卡與連接線存在髒資料卻無提示的問題
- **Commit ID**: `677c8230b6084e1401e2ba2f56141bd33660c519`
- **作者**: yamiyeh10
- **日期**: 2024-09-20 09:03:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 12. [PRODT]C01-20240716003 調整Web流程管理工具中發起權限設定屬性選擇使用者或組織時的畫面
- **Commit ID**: `b91025b58555dbb117919440922ed1cde5726439`
- **作者**: yamiyeh10
- **日期**: 2024-07-17 14:51:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/add-invoke-aurhority-openwin/add-invoke-aurhority-openwin.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.ts`

### 13. [PRODT]C01-20240710001 修正Web流程管理工具在活動定義編輯器中將允許系統改派他人取消勾選後還是存在問題
- **Commit ID**: `2208571b57df1e9867eb47ab7ae9ee1ccd29c73a`
- **作者**: yamiyeh10
- **日期**: 2024-07-10 17:32:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-advanced-attributes/activity-advanced-attributes.component.ts`

### 14. [內部]A00-20240625001 NG-Zorro套件引入越南語系
- **Commit ID**: `43b9b71d5e0d6f7e1bd704fcb490f3d0ac7331d5`
- **作者**: yamiyeh10
- **日期**: 2024-06-26 10:21:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/app.module.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/app.module.ts`
  - 📝 **修改**: `AngularProjects/ProcessArchiveModule/src/app/app.module.ts`

### 15. [PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題
- **Commit ID**: `ce97066332051c52d52b33c2625ba1b6aa5d1a9b`
- **作者**: yamiyeh10
- **日期**: 2024-06-25 17:05:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/bpmn-auto-layout/dist/index.cjs`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 16. [PRODT] V00-20240612001 修正Web流程管理工具加入核決層級關卡後無法儲存會跳出未設定條件等提示訊息問題
- **Commit ID**: `5ba0e9c1e670f2c62b8a119f62a7cbc953191120`
- **作者**: yamiyeh10
- **日期**: 2024-06-12 11:57:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 17. [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
- **Commit ID**: `2b7ec6ddb6f2fecd58bdda0268b7d5e986e2b062`
- **作者**: yamiyeh10
- **日期**: 2024-06-11 17:06:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-rule-list-editor/decision-rule-list-editor.component.ts`

### 18. [行業表單庫]V00-20240620001 增加卡控使用的表單代號必須為英文字母開頭，並且由英文字母、數字或底線組成
- **Commit ID**: `9300bef2506f5540e785792f413b5c4dffa39333`
- **作者**: yamiyeh10
- **日期**: 2024-06-20 11:20:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/form/form-repository/import-form-drawer/import-form-drawer.component.html`
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/form/form-repository/import-form-drawer/import-form-drawer.component.ts`

### 19. [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
- **Commit ID**: `16f0e471fc46d52a17781c34875ab5b64112a14d`
- **作者**: yamiyeh10
- **日期**: 2024-05-31 17:38:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 20. [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制[補]
- **Commit ID**: `eb2b597624fa2279e374f7163bacfe5204fd008d`
- **作者**: yamiyeh10
- **日期**: 2024-05-31 16:31:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 21. [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息
- **Commit ID**: `8c8fee021a402ab82b95c10016864a7a543faf69`
- **作者**: yamiyeh10
- **日期**: 2024-05-29 17:30:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-rule-list-editor/decision-rule-list-editor.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/models/save-process-package-response-body.interface.ts`

### 22. [PRODT]C01-20240517015 修正Web流程管理工具中活動定義編輯器上編輯網路服務的應用程式時會發生找不到portName問題
- **Commit ID**: `1595d0a1d2e3cf81b8fb5ac6e4228b996376094a`
- **作者**: yamiyeh10
- **日期**: 2024-05-21 15:31:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/web-services-openwin/web-services-openwin.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/web-services-openwin/web-services-openwin.component.ts`

### 23. [流程封存] C01-20240510006 已封存流程在監控流程仍查看的到问题修复
- **Commit ID**: `c7d5338e0cd7283e8705a92bf5dbf484e40de119`
- **作者**: 刘旭
- **日期**: 2024-05-15 17:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`

### 24. [PRODT]C01-20240509001 修正Web流程管理工具中連接線條件名稱會覆蓋問題
- **Commit ID**: `c0d5132dd74c312b81fe2bbe339cb3ed21fe55e4`
- **作者**: yamiyeh10
- **日期**: 2024-05-16 08:52:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-custom/customRenderer.js`

### 25. [SYSDT]C01-20240514004 修正TIPTOP(WorkFlow)系統整合設定中網路服務設定一覽表有使用MethodColumnSet時導致畫面呈現loading問題
- **Commit ID**: `3c2798e4a90a2055c5d3a76876f48d6217a26c78`
- **作者**: yamiyeh10
- **日期**: 2024-05-15 15:42:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/tiptop-system-integration/tiptop-system-integration-basic/tiptop-system-integration-basic.component.ts`

### 26. [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制
- **Commit ID**: `eaf7fddfa78793a5a44411b58713c7953cfebbe0`
- **作者**: yamiyeh10
- **日期**: 2024-04-30 14:55:28
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 27. [PRODT]V00-20240407001 修正Web流程管理工具中發起權限設定屬性刪除所有資料後原先資料還是存在的問題
- **Commit ID**: `2d45f92538081524c7b99261e84589c9b65c8c87`
- **作者**: yamiyeh10
- **日期**: 2024-04-24 13:53:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`

### 28. [PRODT]Q00-20240415003 調整流程屬於簽核樣版分類時不驗證流程ID命名規格必須是[營運中心_單據_單別]的機制
- **Commit ID**: `27c46471625d75271079ca23e0aff66e7473bd5d`
- **作者**: yamiyeh10
- **日期**: 2024-04-16 08:56:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 29. [Web]Q00-20240409001 修正依賴衝突導致XPDL流程圖無法正常呈現的问题
- **Commit ID**: `2e192fd313a44fa1fbaf385e763b2b4e75af1e62`
- **作者**: 周权
- **日期**: 2024-04-09 11:01:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/webapp/WEB-INF/jboss-all.xml`

### 30. [PRODT]Q00-20240403001 修正Web流程管理工具中建立核決活動時更換預設id會在發起流程後報錯的問題
- **Commit ID**: `bc86ffded769a6a0265591ea074909d7c27923cb`
- **作者**: pinchi_lin
- **日期**: 2024-04-03 14:51:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/decision-activity-manager-drawer/decision-activity-manager-drawer.component.ts`

### 31. [ORGDT]Q00-20240326005 修正Web組織管理工具中列印組織圖異常問題
- **Commit ID**: `5cd879ddb7c461f72908c1d1fab183e586ede221`
- **作者**: yamiyeh10
- **日期**: 2024-03-26 18:45:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/print-openwin/print-openwin.component.css`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/print-openwin/print-openwin.component.ts`

### 32. [PRODT]Q00-20240327001 修正Web流程管理工具中匯入流程無法覆蓋流程進版的問題
- **Commit ID**: `3c910555a10c71dbf1b75fb9f4b3ad459312f641`
- **作者**: pinchi_lin
- **日期**: 2024-03-27 10:50:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/services/process-package-manage.service.ts`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/ProcessDesignMgr.java`

