# Release Notes - NaNaXWeb

## 版本資訊
- **新版本**: 5.8.10.3_hotfix
- **舊版本**: 5.8.10.3_202409241414_autobuild
- **生成時間**: 2025-07-18 15:48:56
- **新增 Commit 數量**: 14

## 變更摘要

### yamiyeh10 (10 commits)

- **2025-05-08 15:45:49**: [ORGDT]C01-20250507002 調整Web組織管理工具中將新增使用者頁面禁用自動填入功能
  - 變更檔案: 1 個
- **2025-03-24 11:09:41**: [PRODT]C01-20250313002 修正Web流程管理工具中使用核決關卡且參考活動為自定義時無法儲存定義問題
  - 變更檔案: 1 個
- **2024-12-27 14:12:09**: [PRODT]C01-20241216002 修正Web流程管理工具中增加卡控避免因連續點擊多次關卡而導致重複執行問題
  - 變更檔案: 1 個
- **2024-12-26 16:17:38**: [PRODT]C01-20241225001 調整Web流程管理工具中關卡上有多個進入點時增加顯示提示訊息並且無法儲存流程
  - 變更檔案: 1 個
- **2024-12-17 17:03:04**: [PRODT]C01-20241216006 修正Web流程管理工具中活動參與者異常導致派送流程時拋錯問題
  - 變更檔案: 4 個
- **2024-12-12 16:15:52**: [PRODT]C01-20241209004 修正Web流程管理工具中活動參與者為策略分配時無法儲存定義問題
  - 變更檔案: 2 個
- **2024-10-29 15:14:55**: [PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題
  - 變更檔案: 1 個
- **2024-12-04 11:16:29**: [PRODT]C01-20241111005 修正Web流程管理工具中關卡誤刪後點擊復原再儲存時造成關卡與線條遺失的問題[補]
  - 變更檔案: 1 個
- **2024-11-14 11:01:45**: [PRODT]C01-20241111005 修正Web流程管理工具中關卡誤刪後點擊復原再儲存時造成關卡與線條遺失的問題
  - 變更檔案: 1 個
- **2024-10-09 14:06:59**: [PRODT]C01-20241008003 修正Web流程管理工具中連接線名稱在儲存後會消失的問題
  - 變更檔案: 1 個

### 周权 (1 commits)

- **2025-01-16 14:45:03**: [[word套表]]C01-20250115003 调整新增刪除報表定義檔后，選擇報表定義列表不會刷新的問題
  - 變更檔案: 1 個

### lorenchang (3 commits)

- **2024-11-07 11:00:59**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)
  - 變更檔案: 2 個
- **2024-10-28 10:58:05**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)
  - 變更檔案: 2 個
- **2024-10-04 17:16:39**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. [ORGDT]C01-20250507002 調整Web組織管理工具中將新增使用者頁面禁用自動填入功能
- **Commit ID**: `4152654b315c0f0d3c006e65defced43f07f20e7`
- **作者**: yamiyeh10
- **日期**: 2025-05-08 15:45:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/operations/create-user-operation/create-user-operation.component.html`

### 2. [PRODT]C01-20250313002 修正Web流程管理工具中使用核決關卡且參考活動為自定義時無法儲存定義問題
- **Commit ID**: `f9dfa8bfdd91ca32542c72e5311fe2c7473ff7a7`
- **作者**: yamiyeh10
- **日期**: 2025-03-24 11:09:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 3. [[word套表]]C01-20250115003 调整新增刪除報表定義檔后，選擇報表定義列表不會刷新的問題
- **Commit ID**: `1b21806e6c2a901f2ae649e90280369311836c52`
- **作者**: 周权
- **日期**: 2025-01-16 14:45:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/report/report-manage-tool/report-manage/report-manage.component.ts`

### 4. [PRODT]C01-20241216002 修正Web流程管理工具中增加卡控避免因連續點擊多次關卡而導致重複執行問題
- **Commit ID**: `68cc1f38a0b9008b34cc678c1a0659031d0a0d25`
- **作者**: yamiyeh10
- **日期**: 2024-12-27 14:12:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 5. [PRODT]C01-20241225001 調整Web流程管理工具中關卡上有多個進入點時增加顯示提示訊息並且無法儲存流程
- **Commit ID**: `57eed00306f40910ae013635da309d8034f33f83`
- **作者**: yamiyeh10
- **日期**: 2024-12-26 16:17:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 6. [PRODT]C01-20241216006 修正Web流程管理工具中活動參與者異常導致派送流程時拋錯問題
- **Commit ID**: `4e1708236feceaaedebf7ce7a024871ef1960378`
- **作者**: yamiyeh10
- **日期**: 2024-12-17 17:03:04
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/process-relationship/process-relationship.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/strategy-assign/strategy-assign.component.ts`

### 7. [PRODT]C01-20241209004 修正Web流程管理工具中活動參與者為策略分配時無法儲存定義問題
- **Commit ID**: `d256ea5e550410ba11fba4f18667fa03c064fd7d`
- **作者**: yamiyeh10
- **日期**: 2024-12-12 16:15:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/participant-chooser.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/strategy-assign/strategy-assign.component.ts`

### 8. [PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題
- **Commit ID**: `1812c456768444bf8727785316b60cea250a7226`
- **作者**: yamiyeh10
- **日期**: 2024-10-29 15:14:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.ts`

### 9. [PRODT]C01-20241111005 修正Web流程管理工具中關卡誤刪後點擊復原再儲存時造成關卡與線條遺失的問題[補]
- **Commit ID**: `a315aabc7d258e705bb811a4913bc362d4ad6267`
- **作者**: yamiyeh10
- **日期**: 2024-12-04 11:16:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 10. [PRODT]C01-20241111005 修正Web流程管理工具中關卡誤刪後點擊復原再儲存時造成關卡與線條遺失的問題
- **Commit ID**: `15f237c87064b2da341f994b158e138f56b75ab6`
- **作者**: yamiyeh10
- **日期**: 2024-11-14 11:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 11. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)
- **Commit ID**: `9b3f3b897b504debf823bbbdb80a327c0e8d6c16`
- **作者**: lorenchang
- **日期**: 2024-11-07 11:00:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/util/init/TimeScheduleInitializer.java`

### 12. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)
- **Commit ID**: `996d455749072967380ae64cddcdbc0d2b2a60bd`
- **作者**: lorenchang
- **日期**: 2024-10-28 10:58:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java`

### 13. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log
- **Commit ID**: `dd5d26e07889ef8baabc0f5e52c241336f4bc0c7`
- **作者**: lorenchang
- **日期**: 2024-10-04 17:16:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java`

### 14. [PRODT]C01-20241008003 修正Web流程管理工具中連接線名稱在儲存後會消失的問題
- **Commit ID**: `b099dc5eb5643dcf9dd311d3b3045a2c1a6ca2b3`
- **作者**: yamiyeh10
- **日期**: 2024-10-09 14:06:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

