{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "title": "客戶公司連線資訊 Schema", "description": "用於記錄客戶公司各種產品系統的連線資訊", "properties": {"OID": {"type": "string", "description": "唯一識別代碼"}, "companyId": {"type": "string", "description": "客戶公司代號"}, "companyName": {"type": "string", "description": "客戶公司名稱"}, "companyNotes": {"type": "string", "description": "客戶公司相關的額外文字說明或備註，例如VPN連線方式、跳板機資訊等。"}, "connections": {"type": "array", "description": "該客戶公司的所有連線資訊清單", "items": {"oneOf": [{"$ref": "#/definitions/bpmApConnection"}, {"$ref": "#/definitions/bpmDbConnection"}, {"$ref": "#/definitions/hrmConnection"}, {"$ref": "#/definitions/tiptopConnection"}, {"$ref": "#/definitions/t100Connection"}, {"$ref": "#/definitions/workflowConnection"}, {"$ref": "#/definitions/imgConnection"}, {"$ref": "#/definitions/cosmosConnection"}]}}}, "required": ["companyId", "companyName", "connections"], "definitions": {"baseConnection": {"type": "object", "description": "所有連線資訊的基礎欄位定義", "properties": {"id": {"type": "string", "description": "連線資訊的唯一識別碼，用於區分不同的連線資訊設定"}, "productType": {"type": "string", "enum": ["BPM AP", "BPM DB", "HRM", "Tiptop", "T100", "Workflow", "IMG", "Cosmos"], "description": "產品類別"}, "description": {"type": "string", "description": "連線資訊的描述說明，用於記錄此連線的用途或備註"}, "createdAt": {"type": "string", "format": "date-time", "description": "連線資訊建立時間"}, "updatedAt": {"type": "string", "format": "date-time", "description": "連線資訊最後更新時間"}}, "required": ["productType"]}, "bpmApConnection": {"allOf": [{"$ref": "#/definitions/baseConnection"}, {"type": "object", "description": "BPM 應用程式伺服器連線資訊", "properties": {"productType": {"const": "BPM AP", "description": "固定值 BPM AP，表示這是 BPM 應用程式連線"}, "environment": {"type": "string", "description": "連線環境類型，用於區分測試機或正式機環境", "enum": ["測試機", "正式機"]}, "serverIp": {"type": "string", "format": "ip-address", "description": "BPM 伺服器的 IP"}, "username": {"type": "string", "description": "連線到 AP 主機的使用者帳號"}, "password": {"type": "string", "description": "連線到 AP 主機的使用者密碼"}, "bpmVersion": {"type": "string", "description": "BPM 版本"}, "bpmAdmin": {"type": "string", "description": "BPM 管理者帳號"}, "bpmPassword": {"type": "string", "description": "BPM 管理者密碼"}, "serverUrl": {"type": "string", "format": "uri", "description": "BPM 網站 URL 位址，例如 http://127.0.0.1:8086/NaNaWeb"}}, "required": ["environment", "serverIp", "username", "password"]}]}, "bpmDbConnection": {"allOf": [{"if": {"properties": {"dbType": {"const": "SQL Server"}}}, "then": {"properties": {"port": {"default": 1433}}, "required": ["database"]}}, {"if": {"properties": {"dbType": {"const": "Oracle"}}}, "then": {"properties": {"port": {"default": 1521}, "serviceName": {"not": {"required": ["sid"]}, "description": "Oracle 的服務名稱 (Service Name)。與 SID 擇一提供。"}, "sid": {"not": {"required": ["serviceName"]}, "description": "Oracle 的系統識別碼 (SID)。與 Service Name 擇一提供。"}}, "oneOf": [{"required": ["serviceName"]}, {"required": ["sid"]}], "dependencies": {"serviceName": {"not": {"required": ["sid"]}}, "sid": {"not": {"required": ["serviceName"]}}}}}]}, "hrmConnection": {"allOf": [{"$ref": "#/definitions/baseConnection"}, {"type": "object", "description": "HRM 主機連線資訊", "properties": {"productType": {"const": "HRM", "description": "固定值 HRM，表示這是人力資源管理系統連線"}, "environment": {"type": "string", "description": "連線環境類型，用於區分測試機或正式機環境", "enum": ["測試機", "正式機"]}, "serverAddress": {"type": "string", "description": "HRM 系統伺服器位址，可以是 IP 或域名"}}, "required": ["environment", "serverAddress"]}]}, "tiptopConnection": {"allOf": [{"$ref": "#/definitions/baseConnection"}, {"type": "object", "description": "Tiptop 系統連線資訊", "properties": {"productType": {"const": "Tiptop", "description": "固定值 Tiptop，表示這是 Tiptop 系統連線"}, "environment": {"type": "string", "description": "連線環境類型，用於區分測試機或正式機環境", "enum": ["測試機", "正式機"]}, "serverIp": {"type": "string", "format": "ip-address", "description": "主機IP位址 (TIPTOP 主機 IP)", "example": "127.0.0.1"}, "identifier": {"type": "string", "description": "識別碼 (TP 主機環境區域名稱)", "enum": ["topprd", "toptest"], "default": "topprd", "example": "topprd"}, "soapUrl": {"type": "string", "format": "uri", "description": "SOAP網址 (TP 主機 SOAP 網址)", "example": "http://127.0.0.1/web/ws/r/aws_efsrv?WSDL"}, "userCode": {"type": "string", "description": "使用者代號 (TP 主機登入者)", "example": "tiptop"}}, "required": ["environment", "serverIp", "identifier", "soapUrl", "userCode"]}]}, "t100Connection": {"allOf": [{"$ref": "#/definitions/baseConnection"}, {"type": "object", "description": "T100 系統連線資訊", "properties": {"productType": {"const": "T100", "description": "固定值 T100，表示這是 T100 系統連線"}, "environment": {"type": "string", "description": "連線環境類型，用於區分測試機或正式機環境", "enum": ["測試機", "正式機"]}, "host": {"type": "string", "format": "ip-address", "description": "T100 網路位置 (T100 主機 IP)", "example": "127.0.0.1"}, "companyCode": {"type": "string", "description": "企業代號 (出貨預設:99)", "example": "99"}, "apiUrl": {"type": "string", "format": "uri", "description": "文檔中心 API URL (T100 的文檔中心 API 接口)", "example": "http://127.0.0.1/api/dmc/v1/buckets/tiptop/files"}, "port": {"type": "integer", "description": "T100 連接阜 (TP 主機環境區域 port 號)", "enum": [1099, 1199], "default": 1099, "example": 1099}, "environmentCode": {"type": "string", "description": "環境代號 (TP 主機環境代號)", "enum": ["topprd", "toptest"], "default": "toptest", "example": "toptest"}}, "required": ["environment", "host", "companyCode", "apiUrl", "port", "environmentCode"]}]}, "workflowConnection": {"allOf": [{"$ref": "#/definitions/baseConnection"}, {"type": "object", "description": "Workflow 系統連線資訊", "properties": {"productType": {"const": "Workflow", "description": "固定值 Workflow"}, "serverIp": {"type": "string", "format": "ip-address", "description": "主機IP位址 (WF 主機 IP，由 WF 提供)"}, "identifier": {"type": "string", "description": "識別碼 (WF 主機環境區域名稱，目前為固定值 = WorkFlowServer1)", "default": "WorkFlowServer1", "example": "WorkFlowServer1"}, "engineUrl": {"type": "string", "format": "uri", "description": "SOAP網址 (WF 主機 SOAP 網址，即 WF web Service 的位置)", "example": "http://127.0.0.1/WFEFGPService/WFEFGPService.asmx?wsdl"}, "adminUser": {"type": "string", "description": "使用者代號 (WF 和 BPM 建相同的 user 帳號)"}}, "required": ["serverIp", "identifier", "engineUrl", "adminUser"]}]}, "imgConnection": {"allOf": [{"$ref": "#/definitions/baseConnection"}, {"type": "object", "description": "IMG 企業行動導航連線資訊", "properties": {"productType": {"const": "IMG", "description": "固定值 IMG"}, "enterpriseNavigationBackend": {"type": "string", "format": "uri", "description": "企業行動導航後台管理系統 URL (例如: http://127.0.0.1:8080)", "example": "http://127.0.0.1:8080"}, "enterpriseNavigationExternalIp": {"type": "string", "format": "ip-address", "description": "行動導航後台對外網路IP (例如: 127.0.0.1)"}, "enterpriseNavigationExternalPort": {"type": "integer", "description": "行動導航後台對外網路Port (例如: 8080)"}, "enterpriseNavigationUsername": {"type": "string", "description": "行動導航後台登入帳號 (例如: admin)", "example": "admin"}, "enterpriseNavigationPassword": {"type": "string", "description": "行動導航後台登入密碼 (例如: Digiwhale136)", "example": "Digiwhale136"}, "ccManagementCenter": {"type": "string", "format": "uri", "description": "互聯應用管理中心(CC) 系統 URL (例如: http://127.0.0.1:22610)", "example": "http://127.0.0.1:22610"}, "ccManagementCenterUsername": {"type": "string", "description": "互聯應用管理中心(CC) 登入帳號 (例如: superadmin/1qaz@WSX)", "example": "superadmin/1qaz@WSX"}, "iamServiceUrl": {"type": "string", "format": "uri", "description": "互聯應用權限管理(IAM) 服務位址 (例如: http://127.0.0.1:22611)", "example": "http://127.0.0.1:22611"}, "iamVerificationUrl": {"type": "string", "format": "uri", "description": "互聯應用權限管理(IAM) 驗證網址 (例如: http://127.0.0.1:22611/api/iam/v2/env)", "example": "http://127.0.0.1:22611/api/iam/v2/env"}}, "required": ["enterpriseNavigationBackend", "enterpriseNavigationExternalIp", "enterpriseNavigationExternalPort", "enterpriseNavigationUsername", "enterpriseNavigationPassword", "ccManagementCenter", "ccManagementCenterUsername", "iamServiceUrl", "iamVerificationUrl"]}]}, "cosmosConnection": {"allOf": [{"$ref": "#/definitions/baseConnection"}, {"type": "object", "description": "Cosmos 系統連線資訊", "properties": {"productType": {"const": "Cosmos", "description": "固定值 Cosmos，表示這是 Cosmos 系統連線"}, "serverIp": {"type": "string", "format": "ip-address", "description": "主機IP位址 (COSMOS 主機 IP，由 CM 提供)", "example": "***************"}, "identifier": {"type": "string", "description": "識別碼 (COSMOS 主機環境區域名稱，固定值 = COSMOS1)", "default": "COSMOS1", "example": "COSMOS1"}, "soapUrl": {"type": "string", "format": "uri", "description": "SOAP網址 (COSMOS WebService 的位置，由 CM 提供)", "example": "http://127.0.0.1/CMEFGP/CMEFGPService.asmx?wsdl"}, "userCode": {"type": "string", "description": "使用者代號 (CM 和 BPM 建相同的 user 帳號)", "example": "DS"}}, "required": ["serverIp", "identifier", "soapUrl", "userCode"]}]}}}