# Release Notes - NaNaXWeb

## 版本資訊
- **新版本**: 5.8.10.4_hotfix
- **舊版本**: 5.8.10.4_202412101833_autobuild
- **生成時間**: 2025-07-18 15:44:51
- **新增 Commit 數量**: 12

## 變更摘要

### yamiyeh10 (8 commits)

- **2025-06-26 17:44:25**: [PRODT]C01-20250626003 修正Web流程管理中設定關卡的表單存取控管設定無法儲存問題
  - 變更檔案: 1 個
- **2025-07-11 11:35:47**: [PRODT]C01-20250709004 修正Web流程管理工具中核決關卡參考活動為自定義且未異動時儲存會清空所有關卡參與者問題
  - 變更檔案: 1 個
- **2025-03-24 11:09:41**: [PRODT]C01-20250313002 修正Web流程管理工具中使用核決關卡且參考活動為自定義時無法儲存定義問題
  - 變更檔案: 1 個
- **2025-02-26 11:21:12**: [PRODT]C01-20250224003 修正Web流程管理工具的表單存取控管設定刪除後重新設定會無法儲存問題
  - 變更檔案: 1 個
- **2025-01-08 16:45:48**: [PRODT]C01-20250106008 修正Web流程管理工具的註解元件會消失問題
  - 變更檔案: 1 個
- **2024-12-26 16:17:38**: [PRODT]C01-20241225001 調整Web流程管理工具中關卡上有多個進入點時增加顯示提示訊息並且無法儲存流程
  - 變更檔案: 1 個
- **2024-12-17 17:03:04**: [PRODT]C01-20241216006 修正Web流程管理工具中活動參與者異常導致派送流程時拋錯問題
  - 變更檔案: 4 個
- **2024-12-12 16:15:52**: [PRODT]C01-20241209004 修正Web流程管理工具中活動參與者為策略分配時無法儲存定義問題
  - 變更檔案: 2 個

### 周权 (4 commits)

- **2025-06-27 09:35:41**: [word套表]C01-20250624001 iReport报表列印调整可选择表单版本
  - 變更檔案: 1 個
- **2025-02-08 15:03:47**: [文件總結助手] 調整文件總結助手使用權限管理，長知識關聯作業兩隻作業未長出ScrollBar的問題
  - 變更檔案: 4 個
- **2025-02-08 14:57:42**: [文件總結助手] 調整助閲讀關聯作業沒有ScrollBar的問題
  - 變更檔案: 2 個
- **2025-01-16 14:45:03**: [[word套表]]C01-20250115003 调整新增刪除報表定義檔后，選擇報表定義列表不會刷新的問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [PRODT]C01-20250626003 修正Web流程管理中設定關卡的表單存取控管設定無法儲存問題
- **Commit ID**: `5bfb9acb037a25bd3052398a5606b2a968a7abb8`
- **作者**: yamiyeh10
- **日期**: 2025-06-26 17:44:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts`

### 2. [PRODT]C01-20250709004 修正Web流程管理工具中核決關卡參考活動為自定義且未異動時儲存會清空所有關卡參與者問題
- **Commit ID**: `27b332f50a58f4788d1134477188d4ed120a06e0`
- **作者**: yamiyeh10
- **日期**: 2025-07-11 11:35:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-general-attribute.component.ts`

### 3. [word套表]C01-20250624001 iReport报表列印调整可选择表单版本
- **Commit ID**: `3781fffbb38258c0218ae0bf1677f0ff693cc583`
- **作者**: 周权
- **日期**: 2025-06-27 09:35:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/report/report-manage-tool/report-manage/report-manage.component.html`

### 4. [PRODT]C01-20250313002 修正Web流程管理工具中使用核決關卡且參考活動為自定義時無法儲存定義問題
- **Commit ID**: `77e5926ef70a42bdf596a7f06a148d9de0d581eb`
- **作者**: yamiyeh10
- **日期**: 2025-03-24 11:09:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 5. [PRODT]C01-20250224003 修正Web流程管理工具的表單存取控管設定刪除後重新設定會無法儲存問題
- **Commit ID**: `e6aec9440d10f16794531fddd7f3a8639dadf2e8`
- **作者**: yamiyeh10
- **日期**: 2025-02-26 11:21:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts`

### 6. [文件總結助手] 調整文件總結助手使用權限管理，長知識關聯作業兩隻作業未長出ScrollBar的問題
- **Commit ID**: `613289439512f88c423d5c5a16685ff0f156c7b6`
- **作者**: 周权
- **日期**: 2025-02-08 15:03:47
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/administrativ-permission/administrativ-permission-list/administrativ-permission-list.component.html`
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/administrativ-permission/administrativ-permission-list/administrativ-permission-list.component.ts`
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/grow-knowledge/grow-knowledge-file-table/grow-knowledge-file-table.component.html`
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/grow-knowledge/grow-knowledge-file-table/grow-knowledge-file-table.component.ts`

### 7. [文件總結助手] 調整助閲讀關聯作業沒有ScrollBar的問題
- **Commit ID**: `d9b76875f00aab99168d29d1d130fa769fbe60ad`
- **作者**: 周权
- **日期**: 2025-02-08 14:57:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/assisted-reading/assisted-reading-list/assisted-reading-list.component.html`
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/assisted-reading/assisted-reading-list/assisted-reading-list.component.ts`

### 8. [[word套表]]C01-20250115003 调整新增刪除報表定義檔后，選擇報表定義列表不會刷新的問題
- **Commit ID**: `8d72cdf772452a191ae5245f087b2bb4858d6177`
- **作者**: 周权
- **日期**: 2025-01-16 14:45:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/report/report-manage-tool/report-manage/report-manage.component.ts`

### 9. [PRODT]C01-20250106008 修正Web流程管理工具的註解元件會消失問題
- **Commit ID**: `71366df5bf3f6abc36b2ecb19ab052ac1364c4e6`
- **作者**: yamiyeh10
- **日期**: 2025-01-08 16:45:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 10. [PRODT]C01-20241225001 調整Web流程管理工具中關卡上有多個進入點時增加顯示提示訊息並且無法儲存流程
- **Commit ID**: `aff37a8b86e9f6e83cf94a616283b73af11d12e8`
- **作者**: yamiyeh10
- **日期**: 2024-12-26 16:17:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 11. [PRODT]C01-20241216006 修正Web流程管理工具中活動參與者異常導致派送流程時拋錯問題
- **Commit ID**: `386cb6e3011e4ec68133823bc10e9c7152a91fa1`
- **作者**: yamiyeh10
- **日期**: 2024-12-17 17:03:04
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/process-relationship/process-relationship.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/strategy-assign/strategy-assign.component.ts`

### 12. [PRODT]C01-20241209004 修正Web流程管理工具中活動參與者為策略分配時無法儲存定義問題
- **Commit ID**: `1c88ce9857edb79870ca0b7b4e0bef856b962e2a`
- **作者**: yamiyeh10
- **日期**: 2024-12-12 16:15:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/participant-chooser.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/strategy-assign/strategy-assign.component.ts`

