# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.8.3_20220922
- **舊版本**: release_5.8.8.3
- **生成時間**: 2025-07-18 11:11:01
- **新增 Commit 數量**: 24

## 變更摘要

### walter_wu (1 commits)

- **2022-07-29 00:04:37**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
  - 變更檔案: 3 個

### 謝閔皓 (7 commits)

- **2022-10-06 13:39:46**: [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
  - 變更檔案: 1 個
- **2022-09-30 15:47:30**: [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]
  - 變更檔案: 1 個
- **2022-08-23 15:27:14**: [流程引擎]S00-20220722001新增批次通知信件主旨內容
  - 變更檔案: 1 個
- **2022-08-11 12:56:57**: [Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題
  - 變更檔案: 1 個
- **2022-08-10 18:34:57**: [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
  - 變更檔案: 1 個
- **2022-09-30 12:24:25**: [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員
  - 變更檔案: 1 個
- **2022-08-19 10:49:46**: [Web]S00-20220810001簽核意見是否顯示管理員
  - 變更檔案: 5 個

### raven.917 (2 commits)

- **2022-10-06 08:57:33**: [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用(補修正，增加可讀性)
  - 變更檔案: 1 個
- **2022-10-04 15:26:58**: [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用
  - 變更檔案: 1 個

### waynechang (3 commits)

- **2022-09-20 11:19:53**: [Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊
  - 變更檔案: 1 個
- **2022-08-18 11:43:00**: [流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤
  - 變更檔案: 1 個
- **2022-09-20 14:55:38**: [TIPTOP]A00-*********** 新增TIPTOP整合設定，當夾帶附件型態為http,根據TIPTOP附件主機的port號取得附件
  - 變更檔案: 5 個

### 王鵬程 (2 commits)

- **2022-07-29 16:49:53**: [Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示
  - 變更檔案: 1 個
- **2022-07-28 17:32:17**: [Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行
  - 變更檔案: 1 個

### 林致帆 (5 commits)

- **2022-09-27 08:36:26**: [T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容
  - 變更檔案: 1 個
- **2022-08-19 17:57:37**: [TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久
  - 變更檔案: 1 個
- **2022-08-18 17:50:32**: [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]
  - 變更檔案: 1 個
- **2022-08-18 17:47:19**: [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]
  - 變更檔案: 2 個
- **2022-08-18 17:41:56**: [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到
  - 變更檔案: 2 個

### kmin (3 commits)

- **2022-09-22 14:48:45**: Revert "[內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題"
  - 變更檔案: 4 個
- **2022-09-22 14:17:24**: \\解決build問題
  - 變更檔案: 1 個
- **2022-09-22 13:35:57**: //因系統權限管理頁面開啟緩慢問題暫時把系統管理打開
  - 變更檔案: 1 個

### yamiyeh10 (1 commits)

- **2022-09-15 10:23:28**: [內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題
  - 變更檔案: 4 個

## 詳細變更記錄

### 1. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
- **Commit ID**: `952e2a312ef78e285340a7a5091ca97760896d3c`
- **作者**: walter_wu
- **日期**: 2022-07-29 00:04:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 2. [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
- **Commit ID**: `0b8e5a0e715d3cc068eead2fda9dd4d390f75930`
- **作者**: 謝閔皓
- **日期**: 2022-10-06 13:39:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`

### 3. [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用(補修正，增加可讀性)
- **Commit ID**: `6e49bd4e6c24218ad20ef9accbb6f27c54092b79`
- **作者**: raven.917
- **日期**: 2022-10-06 08:57:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 4. [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用
- **Commit ID**: `9bd4e833688ab6f32960e161dc6436ed474c52d3`
- **作者**: raven.917
- **日期**: 2022-10-04 15:26:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 5. [Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊
- **Commit ID**: `6d1e492310f26356f137c1ddf4bb400249494fc9`
- **作者**: waynechang
- **日期**: 2022-09-20 11:19:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 6. [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]
- **Commit ID**: `ba8b04b0f684772273c2221d8ef1d06727bac06a`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 15:47:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 7. [流程引擎]S00-20220722001新增批次通知信件主旨內容
- **Commit ID**: `914b16ce2ac8e95f7a7296f3b59a785ed7d368d0`
- **作者**: 謝閔皓
- **日期**: 2022-08-23 15:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 8. [Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題
- **Commit ID**: `3fd41c94a6424069892033852aadb2a5f325cdf6`
- **作者**: 謝閔皓
- **日期**: 2022-08-11 12:56:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 9. [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
- **Commit ID**: `50a9d56980d442453795eb0ed72d5f472c03b1b2`
- **作者**: 謝閔皓
- **日期**: 2022-08-10 18:34:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 10. [Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示
- **Commit ID**: `877997712019745dd8c4455892c709e3de1b7917`
- **作者**: 王鵬程
- **日期**: 2022-07-29 16:49:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 11. [Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行
- **Commit ID**: `aebbd71392ecf3a06f077f5d682427042618ff22`
- **作者**: 王鵬程
- **日期**: 2022-07-28 17:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 12. [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員
- **Commit ID**: `f6b1d14f80cfe1bb3eacb992d74c89626125fa23`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 12:24:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java`

### 13. [T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容
- **Commit ID**: `de0d3ebdc8b2af539490605dfc6cfb0ea6456c6e`
- **作者**: 林致帆
- **日期**: 2022-09-27 08:36:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`

### 14. [流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤
- **Commit ID**: `ebd9678095c9cadf9210f3e21557b4117f3b6b6c`
- **作者**: waynechang
- **日期**: 2022-08-18 11:43:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java`

### 15. Revert "[內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題"
- **Commit ID**: `71aa2f2a2d3926391c9d9e2570b3345b475d88b0`
- **作者**: kmin
- **日期**: 2022-09-22 14:48:45
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/design_tool_web/SystemManageTool.java`

### 16. \\解決build問題
- **Commit ID**: `a2cc011c161988a25b2e04dbcdfdc95857b6dedb`
- **作者**: kmin
- **日期**: 2022-09-22 14:17:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MFAConfigManagerDelegate.java`

### 17. //因系統權限管理頁面開啟緩慢問題暫時把系統管理打開
- **Commit ID**: `bba87ab56dcf5ae63b8cd9bee5982f3de8f6aa4d`
- **作者**: kmin
- **日期**: 2022-09-22 13:35:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java`

### 18. [TIPTOP]A00-*********** 新增TIPTOP整合設定，當夾帶附件型態為http,根據TIPTOP附件主機的port號取得附件
- **Commit ID**: `6919e0e16020bf366b2d034cf58367450b80c63c`
- **作者**: waynechang
- **日期**: 2022-09-20 14:55:38
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql`

### 19. [內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題
- **Commit ID**: `068fc48aa0f6e962ddf7aade573e2c2b329fe5c3`
- **作者**: yamiyeh10
- **日期**: 2022-09-15 10:23:28
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/design_tool_web/SystemManageTool.java`

### 20. [TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久
- **Commit ID**: `c6557868b263216f2e9c200fe70f496ace493633`
- **作者**: 林致帆
- **日期**: 2022-08-19 17:57:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 21. [Web]S00-20220810001簽核意見是否顯示管理員
- **Commit ID**: `668493fea9bced6d17164564040c7334c9a637ce`
- **作者**: 謝閔皓
- **日期**: 2022-08-19 10:49:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql`

### 22. [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]
- **Commit ID**: `06e9149b26113fcd318a6c0c026a2fd291a5bd10`
- **作者**: 林致帆
- **日期**: 2022-08-18 17:50:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 23. [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]
- **Commit ID**: `320137581f657a4131ec7aa2604ee7d042b347e7`
- **作者**: 林致帆
- **日期**: 2022-08-18 17:47:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

### 24. [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到
- **Commit ID**: `8037c65f3dc300d41af2dddf23592991d71230df`
- **作者**: 林致帆
- **日期**: 2022-08-18 17:41:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

