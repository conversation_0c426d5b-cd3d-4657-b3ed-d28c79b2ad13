{"company_id": "02827000", "company_name": "堃霖冷凍", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\02827000-堃霖冷凍\\01客戶基本資料", "files": [{"filename": "堃霖-連線資訊.txt", "raw_content": "teamviewer 5831\r\n\r\n正式機\r\n***************:8086/NaNaWeb\r\nweb pass:1234\r\n\r\nDB：***************\r\nSql#dsc2018\r\n\r\n測試機\r\n***************:8086/NaNaWeb", "structured_data": {"database": "***************", "***************": "8086/NaNaWeb", "web pass": "1234", "***************": "8086/NaNaWeb", "host": "***************"}, "source_path": "C1.客戶維護相關\\02827000-堃霖冷凍\\01客戶基本資料\\堃霖-連線資訊.txt", "file_size": 146, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:31.601701"}], "total_files": 1, "processed_at": "2025-08-26T10:46:31.601713"}