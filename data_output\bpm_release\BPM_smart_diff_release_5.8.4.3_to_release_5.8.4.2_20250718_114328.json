{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.4.3", "date": "2022-06-26 22:44:16", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.3", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.4.2", "date": "2022-06-26 22:32:36", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.2", "author": "lorenchang"}, "比較時間": "2025-07-18 11:43:28", "新增commit數量": 45, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "21598df23a87db073b57611fc81a20bd0d1dcd9e", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.3", "提交日期": "2022-06-26 22:44:16", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "f5121a2424c1035c1a3aca8fadb9df8c2de805bf", "commit_訊息": "[內部]Q00-20201028003 調整程式排版", "提交日期": "2020-10-28 15:08:09", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f034e3f2690fa618c271a3ba28dc6f80ad9fc9d6", "commit_訊息": "[內部]Q00-20201028001 調整程式排版[補]", "提交日期": "2020-10-28 14:08:05", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/ApplicationToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b6c8e1926db3d6eb4f2d16eb4f5273d3d7ea321b", "commit_訊息": "Revert \"[內部]Q00-20201028001 調整程式排版[補]\"", "提交日期": "2020-10-28 14:05:56", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/ApplicationToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "20c2e1457e0e7a7af74e1ed509d8ee738cb21b5e", "commit_訊息": "[內部]Q00-20201028001 調整程式排版[補]", "提交日期": "2020-10-28 14:04:52", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/ApplicationToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f061277dc2d81d44265ce1c5f619e0e1a998f0ab", "commit_訊息": "[內部]Q00-20201028002 調整程式排版", "提交日期": "2020-10-28 11:51:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/SessionBeanToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a791aa4f1995af7f9ebd2f58ac4bbe648318979", "commit_訊息": "[內部]Q00-20201028001 調整程式排版", "提交日期": "2020-10-28 11:40:54", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/ApplicationToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3363233ed9aa5f2d1690f40a5e06623df1367fd", "commit_訊息": "[BPM APP]Q00-20201026004 修正企業微信在進入待辦顯示錯誤頁面時上方導覽列應不可被點擊的問題", "提交日期": "2020-10-26 19:20:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0dab9c7e4bbf7cf5baa6537c2bbe6aff00c9afd0", "commit_訊息": "[BPM APP]Q00-20201026003調整行動端支持流程關卡活動設定\"必須上傳新附件\"，補上多語系", "提交日期": "2020-10-26 18:43:34", "作者": "詩雅", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ce54fa1a108e0add9699e6a6d82d964d9dcf901", "commit_訊息": "[BPM APP]C01-20200907001 修正移動端(整合钉钉)在認證網址重導後會收到兩次請求問題", "提交日期": "2020-10-23 13:48:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc993eec8443b0aa32c1082427c8cab150f812a5", "commit_訊息": "[BPM APP]C01-20200907001 修正移動端(整合钉钉)在建立發請求的連線時會共用一份導致報錯的問題", "提交日期": "2020-10-23 11:38:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "20357ebefa84d323173aa14f187214a7edfe6396", "commit_訊息": "[流程設計師]Q00-20201023001 調整程式排版", "提交日期": "2020-10-23 11:30:26", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "858ac5205f22fdd609aa9ccb02d309d96837d471", "commit_訊息": "[流程引擎]Q00-20201022004 修正待辦清單接口-流程發起人條件輸入發起人名稱，查詢結果有誤", "提交日期": "2020-10-22 18:33:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36871547f12e9bdc0e350bad46c7cde717e48d6a", "commit_訊息": "[Web]Q00-20201021002 修正欄位有設定小數顯示的位數時，加到grid中會是完整小數數值", "提交日期": "2020-10-21 14:58:48", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2853c548bf9f644a82bbdf63cdebdd2fa4054e33", "commit_訊息": "[流程引擎]Q00-20201015001 修正系統管理員監控流程查詢功能異常", "提交日期": "2020-10-21 14:05:53", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "55add5d8441c8f8cbe5d55072bb52f888456b8cb", "commit_訊息": "[Web]Q00-20201016001 左邊選單系統設定線上人數查詢 頁面，增加 顯示「已使用授權數/總授權數」 功能", "提交日期": "2020-10-16 14:46:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/OnlineUserAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a8d7e66b3af6d1ed4ec16b4739f3d6a0bd432b53", "commit_訊息": "[BPM APP]S00-20200525002調整行動端支持流程關卡活動設定\"必須上傳新附件\"", "提交日期": "2020-10-15 18:54:28", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmWorkItemDataVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BPMPerformRequestTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "877525acf10881988c42cad44a9f83c74223a2bd", "commit_訊息": "[Web]Q00-20201014002 修正開窗中有勾選使用快速搜尋，當改成自訂義開窗後欄位仍然可以手動輸入的問題", "提交日期": "2020-10-14 16:24:51", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4033b57480e451d4ce82bb9941f4d3b723a5bc7a", "commit_訊息": "[流程設計師]Q00-20201014001 調整程式排版", "提交日期": "2020-10-14 11:31:51", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "205ee10ea42029a11bf07660ecb617887e94e628", "commit_訊息": "[BPM APP]C01-20201009001 修正IMG詳情表單簽核意見顯示不全的問題", "提交日期": "2020-10-13 16:51:05", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b1703c0cec973c07f89fdb9d0b82d8c3265f943", "commit_訊息": "[流程引擎]Q00-20201013003 修正系統管理員監控流程清單中查詢條件\"流程發起人\"無效", "提交日期": "2020-10-13 14:21:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0ccb2ea50696a5312241519e17cb73cdf9f78762", "commit_訊息": "[流程引擎]Q00-20201013002 修正流程設計師當關卡處理者為「角色」時,且角色名稱為簡體時，開啟處理者會報錯", "提交日期": "2020-10-13 10:40:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b886e7a653c5197a8767e74d46972876d5ac7530", "commit_訊息": "[BPM APP]Q00-20200923006修改IMG中間層附件可依照流程設定顯示[補]", "提交日期": "2020-10-13 10:36:08", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a7c6b5debb062dc68a2599cb81a6c8ee4e171e11", "commit_訊息": "[流程引擎]Q00-20201013001 調整程式排版", "提交日期": "2020-10-13 10:13:12", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c514d7d4736aa58697c1486a8888ac32c227e08a", "commit_訊息": "[流程引擎]Q00-20201012001 調整程式排版", "提交日期": "2020-10-12 15:54:54", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b23810491c4c5ae5bf22409a0163cd6a4a530e7b", "commit_訊息": "[BPM APP]Q00-20200926004 修正詳情表單流程關卡設定元件隱藏時多欄元件會顯示最外層框的問題", "提交日期": "2020-10-08 15:00:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "141a7fae3d7cf08120f3a72ee9765aa70468ffa0", "commit_訊息": "[BPM APP]C01-20201006003 調整智能快簽明日提醒和後天提醒走BPM外網無法正常執行的問題", "提交日期": "2020-10-08 13:47:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "693b528ebed8803e985ed7dfedf4b61f7ebad342", "commit_訊息": "[Web]Q00-20201007003 修正當流程名稱太長時，在流程處理/發起流程 頁面中無法完整顯示流程名稱", "提交日期": "2020-10-07 18:17:41", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "675b6a58bfeccdecee54cc776e52eec2a84abc89", "commit_訊息": "[T100]S00-20200812001 同一單號重新送簽，應將之前流程標示作廢", "提交日期": "2020-10-07 14:25:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "88b87ee4b164f6997eb6a06d3ee5d238e1cdd633", "commit_訊息": "[Web]S00-20200811001 調整使用者為多部門簽核時部門開窗顯示問題", "提交日期": "2020-10-06 18:24:44", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dae32855059aee716e49c8e642c6a270ec0d2ad7", "commit_訊息": "[Web]S00-20200810001 調整發送通知與增加關卡輸入意見時可選擇片語", "提交日期": "2020-10-06 15:12:46", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6e95f75f96bc8669bba94629d9630a5f7f174834", "commit_訊息": "[Web]A00-20200518001 修正當部門名稱有雙引號時會導致無法發起流程", "提交日期": "2020-10-05 15:48:07", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/TreeViewDataChooserUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "425b29342015c8059b4c1ef8a7465950ba001402", "commit_訊息": "[流程引擎]A00-20200910002 修正流程負責人監控流程以\"目前處理者\"為查詢條件，查詢結果異常[補]", "提交日期": "2020-10-05 13:48:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d8faa58da041d62ed81004db1d3afdfc7db2b94", "commit_訊息": "[ESS]更新ESS表單", "提交日期": "2020-10-05 10:40:27", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF17\\351\\212\\267\\345\\201\\207\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3a59ab2cdf43b2c01b17f5373a693883131af6da", "commit_訊息": "[Web]C01-20200911001 修正Win10 IE11 Attachment元件文字下半部被截掉", "提交日期": "2020-09-30 15:42:40", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f6f2eeea63a8f1e130d45d6b11ff5a597544f63a", "commit_訊息": "[Web]Q00-20200928002 修正在行動版畫面時，監控流程頁面中無法刪除流程和撤銷流程", "提交日期": "2020-09-28 18:27:08", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6208d204897460e7108ae01ebb821523b6e32c83", "commit_訊息": "[Web]C01-20200921002 修正先將畫面調為行動版後在登入BPM，在監控流程頁面中慢慢將畫面拉至PC版面，更多的按鈕位置會跑版。", "提交日期": "2020-09-28 18:07:05", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a6d32e63df8d22683b6b04dfb79cccd13d669e36", "commit_訊息": "[Web]A00-20200924002修正TextBox欄位點選顯示千分位的狀況下，有其他欄位為必填未填寫時，按派送或儲存表單，千分位符號會消失", "提交日期": "2020-09-28 14:55:42", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "4a69e9886345b54fef3fbe80789438b0ef84dc29", "commit_訊息": "[Web]A00-20200924002修正TextBox欄位點選顯示千分位的狀況下，有其他欄位為必填未填寫時，按派送或儲存表單，千分位符號會消失", "提交日期": "2020-09-28 14:55:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aabc837a8b85367824c48a4c2a306ac9b2d698c6", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2020-09-26 15:21:35", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "764f494d7165f195cfeb7a41eec721b57a906b4f", "commit_訊息": "[Web]C01-20200806002 修正加簽後流程圖的分支線有重疊的狀況", "提交日期": "2020-09-26 15:21:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bd246c696236828af906dd26d9262ead336973cd", "commit_訊息": "[Web]A00-20200916003 修正在IOS從監控流程和待辦事項中進入掛雙表單的流程，點擊切換表單後導致無法操作", "提交日期": "2020-09-26 09:35:55", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a40c1f384f87e57b74ff9b6343b2ebdf0180e610", "commit_訊息": "[Web]A00-20200922002 修正從mail待辦進入，若已經是最後一筆資料則直接回到待辦事項清單頁面.", "提交日期": "2020-09-26 09:23:21", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "00a9839d5a20d457bc18e256e4a6d3e5686d2e5d", "commit_訊息": "[Web]A00-20200813002 修正舊版表單有下拉元件(ComboBox)，儲存表單畫面卡住", "提交日期": "2020-09-26 09:11:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "80c21bd3b740841c0b646098929b96a8d45337cd", "commit_訊息": "[Web]A00-20200924005 修正外部連結\"組織資料查詢功能\"點選部門後報錯", "提交日期": "2020-09-26 08:37:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchOrgData/ViewOrgData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}