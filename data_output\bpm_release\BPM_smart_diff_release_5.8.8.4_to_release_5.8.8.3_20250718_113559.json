{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.8.4", "date": "2022-11-16 14:27:02", "message": "[Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題", "author": "<PERSON><PERSON><PERSON>"}, "舊分支": {"branch_name": "release_5.8.8.3", "date": "2022-07-21 16:48:19", "message": "[ESS]Q00-20220721001 修正ESS儲存草稿後並沒有成功新增到草稿上[補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 11:35:59", "新增commit數量": 270, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "02af0f371b5d77fba63b1bf065e95774cffc6818", "commit_訊息": "[Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題", "提交日期": "2022-11-16 14:27:02", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "913748b5cd406437318ec3a77f71a92cdbe99a89", "commit_訊息": "[WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定", "提交日期": "2022-11-01 12:00:20", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "46c0cee9ace45ed54323753f3e30e159e8fdb114", "commit_訊息": "[雙因素模組]Q00-20221026005 在未授權時，BPM首頁左側功能列會顯示雙因素模組功能", "提交日期": "2022-10-26 16:02:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd35d2fef8d5d0389f58faa428bfeb3dec5253fc", "commit_訊息": "[內部]更新5.8.8.4 patch檔", "提交日期": "2022-10-24 15:02:42", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "28d2db4fb8f62dc5c830b975e49a1ab837d87d06", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-24 13:29:55", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a6e29a083c666c9fafcb9b76ce5abd8603f8daf", "commit_訊息": "[WorkFlow]Q00-20221024001 WorkFlow移除標準表單ASTI02", "提交日期": "2022-10-24 10:16:39", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0d642e422be143046f52a4314fba00d8044d8c8", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的多語系", "提交日期": "2022-10-21 20:36:58", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe80ea497dcd8ffcd98d9408957e9090bee94b76", "commit_訊息": "[雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]", "提交日期": "2022-10-21 14:39:33", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c733e6c8e3d6f0a18f32b32c82dd55444d7a9507", "commit_訊息": "[雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]", "提交日期": "2022-10-21 14:30:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9fc5859e0086ec055fbf5fafbbe225fb0eeef4d3", "commit_訊息": "[雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]", "提交日期": "2022-10-21 11:38:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5c1200aa6972c360b28c49fd0c0b809c61735b53", "commit_訊息": "[雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤", "提交日期": "2022-10-21 11:06:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ae5cb7bc68f2d8f6bedeb727a44873be2a80f0e1", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-19 20:01:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ad14a826a182920534efd4684fb27d411ed0a27c", "commit_訊息": "[WEB]Q00-20221014001為相容56版上來的客戶，沒有唯讀背景顏色設定，補防呆", "提交日期": "2022-10-19 17:09:54", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "a3738f6c09dac28ec3882257103b9a3654fbcd41", "commit_訊息": "[內部]新增B2B文件雲", "提交日期": "2022-10-19 17:07:47", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2912cc10a873edba19767ec03b38ae4a9c949c4d", "commit_訊息": "[內部]調整bpm-tools點組織設計工具時提示已Web化訊息[補]", "提交日期": "2022-10-19 11:45:03", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@designer/NaNaTools.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4382fdbabf03c21323803a10db8360c6f5b64730", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2022-10-18 17:38:26", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "82343fe2642cfe5ee391da910eb1dbdb6ee1d6ba", "commit_訊息": "[內部]更新5.8.8.4 patch檔", "提交日期": "2022-10-18 17:38:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e6ce9b366997458ab1881fe84a408b29b7211b02", "commit_訊息": "[內部]調整bpm-tools點組織設計工具時提示已Web化訊息[補]", "提交日期": "2022-10-18 17:12:22", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/DesignerChooseDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "66b3391d9e0aade714a46a38e31d481207731025", "commit_訊息": "[SAP]S00-20220506006 調整 SAP 欄位對應設定，在新增時將呼叫型態固定為 Ajax，並且在畫面中隱藏", "提交日期": "2022-10-18 16:35:00", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "46de7cf3aecb34ba517c4c17af28f2a7a8a9789c", "commit_訊息": "[內部]調整bpm-tools點組織設計工具時提示已Web化訊息", "提交日期": "2022-10-18 13:02:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb49aa2603a80cf6163b824944da797ae70aa2d3", "commit_訊息": "[ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視", "提交日期": "2022-10-18 10:33:07", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4101280b6eb520e94055ad87181f496d4beddb40", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-18 09:18:27", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6fed0239eea34808782d84c09e42d8f3fb621654", "commit_訊息": "[BPM APP]C01-20221013006 調整企業微信在同步使用者時的同步狀態欄位若有簡體字會呈現問號的問題", "提交日期": "2022-10-17 19:04:07", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.8.4_mobile_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "f9b7e7baee72b028aeb92b48a4e9f14da9918e8f", "commit_訊息": "[組織同步]Q00-20221017001 調整5883 updateSQL判斷中介表有建立就不需要在移除後重新再建立", "提交日期": "2022-10-17 14:30:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "986d9bf2b9eb3b9fada2556fa95c4d60814b6a80", "commit_訊息": "[WEB]Q00-20221014003修正變更經常選取對象 & 變更您的關係人，更新資料後沒有即時刷新頁面問題。", "提交日期": "2022-10-17 10:37:56", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f7c0f0769bf8fd124ba430acf8d0b50c1090a706", "commit_訊息": "[WEB]A00-20221014001修正簽核意見斷行顯示。", "提交日期": "2022-10-17 08:52:43", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "31c98b9a321eec7f84e84f72e7d37debbf48841a", "commit_訊息": "[TIPTOP]Q00-20221014007修正客戶從TIPTOP端udm_tree操作原稿匣撤銷流程時，選擇特定流程後，BPM仍會回傳所有可撤銷流程的清單", "提交日期": "2022-10-15 11:47:52", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "654ddac6596904f4c4c8605db05940301c1f56dd", "commit_訊息": "[WorkFlow]]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能", "提交日期": "2022-10-14 17:38:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "e9667d0ee40c11be6f9d8c9e05eda63a1c378523", "commit_訊息": "[BPM APP]C01-20220826004 調整企業微信同步時獲取部門成員與子部門列表的接口為新接口", "提交日期": "2022-10-14 17:02:04", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/wechat/MobileWeChatService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a7e9c480af2581fd6014e069ffc6f25afdea85af", "commit_訊息": "[內部]V00-20221012007 調整在線閱覽管理/轉檔異常處理作業 的「執行」欄位寬度", "提交日期": "2022-10-14 14:45:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "236a7abf515f81f6401a299362d852959f4cec23", "commit_訊息": "[流程設計師]A00-20221012001 修正流程設計師當子流程有變更代號時，流程簽入新版時，資料庫的子流程代號未更新", "提交日期": "2022-10-14 14:29:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/SubflowActivityMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "caaafadbcc58ea51fad17361a5bc3c21de8453c9", "commit_訊息": "[內部]V00-20221013001 修正修改使用者資料中復職操作的問題", "提交日期": "2022-10-14 14:01:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8bf645092bc65160151958536e9afe0aeec880d", "commit_訊息": "[WEB]Q00-20221014001為相容56版上來的客戶，沒有唯讀背景顏色設定。", "提交日期": "2022-10-14 12:52:12", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "3937ecff27e9f65f8692b6acb9f8f710cf812937", "commit_訊息": "[雙因素模組]V00-20221012001 修正郵件認證在登入輸入錯誤密碼還是會寄驗證信[補修正]", "提交日期": "2022-10-14 11:22:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f40e4cd5ffe835f270fca348d1dbdd0be7be2ba0", "commit_訊息": "[內部]V00-20221012008修正流程管理/監控流程 選擇「已關閉」流程，匯出Excel發現多了簽核時間的欄。", "提交日期": "2022-10-14 09:17:30", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09011944be1092879b95ab7aeb21bee480678b08", "commit_訊息": "[內部]V00-20221013004、20221013005 修正維護核准層級異常問題", "提交日期": "2022-10-13 19:13:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d2c8c6e8f6bc6ffad31b0f6b554a5744616a2f9a", "commit_訊息": "[WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。(改)", "提交日期": "2022-10-13 15:36:41", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "91ff01a7d66a0d4f24d1e8d891bec46283324b87", "commit_訊息": "[內部]V00-20221012004 修正組織節點無排序問題", "提交日期": "2022-10-13 14:56:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8cbf837214c7f4d7d2d3d4a0838428f21aa63f0f", "commit_訊息": "[WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。", "提交日期": "2022-10-13 14:03:06", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f2d0527cffb19c911ab791d6fcc55c99beed06c", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-13 13:44:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c0306f1c819e2ce4a9b0c0e7dc96876f4bad643f", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-13 11:52:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e999acb7c0e5d23cf6f34688bc84c962bae4aa51", "commit_訊息": "[雙因素模組]V00-20221012002 移除未認證清單的搜尋按鈕", "提交日期": "2022-10-13 11:46:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/TFAModule/TFAUnauthlist.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a6b13f5f391c21ade9e54d43484fc026df2f6c6f", "commit_訊息": "[雙因素模組]V00-20221012001 修正郵件認證在登入輸入錯誤密碼還是會寄驗證信", "提交日期": "2022-10-13 11:42:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e05497244efb8a34ae58631b35eb58a75d8eee42", "commit_訊息": "[WEB]Q00-20221013002:修正表單欄位有設定 \"唯讀\"時的欄位顏色，顯示卻都為背景顏色。", "提交日期": "2022-10-13 11:28:13", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "b34d2c9f51516617ea17209416600449bbcf6cab", "commit_訊息": "[WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，找不到欄位ID時的Alert訊息", "提交日期": "2022-10-13 11:06:45", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2813b668bf14466b46542acdf0f0cf391ee3fd9a", "commit_訊息": "[WEB]Q00-20221013001修正簽核意見沒有換行符號。", "提交日期": "2022-10-13 08:37:30", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/StringUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3cd993fcec17497f9fe32266c26ddb4d58434ef7", "commit_訊息": "[WorkFlow]Q00-20221012003 調整WF傳狀態Action為5時，回寫關卡須回傳狀態8回去", "提交日期": "2022-10-12 15:45:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "272fe4fc4bc384d21e68bf7109369f31151e098b", "commit_訊息": "[Web]Q00-20221012002修正員工工作轉派的轉派意見若只有輸入單一個反斜線，則使用者的待辦事項無法呈現的問題", "提交日期": "2022-10-12 13:49:55", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cdc2ce7f6068b0d08803f7b34d5994598fbfe8ff", "commit_訊息": "[內部]更新5.8.8.4 patch檔", "提交日期": "2022-10-12 11:38:23", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cff38f8b92be7e538a34c37187143ba7c3e73293", "commit_訊息": "[WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，ID為null時的Alert訊息。", "提交日期": "2022-10-12 11:33:33", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22594e63a1a27a0cafd20698b0dc8c9067fb466e", "commit_訊息": "[內部]修正IndexNaNaDB_Oracle建立索引名稱過長問題", "提交日期": "2022-10-12 11:21:47", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c1be618eafc3f3854be76432c1f810f219d01dd7", "commit_訊息": "[Web]Q00-20221011001 修正ESS表單開啟沒有ESS畫面", "提交日期": "2022-10-11 19:17:51", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e382aa2033d0ad14d82a3549fbce89b3b521085", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-11 11:29:38", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1998c755092d1fe922b0fb29cc01347b46467036", "commit_訊息": "[內部]調整Web化系統工具的系統權限管理頁面重新載入快取資料按鈕的多語系", "提交日期": "2022-10-11 10:47:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0ebb29f2cb3bc9e5a652d1b6210f8de7f69c5785", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2022-10-11 09:20:14", "作者": "raven.917", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "1b30613541ce3b2d014e6c22a0b1ca0a512b22de", "commit_訊息": "[表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持\"-\"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(修)", "提交日期": "2022-10-11 09:20:02", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc26a1f887fcd2377061980ea1febfff34b1c539", "commit_訊息": "[內部]補上遺漏註解", "提交日期": "2022-10-07 20:02:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f88ae1cb0a4434e9fc8ed076c90e02506529806", "commit_訊息": "[內部]新增Web化組織管理工具程式定義與其SQL[補]", "提交日期": "2022-10-07 19:43:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "247fdf8922f583fd8677d0cb79ff19507c8b22fa", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-07 19:38:37", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "aa34c8bec0a9b3615cfb929565de8dd1f3f9ea55", "commit_訊息": "[Web]S00-20220714004刪除元件時，判斷此元件是否與Grid繫結[補修正]", "提交日期": "2022-10-07 18:11:16", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "adc808510debd5da671a8218e9c514c150d2a182", "commit_訊息": "[WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。(補修正多語系)", "提交日期": "2022-10-07 11:05:35", "作者": "raven.917", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cc4254f1b9bf184ca7551b23b6e4f05242c510c", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-06 17:14:18", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7592a1d19a36acc8186de41f715a9305d3751786", "commit_訊息": "[WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。(補修正多語系)", "提交日期": "2022-10-06 14:57:39", "作者": "raven.917", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4e383a1fce65bca6f5b1fb2d1cb9d8e039c7b945", "commit_訊息": "[WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。", "提交日期": "2022-10-06 14:35:32", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f61bd9808f2bbe977e2e56d5ccf02c3d57f39347", "commit_訊息": "[內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]", "提交日期": "2022-10-06 13:59:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4536ac6ea2efb6982d228c1a27996227b36ff10", "commit_訊息": "[內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]", "提交日期": "2022-10-06 13:56:04", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "851679ed80b6d5bbd5a16428a20605e13384d162", "commit_訊息": "[ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視", "提交日期": "2022-10-06 13:39:46", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "631348a61d167d9928d3a47cf142b2a3cfc2c7ac", "commit_訊息": "[流程設計師]Q00-20221006001 調整在流程設計點擊編輯表單欄位權限時，若表單發行狀態已過期或UNDER_REVISION時會彈提示訊息", "提交日期": "2022-10-06 11:29:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "b8e8f1daca8a74424f2a35822fdc03d4cb4cffcd", "commit_訊息": "[WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限\"沒有作用(補修正，增加可讀性)", "提交日期": "2022-10-06 08:57:33", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6ca1fa7596b0a327bde93ca9717eab9af44757a", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-10-05 15:52:05", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d46d0d7a00420d2d3378f2170d22fff30d378d6d", "commit_訊息": "[內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]", "提交日期": "2022-10-04 17:59:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9bd1ac160c39ee69407b945ff85d4abeec75a3fa", "commit_訊息": "[內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]", "提交日期": "2022-10-04 16:45:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c718d609272c2c7882154bbac232e30f72b66e41", "commit_訊息": "[內部]更新5.8.8.4 patch檔", "提交日期": "2022-10-04 16:10:18", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "182599ac1c20c1330a52460065f540553f24e9d5", "commit_訊息": "[WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限\"沒有作用", "提交日期": "2022-10-04 15:26:58", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6a8109be6a095b80f7ff5dd1a0f5ca33cd8be5bf", "commit_訊息": "[內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]", "提交日期": "2022-10-04 14:51:39", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "60f7568aa90e3a1bb6f9f09b6e71b64df1b1d858", "commit_訊息": "[表單設計師]Q00-20221004001 修正DialogInputLabel元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容", "提交日期": "2022-10-04 10:28:05", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09912e9dfd71f8756e03901f7c02e7a7839f421f", "commit_訊息": "[內部]Q00-20221003004 增加Table相關Index", "提交日期": "2022-10-03 16:00:26", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "3dca5cc064183b3ff4d5d60909e4f4b99bba4ac8", "commit_訊息": "[流程引擎]Q00-20221003002 流程預先解析支持流程設計關卡型態為「活動簽核人」的活動", "提交日期": "2022-10-03 13:59:51", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "726c5f566fba05632b244c3be73b0bb3f531fe27", "commit_訊息": "[Web]Q00-20221003003修正設計流程時，關卡名稱有空格，但在待辦事項的檢核意見表中的關卡名稱，空格未顯示的問題", "提交日期": "2022-10-03 13:43:45", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "af5eadd772c686b6fc67fd66db3d8a509801e84a", "commit_訊息": "[流程設計師]Q00-20221003001 調整簽核流設計師，將流程設計師原有的「活動定義/選擇參與者/活動簽核人」重新加回簽核流設計師中", "提交日期": "2022-10-03 10:27:09", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/BpmUserTaskInfoAcquirer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/BpmUserTaskEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskChooserController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskInfoPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/ProcessRelationshipPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "b7a1df87439b8f93df4be2e19c1ef4d4e36ae94e", "commit_訊息": "[內部]新增Web化組織管理工具程式定義與其SQL", "提交日期": "2022-09-30 19:40:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "99e256684036302aa00f7b16989d14a6715677af", "commit_訊息": "[內部]MFA雙因素模組名稱更改成TFA雙因素模組", "提交日期": "2022-09-30 18:45:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MFAConfigManagerDelegate.java", "修改狀態": "重新命名", "狀態代碼": "R076"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/MFANotVerifylist.java", "修改狀態": "重新命名", "狀態代碼": "R082"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/MFASetting.java", "修改狀態": "重新命名", "狀態代碼": "R089"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/MFATrustDevice.java", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/MFAuthentication.java", "修改狀態": "重新命名", "狀態代碼": "R088"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/MFAConfigManager.java", "修改狀態": "重新命名", "狀態代碼": "R062"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/MFAConfigManagerBean.java", "修改狀態": "重新命名", "狀態代碼": "R079"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/GoogleAuthenticator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/MFAModule/MFASetting.jsp", "修改狀態": "重新命名", "狀態代碼": "R096"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/MFAModule/MFAUnauthlist.jsp", "修改狀態": "重新命名", "狀態代碼": "R097"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 23}, {"commit_hash": "d99ee3a389a1ca90e4ded8a0bee3207e059f2d99", "commit_訊息": "[Web]Q00-20220930003修正使用URL進入表單發起畫面，再使用另一個URL進入表單發起畫面，會有頁面殘留的問題", "提交日期": "2022-09-30 18:35:18", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cdfc94162159a5128882f445a634ab426807a29a", "commit_訊息": "[TIPTOP]S00-20220613005 從 TIPTOP 拋到 BPM 的單據，若回寫到 TIPTOP 失敗時，則發送 Mail 給系統管理員[補]", "提交日期": "2022-09-30 16:36:17", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd6c87b38bb9390bef6d07ee6e9aba4c613deb37", "commit_訊息": "[Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]", "提交日期": "2022-09-30 15:47:30", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c11e7966633132dd427cbd7255485d2ed2f6d5f8", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-30 15:34:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "acc90b9d139af31ffeda96e0abac5bc1aac229ca", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-30 14:23:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e21f2626202345ec8159f8c0c48a4d3e3616ed63", "commit_訊息": "[Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員", "提交日期": "2022-09-30 12:24:25", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9d5d554e60adcb9b94606aad878a0248ed1939bd", "commit_訊息": "[BPM APP]S00-20220721001 新增移動端Grid元件點擊取消按鈕後可設定欲執行方法", "提交日期": "2022-09-30 11:24:55", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "edb50c81bf34eac2969a5dc51d32cdb0900da325", "commit_訊息": "[Web]Q00-*********** 執行iReport套件當發生Exception錯誤時，增加列印異常的堆疊資訊", "提交日期": "2022-09-30 10:36:45", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "13183e1f7e019fc0e7a47ef159a6aebfabc1d07f", "commit_訊息": "[WorkFlowERP]S00-*********** 調整WorkFLow取簽核歷程及取簽核頁面URL邏輯", "提交日期": "2022-09-30 09:04:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WorkFlowDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IWFRequestRecordDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBWFRequestRecordDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "30903b098927f0192b946c1cb80b8b014ce61c3d", "commit_訊息": "[WEB]S00-20220524004新增線上使用者最大閒置時間系統變數給系統管理員可控制", "提交日期": "2022-09-28 10:55:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "39d8725d8ea0dd086d913d4e9b22d66b2a160676", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2022-09-28 10:55:03", "作者": "raven.917", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "f3fe0269c386953687637678c7d24f7d25b6d78f", "commit_訊息": "[BPM APP]C01-20220921006 修正在移動端多人處理關卡向前加簽後簽核歷程處理者顯示錯誤問題", "提交日期": "2022-09-28 09:39:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9d1c3018f77b16de17d0d686f403331cf5191008", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-27 16:42:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "87e01542cbe2c5f5b3d32adb0af2630996f577e6", "commit_訊息": "[ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核", "提交日期": "2022-09-27 11:50:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e494dd6c8fd32b42b32cabc67b38f7e6bd7458b", "commit_訊息": "Revert \"[ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核\"", "提交日期": "2022-09-27 11:47:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0a3772f88abf0e86795be1be02835bc5017815ef", "commit_訊息": "[ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核", "提交日期": "2022-09-27 11:34:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4dffee4a5f09a067e396b7df77d711765e108f61", "commit_訊息": "[web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。(補修正)", "提交日期": "2022-09-27 10:51:44", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f7410f6fb834359ee0166e3081881c560914758b", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2022-09-27 09:59:18", "作者": "raven.917", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "667c3f269b11aab7fa83d5050051659af7453825", "commit_訊息": "[T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容", "提交日期": "2022-09-27 08:36:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc2ca12db611244468a6d072e0ab9402ddf75f53", "commit_訊息": "[TIPTOP]S00-20220613005 從 TIPTOP 拋到 BPM 的單據，若回寫到 TIPTOP 失敗時，則發送 Mail 給系統管理員", "提交日期": "2022-09-26 17:09:11", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de37debe8929de5a90078d14550011d176bac103", "commit_訊息": "[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]", "提交日期": "2022-09-26 16:46:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cc3b3b55956278581eda647009790012d24d938c", "commit_訊息": "[web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。", "提交日期": "2022-09-26 15:02:43", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d1cce86437d5821a4ab074b7732f5c7f09d27cae", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-26 09:27:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b52492f027e68a55745be2a65b1c78049d145a45", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-23 16:53:05", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5d56681b6497ac4976e1808ae8727916192fb47c", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-23 12:03:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e68455e3f63a1ba96977c847826cc7908603abb3", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-23 11:31:10", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7394a9457a61b4e2ce19ffbb0eaba8636670ff21", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-23 09:44:31", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34c44facb5528eb9195b40775d623194a5f185a8", "commit_訊息": "[流程引擎]Q00-20220922001 調整流程撈取工作通知內容機制", "提交日期": "2022-09-22 18:04:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b89768e28da9338c321ec379aa8af53699914320", "commit_訊息": "[E10]修正組織同步轉換中介資料時，只有兼職沒有主部門的話會因為insert empId(null)到SYN_Employee時出現異常", "提交日期": "2022-09-22 13:54:12", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a0560a91e44fbf92bcfe6a933721ef4daec36f65", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-21 19:30:51", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c209fe77f86dae12c73be7aba0ef18c60712f8d8", "commit_訊息": "[Web]Q00-20220921002 調整「必須上傳新附件」邏輯，只要存在一筆以上的附件並且符合在該「關卡名稱」上傳的附件，即可通過該驗證", "提交日期": "2022-09-21 17:49:58", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0588a14cbba671fb49ad29340d6379f95da440b1", "commit_訊息": "[WEB] C01-20220919007 Admin 需要能開啟設計師時，直接針對該表單做復原簽出的操作行為。", "提交日期": "2022-09-21 16:42:30", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefNodeState.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7c2c65c36c8a893f2ab8364bc2cbb7380a9530ae", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-21 16:04:10", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "06c2b7d1b62058c8a004a366dec9b80797fb7a5d", "commit_訊息": "[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]", "提交日期": "2022-09-21 14:56:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1318eb79d1665f211a194582b2f55a3c1645cfb7", "commit_訊息": "[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]", "提交日期": "2022-09-21 14:54:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mfa/IdExpert.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mfa/IdExpertBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8891adaece68c54a5f4e832ed7f79227e1c9d29e", "commit_訊息": "[流程引擎]Q00-20220921001 調整發起流程頁面；由表單畫面切換至流程圖時，可根據當前表單內容進行流程預解析", "提交日期": "2022-09-21 14:14:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "23796bebd4449eef49b5b486e9d74b96b19f9539", "commit_訊息": "[E10]因為E10根節點組織ID固定為org001無法修改，改由BPM SyncTable.properties的synRootId設定，預設為Root", "提交日期": "2022-09-21 14:00:16", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88058f3ceba61d85c315e28aa41c52796c7fe6f8", "commit_訊息": "[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]", "提交日期": "2022-09-21 13:49:55", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "32db33e291e16f6335026ecfa739cdc766bc7caf", "commit_訊息": "Revert \"[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]\"", "提交日期": "2022-09-21 13:42:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d96b047167488e56dd5e5c185b22b74ecb0a9160", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2022-09-21 13:40:30", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "55d030f80c49d7607af0d529cc508b9999b87263", "commit_訊息": "[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]", "提交日期": "2022-09-21 13:39:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "86a9185a042f5969e03eae5741173a4f7e93181a", "commit_訊息": "[表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持\"-\"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)", "提交日期": "2022-09-21 10:40:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71233bf48a11a63ce6c06c0c72233b9a3ae7a61f", "commit_訊息": "[表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持\"-\"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)", "提交日期": "2022-09-20 18:09:03", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "423b4567baf00fae5c4c200fb819cd3ca043052b", "commit_訊息": "[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證", "提交日期": "2022-09-20 17:25:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/MFASetting.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/MFAuthentication.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/MFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/MFAModule/MFASetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/MFAModule/MFAUnauthlist.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 21}, {"commit_hash": "66b6857bae99e32b302f7a9e8d38805cf7f5acaa", "commit_訊息": "[表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持\"-\"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。", "提交日期": "2022-09-20 16:57:50", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bc3f0b0994e1b9f2c0a2813efd0c09fbc077ca4f", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2022-09-20 14:55:50", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "5c2c3e8f584ca2ebc5f87c0189c7aafeb9dd02b2", "commit_訊息": "[TIPTOP]A00-20220919001 新增TIPTOP整合設定，當夾帶附件型態為http,根據TIPTOP附件主機的port號取得附件", "提交日期": "2022-09-20 14:55:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "8d77be0f61fd66714439b0b35f81c131fbcf72a4", "commit_訊息": "[內部]Q00-20220920002 T100傳附件用http方式且未帶drivetoken的tag內容，增加log訊息提示修正T100", "提交日期": "2022-09-20 14:39:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0a8ae6418733cdf442e2f7340420e71435431d82", "commit_訊息": "[Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊", "提交日期": "2022-09-20 11:19:53", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b5d739881475ae35e51d427c697d2a8044689a90", "commit_訊息": "[E10]修正組織同步E10有可能傳送人員離職日期為V的內容(代表失效)，調整離職日期對應方式：V視為離職，9998/12/31視為在職", "提交日期": "2022-09-19 15:49:15", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3d5ebeb5903e44af9c71ecefe5e08aa209c78fb", "commit_訊息": "[表單設計師]S00-20220707005系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷，四則運算及單身加總運算。(補修正)", "提交日期": "2022-09-19 14:33:11", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05231e30673fa198f1b5348a27d265b28d94ed22", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-19 14:28:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "86d9a99f44f0dba4e452d5e80fba2b96efe6460e", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-17 11:38:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ac2642215cde8327d68a58dd08bcf3947263bcea", "commit_訊息": "[流程引擎]A00-*********** 此單號先前調整邏輯廢除，改為調整判斷附件權限為共用方法，可讓前後端共同呼叫使用", "提交日期": "2022-09-16 15:28:43", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDTOFactoryDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "eb23d26047057086ab97dbd5efb38cf1ddaa66e2", "commit_訊息": "[表單設計師]S00-20220707005系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷。", "提交日期": "2022-09-16 14:46:47", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c703c4c9e15cb846bf87d1611215fe041a07db73", "commit_訊息": "[Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題", "提交日期": "2022-09-16 13:48:45", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e62ea54d0fcde2a8e458da8308658d86f3d4b1ec", "commit_訊息": "[Web]S00-20220810001簽核意見是否顯示管理員[補修正]", "提交日期": "2022-09-15 22:01:27", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e8cd2c847e89957cbf8a7c0120bccbbd307c83f2", "commit_訊息": "[流程引擎]Q00-20220915001 修正簡易流程圖若流程有設計迴圈型且線的條件剛好為兩個Gateway互為下一關時，加入防呆避免系統Crash", "提交日期": "2022-09-15 17:24:04", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e569439c69f0f4c51cb111111509737520e8984a", "commit_訊息": "[內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題", "提交日期": "2022-09-15 10:23:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/design_tool_web/SystemManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "8e8caeb029dec494813baeef6735751bec40ac23", "commit_訊息": "[流程引擎]A00-*********** 調整寄信時依照「附件權限設定的套用範圍為人員」時，判斷是否需要夾帶附件，在其餘範圍情境下則不主動夾帶附件", "提交日期": "2022-09-14 17:52:35", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be78c34d912f2c9ebf5e0f95ad1815db2bfcd74a", "commit_訊息": "[流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯", "提交日期": "2022-09-14 10:34:55", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52421c326cf0f1613899d44f3e1e644f4cd4d3e2", "commit_訊息": "[Web]S00-20220714004刪除元件時，判斷此元件是否與Grid繫結", "提交日期": "2022-09-13 14:27:14", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e3332c4bcc0a27d42750b11fa93d2ee22adf3f4d", "commit_訊息": "[流程引擎]Q00-20220912004 修正findProcessPackageById方法內容為取得流程包裹最新一版，以避免後續同仁遇到此坑", "提交日期": "2022-09-12 17:59:01", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c3a3ad228ffcb72133193b54ca3e4d16c65bc4a7", "commit_訊息": "[組織同步]QQ00-20220912003 組織同步功能執行人員資料修改時，可保留人員姓名多語系關聯", "提交日期": "2022-09-12 17:39:32", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2fc1b92bad6100be65d9fcfabd6227de42916bef", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-08 16:46:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3aa0350a5a8ae1f050d4c764896ff91340c641f3", "commit_訊息": "[Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本", "提交日期": "2022-09-08 15:12:59", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88a520d69306bacf96b0422463a5c53b6a3b6b84", "commit_訊息": "[表單設計師]Q00-20220908001 修正subTab元件無法編輯", "提交日期": "2022-09-08 14:26:10", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d22f84188e66922c460580b3c6a9fc78e49fa60", "commit_訊息": "[Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息", "提交日期": "2022-09-08 14:06:21", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "421c91c1053586c7dfd2ecf3b1a7d893ea6ae9ba", "commit_訊息": "[組織同步]Q00-20220907004 修正TT組織同步失敗", "提交日期": "2022-09-07 17:39:10", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3abeb5057b7c232208e6e7c9a68860bce8ad4cd1", "commit_訊息": "[Web]Q00-20220907003 修正TIPTOP附件無法下載", "提交日期": "2022-09-07 17:15:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cde89594f891494cc88a488db9f6d2a0269b41ba", "commit_訊息": "[Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題[補修正]", "提交日期": "2022-09-07 17:08:34", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99978cb0a4b50c1197b600b67bafc34473c3c5be", "commit_訊息": "[Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題", "提交日期": "2022-09-07 14:59:37", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e0369e191125ce4b0f33ca4a3f1f817fdef62e7b", "commit_訊息": "[Web]Q00-20220825002 調整模組程式維護作業加入系統語系供使用者設定", "提交日期": "2022-09-07 13:48:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProgramViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageModule-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "476230c37c5ca5bfff80afd0dfac71cd3855975c", "commit_訊息": "[Web]Q00-*********** 增加判斷表單主要隱藏欄位hdnFormDefOID資料未完全載入時，彈出訊息提示以及不給予執行開窗動作", "提交日期": "2022-09-07 11:55:46", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84d79452acecfdc44f331ce61136ddd932facc2e", "commit_訊息": "[Web]Q00-20220906004 修正系統管理員登入BPM不應該發雙因素模組的驗證信", "提交日期": "2022-09-07 08:38:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8943bf839f60481c6a069ebb2a9cd20c340dbe8", "commit_訊息": "[內部]新增調用指定SessionBean的方法", "提交日期": "2022-09-06 16:13:30", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/SessionBeanHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6146e3d20631ab9a7babf1a634ca03115c3c4341", "commit_訊息": "[流程引擎]Q00-20220823003 讓亂數產生的ID增加動態加簽CustomDecisionRule的開頭關鍵字，前面流程圖解析邏輯段也要新增", "提交日期": "2022-09-06 14:52:00", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "328105d4fbb42b188bf8aa5a9fe98ddc0f499e76", "commit_訊息": "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]", "提交日期": "2022-09-05 17:48:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7c0edb9acf298dd9266cb6946ec766d4ea3ccb32", "commit_訊息": "Revert \"[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]\"", "提交日期": "2022-09-05 17:39:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "6282ad27e6f1fd7bbe8dc6a16a12b023636c08c2", "commit_訊息": "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]", "提交日期": "2022-09-05 17:33:28", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "3838207f5c5ca4957d62ccf66348ae71ff315f1c", "commit_訊息": "Revert \"[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]\"", "提交日期": "2022-09-05 17:28:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d808f9cef1995ed9ed1bbc1f2cbe7b2d8cc567b0", "commit_訊息": "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]", "提交日期": "2022-09-05 17:26:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "8f0e428e833aa33faf011405ea368673309a0467", "commit_訊息": "Revert \"[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確\"", "提交日期": "2022-09-05 17:19:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a7736a90b46c3b3eed28f40219287dcc25b38630", "commit_訊息": "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確", "提交日期": "2022-09-05 17:17:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c94504c9c7cee6f1e33463a1ae19d32adde6869f", "commit_訊息": "[Web]Q00-20220902001修正企業流程監控的圈型圖時間單位問題[補修正]", "提交日期": "2022-09-04 00:13:13", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f80f20cc66dbc76b0cb7ad9eb5c4f88fb2aa21de", "commit_訊息": "[BPMAPP]Q00-20220902002 修正企業微信回調接收消息時出錯，調整commons-codec套件版本為1.9版", "提交日期": "2022-09-02 17:15:46", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/lib/JakartaCommons/commons-codec-1.13.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/JakartaCommons/commons-codec-1.9.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "03d7a8da1cc1e6720c73d16af8629865b193279d", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-02 16:59:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8df129b902dd95465fdd801cc10cd1e8cbb02ab6", "commit_訊息": "[Web]Q00-20220902001修正企業流程監控的圈型圖時間單位問題", "提交日期": "2022-09-02 14:36:17", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76645157e93db3fed715ea028dc5e65c9f229afe", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-09-01 18:37:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0d139e11ee78194cc82dd92cec3baec3fca2dc90", "commit_訊息": "[Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式", "提交日期": "2022-09-01 15:53:47", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fcb3ba1957187bcd948ab26dbafdd3390393d03c", "commit_訊息": "[WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能", "提交日期": "2022-08-30 08:34:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "707e419312c4362bb137282f1c3f05f709ceb326", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-29 11:03:27", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c1d4baea88e29b660c3cc5d5fb2e5cdede3aeba3", "commit_訊息": "[Web]Q00-20220826002修正模組定義若底下無程式定義，不會顯示在模組程式維護頁面的問題[補]", "提交日期": "2022-08-29 10:42:15", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "28d568b8960f8156ab0cd41a63ff153109538264", "commit_訊息": "[Web]Q00-20220826002修正模組定義若底下無程式定義，不會顯示在模組程式維護頁面的問題", "提交日期": "2022-08-26 18:47:21", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "175c188e496be29275074711bd49b7ae2d718b56", "commit_訊息": "[BPM APP]C01-20220825002 修正移動端在發起詳情畫面點擊Grid元件查看更多時無法顯示的問題", "提交日期": "2022-08-26 15:52:29", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "06c4c3c2fc0d4a30de7309003025f5417d8b0ea0", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-26 11:30:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3ada000d791b3a599ca99b6ad98895ab4d36e524", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-26 10:39:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1456beff8538d0b0f6d63b0fc89183c6a02c64b1", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-25 18:39:57", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f4d5f7c4096191a4c0e1f9fb317fb0a703878f67", "commit_訊息": "[Web]Q00-20220825003 修正程式權限設定套用範圍為部門但不包含子部門，在修改編輯時卻勾選包含子部門的問題", "提交日期": "2022-08-25 15:41:20", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2503deaec03851d03a3f64332bbd4cd63e720ab8", "commit_訊息": "[流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡", "提交日期": "2022-08-25 14:52:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70d8e3ad327fcde84965d7fade62ad10ac55c778", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-24 18:52:30", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "78bd5429a1d9375469be190c0b472761dfddae68", "commit_訊息": "[流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容", "提交日期": "2022-08-24 11:48:03", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "26180e44b9f2c2346904834109c6a45dd61a9078", "commit_訊息": "[流程引擎]S00-20220722001新增批次通知信件主旨內容", "提交日期": "2022-08-23 15:27:14", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d44f99afaa18345a580db009c9483b9266cc2f91", "commit_訊息": "[流程引擎]Q00-20220823001 修正使用客製的方式執行動態加簽後，無法呈現詳細流程圖畫面的問題", "提交日期": "2022-08-23 14:44:52", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0fddabae15278c3e8c020a713bee981e3816648d", "commit_訊息": "[Web]Q00-20220822002 修正登入頁輸入錯誤帳號，會跳出非標準的錯誤訊息", "提交日期": "2022-08-22 16:19:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e979a57a244b1a6d5129208f97b79b2e3b19d3e5", "commit_訊息": "[Web]Q00-20220822001 修正BPM使用IE瀏覽器上傳附件時會失敗", "提交日期": "2022-08-22 14:11:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a7c604c3b4e14ecd70a85b58cbbb21a6eb28007a", "commit_訊息": "[BPM APP]S00-20220525001 支持同時整合企業微信與LINE", "提交日期": "2022-08-19 18:28:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileCommonManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MobileAuthorizeUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Line.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 19}, {"commit_hash": "31a3f56f09a5d15401cc617c88b9a18d5a3b2b73", "commit_訊息": "[TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久", "提交日期": "2022-08-19 17:57:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "27734706db2d15e6559a9a1e441022d185a55bca", "commit_訊息": "[流程設計工具]Q00-20220819002 服務任務關卡中的呼叫應用程式，移除Mail Delivery下拉項目", "提交日期": "2022-08-19 15:38:23", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/ApplicationsToolsEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d827631348fbaddbfd0524c83f0471772ad79e98", "commit_訊息": "[Web]S00-20220810001簽核意見是否顯示管理員", "提交日期": "2022-08-19 10:49:46", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "5e48602f19f19e4529c67355766c48a9ede41485", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]", "提交日期": "2022-08-18 17:50:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d74732c2253f7fd660f6a0196143c8c073cb35ac", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]", "提交日期": "2022-08-18 17:47:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ba64aded10eeaab2380186dee73f476213adbbbd", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到", "提交日期": "2022-08-18 17:41:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "9bab88985ed1e259568ac9ea150e67ce030c9b68", "commit_訊息": "[WorkFlow]Q00-20220818004 優化易飛，WorkFlow回傳的Debug訊息", "提交日期": "2022-08-18 14:18:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e399bced9ab4046562849d0854d1b03cd6612348", "commit_訊息": "[TIPTOP]A00-20220816001 調整整合產品的createSQL的新增workflow主機指令[補修正]", "提交日期": "2022-08-18 11:54:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_Oracle9i.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_SQLServer2005.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_ORACLE9i.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_SQLServer2005.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b12e376cbca07aa957fad4cead03b764187d840b", "commit_訊息": "[流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤", "提交日期": "2022-08-18 11:43:00", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c2d79e3fee129e27a8f956d35ddb417b20e077a6", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-18 11:41:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c58859c893a28b5fa51b622662d79dd7f2617fb", "commit_訊息": "[Web]Q00-20220818001 儲存系統設定T100整合時，會跳出密碼政策的錯誤訊息", "提交日期": "2022-08-18 11:27:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "47e56fcef310f15c9068c279beb246b31078fc06", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-17 18:57:42", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5f7a7cadc37f265aca9c28f1418debdfb0851a92", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-17 14:56:49", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "84e11d650f4f2499b3c85a0f77739e498d6d8509", "commit_訊息": "[TIPTOP]A00-20220816001 調整整合產品的createSQL的新增workflow主機指令", "提交日期": "2022-08-17 14:05:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_Oracle9i.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_SQLServer2005.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_ORACLE9i.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_SQLServer2005.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1edd545e3b041986bc4bbea1edd0184ec06cd6e4", "commit_訊息": "[Portal]]Q00-20220817001調整有整合Portal，用查看流程圖的外部portlet，導入的畫面不是BPM而是Portal的登入頁面", "提交日期": "2022-08-17 11:58:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3161060ba229d1be016e874e01ea6fd57249870", "commit_訊息": "[Web]A00-20220811001 修正表單若TextBox元件設定浮點數且顯示實際值時會有偏移值問題", "提交日期": "2022-08-16 14:49:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d024d9e9c76bf9a69e147cf1b6f51c2286fc674", "commit_訊息": "[在線閱覽]Q00-*********** 更新PDFjs閱讀器版本(2.3.200)，原因為修正部分PDF因字形而顯示異常的錯誤", "提交日期": "2022-08-15 17:45:31", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MFAConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ar/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ast/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/az/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/be/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn-BD/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn-IN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/br/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/brx/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ca/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cak/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cs/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cy/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/da/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/de/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/el/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-CA/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-GB/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-US/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eo/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-AR/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-CL/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-ES/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-MX/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/et/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eu/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fa/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ff/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fi/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fy-NL/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gd/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gn/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gu-IN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/he/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hi-IN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hsb/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hu/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ia/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/id/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/is/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/it/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ja/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ka/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kab/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kk/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ko/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lij/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/locale.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lt/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/meh/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/my/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nb-NO/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nn-NO/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pa-IN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-BR/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-PT/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rm/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ro/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ru/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/si/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sk/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sq/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sv-SE/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/te/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/th/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uk/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ur/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/vi/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js.map", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 86}, {"commit_hash": "b7588a8d2b96e92a90c120ddd13a6e553678cf5c", "commit_訊息": "[Web]Q00-20220815003修正絕對位置表單中多選開窗小畫面沒有顯示選取清單的問題", "提交日期": "2022-08-15 15:23:05", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d929c6cade7392008027ae81db23cf300991516b", "commit_訊息": "[Web]Q00-20220815001修正絕對位置表單的image無法依照寬高呈現的問題", "提交日期": "2022-08-15 08:31:26", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c429847f3a795fd5e2a8d8622549b83fc52b345b", "commit_訊息": "[流程引擎]S00-*********** 優化流程預解析功能-1.核決關卡加簽後，預覽流程圖即可顯示，2.核決關卡有前置關係人時，預覽流程圖即可顯示，3.流程發起關卡的預覽流程圖，若使用者有多個發起部門，可動態依使用者選擇的部門做解析", "提交日期": "2022-08-12 15:03:20", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "fa486cdf385dfa0d3a076bd48a294a37170acff5", "commit_訊息": "[Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題", "提交日期": "2022-08-11 12:56:57", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "494f7fc048dd1e5114eb83101765128b077da016", "commit_訊息": "[Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題", "提交日期": "2022-08-10 18:34:57", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ab97a8cb70dea0fcd603c695afa8e0959d05417a", "commit_訊息": "Revert \"[Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題\"", "提交日期": "2022-08-10 18:08:04", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5834e5cfb8eabe14d293953b84ebb012029c1eed", "commit_訊息": "[Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題", "提交日期": "2022-08-10 18:03:03", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "94109e97f9a6eae975420f5af471f35adf594f88", "commit_訊息": "[內部]新增組織設計工具使用的服務[補]", "提交日期": "2022-08-10 17:55:30", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dc8a4d1c87347a77d7e4fd15d783d71239760c3b", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-10 17:10:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2edb5cc6afeb7f4ba6ee57a921abe6bb1cb445a0", "commit_訊息": "[Web]Q00-20220810001 修正設定模擬使用者給一般人員，用模擬使用者模擬一般人員，會出現兩筆模擬使用者的作業", "提交日期": "2022-08-10 15:47:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4c0b24f1a92ed9ca9e907398a46ac7721585913", "commit_訊息": "[Web]A00-20220808001 調整報表查詢產出的日期與匯出Excel的日期不一致問題", "提交日期": "2022-08-10 11:01:13", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/ChartQueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a7394f09c8a97359c34fc98c5bebc4be95b78b8", "commit_訊息": "[T100]Q00-20220809005 修正T100拋單，附件為從文檔中心取得的，檔案大小與實際大小不符合", "提交日期": "2022-08-09 15:40:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5b173058239738874d60289d9cc240df369d1c90", "commit_訊息": "[組織同步]Q00-20220809002 修正組織同步log出現Error時改寄送失敗通知信", "提交日期": "2022-08-09 13:51:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "525e63905e003ec93ed6803213744554c344717d", "commit_訊息": "[易飛]Q00-20220809001 調整易飛出貨流程的回寫事件", "提交日期": "2022-08-09 10:47:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@yife/process-default/bpmn/\\351\\200\\262\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(YIFE_PURI09).bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c3c85a9ac2d08d555346ece855df33dbcb5606a", "commit_訊息": "[Web]Q00-20220808003修正使用產品表單中的Date元件，並搭配TextBox元件的進階功能，資料型態整數中的時間區間運算，當遇到元件ID有使用下底線時，會導致TextBox元件無法正常運算", "提交日期": "2022-08-08 17:43:54", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d42291a46fb14305d9bf4255bf3c5a70fdb7db69", "commit_訊息": "[流程引擎]Q00-20220803002 調整流程主機呼叫其他流程主機清除系統快取的服務，若其他主機無法連線時，逾時時間由20秒改為1秒[補]", "提交日期": "2022-08-08 16:01:52", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5162185deae7b6755397b43c95fb48504b7bdb9a", "commit_訊息": "[Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題", "提交日期": "2022-08-08 14:57:44", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b3442d2079dfa86d14a4f0284ec22640629cd10", "commit_訊息": "[流程引擎]Q00-20220803002 調整流程主機呼叫其他流程主機清除系統快取的服務，若其他主機無法連線時，逾時時間由20秒改為1秒", "提交日期": "2022-08-08 14:51:00", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12fe44fa0a800db5b36a136f07123782b1084df5", "commit_訊息": "[內部]更新5.8.8.3 patch檔", "提交日期": "2022-08-08 14:36:18", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9de4a70ea0e18ace787e38e1ea7a44196371fc3f", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-05 17:28:13", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "772eb0da6f644dfb42ea94a04979931320065fe3", "commit_訊息": "[Web]Q00-20220805002 調整log訊息，當流程向後派送，後面關卡解析的使用者找不到或是沒有主部門時，增加log訊息", "提交日期": "2022-08-05 17:26:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f79f700e479fd86f728a71a0307682e52d117b21", "commit_訊息": "[Web]Q00-20220805001 修正作業程序書沒有顯示核決層級關卡的作業名稱", "提交日期": "2022-08-05 14:06:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "006bf89284d91dd8180f7232ef48a07b3d483e03", "commit_訊息": "[Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]", "提交日期": "2022-08-05 12:00:16", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f2a408d1f7059099a37f8dc94b8c3115849da8e1", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-05 11:44:44", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "253195da1f08ebb4d107d5e8c6f5fc91cbbf9376", "commit_訊息": "[Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容", "提交日期": "2022-08-04 22:21:33", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7a3f94e2fbeac1855ffc32195fa2dbcba3b04dc1", "commit_訊息": "[內部]Q00-20220804002 優化ContextManager的log訊息", "提交日期": "2022-08-04 16:21:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fcb3952006df70a801f529bb0d48d728f0e666bb", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-03 19:56:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dc3b8f90e6fb22b8ed798df440fa66fa04845588", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM into develop_v58", "提交日期": "2022-08-03 16:49:17", "作者": "wencheng1208", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a0731c694c9a4436d8ad3a60765c810b2b49277f", "commit_訊息": "[Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。", "提交日期": "2022-08-03 16:48:09", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "57c74512f535c7cf11f0fd6ca63276d48bf82686", "commit_訊息": "[Web]Q00-20220803001修正在手機模式下，檢視附件檔案下載時跳出空白頁的問題", "提交日期": "2022-08-03 11:33:17", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d9548a050c658c82aae9d8d8a84fbe08c0973944", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-08-02 20:24:41", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "82679f63433b30a999d8bb4662b85a1a47b50254", "commit_訊息": "[Web]A00-20220802001 修正無法開啟SAP維護作業", "提交日期": "2022-08-02 11:27:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7505e95810ff35063e87ad5dced53f70c31c725f", "commit_訊息": "[內部]調整系統設計工具Web化組織樹接口", "提交日期": "2022-08-02 10:59:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "58e9caed6e35d72f99b57a40f0edb1c60f6043ad", "commit_訊息": "[Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料", "提交日期": "2022-08-01 16:26:26", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f7ba05161b070de6e5fa6f77cd93d71eda2de81", "commit_訊息": "[Web]Q00-20220801001新增複製流程序號的多語系內容", "提交日期": "2022-08-01 11:28:16", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CommonJs/CopySerialNumber.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "001f7411548ff8233e135cb38c5cd0a209eff787", "commit_訊息": "[易飛]A00-20220729001 修正PURI09表單的Script單身欄位代號錯誤", "提交日期": "2022-07-29 17:50:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@yife/form-default/PURI09.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74ced252b7de769c8b4573af9908ffd9fa8edb44", "commit_訊息": "[Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失", "提交日期": "2022-07-29 17:03:53", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e18724d4d31214518136ae608f4eb167ae8300d", "commit_訊息": "[Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示", "提交日期": "2022-07-29 16:49:53", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f069f728ab46b060962de221112d5dd13e7a81f7", "commit_訊息": "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。", "提交日期": "2022-07-29 16:30:05", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d09de7907277acf369a9d48b0d2c0a604992f812", "commit_訊息": "[WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]", "提交日期": "2022-07-29 14:34:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8856c3f79bed996ea372d6399d8f43db71330e23", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]", "提交日期": "2022-07-29 14:20:21", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8574d28d3536dbbd509666abd3eeb8f538fba807", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]", "提交日期": "2022-07-29 14:02:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f0517ebb311abe716b5169e5937ef5febdfa41e1", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況", "提交日期": "2022-07-29 00:04:37", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1991f2f836709f1ce13bdebfcaced0528495a9d8", "commit_訊息": "[Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行", "提交日期": "2022-07-28 17:32:17", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cdeaa96601380b79bd66c768d69837704d0087bc", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock", "提交日期": "2022-07-28 15:20:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7c9f17af5dd50a32818283a1b31bdc7bd319ce83", "commit_訊息": "[Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]", "提交日期": "2022-07-28 14:39:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3422654da0b0dc00a5b8315aebc81c553851e05", "commit_訊息": "[Web]Q00-20220727004 調整使用JS做singleOpenWin開窗且寬度為720，開窗後顯示的是名片式改為表格式", "提交日期": "2022-07-27 18:14:17", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ebd0d8c8a95377c8054202c7cd86d88e8268f64f", "commit_訊息": "[Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題", "提交日期": "2022-07-27 17:51:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e140afdea0bdd5281a844f6574e1eaa1fc5f3dd3", "commit_訊息": "[Web]S00-20220718006 新增模組-雙因子認證模組[補修正]", "提交日期": "2022-07-27 16:27:04", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb2c4eb459d042146f7e90e4d320e8e246780079", "commit_訊息": "[Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。", "提交日期": "2022-07-27 12:07:05", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6b0f6b8a32ae93c0617f18b1d2ddba4e2f4f0f92", "commit_訊息": "[WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法", "提交日期": "2022-07-27 10:41:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2bb6ed34ec223d13790d890c159dcad0116c9833", "commit_訊息": "[BPM APP]C01-20220713002 調整移動端上傳附件後取消呼叫formSave與formClose事件", "提交日期": "2022-07-27 09:54:27", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "3dbf5ddaaaa7870f5a5b6c38d8afbbde85da12ba", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean[補]", "提交日期": "2022-07-26 20:33:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f10c73e660863a3e76a72156d92654aa83b3143", "commit_訊息": "[BPM APP]C01-20220722001 修正移動端Grid元件的itemOrder有0時會造成欄位順序錯亂問題", "提交日期": "2022-07-26 19:37:46", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a6e7947b88e51ed41e2e083179ecaa7998339e3", "commit_訊息": "[內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來", "提交日期": "2022-07-26 18:17:44", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/JpaService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b0b30bc124c7a365591c067c80f3fefa794608b3", "commit_訊息": "[Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入", "提交日期": "2022-07-26 18:16:29", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b4138b68bc7d88d7644e1453bc56cabb8d0f56d", "commit_訊息": "[Web]S00-20220718006 新增模組-雙因子認證模組[補修正]", "提交日期": "2022-07-26 09:25:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/MFAModule/MFASetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d11672ff497727146e9aa7ea5dcb98c9e0e36b48", "commit_訊息": "[BPM APP]C01-20220627005 修正IMG中間層Grid元件的itemOrder有0時會造成欄位順序錯亂問題[補]", "提交日期": "2022-07-25 18:57:30", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9b62c1984fd559d10052c597c68cc095e4378c2b", "commit_訊息": "[內部]Q00-20220725003 Index指令回收", "提交日期": "2022-07-25 17:19:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "1ac86343489e60928d743dbd560ded3994b2cfb2", "commit_訊息": "[TIPTOP]Q00-20220519002 axmt700_icd表單入版", "提交日期": "2022-07-25 16:39:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@tiptop/conf/5.25/Process_Mapping.prsmapping", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt700_icd.form", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "ddfe649e9e166c3ed1da9238ba899139d6bea643", "commit_訊息": "[Web]S00-20220129003 調整當表單元件的Label內容過長時完整顯示出Label內容", "提交日期": "2022-07-25 15:44:06", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c0ff3d034164d789260e004aae76c88893c4822", "commit_訊息": "[Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法", "提交日期": "2022-07-25 10:55:23", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ef025d5f4224bcda3a4f531c426a61fc03dcacf8", "commit_訊息": "[Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題", "提交日期": "2022-07-25 10:22:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "733949d9b237d94324dcab9328036815c66dfd6f", "commit_訊息": "[內部]新增提供給組織設計工具Web化使用的SessionBean", "提交日期": "2022-07-22 17:38:10", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}