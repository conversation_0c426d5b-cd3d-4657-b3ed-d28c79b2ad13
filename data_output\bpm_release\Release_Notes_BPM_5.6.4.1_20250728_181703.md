# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.4.1
- **舊版本**: 5.6.3.1
- **生成時間**: 2025-07-28 18:17:03
- **新增 Commit 數量**: 199

## 變更摘要

### joseph (36 commits)

- **2017-07-21 17:24:54**: 多語系維護作業   描述查詢條件選項  移除 等於和不等於
  - 變更檔案: 1 個
- **2017-07-21 10:01:01**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-21 10:00:30**: Q00-20170721001 修正ISO 文件總管 ,點選文件中的放大鏡會報錯 	原因:jspd中要取得的Session是空的
  - 變更檔案: 1 個
- **2017-07-18 18:42:36**: 修正 :離職人員維護作業當沒有人時會報錯的問題
  - 變更檔案: 2 個
- **2017-07-17 15:27:57**: 修正 : Udpate SQLSERVER 關注事項 CriticalFocusProcess ,CriticalProcessDefinition 的processPackageId 改為nvarchar(255)
  - 變更檔案: 2 個
- **2017-07-17 14:55:11**: 修改 :關注事項ORACLE欄位型態  及 將系統多語系維護作業URL更改為查詢維護樣板的URL與JSP的 ACESSRIGHT
  - 變更檔案: 5 個
- **2017-07-17 09:11:19**: 修正:出貨5.6.4.1 Oracle Update_SQL
  - 變更檔案: 1 個
- **2017-07-14 18:35:50**: 修改 5.6.4.1 updateSQL.Oracle
  - 變更檔案: 1 個
- **2017-07-14 09:18:34**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-14 09:18:03**: Q00-20170602002 移除log及調整多語系
  - 變更檔案: 2 個
- **2017-07-12 12:02:38**: 修改 :查詢維護樣板 DropDown元件中請選擇的內顯值'$$$$$$' 改為''
  - 變更檔案: 2 個
- **2017-07-12 11:41:54**: 新增:離職人員維護作業多語系
  - 變更檔案: 3 個
- **2017-07-12 11:40:44**: 修改多語系檔名
  - 變更檔案: 1 個
- **2017-07-12 09:32:02**: S00-20170703001 :修改離職人員作業:將搜尋分為可以只顯示需處理離職人員及所有離職人員
  - 變更檔案: 6 個
- **2017-07-12 09:28:08**: 移除不必要的log,及埋入SQL Command log
  - 變更檔案: 1 個
- **2017-07-12 09:25:00**: 新增 : 多語系依照Key刪除_ajax接口
  - 變更檔案: 6 個
- **2017-07-11 18:05:01**: 修改 :語系支援Win10繁中及簡中
  - 變更檔案: 1 個
- **2017-07-11 18:03:33**: 新增:事件重要等級定義作業 Create接口
  - 變更檔案: 4 個
- **2017-07-11 17:59:46**: 增加 : 新增及修改多語系時,要寫入Updater及時間
  - 變更檔案: 1 個
- **2017-07-11 17:58:55**: 新增 : 多語系維護作業Ajax接口
  - 變更檔案: 1 個
- **2017-07-11 17:57:55**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-11 17:57:35**: 新增: 多語系維護作業ListReader
  - 變更檔案: 4 個
- **2017-07-06 18:25:20**: 修正:出貨DB InitNaNaDB_ORACLE,關注事項TABLE SQL語法ALTER改為CREATE
  - 變更檔案: 1 個
- **2017-06-30 09:13:55**: 新增 :關注項目多語系欄位及新增CriticalDefinition方法
  - 變更檔案: 5 個
- **2017-06-28 17:44:30**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-06-28 17:44:04**: A00-20170623002 修正:取ESS維護作業權限錯誤,iterator使用錯誤導致
  - 變更檔案: 1 個
- **2017-06-28 15:39:42**: A00-20170427001 將呼叫Tiptop清理附件交給Queue執行,與開單拆開
  - 變更檔案: 5 個
- **2017-06-28 09:46:12**: C01-20170607002 ESS錯誤代碼機制補充註解說明
  - 變更檔案: 1 個
- **2017-06-22 13:50:52**: C01-20170531001 增加卡控ISO文件編碼 編碼規則中的文件階層及文件型態不能為空
  - 變更檔案: 1 個
- **2017-06-21 18:17:09**: ESSTAG增加註寫說明:ESS維護權限、附件參考的Tag
  - 變更檔案: 1 個
- **2017-06-21 18:11:46**: C01-20170619002  修正:流程簽核時附件反灰無法點選問題
  - 變更檔案: 1 個
- **2017-06-16 11:54:16**: 5.6.3.1_updateSQL_Oracle 修正
  - 變更檔案: 1 個
- **2017-06-08 17:28:34**: C01-20170320001 因表單定義中欄位缺少datatype,導致發起流程及追蹤流程會有錯誤 ,因此判斷datatyep為空時,預設為String型態
  - 變更檔案: 1 個
- **2017-06-05 17:25:01**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-06-05 17:16:38**: Q00-20170602002 修正:從Email開啟直接簽核的連結後，按下派送後都失敗
  - 變更檔案: 6 個
- **2017-05-31 16:23:59**: A00-20170525001 修正 :流程關卡中的允許輸入密碼已勾選，但在簽核時並沒有跳出需輸入密碼的訊息
  - 變更檔案: 1 個

### ChinRong (39 commits)

- **2017-07-21 13:52:58**: 修正鼎慧追蹤-已/未完成列表異常
  - 變更檔案: 3 個
- **2017-07-20 11:36:05**: 修正鼎慧列表部署工具，追蹤流程已/未完成選項名稱寫反了
  - 變更檔案: 1 個
- **2017-07-20 10:13:36**: 修正鼎慧使用者匯入匯出錯誤
  - 變更檔案: 1 個
- **2017-07-19 11:39:45**: 新增BPM APP草稿刪除功能
  - 變更檔案: 5 個
- **2017-07-18 14:10:05**: 修正絕對位置表單grid元件使用addbinding的元件，畫面上會跑版
  - 變更檔案: 1 個
- **2017-07-18 10:17:01**: 調整統計組件維護UI，註解GridElementMobile中測試用的log
  - 變更檔案: 2 個
- **2017-07-17 09:43:06**: 修正鼎慧部分功能異常
  - 變更檔案: 6 個
- **2017-07-14 16:36:36**: 修正統計組件多語系錯誤，修正工具版號
  - 變更檔案: 5 個
- **2017-07-14 10:03:29**: 調整行動簽核中心
  - 變更檔案: 2 個
- **2017-07-13 09:22:58**: 調整入口平台欄位顯示格式
  - 變更檔案: 1 個
- **2017-07-12 15:48:56**: 調整統計組件
  - 變更檔案: 4 個
- **2017-07-12 14:50:12**: 鼎慧部署工具新增統計組件選項
  - 變更檔案: 4 個
- **2017-07-12 09:44:18**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-12 09:29:07**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-12 09:28:45**: 修正入口平台讀取微信企業號錯誤問題
  - 變更檔案: 2 個
- **2017-07-11 20:02:32**: 調整統計組件ajax資料封裝格式
  - 變更檔案: 2 個
- **2017-07-11 17:32:31**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-11 17:31:59**: 修正行動簽核中心部分錯誤
  - 變更檔案: 6 個
- **2017-07-10 19:06:04**: 修正行動簽核中心微信企業號部份問題
  - 變更檔案: 2 個
- **2017-07-10 18:09:38**: 調整行動簽核中心，新增統計元件欄位及互聯Secret
  - 變更檔案: 8 個
- **2017-07-05 17:36:55**: 新增統計組件欄位，調整鼎慧取到已結案流程時的提示字
  - 變更檔案: 9 個
- **2017-07-05 16:46:05**: 調整入口平台管理機制
  - 變更檔案: 13 個
- **2017-07-05 09:13:32**: 將微信jssdk存到本地端載入
  - 變更檔案: 2 個
- **2017-07-05 09:09:43**: 新稱鼎慧平台部署工具
  - 變更檔案: 8 個
- **2017-06-28 16:41:26**: 調整使用者管理，移除編輯格式驗證
  - 變更檔案: 1 個
- **2017-06-27 11:34:27**: 調整使用者管理欄位名稱以及修正部分邏輯
  - 變更檔案: 4 個
- **2017-06-26 18:11:30**: 補上使用者管理頁面漏掉的地方
  - 變更檔案: 1 個
- **2017-06-26 17:52:01**: 調整使用者管理頁籤，移除微信開關
  - 變更檔案: 1 個
- **2017-06-26 16:57:37**: 調整行動簽核中心使用者管理介面，新增企業微信部分
  - 變更檔案: 3 個
- **2017-06-26 13:50:29**: 行動簽核中心開窗選擇使用者新增聯絡電話
  - 變更檔案: 5 個
- **2017-06-22 17:22:06**: 調整使用者管理頁面微信帳號驗證機制
  - 變更檔案: 2 個
- **2017-06-20 10:51:00**: 移除行動版Grid調整中測試用的log
  - 變更檔案: 1 個
- **2017-06-20 09:43:32**: 調整行動版Grid綁定機制
  - 變更檔案: 4 個
- **2017-06-13 15:56:33**: 修正相對位置設計器欄位模板、無元件顯示名稱
  - 變更檔案: 1 個
- **2017-06-09 10:22:49**: 修正行動版表單RadioButton,CheckBox事件消失問題
  - 變更檔案: 1 個
- **2017-06-08 17:11:00**: 修正行動簽核中心IE破版問題
  - 變更檔案: 1 個
- **2017-06-06 13:57:43**: 調整BPM App提示訊息
  - 變更檔案: 7 個
- **2017-06-06 13:55:53**: 調整BPM App外部網址URL
  - 變更檔案: 1 個
- **2017-06-01 16:44:25**: 修正工作通知問題
  - 變更檔案: 3 個

### 張詠威 (24 commits)

- **2017-07-21 09:48:27**: [寒舍C01-20170608001]追蹤發起參考流程，流程圖會顯示異常
  - 變更檔案: 1 個
- **2017-07-18 19:09:31**: 修正開啟加簽關卡時，當employeeID與UserID不相同時會報錯
  - 變更檔案: 1 個
- **2017-07-14 17:04:02**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-14 17:03:38**: 補 S00-功能單 加簽的關卡可否增加刪除功能
  - 變更檔案: 1 個
- **2017-07-14 15:45:00**: 增加sap import json jar
  - 變更檔案: 1 個
- **2017-07-14 14:19:58**: 調整關注欄位的css樣式 及調整多語系cache機制
  - 變更檔案: 4 個
- **2017-07-14 11:25:34**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-14 11:25:17**: 調整多語系excel 及 DB 的patch
  - 變更檔案: 3 個
- **2017-07-14 10:25:12**: 調整關注欄位jsp
  - 變更檔案: 1 個
- **2017-07-14 09:52:03**: 更新DB的patch 及 調整關註欄位
  - 變更檔案: 4 個
- **2017-07-13 15:07:37**: 新增獨立模組呼叫接口
  - 變更檔案: 2 個
- **2017-07-13 14:23:02**: 調整多語系維護作業的code
  - 變更檔案: 2 個
- **2017-07-13 12:15:35**: 調整5641出貨SQL
  - 變更檔案: 5 個
- **2017-07-13 11:15:44**: 多語系獨立模組
  - 變更檔案: 35 個
- **2017-07-13 11:15:11**: 修正sap Object error
  - 變更檔案: 4 個
- **2017-07-12 14:36:38**: 增加關注模組的設定
  - 變更檔案: 1 個
- **2017-07-12 10:24:21**: 偉昌二次開發關注項目維護作業的code
  - 變更檔案: 8 個
- **2017-07-11 11:53:52**: 將5.6.3.2的sql指令調整為5.6.4.1
  - 變更檔案: 2 個
- **2017-07-11 11:51:48**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-11 11:51:25**: 將5.6.3.2的sql指令調整為5.6.4.1
  - 變更檔案: 2 個
- **2017-07-11 11:10:07**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-11 11:09:43**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-11 11:09:21**: 增加獨立模組的接口
  - 變更檔案: 8 個
- **2017-07-11 10:45:27**: A00-20170629002 waynechang 修正標準加簽的預覽流程圖異常
  - 變更檔案: 1 個

### MiYu (30 commits)

- **2017-07-17 19:04:37**: 修正在流程設定invisible時也要產生該元件
  - 變更檔案: 1 個
- **2017-07-14 10:25:55**: 修正BPMAPP批次簽核畫面跑版
  - 變更檔案: 1 個
- **2017-07-13 09:19:34**: 調整新增行事曆時增加loading
  - 變更檔案: 1 個
- **2017-07-12 10:36:12**: 修正入口平台整合設定微信使用者管理匯入問題 企業號帳號與手機號顛倒問題
  - 變更檔案: 1 個
- **2017-07-11 18:08:19**: 修正BPMAPP表單元件事件消失問題與調整Select元件事件位置 1.TextBox 2.TextArea 3.ListBox 4.DropDown
  - 變更檔案: 3 個
- **2017-07-11 11:06:53**: 調整搜尋行事曆功能
  - 變更檔案: 3 個
- **2017-07-11 08:21:26**: 利用關卡OID取得紀錄行事曆
  - 變更檔案: 3 個
- **2017-07-10 21:05:08**: 調整狀態碼
  - 變更檔案: 1 個
- **2017-07-10 20:56:15**: 調整紀錄行事曆新增與修改 合併回MobileScheduleDTO
  - 變更檔案: 4 個
- **2017-07-10 20:39:20**: 調整MobileScheduleListDTO
  - 變更檔案: 1 個
- **2017-07-10 20:35:01**: marge
  - 變更檔案: 2 個
- **2017-07-10 20:31:19**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 2 個
- **2017-07-10 20:30:44**: marge
  - 變更檔案: 2 個
- **2017-07-10 16:14:36**: 修正行事曆關注資訊與WorkItemOID
  - 變更檔案: 2 個
- **2017-07-10 15:41:26**: 調整紀錄行事曆 1.domain預計時間與儲存時間為Timestamp 2.日期時間轉換 3.List改為MobileScheduleListDTO 4.MobileScheduleListDTO增加MobileScheduleRecordDTO
  - 變更檔案: 5 個
- **2017-07-10 10:29:06**: 調整紀錄行事曆的DTO&Domain(新增主旨) sessionbean新增利用預約時間搜尋紀錄行事曆
  - 變更檔案: 5 個
- **2017-07-10 10:15:04**: 調整:5.6.4.1 create&update SQL(紀錄行事曆新增主旨欄位)
  - 變更檔案: 5 個
- **2017-07-07 15:29:51**: 新增紀錄行事曆的DTO與Domain
  - 變更檔案: 5 個
- **2017-07-07 15:12:08**: 調整新增行事曆沒有流程主旨時改存關卡名稱與時間 調整新增行事曆增加關注資訊、關卡OID 新增關注資訊與關卡OID欄位
  - 變更檔案: 4 個
- **2017-07-07 15:01:32**: 新增紀錄行事曆TABLE
  - 變更檔案: 5 個
- **2017-07-06 17:32:02**: C01-*********** ESS表單含單身資料會報錯(使用流程維護設定) 調整初始化相關套件
  - 變更檔案: 2 個
- **2017-07-04 13:50:28**: A00-*********** 調整Android與iOS顯示落差問題 增加判斷Android手機4.7吋-5.5吋規格的font-size 調整viewport的content增加device-height
  - 變更檔案: 5 個
- **2017-07-04 10:48:07**: 修正入口平台整合設定多打符號
  - 變更檔案: 1 個
- **2017-07-03 17:47:33**: 調整入口平台整合設定畫面給予最小寬度
  - 變更檔案: 1 個
- **2017-06-28 09:01:25**: 補上漏掉企業微信回調網址含IP時提示訊息之多語系
  - 變更檔案: 2 個
- **2017-06-27 12:49:23**: 增加判斷企業微信回調網址含IP時顯示提示訊息 BPMAPP聯絡人的手機與信箱功能關閉
  - 變更檔案: 3 個
- **2017-06-14 12:57:43**: 補上漏修BPMAPP唯讀grid樣式調整 修正鼎慧行事曆指定日期的icon 修正驗證鼎慧平台連線與文字顏色與文字不見問題
  - 變更檔案: 3 個
- **2017-06-07 13:45:16**: 修正BPMAPP使用連結進入表單畫面後，該筆已結案或終止時會出現工作取回失敗訊息的問題
  - 變更檔案: 3 個
- **2017-06-06 13:41:12**: 調整BPMAPP表單元件唯讀樣式 調整關注資訊的UI
  - 變更檔案: 3 個
- **2017-05-26 16:43:57**: 調整關注資訊顯示內容 調整行事曆前端傳的資料內容
  - 變更檔案: 5 個

### pinchi_lin (35 commits)

- **2017-07-17 14:45:18**: A00-20170717001 修正問題-追蹤流程中，若表單元件權限設為隱藏，元件依舊能看到
  - 變更檔案: 1 個
- **2017-07-13 18:54:01**: 修正微信組織綁定表缺少OAuthConfigOID欄位問題(漏簽一支)
  - 變更檔案: 1 個
- **2017-07-13 17:21:29**: 修正微信組織綁定表缺少OAuthConfigOID欄位問題
  - 變更檔案: 7 個
- **2017-07-11 17:54:12**: 修正驗證互聯連線測試功能畫面
  - 變更檔案: 1 個
- **2017-07-11 17:25:13**: 修正驗證互聯連線測試功能bug
  - 變更檔案: 1 個
- **2017-07-11 11:17:53**: 修正簽核時更新行事曆紀錄BUG
  - 變更檔案: 2 個
- **2017-07-10 21:07:33**: 修正密榆調整MobileSchedulDTO部分
  - 變更檔案: 1 個
- **2017-07-10 20:50:22**: 新增功能-簽核時更新行事曆紀錄
  - 變更檔案: 1 個
- **2017-07-10 20:49:13**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-10 20:48:48**: 合併MobileMPlatformScheduleTool
  - 變更檔案: 2 個
- **2017-07-10 18:25:46**: 修正設定檔中BPMAPP整合平台開關邏輯與預設值
  - 變更檔案: 3 個
- **2017-07-10 16:54:22**: 新增功能-行事曆在新增時同步紀錄對應的workItemOID與時間與行事曆ID到DB內
  - 變更檔案: 6 個
- **2017-07-10 14:14:25**: 修正直連表單JS行事曆BUG
  - 變更檔案: 1 個
- **2017-07-07 14:16:25**: 企微通訊錄secret與互聯token&URI移至DB維護後，調整企微同步接口與鼎慧推送消息接口使用的參數值初始化
  - 變更檔案: 2 個
- **2017-07-05 19:13:48**: 新增測試互聯平台連線驗證功能(前端與多語系)
  - 變更檔案: 3 個
- **2017-07-05 17:39:55**: 新增測試互聯平台連線驗證功能(ajax到service)
  - 變更檔案: 7 個
- **2017-07-04 18:36:07**: Q00-*********** 修正流程結案時，BPMAPP推送消息會推送兩次問題
  - 變更檔案: 1 個
- **2017-07-04 09:55:46**: 修正昨日調整之功能BUG(BPMAPP使用微信登入時，若菜單連結未設定語系，則以使用者的電子郵件語系為頁面語系)
  - 變更檔案: 1 個
- **2017-07-03 16:59:59**: 調整BPMAPP使用微信登入時，若菜單連結未設定語系，則以使用者的電子郵件語系為頁面語系
  - 變更檔案: 1 個
- **2017-06-30 18:25:17**: 修正BPMAPP資料庫create&update缺少的的SQL語法(MSSQL)
  - 變更檔案: 1 個
- **2017-06-30 18:23:51**: 修正BPMAPP資料庫create&update缺少的的SQL語法(ORACLESQL)
  - 變更檔案: 4 個
- **2017-06-30 11:44:41**: 修正BPMAPP資料庫create&update缺少的的SQL語法(MSSQL)
  - 變更檔案: 4 個
- **2017-06-29 18:05:11**: Q00-*********** 修正web表單設計師中行動版腳本問題
  - 變更檔案: 1 個
- **2017-06-29 10:23:48**: 修正BPMAPP中，grid維護資料畫面的資料數量提示訊息，更改為grid名稱+資料數量
  - 變更檔案: 3 個
- **2017-06-27 19:13:23**: 調整BPMAPP顯示流程中的流程圖，將其中的"檢視詳細流程資訊.."連結隱藏
  - 變更檔案: 1 個
- **2017-06-27 14:49:56**: 移除部分註解
  - 變更檔案: 1 個
- **2017-06-26 17:22:27**: 調整設定檔中BPMAPP整合平台開關
  - 變更檔案: 8 個
- **2017-06-23 17:29:53**: 調整BPMAPP能支援整合企業微信
  - 變更檔案: 4 個
- **2017-06-23 15:16:53**: Q00-20170623001 修正BPMAPP進入追蹤流程清單時，jboss會報一堆錯誤問題
  - 變更檔案: 1 個
- **2017-06-20 14:52:32**: 修正BPMAPP多選客製開窗問題(由V5找出的BUG上修至V6，議題單號:C01-20170601001)
  - 變更檔案: 1 個
- **2017-06-16 14:55:26**: Q00-20170616001 修正BPMAPP絕對位置與相對位置，若附件有設定權限，表單開不起來問題與附件列表沒有顯示權限設定資訊問題
  - 變更檔案: 6 個
- **2017-06-14 16:17:10**: 修正部分LOG內容
  - 變更檔案: 2 個
- **2017-06-08 18:09:47**: 修改LOG內容錯誤
  - 變更檔案: 1 個
- **2017-06-08 14:30:37**: BPMAPP的消息推播功能將全部推送改成依流程設計師中有開啟支援行動簽核才進行推送
  - 變更檔案: 7 個
- **2017-06-03 16:19:57**: Q00-*********** 修正進入微信應用後的被動響應消息失效問題(含使用被動響應消息的查詢發單、查詢人員)
  - 變更檔案: 5 個

### jd (14 commits)

- **2017-07-12 10:55:14**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-12 10:52:28**: 修正统计组件颜色问题
  - 變更檔案: 1 個
- **2017-07-10 15:15:52**: 調整REST對照Key值,改採物件管理 調整REST統計元件管理 調整REST統計元件設定
  - 變更檔案: 9 個
- **2017-07-10 15:13:44**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-10 11:27:22**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-10 10:22:54**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-06 11:34:04**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-07-06 11:18:04**: 合并InitMobileDB_Oracle.sql
  - 變更檔案: 1 個
- **2017-07-05 15:54:17**: 新增直連表單session控制功能 新增統計組件功能
  - 變更檔案: 4 個
- **2017-06-28 13:48:56**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-06-28 13:47:25**: 修正關注欄位,改為顯示關注提示訊息
  - 變更檔案: 3 個
- **2017-05-31 16:23:59**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-05-31 16:22:52**: 修正鼎慧异常问题
  - 變更檔案: 3 個
- **2017-05-26 16:42:14**: 修正統計組件樣式問題
  - 變更檔案: 2 個

### jerry1218 (3 commits)

- **2017-07-11 10:49:46**: Q00-20170711001 修正流程內容中如有子流程關卡 , 在點選子流程關卡後跳到子流程流程圖時會出現ClassCastException錯誤
  - 變更檔案: 1 個
- **2017-07-07 16:05:06**: 5.6.3.2 update SQL製作(移動桌面)
  - 變更檔案: 2 個
- **2017-06-29 17:55:56**: 修改移動桌面相關table名稱欄位長度(改為255)
  - 變更檔案: 2 個

### Gaspard (2 commits)

- **2017-06-28 11:28:16**: SqlInjection議題防範，增加「 OR 」關鍵字判斷
  - 變更檔案: 1 個
- **2017-06-26 14:55:53**: 修正資料選取器與維護樣板SQL異常自動刪除orderby的條件
  - 變更檔案: 1 個

### wayne (16 commits)

- **2017-06-22 16:13:44**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-06-22 16:13:13**: 調整5632update SQL
  - 變更檔案: 2 個
- **2017-06-21 18:05:34**: 調整關註欄位維護作業及新增SQL連結及多語系
  - 變更檔案: 6 個
- **2017-06-15 14:35:05**: 新增關注欄位維護作業模組
  - 變更檔案: 42 個
- **2017-06-15 14:24:49**: 增加json開窗功能
  - 變更檔案: 1 個
- **2017-06-15 14:24:24**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-06-15 14:23:32**: 增加json客製開窗功能
  - 變更檔案: 3 個
- **2017-06-14 14:34:29**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-06-14 14:32:39**: C01-20170609001 修正同個workitem有多筆轉派紀錄時，批次簽核執行會發生錯誤
  - 變更檔案: 2 個
- **2017-06-12 18:11:14**: A00-20170414001 修正動態加簽關卡導致流程圖顯示異常
  - 變更檔案: 2 個
- **2017-06-03 14:24:36**: 調整關注欄位維護作業
  - 變更檔案: 8 個
- **2017-06-02 15:28:05**: A00-20170414001 修正動態加簽關卡流程圖顯示異常
  - 變更檔案: 1 個
- **2017-06-02 10:08:56**: 調整關注欄位hibernate XML
  - 變更檔案: 1 個
- **2017-06-01 14:59:25**: 新增關注欄位維護作業Hibernate
  - 變更檔案: 34 個
- **2017-06-01 14:24:49**: 將異常流程單據調整為全部撈取
  - 變更檔案: 2 個
- **2017-06-01 14:09:28**: A00-20170414001 修正動態加簽關卡流程圖顯示異常
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. 多語系維護作業   描述查詢條件選項  移除 等於和不等於
- **Commit ID**: `ad91ae34597b8258d44c4e6fa1da9bca3e1353b7`
- **作者**: joseph
- **日期**: 2017-07-21 17:24:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`

### 2. 修正鼎慧追蹤-已/未完成列表異常
- **Commit ID**: `b819a04df76f5dc90a00b8f3d080445bd31f0f62`
- **作者**: ChinRong
- **日期**: 2017-07-21 13:52:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js`

### 3. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `cb6074c9564a85f0f9edeefbcde7dc043ece0618`
- **作者**: joseph
- **日期**: 2017-07-21 10:01:01
- **變更檔案數量**: 0

### 4. Q00-20170721001 修正ISO 文件總管 ,點選文件中的放大鏡會報錯 	原因:jspd中要取得的Session是空的
- **Commit ID**: `e6b1f0880086030df1010ea21ad678b586ed560a`
- **作者**: joseph
- **日期**: 2017-07-21 10:00:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 5. [寒舍C01-20170608001]追蹤發起參考流程，流程圖會顯示異常
- **Commit ID**: `3f81589e42cd1b24fce21f3c5c5556a43438ec3b`
- **作者**: 張詠威
- **日期**: 2017-07-21 09:48:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 6. 修正鼎慧列表部署工具，追蹤流程已/未完成選項名稱寫反了
- **Commit ID**: `baea04bc542b5efc78bdc01d269d1f7fb83c050a`
- **作者**: ChinRong
- **日期**: 2017-07-20 11:36:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 7. 修正鼎慧使用者匯入匯出錯誤
- **Commit ID**: `daa786023fd76b399d1765fc037a3d203f6e8713`
- **作者**: ChinRong
- **日期**: 2017-07-20 10:13:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 8. 新增BPM APP草稿刪除功能
- **Commit ID**: `511e4f003b29d24647a4925d6b6fc62370a91977`
- **作者**: ChinRong
- **日期**: 2017-07-19 11:39:45
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 9. 修正開啟加簽關卡時，當employeeID與UserID不相同時會報錯
- **Commit ID**: `ae9df32b4f07428bee3aa4b418253d84400e9f43`
- **作者**: 張詠威
- **日期**: 2017-07-18 19:09:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`

### 10. 修正 :離職人員維護作業當沒有人時會報錯的問題
- **Commit ID**: `68daa66c02c9be91f2c5c8dc05f3c32da2387adb`
- **作者**: joseph
- **日期**: 2017-07-18 18:42:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java`

### 11. 修正絕對位置表單grid元件使用addbinding的元件，畫面上會跑版
- **Commit ID**: `d60e81dcfedd4c6ab69e8687da2b29dac4068457`
- **作者**: ChinRong
- **日期**: 2017-07-18 14:10:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`

### 12. 調整統計組件維護UI，註解GridElementMobile中測試用的log
- **Commit ID**: `aa7ffc4226713bab9cef4d92ce3c1be7d9e66b3b`
- **作者**: ChinRong
- **日期**: 2017-07-18 10:17:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 13. 修正在流程設定invisible時也要產生該元件
- **Commit ID**: `62c27c682ee77f10fe6010818146675181419498`
- **作者**: MiYu
- **日期**: 2017-07-17 19:04:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`

### 14. 修正 : Udpate SQLSERVER 關注事項 CriticalFocusProcess ,CriticalProcessDefinition 的processPackageId 改為nvarchar(255)
- **Commit ID**: `44587e925251652cf85075034f0cb3771fdf6a94`
- **作者**: joseph
- **日期**: 2017-07-17 15:27:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_SQLServer.sql`

### 15. 修改 :關注事項ORACLE欄位型態  及 將系統多語系維護作業URL更改為查詢維護樣板的URL與JSP的 ACESSRIGHT
- **Commit ID**: `3571229238286187b87b0d749f539b86f3070027`
- **作者**: joseph
- **日期**: 2017-07-17 14:55:11
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_SQLServer.sql`

### 16. A00-20170717001 修正問題-追蹤流程中，若表單元件權限設為隱藏，元件依舊能看到
- **Commit ID**: `7936141f536da0a82f775cf1166fb19d752f6022`
- **作者**: pinchi_lin
- **日期**: 2017-07-17 14:45:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`

### 17. 修正鼎慧部分功能異常
- **Commit ID**: `74cd7a73a5920391a4c5d70b72995172178664ee`
- **作者**: ChinRong
- **日期**: 2017-07-17 09:43:06
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js`

### 18. 修正:出貨5.6.4.1 Oracle Update_SQL
- **Commit ID**: `e4da30e7025929576e79c41b3cb6310c130be863`
- **作者**: joseph
- **日期**: 2017-07-17 09:11:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql`

### 19. 修改 5.6.4.1 updateSQL.Oracle
- **Commit ID**: `bdab6bfde6dfc150ad5dfeb0055889bdf49f9bda`
- **作者**: joseph
- **日期**: 2017-07-14 18:35:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql`

### 20. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `4821eb2600f5e34064311d7335d08739979f9181`
- **作者**: 張詠威
- **日期**: 2017-07-14 17:04:02
- **變更檔案數量**: 0

### 21. 補 S00-功能單 加簽的關卡可否增加刪除功能
- **Commit ID**: `865c6dad1bd17a2b817f0057b78a0774c7bc3f3a`
- **作者**: 張詠威
- **日期**: 2017-07-14 17:03:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 22. 修正統計組件多語系錯誤，修正工具版號
- **Commit ID**: `0a67e08619bc5699d9cd5610336a4f0d825b63fc`
- **作者**: ChinRong
- **日期**: 2017-07-14 16:36:36
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 23. 增加sap import json jar
- **Commit ID**: `6a2051cf4d6ab8a9cb1caeea6f1fefc54a18f64f`
- **作者**: 張詠威
- **日期**: 2017-07-14 15:45:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`

### 24. 調整關注欄位的css樣式 及調整多語系cache機制
- **Commit ID**: `693208aabe744b94e8a5d9e9636f9f15c1b603ec`
- **作者**: 張詠威
- **日期**: 2017-07-14 14:19:58
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/RsrcBundleCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`

### 25. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `fd4da86976a95c06ee9f2186ca1bb55610c25f82`
- **作者**: 張詠威
- **日期**: 2017-07-14 11:25:34
- **變更檔案數量**: 0

### 26. 調整多語系excel 及 DB 的patch
- **Commit ID**: `96f8969d6a7fff513662350d71cb3ad27b1dc65c`
- **作者**: 張詠威
- **日期**: 2017-07-14 11:25:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 27. 修正BPMAPP批次簽核畫面跑版
- **Commit ID**: `4abd99355d2bf3714bb62e1394eb26f8dcf621c5`
- **作者**: MiYu
- **日期**: 2017-07-14 10:25:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 28. 調整關注欄位jsp
- **Commit ID**: `32abb6ada8f0ca64529b736cccf2356323c8e0e8`
- **作者**: 張詠威
- **日期**: 2017-07-14 10:25:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`

### 29. 調整行動簽核中心
- **Commit ID**: `28d219aac252cd8d844e462e97ef491ab6ef18c9`
- **作者**: ChinRong
- **日期**: 2017-07-14 10:03:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 30. 更新DB的patch 及 調整關註欄位
- **Commit ID**: `b9e3a332301be7519a6fd197ce487ab8c331a74d`
- **作者**: 張詠威
- **日期**: 2017-07-14 09:52:03
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 31. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `438d2c6d6537883beea9c759d4785b4dc05a60b8`
- **作者**: joseph
- **日期**: 2017-07-14 09:18:34
- **變更檔案數量**: 0

### 32. Q00-20170602002 移除log及調整多語系
- **Commit ID**: `72fd545a1605313fa2fb37e5c8881947549af928`
- **作者**: joseph
- **日期**: 2017-07-14 09:18:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmMailStraightSignOffPhrase.jsp`

### 33. 修正微信組織綁定表缺少OAuthConfigOID欄位問題(漏簽一支)
- **Commit ID**: `d185c83c01aaa9c93f26b0986d464f0280dffc8e`
- **作者**: pinchi_lin
- **日期**: 2017-07-13 18:54:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql`

### 34. 修正微信組織綁定表缺少OAuthConfigOID欄位問題
- **Commit ID**: `59f228acbde8c754567cff92b1620244424e9014`
- **作者**: pinchi_lin
- **日期**: 2017-07-13 17:21:29
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthWeChatOrganization.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthClientOrgDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`

### 35. 新增獨立模組呼叫接口
- **Commit ID**: `abbd0d163c7db4a9bf7f914859edcafbf619bab8`
- **作者**: 張詠威
- **日期**: 2017-07-13 15:07:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 36. 調整多語系維護作業的code
- **Commit ID**: `406a549e9b0ddefa517fd9dde02646c7cbe3ba43`
- **作者**: 張詠威
- **日期**: 2017-07-13 14:23:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/js/QueryTemplate.js`

### 37. 調整5641出貨SQL
- **Commit ID**: `00bad35c792788e5e2a1c9192ddfe06944dec46e`
- **作者**: 張詠威
- **日期**: 2017-07-13 12:15:35
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@criticalModule/5.6.4.1_createSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@criticalModule/5.6.4.1_createSQL_SQLServer.sql`

### 38. 多語系獨立模組
- **Commit ID**: `c4bb303097fb7e438242c6922d23bbdfc2e2ba85`
- **作者**: 張詠威
- **日期**: 2017-07-13 11:15:44
- **變更檔案數量**: 35
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/CannotAccessWarnning.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/MultiLanguageSet.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/UserInfo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/BpmTable.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/QueryDesinger.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/bootstrap/bootstrap-3.3.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/AccessRight.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/BpmTable.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/CustomDataChooser.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/ModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/OpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/QueryTemplate.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrap-3.3.4.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/jquery-1.11.3.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/jqueryUI/jquery-ui-1.11.4.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/jqueryUI/jquery.ui-contextmenu.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/js/json2.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_en_US.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_zh_CN.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_zh_TW.js`

### 39. 修正sap Object error
- **Commit ID**: `2b003ea4a4b57fe786cf3daa17bfe6f686aaaf50`
- **作者**: 張詠威
- **日期**: 2017-07-13 11:15:11
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`

### 40. 調整入口平台欄位顯示格式
- **Commit ID**: `4ea1edd04ead641541fe17ec329059f32a2ca8ea`
- **作者**: ChinRong
- **日期**: 2017-07-13 09:22:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 41. 調整新增行事曆時增加loading
- **Commit ID**: `c945cecb724ab9c3c766efbfd4b12c2076811ea9`
- **作者**: MiYu
- **日期**: 2017-07-13 09:19:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`

### 42. 調整統計組件
- **Commit ID**: `151182957315b36304e2270c3dc1c45e5266e5f1`
- **作者**: ChinRong
- **日期**: 2017-07-12 15:48:56
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 43. 鼎慧部署工具新增統計組件選項
- **Commit ID**: `213253aec0ef69d15726007e2f6f3229f5871a37`
- **作者**: ChinRong
- **日期**: 2017-07-12 14:50:12
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 44. 增加關注模組的設定
- **Commit ID**: `ff4cb8f1ae44b31794580a2535c7c58291d22139`
- **作者**: 張詠威
- **日期**: 2017-07-12 14:36:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties`

### 45. 修改 :查詢維護樣板 DropDown元件中請選擇的內顯值'$$$$$$' 改為''
- **Commit ID**: `cbf74661ee8710b31c02105f08e93c3295e0d092`
- **作者**: joseph
- **日期**: 2017-07-12 12:02:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`

### 46. 新增:離職人員維護作業多語系
- **Commit ID**: `a4b1115b756274a47df65cb5cdb8fed97de81007`
- **作者**: joseph
- **日期**: 2017-07-12 11:41:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`

### 47. 修改多語系檔名
- **Commit ID**: `be1ef6d39bc018984cf0288efb04a4239ebfe67d`
- **作者**: joseph
- **日期**: 2017-07-12 11:40:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`

### 48. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `4938ddc3fb7536160277a2cbc8575b5be73bf3f7`
- **作者**: jd
- **日期**: 2017-07-12 10:55:14
- **變更檔案數量**: 0

### 49. 修正统计组件颜色问题
- **Commit ID**: `d343901d5b3e251ba03466d4f9d503d41738a95a`
- **作者**: jd
- **日期**: 2017-07-12 10:52:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`

### 50. 修正入口平台整合設定微信使用者管理匯入問題 企業號帳號與手機號顛倒問題
- **Commit ID**: `d19b128b0585042e4cdbe85bbdcc3033ae2f24f7`
- **作者**: MiYu
- **日期**: 2017-07-12 10:36:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 51. 偉昌二次開發關注項目維護作業的code
- **Commit ID**: `6c9533afa59dceef8436442ee4d48d797ffeca89`
- **作者**: 張詠威
- **日期**: 2017-07-12 10:24:21
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/MultiLanguageSet.jsp`

### 52. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a7e7898c5c68b7456cb07e9c24c59f19557318e7`
- **作者**: ChinRong
- **日期**: 2017-07-12 09:44:18
- **變更檔案數量**: 0

### 53. S00-20170703001 :修改離職人員作業:將搜尋分為可以只顯示需處理離職人員及所有離職人員
- **Commit ID**: `f48f928aefac6b12e80e53ae3eb167b207407b05`
- **作者**: joseph
- **日期**: 2017-07-12 09:32:02
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/resignedEmployees/ResignedEmployeesSearchViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-resignedEmployeesMaintain-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`

### 54. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `666cc6f023b1e02adefe7356a3bfac8a587eb998`
- **作者**: ChinRong
- **日期**: 2017-07-12 09:29:07
- **變更檔案數量**: 0

### 55. 修正入口平台讀取微信企業號錯誤問題
- **Commit ID**: `bf07a9cc544fba4c9f1d143bbe7eb096eaa811e1`
- **作者**: ChinRong
- **日期**: 2017-07-12 09:28:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 56. 移除不必要的log,及埋入SQL Command log
- **Commit ID**: `6b00d3fffd0b8205bb29604dc5b79c679a648f0b`
- **作者**: joseph
- **日期**: 2017-07-12 09:28:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RscBundleListReader.java`

### 57. 新增 : 多語系依照Key刪除_ajax接口
- **Commit ID**: `ddf36669d571a26fab3aa64551399c9939315fd6`
- **作者**: joseph
- **日期**: 2017-07-12 09:25:00
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RsrcBundleDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/ISysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java`

### 58. 調整統計組件ajax資料封裝格式
- **Commit ID**: `e5d1adfaf666b7cf63462dbaee0129fb86cc475c`
- **作者**: ChinRong
- **日期**: 2017-07-11 20:02:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 59. 修正BPMAPP表單元件事件消失問題與調整Select元件事件位置 1.TextBox 2.TextArea 3.ListBox 4.DropDown
- **Commit ID**: `e1234f23ee2e53b46672a062e115bd2099477edd`
- **作者**: MiYu
- **日期**: 2017-07-11 18:08:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`

### 60. 修改 :語系支援Win10繁中及簡中
- **Commit ID**: `7dc13966962fea97aa4bf00a971b11a6c861b9ff`
- **作者**: joseph
- **日期**: 2017-07-11 18:05:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/IntegratePortalURLEntranceAction.java`

### 61. 新增:事件重要等級定義作業 Create接口
- **Commit ID**: `0eb9f45f4b7db738007df91c838e5e5b767c6f17`
- **作者**: joseph
- **日期**: 2017-07-11 18:03:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/CriticalFocusProcessDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/critical/CriticalManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalFocusProcessViewer.java`

### 62. 增加 : 新增及修改多語系時,要寫入Updater及時間
- **Commit ID**: `981a027bba6558b73955870dabe859d1e2aa1da4`
- **作者**: joseph
- **日期**: 2017-07-11 17:59:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`

### 63. 新增 : 多語系維護作業Ajax接口
- **Commit ID**: `12a52d679e9f29618dd772efef741ab958f006d2`
- **作者**: joseph
- **日期**: 2017-07-11 17:58:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java`

### 64. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `3fb1f312bac4a2e6bb9f9b6fb74771bc11ebc324`
- **作者**: joseph
- **日期**: 2017-07-11 17:57:55
- **變更檔案數量**: 0

### 65. 新增: 多語系維護作業ListReader
- **Commit ID**: `a12417201a6a256f78ba60c96db3c4e91078c5de`
- **作者**: joseph
- **日期**: 2017-07-11 17:57:35
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RscBundleListReader.java`

### 66. 修正驗證互聯連線測試功能畫面
- **Commit ID**: `a15f84c3754a5c58381372459e4cdff02e42c414`
- **作者**: pinchi_lin
- **日期**: 2017-07-11 17:54:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 67. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `660874ca30c0521e37dc0466273fb3b8a4968d90`
- **作者**: ChinRong
- **日期**: 2017-07-11 17:32:31
- **變更檔案數量**: 0

### 68. 修正行動簽核中心部分錯誤
- **Commit ID**: `80662243b3e65d6dd2b49589ba18ac5b25bf9067`
- **作者**: ChinRong
- **日期**: 2017-07-11 17:31:59
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql`

### 69. 修正驗證互聯連線測試功能bug
- **Commit ID**: `4be5f3631f161270eb02242fdd385ae69ba2c3a7`
- **作者**: pinchi_lin
- **日期**: 2017-07-11 17:25:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 70. 將5.6.3.2的sql指令調整為5.6.4.1
- **Commit ID**: `8e97cc0cfa626c2e539fad29454d7c43e3661321`
- **作者**: 張詠威
- **日期**: 2017-07-11 11:53:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_SQLServer.sql`

### 71. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `49d3ec6004390840791764c83fc60c3baa5c5610`
- **作者**: 張詠威
- **日期**: 2017-07-11 11:51:48
- **變更檔案數量**: 0

### 72. 將5.6.3.2的sql指令調整為5.6.4.1
- **Commit ID**: `0afc6f82dbbdcc661d88f7edd1947c89224bdfe4`
- **作者**: 張詠威
- **日期**: 2017-07-11 11:51:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_SQLServer.sql`

### 73. 修正簽核時更新行事曆紀錄BUG
- **Commit ID**: `49259625acbdfc1a839fa4d666dba2a11a28be52`
- **作者**: pinchi_lin
- **日期**: 2017-07-11 11:17:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`

### 74. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `e0401b009efc14991196707f8674a7a033bd139c`
- **作者**: 張詠威
- **日期**: 2017-07-11 11:10:07
- **變更檔案數量**: 0

### 75. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `cd61f81af59ced5d5c291dcde1cebbbae95c5685`
- **作者**: 張詠威
- **日期**: 2017-07-11 11:09:43
- **變更檔案數量**: 0

### 76. 增加獨立模組的接口
- **Commit ID**: `c4ff0865b57ffe776d57b0e932d077d943b872cb`
- **作者**: 張詠威
- **日期**: 2017-07-11 11:09:21
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/RsrcBundleCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LanguageMaintainAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/CustomModuleUserInfoCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 77. 調整搜尋行事曆功能
- **Commit ID**: `0b5e1801d90cb5140612b239eb7639db5fefdb51`
- **作者**: MiYu
- **日期**: 2017-07-11 11:06:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java`

### 78. Q00-20170711001 修正流程內容中如有子流程關卡 , 在點選子流程關卡後跳到子流程流程圖時會出現ClassCastException錯誤
- **Commit ID**: `49d1b89384f29db9c37a121222a0221dd3d8120f`
- **作者**: jerry1218
- **日期**: 2017-07-11 10:49:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 79. A00-20170629002 waynechang 修正標準加簽的預覽流程圖異常
- **Commit ID**: `df6da21165f1a54f761cd9a0b7a3e1c13793c391`
- **作者**: 張詠威
- **日期**: 2017-07-11 10:45:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`

### 80. 利用關卡OID取得紀錄行事曆
- **Commit ID**: `9daccb958d0ab5bbc392f07813f16cb107aee249`
- **作者**: MiYu
- **日期**: 2017-07-11 08:21:26
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java`

### 81. 修正密榆調整MobileSchedulDTO部分
- **Commit ID**: `557d15c877775c710cf52784eeb067a7185da08f`
- **作者**: pinchi_lin
- **日期**: 2017-07-10 21:07:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`

### 82. 調整狀態碼
- **Commit ID**: `3140012e3bc4c680d1ba4f9cac80e06c9613fa6b`
- **作者**: MiYu
- **日期**: 2017-07-10 21:05:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`

### 83. 調整紀錄行事曆新增與修改 合併回MobileScheduleDTO
- **Commit ID**: `ed0028516ddf3c4145b269689db924e479af1a93`
- **作者**: MiYu
- **日期**: 2017-07-10 20:56:15
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java`

### 84. 新增功能-簽核時更新行事曆紀錄
- **Commit ID**: `7189f0df41d5cd1ad33d7c43303dd16e8de56f20`
- **作者**: pinchi_lin
- **日期**: 2017-07-10 20:50:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 85. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `d436618f0118062f66add270d9b0d0168feb2197`
- **作者**: pinchi_lin
- **日期**: 2017-07-10 20:49:13
- **變更檔案數量**: 0

### 86. 合併MobileMPlatformScheduleTool
- **Commit ID**: `fa3078f5b7a279b40ba2633ac0c6d802c2401bb7`
- **作者**: pinchi_lin
- **日期**: 2017-07-10 20:48:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java`

### 87. 調整MobileScheduleListDTO
- **Commit ID**: `23cf91e80ac790c2b164fb22cacc5f1b8caa6c69`
- **作者**: MiYu
- **日期**: 2017-07-10 20:39:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleListDTO.java`

### 88. marge
- **Commit ID**: `fddfa31d5836e99a70cd1d95e6287eac07842b6e`
- **作者**: MiYu
- **日期**: 2017-07-10 20:35:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleListDTO.java`
  - ❌ **刪除**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleRecordDTO.java`

### 89. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `086129ade263112f657c848ce5e27aef80e8dc65`
- **作者**: MiYu
- **日期**: 2017-07-10 20:31:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`

### 90. marge
- **Commit ID**: `3f0a0469fcf7c781720f7c257a126e6a8bf024f0`
- **作者**: MiYu
- **日期**: 2017-07-10 20:30:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`

### 91. 修正行動簽核中心微信企業號部份問題
- **Commit ID**: `be00a6a94159ef09ac6fede91510c17b120cca26`
- **作者**: ChinRong
- **日期**: 2017-07-10 19:06:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 92. 修正設定檔中BPMAPP整合平台開關邏輯與預設值
- **Commit ID**: `c30bda47dcf8bf8d8668af0579aa30ace1aeb73f`
- **作者**: pinchi_lin
- **日期**: 2017-07-10 18:25:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`

### 93. 調整行動簽核中心，新增統計元件欄位及互聯Secret
- **Commit ID**: `68ee4dfea0d7006fee9973f7c15cf86b263144ca`
- **作者**: ChinRong
- **日期**: 2017-07-10 18:09:38
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql`

### 94. 新增功能-行事曆在新增時同步紀錄對應的workItemOID與時間與行事曆ID到DB內
- **Commit ID**: `9b8e22f1adf654d4bbea4f53832b3c4dc582cf72`
- **作者**: pinchi_lin
- **日期**: 2017-07-10 16:54:22
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`

### 95. 修正行事曆關注資訊與WorkItemOID
- **Commit ID**: `cddfc76e8d6dbbf77edf7ffa364f37f2238ffb2e`
- **作者**: MiYu
- **日期**: 2017-07-10 16:14:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`

### 96. 調整紀錄行事曆 1.domain預計時間與儲存時間為Timestamp 2.日期時間轉換 3.List改為MobileScheduleListDTO 4.MobileScheduleListDTO增加MobileScheduleRecordDTO
- **Commit ID**: `3cc77c01697858142edc6adeb10964387596f52d`
- **作者**: MiYu
- **日期**: 2017-07-10 15:41:26
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileScheduleRecord.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java`

### 97. 調整REST對照Key值,改採物件管理 調整REST統計元件管理 調整REST統計元件設定
- **Commit ID**: `e8757c8a8d2c996b0723f2a0aea08211dc13eb6c`
- **作者**: jd
- **日期**: 2017-07-10 15:15:52
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/.settings/org.eclipse.core.resources.prefs`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/dataformat/MobileHttpStatusCode.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformDAPService.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`

### 98. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `e6c79fb7ce8128f6cb083e5ca3c489e5021d7c68`
- **作者**: jd
- **日期**: 2017-07-10 15:13:44
- **變更檔案數量**: 0

### 99. 修正直連表單JS行事曆BUG
- **Commit ID**: `22b801f7690738b17b98250dad4c1b88e9b5d086`
- **作者**: pinchi_lin
- **日期**: 2017-07-10 14:14:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`

### 100. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `5d9fa7acf4ef64539917c0e3065de5a51e6b311e`
- **作者**: jd
- **日期**: 2017-07-10 11:27:22
- **變更檔案數量**: 0

### 101. 調整紀錄行事曆的DTO&Domain(新增主旨) sessionbean新增利用預約時間搜尋紀錄行事曆
- **Commit ID**: `16d922d40065e001dd4077bc31f66fc3dd35a0f4`
- **作者**: MiYu
- **日期**: 2017-07-10 10:29:06
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileScheduleRecord.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleRecordDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java`

### 102. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `6fa5e6b2e7af51d3b28c2315c76f3677ee53fc77`
- **作者**: jd
- **日期**: 2017-07-10 10:22:54
- **變更檔案數量**: 0

### 103. 調整:5.6.4.1 create&update SQL(紀錄行事曆新增主旨欄位)
- **Commit ID**: `dc2ce4c30fb2bbb0048fe12aad5a35c2d61bf567`
- **作者**: MiYu
- **日期**: 2017-07-10 10:15:04
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql`

### 104. 5.6.3.2 update SQL製作(移動桌面)
- **Commit ID**: `afe91cabb8a42f1d02b1fc54385a1e9aa8f7e0df`
- **作者**: jerry1218
- **日期**: 2017-07-07 16:05:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_SQLServer.sql`

### 105. 新增紀錄行事曆的DTO與Domain
- **Commit ID**: `4fbc0f781e2c4673fc9c5621d48fe2b08f1dd3f7`
- **作者**: MiYu
- **日期**: 2017-07-07 15:29:51
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileScheduleRecord.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleRecordDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java`

### 106. 調整新增行事曆沒有流程主旨時改存關卡名稱與時間 調整新增行事曆增加關注資訊、關卡OID 新增關注資訊與關卡OID欄位
- **Commit ID**: `20d6c9f469453534f87928866f24c4a44a44acd8`
- **作者**: MiYu
- **日期**: 2017-07-07 15:12:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`

### 107. 新增紀錄行事曆TABLE
- **Commit ID**: `834291561b3c2fc568dd760ce8cdf273dacfaa84`
- **作者**: MiYu
- **日期**: 2017-07-07 15:01:32
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql`

### 108. 企微通訊錄secret與互聯token&URI移至DB維護後，調整企微同步接口與鼎慧推送消息接口使用的參數值初始化
- **Commit ID**: `59f2661ed1bf8f1e0fb8034664b2a7e5decd760a`
- **作者**: pinchi_lin
- **日期**: 2017-07-07 14:16:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`

### 109. 修正:出貨DB InitNaNaDB_ORACLE,關注事項TABLE SQL語法ALTER改為CREATE
- **Commit ID**: `09d863eb1da03b38a08c658ce93c51acbee2c897`
- **作者**: joseph
- **日期**: 2017-07-06 18:25:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`

### 110. C01-*********** ESS表單含單身資料會報錯(使用流程維護設定) 調整初始化相關套件
- **Commit ID**: `f14874cd27dcffee2ef699c29d52270cb89dd258`
- **作者**: MiYu
- **日期**: 2017-07-06 17:32:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`

### 111. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `87356dc73960b4ce0aac60f2df7e62c44d3b92d3`
- **作者**: jd
- **日期**: 2017-07-06 11:34:04
- **變更檔案數量**: 0

### 112. 合并InitMobileDB_Oracle.sql
- **Commit ID**: `4691da4631114598b66c1691f94aaabda8c77e79`
- **作者**: jd
- **日期**: 2017-07-06 11:18:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`

### 113. 新增測試互聯平台連線驗證功能(前端與多語系)
- **Commit ID**: `fee2de3bf0222d2265fe405e99778d1afaa02d8a`
- **作者**: pinchi_lin
- **日期**: 2017-07-05 19:13:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 114. 新增測試互聯平台連線驗證功能(ajax到service)
- **Commit ID**: `0c13306f2af736793289c2f7ab42bdca91b6b577`
- **作者**: pinchi_lin
- **日期**: 2017-07-05 17:39:55
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformRESTTransferTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 115. 新增統計組件欄位，調整鼎慧取到已結案流程時的提示字
- **Commit ID**: `a3d25a571859352d29085593abe6caa68f20c52c`
- **作者**: ChinRong
- **日期**: 2017-07-05 17:36:55
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql`

### 116. 調整入口平台管理機制
- **Commit ID**: `6dba4aaef7e2ab856ce659d82717944941256a3b`
- **作者**: ChinRong
- **日期**: 2017-07-05 16:46:05
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql`

### 117. 新增直連表單session控制功能 新增統計組件功能
- **Commit ID**: `433b0c47ba06faa476b6c51a524528a1b539f9a8`
- **作者**: jd
- **日期**: 2017-07-05 15:54:17
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`

### 118. 將微信jssdk存到本地端載入
- **Commit ID**: `0eaacba1052905fe8d52ca83027a61de02ccc0b7`
- **作者**: ChinRong
- **日期**: 2017-07-05 09:13:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/jweixin-1.2.0.js`

### 119. 新稱鼎慧平台部署工具
- **Commit ID**: `2fe0abed0f67f4bc9bb99e617fc8dc82dfdc30e2`
- **作者**: ChinRong
- **日期**: 2017-07-05 09:09:43
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAllProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 120. Q00-*********** 修正流程結案時，BPMAPP推送消息會推送兩次問題
- **Commit ID**: `f9490554f4eb5c8fa4187dc2201b13750a1353dc`
- **作者**: pinchi_lin
- **日期**: 2017-07-04 18:36:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 121. A00-*********** 調整Android與iOS顯示落差問題 增加判斷Android手機4.7吋-5.5吋規格的font-size 調整viewport的content增加device-height
- **Commit ID**: `a8854f37420e34db39961161255be62baa08f9ea`
- **作者**: MiYu
- **日期**: 2017-07-04 13:50:28
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`

### 122. 修正入口平台整合設定多打符號
- **Commit ID**: `e41614292d7732a88a3cd4f90916f49cb6ba2ddd`
- **作者**: MiYu
- **日期**: 2017-07-04 10:48:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 123. 修正昨日調整之功能BUG(BPMAPP使用微信登入時，若菜單連結未設定語系，則以使用者的電子郵件語系為頁面語系)
- **Commit ID**: `8997fb2eb62a5baf0c1f2eee8f223760fc670f55`
- **作者**: pinchi_lin
- **日期**: 2017-07-04 09:55:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 124. 調整入口平台整合設定畫面給予最小寬度
- **Commit ID**: `1e8ed7d9d77cb2de7add25f0209a2c4830823d6e`
- **作者**: MiYu
- **日期**: 2017-07-03 17:47:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 125. 調整BPMAPP使用微信登入時，若菜單連結未設定語系，則以使用者的電子郵件語系為頁面語系
- **Commit ID**: `78ea9b1ba063100ba63e8e64e545aa376741a81e`
- **作者**: pinchi_lin
- **日期**: 2017-07-03 16:59:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 126. 修正BPMAPP資料庫create&update缺少的的SQL語法(MSSQL)
- **Commit ID**: `6acaeefaf04a53a6c57558c73c275daecb979cbf`
- **作者**: pinchi_lin
- **日期**: 2017-06-30 18:25:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_SQLServer.sql`

### 127. 修正BPMAPP資料庫create&update缺少的的SQL語法(ORACLESQL)
- **Commit ID**: `b2eeb95c73d13f69957d54418bc1527d38d781b2`
- **作者**: pinchi_lin
- **日期**: 2017-06-30 18:23:51
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_Oracle.sql`

### 128. 修正BPMAPP資料庫create&update缺少的的SQL語法(MSSQL)
- **Commit ID**: `a30b9be1f10017d7d6ae0b251e0ff5a3c300626b`
- **作者**: pinchi_lin
- **日期**: 2017-06-30 11:44:41
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.2.1_updateSQL_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_SQLServer.sql`

### 129. 新增 :關注項目多語系欄位及新增CriticalDefinition方法
- **Commit ID**: `b6023898649f6c667a28009710953a0c94687476`
- **作者**: joseph
- **日期**: 2017-06-30 09:13:55
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/CriticalManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalDefinitionViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalPriorityViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalProcessDefinitionViewer.java`

### 130. Q00-*********** 修正web表單設計師中行動版腳本問題
- **Commit ID**: `58c0de2084e8eb2ac2133e6f5cbbb55dd461929a`
- **作者**: pinchi_lin
- **日期**: 2017-06-29 18:05:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`

### 131. 修改移動桌面相關table名稱欄位長度(改為255)
- **Commit ID**: `86a241df758353ddb61210b6fb6ceb3b56014096`
- **作者**: jerry1218
- **日期**: 2017-06-29 17:55:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`

### 132. 修正BPMAPP中，grid維護資料畫面的資料數量提示訊息，更改為grid名稱+資料數量
- **Commit ID**: `f1ebb4f005f623987bfe3e2a3e1ffcd9e6ddd357`
- **作者**: pinchi_lin
- **日期**: 2017-06-29 10:23:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`

### 133. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `7a818bb2d22747f50a94164874aa8af5047dc8ed`
- **作者**: joseph
- **日期**: 2017-06-28 17:44:30
- **變更檔案數量**: 0

### 134. A00-20170623002 修正:取ESS維護作業權限錯誤,iterator使用錯誤導致
- **Commit ID**: `881ffa9bd53b909c0e4cf86089a1f2988b981cc0`
- **作者**: joseph
- **日期**: 2017-06-28 17:44:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 135. 調整使用者管理，移除編輯格式驗證
- **Commit ID**: `f17b4937c7d7f9ba0cf510c266191cd629221e3e`
- **作者**: ChinRong
- **日期**: 2017-06-28 16:41:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 136. A00-20170427001 將呼叫Tiptop清理附件交給Queue執行,與開單拆開
- **Commit ID**: `e19be849ab53d14f43e1107f60c6b87e60a53830`
- **作者**: joseph
- **日期**: 2017-06-28 15:39:42
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/TiptopCleanDocumentBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerLocal.java`

### 137. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `32db05d86a49db240833b6b3420d5c66cba7c21f`
- **作者**: jd
- **日期**: 2017-06-28 13:48:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 138. 修正關注欄位,改為顯示關注提示訊息
- **Commit ID**: `c40eb65dcc09819210ed6c38bcaaf165e0a934bb`
- **作者**: jd
- **日期**: 2017-06-28 13:47:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`

### 139. SqlInjection議題防範，增加「 OR 」關鍵字判斷
- **Commit ID**: `3ed59c472da6480a6dd12e848c4dee2abe6794df`
- **作者**: Gaspard
- **日期**: 2017-06-28 11:28:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 140. C01-20170607002 ESS錯誤代碼機制補充註解說明
- **Commit ID**: `7318ec57b95d16b0911b6c56e8d4afd20471c5ee`
- **作者**: joseph
- **日期**: 2017-06-28 09:46:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 141. 補上漏掉企業微信回調網址含IP時提示訊息之多語系
- **Commit ID**: `99abcb1f3be691f0b740c054322efb0f681e65d7`
- **作者**: MiYu
- **日期**: 2017-06-28 09:01:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`

### 142. 調整BPMAPP顯示流程中的流程圖，將其中的"檢視詳細流程資訊.."連結隱藏
- **Commit ID**: `f700ef0006639c3e9c19aa7a51e15cf49f25ecfb`
- **作者**: pinchi_lin
- **日期**: 2017-06-27 19:13:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp`

### 143. 移除部分註解
- **Commit ID**: `0e7d96c109281a1f08d96dd4a485677a576253dc`
- **作者**: pinchi_lin
- **日期**: 2017-06-27 14:49:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 144. 增加判斷企業微信回調網址含IP時顯示提示訊息 BPMAPP聯絡人的手機與信箱功能關閉
- **Commit ID**: `ccda730421e7530c307d68a18e73c144b1d8f5b4`
- **作者**: MiYu
- **日期**: 2017-06-27 12:49:23
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`

### 145. 調整使用者管理欄位名稱以及修正部分邏輯
- **Commit ID**: `dad41d97ae8e6cf97f3b60d59e85b0c8d6e8cf05`
- **作者**: ChinRong
- **日期**: 2017-06-27 11:34:27
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 146. 補上使用者管理頁面漏掉的地方
- **Commit ID**: `d57d6691f7e1daa6b283b4d5c65f2c519bd749ed`
- **作者**: ChinRong
- **日期**: 2017-06-26 18:11:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 147. 調整使用者管理頁籤，移除微信開關
- **Commit ID**: `8467da0d09826105711933e8c19b8b05d8329e5a`
- **作者**: ChinRong
- **日期**: 2017-06-26 17:52:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 148. 調整設定檔中BPMAPP整合平台開關
- **Commit ID**: `7ad333a17f3dc89300ac1c73a1c28f7310f2af5e`
- **作者**: pinchi_lin
- **日期**: 2017-06-26 17:22:27
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 149. 調整行動簽核中心使用者管理介面，新增企業微信部分
- **Commit ID**: `33097a04072c1fcee4bf0c703181403eaa6c7a4f`
- **作者**: ChinRong
- **日期**: 2017-06-26 16:57:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 150. 修正資料選取器與維護樣板SQL異常自動刪除orderby的條件
- **Commit ID**: `255ae3dceea5f3904c6a5e4d0e8a27c665b11ef8`
- **作者**: Gaspard
- **日期**: 2017-06-26 14:55:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 151. 行動簽核中心開窗選擇使用者新增聯絡電話
- **Commit ID**: `a20512a01e36851f01819390136ed845c73cc7ea`
- **作者**: ChinRong
- **日期**: 2017-06-26 13:50:29
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 152. 調整BPMAPP能支援整合企業微信
- **Commit ID**: `7439777556682b347c8d0292b17fdd799d5ec476`
- **作者**: pinchi_lin
- **日期**: 2017-06-23 17:29:53
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/wechat/MobileWeChatService.java`

### 153. Q00-20170623001 修正BPMAPP進入追蹤流程清單時，jboss會報一堆錯誤問題
- **Commit ID**: `e7c07ac1025bd699a83e0ca587f38a99cf639033`
- **作者**: pinchi_lin
- **日期**: 2017-06-23 15:16:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

### 154. 調整使用者管理頁面微信帳號驗證機制
- **Commit ID**: `156992adea8c0e2f55446637f850b612ade3a14d`
- **作者**: ChinRong
- **日期**: 2017-06-22 17:22:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`

### 155. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f54d63660ff5b0678359a3717722911d9b7ae3be`
- **作者**: wayne
- **日期**: 2017-06-22 16:13:44
- **變更檔案數量**: 0

### 156. 調整5632update SQL
- **Commit ID**: `958ed0ca1a48db02bf1ceec910de6370bb5e45e0`
- **作者**: wayne
- **日期**: 2017-06-22 16:13:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_SQLServer.sql`

### 157. C01-20170531001 增加卡控ISO文件編碼 編碼規則中的文件階層及文件型態不能為空
- **Commit ID**: `edeb97c3fe6c7b80988cf7fd1a53083f6d912ca7`
- **作者**: joseph
- **日期**: 2017-06-22 13:50:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/snGenRule.js`

### 158. ESSTAG增加註寫說明:ESS維護權限、附件參考的Tag
- **Commit ID**: `dee49ee820e865bd6a8eeee05870279b18edc919`
- **作者**: joseph
- **日期**: 2017-06-21 18:17:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 159. C01-20170619002  修正:流程簽核時附件反灰無法點選問題
- **Commit ID**: `7bd6cc161bd09765df583a421229482bd28f1ca0`
- **作者**: joseph
- **日期**: 2017-06-21 18:11:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`

### 160. 調整關註欄位維護作業及新增SQL連結及多語系
- **Commit ID**: `4bb169c48117ee9e8240c189f3dc76fc2abcdf6b`
- **作者**: wayne
- **日期**: 2017-06-21 18:05:34
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_SQLServer.sql`

### 161. 修正BPMAPP多選客製開窗問題(由V5找出的BUG上修至V6，議題單號:C01-20170601001)
- **Commit ID**: `7815e1b0504cf551dbeb6edde7c7293af4949b8d`
- **作者**: pinchi_lin
- **日期**: 2017-06-20 14:52:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js`

### 162. 移除行動版Grid調整中測試用的log
- **Commit ID**: `1b31e86c668c29892ed7bea9a4736c92189e0e02`
- **作者**: ChinRong
- **日期**: 2017-06-20 10:51:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`

### 163. 調整行動版Grid綁定機制
- **Commit ID**: `188d981c5089b0aee6509c000b22c79332b9aeb3`
- **作者**: ChinRong
- **日期**: 2017-06-20 09:43:32
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppGridTemplate.txt`

### 164. Q00-20170616001 修正BPMAPP絕對位置與相對位置，若附件有設定權限，表單開不起來問題與附件列表沒有顯示權限設定資訊問題
- **Commit ID**: `5905dff301489ea7892963d5d45a87eaecf29997`
- **作者**: pinchi_lin
- **日期**: 2017-06-16 14:55:26
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`

### 165. 5.6.3.1_updateSQL_Oracle 修正
- **Commit ID**: `e21a9769113f61f9a311f5d929dc038b41224fde`
- **作者**: joseph
- **日期**: 2017-06-16 11:54:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.1_updateSQL_Oracle.sql`

### 166. 新增關注欄位維護作業模組
- **Commit ID**: `90fb8784e5f51e64ada2fbf2c225498540fa0d99`
- **作者**: wayne
- **日期**: 2017-06-15 14:35:05
- **變更檔案數量**: 42
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CannotAccessWarnning.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/UserInfo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/BpmTable.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/QueryDesinger.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/bootstrap/bootstrap-3.3.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/AccessRight.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/BpmTable.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/CustomDataChooser.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/ModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/OpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/QueryTemplate.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrap-3.3.4.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/jquery-1.11.3.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/jqueryUI/jquery-ui-1.11.4.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/jqueryUI/jquery.ui-contextmenu.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/js/json2.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_en_US.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_zh_CN.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_zh_TW.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/NewTiptop/NewTiptop.js`

### 167. 增加json開窗功能
- **Commit ID**: `1ad311111c3ad170bb7d7499d28db9de6ccd2697`
- **作者**: wayne
- **日期**: 2017-06-15 14:24:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml`

### 168. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `bb4d78b5c52d3169951b72a9ed69209b893cefe5`
- **作者**: wayne
- **日期**: 2017-06-15 14:24:24
- **變更檔案數量**: 0

### 169. 增加json客製開窗功能
- **Commit ID**: `3c7e29c236d1f3e737014f85479a6358368ebdd5`
- **作者**: wayne
- **日期**: 2017-06-15 14:23:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 170. 修正部分LOG內容
- **Commit ID**: `9536e5c157a401a0ba12e815ff8012fe06c2d155`
- **作者**: pinchi_lin
- **日期**: 2017-06-14 16:17:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java`

### 171. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `cb293b21efe5cc3d04a50ce02c661ee3377e1a20`
- **作者**: wayne
- **日期**: 2017-06-14 14:34:29
- **變更檔案數量**: 0

### 172. C01-20170609001 修正同個workitem有多筆轉派紀錄時，批次簽核執行會發生錯誤
- **Commit ID**: `022ce98bc145575d68ea44a23ea2aeedf5c344b1`
- **作者**: wayne
- **日期**: 2017-06-14 14:32:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 173. 補上漏修BPMAPP唯讀grid樣式調整 修正鼎慧行事曆指定日期的icon 修正驗證鼎慧平台連線與文字顏色與文字不見問題
- **Commit ID**: `abbaf7344534b2f24bdf2ee48f10748335431630`
- **作者**: MiYu
- **日期**: 2017-06-14 12:57:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 174. 修正相對位置設計器欄位模板、無元件顯示名稱
- **Commit ID**: `6919460dbf2068e09b9ac5636b528a9793abf326`
- **作者**: ChinRong
- **日期**: 2017-06-13 15:56:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 175. A00-20170414001 修正動態加簽關卡導致流程圖顯示異常
- **Commit ID**: `67ed8c301ebf2796e06072be25987ebbd1b2c6b7`
- **作者**: wayne
- **日期**: 2017-06-12 18:11:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 176. 修正行動版表單RadioButton,CheckBox事件消失問題
- **Commit ID**: `d65d8bb0fc16497d9e75786cc40e3a957866342d`
- **作者**: ChinRong
- **日期**: 2017-06-09 10:22:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`

### 177. 修改LOG內容錯誤
- **Commit ID**: `32d4c059996c9584846d750e7a61136c4c285ea3`
- **作者**: pinchi_lin
- **日期**: 2017-06-08 18:09:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`

### 178. C01-20170320001 因表單定義中欄位缺少datatype,導致發起流程及追蹤流程會有錯誤 ,因此判斷datatyep為空時,預設為String型態
- **Commit ID**: `8693937e6b579b6b673993095e598e2744b3fbd4`
- **作者**: joseph
- **日期**: 2017-06-08 17:28:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TextElementDefinition.java`

### 179. 修正行動簽核中心IE破版問題
- **Commit ID**: `4b048f7fd5794159a53e94263201c00e2bb433b6`
- **作者**: ChinRong
- **日期**: 2017-06-08 17:11:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 180. BPMAPP的消息推播功能將全部推送改成依流程設計師中有開啟支援行動簽核才進行推送
- **Commit ID**: `546ac68d7bbedcee7b0f1db9108c4c8fbf52c1ab`
- **作者**: pinchi_lin
- **日期**: 2017-06-08 14:30:37
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java`

### 181. 修正BPMAPP使用連結進入表單畫面後，該筆已結案或終止時會出現工作取回失敗訊息的問題
- **Commit ID**: `575cdfe1ff34ae200dfdfb8ab3c94ef38b5782fe`
- **作者**: MiYu
- **日期**: 2017-06-07 13:45:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 182. 調整BPM App提示訊息
- **Commit ID**: `b59424000b9bad4b6f5d21144e24ec125335c18d`
- **作者**: ChinRong
- **日期**: 2017-06-06 13:57:43
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTrace.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 183. 調整BPM App外部網址URL
- **Commit ID**: `ce85ceb48cc8f249607cf271245ae21439cf9102`
- **作者**: ChinRong
- **日期**: 2017-06-06 13:55:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`

### 184. 調整BPMAPP表單元件唯讀樣式 調整關注資訊的UI
- **Commit ID**: `d60927a051775fb0294da579952204ef7649697e`
- **作者**: MiYu
- **日期**: 2017-06-06 13:41:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 185. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b9847120fb787b3a68e52d41bfa5ef043b64fcd1`
- **作者**: joseph
- **日期**: 2017-06-05 17:25:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 186. Q00-20170602002 修正:從Email開啟直接簽核的連結後，按下派送後都失敗
- **Commit ID**: `90f5cdf22eb83b0effb7e416b92727a50fda12a1`
- **作者**: joseph
- **日期**: 2017-06-05 17:16:38
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmCompleteMailStraightSignOff.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmMailStraightSignOffPhrase.jsp`

### 187. Q00-*********** 修正進入微信應用後的被動響應消息失效問題(含使用被動響應消息的查詢發單、查詢人員)
- **Commit ID**: `7f4a4e1a20266d58b4480614555870d0e94295d9`
- **作者**: pinchi_lin
- **日期**: 2017-06-03 16:19:57
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 188. 調整關注欄位維護作業
- **Commit ID**: `4dcd37473f749b06f46c9590a392bfd952f60fa1`
- **作者**: wayne
- **日期**: 2017-06-03 14:24:36
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/critical/CriticalManagerMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalPriorityDao.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalPriorityDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/critical/CriticalManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 189. A00-20170414001 修正動態加簽關卡流程圖顯示異常
- **Commit ID**: `0dd1105d503a9a4f96d4a78f7e8d2f557e0acc0f`
- **作者**: wayne
- **日期**: 2017-06-02 15:28:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 190. 調整關注欄位hibernate XML
- **Commit ID**: `65bbae600ac11b0c7d70771203bb49857901d2f3`
- **作者**: wayne
- **日期**: 2017-06-02 10:08:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessDefinition.hbm.xml`

### 191. 修正工作通知問題
- **Commit ID**: `49a5c81a16445755f525a7c5859e395b585d6d2e`
- **作者**: ChinRong
- **日期**: 2017-06-01 16:44:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`

### 192. 新增關注欄位維護作業Hibernate
- **Commit ID**: `17c42ac95ccb43853baa5da44e4885123c58b880`
- **作者**: wayne
- **日期**: 2017-06-01 14:59:25
- **變更檔案數量**: 34
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/CriticalManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalConditionDefinition.hbm.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalConditionDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalDefinition.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalFocusProcess.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalFocusProcess.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalPriority.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalPriority.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessDefinition.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessHintFields.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessHintFields.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/hibernate/hibernate.cfg.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/hibernate/spring-bo.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/hibernate/spring-dao.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/BizServiceFacade.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/critical/CriticalManagerMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalDefinitionDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalFocusProcessDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalPriorityDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalProcessDefinitionDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalDefinitionDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalFocusProcessDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalPriorityDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalProcessDefinitionDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/critical/CriticalManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/critical/CriticalManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/CriticalProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalProcessDefinitionViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 193. 將異常流程單據調整為全部撈取
- **Commit ID**: `3e522cffd8851817f168c3c89c5720f76f828bbc`
- **作者**: wayne
- **日期**: 2017-06-01 14:24:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 194. A00-20170414001 修正動態加簽關卡流程圖顯示異常
- **Commit ID**: `9798a9f5ac4ecf7e7446509ea9f35a5cb3732e49`
- **作者**: wayne
- **日期**: 2017-06-01 14:09:28
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 195. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `8385a3f7b652d2b3e1c89377dec87461511ba3cf`
- **作者**: jd
- **日期**: 2017-05-31 16:23:59
- **變更檔案數量**: 0

### 196. A00-20170525001 修正 :流程關卡中的允許輸入密碼已勾選，但在簽核時並沒有跳出需輸入密碼的訊息
- **Commit ID**: `bd4558caedfd9d5e6be50a50093eb6f63319cb32`
- **作者**: joseph
- **日期**: 2017-05-31 16:23:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 197. 修正鼎慧异常问题
- **Commit ID**: `d76cbbb5f6f34705423062247fe31719a279f675`
- **作者**: jd
- **日期**: 2017-05-31 16:22:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`

### 198. 調整關注資訊顯示內容 調整行事曆前端傳的資料內容
- **Commit ID**: `b3508aae33ff6311a410b99797d1e6853628f4c4`
- **作者**: MiYu
- **日期**: 2017-05-26 16:43:57
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/infoicon.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 199. 修正統計組件樣式問題
- **Commit ID**: `49182ab562bd16e2fe5405073a391c4bda006870`
- **作者**: jd
- **日期**: 2017-05-26 16:42:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`

