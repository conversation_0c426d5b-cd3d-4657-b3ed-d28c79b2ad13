# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_*******_20221024
- **舊版本**: release_*******
- **生成時間**: 2025-07-18 11:16:05
- **新增 Commit 數量**: 194

## 變更摘要

### lorenchang (4 commits)

- **2022-06-26 20:48:05**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
  - 變更檔案: 25 個
- **2022-07-06 17:40:48**: [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
  - 變更檔案: 21 個
- **2022-07-06 17:40:48**: [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
  - 變更檔案: 21 個
- **2022-07-04 08:58:18**: [內部]Log調整
  - 變更檔案: 2 個

### 林致帆 (27 commits)

- **2022-08-17 11:58:58**: [Portal]]Q00-20220817001調整有整合Portal，用查看流程圖的外部portlet，導入的畫面不是BPM而是Portal的登入頁面
  - 變更檔案: 1 個
- **2022-09-27 11:50:22**: [ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核
  - 變更檔案: 1 個
- **2022-09-05 17:48:01**: [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
  - 變更檔案: 4 個
- **2022-08-30 08:34:29**: [WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能
  - 變更檔案: 1 個
- **2022-08-22 14:11:47**: [Web]Q00-20220822001 修正BPM使用IE瀏覽器上傳附件時會失敗
  - 變更檔案: 1 個
- **2022-08-19 17:57:37**: [TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久
  - 變更檔案: 1 個
- **2022-07-29 14:34:14**: [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]
  - 變更檔案: 1 個
- **2022-08-02 11:27:26**: [Web]A00-20220802001 修正無法開啟SAP維護作業
  - 變更檔案: 1 個
- **2022-07-29 14:02:17**: [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]
  - 變更檔案: 1 個
- **2022-07-28 15:20:57**: [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
  - 變更檔案: 3 個
- **2022-07-28 15:20:57**: [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
  - 變更檔案: 3 個
- **2022-07-27 10:41:09**: [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法
  - 變更檔案: 1 個
- **2022-06-29 11:34:54**: [資安]Q00-20220629001 修正/NaNaWeb/webservice/servlet/AxisServlet要加入為WebService白名單控管範圍
  - 變更檔案: 1 個
- **2022-06-23 14:10:20**: [Web]A00-20220622002 修正流程新增關卡頁面輸入簽核意見在新增向前or向後關卡按下確定後，簽核意見內容被清除
  - 變更檔案: 1 個
- **2022-06-21 13:42:26**: [Web]Q00-20220621003修正發起流程-查詢流程清單搜尋純數字的流程名稱會報錯
  - 變更檔案: 1 個
- **2022-06-07 14:20:54**: [Web]Q00-20220607002 修正首頁待辦清單第三頁以上的流程進行派送時會報錯
  - 變更檔案: 1 個
- **2022-06-14 11:52:54**: [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯[補修正]
  - 變更檔案: 1 個
- **2022-06-10 11:42:26**: [內部]Q00-20220610001修正WorkFlow拋單log會顯示[Fatal Error] :1:1的錯誤訊息
  - 變更檔案: 1 個
- **2022-06-06 14:26:44**: [Web]Q00-20220606001 修正第二關之後的關卡預解析，流程線的條件式採用表單欄位時，預解析的關卡與派送的關卡不符合
  - 變更檔案: 1 個
- **2022-05-25 16:48:50**: [Web]Q00-20220525004 修正輸入單身資料有&#加任意數字，被轉成特殊符號，會與輸入資料不符
  - 變更檔案: 2 個
- **2022-05-25 16:32:13**: [TIPTOP]Q00-20220525003 修正拋單的單身資料有中刮號會被轉成小括號，導致資料與TIPTOP不符合
  - 變更檔案: 1 個
- **2022-05-18 10:39:03**: [Web]Q00-20220518001 修正退件表單資訊與開啟的表單關連錯誤
  - 變更檔案: 1 個
- **2022-05-05 11:00:24**: [Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢
  - 變更檔案: 1 個
- **2022-06-16 13:59:23**: [Web]Q00-20220616001 調整待辦通知信中簽核歷程的處理者欄位移除粗體樣式
  - 變更檔案: 1 個
- **2022-05-19 17:04:27**: [Web]A00-20220517004 修正調離職人員帳號更新排程預設出貨端口改成8086
  - 變更檔案: 1 個
- **2022-05-09 08:55:56**: [內部]Q00-20220509001修正設定檔ESS內網IP敘述的Oracle指令有誤
  - 變更檔案: 1 個
- **2022-05-13 17:23:56**: [流程引擎]A00-20220513001 修正ProcessMapping在Oracle遺漏attachInfo欄位導致拋單失敗
  - 變更檔案: 1 個

### cherryliao (9 commits)

- **2022-10-06 11:29:53**: [流程設計師]Q00-20221006001 調整在流程設計點擊編輯表單欄位權限時，若表單發行狀態已過期或UNDER_REVISION時會彈提示訊息
  - 變更檔案: 5 個
- **2022-09-16 13:48:45**: [Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題
  - 變更檔案: 1 個
- **2022-09-08 14:06:21**: [Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息
  - 變更檔案: 2 個
- **2022-08-10 11:01:13**: [Web]A00-20220808001 調整報表查詢產出的日期與匯出Excel的日期不一致問題
  - 變更檔案: 1 個
- **2022-07-27 17:51:33**: [Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題
  - 變更檔案: 1 個
- **2022-07-28 14:39:59**: [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]
  - 變更檔案: 1 個
- **2022-07-20 11:38:23**: [Web]A00-20220718001 修正Gird元件在某關卡隱藏時開啟表單會出現該物件沒有定義的問題
  - 變更檔案: 2 個
- **2022-07-19 18:01:01**: [登入]Q00-20220719002 修正DB為Oracle時，使用者登出登入紀錄作業中使用操作時間為查詢條件會查不到結果的問題
  - 變更檔案: 1 個
- **2022-07-12 11:15:32**: [BPM APP]Q00-20220711001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟移動端表單會報錯的問題
  - 變更檔案: 1 個

### wencheng1208 (14 commits)

- **2022-09-30 10:36:45**: [Web]Q00-20220930001 執行iReport套件當發生Exception錯誤時，增加列印異常的堆疊資訊
  - 變更檔案: 1 個
- **2022-09-21 17:49:58**: [Web]Q00-20220921002 調整「必須上傳新附件」邏輯，只要存在一筆以上的附件並且符合在該「關卡名稱」上傳的附件，即可通過該驗證
  - 變更檔案: 1 個
- **2022-09-01 15:53:47**: [Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式
  - 變更檔案: 1 個
- **2022-09-12 17:59:01**: [流程引擎]Q00-20220912004 修正findProcessPackageById方法內容為取得流程包裹最新一版，以避免後續同仁遇到此坑
  - 變更檔案: 1 個
- **2022-09-12 17:39:32**: [組織同步]QQ00-20220912003 組織同步功能執行人員資料修改時，可保留人員姓名多語系關聯
  - 變更檔案: 1 個
- **2022-09-08 15:12:59**: [Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本
  - 變更檔案: 1 個
- **2022-09-14 10:34:55**: [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
  - 變更檔案: 1 個
- **2022-08-24 11:48:03**: [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
  - 變更檔案: 1 個
- **2022-08-23 14:44:52**: [流程引擎]Q00-20220823001 修正使用客製的方式執行動態加簽後，無法呈現詳細流程圖畫面的問題
  - 變更檔案: 1 個
- **2022-08-05 12:00:16**: [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]
  - 變更檔案: 1 個
- **2022-08-03 16:48:09**: [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。
  - 變更檔案: 1 個
- **2022-07-25 10:55:23**: [Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法
  - 變更檔案: 1 個
- **2022-07-29 16:30:05**: [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。
  - 變更檔案: 2 個
- **2022-07-27 12:07:05**: [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。
  - 變更檔案: 1 個

### 謝閔皓 (12 commits)

- **2022-09-07 17:08:34**: [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題[補修正]
  - 變更檔案: 1 個
- **2022-09-07 14:59:37**: [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題
  - 變更檔案: 1 個
- **2022-10-15 11:47:52**: [TIPTOP]Q00-20221014007修正客戶從TIPTOP端udm_tree操作原稿匣撤銷流程時，選擇特定流程後，BPM仍會回傳所有可撤銷流程的清單
  - 變更檔案: 1 個
- **2022-09-30 15:47:30**: [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]
  - 變更檔案: 1 個
- **2022-09-30 12:24:25**: [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員
  - 變更檔案: 1 個
- **2022-08-08 14:57:44**: [Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題
  - 變更檔案: 1 個
- **2022-08-04 22:21:33**: [Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容
  - 變更檔案: 2 個
- **2022-08-23 15:27:14**: [流程引擎]S00-20220722001新增批次通知信件主旨內容
  - 變更檔案: 1 個
- **2022-08-08 17:43:54**: [Web]Q00-20220808003修正使用產品表單中的Date元件，並搭配TextBox元件的進階功能，資料型態整數中的時間區間運算，當遇到元件ID有使用下底線時，會導致TextBox元件無法正常運算
  - 變更檔案: 1 個
- **2022-08-11 12:56:57**: [Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題
  - 變更檔案: 1 個
- **2022-08-10 18:34:57**: [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
  - 變更檔案: 1 個
- **2022-08-01 16:26:26**: [Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料
  - 變更檔案: 1 個

### kmin (34 commits)

- **2022-10-19 11:27:34**: [WEB]Q00-20221013002:修正表單欄位有設定 "唯讀"時的欄位顏色，顯示卻都為背景顏色。
  - 變更檔案: 5 個
- **2022-10-05 15:13:19**: 移除[ESS]S00-20211208003新增ESS外網主機IP設定
  - 變更檔案: 3 個
- **2022-10-05 15:04:14**: Revert "[ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)"
  - 變更檔案: 21 個
- **2022-10-03 11:36:39**: Revert "[Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦"
  - 變更檔案: 1 個
- **2022-09-19 13:54:05**: [Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部 原單號: C01-20220527001 個人資訊>流程相關設定>工作事項顯示設定 ，當點選關注流程應該進入待辦中的關鍵流程、點選重要流程應該進入待辦的重要流程
  - 變更檔案: 1 個
- **2022-09-15 11:22:47**: [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
  - 變更檔案: 1 個
- **2022-09-15 10:33:07**: Revert "[流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯"
  - 變更檔案: 1 個
- **2022-09-15 09:47:02**: [Web]S00-20220810001簽核意見是否顯示管理員
  - 變更檔案: 3 個
- **2022-09-15 09:19:15**: [流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤
  - 變更檔案: 1 個
- **2022-09-15 09:16:41**: [流程引擎]Q00-20220627001 優化核決層級關卡解析人員緩慢問題
  - 變更檔案: 1 個
- **2022-09-06 15:55:17**: [流程引擎]Q00-20220823003 讓亂數產生的ID增加動態加簽CustomDecisionRule的開頭關鍵字，前面流程圖解析邏輯段也要新增
  - 變更檔案: 2 個
- **2022-09-06 15:52:12**: [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
  - 變更檔案: 1 個
- **2022-08-29 10:11:28**: Revert "[流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容"
  - 變更檔案: 1 個
- **2022-07-27 14:37:52**: [內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來
  - 變更檔案: 1 個
- **2022-08-10 08:56:52**: Revert "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。"
  - 變更檔案: 2 個
- **2022-08-03 16:04:39**: [Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失 RWD不會發生的原因是就算前端直是空的傳進來也是[] 移動防呆位置
  - 變更檔案: 1 個
- **2022-08-03 15:53:36**: [流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料
  - 變更檔案: 2 個
- **2022-08-03 15:38:59**: Revert "[流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料"
  - 變更檔案: 2 個
- **2022-08-01 16:38:12**: Revert "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock"
  - 變更檔案: 3 個
- **2022-08-01 13:51:55**: Revert "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況"
  - 變更檔案: 3 個
- **2022-08-01 13:50:19**: Revert "[MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]"
  - 變更檔案: 3 個
- **2022-07-25 17:12:47**: Revert "[Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部"
  - 變更檔案: 1 個
- **2022-07-18 10:06:16**: [Web]Q00-20220714003 修正Dialog元件的txt屬性如果被FormScript或其他非預期方式刪除，在產生表單畫面時報錯 如果屬性被改成null或是遺失將其防呆為空字串 內部測試將元件隱藏一關或是連續兩關以上都無法重現，應該是客戶的Script有改到元件內容
  - 變更檔案: 1 個
- **2022-07-15 08:37:59**: [Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法 語法: FormUtil.setScrollBarHeight("0");
  - 變更檔案: 1 個
- **2022-07-14 17:29:49**: [流程引擎]Q00-20220622001 修正當RWD表單的RadioButton元件與CheckBox元件選項內容太長時會斷行 相關議題單：C01-20220613009。
  - 變更檔案: 1 個
- **2022-07-14 17:27:26**: [Web]Q00-20220621001 修正非CheckBox或RadioButton的選擇元件執行到額外輸入框邏輯導致出現非預期異常
  - 變更檔案: 1 個
- **2022-07-14 16:39:42**: [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼 1.移動端原本就會把中文字轉換成Unicode字符代碼作呈現,如果再轉一次就會把&轉換掉 2.關聯紀錄:A00-20220511001
  - 變更檔案: 1 個
- **2022-07-14 16:37:51**: Revert "[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼"
  - 變更檔案: 1 個
- **2022-07-14 16:34:56**: [流程引擎]Q00-20220707002 修正表單日期元件預設值計算錯誤 相關議題單：C01-20220701004。
  - 變更檔案: 1 個
- **2022-07-14 16:28:34**: [Web]A00-20220628001 修正已簽核過的關卡，從Mail的待辦連結進入該表單時可以移除附件
  - 變更檔案: 1 個
- **2022-07-14 16:20:49**: [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
  - 變更檔案: 1 個
- **2022-07-14 16:19:46**: Revert "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開"
  - 變更檔案: 1 個
- **2022-07-14 16:14:21**: [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
  - 變更檔案: 1 個
- **2022-07-14 16:13:16**: Revert "[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘"
  - 變更檔案: 1 個

### waynechang (7 commits)

- **2022-10-14 14:29:50**: [流程設計師]A00-20221012001 修正流程設計師當子流程有變更代號時，流程簽入新版時，資料庫的子流程代號未更新
  - 變更檔案: 1 個
- **2022-10-03 13:59:51**: [流程引擎]Q00-20221003002 流程預先解析支持流程設計關卡型態為「活動簽核人」的活動
  - 變更檔案: 1 個
- **2022-10-03 10:27:09**: [流程設計師]Q00-20221003001 調整簽核流設計師，將流程設計師原有的「活動定義/選擇參與者/活動簽核人」重新加回簽核流設計師中
  - 變更檔案: 5 個
- **2022-08-25 14:52:12**: [流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡
  - 變更檔案: 1 個
- **2022-09-20 11:19:53**: [Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊
  - 變更檔案: 1 個
- **2022-07-21 15:36:43**: [流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送
  - 變更檔案: 1 個
- **2022-06-22 15:47:30**: [Web]Q00-20220523001 修正同瀏覽器有二次登入時，登入頁「記住我」的功能會失效
  - 變更檔案: 1 個

### raven.917 (4 commits)

- **2022-10-06 14:35:32**: [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。
  - 變更檔案: 2 個
- **2022-10-06 08:57:33**: [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用(補修正，增加可讀性)
  - 變更檔案: 1 個
- **2022-10-04 15:26:58**: [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用
  - 變更檔案: 1 個
- **2022-09-21 16:42:30**: [WEB] C01-20220919007 Admin 需要能開啟設計師時，直接針對該表單做復原簽出的操作行為。
  - 變更檔案: 2 個

### yanann_chen (29 commits)

- **2020-08-05 10:44:55**: [Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦
  - 變更檔案: 1 個
- **2022-07-01 15:05:36**: [流程引擎]Q00-20220629004 修正流程定義設定「流程撤銷時逐級通知」，在使用者撤銷流程後，只有撤銷流程當下進行中的關卡的處理者在系統通知清單內有該筆通知資料
  - 變更檔案: 3 個
- **2022-06-29 14:31:43**: [流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料
  - 變更檔案: 2 個
- **2022-07-14 18:28:53**: [Web]Q00-20220714004 修正使用safari瀏覽器時，點選在線閱讀附件沒有反應
  - 變更檔案: 2 個
- **2022-07-06 17:43:27**: [流程引擎]Q00-20220706005 修正資料庫為MSSQL，且流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
  - 變更檔案: 1 個
- **2022-07-13 16:21:42**: [Web]Q00-20220713004 修正移動消息訂閱管理頁面無法開啟
  - 變更檔案: 1 個
- **2022-07-11 17:38:00**: [流程引擎]Q00-20220711003 修正在表單上將Excel匯入單身後，開窗畫面變成空白
  - 變更檔案: 1 個
- **2022-06-20 14:09:46**: [Web]A00-20220616001 退回重辦頁面增加退回重辦方式的說明
  - 變更檔案: 1 個
- **2022-06-28 17:17:20**: [流程引擎]Q00-20220628001 調整流程關係人與關係部門解析邏輯，若流程中沒有變更流程關係人或關係部門資料，系統在流程往下派送時就不會再解析流程關係人與關係部門
  - 變更檔案: 1 個
- **2022-07-07 16:10:43**: [流程引擎]Q00-20220707003 修正DialogInput元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容
  - 變更檔案: 1 個
- **2022-06-09 17:18:03**: [Web]A00-20220608003 修正進入追蹤流程畫面時未清除「撤銷理由」欄位內容
  - 變更檔案: 1 個
- **2022-06-09 16:32:41**: [流程引擎]Q00-20220609003 修正使用者操作個人預設代理人設定時，代理人可能有多筆相同人員的問題
  - 變更檔案: 1 個
- **2022-05-25 17:01:11**: [表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出
  - 變更檔案: 3 個
- **2022-05-25 14:01:03**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
  - 變更檔案: 1 個
- **2022-05-25 10:57:21**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
  - 變更檔案: 1 個
- **2022-05-24 17:45:41**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤
  - 變更檔案: 1 個
- **2022-05-20 15:42:50**: [流程引擎]Q00-20220520003 修正流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
  - 變更檔案: 1 個
- **2022-05-12 18:19:18**: [流程引擎]A00-20220511001 修正使用者輸入到表單TextArea的內容在儲存表單後變成亂碼的問題
  - 變更檔案: 1 個
- **2022-05-12 14:36:06**: [流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差
  - 變更檔案: 1 個
- **2022-05-12 14:12:31**: [Web]Q00-20220411005 修正使用者在絕對位置表單進行簽核時遭遇產品程式錯誤
  - 變更檔案: 1 個
- **2022-05-12 11:47:55**: [內部]Q00-20220512001 bootstrap-table-1.18.3.js更換檔案名稱
  - 變更檔案: 4 個
- **2022-05-11 15:03:45**: [Web]Q00-20220511005 修正T100拋轉單據中有舊值的單身內容沒有顯示為紅色
  - 變更檔案: 1 個
- **2022-05-11 12:07:20**: [流程引擎]Q00-20220506001 調整使用者「授權的流程」，流程清單筆數改為設定檔設定
  - 變更檔案: 1 個
- **2022-05-11 11:23:52**: [流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料
  - 變更檔案: 1 個
- **2022-05-11 11:10:44**: [流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知
  - 變更檔案: 1 個
- **2022-05-11 10:54:24**: [Web]Q00-20220511001 調整列印表單畫面簽核歷程，移除「資料代號」、「通知者」欄位
  - 變更檔案: 1 個
- **2022-06-20 16:44:21**: [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-04 14:02:42**: [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題
  - 變更檔案: 1 個
- **2022-05-20 14:08:18**: [流程引擎]A00-20220519003 修正終止前置流程時，後置流程完成流程撤銷後，前置流程出現「派送失敗」的錯誤訊息，無法正常終止
  - 變更檔案: 2 個

### 王鵬程 (12 commits)

- **2022-07-29 16:49:53**: [Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示
  - 變更檔案: 1 個
- **2022-07-28 17:32:17**: [Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行
  - 變更檔案: 1 個
- **2022-07-26 18:16:29**: [Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入
  - 變更檔案: 1 個
- **2022-07-20 15:26:09**: [Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部
  - 變更檔案: 1 個
- **2022-07-13 16:33:02**: [Web]Q00-20220713003 修正在行動版面中，在表單內向下滑動時，右下角的浮動按鈕會隱藏而無法後續操作
  - 變更檔案: 1 個
- **2022-07-11 15:22:15**: [表單設計師]Q00-20220711002 修正絕對表單元件不存在某些屬性而取用該屬性導致無法開啟表單，增加防呆
  - 變更檔案: 1 個
- **2022-06-21 16:55:38**: [Web]A00-20220616003 修正SerialNumber元件的字體設25px以上，在流程中該元件的顯示會有部分被遮蔽到
  - 變更檔案: 1 個
- **2022-06-09 11:50:53**: [Web]Q00-20220609001 修正行動裝置在加簽關卡選擇參與者人員後，選擇參與者的下方欄位會顯示已選取0個項目
  - 變更檔案: 1 個
- **2022-06-13 16:14:20**: [Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾
  - 變更檔案: 1 個
- **2022-05-05 17:52:51**: [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果[補]
  - 變更檔案: 1 個
- **2022-05-05 15:24:43**: [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果
  - 變更檔案: 1 個
- **2022-04-27 16:50:57**: [Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色
  - 變更檔案: 1 個

### yamiyeh10 (9 commits)

- **2022-08-09 13:51:45**: [組織同步]Q00-20220809002 修正組織同步log出現Error時改寄送失敗通知信
  - 變更檔案: 1 個
- **2022-07-25 10:22:50**: [Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題
  - 變更檔案: 1 個
- **2022-07-20 18:23:57**: [Web]Q00-20220720002 修正列印模式下附件與簽核歷程的右邊邊線會不見問題
  - 變更檔案: 1 個
- **2022-07-08 11:01:58**: [表單設計師]C01-20220706003 修正當變更表單的對齊方式並儲存後會將已設計過的行動版表單設計欄位清空問題
  - 變更檔案: 1 個
- **2022-05-26 10:06:49**: [BPM APP]C01-20220524002 修正改派通知設定整張表單時Line推播內容不會呈現表單訊息格式的問題
  - 變更檔案: 1 個
- **2022-05-10 10:49:37**: [BPM APP]C01-20220509009 修正行動端在Grid元件存在沒有繫結元件情況下點擊編輯按鈕會失敗問題
  - 變更檔案: 1 個
- **2022-05-26 09:50:36**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-16 15:15:57**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-10 18:03:59**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題
  - 變更檔案: 1 個

### walter_wu (23 commits)

- **2022-07-29 14:20:21**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]
  - 變更檔案: 2 個
- **2022-07-29 00:04:37**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
  - 變更檔案: 3 個
- **2022-07-29 00:04:37**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
  - 變更檔案: 3 個
- **2022-07-14 11:32:09**: [流程引擎]Q00-20220713005 修正核決關卡設定自動簽核，取/退回後再次簽核進核決層級時會報錯無法派送
  - 變更檔案: 1 個
- **2022-06-28 18:06:01**: [Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久
  - 變更檔案: 1 個
- **2022-07-01 11:54:21**: [Web]Q00-20220701001 調整時間元件如果輸入不是數字直接換成00
  - 變更檔案: 1 個
- **2022-06-08 09:21:56**: [報表設計器]Q00-20220607003 修正欄位字串如果含as會辨識錯欄位名稱
  - 變更檔案: 1 個
- **2022-07-08 18:24:42**: [表單設計師]A00-20220704001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟表單會報錯
  - 變更檔案: 2 個
- **2022-06-13 16:07:01**: [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯
  - 變更檔案: 1 個
- **2022-06-09 15:28:14**: [內部]Q00-20220609002 調整DWR設定讓Log不要一直出現轉換ProcessInstanceStateType的錯誤
  - 變更檔案: 1 個
- **2022-06-10 18:10:24**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
  - 變更檔案: 1 個
- **2022-06-08 15:56:57**: [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
  - 變更檔案: 1 個
- **2022-06-08 11:49:23**: [WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯
  - 變更檔案: 1 個
- **2022-06-01 13:57:59**: [Web]A00-20220526001 修正如果DB是Oralce在線閱讀浮水印管理出現無法取得EJB所提供的服務
  - 變更檔案: 1 個
- **2022-05-30 15:34:30**: [內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方
  - 變更檔案: 1 個
- **2022-05-27 17:34:09**: [Web]Q00-20220527003 修正使用者使用監控流程的最大筆數沒有根據process.default.show.records的設定
  - 變更檔案: 1 個
- **2022-05-27 17:15:41**: [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
  - 變更檔案: 1 個
- **2022-05-25 18:07:49**: [Web]A00-20220519001 修正IE加簽會加成兩次的問題
  - 變更檔案: 1 個
- **2022-05-24 19:04:27**: [系統管理工具]C01-20220524002 修正進階功能>檢查密碼如果User裡有關聯有異常的會全部撈不出來
  - 變更檔案: 1 個
- **2022-05-23 18:19:20**: [內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)
  - 變更檔案: 1 個
- **2022-05-11 14:44:02**: [內部]Q00-20220511003 調整BCL8轉檔Timeout從預設2分鐘拉長到5分鐘
  - 變更檔案: 1 個
- **2022-05-09 16:41:13**: [Web]Q00-20220509002 修正授權的流程使用closeTime(結案時間)排序會報錯的問題
  - 變更檔案: 1 個
- **2022-04-27 15:00:41**: [內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤
  - 變更檔案: 1 個

### pinchi_lin (1 commits)

- **2022-07-12 19:29:30**: [MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]
  - 變更檔案: 3 個

### 郭哲榮 (6 commits)

- **2022-07-12 20:01:24**: [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼
  - 變更檔案: 1 個
- **2022-05-27 19:22:55**: [BPM APP]C01-20220523004 修正移動端subTab元件使用formScript在單獨顯示時會顯示其他頁籤內容的問題
  - 變更檔案: 1 個
- **2022-05-18 18:47:47**: [BPM APP]C01-20220516002 修正行動端FormUtil.disable為true時Dropdown元件顯示異常問題
  - 變更檔案: 1 個
- **2022-05-17 15:12:44**: [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題[補]
  - 變更檔案: 1 個
- **2022-05-16 15:10:33**: [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題
  - 變更檔案: 1 個
- **2022-04-28 12:23:28**: [BPM APP]C01-20220419001 修正移動端選項元件使用動態塞值且隱藏標籤會顯示異常問題
  - 變更檔案: 1 個

### wayne (3 commits)

- **2022-05-12 16:55:22**: [Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」
  - 變更檔案: 2 個
- **2022-05-10 15:59:21**: [Web]Q00-20220510001 修正IE瀏覽器開啟產品授權註冊頁面時，畫面跑版呈現異常
  - 變更檔案: 1 個
- **2022-05-17 11:32:25**: [內部]A00-20220517002 修正DB為 Oracle時，產品授權畫面無顯示模組名稱
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
- **Commit ID**: `1d54b8f5eeffae1dd9d0c9cbe99988946352b89d`
- **作者**: lorenchang
- **日期**: 2022-06-26 20:48:05
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [Portal]]Q00-20220817001調整有整合Portal，用查看流程圖的外部portlet，導入的畫面不是BPM而是Portal的登入頁面
- **Commit ID**: `acf5091dbd10e08f5df901b172cb35a241e75b77`
- **作者**: 林致帆
- **日期**: 2022-08-17 11:58:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 3. [流程設計師]Q00-20221006001 調整在流程設計點擊編輯表單欄位權限時，若表單發行狀態已過期或UNDER_REVISION時會彈提示訊息
- **Commit ID**: `0452686311abe9c5af39cdc698fb834f480e75a6`
- **作者**: cherryliao
- **日期**: 2022-10-06 11:29:53
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`

### 4. [Web]Q00-20220930001 執行iReport套件當發生Exception錯誤時，增加列印異常的堆疊資訊
- **Commit ID**: `72fed5ba073dac7afdcf4e941acaf2711fbf9dec`
- **作者**: wencheng1208
- **日期**: 2022-09-30 10:36:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`

### 5. [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題[補修正]
- **Commit ID**: `bed986e5897f52ebb2307d1ceec8c875e1dfd180`
- **作者**: 謝閔皓
- **日期**: 2022-09-07 17:08:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 6. [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題
- **Commit ID**: `164824c8b781dfba73cf4daf8921d354bbd01c8f`
- **作者**: 謝閔皓
- **日期**: 2022-09-07 14:59:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 7. [WEB]Q00-20221013002:修正表單欄位有設定 "唯讀"時的欄位顏色，顯示卻都為背景顏色。
- **Commit ID**: `696523b4daf9f21112a21d2640079e84887dabdd`
- **作者**: kmin
- **日期**: 2022-10-19 11:27:34
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 8. [流程設計師]A00-20221012001 修正流程設計師當子流程有變更代號時，流程簽入新版時，資料庫的子流程代號未更新
- **Commit ID**: `15afabe6fb342b2994b742df4993c5d77135714f`
- **作者**: waynechang
- **日期**: 2022-10-14 14:29:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/SubflowActivityMCERTableModel.java`

### 9. [TIPTOP]Q00-20221014007修正客戶從TIPTOP端udm_tree操作原稿匣撤銷流程時，選擇特定流程後，BPM仍會回傳所有可撤銷流程的清單
- **Commit ID**: `2e47a6b0e278d3f512ad1973e64c74a3fbdeb969`
- **作者**: 謝閔皓
- **日期**: 2022-10-15 11:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 10. [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。
- **Commit ID**: `210bff96f039a88aa6ba05ece73914aa75b538bc`
- **作者**: raven.917
- **日期**: 2022-10-06 14:35:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 11. [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用(補修正，增加可讀性)
- **Commit ID**: `ede8913d9862ba32527d0c6fc94621df89b191e3`
- **作者**: raven.917
- **日期**: 2022-10-06 08:57:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 12. [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
- **Commit ID**: `20d1e0cc6495b417056f0232f2bb5f8bf038c3e3`
- **作者**: lorenchang
- **日期**: 2022-07-06 17:40:48
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AbsSSOHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_Oracle.sql`

### 13. 移除[ESS]S00-20211208003新增ESS外網主機IP設定
- **Commit ID**: `7ac9732b65674818da20c055bb7d1630580f629c`
- **作者**: kmin
- **日期**: 2022-10-05 15:13:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`

### 14. Revert "[ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)"
- **Commit ID**: `2f3b844086e50efbcf96c4f9962911c218a551eb`
- **作者**: kmin
- **日期**: 2022-10-05 15:04:14
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AbsSSOHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_MSSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_Oracle.sql`

### 15. [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
- **Commit ID**: `6e1be2dfc456cd57a13529f8bf3ecc5c42ab3072`
- **作者**: lorenchang
- **日期**: 2022-07-06 17:40:48
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AbsSSOHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_Oracle.sql`

### 16. [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用
- **Commit ID**: `31c0c3bf260a1e1eb7593246b6a360c8db2de709`
- **作者**: raven.917
- **日期**: 2022-10-04 15:26:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 17. [流程引擎]Q00-20221003002 流程預先解析支持流程設計關卡型態為「活動簽核人」的活動
- **Commit ID**: `e8a366350092080c23c68e63cbd26bfa348d4433`
- **作者**: waynechang
- **日期**: 2022-10-03 13:59:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 18. [流程設計師]Q00-20221003001 調整簽核流設計師，將流程設計師原有的「活動定義/選擇參與者/活動簽核人」重新加回簽核流設計師中
- **Commit ID**: `f60512b795b04b346c7b25ec493af61c895cf8b3`
- **作者**: waynechang
- **日期**: 2022-10-03 10:27:09
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/BpmUserTaskInfoAcquirer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/BpmUserTaskEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskChooserController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskInfoPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/ProcessRelationshipPanel.java`

### 19. Revert "[Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦"
- **Commit ID**: `1380460fb68c3f71a06b7e7fefc2eff600317934`
- **作者**: kmin
- **日期**: 2022-10-03 11:36:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 20. [Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦
- **Commit ID**: `8b0e42b0c9af521bfdffa8db6ce9e73e5d71f5c0`
- **作者**: yanann_chen
- **日期**: 2020-08-05 10:44:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 21. [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]
- **Commit ID**: `438ebc602a888fd6c729289577b66944965cc32e`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 15:47:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 22. [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員
- **Commit ID**: `b9a3046f9032af75d99e39a7f12d1233c4d49303`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 12:24:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java`

### 23. [ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核
- **Commit ID**: `d034999419e4b2dc274cdeb392cff2edcd950946`
- **作者**: 林致帆
- **日期**: 2022-09-27 11:50:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 24. [流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡
- **Commit ID**: `7b7ed70fbb4e42de5d577b6dcc666e30b09f9880`
- **作者**: waynechang
- **日期**: 2022-08-25 14:52:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 25. [Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊
- **Commit ID**: `26483e5cc05a89246bd80aa335020f1fcb655118`
- **作者**: waynechang
- **日期**: 2022-09-20 11:19:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 26. [WEB] C01-20220919007 Admin 需要能開啟設計師時，直接針對該表單做復原簽出的操作行為。
- **Commit ID**: `9b6b59cd9aefca013eee956b0e2329f159811990`
- **作者**: raven.917
- **日期**: 2022-09-21 16:42:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefNodeState.java`

### 27. [Web]Q00-20220921002 調整「必須上傳新附件」邏輯，只要存在一筆以上的附件並且符合在該「關卡名稱」上傳的附件，即可通過該驗證
- **Commit ID**: `baa126f6709fa169ae3d3e8a59d0e6669a31787a`
- **作者**: wencheng1208
- **日期**: 2022-09-21 17:49:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 28. [Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題
- **Commit ID**: `cb3838615f63e8c71254a816520898e1570710dc`
- **作者**: 謝閔皓
- **日期**: 2022-08-08 14:57:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 29. [Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容
- **Commit ID**: `61f938322cfb5f4e24f7e700bb85cf6632a236d2`
- **作者**: 謝閔皓
- **日期**: 2022-08-04 22:21:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 30. [Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題
- **Commit ID**: `30c4edbfb6045c6621c1a283abfc45d103610bdd`
- **作者**: cherryliao
- **日期**: 2022-09-16 13:48:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 31. [Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式
- **Commit ID**: `28beda36762e0b60d7c33c1b62501d52c87e6d76`
- **作者**: wencheng1208
- **日期**: 2022-09-01 15:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 32. [流程引擎]Q00-20220912004 修正findProcessPackageById方法內容為取得流程包裹最新一版，以避免後續同仁遇到此坑
- **Commit ID**: `b912eefbdb5bd4bd4af04a495507c422236324bf`
- **作者**: wencheng1208
- **日期**: 2022-09-12 17:59:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 33. [組織同步]QQ00-20220912003 組織同步功能執行人員資料修改時，可保留人員姓名多語系關聯
- **Commit ID**: `cfb2716e35ea897eae91da2922c3dd0fd85fbcba`
- **作者**: wencheng1208
- **日期**: 2022-09-12 17:39:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 34. [Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本
- **Commit ID**: `ee321aa933c1286b752287cb2d6f1f97ce203643`
- **作者**: wencheng1208
- **日期**: 2022-09-08 15:12:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java`

### 35. [Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息
- **Commit ID**: `bfb2c7fe5e2a371a9ec214a00bf24bbc123bab13`
- **作者**: cherryliao
- **日期**: 2022-09-08 14:06:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 36. [Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部 原單號: C01-20220527001 個人資訊>流程相關設定>工作事項顯示設定 ，當點選關注流程應該進入待辦中的關鍵流程、點選重要流程應該進入待辦的重要流程
- **Commit ID**: `90b2bd43bedceddfa8b11a93e1313a1b8b76c37f`
- **作者**: kmin
- **日期**: 2022-09-19 13:54:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 37. [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
- **Commit ID**: `3aaa7d1b5065a47cb1842d944f66c544655f34bb`
- **作者**: kmin
- **日期**: 2022-09-15 11:22:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 38. Revert "[流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯"
- **Commit ID**: `6c4fa426bdbd722a183655f4fcbde85f20cee410`
- **作者**: kmin
- **日期**: 2022-09-15 10:33:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 39. [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
- **Commit ID**: `3c49dd5e00f6bc612c57f8f4cd4aec9b84c90ae6`
- **作者**: wencheng1208
- **日期**: 2022-09-14 10:34:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 40. [Web]S00-20220810001簽核意見是否顯示管理員
- **Commit ID**: `bee3b10e5f174c0d0dc9e6043db147d444449f2c`
- **作者**: kmin
- **日期**: 2022-09-15 09:47:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java`

### 41. [流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤
- **Commit ID**: `f488c06a3327d8f2ef959bc4a04d75b451055d93`
- **作者**: kmin
- **日期**: 2022-09-15 09:19:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/OrganizationUnit.java`

### 42. [流程引擎]Q00-20220627001 優化核決層級關卡解析人員緩慢問題
- **Commit ID**: `f9412cc4aa0afcb0e4000affefd3c189fed196af`
- **作者**: kmin
- **日期**: 2022-09-15 09:16:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/OrganizationUnit.java`

### 43. [流程引擎]Q00-20220823003 讓亂數產生的ID增加動態加簽CustomDecisionRule的開頭關鍵字，前面流程圖解析邏輯段也要新增
- **Commit ID**: `02f2996cb7e1dae2d5824b1745452163ebaad168`
- **作者**: kmin
- **日期**: 2022-09-06 15:55:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 44. [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
- **Commit ID**: `cea4ee7a807c2fc04e0cffda211e16cdd57e4293`
- **作者**: kmin
- **日期**: 2022-09-06 15:52:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 45. [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
- **Commit ID**: `c9834d376f751eb5088b5bb5315f288cc99f17bb`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:48:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 46. [WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能
- **Commit ID**: `3e751eefed0cc33cb84390f6349df340fde5b09a`
- **作者**: 林致帆
- **日期**: 2022-08-30 08:34:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 47. [流程引擎]S00-20220722001新增批次通知信件主旨內容
- **Commit ID**: `4e8c9a768beb91736c51c84cc09980bf939390b7`
- **作者**: 謝閔皓
- **日期**: 2022-08-23 15:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 48. Revert "[流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容"
- **Commit ID**: `8796d926d1d1d5836d4e50fbaed104775a723a1b`
- **作者**: kmin
- **日期**: 2022-08-29 10:11:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 49. [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
- **Commit ID**: `41972a69f72a677308e184ec90bb730b574fb2e7`
- **作者**: wencheng1208
- **日期**: 2022-08-24 11:48:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 50. [流程引擎]Q00-20220823001 修正使用客製的方式執行動態加簽後，無法呈現詳細流程圖畫面的問題
- **Commit ID**: `b4ce57407a210883dd7b8f09be168a285d98b856`
- **作者**: wencheng1208
- **日期**: 2022-08-23 14:44:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java`

### 51. [Web]Q00-20220822001 修正BPM使用IE瀏覽器上傳附件時會失敗
- **Commit ID**: `db915813d2d9a1eaca33c063d14ca325dbc93f3a`
- **作者**: 林致帆
- **日期**: 2022-08-22 14:11:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

### 52. [TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久
- **Commit ID**: `05bc8adab61fad4cb327ed87e1a7671961495eac`
- **作者**: 林致帆
- **日期**: 2022-08-19 17:57:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 53. [Web]Q00-20220808003修正使用產品表單中的Date元件，並搭配TextBox元件的進階功能，資料型態整數中的時間區間運算，當遇到元件ID有使用下底線時，會導致TextBox元件無法正常運算
- **Commit ID**: `7b928a6aee56db4dd73ba59714b63c2122da7f38`
- **作者**: 謝閔皓
- **日期**: 2022-08-08 17:43:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 54. [Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題
- **Commit ID**: `6e1babac34a9a0d5cd655bb880c2eb27c2b12367`
- **作者**: 謝閔皓
- **日期**: 2022-08-11 12:56:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 55. [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
- **Commit ID**: `ae5d12df75fe9bbcf6e11cdf3e7ba82239786ef7`
- **作者**: 謝閔皓
- **日期**: 2022-08-10 18:34:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 56. [Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示
- **Commit ID**: `264b574094638268c0467e857b387449c8ec32fd`
- **作者**: 王鵬程
- **日期**: 2022-07-29 16:49:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 57. [Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行
- **Commit ID**: `a6d1eafd6fa1f3a20a3893bb5a01ed8f1e1f86f6`
- **作者**: 王鵬程
- **日期**: 2022-07-28 17:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 58. [Web]A00-20220808001 調整報表查詢產出的日期與匯出Excel的日期不一致問題
- **Commit ID**: `cae9a1fb40764a1fe0e24082ab4525bbcf6aaaf0`
- **作者**: cherryliao
- **日期**: 2022-08-10 11:01:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/ChartQueryTemplate.js`

### 59. [組織同步]Q00-20220809002 修正組織同步log出現Error時改寄送失敗通知信
- **Commit ID**: `4d2114c868126858174040d219327445dcee2256`
- **作者**: yamiyeh10
- **日期**: 2022-08-09 13:51:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`

### 60. [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]
- **Commit ID**: `2458402fdcc11baed9f733ede602245303cb7697`
- **作者**: wencheng1208
- **日期**: 2022-08-05 12:00:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 61. [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。
- **Commit ID**: `80cbadca5d7bbfde80b789e5a4d160ddfa43edca`
- **作者**: wencheng1208
- **日期**: 2022-08-03 16:48:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 62. [Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料
- **Commit ID**: `584139301094ff96ec3c3bd7cad16444a99a2403`
- **作者**: 謝閔皓
- **日期**: 2022-08-01 16:26:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 63. [內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來
- **Commit ID**: `a3e333f4419f52d74bd246e466496d4e5c6b1f6b`
- **作者**: kmin
- **日期**: 2022-07-27 14:37:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/persistence/src/com/dsc/nana/persistence/JpaService.java`

### 64. [Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入
- **Commit ID**: `1b5f5da3109bfdb03891ac382a6b4830403f91fc`
- **作者**: 王鵬程
- **日期**: 2022-07-26 18:16:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 65. [Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法
- **Commit ID**: `9887b73a36466c5f1d5b156971c2fa3b63cd2ce1`
- **作者**: wencheng1208
- **日期**: 2022-07-25 10:55:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 66. [Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題
- **Commit ID**: `046f00f571ad53f4359472ce8b40aff7725353b3`
- **作者**: yamiyeh10
- **日期**: 2022-07-25 10:22:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 67. [流程引擎]Q00-20220629004 修正流程定義設定「流程撤銷時逐級通知」，在使用者撤銷流程後，只有撤銷流程當下進行中的關卡的處理者在系統通知清單內有該筆通知資料
- **Commit ID**: `09daf70e0131dc3dac6e57dfddd5743cd9434d01`
- **作者**: yanann_chen
- **日期**: 2022-07-01 15:05:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerLocal.java`

### 68. Revert "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。"
- **Commit ID**: `e69f03cab80ecb61cfe1304696b7799a3498afdf`
- **作者**: kmin
- **日期**: 2022-08-10 08:56:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 69. [Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失 RWD不會發生的原因是就算前端直是空的傳進來也是[] 移動防呆位置
- **Commit ID**: `da5b5c4220450b2b46cb6dd8b71d38d7657b021e`
- **作者**: kmin
- **日期**: 2022-08-03 16:04:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 70. [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。
- **Commit ID**: `c209cc1f152d305f4021c45adce456bdbc068b06`
- **作者**: wencheng1208
- **日期**: 2022-07-29 16:30:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 71. [流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送
- **Commit ID**: `678cbe117d658fd527a5e169087b8e501a670d5b`
- **作者**: waynechang
- **日期**: 2022-07-21 15:36:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 72. [流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料
- **Commit ID**: `a056660157cae186b4044ac1cdeaed29ddc34641`
- **作者**: kmin
- **日期**: 2022-08-03 15:53:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 73. Revert "[流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料"
- **Commit ID**: `756afc159b46123d50e00f9b17762758843086ea`
- **作者**: kmin
- **日期**: 2022-08-03 15:38:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 74. [流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料
- **Commit ID**: `2c62d2a67f740dba4c4b9de5278842d290f305b5`
- **作者**: yanann_chen
- **日期**: 2022-06-29 14:31:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 75. [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]
- **Commit ID**: `a4b215c54e1cfea008defbe1ee0b564cf87595c1`
- **作者**: 林致帆
- **日期**: 2022-07-29 14:34:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 76. [Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題
- **Commit ID**: `754970492b7e6e5dd673fa321a62ed80f4de16cd`
- **作者**: cherryliao
- **日期**: 2022-07-27 17:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 77. [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]
- **Commit ID**: `5cbacd775df77172323d6129dbc1433953edef27`
- **作者**: cherryliao
- **日期**: 2022-07-28 14:39:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 78. [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。
- **Commit ID**: `e743ffd22d13e7c2ccdb92e89040325bf5f8609c`
- **作者**: wencheng1208
- **日期**: 2022-07-27 12:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 79. [Web]A00-20220802001 修正無法開啟SAP維護作業
- **Commit ID**: `c710bda537773a3d8b1fb37aebfcf355279c257c`
- **作者**: 林致帆
- **日期**: 2022-08-02 11:27:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 80. [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]
- **Commit ID**: `5caf081cb7708b9408fee161ab60057746a5832a`
- **作者**: 林致帆
- **日期**: 2022-07-29 14:02:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 81. [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
- **Commit ID**: `ca6439b52e14e25dd1f36a8cc1f0707d45d00df2`
- **作者**: 林致帆
- **日期**: 2022-07-28 15:20:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 82. Revert "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock"
- **Commit ID**: `b3023f7146ee8bdbe55d5fc64c9b987d9b3003d5`
- **作者**: kmin
- **日期**: 2022-08-01 16:38:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 83. [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
- **Commit ID**: `2b34ffdd46218c2bb2b3693738fc5e50439c00d9`
- **作者**: 林致帆
- **日期**: 2022-07-28 15:20:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 84. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]
- **Commit ID**: `c6484f3ab16b220889e23161032bda07d9b139c2`
- **作者**: walter_wu
- **日期**: 2022-07-29 14:20:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 85. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
- **Commit ID**: `6a34311627ddb878ac7890ff54129650d37c6dcb`
- **作者**: walter_wu
- **日期**: 2022-07-29 00:04:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 86. Revert "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況"
- **Commit ID**: `170861ad2b4d77efec6f1e396ec67cc5f902f34a`
- **作者**: kmin
- **日期**: 2022-08-01 13:51:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 87. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
- **Commit ID**: `b7ead7ae38f728c9e726e4e6b37fa13e441c3964`
- **作者**: walter_wu
- **日期**: 2022-07-29 00:04:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 88. Revert "[MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]"
- **Commit ID**: `8a24c043da8c40dcbd5078f6eb778ab0c01b9671`
- **作者**: kmin
- **日期**: 2022-08-01 13:50:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 89. [MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]
- **Commit ID**: `4fae0e3c2582cfa92d1a7a6203506a845567fb87`
- **作者**: pinchi_lin
- **日期**: 2022-07-12 19:29:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 90. [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法
- **Commit ID**: `bcd9da43d09d4664ca0a0f80a77c49583f2a5a47`
- **作者**: 林致帆
- **日期**: 2022-07-27 10:41:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 91. [內部]Log調整
- **Commit ID**: `b46a273694ad0a7d9c6f7c47b6b75dfe90b59c64`
- **作者**: lorenchang
- **日期**: 2022-07-04 08:58:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 92. [Web]Q00-20220720002 修正列印模式下附件與簽核歷程的右邊邊線會不見問題
- **Commit ID**: `685568764d2c4310fa42b5f4b2f591bdcfdc200a`
- **作者**: yamiyeh10
- **日期**: 2022-07-20 18:23:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`

### 93. Revert "[Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部"
- **Commit ID**: `992c075955e3b09290b0b18065d57f3477aca6d8`
- **作者**: kmin
- **日期**: 2022-07-25 17:12:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 94. [Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部
- **Commit ID**: `979c526610ee3c35a2bf43776edf8ed59927e040`
- **作者**: 王鵬程
- **日期**: 2022-07-20 15:26:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 95. [Web]A00-20220718001 修正Gird元件在某關卡隱藏時開啟表單會出現該物件沒有定義的問題
- **Commit ID**: `630b34ec1f5aa0c718d05b97d7dadfa068dc4f8f`
- **作者**: cherryliao
- **日期**: 2022-07-20 11:38:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 96. [登入]Q00-20220719002 修正DB為Oracle時，使用者登出登入紀錄作業中使用操作時間為查詢條件會查不到結果的問題
- **Commit ID**: `8a869815b15a74613153802bee16a9f13d438ce2`
- **作者**: cherryliao
- **日期**: 2022-07-19 18:01:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 97. [Web]Q00-20220714004 修正使用safari瀏覽器時，點選在線閱讀附件沒有反應
- **Commit ID**: `fa30564db8e130af2c0fd839e564c5c7ea0202f3`
- **作者**: yanann_chen
- **日期**: 2022-07-14 18:28:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 98. [Web]Q00-20220713003 修正在行動版面中，在表單內向下滑動時，右下角的浮動按鈕會隱藏而無法後續操作
- **Commit ID**: `c5a72b06c2a0b21af2089c49cf16a8ee04256618`
- **作者**: 王鵬程
- **日期**: 2022-07-13 16:33:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 99. [Web]Q00-20220714003 修正Dialog元件的txt屬性如果被FormScript或其他非預期方式刪除，在產生表單畫面時報錯 如果屬性被改成null或是遺失將其防呆為空字串 內部測試將元件隱藏一關或是連續兩關以上都無法重現，應該是客戶的Script有改到元件內容
- **Commit ID**: `e8e15006b589d43fe9b77150a07c11164481a7cc`
- **作者**: kmin
- **日期**: 2022-07-18 10:06:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 100. [流程引擎]Q00-20220713005 修正核決關卡設定自動簽核，取/退回後再次簽核進核決層級時會報錯無法派送
- **Commit ID**: `23a28bdefa5016de43ad7ba9f6bde2fa3e484682`
- **作者**: walter_wu
- **日期**: 2022-07-14 11:32:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 101. [Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法 語法: FormUtil.setScrollBarHeight("0");
- **Commit ID**: `a3a615817f444bcb70b1096a71bf3abd98bf5150`
- **作者**: kmin
- **日期**: 2022-07-15 08:37:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 102. [表單設計師]Q00-20220711002 修正絕對表單元件不存在某些屬性而取用該屬性導致無法開啟表單，增加防呆
- **Commit ID**: `c21f1f4d10ad95fdd39b3bd295bdaf0768ac5f19`
- **作者**: 王鵬程
- **日期**: 2022-07-11 15:22:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 103. [流程引擎]Q00-20220706005 修正資料庫為MSSQL，且流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
- **Commit ID**: `a5152db54620f4ecbc0aa77803a0f5eb7e875444`
- **作者**: yanann_chen
- **日期**: 2022-07-06 17:43:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 104. [Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久
- **Commit ID**: `9327480cbf619bcdde10a79f5aa2b1fa7da06eb5`
- **作者**: walter_wu
- **日期**: 2022-06-28 18:06:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 105. [流程引擎]Q00-20220622001 修正當RWD表單的RadioButton元件與CheckBox元件選項內容太長時會斷行 相關議題單：C01-20220613009。
- **Commit ID**: `b9d309c72dd36cc6448ac6ed7bb5ab0b904675dd`
- **作者**: kmin
- **日期**: 2022-07-14 17:29:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 106. [Web]Q00-20220621001 修正非CheckBox或RadioButton的選擇元件執行到額外輸入框邏輯導致出現非預期異常
- **Commit ID**: `92368e872c7d106b815248361a8fc8e934ef0786`
- **作者**: kmin
- **日期**: 2022-07-14 17:27:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 107. [Web]Q00-20220713004 修正移動消息訂閱管理頁面無法開啟
- **Commit ID**: `bdd4c6865b8c76cc3e3952aad15e2a8ebde55e7e`
- **作者**: yanann_chen
- **日期**: 2022-07-13 16:21:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`

### 108. [流程引擎]Q00-20220711003 修正在表單上將Excel匯入單身後，開窗畫面變成空白
- **Commit ID**: `a566e9feb6ac3307b6192f9603977d986fcc61c2`
- **作者**: yanann_chen
- **日期**: 2022-07-11 17:38:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`

### 109. [Web]Q00-20220701001 調整時間元件如果輸入不是數字直接換成00
- **Commit ID**: `91b05ba89cabea5201cbfc5faa894ec859f03e5a`
- **作者**: walter_wu
- **日期**: 2022-07-01 11:54:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 110. [資安]Q00-20220629001 修正/NaNaWeb/webservice/servlet/AxisServlet要加入為WebService白名單控管範圍
- **Commit ID**: `b9179954f3708f8d659b0986294af383bbe8ea8a`
- **作者**: 林致帆
- **日期**: 2022-06-29 11:34:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 111. [Web]A00-20220622002 修正流程新增關卡頁面輸入簽核意見在新增向前or向後關卡按下確定後，簽核意見內容被清除
- **Commit ID**: `e6d5832a2b9539fcf61afc755e9a4056598c44c2`
- **作者**: 林致帆
- **日期**: 2022-06-23 14:10:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 112. [Web]Q00-20220523001 修正同瀏覽器有二次登入時，登入頁「記住我」的功能會失效
- **Commit ID**: `bc1b44733c0006ee17a2c088dde79b29a1a831e5`
- **作者**: waynechang
- **日期**: 2022-06-22 15:47:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 113. [Web]A00-20220616003 修正SerialNumber元件的字體設25px以上，在流程中該元件的顯示會有部分被遮蔽到
- **Commit ID**: `6027984f5f20c23d39bf6d1cf12e20910800ab39`
- **作者**: 王鵬程
- **日期**: 2022-06-21 16:55:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 114. [Web]Q00-20220621003修正發起流程-查詢流程清單搜尋純數字的流程名稱會報錯
- **Commit ID**: `24b41f87171d72661bf64fd8f1441f30d1bae8dd`
- **作者**: 林致帆
- **日期**: 2022-06-21 13:42:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`

### 115. [Web]A00-20220616001 退回重辦頁面增加退回重辦方式的說明
- **Commit ID**: `2f96b0529d219879fe2679c40b6603e8b7309ee6`
- **作者**: yanann_chen
- **日期**: 2022-06-20 14:09:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 116. [Web]Q00-20220609001 修正行動裝置在加簽關卡選擇參與者人員後，選擇參與者的下方欄位會顯示已選取0個項目
- **Commit ID**: `6f724526dcdf1eea047294858cfecf9bd0661df9`
- **作者**: 王鵬程
- **日期**: 2022-06-09 11:50:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 117. [報表設計器]Q00-20220607003 修正欄位字串如果含as會辨識錯欄位名稱
- **Commit ID**: `b379956f95cf06d57f991b653dc6480c3f7ae7c0`
- **作者**: walter_wu
- **日期**: 2022-06-08 09:21:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`

### 118. [Web]Q00-20220607002 修正首頁待辦清單第三頁以上的流程進行派送時會報錯
- **Commit ID**: `c7b2d36dee04fd8cc047a4ce0a3f26325d62440a`
- **作者**: 林致帆
- **日期**: 2022-06-07 14:20:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 119. [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼 1.移動端原本就會把中文字轉換成Unicode字符代碼作呈現,如果再轉一次就會把&轉換掉 2.關聯紀錄:A00-20220511001
- **Commit ID**: `c19c7517a4280f2ea190a5ebb78b6d3b0d96a8f1`
- **作者**: kmin
- **日期**: 2022-07-14 16:39:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 120. Revert "[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼"
- **Commit ID**: `ff9754ebb34842d9a7b2c570f845fc1277289125`
- **作者**: kmin
- **日期**: 2022-07-14 16:37:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 121. [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼
- **Commit ID**: `d75c994b9ebf5e12ffd5a75b93f4433a24850191`
- **作者**: 郭哲榮
- **日期**: 2022-07-12 20:01:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 122. [流程引擎]Q00-20220628001 調整流程關係人與關係部門解析邏輯，若流程中沒有變更流程關係人或關係部門資料，系統在流程往下派送時就不會再解析流程關係人與關係部門
- **Commit ID**: `c969b52102ecb4def4a86fff53097d8966931a97`
- **作者**: yanann_chen
- **日期**: 2022-06-28 17:17:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 123. [流程引擎]Q00-20220707002 修正表單日期元件預設值計算錯誤 相關議題單：C01-20220701004。
- **Commit ID**: `0202ed19fb05f4ef5caced212eaf59732c4afe40`
- **作者**: kmin
- **日期**: 2022-07-14 16:34:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 124. [BPM APP]Q00-20220711001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟移動端表單會報錯的問題
- **Commit ID**: `d8ca71f47ea6d2662f690b837cbda0c4e18358f8`
- **作者**: cherryliao
- **日期**: 2022-07-12 11:15:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`

### 125. [表單設計師]A00-20220704001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟表單會報錯
- **Commit ID**: `4a7839f6112ce0f9793dc88d13827b7169f70b99`
- **作者**: walter_wu
- **日期**: 2022-07-08 18:24:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 126. [表單設計師]C01-20220706003 修正當變更表單的對齊方式並儲存後會將已設計過的行動版表單設計欄位清空問題
- **Commit ID**: `43126117cd2f58ca7ebb13c58093b4894adae349`
- **作者**: yamiyeh10
- **日期**: 2022-07-08 11:01:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`

### 127. [流程引擎]Q00-20220707003 修正DialogInput元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容
- **Commit ID**: `a91321f3fe9f0c7fe064f7c6b1867aa4406c2709`
- **作者**: yanann_chen
- **日期**: 2022-07-07 16:10:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 128. [Web]A00-20220628001 修正已簽核過的關卡，從Mail的待辦連結進入該表單時可以移除附件
- **Commit ID**: `17d2f439eed1efb31126c52c678ac5a09292b906`
- **作者**: kmin
- **日期**: 2022-07-14 16:28:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 129. [Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾
- **Commit ID**: `b351b7ac7de092698624bb3fb5339ede8495a92e`
- **作者**: 王鵬程
- **日期**: 2022-06-13 16:14:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`

### 130. [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯[補修正]
- **Commit ID**: `40b4ab1d5f46cc66c43a675819b363df40c62218`
- **作者**: 林致帆
- **日期**: 2022-06-14 11:52:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 131. [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯
- **Commit ID**: `a54aee90cc354946b0b0ecc2d6c6af223527b0a3`
- **作者**: walter_wu
- **日期**: 2022-06-13 16:07:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 132. [內部]Q00-20220610001修正WorkFlow拋單log會顯示[Fatal Error] :1:1的錯誤訊息
- **Commit ID**: `38f5c4331370bb8b639116d0a47e783d2e124193`
- **作者**: 林致帆
- **日期**: 2022-06-10 11:42:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 133. [Web]A00-20220608003 修正進入追蹤流程畫面時未清除「撤銷理由」欄位內容
- **Commit ID**: `7f0adcd722b72637ea0e2ce96e77fd7cd6848873`
- **作者**: yanann_chen
- **日期**: 2022-06-09 17:18:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 134. [流程引擎]Q00-20220609003 修正使用者操作個人預設代理人設定時，代理人可能有多筆相同人員的問題
- **Commit ID**: `865774fa9b179a00f322eaff4163c2676a7a84e0`
- **作者**: yanann_chen
- **日期**: 2022-06-09 16:32:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/user_profile/MultiDefaultSubstituteForManaging.java`

### 135. [內部]Q00-20220609002 調整DWR設定讓Log不要一直出現轉換ProcessInstanceStateType的錯誤
- **Commit ID**: `18e25acb3b7f0414ddf505687e96c5cc66c0e3ed`
- **作者**: walter_wu
- **日期**: 2022-06-09 15:28:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 136. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
- **Commit ID**: `c6ccf3fee3d00c3a620ba7c2db247a20c9cff9de`
- **作者**: walter_wu
- **日期**: 2022-06-10 18:10:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 137. [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
- **Commit ID**: `eeb41f2b2ec36cbca2e5989c272bf8b90886920d`
- **作者**: kmin
- **日期**: 2022-07-14 16:20:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 138. Revert "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開"
- **Commit ID**: `5a2411fdd14302b4f3cfbb506efe248d9d30d460`
- **作者**: kmin
- **日期**: 2022-07-14 16:19:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 139. [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
- **Commit ID**: `36869fb976f50f659b1bdd104cef8f7bc8fa0a7c`
- **作者**: walter_wu
- **日期**: 2022-06-08 15:56:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 140. [WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯
- **Commit ID**: `5a89e296bee6eef02313fc33a1c5e9da8778d7fb`
- **作者**: walter_wu
- **日期**: 2022-06-08 11:49:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 141. [Web]Q00-20220606001 修正第二關之後的關卡預解析，流程線的條件式採用表單欄位時，預解析的關卡與派送的關卡不符合
- **Commit ID**: `46257b441af7fa32405dbe75555f7b915460b7e1`
- **作者**: 林致帆
- **日期**: 2022-06-06 14:26:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 142. [Web]A00-20220526001 修正如果DB是Oralce在線閱讀浮水印管理出現無法取得EJB所提供的服務
- **Commit ID**: `a61981f8b3b11ef21fe916b79f542cbf2c15f720`
- **作者**: walter_wu
- **日期**: 2022-06-01 13:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java`

### 143. [內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方
- **Commit ID**: `c5ee178664e6b6e9b0a45d94f6255f2f860e05de`
- **作者**: walter_wu
- **日期**: 2022-05-30 15:34:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 144. [BPM APP]C01-20220523004 修正移動端subTab元件使用formScript在單獨顯示時會顯示其他頁籤內容的問題
- **Commit ID**: `bbf6cdaba99d733ce9fe7f8213e5eb67e2b27759`
- **作者**: 郭哲榮
- **日期**: 2022-05-27 19:22:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileSubTab.js`

### 145. [Web]Q00-20220527003 修正使用者使用監控流程的最大筆數沒有根據process.default.show.records的設定
- **Commit ID**: `72b64ec91f0f44e402f870bccf743f5bb820d042`
- **作者**: walter_wu
- **日期**: 2022-05-27 17:34:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 146. [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
- **Commit ID**: `a609d8da256c68226cee6c806df3edff3854069c`
- **作者**: kmin
- **日期**: 2022-07-14 16:14:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 147. Revert "[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘"
- **Commit ID**: `609cdce934fcef062a77186802a1131ffba5ef0d`
- **作者**: kmin
- **日期**: 2022-07-14 16:13:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 148. [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
- **Commit ID**: `6f10ef8d256aaf3cbae2014d7ae6712710b19dd6`
- **作者**: walter_wu
- **日期**: 2022-05-27 17:15:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 149. [BPM APP]C01-20220524002 修正改派通知設定整張表單時Line推播內容不會呈現表單訊息格式的問題
- **Commit ID**: `17062d606905ce9eb3c09a6d27037a1d5538e339`
- **作者**: yamiyeh10
- **日期**: 2022-05-26 10:06:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 150. [Web]A00-20220519001 修正IE加簽會加成兩次的問題
- **Commit ID**: `376f93870331f3b05488b75f674136eac7c976ee`
- **作者**: walter_wu
- **日期**: 2022-05-25 18:07:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 151. [表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出
- **Commit ID**: `76db9c269d9c579b4d4f27d9909c284c8328a5e5`
- **作者**: yanann_chen
- **日期**: 2022-05-25 17:01:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 152. [Web]Q00-20220525004 修正輸入單身資料有&#加任意數字，被轉成特殊符號，會與輸入資料不符
- **Commit ID**: `45407b00ec35e0dac4a3ce528e7b0d04805210cf`
- **作者**: 林致帆
- **日期**: 2022-05-25 16:48:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 153. [TIPTOP]Q00-20220525003 修正拋單的單身資料有中刮號會被轉成小括號，導致資料與TIPTOP不符合
- **Commit ID**: `d3a9fd788e9e1e16e58d81f9e19de67fe4daab79`
- **作者**: 林致帆
- **日期**: 2022-05-25 16:32:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 154. [系統管理工具]C01-20220524002 修正進階功能>檢查密碼如果User裡有關聯有異常的會全部撈不出來
- **Commit ID**: `bbfeb44c4307ea6400f09ce9d4780ac9c80497c2`
- **作者**: walter_wu
- **日期**: 2022-05-24 19:04:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/util/CheckPassDialog.java`

### 155. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
- **Commit ID**: `f00f82cb6b0be171a0174cfbd03708d88ebae3b9`
- **作者**: yanann_chen
- **日期**: 2022-05-25 14:01:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 156. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
- **Commit ID**: `a8f914520555ca73234fbf0c7fead761f49bd875`
- **作者**: yanann_chen
- **日期**: 2022-05-25 10:57:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 157. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤
- **Commit ID**: `92d2f06ff00910ede22c220a303bb88dfaa46d6b`
- **作者**: yanann_chen
- **日期**: 2022-05-24 17:45:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 158. [內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)
- **Commit ID**: `f7abe4bb2ddbd60043acc83d567a426828ab2ce3`
- **作者**: walter_wu
- **日期**: 2022-05-23 18:19:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 159. [流程引擎]Q00-20220520003 修正流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
- **Commit ID**: `c736e90c69c8718d13d0d4ed667e37eb6cc66433`
- **作者**: yanann_chen
- **日期**: 2022-05-20 15:42:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 160. [BPM APP]C01-20220516002 修正行動端FormUtil.disable為true時Dropdown元件顯示異常問題
- **Commit ID**: `e867113bc82fba87d0155404b131c6579e495721`
- **作者**: 郭哲榮
- **日期**: 2022-05-18 18:47:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 161. [Web]Q00-20220518001 修正退件表單資訊與開啟的表單關連錯誤
- **Commit ID**: `1d1f78987f576456c88b36bf6fcc7f22af35cd48`
- **作者**: 林致帆
- **日期**: 2022-05-18 10:39:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 162. [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題[補]
- **Commit ID**: `308d711ac70660457534c193ad6b766ecfca5421`
- **作者**: 郭哲榮
- **日期**: 2022-05-17 15:12:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 163. [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題
- **Commit ID**: `111da87c3aab7f8bef5481d6b2302367813faa76`
- **作者**: 郭哲榮
- **日期**: 2022-05-16 15:10:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 164. [流程引擎]A00-20220511001 修正使用者輸入到表單TextArea的內容在儲存表單後變成亂碼的問題
- **Commit ID**: `f6f7a2a2671881d90f6465235cc44fbbe470add9`
- **作者**: yanann_chen
- **日期**: 2022-05-12 18:19:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 165. [Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」
- **Commit ID**: `8e3c11ac3da316033d2f492b08f924a2b75a70f3`
- **作者**: wayne
- **日期**: 2022-05-12 16:55:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`

### 166. [流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差
- **Commit ID**: `90fcad606b409e4042b1bef7deedac699c81a377`
- **作者**: yanann_chen
- **日期**: 2022-05-12 14:36:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 167. [Web]Q00-20220411005 修正使用者在絕對位置表單進行簽核時遭遇產品程式錯誤
- **Commit ID**: `491da6dbb2e32308231f696260e999bccc245e0b`
- **作者**: yanann_chen
- **日期**: 2022-05-12 14:12:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 168. [內部]Q00-20220512001 bootstrap-table-1.18.3.js更換檔案名稱
- **Commit ID**: `feca23c9bbd4d4fa19731df8d118eaa6119f321c`
- **作者**: yanann_chen
- **日期**: 2022-05-12 11:47:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js`

### 169. [Web]Q00-20220511005 修正T100拋轉單據中有舊值的單身內容沒有顯示為紅色
- **Commit ID**: `e857eee9d14d7900dac6823a0c3d0e80875b2810`
- **作者**: yanann_chen
- **日期**: 2022-05-11 15:03:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js`

### 170. [內部]Q00-20220511003 調整BCL8轉檔Timeout從預設2分鐘拉長到5分鐘
- **Commit ID**: `362595d986a914d1d73bd29a134efdbac19a0843`
- **作者**: walter_wu
- **日期**: 2022-05-11 14:44:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 171. [流程引擎]Q00-20220506001 調整使用者「授權的流程」，流程清單筆數改為設定檔設定
- **Commit ID**: `8cdb96c57f96594b3875ade082de583f64626fa3`
- **作者**: yanann_chen
- **日期**: 2022-05-11 12:07:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 172. [流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料
- **Commit ID**: `27e790b2aac3873472de9abaeaffd2dc73806941`
- **作者**: yanann_chen
- **日期**: 2022-05-11 11:23:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 173. [流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知
- **Commit ID**: `cb108e61e86af69550310d90255b150951786861`
- **作者**: yanann_chen
- **日期**: 2022-05-11 11:10:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 174. [Web]Q00-20220511001 調整列印表單畫面簽核歷程，移除「資料代號」、「通知者」欄位
- **Commit ID**: `ee4d7001d1002b9eb3ce8c39c6ed9b186ebabbc1`
- **作者**: yanann_chen
- **日期**: 2022-05-11 10:54:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 175. [Web]Q00-20220510001 修正IE瀏覽器開啟產品授權註冊頁面時，畫面跑版呈現異常
- **Commit ID**: `b5e6b24502de1f68fa32e7b5a8c18577599e2e0e`
- **作者**: wayne
- **日期**: 2022-05-10 15:59:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`

### 176. [BPM APP]C01-20220509009 修正行動端在Grid元件存在沒有繫結元件情況下點擊編輯按鈕會失敗問題
- **Commit ID**: `94ed24fd530b825fd409ddf9be9980e440c9f68c`
- **作者**: yamiyeh10
- **日期**: 2022-05-10 10:49:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 177. [Web]Q00-20220509002 修正授權的流程使用closeTime(結案時間)排序會報錯的問題
- **Commit ID**: `a4a56b5209e3ae4e8b658eb18dfcb56fa7bc2e19`
- **作者**: walter_wu
- **日期**: 2022-05-09 16:41:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 178. [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果[補]
- **Commit ID**: `0b32f722e9b9f876c594e612079f917c99549eb3`
- **作者**: 王鵬程
- **日期**: 2022-05-05 17:52:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 179. [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果
- **Commit ID**: `f0ac924b4b31ad4e2cd50ed2318ed58e8e9c30b9`
- **作者**: 王鵬程
- **日期**: 2022-05-05 15:24:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 180. [Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢
- **Commit ID**: `2e43780e2adce65ebb4eb223e1162da0309ee205`
- **作者**: 林致帆
- **日期**: 2022-05-05 11:00:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 181. [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]
- **Commit ID**: `f7bc2ccbe0e53fcce61966ae4652c9af75b0869c`
- **作者**: yanann_chen
- **日期**: 2022-06-20 16:44:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 182. [Web]Q00-20220616001 調整待辦通知信中簽核歷程的處理者欄位移除粗體樣式
- **Commit ID**: `3e92541da2e6f767fafd2b39762b71fabca29cc7`
- **作者**: 林致帆
- **日期**: 2022-06-16 13:59:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 183. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
- **Commit ID**: `6e8c290af7d49313599bc9d2c487f1a1b0f0be7c`
- **作者**: yamiyeh10
- **日期**: 2022-05-26 09:50:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 184. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
- **Commit ID**: `4ecab37c0ba800aff656e15697922a7905333a38`
- **作者**: yamiyeh10
- **日期**: 2022-05-16 15:15:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 185. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題
- **Commit ID**: `c83c05c6362fbafaf767d8fbae44c8f085e1d43d`
- **作者**: yamiyeh10
- **日期**: 2022-05-10 18:03:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 186. [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題
- **Commit ID**: `5241a7266e699eae9ce7df37a8318f455eba5260`
- **作者**: yanann_chen
- **日期**: 2022-05-04 14:02:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 187. [BPM APP]C01-20220419001 修正移動端選項元件使用動態塞值且隱藏標籤會顯示異常問題
- **Commit ID**: `cbb1f75cdd68334a25f6bd6afaacff11663fed80`
- **作者**: 郭哲榮
- **日期**: 2022-04-28 12:23:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 188. [Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色
- **Commit ID**: `99882ef3e28f0fe40d995613bfc079c3cebef6f9`
- **作者**: 王鵬程
- **日期**: 2022-04-27 16:50:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 189. [內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤
- **Commit ID**: `e21554874ff8f1a19e7913cd46373a366085b4d5`
- **作者**: walter_wu
- **日期**: 2022-04-27 15:00:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 190. [流程引擎]A00-20220519003 修正終止前置流程時，後置流程完成流程撤銷後，前置流程出現「派送失敗」的錯誤訊息，無法正常終止
- **Commit ID**: `b6ce7ebaa673892bb38fbbc04eb1bb7883c9bc30`
- **作者**: yanann_chen
- **日期**: 2022-05-20 14:08:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 191. [Web]A00-20220517004 修正調離職人員帳號更新排程預設出貨端口改成8086
- **Commit ID**: `d2588982d05156ee7d73b94fd6796fb2c74ec775`
- **作者**: 林致帆
- **日期**: 2022-05-19 17:04:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaJobs.xml`

### 192. [內部]Q00-20220509001修正設定檔ESS內網IP敘述的Oracle指令有誤
- **Commit ID**: `d8c4aa3f68bc813e3f9153809738802f6159904e`
- **作者**: 林致帆
- **日期**: 2022-05-09 08:55:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 193. [流程引擎]A00-20220513001 修正ProcessMapping在Oracle遺漏attachInfo欄位導致拋單失敗
- **Commit ID**: `5cabb6a193ce4d7930008bd9863bf345c2e22b34`
- **作者**: 林致帆
- **日期**: 2022-05-13 17:23:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 194. [內部]A00-20220517002 修正DB為 Oracle時，產品授權畫面無顯示模組名稱
- **Commit ID**: `36049cb4b8d31b5684bcc014bc16f8b2c192f97f`
- **作者**: wayne
- **日期**: 2022-05-17 11:32:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

