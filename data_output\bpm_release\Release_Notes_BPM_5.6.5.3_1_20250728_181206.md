# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.5.3_1
- **舊版本**: 5.6.5.3
- **生成時間**: 2025-07-28 18:12:06
- **新增 Commit 數量**: 33

## 變更摘要

### Chin<PERSON>ong (9 commits)

- **2018-04-17 10:42:57**: 修正鼎捷移動session already invalided 問題
  - 變更檔案: 2 個
- **2018-04-11 15:59:45**: 修正升版後統計元件的顏色還是彩色的問題
  - 變更檔案: 2 個
- **2018-04-10 17:22:31**: C01-*********** 微信帳號的搜索功能失效
  - 變更檔案: 6 個
- **2018-03-31 11:40:11**: 調整入口平台設定頁面jsp，增加class類別
  - 變更檔案: 12 個
- **2018-03-31 10:23:38**: Q00-*********** 修正入口平台頁面鼎捷使用者與微信使用者頁面取得的資料異常。
  - 變更檔案: 9 個
- **2018-03-30 14:32:20**: A00-20180329002 新增行動版Grid reload方法
  - 變更檔案: 1 個
- **2018-03-28 18:18:48**: 修正表單中間層錯誤
  - 變更檔案: 1 個
- **2018-03-28 15:24:10**: 修正表單中間層在表單有多欄位時只會截取右邊的欄位的問題
  - 變更檔案: 1 個
- **2018-03-26 10:32:37**: 修正APP議題
  - 變更檔案: 4 個

### pinchi_lin (4 commits)

- **2018-04-17 09:14:28**: 修正form-Builder中，產生BPMAPP表單元件缺少Password類型
  - 變更檔案: 1 個
- **2018-04-10 15:03:58**: C01-20180409002 修正BPMAPP向後加簽多語系錯誤
  - 變更檔案: 2 個
- **2018-03-29 14:55:31**: A00-20180320001 修正TT流程再中間層簽核時會派送失敗問題
  - 變更檔案: 1 個
- **2018-03-23 10:20:27**: 修正絕對位置grid表單元件亂跑問題
  - 變更檔案: 2 個

### yamiyeh10 (6 commits)

- **2018-04-16 17:25:50**: A00-20180409001 修正客製開窗在字數過多時會產生重疊問題
  - 變更檔案: 1 個
- **2018-04-11 11:07:29**: 修正表單畫面多語系問題
  - 變更檔案: 5 個
- **2018-03-31 15:18:23**: Q00-20180330003 修正測試模擬使用者開窗異常議題
  - 變更檔案: 1 個
- **2018-03-31 13:46:55**: C01-20180320001 修正app發起流程畫面最後一行顯示一半
  - 變更檔案: 2 個
- **2018-03-23 12:40:18**: 補上鼎捷移動電話簿SQL語法
  - 變更檔案: 2 個
- **2018-03-22 23:43:29**: 修正議題 1.入口平台微信無法編輯 2.行動表單開啟異常 3.加簽畫面跑版 4.絕對位置表單日期時間元件樣式變一條線 5.行動表單閒置15分鐘不會登出 6.微信推播網址錯誤 7.sql語法預設資料庫代號統一為BPMSQL
  - 變更檔案: 17 個

### 顏伸儒 (4 commits)

- **2018-04-16 17:15:22**: [A00-20180410003]修正流程簽核掛外部連結畫面,使用Chrome瀏覽器無法正常派送流程的問題。
  - 變更檔案: 1 個
- **2018-04-13 16:22:46**: [A00-20180410003]此議題先還原 ->修正外部連結畫面使用Chrome瀏覽器無法正常派送流程的問題。
  - 變更檔案: 1 個
- **2018-04-13 10:03:25**: [A00-20180410003]修正外部連結畫面使用Chrome瀏覽器無法正常派送流程的問題。
  - 變更檔案: 1 個
- **2018-03-28 09:32:28**: [A00-***********]修正組織設計師部門主管無法正常顯示的問題。
  - 變更檔案: 8 個

### 治傑 (3 commits)

- **2018-04-13 18:11:57**: A00-20180410001 修正自帶小數點保留功能時,表單沒有顯示問題
  - 變更檔案: 1 個
- **2018-04-11 14:15:36**: 修正將流程設定為不支援行動版，在行動版的常用發起仍然可以看到該流程問題
  - 變更檔案: 1 個
- **2018-03-23 14:20:33**: 修正鼎捷移動多組織簽核失敗問題
  - 變更檔案: 2 個

### 張詠威 (3 commits)

- **2018-04-12 18:15:17**: A00-20180412009 協助commit 修正在退回重辦需要逐級通知的前提下當流程關卡包含核決關卡
  - 變更檔案: 1 個
- **2018-04-10 16:26:35**: 取消開窗限制500筆資料的限制(因為會導致客戶原有的script異常)-V57統一調整
  - 變更檔案: 1 個
- **2018-04-10 16:19:09**: Q00-20180410001 修正檔案總管新增文件異常 及 ISO檔案下載失敗
  - 變更檔案: 4 個

### 施廷緯 (4 commits)

- **2018-03-29 08:39:45**: 廷緯 修正進階查詢及表單設計師Time開窗偏移問題。
  - 變更檔案: 1 個
- **2018-03-26 09:42:28**: 修正進階查詢搜尋流程發起人失效問題(程式錯誤修正)
  - 變更檔案: 3 個
- **2018-03-23 16:22:16**: 2018/03/23 廷緯 修改進階查詢開窗會偏移問題。
  - 變更檔案: 1 個
- **2018-03-22 16:35:38**: 20180322 廷緯 調整(模糊查詢)追蹤流程頁面的進階查詢流程發起人無法正確搜尋的問題。
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. 修正鼎捷移動session already invalided 問題
- **Commit ID**: `1d0663b1ed460bb27b69d632d24c40dc7703ceb1`
- **作者**: ChinRong
- **日期**: 2018-04-17 10:42:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java`

### 2. 修正form-Builder中，產生BPMAPP表單元件缺少Password類型
- **Commit ID**: `6b322e4eebf2471cd5310bbfec242741d0579554`
- **作者**: pinchi_lin
- **日期**: 2018-04-17 09:14:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`

### 3. A00-20180409001 修正客製開窗在字數過多時會產生重疊問題
- **Commit ID**: `b985b83950c2916f95dee48b9b3f6d974d035e6d`
- **作者**: yamiyeh10
- **日期**: 2018-04-16 17:25:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 4. [A00-20180410003]修正流程簽核掛外部連結畫面,使用Chrome瀏覽器無法正常派送流程的問題。
- **Commit ID**: `103b376309ff0d09bce60f7fef4d719af204b85c`
- **作者**: 顏伸儒
- **日期**: 2018-04-16 17:15:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 5. A00-20180410001 修正自帶小數點保留功能時,表單沒有顯示問題
- **Commit ID**: `2a1094f9c95ddbfcecfa25447bcc99d27124d264`
- **作者**: 治傑
- **日期**: 2018-04-13 18:11:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 6. [A00-20180410003]此議題先還原 ->修正外部連結畫面使用Chrome瀏覽器無法正常派送流程的問題。
- **Commit ID**: `807326b09ed19391e305f317d2e1f550ff4bcada`
- **作者**: 顏伸儒
- **日期**: 2018-04-13 16:22:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 7. [A00-20180410003]修正外部連結畫面使用Chrome瀏覽器無法正常派送流程的問題。
- **Commit ID**: `2eb6a080cdcb6cfc5ad50820d8cdd11680a041af`
- **作者**: 顏伸儒
- **日期**: 2018-04-13 10:03:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 8. A00-20180412009 協助commit 修正在退回重辦需要逐級通知的前提下當流程關卡包含核決關卡
- **Commit ID**: `f6afec66cc4098dcd860cd7bae4de67fce5d9960`
- **作者**: 張詠威
- **日期**: 2018-04-12 18:15:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 9. 修正升版後統計元件的顏色還是彩色的問題
- **Commit ID**: `15831eda74ae21e65ef5a8964df9bbc9e0461663`
- **作者**: ChinRong
- **日期**: 2018-04-11 15:59:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 10. 修正將流程設定為不支援行動版，在行動版的常用發起仍然可以看到該流程問題
- **Commit ID**: `0f3f012a94a7f4c2cc237c0c3731f97e302dd1aa`
- **作者**: 治傑
- **日期**: 2018-04-11 14:15:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java`

### 11. 修正表單畫面多語系問題
- **Commit ID**: `10f09fe003bf775d5cd7599e1c31c74edfbd2f81`
- **作者**: yamiyeh10
- **日期**: 2018-04-11 11:07:29
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`

### 12. C01-*********** 微信帳號的搜索功能失效
- **Commit ID**: `d296bd5cfc9fdf96fdc871f4e0dfc3d26e59791a`
- **作者**: ChinRong
- **日期**: 2018-04-10 17:22:31
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp`

### 13. 取消開窗限制500筆資料的限制(因為會導致客戶原有的script異常)-V57統一調整
- **Commit ID**: `14b6e477459223f532b6ad2e8ddf5335b0d54b8a`
- **作者**: 張詠威
- **日期**: 2018-04-10 16:26:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 14. Q00-20180410001 修正檔案總管新增文件異常 及 ISO檔案下載失敗
- **Commit ID**: `f509a864305aa14a390d6319c30f0efa1c2ec8ce`
- **作者**: 張詠威
- **日期**: 2018-04-10 16:19:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOInv001.form`

### 15. C01-20180409002 修正BPMAPP向後加簽多語系錯誤
- **Commit ID**: `88b47c2e572544a760eb184fc216e32d3aca3b05`
- **作者**: pinchi_lin
- **日期**: 2018-04-10 15:03:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5653.xls`

### 16. Q00-20180330003 修正測試模擬使用者開窗異常議題
- **Commit ID**: `2f573b59ccd0cbb481acc1bc9c053f8bf41902fa`
- **作者**: yamiyeh10
- **日期**: 2018-03-31 15:18:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 17. C01-20180320001 修正app發起流程畫面最後一行顯示一半
- **Commit ID**: `cd80f13831f4a482117f12e33efcfda3309c36df`
- **作者**: yamiyeh10
- **日期**: 2018-03-31 13:46:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`

### 18. 調整入口平台設定頁面jsp，增加class類別
- **Commit ID**: `2d5d1f16b0fb715717d3dc9e76ad96177701e147`
- **作者**: ChinRong
- **日期**: 2018-03-31 11:40:11
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeploy.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 19. Q00-*********** 修正入口平台頁面鼎捷使用者與微信使用者頁面取得的資料異常。
- **Commit ID**: `47b81e2b5cd7ac97629e6c11924d09e44f023d38`
- **作者**: ChinRong
- **日期**: 2018-03-31 10:23:38
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 20. A00-20180329002 新增行動版Grid reload方法
- **Commit ID**: `32ec7b46761bb03b6bb0393cdebe4bfaf279e99e`
- **作者**: ChinRong
- **日期**: 2018-03-30 14:32:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 21. A00-20180320001 修正TT流程再中間層簽核時會派送失敗問題
- **Commit ID**: `34076ed722d3627336af6900331b8f2147e35a0e`
- **作者**: pinchi_lin
- **日期**: 2018-03-29 14:55:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 22. 廷緯 修正進階查詢及表單設計師Time開窗偏移問題。
- **Commit ID**: `6034dacf4e693f646d285f9920bbdbea997278db`
- **作者**: 施廷緯
- **日期**: 2018-03-29 08:39:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/popup.js`

### 23. 修正表單中間層錯誤
- **Commit ID**: `f25617312591475d4570a6619d2ef440d0bff4b1`
- **作者**: ChinRong
- **日期**: 2018-03-28 18:18:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 24. 修正表單中間層在表單有多欄位時只會截取右邊的欄位的問題
- **Commit ID**: `c8c71e35e9e88b2d14c3a3d80e1f99e72651b2e6`
- **作者**: ChinRong
- **日期**: 2018-03-28 15:24:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 25. [A00-***********]修正組織設計師部門主管無法正常顯示的問題。
- **Commit ID**: `dc06cb9104f63e903364a8da850b3dfbf8d8b430`
- **作者**: 顏伸儒
- **日期**: 2018-03-28 09:32:28
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/AddUserToUnitDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/table/UserTableController.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 26. 修正APP議題
- **Commit ID**: `524c0ccb7fdb824f5fe52226de3ea4019dfff29e`
- **作者**: ChinRong
- **日期**: 2018-03-26 10:32:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`

### 27. 修正進階查詢搜尋流程發起人失效問題(程式錯誤修正)
- **Commit ID**: `be736049b01b0600c6760fd095b0391f75f949ba`
- **作者**: 施廷緯
- **日期**: 2018-03-26 09:42:28
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 28. 2018/03/23 廷緯 修改進階查詢開窗會偏移問題。
- **Commit ID**: `68f868fced7850091ace7ffece099cdd8e81dae7`
- **作者**: 施廷緯
- **日期**: 2018-03-23 16:22:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/popup.js`

### 29. 修正鼎捷移動多組織簽核失敗問題
- **Commit ID**: `b28b898630d7699441311e7253e49cb6378df275`
- **作者**: 治傑
- **日期**: 2018-03-23 14:20:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 30. 補上鼎捷移動電話簿SQL語法
- **Commit ID**: `c057c1e423ffe48beca660b80fb2da46198156f9`
- **作者**: yamiyeh10
- **日期**: 2018-03-23 12:40:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`

### 31. 修正絕對位置grid表單元件亂跑問題
- **Commit ID**: `67fd38dc5081fcff86f82bb3865e7df9f7814856`
- **作者**: pinchi_lin
- **日期**: 2018-03-23 10:20:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 32. 修正議題 1.入口平台微信無法編輯 2.行動表單開啟異常 3.加簽畫面跑版 4.絕對位置表單日期時間元件樣式變一條線 5.行動表單閒置15分鐘不會登出 6.微信推播網址錯誤 7.sql語法預設資料庫代號統一為BPMSQL
- **Commit ID**: `7afe3a3217486dcbe675b9c6d217f3932624e733`
- **作者**: yamiyeh10
- **日期**: 2018-03-22 23:43:29
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_SQLServer.sql`

### 33. 20180322 廷緯 調整(模糊查詢)追蹤流程頁面的進階查詢流程發起人無法正確搜尋的問題。
- **Commit ID**: `d6eb44619cdfabd50e1baf2417e45b095faa0564`
- **作者**: 施廷緯
- **日期**: 2018-03-22 16:35:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

