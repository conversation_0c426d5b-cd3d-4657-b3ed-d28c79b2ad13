# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.2.1
- **舊版本**: 5.6.1.1
- **生成時間**: 2025-07-28 18:22:19
- **新增 Commit 數量**: 163

## 變更摘要

### <PERSON>ne (44 commits)

- **2016-12-19 11:15:02**: 取消  (ireport)JBOSS HOME\NaNa\lib\Ext裡缺少jasperreports-4.1.3.jar之jar檔，因此無法利用已建立好的ireport表單轉成pdf檔。
  - 變更檔案: 1 個
- **2016-12-19 10:48:05**: Q00-20161215002  (ireport)JBOSS HOME\NaNa\lib\Ext裡缺少jasperreports-4.1.3.jar之jar檔，因此無法利用已建立好的ireport表單轉成pdf檔。
  - 變更檔案: 1 個
- **2016-12-16 12:02:43**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-16 12:00:41**: 調整模糊查詢index
  - 變更檔案: 1 個
- **2016-12-14 17:34:16**: 調整建立index 語法
  - 變更檔案: 3 個
- **2016-12-14 16:46:56**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-14 16:46:31**: 調整vip人員的update sql
  - 變更檔案: 1 個
- **2016-12-12 15:10:12**: 修正 NaNaIntsys.properties中的MCLOUD WSDL的網址預設值有誤
  - 變更檔案: 1 個
- **2016-12-12 11:20:46**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-12 10:47:48**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-09 10:28:42**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-09 10:26:19**: 修正核決權限關卡退回重辦時，無法退回重瓣
  - 變更檔案: 1 個
- **2016-12-08 11:14:02**: 更新5621的patch(自動安裝5611版本及update5621SQL)
  - 變更檔案: 1 個
- **2016-12-07 14:24:26**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-02 14:25:31**: 取回重瓣時在 簽核意見上加註取回人員
  - 變更檔案: 1 個
- **2016-12-01 14:33:38**: mail樣式 增加員工工號
  - 變更檔案: 1 個
- **2016-12-01 09:46:43**: 信件的簽核歷程的顯示，加上處理狀態
  - 變更檔案: 1 個
- **2016-11-30 18:24:34**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-30 18:23:43**: [管理流程] 以administrator 權限登入時，可直接刪除流程，增加log紀錄
  - 變更檔案: 1 個
- **2016-11-30 10:49:53**: A00-20161121001 修正手機簽核程式出現部分表單簽核歷程無法顯示
  - 變更檔案: 1 個
- **2016-11-30 10:29:04**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-30 10:27:47**: [Q]PDF Viewers 與 黑名單的處理機制須變更規格
  - 變更檔案: 1 個
- **2016-11-30 10:22:26**: Q00-20161013001 PDF Viewers 與 黑名單的處理機制變更規格
  - 變更檔案: 2 個
- **2016-11-28 16:22:22**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-28 16:21:48**: 自動簽核跳關時，將待辦通知信連結更改為追蹤通知信。並取消mail圖示
  - 變更檔案: 6 個
- **2016-11-23 14:24:17**: 登入時，檢核所使用的瀏覽器為可支援的版本
  - 變更檔案: 1 個
- **2016-11-23 11:17:34**: 優化listerReader綁訂變數及調整SQLnoLock
  - 變更檔案: 7 個
- **2016-11-22 18:04:56**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-22 17:52:10**: 調整oracle Create時發生錯誤
  - 變更檔案: 2 個
- **2016-11-21 11:14:32**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-21 11:12:58**: 修正人員開窗輸入條件時，無法帶出畫面  Q00-20161121001
  - 變更檔案: 1 個
- **2016-11-16 16:43:09**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-16 16:42:19**: 調整oracle的create SQL
  - 變更檔案: 1 個
- **2016-11-15 17:48:40**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-15 17:47:36**: 調整License 不足的信件內容
  - 變更檔案: 1 個
- **2016-11-15 11:39:12**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-15 11:37:30**: 建立 ParticipantDefinition 的 participantType、employeeId index
  - 變更檔案: 4 個
- **2016-11-15 10:39:14**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 2 個
- **2016-11-15 10:38:30**: 將離職員工維護作業開放給一般使用者-增加員工資訊
  - 變更檔案: 15 個
- **2016-11-08 13:40:25**: 列印表單時，textbox增加border
  - 變更檔案: 1 個
- **2016-10-27 10:04:04**: [內部]將離職員工維護作業開放給一般使用者
  - 變更檔案: 1 個
- **2016-10-26 17:34:51**: [內部]將匯入匯出系統多語系開放給一般使用者
  - 變更檔案: 19 個
- **2016-10-24 14:57:01**: [內部]將離職員工維護作業開放給一般使用者
  - 變更檔案: 8 個
- **2016-10-24 14:43:22**: [內部]將轉派員工的工作開放給一般使用者
  - 變更檔案: 5 個

### Gaspard (8 commits)

- **2016-12-19 10:22:45**: 修正TextArea元件無型態選項
  - 變更檔案: 5 個
- **2016-12-19 10:22:27**: 修正多選開窗回傳值第一欄為流水號
  - 變更檔案: 1 個
- **2016-12-07 09:59:27**: 修正上傳表單時，若表單檔名為中文時，會跳出驗證錯誤，不再繼續將表單丟到後端解析取得表單代號
  - 變更檔案: 2 個
- **2016-11-30 06:37:43**: Q00-20161130004 修正自訂使用產品紅框的錯誤提示方式時，寫在onblur事件會失效
  - 變更檔案: 1 個
- **2016-11-30 06:36:23**: Q00-20161130002 Q00-20161130003 無法修改元件代號 1.修正當畫面有DB Connection或SQL Command元件時，無法修改元件代號 2.修正無法修改Label元件的代號
  - 變更檔案: 1 個
- **2016-11-15 11:35:44**: 修正表單設計師的上傳表單功能，表單代號預設抓取檔案名稱，修改為將上傳的表單先丟到後端解析出表單代號，將其當作新的表單代號預設值
  - 變更檔案: 3 個
- **2016-11-11 10:05:10**: 修正資料選取器JS檔與SAP的JS檔衝突，故修改載入順序
  - 變更檔案: 1 個
- **2016-11-11 10:03:18**: 修正多語系未顯示問題與無法塞入預設值
  - 變更檔案: 1 個

### WenCheng (7 commits)

- **2016-12-16 20:45:40**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-16 20:44:32**: Q00-20161216001 [內部]調整離職人員於離職日當天仍可登入操作使用。
  - 變更檔案: 1 個
- **2016-12-15 13:52:48**: S00-20161215001 HR小助手組織同步功能增加可從設定檔中調整設定根節點組織ID
  - 變更檔案: 6 個
- **2016-12-15 12:16:43**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-15 12:15:35**: Q00-20161215002 組織設計師設定跨組織的部門兼職，在部門中的清單資料會重覆呈現問題已修正。
  - 變更檔案: 1 個
- **2016-11-11 16:50:21**: A00-20161101002 [欣興]修正一般使用者使用「管理流程」功能時，點按「轉出Excel」功能，其資料內容「處理者」欄位為空問題。
  - 變更檔案: 1 個
- **2016-11-09 10:48:32**: A00-20161109001 T100單據中有附件，執行BPM流程發起時，出現DocManagerFactoryLocal not bound
  - 變更檔案: 1 個

### LALA (32 commits)

- **2016-12-16 10:53:54**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-16 10:53:06**: Q00-20161216001[內部]pdfViewer套件更新
  - 變更檔案: 2 個
- **2016-12-13 17:32:19**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-13 17:31:50**: Q00-20161213002[內部]修正卡關處理者為群組時，解析處理者失敗。
  - 變更檔案: 1 個
- **2016-12-13 16:10:08**: Q00-20161213001[內部]待辦、通知等bagBadgeNumber超過1000則顯示999+
  - 變更檔案: 1 個
- **2016-12-13 16:08:46**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-13 16:07:34**: Q00-20161213001[內部]待辦、通知等BadgeNumber
  - 變更檔案: 1 個
- **2016-12-09 11:52:00**: Q00-20161209001[欣興]修正pdfViewer顯示pdf檔中的圖片會模糊
  - 變更檔案: 1 個
- **2016-12-08 17:17:46**: A00-20161208001[巨輪]修正編輯預設排程，會額外新增一筆新的排程。
  - 變更檔案: 1 個
- **2016-12-07 17:40:09**: C01-20161004004[欣興]文件在變更申請中、變更中、作廢申請中、已作廢等狀態下，變更與作廢單不允許申請。
  - 變更檔案: 3 個
- **2016-12-06 18:28:50**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-06 18:28:04**: C01-20161102001[台灣之星]多AP一DB情況，新增與修改人員時，一併呼叫其他台主機更新UserCache，組織同步則是同步完後，才進行initUserCache。
  - 變更檔案: 14 個
- **2016-12-06 14:57:16**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-06 14:51:09**: C01-20160810001[欣興]ISO確認變更Invoke狀態無變更問題
  - 變更檔案: 3 個
- **2016-11-25 18:01:13**: Q00-20161125001[內部]追蹤流程自訂日期範圍都填空，日期顯示異常
  - 變更檔案: 1 個
- **2016-11-21 15:29:14**: A00-20160919001[創元]修正ISO文件型態階層，多語系少内容。
  - 變更檔案: 2 個
- **2016-11-21 15:18:12**: A00-20160919001[創元]修正ISO文件型態階層，多語系少内容。 20161121 Shih-Yun 06394 1.修正多餘code。
  - 變更檔案: 1 個
- **2016-11-21 15:13:09**: A00-20160919001[創元]修正ISO文件型態階層，多語系少内容。
  - 變更檔案: 7 個
- **2016-11-18 16:56:33**: C01-20161004001[欣興]ISO文件編號有特殊符號的限制，在新增文件時去判斷並提醒使用者不的有特殊字元
  - 變更檔案: 1 個
- **2016-11-18 15:24:11**: A00-20160912001[驊訊]修正工作日期會計算特定放假日但不會計算特定上班工作日的問題。
  - 變更檔案: 1 個
- **2016-11-17 17:47:53**: A00-20160824002[尚至]修正變數"工作收受託者"應從Workitem找出Assignee，而不是收件人
  - 變更檔案: 12 個
- **2016-11-15 17:15:01**: A00-20160824002[尚至]修正變數"工作收受託者"應從Workitem找出Assignee，而不是收件人
  - 變更檔案: 2 個
- **2016-11-15 10:26:39**: C01-20161004001[欣興]ISO文件編號有特殊符號的限制，在新增文件時去判斷並提醒使用者不的有特殊字元
  - 變更檔案: 1 個
- **2016-11-10 17:46:29**: Q00-20161110001[內部]修正ISO文件「保存年限」為「保存到期日」減去「文件生效日」
  - 變更檔案: 3 個
- **2016-11-10 11:40:43**: C01-20161004001[欣興]ISO文件編號有特殊符號的限制，在新增文件時去判斷並提醒使用者不的有特殊字元
  - 變更檔案: 5 個
- **2016-11-09 14:42:24**: C01-20161020001[地樺]修正授權的流程效能調整
  - 變更檔案: 1 個
- **2016-11-07 14:08:33**: C01-20161107002[欣興]修正無論是否轉PDF，只要索引失敗(ISOFile.isIndexed=0)的檔案皆列出
  - 變更檔案: 1 個
- **2016-11-04 17:15:07**: Q00-20161104001[內部]修正待辦通知信件的簡易簽核URL有誤
  - 變更檔案: 1 個
- **2016-11-04 14:10:22**: C01-20160825003[欣興]修正ISO文件編號有特殊符號開啟檔案會報錯
  - 變更檔案: 3 個
- **2016-11-02 17:40:59**: C01-20161004002[欣興]修正簡易查詢功能文件編號及文件名稱可搜尋特殊符號
  - 變更檔案: 2 個
- **2016-11-02 16:35:28**: C01-20161005002[欣興]確認ISO變更時版號重覆則不更新
  - 變更檔案: 1 個
- **2016-11-02 16:01:58**: A00-20161005001[欣興]文件變更狀態應不影響報表記錄的查詢
  - 變更檔案: 1 個

### yylee1123 (16 commits)

- **2016-12-15 11:52:47**: 加上請分段執行的註解
  - 變更檔案: 1 個
- **2016-12-14 18:02:56**: 修正SQL錯誤
  - 變更檔案: 5 個
- **2016-12-14 11:48:51**: 修正web表單設計師編輯選項時，按enter鍵的問題
  - 變更檔案: 1 個
- **2016-12-05 11:39:36**: [Q00-20150729005] 修正組織設計師更新人員資料時，姓名多語系Cache的內容
  - 變更檔案: 2 個
- **2016-12-01 14:35:17**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-01 14:34:39**: [Q00-20150610008] 文件總管-新增文件，文件編號超過30字，不可新增
  - 變更檔案: 1 個
- **2016-11-30 10:08:25**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-30 10:07:16**: [A00-20161115001]將FavoriteProcess的processID欄位長度放寬到100，與ProcessPackage的id相同
  - 變更檔案: 4 個
- **2016-11-29 17:04:27**: 更改RsrcBundle檔名的版號
  - 變更檔案: 1 個
- **2016-11-29 16:54:03**: 更改updateSQL檔名的版號
  - 變更檔案: 4 個
- **2016-11-29 16:18:34**: S00-***********、S00-***********、Q00-***********、Q00-***********、Q00-***********、Q00-***********
  - 變更檔案: 105 個
- **2016-11-01 11:55:15**: [A00-20161028001]新增取使用者名稱多語系的方法，避免找不到tUserNameMap或沒有傳入 Locale
  - 變更檔案: 23 個
- **2016-10-28 16:38:13**: [Q00-20161028001]修正模組程式維護的程式連線URL含有&、<、>，儲存後變成&amp;、&lt;、&gt;的問題
  - 變更檔案: 2 個
- **2016-10-21 17:23:24**: [S00-20161019001]管理片語增加更新時間
  - 變更檔案: 15 個
- **2016-10-21 17:16:13**: [S00-20161004007]流程模組維護增加更新人員OID、時間
  - 變更檔案: 12 個
- **2016-10-21 17:01:35**: [S00-20161017001]系統排程增加更新人員OID、時間(記錄在/NaNa/conf/NaNaJobs.xml)
  - 變更檔案: 2 個

### jerry1218 (16 commits)

- **2016-12-14 16:40:55**: A00-20161214002 T100整合-修正當webServerAddress為非IP+PORT , 建立簽核流會失敗問題
  - 變更檔案: 1 個
- **2016-12-14 15:03:41**: A00-20161214001 修正特定表單grid會因itemOrder都為0,導致儲存表單後grid順序及資料錯亂問題
  - 變更檔案: 2 個
- **2016-12-12 17:54:38**: Q00-20161212001 JSP多與細微調
  - 變更檔案: 1 個
- **2016-12-12 17:50:29**: Q00-20161212001 T100整合-從E-mail簽核，無法顯示自動確認失敗的原因
  - 變更檔案: 2 個
- **2016-12-07 17:02:13**: T100整合-FormFlowCreate服務修改邏輯 , 原本建流程途中出現exception會較exception擋下後response失敗的XML給T100,但此舉會造成流程有些資料無法roolback ,故修改將exception throw出 , 另增加建流程必要參數防呆
  - 變更檔案: 1 個
- **2016-12-06 14:32:11**: S00-*********** 功能微調-拿掉system.out.println
  - 變更檔案: 1 個
- **2016-12-05 15:21:09**: T100整合新增表單-aapt110,afmt015,anmt310
  - 變更檔案: 3 個
- **2016-12-02 14:55:04**: S00-*********** 功能微調
  - 變更檔案: 3 個
- **2016-12-01 16:11:37**: S00-*********** T100&BPM，簡易測通二邊服務的功能，於[整合系統設定]頁面加上測試按鈕，測試是否可以成功連接T100 JBOSS
  - 變更檔案: 7 個
- **2016-11-30 16:09:05**: C01-20161103001 修正T100抽單失敗後 , 如果再去BPM撤銷 , 會造成沒有呼叫T100即成功撤銷問題
  - 變更檔案: 1 個
- **2016-11-22 15:56:25**: A00-*********** 修正ProcessInstanceService webservice取得簽核意見,如果關卡撤銷時無顯示撤銷人問題
  - 變更檔案: 2 個
- **2016-11-21 16:16:22**: S00-20161117001 ApproveLogGet(取得簽核流程紀錄)需求變更 1.有簽核動作的關卡(同意、抽單、中止流程)，才需要傳給T100 2.撤銷，關卡補上 "處理者"
  - 變更檔案: 1 個
- **2016-11-16 17:38:39**: Q00-20161116002 修正T100整合ProcessListGet服務所給的URL , 使用chrome開啟會是亂碼問題
  - 變更檔案: 1 個
- **2016-10-31 17:41:02**: S00-**********2 修改T100整合邏輯 , 於簽核中察看T100單據功能 , 阻擋IE10以下IE版本
  - 變更檔案: 1 個
- **2016-10-31 17:39:01**: S00-**********2 修改T100整合邏輯 , 於簽核中察看T100單據功能 , 阻擋IE10以下IE版本
  - 變更檔案: 2 個
- **2016-10-31 10:43:40**: A00-20161028002 T100整合-修正T100抽單失敗後 , 如果在去BPM撤銷 , 會造成沒有呼叫T100即成功撤銷問題
  - 變更檔案: 1 個

### Joe (23 commits)

- **2016-12-14 13:42:48**: Q00-20161214001 APP修正取待辦列表資料問題
  - 變更檔案: 1 個
- **2016-12-14 10:51:07**: Q00-20161214002 修正APP終止流程無反應問題
  - 變更檔案: 1 個
- **2016-12-12 11:07:37**: BPM APP 新UI第二階段 畫面修改
  - 變更檔案: 1 個
- **2016-12-09 15:50:28**: BPM APP新UI第二階段 ICON修改
  - 變更檔案: 11 個
- **2016-12-09 09:19:22**: APP GRID 相關程式修正
  - 變更檔案: 1 個
- **2016-12-08 16:23:12**: 修正讓APP可以正常顯示PC版GRID元件
  - 變更檔案: 2 個
- **2016-12-08 14:27:30**: 修正APP列表會取回失敗問題
  - 變更檔案: 1 個
- **2016-12-08 09:02:40**: APP新UI第二階段 退回畫面修正
  - 變更檔案: 1 個
- **2016-12-07 18:57:01**: BPM APP新UI 補上缺漏的圖片
  - 變更檔案: 3 個
- **2016-12-07 16:47:25**: 新UI第二階段 流程列表過濾修正
  - 變更檔案: 6 個
- **2016-12-07 14:25:30**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-07 14:25:10**: APP新UI 簽核歷程畫面修正
  - 變更檔案: 1 個
- **2016-12-06 17:49:15**: BPM APP新UI第二階段 功能修正
  - 變更檔案: 6 個
- **2016-12-06 15:40:29**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-06 15:39:04**: BPM APP 新UI 第二階段
  - 變更檔案: 37 個
- **2016-11-29 14:58:03**: BPM APP 新增後端取得流程預設主旨功能
  - 變更檔案: 5 個
- **2016-11-22 17:05:50**: Q00-20161101001 追蹤流程上傳附件異常修正(追蹤流程及工作通知不應可以上傳附件，直接停用)
  - 變更檔案: 2 個
- **2016-11-22 16:21:51**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-11-22 15:56:40**: Q00-20161116005 流程追蹤-已發起:撤銷原因偵測修正
  - 變更檔案: 2 個
- **2016-11-22 14:02:38**: Q00-20161116008 APP GRID手勢切換功能修正
  - 變更檔案: 1 個
- **2016-11-22 13:56:42**: Q00-20161116006 APP GRID畫面在小螢幕手機異常修正
  - 變更檔案: 1 個
- **2016-11-22 11:11:18**: Q00-20161116010 補上APP GRID缺漏的多語系檔
  - 變更檔案: 2 個
- **2016-11-04 09:47:54**: 程式說明註解修改，避免混淆
  - 變更檔案: 1 個

### jd (12 commits)

- **2016-12-13 17:17:02**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-13 17:14:37**: 修正公告编辑异常
  - 變更檔案: 4 個
- **2016-12-12 10:37:32**: 修正菜單樣式
  - 變更檔案: 3 個
- **2016-12-09 16:10:48**: 修正表单发布公告功能和函式库
  - 變更檔案: 4 個
- **2016-12-08 14:05:40**: 修正App表單在IOS上字體會忽大忽小問題
  - 變更檔案: 5 個
- **2016-12-07 11:46:46**: BPM App New UI
  - 變更檔案: 6 個
- **2016-11-10 11:05:21**: [BPM App]新UI第二階段
  - 變更檔案: 2 個
- **2016-11-10 11:00:44**: Merge remote-tracking branch 'origin/develop' into develop
  - 變更檔案: 7 個
- **2016-11-10 10:59:25**: [BPM App]新第二階段修正
  - 變更檔案: 9 個
- **2016-10-13 11:07:00**: 20161013,v5611紧急修正
  - 變更檔案: 1 個
- **2016-10-07 16:29:49**: Merge remote-tracking branch 'origin/develop' into develop
  - 變更檔案: 2 個
- **2016-10-07 16:09:21**: 新UI第二階段議題修正
  - 變更檔案: 2 個

### pinchi_lin (2 commits)

- **2016-12-09 14:33:33**: 修正"等待微信驗證中"缺少的多語系
  - 變更檔案: 3 個
- **2016-11-15 18:39:47**: 修正公告系統遺漏SQL
  - 變更檔案: 2 個

### loren (3 commits)

- **2016-12-07 12:23:00**: 增加Java Web Start(Jnlp)程式相容Java 8 > 移至ProgramDefinition
  - 變更檔案: 6 個
- **2016-12-05 11:13:10**: 增加Java Web Start(Jnlp)程式相容Java 8 > 合併分支衝突檔
  - 變更檔案: 3 個
- **2016-12-05 09:50:05**: 增加Java Web Start(Jnlp)程式相容Java 8
  - 變更檔案: 142 個

## 詳細變更記錄

### 1. 取消  (ireport)JBOSS HOME\NaNa\lib\Ext裡缺少jasperreports-4.1.3.jar之jar檔，因此無法利用已建立好的ireport表單轉成pdf檔。
- **Commit ID**: `3449c8f31c58a4f9552dfa5f9103e606e5d39b83`
- **作者**: wayne
- **日期**: 2016-12-19 11:15:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/NaNa/lib/Ext/jasperreports-4.1.3.jar`

### 2. Q00-20161215002  (ireport)JBOSS HOME\NaNa\lib\Ext裡缺少jasperreports-4.1.3.jar之jar檔，因此無法利用已建立好的ireport表單轉成pdf檔。
- **Commit ID**: `01edba44bbf332c8e9e99c969aeaeb66c8bc5d29`
- **作者**: wayne
- **日期**: 2016-12-19 10:48:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/lib/Ext/jasperreports-4.1.3.jar`

### 3. 修正TextArea元件無型態選項
- **Commit ID**: `99df449ef7badcfa6cee7d702b07e37741baaff6`
- **作者**: Gaspard
- **日期**: 2016-12-19 10:22:45
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5621.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`

### 4. 修正多選開窗回傳值第一欄為流水號
- **Commit ID**: `a83aead101bc7768a07095d52d8c7104638bf953`
- **作者**: Gaspard
- **日期**: 2016-12-19 10:22:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 5. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `cd90efff719d51f3b45e9563f036d191e676568c`
- **作者**: WenCheng
- **日期**: 2016-12-16 20:45:40
- **變更檔案數量**: 0

### 6. Q00-20161216001 [內部]調整離職人員於離職日當天仍可登入操作使用。
- **Commit ID**: `11687f36a8a376a9664d1bb23c9e2ca0986a8016`
- **作者**: WenCheng
- **日期**: 2016-12-16 20:44:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`

### 7. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `0639d08bab31c277ee3c691ca845977e728c6383`
- **作者**: wayne
- **日期**: 2016-12-16 12:02:43
- **變更檔案數量**: 0

### 8. 調整模糊查詢index
- **Commit ID**: `7a81dcae0870a457e109d3e4175fe473246f85bb`
- **作者**: wayne
- **日期**: 2016-12-16 12:00:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5602.xls`

### 9. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `fd39f1d773a2c9a64e5e01eaaa40c4aa2e1bcaef`
- **作者**: LALA
- **日期**: 2016-12-16 10:53:54
- **變更檔案數量**: 0

### 10. Q00-20161216001[內部]pdfViewer套件更新
- **Commit ID**: `76a52b707db909964ac255fe080234bfe746d256`
- **作者**: LALA
- **日期**: 2016-12-16 10:53:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/lib/ICEpdf/icepdf-core.jar`
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/lib/ICEpdf/icepdf-viewer.jar`

### 11. S00-20161215001 HR小助手組織同步功能增加可從設定檔中調整設定根節點組織ID
- **Commit ID**: `467da428ac74160addfb5cc42b97dff2b637cbf4`
- **作者**: WenCheng
- **日期**: 2016-12-15 13:52:48
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/syncorg/SyncTable.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/AppProperties.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/SyncTableConstants.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 12. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `81ac1604c07814873aa4e0f6e0bf48cc4da0c36f`
- **作者**: WenCheng
- **日期**: 2016-12-15 12:16:43
- **變更檔案數量**: 0

### 13. Q00-20161215002 組織設計師設定跨組織的部門兼職，在部門中的清單資料會重覆呈現問題已修正。
- **Commit ID**: `dbc9fff581c1593db3b8b9b0ab100444736515ce`
- **作者**: WenCheng
- **日期**: 2016-12-15 12:15:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`

### 14. 加上請分段執行的註解
- **Commit ID**: `59568e0021e1ea9f282f9234cd45ba437bec56fa`
- **作者**: yylee1123
- **日期**: 2016-12-15 11:52:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 15. 修正SQL錯誤
- **Commit ID**: `1e6b13f4a0125d88eaf2f8b973f78ac462bcbd9c`
- **作者**: yylee1123
- **日期**: 2016-12-14 18:02:56
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.3_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_updateSQL_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 16. 調整建立index 語法
- **Commit ID**: `c607426b107f28bff5725a768948c1cb7e53fa31`
- **作者**: wayne
- **日期**: 2016-12-14 17:34:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 17. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `4ef7eefd2bba7f03a4bdb7a5e793f31dec2c1c86`
- **作者**: wayne
- **日期**: 2016-12-14 16:46:56
- **變更檔案數量**: 0

### 18. 調整vip人員的update sql
- **Commit ID**: `61d795f31445c3d3c5849d89d3a5edaf743fe6ce`
- **作者**: wayne
- **日期**: 2016-12-14 16:46:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_updateSQL_Oracle.sql`

### 19. A00-20161214002 T100整合-修正當webServerAddress為非IP+PORT , 建立簽核流會失敗問題
- **Commit ID**: `a90cef8e75a27372ea477a09eb993cf521f79c76`
- **作者**: jerry1218
- **日期**: 2016-12-14 16:40:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 20. A00-20161214001 修正特定表單grid會因itemOrder都為0,導致儲存表單後grid順序及資料錯亂問題
- **Commit ID**: `54712e471a60cd5b42f883604c493ba0fdfa4e93`
- **作者**: jerry1218
- **日期**: 2016-12-14 15:03:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 21. Q00-20161214001 APP修正取待辦列表資料問題
- **Commit ID**: `c227fbddeacb8d2b3128e9ef8d9e2ee2da58a441`
- **作者**: Joe
- **日期**: 2016-12-14 13:42:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 22. 修正web表單設計師編輯選項時，按enter鍵的問題
- **Commit ID**: `62670831774adb15b847bf58720ebe93af099bd4`
- **作者**: yylee1123
- **日期**: 2016-12-14 11:48:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 23. Q00-20161214002 修正APP終止流程無反應問題
- **Commit ID**: `f7f632c829384c821b815086e30cdac1eec2ed97`
- **作者**: Joe
- **日期**: 2016-12-14 10:51:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 24. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `e74286cdc263c5918927daed68789d73a751d612`
- **作者**: LALA
- **日期**: 2016-12-13 17:32:19
- **變更檔案數量**: 0

### 25. Q00-20161213002[內部]修正卡關處理者為群組時，解析處理者失敗。
- **Commit ID**: `8cb4df41d8961f2aec9550f69e09c7d1aed996f7`
- **作者**: LALA
- **日期**: 2016-12-13 17:31:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 26. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `51c0b0bf220e19e3dfe4d2b670c50134f048c41b`
- **作者**: jd
- **日期**: 2016-12-13 17:17:02
- **變更檔案數量**: 0

### 27. 修正公告编辑异常
- **Commit ID**: `0d75d1abd04524c9821a9119499b604cbf3e63cb`
- **作者**: jd
- **日期**: 2016-12-13 17:14:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/announcement/Announcement.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/data/AnnouncementDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AnnouncementManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/Publisher.js`

### 28. Q00-20161213001[內部]待辦、通知等bagBadgeNumber超過1000則顯示999+
- **Commit ID**: `37e18187e941a66eccf955795a92b55f40d9b7f8`
- **作者**: LALA
- **日期**: 2016-12-13 16:10:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 29. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `29ab952f50dc8a9b99a68c76727625791e5797ba`
- **作者**: LALA
- **日期**: 2016-12-13 16:08:46
- **變更檔案數量**: 0

### 30. Q00-20161213001[內部]待辦、通知等BadgeNumber
- **Commit ID**: `4c2536f56dd5711ed22e6c7b7fa440a7dda46ba3`
- **作者**: LALA
- **日期**: 2016-12-13 16:07:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 31. Q00-20161212001 JSP多與細微調
- **Commit ID**: `cad7dc6babf0bea2ad440b61a8ef11301214d33b`
- **作者**: jerry1218
- **日期**: 2016-12-12 17:54:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmCompleteMailStraightSignOff.jsp`

### 32. Q00-20161212001 T100整合-從E-mail簽核，無法顯示自動確認失敗的原因
- **Commit ID**: `dfbfeccd5cdb86b4ec3d01b72f13e5ed5871f8e3`
- **作者**: jerry1218
- **日期**: 2016-12-12 17:50:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmCompleteMailStraightSignOff.jsp`

### 33. 修正 NaNaIntsys.properties中的MCLOUD WSDL的網址預設值有誤
- **Commit ID**: `9ce533aecfd09f808bf7b14abb9f534a39973793`
- **作者**: wayne
- **日期**: 2016-12-12 15:10:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`

### 34. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `7bfb2a3d645801f6aa4d10c5d1860c0849faa798`
- **作者**: wayne
- **日期**: 2016-12-12 11:20:46
- **變更檔案數量**: 0

### 35. BPM APP 新UI第二階段 畫面修改
- **Commit ID**: `e6c1e8decab5f5d8823448f5c4c78778751d856c`
- **作者**: Joe
- **日期**: 2016-12-12 11:07:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 36. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `1b7f4b6a267ac786e63c1ac7d78b5116afea504d`
- **作者**: wayne
- **日期**: 2016-12-12 10:47:48
- **變更檔案數量**: 0

### 37. 修正菜單樣式
- **Commit ID**: `28ca4cf31a7dd56f5fac790935f5f8a037588b91`
- **作者**: jd
- **日期**: 2016-12-12 10:37:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/more_infor.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-icon.css`

### 38. 修正表单发布公告功能和函式库
- **Commit ID**: `7fcf21929ee85511e4218aceaa8c1b65da26d298`
- **作者**: jd
- **日期**: 2016-12-09 16:10:48
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/data/AnnouncementDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/search/SearchKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AnnouncementManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/Publisher.js`

### 39. BPM APP新UI第二階段 ICON修改
- **Commit ID**: `e83a210fe061576b9b89326fc1d6387316dae022`
- **作者**: Joe
- **日期**: 2016-12-09 15:50:28
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/StartUp.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/agree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/attachment.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/left_slide.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/right_slide.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/stop_process.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 40. 修正"等待微信驗證中"缺少的多語系
- **Commit ID**: `74c7eeddbf5fad2f034cad1cb6d2a448c36882b3`
- **作者**: pinchi_lin
- **日期**: 2016-12-09 14:33:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5621.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileOAuthRecive.jsp`

### 41. Q00-20161209001[欣興]修正pdfViewer顯示pdf檔中的圖片會模糊
- **Commit ID**: `c92967e4f8da3222c6a3df69d27e45ecde97ef04`
- **作者**: LALA
- **日期**: 2016-12-09 11:52:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/src/com/dsc/nana/user_interface/pdf/efgp_pdfViewer/controller/SecurityManager.java`

### 42. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `53809ccb31d60b551a6678a36eab0798cc341cfb`
- **作者**: wayne
- **日期**: 2016-12-09 10:28:42
- **變更檔案數量**: 0

### 43. 修正核決權限關卡退回重辦時，無法退回重瓣
- **Commit ID**: `404b7babcac196d0cd7a3856acd58a525cf6a458`
- **作者**: wayne
- **日期**: 2016-12-09 10:26:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 44. APP GRID 相關程式修正
- **Commit ID**: `161adb7068e9163f2c61be308931cf6d49b1f31b`
- **作者**: Joe
- **日期**: 2016-12-09 09:19:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 45. A00-20161208001[巨輪]修正編輯預設排程，會額外新增一筆新的排程。
- **Commit ID**: `551fea21b85c24b468c5f9b08f58ef0e9ccb8505`
- **作者**: LALA
- **日期**: 2016-12-08 17:17:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/schedule/TimerFacadeBean.java`

### 46. 修正讓APP可以正常顯示PC版GRID元件
- **Commit ID**: `c6bcab4531fef37ec41b49c8109694cff3e0eaf6`
- **作者**: Joe
- **日期**: 2016-12-08 16:23:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppGridTemplate.txt`

### 47. 修正APP列表會取回失敗問題
- **Commit ID**: `e00ceacbbc36a1de7b7579c63ef8ac4ca933f9f0`
- **作者**: Joe
- **日期**: 2016-12-08 14:27:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 48. 修正App表單在IOS上字體會忽大忽小問題
- **Commit ID**: `9ceee9c39b5a81d96555ba024359ef3e3cb48b91`
- **作者**: jd
- **日期**: 2016-12-08 14:05:40
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 49. 更新5621的patch(自動安裝5611版本及update5621SQL)
- **Commit ID**: `ffac6a47b76fdba0df53d44e104e17f6ebfdea05`
- **作者**: wayne
- **日期**: 2016-12-08 11:14:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 50. APP新UI第二階段 退回畫面修正
- **Commit ID**: `46a3fe4e2e2c9cb81c0406e0fb22bf3f4d73cc86`
- **作者**: Joe
- **日期**: 2016-12-08 09:02:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 51. BPM APP新UI 補上缺漏的圖片
- **Commit ID**: `807d585b67f376005ac3aeb81c9bf65dc678411c`
- **作者**: Joe
- **日期**: 2016-12-07 18:57:01
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomImage/announce.jpg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomImage/notice.jpg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomImage/work.jpg`

### 52. C01-20161004004[欣興]文件在變更申請中、變更中、作廢申請中、已作廢等狀態下，變更與作廢單不允許申請。
- **Commit ID**: `3f49e91debf495f57061c36004e5467a4d367dfe`
- **作者**: LALA
- **日期**: 2016-12-07 17:40:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocCmItemVo.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOCancel001.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOMod001.form`

### 53. T100整合-FormFlowCreate服務修改邏輯 , 原本建流程途中出現exception會較exception擋下後response失敗的XML給T100,但此舉會造成流程有些資料無法roolback ,故修改將exception throw出 , 另增加建流程必要參數防呆
- **Commit ID**: `50c6646134df5e02b6a7c6c4bac04aecab571356`
- **作者**: jerry1218
- **日期**: 2016-12-07 17:02:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 54. 新UI第二階段 流程列表過濾修正
- **Commit ID**: `a4ca2a8cb42a910eabfb0835f310793b26410c26`
- **作者**: Joe
- **日期**: 2016-12-07 16:47:25
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-icon.css`

### 55. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a5f67fff1b6f3c0c07fa797e761c5e7a89ca3f70`
- **作者**: Joe
- **日期**: 2016-12-07 14:25:30
- **變更檔案數量**: 0

### 56. APP新UI 簽核歷程畫面修正
- **Commit ID**: `48aabba646c310d209e0ec6bd8ac8b0581fccc42`
- **作者**: Joe
- **日期**: 2016-12-07 14:25:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 57. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `75ce64f7cf4aa37c6462a4d983a47e200dbe549d`
- **作者**: wayne
- **日期**: 2016-12-07 14:24:26
- **變更檔案數量**: 0

### 58. 增加Java Web Start(Jnlp)程式相容Java 8 > 移至ProgramDefinition
- **Commit ID**: `347897fdb8974b86e599a3ba7979af291cbb4f78`
- **作者**: loren
- **日期**: 2016-12-07 12:23:00
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5621.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 59. BPM App New UI
- **Commit ID**: `83b731919908366aa78c9725e1a14e86eba91bf4`
- **作者**: jd
- **日期**: 2016-12-07 11:46:46
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/data/AnnouncementDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AnnouncementManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-icon.css`

### 60. 修正上傳表單時，若表單檔名為中文時，會跳出驗證錯誤，不再繼續將表單丟到後端解析取得表單代號
- **Commit ID**: `f1fcf06f13059bb42663098fa5c121816c2a4b9b`
- **作者**: Gaspard
- **日期**: 2016-12-07 09:59:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 61. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `147f49ce9073e9bcc4485dc7ffdd158be0b827d6`
- **作者**: LALA
- **日期**: 2016-12-06 18:28:50
- **變更檔案數量**: 0

### 62. C01-20161102001[台灣之星]多AP一DB情況，新增與修改人員時，一併呼叫其他台主機更新UserCache，組織同步則是同步完後，才進行initUserCache。
- **Commit ID**: `eaff8c8369bc91ede5b8d4ab2bee7b70ad144c3c`
- **作者**: LALA
- **日期**: 2016-12-06 18:28:04
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/server_manager/ServerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerHome.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocalHome.java`

### 63. BPM APP新UI第二階段 功能修正
- **Commit ID**: `ad3736aad374bafe270213752408fa006c0d8074`
- **作者**: Joe
- **日期**: 2016-12-06 17:49:15
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 64. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `d7df3742474869b81f90db580838d62789ab6c09`
- **作者**: Joe
- **日期**: 2016-12-06 15:40:29
- **變更檔案數量**: 0

### 65. BPM APP 新UI 第二階段
- **Commit ID**: `5e5d9d3702be1abb1e0356a1e6a53d1384dfa060`
- **作者**: Joe
- **日期**: 2016-12-06 15:39:04
- **變更檔案數量**: 37
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5621.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/Bell_60x90.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/Float_return.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/add_sign.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/agree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/confirm_retrieve.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/end.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/ended.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/go_back.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/left_slide.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/minus-symbol.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/more.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/next_record.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/no_read.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/previous_record.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/processing.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/read.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/retrieve.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/return.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/revoke.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/right_slide.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/round-add-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/save_form.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/send.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/urgent_only.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 66. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `c837cb4a568843772eb98bb5c6228dea79012932`
- **作者**: LALA
- **日期**: 2016-12-06 14:57:16
- **變更檔案數量**: 0

### 67. C01-20160810001[欣興]ISO確認變更Invoke狀態無變更問題
- **Commit ID**: `ae3622bb66cbcd443f745725ca5400bb4d9b4eb0`
- **作者**: LALA
- **日期**: 2016-12-06 14:51:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/AbsUnitDao.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/hibernate/AbsUnitDaoImpl.java`

### 68. S00-*********** 功能微調-拿掉system.out.println
- **Commit ID**: `c8d2dcfd484aeeb990e96f3eec90f19e5044e46d`
- **作者**: jerry1218
- **日期**: 2016-12-06 14:32:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TiptopAccessor.java`

### 69. T100整合新增表單-aapt110,afmt015,anmt310
- **Commit ID**: `480676e07999597050db5dd0e367bbddf2585a66`
- **作者**: jerry1218
- **日期**: 2016-12-05 15:21:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\350\262\250\346\254\276\345\260\215\345\270\263\344\275\234\346\245\255(aapt110).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\236\215\350\263\207\347\224\263\350\253\213\345\226\256(afmt015).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\200\345\255\230\346\224\266\346\224\257\347\266\255\350\255\267\344\275\234\346\245\255(anmt310).form"`

### 70. [Q00-20150729005] 修正組織設計師更新人員資料時，姓名多語系Cache的內容
- **Commit ID**: `89a3a33f505d60f7c9b285ebfc7fb3982680f98b`
- **作者**: yylee1123
- **日期**: 2016-12-05 11:39:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 71. 增加Java Web Start(Jnlp)程式相容Java 8 > 合併分支衝突檔
- **Commit ID**: `7920355a26b237501f6f7f53257393cc11002831`
- **作者**: loren
- **日期**: 2016-12-05 11:13:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5621.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 72. 增加Java Web Start(Jnlp)程式相容Java 8
- **Commit ID**: `b224c3ad1295aa3b77ab339870d39774abfb885d`
- **作者**: loren
- **日期**: 2016-12-05 09:50:05
- **變更檔案數量**: 142
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/jgoext/view/ProcessView.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_side_util/RemoteCallConnection.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/view/dialog/DurationDateChooserController.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/view/dialog/DurationDateChooserDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/SearchDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/SearchDialogController.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/jgoext/view/ProcessView.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/bpm.keystore`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/nana.keystore`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/InstallCertificateAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-installCertificate-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/BPMCertificateInstaller.exe`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/bpm.cer`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option1_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option2_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option2_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option2_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option2_Step5.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option2_Step6.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option2_Step7.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option2_Step8.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option2_Step9.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option3_Step1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option3_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option3_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option3_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/Windows_Option3_Step5.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/macOS_Option1_Step1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/macOS_Option1_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/macOS_Option1_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/macOS_Option1_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/en_US/macOS_Option1_Step5.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option1_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option2_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option2_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option2_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option2_Step5.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option2_Step6.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option2_Step7.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option2_Step8.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option2_Step9.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option3_Step1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option3_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option3_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option3_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/Windows_Option3_Step5.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/macOS_Option1_Step1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/macOS_Option1_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/macOS_Option1_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/macOS_Option1_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_CN/macOS_Option1_Step5.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option1_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option2_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option2_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option2_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option2_Step5.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option2_Step6.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option2_Step7.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option2_Step8.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option2_Step9.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option3_Step1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option3_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option3_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option3_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/Windows_Option3_Step5.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/macOS_Option1_Step1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/macOS_Option1_Step2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/macOS_Option1_Step3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/macOS_Option1_Step4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/img/zh_TW/macOS_Option1_Step5.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/ToolSuite.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Axis/activation.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Axis/axis.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Axis/commons-discovery.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Axis/jaxrpc.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Axis/saaj.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Axis/wsdl4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/BrowserLauncher/BrowserLauncher.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Dom4J/dom4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Forms/forms-1.1.0.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-awt-util.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-dom.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-svg-dom.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-svggen.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-util.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/batik-xml.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-core.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-extra.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-pro-intl.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-pro.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/icepdf-viewer.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/ICEpdf/levigo-jbig2-imageio.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/J2EE/j2ee.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JCalendar/jcalendar-1.3.2.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JCalendar/jcalendar.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JCalendar/kunststoff.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JDiagram/JDiagram.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JGo/JGo.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JGo/JGoLayout.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JIntellitype/jintellitype-1.3.9.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JTaskpane/swingx.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Jag/jag.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-beanutils.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-codec.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-collections.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-httpclient.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-io.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-lang.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-logging.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaCommons/commons-pool.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/JakartaOJB/db-ojb.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Jaxen/jaxen.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Jython/jython.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/LookAndFeel/looks-2.1.4.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/LookAndFeel/panel-skin.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Mail/activation.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Mail/mail.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Msv/isorelax.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Msv/msv.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Msv/relaxngDatatype.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Msv/xmlgen.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Msv/xsdlib.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Sdo/sdo2_1.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Trinity/TrinityServiceEJB.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/XStream/xpp3_min.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/XStream/xstream.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Xalan/serializer.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Xalan/xalan.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Xerces/resolver.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Xerces/xercesImpl.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/lib/Xerces/xml-apis.jar`

### 73. S00-*********** 功能微調
- **Commit ID**: `74cd4c83aa4f9a06f42bdd0b5a30eae053bc87c7`
- **作者**: jerry1218
- **日期**: 2016-12-02 14:55:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5621.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TiptopAccessor.java`

### 74. 取回重瓣時在 簽核意見上加註取回人員
- **Commit ID**: `13f4f527c2ffa03d759178cf41e924224df65d90`
- **作者**: wayne
- **日期**: 2016-12-02 14:25:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 75. S00-*********** T100&BPM，簡易測通二邊服務的功能，於[整合系統設定]頁面加上測試按鈕，測試是否可以成功連接T100 JBOSS
- **Commit ID**: `93c77953d8f4a38746fe260bf01a42c9b835ef4d`
- **作者**: jerry1218
- **日期**: 2016-12-01 16:11:37
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/NewTiptopManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5621.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TiptopAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`

### 76. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `5aa0adaad0e240d505ebfc94a192e67127866ce1`
- **作者**: yylee1123
- **日期**: 2016-12-01 14:35:17
- **變更檔案數量**: 0

### 77. [Q00-20150610008] 文件總管-新增文件，文件編號超過30字，不可新增
- **Commit ID**: `1cf52786803c16060eaa6847fe4f9953187ec964`
- **作者**: yylee1123
- **日期**: 2016-12-01 14:34:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`

### 78. mail樣式 增加員工工號
- **Commit ID**: `c1a67c066ca69580bef3ef8a7f902eafd8ce2b75`
- **作者**: wayne
- **日期**: 2016-12-01 14:33:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 79. 信件的簽核歷程的顯示，加上處理狀態
- **Commit ID**: `530bc034dfe22adf324a3f1d2c2968854ad11181`
- **作者**: wayne
- **日期**: 2016-12-01 09:46:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 80. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `d07de143864e9127f8bffcce2ac798e0f3364f37`
- **作者**: wayne
- **日期**: 2016-11-30 18:24:34
- **變更檔案數量**: 0

### 81. [管理流程] 以administrator 權限登入時，可直接刪除流程，增加log紀錄
- **Commit ID**: `f3a31bb7931e7714296b6ab5cdefa1e1d37642ea`
- **作者**: wayne
- **日期**: 2016-11-30 18:23:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 82. C01-20161103001 修正T100抽單失敗後 , 如果再去BPM撤銷 , 會造成沒有呼叫T100即成功撤銷問題
- **Commit ID**: `bc8faf57b29382e9596bae3a5cabd0ebdc054f72`
- **作者**: jerry1218
- **日期**: 2016-11-30 16:09:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`

### 83. A00-20161121001 修正手機簽核程式出現部分表單簽核歷程無法顯示
- **Commit ID**: `0494be42e13d76958692a4a07153ea05f89f0cac`
- **作者**: wayne
- **日期**: 2016-11-30 10:49:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`

### 84. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `08d49e07fd91a639ba669853380c3795f419b0ce`
- **作者**: wayne
- **日期**: 2016-11-30 10:29:04
- **變更檔案數量**: 0

### 85. [Q]PDF Viewers 與 黑名單的處理機制須變更規格
- **Commit ID**: `****************************************`
- **作者**: wayne
- **日期**: 2016-11-30 10:27:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/src/com/dsc/nana/user_interface/pdf/efgp_pdfViewer/controller/SecurityManager.java`

### 86. Q00-20161013001 PDF Viewers 與 黑名單的處理機制變更規格
- **Commit ID**: `259d7a649b463bb7a42170ec155817eb409b99c4`
- **作者**: wayne
- **日期**: 2016-11-30 10:22:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/src/com/dsc/nana/user_interface/pdf/efgp_pdfViewer/PdfViewerApp.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPkgCategoryListReader.java`

### 87. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `1f8b533fafcca46bd1816cd915a053fdea062f49`
- **作者**: yylee1123
- **日期**: 2016-11-30 10:08:25
- **變更檔案數量**: 0

### 88. [A00-20161115001]將FavoriteProcess的processID欄位長度放寬到100，與ProcessPackage的id相同
- **Commit ID**: `c739c9e7ada53ae661e9990e47d178b4e6104ea2`
- **作者**: yylee1123
- **日期**: 2016-11-30 10:07:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 89. Q00-20161130004 修正自訂使用產品紅框的錯誤提示方式時，寫在onblur事件會失效
- **Commit ID**: `7370fe3c027f8cf92ec578832ee5699a38eebf20`
- **作者**: Gaspard
- **日期**: 2016-11-30 06:37:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 90. Q00-20161130002 Q00-20161130003 無法修改元件代號 1.修正當畫面有DB Connection或SQL Command元件時，無法修改元件代號 2.修正無法修改Label元件的代號
- **Commit ID**: `bf96312bc54f5df06619eb0970a7d319b70a3b5e`
- **作者**: Gaspard
- **日期**: 2016-11-30 06:36:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 91. 更改RsrcBundle檔名的版號
- **Commit ID**: `ff94f85be05e7c8a456fddac7b663c0df7dd3bbf`
- **作者**: yylee1123
- **日期**: 2016-11-29 17:04:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`

### 92. 更改updateSQL檔名的版號
- **Commit ID**: `93a73025d09836e568db66929dd64c2409f3bf98`
- **作者**: yylee1123
- **日期**: 2016-11-29 16:54:03
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.2_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.2_updateSQL_SQLServer.sql`

### 93. S00-***********、S00-***********、Q00-***********、Q00-***********、Q00-***********、Q00-***********
- **Commit ID**: `6bffd68450e5726c822f97dcffe237b041e12195`
- **作者**: yylee1123
- **日期**: 2016-11-29 16:18:34
- **變更檔案數量**: 105
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BpmHelperDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProgramDefManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WizardAuthorityManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/favorities/FavoriteMenu.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/OwnerType2StringFieldConversion.java`
  - ❌ **刪除**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/helper/IssueExplanation.java`
  - ❌ **刪除**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/helper/IssueManagement.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/OwnerType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramAccessRight.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - ❌ **刪除**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/BpmHelperForIssueDTO.java`
  - ❌ **刪除**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/BpmHelperForListTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ModuleDefinitionDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProgramDefinitionDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_bpm.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/DAOFactory.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IBpmHelperDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/JDBCDAOFactory.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBBpmHelperDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBDAOFactory.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/helper/BpmHelper.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/helper/BpmHelperBean.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/helper/BpmHelperHome.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/helper/BpmHelperLocal.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/helper/BpmHelperLocalHome.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/BpmHelperListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/BpmHelperAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ProgramAccessRightVo.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/RoleAccessControl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/favorities/FavoritiesMenuViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ModuleViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProgramViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MainMenuManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/ProductManifest.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-bpmHelper-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-managePhrase-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/BpmHelper/BpmHelper.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/BpmHelper/BpmHelperInstructions.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/BpmHelper/BpmHelperMain.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/BpmHelper/BpmHelperProblemPage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISODocumentsList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOEffectInvalidList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOReleaseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/css/EFGP.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/css/MultiAp_EFGP.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@SP7/update/sp7.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-ems/create/Init_EMS_Data_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-ems/create/Init_EMS_Data_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mcloud/create/InitMCloud_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mcloud/create/InitMCloud_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.2_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.2_updateSQL_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@sap/create/InitSap_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@sap/create/InitSap_Oracle.sql`

### 94. BPM APP 新增後端取得流程預設主旨功能
- **Commit ID**: `ea7a82239d6835c9fc99e189b3c63dfe76f43ec1`
- **作者**: Joe
- **日期**: 2016-11-29 14:58:03
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileInvokableProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmInvokeWorkItemVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BPMPerformRequestTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`

### 95. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `3834bbbdc014336b14f0eef5253cbf001bfbba7e`
- **作者**: wayne
- **日期**: 2016-11-28 16:22:22
- **變更檔案數量**: 0

### 96. 自動簽核跳關時，將待辦通知信連結更改為追蹤通知信。並取消mail圖示
- **Commit ID**: `c9b84f3f9023d71fb71d436f96b7c93daf48ba87`
- **作者**: wayne
- **日期**: 2016-11-28 16:21:48
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_SQLServer.sql`

### 97. Q00-20161125001[內部]追蹤流程自訂日期範圍都填空，日期顯示異常
- **Commit ID**: `0bf5ce763d955e7e390e70eb94c6f804a7463ccc`
- **作者**: LALA
- **日期**: 2016-11-25 18:01:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 98. 登入時，檢核所使用的瀏覽器為可支援的版本
- **Commit ID**: `ad9e61956bd5d24341ad816573dc464ba9ed3a7c`
- **作者**: wayne
- **日期**: 2016-11-23 14:24:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 99. 優化listerReader綁訂變數及調整SQLnoLock
- **Commit ID**: `36a1392fc21e92338bd2d673b0289324159f87cb`
- **作者**: wayne
- **日期**: 2016-11-23 11:17:34
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`

### 100. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `982bafeed07f43a78e451be3597f12ce9b0456e3`
- **作者**: wayne
- **日期**: 2016-11-22 18:04:56
- **變更檔案數量**: 0

### 101. 調整oracle Create時發生錯誤
- **Commit ID**: `999769c50e1accbb941713818c086023aad37090`
- **作者**: wayne
- **日期**: 2016-11-22 17:52:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`

### 102. Q00-20161101001 追蹤流程上傳附件異常修正(追蹤流程及工作通知不應可以上傳附件，直接停用)
- **Commit ID**: `0f05ff569004cc6d4fb2ed89cc999e534221d7f9`
- **作者**: Joe
- **日期**: 2016-11-22 17:05:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 103. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `69c8678bd9f12af828958376bfce509ed3479a22`
- **作者**: Joe
- **日期**: 2016-11-22 16:21:51
- **變更檔案數量**: 0

### 104. Q00-20161116005 流程追蹤-已發起:撤銷原因偵測修正
- **Commit ID**: `f27b013924ab8c4d5742a8d55b9ce73f5760af17`
- **作者**: Joe
- **日期**: 2016-11-22 15:56:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 105. A00-*********** 修正ProcessInstanceService webservice取得簽核意見,如果關卡撤銷時無顯示撤銷人問題
- **Commit ID**: `b18e715404f2f6597156bbdb775697722f28c754`
- **作者**: jerry1218
- **日期**: 2016-11-22 15:56:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/.classpath`

### 106. Q00-20161116008 APP GRID手勢切換功能修正
- **Commit ID**: `fae5ef154c71dccae97e91eb4cefbb0beb4c9110`
- **作者**: Joe
- **日期**: 2016-11-22 14:02:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`

### 107. Q00-20161116006 APP GRID畫面在小螢幕手機異常修正
- **Commit ID**: `ebbc91df23b2344f57041f919d498ee1caf63ad7`
- **作者**: Joe
- **日期**: 2016-11-22 13:56:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 108. Q00-20161116010 補上APP GRID缺漏的多語系檔
- **Commit ID**: `e8379f4c503e185652bae4ddd9ff4d8729237b87`
- **作者**: Joe
- **日期**: 2016-11-22 11:11:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`

### 109. S00-20161117001 ApproveLogGet(取得簽核流程紀錄)需求變更 1.有簽核動作的關卡(同意、抽單、中止流程)，才需要傳給T100 2.撤銷，關卡補上 "處理者"
- **Commit ID**: `f88e14cf69dc0e17e771b780952965d9f904dec8`
- **作者**: jerry1218
- **日期**: 2016-11-21 16:16:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`

### 110. A00-20160919001[創元]修正ISO文件型態階層，多語系少内容。
- **Commit ID**: `9cfa6f7bb0fd992ebb57d2849190d444e0da2582`
- **作者**: LALA
- **日期**: 2016-11-21 15:29:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`

### 111. A00-20160919001[創元]修正ISO文件型態階層，多語系少内容。 20161121 Shih-Yun 06394 1.修正多餘code。
- **Commit ID**: `08ae00b67f917d4f872ca0e6b495b58c82091a81`
- **作者**: LALA
- **日期**: 2016-11-21 15:18:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageAccessRight/AccessRightMain.jsp`

### 112. A00-20160919001[創元]修正ISO文件型態階層，多語系少内容。
- **Commit ID**: `198364acca140bf8b7198c463202f0ca109d698d`
- **作者**: LALA
- **日期**: 2016-11-21 15:13:09
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageAccessRightAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageAttTemplatesAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocClauseAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocTypeAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageAccessRight/AccessRightMain.jsp`

### 113. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `c366fb1370cf841e33b85c4c1c7a167969c01d87`
- **作者**: wayne
- **日期**: 2016-11-21 11:14:32
- **變更檔案數量**: 0

### 114. 修正人員開窗輸入條件時，無法帶出畫面  Q00-20161121001
- **Commit ID**: `2c59674fc15dc874ecf89c879efca4e43162c6df`
- **作者**: wayne
- **日期**: 2016-11-21 11:12:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 115. C01-20161004001[欣興]ISO文件編號有特殊符號的限制，在新增文件時去判斷並提醒使用者不的有特殊字元
- **Commit ID**: `b1cd154e71198b1f75a515d6c933a2d7505fb93c`
- **作者**: LALA
- **日期**: 2016-11-18 16:56:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 116. A00-20160912001[驊訊]修正工作日期會計算特定放假日但不會計算特定上班工作日的問題。
- **Commit ID**: `b39e629d57863fd5bc487344529a7dcd9b081aea`
- **作者**: LALA
- **日期**: 2016-11-18 15:24:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/WorkCalendar.java`

### 117. A00-20160824002[尚至]修正變數"工作收受託者"應從Workitem找出Assignee，而不是收件人
- **Commit ID**: `91e59514ca732c6f11dc7261bb01b4ecbf83eda8`
- **作者**: LALA
- **日期**: 2016-11-17 17:47:53
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/common/PatternEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/processpackage/VariableNamesList.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/IProcessVariableNames.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 118. Q00-20161116002 修正T100整合ProcessListGet服務所給的URL , 使用chrome開啟會是亂碼問題
- **Commit ID**: `1d338526be0af951277e0a703d2fc6fbc6e958a9`
- **作者**: jerry1218
- **日期**: 2016-11-16 17:38:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ViewProcessPackage.java`

### 119. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `452d400799e7ee503ac4ab8b7ec4d56ea4388a10`
- **作者**: wayne
- **日期**: 2016-11-16 16:43:09
- **變更檔案數量**: 0

### 120. 調整oracle的create SQL
- **Commit ID**: `9a1f64e02c21d579ecf6b5144373f61d56b5d445`
- **作者**: wayne
- **日期**: 2016-11-16 16:42:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@sap/create/InitSap_Oracle.sql`

### 121. 修正公告系統遺漏SQL
- **Commit ID**: `d5f425d9a1ec386a286e26d943a2fd355ba2f7a2`
- **作者**: pinchi_lin
- **日期**: 2016-11-15 18:39:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`

### 122. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a1c0cf67781de8a4fffa36068a5644ae727132d7`
- **作者**: wayne
- **日期**: 2016-11-15 17:48:40
- **變更檔案數量**: 0

### 123. 調整License 不足的信件內容
- **Commit ID**: `cb5aba6fdf19153b8637c658448816ffa41c2b6d`
- **作者**: wayne
- **日期**: 2016-11-15 17:47:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 124. A00-20160824002[尚至]修正變數"工作收受託者"應從Workitem找出Assignee，而不是收件人
- **Commit ID**: `16472f9aeb621c4a0912cc8352e934099b845a29`
- **作者**: LALA
- **日期**: 2016-11-15 17:15:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 125. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b5fff028bab0933e1106c0e4d125c696187b56fd`
- **作者**: wayne
- **日期**: 2016-11-15 11:39:12
- **變更檔案數量**: 0

### 126. 建立 ParticipantDefinition 的 participantType、employeeId index
- **Commit ID**: `b4f77b2dfa22e049154aeceef6240bfe70506dd7`
- **作者**: wayne
- **日期**: 2016-11-15 11:37:30
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_SQLServer.sql`

### 127. 修正表單設計師的上傳表單功能，表單代號預設抓取檔案名稱，修改為將上傳的表單先丟到後端解析出表單代號，將其當作新的表單代號預設值
- **Commit ID**: `b3d8ce9441df59b7ecd459e0daeefd305dc4b1d7`
- **作者**: Gaspard
- **日期**: 2016-11-15 11:35:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 128. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `17af10eec55492009256677aa0b380004c82f48d`
- **作者**: wayne
- **日期**: 2016-11-15 10:39:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 129. 將離職員工維護作業開放給一般使用者-增加員工資訊
- **Commit ID**: `eb990484b11bc2bfbf16f903b1bc2ce43565c2c2`
- **作者**: wayne
- **日期**: 2016-11-15 10:38:30
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ResignedEmployeesManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/orgAnalyze/OrgAnalyzeManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/resignedEmployees/ResignedEmployeesSearchViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`

### 130. C01-20161004001[欣興]ISO文件編號有特殊符號的限制，在新增文件時去判斷並提醒使用者不的有特殊字元
- **Commit ID**: `95843ac82156e57b93a8913e23ad81c224ae3c78`
- **作者**: LALA
- **日期**: 2016-11-15 10:26:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/BatchUploadAction.java`

### 131. A00-20161101002 [欣興]修正一般使用者使用「管理流程」功能時，點按「轉出Excel」功能，其資料內容「處理者」欄位為空問題。
- **Commit ID**: `73dc9fe668626df26772332940f36bfd9a2d05af`
- **作者**: WenCheng
- **日期**: 2016-11-11 16:50:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 132. 修正資料選取器JS檔與SAP的JS檔衝突，故修改載入順序
- **Commit ID**: `8cae436966d59a3e8fbdd45c277d16749ac51e0f`
- **作者**: Gaspard
- **日期**: 2016-11-11 10:05:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 133. 修正多語系未顯示問題與無法塞入預設值
- **Commit ID**: `a945111f334ba348c6b206e9c18597824c80770d`
- **作者**: Gaspard
- **日期**: 2016-11-11 10:03:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`

### 134. Q00-20161110001[內部]修正ISO文件「保存年限」為「保存到期日」減去「文件生效日」
- **Commit ID**: `7224f02b947a4a21a33c0a750a77e024f09bb28d`
- **作者**: LALA
- **日期**: 2016-11-10 17:46:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocumentVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocumentViewer.java`

### 135. C01-20161004001[欣興]ISO文件編號有特殊符號的限制，在新增文件時去判斷並提醒使用者不的有特殊字元
- **Commit ID**: `52f6e89403c85c6f273834ea1acd8fd369025c0f`
- **作者**: LALA
- **日期**: 2016-11-10 11:40:43
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/BatchUploadAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/snGenRule.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISONew001.form`

### 136. [BPM App]新UI第二階段
- **Commit ID**: `124f4b972839a4824466cc43ba43f7947ebeea38`
- **作者**: jd
- **日期**: 2016-11-10 11:05:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/process_folder.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/process_kind.png`

### 137. Merge remote-tracking branch 'origin/develop' into develop
- **Commit ID**: `682df9840250ef7bf41d5a22a27edca6c2aa6733`
- **作者**: jd
- **日期**: 2016-11-10 11:00:44
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileKickStart.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 138. [BPM App]新第二階段修正
- **Commit ID**: `f5453fb581034cdf042c28bd23af8d475873a565`
- **作者**: jd
- **日期**: 2016-11-10 10:59:25
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-icon.css`

### 139. C01-20161020001[地樺]修正授權的流程效能調整
- **Commit ID**: `3fd8d5cb4de8b9cb1e97a42fe4de3b5a9fe4a626`
- **作者**: LALA
- **日期**: 2016-11-09 14:42:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/AuthorityHelper.java`

### 140. A00-20161109001 T100單據中有附件，執行BPM流程發起時，出現DocManagerFactoryLocal not bound
- **Commit ID**: `b38d30a4c7f882bcd086360bd67632c37f317fe3`
- **作者**: WenCheng
- **日期**: 2016-11-09 10:48:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 141. 列印表單時，textbox增加border
- **Commit ID**: `61d2163a2721f2e972aeee5b4c4c641828aab32a`
- **作者**: wayne
- **日期**: 2016-11-08 13:40:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 142. C01-20161107002[欣興]修正無論是否轉PDF，只要索引失敗(ISOFile.isIndexed=0)的檔案皆列出
- **Commit ID**: `7ea190fc0f6cbce43d7905783189d57efe8f7d9b`
- **作者**: LALA
- **日期**: 2016-11-07 14:08:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/IndexableISODocListReaderImpl.java`

### 143. Q00-20161104001[內部]修正待辦通知信件的簡易簽核URL有誤
- **Commit ID**: `dcf854ef27b351edc443dc83d2b4d2398e057f22`
- **作者**: LALA
- **日期**: 2016-11-04 17:15:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 144. C01-20160825003[欣興]修正ISO文件編號有特殊符號開啟檔案會報錯
- **Commit ID**: `86dc64e1d60dc4c4b7de71623fc095bfb85c7988`
- **作者**: LALA
- **日期**: 2016-11-04 14:10:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/FileListViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`

### 145. 程式說明註解修改，避免混淆
- **Commit ID**: `ea3c348a688d880cda37fb540262426f319e70d0`
- **作者**: Joe
- **日期**: 2016-11-04 09:47:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/MobileProcessPackageVo.java`

### 146. C01-20161004002[欣興]修正簡易查詢功能文件編號及文件名稱可搜尋特殊符號
- **Commit ID**: `14b060b91e74bdc624b9d479807dbaee4aba27f2`
- **作者**: LALA
- **日期**: 2016-11-02 17:40:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp`

### 147. C01-20161005002[欣興]確認ISO變更時版號重覆則不更新
- **Commit ID**: `2fda5655e366c29e180d6b5492099e6bcddbdbd7`
- **作者**: LALA
- **日期**: 2016-11-02 16:35:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`

### 148. A00-20161005001[欣興]文件變更狀態應不影響報表記錄的查詢
- **Commit ID**: `e59517bf59dee47a46672e693f7b70d4550a36e1`
- **作者**: LALA
- **日期**: 2016-11-02 16:01:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOReleaseDocList.jsp`

### 149. [A00-20161028001]新增取使用者名稱多語系的方法，避免找不到tUserNameMap或沒有傳入 Locale
- **Commit ID**: `a6c0cfa3032c7763f3064326e9a5ac652ab8db95`
- **作者**: yylee1123
- **日期**: 2016-11-01 11:55:15
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MobileUserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPkgCategoryListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReportDefinitionListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimpleExpenseAccountItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimplePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ProcessPerformanceRecordUtil.java`

### 150. S00-**********2 修改T100整合邏輯 , 於簽核中察看T100單據功能 , 阻擋IE10以下IE版本
- **Commit ID**: `2e38c167034ca6ed6502b87dd96a12168df430d4`
- **作者**: jerry1218
- **日期**: 2016-10-31 17:41:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 151. S00-**********2 修改T100整合邏輯 , 於簽核中察看T100單據功能 , 阻擋IE10以下IE版本
- **Commit ID**: `74a6620c8673559368820ff614466cc05b470f03`
- **作者**: jerry1218
- **日期**: 2016-10-31 17:39:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 152. A00-20161028002 T100整合-修正T100抽單失敗後 , 如果在去BPM撤銷 , 會造成沒有呼叫T100即成功撤銷問題
- **Commit ID**: `ad996776a3c1d1423f1e1eb515ce1388d5c9184d`
- **作者**: jerry1218
- **日期**: 2016-10-31 10:43:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`

### 153. [Q00-20161028001]修正模組程式維護的程式連線URL含有&、<、>，儲存後變成&amp;、&lt;、&gt;的問題
- **Commit ID**: `c49afd54662285c6bc401ab5c354a5a4f63f6bde`
- **作者**: yylee1123
- **日期**: 2016-10-28 16:38:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`

### 154. [內部]將離職員工維護作業開放給一般使用者
- **Commit ID**: `959c58f3c62d1eb2f2fe7db8d60f8bef98a235b2`
- **作者**: wayne
- **日期**: 2016-10-27 10:04:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 155. [內部]將匯入匯出系統多語系開放給一般使用者
- **Commit ID**: `5449cb83a4c67bb9493093350e0f45eeff450e9d`
- **作者**: wayne
- **日期**: 2016-10-26 17:34:51
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RsrcBundleDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/SysLanguage.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/rsrcBundle/DBRsrcBundle.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_bpm.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/ISysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LanguageMaintainAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/UpdateVersionAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-languageMaintain-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_SQLServer.sql`

### 156. [內部]將離職員工維護作業開放給一般使用者
- **Commit ID**: `55e7c1e1c65588f862013d75d61801c289bf627c`
- **作者**: wayne
- **日期**: 2016-10-24 14:57:01
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/BatchReassignElement.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-resignedEmployeesMaintain-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`

### 157. [內部]將轉派員工的工作開放給一般使用者
- **Commit ID**: `037759c5b7454b98a72c39a3ed907c81f1464c68`
- **作者**: wayne
- **日期**: 2016-10-24 14:43:22
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 158. [S00-20161019001]管理片語增加更新時間
- **Commit ID**: `823ec252938ad9d10f6baeb6ca9620a88f68c0aa`
- **作者**: yylee1123
- **日期**: 2016-10-21 17:23:24
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Phrase.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PhraseListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/enumTypes/SearchParameterType.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-managePhrase-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_SQLServer.sql`

### 159. [S00-20161004007]流程模組維護增加更新人員OID、時間
- **Commit ID**: `5b59a4f7f24ea38fa88d76e342bd623926556a05`
- **作者**: yylee1123
- **日期**: 2016-10-21 17:16:13
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProcessModuleAccessRight.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProcessModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5612.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ProcessModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/CreateProcessModule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.2_updateSQL_SQLServer.sql`

### 160. [S00-20161017001]系統排程增加更新人員OID、時間(記錄在/NaNa/conf/NaNaJobs.xml)
- **Commit ID**: `313c4a91cb438e08c73d77da6e64b85b05aad394`
- **作者**: yylee1123
- **日期**: 2016-10-21 17:01:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/schedule/TimerFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SystemScheduleAccessor.java`

### 161. 20161013,v5611紧急修正
- **Commit ID**: `8e1ba2b7044b6c280afecff99ad9bf17cf35120f`
- **作者**: jd
- **日期**: 2016-10-13 11:07:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileKickStart.jsp`

### 162. Merge remote-tracking branch 'origin/develop' into develop
- **Commit ID**: `0c3e0c5c623a48cbf8cfdd8f7f53b586cf5e7ce8`
- **作者**: jd
- **日期**: 2016-10-07 16:29:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/.classpath`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 163. 新UI第二階段議題修正
- **Commit ID**: `41e6a4699cb7e57ffe97a6f2018c8262d80ecdb7`
- **作者**: jd
- **日期**: 2016-10-07 16:09:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/ProcessPackageManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

