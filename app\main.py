"""
BPM Easy Tools - FastAPI 主應用程式
"""
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from pathlib import Path
import sys

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 導入路由
from app.routers import home, release_query, file_search, customer_connections

# 建立 FastAPI 應用程式
app = FastAPI(
    title="BPM服務部好用工具",
    description="BPM 服務部開發的綜合性工具集",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 設定靜態檔案
app.mount("/static", StaticFiles(directory="static"), name="static")

# 設定模板
templates = Jinja2Templates(directory="templates")

# 註冊路由
app.include_router(home.router, tags=["首頁"])
app.include_router(release_query.router, prefix="/release", tags=["Release查詢"])
app.include_router(file_search.router, prefix="/files", tags=["檔案搜尋"])
app.include_router(customer_connections.router, prefix="/customers", tags=["客戶連線管理"])

# 根路徑重定向到首頁
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """根路徑重定向到首頁"""
    return await home.home_page(request)

# 健康檢查端點
@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {"status": "healthy", "service": "BPM Easy Tools"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8888,
        reload=True,
        log_level="info"
    )
