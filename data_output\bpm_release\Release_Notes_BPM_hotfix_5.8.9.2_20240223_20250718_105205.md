# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.2_20240223
- **舊版本**: release_5.8.9.2
- **生成時間**: 2025-07-18 10:52:05
- **新增 Commit 數量**: 193

## 變更摘要

### liuyun (17 commits)

- **2024-02-22 10:25:32**: [Web] Q00-20240222001 修正列印模式FormUtil.getValue获取不到Textbox和HiddenTextbox值
  - 變更檔案: 2 個
- **2023-11-24 10:33:10**: [Web] Q00-20231124002 修正流程主旨範本設定<#workItemName>显示N.A.
  - 變更檔案: 3 個
- **2024-02-02 17:51:56**: [Web] Q00-20240202002 修正通过服务任务给表单赋值触发警告问题
  - 變更檔案: 1 個
- **2023-11-02 13:12:36**: [T100] Q00-20231102002 T100的签核历程沒有权限的显示提示告知登入者
  - 變更檔案: 3 個
- **2023-10-31 13:39:56**: [Web] Q00-20231031001 修正缩小ESSPlus管理页面时，查询出来的结果在grid显示不全
  - 變更檔案: 1 個
- **2023-10-23 09:15:57**: [Web]Q00-20231023001 修正列印预览grid标题字体颜色为白色
  - 變更檔案: 1 個
- **2023-09-22 11:21:43**: [Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效
  - 變更檔案: 1 個
- **2023-09-22 10:27:58**: [Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错
  - 變更檔案: 1 個
- **2023-09-11 11:47:04**: [Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误
  - 變更檔案: 1 個
- **2023-09-11 16:08:36**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】
  - 變更檔案: 2 個
- **2023-09-07 17:54:10**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】
  - 變更檔案: 4 個
- **2023-09-06 11:11:10**: [Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白
  - 變更檔案: 1 個
- **2023-08-25 15:40:09**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面
  - 變更檔案: 3 個
- **2023-08-18 10:41:47**: [Web] Q00-20230817004 修改手机版追踪流程，取回重办、撤销流程显示两个时间
  - 變更檔案: 3 個
- **2023-08-16 12:08:59**: [Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常 [补修正]
  - 變更檔案: 1 個
- **2023-08-15 13:57:44**: [Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常
  - 變更檔案: 1 個
- **2023-08-11 15:36:12**: [Web] Q00-20230811001 修正BPM首页手机端流程有两个时间显示
  - 變更檔案: 1 個

### waynechang (21 commits)

- **2023-11-27 15:20:06**: [流程引擎]Q00-20231127001 修正關卡設定多人都要簽核且設定自動簽核2，與前一關相同者，若前一關為核決關卡，且未實際展開核決關卡時，流程無法派送至下一關的異常
  - 變更檔案: 1 個
- **2023-11-20 17:25:48**: [流程引擎]Q00-20231120003 修正監控流程的詳細流程圖頁面，當按下「跳過此關卡」功能時，下一關處理者無法收到代辦通知信件
  - 變更檔案: 1 個
- **2023-11-24 17:33:54**: [SAP]Q00-20231124005 優化SAP整合服務，當整合的資料類型為Grid時，同時支持RWD表單Grid及絕對位置表單Grid
  - 變更檔案: 1 個
- **2023-11-21 11:55:25**: [SAP]Q00-20231121001 修正SAP整合，當mapping內容有Grid時，可能會有GridColumnId與GridValue順序錯誤的異常
  - 變更檔案: 1 個
- **2023-10-30 14:41:37**: [流程引擎]Q00-20231030001 修正流程發起者於追蹤流程頁面進入表單畫面點擊撤銷流程後，詳細流程圖的流程詳細資訊應為是「流程發起者」而非「流程負責人」撤銷
  - 變更檔案: 1 個
- **2023-10-23 11:10:11**: [Web]Q00-20231023002 修正流程第一關有設定必須上傳新附件，若從流程草稿開啟時，系統沒有卡控必須上傳新附件
  - 變更檔案: 1 個
- **2023-09-07 10:31:49**: [流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常
  - 變更檔案: 1 個
- **2023-06-02 15:48:05**: [WEB]Q00-20230602004 修正流程重要性在下列情境時，無法正確顯示設定的重要性 1.關卡設定列印模式，並點擊列印表單後 2.多人關卡只需一人處理，並點擊「由我處理」後 3.關卡派送失敗，畫面提示派送失敗後
  - 變更檔案: 3 個
- **2023-08-29 16:50:50**: [流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常
  - 變更檔案: 1 個
- **2023-08-29 10:32:40**: [流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據
  - 變更檔案: 2 個
- **2023-08-23 15:27:37**: [Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常
  - 變更檔案: 3 個
- **2023-08-14 15:59:34**: [在線閱覽]Q00-20230814002 調整在線閱讀浮水印機制，當浮水印內容有特殊字導致無法添加浮水印時，改使用預設內容「userId+閱讀時間」作為浮水印內容，避免使用者無法順利閱讀檔案
  - 變更檔案: 3 個
- **2023-08-02 16:07:19**: [流程引擎]V00-20230802001 修正流程引擎在核決關卡展開第一關時向前加簽或核決關卡展開的最後一關向後加簽時，若PerformWorkItemHandlerBean的log層級設定info以上時，系統會出現錯誤訊息，實際上加簽關卡已成功的異常
  - 變更檔案: 1 個
- **2023-08-02 13:48:33**: [在線閱覽]Q00-20230802001 修正在線閱覽管理-轉檔異常處理作業，當關閉「上傳PDF」的開窗畫面後，系統會彈跳服務錯誤的訊息
  - 變更檔案: 1 個
- **2023-07-25 15:27:07**: [Web]Q00-20230725002 修正發起流程切換預解析流程時，當系統設定有設定該流程只以主部門發起(invoke.process.by.main.unit.process.package.ids)時，若使用者有多個兼職部門時，切換流程圖不應讓使用者選擇參考部門
  - 變更檔案: 1 個
- **2023-07-10 11:38:17**: [其他]Q00-20230704001 調整BCL8單個檔案轉檔逾時時間由預設2分鐘改為預設10分鐘
  - 變更檔案: 1 個
- **2023-07-04 17:40:33**: [Web]Q00-20230704003 調整formScript撰寫ajax加簽關卡後，需要更新session裡面的ProcessInst的相關屬性(Processpackage,ProcessDef等屬性)，避免預覽流程仍以加簽前的定義做解析
  - 變更檔案: 1 個
- **2023-07-04 17:02:01**: [Web]Q00-20230704001 調整CommonAccessor.updateConnectedUserInfo()更新使用者時間的方法，當更新異常時，由前端畫面提示「更新線上時間失敗」改為後端serverlog記錄就好
  - 變更檔案: 2 個
- **2023-06-28 18:02:27**: [SAP]Q00-20230628003 修正SAP整合回寫呼叫SAP的invoke服務，當SAP回傳的訊息需存放在Grid時，若Grid內容為空時，可能會導致formInstance.fieldValues產生多組相同Grid代號的內容
  - 變更檔案: 1 個
- **2023-06-27 15:09:38**: [SAP]Q00-20230627003 SAP整合的invoke服務任務增加表單相關資訊log
  - 變更檔案: 1 個
- **2023-06-01 11:24:59**: [其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容
  - 變更檔案: 2 個

### 林致帆 (42 commits)

- **2023-10-12 15:30:00**: [表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗
  - 變更檔案: 1 個
- **2023-11-01 14:04:59**: [雙因素認證]Q00-*********** 修正LDAP登入輸入帳號錯誤不該影響登入畫面進錯誤頁面
  - 變更檔案: 1 個
- **2024-01-18 11:05:51**: [Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache
  - 變更檔案: 5 個
- **2024-01-17 13:48:14**: [Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache
  - 變更檔案: 5 個
- **2023-11-27 16:31:42**: [Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊
  - 變更檔案: 1 個
- **2023-11-01 14:10:52**: [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯[補修正]
  - 變更檔案: 1 個
- **2023-11-01 14:04:59**: [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯
  - 變更檔案: 5 個
- **2023-11-01 14:04:59**: [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯
  - 變更檔案: 5 個
- **2023-09-19 13:42:07**: [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"[補修正]
  - 變更檔案: 2 個
- **2023-09-18 14:02:51**: [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"
  - 變更檔案: 2 個
- **2023-09-22 10:42:52**: [Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加
  - 變更檔案: 1 個
- **2023-09-08 09:24:46**: [SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗
  - 變更檔案: 1 個
- **2023-09-01 14:46:19**: [WorkFlow]Q00-20230901003 修正附件URL帶有#字號會無法下載附件
  - 變更檔案: 1 個
- **2023-08-30 10:43:55**: [TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷
  - 變更檔案: 1 個
- **2023-08-29 15:50:48**: [ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗
  - 變更檔案: 2 個
- **2023-08-28 16:57:53**: [SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤
  - 變更檔案: 1 個
- **2023-08-15 17:13:54**: [WorkFlow]Q00-20230815004 新增WorkFlow回寫增加時間訊息
  - 變更檔案: 1 個
- **2023-08-10 12:00:14**: [Web]V00-20230810005 修正ORACLE使用者開窗在有離職人員的狀況下打開會異常
  - 變更檔案: 1 個
- **2023-08-02 18:54:36**: [流程引擎]Q00-20230802004 修正流程未設定"被處理者終止時逐級通知"應該只要發起人收到信件通知
  - 變更檔案: 1 個
- **2023-07-26 10:15:28**: [WEB]Q00-20230725004 修正主旨樣板選擇"允許修改主旨"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨[補修正]
  - 變更檔案: 1 個
- **2023-07-25 18:16:32**: [WEB]Q00-20230725004 修正主旨樣板選擇"允許修改主旨"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨
  - 變更檔案: 1 個
- **2023-07-10 10:49:59**: [ESS]Q00-20230710001 調整log錯誤訊息的顯示
  - 變更檔案: 1 個
- **2023-07-20 11:16:58**: [Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員 [補修正]
  - 變更檔案: 1 個
- **2023-07-19 14:28:32**: [Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員
  - 變更檔案: 1 個
- **2023-07-13 10:23:47**: [Web]Q00-20230713002 修正使用者為部門主管從Portal進入BPM點選首頁顯示內容不為主管首頁
  - 變更檔案: 1 個
- **2023-06-27 13:39:34**: [T100]Q00-20230627001 修正關卡設置"所有附件皆需開啟過"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送
  - 變更檔案: 1 個
- **2023-06-15 17:18:15**: [Web]Q00-20230615002 修正離職維護作業無法開啟
  - 變更檔案: 2 個
- **2023-06-12 12:02:20**: [Web]Q00-20230612003 修正Script撰寫Grid的setColumnWith語法會跳出alert
  - 變更檔案: 1 個
- **2023-06-12 11:27:32**: [在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕
  - 變更檔案: 1 個
- **2023-05-26 10:45:38**: [Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間[補修正]
  - 變更檔案: 1 個
- **2023-05-25 15:33:05**: [Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間
  - 變更檔案: 1 個
- **2023-06-07 15:58:33**: [Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系
  - 變更檔案: 1 個
- **2023-06-06 14:44:56**: [ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持"通知"選項
  - 變更檔案: 1 個
- **2023-06-02 11:45:57**: [WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料
  - 變更檔案: 1 個
- **2023-06-02 10:23:40**: [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]
  - 變更檔案: 1 個
- **2023-06-01 10:56:27**: [Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常
  - 變更檔案: 1 個
- **2023-06-01 12:04:53**: [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息
  - 變更檔案: 1 個
- **2023-05-31 17:47:43**: [WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤
  - 變更檔案: 1 個
- **2023-05-26 16:38:31**: [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
  - 變更檔案: 3 個
- **2023-05-24 17:36:47**: [流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常
  - 變更檔案: 1 個
- **2023-05-24 17:01:45**: [Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加
  - 變更檔案: 1 個
- **2023-05-26 10:36:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個

### 邱郁晏 (28 commits)

- **2024-01-08 11:52:45**: [Web] Q00-20240108001 調整絕對位置表單進版時，部分表單欄位反黑異常問題，新增防呆
  - 變更檔案: 1 個
- **2024-01-23 11:17:39**: [Web] Q00-20240123001 調整信件樣板設置為整張表單時，Grid元件跑版問題
  - 變更檔案: 1 個
- **2023-11-24 15:00:42**: [流程引擎] Q00-20231124004 修正批次簽核造成重複寄信問題
  - 變更檔案: 1 個
- **2023-11-14 15:54:22**: [Web] Q00-20231114003 修正寄件人重複顯示問題
  - 變更檔案: 1 個
- **2023-11-08 11:27:29**: [Web] Q00-20231108001 調整客製開窗新增註解以及改回原本與服務確認之邏輯
  - 變更檔案: 1 個
- **2023-11-07 17:20:53**: [Web] Q00-20231107005 修正客製開窗包子查詢，查詢欄位名稱有別名導致比對計數異常問題
  - 變更檔案: 1 個
- **2023-11-07 14:44:28**: [Web] Q00-20231107004 修正客製開窗包子查詢，多條件篩選時，別名沒有被過濾導致查詢條件異常
  - 變更檔案: 1 個
- **2023-11-06 17:47:59**: [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)
  - 變更檔案: 2 個
- **2023-11-03 11:12:20**: [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)
  - 變更檔案: 1 個
- **2023-10-25 18:14:01**: [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補修正)
  - 變更檔案: 1 個
- **2023-10-25 16:35:30**: [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題
  - 變更檔案: 1 個
- **2023-10-17 14:56:38**: [流程引擎] Q00-20231017004 修正工作受託者<#allAssigneesIDnName>沒有帶出資料的問題
  - 變更檔案: 1 個
- **2023-09-12 17:23:00**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
  - 變更檔案: 1 個
- **2023-08-08 12:24:06**: [Web] Q00-20230525002 調整匯出表單，日期格式異常問題
  - 變更檔案: 2 個
- **2023-09-12 10:23:55**: [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)
  - 變更檔案: 1 個
- **2023-09-12 10:06:27**: [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法
  - 變更檔案: 1 個
- **2023-09-06 12:03:56**: [Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。
  - 變更檔案: 1 個
- **2023-09-04 10:53:42**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
  - 變更檔案: 1 個
- **2023-08-31 15:41:41**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題
  - 變更檔案: 1 個
- **2023-08-24 13:43:54**: [Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。
  - 變更檔案: 1 個
- **2023-08-02 11:31:06**: [資安] S00-20211220001 S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信(補)
  - 變更檔案: 4 個
- **2023-08-01 16:27:12**: [資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信
  - 變更檔案: 6 個
- **2023-07-20 11:50:08**: [資安] Q00-20230718001 修正發送通知者寄送Mail，不應副本給通知者的問題。
  - 變更檔案: 4 個
- **2023-08-15 15:10:34**: [Web] V00-20230815001 修正無上傳簽名圖檔時，後端拋錯異常，新增防呆。
  - 變更檔案: 2 個
- **2023-07-20 13:44:37**: [Web] Q00-20230713005 新增登入登出LOG印出SessionId，修正模擬為同一IP時取瀏覽器資訊異常問題
  - 變更檔案: 2 個
- **2023-07-17 17:55:45**: [流程引擎] Q00-20230717003 修正終止流程時，偶發ProcessInstance與BamProInstData狀態不一致問題
  - 變更檔案: 5 個
- **2023-07-14 14:48:48**: [流程引擎] Q00-20230714001 修正服務任務關卡執行後，不會重新組成全文檢索欄位問題
  - 變更檔案: 1 個
- **2023-07-13 11:44:10**: [Web] Q00-20230713003 優化使用者登入時異常時的LOG
  - 變更檔案: 1 個

### 周权 (16 commits)

- **2023-08-09 11:49:25**: [Web]Q00-20230526002 修正關卡通知信設定以整張表單時，ListBox元件在信件上呈現應該要為顯示值
  - 變更檔案: 1 個
- **2023-11-15 13:51:34**: [Web] Q00-20231115001 调整流程代理人页面选择流程开窗，流程名称以多语系显示
  - 變更檔案: 1 個
- **2023-11-06 11:11:50**: [WEB]Q00-20231106001 调整Select元件在设置背景色时列印却显示唯读背景色的问题
  - 變更檔案: 1 個
- **2023-09-12 15:29:48**: [Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。
  - 變更檔案: 3 個
- **2023-09-11 15:39:19**: [Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。
  - 變更檔案: 1 個
- **2023-09-08 10:14:12**: [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]
  - 變更檔案: 2 個
- **2023-09-05 18:01:25**: [Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。
  - 變更檔案: 1 個
- **2023-09-06 13:19:12**: [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。
  - 變更檔案: 1 個
- **2023-08-17 11:05:11**: [Web]Q00-20230817001 修正有關卡設定為列印模式，從待辦事項點選列印時，格式會跑掉
  - 變更檔案: 1 個
- **2023-08-14 16:08:20**: [Web]V00-20230814001 元件在表單存取控管設為Invisible 绝对位置表单列印空白
  - 變更檔案: 4 個
- **2023-08-10 13:22:36**: [Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白[补修正]
  - 變更檔案: 1 個
- **2023-07-17 09:25:32**: [Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]
  - 變更檔案: 4 個
- **2023-07-18 12:40:40**: [流程引擎]Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序[补修正]
  - 變更檔案: 1 個
- **2023-07-14 17:36:50**: [Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。
  - 變更檔案: 2 個
- **2023-07-20 10:33:53**: [流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException[补修正]
  - 變更檔案: 1 個
- **2023-07-18 16:45:19**: [流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException
  - 變更檔案: 1 個

### kmin (5 commits)

- **2024-01-18 10:07:56**: Revert "[Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache"
  - 變更檔案: 5 個
- **2023-11-01 15:19:00**: Revert "[雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯"
  - 變更檔案: 5 個
- **2023-08-31 11:31:16**: Revert "[Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題"
  - 變更檔案: 2 個
- **2023-08-24 11:07:33**: 退件表單資訊檢視表單內容報錯，會報錯「Error occurred while get TraceProcess url ! 」
  - 變更檔案: 1 個
- **2023-08-23 16:34:08**: Revert "[Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常"
  - 變更檔案: 3 個

### cherryliao (13 commits)

- **2024-01-17 10:38:40**: [Web]Q00-20240117001 修正追蹤流程列表中任一流程有顯示關注訊息，手機模式下點擊流程就沒有反應的問題
  - 變更檔案: 1 個
- **2023-09-15 10:22:25**: [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]
  - 變更檔案: 1 個
- **2023-09-08 16:39:12**: [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題
  - 變更檔案: 1 個
- **2023-08-30 11:47:29**: [Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題
  - 變更檔案: 2 個
- **2023-08-30 11:47:29**: [Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題
  - 變更檔案: 2 個
- **2023-06-20 15:54:17**: [內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息
  - 變更檔案: 1 個
- **2023-07-31 15:59:38**: [表單設計師]Q00-20230731001 調整表單設計師縮小或切換頁簽後切回來操作沒立即更新使用者資訊的問題
  - 變更檔案: 2 個
- **2023-07-25 11:17:31**: [表單設計師]Q00-20230725001 調整選項元件進階設定選項開窗顯示值多語系有含單引號時確定按鈕異常的問題
  - 變更檔案: 2 個
- **2023-06-09 12:02:28**: [Web]Q00-20230609001 調整待辦流程開啟列印表單時Grid數據沒有加載的問題
  - 變更檔案: 1 個
- **2023-06-19 10:12:14**: [Web]Q00-20230619001 修正Grid元件在多欄位時欄位寬度異常顯示問題
  - 變更檔案: 2 個
- **2023-06-16 13:56:03**: [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題[補]
  - 變更檔案: 1 個
- **2023-06-06 13:57:44**: [Web]A00-20230605001 修正在待辦情況下將HandWriting元件透過Script設置disable時沒有作用問題
  - 變更檔案: 1 個
- **2023-06-07 14:51:50**: [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題
  - 變更檔案: 1 個

### raven.917 (29 commits)

- **2023-06-05 11:13:09**: [T100] Q00-20230605001 修正T100同步單別時，作業代號若有底線，造成同步異常問題
  - 變更檔案: 1 個
- **2023-06-12 13:57:42**: [Web] Q00-20230612004 修正絕對定位表單，上傳圖片異常問題。
  - 變更檔案: 1 個
- **2023-05-30 16:03:49**: [流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序(補修正)
  - 變更檔案: 2 個
- **2023-05-25 14:03:05**: [流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序
  - 變更檔案: 1 個
- **2023-06-20 17:03:38**: [在地化] Q00-20230620003 增加驗證SSOkey時，時間間隔超過5分鐘，印出LOG訊息
  - 變更檔案: 1 個
- **2023-07-11 10:24:08**: [流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題(補)
  - 變更檔案: 3 個
- **2023-07-07 14:39:22**: [流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題。
  - 變更檔案: 3 個
- **2023-07-06 15:22:50**: [組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。
  - 變更檔案: 1 個
- **2023-07-04 17:34:15**: [Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級(補)
  - 變更檔案: 1 個
- **2023-07-04 16:43:46**: [Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級
  - 變更檔案: 1 個
- **2023-07-03 16:31:00**: [Web] Q00-20230703004 修正表單列印畫面元件跑版問題，邊界調整為0
  - 變更檔案: 2 個
- **2023-06-27 14:41:06**: [TipTop] Q00-20230627002 調整TIPTOP的附件選擇txt時，上傳文件且未填說明欄位，轉檔異常問題。
  - 變更檔案: 1 個
- **2023-06-26 16:28:03**: [Tiptop] Q00-20230626002 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。
  - 變更檔案: 1 個
- **2023-06-21 11:44:24**: [Web] Q00-20230621003 修正Rwd-Grid 設置必填時，Alert訊息異常問題
  - 變更檔案: 1 個
- **2023-06-20 14:56:48**: [Web] Q00-20230620001 調整絕對定位表單追蹤流程下列印表單畫面。
  - 變更檔案: 3 個
- **2023-06-16 16:45:08**: [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)
  - 變更檔案: 2 個
- **2023-06-15 11:52:52**: [Web] Q00-20230615001 修正客制開窗order by轉小寫導致模糊查詢異常問題。
  - 變更檔案: 1 個
- **2023-06-05 11:41:57**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補)
  - 變更檔案: 1 個
- **2023-06-13 14:18:28**: [Web] Q00-20230613003 調整參與者型式的關卡頁面的「檢視轉派歷程」按鈕圖示。
  - 變更檔案: 1 個
- **2023-06-13 11:36:01**: [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)
  - 變更檔案: 1 個
- **2023-06-12 14:57:27**: [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。
  - 變更檔案: 1 個
- **2023-06-12 10:45:38**: [Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息(補修正)
  - 變更檔案: 3 個
- **2023-06-09 17:16:16**: [Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息
  - 變更檔案: 1 個
- **2023-06-09 15:13:05**: [Web] Q00-20230609004 修正匯入Excel資料內有,會被替換成空白問題
  - 變更檔案: 1 個
- **2023-06-05 17:14:49**: [流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求
  - 變更檔案: 1 個
- **2023-06-02 10:54:49**: [Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題
  - 變更檔案: 3 個
- **2023-05-26 14:59:32**: [Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題
  - 變更檔案: 1 個
- **2023-05-25 10:16:09**: [Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題
  - 變更檔案: 1 個
- **2023-05-25 19:35:08**: [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
  - 變更檔案: 1 個

### 刘旭 (8 commits)

- **2023-08-22 11:22:33**: [web]V00-20230817002 grid高度明明還夠卻長出了滾軸问题修复
  - 變更檔案: 1 個
- **2023-08-10 15:06:04**: [web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复[补修正]
  - 變更檔案: 1 個
- **2023-08-09 08:45:00**: [web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复[补修正]
  - 變更檔案: 1 個
- **2023-09-27 17:19:26**: [web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复
  - 變更檔案: 1 個
- **2023-08-29 14:02:09**: [web]Q00-20230829003 列印時附件資訊會超出邊界问题修复
  - 變更檔案: 1 個
- **2023-08-25 17:38:15**: [web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复
  - 變更檔案: 1 個
- **2023-07-17 14:37:58**: [web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复
  - 變更檔案: 1 個
- **2023-07-12 15:59:31**: [web]Q00-20230712003 修正在转存表单时栏位原數值為小數點後兩位，轉存表單後僅剩小數點後一位
  - 變更檔案: 1 個

### pinchi_lin (2 commits)

- **2023-08-28 11:15:49**: [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
  - 變更檔案: 1 個
- **2023-07-20 12:13:56**: [DT]C01-20230719005 修正設計師使用權限管理中的組織設計師清單其id與名稱顯示異常問題
  - 變更檔案: 3 個

### yamiyeh10 (3 commits)

- **2023-07-26 16:31:24**: [BPM APP]Q00-20230726002 修正移動端絕對位置表單中Grid元件單身數據有空值時組成格式不正確問題
  - 變更檔案: 1 個
- **2023-06-01 15:10:16**: [BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題
  - 變更檔案: 1 個
- **2023-05-26 10:10:30**: [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]
  - 變更檔案: 1 個

### xiaobai (2 commits)

- **2023-07-11 14:12:59**: [Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白。
  - 變更檔案: 1 個
- **2023-07-05 14:23:40**: [Web]Q00-20230705002 修正表單在预览时，更換image后，显示图片不正确的问题
  - 變更檔案: 2 個

### liuxua (2 commits)

- **2023-07-10 16:56:51**: [web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题
  - 變更檔案: 2 個
- **2023-07-10 16:51:34**: [web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题
  - 變更檔案: 1 個

### develop_20274 (5 commits)

- **2023-06-05 10:37:25**: [Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示
  - 變更檔案: 1 個
- **2023-05-30 10:27:14**: [Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態
  - 變更檔案: 1 個
- **2023-05-29 17:06:25**: [Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)
  - 變更檔案: 1 個
- **2023-05-29 16:54:46**: [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正
  - 變更檔案: 2 個
- **2023-05-26 15:02:51**: [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [Web] Q00-20240222001 修正列印模式FormUtil.getValue获取不到Textbox和HiddenTextbox值
- **Commit ID**: `a04792094b73ea63deb33b06daaeb7125aeb2263`
- **作者**: liuyun
- **日期**: 2024-02-22 10:25:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 2. [流程引擎]Q00-20231127001 修正關卡設定多人都要簽核且設定自動簽核2，與前一關相同者，若前一關為核決關卡，且未實際展開核決關卡時，流程無法派送至下一關的異常
- **Commit ID**: `8204524a433d1d75cffa21fc22ae46404dae8b37`
- **作者**: waynechang
- **日期**: 2023-11-27 15:20:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 3. [Web] Q00-20231124002 修正流程主旨範本設定<#workItemName>显示N.A.
- **Commit ID**: `bbc82c1ba653e213360c297694ea8ccf5dbf3680`
- **作者**: liuyun
- **日期**: 2023-11-24 10:33:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 4. [Web] Q00-20240202002 修正通过服务任务给表单赋值触发警告问题
- **Commit ID**: `6da794fbddc84852ed52b1c0321748a7bbe24eb7`
- **作者**: liuyun
- **日期**: 2024-02-02 17:51:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 5. [表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗
- **Commit ID**: `21a858ae1d0eb09a97e52eb4cf285886b5049175`
- **作者**: 林致帆
- **日期**: 2023-10-12 15:30:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 6. [Web] Q00-20240108001 調整絕對位置表單進版時，部分表單欄位反黑異常問題，新增防呆
- **Commit ID**: `6c6962f386a72074f5bd1f68d09e161d44b7601e`
- **作者**: 邱郁晏
- **日期**: 2024-01-08 11:52:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java`

### 7. [Web] Q00-20240123001 調整信件樣板設置為整張表單時，Grid元件跑版問題
- **Commit ID**: `347551616e2df32303b205e7359bd0c32ff733fa`
- **作者**: 邱郁晏
- **日期**: 2024-01-23 11:17:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 8. [Web]Q00-20230526002 修正關卡通知信設定以整張表單時，ListBox元件在信件上呈現應該要為顯示值
- **Commit ID**: `64777bf3b1997124863b2ef06b8e599952368df0`
- **作者**: 周权
- **日期**: 2023-08-09 11:49:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 9. [雙因素認證]Q00-*********** 修正LDAP登入輸入帳號錯誤不該影響登入畫面進錯誤頁面
- **Commit ID**: `d910f5aecdb1d23b7bb878e5f2f26778cb1c811e`
- **作者**: 林致帆
- **日期**: 2023-11-01 14:04:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`

### 10. [Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache
- **Commit ID**: `30d50ebb8553747d9b8dd7d58a5c1888a5f14fe9`
- **作者**: 林致帆
- **日期**: 2024-01-18 11:05:51
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 11. Revert "[Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache"
- **Commit ID**: `fd53d93b9ce112a174e7bd62c97f99ccd2ee177f`
- **作者**: kmin
- **日期**: 2024-01-18 10:07:56
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 12. [Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache
- **Commit ID**: `03f874901569572611b1fc65d7f51c97d54653e8`
- **作者**: 林致帆
- **日期**: 2024-01-17 13:48:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 13. [Web]Q00-20240117001 修正追蹤流程列表中任一流程有顯示關注訊息，手機模式下點擊流程就沒有反應的問題
- **Commit ID**: `cb2da1cc3a3270ec0446d72598594a94e219af8f`
- **作者**: cherryliao
- **日期**: 2024-01-17 10:38:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 14. [流程引擎]Q00-20231120003 修正監控流程的詳細流程圖頁面，當按下「跳過此關卡」功能時，下一關處理者無法收到代辦通知信件
- **Commit ID**: `239024429f6c6a01adc062cc061cfb4ff209eeb8`
- **作者**: waynechang
- **日期**: 2023-11-20 17:25:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 15. [Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊
- **Commit ID**: `6a271d18e83c7e52a8c19b3b35b8c0318b5cc412`
- **作者**: 林致帆
- **日期**: 2023-11-27 16:31:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 16. [SAP]Q00-20231124005 優化SAP整合服務，當整合的資料類型為Grid時，同時支持RWD表單Grid及絕對位置表單Grid
- **Commit ID**: `99834fb15080ebda932a52edde5057b89089bbbc`
- **作者**: waynechang
- **日期**: 2023-11-24 17:33:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 17. [T100] Q00-20230605001 修正T100同步單別時，作業代號若有底線，造成同步異常問題
- **Commit ID**: `24ff0555aeab15fd38f56788e18cf91aeb3adb26`
- **作者**: raven.917
- **日期**: 2023-06-05 11:13:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 18. [流程引擎] Q00-20231124004 修正批次簽核造成重複寄信問題
- **Commit ID**: `5a51bcb6395bac63ba2462b7a7a2cf42f0c17570`
- **作者**: 邱郁晏
- **日期**: 2023-11-24 15:00:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 19. [SAP]Q00-20231121001 修正SAP整合，當mapping內容有Grid時，可能會有GridColumnId與GridValue順序錯誤的異常
- **Commit ID**: `b4b0c929aee8e00e49d5d0226c8c226614d5894e`
- **作者**: waynechang
- **日期**: 2023-11-21 11:55:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 20. [Web] Q00-20231115001 调整流程代理人页面选择流程开窗，流程名称以多语系显示
- **Commit ID**: `e2151bd163afeeab1e4b57020860b0558ed581d3`
- **作者**: 周权
- **日期**: 2023-11-15 13:51:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 21. [Web] Q00-20231114003 修正寄件人重複顯示問題
- **Commit ID**: `a00897ce4ac4960fc73ba7955a49ed4efdbe3296`
- **作者**: 邱郁晏
- **日期**: 2023-11-14 15:54:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 22. [Web] Q00-20231108001 調整客製開窗新增註解以及改回原本與服務確認之邏輯
- **Commit ID**: `36b5e68855e9f1054292404e4d53a738264ff3c2`
- **作者**: 邱郁晏
- **日期**: 2023-11-08 11:27:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 23. [Web] Q00-20231107005 修正客製開窗包子查詢，查詢欄位名稱有別名導致比對計數異常問題
- **Commit ID**: `c83318ccc4231455dddeca304b90014dc983520a`
- **作者**: 邱郁晏
- **日期**: 2023-11-07 17:20:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 24. [Web] Q00-20231107004 修正客製開窗包子查詢，多條件篩選時，別名沒有被過濾導致查詢條件異常
- **Commit ID**: `6aea9d7919108e1842914937b453ef8c063ac40e`
- **作者**: 邱郁晏
- **日期**: 2023-11-07 14:44:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 25. [web]V00-20230817002 grid高度明明還夠卻長出了滾軸问题修复
- **Commit ID**: `da2d827d286882da0387391ee07b2fec22309365`
- **作者**: 刘旭
- **日期**: 2023-08-22 11:22:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 26. [web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复[补修正]
- **Commit ID**: `4f5f5d9aeec9684a8d7c5f1bac34d80bc517e241`
- **作者**: 刘旭
- **日期**: 2023-08-10 15:06:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 27. [web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复[补修正]
- **Commit ID**: `f9721b17c623a12fd9aecf4ae0124e20e89517ad`
- **作者**: 刘旭
- **日期**: 2023-08-09 08:45:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 28. [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)
- **Commit ID**: `94a6093b15512693f8d6089ab4e405d826f063ba`
- **作者**: 邱郁晏
- **日期**: 2023-11-06 17:47:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java`

### 29. [WEB]Q00-20231106001 调整Select元件在设置背景色时列印却显示唯读背景色的问题
- **Commit ID**: `d2dfba24e2835864b6e3b290306e7624ffec92b9`
- **作者**: 周权
- **日期**: 2023-11-06 11:11:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 30. [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)
- **Commit ID**: `83c5daf63346025150d34896ddfdff99d0e24cfd`
- **作者**: 邱郁晏
- **日期**: 2023-11-03 11:12:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 31. [T100] Q00-20231102002 T100的签核历程沒有权限的显示提示告知登入者
- **Commit ID**: `22dfdebd28267a022664fa2d2939c91068d34e08`
- **作者**: liuyun
- **日期**: 2023-11-02 13:12:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessInfoGet.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`

### 32. [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯[補修正]
- **Commit ID**: `526bf2d3b7058e881ca91e69d4b195908b40d5f6`
- **作者**: 林致帆
- **日期**: 2023-11-01 14:10:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 33. [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯
- **Commit ID**: `4dcd4b952697dbc49a37bdb942400911178e317a`
- **作者**: 林致帆
- **日期**: 2023-11-01 14:04:59
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 34. Revert "[雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯"
- **Commit ID**: `6296e21db8d982e0e62365e99d0ed5cf7be3b7bf`
- **作者**: kmin
- **日期**: 2023-11-01 15:19:00
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 35. [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯
- **Commit ID**: `a60b6b7a401714b9d7e5ac6edf7bdae4b4e68aad`
- **作者**: 林致帆
- **日期**: 2023-11-01 14:04:59
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 36. [Web] Q00-20231031001 修正缩小ESSPlus管理页面时，查询出来的结果在grid显示不全
- **Commit ID**: `cddb9635c668d666883d80a616e7730ede0ec293`
- **作者**: liuyun
- **日期**: 2023-10-31 13:39:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`

### 37. [流程引擎]Q00-20231030001 修正流程發起者於追蹤流程頁面進入表單畫面點擊撤銷流程後，詳細流程圖的流程詳細資訊應為是「流程發起者」而非「流程負責人」撤銷
- **Commit ID**: `917beaab7284ef94a955a967ef79f32598015d2d`
- **作者**: waynechang
- **日期**: 2023-10-30 14:41:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 38. [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補修正)
- **Commit ID**: `4dbd51af03cd00b2266367045e00d8dafe7eae70`
- **作者**: 邱郁晏
- **日期**: 2023-10-25 18:14:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 39. [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題
- **Commit ID**: `aa9cda9ec03a6851fd99ec581af7f9bb064fe949`
- **作者**: 邱郁晏
- **日期**: 2023-10-25 16:35:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 40. [Web]Q00-20231023001 修正列印预览grid标题字体颜色为白色
- **Commit ID**: `a50a396a1f0c150c5ec5016cb7bf001413a4d81a`
- **作者**: liuyun
- **日期**: 2023-10-23 09:15:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 41. [Web]Q00-20231023002 修正流程第一關有設定必須上傳新附件，若從流程草稿開啟時，系統沒有卡控必須上傳新附件
- **Commit ID**: `608053531fc9a865d4ca10f36acf58b70cac929f`
- **作者**: waynechang
- **日期**: 2023-10-23 11:10:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`

### 42. [流程引擎] Q00-20231017004 修正工作受託者<#allAssigneesIDnName>沒有帶出資料的問題
- **Commit ID**: `0dd1e4a368886069d5d993f9a8cdd233b9925628`
- **作者**: 邱郁晏
- **日期**: 2023-10-17 14:56:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 43. [web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复
- **Commit ID**: `cad867e7c3261f896b6dea9538fa61418f3d7637`
- **作者**: 刘旭
- **日期**: 2023-09-27 17:19:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java`

### 44. [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"[補修正]
- **Commit ID**: `d607c663c580299436be957c5d57e303798d774c`
- **作者**: 林致帆
- **日期**: 2023-09-19 13:42:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 45. [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"
- **Commit ID**: `1f4cca5c0feb97b845a2f839fdde41778ccb3a79`
- **作者**: 林致帆
- **日期**: 2023-09-18 14:02:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 46. [Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效
- **Commit ID**: `7236f4b30af0d33b4fd8d832bcb7c6a36e69967e`
- **作者**: liuyun
- **日期**: 2023-09-22 11:21:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`

### 47. [Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加
- **Commit ID**: `35a0543c1eff89080f13dc7dcf24417692017aa3`
- **作者**: 林致帆
- **日期**: 2023-09-22 10:42:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 48. [Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错
- **Commit ID**: `3d41e2bf61f09e9e4604d0d3018f9d965d0df3c2`
- **作者**: liuyun
- **日期**: 2023-09-22 10:27:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 49. [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]
- **Commit ID**: `e2c1e2c5850dae06b74b37f150954918d78c199f`
- **作者**: cherryliao
- **日期**: 2023-09-15 10:22:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 50. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
- **Commit ID**: `acd395797f0154f45b6cba933d9e9b0375904654`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 17:23:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 51. [Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误
- **Commit ID**: `5cc9b58ed846c24a63370be29b26dd25309af4d7`
- **作者**: liuyun
- **日期**: 2023-09-11 11:47:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 52. [Web] Q00-20230525002 調整匯出表單，日期格式異常問題
- **Commit ID**: `994b0541df08d1e73a013d470cb5d40bae184343`
- **作者**: 邱郁晏
- **日期**: 2023-08-08 12:24:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 53. [Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。
- **Commit ID**: `c6080dbe6a5608921018b8c39b5c08d66e41bdc7`
- **作者**: 周权
- **日期**: 2023-09-12 15:29:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 54. [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)
- **Commit ID**: `e1038cd01d78326a5b7ec299468a253a869b5283`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 10:23:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 55. [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法
- **Commit ID**: `028239c8ff5f8718df4a7fbafd6ea208a0afbce0`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 10:06:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 56. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】
- **Commit ID**: `ab5f36018b2933c11b898f90acd61e65b5185450`
- **作者**: liuyun
- **日期**: 2023-09-11 16:08:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 57. [Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。
- **Commit ID**: `bde1d52bac144a6b7b6517284d5906c864bb1085`
- **作者**: 周权
- **日期**: 2023-09-11 15:39:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`

### 58. [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題
- **Commit ID**: `6a9abd60d6cb81d3d56549204f6c2b9ca80eb652`
- **作者**: cherryliao
- **日期**: 2023-09-08 16:39:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 59. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】
- **Commit ID**: `631af3cc5c1c7165bade8f7b02a23f9b6cffc383`
- **作者**: liuyun
- **日期**: 2023-09-07 17:54:10
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 60. [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]
- **Commit ID**: `0ae1c43eb6fc5fe0b1c386b41a5c86c332749711`
- **作者**: 周权
- **日期**: 2023-09-08 10:14:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`

### 61. [SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗
- **Commit ID**: `4f6c70f36160445a81b66df378df379905aca331`
- **作者**: 林致帆
- **日期**: 2023-09-08 09:24:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 62. [流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常
- **Commit ID**: `1cb9eb243f30171baed17b34158d6f655b7dd813`
- **作者**: waynechang
- **日期**: 2023-09-07 10:31:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 63. [Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。
- **Commit ID**: `4bb48129c9e0fc35fa4e3c354407751fe6123339`
- **作者**: 周权
- **日期**: 2023-09-05 18:01:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 64. [Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。
- **Commit ID**: `98f5c6f4efe51046ba0f5d2d2d9a90bbbf989eb4`
- **作者**: 邱郁晏
- **日期**: 2023-09-06 12:03:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`

### 65. [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。
- **Commit ID**: `b90447ed38c62e399327fa2eb1aad8007ac6bd30`
- **作者**: 周权
- **日期**: 2023-09-06 13:19:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`

### 66. [Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白
- **Commit ID**: `d15587383a61e354a2cbff76a5db7ee18960b18f`
- **作者**: liuyun
- **日期**: 2023-09-06 11:11:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 67. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
- **Commit ID**: `f16c21c7841cbe6f22e79b123bf6b474ad217cde`
- **作者**: 邱郁晏
- **日期**: 2023-09-04 10:53:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 68. [WEB]Q00-20230602004 修正流程重要性在下列情境時，無法正確顯示設定的重要性 1.關卡設定列印模式，並點擊列印表單後 2.多人關卡只需一人處理，並點擊「由我處理」後 3.關卡派送失敗，畫面提示派送失敗後
- **Commit ID**: `34cdf2a6c24de677a20cabcc0254708c0d8edba3`
- **作者**: waynechang
- **日期**: 2023-06-02 15:48:05
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 69. [WorkFlow]Q00-20230901003 修正附件URL帶有#字號會無法下載附件
- **Commit ID**: `bfaf8da412e6b5b2877f49dac64c930cf6bab538`
- **作者**: 林致帆
- **日期**: 2023-09-01 14:46:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/TiptopUtil.java`

### 70. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題
- **Commit ID**: `3807b7d9d9ee23759c6db9dc1ad678192f635b33`
- **作者**: 邱郁晏
- **日期**: 2023-08-31 15:41:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 71. [Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題
- **Commit ID**: `20022aba0e117abcbb0f44dd6835a5fbb67aaadd`
- **作者**: cherryliao
- **日期**: 2023-08-30 11:47:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 72. Revert "[Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題"
- **Commit ID**: `05f62ac46944168d4041cd9620d85613ff2d0fdb`
- **作者**: kmin
- **日期**: 2023-08-31 11:31:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 73. [Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題
- **Commit ID**: `7f3bea1f90b1fcacaf3daacedb3cf4b108f15177`
- **作者**: cherryliao
- **日期**: 2023-08-30 11:47:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 74. [TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷
- **Commit ID**: `70d5e25d249d195759511eb3dd8a789b4cb66051`
- **作者**: 林致帆
- **日期**: 2023-08-30 10:43:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 75. [ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗
- **Commit ID**: `71677e6500f2bd78c10d17c0acfd4f9935f28991`
- **作者**: 林致帆
- **日期**: 2023-08-29 15:50:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 76. [流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常
- **Commit ID**: `a9148619857a85c5d573fad98967cab455441017`
- **作者**: waynechang
- **日期**: 2023-08-29 16:50:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 77. [web]Q00-20230829003 列印時附件資訊會超出邊界问题修复
- **Commit ID**: `bee1ee547921492000df78867685f9c3088e3db6`
- **作者**: 刘旭
- **日期**: 2023-08-29 14:02:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 78. [SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤
- **Commit ID**: `bc13766064ca23e58da713dabb0a0fa0236041c2`
- **作者**: 林致帆
- **日期**: 2023-08-28 16:57:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 79. [流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據
- **Commit ID**: `619539a413804b28a72f6976a3bcac02968937c2`
- **作者**: waynechang
- **日期**: 2023-08-29 10:32:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 80. [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
- **Commit ID**: `31cda5b613ef208aaa8c141bffdaa87f2569d058`
- **作者**: pinchi_lin
- **日期**: 2023-08-28 11:15:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 81. [web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复
- **Commit ID**: `8bd98800a3a0c1b1fd439368862729a2c8bd9fcc`
- **作者**: 刘旭
- **日期**: 2023-08-25 17:38:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 82. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面
- **Commit ID**: `6667ae6e592b047491a788b1c0be5f06208b5c1f`
- **作者**: liuyun
- **日期**: 2023-08-25 15:40:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 83. [內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息
- **Commit ID**: `e89006d5b4236b075f8b17a3af82b43a9a7043c1`
- **作者**: cherryliao
- **日期**: 2023-06-20 15:54:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 84. [Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。
- **Commit ID**: `119d393f42a4cf822ff8a12438297c35bce24997`
- **作者**: 邱郁晏
- **日期**: 2023-08-24 13:43:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 85. 退件表單資訊檢視表單內容報錯，會報錯「Error occurred while get TraceProcess url ! 」
- **Commit ID**: `4be402dc2ec8727c12f2b1185b0b193ba6267036`
- **作者**: kmin
- **日期**: 2023-08-24 11:07:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkStep.java`

### 86. Revert "[Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常"
- **Commit ID**: `ef00c96c8c10e47f5a8fe382360cde638829ac85`
- **作者**: kmin
- **日期**: 2023-08-23 16:34:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

### 87. [Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常
- **Commit ID**: `69751bf1a4223d95f119f00ed7c344e990b653fa`
- **作者**: waynechang
- **日期**: 2023-08-23 15:27:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

### 88. [資安] S00-20211220001 S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信(補)
- **Commit ID**: `05d72e6d520fada627b175c8151fa780b7268171`
- **作者**: 邱郁晏
- **日期**: 2023-08-02 11:31:06
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 89. [資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信
- **Commit ID**: `4ea7c2636820889857a89eb1b190c576cf5aadb8`
- **作者**: 邱郁晏
- **日期**: 2023-08-01 16:27:12
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.3_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.3_DML_Oracle.sql`

### 90. [資安] Q00-20230718001 修正發送通知者寄送Mail，不應副本給通知者的問題。
- **Commit ID**: `6298acb838c74bb082f68d4e3a8c3079018e247c`
- **作者**: 邱郁晏
- **日期**: 2023-07-20 11:50:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 91. [Web] Q00-20230817004 修改手机版追踪流程，取回重办、撤销流程显示两个时间
- **Commit ID**: `62e51be08467200ab0fa21aa45f18dface5c56ae`
- **作者**: liuyun
- **日期**: 2023-08-18 10:41:47
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 92. [Web]Q00-20230817001 修正有關卡設定為列印模式，從待辦事項點選列印時，格式會跑掉
- **Commit ID**: `bed691d4bf30566611d01ff417578e18f51205be`
- **作者**: 周权
- **日期**: 2023-08-17 11:05:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 93. [Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常 [补修正]
- **Commit ID**: `55b7791cd3c98498cc968b169e24178b7a81b0ef`
- **作者**: liuyun
- **日期**: 2023-08-16 12:08:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 94. [Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常
- **Commit ID**: `32677eef161658c2cab1dbcd3aa3647f24e66462`
- **作者**: liuyun
- **日期**: 2023-08-15 13:57:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 95. [Web] Q00-20230612004 修正絕對定位表單，上傳圖片異常問題。
- **Commit ID**: `76f3b43150049bd8afe4dbbe7135103cc8e02a4c`
- **作者**: raven.917
- **日期**: 2023-06-12 13:57:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 96. [Web] V00-20230815001 修正無上傳簽名圖檔時，後端拋錯異常，新增防呆。
- **Commit ID**: `c1fb067e6f7467ed3108f7dfd916a900917c35cb`
- **作者**: 邱郁晏
- **日期**: 2023-08-15 15:10:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 97. [WorkFlow]Q00-20230815004 新增WorkFlow回寫增加時間訊息
- **Commit ID**: `6e3b5f42bf955a2029a00794d37acceb997f29ca`
- **作者**: 林致帆
- **日期**: 2023-08-15 17:13:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 98. [Web]V00-20230810005 修正ORACLE使用者開窗在有離職人員的狀況下打開會異常
- **Commit ID**: `c11a0fbaa41a99a89b255f760611d3e459590213`
- **作者**: 林致帆
- **日期**: 2023-08-10 12:00:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 99. [Web]V00-20230814001 元件在表單存取控管設為Invisible 绝对位置表单列印空白
- **Commit ID**: `15a7b148bbb5dd47cf5b6230f8f72a73d82c00c1`
- **作者**: 周权
- **日期**: 2023-08-14 16:08:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 100. [Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白[补修正]
- **Commit ID**: `136c32b278339d60263a625a444bb6643ecefeab`
- **作者**: 周权
- **日期**: 2023-08-10 13:22:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 101. [在線閱覽]Q00-20230814002 調整在線閱讀浮水印機制，當浮水印內容有特殊字導致無法添加浮水印時，改使用預設內容「userId+閱讀時間」作為浮水印內容，避免使用者無法順利閱讀檔案
- **Commit ID**: `a192059f13d5ca6f6d51a507434a7163f0fcb5aa`
- **作者**: waynechang
- **日期**: 2023-08-14 15:59:34
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 102. [Web] Q00-20230811001 修正BPM首页手机端流程有两个时间显示
- **Commit ID**: `f34347c441444e1744b0cea51be4d68e30236b57`
- **作者**: liuyun
- **日期**: 2023-08-11 15:36:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 103. [流程引擎]Q00-20230802004 修正流程未設定"被處理者終止時逐級通知"應該只要發起人收到信件通知
- **Commit ID**: `0d07714fdcece81f2ea82497346ce29308e48e29`
- **作者**: 林致帆
- **日期**: 2023-08-02 18:54:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 104. [表單設計師]Q00-20230731001 調整表單設計師縮小或切換頁簽後切回來操作沒立即更新使用者資訊的問題
- **Commit ID**: `c4cfed03c122d56573a1c0b4790825fe30b511d3`
- **作者**: cherryliao
- **日期**: 2023-07-31 15:59:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 105. [流程引擎]V00-20230802001 修正流程引擎在核決關卡展開第一關時向前加簽或核決關卡展開的最後一關向後加簽時，若PerformWorkItemHandlerBean的log層級設定info以上時，系統會出現錯誤訊息，實際上加簽關卡已成功的異常
- **Commit ID**: `0e379c71b448b2e2104b6f3ed5bbb0e890cccc71`
- **作者**: waynechang
- **日期**: 2023-08-02 16:07:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 106. [在線閱覽]Q00-20230802001 修正在線閱覽管理-轉檔異常處理作業，當關閉「上傳PDF」的開窗畫面後，系統會彈跳服務錯誤的訊息
- **Commit ID**: `65f50bed63c4be1493df5b803a20fbd251fdc330`
- **作者**: waynechang
- **日期**: 2023-08-02 13:48:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp`

### 107. [BPM APP]Q00-20230726002 修正移動端絕對位置表單中Grid元件單身數據有空值時組成格式不正確問題
- **Commit ID**: `cd12b56b261c5b37331e93b0df691b56915bb528`
- **作者**: yamiyeh10
- **日期**: 2023-07-26 16:31:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 108. [WEB]Q00-20230725004 修正主旨樣板選擇"允許修改主旨"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨[補修正]
- **Commit ID**: `653a28397caf5df5545fc44e54f844efff5f0b25`
- **作者**: 林致帆
- **日期**: 2023-07-26 10:15:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 109. [WEB]Q00-20230725004 修正主旨樣板選擇"允許修改主旨"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨
- **Commit ID**: `264e17cfac1401d2de40019376306f3a64617bc6`
- **作者**: 林致帆
- **日期**: 2023-07-25 18:16:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 110. [Web]Q00-20230725002 修正發起流程切換預解析流程時，當系統設定有設定該流程只以主部門發起(invoke.process.by.main.unit.process.package.ids)時，若使用者有多個兼職部門時，切換流程圖不應讓使用者選擇參考部門
- **Commit ID**: `b6d7a2e0fb05489f89f35540a545a63918d942f8`
- **作者**: waynechang
- **日期**: 2023-07-25 15:27:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 111. [表單設計師]Q00-20230725001 調整選項元件進階設定選項開窗顯示值多語系有含單引號時確定按鈕異常的問題
- **Commit ID**: `d9977a78ea7353126c8d315d324efca8985281b4`
- **作者**: cherryliao
- **日期**: 2023-07-25 11:17:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 112. [Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]
- **Commit ID**: `8cce2261fc5f227f5106e398481c42cca377cea4`
- **作者**: 周权
- **日期**: 2023-07-17 09:25:32
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 113. [流程引擎]Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序[补修正]
- **Commit ID**: `ac8ef6489b530b4906d10748851897418222fd1b`
- **作者**: 周权
- **日期**: 2023-07-18 12:40:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 114. [Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。
- **Commit ID**: `386562baff8e1cfd0c6e853df74435fe0cd596d9`
- **作者**: 周权
- **日期**: 2023-07-14 17:36:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 115. [流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序(補修正)
- **Commit ID**: `ec2aaa8a764e59ab64ccd11bf93b78bfb91fb6e6`
- **作者**: raven.917
- **日期**: 2023-05-30 16:03:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActivityInstanceComparator.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 116. [流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序
- **Commit ID**: `13cc2014085068c6433b880199df9daf4519b499`
- **作者**: raven.917
- **日期**: 2023-05-25 14:03:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 117. [ESS]Q00-20230710001 調整log錯誤訊息的顯示
- **Commit ID**: `79c9a925292248de488b5944292104a6ade61401`
- **作者**: 林致帆
- **日期**: 2023-07-10 10:49:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 118. [Web] Q00-20230713005 新增登入登出LOG印出SessionId，修正模擬為同一IP時取瀏覽器資訊異常問題
- **Commit ID**: `2c6a995677ed1a4d523e715c2685b88a7f62d59b`
- **作者**: 邱郁晏
- **日期**: 2023-07-20 13:44:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 119. [在地化] Q00-20230620003 增加驗證SSOkey時，時間間隔超過5分鐘，印出LOG訊息
- **Commit ID**: `e0e066dfdb1fa81d7dde0a12847582c19a056424`
- **作者**: raven.917
- **日期**: 2023-06-20 17:03:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 120. [DT]C01-20230719005 修正設計師使用權限管理中的組織設計師清單其id與名稱顯示異常問題
- **Commit ID**: `3c2b454bdefe63b2353f5ef81b66dec59cea18c3`
- **作者**: pinchi_lin
- **日期**: 2023-07-20 12:13:56
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/ParticipantInfoAcquirer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/AccessCrtlMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java`

### 121. [Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員 [補修正]
- **Commit ID**: `aafe83f9744a0522fa4b16467f8fc1f6b559336f`
- **作者**: 林致帆
- **日期**: 2023-07-20 11:16:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 122. [流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException[补修正]
- **Commit ID**: `edabb9e0cb8722c57fd8ddd88d5d79c8bec2ed7a`
- **作者**: 周权
- **日期**: 2023-07-20 10:33:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 123. [流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException
- **Commit ID**: `fc582f6da64668f93d0c6ffaa029deed2235e987`
- **作者**: 周权
- **日期**: 2023-07-18 16:45:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 124. [Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員
- **Commit ID**: `0f788d638ad67ac07d9b8d81d2a0e3d0f024c7a0`
- **作者**: 林致帆
- **日期**: 2023-07-19 14:28:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 125. [流程引擎] Q00-20230717003 修正終止流程時，偶發ProcessInstance與BamProInstData狀態不一致問題
- **Commit ID**: `c00c08cb53e93fa28864627f6e82c57ee87f3bb8`
- **作者**: 邱郁晏
- **日期**: 2023-07-17 17:55:45
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`

### 126. [web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复
- **Commit ID**: `2f528ac2e10384e8bc2909b3d893c6614bf50cdb`
- **作者**: 刘旭
- **日期**: 2023-07-17 14:37:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 127. [流程引擎] Q00-20230714001 修正服務任務關卡執行後，不會重新組成全文檢索欄位問題
- **Commit ID**: `8836d2fef061b8277612950109ca6c9d06d1915c`
- **作者**: 邱郁晏
- **日期**: 2023-07-14 14:48:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 128. [Web] Q00-20230713003 優化使用者登入時異常時的LOG
- **Commit ID**: `62d8599eafb1afd676fc0759806242adb502d48a`
- **作者**: 邱郁晏
- **日期**: 2023-07-13 11:44:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/persistence/PersistentObjectHelper.java`

### 129. [Web]Q00-20230713002 修正使用者為部門主管從Portal進入BPM點選首頁顯示內容不為主管首頁
- **Commit ID**: `8c3136f94185f1a6a7ae4cd4caed76991059cb0a`
- **作者**: 林致帆
- **日期**: 2023-07-13 10:23:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 130. [web]Q00-20230712003 修正在转存表单时栏位原數值為小數點後兩位，轉存表單後僅剩小數點後一位
- **Commit ID**: `8460f5b46051afe0a1bf0d9ea9d70e36b25a4b41`
- **作者**: 刘旭
- **日期**: 2023-07-12 15:59:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 131. [Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白。
- **Commit ID**: `1340937f84f9f2c661d156f10b560fdf7070e44d`
- **作者**: xiaobai
- **日期**: 2023-07-11 14:12:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 132. [流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題(補)
- **Commit ID**: `4148e0147df5360584669f5a36c4c6d37b30af64`
- **作者**: raven.917
- **日期**: 2023-07-11 10:24:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`

### 133. [流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題。
- **Commit ID**: `c2a731f0ba096ff654dd7ff1958831b92a28550b`
- **作者**: raven.917
- **日期**: 2023-07-07 14:39:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`

### 134. [web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题
- **Commit ID**: `5ae761b8897e3299c43924e6b126a748d53654ae`
- **作者**: liuxua
- **日期**: 2023-07-10 16:56:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`

### 135. [web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题
- **Commit ID**: `7c967dc6e64de3e36dcfc2a6743fe64736beed0c`
- **作者**: liuxua
- **日期**: 2023-07-10 16:51:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`

### 136. [其他]Q00-20230704001 調整BCL8單個檔案轉檔逾時時間由預設2分鐘改為預設10分鐘
- **Commit ID**: `7c4a1138898a33d8aaf6fac7f8040c41c183d3d6`
- **作者**: waynechang
- **日期**: 2023-07-10 11:38:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 137. [組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。
- **Commit ID**: `d3145c639005ba13adaeb4e0ee35d7716266bada`
- **作者**: raven.917
- **日期**: 2023-07-06 15:22:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 138. [Web]Q00-20230705002 修正表單在预览时，更換image后，显示图片不正确的问题
- **Commit ID**: `f9286e2062de27ff6f0ec71845f20857cb357f8d`
- **作者**: xiaobai
- **日期**: 2023-07-05 14:23:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ImageElement.java`

### 139. [Web]Q00-20230704003 調整formScript撰寫ajax加簽關卡後，需要更新session裡面的ProcessInst的相關屬性(Processpackage,ProcessDef等屬性)，避免預覽流程仍以加簽前的定義做解析
- **Commit ID**: `b8bf4fe66412e250d96f526e456fdf27cb66c531`
- **作者**: waynechang
- **日期**: 2023-07-04 17:40:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 140. [Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級(補)
- **Commit ID**: `e61a884d84b124afd3f9fe7ce225455f5494d141`
- **作者**: raven.917
- **日期**: 2023-07-04 17:34:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 141. [Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級
- **Commit ID**: `2f41906d8ea89b45960d13034fecd607eece5268`
- **作者**: raven.917
- **日期**: 2023-07-04 16:43:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 142. [Web]Q00-20230704001 調整CommonAccessor.updateConnectedUserInfo()更新使用者時間的方法，當更新異常時，由前端畫面提示「更新線上時間失敗」改為後端serverlog記錄就好
- **Commit ID**: `b83a5ca0fc7355b63c5a0b7c0ae1b1ae2903d4c4`
- **作者**: waynechang
- **日期**: 2023-07-04 17:02:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js`

### 143. [Web] Q00-20230703004 修正表單列印畫面元件跑版問題，邊界調整為0
- **Commit ID**: `80c003dc533af2a497d5589f2639c1220d34f0ea`
- **作者**: raven.917
- **日期**: 2023-07-03 16:31:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 144. [Web]Q00-20230609001 調整待辦流程開啟列印表單時Grid數據沒有加載的問題
- **Commit ID**: `725e5ca7380785ed5c5b1c2eadfcf137301f99d1`
- **作者**: cherryliao
- **日期**: 2023-06-09 12:02:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`

### 145. [SAP]Q00-20230628003 修正SAP整合回寫呼叫SAP的invoke服務，當SAP回傳的訊息需存放在Grid時，若Grid內容為空時，可能會導致formInstance.fieldValues產生多組相同Grid代號的內容
- **Commit ID**: `496c3900bcfcfa0ee21a76f9f83e3c67d2d93990`
- **作者**: waynechang
- **日期**: 2023-06-28 18:02:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormInstance.java`

### 146. [SAP]Q00-20230627003 SAP整合的invoke服務任務增加表單相關資訊log
- **Commit ID**: `3d4c925a54cd3b1935c5cef028033a6f1b9458e2`
- **作者**: waynechang
- **日期**: 2023-06-27 15:09:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrInvoke.java`

### 147. [TipTop] Q00-20230627002 調整TIPTOP的附件選擇txt時，上傳文件且未填說明欄位，轉檔異常問題。
- **Commit ID**: `70284b28ae495eb228734f2f6683c402f9486448`
- **作者**: raven.917
- **日期**: 2023-06-27 14:41:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java`

### 148. [T100]Q00-20230627001 修正關卡設置"所有附件皆需開啟過"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送
- **Commit ID**: `66d963f1cf56945d586c84cc56cd7851d36d243c`
- **作者**: 林致帆
- **日期**: 2023-06-27 13:39:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 149. [Tiptop] Q00-20230626002 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。
- **Commit ID**: `dc65303df2e29fd80e723ebd68da1b825cb3d0c8`
- **作者**: raven.917
- **日期**: 2023-06-26 16:28:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 150. [Web] Q00-20230621003 修正Rwd-Grid 設置必填時，Alert訊息異常問題
- **Commit ID**: `a98fbcd500a2f55192f5e8516bcb87d48cdd9ede`
- **作者**: raven.917
- **日期**: 2023-06-21 11:44:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 151. [Web] Q00-20230620001 調整絕對定位表單追蹤流程下列印表單畫面。
- **Commit ID**: `f4e8cc1b1e5872edd97568885677d5f1389baa0f`
- **作者**: raven.917
- **日期**: 2023-06-20 14:56:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 152. [Web]Q00-20230619001 修正Grid元件在多欄位時欄位寬度異常顯示問題
- **Commit ID**: `70df8e2b201aebca721e28c12cb9807900007148`
- **作者**: cherryliao
- **日期**: 2023-06-19 10:12:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`

### 153. [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)
- **Commit ID**: `18e7381a2579f976079d29a9e616128cf3869280`
- **作者**: raven.917
- **日期**: 2023-06-16 16:45:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 154. [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題[補]
- **Commit ID**: `abeabeed156b7026be0dcfbc69fb6f59cc9bae48`
- **作者**: cherryliao
- **日期**: 2023-06-16 13:56:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`

### 155. [Web]Q00-20230615002 修正離職維護作業無法開啟
- **Commit ID**: `b81ce47c61e8107c4ed254b8219768b4f077d4c1`
- **作者**: 林致帆
- **日期**: 2023-06-15 17:18:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java`

### 156. [Web] Q00-20230615001 修正客制開窗order by轉小寫導致模糊查詢異常問題。
- **Commit ID**: `2bbe00d25531c762f9618270e49a9ad1e63d67fc`
- **作者**: raven.917
- **日期**: 2023-06-15 11:52:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 157. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補)
- **Commit ID**: `c068bc54562590d6548eaa7e358fd9753bb0e5c0`
- **作者**: raven.917
- **日期**: 2023-06-05 11:41:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 158. [Web] Q00-20230613003 調整參與者型式的關卡頁面的「檢視轉派歷程」按鈕圖示。
- **Commit ID**: `6afb05dadf8a24d7c3a20b56d27ff507dececbd8`
- **作者**: raven.917
- **日期**: 2023-06-13 14:18:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 159. [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)
- **Commit ID**: `02c0450d001b5267e58f30b51eca64ae478a4711`
- **作者**: raven.917
- **日期**: 2023-06-13 11:36:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 160. [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。
- **Commit ID**: `bd71e74c850bdab523818fed0bd80ecbf8d8702e`
- **作者**: raven.917
- **日期**: 2023-06-12 14:57:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 161. [Web]Q00-20230612003 修正Script撰寫Grid的setColumnWith語法會跳出alert
- **Commit ID**: `fe114df67129a5ea377789fdf8453eaeb686eb62`
- **作者**: 林致帆
- **日期**: 2023-06-12 12:02:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 162. [在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕
- **Commit ID**: `a8b4e25bc25a0d29efc9bf12c6d11dd0cee18818`
- **作者**: 林致帆
- **日期**: 2023-06-12 11:27:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 163. [Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間[補修正]
- **Commit ID**: `cd837fc6847cbc1581f23a344abdac9a2540f0f4`
- **作者**: 林致帆
- **日期**: 2023-05-26 10:45:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 164. [Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間
- **Commit ID**: `ee073a7c97ffa26a05ca184b5f52f2d34395482c`
- **作者**: 林致帆
- **日期**: 2023-05-25 15:33:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 165. [Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息(補修正)
- **Commit ID**: `bc98a848c088df53b51b0910d727ccc537891167`
- **作者**: raven.917
- **日期**: 2023-06-12 10:45:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 166. [Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息
- **Commit ID**: `152815aa6f63074961368e6c2682abce9b796aff`
- **作者**: raven.917
- **日期**: 2023-06-09 17:16:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 167. [Web] Q00-20230609004 修正匯入Excel資料內有,會被替換成空白問題
- **Commit ID**: `87231d6677524bca1103b0d5ea5d82c77947d78f`
- **作者**: raven.917
- **日期**: 2023-06-09 15:13:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 168. [Web]A00-20230605001 修正在待辦情況下將HandWriting元件透過Script設置disable時沒有作用問題
- **Commit ID**: `54ade605839d6befe3b6720e28974a214591de53`
- **作者**: cherryliao
- **日期**: 2023-06-06 13:57:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`

### 169. [Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系
- **Commit ID**: `8c565b7e43091d7c353185f535ac2d264dcb5760`
- **作者**: 林致帆
- **日期**: 2023-06-07 15:58:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java`

### 170. [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題
- **Commit ID**: `8812df4c09c48db0f2d506641f0d4239796cdba6`
- **作者**: cherryliao
- **日期**: 2023-06-07 14:51:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`

### 171. [ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持"通知"選項
- **Commit ID**: `05a01ebc45a6add25c0f63a8346dd8b052a31834`
- **作者**: 林致帆
- **日期**: 2023-06-06 14:44:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 172. [流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求
- **Commit ID**: `ecc88a61dbc4ea0d5ae16956dfac7e9c842d04e7`
- **作者**: raven.917
- **日期**: 2023-06-05 17:14:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/WebApplicationAgent.java`

### 173. [Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示
- **Commit ID**: `49a235dc520fd84652e53353c19fa8885e68c652`
- **作者**: develop_20274
- **日期**: 2023-06-05 10:37:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`

### 174. [Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題
- **Commit ID**: `8088ac8fef882d3642e763766e45acdc61474dd9`
- **作者**: raven.917
- **日期**: 2023-06-02 10:54:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 175. [WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料
- **Commit ID**: `f48fac77aa1f751759f325043641f43783dbc0e1`
- **作者**: 林致帆
- **日期**: 2023-06-02 11:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 176. [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]
- **Commit ID**: `acdfd7b7d457a9d312a383bdfdcf3d542c86ff76`
- **作者**: 林致帆
- **日期**: 2023-06-02 10:23:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 177. [BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題
- **Commit ID**: `01f34b18f3cb2674099a28bbc63dd503bc57ceea`
- **作者**: yamiyeh10
- **日期**: 2023-06-01 15:10:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 178. [其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容
- **Commit ID**: `8186af7b5bb50188f75c03ec114653c012364ad8`
- **作者**: waynechang
- **日期**: 2023-06-01 11:24:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`

### 179. [Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常
- **Commit ID**: `dee1ff9a9f49a64f2036216c91549582832b47c5`
- **作者**: 林致帆
- **日期**: 2023-06-01 10:56:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`

### 180. [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息
- **Commit ID**: `bbba77b6293efa1c468c03bbea5681d634c91537`
- **作者**: 林致帆
- **日期**: 2023-06-01 12:04:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 181. [WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤
- **Commit ID**: `7465c9e3fae294f55777e734f7262a6de032f0fd`
- **作者**: 林致帆
- **日期**: 2023-05-31 17:47:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 182. [Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態
- **Commit ID**: `f1654eb781f177acdabd651a99072bebbfb3464f`
- **作者**: develop_20274
- **日期**: 2023-05-30 10:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 183. [Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)
- **Commit ID**: `256583ba35be24e43684df0f48927bbe9ab01313`
- **作者**: develop_20274
- **日期**: 2023-05-29 17:06:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 184. [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正
- **Commit ID**: `f879995c811268ee03a7fa0f5bb44fb8b838acc1`
- **作者**: develop_20274
- **日期**: 2023-05-29 16:54:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 185. [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
- **Commit ID**: `eb4fcda9bed15bec18820491e2c67e769a24a91d`
- **作者**: 林致帆
- **日期**: 2023-05-26 16:38:31
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 186. [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題
- **Commit ID**: `d72accd32ca2ac64d731380966e8b8f53529744d`
- **作者**: develop_20274
- **日期**: 2023-05-26 15:02:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 187. [Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題
- **Commit ID**: `4ae609cd95bff0a2b8728345da221270ec06eafb`
- **作者**: raven.917
- **日期**: 2023-05-26 14:59:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 188. [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]
- **Commit ID**: `0eabc66d8706bfec8d0f937d7f5ad593bfbc736b`
- **作者**: yamiyeh10
- **日期**: 2023-05-26 10:10:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 189. [Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題
- **Commit ID**: `64fa32e7bc15301766f00da2e16ea1dbe87fdc20`
- **作者**: raven.917
- **日期**: 2023-05-25 10:16:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 190. [流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常
- **Commit ID**: `ab7a426ee61294822f61d9d19fb01ace9ac934c4`
- **作者**: 林致帆
- **日期**: 2023-05-24 17:36:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java`

### 191. [Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加
- **Commit ID**: `cb8973a1085888406960b5ae2c4373b226bd9b14`
- **作者**: 林致帆
- **日期**: 2023-05-24 17:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 192. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `ed1a7b3d587b5d6d7a041912c9f502e537d2eff5`
- **作者**: 林致帆
- **日期**: 2023-05-26 10:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 193. [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
- **Commit ID**: `798a76611aaaefd9e7ca40e01e571b75e5b99e7a`
- **作者**: raven.917
- **日期**: 2023-05-25 19:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

