# Release Notes - BPM-ISO

## 版本資訊
- **新版本**: release_5.8.10.3
- **舊版本**: release_5.8.10.2
- **生成時間**: 2025-07-18 11:45:24
- **新增 Commit 數量**: 20

## 變更摘要

### lorenchang (3 commits)

- **2024-09-02 14:52:35**: [ISO]C01-20240829007 修正文件類別管理的回到查詢清單提示文字顯示為亂碼的異常
  - 變更檔案: 1 個
- **2024-07-30 15:36:03**: [ISO]C01-20240730001 修正建立新ISO文件時，若指定的浮水印不存在將導致服務任務出現NullPointerException，增加明確的Log
  - 變更檔案: 1 個
- **2024-07-09 15:30:58**: [內部]更新 README.md
  - 變更檔案: 1 個

### 張詠威 (1 commits)

- **2024-08-20 14:14:53**: C01-20240815005 修正Oracle資料庫在ISO文管首頁模糊查詢會查詢很久的議題
  - 變更檔案: 1 個

### 周权 (8 commits)

- **2024-08-20 09:51:43**: [Word套表] PDF浮水印属性管理，新增字段(type)
  - 變更檔案: 6 個
- **2024-08-13 09:04:58**: [Word套表] 调整<#SystemDate>只显示为日期
  - 變更檔案: 2 個
- **2024-08-08 15:06:05**: [Word套表] 调整从设计工具进入"PDF浮水印管理"作业的[监视浮水印]逻辑
  - 變更檔案: 1 個
- **2024-08-07 14:48:45**: [Word套表] PDF浮水印管理作业隐藏不需要的栏位
  - 變更檔案: 2 個
- **2024-08-06 16:47:45**: [Word套表] 调整json传已经插入文字的图片
  - 變更檔案: 4 個
- **2024-07-31 16:51:08**: [Word套表] 调整获取原有发行图章json
  - 變更檔案: 2 個
- **2024-07-29 15:06:46**: [Word套表] 设计工具增加“PDF浮水印圖片管理”、“PDF浮水印屬性管理”两个作业
  - 變更檔案: 1 個
- **2024-07-19 10:46:23**: [Word套表] 新增通过OID取得圖片浮水印的逻辑
  - 變更檔案: 2 個

### liuyun (1 commits)

- **2024-08-13 16:48:29**: [Word套表] PDF浮水印管理作业隐藏不需要的栏位[补]
  - 變更檔案: 1 個

### davidhr (1 commits)

- **2024-07-22 11:33:45**: [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
  - 變更檔案: 1 個

### 邱郁晏 (6 commits)

- **2024-07-10 15:48:21**: [ISO] C01-20240612001 調整由評審單發起的變更單流程，若中止或者撤銷，要將評審單狀態改為Close
  - 變更檔案: 1 個
- **2024-07-04 13:36:33**: Revert "[ISO] Q00-20240703001 修正ISO階層ID相同時，開窗頁面被濾掉問題"
  - 變更檔案: 1 個
- **2024-07-03 14:13:33**: [ISO] Q00-20240703001 修正ISO階層ID相同時，開窗頁面被濾掉問題
  - 變更檔案: 1 個
- **2024-06-26 16:31:30**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
  - 變更檔案: 2 個
- **2024-07-01 16:56:55**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
  - 變更檔案: 4 個
- **2024-06-26 16:31:30**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能
  - 變更檔案: 8 個

## 詳細變更記錄

### 1. [ISO]C01-20240829007 修正文件類別管理的回到查詢清單提示文字顯示為亂碼的異常
- **Commit ID**: `f400f656c666dde5b1705c453bfb9d74ba6b0a5d`
- **作者**: lorenchang
- **日期**: 2024-09-02 14:52:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocCategory.jsp`

### 2. C01-20240815005 修正Oracle資料庫在ISO文管首頁模糊查詢會查詢很久的議題
- **Commit ID**: `2c7da3640de4ce3d9317dbf05b296e72292d7d0b`
- **作者**: 張詠威
- **日期**: 2024-08-20 14:14:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`

### 3. [Word套表] PDF浮水印属性管理，新增字段(type)
- **Commit ID**: `08025f7027fe58115e01fe46161415519576e0c6`
- **作者**: 周权
- **日期**: 2024-08-20 09:51:43
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.hbm.xml`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/util/ConditionUtil.java`

### 4. [Word套表] PDF浮水印管理作业隐藏不需要的栏位[补]
- **Commit ID**: `3713e687e25b07c1b98cd3eed0fdf2c48e5c590e`
- **作者**: liuyun
- **日期**: 2024-08-13 16:48:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`

### 5. [Word套表] 调整<#SystemDate>只显示为日期
- **Commit ID**: `a9d58afacbd15b44ffb730106f3a06c89f2e9155`
- **作者**: 周权
- **日期**: 2024-08-13 09:04:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`

### 6. [Word套表] 调整从设计工具进入"PDF浮水印管理"作业的[监视浮水印]逻辑
- **Commit ID**: `26a5bde6c699ee44a9895bf64e697dc081824332`
- **作者**: 周权
- **日期**: 2024-08-08 15:06:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`

### 7. [Word套表] PDF浮水印管理作业隐藏不需要的栏位
- **Commit ID**: `9dc4afc6781c213eb325b9ce6eeae3db26329d19`
- **作者**: 周权
- **日期**: 2024-08-07 14:48:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`

### 8. [Word套表] 调整json传已经插入文字的图片
- **Commit ID**: `a2a448afdc813367c123069cd324590cd18cfc74`
- **作者**: 周权
- **日期**: 2024-08-06 16:47:45
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`

### 9. [Word套表] 调整获取原有发行图章json
- **Commit ID**: `3d3654d31a22d384e5cc2cfb1dd3ee5af61c51fe`
- **作者**: 周权
- **日期**: 2024-07-31 16:51:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java`

### 10. [ISO]C01-20240730001 修正建立新ISO文件時，若指定的浮水印不存在將導致服務任務出現NullPointerException，增加明確的Log
- **Commit ID**: `734473c9e77e2ec16bdd9998b0367c204a040f1d`
- **作者**: lorenchang
- **日期**: 2024-07-30 15:36:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`

### 11. [Word套表] 设计工具增加“PDF浮水印圖片管理”、“PDF浮水印屬性管理”两个作业
- **Commit ID**: `5e213382a01144366e5f116a9488badf332ead8d`
- **作者**: 周权
- **日期**: 2024-07-29 15:06:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/util/filter/FilterUtil.java`

### 12. [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
- **Commit ID**: `dea5e1e9b9dd3d1489ab148223488ad56066e0de`
- **作者**: davidhr
- **日期**: 2024-07-22 11:33:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocFileMain.jsp`

### 13. [Word套表] 新增通过OID取得圖片浮水印的逻辑
- **Commit ID**: `7082d889f293119e3b1fa1d5b0d5bd4c73e45237`
- **作者**: 周权
- **日期**: 2024-07-19 10:46:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java`

### 14. [ISO] C01-20240612001 調整由評審單發起的變更單流程，若中止或者撤銷，要將評審單狀態改為Close
- **Commit ID**: `461c411e52e761c546bf9880d2c4677feca72e2d`
- **作者**: 邱郁晏
- **日期**: 2024-07-10 15:48:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java`

### 15. [內部]更新 README.md
- **Commit ID**: `126b8deb256c27c84d5b80cf673acbecf4051393`
- **作者**: lorenchang
- **日期**: 2024-07-09 15:30:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `README.md`

### 16. Revert "[ISO] Q00-20240703001 修正ISO階層ID相同時，開窗頁面被濾掉問題"
- **Commit ID**: `0acccfe27682bb029942df13a022f3b111012a5f`
- **作者**: 邱郁晏
- **日期**: 2024-07-04 13:36:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/comparator/ISODocLevelComparator.java`

### 17. [ISO] Q00-20240703001 修正ISO階層ID相同時，開窗頁面被濾掉問題
- **Commit ID**: `2789aa861712f6ca1ff85421068b861003c4315f`
- **作者**: 邱郁晏
- **日期**: 2024-07-03 14:13:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/comparator/ISODocLevelComparator.java`

### 18. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
- **Commit ID**: `431c11f99be358f3d55e8be1aea15ab048aa65c7`
- **作者**: 邱郁晏
- **日期**: 2024-06-26 16:31:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`

### 19. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
- **Commit ID**: `b3bd70a78821ace68934fda1a71387a89db5223b`
- **作者**: 邱郁晏
- **日期**: 2024-07-01 16:56:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFullTextSearch.hbm.xml`
  - ➕ **新增**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFullTextSearch.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/util/hibernate/module.hibernate.cfg.xml`

### 20. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能
- **Commit ID**: `e38f7deb695a2e0b5b97078c29ae839cec2d086e`
- **作者**: 邱郁晏
- **日期**: 2024-06-26 16:31:30
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOSearchCondictionKey.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/SearchCondiction.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/DocListController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/IndexingHandler.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ManageDocumentMgr.java`

