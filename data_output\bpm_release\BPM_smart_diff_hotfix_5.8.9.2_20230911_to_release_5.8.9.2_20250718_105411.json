{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.2_20230911", "date": "2023-09-11 16:08:36", "message": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】", "author": "liu<PERSON>"}, "舊分支": {"branch_name": "release_5.8.9.2", "date": "2023-05-26 10:36:32", "message": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 10:54:11", "新增commit數量": 138, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "ab5f36018b2933c11b898f90acd61e65b5185450", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】", "提交日期": "2023-09-11 16:08:36", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bde1d52bac144a6b7b6517284d5906c864bb1085", "commit_訊息": "[Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。", "提交日期": "2023-09-11 15:39:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6a9abd60d6cb81d3d56549204f6c2b9ca80eb652", "commit_訊息": "[表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題", "提交日期": "2023-09-08 16:39:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "631af3cc5c1c7165bade8f7b02a23f9b6cffc383", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】", "提交日期": "2023-09-07 17:54:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0ae1c43eb6fc5fe0b1c386b41a5c86c332749711", "commit_訊息": "[Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]", "提交日期": "2023-09-08 10:14:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4f6c70f36160445a81b66df378df379905aca331", "commit_訊息": "[SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗", "提交日期": "2023-09-08 09:24:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1cb9eb243f30171baed17b34158d6f655b7dd813", "commit_訊息": "[流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常", "提交日期": "2023-09-07 10:31:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4bb48129c9e0fc35fa4e3c354407751fe6123339", "commit_訊息": "[Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。", "提交日期": "2023-09-05 18:01:25", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "98f5c6f4efe51046ba0f5d2d2d9a90bbbf989eb4", "commit_訊息": "[Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。", "提交日期": "2023-09-06 12:03:56", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b90447ed38c62e399327fa2eb1aad8007ac6bd30", "commit_訊息": "[Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。", "提交日期": "2023-09-06 13:19:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d15587383a61e354a2cbff76a5db7ee18960b18f", "commit_訊息": "[Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白", "提交日期": "2023-09-06 11:11:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f16c21c7841cbe6f22e79b123bf6b474ad217cde", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)", "提交日期": "2023-09-04 10:53:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34cdf2a6c24de677a20cabcc0254708c0d8edba3", "commit_訊息": "[WEB]Q00-20230602004 修正流程重要性在下列情境時，無法正確顯示設定的重要性 1.關卡設定列印模式，並點擊列印表單後 2.多人關卡只需一人處理，並點擊「由我處理」後 3.關卡派送失敗，畫面提示派送失敗後", "提交日期": "2023-06-02 15:48:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "bfaf8da412e6b5b2877f49dac64c930cf6bab538", "commit_訊息": "[WorkFlow]Q00-20230901003 修正附件URL帶有#字號會無法下載附件", "提交日期": "2023-09-01 14:46:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/TiptopUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3807b7d9d9ee23759c6db9dc1ad678192f635b33", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題", "提交日期": "2023-08-31 15:41:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "20022aba0e117abcbb0f44dd6835a5fbb67aaadd", "commit_訊息": "[Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題", "提交日期": "2023-08-30 11:47:29", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "05f62ac46944168d4041cd9620d85613ff2d0fdb", "commit_訊息": "Revert \"[Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題\"", "提交日期": "2023-08-31 11:31:16", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7f3bea1f90b1fcacaf3daacedb3cf4b108f15177", "commit_訊息": "[Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題", "提交日期": "2023-08-30 11:47:29", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "70d5e25d249d195759511eb3dd8a789b4cb66051", "commit_訊息": "[TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷", "提交日期": "2023-08-30 10:43:55", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71677e6500f2bd78c10d17c0acfd4f9935f28991", "commit_訊息": "[ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗", "提交日期": "2023-08-29 15:50:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a9148619857a85c5d573fad98967cab455441017", "commit_訊息": "[流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常", "提交日期": "2023-08-29 16:50:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bee1ee547921492000df78867685f9c3088e3db6", "commit_訊息": "[web]Q00-20230829003 列印時附件資訊會超出邊界问题修复", "提交日期": "2023-08-29 14:02:09", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc13766064ca23e58da713dabb0a0fa0236041c2", "commit_訊息": "[SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤", "提交日期": "2023-08-28 16:57:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "619539a413804b28a72f6976a3bcac02968937c2", "commit_訊息": "[流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據", "提交日期": "2023-08-29 10:32:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "31cda5b613ef208aaa8c141bffdaa87f2569d058", "commit_訊息": "[DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題", "提交日期": "2023-08-28 11:15:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8bd98800a3a0c1b1fd439368862729a2c8bd9fcc", "commit_訊息": "[web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复", "提交日期": "2023-08-25 17:38:15", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6667ae6e592b047491a788b1c0be5f06208b5c1f", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面", "提交日期": "2023-08-25 15:40:09", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e89006d5b4236b075f8b17a3af82b43a9a7043c1", "commit_訊息": "[內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息", "提交日期": "2023-06-20 15:54:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "119d393f42a4cf822ff8a12438297c35bce24997", "commit_訊息": "[Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。", "提交日期": "2023-08-24 13:43:54", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4be402dc2ec8727c12f2b1185b0b193ba6267036", "commit_訊息": "退件表單資訊檢視表單內容報錯，會報錯「Error occurred while get TraceProcess url ! 」", "提交日期": "2023-08-24 11:07:33", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkStep.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ef00c96c8c10e47f5a8fe382360cde638829ac85", "commit_訊息": "Revert \"[Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常\"", "提交日期": "2023-08-23 16:34:08", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "69751bf1a4223d95f119f00ed7c344e990b653fa", "commit_訊息": "[Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常", "提交日期": "2023-08-23 15:27:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "05d72e6d520fada627b175c8151fa780b7268171", "commit_訊息": "[資安] S00-20211220001 S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信(補)", "提交日期": "2023-08-02 11:31:06", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "4ea7c2636820889857a89eb1b190c576cf5aadb8", "commit_訊息": "[資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信", "提交日期": "2023-08-01 16:27:12", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.3_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "6298acb838c74bb082f68d4e3a8c3079018e247c", "commit_訊息": "[資安] Q00-20230718001 修正發送通知者寄送Mail，不應副本給通知者的問題。", "提交日期": "2023-07-20 11:50:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "62e51be08467200ab0fa21aa45f18dface5c56ae", "commit_訊息": "[Web] Q00-20230817004 修改手机版追踪流程，取回重办、撤销流程显示两个时间", "提交日期": "2023-08-18 10:41:47", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "bed691d4bf30566611d01ff417578e18f51205be", "commit_訊息": "[Web]Q00-20230817001 修正有關卡設定為列印模式，從待辦事項點選列印時，格式會跑掉", "提交日期": "2023-08-17 11:05:11", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "55b7791cd3c98498cc968b169e24178b7a81b0ef", "commit_訊息": "[Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常 [补修正]", "提交日期": "2023-08-16 12:08:59", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32677eef161658c2cab1dbcd3aa3647f24e66462", "commit_訊息": "[Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常", "提交日期": "2023-08-15 13:57:44", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76f3b43150049bd8afe4dbbe7135103cc8e02a4c", "commit_訊息": "[Web] Q00-20230612004 修正絕對定位表單，上傳圖片異常問題。", "提交日期": "2023-06-12 13:57:42", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c1fb067e6f7467ed3108f7dfd916a900917c35cb", "commit_訊息": "[Web] V00-20230815001 修正無上傳簽名圖檔時，後端拋錯異常，新增防呆。", "提交日期": "2023-08-15 15:10:34", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6e3b5f42bf955a2029a00794d37acceb997f29ca", "commit_訊息": "[WorkFlow]Q00-20230815004 新增WorkFlow回寫增加時間訊息", "提交日期": "2023-08-15 17:13:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c11a0fbaa41a99a89b255f760611d3e459590213", "commit_訊息": "[Web]V00-20230810005 修正ORACLE使用者開窗在有離職人員的狀況下打開會異常", "提交日期": "2023-08-10 12:00:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "15a7b148bbb5dd47cf5b6230f8f72a73d82c00c1", "commit_訊息": "[Web]V00-20230814001 元件在表單存取控管設為Invisible 绝对位置表单列印空白", "提交日期": "2023-08-14 16:08:20", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "136c32b278339d60263a625a444bb6643ecefeab", "commit_訊息": "[Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白[补修正]", "提交日期": "2023-08-10 13:22:36", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a192059f13d5ca6f6d51a507434a7163f0fcb5aa", "commit_訊息": "[在線閱覽]Q00-20230814002 調整在線閱讀浮水印機制，當浮水印內容有特殊字導致無法添加浮水印時，改使用預設內容「userId+閱讀時間」作為浮水印內容，避免使用者無法順利閱讀檔案", "提交日期": "2023-08-14 15:59:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f34347c441444e1744b0cea51be4d68e30236b57", "commit_訊息": "[Web] Q00-20230811001 修正BPM首页手机端流程有两个时间显示", "提交日期": "2023-08-11 15:36:12", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0d07714fdcece81f2ea82497346ce29308e48e29", "commit_訊息": "[流程引擎]Q00-20230802004 修正流程未設定\"被處理者終止時逐級通知\"應該只要發起人收到信件通知", "提交日期": "2023-08-02 18:54:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c4cfed03c122d56573a1c0b4790825fe30b511d3", "commit_訊息": "[表單設計師]Q00-20230731001 調整表單設計師縮小或切換頁簽後切回來操作沒立即更新使用者資訊的問題", "提交日期": "2023-07-31 15:59:38", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0e379c71b448b2e2104b6f3ed5bbb0e890cccc71", "commit_訊息": "[流程引擎]V00-20230802001 修正流程引擎在核決關卡展開第一關時向前加簽或核決關卡展開的最後一關向後加簽時，若PerformWorkItemHandlerBean的log層級設定info以上時，系統會出現錯誤訊息，實際上加簽關卡已成功的異常", "提交日期": "2023-08-02 16:07:19", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65f50bed63c4be1493df5b803a20fbd251fdc330", "commit_訊息": "[在線閱覽]Q00-20230802001 修正在線閱覽管理-轉檔異常處理作業，當關閉「上傳PDF」的開窗畫面後，系統會彈跳服務錯誤的訊息", "提交日期": "2023-08-02 13:48:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cd12b56b261c5b37331e93b0df691b56915bb528", "commit_訊息": "[BPM APP]Q00-20230726002 修正移動端絕對位置表單中Grid元件單身數據有空值時組成格式不正確問題", "提交日期": "2023-07-26 16:31:24", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "653a28397caf5df5545fc44e54f844efff5f0b25", "commit_訊息": "[WEB]Q00-20230725004 修正主旨樣板選擇\"允許修改主旨\"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨[補修正]", "提交日期": "2023-07-26 10:15:28", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "264e17cfac1401d2de40019376306f3a64617bc6", "commit_訊息": "[WEB]Q00-20230725004 修正主旨樣板選擇\"允許修改主旨\"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨", "提交日期": "2023-07-25 18:16:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b6d7a2e0fb05489f89f35540a545a63918d942f8", "commit_訊息": "[Web]Q00-20230725002 修正發起流程切換預解析流程時，當系統設定有設定該流程只以主部門發起(invoke.process.by.main.unit.process.package.ids)時，若使用者有多個兼職部門時，切換流程圖不應讓使用者選擇參考部門", "提交日期": "2023-07-25 15:27:07", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d9977a78ea7353126c8d315d324efca8985281b4", "commit_訊息": "[表單設計師]Q00-20230725001 調整選項元件進階設定選項開窗顯示值多語系有含單引號時確定按鈕異常的問題", "提交日期": "2023-07-25 11:17:31", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8cce2261fc5f227f5106e398481c42cca377cea4", "commit_訊息": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]", "提交日期": "2023-07-17 09:25:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ac8ef6489b530b4906d10748851897418222fd1b", "commit_訊息": "[流程引擎]Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序[补修正]", "提交日期": "2023-07-18 12:40:40", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "386562baff8e1cfd0c6e853df74435fe0cd596d9", "commit_訊息": "[Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。", "提交日期": "2023-07-14 17:36:50", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ec2aaa8a764e59ab64ccd11bf93b78bfb91fb6e6", "commit_訊息": "[流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序(補修正)", "提交日期": "2023-05-30 16:03:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActivityInstanceComparator.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "13cc2014085068c6433b880199df9daf4519b499", "commit_訊息": "[流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序", "提交日期": "2023-05-25 14:03:05", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79c9a925292248de488b5944292104a6ade61401", "commit_訊息": "[ESS]Q00-20230710001 調整log錯誤訊息的顯示", "提交日期": "2023-07-10 10:49:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c6a995677ed1a4d523e715c2685b88a7f62d59b", "commit_訊息": "[Web] Q00-20230713005 新增登入登出LOG印出SessionId，修正模擬為同一IP時取瀏覽器資訊異常問題", "提交日期": "2023-07-20 13:44:37", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e0e066dfdb1fa81d7dde0a12847582c19a056424", "commit_訊息": "[在地化] Q00-20230620003 增加驗證SSOkey時，時間間隔超過5分鐘，印出LOG訊息", "提交日期": "2023-06-20 17:03:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c2b454bdefe63b2353f5ef81b66dec59cea18c3", "commit_訊息": "[DT]C01-20230719005 修正設計師使用權限管理中的組織設計師清單其id與名稱顯示異常問題", "提交日期": "2023-07-20 12:13:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/ParticipantInfoAcquirer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/AccessCrtlMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "aafe83f9744a0522fa4b16467f8fc1f6b559336f", "commit_訊息": "[Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員 [補修正]", "提交日期": "2023-07-20 11:16:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "edabb9e0cb8722c57fd8ddd88d5d79c8bec2ed7a", "commit_訊息": "[流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException[补修正]", "提交日期": "2023-07-20 10:33:53", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc582f6da64668f93d0c6ffaa029deed2235e987", "commit_訊息": "[流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException", "提交日期": "2023-07-18 16:45:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f788d638ad67ac07d9b8d81d2a0e3d0f024c7a0", "commit_訊息": "[Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員", "提交日期": "2023-07-19 14:28:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c00c08cb53e93fa28864627f6e82c57ee87f3bb8", "commit_訊息": "[流程引擎] Q00-20230717003 修正終止流程時，偶發ProcessInstance與BamProInstData狀態不一致問題", "提交日期": "2023-07-17 17:55:45", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "2f528ac2e10384e8bc2909b3d893c6614bf50cdb", "commit_訊息": "[web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复", "提交日期": "2023-07-17 14:37:58", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8836d2fef061b8277612950109ca6c9d06d1915c", "commit_訊息": "[流程引擎] Q00-20230714001 修正服務任務關卡執行後，不會重新組成全文檢索欄位問題", "提交日期": "2023-07-14 14:48:48", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "62d8599eafb1afd676fc0759806242adb502d48a", "commit_訊息": "[Web] Q00-20230713003 優化使用者登入時異常時的LOG", "提交日期": "2023-07-13 11:44:10", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/PersistentObjectHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c3136f94185f1a6a7ae4cd4caed76991059cb0a", "commit_訊息": "[Web]Q00-20230713002 修正使用者為部門主管從Portal進入BPM點選首頁顯示內容不為主管首頁", "提交日期": "2023-07-13 10:23:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8460f5b46051afe0a1bf0d9ea9d70e36b25a4b41", "commit_訊息": "[web]Q00-20230712003 修正在转存表单时栏位原數值為小數點後兩位，轉存表單後僅剩小數點後一位", "提交日期": "2023-07-12 15:59:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1340937f84f9f2c661d156f10b560fdf7070e44d", "commit_訊息": "[Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白。", "提交日期": "2023-07-11 14:12:59", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4148e0147df5360584669f5a36c4c6d37b30af64", "commit_訊息": "[流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題(補)", "提交日期": "2023-07-11 10:24:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c2a731f0ba096ff654dd7ff1958831b92a28550b", "commit_訊息": "[流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題。", "提交日期": "2023-07-07 14:39:22", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5ae761b8897e3299c43924e6b126a748d53654ae", "commit_訊息": "[web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题", "提交日期": "2023-07-10 16:56:51", "作者": "liuxua", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7c967dc6e64de3e36dcfc2a6743fe64736beed0c", "commit_訊息": "[web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题", "提交日期": "2023-07-10 16:51:34", "作者": "liuxua", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c4a1138898a33d8aaf6fac7f8040c41c183d3d6", "commit_訊息": "[其他]Q00-20230704001 調整BCL8單個檔案轉檔逾時時間由預設2分鐘改為預設10分鐘", "提交日期": "2023-07-10 11:38:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3145c639005ba13adaeb4e0ee35d7716266bada", "commit_訊息": "[組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。", "提交日期": "2023-07-06 15:22:50", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9286e2062de27ff6f0ec71845f20857cb357f8d", "commit_訊息": "[Web]Q00-20230705002 修正表單在预览时，更換image后，显示图片不正确的问题", "提交日期": "2023-07-05 14:23:40", "作者": "<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ImageElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b8bf4fe66412e250d96f526e456fdf27cb66c531", "commit_訊息": "[Web]Q00-20230704003 調整formScript撰寫ajax加簽關卡後，需要更新session裡面的ProcessInst的相關屬性(Processpackage,ProcessDef等屬性)，避免預覽流程仍以加簽前的定義做解析", "提交日期": "2023-07-04 17:40:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e61a884d84b124afd3f9fe7ce225455f5494d141", "commit_訊息": "[Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級(補)", "提交日期": "2023-07-04 17:34:15", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f41906d8ea89b45960d13034fecd607eece5268", "commit_訊息": "[Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級", "提交日期": "2023-07-04 16:43:46", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b83a5ca0fc7355b63c5a0b7c0ae1b1ae2903d4c4", "commit_訊息": "[Web]Q00-20230704001 調整CommonAccessor.updateConnectedUserInfo()更新使用者時間的方法，當更新異常時，由前端畫面提示「更新線上時間失敗」改為後端serverlog記錄就好", "提交日期": "2023-07-04 17:02:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "80c003dc533af2a497d5589f2639c1220d34f0ea", "commit_訊息": "[Web] Q00-20230703004 修正表單列印畫面元件跑版問題，邊界調整為0", "提交日期": "2023-07-03 16:31:00", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "725e5ca7380785ed5c5b1c2eadfcf137301f99d1", "commit_訊息": "[Web]Q00-20230609001 調整待辦流程開啟列印表單時Grid數據沒有加載的問題", "提交日期": "2023-06-09 12:02:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "496c3900bcfcfa0ee21a76f9f83e3c67d2d93990", "commit_訊息": "[SAP]Q00-20230628003 修正SAP整合回寫呼叫SAP的invoke服務，當SAP回傳的訊息需存放在Grid時，若Grid內容為空時，可能會導致formInstance.fieldValues產生多組相同Grid代號的內容", "提交日期": "2023-06-28 18:02:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d4c925a54cd3b1935c5cef028033a6f1b9458e2", "commit_訊息": "[SAP]Q00-20230627003 SAP整合的invoke服務任務增加表單相關資訊log", "提交日期": "2023-06-27 15:09:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70284b28ae495eb228734f2f6683c402f9486448", "commit_訊息": "[TipTop] Q00-20230627002 調整TIPTOP的附件選擇txt時，上傳文件且未填說明欄位，轉檔異常問題。", "提交日期": "2023-06-27 14:41:06", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "66d963f1cf56945d586c84cc56cd7851d36d243c", "commit_訊息": "[T100]Q00-20230627001 修正關卡設置\"所有附件皆需開啟過\"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送", "提交日期": "2023-06-27 13:39:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc65303df2e29fd80e723ebd68da1b825cb3d0c8", "commit_訊息": "[Tiptop] Q00-20230626002 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。", "提交日期": "2023-06-26 16:28:03", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a98fbcd500a2f55192f5e8516bcb87d48cdd9ede", "commit_訊息": "[Web] Q00-20230621003 修正Rwd-Grid 設置必填時，Alert訊息異常問題", "提交日期": "2023-06-21 11:44:24", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f4e8cc1b1e5872edd97568885677d5f1389baa0f", "commit_訊息": "[Web] Q00-20230620001 調整絕對定位表單追蹤流程下列印表單畫面。", "提交日期": "2023-06-20 14:56:48", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "70df8e2b201aebca721e28c12cb9807900007148", "commit_訊息": "[Web]Q00-20230619001 修正Grid元件在多欄位時欄位寬度異常顯示問題", "提交日期": "2023-06-19 10:12:14", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "18e7381a2579f976079d29a9e616128cf3869280", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)", "提交日期": "2023-06-16 16:45:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "abeabeed156b7026be0dcfbc69fb6f59cc9bae48", "commit_訊息": "[Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題[補]", "提交日期": "2023-06-16 13:56:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b81ce47c61e8107c4ed254b8219768b4f077d4c1", "commit_訊息": "[Web]Q00-20230615002 修正離職維護作業無法開啟", "提交日期": "2023-06-15 17:18:15", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2bbe00d25531c762f9618270e49a9ad1e63d67fc", "commit_訊息": "[Web] Q00-20230615001 修正客制開窗order by轉小寫導致模糊查詢異常問題。", "提交日期": "2023-06-15 11:52:52", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c068bc54562590d6548eaa7e358fd9753bb0e5c0", "commit_訊息": "[Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補)", "提交日期": "2023-06-05 11:41:57", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6afb05dadf8a24d7c3a20b56d27ff507dececbd8", "commit_訊息": "[Web] Q00-20230613003 調整參與者型式的關卡頁面的「檢視轉派歷程」按鈕圖示。", "提交日期": "2023-06-13 14:18:28", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "02c0450d001b5267e58f30b51eca64ae478a4711", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)", "提交日期": "2023-06-13 11:36:01", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd71e74c850bdab523818fed0bd80ecbf8d8702e", "commit_訊息": "[Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。", "提交日期": "2023-06-12 14:57:27", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe114df67129a5ea377789fdf8453eaeb686eb62", "commit_訊息": "[Web]Q00-20230612003 修正Script撰寫Grid的setColumnWith語法會跳出alert", "提交日期": "2023-06-12 12:02:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a8b4e25bc25a0d29efc9bf12c6d11dd0cee18818", "commit_訊息": "[在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕", "提交日期": "2023-06-12 11:27:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cd837fc6847cbc1581f23a344abdac9a2540f0f4", "commit_訊息": "[Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間[補修正]", "提交日期": "2023-05-26 10:45:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee073a7c97ffa26a05ca184b5f52f2d34395482c", "commit_訊息": "[Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間", "提交日期": "2023-05-25 15:33:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc98a848c088df53b51b0910d727ccc537891167", "commit_訊息": "[Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息(補修正)", "提交日期": "2023-06-12 10:45:38", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "152815aa6f63074961368e6c2682abce9b796aff", "commit_訊息": "[Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息", "提交日期": "2023-06-09 17:16:16", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "87231d6677524bca1103b0d5ea5d82c77947d78f", "commit_訊息": "[Web] Q00-20230609004 修正匯入Excel資料內有,會被替換成空白問題", "提交日期": "2023-06-09 15:13:05", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "54ade605839d6befe3b6720e28974a214591de53", "commit_訊息": "[Web]A00-20230605001 修正在待辦情況下將HandWriting元件透過Script設置disable時沒有作用問題", "提交日期": "2023-06-06 13:57:44", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c565b7e43091d7c353185f535ac2d264dcb5760", "commit_訊息": "[Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系", "提交日期": "2023-06-07 15:58:33", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8812df4c09c48db0f2d506641f0d4239796cdba6", "commit_訊息": "[Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題", "提交日期": "2023-06-07 14:51:50", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-handWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05a01ebc45a6add25c0f63a8346dd8b052a31834", "commit_訊息": "[ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持\"通知\"選項", "提交日期": "2023-06-06 14:44:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecc88a61dbc4ea0d5ae16956dfac7e9c842d04e7", "commit_訊息": "[流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求", "提交日期": "2023-06-05 17:14:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/WebApplicationAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "49a235dc520fd84652e53353c19fa8885e68c652", "commit_訊息": "[Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示", "提交日期": "2023-06-05 10:37:25", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8088ac8fef882d3642e763766e45acdc61474dd9", "commit_訊息": "[Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題", "提交日期": "2023-06-02 10:54:49", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f48fac77aa1f751759f325043641f43783dbc0e1", "commit_訊息": "[WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料", "提交日期": "2023-06-02 11:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "acdfd7b7d457a9d312a383bdfdcf3d542c86ff76", "commit_訊息": "[WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]", "提交日期": "2023-06-02 10:23:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "01f34b18f3cb2674099a28bbc63dd503bc57ceea", "commit_訊息": "[BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題", "提交日期": "2023-06-01 15:10:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8186af7b5bb50188f75c03ec114653c012364ad8", "commit_訊息": "[其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容", "提交日期": "2023-06-01 11:24:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dee1ff9a9f49a64f2036216c91549582832b47c5", "commit_訊息": "[Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常", "提交日期": "2023-06-01 10:56:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/lib/Dwr/dwr.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbba77b6293efa1c468c03bbea5681d634c91537", "commit_訊息": "[WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息", "提交日期": "2023-06-01 12:04:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7465c9e3fae294f55777e734f7262a6de032f0fd", "commit_訊息": "[WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤", "提交日期": "2023-05-31 17:47:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1654eb781f177acdabd651a99072bebbfb3464f", "commit_訊息": "[Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態", "提交日期": "2023-05-30 10:27:14", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "256583ba35be24e43684df0f48927bbe9ab01313", "commit_訊息": "[Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)", "提交日期": "2023-05-29 17:06:25", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f879995c811268ee03a7fa0f5bb44fb8b838acc1", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正", "提交日期": "2023-05-29 16:54:46", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "eb4fcda9bed15bec18820491e2c67e769a24a91d", "commit_訊息": "[WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起", "提交日期": "2023-05-26 16:38:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d72accd32ca2ac64d731380966e8b8f53529744d", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題", "提交日期": "2023-05-26 15:02:51", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ae609cd95bff0a2b8728345da221270ec06eafb", "commit_訊息": "[Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題", "提交日期": "2023-05-26 14:59:32", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0eabc66d8706bfec8d0f937d7f5ad593bfbc736b", "commit_訊息": "[WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]", "提交日期": "2023-05-26 10:10:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "64fa32e7bc15301766f00da2e16ea1dbe87fdc20", "commit_訊息": "[Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題", "提交日期": "2023-05-25 10:16:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ab7a426ee61294822f61d9d19fb01ace9ac934c4", "commit_訊息": "[流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常", "提交日期": "2023-05-24 17:36:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cb8973a1085888406960b5ae2c4373b226bd9b14", "commit_訊息": "[Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加", "提交日期": "2023-05-24 17:01:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed1a7b3d587b5d6d7a041912c9f502e537d2eff5", "commit_訊息": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "提交日期": "2023-05-26 10:36:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "798a76611aaaefd9e7ca40e01e571b75e5b99e7a", "commit_訊息": "[組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題", "提交日期": "2023-05-25 19:35:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}