{"company_id": "07125600", "company_name": "勤業", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\07125600_勤業\\01客戶基本資料", "files": [{"filename": "勤業.txt", "raw_content": "遠端方式:\tthinkpad筆電→點選下方工具列的「f5」VPN連線工具→連接後輸入下方密碼→再輸入小手機密碼\r\n全部登入帳密:\tpamproject4/Dscadmin202401!\r\n\r\n正式機(********):**************\r\nBPM網頁帳密:\tadmin1qaz@WSX\r\n\r\n正式機DB:**************\r\nhttps://TWAZURAPD001.atrapa.deloitte.com:20103\t\r\n\r\n測試機(********):**************\r\nBPM網頁帳密:1111\r\n\r\n測試機DB:**************\r\n名稱/帳號/密碼:\t\r\nhttps://TWAZURATS001.atrapa.deloitte.com:20103\t\r\n\r\n憑證密碼\tDeloitte@123\r\n60-45-BD-62-D0-60\r\n\t\r\n聯絡人:\r\n\"<PERSON> W. Lin 林文心\r\nM: +***********-614 | <EMAIL>\"\t\t\r\n\t\t\r\n\t\t\r\n\"<PERSON><PERSON> <PERSON><PERSON> Yu 游閔茹\r\n02- 2725-9988 #6038 \r\n0905-380-872\r\n<EMAIL> \"\t\t\r\n\t\t\r\n\t\t\r\n", "structured_data": {"遠端方式": "thinkpad筆電→點選下方工具列的「f5」VPN連線工具→連接後輸入下方密碼→再輸入小手機密碼", "全部登入帳密": "pamproject4/Dscadmin202401!", "正式機(********)": "**************", "bpm網頁帳密": "1111", "正式機db": "**************", "https": "//TWAZURATS001.atrapa.deloitte.com:20103", "測試機(********)": "**************", "測試機db": "**************", "m": "+***********-614 | <EMAIL>\"", "host": "********"}, "source_path": "C1.客戶維護相關\\07125600_勤業\\01客戶基本資料\\勤業.txt", "file_size": 739, "encoding_used": "UTF-8-SIG", "processed_at": "2025-08-26T10:46:28.108331"}], "total_files": 1, "processed_at": "2025-08-26T10:46:28.108338"}