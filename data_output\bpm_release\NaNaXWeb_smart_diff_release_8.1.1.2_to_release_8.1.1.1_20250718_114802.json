{"比較資訊": {"專案ID": "NaNaXWeb", "倉庫路徑": "D:\\IDEA_workspace\\NaNaXWeb", "新分支": {"branch_name": "release_8.1.1.2", "date": "2025-06-30 17:44:34", "message": "[智能表單設計助手] 調整表單設計助手j不可新增重複ID的表單", "author": "yamiyeh10"}, "舊分支": {"branch_name": "release_8.1.1.1", "date": "2025-04-01 11:47:50", "message": "Revert \"[文件總結助手]調整若問答出現“抱歉，您问的问题尚未出现在文档中”就不顯示參考文件\"", "author": "周权"}, "比較時間": "2025-07-18 11:48:02", "新增commit數量": 36, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "192475ee05a2369f04a391c81535bdd422e71106", "commit_訊息": "[智能表單設計助手] 調整表單設計助手j不可新增重複ID的表單", "提交日期": "2025-06-30 17:44:34", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/generate-form/generate-form.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea61a609241530db827e35429c29960a96c49277", "commit_訊息": "[智能表單設計助手]修正扣點失敗自動停用數字員工的邏輯", "提交日期": "2025-06-30 17:11:48", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/pipe/form-designassistant-status.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/FormDesignSupportServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0f7bf0acb2dc32796e81146a932d8313b37292d7", "commit_訊息": "[智能表單設計助手] 調整表單設計助手删除当前页全部数据画面还停留在当前页的问题", "提交日期": "2025-06-30 13:16:15", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant-list/form-design-assistant-list.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "393913f033f6141b136c2506a1946bf906925b9b", "commit_訊息": "Merge remote-tracking branch 'origin/master'", "提交日期": "2025-06-30 11:06:24", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a29bd4408f15f21ca3ccda59c39029b6ca8e84a8", "commit_訊息": "[智能表單設計助手] 調整表單設計助手畫面佈局", "提交日期": "2025-06-30 11:05:59", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant-list/form-design-assistant-list.component.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82e984d82df97772f51bfdcca77ca4dbd4733be7", "commit_訊息": "[智能表單設計助手]JSON生成表單定義-Grid再優化", "提交日期": "2025-06-30 11:05:40", "作者": "kmin", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/utils/GenerateRwdFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71b67009c4fb3dfe79a0428e44f3dcd470341cc0", "commit_訊息": "[智能表單設計助手]JSON生成表單定義-Grid再優化", "提交日期": "2025-06-30 09:17:08", "作者": "kmin", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/utils/GenerateRwdFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b8b0bda595856386cf7c1f8834d9f606ed7220a", "commit_訊息": "Merge remote-tracking branch 'origin/master'", "提交日期": "2025-06-27 09:37:33", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "bf07bb21e336461797a1869b94b2ee15f10c85aa", "commit_訊息": "[word套表]C01-20250624001 iReport报表列印调整可选择表单版本", "提交日期": "2025-06-27 09:35:41", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/CommonProgramModule/src/app/report/report-manage-tool/report-manage/report-manage.component.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8df0184f67a8fb52accc90f88e9893e6ba2ac387", "commit_訊息": "[PRODT]C01-20250626003 修正Web流程管理中設定關卡的表單存取控管設定無法儲存問題", "提交日期": "2025-06-26 17:44:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8e3df2d1d13e817309e0816fdc8a37feaa489641", "commit_訊息": "[智能表單設計助手]JSON生成表單定義-Grid、Date優化", "提交日期": "2025-06-26 17:11:47", "作者": "kmin", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/utils/GenerateRwdFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd10189759db93f0b2b47950581be7a50cb16935", "commit_訊息": "[智能表單設計助手]新增解析成功已扣點但生成失敗的補償機制", "提交日期": "2025-06-25 08:57:59", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/dto/FormDesignAssistantDTO.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/restful/FormDesignAssistantController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/FormDesignAssistantService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/FormDesignAssistantServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/FormDesignSupportServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "5725ada404e3d739f3557fb652df54b49fd07c94", "commit_訊息": "[智能表單設計助手]配合驗證更新、修正SonarQube掃出之加解密安全性議題(補)", "提交日期": "2025-06-20 10:15:48", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/restful/FormDesignAssistantController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/FormDesignAssistantService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/FormDesignAssistantServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "af0adf987e4b7607a5eff2ba2ca8353535b2a598", "commit_訊息": "[智能表單設計助手]數智員工參數設定畫面多語系調整", "提交日期": "2025-06-18 17:57:10", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/pasrameter-setting/pasrameter-setting.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/pasrameter-setting/pasrameter-setting.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0b8699be4486cbfda428d310d1461df4352f7670", "commit_訊息": "[智能表單設計助手]新增限制上傳檔案大小功能", "提交日期": "2025-06-18 16:50:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant-list/form-design-assistant-list.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc3a2516c6eefb035f28adbff3caeff0c62ab6b2", "commit_訊息": "[智能表單設計助手]配合驗證更新、修正SonarQube掃出之加解密安全性議題", "提交日期": "2025-06-18 16:01:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/pasrameter-setting/pasrameter-setting.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/client/CacClient.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/client/IamClient.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/restful/SmartEmployeesController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/SmartEmployeesService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/FormDesignSupportServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/SmartEmployeesServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/utils/CryptoUtil.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 8}, {"commit_hash": "0456feb9e9349769f8970b0fab8d1e6560d8afb2", "commit_訊息": "[智能表單設計助手]新增數智員工參數設定畫面", "提交日期": "2025-06-18 13:58:33", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/chatfile-routing.module.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/pasrameter-setting/pasrameter-setting.component.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/pasrameter-setting/pasrameter-setting.component.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/pasrameter-setting/pasrameter-setting.component.spec.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/pasrameter-setting/pasrameter-setting.component.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/smart-employees-parameters-routing.module.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/smart-employees-parameters/smart-employees-parameters.module.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/global-config.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/services/chatfile.service.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/restful/SmartEmployeesController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/SmartEmployeesService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/SmartEmployeesServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "03c600cf46c2dc2739ac61633d13e37d04120d9c", "commit_訊息": "[資安]Q00-20250610001 SonarQube安全性議題修正：Cross-Site Scripting (XSS)[補]", "提交日期": "2025-06-17 13:58:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/pipe/safe-html.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/pipes/safe-html.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/shared/pipe/search-highlight.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ProcessArchiveModule/src/app/bpm-design-tool/process-design-tool/shared/pipes/safe-html.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b0f30bed85bba6932395fa781c0f8907fb2f554e", "commit_訊息": "[SYSDT]C01-20250617001 修正Web系統管理中資料來源設定在編輯狀態下調整ID後儲存會發生後端接口調用失敗的問題", "提交日期": "2025-06-17 11:54:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9a30ed4d3facbc3fc1b132556bff4511e5564f2e", "commit_訊息": "[智能表單設計助手]初版", "提交日期": "2025-05-22 17:16:22", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/chatfile-routing.module.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant-list/form-design-assistant-list.component.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant-list/form-design-assistant-list.component.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant-list/form-design-assistant-list.component.spec.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant-list/form-design-assistant-list.component.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant-routing.module.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/form-design-assistant.module.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/generate-form/generate-form.component.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/generate-form/generate-form.component.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/generate-form/generate-form.component.spec.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/generate-form/generate-form.component.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/upload-form-imgage/upload-form-imgage.component.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/upload-form-imgage/upload-form-imgage.component.html", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/upload-form-imgage/upload-form-imgage.component.spec.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/form-design-assistant/upload-form-imgage/upload-form-imgage.component.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/global-config.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/models/form-design-assistant.model.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/pipe/form-designassistant-status.pipe.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/pipe/form-designassistant-template.pipe.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/services/chatfile.service.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/services/form-design-assistant.service.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/PlatformModule/src/app/shared/models/platform.query.condition.model.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/client/CacClient.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/client/IamClient.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/dto/FormDesignAssistantDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/queue/FormDesignQueueService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/restful/FormDesignAssistantController.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/FormDesignAssistantService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/FormDesignSupportService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/FormDesignAssistantServiceImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/FormDesignSupportServiceImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/utils/ApiHelper.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/utils/CryptoUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/utils/GenerateRwdFormHelper.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 35}, {"commit_hash": "90126019c07938de4ab821395bfd81deed1abbd9", "commit_訊息": "[PRODT]Q00-20250613001 修正Web流程管理中在流程畫布上任意空白位置連點兩次無法關閉loading畫面問題", "提交日期": "2025-06-13 17:16:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cfb0a13b4ae8d228a7f227bdeb9e0c144b76e304", "commit_訊息": "[資安]Q00-20250612002 原碼掃描安全熱點:Weak Cryptography ，針對程式內容:Math.random()", "提交日期": "2025-06-13 11:50:08", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/IdGenerator.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75a02248c5544a0665ad139ecf570202285e26a8", "commit_訊息": "[文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题[補]", "提交日期": "2025-06-11 17:58:20", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c355bee6617d2d08d300e487a46a3414d36643b", "commit_訊息": "[文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题[補]", "提交日期": "2025-06-11 17:44:51", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d87d9d9902da0725721b730f529778eb589978ef", "commit_訊息": "[文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题", "提交日期": "2025-06-11 16:04:40", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "994f6b08f403ded8f722b4e5541d6b7008a593f2", "commit_訊息": "[資安]Q00-20250610001 SonarQube安全性議題修正：Cross-Site Scripting (XSS)", "提交日期": "2025-06-10 16:04:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/shared/pipe/safe-html.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/pipes/safe-html.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/shared/pipe/search-highlight.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ProcessArchiveModule/src/app/bpm-design-tool/process-design-tool/shared/pipes/safe-html.pipe.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "3362e9fb830da7c685b1f79152f2f5cb0f7fdbd5", "commit_訊息": "[文件總結助手]Q00-20250526001 调整ChatFile获取token机制", "提交日期": "2025-06-05 11:10:31", "作者": "周权", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileQARecordServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3cfeb11a58840aa610e014939f3e8ff52e4e036a", "commit_訊息": "[ORGDT]C01-20250507002 調整Web組織管理工具中將新增使用者頁面禁用自動填入功能", "提交日期": "2025-05-08 15:45:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/operations/create-user-operation/create-user-operation.component.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "492dc4a477c4acea814060a6f7101bccb91053f7", "commit_訊息": "[行業表單庫]Q00-20250505001 修正行業表單庫因資料撈取筆數限制導致範例表單顯示不全的問題[補]", "提交日期": "2025-05-05 15:47:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/CommonProgramModule/src/app/form/form-repository/form-repository.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "25a32d5a7e79dd70c9e920edab88a56587e04d96", "commit_訊息": "Merge branch 'master' of http://10.40.41.229/BPM_Group/NaNaXWeb", "提交日期": "2025-05-05 14:06:19", "作者": "yamiyeh10", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "3753f2f78c5052ceee4c24b5fa6cfc0cd892acb9", "commit_訊息": "[行業表單庫]Q00-20250505001 修正行業表單庫因資料撈取筆數限制導致範例表單顯示不全的問題", "提交日期": "2025-05-05 13:58:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/CommonProgramModule/src/app/form/form-repository/form-repository.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "44adab7ba313cd2c36aa575504a4200593c3c58f", "commit_訊息": "[PRODT]C01-20250417005 優化Web流程管理中表單存取控管設定欄位過多時滑鼠滾動會有卡頓問題", "提交日期": "2025-05-02 16:31:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1e63a70171e351b9d42a90c22166177b8fd8ed66", "commit_訊息": "[PRODT]Q00-20250416001 修正Web流程管理中設定關卡的表單存取控管設定無法開啟問題", "提交日期": "2025-04-18 10:24:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-attachment-attributes/form-attachment-attributes.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-attachment-attributes/form-attachment-attributes.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-variable/form-type/form-type.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-variable/form-type/form-type.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "c468813a73a22aa233f0263c650d3269c2ba0a96", "commit_訊息": "[SYSDT]C01-20250414004 修正Web系統管理中資料來源設定在編輯狀態下儲存會顯示重複ID訊息問題", "提交日期": "2025-04-15 15:31:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32aa6f992e33fac70b89ed7c2292e5111eea8406", "commit_訊息": "[文件總結助手]Q00-20250409001 調整ISO文件上傳顯示抛轉失敗，報錯“參數校驗失敗\"的問題[補]", "提交日期": "2025-04-10 10:30:27", "作者": "周权", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileISOTransferRecordsServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileKnowledgeServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6c01009a753026f39418b204a5fdcce9574cc19d", "commit_訊息": "[文件總結助手]Q00-20250409001 調整ISO文件上傳顯示抛轉失敗，報錯“參數校驗失敗\"的問題", "提交日期": "2025-04-09 17:48:23", "作者": "周权", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileISOTransferRecordsServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileKnowledgeServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}