{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.6.1", "date": "2022-06-26 22:15:41", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.1", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.5.2", "date": "2022-06-26 22:20:59", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2", "author": "lorenchang"}, "比較時間": "2025-07-18 11:42:06", "新增commit數量": 135, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "47fc3b9c1fad0beea90e01c8688f4ce5a07651cb", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.1", "提交日期": "2022-06-26 22:15:41", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "836c7c4f0a4ebe85d5dfc4b9d5bd70b1efecc3c5", "commit_訊息": "[Web]Q00-20210416002 修正點選常用流程後畫面會先閃一下回原畫面後才進入流程", "提交日期": "2021-04-16 15:52:04", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84f50946fc5b9682f8351d53c241192588ff6e16", "commit_訊息": "[簽核流程設計師]Q00-20210416001 修正在簽出ProcessPackage時會誤取到CustomProcessPackage", "提交日期": "2021-04-16 11:15:25", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "097ccdcbdfd3683ecfb76a2fe7667080361f1822", "commit_訊息": "[內部]更新5.8.6.1 patch檔", "提交日期": "2021-04-15 14:59:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09b6570dce8e4439389021847a1536f7be66e8cf", "commit_訊息": "[內部]Q00-20210414001 行動端於小米手機操作詳情表單發送通知功能在選擇人員的頁面往上滑動功能異常", "提交日期": "2021-04-15 11:13:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "17d8fc28d7e580fc9066ff40938522a35af2e8df", "commit_訊息": "[BPM APP] Q00-20210401003調整行動端表單textbox元件設定浮點數時的樣式", "提交日期": "2021-04-15 10:07:24", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f831f7cd3c526366626f056aebcc6e17edf85dac", "commit_訊息": "[流程引擎]Q00-20210415001修正Collections.sort在1.7版在資料異常的狀況下會報IllegalArgumentException", "提交日期": "2021-04-15 08:53:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/bin/standalone.conf", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/bin/standalone.conf.bat", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "31b484c9c62f6d3c4633b26fa6daf23d0e97b1f0", "commit_訊息": "[BPM APP]Q00-20210414003 修正整合IMG在推播時判斷授權的邏輯異常導致無法推播問題", "提交日期": "2021-04-14 17:34:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "94280b7e692beaae03c35d9f796d04052fd65c94", "commit_訊息": "[內部]Q00-20210414002 整合鼎捷雲且沒BPMAPP序號時動態表單同步失敗", "提交日期": "2021-04-14 16:04:01", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e6b6eb443f4e5d8d7946d565b0928897906bf21b", "commit_訊息": "[內部]Q00-20210413001 用UserId與LDAPId取得User接口Log優化", "提交日期": "2021-04-13 17:54:51", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b5df8cbc26b8b66aa46491160cf70725e5d5cd70", "commit_訊息": "[BPM APP]Q00-20210409011 手寫板橫式時跑版", "提交日期": "2021-04-13 10:32:39", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82df8d03f44d70e31e6904ba461d00c87a11af0f", "commit_訊息": "[BPM APP]Q00-20210409008 修正整合地端IMG時動態渲染表單中的圖片元件顯示不出來問題", "提交日期": "2021-04-12 18:41:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8d42022f939eedfaefcd2cf05f49a437390139fb", "commit_訊息": "[BPM APP]Q00-20210409013 修正安卓手機在產品開窗滾動選擇人員時無法載入下一頁人員", "提交日期": "2021-04-12 18:10:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f3e0421fdc76db7b13c26d2a7d6a64053865098b", "commit_訊息": "[BPM APP]Q00-20210409001 修正安卓手機在發送通知、加簽等功能滾動選擇人員時無法載入下一頁人員", "提交日期": "2021-04-12 18:06:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bbd73d0afc6058b5b816a9e9c4634c772d6cd532", "commit_訊息": "[內部]Q00-20210409010 修正線上人數查詢維護作業若只有IMG使用者在線使用，會顯示無資料", "提交日期": "2021-04-12 15:13:18", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ConnectedUserInfoListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7f103c655fb763117661ccb9b584f425f510694", "commit_訊息": "[內部]更新越南多語系", "提交日期": "2021-04-12 14:26:35", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3fc8367eeba05c9a2f0aa83a495f591a73dac8c2", "commit_訊息": "[BPM APP]Q00-20210409005 修正IMG轉由他人選擇人員頁面搜尋框跑版問題", "提交日期": "2021-04-12 13:53:40", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84bb87f8caeb00a3f6de4e389d549df6cd7767f6", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-04-12 10:16:49", "作者": "詩雅", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "4f53e1e72280fe5703186353c2d380c9e7fb791c", "commit_訊息": "[內部]Q00-20210409004 調整腳本樣版說明與移除行動端不支持的方法，缺少匯入Oracle SQL資料", "提交日期": "2021-04-12 10:15:30", "作者": "詩雅", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e42c36d5a221d6e2f0c623305033942f0a2916c7", "commit_訊息": "[TIPTOP]Q00-20210412001 調整TIPTOP預設出貨流程為SessionBean", "提交日期": "2021-04-12 10:11:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/bpmn/5.25/\\350\\253\\213\\350\\263\\274\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_1_Check.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_2.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_1_Check.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_2.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.8.2.2_TIPTOP_DML_MSSQL_1_Check.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.8.2.2_TIPTOP_DML_Oracle_1_Check.sql", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 7}, {"commit_hash": "c8fec81395eb5948845cf479ef809f6435791167", "commit_訊息": "[BPM APP]Q00-20210409002 修正未進入菜單前先點企業微信推播進入表單畫面會空白問題", "提交日期": "2021-04-09 21:06:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d934708c1a96080bc1b1b6a8d1f3f9da959cd47a", "commit_訊息": "[內部]Q00-20210409009 修正移動各平台推播邏輯異常問題", "提交日期": "2021-04-09 20:13:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "356a1a3033104c7d31d4a649952643389dd00a7c", "commit_訊息": "[內部]Q00-20210409006 修正在整合雲端IMG時動態渲染表單會同步失敗問題", "提交日期": "2021-04-09 18:28:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "81842b82db3ed4ca5cb327190b9d6587585025d2", "commit_訊息": "[TIPTOP]Q00-20210409012修正BPM為多主機時，TIPTOP回寫結案與拋單的主機不同的狀況下，cache無法清除導致TIPTOP重新拋單會失敗", "提交日期": "2021-04-09 17:50:08", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IServerCacheManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TipTopCreatedFormInstances.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "b6fea63e969bafeffa7183954a6d35d84995b327", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-04-09 17:12:51", "作者": "pinchi_lin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "55432a64761ba24d5d24a44a52facb5fb0d96e96", "commit_訊息": "[流程引擎]A00-20210408001 修正若使用者的密碼中包含特殊字元，該使用者無法從搖旗吶喊小助手開啟BPM畫面", "提交日期": "2021-04-09 17:11:30", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ForwardIndexAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8625db5ebba2c3829b12a21031c97cf8cee38283", "commit_訊息": "[內部]Q00-20210408007 加入新增的cac與img網址的系統變數缺少的Oracle SQL", "提交日期": "2021-04-09 17:11:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "da6376e12f1fd13b2da86e1aa64fe985c1102b96", "commit_訊息": "[內部]Q00-20210409003 修正更新雲商品授權日期排程在Oracle的SQL資料多了--導致版更工具異常問題", "提交日期": "2021-04-09 14:13:44", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0c600b884a92a4f3d65048bf386a246f578962c1", "commit_訊息": "[內部]Q00-20210408002 加入動態渲染表單上附件與圖片走外網機制處理的系統變數缺少匯入SQL資料", "提交日期": "2021-04-08 18:37:58", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1b4b9876f34395d023db0d68a1e31ffd2f12fba2", "commit_訊息": "[BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]", "提交日期": "2021-04-08 12:18:42", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MobileAuthorizeUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "12b5d35ca4bdc68ee14b6c45a40ec0a89f2727fc", "commit_訊息": "[BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]", "提交日期": "2021-04-08 12:05:42", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MobileAuthorizeUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b3ea7b6fcef06fbfd8adeb7f3868e5da731713bc", "commit_訊息": "[Web]線上人數查詢維護作業新增使用者登入資訊", "提交日期": "2021-04-08 11:58:18", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/license/ConnectedUserInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/onlinemgt/OnlineUserMgtPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ConnectedUserInfoListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/OnlineUserAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/user_profile/ConnectedUserProfile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "16ad2cc35e7ed1de65b29ef1d80e2e1248fd6f3b", "commit_訊息": "[BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]", "提交日期": "2021-04-08 11:13:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "39cca690f6bee2e19be6902de19d4647c8207600", "commit_訊息": "將5.8.6.1多語系Merge至develop_v58", "提交日期": "2021-04-08 11:00:28", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5861.xlsx", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 2}, {"commit_hash": "a18513599b6cd320e5aacfcf3fcf62f4c0f89bfa", "commit_訊息": "[BPM APP]調整腳本樣版說明與移除行動端不支持的方法", "提交日期": "2021-04-07 16:28:43", "作者": "詩雅", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1373cf4013960473e62ddbafc540787bb998fba5", "commit_訊息": "[補修正]S00-20210323001 整理Log", "提交日期": "2021-03-31 15:12:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b077cc1b854117b3ac219739ba7329701eb04574", "commit_訊息": "[BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]", "提交日期": "2021-03-31 14:25:01", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5861.xlsx", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "a8bb63b2a88184dd21e637a43d00c7ba787fe2e1", "commit_訊息": "S00-20210323001 帳號鎖定功能", "提交日期": "2021-03-30 17:54:08", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/exception/UserLockOutException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 17}, {"commit_hash": "a90609613d436aa23f8d873f452ed81c7270d80d", "commit_訊息": "[內部]新增鼎捷雲平台URL和CAC URL的系統變數[補]", "提交日期": "2021-03-26 15:36:02", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a12f7c24727dda75812e387e78541d4ec534c867", "commit_訊息": "[內部]新增鼎捷雲平台URL和CAC URL的系統變數", "提交日期": "2021-03-26 09:32:52", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7fda6f21eb797758d5d70373f8080853c6362155", "commit_訊息": "[ESS]Q00-20210325002 修正開啟ESS查詢作業模組，ESS頁面顯示'未將對象引用設置到對象的實例'", "提交日期": "2021-03-25 14:48:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "35fbbd70ca795656759848593f3e0aab38845617", "commit_訊息": "[BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]", "提交日期": "2021-03-23 20:41:39", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1de9dd386fdb2e4dafc92fe0850f5715e3e36383", "commit_訊息": "[BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]", "提交日期": "2021-03-23 10:50:52", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b266ff2bb492ecb674cfa01b754a1324271843c4", "commit_訊息": "[SSC]新增SSC整合程式[補]", "提交日期": "2021-03-22 17:47:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9583397501cbf68fe6477344ad01b6b95b4d96a", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]", "提交日期": "2021-03-22 16:04:20", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5e6f26301e0427ce2c77765fa9d3be2e19e90649", "commit_訊息": "[SSC]新增SSC整合程式", "提交日期": "2021-03-12 17:40:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkListParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/PortletEntry.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "080265e5c689795977eac521ae8ddba6e1c8559f", "commit_訊息": "[BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]", "提交日期": "2021-03-11 11:13:30", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d1f3179fe8d01ea33382fc95d5fe35a4aa820a3f", "commit_訊息": "[BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]", "提交日期": "2021-03-10 18:29:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "5c5baacc98379480129fe200e690be6d149b8070", "commit_訊息": "[BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊", "提交日期": "2021-03-10 16:54:57", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.6.1_mobile_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.6.1_mobile_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 15}, {"commit_hash": "eff48fc4644ddef17ecc7c33dcc8a818e908b157", "commit_訊息": "[BPM APP]調整行動端詳情簽核派送時可依系統變數設定啟用檢查簽核意見是否為空功能[補]", "提交日期": "2021-01-28 14:02:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "10f7c63cfc0b16f78c9079b352ae110ccab3823e", "commit_訊息": "[E10]S00-20210105002 E10發單，採用設定檔選擇是否由主部門發起", "提交日期": "2021-01-27 16:36:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "d0b55b5a8a041bc4ab820a5b0bfd1a7fafc40e46", "commit_訊息": "[BPM APP]C01-20210104004 調整授權中間層使用者維護作業使用者人名若為簡體會呈現問號的問題", "提交日期": "2021-01-07 12:01:48", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.6.1_mobile_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "777a6972522e4488e28d3d89bf1f195f9a1f760e", "commit_訊息": "[BPM APP]Q00-20210308003 修正手寫元件唯讀時在移動端會無法顯示內容與點擊會觸發遮罩問題", "提交日期": "2021-04-07 18:17:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "92e9e513e11de7d52242216ee26165d09bc19f41", "commit_訊息": "[流程引擎]Q00-20210407003 修正發起關卡設定\"允許通知信夾帶附件\"下一關為核決關卡時，發起關卡上傳附件派送到下一關會報錯", "提交日期": "2021-04-07 18:09:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "041a0a062b7ab71b7f3a0374bf98d7c51de1a850", "commit_訊息": "[E10]調整E10整合表單在行動端支援多個子單身功能與樣式[補]", "提交日期": "2021-04-07 16:19:25", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ac4ae1aee9e8a5ae99699fa97acbcbdb4dba1205", "commit_訊息": "[流程引擎]Q00-20210407002 追蹤流程可呈現多張名稱相同的表單", "提交日期": "2021-04-07 13:46:13", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "11cba4c3fd3e031e9927fa5d08b23b52568bcc73", "commit_訊息": "[DotJ]Q00-20210407001 修正流程派送異常", "提交日期": "2021-04-07 12:54:58", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f9b65b95f65a8e1bf39d6fe2df88a247e4119847", "commit_訊息": "[BPM APP]Q00-20210330001 修正IMG智能快簽列表在啟用動態渲染表單時有報錯問題", "提交日期": "2021-04-07 11:48:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "340318fd31a9354d8dcbebe184fc50f9e9cfb4a0", "commit_訊息": "[TIPTOP]Q00-20210406001 修正TIPTOP拋單緊急度設定緊急，流程實例的緊急度為一般", "提交日期": "2021-04-06 11:35:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1a96d50ad735624944f54d8c784ca63163b1bd5", "commit_訊息": "[表單設計師]Q00-20210401001 絕對位置表單轉換RWD表單時移除表單內多餘的元件，避免轉換時發生異常導致無法開啟表單", "提交日期": "2021-04-01 15:43:51", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d9bdda92d8d628bb734c14bc5207459f12791ede", "commit_訊息": "[Web]S00-20210326002 表單删除附件時，增加提示「確認是否刪除附件」", "提交日期": "2021-04-01 15:01:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a9a676f8ea577324b4d13629abf1a99219a5a32b", "commit_訊息": "[內部]調整因開APP整合但沒匯動態渲染表單資料表會導致發單或簽核失敗問題", "提交日期": "2021-03-31 14:07:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "c897082e0047fa888e05c19e868ab3ce5c25c119", "commit_訊息": "[流程設計工具]調整選擇流程在移動裝置使用方式時加入表單支持的提示", "提交日期": "2021-03-30 18:27:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/MobilityOperationCellEditorRenderer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "494aec8442e926f0212a17f3efdfa20ebf3e36d4", "commit_訊息": "[Web]Q00-20210329001 解決列印關卡無法列印表單Title元件Logo圖片的問題", "提交日期": "2021-03-29 17:23:11", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a91feb15de2a2927324e0c152013e360115ed0c0", "commit_訊息": "[Web]Q00-20210326002 修正BPM 5.8.5.2版本在ISO新增單、變更單調整「檔案安全性」時，無法顯示html按鈕元件", "提交日期": "2021-03-26 16:53:15", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8738286b689efe84802765e2e80b555fe24bcdb", "commit_訊息": "[BPM APP]調整動態渲染表單上附件資料在雲地與內外網的不同處理方式的邏輯[補]", "提交日期": "2021-03-26 16:16:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f9069f620aed5f6c97665b7c5cd261b28068b582", "commit_訊息": "[BPM APP]S00-20210318001 調整IMG追蹤流程列表的預設時間區間改系統變數控制", "提交日期": "2021-03-26 15:47:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7ec49f9b7c8b2cd3b14e1968e7edd54400354f98", "commit_訊息": "[內部]Q00-20210326001 增加E10回寫任務的log訊息", "提交日期": "2021-03-26 14:22:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecbae91148e8d89ef51714c753677a50d33085c7", "commit_訊息": "[BPM APP]優化行動端發送通知、加簽、轉派、退回等功能與畫面", "提交日期": "2021-03-25 19:27:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "d5dd6c9dcfcb25861bcab2cc6ed63a0ce7c7f50b", "commit_訊息": "[BPM APP]調整動態渲染表單上附件資料在雲地與內外網的不同處理方式的邏輯", "提交日期": "2021-03-25 16:36:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DataListContent.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleTransAttachmentRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterTransAttachmentRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataTransAttachmentRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "a877b2b2e38033ea298cc70c8c01d6a3dfd549cb", "commit_訊息": "[BPM APP]調整動態渲染表單上圖片資料在雲地與內外網的不同處理方式的邏輯[補]", "提交日期": "2021-03-25 15:20:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9782161074997038185e7dd3a51266787eb82245", "commit_訊息": "[BPM APP]調整動態渲染表單上圖片資料在雲地與內外網的不同處理方式的邏輯", "提交日期": "2021-03-25 15:13:15", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c90dc8ee85b25d1182251d8b0f5a90b983115185", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-03-25 11:39:37", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "de21c8691b094928e996a2ac2bfb03833b695928", "commit_訊息": "[內部]Q00-20210325001 修正每個User進入首頁console就會印出四行SQL造成系統負擔", "提交日期": "2021-03-25 11:38:46", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b71c24f6f8b24043d6b7d5047bc074615aae650a", "commit_訊息": "[流程引擎]A00-20210315002 修正從HR同步時，設定檔內的部門/專案 有設不同步部門層級時，依然會同步該欄位而導致報錯", "提交日期": "2021-03-24 18:17:18", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-importer/src/com/dsc/nana/user_interface/apps/org_importer/util/OrgXMLGenerator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/OrgXMLGenerator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6389b635266dd82cac6bed57a2ecc49c0e7d85fe", "commit_訊息": "[流程引擎]Q00-20210324002 調整流程關卡自動簽核「2.與前一關同簽核者，則跳過」邏輯", "提交日期": "2021-03-24 15:39:56", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "890726931563e1b48422a2b76edccbf60809dcdb", "commit_訊息": "[Web]Q00-20210324001 修正Chrome,Edge列印絕對位置表單時，因為表格標頭跨頁重複顯示導至與其他元件重疊", "提交日期": "2021-03-24 11:31:03", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d34eb28e1d6f41fc620be992b18ca4641428a3ba", "commit_訊息": "[流程引擎]Q00-20210323001 使用者取回工作重辦清單過濾通知關卡，讓使用者無法取回通知", "提交日期": "2021-03-23 16:55:07", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0d739886e54ffe5c3d6e4ce67c3e8c3d71b8c33", "commit_訊息": "[Web]A00-20210101001 修正列印會出現右邊邊界留白過大的狀況(圖一)，該情況在不同瀏覽器均如此", "提交日期": "2021-03-23 11:59:12", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "44289f62bb4877daf80ee9e90116a7a2bd67647c", "commit_訊息": "[BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數[補]", "提交日期": "2021-03-22 16:29:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63685821a848b57b294e43effa40976ca2daa58d", "commit_訊息": "[BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數[補]", "提交日期": "2021-03-22 16:26:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f012e6528b8cef1e7d9dcdeb87983d0d952a5a0d", "commit_訊息": "[BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數", "提交日期": "2021-03-22 16:19:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "07406ffddfa496d041767b8cc18766da0e4303c0", "commit_訊息": "[流程引擎]Q00-20210319004 加簽時將表單設定為唯讀，原本權限為完全控制的附件元件也會在加簽關卡中被調整成唯讀", "提交日期": "2021-03-19 17:01:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5055444ad1f5fda4cd8efb785fd7f1d458a92e7e", "commit_訊息": "[Web]Q00-20210319003 退回重辦頁面加上簽核意見的片語按鈕", "提交日期": "2021-03-19 14:44:24", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "21899d680a2339555b608a7c654841cfc4dcb217", "commit_訊息": "[流程引擎]Q00-20210319001 工作歷程依流程關卡設定限制發送工作通知", "提交日期": "2021-03-19 11:05:53", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "aa4e7b522ba61b50cd395061eea5ad4d1437089f", "commit_訊息": "[BPM APP]Q00-20210318002 修正行動端詳情表單發送通知功能在選擇發送通知人員時有重複人員的問題", "提交日期": "2021-03-19 10:10:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "73b3bbdb8d1122677d9c0cc94b2b077ee4d31610", "commit_訊息": "[Web]Q00-20210318003 調整BPM登入頁面，提高系統安全性", "提交日期": "2021-03-18 17:43:26", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ForwardIndexAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "22b2ea601c290d162afd7d8f3305944a84eb4ee8", "commit_訊息": "[AD登入]C01-20210315009 修正使用者同時有兩個以上AD，密碼又不一樣會因為直接拋密碼錯誤，導至密碼正確的AD驗證不到", "提交日期": "2021-03-17 17:30:45", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d8663452f627350d8ec4cae1f86cd2a8caa5a2b", "commit_訊息": "[Web]A00-20210317002 修正進入多個處理者僅需一人處理的待辦事項，若使用者未接收工作就返回待辦清單，畫面卡住問題", "提交日期": "2021-03-17 16:18:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ce0dc23aa9869c332c0b6e66ebd0d24cb2f6fef3", "commit_訊息": "[T100]結案回寫給T100，T100回饋錯誤訊息，通知系統管理員的Mail會顯示T100回饋的訊息", "提交日期": "2021-03-17 16:05:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3dd396427b9357cac39585d4951270c6c3397c46", "commit_訊息": "[BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]", "提交日期": "2021-03-17 13:37:55", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "590cf36b92ee64e7abe26e7957075a8662c846f6", "commit_訊息": "[BPM APP]調整行動端詳情表單加簽功能的工作分派的方式", "提交日期": "2021-03-17 11:25:58", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "05791c73dbeaed834ad8eb1afccb53a41239c8de", "commit_訊息": "[Web]C01-20210312001 修正在\"非\"發起人關卡上傳附件(勾選在線閱讀)，點選上傳的附件會出現閱讀檔未準備好", "提交日期": "2021-03-16 16:48:28", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e48bb878b839ae356c97ba70407d5b1550ae9d28", "commit_訊息": "[Web]Q00-20210316003 修正附件元件設定FullControl 上傳檔案之後點擊下載按鈕出現請洽系統管理員", "提交日期": "2021-03-16 16:20:16", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "166f04df5d4745c98295747a7e4e5e027e322561", "commit_訊息": "[Web]Q00-20210316002 修正在線閱覽的檔案點擊沒有反應", "提交日期": "2021-03-16 16:14:06", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e2236eeb19450162c4a29c7037fcf9656fb26bc4", "commit_訊息": "[Web]Q00-20210316001 修正關卡設定必須上傳附件，沒有上傳卻能通過", "提交日期": "2021-03-16 16:01:43", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b33bb86821264ea77d52e6679278299af7e849a1", "commit_訊息": "[BPM APP]C01-20210315006 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題[補]", "提交日期": "2021-03-16 16:01:06", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1fa59f249064f0d5f402aceaae9ea83b893675e", "commit_訊息": "[BPM APP]C01-20210315006 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題", "提交日期": "2021-03-16 15:52:43", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9a78cb30f3baeaec08caf3ad77341e41f0e9fa8", "commit_訊息": "[BPM APP]C01-20210310003 修正企業微信在iOS手機上，滾動查看到完整附件內容，操作不易的問題[補]", "提交日期": "2021-03-16 14:34:54", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d37971926797f616c46b0db396319db3caec1bb6", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-03-16 12:03:33", "作者": "peng_cheng_wang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "9b93a46a262897fa32fd0f2c383776d3a8caa7ae", "commit_訊息": "[BPM APP]C01-20210310003 修正企業微信在iOS手機上，滾動查看到完整附件內容，操作不易的問題", "提交日期": "2021-03-16 11:41:14", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "cf9467594047f5408afb55cb95477327a7de9134", "commit_訊息": "Revert \"[流程引擎]Q00-20210315004 修正TextBox設日期/時間型態，年月日之間以dash作為分隔符在轉存表單時會錯\"", "提交日期": "2021-03-16 11:24:16", "作者": "peng_cheng_wang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b16c4a844adcc7c9495edc9f75b2d45d3cd9b59d", "commit_訊息": "[流程引擎]Q00-20210315004 修正TextBox設日期/時間型態，年月日之間以dash作為分隔符在轉存表單時會錯", "提交日期": "2021-03-15 17:52:36", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2881b0490227a134c284277b0e68926056e75a68", "commit_訊息": "[E10]調整E10整合表單在行動端支援多個子單身功能與樣式", "提交日期": "2021-03-15 17:41:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "faf227a54895e265a8ab4be54b0b828f4193d946", "commit_訊息": "[流程引擎]Q00-20210315002 修正搖旗吶喊小助手顯示的待辦事項數量與BPM待辦事項清單上的數量不符", "提交日期": "2021-03-15 16:04:36", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NotifierListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2b223a65def66aaffee351bf288f2864e231b405", "commit_訊息": "[Web]Q00-20210315001 維護查詢樣版js新增「重載頁面」功能", "提交日期": "2021-03-15 15:54:51", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7d2704c387f04c14ad21e47fe898fda53273e03", "commit_訊息": "[流程引擎]Q00-*********** 單身繫結元件為記錄Radio元件實際值的隱藏欄位，且單身欄位代號與元件代號相同，儲存表單失敗[補]", "提交日期": "2021-03-12 18:29:19", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18087235d7dd3c0aeb6f23525b87b730d730e3cc", "commit_訊息": "[流程引擎]A00-20210311002 修正若進行中的關卡有多個處理者，撤銷流程清單上資料的實際數量與下方顯示的資料總筆數不符", "提交日期": "2021-03-12 17:32:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AbortProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1179312b5b780929a9b0b4585dfba5f895e318be", "commit_訊息": "[Web]A00-20210311001 修正 Safrai在追蹤，待辦，監控流程及發起流程下載附件須把下載視窗關閉，才能再下載附件", "提交日期": "2021-03-12 14:18:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9f4716b01b3988737865b761c8501ca3ac233058", "commit_訊息": "[E10]Q00-20210311005 修正E10取得簽核歷程排程，在執行第一筆E10單據報錯後，後面的E10單據就無法往下執行", "提交日期": "2021-03-11 16:58:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "be5771b5ed940eb7bb071377aafd1bd2ac08d052", "commit_訊息": "[Web]S00-*********** 新增簡易流程圖支援核決關卡預先解析處理人員", "提交日期": "2021-03-11 16:36:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "986759923c01af88abe3c672ccb91d44d74983b4", "commit_訊息": "[流程引擎]Q00-*********** 表單單身繫結元件為記錄Radio元件實際值的隱藏欄位，且單身欄位代號與元件代號相同，儲存表單發生異常", "提交日期": "2021-03-11 15:07:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe64f15e5a09136dc81abe1ea32d9c22867f22dc", "commit_訊息": "[Web]Q00-20210311003 修正Chrome升級到89.0.4389.82版，在追蹤，待辦，監控流程及發起流程，無法下載附件", "提交日期": "2021-03-11 14:35:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0803b61e1648d027287c00ca6e1546230742a0c0", "commit_訊息": "[BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]", "提交日期": "2021-03-11 11:10:21", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileCommonManageTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MobileAuthorizeUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "2159a4a404c221c76b35fa72de7c1749d606c80c", "commit_訊息": "[Web]Q00-20210310001 修正表單欄位設顯示小數後幾位和跨欄位運算，且流程關卡中欄位驗證是onblur或both，使欄位顯示異常", "提交日期": "2021-03-10 18:10:03", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5720aea520e65b08f808f12ea1b7989bce9433da", "commit_訊息": "[ESS]新增ESS整合表單:ESSF01B員工排班(多人多天)", "提交日期": "2021-03-10 17:10:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF01B\\345\\223\\241\\345\\267\\245\\346\\216\\222\\347\\217\\255(\\345\\244\\232\\344\\272\\272\\345\\244\\232\\345\\244\\251).form\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "1a3a9ae1bf0dc7e99d13125b3f334ccb30b68534", "commit_訊息": "[流程引擎]C01-20210305004 修正5621以下OracleDB客戶版更後，流程派送報錯", "提交日期": "2021-03-10 15:50:33", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle_3.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.2.2_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c413f549032c902068ecd13ff92371083734f06a", "commit_訊息": "[流程引擎]A00-20210305001 修正若資料庫為Oracle DB，在授權的流程清單使用建立時間排序發生異常[補]", "提交日期": "2021-03-10 11:17:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9681d96ca64f4d7b835637e6940d0373ad90729b", "commit_訊息": "[Web]Q00-20210309001 修正若資料庫為Oracle DB，追蹤流程點擊授權的流程使用「流程名稱」進行排序會報錯[補修正]", "提交日期": "2021-03-09 11:30:16", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "353c37b82e26c50a0381960b68cec27f650159fd", "commit_訊息": "[Web]Q00-20210309001 修正若資料庫為Oracle DB，追蹤流程點擊授權的流程使用「流程名稱」進行排序會報錯", "提交日期": "2021-03-09 11:25:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07eff3d20e311e22cfbdbb4c2fecb71d7f17fba5", "commit_訊息": "[Web]Q00-20210308005 修正監控流程中進入XPDL流程，流程圖太大沒出現scrollbar，以致無法查看流程圖", "提交日期": "2021-03-08 16:44:22", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "97cf0ee71f18e31a50fa3dd5473a466ad51b7300", "commit_訊息": "[內部]Q00-20210308004 調整字串(String)轉多語系(Locale)方法，加入NULL判斷避免資料轉換時發生異常", "提交日期": "2021-03-08 16:26:34", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/Locale2StringFieldConversion.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f81562e8e60c13624c36de7ad03bcfa646f1c8a8", "commit_訊息": "[E10]Q00-20210308001 修正E10流程畫面子單身按鈕變成html字串", "提交日期": "2021-03-08 11:23:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4bfc8cfca326ad90afe50ae77893735e1d8e733b", "commit_訊息": "[流程引擎]A00-20210305001 修正若資料庫為Oracle DB，在\"授權的流程\"清單使用\"建立時間\"排序發生異常", "提交日期": "2021-03-05 18:15:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fdb3d849cd7e27aff78079542674f3327cd8dc8", "commit_訊息": "[BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]", "提交日期": "2021-03-05 13:34:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SystemConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/SystemConfigManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "0975fce8923635aa7f546497eefb8817e79c2f3e", "commit_訊息": "[Web]Q00-20210304001 調整JavaScript浮點數運算誤差造成欄位計算結果不符預期", "提交日期": "2021-03-04 17:25:45", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "591c95d1344ade6de7f2f93e724986f140229583", "commit_訊息": "[流程引擎]A00-20210303001 修正建立workItem轉派記錄時發生NullPointerException問題", "提交日期": "2021-03-04 14:53:01", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5df4bd373cb9faa71976e6270ad0755d367c4ed4", "commit_訊息": "[BPM APP]新增Web表單設計師的檢視表單發行歷程添加行動端動態渲染表單同步狀態功能", "提交日期": "2021-03-04 13:36:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/FormDefinitionHistoryForListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionHistoryListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/JSONConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "47742d46616a24c58561f48173f6663192410538", "commit_訊息": "[BPM APP]調整行動端相關功能在各平台的授權卡控機制", "提交日期": "2021-03-04 11:56:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MobileAuthorizeUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/PatternViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "ca741e9eacb6647f42110d9edc61bf6cd4cb6ec2", "commit_訊息": "[E10]A00-20210304001 修正E10單據在沒有附件的狀況，到取得附件關卡，無法派送到下個關卡", "提交日期": "2021-03-04 11:22:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "56e494672bcb35876805e982ca8a33a473a82372", "commit_訊息": "[Web]A00-20210225001 修正工作通知頁面用流程發起人來搜尋，會搜尋不到資料", "提交日期": "2021-03-03 17:54:53", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4dc25e3ceb7d54b0f763611a789b86d071cfd868", "commit_訊息": "[Web]Q00-20210303002 修正「開發教學索引->表單元件介紹及範例->CheckBox/RadioButton」範例內容錯誤", "提交日期": "2021-03-03 11:29:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07031f8c0a89d888a954be9e6bca270d0af63abb", "commit_訊息": "[流程引擎]Q00-20210303001 修正時間已達到活動逾時通知設定的間隔時間，但活動逾時排程未發送通知信", "提交日期": "2021-03-03 10:47:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a90fdc53ba1f013415668e7c7ee28ed4ae6b560", "commit_訊息": "[Web]Q00-20210302001 修正流程有迴圈設計，在迴圈的指定關卡為服務任務，造成簡易流程圖檢視異常", "提交日期": "2021-03-02 11:55:04", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b6e9a0b5f52c5d3500acc760567be1b336bff0b3", "commit_訊息": "[Web]修正 Chrome在追蹤，待辦，監控流程及發起流程下載附件須把下載視窗關閉，才能再下載附件", "提交日期": "2021-02-26 18:25:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}]}