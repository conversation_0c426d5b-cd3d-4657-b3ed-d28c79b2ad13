{"company_id": "06221001", "company_name": "順達", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06221001_順達\\01客戶基本資料", "files": [{"filename": "[順達] 連線資訊.txt", "raw_content": "VPN：L2TP , 共用金鑰：1111\r\nIP  ：*************\r\n帳號：DSC\r\n密碼：DYNAPACK2008\r\n--不能連了--\r\n\r\n***********(測試機)\r\nBirthday121$\r\nadministrator’s pwd: P@ssw0rd\r\nhttp://***********:8888/NaNaWeb\r\n\r\n***********(正式機)\r\n帳號：.\\administrator\r\nadministrator’s pwd:Birthday121$\r\nhttp://***********:80/NaNaWeb\r\nWEB PW: Gugi@1124\r\n\r\n172.19.1.24(資料庫)\r\n帳號：.\\administrator\r\nadministrator’s pwd: Birthday121$\r\n   <user-name>sa</user-name>\r\n        <password>1QAZ!qaz</password>\r\n\r\nTEL:03-3963399 #7602 陳金祥(Brain)\r\n(大陸分機 #87601)\r\n\r\n\r\n\r\nhr : 郭郁婷 <EMAIL>\r\nTT : 陳亭佑 <EMAIL>\r\n-------------------\r\n \r\n測試區 AP：*********** , DB：***********\r\n帳號：.\\administrator\r\n密碼：Birthday121$\r\n \r\n ", "structured_data": {"vpn": "L2TP , 共用金鑰：1111", "host": "*************", "username": ".\\administrator", "password": "Birthday121$", "測試區 ap": "*********** , DB：***********", "administrator’s pwd": "Birthday121$", "http": "//***********:80/NaNaWeb", "web pw": "Gugi@1124", "tel": "03-3963399 #7602 陳金祥(Brain)", "hr": "郭郁婷 <EMAIL>", "tt": "陳亭佑 <EMAIL>"}, "source_path": "C1.客戶維護相關\\06221001_順達\\01客戶基本資料\\[順達] 連線資訊.txt", "file_size": 722, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:30.625895"}], "total_files": 1, "processed_at": "2025-08-26T10:46:30.625904"}