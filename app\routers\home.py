"""
首頁路由
"""
from fastapi import APIRouter, Request
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from pathlib import Path
import sys

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.config import APP_CONFIG, TOOLS_CONFIG, BPM_PATH_DIR, BPM_RELEASE_DIR

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/home", response_class=HTMLResponse)
async def home_page(request: Request):
    """首頁"""
    # 檢查必要目錄是否存在
    warnings = []
    if not BPM_PATH_DIR.exists():
        warnings.append("未找到data_output/bpm_path目錄，檔案索引查詢功能可能無法正常使用")
    
    if not BPM_RELEASE_DIR.exists():
        warnings.append("未找到data_output/bpm_release目錄，產品release記錄查詢功能可能無法正常使用")
    
    context = {
        "request": request,
        "app_config": APP_CONFIG,
        "tools_config": TOOLS_CONFIG,
        "warnings": warnings,
        "page_title": "首頁"
    }
    
    return templates.TemplateResponse("home.html", context)
