{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.7.2_All", "date": "2022-06-26 22:04:59", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.7.2", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.7.2", "date": "2022-06-26 22:04:59", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.7.2", "author": "lorenchang"}, "比較時間": "2025-07-18 11:21:09", "新增commit數量": 159, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "179c56851dbad8ac325ddfd2749ddeab133b0574", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.7.2", "提交日期": "2022-06-26 22:04:59", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "066f8a112c70928de1375b22f1b80e76fd4a72e4", "commit_訊息": "[流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常", "提交日期": "2023-05-24 17:36:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "214ad023e003058c65752d036ebbd60c213def73", "commit_訊息": "[Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息", "提交日期": "2022-11-11 11:07:05", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "031a267dd97509fe130456e8c578185f2ca5fbf2", "commit_訊息": "[Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題", "提交日期": "2023-02-14 14:03:47", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "86cf90d9d8384a9303b603fe3bb89df985183f4c", "commit_訊息": "[Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息", "提交日期": "2022-09-08 14:06:21", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4ad3f66ab1fe8c9e978fbd5b9c68bbd938498885", "commit_訊息": "[Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。", "提交日期": "2022-10-28 10:41:16", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5749d46e9d25513cf20f0d7c5ecb9f602887baa1", "commit_訊息": "[Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久", "提交日期": "2022-06-28 18:06:01", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f315488fff190a22cb3c9bc638f5d7312bd73228", "commit_訊息": "[流程引擎]Q00-20220427002 調整流程多次逾時通知中記錄通知時間的邏輯，若該流程有逾時的情況發生，則逾時通知時間以流程逾時排程觸發時間做記錄", "提交日期": "2022-07-01 17:48:15", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "96b7311ce44406e8e9e6db31ac9e64d4e994963b", "commit_訊息": "[TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久", "提交日期": "2022-08-19 17:57:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d3edeb7e665f146e5dfb8d7c32323f4836fc4e0", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock", "提交日期": "2022-07-28 15:20:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "261c890398c9fab536d34f13916f0db70282236d", "commit_訊息": "Revert \"[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock\"", "提交日期": "2022-08-22 16:11:47", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9764e744567867da49915e0aa68ecbb8e94c3e1d", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock", "提交日期": "2022-07-28 15:20:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e2b7fd954ea2330b55efdfcdd59eea330f079ad3", "commit_訊息": "[流程引擎]Q00-20220713005 修正核決關卡設定自動簽核，取/退回後再次簽核進核決層級時會報錯無法派送", "提交日期": "2022-07-14 11:32:09", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "40efbd7c75332954ba8ef7792ef5653372e0c32e", "commit_訊息": "Revert \"[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。\"", "提交日期": "2022-08-10 09:15:00", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b855b727b8d76aa36a51f53184480c77b4393d11", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況", "提交日期": "2022-07-29 00:04:37", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5dd151453570c0e7c4ae85b1f466923828eba964", "commit_訊息": "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。", "提交日期": "2022-07-29 16:30:05", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e3e8fcd1478d2ba3637232a468522283559839fa", "commit_訊息": "[Web]Q00-20220714003 修正Dialog元件的txt屬性如果被FormScript或其他非預期方式刪除，在產生表單畫面時報錯 如果屬性被改成null或是遺失將其防呆為空字串 內部測試將元件隱藏一關或是連續兩關以上都無法重現，應該是客戶的Script有改到元件內容", "提交日期": "2022-07-19 09:58:12", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2df45dcda7d9841df614d36f62406cabdb007e13", "commit_訊息": "[流程引擎]Q00-20220707002 修正表單日期元件預設值計算錯誤 相關議題單：C01-20220701004。", "提交日期": "2022-07-19 09:45:32", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "33359a18740ab947aa0c906f671ef350f909a6ec", "commit_訊息": "[Web]Q00-20220411005 修正使用者在絕對位置表單進行簽核時遭遇產品程式錯誤", "提交日期": "2022-05-12 14:12:31", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f04e1194cc7c70e177d5ffd14cd36641bf721790", "commit_訊息": "[流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差[補]", "提交日期": "2022-07-12 10:34:44", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea0381bd6d850f92ad9ec06cacec468835476758", "commit_訊息": "[流程引擎]Q00-20220706005 修正資料庫為MSSQL，且流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能", "提交日期": "2022-07-06 17:43:27", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2749e7c2c52bcb40038fe4c63385dac6dc95eea2", "commit_訊息": "[表單設計師]Q00-20220711002 修正絕對表單元件不存在某些屬性而取用該屬性導致無法開啟表單，增加防呆", "提交日期": "2022-07-11 15:22:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70be732e8d00998d7fb7b5bb5eafa46289972432", "commit_訊息": "[Web]A00-20220622002 修正流程新增關卡頁面輸入簽核意見在新增向前or向後關卡按下確定後，簽核意見內容被清除", "提交日期": "2022-06-23 14:10:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d53ed15ff0599542620bb1f4ef7c4d214dbaf8a4", "commit_訊息": "[Web]Q00-20220523001 修正同瀏覽器有二次登入時，登入頁「記住我」的功能會失效", "提交日期": "2022-06-22 15:47:30", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8853a57379d7fc32309e3f3937b2d22614cd734d", "commit_訊息": "[Web]A00-20220616003 修正SerialNumber元件的字體設25px以上，在流程中該元件的顯示會有部分被遮蔽到", "提交日期": "2022-06-21 16:55:38", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53cb3a882a4e05afe524eceae32e3de87862bdd8", "commit_訊息": "[Web]Q00-20220621005 調整RWD Grid當單身資料太多時導致開啟表單時間較長", "提交日期": "2022-06-21 15:19:39", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0522091773b35b0f749e5a8980a1d13c5ff25321", "commit_訊息": "[Web]Q00-20220525004 修正輸入單身資料有&#加任意數字，被轉成特殊符號，會與輸入資料不符", "提交日期": "2022-05-25 16:48:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8222568ca59a20fb62f5605e33eb31a6ae51ab0a", "commit_訊息": "[Web]Q00-20220621003修正發起流程-查詢流程清單搜尋純數字的流程名稱會報錯", "提交日期": "2022-06-21 13:42:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29d76e647d92a97dd9bba00691a2753344b6b079", "commit_訊息": "[流程引擎]Q00-20220622001 修正當RWD表單的RadioButton元件與CheckBox元件選項內容太長時會斷行 相關議題單：C01-20220613009。", "提交日期": "2022-06-24 16:30:14", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d847f5275ed3a5c8367dce9ea7ae7336ec069d9", "commit_訊息": "[Web]Q00-20220621001 修正非CheckBox或RadioButton的選擇元件執行到額外輸入框邏輯導致出現非預期異常", "提交日期": "2022-06-24 16:29:41", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c3aabbfcfcee5a6c724d352247d61520debed65", "commit_訊息": "[Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾", "提交日期": "2022-06-13 16:14:20", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "240e733f27d6bd44f471fff1ced2d845055109bd", "commit_訊息": "[流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯[補修正]", "提交日期": "2022-06-14 11:52:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e78ba73c4e6806017f15d00a78a6fbc5d8087c27", "commit_訊息": "[流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯", "提交日期": "2022-06-13 16:07:01", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "650fe7e1d17c16de1da34df84172230c3c42f5ba", "commit_訊息": "[Web]A00-20220608003 修正進入追蹤流程畫面時未清除「撤銷理由」欄位內容", "提交日期": "2022-06-09 17:18:03", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d3076768dff0bf32007adbf82b44c310ec93432", "commit_訊息": "[流程引擎]Q00-20220609003 修正使用者操作個人預設代理人設定時，代理人可能有多筆相同人員的問題", "提交日期": "2022-06-09 16:32:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/user_profile/MultiDefaultSubstituteForManaging.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "edbba9c324510e464193367ee0a20eb290d0716d", "commit_訊息": "[WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務-5872專用", "提交日期": "2022-06-24 15:19:30", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8f0a8aa22d9c9523351cee496ffb70cd36f8334b", "commit_訊息": "Revert \"[WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯\"", "提交日期": "2022-06-24 15:01:56", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65cbe71c5de441842842a7054e03ed9aee86ff3b", "commit_訊息": "[WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯", "提交日期": "2022-06-08 11:49:23", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f3e4eb87b4de8cbebe9ed8533cd6c5d38d1b38a", "commit_訊息": "[報表設計器]Q00-20220607003 修正欄位字串如果含as會辨識錯欄位名稱", "提交日期": "2022-06-08 09:21:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5af16e8c8b16133c804bca27c2b23b556b0c447", "commit_訊息": "[Web]Q00-20220607002 修正首頁待辦清單第三頁以上的流程進行派送時會報錯", "提交日期": "2022-06-07 14:20:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37d7606d28bf5907d78fdb3a5bcf86b3ba635a8d", "commit_訊息": "[流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差[補]", "提交日期": "2022-06-24 14:55:21", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53f9dc73ea92cb5ce550e272c1e204d12e05d13c", "commit_訊息": "[Web]Q00-20220518001 修正退件表單資訊與開啟的表單關連錯誤", "提交日期": "2022-05-18 10:39:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d399fecd6ec3ef7394f03e6496268e655d7493bd", "commit_訊息": "[流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差", "提交日期": "2022-05-12 14:36:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a7204e6594be3721e6921f81e698d7a6e928756", "commit_訊息": "[資安]Q00-20220331001修正漏洞：透過下載附件URL替換檔案名稱就能下載任意使用者的附件", "提交日期": "2022-04-01 15:52:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "692c8e4b59b008be2e07da4f34f909963df5091a", "commit_訊息": "[Web]Q00-20220606001 修正第二關之後的關卡預解析，流程線的條件式採用表單欄位時，預解析的關卡與派送的關卡不符合", "提交日期": "2022-06-06 14:26:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d860c2e1293a45eac01134b2046a6b7cd11cd6b9", "commit_訊息": "[內部]Q00-20220609002 調整DWR設定讓Log不要一直出現轉換ProcessInstanceStateType的錯誤", "提交日期": "2022-06-09 15:28:14", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5e02f2f35ad5b52756683df12a2808bbd8de28e0", "commit_訊息": "[內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤", "提交日期": "2022-04-27 15:00:41", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b48d5d07b84cd113d87c995a604f4bc93faf8bda", "commit_訊息": "[內部]Q00-20220610001修正WorkFlow拋單log會顯示[Fatal Error] :1:1的錯誤訊息", "提交日期": "2022-06-10 11:42:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "832968d34564f151e23c5feeb2bd7911157a1894", "commit_訊息": "[TIPTOP]Q00-20220525003 修正拋單的單身資料有中刮號會被轉成小括號，導致資料與TIPTOP不符合", "提交日期": "2022-05-25 16:32:13", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0226b72f52409952bd96c43c7223f2ba2e416fb4", "commit_訊息": "[表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出", "提交日期": "2022-05-25 17:01:11", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9535754a704a2ad33fcc78a74176310702d71a02", "commit_訊息": "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開", "提交日期": "2022-06-09 09:55:48", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e2b11fe93688fcb6902c966f48d69bc7557d079", "commit_訊息": "[Web]Q00-20220413003 修正DialogInputLabel有設提示文字，版更到5872後進入流程該元件未顯示提示訊息", "提交日期": "2022-04-13 14:39:06", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c18997f674d39942b89295ffe44b9f3a282f11f2", "commit_訊息": "[Web]Q00-20220413002 修正DialogInput有設提示文字，版更到5872後進入流程該元件未顯示提示訊息", "提交日期": "2022-04-13 14:08:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f38a873ec959a65936b3b9e87a5ff358ee2d439c", "commit_訊息": "[流程引擎]Q00-20220406001 修正因日期元件缺少特定屬性造成流程無法正常往下派送的問題", "提交日期": "2022-04-11 16:40:27", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bdacb313baa10b171442c3ce5e9340de7a6e970d", "commit_訊息": "[Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]", "提交日期": "2022-06-10 18:10:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9fbf3d4ffd5089cc0d6c5f0986878224d86d953e", "commit_訊息": "[Web]Q00-20220527003 修正使用者使用監控流程的最大筆數沒有根據process.default.show.records的設定", "提交日期": "2022-05-27 17:34:09", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f5d71f3da08221d90f9fe8083c7fe4bc64600abb", "commit_訊息": "[內部]V00-20220414004 修正流程負責人-監控流程-點選「建立時間、流程結案時間」欄位排序，會出現請洽系統管理員", "提交日期": "2022-04-18 16:28:58", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "26c369b1dda8e575d343f096944b0f23e7f995fa", "commit_訊息": "[內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方", "提交日期": "2022-05-30 15:34:30", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a30e071fcf54a84dafc31103d5414229ba7fb0bc", "commit_訊息": "[Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果[補]", "提交日期": "2022-05-05 17:52:51", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "27531f04ffbec5b79ca5a10f57cc14eebf402e45", "commit_訊息": "[Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果", "提交日期": "2022-05-05 15:24:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1cc3592da1e8f913fd7304e57cdf33faab1a05c8", "commit_訊息": "[Web]V00-20220422002 修正有取回重辦後，在行動版的待辦清單頁中該流程的主旨會無法正確顯示", "提交日期": "2022-04-22 16:50:44", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22914e54f001fd8de2449f721e9a864aeb7660cd", "commit_訊息": "[Web]V00-20220422001 修正行動版的待辦清單頁面中，有使用智能示警會無法正確顯示", "提交日期": "2022-04-22 16:33:23", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c262c086edcf140c5e57cf3bffd5fe9ea2e18a17", "commit_訊息": "[Web]Q00-20220421002 修正Radio&Checkbox在切換不同的Binding欄位內容時，還殘留前一次被勾選樣式的問題", "提交日期": "2022-04-21 11:58:20", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75ff694dcbd5b5406526b88110d220d4f8c185b9", "commit_訊息": "[Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]", "提交日期": "2022-04-19 16:50:47", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8fdddd93eaabbd8b4ac168b20772d9a0c1b0cf9", "commit_訊息": "[Web]Q00-20220419001 修正如果切換讀取較久的頁面視覺上會跑一半又呈現原畫面再跳轉", "提交日期": "2022-04-19 10:45:07", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "536bd0a720f69da0c14942337d1b1b09dd5a8ab0", "commit_訊息": "[內部] 調整瀏覽器閒置過久，請重新登入的多語系", "提交日期": "2022-04-14 09:58:18", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "397d975ab6b61293bdf81a3737f88e18a7de4818", "commit_訊息": "[Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]", "提交日期": "2022-05-25 14:01:03", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a5749ed2900e2cd5132425a2a9350e0bd4d3c4a1", "commit_訊息": "[Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]", "提交日期": "2022-05-25 10:57:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32800924d1b0d1ba9d5de73ddcf603c55de8e3eb", "commit_訊息": "[Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤", "提交日期": "2022-05-24 17:45:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "823734ca2650d9ab168b39501e9480424d2d454c", "commit_訊息": "[內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)", "提交日期": "2022-05-23 18:19:20", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f77b839ef081cbe80b997ea906f14afab6bdf56a", "commit_訊息": "[流程引擎]Q00-20220520003 修正流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能", "提交日期": "2022-05-20 15:42:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e0a80f0ddabdd8e33078220ab9092dd00d7d363b", "commit_訊息": "[流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知", "提交日期": "2022-05-11 11:10:44", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "24029ec331cdfe20c6a66dcd924c973b36eff9cf", "commit_訊息": "[Web]A00-20220519001 修正IE加簽會加成兩次的問題", "提交日期": "2022-05-25 18:07:49", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cc1f3cc7be2ab43eb34b58d10f89b5a400c0d4c9", "commit_訊息": "[流程引擎]Q00-20220415001 修正因多餘附件移除邏輯改成流程結案處理，導至流程無法結案", "提交日期": "2022-04-15 10:15:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecc978bdca5f4dfca43849cfed0785dfa308d449", "commit_訊息": "[Web]Q00-20220511005 修正T100拋轉單據中有舊值的單身內容沒有顯示為紅色", "提交日期": "2022-05-11 15:03:45", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0c6121878ee205a68bf6d58b1ba4b395a416fbf9", "commit_訊息": "[流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料", "提交日期": "2022-05-11 11:23:52", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6a6d210e0838902a1f9e1f9cd460f5f8c54dd8e4", "commit_訊息": "[Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢", "提交日期": "2022-05-05 11:00:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9481dceeddd0fed2f64871d18bcc8d9770ba25b0", "commit_訊息": "[Web]Q00-20220414002 修正一般使用者追蹤流程清單匯出Excel報表，簽核時間欄位沒有值", "提交日期": "2022-04-15 13:37:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "237ce9ae70588a3deb01d999eeff60d82b8598a4", "commit_訊息": "[Web]Q00-20220509002 修正授權的流程使用closeTime(結案時間)排序會報錯的問題", "提交日期": "2022-05-09 16:41:13", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "421c0d4c64881e8c8349a3d6c337a6148b4efb64", "commit_訊息": "[WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務[補修正] 內網判斷192.168改成只判斷192", "提交日期": "2022-04-25 16:16:24", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "807a9bb3b34a0030ea20f8ac0de5a45b50875020", "commit_訊息": "[WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務", "提交日期": "2022-04-21 15:28:09", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f19392259e312788da541246ed5830aeced5b97f", "commit_訊息": "[Web]V00-20220414001 修正流程有標示智能示警時，在行動版的追蹤(監控)、BPM首頁顯示會出現html文字", "提交日期": "2022-04-15 15:13:56", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f5e3c75a9e8240b4045d00f95535b981823673c0", "commit_訊息": "[Web]Q00-20220329002 調整退回重辦流程清單以結案時間為主排序發起時間為副排序", "提交日期": "2022-03-29 16:48:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84cac48e5f1ae8346177d29855b503e2dda2203a", "commit_訊息": "[流程引擎]Q00-20220329001 修正模擬使用者進到BPM首頁再從下方進入待辦並簽核後，按回到清單頁會卡住", "提交日期": "2022-03-29 14:42:41", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8bf38b6adf5ac7d901eb512d7402a13efa363e1c", "commit_訊息": "[Web]Q00-20220328001 修正從首頁的待辦清單點選第二頁以後的任一流程，點擊繼續派送會報錯", "提交日期": "2022-03-28 10:00:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7fe8a107a842750403039384fb7c5b4c3064deae", "commit_訊息": "[系統管理工具]Q00-20220325003 修正使用者密碼帶有^符號，從一個tool中再開啟其他tool會出現錯誤", "提交日期": "2022-03-25 17:45:39", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5b230cd95955d0367084d7abb3ce73bf288224fe", "commit_訊息": "[Web]A00-20220325001 修正URL開啟追蹤TraceProcessForSearchForm畫面上的列印按鈕title無法正常顯示多語系", "提交日期": "2022-03-25 16:56:17", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c61e163b4d90516e3a17ea2857435f8015d8823f", "commit_訊息": "[流程引擎]Q00-20220325002 修正一般使用者-追蹤流程-已轉派的工作-匯出excel時，當資料庫為Oracle且匯出流程數量大於300時會報錯", "提交日期": "2022-03-25 16:30:15", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5791f8d1fe14fd45539450960cffe31f0d07d1f9", "commit_訊息": "[Web]Q00-20220325001 修正表單單身欄位對應錯誤", "提交日期": "2022-03-25 14:21:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5b1842731dda066240d09cfbf679daf62e859159", "commit_訊息": "[流程引擎]A00-20220323001 修正流程核決層級關卡之後的關卡若有設定自動簽核，流程無法往下派送的問題", "提交日期": "2022-03-24 18:28:08", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f2c9ddc34b096aa5121f96393e7eacd2a5520773", "commit_訊息": "[Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出", "提交日期": "2022-03-24 17:25:19", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9c8926633dbe271b315a59c41bb09e05c420a4fc", "commit_訊息": "[表單設計師]Q00-20220324002 修正使用IE開啟有時間元件的RWD表單會出現錯誤", "提交日期": "2022-03-24 17:04:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7cc379fa84c7e38c72a4002ceabe108e4aa6ffa9", "commit_訊息": "[流程引擎]A00-20220322002 修正SQL註冊器中語法有使用到order by 會導致報錯", "提交日期": "2022-03-23 16:59:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd55d2e65944063a187287add732030d7de3c1cc", "commit_訊息": "[Web]A00-20220322001 修正流程負責人透過監控流程匯出excel時，當流程狀態為已完成、已撤銷、已中止時，「目前處理者」欄位仍有資料", "提交日期": "2022-03-23 16:46:48", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a444e96aa21bc67a85c74945dd9adac7f3652a81", "commit_訊息": "[Web]Q00-20220322002 修正行動版面下，從追蹤進入絕對表單的流程，沒有橫向scrollbar而無法看到完整表單", "提交日期": "2022-03-22 17:00:14", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3a6b03bf822cc47732f3ca2751a3c84a81ce7fc", "commit_訊息": "[ESS]Q00-20220321003修正ESS流程經過取回or退回重辦在服務任務關卡前，撈取表單實例序號錯誤導致繼續派送報錯", "提交日期": "2022-03-21 16:51:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a3229ceaf9ab5adb3a39e7707a3319da2113887", "commit_訊息": "[Web]Q00-20220321002 修正一般使用者-追蹤流程-已轉派的工作，使用「表單序號」當條件查詢無效", "提交日期": "2022-03-21 16:38:17", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a23364dc8bea7d515cc321b352718664d78479cc", "commit_訊息": "[Web]A00-20220317001 修正透過URL(TraceProcessForSearchForm)開啟追蹤流程頁面，且系統變數設置「追蹤頁面簽核歷程為top」時，簽核歷程無法展開顯示內容", "提交日期": "2022-03-21 16:28:59", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "04b43324ee1a6a82f953b8232f64ade86349e499", "commit_訊息": "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]", "提交日期": "2022-03-22 15:15:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be03f919f95bd0f37b5e54356f8300dfa5baa7e0", "commit_訊息": "[Web]Q00-20220321001 修正絕對位置表單在第一關以外的關卡上傳附件按上傳後，開窗變空白", "提交日期": "2022-03-21 10:51:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e55dc0a3cf93af163c2351aa4bf703cf216ada1c", "commit_訊息": "[Web]Q00-20220317004 修正在IE開啟加簽頁面，確認按鈕無法點選", "提交日期": "2022-03-18 15:26:09", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd34eb6ab805bb8adaa954516460a70faa546350", "commit_訊息": "[流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤[補]", "提交日期": "2022-03-17 16:16:39", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9524e1c5773a84f8a13f00cd06ee00e1c2a8def6", "commit_訊息": "[Web]S00-20220316001 新增整合暫存管理供未註冊的BPM使用", "提交日期": "2022-03-16 11:19:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74316c9ea74343e28cdf0306f9eefadebda1ae1c", "commit_訊息": "[WEB]Q00-20220315004 修正離職作業維護-轉派離職人員的工作-勾選「全部轉派第一優先代理人」時，未將預設代理人帶回「接收者」欄位中", "提交日期": "2022-03-15 17:34:33", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c964e052029186aef6f988d0e6cfa9fabf539e1b", "commit_訊息": "[表單設計師]Q00-20220315003 修正表單設計師DialogInput有設提示文字，版更到5872會導致提示文字出現在預設值", "提交日期": "2022-03-15 16:19:59", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "46b7c1c24621c834bf43b9753ebbe83458ccdf73", "commit_訊息": "[組織同步]A00-20220314001 修正組織同步完會蓋掉使用者設定的 使用者是否顯示待辦事項小視窗", "提交日期": "2022-03-15 14:52:49", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f82fd05bf809ab35a97578c4062fc44b36d76366", "commit_訊息": "[WEB]A00-20220216001 修正追蹤流程-已轉派的工作，點進表單後再返回清單頁沒有保留原本的查詢條件", "提交日期": "2022-03-15 14:10:49", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "454e97da579771c4a9c99cc66293b5e0d8baab94", "commit_訊息": "[WEB]Q00-20220315002 修正舊版表單InputElement若沒有textValue屬性時，儲存表單會發生錯誤", "提交日期": "2022-03-15 14:01:56", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TextElementDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ffd8e45e8b239838e209dd4d62fc1b89ccbf4193", "commit_訊息": "[Web]Q00-20220223001表單無法派送，把元件applUserId刪除重拉就可以正常派送", "提交日期": "2022-03-15 13:39:07", "作者": "ocean_yeh", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d19f81cfd6825c7c6d9c2fcf5299994bd21c5707", "commit_訊息": "[流程引擎]Q00-20220314003 修正流程關卡下一關卡為多人關卡處理且設定自動簽核為\"與前一關相同簽核者，則跳過\"，繼續派送會失敗", "提交日期": "2022-03-14 17:46:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "27efc41e015d53a50dedb2c5c1db9070d363c898", "commit_訊息": "[BPM APP]C01-20220311002 修正用平板登IMG時session會清空導致發單簽核有空指針異常問題", "提交日期": "2022-03-11 18:02:08", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8cbc71f61b04a81291edef87e8ba0fee3178c7c", "commit_訊息": "[流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤", "提交日期": "2022-03-11 16:49:37", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "6f6b7775c49cf02fbdcad029f3935b9687e7b123", "commit_訊息": "Revert \"[流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤\"", "提交日期": "2022-03-17 10:46:02", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "3c236b20de4f06a3d61c18e995ecdd04f22b2d36", "commit_訊息": "[流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤", "提交日期": "2022-03-11 16:49:37", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "9d412384c2f921f665c5e06a47ce883841a6d0a4", "commit_訊息": "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]", "提交日期": "2022-03-10 15:45:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3b219fc563851dc4f135d6ab26b2cea54c5f4cb2", "commit_訊息": "[BPM APP]C01-20220309001 修正Web端行動模擬發單功能於表單載入時不會呼叫到formCreate方法問題", "提交日期": "2022-03-10 10:28:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "48efc636198405bde61a11ec46a7344130169990", "commit_訊息": "[ISO]Q00-20220309001 修正ISO變更單，在ModDocRequester關卡載入附件時，若表單有選擇元件(RadioBox,ComboBox,CheckBox,ListBox)並將元件設定為invisible時，無法載入附件", "提交日期": "2022-03-09 14:50:42", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b6404097b6eb708f86d46d5543fd48512ef8001a", "commit_訊息": "[流程引擎]A00-20220308001 修正一般使用者進入監控流程並點選『全部』，再到其他頁面後再回到監控會變一片空白", "提交日期": "2022-03-08 15:44:47", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "540e82931297e36642899ead7cf03d69643800c2", "commit_訊息": "[Web]Q00-20220307001 修正子單身過多造成表單開啟時間變長", "提交日期": "2022-03-07 18:24:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/SubGridTransfer.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "30f9a3a67df836bd0b6fb7f649c46ce8b571ee6f", "commit_訊息": "[登入]Q00-20220304002 調整登入加密機制，避免後端session失效時取不到值登入失敗", "提交日期": "2022-03-04 18:24:11", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "fe0fab5d4559398053689b3fe6d74dbf5d3207b5", "commit_訊息": "[Web]Q00-20220304001 修正查詢樣板使用6個$當作『請選擇』的內存值，在渲染後會被換成12個$", "提交日期": "2022-03-04 17:59:14", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fea62543f6b23ffd1b0b598a56212f6c74b2702", "commit_訊息": "[流程引擎]Q00-20220215001 修正偶發附件遺失問題[補修正]", "提交日期": "2022-03-03 16:11:02", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f00c62c65f5ae1fb37b429ffda39626d7af403fd", "commit_訊息": "[組織同步]A00-20220224002 修正組織同步完會蓋掉使用者設定的 簽核完畢後的行為", "提交日期": "2022-03-02 17:41:15", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aab31096c484e986fb1ab59f07e02ecd92ca468e", "commit_訊息": "[Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢[補修正]", "提交日期": "2022-03-02 09:25:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5db791ba5e7654397aa62b9b7762ef76afbe5b96", "commit_訊息": "[BPM APP]C01-20220301002 修正在沒有整合行動方案情況下開啟Web表單設計師任一表單時會發生錯誤的問題", "提交日期": "2022-03-01 18:45:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0804fb1fded16a85ed45dcf0c77f732dffe16ade", "commit_訊息": "[流程引擎]A00-20220224001 修正條件式中各條件先做or後再做and，會無法派送到該條件後的關卡", "提交日期": "2022-03-01 17:16:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/ConditionEvaluator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "06235e6fd6da6093b0e74ee3fcdea406294a7473", "commit_訊息": "[Web]Q00-20220301001 修正SQLCommand沒有設定SQL卻佔用連線的問題", "提交日期": "2022-03-01 16:23:22", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "98b2dcf1e69689da547c478e3aa6951b937e8532", "commit_訊息": "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]", "提交日期": "2022-03-01 15:16:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5b07223f949e544c263f957113b3224eabadb683", "commit_訊息": "[流程引擎]Q00-20220208004 修正「已轉派的工作」清單，在「全部」頁籤取得的資料筆數與「處理中」、「已處理」兩個頁籤相加的數量不符", "提交日期": "2022-02-25 16:45:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e2534e91c8bddf8e925594304fbb38f9af4097b8", "commit_訊息": "[Web]S00-20211117003 流程資料的頁面排序調整以發起時間大到小排序(DESC)", "提交日期": "2022-02-24 14:47:46", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDataSearchListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e049b273784f3687218835123309d3ff3782d25e", "commit_訊息": "[Web]Q00-20220224001 修正維護樣板作業從PC版切換到行動版時資料無法顯示", "提交日期": "2022-02-24 16:16:23", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ff436c7a887d0fa81ae0fce4dd99c74c2b0c801a", "commit_訊息": "[Web]Q00-20220221002 修正離職交接人作業維護離職人員開窗增加離職人員可以選擇", "提交日期": "2022-02-21 14:02:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cdbebd5cbd18a27f87032c301ea36419afd73649", "commit_訊息": "[流程引擎]Q00-20220221001 修正流程派送到離職人員，離職人員沒有設置離職交接人作業維護就會派送失敗", "提交日期": "2022-02-21 11:46:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a68565c60891a97409eff57cae0692daf0c3fc71", "commit_訊息": "[Web]A00-20220216002 修正RWD表單當右下角有出現滑到頂部按鈕時，按鈕也會被列印出來", "提交日期": "2022-02-18 17:28:24", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b39bd3e0529979d06d4b33fa000fa68cacc475a4", "commit_訊息": "[Web]Q00-20220218002 修正維護樣板查詢欄位DropDown使用『請選擇』時，渲染到畫面上時該選項的順序沒出現在第一個", "提交日期": "2022-02-18 11:40:13", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f2a6b80e26e13f9ddbdf5d26ea7bab142440c4ea", "commit_訊息": "[Web]A00-20220214001 修正附件權限設定關卡在追蹤流程看不到的問題", "提交日期": "2022-02-17 22:58:33", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ee9977e9bbedadecc5db20cd4d0623a21d16d4c", "commit_訊息": "[Web]Q00-20220217004 TextBox有設定小數位時，使用FormUtil的寫法無法在formOpen時更換背景色", "提交日期": "2022-02-17 15:45:28", "作者": "ocean_yeh", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "324072e8d0c5d56f048535242678f5536eacbabe", "commit_訊息": "[Web]Q00-20220217002 修正流程負責人在監控流程匯出Excel，處理者欄位內容應與系統管理員的處理者欄位內容一致", "提交日期": "2022-02-17 14:19:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "df1dc75774ddf3f2d028e2823a895f43036f0e0d", "commit_訊息": "[Web]Q00-20220217001 修正使用32位元的Chrome進入BPM登入頁面會彈出BPM僅支援的瀏覽器資訊的警告框", "提交日期": "2022-02-17 14:10:05", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ba211b8812e80902915402670c53896abd67793", "commit_訊息": "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]", "提交日期": "2022-02-16 18:22:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "73f7db725f6ba2b7b8c38e991c144cc55706f2c2", "commit_訊息": "[Web]Q00-20220215002 調整讓行動版與PC版一致讓Grid只支援(a、br、input、i、button)五種html標籤", "提交日期": "2022-02-15 17:36:01", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "637f7b76598923808d9a320464e01a32715812ca", "commit_訊息": "[流程引擎]Q00-20220215001 修正偶發附件遺失問題", "提交日期": "2022-02-15 16:11:04", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e3d063e694653416aed5a896fb187b1f10731b70", "commit_訊息": "[流程引擎]Q00-20220211001 調整簡易流程圖預先解析，當前核決層級關卡的工作處理者為原處理者的代理人時，以原處理者解析後續關卡", "提交日期": "2022-02-11 18:14:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a61747359bef230ff9b3985cb301acc15d2c09f", "commit_訊息": "[ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態", "提交日期": "2022-02-10 17:51:10", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd673e6fb68eee55acc598973a856a0a9235142a", "commit_訊息": "[MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]", "提交日期": "2022-02-10 14:00:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "072ce073160f8f19f839b8f80aebc9c9ee6b3b53", "commit_訊息": "[流程引擎]Q00-20220209001 調整追蹤流程接口邏輯，當追蹤流程url中未包含表單定義ID時，開放給流程處理者與系統管理員查看流程", "提交日期": "2022-02-09 18:06:33", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "904e596f9266672be2dd4f530854b9915e27c8de", "commit_訊息": "[流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過", "提交日期": "2022-02-08 18:03:20", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2497a5046786c3ea38a6a77f90aedb9bf9a2de73", "commit_訊息": "[Web]Q00-20220208002 單身加總欄位設定「顯示至小數點後第X位」且在其他欄位的運算規則中，單身加總數值改變後沒有觸發欄位運算", "提交日期": "2022-02-08 17:06:17", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b695c87c9446129ab1746555dddc4eb9e37036ee", "commit_訊息": "[流程引擎]A00-20220127001修正流程退回重辦選擇\"按照流程定義依序重新執行\"，關卡會經過\"服務任務\"會導致主旨的退回重辦標籤沒有顯示", "提交日期": "2022-02-08 16:08:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b1ae83ee44e7034f97a7a12562a020f639c69336", "commit_訊息": "[流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程", "提交日期": "2022-02-08 14:49:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f23377040bfdd5295acd9db62fda9ed7a21e8a5c", "commit_訊息": "[Web]A00-20220121001修正從工作通知從郵件進入，點擊\"回到工作清單\"按紐會應該要回到工作通知清單而不是待辦清單", "提交日期": "2022-01-26 10:41:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89722ef85211ad4d272643154ec37ddbf9a315d3", "commit_訊息": "[表單設計師]A00-20220120002 修正欄位樣板的三欄式和四欄式的最右邊欄位切分2欄後，再次編輯會變回切分1欄[補]", "提交日期": "2022-01-24 14:20:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4e38bef200f73ce952b7b7972034417cc25d8c8d", "commit_訊息": "[內部]Q00-20211115001新增GuardService連線成功的提示訊息", "提交日期": "2022-01-22 16:18:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f28ea8d1334e09e38cd693d4573c4c54986a376a", "commit_訊息": "[MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題", "提交日期": "2022-01-22 11:40:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65fbfe2e1a8418edac0dfddf9fc83016d6bf505f", "commit_訊息": "[表單設計師]A00-20220120002 修正欄位樣板的三欄式和四欄式的最右邊欄位切分2欄後，再次編輯會變回切分1欄", "提交日期": "2022-01-21 17:58:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4295f67a41be2a190a8d9024c8ca003cec955dd0", "commit_訊息": "[Web]A00-20220120001修正IE開起流程用SQLcommand因為用replaceall函式導致報錯", "提交日期": "2022-01-21 11:39:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "28368d635a37a71e1c8e9e44e3347c7ae5063745", "commit_訊息": "[Web]Q00-20220120003 流程代理人設定的選擇流程開窗，預設用流程代號做排序", "提交日期": "2022-01-20 18:23:35", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "739eacca38a5fcf00c9a2cea2a700857cf69fcd4", "commit_訊息": "[流程引擎]Q00-20220120001 修正「使用者有多個部門，在選擇發起部門後若發起流程失敗，回到表單頁面後無法再發起流程或儲存表單」問題", "提交日期": "2022-01-20 16:51:55", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a466310d9097beb50eaffb2e9182dd21d47ba6b1", "commit_訊息": "[流程引擎]A00-20211221001 修正「第一次發起流程因使用者填寫的表單資料有誤導致發起失敗，在使用者更正表單後仍無法發起流程」的問題", "提交日期": "2022-01-20 16:50:48", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}