# Release Notes - BPM

## 版本資訊
- **新版本**: 5.7.3.2_1
- **舊版本**: 5.7.3.1_1
- **生成時間**: 2025-07-28 18:02:54
- **新增 Commit 數量**: 184

## 變更摘要

### walter_wu (23 commits)

- **2018-10-12 11:20:07**: 修正 Q00-20180913003 的修改錯誤
  - 變更檔案: 1 個
- **2018-10-11 11:26:01**: 修正5732 DML Oracle 日期錯誤
  - 變更檔案: 1 個
- **2018-10-09 16:08:41**: ListReader SQL 更改 錯誤修正
  - 變更檔案: 1 個
- **2018-10-09 15:18:44**: 修正資料選取器JsonDataChooser搜尋邏輯錯誤
  - 變更檔案: 1 個
- **2018-10-08 18:26:58**: Q00-20180913003 修正上次這張議題有code誤刪
  - 變更檔案: 1 個
- **2018-10-08 10:18:44**: Q00-20181005001 修正主旨特殊符號問題
  - 變更檔案: 3 個
- **2018-10-04 19:00:11**: C01-20180925009 修正Chrome無法從子視窗使用父視窗confirm()問題(批次轉派修正)
  - 變更檔案: 1 個
- **2018-10-04 17:57:03**: C01-20180925007 補 .trim()
  - 變更檔案: 1 個
- **2018-10-04 17:54:50**: C01-20180925007 修正Chrome無法從子視窗使用父視窗confirm()問題  順便處理空白通過
  - 變更檔案: 2 個
- **2018-10-04 14:34:43**: Q00-20181002004 處理容器set更改轉碼造成App呈現的主旨未反轉碼
  - 變更檔案: 8 個
- **2018-09-28 17:22:47**: Q00-20180905003 修正關注流程定義維護-->新增-->重要性名稱的開窗  使用IE瀏覽器時 點擊開窗裡的資料沒有反應
  - 變更檔案: 1 個
- **2018-09-28 15:46:08**: C01-20180412001 從追蹤流程無法點開資料選取器
  - 變更檔案: 2 個
- **2018-09-27 11:54:23**: S00-20180927001 處理傳送給前端網頁容器與網頁的特殊字元
  - 變更檔案: 9 個
- **2018-09-20 15:22:16**: Q00-20180824001 修正清單內容如果有特殊字元無法顯示的問題
  - 變更檔案: 1 個
- **2018-09-19 15:45:31**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-09-19 15:39:57**: C01-20180621002 避免Web表單設計師，偶爾會出現滑鼠黏著元件的狀況發生。
  - 變更檔案: 1 個
- **2018-09-17 17:57:52**: A00-20180314001 修正列印表單上面的值與表單上實際的數值不同
  - 變更檔案: 1 個
- **2018-09-14 16:15:35**: A00-20180529003 授權流程選擇的流程分類"無"流程實例時，會請洽系統管理員
  - 變更檔案: 1 個
- **2018-09-14 13:44:12**: Q00-20180913003 修正資料註冊器設定的開窗，設定搜尋條件之後有異常
  - 變更檔案: 1 個
- **2018-09-13 19:04:09**: C01-20180723001 Q00-20180913001處理客制開窗、單選開窗、多選開窗特殊符號在Html轉換問題 修復多選開窗值有單引號會全白問題
  - 變更檔案: 4 個
- **2018-09-07 17:40:01**: C01-20180725001 修正在5.7版，當發起人有多部門，發起流程後，選項不會停在主部門上。
  - 變更檔案: 1 個
- **2018-09-06 17:51:34**: S00-20180614001 User追蹤流程-->匯出Excel Excel增加簽核時間欄位
  - 變更檔案: 1 個
- **2018-09-05 17:14:36**: A00-20180503001 修正系統權限管理員中可存取範圍無法查找到離職人員工號
  - 變更檔案: 1 個

### ChinRong (17 commits)

- **2018-10-11 19:07:41**: 調整行動版多選開窗將返回值帶回清單
  - 變更檔案: 3 個
- **2018-10-03 14:56:34**: 將行動設計器元件群組化功能先鎖起來
  - 變更檔案: 3 個
- **2018-10-02 17:23:02**: 調整行動版FormUtil的開窗方法
  - 變更檔案: 1 個
- **2018-10-02 17:21:57**: 調整待辦清單RESTful服務
  - 變更檔案: 20 個
- **2018-09-21 14:29:18**: 修正行動版表單設計器在有SQL command元件與DB Connection元件時會壞掉的問題
  - 變更檔案: 2 個
- **2018-09-21 10:06:03**: 新增取得草稿清單RESTful服務
  - 變更檔案: 6 個
- **2018-09-20 14:32:54**: 復原鼎捷行事曆被覆蓋掉的欄位
  - 變更檔案: 1 個
- **2018-09-19 16:58:52**: 調整行動版表單設計器快搜功能
  - 變更檔案: 3 個
- **2018-09-19 15:32:58**: 新增行動表單設計器群組頁籤多語系
  - 變更檔案: 4 個
- **2018-09-19 14:30:59**: 企業微信通知列表新增通隻來源標籤
  - 變更檔案: 1 個
- **2018-09-19 14:30:37**: 調整行動表單設計器
  - 變更檔案: 3 個
- **2018-09-13 18:10:30**: 修正中間層簽核session過期造成派送失敗問題
  - 變更檔案: 1 個
- **2018-09-06 13:41:28**: 新增待辦清單RESTful服務的多語系
  - 變更檔案: 2 個
- **2018-09-06 09:48:55**: 調整鼎捷移動使用者資料表更新機制,避免頻繁的更新表容易出現資料無法寫入的問題
  - 變更檔案: 4 個
- **2018-09-04 10:20:47**: 修正開窗畫面與附件下載畫面拉到頂端或底端後無法滑動的問題
  - 變更檔案: 10 個
- **2018-09-04 10:20:05**: 移除多餘的log
  - 變更檔案: 1 個
- **2018-09-04 10:19:49**: 新增取得待辦清單RESTful服務
  - 變更檔案: 9 個

### waynechang (16 commits)

- **2018-10-11 15:43:26**: C01-20180813001 調整是否代理的判斷邏輯
  - 變更檔案: 1 個
- **2018-10-08 15:06:44**: C01-20180906002 簡易和完整流程圖顯示自動簽核狀態
  - 變更檔案: 4 個
- **2018-10-04 14:02:37**: C01-20180926001 調整員工工作轉派的處理者字眼
  - 變更檔案: 1 個
- **2018-10-03 17:21:12**: A00-20180717003 流程負責人在管理流程看不到授權的流程
  - 變更檔案: 1 個
- **2018-09-28 16:20:20**: C01-20180919005 修正取回重辦發生非預期錯誤導致關卡未被rollback
  - 變更檔案: 1 個
- **2018-09-28 13:50:37**: C01-20180927001 增加判斷當身分為admin或是模擬使用者時，不卡控權限
  - 變更檔案: 1 個
- **2018-09-28 09:38:11**: C01-20180927001 修正未經授權的url不允許登入
  - 變更檔案: 1 個
- **2018-09-27 16:42:47**: C01-20180927001 修正未經授權的url不允許登入
  - 變更檔案: 1 個
- **2018-09-26 15:10:20**: Q00-20180926001 EFGPShareMethod.js 單選開窗無法使用
  - 變更檔案: 1 個
- **2018-09-20 17:43:56**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-09-20 17:43:42**: C01-20180720001 增加表單卡控避免發生蓋單議題
  - 變更檔案: 4 個
- **2018-09-18 14:25:20**: 調整QRCode驗證模式
  - 變更檔案: 1 個
- **2018-09-17 18:13:34**: C01-20180906004 修正表單欄位消失議題
  - 變更檔案: 1 個
- **2018-09-14 17:01:32**: A00-20180913001
  - 變更檔案: 1 個
- **2018-09-11 17:28:42**: Q00-20180911003 調整QRCode登入機制
  - 變更檔案: 3 個
- **2018-09-05 16:54:19**: Q00-*********** ISO舊表單須將產生編碼的規則替換為V57版本
  - 變更檔案: 12 個

### joseph (25 commits)

- **2018-10-11 11:38:51**: 新增:撤銷註記API的中台註冊服務
  - 變更檔案: 1 個
- **2018-10-11 10:49:25**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 1 個
- **2018-10-11 10:49:01**: 修正 :撤銷註記restful接口發信異常 ,url調整V2及log調整
  - 變更檔案: 3 個
- **2018-10-09 16:19:05**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-10-09 16:18:50**: <V57> Q00-20181009001 修正 :發起流程,查看簡易流程圖,不會顯示預先解析人員
  - 變更檔案: 1 個
- **2018-10-09 13:55:34**: S00-20180908001 預覽流程檢視：當解析到離職人員時，於姓名前加上 (X) 註記，可識別是離職人員
  - 變更檔案: 1 個
- **2018-10-09 13:46:06**: 修正 : 補上RestfulApplication 缺少的定義
  - 變更檔案: 1 個
- **2018-10-08 14:44:28**: <V57> 二次調整 C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度
  - 變更檔案: 1 個
- **2018-10-08 14:39:22**: <V57> A00-20180720001 修正 :從portal開啟待辦是空白畫面
  - 變更檔案: 1 個
- **2018-10-04 18:32:26**: 調整 Moblie DB存放路徑
  - 變更檔案: 21 個
- **2018-10-04 13:59:04**: 修正 :攻略雲指標SQL異常
  - 變更檔案: 4 個
- **2018-10-04 10:05:27**: <V57>二次修正 C01-20180528001 調整提示文字為 :僅支持T100及程的流程
  - 變更檔案: 6 個
- **2018-10-03 13:56:53**: <V57> C01-20180528001 調整當流程關卡進階勾選直接簽核網址時會提示不支援ESS流程
  - 變更檔案: 6 個
- **2018-10-03 13:51:40**: <V57> C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度
  - 變更檔案: 1 個
- **2018-10-03 13:49:54**: <V57> A00-20180124002 將T100發單和寫入發單紀錄的邏輯調整為獨立的交易
  - 變更檔案: 1 個
- **2018-10-03 10:38:32**: 移除 :攻略雲table設定
  - 變更檔案: 1 個
- **2018-10-03 10:22:31**: 調整 :攻略雲指標SQL移至5741
  - 變更檔案: 6 個
- **2018-10-02 14:14:17**: 新增  restful API : [取得最後簽核者]、[更新BPM撤銷註記]
  - 變更檔案: 17 個
- **2018-10-01 17:14:52**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-10-01 17:09:04**: 補上  ProcessMappingkey companyId欄位設定
  - 變更檔案: 1 個
- **2018-09-28 09:51:49**: 移除 sys.print
  - 變更檔案: 1 個
- **2018-09-27 13:41:30**: 調整 :整合產品開單紀錄開單資訊 及 新增記錄companyId
  - 變更檔案: 11 個
- **2018-09-27 10:48:19**: 調整 :攻略雲指標SQL欄位型態
  - 變更檔案: 4 個
- **2018-09-26 17:59:12**: <V57>新增 :ESSQ92、ESSQ93查詢作業
  - 變更檔案: 4 個
- **2018-09-26 17:00:46**: 新增 : 攻略雲指標功能
  - 變更檔案: 40 個

### pinchi_lin (13 commits)

- **2018-10-09 18:07:01**: 調整微服務用的bean與邏輯
  - 變更檔案: 3 個
- **2018-10-08 09:49:50**: 新增通知流程restful服務漏簽程式
  - 變更檔案: 1 個
- **2018-10-05 15:45:51**: 調整取得通知流程等restful服務邏輯
  - 變更檔案: 5 個
- **2018-10-05 11:23:40**: 新增取通知流程restful的條件並將所有set方法中參數加入p開頭
  - 變更檔案: 1 個
- **2018-10-04 17:01:08**: 新增取得通知流程等restful服務
  - 變更檔案: 8 個
- **2018-10-03 16:55:42**: 將企業微信的推播追蹤流程連結從連到列表改至連到表單
  - 變更檔案: 2 個
- **2018-10-03 11:03:37**: 調整設定IMG列表使用中間層或詳情簽核的多語系
  - 變更檔案: 5 個
- **2018-10-02 18:16:03**: IMG待辦、追蹤、通知列表依流程設定顯示中間層或詳情功能(註解遺漏地方)
  - 變更檔案: 4 個
- **2018-09-20 17:47:36**: IMG列表依流程設定使用中間層或詳情作簽核功能(DTO層)
  - 變更檔案: 2 個
- **2018-09-20 17:41:32**: 簽核流設計師加入設定IMG列表使用中間層或詳情簽核的欄位
  - 變更檔案: 8 個
- **2018-09-20 17:19:41**: IMG消息推播依照流程設定顯示中間層或詳情功能
  - 變更檔案: 1 個
- **2018-09-20 17:00:18**: IMG行事曆依流程設定顯示中間層或詳情功能
  - 變更檔案: 6 個
- **2018-09-20 16:36:45**: Q00-20180920001 修正IMG行事曆使用中間層顯示其簽核歷程會顯示異常問題
  - 變更檔案: 1 個

### Gaspard (9 commits)

- **2018-10-09 16:36:42**: 將5741的SQL移到5732發版
  - 變更檔案: 4 個
- **2018-10-08 18:28:57**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-10-08 18:28:29**: 修正手寫元件開窗後直接點確定會清空畫面的異常+加入手寫元件的腳本樣版
  - 變更檔案: 3 個
- **2018-09-28 09:21:55**: 修正完成取回重辦後的清單頁無法顯示之異常
  - 變更檔案: 1 個
- **2018-09-06 15:30:18**: 修正RWD表單有Image元建時，預使用腳本樣本產生所有全域變數無反應的異常
  - 變更檔案: 1 個
- **2018-09-05 11:44:59**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-09-05 11:44:35**: 修正開啟RWD表單後，SqlCommand與HiddenTextBox無法刪除的異常
  - 變更檔案: 1 個
- **2018-09-04 11:06:28**: 修正腳本樣版中，程式碼過長時自動產生卷軸可滾動
  - 變更檔案: 1 個
- **2018-08-30 15:27:55**: 優化手寫元件填寫方式，改成開窗的方式填寫
  - 變更檔案: 13 個

### yamiyeh10 (29 commits)

- **2018-10-09 16:09:23**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-10-09 16:08:58**: C01-20181009001 修正安卓手機螢幕過大版面跑版議題
  - 變更檔案: 1 個
- **2018-10-09 11:23:55**: 修正手寫元件附件上傳與在Grid內時顯示異常 調整手寫元件唯讀時顯示樣式
  - 變更檔案: 6 個
- **2018-10-08 18:58:39**: 補上App手寫元件漏掉地方
  - 變更檔案: 1 個
- **2018-10-08 18:51:51**: 調整App手寫元件改為彈窗顯示方式
  - 變更檔案: 14 個
- **2018-10-05 18:53:15**: 修正上傳附件之後，客製開窗選完人員後不會將值更新
  - 變更檔案: 1 個
- **2018-10-05 17:50:21**: 修正議題 1.手寫簽名元件如果在流程設計師關卡中設定隱藏的話會造成表單打不開 2.首次點擊Grid新增按鈕後手寫簽名元件會多長一個出來
  - 變更檔案: 2 個
- **2018-10-05 14:17:50**: 修正議題: 1.企業微信安卓手機待辦、通知筆數跑版 2.企業微信安卓手機列表篩選區塊沒有置中 3.企業微信安卓手機工作首頁下方導覽列跑版,最下方被切掉
  - 變更檔案: 3 個
- **2018-10-03 13:59:13**: 修正沒有註冊BPM App序號只開設定檔入口平台設定頁籤仍會出現
  - 變更檔案: 1 個
- **2018-10-02 15:19:33**: 新增TextArea繼續閱讀樣式
  - 變更檔案: 13 個
- **2018-10-02 15:04:03**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-10-02 15:02:38**: 修正IMG待辦連續簽核後的下一筆流程詳情表單打不開
  - 變更檔案: 1 個
- **2018-10-02 13:26:31**: 調整退回重辦意見增加多行顯示效果
  - 變更檔案: 4 個
- **2018-10-01 18:25:50**: 調整待辦的顯示流程切換頁面箭頭隱藏
  - 變更檔案: 1 個
- **2018-10-01 18:25:09**: 修正微信待辦列表批次簽核功能彈窗無遮罩
  - 變更檔案: 2 個
- **2018-10-01 16:07:20**: 修正IMG加簽快搜功能異常
  - 變更檔案: 1 個
- **2018-10-01 16:03:03**: 修正產品開窗與加簽的快搜功能異常
  - 變更檔案: 2 個
- **2018-10-01 15:58:22**: 調整待辦清單RESTFul 1.調整Controller與Javabean 2.調整Conditions改用Javabean 3.加入中台使用名稱
  - 變更檔案: 18 個
- **2018-09-20 17:34:51**: 新增取可發起流程清單RESTFul服務 調整取得使用者資訊的Log資訊
  - 變更檔案: 6 個
- **2018-09-19 00:24:07**: 調整RESTFul服務 1.增加身分驗證 2.增加取得組織資訊 3.增加人員查詢資訊
  - 變更檔案: 26 個
- **2018-09-18 11:04:09**: 修正57版本鼎捷移動處理的流程數據模塊篩選功能無效
  - 變更檔案: 1 個
- **2018-09-11 17:18:55**: 修正行動版表單設計師文字過長時會跑版
  - 變更檔案: 3 個
- **2018-09-11 16:30:05**: 修正議題 1.修正ESS附件不顯示其相關資訊 2.修正除表單內容與附件資訊外其餘為白底 3.修正ESS表單唯讀使用disabled導致無法正常使用點擊展開功能
  - 變更檔案: 12 個
- **2018-09-07 18:33:55**: 調整待辦詳情服務
  - 變更檔案: 5 個
- **2018-09-06 13:50:55**: 調整畫面自動捲動功能為共用方法
  - 變更檔案: 3 個
- **2018-09-06 13:49:15**: 新增待辦詳情RESTFul服務 調整RESTFul的使用者代號參數
  - 變更檔案: 10 個
- **2018-09-04 17:05:11**: 調整Grid在點擊新增 修改 取消按鈕時會自動捲動到輸入區塊 修正一般表單的Grid一鍵展開會同時顯示兩個按鈕
  - 變更檔案: 2 個
- **2018-09-04 15:27:33**: 修正產品開窗快搜功能會搜出相同人員與不相關人員議題
  - 變更檔案: 1 個
- **2018-08-30 18:48:00**: 新增附件下載RESTFul
  - 變更檔案: 5 個

### 施廷緯 (11 commits)

- **2018-10-08 18:54:05**: 同C01-20171205003 修正資料擷取器開窗問題。
  - 變更檔案: 1 個
- **2018-10-05 16:27:44**: C01-20171205003 調整資料擷取器開窗至螢幕中間。
  - 變更檔案: 1 個
- **2018-10-05 16:10:50**: 同 A00-20180904001 修正Update SQL的 5.7.3.1_DML_Oracle_1指令
  - 變更檔案: 1 個
- **2018-10-03 17:15:46**: C01-20180516002 修正Web表單設計師在IE開窗後無法縮放問題
  - 變更檔案: 1 個
- **2018-09-20 18:26:19**: C01-20180820001 修正響應式表單Textbox進階設定為浮點數且Textbox為Disable時，沒有反灰 。
  - 變更檔案: 1 個
- **2018-09-19 10:58:26**: A00-20180904001 修正Update SQL的 5.7.3.1_DML_Oracle_1指令
  - 變更檔案: 1 個
- **2018-09-19 09:48:38**: 修正ESS表單追蹤流程點擊BPM列印按鈕後出現空白，因ESS表單本身已有列印按鈕，故將BPM列印按鈕隱藏。
  - 變更檔案: 1 個
- **2018-09-18 17:04:45**: A00-20180918001 修正附件開啟除了發起關卡外，都是空白問題。
  - 變更檔案: 1 個
- **2018-09-13 11:58:16**: S00-20180910003 資料選取註冊器的SQL語法新增備註說明。
  - 變更檔案: 1 個
- **2018-09-10 11:34:26**: A00-20180905001 修權限不一致問題，關卡流程表單頁面沒有設定權限，但是開啟Attachement按鈕卻可以看到附件的問題。
  - 變更檔案: 2 個
- **2018-08-30 14:56:54**: A00-20180829001 SQL註冊器的SQL語法卡控限制長度為兩千個字元。
  - 變更檔案: 3 個

### 顏伸儒 (18 commits)

- **2018-10-05 15:29:56**: A00-20180223001RWD 修正BPM57版,表單設計師表單為RWD,ID第一個字必須為字母做卡控。
  - 變更檔案: 1 個
- **2018-10-04 18:36:44**: A00-20180223001 修正BPM57版,表單設計師ID第一個字必須為字母做卡控。
  - 變更檔案: 4 個
- **2018-10-02 17:06:28**: A00-20180831001-1 修正BPM57版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。
  - 變更檔案: 2 個
- **2018-10-02 14:43:44**: C01-20180614002 修正BPM57版,簽核流程設計師流程定義過期顯示紅叉。
  - 變更檔案: 1 個
- **2018-09-28 16:53:48**: A00-20171003002 將log刪除。
  - 變更檔案: 1 個
- **2018-09-28 16:36:31**: A00-20180831001 修正BPM57版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。
  - 變更檔案: 2 個
- **2018-09-27 15:59:15**: A00-20180914002 修正BPM57版本,將流程設計師中呼叫網路服務設計師的Operations的背景改為白色。
  - 變更檔案: 1 個
- **2018-09-26 19:23:52**: A00-*********** 修正BPM57版本,表單更新Name後流程所掛的表單也會更新。
  - 變更檔案: 2 個
- **2018-09-25 16:39:23**: Q00-20180913002 修正BPM57版本,將PDF轉檔所用的jar檔改為空的jar。
  - 變更檔案: 2 個
- **2018-09-21 16:52:45**: A00-20180919001 修正BPM57版本,退回重辦後會帶回目前處理位置的下一筆資料。
  - 變更檔案: 3 個
- **2018-09-21 15:46:34**: Q00-20180921001 修正BPM57版本,點待辦清單時加入索引。
  - 變更檔案: 1 個
- **2018-09-18 17:50:22**: Q00-20180913002 修正BPM57版本,將PDF轉檔所需的jar檔移至NaNa的lib裡。
  - 變更檔案: 4 個
- **2018-09-18 15:51:08**: C01-2018073000 修正BPM57版,從mail進入時不顯示處理下個工作的按鈕。
  - 變更檔案: 1 個
- **2018-09-14 11:19:42**: C01-20180331002 修正BPM57版本,ISO製作文件索引失敗的問題。
  - 變更檔案: 1 個
- **2018-09-13 15:34:01**: Q00-20180913002 修正BPM57版本,更新PDF轉檔所使用的jar檔。
  - 變更檔案: 2 個
- **2018-09-12 20:12:15**: C01-20180907001 修正BPM57版本,信件通知表單有隱藏欄位時,不將隱藏欄位顯示在信上。
  - 變更檔案: 1 個
- **2018-09-06 15:52:59**: A00-20180824001 修正BPM57版本,TIPTOP端原稿夾看不到可撤銷流程。
  - 變更檔案: 1 個
- **2018-09-05 16:48:27**: A00-20180830002 修正BPM57版本,在TT開單時解析xml裡轉換字元的部分加入防呆。
  - 變更檔案: 1 個

### Catherine (2 commits)

- **2018-10-03 10:57:31**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-10-03 10:55:22**: C01-20180822003 M-cloud指令，預設語系調整為繁體中文
  - 變更檔案: 4 個

### jd (4 commits)

- **2018-10-02 10:33:13**: 修正合併後導致的錯誤,會導致build失敗
  - 變更檔案: 1 個
- **2018-10-01 14:31:28**: 修正Form.java Controller合併錯誤
  - 變更檔案: 1 個
- **2018-10-01 14:30:43**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 2 個
- **2018-10-01 14:24:28**: 調整附件下載RESTful服務,改為傳回資料下載位址。
  - 變更檔案: 3 個

### 治傑 (3 commits)

- **2018-09-21 15:17:13**: IMG待辦、追蹤、通知列表依流程設定顯示中間層或詳情功能
  - 變更檔案: 9 個
- **2018-09-11 14:04:25**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-09-11 14:04:07**: 調整鼎捷移動直連表單網址改為使用流程序號
  - 變更檔案: 2 個

### jerry1218 (13 commits)

- **2018-09-19 11:34:59**: 微調追蹤流程-重發新流程按鈕id
  - 變更檔案: 1 個
- **2018-09-11 14:03:44**: 修改updateSQL檔名(oracle 5732)
  - 變更檔案: 1 個
- **2018-09-06 16:59:19**: 新增客製RESTful範本
  - 變更檔案: 66 個
- **2018-09-06 16:55:51**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-09-06 15:31:17**: 調整ISO報表功能列button位置
  - 變更檔案: 5 個
- **2018-09-06 10:42:48**: 資料選取器功能及頁面優化 1.單選時不需body的scrollbar(Mobile除外),應動態調整grid高度 2.需使用BPM V57新scrollbar套件 3.function scrollToTop調整 4.修改多選mobile情境下,呈現[全資料]及[被選取資料]邏輯統合,統合至FloatButton,取消左上[返回全資料] 5.搜尋FloatButton取消(因不需要且與其餘頁面功能不一致) 6.頁面寬度不對,剛進頁面時為768,頁面載入完成變751(導致初始頁面為mobile) 7.該頁面判斷與產品不一致(產品判斷PC or Mobile是用寬度>768與否 , 該頁面為>=) 8.資料選取器設定頁預設寬度改為800(原預設768) 9.移除頁面多餘style屬性
  - 變更檔案: 2 個
- **2018-09-05 17:52:01**: 修正追蹤流程頁面-關鍵流程名稱顯示異常
  - 變更檔案: 1 個
- **2018-09-05 17:51:20**: Q00-20180905001 修正追蹤流程清單-目前關卡名稱特殊字元會導致顯示異常
  - 變更檔案: 2 個
- **2018-09-05 11:36:11**: 修正ISO設定檔commit錯誤
  - 變更檔案: 5 個
- **2018-08-30 10:56:42**: 修正服務任務-RESTful的錯誤及修改衝突
  - 變更檔案: 20 個
- **2018-08-30 09:59:19**: 修正非server.log檔案內容重複出現兩次的問題
  - 變更檔案: 2 個
- **2018-08-30 09:56:26**: 修正RestfulHelper程式命名錯誤
  - 變更檔案: 2 個
- **2018-08-29 10:16:12**: 新增服務元件模式-restful
  - 變更檔案: 93 個

### arielshih (1 commits)

- **2018-08-30 15:56:35**: Oracle 語法有錯誤，getDate() -> sysdate
  - 變更檔案: 2 個

## 詳細變更記錄

### 1. 修正 Q00-20180913003 的修改錯誤
- **Commit ID**: `5c927366159a19059de4c508934e48d19740bb8d`
- **作者**: walter_wu
- **日期**: 2018-10-12 11:20:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 2. 調整行動版多選開窗將返回值帶回清單
- **Commit ID**: `53e22e1a1ed6b7d8384670153ddb752fbdb5b867`
- **作者**: ChinRong
- **日期**: 2018-10-11 19:07:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 3. C01-20180813001 調整是否代理的判斷邏輯
- **Commit ID**: `cf14f3a200d615553c7077fd3d24579e59ceacfa`
- **作者**: waynechang
- **日期**: 2018-10-11 15:43:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SubstituteUtil.java`

### 4. 新增:撤銷註記API的中台註冊服務
- **Commit ID**: `c7696413547882bd4a95ce7e76c4af0d29de7cba`
- **作者**: joseph
- **日期**: 2018-10-11 11:38:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`

### 5. 修正5732 DML Oracle 日期錯誤
- **Commit ID**: `0b3135b8b51dd36674bf1164a7498f3098d89932`
- **作者**: walter_wu
- **日期**: 2018-10-11 11:26:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql`

### 6. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `c72c077a53477d4e9738d85cc9f8899d70bff018`
- **作者**: joseph
- **日期**: 2018-10-11 10:49:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 7. 修正 :撤銷註記restful接口發信異常 ,url調整V2及log調整
- **Commit ID**: `dde25efd2859ccf144450c08d35a7917bf6d019f`
- **作者**: joseph
- **日期**: 2018-10-11 10:49:01
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 8. 調整微服務用的bean與邏輯
- **Commit ID**: `c45ad736fd35df12d40b0db82da79bdc1da13cac`
- **作者**: pinchi_lin
- **日期**: 2018-10-09 18:07:01
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/NoticeTypeParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 9. 將5741的SQL移到5732發版
- **Commit ID**: `dd0129f4c5b06e1898e0058b55890e1f1cce4e75`
- **作者**: Gaspard
- **日期**: 2018-10-09 16:36:42
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql`

### 10. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `d70f348a0e73994878a145947a514f7c0eee74eb`
- **作者**: joseph
- **日期**: 2018-10-09 16:19:05
- **變更檔案數量**: 0

### 11. <V57> Q00-20181009001 修正 :發起流程,查看簡易流程圖,不會顯示預先解析人員
- **Commit ID**: `2354e84395fee709ae0a0f31bbfef10d21056409`
- **作者**: joseph
- **日期**: 2018-10-09 16:18:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 12. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `b8eb3bca2f7cafc5590634e0cf9b15b281d4a240`
- **作者**: yamiyeh10
- **日期**: 2018-10-09 16:09:23
- **變更檔案數量**: 0

### 13. C01-20181009001 修正安卓手機螢幕過大版面跑版議題
- **Commit ID**: `b7950e490311fd781e73905193585865a5479820`
- **作者**: yamiyeh10
- **日期**: 2018-10-09 16:08:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 14. ListReader SQL 更改 錯誤修正
- **Commit ID**: `4aef4391094c5014753c0220febbc004c709ae60`
- **作者**: walter_wu
- **日期**: 2018-10-09 16:08:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 15. 修正資料選取器JsonDataChooser搜尋邏輯錯誤
- **Commit ID**: `26743047b9f4ce632c743f72c9223d66de235bf1`
- **作者**: walter_wu
- **日期**: 2018-10-09 15:18:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`

### 16. S00-20180908001 預覽流程檢視：當解析到離職人員時，於姓名前加上 (X) 註記，可識別是離職人員
- **Commit ID**: `8bf6ec06e00c57561a23865193a542178ac24c7c`
- **作者**: joseph
- **日期**: 2018-10-09 13:55:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 17. 修正 : 補上RestfulApplication 缺少的定義
- **Commit ID**: `e4ce360b79f648f09648b0d5b641a751f5cb3cb2`
- **作者**: joseph
- **日期**: 2018-10-09 13:46:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 18. 修正手寫元件附件上傳與在Grid內時顯示異常 調整手寫元件唯讀時顯示樣式
- **Commit ID**: `c143cf2106faae89c325b2b2cbe3431054b18e17`
- **作者**: yamiyeh10
- **日期**: 2018-10-09 11:23:55
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 19. 補上App手寫元件漏掉地方
- **Commit ID**: `fd39bb2160e1f0ddf7f463066aea05eb756b0456`
- **作者**: yamiyeh10
- **日期**: 2018-10-08 18:58:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js`

### 20. 同C01-20171205003 修正資料擷取器開窗問題。
- **Commit ID**: `8672436c03c3fd8d192d03916122b63129b22682`
- **作者**: 施廷緯
- **日期**: 2018-10-08 18:54:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 21. 調整App手寫元件改為彈窗顯示方式
- **Commit ID**: `04aa5a521e356c3fbb9a787496a571b79d819f15`
- **作者**: yamiyeh10
- **日期**: 2018-10-08 18:51:51
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HandWritingElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 22. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `11b34fbaa539deebdc51bb2f59ebae8aa84b1fb4`
- **作者**: Gaspard
- **日期**: 2018-10-08 18:28:57
- **變更檔案數量**: 0

### 23. 修正手寫元件開窗後直接點確定會清空畫面的異常+加入手寫元件的腳本樣版
- **Commit ID**: `c8b69716bb19afed649d53f7804eb93481b01a05`
- **作者**: Gaspard
- **日期**: 2018-10-08 18:28:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql`

### 24. Q00-20180913003 修正上次這張議題有code誤刪
- **Commit ID**: `e69fcfe286b89062f92a0df01dd283c6f679aa10`
- **作者**: walter_wu
- **日期**: 2018-10-08 18:26:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 25. C01-20180906002 簡易和完整流程圖顯示自動簽核狀態
- **Commit ID**: `b73ca3c858347e2ef88b9d7d863214a91d3bac4e`
- **作者**: waynechang
- **日期**: 2018-10-08 15:06:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 26. <V57> 二次調整 C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度
- **Commit ID**: `493dd28dcabebae5c5dc0924b10c772f0e44da64`
- **作者**: joseph
- **日期**: 2018-10-08 14:44:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`

### 27. <V57> A00-20180720001 修正 :從portal開啟待辦是空白畫面
- **Commit ID**: `e4f4abb1e91a8eaecc40c9ff404a0e399b29ba33`
- **作者**: joseph
- **日期**: 2018-10-08 14:39:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 28. Q00-20181005001 修正主旨特殊符號問題
- **Commit ID**: `20cff162ff208754d1394b4ebc57ad66589fc739`
- **作者**: walter_wu
- **日期**: 2018-10-08 10:18:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 29. 新增通知流程restful服務漏簽程式
- **Commit ID**: `6c20983b070ee9a674fba96e4b2d325e274a243a`
- **作者**: pinchi_lin
- **日期**: 2018-10-08 09:49:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/RestfulUtil.java`

### 30. 修正上傳附件之後，客製開窗選完人員後不會將值更新
- **Commit ID**: `3f97b51d47323aaf653cefee842bee36fe760f1f`
- **作者**: yamiyeh10
- **日期**: 2018-10-05 18:53:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 31. 修正議題 1.手寫簽名元件如果在流程設計師關卡中設定隱藏的話會造成表單打不開 2.首次點擊Grid新增按鈕後手寫簽名元件會多長一個出來
- **Commit ID**: `28363ad711a71abb9cc930f9e221db941a23b35a`
- **作者**: yamiyeh10
- **日期**: 2018-10-05 17:50:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HandWritingElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 32. C01-20171205003 調整資料擷取器開窗至螢幕中間。
- **Commit ID**: `0b4c94cdad92aa916a136b02122446ea1678e8f3`
- **作者**: 施廷緯
- **日期**: 2018-10-05 16:27:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 33. 同 A00-20180904001 修正Update SQL的 5.7.3.1_DML_Oracle_1指令
- **Commit ID**: `f0304da115a4f8909e09d91dd6aa5213a1219a06`
- **作者**: 施廷緯
- **日期**: 2018-10-05 16:10:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_Oracle_1.sql`

### 34. 調整取得通知流程等restful服務邏輯
- **Commit ID**: `842dce7ee71ba1c2e27157321991cf3c2f8cdb55`
- **作者**: pinchi_lin
- **日期**: 2018-10-05 15:45:51
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/NoticeListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/NoticeTypeParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessNoticeListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTypeManager.java`

### 35. A00-20180223001RWD 修正BPM57版,表單設計師表單為RWD,ID第一個字必須為字母做卡控。
- **Commit ID**: `d57ef8ddc32fb8bfb0b3c47dedfeb8be06c1ac0b`
- **作者**: 顏伸儒
- **日期**: 2018-10-05 15:29:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 36. 修正議題: 1.企業微信安卓手機待辦、通知筆數跑版 2.企業微信安卓手機列表篩選區塊沒有置中 3.企業微信安卓手機工作首頁下方導覽列跑版,最下方被切掉
- **Commit ID**: `66b515b30b13207de950a010bfabd253415c3f8f`
- **作者**: yamiyeh10
- **日期**: 2018-10-05 14:17:50
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmAppWorkMenu.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`

### 37. 新增取通知流程restful的條件並將所有set方法中參數加入p開頭
- **Commit ID**: `75a388e3f617b2456c0a13b4a61def610c4d204e`
- **作者**: pinchi_lin
- **日期**: 2018-10-05 11:23:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java`

### 38. C01-20180925009 修正Chrome無法從子視窗使用父視窗confirm()問題(批次轉派修正)
- **Commit ID**: `3e17317ca7079f4bbf4f195e43f6a42ec75fde00`
- **作者**: walter_wu
- **日期**: 2018-10-04 19:00:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 39. A00-20180223001 修正BPM57版,表單設計師ID第一個字必須為字母做卡控。
- **Commit ID**: `eec4273890dc8b91a0dea124b705a1a61561bcd9`
- **作者**: 顏伸儒
- **日期**: 2018-10-04 18:36:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5732.xls`

### 40. 調整 Moblie DB存放路徑
- **Commit ID**: `1dd633d46a8dd7e567c7f82565e38b398b27bbcb`
- **作者**: joseph
- **日期**: 2018-10-04 18:32:26
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/create/InitMobileDB_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/create/InitMobileDB_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.1.1_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.1.1_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.2.1_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.3.1_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.3.1_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.4.1_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.5.1_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.5.1_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.5.2_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.5.3_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.6.5.3_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.7.0.1_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.7.0.1_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.7.2.1_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.7.2.1_updateSQL_SQLServer.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.7.3.1_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@mobile/db/update/5.7.3.1_updateSQL_SQLServer.sql`

### 41. C01-20180925007 補 .trim()
- **Commit ID**: `876d1c55fbb63f67b39566f5ed61524ac522cbe5`
- **作者**: walter_wu
- **日期**: 2018-10-04 17:57:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 42. C01-20180925007 修正Chrome無法從子視窗使用父視窗confirm()問題  順便處理空白通過
- **Commit ID**: `1f64158b2d807e84337541dd2373f4190b30ec85`
- **作者**: walter_wu
- **日期**: 2018-10-04 17:54:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 43. 新增取得通知流程等restful服務
- **Commit ID**: `39ba1b9a0b717bfe4a04f7764e3ffce836458bec`
- **作者**: pinchi_lin
- **日期**: 2018-10-04 17:01:08
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/NoticeCountParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/NoticeListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/NoticeTypeParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessNoticeCountParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessNoticeListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessNoticeTotalParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 44. Q00-20181002004 處理容器set更改轉碼造成App呈現的主旨未反轉碼
- **Commit ID**: `65664162d1eb60086157899159d74e25a9b0ef04`
- **作者**: walter_wu
- **日期**: 2018-10-04 14:34:43
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmProcessInstForTracingVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmWorkItemViewVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/StringUtil.java`

### 45. C01-20180926001 調整員工工作轉派的處理者字眼
- **Commit ID**: `6997296a1ffc0753136fb5a257e32c61eea30367`
- **作者**: waynechang
- **日期**: 2018-10-04 14:02:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 46. 修正 :攻略雲指標SQL異常
- **Commit ID**: `b5bcadc0c472cf506c8e23dfccd9b7b3f3d69d4b`
- **作者**: joseph
- **日期**: 2018-10-04 13:59:04
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_Oracle_1.sql`

### 47. <V57>二次修正 C01-20180528001 調整提示文字為 :僅支持T100及程的流程
- **Commit ID**: `c86d68efac6b73b413cbea28469794c3349885bd`
- **作者**: joseph
- **日期**: 2018-10-04 10:05:27
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_TW.properties`

### 48. A00-20180717003 流程負責人在管理流程看不到授權的流程
- **Commit ID**: `d1232c4acc141f452590b218fd948d4d70e9d247`
- **作者**: waynechang
- **日期**: 2018-10-03 17:21:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 49. C01-20180516002 修正Web表單設計師在IE開窗後無法縮放問題
- **Commit ID**: `980c0aad4a7289485367fca40f8dfb68cb139679`
- **作者**: 施廷緯
- **日期**: 2018-10-03 17:15:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`

### 50. 將企業微信的推播追蹤流程連結從連到列表改至連到表單
- **Commit ID**: `73db2322a92df02514e46b6be19bbdba003b65f8`
- **作者**: pinchi_lin
- **日期**: 2018-10-03 16:55:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 51. 將行動設計器元件群組化功能先鎖起來
- **Commit ID**: `b5834bd34718246df61f173ac5bab733aaa3dfb0`
- **作者**: ChinRong
- **日期**: 2018-10-03 14:56:34
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css`

### 52. 修正沒有註冊BPM App序號只開設定檔入口平台設定頁籤仍會出現
- **Commit ID**: `66d2a811a7fb3e610598af93b4ebd0fa40026a24`
- **作者**: yamiyeh10
- **日期**: 2018-10-03 13:59:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/PatternViewer.java`

### 53. <V57> C01-20180528001 調整當流程關卡進階勾選直接簽核網址時會提示不支援ESS流程
- **Commit ID**: `52a8509e7072b0582110db3ddd24ec30d91e374d`
- **作者**: joseph
- **日期**: 2018-10-03 13:56:53
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_TW.properties`

### 54. <V57> C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度
- **Commit ID**: `36601a4c3b19a54cf7edad5e719a0d37c3648e1c`
- **作者**: joseph
- **日期**: 2018-10-03 13:51:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`

### 55. <V57> A00-20180124002 將T100發單和寫入發單紀錄的邏輯調整為獨立的交易
- **Commit ID**: `958fd0af389db5b28f161d64a2b9bbbd3502d2c6`
- **作者**: joseph
- **日期**: 2018-10-03 13:49:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 56. 調整設定IMG列表使用中間層或詳情簽核的多語系
- **Commit ID**: `9d7eb2e898e2a3c4db59c0dfdbb986c1b17622d1`
- **作者**: pinchi_lin
- **日期**: 2018-10-03 11:03:37
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`

### 57. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `d498a1a15ebf7fca28bee5ddb72ad514ad17510d`
- **作者**: Catherine
- **日期**: 2018-10-03 10:57:31
- **變更檔案數量**: 0

### 58. C01-20180822003 M-cloud指令，預設語系調整為繁體中文
- **Commit ID**: `d2ec1f281c8089b811d71a3091e98c26b4b189da`
- **作者**: Catherine
- **日期**: 2018-10-03 10:55:22
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mcloud/create/InitMCloud_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mcloud/create/InitMCloud_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mcloud/update/5.5.4.1_updateMCloud_MSSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mcloud/update/5.5.4.1_updateMCloud_Oracle.sql`

### 59. 移除 :攻略雲table設定
- **Commit ID**: `3f2ff3defc2733a9a024745142fbac0d90f16f41`
- **作者**: joseph
- **日期**: 2018-10-03 10:38:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 60. 調整 :攻略雲指標SQL移至5741
- **Commit ID**: `42be4fbda26e6816a2371ceba02c6f058f8b23aa`
- **作者**: joseph
- **日期**: 2018-10-03 10:22:31
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_Oracle_1.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_MSSQL_1.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql`

### 61. IMG待辦、追蹤、通知列表依流程設定顯示中間層或詳情功能(註解遺漏地方)
- **Commit ID**: `27d0a29c342f730d7349ee0d9022dd66c892776c`
- **作者**: pinchi_lin
- **日期**: 2018-10-02 18:16:03
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`

### 62. 調整行動版FormUtil的開窗方法
- **Commit ID**: `1d5b3103660b8ad8cafd4970d47e38b9c2f77a73`
- **作者**: ChinRong
- **日期**: 2018-10-02 17:23:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 63. 調整待辦清單RESTful服務
- **Commit ID**: `f751d7fda50625dc72035db8e9048da192ae443f`
- **作者**: ChinRong
- **日期**: 2018-10-02 17:21:57
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PerformTypeParameterRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessAssignmentTypeRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessListResultRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessManualReassignTypeRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessPerformTypeRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessProformListParameterRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessWorkItemStateRes.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ResignmentParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTypeManager.java`

### 64. A00-20180831001-1 修正BPM57版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。
- **Commit ID**: `0c00c07a4122c37761a684a9bfac809dbe18e4c1`
- **作者**: 顏伸儒
- **日期**: 2018-10-02 17:06:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/RelevantDataHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/relevantdata/FormTypeEditorPanel.java`

### 65. 新增TextArea繼續閱讀樣式
- **Commit ID**: `3d2394958b28b3721c3f1426f2a52e8ad8548ca3`
- **作者**: yamiyeh10
- **日期**: 2018-10-02 15:19:33
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 66. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `1d959c177e2ca4957bb54c824175ad76cde8164c`
- **作者**: yamiyeh10
- **日期**: 2018-10-02 15:04:03
- **變更檔案數量**: 0

### 67. 修正IMG待辦連續簽核後的下一筆流程詳情表單打不開
- **Commit ID**: `3b531357dd0b793a62581c800990ce14665653ac`
- **作者**: yamiyeh10
- **日期**: 2018-10-02 15:02:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 68. C01-20180614002 修正BPM57版,簽核流程設計師流程定義過期顯示紅叉。
- **Commit ID**: `f1f302484050fa347c532ab57ce0ca61c0b1d774`
- **作者**: 顏伸儒
- **日期**: 2018-10-02 14:43:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeCellRenderer.java`

### 69. 新增  restful API : [取得最後簽核者]、[更新BPM撤銷註記]
- **Commit ID**: `f432e88ee017f9b35b4643326fce86da53353a96`
- **作者**: joseph
- **日期**: 2018-10-02 14:14:17
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessInstanceDTOFactoryDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/CriticalProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessCancelSymbolUpdateParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessCancelSymbolUpdateReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessCancelSymbolUpdateStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessFinalPerformerParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessFinalPerformerReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessFinalPerformerStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCancelSymbolUpdateRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCancelSymbolUpdateStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessFinalPerformerParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessFinalPerformerRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessFinalPerformerStdDataRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 70. 調整退回重辦意見增加多行顯示效果
- **Commit ID**: `356cbd24e078856e51b8e6b8050eda8aeb4ee25b`
- **作者**: yamiyeh10
- **日期**: 2018-10-02 13:26:31
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 71. 修正合併後導致的錯誤,會導致build失敗
- **Commit ID**: `d73b8b21503284c1df0af0ab6b9070a1a9da7c5f`
- **作者**: jd
- **日期**: 2018-10-02 10:33:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`

### 72. 調整待辦的顯示流程切換頁面箭頭隱藏
- **Commit ID**: `e6c7cdd10d692d7542ddcc6957a3b79e5d015674`
- **作者**: yamiyeh10
- **日期**: 2018-10-01 18:25:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`

### 73. 修正微信待辦列表批次簽核功能彈窗無遮罩
- **Commit ID**: `48a05c4407e492a363d349f04c54ee17d93ad4a4`
- **作者**: yamiyeh10
- **日期**: 2018-10-01 18:25:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmMobileLibrary.js`

### 74. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `4971469d2015594d075940138ecacd4f3c443ea3`
- **作者**: joseph
- **日期**: 2018-10-01 17:14:52
- **變更檔案數量**: 0

### 75. 補上  ProcessMappingkey companyId欄位設定
- **Commit ID**: `fb417e31b54dbf413e0f813f248fcc13dec577ea`
- **作者**: joseph
- **日期**: 2018-10-01 17:09:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 76. 修正IMG加簽快搜功能異常
- **Commit ID**: `1c0b5b9447205c66186d0e77f3e4ddb6187352cc`
- **作者**: yamiyeh10
- **日期**: 2018-10-01 16:07:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 77. 修正產品開窗與加簽的快搜功能異常
- **Commit ID**: `5e5a95e3a4ad5daa569586162f47b911fa357307`
- **作者**: yamiyeh10
- **日期**: 2018-10-01 16:03:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 78. 調整待辦清單RESTFul 1.調整Controller與Javabean 2.調整Conditions改用Javabean 3.加入中台使用名稱
- **Commit ID**: `64966f4795792eebb948246513f449e96cea791a`
- **作者**: yamiyeh10
- **日期**: 2018-10-01 15:58:22
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AssignmentParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CriticalListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ManualResignmentParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PerformTypeParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessLevelParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessWorkCountParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessWorkListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessWorkTotalParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ResignmentParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/StateParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkCountParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 79. 修正Form.java Controller合併錯誤
- **Commit ID**: `20bf4958d3a349f76a6a99004ae184b62f25074b`
- **作者**: jd
- **日期**: 2018-10-01 14:31:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`

### 80. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `922a771c4726d3da8a6362a87e98ed0c92b3b3de`
- **作者**: jd
- **日期**: 2018-10-01 14:30:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`

### 81. 調整附件下載RESTful服務,改為傳回資料下載位址。
- **Commit ID**: `c4a0cffb9a7d089c3d99804e5e7fcfafdb432766`
- **作者**: jd
- **日期**: 2018-10-01 14:24:28
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 82. Q00-20180905003 修正關注流程定義維護-->新增-->重要性名稱的開窗  使用IE瀏覽器時 點擊開窗裡的資料沒有反應
- **Commit ID**: `86bae05b9d422cbe78fac695189752d4a537c61c`
- **作者**: walter_wu
- **日期**: 2018-09-28 17:22:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`

### 83. A00-20171003002 將log刪除。
- **Commit ID**: `0b53b2e6c96c85db4fc5d956149db297f2ff81ab`
- **作者**: 顏伸儒
- **日期**: 2018-09-28 16:53:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorCER.java`

### 84. A00-20180831001 修正BPM57版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。
- **Commit ID**: `ee29b63f2e69abc22889a2517cb0fb481a8a58df`
- **作者**: 顏伸儒
- **日期**: 2018-09-28 16:36:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorCER.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorPanel.java`

### 85. C01-20180919005 修正取回重辦發生非預期錯誤導致關卡未被rollback
- **Commit ID**: `a59ffc72effa13e56f623cf9dd980ebb52fcd214`
- **作者**: waynechang
- **日期**: 2018-09-28 16:20:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 86. C01-20180412001 從追蹤流程無法點開資料選取器
- **Commit ID**: `a55411610fa07fc3316d732c07c68ae35e124e52`
- **作者**: walter_wu
- **日期**: 2018-09-28 15:46:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`

### 87. C01-20180927001 增加判斷當身分為admin或是模擬使用者時，不卡控權限
- **Commit ID**: `56dba3904f994c10321eb2f76c474364b2cad66c`
- **作者**: waynechang
- **日期**: 2018-09-28 13:50:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 88. 移除 sys.print
- **Commit ID**: `36a301bb6bc5d72b185fd94b365569758f94bab4`
- **作者**: joseph
- **日期**: 2018-09-28 09:51:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`

### 89. C01-20180927001 修正未經授權的url不允許登入
- **Commit ID**: `768d7609c855d1adaf847da023906b965085952f`
- **作者**: waynechang
- **日期**: 2018-09-28 09:38:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 90. 修正完成取回重辦後的清單頁無法顯示之異常
- **Commit ID**: `d27b65cdfba270f774bbf0703325631e10b66cff`
- **作者**: Gaspard
- **日期**: 2018-09-28 09:21:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp`

### 91. C01-20180927001 修正未經授權的url不允許登入
- **Commit ID**: `e713aa2358cdfafad859075feedc1ba269c3f53c`
- **作者**: waynechang
- **日期**: 2018-09-27 16:42:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 92. A00-20180914002 修正BPM57版本,將流程設計師中呼叫網路服務設計師的Operations的背景改為白色。
- **Commit ID**: `eb9421e3a6e2277e747089231d73a0d47de6d893`
- **作者**: 顏伸儒
- **日期**: 2018-09-27 15:59:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/WSInvocationEditorPanel.java`

### 93. 調整 :整合產品開單紀錄開單資訊 及 新增記錄companyId
- **Commit ID**: `2598e2f1235e517908de9555c2920d94f7326046`
- **作者**: joseph
- **日期**: 2018-09-27 13:41:30
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/ProcessMappingKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/IPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/OJBPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeProcessPrimaryKeyReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 94. S00-20180927001 處理傳送給前端網頁容器與網頁的特殊字元
- **Commit ID**: `fe9eb6506266ad6bb0d6a0de44e914fd35f2bab3`
- **作者**: walter_wu
- **日期**: 2018-09-27 11:54:23
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WfNotificationForManaging.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemViewer.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/StringUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/StringUtil.js`

### 95. 調整 :攻略雲指標SQL欄位型態
- **Commit ID**: `d9a8a278f6876408e3cd7233f0036943ff516cc4`
- **作者**: joseph
- **日期**: 2018-09-27 10:48:19
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_Oracle_1.sql`

### 96. A00-*********** 修正BPM57版本,表單更新Name後流程所掛的表單也會更新。
- **Commit ID**: `aaf669cc12c61e5a46c2cfac4ad516f935fff9ae`
- **作者**: 顏伸儒
- **日期**: 2018-09-26 19:23:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/RelevantDataHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/FormDefinitionManagerClientDelegate.java`

### 97. <V57>新增 :ESSQ92、ESSQ93查詢作業
- **Commit ID**: `8a3e7985c25e5c58cbe373861242dc1878c1b4a6`
- **作者**: joseph
- **日期**: 2018-09-26 17:59:12
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.3.2_AppForm_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.3.2_AppForm_DML_Oracle_1.sql`

### 98. 新增 : 攻略雲指標功能
- **Commit ID**: `60bf697e36ca2a5a38cfad899f520505e2b61d3d`
- **作者**: joseph
- **日期**: 2018-09-26 17:00:46
- **變更檔案數量**: 40
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/IWCDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/CalculationIntervalType2IntFieldConversion.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/CalculationStandardType2IntFieldConversion.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/IndicatorCalculationType2IntFieldConversion.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/iwc/IndicatorDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/iwc/enumTypes/CalculationIntervalType.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/iwc/enumTypes/CalculationStandardType.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/iwc/enumTypes/IndicatorCalculationType.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/IndicatorDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IIndicatorDefinitionDAO.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBIndicatorDefinitionDAO.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/IndicatorListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/iwc/IWCManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/iwc/IWCManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/iwc/IWCManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/iwc/IWCMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/iwc/IndicatorCalculationMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeProcessParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/IWC.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IWCMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/IWCIndicatorDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5732.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql`

### 99. Q00-20180926001 EFGPShareMethod.js 單選開窗無法使用
- **Commit ID**: `d8b3f1631d8bb9f749e15ba27ec5cb65608eaeb6`
- **作者**: waynechang
- **日期**: 2018-09-26 15:10:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`

### 100. Q00-20180913002 修正BPM57版本,將PDF轉檔所用的jar檔改為空的jar。
- **Commit ID**: `965476c209260c713e9d2866a545b01b691ce685`
- **作者**: 顏伸儒
- **日期**: 2018-09-25 16:39:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/easypdf-jacob.jar`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/jacob.jar`

### 101. A00-20180919001 修正BPM57版本,退回重辦後會帶回目前處理位置的下一筆資料。
- **Commit ID**: `522c65b8a70edffdcc25f38888f0fbf313bad645`
- **作者**: 顏伸儒
- **日期**: 2018-09-21 16:52:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 102. Q00-20180921001 修正BPM57版本,點待辦清單時加入索引。
- **Commit ID**: `fae5d564aa257e0ccb9a2a5da4fd106823fc69ed`
- **作者**: 顏伸儒
- **日期**: 2018-09-21 15:46:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 103. IMG待辦、追蹤、通知列表依流程設定顯示中間層或詳情功能
- **Commit ID**: `f45b570c74996c93ae3df2a8e8439f15e29190fb`
- **作者**: 治傑
- **日期**: 2018-09-21 15:17:13
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java`

### 104. 修正行動版表單設計器在有SQL command元件與DB Connection元件時會壞掉的問題
- **Commit ID**: `0fcb9c0f6522565175a1dca8d2507e766be6f7fb`
- **作者**: ChinRong
- **日期**: 2018-09-21 14:29:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 105. 新增取得草稿清單RESTful服務
- **Commit ID**: `b65847eb8faba96ad2b787179bb6f66cd3caab66`
- **作者**: ChinRong
- **日期**: 2018-09-21 10:06:03
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessDraftListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessDraftListResultRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 106. C01-20180820001 修正響應式表單Textbox進階設定為浮點數且Textbox為Disable時，沒有反灰 。
- **Commit ID**: `fdb3d1816314a26d8c5647a34d433303c9ec4bb9`
- **作者**: 施廷緯
- **日期**: 2018-09-20 18:26:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 107. IMG列表依流程設定使用中間層或詳情作簽核功能(DTO層)
- **Commit ID**: `e0c8836333ce3b971fc32a8661163b67266c38fb`
- **作者**: pinchi_lin
- **日期**: 2018-09-20 17:47:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessInstanceForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForListDTO.java`

### 108. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `c0831e0aa05fb88d62527b9cce3f86ec04a90c4f`
- **作者**: waynechang
- **日期**: 2018-09-20 17:43:56
- **變更檔案數量**: 0

### 109. C01-20180720001 增加表單卡控避免發生蓋單議題
- **Commit ID**: `7f9e4781a91749237e4ecaf0077d50319e03cc54`
- **作者**: waynechang
- **日期**: 2018-09-20 17:43:42
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`

### 110. 簽核流設計師加入設定IMG列表使用中間層或詳情簽核的欄位
- **Commit ID**: `80f4928c3abaf77a93ebef25385eb8d16e810d0a`
- **作者**: pinchi_lin
- **日期**: 2018-09-20 17:41:32
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/MobilitySignOffCellEditorRenderer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`

### 111. 新增取可發起流程清單RESTFul服務 調整取得使用者資訊的Log資訊
- **Commit ID**: `86313aee54f3189d55a47c516c096c7ecef3cdf5`
- **作者**: yamiyeh10
- **日期**: 2018-09-20 17:34:51
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInvokeListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessPackageListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 112. IMG消息推播依照流程設定顯示中間層或詳情功能
- **Commit ID**: `6d22c1173774154041f64962dea88cebb068ed27`
- **作者**: pinchi_lin
- **日期**: 2018-09-20 17:19:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 113. IMG行事曆依流程設定顯示中間層或詳情功能
- **Commit ID**: `38e0322b804c25674598282a551babf11428b73d`
- **作者**: pinchi_lin
- **日期**: 2018-09-20 17:00:18
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/ScheduleBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`

### 114. Q00-20180920001 修正IMG行事曆使用中間層顯示其簽核歷程會顯示異常問題
- **Commit ID**: `c1f6a6075ced35c3e5002f6ebaf35815a52583de`
- **作者**: pinchi_lin
- **日期**: 2018-09-20 16:36:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 115. Q00-20180824001 修正清單內容如果有特殊字元無法顯示的問題
- **Commit ID**: `9f4a91afc81c57a4265c447758db8eadd127032e`
- **作者**: walter_wu
- **日期**: 2018-09-20 15:22:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WfNotificationForManaging.java`

### 116. 復原鼎捷行事曆被覆蓋掉的欄位
- **Commit ID**: `cf6ff12cf2a920253eb403dda750c5aa03455963`
- **作者**: ChinRong
- **日期**: 2018-09-20 14:32:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 117. 調整行動版表單設計器快搜功能
- **Commit ID**: `e5b41e33a920199b7205419898de2f47c03abd42`
- **作者**: ChinRong
- **日期**: 2018-09-19 16:58:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css`

### 118. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `3c3432677936a2e3da4efa73c915ae0cfc209103`
- **作者**: walter_wu
- **日期**: 2018-09-19 15:45:31
- **變更檔案數量**: 0

### 119. C01-20180621002 避免Web表單設計師，偶爾會出現滑鼠黏著元件的狀況發生。
- **Commit ID**: `7465542cb35b42c643a6fe6561248739fb68f08a`
- **作者**: walter_wu
- **日期**: 2018-09-19 15:39:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`

### 120. 新增行動表單設計器群組頁籤多語系
- **Commit ID**: `24f5cf8af602d4d2a65ad213cd57375ee64723fc`
- **作者**: ChinRong
- **日期**: 2018-09-19 15:32:58
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5732.xls`

### 121. 企業微信通知列表新增通隻來源標籤
- **Commit ID**: `1ea7aa3a6da71416d8e96908179f692ee05cc48d`
- **作者**: ChinRong
- **日期**: 2018-09-19 14:30:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp`

### 122. 調整行動表單設計器
- **Commit ID**: `bc03c01782a1289b5e785f6566be7b46ca0b5254`
- **作者**: ChinRong
- **日期**: 2018-09-19 14:30:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css`

### 123. 微調追蹤流程-重發新流程按鈕id
- **Commit ID**: `4aa24f37838063f2de1b7464f5ddfa5a1d8a7ab6`
- **作者**: jerry1218
- **日期**: 2018-09-19 11:34:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 124. A00-20180904001 修正Update SQL的 5.7.3.1_DML_Oracle_1指令
- **Commit ID**: `f5196353694e7ce34ae75b8d18c9d6f79b9c116f`
- **作者**: 施廷緯
- **日期**: 2018-09-19 10:58:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_Oracle_1.sql`

### 125. 修正ESS表單追蹤流程點擊BPM列印按鈕後出現空白，因ESS表單本身已有列印按鈕，故將BPM列印按鈕隱藏。
- **Commit ID**: `15fb25b371c3320d8140857d7dbf8e2e654e30bc`
- **作者**: 施廷緯
- **日期**: 2018-09-19 09:48:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 126. 調整RESTFul服務 1.增加身分驗證 2.增加取得組織資訊 3.增加人員查詢資訊
- **Commit ID**: `9bd9947295e076a2e73fb119602fe7babb399dbc`
- **作者**: yamiyeh10
- **日期**: 2018-09-19 00:24:07
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DepartmentListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GroupListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/MailingFrequencyTypeParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OrganizationDataParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OrganizationListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessSubstituteListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProjectListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserInfoDataParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserInfoParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserNameParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserPreferListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserRelationshipListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserSubstituteListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5732.xls`

### 127. Q00-20180913002 修正BPM57版本,將PDF轉檔所需的jar檔移至NaNa的lib裡。
- **Commit ID**: `882de04d9251eb5e49711aeac0bfd33dc8437ffd`
- **作者**: 顏伸儒
- **日期**: 2018-09-18 17:50:22
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/easypdf-jacob.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/jacob.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/module.xml`

### 128. A00-20180918001 修正附件開啟除了發起關卡外，都是空白問題。
- **Commit ID**: `d6d7332e9a2d63eb2fc523618619068e5b92e580`
- **作者**: 施廷緯
- **日期**: 2018-09-18 17:04:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AttachmentUtil.java`

### 129. C01-2018073000 修正BPM57版,從mail進入時不顯示處理下個工作的按鈕。
- **Commit ID**: `ff84a2dfe83d31d290231322ff3293e9bb7fd52a`
- **作者**: 顏伸儒
- **日期**: 2018-09-18 15:51:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 130. 調整QRCode驗證模式
- **Commit ID**: `0a6e1d02769e77a59fae8de366b5b53ebf4799d1`
- **作者**: waynechang
- **日期**: 2018-09-18 14:25:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`

### 131. 修正57版本鼎捷移動處理的流程數據模塊篩選功能無效
- **Commit ID**: `4f3a19c6d5169a389ded18cefcb6da6a43b652f3`
- **作者**: yamiyeh10
- **日期**: 2018-09-18 11:04:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 132. C01-20180906004 修正表單欄位消失議題
- **Commit ID**: `b90bace45440d24825c5962b80f642dfb4b5de4d`
- **作者**: waynechang
- **日期**: 2018-09-17 18:13:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 133. A00-20180314001 修正列印表單上面的值與表單上實際的數值不同
- **Commit ID**: `3fe403c82efa3c94d1915a43184280f14f2fd6ab`
- **作者**: walter_wu
- **日期**: 2018-09-17 17:57:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 134. A00-20180913001
- **Commit ID**: `9a8e4d850b6958efee544892352df123ecdb6def`
- **作者**: waynechang
- **日期**: 2018-09-14 17:01:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 135. A00-20180529003 授權流程選擇的流程分類"無"流程實例時，會請洽系統管理員
- **Commit ID**: `eddeed9abc9090249a6dc2d3dece20ecde2c72e1`
- **作者**: walter_wu
- **日期**: 2018-09-14 16:15:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 136. Q00-20180913003 修正資料註冊器設定的開窗，設定搜尋條件之後有異常
- **Commit ID**: `5fac823deb5de67ec489617d6c1f848c00f3564d`
- **作者**: walter_wu
- **日期**: 2018-09-14 13:44:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 137. C01-20180331002 修正BPM57版本,ISO製作文件索引失敗的問題。
- **Commit ID**: `c7c40be66323a6df0ff447ef13d60fe056eac87a`
- **作者**: 顏伸儒
- **日期**: 2018-09-14 11:19:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`

### 138. C01-20180723001 Q00-20180913001處理客制開窗、單選開窗、多選開窗特殊符號在Html轉換問題 修復多選開窗值有單引號會全白問題
- **Commit ID**: `cf7db317c21ab37c0c8555733ad459ae9bcdf167`
- **作者**: walter_wu
- **日期**: 2018-09-13 19:04:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/ResultObjectForDataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`

### 139. 修正中間層簽核session過期造成派送失敗問題
- **Commit ID**: `8cc84521a8736d821069a5fff9c75b8bc34e231f`
- **作者**: ChinRong
- **日期**: 2018-09-13 18:10:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 140. Q00-20180913002 修正BPM57版本,更新PDF轉檔所使用的jar檔。
- **Commit ID**: `6f24748a90de055dd19274873f1b29a29ea443fa`
- **作者**: 顏伸儒
- **日期**: 2018-09-13 15:34:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/lib/PDF/easypdf-jacob.jar`
  - 📝 **修改**: `3.Implementation/subproject/system/lib/PDF/easypdf-jacob.jar`

### 141. S00-20180910003 資料選取註冊器的SQL語法新增備註說明。
- **Commit ID**: `54c2f034134775c8b620ececc1c268e795dbc36d`
- **作者**: 施廷緯
- **日期**: 2018-09-13 11:58:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp`

### 142. C01-20180907001 修正BPM57版本,信件通知表單有隱藏欄位時,不將隱藏欄位顯示在信上。
- **Commit ID**: `c2200ecac885fc7d5157c87d363d203b8fd177fb`
- **作者**: 顏伸儒
- **日期**: 2018-09-12 20:12:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 143. Q00-20180911003 調整QRCode登入機制
- **Commit ID**: `33c963746ccc9fb8315e3d89a288637d0f2b1722`
- **作者**: waynechang
- **日期**: 2018-09-11 17:28:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5732.xls`

### 144. 修正行動版表單設計師文字過長時會跑版
- **Commit ID**: `64aa569413b720de9f4ab22b06dbd575ae41f77b`
- **作者**: yamiyeh10
- **日期**: 2018-09-11 17:18:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css`

### 145. 修正議題 1.修正ESS附件不顯示其相關資訊 2.修正除表單內容與附件資訊外其餘為白底 3.修正ESS表單唯讀使用disabled導致無法正常使用點擊展開功能
- **Commit ID**: `53b9c39079171cfb2b4812c3044194607520abed`
- **作者**: yamiyeh10
- **日期**: 2018-09-11 16:30:05
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 146. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `909cb317a3ee8b033b6c5d4e23e5246cb19cc005`
- **作者**: 治傑
- **日期**: 2018-09-11 14:04:25
- **變更檔案數量**: 0

### 147. 調整鼎捷移動直連表單網址改為使用流程序號
- **Commit ID**: `7504757dcf5be7a1a1a8aea2d26ba4dca3bf501e`
- **作者**: 治傑
- **日期**: 2018-09-11 14:04:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 148. 修改updateSQL檔名(oracle 5732)
- **Commit ID**: `56df7821247b70b54e19e42884d0a0cd6034c1c8`
- **作者**: jerry1218
- **日期**: 2018-09-11 14:03:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_Oracle.sql`

### 149. A00-20180905001 修權限不一致問題，關卡流程表單頁面沒有設定權限，但是開啟Attachement按鈕卻可以看到附件的問題。
- **Commit ID**: `8236aab4eb8ba41317df2d947d3fc958d96d7530`
- **作者**: 施廷緯
- **日期**: 2018-09-10 11:34:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AttachmentUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`

### 150. 調整待辦詳情服務
- **Commit ID**: `ec17171d31e65ffa033d76b9edd802ca97d008c8`
- **作者**: yamiyeh10
- **日期**: 2018-09-07 18:33:55
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DepartmentUnitsParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessFormDetailParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessWorkitemParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserDepartmentListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 151. C01-20180725001 修正在5.7版，當發起人有多部門，發起流程後，選項不會停在主部門上。
- **Commit ID**: `499de082b89345ae33e37c7fbca69232034d1a30`
- **作者**: walter_wu
- **日期**: 2018-09-07 17:40:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`

### 152. S00-20180614001 User追蹤流程-->匯出Excel Excel增加簽核時間欄位
- **Commit ID**: `65672d00546e1bd64c1fa8694c49a4b47b9e9f29`
- **作者**: walter_wu
- **日期**: 2018-09-06 17:51:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 153. 新增客製RESTful範本
- **Commit ID**: `183cd149712e04ccb89521bc7834b629d3c9454c`
- **作者**: jerry1218
- **日期**: 2018-09-06 16:59:19
- **變更檔案數量**: 66
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/META-INF/MANIFEST.MF`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/jboss-all.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/jboss-deployment-structure.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/BPMUtil.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-beanutils.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-chain.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-codec.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-collections-3.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-collections.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-csv-1.2.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-dbcp.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-dbutils.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-digester.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-discovery-0.2.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-fileupload.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-httpclient.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-io.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-lang.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-logging-1.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-logging.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-pool.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/commons-validator.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/dom4j-1.6.1-changed_serialization.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/json.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/log4j.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/servlet-api.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-aop-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-aspects-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-beans-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-context-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-context-support-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-core-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-expression-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-instrument-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-instrument-tomcat-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-jdbc-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-jms-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-messaging-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-orm-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-oxm-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-test-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-tx-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-web-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-webmvc-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-webmvc-portlet-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/spring-websocket-4.3.9.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/xalan.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/lib/xercesImpl.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/spring-Config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/WebContent/WEB-INF/web.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/cust/rest/CustController.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/domain/AttachmentElementInstance.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/domain/FormInstance.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/BpmRestUtil.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/Dom4jUtil.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmCommonsLogFactoryProxy.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmLog.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmLog4jRepositorySelector.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmLogConfigurationException.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmLogConfigure.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmLogFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmLogFactoryImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmLogRepository.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/BpmLogger.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/configuration/BpmPropertiesTable.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomRestApi/src/com/digiwin/bpm/util/logging/configuration/BpmPropertiesType.java`

### 154. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `28a623f1493194a4cfdc7ddcf4d1be41c0725bde`
- **作者**: jerry1218
- **日期**: 2018-09-06 16:55:51
- **變更檔案數量**: 0

### 155. A00-20180824001 修正BPM57版本,TIPTOP端原稿夾看不到可撤銷流程。
- **Commit ID**: `511556faf6ccfb625061a4cddbe8f58f328e9521`
- **作者**: 顏伸儒
- **日期**: 2018-09-06 15:52:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 156. 調整ISO報表功能列button位置
- **Commit ID**: `b402adbe1158644c88954205b564bea11016e10e`
- **作者**: jerry1218
- **日期**: 2018-09-06 15:31:17
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`

### 157. 修正RWD表單有Image元建時，預使用腳本樣本產生所有全域變數無反應的異常
- **Commit ID**: `0f61c740995d432dfc21c8efbb2ee9880c1c8f2f`
- **作者**: Gaspard
- **日期**: 2018-09-06 15:30:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 158. 調整畫面自動捲動功能為共用方法
- **Commit ID**: `c93459f5c431131969e3b1cfc59b1fa13b575753`
- **作者**: yamiyeh10
- **日期**: 2018-09-06 13:50:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 159. 新增待辦詳情RESTFul服務 調整RESTFul的使用者代號參數
- **Commit ID**: `be046a1b7c181360f82def059b9088c40f79ebbd`
- **作者**: yamiyeh10
- **日期**: 2018-09-06 13:49:15
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AttachmentListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CriticalInfoParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DepartmentUnitsParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormDataParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessFormDetailParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessWorkitemParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 160. 新增待辦清單RESTful服務的多語系
- **Commit ID**: `195d0caf9cb1f3b4213d5b42ec5948a70eb4481b`
- **作者**: ChinRong
- **日期**: 2018-09-06 13:41:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5732.xls`

### 161. 資料選取器功能及頁面優化 1.單選時不需body的scrollbar(Mobile除外),應動態調整grid高度 2.需使用BPM V57新scrollbar套件 3.function scrollToTop調整 4.修改多選mobile情境下,呈現[全資料]及[被選取資料]邏輯統合,統合至FloatButton,取消左上[返回全資料] 5.搜尋FloatButton取消(因不需要且與其餘頁面功能不一致) 6.頁面寬度不對,剛進頁面時為768,頁面載入完成變751(導致初始頁面為mobile) 7.該頁面判斷與產品不一致(產品判斷PC or Mobile是用寬度>768與否 , 該頁面為>=) 8.資料選取器設定頁預設寬度改為800(原預設768) 9.移除頁面多餘style屬性
- **Commit ID**: `23e3b2480ad13ac4634d7211a78a611703ae2a44`
- **作者**: jerry1218
- **日期**: 2018-09-06 10:42:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 162. 調整鼎捷移動使用者資料表更新機制,避免頻繁的更新表容易出現資料無法寫入的問題
- **Commit ID**: `d42cf03b79710671884608089231b104991468b8`
- **作者**: ChinRong
- **日期**: 2018-09-06 09:48:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DinWhaleUserSingletonMap.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 163. 修正追蹤流程頁面-關鍵流程名稱顯示異常
- **Commit ID**: `d805345bba74ff47ace4cc4547d458937197f083`
- **作者**: jerry1218
- **日期**: 2018-09-05 17:52:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 164. Q00-20180905001 修正追蹤流程清單-目前關卡名稱特殊字元會導致顯示異常
- **Commit ID**: `0fe904e1e0264537fdb84beb2a28fe0739c75b7c`
- **作者**: jerry1218
- **日期**: 2018-09-05 17:51:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemViewer.java`

### 165. A00-20180503001 修正系統權限管理員中可存取範圍無法查找到離職人員工號
- **Commit ID**: `fe60ea76aa2d71ca2ebd0d0026fbec010727a88a`
- **作者**: walter_wu
- **日期**: 2018-09-05 17:14:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/sys-authority/src/com/dsc/nana/user_interface/apps/authority/view/ChooseTargetDialog.java`

### 166. Q00-*********** ISO舊表單須將產生編碼的規則替換為V57版本
- **Commit ID**: `45915f7ababaaf64a3f758385018d2ecccdc9948`
- **作者**: waynechang
- **日期**: 2018-09-05 16:54:19
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WorkflowEngineDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISODocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/snrule/ISOSnGenRule.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/snrule/AbsSnGenRule.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocDraftManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngine.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 167. A00-20180830002 修正BPM57版本,在TT開單時解析xml裡轉換字元的部分加入防呆。
- **Commit ID**: `8bbc25dc07d26e3745501ccf89291a3f840f69c2`
- **作者**: 顏伸儒
- **日期**: 2018-09-05 16:48:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 168. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `6d50c5bc25b6d2344768f7815460728557b48cf4`
- **作者**: Gaspard
- **日期**: 2018-09-05 11:44:59
- **變更檔案數量**: 0

### 169. 修正開啟RWD表單後，SqlCommand與HiddenTextBox無法刪除的異常
- **Commit ID**: `780d3b0c2d173d9369bee92c0b407c63e3048fff`
- **作者**: Gaspard
- **日期**: 2018-09-05 11:44:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 170. 修正ISO設定檔commit錯誤
- **Commit ID**: `0d88e36bff9cefdcac09685e41bf9b65020bc761`
- **作者**: jerry1218
- **日期**: 2018-09-05 11:36:11
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/data-source.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/session-factory.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/spring-bo.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/spring-dao.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/transaction-manager.xml`

### 171. 調整Grid在點擊新增 修改 取消按鈕時會自動捲動到輸入區塊 修正一般表單的Grid一鍵展開會同時顯示兩個按鈕
- **Commit ID**: `bce28f8c2380a569c1599f8843528bbd18696093`
- **作者**: yamiyeh10
- **日期**: 2018-09-04 17:05:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 172. 修正產品開窗快搜功能會搜出相同人員與不相關人員議題
- **Commit ID**: `315e76196342dcb1c5c7d0c850422123cca2a39c`
- **作者**: yamiyeh10
- **日期**: 2018-09-04 15:27:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 173. 修正腳本樣版中，程式碼過長時自動產生卷軸可滾動
- **Commit ID**: `b9cd6b9d04cd021aedbd7a00f176f3fc9127aba6`
- **作者**: Gaspard
- **日期**: 2018-09-04 11:06:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 174. 修正開窗畫面與附件下載畫面拉到頂端或底端後無法滑動的問題
- **Commit ID**: `6c2175c473175de0a1b71fd112d6660f6f9df38d`
- **作者**: ChinRong
- **日期**: 2018-09-04 10:20:47
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`

### 175. 移除多餘的log
- **Commit ID**: `394c09d856ef7d25266ee47f442812606f26a4a8`
- **作者**: ChinRong
- **日期**: 2018-09-04 10:20:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 176. 新增取得待辦清單RESTful服務
- **Commit ID**: `71ca7fae4cb567ac7f34d6f2a8446bb78b016f5c`
- **作者**: ChinRong
- **日期**: 2018-09-04 10:19:49
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessAssignmentTypeRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessListResultRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessManualReassignTypeRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessPerformTypeRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessProformListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessWorkItemStateRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 177. 新增附件下載RESTFul
- **Commit ID**: `59203db0549058bacf765706746ba38a371389bc`
- **作者**: yamiyeh10
- **日期**: 2018-08-30 18:48:00
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormAttachmentDownloadParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`

### 178. Oracle 語法有錯誤，getDate() -> sysdate
- **Commit ID**: `358b26a59d0e4260263bac23f248cfae1ea75b00`
- **作者**: arielshih
- **日期**: 2018-08-30 15:56:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_Oracle_1.sql`

### 179. 優化手寫元件填寫方式，改成開窗的方式填寫
- **Commit ID**: `74711e38c277423a5c27cf129831ad963a90b234`
- **作者**: Gaspard
- **日期**: 2018-08-30 15:27:55
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HandWritingElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/HandWritingTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 180. A00-20180829001 SQL註冊器的SQL語法卡控限制長度為兩千個字元。
- **Commit ID**: `01a8dda5fc6ffba204573a88432d0dd4f84870a4`
- **作者**: 施廷緯
- **日期**: 2018-08-30 14:56:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5732.xls`

### 181. 修正服務任務-RESTful的錯誤及修改衝突
- **Commit ID**: `87452a2e831e6260ec0f6b9935e7da766257568f`
- **作者**: jerry1218
- **日期**: 2018-08-30 10:56:42
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/ApplicationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/common/ApplicationTypeCellRenderer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandlerBean.java`

### 182. 修正非server.log檔案內容重複出現兩次的問題
- **Commit ID**: `dadcd935167f11217930ff0038a7fcc9e4f03dcd`
- **作者**: jerry1218
- **日期**: 2018-08-30 09:59:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaLog.properties`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWebLog.properties`

### 183. 修正RestfulHelper程式命名錯誤
- **Commit ID**: `a693b975aba81db5ff76520185121c564b2881b6`
- **作者**: jerry1218
- **日期**: 2018-08-30 09:56:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestFulHelper.java`

### 184. 新增服務元件模式-restful
- **Commit ID**: `3211bc2f10b55fb7594eff06db7a1ad830d48c9a`
- **作者**: jerry1218
- **日期**: 2018-08-29 10:16:12
- **變更檔案數量**: 93
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/ApplicationDefinitionHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/DomainObjectFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/ApplicationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/ApplicationsToolsEditorController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/ApplicationsToolsEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/FormalParametersEditorPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/InvokeRestfulToolDialogController.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/InvokeRestfulToolMCERTable.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/InvokeRestfulToolMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/common/ApplicationTypeCellRenderer.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController_en_US.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController_vi_VN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController_zh_CN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolDialogController_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolEditorPanel.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolEditorPanel_en_US.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolEditorPanel_vi_VN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolEditorPanel_zh_CN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolEditorPanel_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_en_US.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_vi_VN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_zh_CN.properties`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/app/RestfulApplication.java`
  - 📝 **修改**: `3.Implementation/subproject/persistence/.classpath`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Hibernate/antlr.jar`
  - ❌ **刪除**: `3.Implementation/subproject/persistence/lib/JakartaOJB/antlr.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Hibernate/hibernate3.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/JakartaOJB/antlr-2.7.6.jar`
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/JakartaOJB/antlr.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-aop-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-aspects-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-beans-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-context-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-context-support-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-core-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-expression-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-instrument-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-instrument-tomcat-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-jdbc-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-jms-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-orm-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-oxm-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-test-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-tx-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-web-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-webmvc-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-webmvc-portlet-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-websocket-4.3.7.RELEASE.jar`
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/Spring/spring.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/CustomSqlRowSetExtractor.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/access_control/hibernate/ISODocCmItemDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ConvertableISODocListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/DeployableDocListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/DocCategoryListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocDraftListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocRefListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/IndexableISODocListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/PaperRecordListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ReadingRecordListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/PerDataProManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/PerDataProManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/ApplicationToolAgent.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestAccessTokenSingletonMap.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/SessionBeanToolAgent.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestFulHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/BeanUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/XstreamUtil.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/data-source.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/session-factory.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/spring-bo.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/spring-dao.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/hibernate/main/transaction-manager.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_Oracle.sql`

