# Release Notes - BPM

## 版本資訊
- **新版本**: 5.7.7.2
- **舊版本**: 5.7.7.1
- **生成時間**: 2025-07-28 17:48:21
- **新增 Commit 數量**: 58

## 變更摘要

### walter_wu (12 commits)

- **2020-05-08 16:00:22**: [內部]補上T100文檔中心Url多語系
  - 變更檔案: 1 個
- **2020-05-08 15:53:34**: [BPMMPT]Q00-20200213002 將首頁方案包的URL連結，調整至SystemVariable[補]
  - 變更檔案: 1 個
- **2020-05-08 15:35:08**: [Portal]Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤[補]
  - 變更檔案: 1 個
- **2020-05-07 15:55:45**: Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤
  - 變更檔案: 3 個
- **2020-05-07 11:35:03**: [Web]C01-20200428002 修正流程統計類功能 Oracle日期查詢錯誤 統計結果多一天
  - 變更檔案: 1 個
- **2020-04-24 11:36:57**: [E10]修正5771 E10流程範本無法匯入
  - 變更檔案: 1 個
- **2020-04-22 08:46:40**: C01-20200331004 修正如果表單DIALOG對話窗型式元件在流程設定隱藏下一關資料會遺失
  - 變更檔案: 1 個
- **2020-04-15 19:45:23**: C01-20200410005 修正E10同步表單如果是修改狀態 再Grid裡新增欄位會導致欄位值錯位的問題
  - 變更檔案: 1 個
- **2020-04-14 17:34:12**: C01-20200407004 修正E10同步表單如果是修改需要比對的話，忽略SubTab
  - 變更檔案: 1 個
- **2020-04-10 15:10:15**: [WorkFlow]A00-20200323001 修正WF流程-資產請購資料建立作業(ASTI19)
  - 變更檔案: 1 個
- **2020-04-07 06:44:19**: [T100]Q00-20191225002 修正T100從文檔中心傳送附件到BPM，附件描述欄位會有異常
  - 變更檔案: 2 個
- **2020-03-26 19:23:26**: [E10]C01-20200324003 修正如果E10傳入的Grid內容有[] 會因為視為子單身用子單身的做法導致資料異常
  - 變更檔案: 1 個

### 林致帆 (15 commits)

- **2020-05-07 19:17:54**: [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料[補修正]
  - 變更檔案: 1 個
- **2020-05-06 17:51:34**: [Web]A00-*********** 移除維護作業"流程模組維護"[補修正]
  - 變更檔案: 21 個
- **2020-05-05 16:26:55**: [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料 [補修正]
  - 變更檔案: 1 個
- **2020-04-28 18:24:04**: [T100]A00-20200217001 修正T100單據aist310無法拋單到EFGP
  - 變更檔案: 1 個
- **2020-04-28 08:46:55**: [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料
  - 變更檔案: 1 個
- **2020-04-23 14:42:47**: 移除 [Web]Q00-20200415003 修正點擊待辦清單上方的查詢條件未清除
  - 變更檔案: 1 個
- **2020-04-23 14:41:10**: 移除 [Web]A00-20200324004 修正點擊追蹤流程，流程清單上方的查詢條件未清除
  - 變更檔案: 1 個
- **2020-04-15 19:29:47**: [Web]Q00-20200415003 修正點擊待辦清單上方的查詢條件未清除
  - 變更檔案: 1 個
- **2020-04-14 11:32:05**: [Web]A00-20200324004 修正點擊追蹤流程，流程清單上方的查詢條件未清除
  - 變更檔案: 1 個
- **2020-03-31 18:25:07**: Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57
- **2020-03-31 18:24:20**: [Web]A00-20200310001修正批次轉派時，點擊經常選取對象，確定鈕不能點擊
  - 變更檔案: 1 個
- **2020-03-18 20:08:38**: [Web]A00-20200311001 修正同時從Mail待辦連結登入及一般登入在簽核表單時儲存表單資料被覆蓋
  - 變更檔案: 1 個
- **2020-03-17 11:39:56**: [Web]C01-20200316002 修正流程開窗開啟異常 [補修正]
  - 變更檔案: 1 個
- **2020-03-16 19:52:36**: [Web]C01-20200316002 修正流程開窗開啟異常
  - 變更檔案: 1 個
- **2020-03-10 15:20:53**: [Web]A00-*********** 移除維護作業"流程模組維護"
  - 變更檔案: 5 個

### kaiteng (1 commits)

- **2020-05-07 10:22:15**: [表單[表單設計師]TextBox正則表達式刪除無作用的功能
  - 變更檔案: 1 個

### yanann_chen (7 commits)

- **2020-05-04 12:00:24**: [流程設計師]A00-20200410003 XPDL轉BPMN加入防呆
  - 變更檔案: 1 個
- **2020-04-30 10:51:06**: [Web]C01-20200327004 修正問題: 系統排程設定"每週"無效 新增排程時，設定"每週"無效[補]
  - 變更檔案: 1 個
- **2020-04-29 13:59:12**: Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57
- **2020-04-29 13:58:36**: [流程引擎]C01-20191223002 修正問題: 主管首頁逾時關卡統計計算錯誤 修正SQL command[補]
  - 變更檔案: 2 個
- **2020-04-09 18:03:55**: [流程引擎]A00-20200408002 修正問題: BamWorkItemData資料表未記錄核決層級關卡人員處理資訊
  - 變更檔案: 1 個
- **2020-03-30 13:40:06**: [Web]C01-20200327004 修正問題: 系統排程設定"每週"無效
  - 變更檔案: 1 個
- **2020-03-05 16:00:49**: [流程引擎]C01-20191223002 修正問題: 主管首頁逾時關卡統計計算錯誤
  - 變更檔案: 3 個

### 詩雅 (1 commits)

- **2020-04-30 17:21:20**: [BPM APP]Q00-20200430001調整微信所有流程名稱篩選功能，選項詞彙
  - 變更檔案: 4 個

### cherryliao (3 commits)

- **2020-04-30 11:16:51**: [內部]移除不必要的log
  - 變更檔案: 1 個
- **2020-04-30 10:55:32**: [BPM APP]Q00-20200430004 修正企業微信的通知表單畫面在查看或下載附件頁面沒有返回按鈕問題
  - 變更檔案: 1 個
- **2020-04-30 10:18:18**: [BPM APP]Q00-20200430003 修正絕對位置表單的Grid按鈕在Android手機上跑版問題
  - 變更檔案: 1 個

### yamiyeh10 (2 commits)

- **2020-04-30 10:36:17**: [BPM APP]Q00-20200430002 修正行動表單使用表單函式庫停用語法時元件非新樣式問題
  - 變更檔案: 2 個
- **2020-03-20 12:09:49**: [BPM APP]C01-20200319007 修正行動端的子單身資料不會根據對應單身資料做顯示
  - 變更檔案: 1 個

### 王鵬程 (12 commits)

- **2020-04-29 18:54:25**: [Web]C01-20200217003 修正進入模組程式維護頁面，程式的URL中有<和>符號時，點選該列後並儲存，再次進來顯示會異常
  - 變更檔案: 1 個
- **2020-04-28 15:00:40**: [Web]A00-20200423001 修正RWD的Grid排序問題
  - 變更檔案: 1 個
- **2020-04-24 18:26:23**: [Web]C01-20200131002 修正 檢視核決權限表型式的關卡頁面未放大最大時，在最下方關卡資訊有資料時卻無法顯示
  - 變更檔案: 1 個
- **2020-04-21 16:25:46**: [Web]A00-20200327001 修正當流程已存在常用流程中，該流程發起成功後的在頁面中還能再次新增到常用流程。[補修正]
  - 變更檔案: 1 個
- **2020-04-16 18:49:55**: [Web]A00-20200327001 修正當流程已存在常用流程中，該流程發起成功後的頁面中還能再次新增到常用流程。
  - 變更檔案: 2 個
- **2020-04-16 11:33:54**: [Web]Q00-20200415004 修正BPM左側選單是簡易顯示時，我的最愛的常用流程點擊沒反應
  - 變更檔案: 1 個
- **2020-04-09 11:47:42**: [Web]A00-20200319001 修正表單設計師頁面未正確顯示多語[補修正]
  - 變更檔案: 1 個
- **2020-04-08 18:49:47**: [Web]A00-20200319001 修正表單設計師頁面未正確顯示多語
  - 變更檔案: 2 個
- **2020-03-26 20:09:16**: [流程引擎]C01-20200220002 修正流程第一關ID為ACT1時，自動簽核異常
  - 變更檔案: 1 個
- **2020-03-25 19:26:53**: [Web]A00-20200318001 修正離職作業維護的查詢頁面讓日期開窗可以完整顯示
  - 變更檔案: 1 個
- **2020-03-23 18:50:54**: [Web]A00-20190711001 修正以英文登入BPM，在退回重辦頁面只有一筆可選時，點選關卡困難
  - 變更檔案: 1 個
- **2020-03-20 15:53:03**: [Web]A00-20200210001 修正當流程名稱跟模組下的程式名稱相同時，在流程頁面內上方title會多了模組名稱
  - 變更檔案: 1 個

### waynechang (4 commits)

- **2020-03-27 10:26:13**: [ISO]C00-20200323002修正舊版ISO評審排程會撈出已失效的document評審清單
  - 變更檔案: 1 個
- **2020-03-17 14:38:27**: [Web]Q00-20200317002 修正首頁模組嵌入BPM發單畫面的表單grid太長時scrollbar無作用
  - 變更檔案: 1 個
- **2020-03-17 14:20:54**: [Web]Q00-20200317001 修正大陸首頁模組嵌入BPM時，在win10 + IE11上，顯示有問題
  - 變更檔案: 1 個
- **2020-03-12 14:43:58**: [Web]Q00-20200312002 修正BPMTable.js的showColumn無作用
  - 變更檔案: 1 個

### pinchi_lin (1 commits)

- **2020-03-12 18:19:04**: [BPMAPP]C01-20200309002 修正表單欄位設置浮點數型態時且填字串會導致移動表單上誤判成表單錯誤問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]補上T100文檔中心Url多語系
- **Commit ID**: `52e6113e7aec20db7e7e392447ea8e9b86625b4f`
- **作者**: walter_wu
- **日期**: 2020-05-08 16:00:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 2. [BPMMPT]Q00-20200213002 將首頁方案包的URL連結，調整至SystemVariable[補]
- **Commit ID**: `8a469e9ebd29fe8b23abb64f58d2e8fc5a5757af`
- **作者**: walter_wu
- **日期**: 2020-05-08 15:53:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.1_DML_Oracle_1.sql`

### 3. [Portal]Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤[補]
- **Commit ID**: `2fc291742f8e3b687389bb2643e2e37ea70ffe24`
- **作者**: walter_wu
- **日期**: 2020-05-08 15:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PortalIntegrationEFGP.java`

### 4. [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料[補修正]
- **Commit ID**: `f47bf778ccc193b63732cfcd13841269674d2ae7`
- **作者**: 林致帆
- **日期**: 2020-05-07 19:17:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java`

### 5. Q00-20200507001 修正Portal 使用SSO追蹤流程會有錯誤
- **Commit ID**: `41ea9a8c68b8c87aaf3c20ea85f14d910868ad8b`
- **作者**: walter_wu
- **日期**: 2020-05-07 15:55:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PortalIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 6. [Web]C01-20200428002 修正流程統計類功能 Oracle日期查詢錯誤 統計結果多一天
- **Commit ID**: `1ccfba310ed631015f7d8fa28807573bbd9dbe35`
- **作者**: walter_wu
- **日期**: 2020-05-07 11:35:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`

### 7. [表單[表單設計師]TextBox正則表達式刪除無作用的功能
- **Commit ID**: `e6552920f627a6e7c0b7beb2095ed8bef749a688`
- **作者**: kaiteng
- **日期**: 2020-05-07 10:22:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 8. [Web]A00-*********** 移除維護作業"流程模組維護"[補修正]
- **Commit ID**: `189dbb80fc6ac2a457ac6a85e71f32e24d9859cd`
- **作者**: 林致帆
- **日期**: 2020-05-06 17:51:34
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FavoeitiesManagerDelegate.java`
  - ❌ **刪除**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessModuleDefDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ❌ **刪除**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessModuleDefinitionDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/favorities/FavoritiesManagerMgr.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDef.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDefBean.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDefLocal.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDefMgr.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ProcessModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ProcessModuleAccessRightVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserServiceBean.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProcessModuleViewer.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.2_DML_MSSQL_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.2_DML_Oracle_2_Check.sql`

### 9. [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料 [補修正]
- **Commit ID**: `dd1b5e7286b30531291340b41ba586eaae6af742`
- **作者**: 林致帆
- **日期**: 2020-05-05 16:26:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java`

### 10. [流程設計師]A00-20200410003 XPDL轉BPMN加入防呆
- **Commit ID**: `dec9fb9ba93c90251e69bbe1ff93ce15eb5fcee2`
- **作者**: yanann_chen
- **日期**: 2020-05-04 12:00:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java`

### 11. [BPM APP]Q00-20200430001調整微信所有流程名稱篩選功能，選項詞彙
- **Commit ID**: `503cbb8289bc8fbf322b9356fb029a4229d635cb`
- **作者**: 詩雅
- **日期**: 2020-04-30 17:21:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`

### 12. [內部]移除不必要的log
- **Commit ID**: `7bc614791c926be3279c11304f480f51d1873a25`
- **作者**: cherryliao
- **日期**: 2020-04-30 11:16:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 13. [BPM APP]Q00-20200430004 修正企業微信的通知表單畫面在查看或下載附件頁面沒有返回按鈕問題
- **Commit ID**: `51fefe6988a2a5e8c48818d79ca6254667498396`
- **作者**: cherryliao
- **日期**: 2020-04-30 10:55:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`

### 14. [Web]C01-20200327004 修正問題: 系統排程設定"每週"無效 新增排程時，設定"每週"無效[補]
- **Commit ID**: `1640cce6d16f66d21c184bc78188d6ddf17f733e`
- **作者**: yanann_chen
- **日期**: 2020-04-30 10:51:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`

### 15. [BPM APP]Q00-20200430002 修正行動表單使用表單函式庫停用語法時元件非新樣式問題
- **Commit ID**: `bde5153770b851a6efac539a4cf05fd8940ef636`
- **作者**: yamiyeh10
- **日期**: 2020-04-30 10:36:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 16. [BPM APP]Q00-20200430003 修正絕對位置表單的Grid按鈕在Android手機上跑版問題
- **Commit ID**: `50fab27f68d02ac6c292f4005806a661724c5a55`
- **作者**: cherryliao
- **日期**: 2020-04-30 10:18:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 17. [Web]C01-20200217003 修正進入模組程式維護頁面，程式的URL中有<和>符號時，點選該列後並儲存，再次進來顯示會異常
- **Commit ID**: `d713818257828c65cf90703d4b7d2cea548f5633`
- **作者**: 王鵬程
- **日期**: 2020-04-29 18:54:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`

### 18. Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `3478227657aabdfa99ceaa91fdceb6acd0f701d9`
- **作者**: yanann_chen
- **日期**: 2020-04-29 13:59:12
- **變更檔案數量**: 0

### 19. [流程引擎]C01-20191223002 修正問題: 主管首頁逾時關卡統計計算錯誤 修正SQL command[補]
- **Commit ID**: `aa30e1d57d9c1c8e57d3df3bbb15f906fd740c6c`
- **作者**: yanann_chen
- **日期**: 2020-04-29 13:58:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.2_DML_MSSQL_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.2_DML_Oracle_2_Check.sql`

### 20. [T100]A00-20200217001 修正T100單據aist310無法拋單到EFGP
- **Commit ID**: `ad1513fe9baf841d2f689bea977cfc2c999c0d21`
- **作者**: 林致帆
- **日期**: 2020-04-28 18:24:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`

### 21. [Web]A00-20200423001 修正RWD的Grid排序問題
- **Commit ID**: `489d9fd56c2f482afc1b9e305cfd26a6e5e486be`
- **作者**: 王鵬程
- **日期**: 2020-04-28 15:00:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 22. [T100]C01-20200410001 T100兼職部門與主部門是不同公司時，SYN_Employee需增加兼職部門資料
- **Commit ID**: `7819e602129cd8bccd5a338a09572309a697ed1d`
- **作者**: 林致帆
- **日期**: 2020-04-28 08:46:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java`

### 23. [Web]C01-20200131002 修正 檢視核決權限表型式的關卡頁面未放大最大時，在最下方關卡資訊有資料時卻無法顯示
- **Commit ID**: `3162b1cb8be8deb4ce4b64c3a3c6d3760c87fe50`
- **作者**: 王鵬程
- **日期**: 2020-04-24 18:26:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`

### 24. [E10]修正5771 E10流程範本無法匯入
- **Commit ID**: `ba5ccd0bcfd059ce8fee98445649b2e74c6747a1`
- **作者**: walter_wu
- **日期**: 2020-04-24 11:36:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\346\265\201\347\250\213\347\257\204\346\234\254.bpmn"`

### 25. 移除 [Web]Q00-20200415003 修正點擊待辦清單上方的查詢條件未清除
- **Commit ID**: `58e896e6f89adc581214cc5d0f89d7d4edbc6140`
- **作者**: 林致帆
- **日期**: 2020-04-23 14:42:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 26. 移除 [Web]A00-20200324004 修正點擊追蹤流程，流程清單上方的查詢條件未清除
- **Commit ID**: `27f60483fd392251ed7e7f196d3993f5b4e15112`
- **作者**: 林致帆
- **日期**: 2020-04-23 14:41:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 27. C01-20200331004 修正如果表單DIALOG對話窗型式元件在流程設定隱藏下一關資料會遺失
- **Commit ID**: `9fdec2ea8848ad6cc5e14c0710ce77bea5193807`
- **作者**: walter_wu
- **日期**: 2020-04-22 08:46:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 28. [Web]A00-20200327001 修正當流程已存在常用流程中，該流程發起成功後的在頁面中還能再次新增到常用流程。[補修正]
- **Commit ID**: `12f69312c06a3ab5684d0b311753c47ade5c5a1b`
- **作者**: 王鵬程
- **日期**: 2020-04-21 16:25:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`

### 29. [Web]A00-20200327001 修正當流程已存在常用流程中，該流程發起成功後的頁面中還能再次新增到常用流程。
- **Commit ID**: `2af6e2ed0435f894c3e1774dbf1fd92c0eac8522`
- **作者**: 王鵬程
- **日期**: 2020-04-16 18:49:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 30. [Web]Q00-20200415004 修正BPM左側選單是簡易顯示時，我的最愛的常用流程點擊沒反應
- **Commit ID**: `e1c92133d62b85d1115f9736f49d849c14291f7e`
- **作者**: 王鵬程
- **日期**: 2020-04-16 11:33:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 31. C01-20200410005 修正E10同步表單如果是修改狀態 再Grid裡新增欄位會導致欄位值錯位的問題
- **Commit ID**: `3fe7f2cae20f5296f067b78afa77d3dbbd26d627`
- **作者**: walter_wu
- **日期**: 2020-04-15 19:45:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`

### 32. [Web]Q00-20200415003 修正點擊待辦清單上方的查詢條件未清除
- **Commit ID**: `d95df4ba9eff5eebd171b24fdf1e213eb37e396f`
- **作者**: 林致帆
- **日期**: 2020-04-15 19:29:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 33. C01-20200407004 修正E10同步表單如果是修改需要比對的話，忽略SubTab
- **Commit ID**: `77364a66dfd678ae7fa3ce8389f18cf733b1f57b`
- **作者**: walter_wu
- **日期**: 2020-04-14 17:34:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`

### 34. [Web]A00-20200324004 修正點擊追蹤流程，流程清單上方的查詢條件未清除
- **Commit ID**: `752a63a5f4452866eb778ec2a773f04ab77c9e14`
- **作者**: 林致帆
- **日期**: 2020-04-14 11:32:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 35. [WorkFlow]A00-20200323001 修正WF流程-資產請購資料建立作業(ASTI19)
- **Commit ID**: `c7779c8bd0f44a371af49d32547f0f1d37785c01`
- **作者**: walter_wu
- **日期**: 2020-04-10 15:10:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip`

### 36. [T100]Q00-20191225002 修正T100從文檔中心傳送附件到BPM，附件描述欄位會有異常
- **Commit ID**: `3da2665488442082538e903e3c255e916cbee453`
- **作者**: walter_wu
- **日期**: 2020-04-07 06:44:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 37. [流程引擎]A00-20200408002 修正問題: BamWorkItemData資料表未記錄核決層級關卡人員處理資訊
- **Commit ID**: `9dbae6e8386bbf81eda02b94d965924fe067283d`
- **作者**: yanann_chen
- **日期**: 2020-04-09 18:03:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 38. [Web]A00-20200319001 修正表單設計師頁面未正確顯示多語[補修正]
- **Commit ID**: `02a315cfdd7657c2a455b338b287586a188402c4`
- **作者**: 王鵬程
- **日期**: 2020-04-09 11:47:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`

### 39. [Web]A00-20200319001 修正表單設計師頁面未正確顯示多語
- **Commit ID**: `cd4db7694a73f78c98ffcede9ea9f1d5ba139917`
- **作者**: 王鵬程
- **日期**: 2020-04-08 18:49:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rsrcBundleManager.js`

### 40. Merge branch 'develop_v57' of http://10.40.41.229/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `fddcceb4c747e1024b6e589d0882bff555e80f27`
- **作者**: 林致帆
- **日期**: 2020-03-31 18:25:07
- **變更檔案數量**: 0

### 41. [Web]A00-20200310001修正批次轉派時，點擊經常選取對象，確定鈕不能點擊
- **Commit ID**: `2c1583b440b4fc6b7fdc9f5572899f2e12fd7119`
- **作者**: 林致帆
- **日期**: 2020-03-31 18:24:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`

### 42. [Web]C01-20200327004 修正問題: 系統排程設定"每週"無效
- **Commit ID**: `3c6321d2ebbe5d88d353c5f0353631a84c07470d`
- **作者**: yanann_chen
- **日期**: 2020-03-30 13:40:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`

### 43. [ISO]C00-20200323002修正舊版ISO評審排程會撈出已失效的document評審清單
- **Commit ID**: `3ccf122ecfb654cfcee4ef33fd59c75ef6a60d3c`
- **作者**: waynechang
- **日期**: 2020-03-27 10:26:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/BackJobListReaderImpl.java`

### 44. [流程引擎]C01-20200220002 修正流程第一關ID為ACT1時，自動簽核異常
- **Commit ID**: `634dce0dd4e6dc17f5f1ec5ee1189aae0fb8ca5e`
- **作者**: 王鵬程
- **日期**: 2020-03-26 20:09:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 45. [E10]C01-20200324003 修正如果E10傳入的Grid內容有[] 會因為視為子單身用子單身的做法導致資料異常
- **Commit ID**: `db7840849e68f751e1e7c2425310527dc621d462`
- **作者**: walter_wu
- **日期**: 2020-03-26 19:23:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormInstanceTransferJson.java`

### 46. [Web]A00-20200318001 修正離職作業維護的查詢頁面讓日期開窗可以完整顯示
- **Commit ID**: `abd852cc4d4069d998b79200d9993b3c86c963e0`
- **作者**: 王鵬程
- **日期**: 2020-03-25 19:26:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`

### 47. [Web]A00-20190711001 修正以英文登入BPM，在退回重辦頁面只有一筆可選時，點選關卡困難
- **Commit ID**: `8279850bf5e02fea9535daa4e8a8b2247e193007`
- **作者**: 王鵬程
- **日期**: 2020-03-23 18:50:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 48. [Web]A00-20200210001 修正當流程名稱跟模組下的程式名稱相同時，在流程頁面內上方title會多了模組名稱
- **Commit ID**: `ba44a6714deaea09da20d4810ece9040186f1dee`
- **作者**: 王鵬程
- **日期**: 2020-03-20 15:53:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 49. [BPM APP]C01-20200319007 修正行動端的子單身資料不會根據對應單身資料做顯示
- **Commit ID**: `89cd643dd375e0f4dffdbbf676104dc2112ed9b2`
- **作者**: yamiyeh10
- **日期**: 2020-03-20 12:09:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 50. [Web]A00-20200311001 修正同時從Mail待辦連結登入及一般登入在簽核表單時儲存表單資料被覆蓋
- **Commit ID**: `1d57b918a2398c82cfc95e47caa378daddc81b6f`
- **作者**: 林致帆
- **日期**: 2020-03-18 20:08:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 51. [Web]Q00-20200317002 修正首頁模組嵌入BPM發單畫面的表單grid太長時scrollbar無作用
- **Commit ID**: `053fa38ed5ddddda9e901850b7bc4f289b48743f`
- **作者**: waynechang
- **日期**: 2020-03-17 14:38:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 52. [Web]Q00-20200317001 修正大陸首頁模組嵌入BPM時，在win10 + IE11上，顯示有問題
- **Commit ID**: `1a192f3c180ae7ab31b144486dba03d530f88fa7`
- **作者**: waynechang
- **日期**: 2020-03-17 14:20:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 53. [Web]C01-20200316002 修正流程開窗開啟異常 [補修正]
- **Commit ID**: `2fdeb115a9567deb5423b3a054e87b5eac708d6d`
- **作者**: 林致帆
- **日期**: 2020-03-17 11:39:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 54. [Web]C01-20200316002 修正流程開窗開啟異常
- **Commit ID**: `087ae5f2b2ac04a548021de3be2600336ebefb40`
- **作者**: 林致帆
- **日期**: 2020-03-16 19:52:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 55. [BPMAPP]C01-20200309002 修正表單欄位設置浮點數型態時且填字串會導致移動表單上誤判成表單錯誤問題
- **Commit ID**: `fe21cd0faf49b51c111db99a69d9375de283d536`
- **作者**: pinchi_lin
- **日期**: 2020-03-12 18:19:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 56. [Web]Q00-20200312002 修正BPMTable.js的showColumn無作用
- **Commit ID**: `eea31560973a2da5bea983a507bf932e86d08fa9`
- **作者**: waynechang
- **日期**: 2020-03-12 14:43:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 57. [Web]A00-*********** 移除維護作業"流程模組維護"
- **Commit ID**: `427a1ad95c717942ee3bf531ff6bd3459cc53488`
- **作者**: 林致帆
- **日期**: 2020-03-10 15:20:53
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/CreateProcessModule.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/ManageProcessModuleMain.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.2_DML_MSSQL_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.2_DML_Oracle_2_Check.sql`

### 58. [流程引擎]C01-20191223002 修正問題: 主管首頁逾時關卡統計計算錯誤
- **Commit ID**: `d92a2c1ba953f7ce7afa1e906555ad00262db9f3`
- **作者**: yanann_chen
- **日期**: 2020-03-05 16:00:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.2_DML_MSSQL_2_Check.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.7.2_DML_Oracle_2_Check.sql`

