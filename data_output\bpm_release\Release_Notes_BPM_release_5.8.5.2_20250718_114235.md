# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.5.2
- **舊版本**: release_5.8.5.1
- **生成時間**: 2025-07-18 11:42:35
- **新增 Commit 數量**: 147

## 變更摘要

### lorenchang (8 commits)

- **2022-06-26 22:20:59**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2
  - 變更檔案: 25 個
- **2021-02-23 15:28:30**: 修正集合快取無法移除或更新的異常(同時可讓58支持多AP)
  - 變更檔案: 26 個
- **2021-02-05 15:06:19**: [流程設計工具]Q00-20210205002 修正某些個人環境操作時會出現畫面錯亂的異常
  - 變更檔案: 1 個
- **2021-02-04 18:06:42**: [DotJ]Q00-20210204004 修正修正如果流程有核決權限表，送簽會出現LazyInitializationException
  - 變更檔案: 1 個
- **2021-02-04 17:51:12**: [DotJ]Q00-20210204003 修正取得BPM流程圖出現NullPointerException
  - 變更檔案: 1 個
- **2021-02-04 15:21:17**: [Web]Q00-20210204001 進度條載入優化，減少卡頓的感受改為等速載入
  - 變更檔案: 1 個
- **2021-02-03 11:45:09**: [組織同步]Q00-20210129001 修正組織同步StepTtls跟StepRoles的IsRemoveData設為true，同步後不會移除相關資料的異常
  - 變更檔案: 1 個
- **2021-01-29 16:28:46**: [內部] 更新越南語系
  - 變更檔案: 1 個

### yamiyeh10 (25 commits)

- **2021-02-26 16:06:58**: [BPM APP]Q00-20210226001 使用IE瀏覽器操作入口平台整合設定中其他工具佈署頁面中產生網址功能顯示異常
  - 變更檔案: 3 個
- **2021-02-25 16:11:48**: [內部]Q00-20210225005 修正在企業微信與IMG操作加簽的選擇人員全選功能異常問題
  - 變更檔案: 2 個
- **2021-02-25 14:05:25**: [BPM APP]Q00-20210224002 修正IMG的轉派頁面點擊浮動按鈕後顯示的遮罩未蓋住所有畫面問題
  - 變更檔案: 2 個
- **2021-02-25 13:18:13**: [BPM APP]Q00-20210225003 修正企業微信操作加簽與發送通知功能的選擇人員畫面按鈕跑版問題
  - 變更檔案: 1 個
- **2021-02-22 10:59:47**: [BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能
  - 變更檔案: 9 個
- **2021-02-19 19:51:39**: [E10]S00-20210105001 新增E10整合表單在行動端上支援E10表單過濾主鍵欄位功能
  - 變更檔案: 5 個
- **2021-02-05 16:11:02**: [BPM APP]Q00-*********** 調整在Line整合方案時行動簽核管理中心的移動消息訂閱管理頁面改撈取所有流程機制
  - 變更檔案: 11 個
- **2021-02-02 10:30:23**: [BPM APP]Q00-20210201005 修正IMG的中間層與動態渲染表單新增明後天提醒後回應的資訊無多語系問題
  - 變更檔案: 2 個
- **2021-02-01 17:21:35**: [內部]補上被覆蓋掉的多語系
  - 變更檔案: 1 個
- **2021-02-01 16:41:13**: [BPM APP]Q00-20210201006 調整入口平台整合設定的其他工具佈署網址中按鈕沒有對應提示訊息問題
  - 變更檔案: 1 個
- **2021-02-01 10:34:38**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
  - 變更檔案: 1 個
- **2021-01-29 10:16:16**: [E10]新增E10整合表單同步時自動產生行動端Grid載入語法
  - 變更檔案: 1 個
- **2021-01-28 10:10:16**: [BPM APP]調整行動端詳情簽核派送時可依系統變數設定啟用檢查簽核意見是否為空功能
  - 變更檔案: 10 個
- **2021-01-25 10:33:07**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
  - 變更檔案: 2 個
- **2021-01-20 11:59:38**: [系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]
  - 變更檔案: 6 個
- **2021-01-14 19:07:06**: [BPM APP]Q00-20210114003 修正行動簽核管理中心的使用者批次維護頁面選擇非Excel的檔案匯入時顯示錯誤訊息的多語系異常
  - 變更檔案: 1 個
- **2021-01-14 19:01:29**: [BPM APP]Q00-20210112001 修正授權中間層的批次匯入使用者無法使用新版的EXCEL匯入
  - 變更檔案: 3 個
- **2021-01-14 15:09:17**: [BPM APP]Q00-20201231001 調整行動簽核管理中心的智能管理中心顯示機制
  - 變更檔案: 1 個
- **2021-01-14 11:17:37**: [BPM APP]Q00-20201223003 修正IMG的明天提醒與後天提醒英文語系拼錯問題
  - 變更檔案: 1 個
- **2021-01-13 18:40:17**: [BPM APP]Q00-20201222001 修正鼎捷移動APP上的部份應用未支持多語系的問題
  - 變更檔案: 3 個
- **2021-01-12 15:51:27**: [BPM APP]Q00-20201231003 修正在IE和Safari瀏覽器頁面整合授權中間層時可選擇隱藏的授權類型問題
  - 變更檔案: 1 個
- **2021-01-12 14:44:07**: [BPM APP]Q00-20210104001 調整入口平台整合設定中測試連線狀態若出現錯誤訊息時會跑版問題
  - 變更檔案: 1 個
- **2020-12-28 17:36:10**: [ESS]新增ESS整合表單:ESSF50B班次變更(多人多天)
  - 變更檔案: 1 個
- **2020-12-25 16:16:35**: [BPM APP]Q00-20201225004 修正入口平台整合設定維護作業存在連線資訊時用手機操作其畫面不會顯示任何資訊問題
  - 變更檔案: 1 個
- **2020-12-25 14:38:57**: [BPM APP]Q00-20201222003 修正授權中間層管理維護作業存在連線資訊時用手機操作其畫面時不會顯示任何資訊問題
  - 變更檔案: 1 個

### cherryliao (15 commits)

- **2021-02-25 19:47:09**: [BPM APP]Q00-20210225006 修正在企業微信與IMG操作加簽與發送通知的選擇人員返回按鈕功能異常
  - 變更檔案: 4 個
- **2021-02-23 10:26:32**: [BPM APP]調整企業微信的簽核歷程依系統變數設定作排序
  - 變更檔案: 10 個
- **2021-02-22 13:34:45**: [BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]
  - 變更檔案: 1 個
- **2021-02-22 12:03:17**: [BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]
  - 變更檔案: 11 個
- **2021-02-20 09:43:34**: [E10]S00-20210105001 新增E10整合表單在行動端上支援E10表單過濾主鍵欄位功能[補]
  - 變更檔案: 2 個
- **2021-02-05 17:18:26**: [BPM APP]S00-20210111002 調整LINE推播訊息依照流程關卡中的允許批次簽核來卡控是否顯示直接簽核按鈕
  - 變更檔案: 1 個
- **2021-02-03 17:31:31**: [BPM APP]Q00-20210201004 修正入口平台整合設定中其他工具佈署網址頁面中資料過多時無scrollbar問題
  - 變更檔案: 1 個
- **2021-02-03 12:15:39**: [系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]
  - 變更檔案: 6 個
- **2021-02-02 14:40:59**: [BPM APP]Q00-20210120002 修正IMG推播消息為動態選染表單時加提醒後於行事曆查看顯示非動態渲染表單畫面的問題
  - 變更檔案: 2 個
- **2021-01-29 11:12:08**: [E10]新增E10整合表單同步時自動產生行動端Grid載入語法[補]
  - 變更檔案: 1 個
- **2021-01-25 14:17:20**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
  - 變更檔案: 1 個
- **2021-01-20 11:07:08**: [系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]
  - 變更檔案: 4 個
- **2021-01-11 19:09:40**: [BPM APP]S00-20200514001 調整行動端發起/簽核時的選擇部門樣式
  - 變更檔案: 4 個
- **2021-01-04 17:55:53**: [BPM APP]A01-20210104002 修正整合LINE的部分用戶無法收到系統排程BPM首頁通知推送的問題
  - 變更檔案: 1 個
- **2020-12-31 13:17:19**: [BPM APP]Q00-20201228002 修正詳情表單加提醒時，若有同步動態渲染表單，在行事曆查看流程時可看到動態渲染表單[補]
  - 變更檔案: 5 個

### yanann_chen (27 commits)

- **2021-02-25 16:59:27**: [流程引擎]Q00-20210225009 修正關注流程設定提示欄位設定中有新版表單新增的欄位時，舊版表單流程無法往下派送
  - 變更檔案: 1 個
- **2021-02-19 16:08:22**: [流程引擎]A00-20201214001 修正流程處理->系統通知沒有「轉由他人處裡」的通知
  - 變更檔案: 1 個
- **2021-02-04 16:45:42**: [流程引擎]Q00-20210204002 修正已封存的工作通知清單中查不到已閱讀的工作通知
  - 變更檔案: 1 個
- **2021-02-03 11:50:39**: [Web]A00-20210201001 調整表單浮點數欄位運算邏輯
  - 變更檔案: 1 個
- **2021-02-03 10:33:20**: Revert "[Web]A00-20210201001 修正在Chrome上的浮點數四捨五入計算結果與在IE上的不一致"
  - 變更檔案: 1 個
- **2021-02-02 15:16:19**: [Web]A00-20210201001 修正在Chrome上的浮點數四捨五入計算結果與在IE上的不一致
  - 變更檔案: 1 個
- **2021-02-01 15:37:12**: [Web]Q00-20210201002 隱藏IE預設的清除按鈕
  - 變更檔案: 1 個
- **2021-01-27 18:43:10**: [流程引擎]Q00-20210127002 修正組織同步後WorkCalendar.containerOID被清空
  - 變更檔案: 1 個
- **2021-01-27 15:45:03**: [流程引擎]A00-20210126003 修正表單人員開窗設定過濾條件後以"人員名稱"為條件查詢無效
  - 變更檔案: 2 個
- **2021-01-26 17:39:57**: [Web]A00-20210114001 修正追蹤流程連結打開Tiptop流程時無法下載URL附件
  - 變更檔案: 1 個
- **2021-01-26 11:54:10**: [流程設計師]A00-20210122001 修正XPDL流程中有空白子流程時，無法轉換為BPMN流程
  - 變更檔案: 1 個
- **2021-01-25 17:29:48**: [流程引擎]A00-20210121001 修正追蹤流程>已轉派工作，已處理"與全部頁籤中模糊查詢功能無效異常
  - 變更檔案: 1 個
- **2021-01-21 10:55:49**: [流程引擎]A00-20210118002 修正關注流程參考表單欄位時，若舊版表單沒有該欄位，則已發起的舊版表單流程無法往下派送
  - 變更檔案: 1 個
- **2021-01-20 15:55:43**: [流程引擎]A00-20201224001 修正表單開窗元件設定過濾條件無效異常
  - 變更檔案: 1 個
- **2021-01-19 17:23:12**: [流程引擎]S00-20201214001 簡易流程圖的關卡中若有部分處理者已離職，則不顯示已離職的人員
  - 變更檔案: 1 個
- **2021-01-15 14:29:10**: [流程引擎]Q00-20210115002 修正在組織設計師刪除某部門時，若該部門有被其他物件參考，則提示相關內容
  - 變更檔案: 1 個
- **2021-01-13 18:25:59**: [Web]Q00-20210113001 修正編輯系統排程後，系統仍按照編輯前的設定內容執行排程
  - 變更檔案: 2 個
- **2021-01-13 15:12:20**: [流程引擎]A00-20210105001 修正經過invoke關卡後，流程主旨參數內容備替換成英文語系的內容
  - 變更檔案: 1 個
- **2021-01-08 11:50:06**: [流程引擎]A00-20210107002 修正WebServie getFormFieldTemplate，複合元件若為自定義開窗時無法填值
  - 變更檔案: 1 個
- **2021-01-07 10:50:37**: [Web]Q00-20210107001 修正流程發起時，若無法解析流程關係人，未呈現相關提示訊息
  - 變更檔案: 1 個
- **2021-01-05 10:55:02**: [Web]A00-20210104001 修正使用HRM Portlet進入BPM追蹤流程，執行撤銷流程時無法填寫撤銷理由
  - 變更檔案: 1 個
- **2021-01-04 17:32:11**: [流程引擎]Q00-20210104003 修正WebService接口getFormFieldTemplate回傳的值與實際儲存的結果不一致
  - 變更檔案: 1 個
- **2020-12-31 15:34:28**: [流程引擎]Q00-20201231002 修正Radio/CheckBox元件設定"最後一個選項額外產生輸入框"，轉存表單未將輸入值存入資料庫
  - 變更檔案: 1 個
- **2020-12-29 14:21:12**: [流程引擎]Q00-20201229001 調整進入程式權限設定清單時清除殘存資料的SQL，以提高系統執行效能
  - 變更檔案: 1 個
- **2020-12-25 15:39:47**: [流程引擎]Q00-20201225003 進入程式權限設定清單時，清除刪除部門後所殘存的程式權限資料，避免維護作業發生異常，無法儲存修改的資料
  - 變更檔案: 1 個
- **2020-12-25 15:11:51**: [流程引擎]Q00-20201225002 修正組織同步修改使用者資料後，使用者帳號狀態變更為"未啟用"
  - 變更檔案: 1 個
- **2020-12-24 14:48:26**: [流程引擎]A00-20201211004 調整DealOvertimeActivity的SQL，提升運行效率
  - 變更檔案: 1 個

### 詩雅 (11 commits)

- **2021-02-25 16:27:54**: [內部]Q00-20210225007 修正IMG取追蹤處理，依動態渲染表單條件的撈取流程列表異常問題
  - 變更檔案: 1 個
- **2021-02-25 14:41:35**: [BPM APP]Q00-20210224001調整使用IE瀏覽器操作入口平台整合設定切回連線資訊管理頁面時，會出現其他工具佈署網址頁面資訊的問題
  - 變更檔案: 1 個
- **2021-02-25 14:07:09**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-02-25 14:06:07**: [BPM APP]Q00-20210224001 使用IE瀏覽器操作入口平台整合設定中其他工具佈署網址頁面中資料過多時無scrollbar
  - 變更檔案: 1 個
- **2021-02-22 11:29:33**: [BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]
  - 變更檔案: 3 個
- **2021-01-29 20:17:05**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
  - 變更檔案: 1 個
- **2021-01-29 19:29:40**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補] 1. 調整待辦列表 2. 調整通知列表 3. 統計元件筆數
  - 變更檔案: 3 個
- **2021-01-19 15:49:14**: [系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位
  - 變更檔案: 2 個
- **2021-01-11 13:41:09**: [BPM APP]C01-20201215003 調整追蹤狀態下表單的全域變數viewMode參數值設定為"TRACE"
  - 變更檔案: 2 個
- **2021-01-05 16:26:38**: [BPM APP]C01-20201230003修正當瀏覽器為IE時，操作整合頁面Scrollbar無法滾動問題(含授權中間層&入口平台整合設定頁面)
  - 變更檔案: 1 個
- **2020-12-28 18:31:03**: [BPM APP]Q00-20201228002 修正詳情表單加提醒時，若有同步動態渲染表單，在行事曆查看流程時可看到動態渲染表單
  - 變更檔案: 4 個

### pinchi_lin (10 commits)

- **2021-02-25 14:52:11**: [BPM APP]Q00-20210225004 修正在IMG上操作同意或不同意按鈕會失敗問題
  - 變更檔案: 1 個
- **2021-01-25 11:11:09**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
  - 變更檔案: 2 個
- **2021-01-22 12:51:54**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
  - 變更檔案: 3 個
- **2021-01-21 18:58:12**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定
  - 變更檔案: 6 個
- **2021-01-20 09:58:12**: [BPM APP]調整IMG用的接口支持驗證BPM的access token
  - 變更檔案: 6 個
- **2021-01-14 16:25:04**: [BPM APP]Q00-20201231001 修正IMG已結案流程應用中處理的流程其流程名稱篩選條件筆數異常問題
  - 變更檔案: 2 個
- **2021-01-12 19:07:55**: [BPM APP]Q00-20210104002 調整IMG在動態渲染表單作連續簽核時下一筆流程表單沒同步時會異常問題[補]
  - 變更檔案: 1 個
- **2021-01-12 18:42:04**: [BPM APP]Q00-20210104002 調整IMG在動態渲染表單作連續簽核時下一筆流程表單沒同步時會異常問題
  - 變更檔案: 2 個
- **2021-01-04 15:04:23**: [內部]調整排版與移除不必要的部分
  - 變更檔案: 5 個
- **2020-12-25 11:36:29**: [BPM APP]Q00-20201225001 修正IMG動態渲染表單點明天後天提醒會失敗問題
  - 變更檔案: 1 個

### 林致帆 (20 commits)

- **2021-02-25 09:06:14**: [Web]Q00-20210225001 修正追蹤流程SQL，符合條件總筆數未達1000確未正確撈出
  - 變更檔案: 1 個
- **2021-02-23 14:46:53**: [Web]Q00-20210107002 修正從待辦連結進入BPM簽核該流程實例，簽核後無法導向下一個簽核流程項目
  - 變更檔案: 1 個
- **2021-02-22 15:14:23**: [Web]A00-20210203001 修正BPM首頁的待辦清單，除了第一筆流程實例，其他流程實例點擊進入都沒有"處理下個工作按鈕"[補修正]
  - 變更檔案: 1 個
- **2021-02-22 14:58:06**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-02-22 14:57:41**: [Web]A00-20210203001 修正BPM首頁的待辦清單，除了第一筆流程實例，其他流程實例點擊進入都沒有"處理下個工作按鈕"
  - 變更檔案: 1 個
- **2021-02-17 10:28:14**: [T100]修正 T100出貨表單axmt500單身裡的欄位Id有重覆
  - 變更檔案: 1 個
- **2021-02-08 15:08:56**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-02-08 15:02:07**: [流程設計師]A00-20210201002 修正 複製一般關卡，流程在該複製的關卡加簽，加簽完會直接結案
  - 變更檔案: 1 個
- **2021-02-04 17:34:08**: [Web]A00-20210202001 Portal導向BPM，BPM首頁左側功能列語系與Portal傳過來的語系不相同
  - 變更檔案: 5 個
- **2021-02-01 14:17:24**: [Web]A00-20210128001 修正 外部連結-追蹤流程實例內單一表單資料，點擊列印鍵沒有反應
  - 變更檔案: 1 個
- **2021-01-27 17:10:25**: [T100]回寫T100服務任務關卡回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單回到「人員處理」關卡，將訊息顯示在簽核畫面上
  - 變更檔案: 3 個
- **2021-01-13 18:42:13**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-01-13 18:40:26**: [流程引擎]Q00-20210113002 人員名稱有特殊字導致開啟ESS表單報錯
  - 變更檔案: 1 個
- **2020-12-29 17:49:07**: [Web]Q00-20201229002 表單有元件使用資料註冊器時，資料註冊器沒有任何一筆資料的狀況下，開啟表單會異常
  - 變更檔案: 1 個
- **2020-12-24 10:12:47**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2020-12-24 10:12:23**: [內部]新增CRM表單-報價變更單
  - 變更檔案: 1 個
- **2020-12-23 16:48:21**: [內部]Q00-20201218002 ActionFilter　log訊息調整
  - 變更檔案: 1 個
- **2020-12-23 16:42:42**: [流程引擎]Q00-20201120005 修正流程進行轉派任務後再取退回重辦(按流程定義退回)，派送的處理者不正確
  - 變更檔案: 1 個
- **2020-12-23 16:40:16**: Revert "[流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確"
  - 變更檔案: 1 個
- **2020-12-23 16:29:45**: [流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確
  - 變更檔案: 1 個

### 王鵬程 (16 commits)

- **2021-02-23 18:10:16**: [Web]Q00-20210223003 修正監控、待辦頁的進階查詢，用backspace和delete將欄位清空，對應的隱藏欄位資料未確實清除
  - 變更檔案: 2 個
- **2021-02-22 17:32:17**: [Web]Q00-20210222001 修正barcode的值有某些特殊符號會導致掃描後資料缺少或異常
  - 變更檔案: 1 個
- **2021-02-03 14:34:37**: [組織設計師]A00-*********** 修正人員有任職部門主管並卸任主管職位後，需重啟BPM才能設為離職
  - 變更檔案: 6 個
- **2021-01-28 13:59:38**: [Web]A00-20201022001 修正流程主旨中最後是\的符號，會導致待辦、監控、追蹤流程的清單頁會無法開啟
  - 變更檔案: 2 個
- **2021-01-27 16:48:58**: [流程設計師]A00-20210119002 修正流程的頭兩個關卡將Dialog元件設為invisible，派送到第三關會報錯
  - 變更檔案: 1 個
- **2021-01-25 17:54:30**: [Web]A00-20210114003 修正在帳號管理頁面，將頁面顯示筆數選大於10，會無法切換下一頁和上一頁
  - 變更檔案: 2 個
- **2021-01-22 18:14:58**: [Web]Q00-20210122001 將Date元件出現提示訊息的字體顏色調整成紅色
  - 變更檔案: 1 個
- **2021-01-20 18:10:31**: [Web]A00-20210118001 修正進入一筆待辦並填寫簽核意見後，點選回到工作清單鈕，再進入待辦任一流程簽核意見沒被清除
  - 變更檔案: 1 個
- **2021-01-20 11:03:26**: [Web]Q00-20210120001 修正administrator登入行動版，左方選單會出現BPM首頁
  - 變更檔案: 1 個
- **2021-01-19 17:28:40**: [Web]Q00-20210119003 修正絕對位置表單在更換image後，在預覽時顯示圖片不正確
  - 變更檔案: 2 個
- **2021-01-12 16:14:56**: [Web]Q00-20210112002 日期元件有勾選顯示時間，在開日期窗未選擇日期時按下確定會出現提示訊息
  - 變更檔案: 1 個
- **2021-01-05 18:13:52**: [Web]Q00-20210105001 修正Date元件有勾選顯示時間時，日期選擇窗有換頁過就無法手動輸入時間
  - 變更檔案: 1 個
- **2021-01-04 18:16:46**: [Web]A00-20201215002 修正使用formTerminateSave來終止流程時，運作邏輯的順序不正確
  - 變更檔案: 1 個
- **2020-12-28 12:04:26**: [Web]Q00-20201228001 修正系統排程設定頁面中，排程生效時間下拉元件太窄導致內容無法完整顯示
  - 變更檔案: 1 個
- **2020-12-23 18:24:13**: [內部]C01-20201208006 修正如果是留職停薪(intermissionDate)有可能導致離職人員作業維護出現異常(NullPointerException)
  - 變更檔案: 1 個
- **2020-12-23 18:15:06**: [Web]A00-20201203001 修正當有類html tag時導致顯示異常，並將調整只開放a標籤，其餘直接顯示
  - 變更檔案: 2 個

### walter_wu (9 commits)

- **2021-02-22 15:32:59**: [WorkFlow][YIFE]A00-20201211001 取得單別單號記錄時未用公司別區分，導致有同單別單號取錯資料回傳
  - 變更檔案: 1 個
- **2020-12-31 16:07:55**: [流程設計師]A00-20201222001 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞
  - 變更檔案: 1 個
- **2020-12-31 16:04:36**: Revert "[流程設計師]A00-20180129002 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞"
  - 變更檔案: 1 個
- **2020-12-31 16:01:58**: [流程設計師]A00-20180129002 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞
  - 變更檔案: 1 個
- **2020-12-25 15:48:41**: [Web]A00-20201217001 修正追蹤流程SQL，符合條件總筆數未達1000確未正確撈出
  - 變更檔案: 1 個
- **2020-11-30 17:59:13**: [Web]A00-20201103001 修正重新發起流程可以往前加簽的問題
  - 變更檔案: 1 個
- **2020-12-23 16:00:05**: [Web]C01-20201130004 修正點已經被簽過的Email簽核連結進入確可以再簽一次的錯誤
  - 變更檔案: 1 個
- **2020-12-23 15:47:06**: [Web]A00-20201201001 修正不是在代辦清單或追蹤清單頁的其他頁面，點選右上角的單筆代辦直接進入代辦，之後按返回上一頁畫面會卡死
  - 變更檔案: 1 個
- **2020-12-23 15:37:17**: Q00-20201223002 修正從E10取得附件接口，在Invoke參數Id設定錯誤的狀態下並未拋錯，導致未取得附件流程就往下走
  - 變更檔案: 2 個

### waynechang (6 commits)

- **2021-02-05 17:34:43**: [Web]A00-20210204001 修正系統多語系維護作業，當「設定值」內容有大寫字母，進入編輯時多語系Grid無資料
  - 變更檔案: 1 個
- **2021-02-05 13:52:28**: [流程引擎]Q00-20210205001 麗智電子客製-核決層級解析邏輯調整
  - 變更檔案: 2 個
- **2021-01-28 15:09:32**: [WEB]S00-20201202001 調整流程管理-作業程序書的「使用表單」欄位的樣式為垂直置中
  - 變更檔案: 1 個
- **2021-01-26 15:41:54**: [WEB]A00-20210112002 修正流程處理追蹤流程處理的流程 Grid清單，點擊時間排序會發生異常
  - 變更檔案: 1 個
- **2021-01-14 14:56:36**: [WEB]Q00-20210114002 修正維護樣板QueryTemplate.js，當設定為雙欄顯示，且查詢條件數量為單數時，畫面會排版異常
  - 變更檔案: 1 個
- **2020-12-31 17:06:52**: [Web]Q00-20200806002 產品回收NTKO專案[補]
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2
- **Commit ID**: `1254df25fe94e6cd2497db1924b0aa1db497b95b`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:20:59
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [BPM APP]Q00-20210226001 使用IE瀏覽器操作入口平台整合設定中其他工具佈署頁面中產生網址功能顯示異常
- **Commit ID**: `4f0b4c8f11376909fe04b1695032abd4e385fdae`
- **作者**: yamiyeh10
- **日期**: 2021-02-26 16:06:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 3. [BPM APP]Q00-20210225006 修正在企業微信與IMG操作加簽與發送通知的選擇人員返回按鈕功能異常
- **Commit ID**: `e527ded52d38519a21646194d99e14509c3993ba`
- **作者**: cherryliao
- **日期**: 2021-02-25 19:47:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 4. [流程引擎]Q00-20210225009 修正關注流程設定提示欄位設定中有新版表單新增的欄位時，舊版表單流程無法往下派送
- **Commit ID**: `23eebd2782ba0e61ec5750dfa8da4796453f94b1`
- **作者**: yanann_chen
- **日期**: 2021-02-25 16:59:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/CriticalProcessMgr.java`

### 5. [內部]Q00-20210225007 修正IMG取追蹤處理，依動態渲染表單條件的撈取流程列表異常問題
- **Commit ID**: `69623816c10d022454abf73640e27cc2b53f1a71`
- **作者**: 詩雅
- **日期**: 2021-02-25 16:27:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

### 6. [內部]Q00-20210225005 修正在企業微信與IMG操作加簽的選擇人員全選功能異常問題
- **Commit ID**: `0570c6146f71b053774a7bdcf93eaa7cf12497e8`
- **作者**: yamiyeh10
- **日期**: 2021-02-25 16:11:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 7. [BPM APP]Q00-20210225004 修正在IMG上操作同意或不同意按鈕會失敗問題
- **Commit ID**: `eef033a2f348a007daea043296cf25a37fd03e4f`
- **作者**: pinchi_lin
- **日期**: 2021-02-25 14:52:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 8. [BPM APP]Q00-20210224001調整使用IE瀏覽器操作入口平台整合設定切回連線資訊管理頁面時，會出現其他工具佈署網址頁面資訊的問題
- **Commit ID**: `331a6f299f037a5f951294c923f14793959399d5`
- **作者**: 詩雅
- **日期**: 2021-02-25 14:41:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`

### 9. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `a3c0720e427888574ec396173de0e4ce01e3fbb6`
- **作者**: 詩雅
- **日期**: 2021-02-25 14:07:09
- **變更檔案數量**: 0

### 10. [BPM APP]Q00-20210224001 使用IE瀏覽器操作入口平台整合設定中其他工具佈署網址頁面中資料過多時無scrollbar
- **Commit ID**: `17f8269d83dc173285a799cd6ef1956cbe237e96`
- **作者**: 詩雅
- **日期**: 2021-02-25 14:06:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp`

### 11. [BPM APP]Q00-20210224002 修正IMG的轉派頁面點擊浮動按鈕後顯示的遮罩未蓋住所有畫面問題
- **Commit ID**: `2e17abfe4c245a53943ba3e496edf69a341c7bcc`
- **作者**: yamiyeh10
- **日期**: 2021-02-25 14:05:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`

### 12. [BPM APP]Q00-20210225003 修正企業微信操作加簽與發送通知功能的選擇人員畫面按鈕跑版問題
- **Commit ID**: `1945bb5a4f6239047907370515f4a14b9d7b4869`
- **作者**: yamiyeh10
- **日期**: 2021-02-25 13:18:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`

### 13. [Web]Q00-20210225001 修正追蹤流程SQL，符合條件總筆數未達1000確未正確撈出
- **Commit ID**: `d3fadcf50f72d0816b9fdc7b37aeddb96714d077`
- **作者**: 林致帆
- **日期**: 2021-02-25 09:06:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 14. [Web]Q00-20210223003 修正監控、待辦頁的進階查詢，用backspace和delete將欄位清空，對應的隱藏欄位資料未確實清除
- **Commit ID**: `afe91d70b9d6ee953bb4abeb88b42d40d9297fff`
- **作者**: 王鵬程
- **日期**: 2021-02-23 18:10:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 15. 修正集合快取無法移除或更新的異常(同時可讓58支持多AP)
- **Commit ID**: `1c750286f2f29294785b365cd746fe1dd141b9f4`
- **作者**: lorenchang
- **日期**: 2021-02-23 15:28:30
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/attachment/AttachmentDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinitionCmItem.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/ParticularRecord.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/ParticularRule.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/WorkCalendar.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/AbsImplementationType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/AbstractActivityDefContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ApplicationDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CompositeType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ConditionDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/DecisionCondition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/DecisionLevel.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/DecisionRuleList.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/Implementation.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessPackage.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessPackageCategory.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessPackageCmItem.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/RedefinableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/StrategyAssignDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/decision_sharing/DecisionConditionSharing.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/decision_sharing/DecisionLevelSharing.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/decision_sharing/DecisionRuleListSharing.java`

### 16. [Web]Q00-20210107002 修正從待辦連結進入BPM簽核該流程實例，簽核後無法導向下一個簽核流程項目
- **Commit ID**: `c3854b8b746fea66171181ce500c1e6a922b614b`
- **作者**: 林致帆
- **日期**: 2021-02-23 14:46:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 17. [BPM APP]調整企業微信的簽核歷程依系統變數設定作排序
- **Commit ID**: `9999ed92e91b5deeb4cfe289fb2a6ed34e52ecb0`
- **作者**: cherryliao
- **日期**: 2021-02-23 10:26:32
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 18. [Web]Q00-20210222001 修正barcode的值有某些特殊符號會導致掃描後資料缺少或異常
- **Commit ID**: `329652790e4deee39b9ecebdc423f18148ad29e1`
- **作者**: 王鵬程
- **日期**: 2021-02-22 17:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/BarcodeElement.java`

### 19. [WorkFlow][YIFE]A00-20201211001 取得單別單號記錄時未用公司別區分，導致有同單別單號取錯資料回傳
- **Commit ID**: `f92ed4b3c74dcf381c1da9ad4292517cfdd91631`
- **作者**: walter_wu
- **日期**: 2021-02-22 15:32:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 20. [Web]A00-20210203001 修正BPM首頁的待辦清單，除了第一筆流程實例，其他流程實例點擊進入都沒有"處理下個工作按鈕"[補修正]
- **Commit ID**: `f1ff12332223983ae809a999dc90348d7f60baf0`
- **作者**: 林致帆
- **日期**: 2021-02-22 15:14:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 21. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `03e501fa3ce11e6c5a3a9a4d647413f2ef1166df`
- **作者**: 林致帆
- **日期**: 2021-02-22 14:58:06
- **變更檔案數量**: 0

### 22. [Web]A00-20210203001 修正BPM首頁的待辦清單，除了第一筆流程實例，其他流程實例點擊進入都沒有"處理下個工作按鈕"
- **Commit ID**: `092bab6d2db6bd76dd16e5bae566afd38c50ea57`
- **作者**: 林致帆
- **日期**: 2021-02-22 14:57:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 23. [BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]
- **Commit ID**: `b3f2b89f76d747c15c1b0e3551859dd6c9a048be`
- **作者**: cherryliao
- **日期**: 2021-02-22 13:34:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 24. [BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]
- **Commit ID**: `cea39a3a2b4d3ddb7e47845e497cea25c6436a8d`
- **作者**: cherryliao
- **日期**: 2021-02-22 12:03:17
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmWorkItemDataVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 25. [BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]
- **Commit ID**: `b7c9af190706c0d2284604a41beced83569c5b93`
- **作者**: 詩雅
- **日期**: 2021-02-22 11:29:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 26. [BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能
- **Commit ID**: `d0cb39b084697e224582bb3cd6ab88f33180fc31`
- **作者**: yamiyeh10
- **日期**: 2021-02-22 10:59:47
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 27. [E10]S00-20210105001 新增E10整合表單在行動端上支援E10表單過濾主鍵欄位功能[補]
- **Commit ID**: `2ae4915dcfac26725e94c6e6538932a303af8230`
- **作者**: cherryliao
- **日期**: 2021-02-20 09:43:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 28. [E10]S00-20210105001 新增E10整合表單在行動端上支援E10表單過濾主鍵欄位功能
- **Commit ID**: `380142de726b23e6d2a193b652aa919febedc428`
- **作者**: yamiyeh10
- **日期**: 2021-02-19 19:51:39
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormDefinitionJSONTransfer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 29. [流程引擎]A00-20201214001 修正流程處理->系統通知沒有「轉由他人處裡」的通知
- **Commit ID**: `f3574b55544550287458ee027cfeda872f18a927`
- **作者**: yanann_chen
- **日期**: 2021-02-19 16:08:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 30. [T100]修正 T100出貨表單axmt500單身裡的欄位Id有重覆
- **Commit ID**: `c8f18aa56a0bb068dccfabbe9e21122679064c83`
- **作者**: 林致帆
- **日期**: 2021-02-17 10:28:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form-default-t100.zip`

### 31. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `7ed6df4ad255597ebf7041317083329244a57a9b`
- **作者**: 林致帆
- **日期**: 2021-02-08 15:08:56
- **變更檔案數量**: 0

### 32. [流程設計師]A00-20210201002 修正 複製一般關卡，流程在該複製的關卡加簽，加簽完會直接結案
- **Commit ID**: `6d382ec0b191a11c8f98a21ad94fe549fa93aa2f`
- **作者**: 林致帆
- **日期**: 2021-02-08 15:02:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramAction.java`

### 33. [Web]A00-20210204001 修正系統多語系維護作業，當「設定值」內容有大寫字母，進入編輯時多語系Grid無資料
- **Commit ID**: `93e7f2c7c72beaffb34fc04e0af626b0841af505`
- **作者**: waynechang
- **日期**: 2021-02-05 17:34:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java`

### 34. [BPM APP]S00-20210111002 調整LINE推播訊息依照流程關卡中的允許批次簽核來卡控是否顯示直接簽核按鈕
- **Commit ID**: `ed3a391a6f35f7b0f637cb01ba736d6f555e015a`
- **作者**: cherryliao
- **日期**: 2021-02-05 17:18:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 35. [BPM APP]Q00-*********** 調整在Line整合方案時行動簽核管理中心的移動消息訂閱管理頁面改撈取所有流程機制
- **Commit ID**: `5ab5b6909ea26afa814a5f75eb9dd3e2f1dd2cbf`
- **作者**: yamiyeh10
- **日期**: 2021-02-05 16:11:02
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SystemConfigManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAllProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 36. [流程設計工具]Q00-20210205002 修正某些個人環境操作時會出現畫面錯亂的異常
- **Commit ID**: `92bf3c90387495c9c2a8d7bfad1747a99dd9cee1`
- **作者**: lorenchang
- **日期**: 2021-02-05 15:06:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/DesignerMainApp.java`

### 37. [流程引擎]Q00-20210205001 麗智電子客製-核決層級解析邏輯調整
- **Commit ID**: `06a1afd5e96bbb854b0620867d741cd2fea9efb0`
- **作者**: waynechang
- **日期**: 2021-02-05 13:52:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`

### 38. [DotJ]Q00-20210204004 修正修正如果流程有核決權限表，送簽會出現LazyInitializationException
- **Commit ID**: `31f151a996aae3beae633c9f1af66f62745945ad`
- **作者**: lorenchang
- **日期**: 2021-02-04 18:06:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 39. [DotJ]Q00-20210204003 修正取得BPM流程圖出現NullPointerException
- **Commit ID**: `0fb6c811b3e6e7b79c8495529a16af8d6cdb6f2e`
- **作者**: lorenchang
- **日期**: 2021-02-04 17:51:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceLocator.java`

### 40. [Web]A00-20210202001 Portal導向BPM，BPM首頁左側功能列語系與Portal傳過來的語系不相同
- **Commit ID**: `2abe1002f9f122fc95042fbbc377f65d272473c4`
- **作者**: 林致帆
- **日期**: 2021-02-04 17:34:08
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProgramDefinitionDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 41. [流程引擎]Q00-20210204002 修正已封存的工作通知清單中查不到已閱讀的工作通知
- **Commit ID**: `878e03cc562d208a2757d872b1d5df50ebfefbca`
- **作者**: yanann_chen
- **日期**: 2021-02-04 16:45:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 42. [Web]Q00-20210204001 進度條載入優化，減少卡頓的感受改為等速載入
- **Commit ID**: `e2ccd098ba3bdd16f2f81ba34ee4c6abd15aa4f9`
- **作者**: lorenchang
- **日期**: 2021-02-04 15:21:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 43. [BPM APP]Q00-20210201004 修正入口平台整合設定中其他工具佈署網址頁面中資料過多時無scrollbar問題
- **Commit ID**: `b2a8c5cdedb05e961a36c92f62aee8a2e02b36be`
- **作者**: cherryliao
- **日期**: 2021-02-03 17:31:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp`

### 44. [組織設計師]A00-*********** 修正人員有任職部門主管並卸任主管職位後，需重啟BPM才能設為離職
- **Commit ID**: `3796c27ce2620530f9aef9e8e55939470018cca2`
- **作者**: 王鵬程
- **日期**: 2021-02-03 14:34:37
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`

### 45. [系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]
- **Commit ID**: `5301c117f19e9519ab14a9f11760e42c90c17c7e`
- **作者**: cherryliao
- **日期**: 2021-02-03 12:15:39
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/onlinemgt/OnlineUserMgtPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_TW.properties`

### 46. [Web]A00-20210201001 調整表單浮點數欄位運算邏輯
- **Commit ID**: `d5bf6a8db6f6eb655fd18a1e3765823e76384b52`
- **作者**: yanann_chen
- **日期**: 2021-02-03 11:50:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 47. [組織同步]Q00-20210129001 修正組織同步StepTtls跟StepRoles的IsRemoveData設為true，同步後不會移除相關資料的異常
- **Commit ID**: `1b105cbffca2745466050688af15dfbb9e0d53ff`
- **作者**: lorenchang
- **日期**: 2021-02-03 11:45:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 48. Revert "[Web]A00-20210201001 修正在Chrome上的浮點數四捨五入計算結果與在IE上的不一致"
- **Commit ID**: `b20a86db6eb54f3ff7e048ff4d4e3a49b70fc365`
- **作者**: yanann_chen
- **日期**: 2021-02-03 10:33:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 49. [Web]A00-20210201001 修正在Chrome上的浮點數四捨五入計算結果與在IE上的不一致
- **Commit ID**: `82036b385a5577a5bf10e2deb12e53b4c24d8675`
- **作者**: yanann_chen
- **日期**: 2021-02-02 15:16:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 50. [BPM APP]Q00-20210120002 修正IMG推播消息為動態選染表單時加提醒後於行事曆查看顯示非動態渲染表單畫面的問題
- **Commit ID**: `1038e26b4f94bb7154f36fc1f05c408299bbd2a4`
- **作者**: cherryliao
- **日期**: 2021-02-02 14:40:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java`

### 51. [BPM APP]Q00-20210201005 修正IMG的中間層與動態渲染表單新增明後天提醒後回應的資訊無多語系問題
- **Commit ID**: `8d3f10ff205ec0492e9d2c1057e344545e56b313`
- **作者**: yamiyeh10
- **日期**: 2021-02-02 10:30:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 52. [內部]補上被覆蓋掉的多語系
- **Commit ID**: `a3bcabaa76929b2eb2a63ff93cbb77c99e12b40a`
- **作者**: yamiyeh10
- **日期**: 2021-02-01 17:21:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 53. [BPM APP]Q00-20210201006 調整入口平台整合設定的其他工具佈署網址中按鈕沒有對應提示訊息問題
- **Commit ID**: `2583c04c4b0fdbcdac614722d707ea225009f937`
- **作者**: yamiyeh10
- **日期**: 2021-02-01 16:41:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp`

### 54. [Web]Q00-20210201002 隱藏IE預設的清除按鈕
- **Commit ID**: `cffc496c1ae57e0ae4ade06c4c914889ed65b4c7`
- **作者**: yanann_chen
- **日期**: 2021-02-01 15:37:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 55. [Web]A00-20210128001 修正 外部連結-追蹤流程實例內單一表單資料，點擊列印鍵沒有反應
- **Commit ID**: `bef009cf69e8c6626b754355621a06c272c3976c`
- **作者**: 林致帆
- **日期**: 2021-02-01 14:17:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

### 56. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
- **Commit ID**: `f510bad79313701322667f1a8ae314fd8c0018a6`
- **作者**: yamiyeh10
- **日期**: 2021-02-01 10:34:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`

### 57. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
- **Commit ID**: `0d6d5e9c7066b62a65d93eafa0df90d5071318e3`
- **作者**: 詩雅
- **日期**: 2021-01-29 20:17:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`

### 58. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補] 1. 調整待辦列表 2. 調整通知列表 3. 統計元件筆數
- **Commit ID**: `196a2fb53e6e7d9c7f98d0912083b2b0d6e10717`
- **作者**: 詩雅
- **日期**: 2021-01-29 19:29:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 59. [內部] 更新越南語系
- **Commit ID**: `20d8e3a9326bfde29c5583d847ade9761f3d70cf`
- **作者**: lorenchang
- **日期**: 2021-01-29 16:28:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 60. [E10]新增E10整合表單同步時自動產生行動端Grid載入語法[補]
- **Commit ID**: `3353e73a2ebd40f352ee796def11f73617fce80a`
- **作者**: cherryliao
- **日期**: 2021-01-29 11:12:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`

### 61. [E10]新增E10整合表單同步時自動產生行動端Grid載入語法
- **Commit ID**: `a38a25d0cdc19ae8a4effa23beb614ca91827d0b`
- **作者**: yamiyeh10
- **日期**: 2021-01-29 10:16:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormDefinitionJSONTransfer.java`

### 62. [WEB]S00-20201202001 調整流程管理-作業程序書的「使用表單」欄位的樣式為垂直置中
- **Commit ID**: `57a49c4fc94f40b03febcb660c597bd387afbf40`
- **作者**: waynechang
- **日期**: 2021-01-28 15:09:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`

### 63. [Web]A00-20201022001 修正流程主旨中最後是\的符號，會導致待辦、監控、追蹤流程的清單頁會無法開啟
- **Commit ID**: `d2dabb63c3a66bfe1ee1c9b30b0ba82901f7a69f`
- **作者**: 王鵬程
- **日期**: 2021-01-28 13:59:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`

### 64. [BPM APP]調整行動端詳情簽核派送時可依系統變數設定啟用檢查簽核意見是否為空功能
- **Commit ID**: `03c019bd76c95c313ffbdb605bfcec40292b6fd9`
- **作者**: yamiyeh10
- **日期**: 2021-01-28 10:10:16
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 65. [流程引擎]Q00-20210127002 修正組織同步後WorkCalendar.containerOID被清空
- **Commit ID**: `4b16243c9595b430d717fda3357d37a4ce86c5c9`
- **作者**: yanann_chen
- **日期**: 2021-01-27 18:43:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Organization.java`

### 66. [T100]回寫T100服務任務關卡回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單回到「人員處理」關卡，將訊息顯示在簽核畫面上
- **Commit ID**: `a1964ea72903a0185e8f03ae9d59e76723087c56`
- **作者**: 林致帆
- **日期**: 2021-01-27 17:10:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPILocal.java`

### 67. [流程設計師]A00-20210119002 修正流程的頭兩個關卡將Dialog元件設為invisible，派送到第三關會報錯
- **Commit ID**: `878e0f732379bd2e67075ec1776bbd8ff04c3a95`
- **作者**: 王鵬程
- **日期**: 2021-01-27 16:48:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 68. [流程引擎]A00-20210126003 修正表單人員開窗設定過濾條件後以"人員名稱"為條件查詢無效
- **Commit ID**: `f539b942db9dc8b038134046e45c069c2a52745b`
- **作者**: yanann_chen
- **日期**: 2021-01-27 15:45:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`

### 69. [Web]A00-20210114001 修正追蹤流程連結打開Tiptop流程時無法下載URL附件
- **Commit ID**: `70f4c828118e63a6f0872db985d7a76b61460ddd`
- **作者**: yanann_chen
- **日期**: 2021-01-26 17:39:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

### 70. [WEB]A00-20210112002 修正流程處理追蹤流程處理的流程 Grid清單，點擊時間排序會發生異常
- **Commit ID**: `75402b56249808072015247a3c2570fc5eae020b`
- **作者**: waynechang
- **日期**: 2021-01-26 15:41:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 71. [流程設計師]A00-20210122001 修正XPDL流程中有空白子流程時，無法轉換為BPMN流程
- **Commit ID**: `a6db0ee8f503e73b69e282d9475687d64d36bb00`
- **作者**: yanann_chen
- **日期**: 2021-01-26 11:54:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java`

### 72. [Web]A00-20210114003 修正在帳號管理頁面，將頁面顯示筆數選大於10，會無法切換下一頁和上一頁
- **Commit ID**: `12f324129c08c6a8bc247af3f49df5bde41edcf8`
- **作者**: 王鵬程
- **日期**: 2021-01-25 17:54:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/UserManageAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp`

### 73. [流程引擎]A00-20210121001 修正追蹤流程>已轉派工作，已處理"與全部頁籤中模糊查詢功能無效異常
- **Commit ID**: `653ebe8f1910c39b46b73daa871cb0bbe72f28ed`
- **作者**: yanann_chen
- **日期**: 2021-01-25 17:29:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 74. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
- **Commit ID**: `d10e357fc8d8846cf5562fe41566427c1814a2c1`
- **作者**: cherryliao
- **日期**: 2021-01-25 14:17:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 75. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
- **Commit ID**: `f2872fe92b22169558a52d44720950f0d3c7abb8`
- **作者**: pinchi_lin
- **日期**: 2021-01-25 11:11:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 76. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
- **Commit ID**: `907e40ec610cec37c69215645c60c14ae1cf120b`
- **作者**: yamiyeh10
- **日期**: 2021-01-25 10:33:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 77. [Web]Q00-20210122001 將Date元件出現提示訊息的字體顏色調整成紅色
- **Commit ID**: `4a39493a1ce16d8eaa752c8aa5cbac9986aded92`
- **作者**: 王鵬程
- **日期**: 2021-01-22 18:14:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 78. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
- **Commit ID**: `8b491560b769f8fb60f393d0d7d70592d334911c`
- **作者**: pinchi_lin
- **日期**: 2021-01-22 12:51:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 79. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定
- **Commit ID**: `bad7308c7fbdedfa0832493f7291944d3d81c036`
- **作者**: pinchi_lin
- **日期**: 2021-01-21 18:58:12
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 80. [流程引擎]A00-20210118002 修正關注流程參考表單欄位時，若舊版表單沒有該欄位，則已發起的舊版表單流程無法往下派送
- **Commit ID**: `700df16bc0b08271dce78c05d50512ab82e8f4b3`
- **作者**: yanann_chen
- **日期**: 2021-01-21 10:55:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/ConditionEvaluator.java`

### 81. [Web]A00-20210118001 修正進入一筆待辦並填寫簽核意見後，點選回到工作清單鈕，再進入待辦任一流程簽核意見沒被清除
- **Commit ID**: `ba6c0d9277a30496eb0b8eb17dbdc3d42f4c8031`
- **作者**: 王鵬程
- **日期**: 2021-01-20 18:10:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 82. [流程引擎]A00-20201224001 修正表單開窗元件設定過濾條件無效異常
- **Commit ID**: `a49f4b7a881a17b16f45a9c4a2caf33f28387863`
- **作者**: yanann_chen
- **日期**: 2021-01-20 15:55:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`

### 83. [系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]
- **Commit ID**: `d0b2319dc4b543a2f96a222c9e70b25fe26e1a55`
- **作者**: yamiyeh10
- **日期**: 2021-01-20 11:59:38
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/onlinemgt/OnlineUserMgtPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_TW.properties`

### 84. [系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]
- **Commit ID**: `295f295b4efaf8c25275c05a17bb36d57bd9bc85`
- **作者**: cherryliao
- **日期**: 2021-01-20 11:07:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/license/ConnectedUserInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/onlinemgt/OnlineUserMgtPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 85. [Web]Q00-20210120001 修正administrator登入行動版，左方選單會出現BPM首頁
- **Commit ID**: `7f518bc09f4bc3ab87db68270b5a4d75bf632d27`
- **作者**: 王鵬程
- **日期**: 2021-01-20 11:03:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 86. [BPM APP]調整IMG用的接口支持驗證BPM的access token
- **Commit ID**: `1263973a05982a2745eb69b21d8aae3368769048`
- **作者**: pinchi_lin
- **日期**: 2021-01-20 09:58:12
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleModuleFeatures.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 87. [Web]Q00-20210119003 修正絕對位置表單在更換image後，在預覽時顯示圖片不正確
- **Commit ID**: `5f528ab3d7b05ea1696d02d4e954477477a4ea77`
- **作者**: 王鵬程
- **日期**: 2021-01-19 17:28:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ImageElement.java`

### 88. [流程引擎]S00-20201214001 簡易流程圖的關卡中若有部分處理者已離職，則不顯示已離職的人員
- **Commit ID**: `9d3da981c75bb7c4633300443389661930cea69a`
- **作者**: yanann_chen
- **日期**: 2021-01-19 17:23:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 89. [系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位
- **Commit ID**: `9c79b28b7abfe03172b292573ef7b60f9ab334b1`
- **作者**: 詩雅
- **日期**: 2021-01-19 15:49:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 90. [流程引擎]Q00-20210115002 修正在組織設計師刪除某部門時，若該部門有被其他物件參考，則提示相關內容
- **Commit ID**: `9770dec5776914c544a914c1330286e4a8b7853e`
- **作者**: yanann_chen
- **日期**: 2021-01-15 14:29:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/workflow_definition/IParticipantDefContainerDaoImpl.java`

### 91. [BPM APP]Q00-20210114003 修正行動簽核管理中心的使用者批次維護頁面選擇非Excel的檔案匯入時顯示錯誤訊息的多語系異常
- **Commit ID**: `79ca63601a7786458fa8a5fb822a77254cd0c23e`
- **作者**: yamiyeh10
- **日期**: 2021-01-14 19:07:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp`

### 92. [BPM APP]Q00-20210112001 修正授權中間層的批次匯入使用者無法使用新版的EXCEL匯入
- **Commit ID**: `6a412ba10dcb6124edc44d2a9863c6663b281660`
- **作者**: yamiyeh10
- **日期**: 2021-01-14 19:01:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserImport.jsp`

### 93. [BPM APP]Q00-20201231001 修正IMG已結案流程應用中處理的流程其流程名稱篩選條件筆數異常問題
- **Commit ID**: `9e0af2ad8648e17725c3bbb427136e04cf393252`
- **作者**: pinchi_lin
- **日期**: 2021-01-14 16:25:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 94. [BPM APP]Q00-20201231001 調整行動簽核管理中心的智能管理中心顯示機制
- **Commit ID**: `5967dc71515241a1393f93924b6f9dad1d4e2faf`
- **作者**: yamiyeh10
- **日期**: 2021-01-14 15:09:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 95. [WEB]Q00-20210114002 修正維護樣板QueryTemplate.js，當設定為雙欄顯示，且查詢條件數量為單數時，畫面會排版異常
- **Commit ID**: `aff2393be521e4b95f4ec7114dc127b3e0c2a1b1`
- **作者**: waynechang
- **日期**: 2021-01-14 14:56:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 96. [BPM APP]Q00-20201223003 修正IMG的明天提醒與後天提醒英文語系拼錯問題
- **Commit ID**: `f694b56e9e3d3a3b80dbd00695334c1e3080e8d3`
- **作者**: yamiyeh10
- **日期**: 2021-01-14 11:17:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 97. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `de55414d76cfb31f91abcd07ae8912a3238156d5`
- **作者**: 林致帆
- **日期**: 2021-01-13 18:42:13
- **變更檔案數量**: 0

### 98. [流程引擎]Q00-20210113002 人員名稱有特殊字導致開啟ESS表單報錯
- **Commit ID**: `6a96790d8e66990c346ce0eba022cae478f7ee4d`
- **作者**: 林致帆
- **日期**: 2021-01-13 18:40:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 99. [BPM APP]Q00-20201222001 修正鼎捷移動APP上的部份應用未支持多語系的問題
- **Commit ID**: `6613f73620a849256239143cc7fc46b0174e0981`
- **作者**: yamiyeh10
- **日期**: 2021-01-13 18:40:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 100. [Web]Q00-20210113001 修正編輯系統排程後，系統仍按照編輯前的設定內容執行排程
- **Commit ID**: `85f097219d00051e6c5adb61f2cf8f5a500c0191`
- **作者**: yanann_chen
- **日期**: 2021-01-13 18:25:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SystemScheduleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`

### 101. [流程引擎]A00-20210105001 修正經過invoke關卡後，流程主旨參數內容備替換成英文語系的內容
- **Commit ID**: `decf52630011d4cb47db5b9cac2ada41648ee19e`
- **作者**: yanann_chen
- **日期**: 2021-01-13 15:12:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 102. [BPM APP]Q00-20210104002 調整IMG在動態渲染表單作連續簽核時下一筆流程表單沒同步時會異常問題[補]
- **Commit ID**: `891ea01bec802cc3d81d72c3df73b97328b8da2d`
- **作者**: pinchi_lin
- **日期**: 2021-01-12 19:07:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 103. [BPM APP]Q00-20210104002 調整IMG在動態渲染表單作連續簽核時下一筆流程表單沒同步時會異常問題
- **Commit ID**: `3ebdc122de46db2223913fa5da4d46ee2fb92e41`
- **作者**: pinchi_lin
- **日期**: 2021-01-12 18:42:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 104. [Web]Q00-20210112002 日期元件有勾選顯示時間，在開日期窗未選擇日期時按下確定會出現提示訊息
- **Commit ID**: `ceea5cdeecfb85204708af3776235f26d6f288e1`
- **作者**: 王鵬程
- **日期**: 2021-01-12 16:14:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 105. [BPM APP]Q00-20201231003 修正在IE和Safari瀏覽器頁面整合授權中間層時可選擇隱藏的授權類型問題
- **Commit ID**: `27ad9af053135f7af75210b033a594f2f0f6d1f8`
- **作者**: yamiyeh10
- **日期**: 2021-01-12 15:51:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js`

### 106. [BPM APP]Q00-20210104001 調整入口平台整合設定中測試連線狀態若出現錯誤訊息時會跑版問題
- **Commit ID**: `e156eaf3ab83a618cf4538bebc23d24e25c286f0`
- **作者**: yamiyeh10
- **日期**: 2021-01-12 14:44:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 107. [BPM APP]S00-20200514001 調整行動端發起/簽核時的選擇部門樣式
- **Commit ID**: `e5d5995733a4158ec6fdf3573f6244cc79b1c166`
- **作者**: cherryliao
- **日期**: 2021-01-11 19:09:40
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobileSelect.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 108. [BPM APP]C01-20201215003 調整追蹤狀態下表單的全域變數viewMode參數值設定為"TRACE"
- **Commit ID**: `ecf00b83116579bb5d43f6f3c9b22b32e016882f`
- **作者**: 詩雅
- **日期**: 2021-01-11 13:41:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileResigendServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 109. [流程引擎]A00-20210107002 修正WebServie getFormFieldTemplate，複合元件若為自定義開窗時無法填值
- **Commit ID**: `3baac870834566dc24e79ad521df97befbaec2b5`
- **作者**: yanann_chen
- **日期**: 2021-01-08 11:50:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java`

### 110. [Web]Q00-20210107001 修正流程發起時，若無法解析流程關係人，未呈現相關提示訊息
- **Commit ID**: `920dd6240b422acac66ae4c1d638a2102e49351f`
- **作者**: yanann_chen
- **日期**: 2021-01-07 10:50:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 111. [Web]Q00-20210105001 修正Date元件有勾選顯示時間時，日期選擇窗有換頁過就無法手動輸入時間
- **Commit ID**: `b62a031c372701d79501202557a2a3355052b5dd`
- **作者**: 王鵬程
- **日期**: 2021-01-05 18:13:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 112. [BPM APP]C01-20201230003修正當瀏覽器為IE時，操作整合頁面Scrollbar無法滾動問題(含授權中間層&入口平台整合設定頁面)
- **Commit ID**: `d92aeb62e09ea9dcd60a8afaaff2f2f7bd2731d0`
- **作者**: 詩雅
- **日期**: 2021-01-05 16:26:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 113. [Web]A00-20210104001 修正使用HRM Portlet進入BPM追蹤流程，執行撤銷流程時無法填寫撤銷理由
- **Commit ID**: `f6864abdd6cdcbe0d6ae2fd380b670922a6eba8e`
- **作者**: yanann_chen
- **日期**: 2021-01-05 10:55:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 114. [Web]A00-20201215002 修正使用formTerminateSave來終止流程時，運作邏輯的順序不正確
- **Commit ID**: `c463270cc6e4a5cd3e20555d8e31414bc50bb223`
- **作者**: 王鵬程
- **日期**: 2021-01-04 18:16:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 115. [BPM APP]A01-20210104002 修正整合LINE的部分用戶無法收到系統排程BPM首頁通知推送的問題
- **Commit ID**: `de641fb72c5791627940467bd2e5a0552e4fbe4e`
- **作者**: cherryliao
- **日期**: 2021-01-04 17:55:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`

### 116. [流程引擎]Q00-20210104003 修正WebService接口getFormFieldTemplate回傳的值與實際儲存的結果不一致
- **Commit ID**: `901b3dc6a409d87377142af1c6f53e57cf3eedc5`
- **作者**: yanann_chen
- **日期**: 2021-01-04 17:32:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java`

### 117. [內部]調整排版與移除不必要的部分
- **Commit ID**: `681d579ed36e98dd534ebc71d1bc82941d0c64de`
- **作者**: pinchi_lin
- **日期**: 2021-01-04 15:04:23
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogAspect.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceBpm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`

### 118. [Web]Q00-20200806002 產品回收NTKO專案[補]
- **Commit ID**: `05a2cffff930ad3508afe70df42532ba6621a4c8`
- **作者**: waynechang
- **日期**: 2020-12-31 17:06:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 119. [流程設計師]A00-20201222001 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞
- **Commit ID**: `f5e167a254dd4b5b1c46044d87482815455498db`
- **作者**: walter_wu
- **日期**: 2020-12-31 16:07:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 120. Revert "[流程設計師]A00-20180129002 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞"
- **Commit ID**: `bc65402c5b580abe9af64fed7a898e05783f216c`
- **作者**: walter_wu
- **日期**: 2020-12-31 16:04:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 121. [流程設計師]A00-20180129002 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞
- **Commit ID**: `3526271b56c319197930716dca03a2142b508e52`
- **作者**: walter_wu
- **日期**: 2020-12-31 16:01:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 122. [流程引擎]Q00-20201231002 修正Radio/CheckBox元件設定"最後一個選項額外產生輸入框"，轉存表單未將輸入值存入資料庫
- **Commit ID**: `dc5f4bcd57648baccdcbfb2e7ea6dd1c2ce1927a`
- **作者**: yanann_chen
- **日期**: 2020-12-31 15:34:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 123. [BPM APP]Q00-20201228002 修正詳情表單加提醒時，若有同步動態渲染表單，在行事曆查看流程時可看到動態渲染表單[補]
- **Commit ID**: `f75c547a071c68d67d9239dede95b3f54553ff73`
- **作者**: cherryliao
- **日期**: 2020-12-31 13:17:19
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 124. [Web]Q00-20201229002 表單有元件使用資料註冊器時，資料註冊器沒有任何一筆資料的狀況下，開啟表單會異常
- **Commit ID**: `311f359f3de85e3b781d22183db35c231ad11e31`
- **作者**: 林致帆
- **日期**: 2020-12-29 17:49:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 125. [流程引擎]Q00-20201229001 調整進入程式權限設定清單時清除殘存資料的SQL，以提高系統執行效能
- **Commit ID**: `f23100fa02f69e9568caa7623018bc3f58a5c262`
- **作者**: yanann_chen
- **日期**: 2020-12-29 14:21:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 126. [BPM APP]Q00-20201228002 修正詳情表單加提醒時，若有同步動態渲染表單，在行事曆查看流程時可看到動態渲染表單
- **Commit ID**: `ed174c5d3dd3e3b4f3fa0e1b6590d3feb7141d47`
- **作者**: 詩雅
- **日期**: 2020-12-28 18:31:03
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 127. [ESS]新增ESS整合表單:ESSF50B班次變更(多人多天)
- **Commit ID**: `7ecd591a7716e0df328923c336fab13c81043fbf`
- **作者**: yamiyeh10
- **日期**: 2020-12-28 17:36:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\346\227\227\350\211\246/ESSF50B\347\217\255\346\254\241\350\256\212\346\233\264(\345\244\232\344\272\272\345\244\232\345\244\251).form"`

### 128. [Web]Q00-20201228001 修正系統排程設定頁面中，排程生效時間下拉元件太窄導致內容無法完整顯示
- **Commit ID**: `fb9986bcae2d4e7fe56f3ea711c7721596be830f`
- **作者**: 王鵬程
- **日期**: 2020-12-28 12:04:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`

### 129. [BPM APP]Q00-20201225004 修正入口平台整合設定維護作業存在連線資訊時用手機操作其畫面不會顯示任何資訊問題
- **Commit ID**: `d7d610ea46ca2d21e6b0461b50681a15eddfeff8`
- **作者**: yamiyeh10
- **日期**: 2020-12-25 16:16:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 130. [Web]A00-20201217001 修正追蹤流程SQL，符合條件總筆數未達1000確未正確撈出
- **Commit ID**: `8b185876cdb6aa4f8df461b39084d28315456cf2`
- **作者**: walter_wu
- **日期**: 2020-12-25 15:48:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 131. [流程引擎]Q00-20201225003 進入程式權限設定清單時，清除刪除部門後所殘存的程式權限資料，避免維護作業發生異常，無法儲存修改的資料
- **Commit ID**: `0df38996bf7115d4185a466c1ff49d3d97f7017c`
- **作者**: yanann_chen
- **日期**: 2020-12-25 15:39:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 132. [流程引擎]Q00-20201225002 修正組織同步修改使用者資料後，使用者帳號狀態變更為"未啟用"
- **Commit ID**: `1a9bf3262c73223c5ff718ebadf02249ba14f55a`
- **作者**: yanann_chen
- **日期**: 2020-12-25 15:11:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 133. [BPM APP]Q00-20201222003 修正授權中間層管理維護作業存在連線資訊時用手機操作其畫面時不會顯示任何資訊問題
- **Commit ID**: `0e9638d2336075e161ca558426b5afa4378b5fff`
- **作者**: yamiyeh10
- **日期**: 2020-12-25 14:38:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js`

### 134. [BPM APP]Q00-20201225001 修正IMG動態渲染表單點明天後天提醒會失敗問題
- **Commit ID**: `a15da9abaa9fac0ce3583d0ec275b40eddc96b5a`
- **作者**: pinchi_lin
- **日期**: 2020-12-25 11:36:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 135. [流程引擎]A00-20201211004 調整DealOvertimeActivity的SQL，提升運行效率
- **Commit ID**: `d435133182724c5a0e31d0c299a5a351442730e7`
- **作者**: yanann_chen
- **日期**: 2020-12-24 14:48:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 136. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `c217c54aa9a98ed2718be3b7a3012b861685729e`
- **作者**: 林致帆
- **日期**: 2020-12-24 10:12:47
- **變更檔案數量**: 0

### 137. [內部]新增CRM表單-報價變更單
- **Commit ID**: `86deb73c7cf824af3f9188e2a7d8edea761ff59a`
- **作者**: 林致帆
- **日期**: 2020-12-24 10:12:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/form-default/\345\240\261\345\203\271\350\256\212\346\233\264\345\226\256.form"`

### 138. [內部]C01-20201208006 修正如果是留職停薪(intermissionDate)有可能導致離職人員作業維護出現異常(NullPointerException)
- **Commit ID**: `b43a1eb49f9d5512ba014563388ef320193967e7`
- **作者**: 王鵬程
- **日期**: 2020-12-23 18:24:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java`

### 139. [Web]A00-20201203001 修正當有類html tag時導致顯示異常，並將調整只開放a標籤，其餘直接顯示
- **Commit ID**: `722ba2912dbcbe0a5fd8d388ed0904b5ed611b9f`
- **作者**: 王鵬程
- **日期**: 2020-12-23 18:15:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 140. [內部]Q00-20201218002 ActionFilter　log訊息調整
- **Commit ID**: `518bb47128544a93961c12ee9ba61cfa3b78c9d5`
- **作者**: 林致帆
- **日期**: 2020-12-23 16:48:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 141. [流程引擎]Q00-20201120005 修正流程進行轉派任務後再取退回重辦(按流程定義退回)，派送的處理者不正確
- **Commit ID**: `1384f6c17a9f7dcb383773616a53a0d854334c59`
- **作者**: 林致帆
- **日期**: 2020-12-23 16:42:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 142. Revert "[流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確"
- **Commit ID**: `6c298ec562ccb194b21a88aa521d8892e9addfbe`
- **作者**: 林致帆
- **日期**: 2020-12-23 16:40:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 143. [流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確
- **Commit ID**: `a00f29cf4ac3bf4347de2173b31bcb20b144b8f1`
- **作者**: 林致帆
- **日期**: 2020-12-23 16:29:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 144. [Web]A00-20201103001 修正重新發起流程可以往前加簽的問題
- **Commit ID**: `4f69d59f6608b216ce7d90cdb6ecc25517f70488`
- **作者**: walter_wu
- **日期**: 2020-11-30 17:59:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 145. [Web]C01-20201130004 修正點已經被簽過的Email簽核連結進入確可以再簽一次的錯誤
- **Commit ID**: `7db0349bad0208ed36ab3b03184f01e77af93db5`
- **作者**: walter_wu
- **日期**: 2020-12-23 16:00:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`

### 146. [Web]A00-20201201001 修正不是在代辦清單或追蹤清單頁的其他頁面，點選右上角的單筆代辦直接進入代辦，之後按返回上一頁畫面會卡死
- **Commit ID**: `e88ff378752a4afb8bc7b60b1e0cc9b0e1919d8c`
- **作者**: walter_wu
- **日期**: 2020-12-23 15:47:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 147. Q00-20201223002 修正從E10取得附件接口，在Invoke參數Id設定錯誤的狀態下並未拋錯，導致未取得附件流程就往下走
- **Commit ID**: `0f641ed98d88ca942a79c2e04fabdada6b53d792`
- **作者**: walter_wu
- **日期**: 2020-12-23 15:37:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

