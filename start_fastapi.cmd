@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ==========================================
echo    BPM Easy Tools - FastAPI 版本啟動
echo ==========================================
echo.

:: 檢查是否在專案目錄
if not exist "app\main.py" (
    echo ❌ 錯誤：找不到 app\main.py 檔案
    echo 請確認您在正確的專案目錄中執行此腳本
    echo.
    pause
    exit /b 1
)

:: 檢查虛擬環境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 錯誤：找不到虛擬環境
    echo 請先執行 setup_environment.cmd 建立虛擬環境
    echo.
    pause
    exit /b 1
)

:: 啟動虛擬環境
echo 🔧 啟動虛擬環境...
call venv\Scripts\activate.bat

:: 檢查 FastAPI 是否已安裝
python -c "import fastapi" 2>nul
if errorlevel 1 (
    echo ❌ 錯誤：FastAPI 未安裝
    echo 正在安裝必要的依賴套件...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 安裝依賴套件失敗
        pause
        exit /b 1
    )
)

:: 檢查埠號是否被佔用
netstat -an | findstr ":8888" >nul
if not errorlevel 1 (
    echo ⚠️  警告：埠號 8888 可能已被佔用
    echo 如果啟動失敗，請檢查是否有其他應用程式使用此埠號
    echo.
)

:: 顯示啟動資訊
echo ==========================================
echo 🚀 正在啟動 FastAPI 應用程式...
echo ==========================================
echo.
echo 📍 本機存取網址：http://localhost:8888
echo 📍 網路存取網址：http://0.0.0.0:8888
echo.
echo 💡 提示：
echo    - 按 Ctrl+C 停止應用程式
echo    - 應用程式支援熱重載，修改程式碼會自動重啟
echo    - API 文件：http://localhost:8888/docs
echo.
echo Note: The application is accessible from other devices on your network
echo Press Ctrl+C to stop the application
echo.

:: 啟動 FastAPI 應用程式
uvicorn app.main:app --host 0.0.0.0 --port 8888 --reload

if errorlevel 1 (
    echo.
    echo ==========================================
    echo Error: Failed to start the application
    echo ==========================================
    echo.
    echo Possible solutions:
    echo 1. Check if all dependencies are installed correctly
    echo 2. Try running setup_environment.cmd again
    echo 3. Make sure port 8888 is not being used by another application
    echo 4. Check if Windows Firewall is blocking the application
    echo 5. Verify that app\main.py exists and is correct
    echo.
    pause
    exit /b 1
)

echo.
echo ==========================================
echo Application stopped
echo ==========================================
pause
