{"比較資訊": {"專案ID": "NaNaXWeb", "倉庫路徑": "D:\\IDEA_workspace\\NaNaXWeb", "新分支": {"branch_name": "5.8.10.2_hotfix", "date": "2025-02-03 15:20:27", "message": "[PRODT]C01-20250123003 修正Web流程管理工具中同時開啟多個流程時，進行其中一個儲存動作後會導致流程無法正常關閉問題", "author": "yamiyeh10"}, "舊分支": {"branch_name": "5.8.10.2_202406261435_autobuild", "date": "2024-06-26 08:29:13", "message": "[文件智能家]修正排程更新 ISO 抛转记录文件狀態可能因為 dmcId 為 null 導致 NullPointerException 的異常", "author": "lorenchang"}, "比較時間": "2025-07-18 15:49:00", "新增commit數量": 13, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "cf100917c719ef17bdacbf4b4125327189b7180d", "commit_訊息": "[PRODT]C01-20250123003 修正Web流程管理工具中同時開啟多個流程時，進行其中一個儲存動作後會導致流程無法正常關閉問題", "提交日期": "2025-02-03 15:20:27", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a83bedafa74fbf29820a798a9a244a3f09b479e4", "commit_訊息": "[PRODT]C01-20241120001 修正Web流程管理工具中匯入流程時代號原本不存在，但修改流程代號後遇到已存在代號卻無法覆蓋的問題", "提交日期": "2024-11-21 17:03:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b74d2797568b77968befe3244f150c45f8a45cdb", "commit_訊息": "[流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)", "提交日期": "2024-11-07 11:00:59", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ProcessArchiveModule/util/init/TimeScheduleInitializer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "245019dd8b31b4ba69ee2f655213449e82b7de4d", "commit_訊息": "[PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題", "提交日期": "2024-10-29 15:14:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05e661484afcd39a18d0430d42b09f35264cb1d0", "commit_訊息": "[流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)", "提交日期": "2024-10-28 10:58:05", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "368a57890a882d339ad96b7a904f680682688cef", "commit_訊息": "[流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log", "提交日期": "2024-10-04 17:16:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "67540bc441eedc75db76b39bc4db025bc0d0b9a2", "commit_訊息": "[流程封存] C01-20240506005 調整流程封存維護作業日期儲存計算方式", "提交日期": "2024-07-12 10:23:15", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f8dfd08cafab9191cfc1a3ccc4ddbddc75c75b69", "commit_訊息": "[PRODT]C01-20241008003 修正Web流程管理工具中連接線名稱在儲存後會消失的問題", "提交日期": "2024-10-09 14:06:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e8221c9cc70810c192b1342198c660b0a61f906", "commit_訊息": "[PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況", "提交日期": "2024-09-26 08:21:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "393a292592e303efbeee5f81121ca499941c65dd", "commit_訊息": "[PRODT]Q00-20240626002 修正Web流程管理工具中關卡與連接線存在髒資料卻無提示的問題", "提交日期": "2024-09-20 09:03:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6bd4962bfc06b49fb4c0d214d18b278e95da4aef", "commit_訊息": "[PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題", "提交日期": "2024-06-25 17:05:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/bpmn-auto-layout/dist/index.cjs", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "422489779ba9fdd1ecd58ec1cb3da942836d8e50", "commit_訊息": "[內部]A00-20240625001 NG-Zorro套件引入越南語系", "提交日期": "2024-06-26 10:21:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/CommonProgramModule/src/app/app.module.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/app.module.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ProcessArchiveModule/src/app/app.module.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0df1989eb0bdeecdc178d3fe4f18fa2104fdb762", "commit_訊息": "[PRODT]Q00-20240828001 調整Web流程管理工具中註解元件無法被拖拉的問題", "提交日期": "2024-09-03 15:42:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-custom/customRules.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}