{"company_id": "06100510", "company_name": "美康健康", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06100510_美康健康\\01客戶基本資料", "files": [{"filename": "連線資訊.txt", "raw_content": "VPN：\r\nFortiClient\r\n遠程網關：***************\r\n自定義端口：10443\r\n帳號：dsc\r\n密碼：1qaz@WSX\r\n\r\n版本:********\r\n\r\n測試機AP\r\n192.168.2.45\r\n遠端:administrator/admin#DSC\r\nhttp://192.168.2.45:8086/NaNaWeb/\r\nBPM網頁密碼:1234\r\n\r\n正式機AP+DB主機\r\n************\r\n遠端:administrator/admin#DSC\r\nhttp://************:8086/NaNaWeb/\r\nBPM網頁密碼:1234\r\n\r\nDB:sa/sql#DSC\r\n\r\n02 8994-2121 #195\r\n許文瑄\r\n<EMAIL>", "structured_data": {"遠程網關": "***************", "自定義端口": "10443", "username": "dsc", "password": "1qaz@WSX", "version": "********", "遠端": "administrator/admin#DSC", "http": "//************:8086/NaNaWeb/", "bpm網頁密碼": "1234", "database": "sa/sql#DSC", "host": "***************"}, "source_path": "C1.客戶維護相關\\06100510_美康健康\\01客戶基本資料\\連線資訊.txt", "file_size": 446, "encoding_used": "utf-8", "processed_at": "2025-08-26T10:46:29.209274"}], "total_files": 1, "processed_at": "2025-08-26T10:46:29.209281"}