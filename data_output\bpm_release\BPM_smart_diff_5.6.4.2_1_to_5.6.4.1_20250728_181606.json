{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.6.4.2_1", "date": "tag 5.6.4.2_1\nTagger: 張容倫 <<EMAIL>>\n\n2017-09-21 11:29:48", "message": "T100費用核銷模組 :修改取得多語系接口", "author": "jose<PERSON>"}, "舊分支": {"branch_name": "5.6.4.1", "date": "tag 5.6.4.1\nTagger: 張詠威 <<EMAIL>>\n\n20170721 18:00 last build2017-07-21 17:24:54", "message": "多語系維護作業   描述查詢條件選項  移除 等於和不等於", "author": "jose<PERSON>"}, "比較時間": "2025-07-28 18:16:06", "新增commit數量": 169, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "227d891115e42e2bf9f5bbc1c86f3229d255fa1a", "commit_訊息": "T100費用核銷模組 :修改取得多語系接口", "提交日期": "2017-09-21 11:29:48", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "75eb40ef3289c9c5126a581c025280c3b4b1dc1e", "commit_訊息": "T100費用核銷模組 :修改SQL語法", "提交日期": "2017-09-21 11:28:34", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "77aed9620794a4c6461bf821a9fc25becb6eac55", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-21 11:03:26", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "adab82cdcdeab78789eac92599b3da3b53e8eebd", "commit_訊息": "修正鼎慧街口ID問題", "提交日期": "2017-09-21 10:42:02", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListDataset.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "97cc43beb0ef9c70878c446a74d95345705abccc", "commit_訊息": "修正錯誤", "提交日期": "2017-09-21 10:40:43", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79fda33078759f96604c10ab646ef09314623061", "commit_訊息": "调整鼎慧问题", "提交日期": "2017-09-21 10:13:25", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8ba01d444c4e288af9927a4c1ed3e4eee930b423", "commit_訊息": "調整鼎慧二期列表接口", "提交日期": "2017-09-21 09:54:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "46494e4bc0d7230293496f5221906a035442a875", "commit_訊息": "修正鼎慧二期列表接口錯誤", "提交日期": "2017-09-21 09:51:47", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeManage.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "f3306b41971c44b6af6e239070c1885566ac0714", "commit_訊息": "調整鼎慧二期中間層服務邏輯", "提交日期": "2017-09-20 19:41:36", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "47eb802589ec49cb8ac7660c5839de4b4d3598a5", "commit_訊息": "調整HtmlFormBuilderJSON的restful服務邏輯", "提交日期": "2017-09-20 17:41:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/BuildFormBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FormDefinitionBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormDefinitionBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "47ed9748cf2a03c114a2765852f3882630e9e33d", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-20 14:33:09", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "1133797dbf845b9537900c219df716b1ef9c52d2", "commit_訊息": "修正鼎慧二期API服務功能", "提交日期": "2017-09-20 14:32:26", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/lib/AspectJ/aspectj-1.8.10.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d517f5e17a946cf754630ea37f7cedbade4e04e5", "commit_訊息": "調整鼎慧二期中間層服務邏輯", "提交日期": "2017-09-20 14:28:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "127234e9667b314daeac0136c70a5ccd31481065", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-20 14:19:59", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 3}, {"commit_hash": "2a777ce40b1e7b5f9cb461036d4d89787835f91e", "commit_訊息": "調整鼎慧API格式", "提交日期": "2017-09-20 14:03:59", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleBatchReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameter.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataBatchReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchWorkStepsBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/FormMgr.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "dd030fa072f351a846b27e97aa141ee203b054c5", "commit_訊息": "調整中間層修改時遺漏部分程式碼", "提交日期": "2017-09-20 14:00:16", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "272e5ae6d5569fbd761b6eb48964a5fb6acf7fdf", "commit_訊息": "調整鼎慧二期服務javaBean格式", "提交日期": "2017-09-20 13:57:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleBatchReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameter.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataBatchReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "cd01d9708a30dba18c985b1f450072a8476c01f7", "commit_訊息": "調整鼎慧二期中間層服務接口邏輯", "提交日期": "2017-09-20 11:16:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementStyle.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDataset.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDatasetList.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "4d7a66319c79279f45e284ae9e60e2a478f9a5f1", "commit_訊息": "調整表單設計師鼎慧中間層元件選取功能", "提交日期": "2017-09-20 10:57:37", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2cc35a03545c89953ac9b3375069d732ef4055ee", "commit_訊息": "調整繼續派送(可批次處理)restful服務，新增更新表單資料", "提交日期": "2017-09-20 10:53:10", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformDispatch.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "2e9fe58199d4e938357713c68fc9bbbc2911debc", "commit_訊息": "調整WorkItemForListBeanRes邏輯", "提交日期": "2017-09-20 10:45:04", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0179836d3936c8937df286a8e2aaadc9644db6eb", "commit_訊息": "調整DropdownExample 說明文件", "提交日期": "2017-09-19 17:32:51", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9d0677f7b9322f5de8ab0c5b048fb94822967b7", "commit_訊息": "S00-20170918001 核決層級關卡都被自動pass時，不允許退回重瓣", "提交日期": "2017-09-19 17:04:36", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5d7ffc90f85f44d6c507fab3e09b09de7c607539", "commit_訊息": "調整restful-將userOID改為userID 補上退回重辦的部分", "提交日期": "2017-09-19 16:09:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ReexecuteActivityBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileReexecuteActivity.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "aba017d98532b3d47c33b40b6b864354e39c40b9", "commit_訊息": "調整userFormValue參數格式", "提交日期": "2017-09-19 16:08:32", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ElementBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeWorkItemReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/SaveFormBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileSaveForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "bc90167580a128e15e63c4c83349d7c02c8e50a9", "commit_訊息": "調整鼎慧ID取用格式問題", "提交日期": "2017-09-19 15:36:44", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "9fe1e419304bf9fd8248678284be1e65a23ef943", "commit_訊息": "調整鼎慧ID封裝格式 新增ESS處理工具", "提交日期": "2017-09-19 15:12:49", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/ESSFileManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/ESSFormHandler.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/v1/mobile/ESSPerformWorkItem.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "6c2d0d1ed3f9b46b600b284848ebf9f998e6d9b3", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-19 15:09:16", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "d1435489bb41bdf46e39879bfe42b3f3497e9882", "commit_訊息": "調整restful-將userOID改為userID 調整HtmlFormBuilderJSON的restful服務邏輯", "提交日期": "2017-09-19 14:30:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AcceptWorkItemBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkItemBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FormDefinitionBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeWorkItemReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ListReaderBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/MobileScheduleListBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/SaveFormBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformDispatch.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileSaveForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileScheduleList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileTraceList.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 18}, {"commit_hash": "22a213b1396fced008c775762066b899f6622f9a", "commit_訊息": "調整RESTful URI路徑規則", "提交日期": "2017-09-19 14:20:55", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Notice.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "969dc749a854709fafa3de80327a4bb5b0aff9e1", "commit_訊息": "新增撤銷流程restful服務", "提交日期": "2017-09-19 13:39:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AbortProcess.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3b11c793d88ddf698050639fccef9236ab4e062c", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-19 12:00:15", "作者": "pinchi_lin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "ac244e8ad61f5bb464d6ef3c8c3dec345614b1b9", "commit_訊息": "調整Grid範例的說明文件", "提交日期": "2017-09-19 11:52:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7bd7c187f9d8755a7dd12caeb3e300aa4df97d4", "commit_訊息": "新增撤銷流程restful服務", "提交日期": "2017-09-19 11:38:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AbortProcess.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "5ffe76fe71fac03de5ddc25c60ba42e8be4e3183", "commit_訊息": "註解spring設定檔中aop的部份", "提交日期": "2017-09-19 11:03:38", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8bb1e176714222a4f71ed4846ec2c6d601858a2d", "commit_訊息": "新增鼎慧二期列表進階查詢功能", "提交日期": "2017-09-19 09:37:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DateTimeField.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ScreeningDataSet.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ScreeningDetail.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "01e093cc21f22ca544acfd61c7b160ce9f9035b9", "commit_訊息": "新增多語系", "提交日期": "2017-09-18 18:27:24", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "30171cd07e4610737fc5ff7387db47cd5d54c257", "commit_訊息": "補上鼎慧二期漏掉的java bean", "提交日期": "2017-09-18 18:24:38", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "af80daa9a9ac0742d65b329a061b91f4db3f86f4", "commit_訊息": "新增鼎慧二期圖片名稱多語系 調整HtmlFormBuilderJSON的restful服務邏輯", "提交日期": "2017-09-18 18:06:21", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6c00a09ff68eaa557517ad2e5b4d6678636b2e2b", "commit_訊息": "補上漏掉鼎慧二期restfulService", "提交日期": "2017-09-18 17:52:43", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "435a73d72d08ef682a1e4fc4de3364298deaf053", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-18 17:49:28", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "8f03f58c9d0418cd8c967654f988a94d4a050a33", "commit_訊息": "還原鼎慧中間層元件的參數", "提交日期": "2017-09-18 17:44:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementStyle.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "f582a2aca9fb78d0935dec948ab588f0aed2e18c", "commit_訊息": "調整鼎慧二期 新增圖片字段與圖片資料來源 預設圖片放置於pickName資料夾", "提交日期": "2017-09-18 17:43:54", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/picName/process.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FormDefinitionBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2b021016678b1e9209f8e497b02946e9b96f19a9", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-18 17:41:07", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a97ba9c36c6e9b137b02b454fcf7f7329fbf2ddc", "commit_訊息": "A00-20170918002 修正 : 當流程取回重辦時，只有一個關卡可以取回重辦會錯誤", "提交日期": "2017-09-18 17:37:36", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c1fde5853588f9c2ebb95fd2e7b2dbb8d770d747", "commit_訊息": "補上 [寒舍C01-20170608001]追蹤發起參考流程，流程圖會顯示異常 意外的被蓋過去了", "提交日期": "2017-09-18 17:19:05", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "032d3cb5236814025e6cfcfe1d224411b69be41f", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-18 15:50:41", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0884a86569d3b97e9d93ff03e0399f275328de3e", "commit_訊息": "修正搜尋條件錯誤", "提交日期": "2017-09-18 15:39:52", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79d13a5895b8c07a7b44976207cb5de9146f8365", "commit_訊息": "修正日期查詢錯誤", "提交日期": "2017-09-18 15:33:30", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5ba5a8e5cf0d2db4a58b81519d6085e643fcf648", "commit_訊息": "新增簽核功能", "提交日期": "2017-09-18 14:54:26", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListDataset.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "5b2585e92b473107a8c3bf99c41978b3949e0e7d", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-18 14:53:50", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "2e78344e5375b76b3171c3c86ef931d177120b30", "commit_訊息": "移除列印表單 TextBox元件 外框", "提交日期": "2017-09-15 17:41:09", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "55fd97e8f08a85af66b7fd198d76a8aba40a48d0", "commit_訊息": "新增簽核功能", "提交日期": "2017-09-15 17:31:47", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "52e63116a0148d3e643c578c4b47018825e6c9d7", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-15 16:26:18", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "cfa82a2bec72fb75b4eb886718be99c97755bfaa", "commit_訊息": "增加鼎慧簽核接口", "提交日期": "2017-09-15 16:24:36", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7aa2a5cd3521b4453ed5ae4f6d6019187de4656c", "commit_訊息": "修改 : WebService 讀取多語系", "提交日期": "2017-09-15 14:24:01", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8a63620c847c01c56e1ab3e3f7f309a413dc445", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-14 17:49:42", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandler.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 3}, {"commit_hash": "2ea9e6dc09c67253e2121603220782fe48c5bf43", "commit_訊息": "marge", "提交日期": "2017-09-14 17:46:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "08249b67aaf07788a011c143bcb8c7078de5bffe", "commit_訊息": "T100費用核銷模組 :新增WebService接口(取多語系,取得T100連線資訊)", "提交日期": "2017-09-14 17:21:08", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceLocator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e42bd545ba17911de9b829034fb1e7e28490511c", "commit_訊息": "C01-20170518002 俊宏與顧問討論的結果為 不調整自動簽核的邏輯", "提交日期": "2017-09-14 15:59:07", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4ec15e28f7ba5446985b7c370b42bcc84a21092", "commit_訊息": "C01-20170906001 補上缺少的updateSql", "提交日期": "2017-09-14 14:56:55", "作者": "張詠威", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.2.1_updateSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "648541b9b3d46a1d0bf8eee0b5263bf7c20e9bc4", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-14 14:54:17", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c75f0f78a032a7d2431794de05a44c856839dd32", "commit_訊息": "C01-20170906001 補上5521缺少的SQL", "提交日期": "2017-09-14 14:53:44", "作者": "張詠威", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.2.1_updateSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16cd281ed22a2fe489eb15508a0447a9db78f1de", "commit_訊息": "新增鼎慧二期獲取詳情可用字段restful服務", "提交日期": "2017-09-14 14:26:04", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "7469219ad861a2704944a04f8c35f48ab83b5b59", "commit_訊息": "增加webService 序號驗證接口", "提交日期": "2017-09-14 13:55:22", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "93e45facce2e37e5f26750311747853c1c6f1874", "commit_訊息": "調整javaScript的 & 條件  改為&&", "提交日期": "2017-09-14 10:19:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b5693bb3bf6900a912e56649a3d3d9bde7f86f42", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-14 10:12:56", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "eece77ec9d0310a73656fd2ece8e79923b2f9a9e", "commit_訊息": "增加獨立模組的restful接口邏輯及調整獨立模組開啟的畫面", "提交日期": "2017-09-14 10:12:32", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleDataChooseTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "88a8db8cc6dfbce19fa1deb41c0943264cae168d", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-14 09:57:11", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c11ab3de9fb67ca598d75f3f09e91e1fefc71237", "commit_訊息": "A00-20170908001 修正:版更後流程草稿無法開啟問題", "提交日期": "2017-09-14 09:56:54", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "206f98825952fc67a890d57bafcf5b9a8b25c8bc", "commit_訊息": "C01-20170518002 調整當關卡為代理人處理時，則不自動跳關", "提交日期": "2017-09-14 09:53:23", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "26d378b40ecab2b66f2ff369a068b93eea0d1e7c", "commit_訊息": "T100費用核銷模組 : 新增WebService修改表單資料接口", "提交日期": "2017-09-14 09:33:19", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceLocator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "b9b65e45f7e90c9ba515e6f25439550bcbe15884", "commit_訊息": "新增進階查詢可用字段", "提交日期": "2017-09-13 18:12:29", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "c2d2820ae9dcfb0ebde1c882be40cc4ae9ed4ed4", "commit_訊息": "調整行動版獲取流程清單restful服務邏輯", "提交日期": "2017-09-13 17:57:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DraftHeaderBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ListReaderBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstanceForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessPackageForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ReassignmentInfoForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileTraceList.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "bbf634316997825b06deff1d1b8b25d4f1e1b3b3", "commit_訊息": "新增鼎慧二期中間層RESTful服務", "提交日期": "2017-09-12 16:58:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "4db3179740fb2597bde514643543548381059821", "commit_訊息": "新增草稿發起Restful服務", "提交日期": "2017-09-11 18:31:29", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileSaveForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BPMPerformRequestTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7272c9becc9aae52b2c3c53d08d843c7d937341e", "commit_訊息": "A00-20170828001 修正:流程設計將Grid物件設定為隱藏(invisible)，附加檔案後，會跑出來", "提交日期": "2017-09-11 17:56:49", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "46291051f9dcc0fc2922f42cc53a089433fe7be1", "commit_訊息": "新增restful服務", "提交日期": "2017-09-08 18:03:42", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeWorkItemReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/SaveFormBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeManage.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileSaveForm.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "bbae5257879978a490e69ca220cf78309d1c82cc", "commit_訊息": "A00-20170814001 修正RTX錯誤導致流程無法結案", "提交日期": "2017-09-08 16:19:03", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/rtxpush/RTXMessagePusher.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5603f6c84fd3dd5002633558d18720fdd66dc8cc", "commit_訊息": "Q00-20170907001 修正BPMAPP使用者產品開窗在無搜尋條件並有過多人員情況下會出現錯誤的問題", "提交日期": "2017-09-08 09:26:16", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3a66d4d4a31ac9678dc258c60aa071f32fe5672a", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-09-07 17:02:34", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "18a30f4754b217c62a5d82d725621348d4a02579", "commit_訊息": "A00-20170807001 修正「每個員工在每個組織」都會新增一筆Employee資料", "提交日期": "2017-09-07 17:02:10", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fb9560df56f07b8f7b2677ce40d2dfa82659de9e", "commit_訊息": "新增鼎慧二期restful服務 取得待辦資料匣資訊(未簽核) 取得待辦資料匣資訊(已簽核) 取得通知資料匣資訊(未讀取) 取得通知資料匣資訊(已讀取) 取得異常關注資訊 取得重點關注流程資訊 取得逾期單據資訊", "提交日期": "2017-09-07 16:55:06", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "087123e0204660e91d42b79073904037f2761942", "commit_訊息": "C01-20170822001、C01-20170822002、C01-20170822003 修正自動自動簽核因Queue太快導致workItem尚未commit進DB，造成自動簽核時錯誤", "提交日期": "2017-09-07 16:36:30", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "410a60dcac2363aa79c7a92ffcc121f3bcccc55d", "commit_訊息": "調整鼎慧發起流程列表", "提交日期": "2017-09-07 16:07:55", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "31a2631c83dc0c7aa8aac53f8ad48725c5d45f40", "commit_訊息": "調整鼎慧二期restful服務邏輯", "提交日期": "2017-09-07 09:53:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e2e8f3aeaa2e4a1f730994a7f5336f06a15ee7d8", "commit_訊息": "A00-20170414001 重新修正流程圖顯示異常議題", "提交日期": "2017-09-06 15:56:04", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb17b0ce75adc70ff1331a8d37d08b312a1f1493", "commit_訊息": "新增鼎慧二期restful服務", "提交日期": "2017-09-06 10:14:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DatetimeFieldDataset.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DatetimeQueryCondition.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldContent.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDataset.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ImageDataset.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListDataset.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageExecutionRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ScreeningQueryCondition.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "5141871b2398a0a1213d9827b1ab7581ad2b5c07", "commit_訊息": "新增RESTful接口-表單定義同步", "提交日期": "2017-09-04 14:48:17", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormTransfer.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/XmlBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "2b92f775df171b8fe1d4a28b822104c115203baa", "commit_訊息": "修正鼎慧二期RESTful寫錯部分", "提交日期": "2017-09-01 16:49:58", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3efae849f2ebfaad01b6099b33c1d85f57d5655", "commit_訊息": "調整鼎慧二期可用字段RESTful服務邏輯", "提交日期": "2017-09-01 15:00:01", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDatetimeFieldDataset.java", "修改狀態": "重新命名", "狀態代碼": "R085"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageFieldDataset.java", "修改狀態": "重新命名", "狀態代碼": "R086"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhale.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageExecution.java", "修改狀態": "重新命名", "狀態代碼": "R082"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdData.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "e7f908e8842a0e265581887c8b8c7ad363c34c2a", "commit_訊息": "調整restful服務 補上註解", "提交日期": "2017-09-01 08:44:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CriticalBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FormDefinitionBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/OAuthConfigBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ReexecuteActivityBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CriticalBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormDefinitionBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OAuthConfigBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ReexecuteActivityTypeBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileReexecuteActivity.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileTraceList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileWeChatOAuth.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 15}, {"commit_hash": "fa8a6cc246a0feb377cfe467502fbc10f6c0d07e", "commit_訊息": "調整restful服務", "提交日期": "2017-08-31 18:38:14", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AcceptWorkItemBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageViewerBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformDispatch.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileWeChatMsg.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "7c130d1eadde4aa12d044fa876390986a9059f51", "commit_訊息": "新增鼎慧Restful服務二期功能 取得通知可用字段", "提交日期": "2017-08-31 18:22:22", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "536f4f713a987ca0f0de241fdc44e08415e79800", "commit_訊息": "新增漏掉註解", "提交日期": "2017-08-31 18:14:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Notice.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "02cd857bcf9770bf793b445663d0e4a180e33d4a", "commit_訊息": "新增鼎慧RESTful服務二期功能", "提交日期": "2017-08-31 17:51:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "56e5a82fa0c6cf7423c48e98f10a82eb1bd35733", "commit_訊息": "新增鼎慧二期列表restful服務工具", "提交日期": "2017-08-31 17:39:20", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2cbed32018a82eb0f222f1439cfcdd5f0595616e", "commit_訊息": "新增鼎慧二期可用字段resful服務", "提交日期": "2017-08-31 17:30:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDatetimeFieldDataset.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageExecution.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageFieldDataset.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdData.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "b9837dfca4318ef70c173c8d01611cfecb3f27c5", "commit_訊息": "調整restful接口部分邏輯", "提交日期": "2017-08-31 14:48:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformCritical.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileReexecuteActivity.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "8e37b76c56303f8ea29cddae16e47f32a2b467da", "commit_訊息": "調整restful接口部分邏輯", "提交日期": "2017-08-30 13:59:20", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileCompleteWorkStep.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileScheduleList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileTerminateProcess.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "4fd467fd922382edcde7ab2e53c5019ae5484eaf", "commit_訊息": "Q00-20170808002 修正微信菜單產生問題", "提交日期": "2017-08-29 18:07:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "af888d41a94986fa7c4fe814211ff4cdc1d6598e", "commit_訊息": "Q00-20170808003 修正oracle mobile_updateSQL錯誤", "提交日期": "2017-08-29 17:09:15", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f344b612c5fe717009e6eb341376fca723a0a80b", "commit_訊息": "調整\"查詢關卡工作項目\"restful服務邏輯", "提交日期": "2017-08-29 09:55:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ApplicationDefinitionBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AttachmentDefinitionBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AttachmentInstanceBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DocCmItemBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DocTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormFieldAccessDefinitionBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormInstanceBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormInstanceForPerformBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/InvokeParametersBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/MailingFrequencyTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SimpleUserBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/StringWorkflowRuntimeValueBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/VerifyPasswordTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepForPerformBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 15}, {"commit_hash": "921288746e9625a2bda1d2e3d9ee628416f04fc5", "commit_訊息": "新增鼎慧RESTful服務二期功能", "提交日期": "2017-08-25 16:59:09", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhale.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageExecution.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdData.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Organization.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileDinWhale.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileMPlatformServiceTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "1c1332b721d7e45972c3ce92778aa6cb141cd470", "commit_訊息": "新增restful服務", "提交日期": "2017-08-25 16:30:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkStepBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchProcessCommentsBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchWorkStepsBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/TerminateProcessBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityDefinitionBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ApplicationDefinitionBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AskActivityReexecuteTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ExternalReferenceBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchProcessCommentsBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchWorkStepsBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepForPerformBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepStateTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkStepTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileCompleteWorkStep.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchProcessComments.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFetchWorkSteps.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileTerminateProcess.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 19}, {"commit_hash": "f0e6a25c24ea0393e5aadb91592b35a374d22e2f", "commit_訊息": "新增使用者接收工作事項的restful服務", "提交日期": "2017-08-24 18:29:57", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AcceptWorkItemBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformDispatch.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformManage.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "66a219a5d62a8afb83394b3f60b52a5a236d572f", "commit_訊息": "新增繼續派送(批次處理)的Restful API", "提交日期": "2017-08-24 16:06:18", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkItemBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/MobileWeChatMsgBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformList.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformManage.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "d870c15ff370010fbee8870ce2264a1b8f858efd", "commit_訊息": "新增退回重辦的Restful服務", "提交日期": "2017-08-24 11:52:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ReexecuteActivityBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ReexecuteActivityTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForPerformBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileReexecuteActivity.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "55b2fc2a7a0b8704061ca0c17ead414723bc453d", "commit_訊息": "整合成4個對應目錄(遺漏部分)", "提交日期": "2017-08-24 09:39:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89e6c4fcf3ba2893edd94d20b3c8ded854f8a830", "commit_訊息": "調整Restful服務路徑與名稱(舊的刪除+遺漏部分)與整合成4個目錄", "提交日期": "2017-08-23 18:48:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerPerform.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerSystem.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerTrace.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerUtil.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Notice.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/CriticalClientBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/FormDefinitionClientBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/ListReaderClientBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/MobileScheduleListClientBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/MobileWeChatMsgClientBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/OAuthConfigClientBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/AssignmentTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/CriticalServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/FormDefinitionServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/InstanceCompleteTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ListReaderServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ManualReassignTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/MobileScheduleListServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/MobileScheduleServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/NoticeTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/OAuthConfigServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/PerformTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ProcessInstanceForListServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ReassignmentInfoForListServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ReassignmentTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/SysLanguageListServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/SysLanguageServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/SysLanguageViewerServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/TracerRoleTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/WorkItemForListServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/WorkItemStateTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormDefinitionTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobilePerformCriticalTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobilePerformManageTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileRsrcBundleTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileScheduleListTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceListTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileWeChatMsgTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileWeChatOAuthTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 46}, {"commit_hash": "08ae923f45a9524cfebeb4fb758f9a3a280233fd", "commit_訊息": "調整Restful服務路徑與名稱(ControllerTrace)", "提交日期": "2017-08-23 17:28:16", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/InstanceCompleteTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstanceForListBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/TracerRoleTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Trace.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileTraceList.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "decd033db28ccffc9e9d0ad54ced44db5eede543", "commit_訊息": "調整Restful服務路徑與名稱(舊的刪除)", "提交日期": "2017-08-23 16:52:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerInvoke.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerNotice.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/DraftHeaderServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ProcessPackageForListServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserDefineModeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserInputSubjectTypeServiceBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeListTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeListTool.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 8}, {"commit_hash": "518f541a4f27cbbcbb706671711e59bae42ac142", "commit_訊息": "調整Restful服務路徑與名稱", "提交日期": "2017-08-23 16:40:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ListReaderBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/MobileScheduleListBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DraftHeaderBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ListReaderBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/MobileScheduleBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/MobileScheduleListBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessPackageForListBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserDefineModeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserInputSubjectTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Invoke.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Notice.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileInvokeList.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileNoticeList.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileScheduleList.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 15}, {"commit_hash": "79800a687ee6b7fe32a3612b8016263ba1a38876", "commit_訊息": "修正相對位置與絕對位置表單再查看Grid時日期元件內容值未顯示的問題", "提交日期": "2017-08-23 16:22:31", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileAppGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dcc34afc913755314d6f64597b126fdd8e9c18c2", "commit_訊息": "調整Restful服務路徑與名稱(ControllerPerform)", "提交日期": "2017-08-23 16:00:36", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CriticalBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CriticalBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Perform.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformCritical.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "61efb42101f5185c8bfea59f4c44afa9e2ac70b9", "commit_訊息": "調整Restful服務路徑與名稱(ControllerUtil)", "提交日期": "2017-08-23 15:48:31", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FormDefinitionBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FormDefinitionBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/OAuthConfigBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Util.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileFormDefinition.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileWeChatOAuth.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "c43a3d12bcf3e2456c9114506636f4d682301d1f", "commit_訊息": "調整Restful服務路徑及名稱", "提交日期": "2017-08-23 15:23:04", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/MobileWeChatMsgBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/OAuthConfigBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AssignmentTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ManualReassignTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/NoticeTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PerformTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ReassignmentInfoForListBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ReassignmentTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageListBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/SysLanguageViewerBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForListBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemStateTypeBeanRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobilePerformManage.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileRsrcBundle.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MobileWeChatMsg.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 17}, {"commit_hash": "a914b4eed069e5a3d048c8b79441d85e2bc532ca", "commit_訊息": "Q00-20170823001 修正用IE開啟管理平台，匯出excel時報錯問題", "提交日期": "2017-08-23 14:43:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b2336e0f43f3e5d4c3e64fcb7af4629477f21c9c", "commit_訊息": "調整行動表單,行動表單設計器樣式", "提交日期": "2017-08-23 13:52:31", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "b7898152cf97178b258f253dd19e6538a098c423", "commit_訊息": "修改controller路径", "提交日期": "2017-08-23 11:21:58", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "7f656d09340e80fd10e2cda817060a73d7aad877", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-08-23 11:04:21", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "2d3533408bc6b4cb7a8f3ad63f232f7151bffba2", "commit_訊息": "调整RESTful服务架构 新增表单中间层服务", "提交日期": "2017-08-23 10:47:00", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerDinwhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerOrganization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "85877aeb1fef1842de8c59f08ad9d78a2ea77c88", "commit_訊息": "新增MobileManageDelegate與行事曆相關的restful服務", "提交日期": "2017-08-18 17:47:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/MobileScheduleListClientBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/MobileScheduleServiceBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileScheduleListTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "2f58f53c6682995ac966982da35e5df0ba5e9507", "commit_訊息": "Q00-20170816001 修正app退回重辦後，關卡狀態變更為擱置中，且無法顯示在簽核歷程中，也無法結案", "提交日期": "2017-08-18 09:05:50", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3a6e5bd73c05c695b65e740b7d1559244c206250", "commit_訊息": "新增HtmlFormBuilderJSON的restful服務", "提交日期": "2017-08-17 15:40:21", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/FormDefinitionClientBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/FormDefinitionServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormDefinitionTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "657b034349175ead04d82bbecd540cdbe5dc1fac", "commit_訊息": "新增sendResponseMsgToWechat的restful服務", "提交日期": "2017-08-17 11:16:14", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerSystem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/MobileWeChatMsgClientBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileRsrcBundleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileWeChatMsgTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "773d4277f03b9dae92849a10abcf0c98a372efc3", "commit_訊息": "調整WorkItemForListServiceBean", "提交日期": "2017-08-17 10:27:01", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/AssignmentTypeServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ManualReassignTypeServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/NoticeTypeServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/PerformTypeServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ReassignmentInfoForListServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ReassignmentTypeServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/WorkItemForListServiceBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/WorkItemStateTypeServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 8}, {"commit_hash": "e3affb7a507d742232574093ef1ebeb997813a8a", "commit_訊息": "新增 RsrcBundle.getAllLauguage的restful服務", "提交日期": "2017-08-16 14:12:10", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerSystem.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/SysLanguageListServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/SysLanguageServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/SysLanguageViewerServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileRsrcBundleTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "e3a7efaecab4444d3c46ccfc053b9b6516702fa0", "commit_訊息": "調整BPMAPP的按鈕圖示、說明文字字型、遮罩顏色", "提交日期": "2017-08-16 10:59:49", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTraceLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/StartUp.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/add_sign.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/agree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/attachment.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/confirm_retrieve.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/exclamation.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/go_back.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/minus.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/more.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/plus.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/remind_calendar.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/retrieve.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/return.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/revoke.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/save_form.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/send.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/common_whiteicon/stop_process.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 25}, {"commit_hash": "6db7d2a0d449ea9c5b97eac43244bfef9673e7a2", "commit_訊息": "新增MobileManageDelegate_getWeChatOpenAuthService的restful服務", "提交日期": "2017-08-16 08:42:09", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/OAuthConfigClientBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/OAuthConfigServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileWeChatOAuthTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "4614a66100b0e59d9f305ebac7eca9e8178838ca", "commit_訊息": "新增MobileManageDelegate_addMobileCalendar的restful服務", "提交日期": "2017-08-15 18:01:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/MobileScheduleListClientBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/MobileScheduleListServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/MobileScheduleServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileScheduleListTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "529bed0acd3dc4c84e603bcbfc42c54bf67e2154", "commit_訊息": "調整微信推播消息格式", "提交日期": "2017-08-15 17:56:01", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "327090bc13d5a17465911b424f7248e1871e23a4", "commit_訊息": "過濾主旨有特殊符號問題", "提交日期": "2017-08-15 15:04:19", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a88fe94a30f26b6b24a16abe077b0893a6e64b6a", "commit_訊息": "A00-20170809001 修正相對位置表單在查看Grid時datetime未顯示問題", "提交日期": "2017-08-15 08:35:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "8e78c22e0dbd3ce0d95a284916a57190eb71fe97", "commit_訊息": "補上WorkItemForListServiceBean", "提交日期": "2017-08-14 19:43:43", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerPerform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/WorkItemForListServiceBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0f01ca54225461ad9926f0923b4173bf6a6b1733", "commit_訊息": "新增行動表單設計器提示區塊", "提交日期": "2017-08-14 19:29:33", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "98bdfbba6c6bf46d3422ebab45079c52b023fec7", "commit_訊息": "補上getTraceProcInstListMobility的ServiceBean", "提交日期": "2017-08-14 17:29:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/InstanceCompleteTypeServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ProcessInstanceForListServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/TracerRoleTypeServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "fbbbcc5ee7228cffb90c87d135d4de3512a54958", "commit_訊息": "調整命名錯誤的ServiceBean", "提交日期": "2017-08-14 15:40:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/DraftHeaderBean.java", "修改狀態": "重新命名", "狀態代碼": "R093"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ProcessPackageForListBean.java", "修改狀態": "重新命名", "狀態代碼": "R089"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserDefineModeBean.java", "修改狀態": "重新命名", "狀態代碼": "R069"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserInputSubjectTypeBean.java", "修改狀態": "重新命名", "狀態代碼": "R068"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/WorkItemForListBean.java", "修改狀態": "重新命名", "狀態代碼": "R089"}], "變更檔案數量": 5}, {"commit_hash": "ab8d97cd3e3c5d41dc054127b95dcba676814238", "commit_訊息": "新增PageListReaderDelegate_getNoticeListMobility的restful服務", "提交日期": "2017-08-14 14:42:41", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerNotice.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/WorkItemForListBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeListTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeListTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "5f97a27c0ca6688b7ac98f39a10d3489303a6012", "commit_訊息": "新增ListReaderDelegate_getInvokableProcessPkgListMobility的restful服務", "提交日期": "2017-08-14 11:58:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ProcessPackageForListBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserDefineModeBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserInputSubjectTypeBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeListTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "e31e24a42f0c0181056d1188e91941449cc210b9", "commit_訊息": "新增ListReaderDelegate_getCommonProcessPkgListMobility的restful服務", "提交日期": "2017-08-14 10:52:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/DraftHeaderBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ProcessPackageForListBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeListTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a4c803050588495e05ed4b5181caaa720eaa85fd", "commit_訊息": "新增PageListReaderDelegate_getTraceProcInstListMobility的restful服務", "提交日期": "2017-08-14 09:48:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerTrace.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceListTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "09b22a38b01cc6046052d8b03b031975ceacb608", "commit_訊息": "調整草稿清單與我的最愛清單的restful服務", "提交日期": "2017-08-11 16:10:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/DraftHeaderBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ProcessPackageForListBean.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "be15658809e3a1b803d282aabb2324f99a05b9ac", "commit_訊息": "新增ProcessInstanceDTOFactoryDelegate_findCriticalMessageLogDTOsByWorkItemOID的restful服務", "提交日期": "2017-08-11 10:55:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerPerform.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/CriticalClientBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/CriticalServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobilePerformCriticalTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "7d754db1a9f6b2987c28f3328835b4a1322f29c6", "commit_訊息": "新增ListReaderDelegate_getFavoritiesProcessPkgListMobility的restful服務", "提交日期": "2017-08-10 17:46:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerInvoke.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ListReaderServiceBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeListTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "42412ea8219f743ad08f7b1f05bf89969815e159", "commit_訊息": "調整PageListReader restful服務中的clientBean格式", "提交日期": "2017-08-10 17:37:19", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/ListReaderClientBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a36dbde5994189da8a27220c3989181b0e64b79", "commit_訊息": "新增PageListReaderDelegate_getPerformableWorkItemListMobility的restful服務", "提交日期": "2017-08-10 16:40:17", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerPerform.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/ListReaderClientBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobilePerformManageTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "cd70f4135ccdbfb0ab1e44072ca41a75ca40bdf2", "commit_訊息": "新增PageListReaderDelegate_getDraftListMobility的restful服務", "提交日期": "2017-08-10 15:43:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/ControllerInvoke.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/ListReaderClientBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/ListReaderServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeListTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "e3db7b7a2a0b33d56d6059cba175cab3fd506dc3", "commit_訊息": "修正RESTful Controller名稱 修正log4j在RESTful Controller的處理機制", "提交日期": "2017-08-09 15:08:19", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/conf/NaNaWebLog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java", "修改狀態": "重新命名", "狀態代碼": "R096"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerOrganization.java", "修改狀態": "重新命名", "狀態代碼": "R091"}], "變更檔案數量": 3}, {"commit_hash": "a8f68c02363eec83510b236735bf5847e19bdedb", "commit_訊息": "新增Delegate轉RESTful Service架構", "提交日期": "2017-08-09 10:04:22", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/dataformat/MobileHttpStatusCode.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerOrganization.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/UserClientBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileService/UserServiceBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileOrganizationManagerTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "f321e32a047b66b42791c1c5a5d806e9e8e3136e", "commit_訊息": "Q00-20170808001 修正沒開啟mobile推播相關設定依舊會推送問題", "提交日期": "2017-08-08 18:13:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "091bc4c4460eb90a56624375806d3ec4928673ce", "commit_訊息": "調整行事曆查詢功能", "提交日期": "2017-08-07 17:36:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1c9cca9f0244436ec5ffa5042792d3007234bd4b", "commit_訊息": "調整查詢本周任務功能與比較行事曆狀態功能", "提交日期": "2017-08-07 14:34:36", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "a9307321540b73a228c6c3609125f80364628b2a", "commit_訊息": "調整依流程設定判斷是否推送微信消息功能的ProcessInstance取得方法", "提交日期": "2017-08-07 14:01:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07675610d9bd5a763b01245d9ae064242f0c6f6f", "commit_訊息": "C01-20170802002 修正入口平台整合設定中新增使用者開窗無過濾離職人員", "提交日期": "2017-08-07 10:50:20", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88dbc2eab576e2c3c7bbe96da3c2032887da2c9f", "commit_訊息": "修正行動表單設計器各區塊按鈕樣式", "提交日期": "2017-08-04 14:56:34", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "462450a8e859ebd8fe71b3df8b2e4e77014c9014", "commit_訊息": "新增行動表單設計器中選取鼎慧中間層表單元件功能", "提交日期": "2017-08-04 14:13:32", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "e1f9ade5c3678198e72678204a561032cc483711", "commit_訊息": "修正讀取Excel資料", "提交日期": "2017-08-03 18:03:38", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16c904a3eeab9c2417694d5ac193af2896218759", "commit_訊息": "S00-*********** 流程在執行invoke關卡時，不得取回重瓣", "提交日期": "2017-08-03 17:43:59", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "5f2a41c94d7c1d3624e059aaabc37bc67d9c24ff", "commit_訊息": "ISO調閱申請單-畫面及FormScript調整", "提交日期": "2017-08-03 17:31:44", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOInv001.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "399559144425657f1f159d579db9e455638f2fb3", "commit_訊息": "Q00-20170803001 修正BPM文件的行動版表單formscript 修正hiddenAppGridColum少了n 修正綁定Grid按鈕少了引號 新增介紹設定Grid顯示特定元件寫法 新增介紹設定Grid隱藏特定元件寫法", "提交日期": "2017-08-03 11:27:15", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9ac34f57b6a758ad673a10dcad08aa1ccd57a8bb", "commit_訊息": "修正因套件影響select元件無法被正確顯示問題(適用相對位置表單) 新增Grid設定使用者要隱藏的元件(適用相對位置表單) 新增Grid設定使用者要顯示的元件(適用相對位置表單,不適用流程設為invisible)", "提交日期": "2017-08-03 10:20:29", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileAppGrid.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0a36bc318a3d07d8865b84775fd2885ffbbd0976", "commit_訊息": "新增簡易表單欄位", "提交日期": "2017-08-02 18:05:03", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementStyle.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d50de817ef67cfebffd5cbb7cf831635d5a46ce7", "commit_訊息": "調整BPM推播消息格式,改為使用文字卡格式推送", "提交日期": "2017-08-02 15:32:44", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "647b1817552fd4409a11e284b3621f6f6e3b0d1b", "commit_訊息": "條件設定畫面調整", "提交日期": "2017-07-31 20:13:12", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f656c5020a2b2a7b32e459056be5bd09349d896", "commit_訊息": "關注事項 :關注欄位維護作業[設定條件畫面調整]", "提交日期": "2017-07-31 20:07:37", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de3c3eab7c66b49fbc3647ce4f84623266d7e1fb", "commit_訊息": "修正入口平台設定多餘的程式碼", "提交日期": "2017-07-27 18:03:49", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be8ae54d31b0915f07f16b048ad1c225bfa44e83", "commit_訊息": "修正查詢行事曆資料與新增行事曆紀錄比較功能", "提交日期": "2017-07-27 17:08:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "c9df410f42b3a3fc6ca85b0413eb466fd5434560", "commit_訊息": "調整統計元件已完成/每週任務取值方式", "提交日期": "2017-07-27 09:27:34", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}