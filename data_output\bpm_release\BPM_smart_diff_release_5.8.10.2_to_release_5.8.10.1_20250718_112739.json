{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.10.2", "date": "2024-06-25 16:34:02", "message": "[內部]更新58102patch", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.10.1", "date": "2024-03-26 16:55:42", "message": "[內部]更新58101patch[補]", "author": "wayne<PERSON>"}, "比較時間": "2025-07-18 11:27:39", "新增commit數量": 186, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "c12c5436936c4557a376ce2641933060190e8189", "commit_訊息": "[內部]更新58102patch", "提交日期": "2024-06-25 16:34:02", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5822282b5e50db0965e137a64ebae28eea8b02c5", "commit_訊息": "[文件智能家]修正多語系：文件智能家應用管理改為文件智能家模組", "提交日期": "2024-06-25 16:05:28", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dce0f0f5c6e04c04e6cb8484b7db9c64797ae827", "commit_訊息": "[文件智能家]修正因流程主機位址不是127.0.0.1或localhost導致取得AccessToken失敗，間接導致不會觸發ChatFile接口", "提交日期": "2024-06-25 15:44:52", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/SystemInfoUtils.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NaNaXWebHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "70f2e96a5816473f1765ddb27c0c27b7eea1bc95", "commit_訊息": "[文件智能家]修正DB為Oracle時，取得最近5筆歷史問答紀錄因為欄位reponseData為Clob無法使用查詢EQUAL的查詢條件，改為EXACT_NOT_LIKE", "提交日期": "2024-06-25 15:36:17", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryOperator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0f365b1c443af0a44f625c94b6d2add5030f03c0", "commit_訊息": "[文件智能家]修正使用首頁模組進入的表單畫面中的附件圖示打開後不會顯示助閱讀", "提交日期": "2024-06-24 15:40:06", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71551ef3ba7a680243a6905ecbbd4433bc05084a", "commit_訊息": "[文件智能家]修正附件圖示無法每次都正常顯示的異常", "提交日期": "2024-06-24 13:51:03", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6accbb6b39767e087517ae9cc371ff675f6ffca6", "commit_訊息": "[Web]C01-20240620007 修正點簽核狀態報錯", "提交日期": "2024-06-24 11:12:01", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/AuthorityHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a93813c032d96dddbb2843e31704fdcc953bea4d", "commit_訊息": "[WEB] C01-20240607001 修正已結案流程，出現元素為Null錯誤，新增防呆", "提交日期": "2024-06-21 09:15:14", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3155ff43dbe326f050d329b1d8b17836bdbbe47e", "commit_訊息": "[文件智能家]更新表單流程及多語系", "提交日期": "2024-06-20 19:53:49", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/copyfiles/@iso/default-form/ISOMod001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001Manager.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@iso/default-process/\\346\\226\\207\\344\\273\\266\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "4be533442ad6e3ef7c79b49595eec77b2c00fca6", "commit_訊息": "[行業表單庫]V00-20240620001 增加卡控使用的表單代號必須為英文字母開頭，並且由英文字母、數字或底線組成[補]", "提交日期": "2024-06-20 11:23:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd0b69d455aba2c790fa50748f666778258f5cff", "commit_訊息": "[SYSDT]Q00-20240605001 調整系統管理工具中測試系統郵件後的提示訊息", "提交日期": "2024-06-19 10:20:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0279175bff04e3bd7a5c05c730457f359d9d89be", "commit_訊息": "[WEB] C01-20240606009 修正表單formDispatch為false時，重複執行formSave行為", "提交日期": "2024-06-18 15:17:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a3a19c1c6ce66cc2df55ae50b08decd6e5eb864", "commit_訊息": "[文件智能家]调整问答窗助阅读显示逻辑", "提交日期": "2024-06-18 15:13:13", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileToolDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3de68b58ed4ba2760204eb31e94ce6dbdb8a9e4e", "commit_訊息": "[內部]更新58102patch", "提交日期": "2024-06-18 10:37:39", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89675a4fba2d17601b1cf2ebff03edcddb516a25", "commit_訊息": "[HR同步] V00-20240613002 修正匯入中介表檢查職務定義邏輯異常問題", "提交日期": "2024-06-23 03:01:36", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ee2b8e8c9449a9cfe3baded271f53f6fdef0d2c", "commit_訊息": "[ISO] V00-20240612011 修正文管首頁調閱後進文件調閱申請後session未清除問題 再從發起流程入口進入後，因wms_manageDocument_documentOID未清除，而預設代入文件編號。", "提交日期": "2024-06-17 11:41:59", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8cde00de99c02f190507ee6b39df2c6507100e4", "commit_訊息": "[T100]C01-20240605004 修正T100傳遞換行符號，Grid呈現上沒有換行效果[補修正]", "提交日期": "2024-06-17 11:16:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed701cabc4c5514d4d5367e8ca72224d3e5f88d0", "commit_訊息": "[T100]C01-20240605004 修正T100傳遞換行符號，Grid呈現上沒有換行效果", "提交日期": "2024-06-17 11:12:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "affcffa0710161f9479445e086dbf70ddc699734", "commit_訊息": "[Web]C01-20240613007 修正外部連結登入BPM，若密碼輸入錯誤再重新登入BPM後，會呈現首頁的畫面的问题", "提交日期": "2024-06-17 10:44:47", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "339781486e77f13cca4418493d49ae05838cabc6", "commit_訊息": "[文件智能家]更新表單及多語系", "提交日期": "2024-06-14 17:57:41", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/copyfiles/@iso/default-form/ISOMod001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001Manager.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ed54a8d64837c6db70252ee7d594f09483a80e72", "commit_訊息": "[内部]V00-20240612012 修正終止流程按取消會跳下一張單據，不會停在原單據", "提交日期": "2024-06-14 15:56:31", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd52a3de9f8f63b3a5543e7e882c8ba4dd152e95", "commit_訊息": "[文件智能家] 长知识关联、ISO抛转记录新增字段", "提交日期": "2024-06-14 15:22:06", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileISOTransferRecordsDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileISOTransferRecords.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileKnowledge.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "7ad3d52180080e3e8213d388c94993602d4ed45d", "commit_訊息": "[流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗[補]", "提交日期": "2024-06-14 11:33:04", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d4cc521bd3f048c54010abcb1f4b8f473e95bc5d", "commit_訊息": "[在線閱讀] C01-20240613002 新增在線閱讀模組越南語系", "提交日期": "2024-06-13 16:02:20", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a380d3f089b950a30791505297b3deae3c64a279", "commit_訊息": "[文件智能家]更新多語系", "提交日期": "2024-06-12 17:12:45", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "8f93e31e0f0b72484a490dad69fed9c1942761ba", "commit_訊息": "[文件智能家]增加卡控：長知識關聯作業有ISO授權才會顯示", "提交日期": "2024-06-12 17:11:13", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ac386d83f440d24f61b81cf4aee8878fac0b9b6f", "commit_訊息": "[BPM APP]C01-20240611001 修正行動簽核管理中心的釘釘待辦同步頁面無法開窗選擇流程名稱與處理者問題", "提交日期": "2024-06-11 17:18:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "68218df1be6ef03207fb9ed83b9e8363f884123d", "commit_訊息": "[文件智能家] 问答使用非流式", "提交日期": "2024-06-11 14:45:50", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "61c36b6d62dcecb6e5dd61cd70ce2e5905a2a942", "commit_訊息": "[Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載(補)", "提交日期": "2024-06-11 10:34:27", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "237cd85cc5c153daf7ba1818e87f0615ed5da6f7", "commit_訊息": "[Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載(補)", "提交日期": "2024-06-07 17:40:30", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6f86bb010367c005eb3f30e068a1315f9120f744", "commit_訊息": "[文件智能家]修正上傳檔案到ChatFile失敗的異常", "提交日期": "2024-06-07 17:31:26", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a7dce2b1d895b4a91141cd7d73f35becd717d99", "commit_訊息": "[表單設計師] C01-20240605002 修正絕對位置表單的label以及TextBox的label轉換為RWD表單，底色沒有跟著過去", "提交日期": "2024-06-07 16:39:55", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a285afcac803636c4fe481ea93c7731fbedbdb0", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58", "提交日期": "2024-06-07 17:17:50", "作者": "kmin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "84200a35a21f78c0a0d70d39c6c9841eabc22384", "commit_訊息": "[流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗[補]", "提交日期": "2024-06-07 17:10:26", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b97449c34ba310b6686d8e8028010650d07bcd27", "commit_訊息": "[Web]C01-20240522002 修正系统权限管理员[可存取的範圍]设定无效的问题", "提交日期": "2024-06-07 17:07:35", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/AuthorityHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d593a9a8373c0346d00ca8f64e65149bf67bf415", "commit_訊息": "[Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載", "提交日期": "2024-06-07 15:09:53", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c5c4c52db825051bf40f84d7ff5e201ec1107ca", "commit_訊息": "[流程引擎]C01-20240603004 修正連接線條件式帶有\",\"逗號會被系統過濾造成派送異常", "提交日期": "2024-06-07 14:35:11", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/util/ConditionEvaluator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b757f28151666079a841f1d4b7f23a4338b4fe7d", "commit_訊息": "[文件智能家]修正ISO表單相關元件名稱", "提交日期": "2024-06-07 14:22:23", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/copyfiles/@iso/default-form/ISOMod001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001Manager.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7cae999809bd1ed747015b1ed365a62c08deb69b", "commit_訊息": "[流程引擎]C01-20240606005 當解析表單欄位參與者型態選用「部門主管」組織卻沒有設定該部門的主管，目前會拋出錯誤訊息告知。", "提交日期": "2024-06-07 11:32:31", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "776d08ca0edd8033164ab47fc940f40a3adf48db", "commit_訊息": "[內部]更新Patch及Create SQL", "提交日期": "2024-06-06 18:03:49", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0cedc7ca21b13cc974656e55d931e94732f215d5", "commit_訊息": "[行業表單庫]新增範例表單檔案", "提交日期": "2024-06-06 16:48:44", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\344\\270\\215\\345\\220\\210\\346\\240\\274\\345\\223\\201\\345\\215\\241.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\251\\225\\351\\221\\221\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\205\\247\\351\\203\\250\\347\\250\\275\\346\\240\\270\\346\\237\\245\\346\\252\\242\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\223\\201\\350\\263\\252\\346\\252\\242\\351\\251\\227\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\256\\242\\346\\210\\266\\345\\233\\236\\351\\245\\213\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\256\\242\\346\\210\\266\\346\\212\\200\\350\\241\\223\\350\\263\\207\\346\\226\\231\\347\\247\\273\\350\\275\\211\\346\\234\\203\\350\\255\\260.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\257\\251\\346\\237\\245\\345\\240\\261\\345\\221\\212.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\257\\251\\346\\237\\245\\351\\240\\205\\347\\233\\256\\346\\270\\205\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\224\\266\\346\\226\\207\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\226\\231\\350\\231\\237\\347\\224\\263\\350\\253\\213\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\226\\260\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\251\\225\\344\\274\\260\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\226\\260\\347\\211\\251\\346\\226\\231\\351\\234\\200\\346\\261\\202\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\252\\242\\351\\251\\227\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\270\\254\\350\\251\\246\\350\\250\\210\\347\\225\\253\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\204\\241\\347\\267\\232\\347\\266\\262\\350\\267\\257\\345\\270\\263\\350\\231\\237\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\224\\242\\345\\223\\201\\350\\250\\255\\350\\250\\210\\350\\246\\217\\346\\240\\274\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\227\\233\\345\\277\\253\\346\\234\\215\\345\\213\\231\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\250\\213\\345\\274\\217\\347\\225\\260\\345\\270\\270\\345\\217\\215\\346\\207\\211\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\250\\275\\346\\240\\270\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\250\\275\\346\\240\\270\\350\\250\\210\\347\\225\\253\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\256\\241\\345\\210\\266\\345\\215\\200\\345\\237\\237\\351\\200\\262\\345\\207\\272\\347\\256\\241\\345\\210\\266\\347\\231\\273\\350\\250\\230.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\263\\273\\347\\265\\261\\344\\270\\273\\346\\251\\237\\345\\256\\211\\345\\205\\250\\346\\252\\242\\346\\237\\245\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\263\\273\\347\\265\\261\\346\\254\\212\\351\\231\\220\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\263\\273\\347\\265\\261\\351\\226\\213\\347\\231\\274\\344\\270\\212\\347\\267\\232\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\266\\255\\344\\277\\256\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\266\\255\\344\\277\\256\\350\\231\\225\\347\\220\\206\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\274\\272\\345\\244\\261\\346\\224\\271\\345\\226\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\243\\275\\347\\250\\213\\345\\267\\241\\346\\252\\242\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\251\\246\\347\\224\\242\\345\\240\\261\\345\\221\\212.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\251\\246\\351\\207\\217\\347\\224\\242\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\256\\212\\346\\233\\264\\350\\250\\230\\351\\214\\204\\351\\200\\232\\347\\237\\245\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\256\\212\\346\\233\\264\\351\\234\\200\\346\\261\\202\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\263\\207\\345\\256\\211\\347\\256\\241\\347\\220\\206\\345\\257\\251\\346\\237\\245\\346\\234\\203\\350\\255\\260\\347\\264\\200\\351\\214\\204.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\263\\207\\350\\250\\212\\344\\275\\234\\346\\245\\255\\347\\225\\260\\345\\270\\270\\350\\231\\225\\347\\220\\206\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\263\\207\\350\\250\\212\\345\\256\\211\\345\\205\\250\\344\\272\\213\\344\\273\\266\\345\\240\\261\\345\\221\\212\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\263\\207\\350\\250\\212\\345\\256\\211\\345\\205\\250\\347\\233\\256\\346\\250\\231\\350\\250\\255\\345\\256\\232\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\263\\207\\350\\250\\212\\350\\263\\207\\347\\224\\242\\346\\224\\234\\345\\270\\266\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\273\\237\\351\\253\\224\\345\\256\\211\\350\\243\\235\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\206\\253\\347\\231\\202\\345\\231\\250\\346\\235\\220\\344\\270\\215\\350\\211\\257\\344\\272\\213\\344\\273\\266\\351\\200\\232\\345\\240\\261\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\226\\213\\346\\241\\210\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\230\\262\\347\\201\\253\\347\\211\\206\\351\\200\\243\\347\\267\\232\\346\\234\\215\\345\\213\\231\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\246\\226\\344\\273\\266\\346\\252\\242\\346\\237\\245\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 42}, {"commit_hash": "a45b1276a9232121b2dfa5a5f0d2ee970c83252d", "commit_訊息": "[內部]更新58102patch", "提交日期": "2024-06-06 11:07:55", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6a3fdf039aa351d1e20c6e01e9b95cc9ec8c0c96", "commit_訊息": "[文件智能家]新增模組", "提交日期": "2024-03-25 16:33:46", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileAssistedReadingDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileISOTransferRecordsDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileKnowledgeCategoryDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileKnowledgeDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFilePropertiesDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileQARecordDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileToolDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileTransferRecordsDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileUserManagementDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileUserTokenDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileISOTransferRecordsDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileKnowledgeCategoryDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileKnowledgeDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFilePropertiesDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileQARecordDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileToolDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileTransferRecordsDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileUserManagementDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileUserTokenDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileAssistedReading.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileISOFile.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileISOTransferRecords.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileKnowledge.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileKnowledgeCategory.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileProperties.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileQARecord.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileTransferRecords.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileUserManagement.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileUserToken.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/service/ChatFileCommonService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileCommonServiceImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/PagingList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryCondition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryOperator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/SortCondition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/SortOrder.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/ValueType.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/response/PlatformApiResponse.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/DateUtils.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/IFileService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NaNaXWebHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/chat-label.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/chat-logo.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISOMod001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001Manager.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@iso/default-process/\\346\\226\\207\\344\\273\\266\\346\\226\\260\\345\\242\\236\\347\\224\\263\\350\\253\\213.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@iso/default-process/\\346\\226\\207\\344\\273\\266\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@iso/default-process/\\346\\226\\207\\347\\256\\241\\346\\226\\260\\345\\242\\236\\346\\226\\207\\344\\273\\266.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 65}, {"commit_hash": "d8fbcf1b3850e6721e019bf6f56f177cae121b34", "commit_訊息": "[T100]C01-20240531008 調整表單同步增加Script防呆語法", "提交日期": "2024-06-05 17:45:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopFormTransfer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "58ccd05a4722e73f18a10654b3ca6d7845581125", "commit_訊息": "[ORGDT]C01-20240517013 調整Web化設計工具在打開後隔一段時間會發生操作錯誤問題", "提交日期": "2024-06-05 17:25:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "f638fdccbd3d19192eebd640562118d824646a58", "commit_訊息": "[流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗", "提交日期": "2024-06-05 15:15:20", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b1acf42f4e56c028c42834da08b4f6aeb64a4347", "commit_訊息": "[PRODT]C01-20240603003 修正流程實例中的詳細流程圖中活動關卡與連接線消失問題", "提交日期": "2024-06-04 14:15:18", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpmn-js/custom-bpmn-navigated-viewer.production.min.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b5818051207d3d067946dbb690b297b70152230", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58", "提交日期": "2024-06-04 13:13:25", "作者": "kmin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "30dd9e20e1705553b9250b33755c9a9fa76b5e89", "commit_訊息": "[<PERSON><PERSON><PERSON>]Q00-20240604003 增加程式註解及移除不必要的程式片段", "提交日期": "2024-06-04 11:52:08", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthSettingManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0505910778793a4fb6f3b90fcbadf61f4ab7dac3", "commit_訊息": "[EBG]]Q00-20240604002 增加程式註解及移除不必要的程式片段", "提交日期": "2024-06-04 11:51:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/ebgModule/EBGHistoricalSigner.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/EBGAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5a5ae85795c8a2733fbea87ad5422dc280d18af0", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58", "提交日期": "2024-06-04 11:35:46", "作者": "kmin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "023047141c411d63288cbd74dae82f350b895f24", "commit_訊息": "[Web] C01-202410603001 檢核表單元件資料型態與DB欄位型態是否一致", "提交日期": "2024-06-04 11:16:33", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cfe4f09a394abc34ec0a36f9cb0905166b17233", "commit_訊息": "[Web] C01-202410603001 檢核表單元件資料型態與DB欄位型態是否一致", "提交日期": "2024-06-04 11:16:33", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a94b98136d235bad3b63d9237e91882d73142c8", "commit_訊息": "[雙因素認證]Q00-20240603001 補上清除信任端點資訊按鈕的多語系", "提交日期": "2024-06-03 13:35:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "47ebb67b5205504c28ad68d2714de2e40ac2d4cc", "commit_訊息": "[Web]C01-20240531001 修正列印表單報錯 _self.table.bootstrapTable is not a function", "提交日期": "2024-06-03 09:00:11", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "58acdaee859cd53eae282ac06b509fa24e4d216d", "commit_訊息": "[BPM APP]C01-20240527003 修正整合企業微信在進行使用者登入返回的錯誤訊息都是undefined問題", "提交日期": "2024-05-30 17:05:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "026d09ca7041a64bedf0a11277575ffe0dfba02f", "commit_訊息": "[CRM]Q00-20240530001 CRM表單轉成RWD響應式表單[補修正]", "提交日期": "2024-05-30 10:58:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\344\\274\\260\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\217\\253\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\240\\261\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\240\\261\\345\\203\\271\\350\\256\\212\\346\\233\\264\\345\\226\\256.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\256\\211\\350\\243\\235\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\256\\211\\350\\243\\235\\351\\251\\227\\346\\224\\266\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\256\\242\\346\\210\\266\\344\\277\\235\\351\\244\\212\\347\\264\\200\\351\\214\\204\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\256\\242\\346\\210\\266\\345\\220\\210\\347\\264\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\256\\242\\346\\210\\266\\346\\234\\215\\345\\213\\231\\347\\231\\273\\351\\214\\204\\350\\231\\225\\347\\220\\206\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\345\\267\\245\\344\\275\\234\\350\\250\\230\\351\\214\\204.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\346\\250\\243\\345\\223\\201\\347\\224\\263\\350\\253\\213\\345\\226\\256.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\347\\266\\255\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\350\\262\\273\\347\\224\\250\\347\\224\\263\\350\\253\\213\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\351\\200\\201\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\351\\200\\201\\345\\216\\237\\345\\273\\240\\347\\266\\255\\344\\277\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\351\\200\\201\\345\\273\\240\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/absolute-form/\\351\\212\\267\\345\\224\\256\\346\\251\\237\\346\\234\\203\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\344\\274\\260\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI30).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\217\\253\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI12).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(CTRI06).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\240\\261\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(SALI30).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\240\\261\\345\\203\\271\\350\\256\\212\\346\\233\\264\\345\\226\\256(SALI59).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\256\\211\\350\\243\\235\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI37).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\256\\211\\350\\243\\235\\351\\251\\227\\346\\224\\266\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI38).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\256\\242\\346\\210\\266\\344\\277\\235\\351\\244\\212\\347\\264\\200\\351\\214\\204\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI23).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\256\\242\\346\\210\\266\\345\\220\\210\\347\\264\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(CTRI05).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\256\\242\\346\\210\\266\\346\\234\\215\\345\\213\\231\\347\\231\\273\\351\\214\\204\\350\\231\\225\\347\\220\\206\\344\\275\\234\\346\\245\\255(SERI12).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\345\\267\\245\\344\\275\\234\\350\\250\\230\\351\\214\\204(SALI21).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\346\\250\\243\\345\\223\\201\\347\\224\\263\\350\\253\\213\\345\\226\\256(SALI54).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\347\\266\\255\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI13).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\350\\262\\273\\347\\224\\250\\347\\224\\263\\350\\253\\213\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PORI51).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\351\\200\\201\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI17).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\351\\200\\201\\345\\216\\237\\345\\273\\240\\347\\266\\255\\344\\277\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI14).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\351\\200\\201\\345\\273\\240\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(REPI15).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/rwd-form/\\351\\212\\267\\345\\224\\256\\346\\251\\237\\346\\234\\203\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(SALI17).form\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 36}, {"commit_hash": "4d345a8fac1f3c1357ea344a1824815aa533690e", "commit_訊息": "[CRM]Q00-20240530001 CRM表單轉成RWD響應式表單", "提交日期": "2024-05-30 10:55:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\344\\274\\260\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\217\\253\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\240\\261\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\240\\261\\345\\203\\271\\350\\256\\212\\346\\233\\264\\345\\226\\256.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\256\\211\\350\\243\\235\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\256\\211\\350\\243\\235\\351\\251\\227\\346\\224\\266\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\256\\242\\346\\210\\266\\344\\277\\235\\351\\244\\212\\347\\264\\200\\351\\214\\204\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\256\\242\\346\\210\\266\\345\\220\\210\\347\\264\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\256\\242\\346\\210\\266\\346\\234\\215\\345\\213\\231\\347\\231\\273\\351\\214\\204\\350\\231\\225\\347\\220\\206\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\345\\267\\245\\344\\275\\234\\350\\250\\230\\351\\214\\204.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\346\\250\\243\\345\\223\\201\\347\\224\\263\\350\\253\\213\\345\\226\\256.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\347\\266\\255\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\350\\262\\273\\347\\224\\250\\347\\224\\263\\350\\253\\213\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\351\\200\\201\\344\\277\\256\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\351\\200\\201\\345\\216\\237\\345\\273\\240\\347\\266\\255\\344\\277\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\351\\200\\201\\345\\273\\240\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "\"Release/copyfiles/@crm/form-default/\\351\\212\\267\\345\\224\\256\\346\\251\\237\\346\\234\\203\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255.form\"", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 18}, {"commit_hash": "a87506fe67219efe7898a2a28c2d07f23365beea", "commit_訊息": "[PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]", "提交日期": "2024-05-29 16:47:14", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "87b25e00e1c8b7e01ca4d0d7a5f2e1603e327e92", "commit_訊息": "[PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息", "提交日期": "2024-05-29 16:09:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c23a892f187aba868b6719a7266ac9944b32519", "commit_訊息": "[E10]Q00-20240529001 移除E10回寫服務多餘的程式內容", "提交日期": "2024-05-29 11:21:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "40cbe69069b1ded081d506ab4fa492b9671723cb", "commit_訊息": "[Web]C01-20240528001 修正Grid无资料时栏位宽度异常的问题", "提交日期": "2024-05-29 09:04:58", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63f920b752c10b85516590babe0344cd14bdc6d0", "commit_訊息": "[WorkFlow]C01-20240520001 修正因ProcessMappingKey的wfRuntimeValueOID未更新造成iReport列印失敗", "提交日期": "2024-05-28 16:08:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9b8de78ca4b120edb13e315d5b255ee029f4cbbd", "commit_訊息": "[Web]C01-20240524002 修正逾時關卡排程新增的工作轉派稽核紀錄表的狀態碼應改成為2逾時未處裡轉派", "提交日期": "2024-05-28 11:04:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a52e6a23bb61c2104d4ef240e2cc53720009f20b", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2024-05-27 09:11:08", "作者": "邱郁晏", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "416b1e09db25ed627c8d7e6fd60d333cf2f36323", "commit_訊息": "[雙因素模組] C01-20240523005 修正若開啟全景系統時，不應判斷jndiName，新增防呆", "提交日期": "2024-05-27 09:01:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7cd2ceaa0896ab54162c9eec502ba5382fef51d3", "commit_訊息": "[雙因素模組] C01-20240523005 修正若無開啟全景系統時，不應判斷jndiName，新增防呆(補)", "提交日期": "2024-05-27 09:01:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b056eafe05d9ee286261440751ce394b5eae0505", "commit_訊息": "[雙因素模組] C01-20240523005 修正若無開啟全景系統時，不應判斷jndiName，新增防呆", "提交日期": "2024-05-27 08:51:48", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f1ddbf151084dea8737c0290f0bba65931928f1", "commit_訊息": "[ISO] C01-20240523004 優化轉檔工具異常Log訊息", "提交日期": "2024-05-24 17:49:04", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a89165cee033811984101607ec0a597e1bfdcac2", "commit_訊息": "[Web] C01-20240521003 修正流程表單元件設置invisible時，前端表單會生成元件，導致安全性問題", "提交日期": "2024-05-24 16:52:06", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2378308a7a773ee4e464d6b037d391aefe40f9b6", "commit_訊息": "[EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件[補修正]", "提交日期": "2024-05-23 16:47:04", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGCreateForm.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22677c9f63511491f04c20d1a071f98b007f1f65", "commit_訊息": "[Web] C01-20240510002 處理人員收到兩封待辦通知信问题修正", "提交日期": "2024-05-23 11:18:25", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "55724206244b87046907f9ebf83329ed5d08df74", "commit_訊息": "[Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正[补]", "提交日期": "2024-05-22 18:27:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "23b79b9e9a10ebcdf0afc6e00a63ec44b43e84e2", "commit_訊息": "[EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件[補修正]", "提交日期": "2024-05-22 16:41:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cb43653627388d288a3791dfb230422bf9e5d85a", "commit_訊息": "[流程引擎]C01-20240516004 修正TIPTOP拋單，log出現ESS的相關內容[補修正]", "提交日期": "2024-05-22 16:28:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1f2042716913ed67380b35a416da6c991fcb648e", "commit_訊息": "[流程引擎] C01-20240516007 修正因核決關卡名稱導致流程派送異常問題", "提交日期": "2024-05-22 16:09:40", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "31e2db0ff4ef7c31c681e7a7cf585d6d3a734836", "commit_訊息": "[Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正[补]", "提交日期": "2024-05-21 18:58:27", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4d3bf516eb7a6217d12fc23cd15b928b0d33aa0c", "commit_訊息": "[Web]C01-20240516002 修正流程設定授權人員，外部連結查看表單資訊會顯示此人員無權限訪問", "提交日期": "2024-05-21 17:59:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c29803fd774020ea23fa5383e0a8bb416b3a1fe2", "commit_訊息": "[WorkFlow]Q00-20240521001 WorkFlow表單轉成RWD響應式表單", "提交日期": "2024-05-21 15:17:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@workflow/form-default/APS\\346\\216\\241\\350\\263\\274\\350\\252\\277\\346\\225\\264\\345\\273\\272\\350\\255\\260\\344\\275\\234\\346\\245\\255(APSI11).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/APS\\350\\243\\275\\344\\273\\244\\350\\243\\275\\347\\250\\213\\350\\252\\277\\346\\225\\264\\345\\273\\272\\350\\255\\260\\344\\275\\234\\346\\245\\255(APSI13).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/APS\\350\\243\\275\\344\\273\\244\\350\\252\\277\\346\\225\\264\\345\\273\\272\\350\\255\\260\\344\\275\\234\\346\\245\\255(APSI10).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/APS\\350\\250\\202\\345\\226\\256\\350\\252\\277\\346\\225\\264\\345\\273\\272\\350\\255\\260\\344\\275\\234\\346\\245\\255(APSI09).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/BOM \\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI04).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/BOM\\347\\224\\250\\351\\207\\217\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI02).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/E-BOM\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI12).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/PACKING LIST \\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI06).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/PACKING LIST\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI43).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/SI\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IPSI04).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WAFER BANK\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI11).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WAFER \\350\\253\\213\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI15).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\344\\273\\230\\346\\254\\276\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACPI03).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\345\\273\\240\\345\\244\\226\\345\\212\\240\\345\\267\\245\\345\\207\\272\\345\\273\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI17).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\345\\273\\240\\345\\244\\226\\345\\212\\240\\345\\267\\245\\345\\223\\201\\351\\201\\213\\345\\233\\236\\351\\200\\262\\345\\273\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI18).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\346\\251\\237\\345\\231\\250\\350\\250\\255\\345\\202\\231\\351\\200\\262\\345\\207\\272\\345\\217\\243\\347\\225\\260\\345\\213\\225\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI14).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI08).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI05).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\344\\277\\235\\347\\250\\205\\350\\262\\250\\345\\223\\201\\345\\207\\272\\345\\273\\240\\344\\277\\256\\347\\220\\206\\346\\252\\242\\346\\270\\254\\346\\210\\226\\346\\240\\270\\346\\250\\243\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI15).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\344\\277\\241\\347\\224\\250\\347\\213\\200\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI11).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\200\\237\\345\\207\\272\\345\\205\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI11)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\200\\237\\345\\207\\272\\345\\205\\245\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI12)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\205\\266\\344\\273\\226\\345\\207\\272\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI13).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\207\\272\\345\\217\\243\\350\\262\\273\\347\\224\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI10).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\207\\272\\345\\273\\240\\346\\224\\276\\350\\241\\214\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI09).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\207\\272\\345\\273\\240\\346\\224\\276\\350\\241\\214\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI12).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI05).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI62).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\212\\240\\345\\267\\245\\346\\240\\270\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(MOCI10).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI19).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI58).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI20).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI59).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\223\\201\\350\\231\\237\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI24).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\240\\261\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI05).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\244\\232\\345\\270\\263\\346\\234\\254\\346\\234\\203\\350\\250\\210\\345\\202\\263\\347\\245\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI62).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\247\\224\\345\\244\\226\\345\\267\\245\\345\\226\\256\\351\\226\\213\\347\\253\\213(IDLI33).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\256\\242\\346\\210\\266\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI06).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\256\\242\\346\\210\\266\\350\\263\\207\\346\\226\\231\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI15).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\345\\272\\253\\345\\255\\230\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI05)[GPSD260030].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\207\\211\\344\\273\\230\\346\\206\\221\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACPI02).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\211\\271\\346\\254\\241\\346\\216\\241\\350\\263\\274\\350\\250\\210\\345\\212\\203\\347\\266\\255\\350\\255\\267-\\344\\276\\235\\345\\223\\201\\350\\231\\237(LRPI03).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\211\\271\\346\\254\\241\\347\\224\\237\\347\\224\\242\\350\\250\\210\\345\\212\\203\\347\\266\\255\\350\\255\\267-\\344\\276\\235\\345\\223\\201\\350\\231\\237(LRPI01).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\213\\206\\350\\247\\243\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI06).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\216\\222\\347\\250\\213\\350\\246\\217\\345\\212\\203\\344\\275\\234\\346\\245\\255(APSQ02).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\216\\241\\350\\263\\274\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI07)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI08)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\224\\266\\346\\254\\276\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACRI03).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\225\\231\\350\\202\\262\\350\\250\\223\\347\\267\\264\\347\\224\\263\\350\\253\\213\\345\\240\\261\\345\\220\\215\\344\\275\\234\\346\\245\\255(HRSI34).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\226\\260\\345\\256\\242\\346\\210\\266\\347\\224\\263\\350\\253\\213\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI21).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\232\\253\\345\\207\\272\\345\\205\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI19).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\232\\253\\345\\207\\272\\345\\205\\245\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI20).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\234\\203\\350\\250\\210\\345\\202\\263\\347\\245\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI10).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\240\\270\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI03).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\346\\264\\276\\350\\273\\212\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI14).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\347\\265\\204\\345\\220\\210\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI05).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\347\\265\\220\\345\\270\\263\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACRI02).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\243\\275\\351\\200\\240\\345\\221\\275\\344\\273\\244\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(MOCI02).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\243\\275\\351\\200\\240\\345\\221\\275\\344\\273\\244\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(MOCI12).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI07).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\250\\210\\345\\212\\203\\344\\276\\206\\346\\272\\220\\350\\250\\230\\351\\214\\204\\347\\266\\255\\350\\255\\267(LRPI05).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\250\\223\\347\\267\\264\\347\\224\\263\\350\\253\\213\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(HRSI23).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\251\\242\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI14)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\252\\277\\346\\225\\264\\346\\262\\226\\351\\212\\267\\345\\210\\206\\351\\214\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(FCSI04).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\253\\213\\350\\263\\274\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI05)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\253\\213\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI16).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\253\\213\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI16)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\262\\250\\351\\201\\213\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI07).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\345\\240\\261\\345\\273\\242\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI08).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI13).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\212\\230\\350\\210\\212\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI11).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(ASTI24).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\216\\241\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI22).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\224\\266\\345\\233\\236\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI14).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\224\\271\\350\\211\\257\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI06).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\346\\270\\233\\346\\220\\215\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI25).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\347\\247\\273\\350\\275\\211\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI12).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\350\\251\\242\\345\\203\\271\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI20).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\350\\252\\277\\346\\225\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI10).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\350\\253\\213\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI19).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\351\\200\\262\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI23).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\263\\207\\347\\224\\242\\351\\207\\215\\344\\274\\260\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI07).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\350\\275\\211\\346\\222\\245\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI08)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\351\\200\\200\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI11)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\351\\200\\262\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI09)[GP25(PR)].form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\351\\212\\267\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI08).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\351\\212\\267\\351\\200\\200\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI09).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\351\\240\\220\\347\\256\\227\\346\\214\\252\\347\\224\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI23).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/\\351\\240\\220\\347\\256\\227\\350\\277\\275\\345\\212\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI22).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/APS\\346\\216\\241\\350\\263\\274\\350\\252\\277\\346\\225\\264\\345\\273\\272\\350\\255\\260\\344\\275\\234\\346\\245\\255(APSI11).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/APS\\350\\243\\275\\344\\273\\244\\350\\243\\275\\347\\250\\213\\350\\252\\277\\346\\225\\264\\345\\273\\272\\350\\255\\260\\344\\275\\234\\346\\245\\255(APSI13).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/APS\\350\\243\\275\\344\\273\\244\\350\\252\\277\\346\\225\\264\\345\\273\\272\\350\\255\\260\\344\\275\\234\\346\\245\\255(APSI10).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/APS\\350\\250\\202\\345\\226\\256\\350\\252\\277\\346\\225\\264\\345\\273\\272\\350\\255\\260\\344\\275\\234\\346\\245\\255(APSI09).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/BOM \\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI04).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/BOM\\347\\224\\250\\351\\207\\217\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI02).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/E-BOM\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI12).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/PACKING LIST \\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI06).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/PACKING LIST\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI43).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/SI\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IPSI04).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/WAFER BANK\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI11).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/WAFER \\350\\253\\213\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDLI15).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\344\\273\\230\\346\\254\\276\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACPI03).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\344\\277\\235\\347\\250\\205\\345\\273\\240\\345\\244\\226\\345\\212\\240\\345\\267\\245\\345\\207\\272\\345\\273\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI17).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\344\\277\\235\\347\\250\\205\\345\\273\\240\\345\\244\\226\\345\\212\\240\\345\\267\\245\\345\\223\\201\\351\\201\\213\\345\\233\\236\\351\\200\\262\\345\\273\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI18).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\344\\277\\235\\347\\250\\205\\346\\251\\237\\345\\231\\250\\350\\250\\255\\345\\202\\231\\351\\200\\262\\345\\207\\272\\345\\217\\243\\347\\225\\260\\345\\213\\225\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI14).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\344\\277\\235\\347\\250\\205\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI08).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\344\\277\\235\\347\\250\\205\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI05).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\344\\277\\235\\347\\250\\205\\350\\262\\250\\345\\223\\201\\345\\207\\272\\345\\273\\240\\344\\277\\256\\347\\220\\206\\346\\252\\242\\346\\270\\254\\346\\210\\226\\346\\240\\270\\346\\250\\243\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI15).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\344\\277\\241\\347\\224\\250\\347\\213\\200\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI11).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\200\\237\\345\\207\\272\\345\\205\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI11)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\200\\237\\345\\207\\272\\345\\205\\245\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI12)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\205\\266\\344\\273\\226\\345\\207\\272\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI13).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\207\\272\\345\\217\\243\\350\\262\\273\\347\\224\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI10).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\207\\272\\345\\273\\240\\346\\224\\276\\350\\241\\214\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCHI09).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\207\\272\\345\\273\\240\\346\\224\\276\\350\\241\\214\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BCSI12).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI05).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI62).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\212\\240\\345\\267\\245\\346\\240\\270\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(MOCI10).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI19).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI58).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI20).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\220\\210\\347\\264\\204\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI59).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\223\\201\\350\\231\\237\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI24).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\240\\261\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI05).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\244\\232\\345\\270\\263\\346\\234\\254\\346\\234\\203\\350\\250\\210\\345\\202\\263\\347\\245\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI62).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\247\\224\\345\\244\\226\\345\\267\\245\\345\\226\\256\\351\\226\\213\\347\\253\\213(IDLI33).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\256\\242\\346\\210\\266\\350\\250\\202\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI06).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\256\\242\\346\\210\\266\\350\\263\\207\\346\\226\\231\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI15).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\345\\272\\253\\345\\255\\230\\347\\225\\260\\345\\213\\225\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI05)[GPSD260030].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\207\\211\\344\\273\\230\\346\\206\\221\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACPI02).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\211\\271\\346\\254\\241\\346\\216\\241\\350\\263\\274\\350\\250\\210\\345\\212\\203\\347\\266\\255\\350\\255\\267-\\344\\276\\235\\345\\223\\201\\350\\231\\237(LRPI03).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\211\\271\\346\\254\\241\\347\\224\\237\\347\\224\\242\\350\\250\\210\\345\\212\\203\\347\\266\\255\\350\\255\\267-\\344\\276\\235\\345\\223\\201\\350\\231\\237(LRPI01).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\213\\206\\350\\247\\243\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI06).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\216\\222\\347\\250\\213\\350\\246\\217\\345\\212\\203\\344\\275\\234\\346\\245\\255(APSQ02).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\216\\241\\350\\263\\274\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI07)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI08)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\224\\266\\346\\254\\276\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACRI03).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\225\\231\\350\\202\\262\\350\\250\\223\\347\\267\\264\\347\\224\\263\\350\\253\\213\\345\\240\\261\\345\\220\\215\\344\\275\\234\\346\\245\\255(HRSI34).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\226\\260\\345\\256\\242\\346\\210\\266\\347\\224\\263\\350\\253\\213\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI21).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\232\\253\\345\\207\\272\\345\\205\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI19).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\232\\253\\345\\207\\272\\345\\205\\245\\346\\255\\270\\351\\202\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(IDL)(IDLI20).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\234\\203\\350\\250\\210\\345\\202\\263\\347\\245\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI10).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\240\\270\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI03).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\346\\264\\276\\350\\273\\212\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI14).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\347\\265\\204\\345\\220\\210\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(BOMI05).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\347\\265\\220\\345\\270\\263\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACRI02).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\243\\275\\351\\200\\240\\345\\221\\275\\344\\273\\244\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(MOCI02).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\243\\275\\351\\200\\240\\345\\221\\275\\344\\273\\244\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(MOCI12).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI07).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\250\\210\\345\\212\\203\\344\\276\\206\\346\\272\\220\\350\\250\\230\\351\\214\\204\\347\\266\\255\\350\\255\\267(LRPI05).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\250\\223\\347\\267\\264\\347\\224\\263\\350\\253\\213\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(HRSI23).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\251\\242\\345\\203\\271\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI14)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\252\\277\\346\\225\\264\\346\\262\\226\\351\\212\\267\\345\\210\\206\\351\\214\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(FCSI04).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\253\\213\\350\\263\\274\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI05)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\253\\213\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI16).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\253\\213\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI16)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\262\\250\\351\\201\\213\\351\\200\\232\\347\\237\\245\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(EPSI07).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\345\\240\\261\\345\\273\\242\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI08).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI13).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\346\\212\\230\\350\\210\\212\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI11).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(ASTI24).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\346\\216\\241\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI22).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\346\\224\\266\\345\\233\\236\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI14).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\346\\224\\271\\350\\211\\257\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI06).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\346\\270\\233\\346\\220\\215\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI25).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\347\\247\\273\\350\\275\\211\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI12).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\350\\251\\242\\345\\203\\271\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI20).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\350\\252\\277\\346\\225\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI10).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\350\\253\\213\\350\\263\\274\\350\\263\\207\\346\\226\\231\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI19).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\351\\200\\262\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI23).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\263\\207\\347\\224\\242\\351\\207\\215\\344\\274\\260\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI07).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\350\\275\\211\\346\\222\\245\\345\\226\\256\\346\\223\\232\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(INVI08)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\351\\200\\200\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI11)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\351\\200\\262\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PURI09)[GP25(PR)].form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\351\\212\\267\\350\\262\\250\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI08).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\351\\212\\267\\351\\200\\200\\345\\226\\256\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COPI09).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\351\\240\\220\\347\\256\\227\\346\\214\\252\\347\\224\\250\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI23).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/rwd-form/\\351\\240\\220\\347\\256\\227\\350\\277\\275\\345\\212\\240\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ACTI22).form\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 178}, {"commit_hash": "d4e9f504978fa649b3ab8723ae75c5e83cb1fed2", "commit_訊息": "[Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正", "提交日期": "2024-05-21 13:39:19", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2618a6694f111e504b08ece573e993b2b794533d", "commit_訊息": "[組織同步] C01-20240517001 HR同步，新增職務定義檢查", "提交日期": "2024-05-21 11:43:24", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "827a9aacf0df5f661c0aea0ef36b90354777942a", "commit_訊息": "[Web]C01-20240517012 修正开窗类型为使用者可以勾选\"前置组织代号\"的问题", "提交日期": "2024-05-21 10:50:43", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5bdd449266b82b3cfb22d80cb54135340e0af31", "commit_訊息": "[PRODT]C01-20240513002 修正Web流程管理工具的核決層級關卡中設定過參考活動為自定義時殘留的贓資料會導致流程無法簽入的問題", "提交日期": "2024-05-20 12:06:56", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "691c559c273d77e82a0b34a68af6859de4241b73", "commit_訊息": "[流程引擎]C01-20240516004 修正TIPTOP拋單，log出現ESS的相關內容", "提交日期": "2024-05-20 10:37:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a8e0b25a8f1eb65e60911ce62cb2ee7571d6e43f", "commit_訊息": "[EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件", "提交日期": "2024-05-17 18:05:12", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/EBGModule/EBGCreateForm.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@ebg/form-default/EBGForm.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "af4d8a0f7a33d72ab9547a6bfe71a4b638332630", "commit_訊息": "[PRODT]C01-20240509001 修正流程實例中的詳細流程圖中連接線條件名稱會覆蓋問題", "提交日期": "2024-05-17 15:26:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d795032830797c25c677ac98232356bc9001c831", "commit_訊息": "[Athena]Q00-20240517001 應用Token出貨改成固定參數", "提交日期": "2024-05-17 12:06:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e57cd6857079c54bf5685e548355ad2ed8ace948", "commit_訊息": "[Web]C01-20240516006 修正简易sql被过滤表名导致查询失败的问题", "提交日期": "2024-05-17 10:52:46", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18d480e19bf33e968c549dfd5c4335edb4b562e0", "commit_訊息": "[流程引擎] C01-20240508002 修正流程在併簽關卡，刪除流程時未釋放連線數問題", "提交日期": "2024-05-16 17:28:25", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "926455dfc73a64d7ff292c3372f11d1a160d4ca9", "commit_訊息": "[内部]Q00-20240516001 修正查询sql包含多组\"order by\",导致资料选取注册器及SQL注册器查询失败问题", "提交日期": "2024-05-16 15:45:10", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4e7eee70e86ce8c76f50d418d5e629f6b9ba077e", "commit_訊息": "[流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法(補)", "提交日期": "2024-05-16 13:39:01", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6b30a876676c2b7fe53ccef61a35d8b16799e1cf", "commit_訊息": "[Web]C01-20240515001 修正客制開窗sql语句包含多组order by，sql會查詢失敗的問題", "提交日期": "2024-05-16 08:57:01", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3cc01e50b9120fa6810ecb8417dff3fd7062a6b1", "commit_訊息": "[Web]C01-20240514002 修正企業流程監控→加總→已處理工作量,選取多人或多部門產生統計圖報錯的問題[補]", "提交日期": "2024-05-15 15:06:00", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99021b78c8410a48c5e0e46a23f524331a3571a9", "commit_訊息": "[Web]C01-20240514002 修正企業流程監控→加總→已處理工作量,選取多人或多部門產生統計圖報錯的問題", "提交日期": "2024-05-15 10:00:05", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aab7c5cefc14401bb33c98f2bcd825b1011890e4", "commit_訊息": "[TIPTOP]C01-20240510004 修正流程設定授權人員，外部連結查看該流程會顯示此人員無權限訪問", "提交日期": "2024-05-14 13:56:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b95d8366a74d96d0b03f5ff6c112c72e8e66f278", "commit_訊息": "[内部]Q00-20240514001 修改系統設定trace.process.hidden.process.package.ids.prefix的描述", "提交日期": "2024-05-14 10:21:01", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ff863af0d7230c271128d88e6483c18369e95a8e", "commit_訊息": "[Web] C01-20240509007 \"模擬使用者\"可以授權給一般使用者，會讓一般使用者可以模擬最高權限的administrator问题修正", "提交日期": "2024-05-11 09:50:36", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4e3ad72300d14366f6a97b7e6e02d254dc94634a", "commit_訊息": "[Web]C01-20240509004 修正grid多栏位格线对不齐的问题", "提交日期": "2024-05-10 17:31:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea10eccb8b2ee27910407ed6a42d107940490413", "commit_訊息": "[Web]V00-20240508001 修正流程主旨範本設定<#ActivityId>、 <#ActivityName>显示N.A.", "提交日期": "2024-05-10 14:04:21", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc2a8d49ae4adec5782277f1397429ee075a2db6", "commit_訊息": "[Web] C01-20240509002 系统设定中变更密码参数需要重启服务才生效问题修正", "提交日期": "2024-05-10 11:06:37", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "241b4a3413755de59a29a7532297225665dcf54c", "commit_訊息": "[流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法(補)", "提交日期": "2024-05-10 09:17:16", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/CommonProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5b6ea52e3d6bfac66d65ce753ae26ba6327ea56b", "commit_訊息": "[TIPTOP]C01-20240429008 修正回寫失敗訊息無法顯示在畫面上", "提交日期": "2024-05-09 17:21:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4df1668a3d0327afaeb54a05bf0ccb3a4b202b45", "commit_訊息": "[BPM APP]C01-20240508004 修正結案的流程從企業微信推播進入不會導向追蹤表單畫面問題", "提交日期": "2024-05-09 17:07:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29ff5a82951e912df4c0c774e8e08afba909bd32", "commit_訊息": "[流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法", "提交日期": "2024-05-09 15:09:21", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/orgAnalyze/OrgAnalyzeManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/CommonProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DoneWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDataSearchListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ParamForProcInvokingListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignTrackListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimpleExpenseAccountItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimplePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SuspendedInvokeActListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileInvokableProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 29}, {"commit_hash": "c5277d92c4069177af920ea66363750600ae1294", "commit_訊息": "[內部]Q00-20240509001 調整重新向資料庫取SystemVariable內容發生錯誤時的log訊息", "提交日期": "2024-05-09 11:29:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c8d94f56ceee5960e4c0da8d6b93bf0c5f2d770", "commit_訊息": "[流程設計師]C01-20240416006 修正流程走到核決關卡後點擊待辦清單的流程會無法正常打開", "提交日期": "2024-05-09 10:35:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "692c71940bffb750fea7fd52039a162c4624d5a2", "commit_訊息": "[流程引擎] C01-20240508002 修正加簽關卡後，沒有釋放連線數", "提交日期": "2024-05-08 17:29:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecebd893fba4677dccd2004103991703aaff8083", "commit_訊息": "[流程引擎]C01-20240502001 修正在核決權限關卡加簽，預解析会重复出现核決權限表名稱的问题", "提交日期": "2024-05-08 11:43:06", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e90dd43032fa6e49124dbd0795a738ba98cd0381", "commit_訊息": "[Web]Q00-20240508001 修正系統設定發起人看不到追蹤流程之設定", "提交日期": "2024-05-08 10:43:00", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52e645304b4fa78bbd2dfbd075914b49a6214c85", "commit_訊息": "[Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log(補)", "提交日期": "2024-05-08 10:24:27", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6cb3b458d927a16431d4ba30464c28524b3496a9", "commit_訊息": "[Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log", "提交日期": "2024-05-07 18:01:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d46cf5edc2b3feb12544619d83e7a09fb8b698a5", "commit_訊息": "[流程引擎] Q00-20240507001 新增單筆封存流程還原接口的防呆，若無流程封存模組則不可調用", "提交日期": "2024-05-07 14:57:51", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NaNaXWebHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5a5a6891050937114bd7b9c4dea66bcd95afa1a1", "commit_訊息": "[MPT]Q00-20240506001 調整ecp_source_connect表ip欄位上限", "提交日期": "2024-05-06 16:23:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.10.2_DDL_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.10.2_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.10.2_DDL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "b3f3b5df88f2091234271ef604ef4bca4e74f8e3", "commit_訊息": "[SYSDT]C01-20240502002 修正設計師使用權限管理中若人員的最後工作日設為未來日期時會無法顯示使用者問題", "提交日期": "2024-05-03 10:00:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb52917dd8eee5dd3defce3d78abf0165a06ad0a", "commit_訊息": "[PLM]增加 Digiwin PLM 整合，與 Open PLM 共用序號[補]", "提交日期": "2024-05-03 09:42:25", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b3a368fe4db47135c65f3c7cc64e7b7143263719", "commit_訊息": "[PLM]增加 Digiwin PLM 整合，與 Open PLM 共用序號", "提交日期": "2024-05-02 16:49:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/UpdateProcessStatusBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIServiceLocator.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIServicePortType.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIServicePortTypeProxy.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIServiceSoap11BindingStub.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.2_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "7a22a7a5ed1619dfec29835657d7ff4cb90317b4", "commit_訊息": "[SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限", "提交日期": "2024-05-02 16:34:00", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "bd7f1073158670612aebb095bde43299b5559c5a", "commit_訊息": "Revert \"[SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限\"", "提交日期": "2024-05-02 16:17:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "b9f0690e91324810de4c6c1d3187e472954839af", "commit_訊息": "[SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限", "提交日期": "2024-05-02 16:04:56", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "ed0c6bc78573b12e86deecfac367ebd479610e1f", "commit_訊息": "[BPM APP]C01-20240430007 調整當郵件內容為空時補上空格讓企業微信可以正常推播", "提交日期": "2024-05-02 14:09:00", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "377122fff39f288deec6dd3687388ef198464dc3", "commit_訊息": "[PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制", "提交日期": "2024-04-30 15:47:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a1a5287cfd39ae431d123744fd93ae55eca7654c", "commit_訊息": "[Web] Q00-20240429004 報表設計器儲存報 SqlConditionList is too long问题修正", "提交日期": "2024-04-29 17:39:16", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e28fbad775ede523a7a518fa4cc8ec3d5d28779f", "commit_訊息": "[T100]Q00-20240429001 新增RWD響應式表單", "提交日期": "2024-04-29 14:12:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/ECR\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(abmt500).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/Invoice\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt620).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/MPS\\350\\250\\210\\345\\212\\203\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apst310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/RMA\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(armt100).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\270\\200\\350\\210\\254\\345\\224\\256\\345\\203\\271\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(aprt112).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\270\\200\\350\\210\\254\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt480).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\270\\200\\350\\210\\254\\346\\216\\241\\350\\263\\274\\350\\251\\242\\345\\203\\271\\344\\275\\234\\346\\245\\255(apmt420).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\270\\200\\350\\210\\254\\346\\240\\270\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt440).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\270\\200\\350\\210\\254\\351\\200\\262\\345\\203\\271\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(aprt111).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\270\\200\\351\\232\\216\\346\\256\\265\\350\\252\\277\\346\\222\\245\\344\\275\\234\\346\\245\\255(aint330).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\345\\207\\206\\345\\205\\245\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt801).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt100).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\350\\255\\211\\347\\205\\247\\347\\225\\260\\345\\213\\225\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt820).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\345\\207\\206\\345\\205\\245\\345\\217\\212\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt800).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\345\\220\\210\\347\\264\\204\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt301).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt200).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\265\\220\\347\\256\\227\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt340).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\270\\276\\346\\225\\210\\350\\251\\225\\346\\240\\270\\345\\256\\232\\346\\200\\247\\345\\260\\210\\346\\241\\210\\350\\251\\225\\345\\210\\206\\344\\275\\234\\346\\245\\255(apmt811).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\270\\276\\346\\225\\210\\350\\251\\225\\346\\240\\270\\347\\266\\234\\345\\220\\210\\345\\276\\227\\345\\210\\206\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(apmt814).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\262\\250\\346\\254\\276\\345\\260\\215\\345\\270\\263\\344\\275\\234\\346\\245\\255(aapt110).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\262\\273\\347\\224\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt320).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\277\\203\\351\\212\\267\\345\\203\\271\\346\\240\\274\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(aprt113).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\277\\203\\351\\212\\267\\350\\253\\207\\345\\210\\244\\346\\242\\235\\344\\273\\266\\347\\224\\263\\350\\253\\213(aprt310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\344\\277\\241\\347\\224\\250\\350\\266\\205\\351\\231\\220\\346\\224\\276\\350\\241\\214\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt140).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\200\\237\\350\\262\\250\\345\\207\\272\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt542).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\200\\237\\350\\262\\250\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt521).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\200\\237\\350\\262\\250\\350\\250\\202\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt501).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\200\\237\\350\\262\\250\\351\\202\\204\\351\\207\\217\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt591).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\204\\237\\351\\202\\204\\346\\234\\254\\346\\201\\257\\347\\266\\255\\350\\255\\267(afmt170).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\205\\247\\351\\203\\250\\347\\265\\220\\347\\256\\227\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt740).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\205\\266\\344\\273\\226\\346\\207\\211\\344\\273\\230\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt301).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\205\\266\\344\\273\\226\\346\\207\\211\\346\\224\\266\\345\\270\\263\\346\\254\\276\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt330).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\207\\272\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt540).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\207\\272\\350\\262\\250\\346\\207\\211\\346\\224\\266\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\207\\272\\350\\262\\250\\347\\260\\275\\346\\224\\266\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt580).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\207\\272\\350\\262\\250\\347\\260\\275\\351\\200\\200\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt590).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt520).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\210\\206\\351\\212\\267\\345\\207\\272\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt540).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\210\\206\\351\\212\\267\\345\\207\\272\\350\\262\\250\\347\\260\\275\\346\\224\\266\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt580).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\210\\206\\351\\212\\267\\345\\207\\272\\350\\262\\250\\347\\260\\275\\351\\200\\200\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt590).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\210\\206\\351\\212\\267\\350\\250\\202\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt500).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\210\\206\\351\\212\\267\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt510).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\210\\206\\351\\212\\267\\351\\212\\267\\351\\200\\200\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt600).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\210\\270\\347\\250\\256\\345\\237\\272\\346\\234\\254\\350\\263\\207\\346\\226\\231\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(agct300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\214\\205\\350\\243\\235\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt610).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\223\\201\\350\\263\\252\\346\\252\\242\\351\\251\\227\\350\\250\\230\\351\\214\\204\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aqct300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\223\\201\\350\\263\\252\\347\\225\\260\\345\\270\\270\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(aqct310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\225\\206\\345\\223\\201\\345\\207\\206\\345\\205\\245\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(artt300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\233\\272\\350\\263\\207\\351\\240\\220\\347\\256\\227\\347\\267\\250\\350\\243\\275\\344\\275\\234\\346\\245\\255(abgt815).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\234\\260\\347\\243\\205\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt640).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\240\\261\\345\\267\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255-\\345\\226\\256\\347\\255\\206\\345\\274\\217(asft335).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\240\\264\\345\\234\\260\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(amht204).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\244\\226\\345\\214\\257\\344\\272\\244\\346\\230\\223\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afmt534).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\244\\226\\345\\214\\257\\345\\220\\210\\347\\264\\204\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afmt533).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\244\\226\\345\\214\\257\\346\\234\\237\\346\\234\\253\\345\\205\\254\\345\\205\\201\\345\\203\\271\\345\\200\\274\\347\\266\\255\\350\\255\\267(afmt552).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\244\\232\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(\\346\\265\\201\\351\\200\\232)(aapt815).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\345\\200\\211\\351\\200\\200\\344\\275\\234\\346\\245\\255(apmt581).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\344\\275\\234\\346\\245\\255(apmt571).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt481).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt491).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt501).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\344\\275\\234\\346\\245\\255(apmt521).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\350\\251\\242\\345\\203\\271\\344\\275\\234\\346\\245\\255(apmt421).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\344\\275\\234\\346\\245\\255(apmt561).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\247\\224\\345\\244\\226\\346\\240\\270\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt441).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\256\\232\\345\\255\\230\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afmt531).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\256\\242\\346\\210\\266\\344\\277\\241\\347\\224\\250\\351\\241\\215\\345\\272\\246\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt206).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\256\\242\\346\\210\\266\\345\\207\\206\\345\\205\\245\\345\\217\\212\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt800).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\256\\242\\346\\210\\266\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt200).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\256\\242\\346\\210\\266\\350\\262\\250\\346\\254\\276\\345\\260\\215\\345\\270\\263\\344\\275\\234\\346\\245\\255(aist310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\256\\242\\350\\250\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt700).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\257\\246\\345\\234\\260\\347\\233\\244\\351\\273\\236\\350\\250\\210\\347\\225\\253\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint820).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\260\\210\\346\\253\\203\\345\\220\\210\\347\\264\\204\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt401).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\260\\210\\346\\253\\203\\346\\226\\260\\345\\225\\206\\345\\223\\201\\345\\274\\225\\351\\200\\262\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(artt407).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\344\\270\\200\\350\\210\\254\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft323).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\345\\200\\222\\346\\211\\243\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft324).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\345\\200\\222\\346\\211\\243\\351\\240\\230\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft314).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\345\\234\\250\\350\\243\\275\\344\\270\\213\\351\\232\\216\\346\\226\\231\\345\\240\\261\\345\\273\\242\\344\\275\\234\\346\\245\\255(asft339).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\345\\256\\214\\345\\267\\245\\345\\205\\245\\345\\272\\253\\344\\275\\234\\346\\245\\255(asft340).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\346\\210\\220\\345\\245\\227\\347\\231\\274\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft311).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\346\\210\\220\\345\\245\\227\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft321).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\346\\254\\240\\346\\226\\231\\350\\243\\234\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft313).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\347\\225\\266\\347\\253\\231\\344\\270\\213\\347\\267\\232\\344\\275\\234\\346\\245\\255(asft337).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\347\\225\\266\\347\\253\\231\\345\\240\\261\\345\\273\\242\\344\\275\\234\\346\\245\\255(asft336).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\350\\243\\275\\347\\250\\213\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft801).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\350\\243\\275\\347\\250\\213\\351\\207\\215\\345\\267\\245\\350\\275\\211\\345\\207\\272\\344\\275\\234\\346\\245\\255(asft338).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft800).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\350\\266\\205\\351\\240\\230\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft312).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\267\\245\\345\\226\\256\\350\\266\\205\\351\\240\\230\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft322).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\270\\202\\345\\240\\264\\346\\216\\250\\345\\273\\243\\346\\264\\273\\345\\213\\225\\346\\240\\270\\351\\212\\267\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt605).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\270\\202\\345\\240\\264\\346\\216\\250\\345\\273\\243\\346\\264\\273\\345\\213\\225\\347\\224\\263\\350\\253\\213(astt604).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\270\\263\\351\\275\\241\\345\\217\\212\\345\\243\\236\\345\\270\\263\\346\\217\\220\\345\\210\\227\\347\\266\\255\\350\\255\\267(axrt940).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\272\\224\\346\\224\\266\\347\\245\\250\\346\\215\\256\\346\\224\\266\\347\\245\\250\\344\\275\\234\\344\\270\\232(anmt510).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\272\\253\\344\\275\\215\\345\\217\\226\\346\\266\\210\\347\\225\\231\\347\\275\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint161).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\272\\253\\345\\255\\230\\345\\240\\261\\345\\273\\242\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(aint310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\272\\253\\345\\255\\230\\345\\240\\261\\345\\273\\242\\351\\231\\244\\345\\270\\263\\344\\275\\234\\346\\245\\255(aint311).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\272\\253\\345\\255\\230\\346\\234\\211\\346\\225\\210\\346\\227\\245\\346\\234\\237\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(aint180).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\272\\253\\345\\255\\230\\347\\225\\260\\345\\270\\270\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint170).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\275\\210\\346\\200\\247\\346\\216\\241\\350\\263\\274\\345\\203\\271\\346\\240\\274\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt128).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\345\\275\\210\\346\\200\\247\\351\\212\\267\\345\\224\\256\\345\\203\\271\\346\\240\\274\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt128).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\204\\217\\345\\220\\221\\345\\215\\224\\350\\255\\260\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt811).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\207\\211\\344\\273\\230\\345\\214\\257\\346\\254\\276\\347\\225\\260\\345\\213\\225\\344\\275\\234\\346\\245\\255(anmt480).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\207\\211\\344\\273\\230\\345\\214\\257\\346\\254\\276\\351\\226\\213\\347\\253\\213\\344\\275\\234\\346\\245\\255(anmt460).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\207\\211\\344\\273\\230\\345\\270\\263\\346\\254\\276\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\207\\211\\344\\273\\230\\345\\276\\205\\346\\212\\265\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt340).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\207\\211\\344\\273\\230\\346\\240\\270\\351\\212\\267\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt420).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\207\\211\\346\\224\\266\\345\\276\\205\\346\\212\\265\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt340).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\212\\225\\350\\263\\207\\347\\220\\206\\350\\262\\241\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afmt532).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\212\\225\\350\\263\\207\\347\\224\\263\\350\\253\\213\\345\\226\\256(afmt510).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\212\\225\\350\\263\\207\\350\\263\\274\\350\\262\\267\\345\\270\\263\\345\\213\\231\\345\\226\\256(afmt535).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\345\\273\\266\\346\\234\\237\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt803).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt801).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\347\\265\\202\\346\\255\\242\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt805).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\350\\262\\273\\347\\224\\250\\345\\204\\252\\346\\203\\240\\347\\224\\263\\350\\253\\213(astt802).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\350\\262\\273\\347\\224\\250\\346\\250\\231\\346\\272\\226\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(astt806).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\351\\235\\242\\347\\251\\215\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213(astt804).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\345\\200\\211\\351\\200\\200\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt580).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\345\\203\\271\\346\\240\\274\\350\\241\\250\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt129).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt570).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt880).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt490).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt500).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt840).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt530).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt862).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt520).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt860).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\350\\243\\234\\345\\267\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aprt601).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt510).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt850).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\351\\200\\200\\345\\273\\240\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt890).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\351\\240\\220\\344\\273\\230\\346\\206\\221\\350\\255\\211\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt560).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt870).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\224\\257\\345\\207\\272\\351\\241\\236\\345\\220\\210\\345\\220\\214\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt820).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\224\\266\\346\\254\\276\\346\\240\\270\\351\\212\\267\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt400).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\224\\276\\350\\241\\214\\345\\226\\256\\347\\256\\241\\345\\210\\266\\344\\275\\234\\346\\245\\255(abxt400).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\226\\231\\344\\273\\266\\346\\211\\277\\350\\252\\215\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(abmt400).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\226\\231\\344\\273\\266\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aimt300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\226\\231\\344\\273\\266\\350\\243\\275\\347\\250\\213\\350\\263\\207\\346\\226\\231\\346\\226\\260\\345\\242\\236\\343\\200\\201\\344\\277\\256\\346\\224\\271\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(aect801).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\232\\253\\344\\274\\260\\346\\207\\211\\346\\224\\266\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt320).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\234\\203\\345\\223\\241\\345\\215\\241\\345\\204\\262\\345\\200\\274\\351\\207\\221\\351\\241\\215\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(ammt428).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\234\\203\\345\\223\\241\\345\\215\\241\\347\\250\\256\\345\\237\\272\\346\\234\\254\\350\\263\\207\\346\\226\\231\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(ammt320).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\234\\203\\345\\223\\241\\345\\215\\241\\347\\251\\215\\351\\273\\236\\350\\252\\277\\346\\225\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(ammt421).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\234\\237\\345\\210\\245\\350\\262\\273\\347\\224\\250\\351\\240\\220\\347\\256\\227\\347\\250\\275\\350\\246\\210(abgt635).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\234\\237\\346\\234\\253\\345\\205\\254\\345\\205\\201\\345\\203\\271\\345\\200\\274\\347\\266\\255\\350\\255\\267(afmt551).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\346\\264\\276\\350\\273\\212\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt630).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt532).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt863).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt522).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt861).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\204\\241\\350\\250\\202\\345\\226\\256\\345\\207\\272\\350\\262\\250\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt541).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\207\\237\\351\\201\\213\\346\\223\\232\\351\\273\\236ECN\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(abmt310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\207\\237\\351\\201\\213\\346\\223\\232\\351\\273\\236\\345\\244\\232\\344\\270\\273\\344\\273\\266ECN\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(abmt311).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\215\\250\\347\\253\\213\\351\\234\\200\\346\\261\\202\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apst300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\224\\237\\351\\256\\256\\345\\203\\271\\346\\240\\274\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(aprt121).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\224\\242\\345\\223\\201\\347\\265\\204\\345\\220\\210\\345\\214\\205\\350\\243\\235\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint390).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\231\\274\\347\\245\\250\\350\\253\\213\\346\\254\\276\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt415).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\247\\237\\350\\263\\203\\350\\262\\273\\347\\224\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt810).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\344\\273\\243\\345\\242\\212\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt606).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\345\\220\\210\\347\\264\\204\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt601).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\347\\265\\220\\347\\256\\227\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt640).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\350\\262\\273\\347\\224\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt620).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\207\\252\\346\\234\\211\\346\\226\\260\\345\\225\\206\\345\\223\\201\\345\\274\\225\\351\\200\\262\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(artt406).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\207\\252\\347\\207\\237\\345\\225\\206\\345\\223\\201\\345\\274\\225\\351\\200\\262\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(artt405).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\236\\215\\350\\263\\207\\345\\220\\210\\345\\220\\214\\347\\266\\255\\350\\255\\267(afmt035).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\236\\215\\350\\263\\207\\347\\224\\263\\350\\253\\213\\345\\226\\256(afmt015).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\236\\215\\350\\263\\207\\350\\263\\207\\351\\207\\221\\345\\210\\260\\345\\270\\263\\347\\266\\255\\350\\255\\267(afmt140).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\246\\201\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt830).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\246\\201\\350\\262\\250\\345\\226\\256\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt835).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\250\\202\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt500).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt510).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\250\\202\\351\\207\\221\\351\\240\\220\\346\\224\\266\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\252\\277\\346\\222\\245\\345\\267\\256\\347\\225\\260\\350\\252\\277\\346\\225\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint520).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\252\\277\\346\\222\\245\\347\\224\\263\\350\\253\\213\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint320).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\253\\213\\350\\263\\274\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt400).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\253\\213\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt410).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\262\\273\\347\\224\\250\\345\\240\\261\\346\\224\\257\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt330).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\262\\273\\347\\224\\250\\346\\250\\231\\346\\272\\226\\350\\250\\255\\345\\256\\232\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt252).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\262\\273\\347\\224\\250\\351\\240\\220\\346\\224\\257\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(aapt331).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\346\\272\\220\\346\\255\\270\\351\\202\\204\\344\\275\\234\\346\\245\\255(amrt250).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\346\\272\\220\\347\\266\\255\\344\\277\\256\\345\\267\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(amrt300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\345\\207\\272\\345\\224\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat504).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\345\\240\\261\\345\\273\\242\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat507).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\346\\224\\266\\345\\233\\236\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat450).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat440).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\346\\222\\245\\345\\205\\245\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat491).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\346\\222\\245\\345\\207\\272\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat490).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\346\\224\\271\\350\\211\\257\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat508).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\350\\263\\207\\346\\234\\254\\345\\214\\226\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat400).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\351\\203\\250\\351\\226\\200\\350\\275\\211\\347\\247\\273\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat421).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\351\\207\\215\\344\\274\\260\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat503).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\263\\207\\347\\224\\242\\351\\212\\267\\345\\270\\263\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat506).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\350\\275\\211\\345\\270\\263\\345\\202\\263\\347\\245\\250\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aglt310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\200\\200\\350\\262\\250\\347\\224\\263\\350\\253\\213\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint530).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\200\\345\\255\\230\\346\\224\\266\\346\\224\\257\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(anmt310).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\267\\345\\224\\256\\344\\274\\260\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt400).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\267\\345\\224\\256\\345\\203\\271\\346\\240\\274\\350\\241\\250\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt129).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\267\\345\\224\\256\\345\\220\\210\\347\\264\\204\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt440).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\267\\345\\224\\256\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt450).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\267\\345\\224\\256\\345\\240\\261\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt410).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\267\\345\\224\\256\\346\\240\\270\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt420).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\267\\345\\224\\256\\350\\243\\234\\345\\267\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aprt602).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\212\\267\\351\\200\\200\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt600).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\213\\252\\344\\275\\215\\345\\200\\213\\345\\210\\245\\350\\262\\273\\347\\224\\250\\346\\250\\231\\346\\272\\226\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt253).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\213\\252\\344\\275\\215\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(amht205).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\213\\252\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt832).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\226\\200\\345\\272\\227\\350\\252\\277\\346\\222\\245\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint511).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\226\\200\\345\\272\\227\\350\\263\\207\\346\\272\\220\\345\\215\\224\\350\\255\\260\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(artt230).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\233\\206\\345\\234\\230\\347\\240\\224\\347\\231\\274ECN\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(abmt300).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\233\\206\\345\\234\\230\\347\\240\\224\\347\\231\\274\\345\\244\\232\\344\\270\\273\\344\\273\\266ECN\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(abmt301).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\233\\206\\345\\234\\230\\347\\240\\224\\347\\231\\274\\347\\224\\242\\345\\223\\201\\347\\265\\220\\346\\247\\213\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(abmt200).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\233\\234\\351\\240\\205\\345\\272\\253\\345\\255\\230\\346\\224\\266\\346\\226\\231\\344\\275\\234\\346\\245\\255(aint302).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\233\\234\\351\\240\\205\\345\\272\\253\\345\\255\\230\\347\\231\\274\\346\\226\\231\\344\\275\\234\\346\\245\\255(aint301).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\233\\234\\351\\240\\205\\345\\276\\205\\346\\212\\265\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt341).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\240\\220\\347\\247\\237\\345\\215\\224\\350\\255\\260\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt812).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\240\\220\\347\\256\\227\\346\\214\\252\\347\\224\\250\\347\\266\\255\\350\\255\\267(abgt060).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/RWD\\350\\241\\250\\345\\226\\256/\\351\\240\\220\\347\\256\\227\\350\\277\\275\\345\\212\\240\\347\\266\\255\\350\\255\\267(abgt050).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/ECR\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(abmt500).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/Invoice\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt620).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\270\\200\\350\\210\\254\\345\\224\\256\\345\\203\\271\\350\\252\\277\\345\\203\\271\\344\\275\\234\\346\\245\\255(aprt112).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\270\\200\\350\\210\\254\\351\\200\\262\\345\\203\\271\\350\\252\\277\\345\\203\\271\\344\\275\\234\\346\\245\\255(aprt111).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\270\\200\\351\\232\\216\\346\\256\\265\\350\\252\\277\\346\\222\\245\\344\\275\\234\\346\\245\\255(aint330).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\345\\207\\206\\345\\205\\245\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt801).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt100).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\350\\255\\211\\347\\205\\247\\347\\225\\260\\345\\213\\225\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt820).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\345\\207\\206\\345\\205\\245\\345\\226\\256(apmt800).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt200).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\265\\220\\347\\256\\227\\345\\226\\256(astt340).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\270\\276\\346\\225\\210\\350\\251\\225\\346\\240\\270\\345\\256\\232\\346\\200\\247\\350\\251\\225\\345\\210\\206\\345\\226\\256(apmt811).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\270\\276\\346\\225\\210\\350\\251\\225\\346\\240\\270\\347\\266\\234\\345\\220\\210\\345\\276\\227\\345\\210\\206\\350\\252\\277\\346\\225\\264\\345\\226\\256(apmt814).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\262\\250\\346\\254\\276\\345\\260\\215\\345\\270\\263\\344\\275\\234\\346\\245\\255(aapt110).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\262\\273\\347\\224\\250\\345\\226\\256(astt320).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\277\\203\\351\\212\\267\\350\\252\\277\\345\\203\\271\\344\\275\\234\\346\\245\\255(aprt113).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\344\\277\\203\\351\\212\\267\\350\\253\\207\\345\\210\\244\\347\\224\\263\\350\\253\\213(aprt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\200\\237\\350\\262\\250\\345\\207\\272\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt542).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\200\\237\\350\\262\\250\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt521).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\200\\237\\350\\262\\250\\350\\250\\202\\345\\226\\256(axmt501).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\200\\237\\350\\262\\250\\351\\202\\204\\351\\207\\217\\345\\226\\256(axmt591).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\204\\237\\351\\202\\204\\346\\234\\254\\346\\201\\257\\347\\266\\255\\350\\255\\267(afmt170).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\205\\247\\351\\203\\250\\347\\265\\220\\347\\256\\227\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt740).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\205\\266\\344\\273\\226\\346\\207\\211\\344\\273\\230\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt301).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\205\\266\\344\\273\\226\\346\\207\\211\\346\\224\\266\\345\\270\\263\\346\\254\\276\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt330).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\207\\272\\350\\262\\250\\345\\226\\256(axmt540).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\207\\272\\350\\262\\250\\347\\260\\275\\346\\224\\266\\345\\226\\256(axmt580).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\207\\272\\350\\262\\250\\347\\260\\275\\351\\200\\200\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt590).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256(axmt520).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\345\\207\\272\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt540).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\345\\207\\272\\350\\262\\250\\347\\260\\275\\346\\224\\266\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt580).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\345\\207\\272\\350\\262\\250\\347\\260\\275\\351\\200\\200\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt590).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\345\\220\\210\\347\\264\\204\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt601).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\350\\250\\202\\345\\226\\256(adbt500).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt510).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\351\\212\\267\\351\\200\\200\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt600).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\210\\270\\347\\250\\256\\345\\237\\272\\346\\234\\254\\350\\263\\207\\346\\226\\231\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(agct300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\214\\205\\350\\243\\235\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt610).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\223\\201\\350\\263\\252\\346\\252\\242\\351\\251\\227\\345\\226\\256(aqct300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\223\\201\\350\\263\\252\\347\\225\\260\\345\\270\\270\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(aqct310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\225\\206\\345\\223\\201\\345\\207\\206\\345\\205\\245\\347\\224\\263\\350\\253\\213\\345\\226\\256(artt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\226\\256\\344\\270\\200\\344\\270\\273\\344\\273\\266ECN(abmt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\234\\260\\347\\243\\205\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt640).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\240\\261\\345\\267\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255-\\345\\226\\256\\347\\255\\206\\345\\274\\217(asft335).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\240\\261\\345\\273\\242\\347\\224\\263\\350\\253\\213\\345\\226\\256(aint310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\240\\264\\345\\234\\260\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(amht204).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\244\\226\\345\\214\\257\\344\\272\\244\\346\\230\\223\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afmt534).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\244\\226\\345\\214\\257\\345\\220\\210\\347\\264\\204\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afmt533).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\244\\226\\345\\214\\257\\346\\234\\237\\346\\234\\253\\345\\205\\254\\345\\205\\201\\345\\203\\271\\345\\200\\274\\347\\266\\255\\350\\255\\267(afmt552).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\244\\232\\344\\270\\273\\344\\273\\266ECN(abmt301).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\244\\232\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(\\346\\265\\201\\351\\200\\232)(aapt815).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\345\\200\\211\\351\\200\\200\\345\\226\\256(apmt581).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt571).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt481).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt491).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\344\\275\\234\\346\\245\\255(apmt521).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt501).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\350\\251\\242\\345\\203\\271\\344\\275\\234\\346\\245\\255(apmt421).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\344\\275\\234\\346\\245\\255(apmt561).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\240\\270\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt441).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\256\\232\\345\\255\\230\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afmt531).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\256\\242\\346\\210\\266\\345\\207\\206\\345\\205\\245\\345\\217\\212\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(axmt800).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\256\\242\\346\\210\\266\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt200).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\256\\242\\346\\210\\266\\350\\262\\250\\346\\254\\276\\345\\260\\215\\345\\270\\263\\344\\275\\234\\346\\245\\255(aist310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\256\\242\\350\\250\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt700).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\257\\246\\345\\234\\260\\347\\233\\244\\351\\273\\236\\350\\250\\210\\347\\225\\253\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint820).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\260\\210\\346\\253\\203\\345\\220\\210\\347\\264\\204\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213(astt401).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\260\\210\\346\\253\\203\\346\\226\\260\\345\\225\\206\\345\\223\\201\\345\\274\\225\\351\\200\\262\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(artt407).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\344\\270\\200\\350\\210\\254\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft323).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\345\\200\\222\\346\\211\\243\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft324).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\345\\200\\222\\346\\211\\243\\351\\240\\230\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft314).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\345\\234\\250\\350\\243\\275\\344\\270\\213\\351\\232\\216\\346\\226\\231\\345\\240\\261\\345\\273\\242\\344\\275\\234\\346\\245\\255(asft339).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\345\\256\\214\\345\\267\\245\\345\\205\\245\\345\\272\\253\\344\\275\\234\\346\\245\\255(asft340).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\346\\210\\220\\345\\245\\227\\347\\231\\274\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft311).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\346\\210\\220\\345\\245\\227\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft321).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\346\\254\\240\\346\\226\\231\\350\\243\\234\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft313).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\347\\225\\266\\347\\253\\231\\345\\240\\261\\345\\273\\242\\344\\275\\234\\346\\245\\255(asft336).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\243\\275\\347\\250\\213\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft801).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\243\\275\\347\\250\\213\\351\\207\\215\\345\\267\\245\\350\\275\\211\\345\\207\\272\\344\\275\\234\\346\\245\\255(asft338).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\256\\212\\346\\233\\264(asft800).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\266\\205\\351\\240\\230\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft312).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\266\\205\\351\\240\\230\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft322).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\270\\202\\345\\240\\264\\346\\216\\250\\345\\273\\243\\346\\264\\273\\345\\213\\225\\346\\240\\270\\351\\212\\267\\345\\226\\256(astt605).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\270\\202\\345\\240\\264\\346\\216\\250\\345\\273\\243\\346\\264\\273\\345\\213\\225\\347\\224\\263\\350\\253\\213\\345\\226\\256(astt604).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\270\\263\\351\\275\\241\\345\\217\\212\\345\\243\\236\\345\\270\\263\\346\\217\\220\\345\\210\\227\\347\\266\\255\\350\\255\\267(axrt940).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\272\\224\\346\\224\\266\\347\\245\\250\\346\\215\\256\\346\\224\\266\\347\\245\\250\\344\\275\\234\\344\\270\\232(anmt510).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\272\\253\\344\\275\\215\\345\\217\\226\\346\\266\\210\\347\\225\\231\\347\\275\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint161).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\272\\253\\345\\255\\230\\345\\240\\261\\345\\273\\242\\351\\231\\244\\345\\270\\263(aint311).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\272\\253\\345\\255\\230\\346\\234\\211\\346\\225\\210\\346\\234\\237\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(aint180).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\272\\253\\345\\255\\230\\347\\225\\260\\345\\270\\270\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(aint170).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\275\\210\\346\\200\\247\\346\\216\\241\\350\\263\\274\\345\\203\\271\\346\\240\\274\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt128).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\345\\275\\210\\346\\200\\247\\351\\212\\267\\345\\224\\256\\345\\203\\271\\346\\240\\274\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt128).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\204\\217\\345\\220\\221\\345\\215\\224\\350\\255\\260\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt811).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\345\\214\\257\\346\\254\\276\\347\\225\\260\\345\\213\\225\\344\\275\\234\\346\\245\\255(anmt480).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\345\\214\\257\\346\\254\\276\\351\\226\\213\\347\\253\\213\\344\\275\\234\\346\\245\\255(anmt460).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\345\\270\\263\\346\\254\\276\\346\\206\\221\\345\\226\\256(aapt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\345\\276\\205\\346\\212\\265\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt340).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\346\\240\\270\\351\\212\\267\\345\\226\\256(aapt420).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\207\\211\\346\\224\\266\\345\\270\\263\\346\\254\\276\\346\\206\\221\\345\\226\\256(axrt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\207\\211\\346\\224\\266\\345\\276\\205\\346\\212\\265\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt340).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\207\\211\\346\\224\\266\\346\\262\\226\\351\\212\\267\\346\\206\\221\\350\\255\\211(axrt400).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\212\\225\\350\\263\\207\\347\\220\\206\\350\\262\\241\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afmt532).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\212\\225\\350\\263\\207\\347\\224\\263\\350\\253\\213\\345\\226\\256(afmt510).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\212\\225\\350\\263\\207\\350\\263\\274\\350\\262\\267\\345\\270\\263\\345\\213\\231\\345\\226\\256(afmt535).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\345\\273\\266\\346\\234\\237\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt803).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt801).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\347\\265\\202\\346\\255\\242\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt805).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\350\\262\\273\\347\\224\\250\\345\\204\\252\\346\\203\\240\\347\\224\\263\\350\\253\\213(astt802).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\350\\262\\273\\347\\224\\250\\346\\250\\231\\346\\272\\226\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(astt806).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\351\\235\\242\\347\\251\\215\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213(astt804).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\200\\211\\351\\200\\200\\345\\226\\256(apmt580).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\200\\211\\351\\200\\200\\345\\226\\256(apmt890).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\203\\271\\346\\240\\274\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt129).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt570).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt880).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\345\\226\\256(apmt480).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt490).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\226\\256(apmt500).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\226\\256(apmt840).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt862).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt530).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256(apmt520).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256(apmt860).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\350\\243\\234\\345\\267\\256\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aprt601).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256(apmt510).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt850).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\351\\240\\220\\344\\273\\230\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\345\\226\\256(apmt560).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\345\\226\\256(apmt870).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\224\\257\\345\\207\\272\\351\\241\\236\\345\\220\\210\\345\\220\\214\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt820).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\226\\231\\344\\273\\266\\346\\211\\277\\350\\252\\215\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(abmt400).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\226\\231\\344\\273\\266\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aimt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\226\\231\\344\\273\\266\\350\\243\\275\\347\\250\\213\\350\\263\\207\\346\\226\\231\\346\\226\\260\\345\\242\\236\\343\\200\\201\\344\\277\\256\\346\\224\\271\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(aect801).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\232\\253\\344\\274\\260\\346\\207\\211\\346\\224\\266\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt320).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\234\\203\\345\\223\\241\\345\\215\\241\\345\\204\\262\\345\\200\\274\\351\\207\\221\\351\\241\\215\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(ammt428).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\234\\203\\345\\223\\241\\345\\215\\241\\347\\250\\256\\347\\224\\263\\350\\253\\213\\345\\226\\256(ammt320).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\234\\203\\345\\223\\241\\345\\215\\241\\347\\251\\215\\351\\273\\236\\350\\252\\277\\346\\225\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(ammt421).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\234\\237\\346\\234\\253\\345\\205\\254\\345\\205\\201\\345\\203\\271\\345\\200\\274\\347\\266\\255\\350\\255\\267(afmt551).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\240\\270\\345\\203\\271\\345\\226\\256(apmt440).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\346\\264\\276\\350\\273\\212\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt630).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt863).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt532).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256(apmt861).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt522).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\204\\241\\350\\250\\202\\345\\226\\256\\345\\207\\272\\350\\262\\250\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt541).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\207\\237\\351\\201\\213\\346\\223\\232\\351\\273\\236ECN\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(abmt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\207\\237\\351\\201\\213\\346\\223\\232\\351\\273\\236\\345\\244\\232\\344\\270\\273\\344\\273\\266ECN\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(abmt311).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\215\\250\\347\\253\\213\\351\\234\\200\\346\\261\\202\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apst300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\224\\237\\351\\256\\256\\345\\203\\271\\346\\240\\274\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(aprt121).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\225\\266\\347\\253\\231\\344\\270\\213\\347\\267\\232\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft337).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\231\\274\\347\\245\\250\\350\\253\\213\\346\\254\\276\\345\\226\\256(aapt415).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\247\\237\\350\\263\\203\\350\\262\\273\\347\\224\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt810).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\344\\273\\243\\345\\242\\212\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt606).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\347\\265\\220\\347\\256\\227\\345\\226\\256(astt640).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\350\\262\\273\\347\\224\\250\\345\\226\\256(astt620).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\207\\252\\346\\234\\211\\346\\226\\260\\345\\225\\206\\345\\223\\201\\345\\274\\225\\351\\200\\262\\345\\226\\256(artt406).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\207\\252\\347\\207\\237\\345\\220\\210\\347\\264\\204\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\345\\226\\256(astt301).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\207\\252\\347\\207\\237\\346\\226\\260\\347\\224\\242\\345\\223\\201\\345\\274\\225\\351\\200\\262(artt405).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\236\\215\\350\\263\\207\\345\\220\\210\\345\\220\\214\\347\\266\\255\\350\\255\\267(afmt035).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\236\\215\\350\\263\\207\\347\\224\\263\\350\\253\\213\\345\\226\\256(afmt015).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\236\\215\\350\\263\\207\\350\\263\\207\\351\\207\\221\\345\\210\\260\\345\\270\\263\\347\\266\\255\\350\\255\\267(afmt140).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\246\\201\\350\\262\\250\\345\\226\\256(apmt830).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\246\\201\\350\\262\\250\\345\\226\\256\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt835).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\250\\202\\345\\226\\256(axmt500).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264(axmt510).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\250\\202\\351\\207\\221\\351\\240\\220\\346\\224\\266\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\251\\242\\345\\203\\271\\345\\226\\256(apmt420).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\252\\277\\346\\222\\245\\345\\267\\256\\347\\225\\260\\350\\252\\277\\346\\225\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint520).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\252\\277\\346\\222\\245\\347\\224\\263\\350\\253\\213\\345\\226\\256(aint320).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\253\\213\\350\\263\\274\\345\\226\\256(apmt400).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\253\\213\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256(apmt410).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\262\\273\\347\\224\\250\\345\\240\\261\\346\\224\\257\\345\\226\\256(aapt330).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\262\\273\\347\\224\\250\\346\\250\\231\\346\\272\\226\\350\\250\\255\\345\\256\\232\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt252).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\262\\273\\347\\224\\250\\351\\240\\220\\346\\224\\257\\347\\224\\263\\350\\253\\213\\345\\226\\256(aapt331).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\346\\272\\220\\346\\255\\270\\351\\202\\204\\344\\275\\234\\346\\245\\255(amrt250).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\346\\272\\220\\347\\266\\255\\344\\277\\256\\345\\267\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(amrt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\345\\207\\272\\345\\224\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat504).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\345\\240\\261\\345\\273\\242\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat507).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\346\\224\\266\\345\\233\\236\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat450).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat440).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\346\\224\\271\\350\\211\\257\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat508).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\351\\203\\250\\351\\226\\200\\350\\275\\211\\347\\247\\273\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat421).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\351\\207\\215\\344\\274\\260\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat503).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\351\\212\\267\\345\\270\\263\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat506).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\350\\275\\211\\345\\270\\263\\345\\202\\263\\347\\245\\250(aglt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\200\\200\\350\\262\\250\\347\\224\\263\\350\\253\\213\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint530).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\200\\345\\255\\230\\346\\224\\266\\346\\224\\257\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(anmt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\344\\274\\260\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt400).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\345\\203\\271\\346\\240\\274\\350\\241\\250\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt129).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\345\\220\\210\\347\\264\\204\\345\\226\\256(axmt440).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt450).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\345\\240\\261\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt410).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\346\\240\\270\\345\\203\\271\\345\\226\\256(axmt420).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\350\\243\\234\\345\\267\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aprt602).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\212\\267\\351\\200\\200\\345\\226\\256(axmt600).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\213\\252\\344\\275\\215\\345\\200\\213\\345\\210\\245\\350\\262\\273\\347\\224\\250\\346\\250\\231\\346\\272\\226\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt253).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\213\\252\\344\\275\\215\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(amht205).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\213\\252\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt832).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\226\\200\\345\\272\\227\\350\\252\\277\\346\\222\\245\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint511).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\226\\200\\345\\272\\227\\350\\263\\207\\346\\272\\220\\345\\215\\224\\350\\255\\260\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(artt230).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\233\\206\\345\\234\\230\\347\\240\\224\\347\\231\\274\\347\\224\\242\\345\\223\\201\\347\\265\\220\\346\\247\\213\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(abmt200).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\233\\234\\351\\240\\205\\345\\272\\253\\345\\255\\230\\346\\224\\266\\346\\226\\231\\345\\226\\256(aint302).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\233\\234\\351\\240\\205\\345\\272\\253\\345\\255\\230\\347\\231\\274\\346\\226\\231\\345\\226\\256(aint301).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\233\\234\\351\\240\\205\\345\\276\\205\\346\\212\\265\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt341).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\240\\220\\347\\247\\237\\345\\215\\224\\350\\255\\260\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt812).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\240\\220\\347\\256\\227\\346\\214\\252\\347\\224\\250\\347\\266\\255\\350\\255\\267(abgt060).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@t100/form-default/\\351\\240\\220\\347\\256\\227\\350\\277\\275\\345\\212\\240\\347\\266\\255\\350\\255\\267(abgt050).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 427}, {"commit_hash": "598570e7b9894ce8226e57284cb8f604f75db962", "commit_訊息": "[Web] Q00-20240428001 開窗資料條件財產名稱輸入[PL3/雙驅動改造]，資料帶回gird，儲存草稿/儲存表單後gird資料顯示異常问题修正", "提交日期": "2024-04-28 14:22:18", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fee070975700e0be5e3c2d81f12683bb63617372", "commit_訊息": "[Web] Q00-20240426002 修正主管首頁待辦處理量OracleDB異常", "提交日期": "2024-04-26 11:03:40", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2deede88186481e3f61d08070ef19b933fd7a724", "commit_訊息": "[Web]Q00-20240426003 修正栏位过多，设置栏位宽度没效果的问题", "提交日期": "2024-04-26 10:35:22", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bee1a80ddce0a2716a20998aa928a78d53e30054", "commit_訊息": "[PLM]Q00-20240426001 調整PLM歷程接口返回內容", "提交日期": "2024-04-26 08:56:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WebServiceUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "61e85a135d9ab123f821fb0f3804e9e163de14bd", "commit_訊息": "[Web]Q00-20240425005 修正加簽關卡選取經常對像在吳資料的狀況下會顯示錯誤頁面", "提交日期": "2024-04-26 08:21:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7d8e466d2499e805cded2e474b85fc6415d08ea", "commit_訊息": "[Web] Q00-20240425004 修正絕對位置表單，調整表單大小導致ScrollBar異常增加問題", "提交日期": "2024-04-25 17:48:22", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ece4dfb2cd396174002f249713ffdcc0af8506e9", "commit_訊息": "[流程引擎]Q00-20240425003 修正核決關卡設置前置關係人造成前置關係人關卡無法繼續簽核", "提交日期": "2024-04-25 15:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "255f0ab3062675eea2e45a275db40ad87e36bc42", "commit_訊息": "[Web]Q00-20240425002 修正开启绝对位置表单偶发报错TypeError: Cannot read properties of undefined (reading 'ElementGroup')的问题", "提交日期": "2024-04-25 15:32:11", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa7cc43605c7c2376ff1bb3ba45bb3c59ec05003", "commit_訊息": "[TIPTOP]Q00-20240425001 調整TIPTOP接口呼叫封存的PORT號改成取流程主機設定", "提交日期": "2024-04-25 09:09:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7eb9a746a04dbb32258d8a134f7d283bf1112818", "commit_訊息": "[内部]Q00-20240423002 登陸頁面新增個資安全宣告，需通過系統設定personal.data.protection.web開啓", "提交日期": "2024-04-24 15:39:24", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a8caf501639583af60f8e36a2d7f53ec8e93763", "commit_訊息": "[PRODT]Q00-20240424002 修正Web流程設計師中發起權限設定屬性的職務資料在編輯狀態後儲存會遺失問題", "提交日期": "2024-04-24 14:08:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "43d09cdc6f3c723ebe1135b00d08aea01282a96a", "commit_訊息": "[TIPTOP]Q00-20240424001 修正流程封存邏輯影響到TIPTOP流程結案異常", "提交日期": "2024-04-24 11:15:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9ea40861bb2b85345b507cfaa44a678d04bee089", "commit_訊息": "[Web]Q00-20240423004 在觸發排程Trigger加入睡眠機制，以避免排程執行過快導致重複觸發狀況", "提交日期": "2024-04-23 15:15:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/schedule/SystematicJob.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e9eaad612f0cc5a4f939bd16cc1d09df2ec42a9", "commit_訊息": "[Web] Q00-20240423001 客户5521版到5894 將流程從XPDL轉BPMN會失敗问题修正", "提交日期": "2024-04-23 09:14:36", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8b2febe555314c3a6a5af2ce54087d5a32c419e", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2024-04-22 15:49:49", "作者": "邱郁晏", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "22f1787c5c7c8bf5dfd711c059a57cd3ee912f37", "commit_訊息": "[Web]Q00-20240422002 調整主管頁面平均處理天數時效計算SQL寫法", "提交日期": "2024-04-22 15:37:19", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb342642ffa36cdcc1b22889540c13dc369fa09b", "commit_訊息": "[Web]Q00-20240422002 調整主管頁面在途中流程處理時效計算SQL寫法", "提交日期": "2024-04-22 15:37:19", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2fd57d33b21a24d3b6e682404c93b82b2b8ca149", "commit_訊息": "[Web]Q00-20240422001 修正調用IAM的歸戶人員使用的IAM路徑改參考系統參數", "提交日期": "2024-04-22 10:12:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c237f7834f7d4c91d5d3ff9008ba08ee7e273a0", "commit_訊息": "[Web]Q00-20240416001 修正含有日期元件運算异常问题[補修正]", "提交日期": "2024-04-19 15:40:55", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "55a1ab83c0545e8ce3171a108c6fdf6a2df87cd7", "commit_訊息": "[Web]Q00-20240419003 修正追踪流程作业清单加载卡慢的问题", "提交日期": "2024-04-19 13:56:50", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "933f95a88c8e87fda9a2c6ae1ffd28d4abf2a271", "commit_訊息": "[Web] Q00-20240419001 修正檢視參與者發送按鈕消失異常", "提交日期": "2024-04-19 11:28:32", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5567ba062d8b6e3815a3bb5f5ebf222e372653e7", "commit_訊息": "[流程引擎]Q00-20240418003 修正多人關卡設定工作完工比率時，流程實例開啟報OOPS的问题", "提交日期": "2024-04-18 14:49:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84176567c7f46361c72b042de9dfa5d2ce196c3c", "commit_訊息": "[Web] Q00-20240417002 数据库中forminstance表中的fieldvalues字段行间距很大问题修正[补]", "提交日期": "2024-04-18 14:31:05", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c66955a7982bb0493740c37f81b379bbc9a5a5e7", "commit_訊息": "[BPM APP]Q00-20240418002 調整釘釘推播消息當Title長度超過64時無法推送的問題", "提交日期": "2024-04-18 14:04:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "77f31ee709f0c3eb74f847947550480e2d5f81d2", "commit_訊息": "[Web]Q00-20240418001 修正资料选取器回传栏位Disable状态下，双击清空栏位仍生效的问题", "提交日期": "2024-04-18 10:34:02", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f7336d6af103d6cda67002351856d0cf3fed7784", "commit_訊息": "[Web]Q00-20240417004 修正radio元件在Disable的狀態下 在「重發新流程」時不會將該元件的選項清空", "提交日期": "2024-04-17 13:56:05", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c2072ac2635771a97a8b3e50341a6a5199c8552", "commit_訊息": "Revert \"[Web]Q00-20240417003 模拟使用者报错NullPointerException新增防呆及埋log\"", "提交日期": "2024-04-17 11:29:23", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "61bc0bd06e8df94b0bb9976296c5e304eaedeb2c", "commit_訊息": "[Web]Q00-20240417003 模拟使用者报错NullPointerException新增防呆及埋log", "提交日期": "2024-04-17 10:45:56", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9887d6993c75b48eddea4fd8c1e716374e21967e", "commit_訊息": "[Web] Q00-20240417002 数据库中forminstance表中的fieldvalues字段行间距很大问题修正", "提交日期": "2024-04-17 10:23:42", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7f6bf7b64b4f18528e4406e4e02f81b2ad756b6", "commit_訊息": "[流程引擎]Q00-20240416002 修正流程在併簽關卡進行撤銷or終止，若有呼叫流程事件會造成資料庫Lock", "提交日期": "2024-04-16 13:47:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6850ef70daf8713976e247f53434f0fbe9d20bb4", "commit_訊息": "[Web]Q00-20240416001 修正含有日期元件運算异常问题", "提交日期": "2024-04-16 10:59:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fa9960937367eeada75b9957f2d4fcfafd8b1c0b", "commit_訊息": "[ESS]Q00-20240415001 新增ESS模組作業ESSQ95,ESSQ81,ESSQ82", "提交日期": "2024-04-15 14:45:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/db/optional/@appform-essplus/\\344\\272\\272\\346\\272\\220\\347\\242\\263\\347\\233\\244\\346\\237\\245\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/\\344\\272\\272\\346\\272\\220\\347\\242\\263\\347\\233\\244\\346\\237\\245\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/\\346\\216\\222\\347\\217\\255\\347\\211\\271\\345\\212\\251\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/\\346\\216\\222\\347\\217\\255\\347\\211\\271\\345\\212\\251\\346\\250\\241\\347\\265\\204/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "ed265c87a8545e9f265a53945725aa766a9ae392", "commit_訊息": "[Web]Q00-20240412003 修正主旨顯示為編碼後的內容", "提交日期": "2024-04-12 16:15:04", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/StringUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "170bfc3bca683f226381c917b8e2ceee18de113c", "commit_訊息": "[流程設計師]Q00-20240412002 修正流程存在核決關卡時執行XDPL轉BPMN因活動集合定義遺失無法替換id而導致無法轉換問題", "提交日期": "2024-04-12 12:07:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c68001b2d0d664d2dafb7cd5fdd4926a104b7a18", "commit_訊息": "[Web]Q00-20240412001 修正grid 設定table 模式，有很多個欄位會擠一起的情況", "提交日期": "2024-04-12 10:05:56", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7824e9c9543d2beee26edabcf0f0d6c788a8649", "commit_訊息": "[ISO] Q00-20240411001 調整ISOPaperRecord表 checkoutId、checkoutOrgId 欄位上限，以避免服務任務執行失敗", "提交日期": "2024-04-11 18:02:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.2_DDL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "58b8483893605623f8f2035b9f277a46583a9eae", "commit_訊息": "[Web] Q00-20240403002 Grid單身欄位加總異常问题修正[补]", "提交日期": "2024-04-11 11:27:19", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eae09f416440b6e22f8a220e8605409faf92f2a0", "commit_訊息": "[Web] Q00-20240410002 從5894版到58101後，Grid 欄位框線不會對齊,將調整寬度的script註解掉，Grid的欄位框線就可正常對齊问题修正", "提交日期": "2024-04-10 18:21:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "613eb15b065078fbdd1f9027a2044879ab2d743c", "commit_訊息": "[BPM APP]Q00-20240409002 調整釘釘待辦在創建與更新待辦機制", "提交日期": "2024-04-10 16:41:38", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7a51e82d6c8684606eedd67eed13a04f4cca1c3b", "commit_訊息": "[雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶 [補修正]", "提交日期": "2024-04-10 08:56:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cc8d2dcbeb019506f329eac84dc2609db48bf323", "commit_訊息": "[Secudocx] V00-20240409001 調整以柔整合寫法優化", "提交日期": "2024-04-09 17:45:34", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "013a50bc8588b793e8d216556af44ea0120f36c6", "commit_訊息": "[雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶", "提交日期": "2024-04-09 17:15:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "946ef3274c1a4e0f0960ed04358ba7e22358b041", "commit_訊息": "[Web] Q00-20240409004 修正流程草稿主旨上有反斜線\\導致畫面空白問題", "提交日期": "2024-04-09 16:27:19", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a7dc05eb5e2e48c94ea0d5baacbf8a0189829dc", "commit_訊息": "[Web]Q00-20240409005 修正attachment物件OID为空时，会抛错[此URL没有下载文件的权限]的问题", "提交日期": "2024-04-09 16:17:15", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07752d69174097e96ccd5fa5a4961fffea74d897", "commit_訊息": "[雙因素模組]Q00-20240409003 信任端點裝置時間修正為24小時制", "提交日期": "2024-04-09 15:32:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e21aa33ac063e905224f04cfd9707250cc687b1", "commit_訊息": "[ORGDT]V00-20240321002 修正Web組織管理工具中透過放大鏡搜尋部門後查看隸屬單位顯示錯誤問題", "提交日期": "2024-04-09 11:42:27", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71a060e9f8f6065cb3b202cce23a4e25dd3dcbad", "commit_訊息": "[Web] Q00-20240408001 調整刪除流程的邏輯，改為不檢核直接刪除流程", "提交日期": "2024-04-08 18:08:07", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "94c6b6d49cb4f76b589010a24864c497960c495e", "commit_訊息": "[Web] Q00-20240403002 Grid單身欄位加總異常问题修正", "提交日期": "2024-04-03 16:22:52", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3bba0eb0bae4de10e833ea3ee5668cdfe77529a", "commit_訊息": "[Web] V00-20240402002 流程发起时选择流程重要性为：紧急，但是在待办事项列表中没有出现红色标记列问题修正", "提交日期": "2024-04-03 14:01:05", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5930b950de066d2017099de412cd2dbc5366de3e", "commit_訊息": "[Web]Q00-20240402001 修正[報表維護作業]產出的報表畫面欄位异常", "提交日期": "2024-04-02 16:59:55", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e933dbc683743c16f73d3a8a67f0bf3bca0c84db", "commit_訊息": "[Web]V00-20240402001 修正待办事项中【由我处理】按钮点击没有反应", "提交日期": "2024-04-02 16:00:27", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37f66e4dc43f23bd862477402ed4a5ddffe6cec2", "commit_訊息": "[Cosmos]Q00-20240401002 補上COSMOS表單缺漏的元件資訊", "提交日期": "2024-04-01 11:49:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@cosmos/form-default/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\350\\253\\213\\346\\254\\276\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PCMI10).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@cosmos/form-default/\\345\\207\\272\\345\\267\\256\\346\\227\\205\\350\\262\\273\\350\\253\\213\\346\\254\\276\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PCMI11).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@cosmos/form-default/\\346\\252\\224\\346\\234\\237\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COMI10)\\343\\200\\220administrator\\343\\200\\221.form\"", "修改狀態": "重新命名", "狀態代碼": "R070"}, {"檔案路徑": "\"Release/copyfiles/@cosmos/form-default/\\350\\263\\207\\347\\224\\242\\346\\212\\230\\350\\210\\212\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI11).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@cosmos/form-default/\\350\\263\\207\\347\\224\\242\\346\\224\\271\\350\\211\\257\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(ASTI06).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@cosmos/form-default/\\351\\200\\232\\350\\267\\257\\345\\271\\264\\345\\272\\246\\345\\220\\210\\347\\264\\204\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COMI12).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@cosmos/form-default/\\351\\200\\232\\350\\267\\257\\345\\271\\264\\345\\272\\246\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(COMI09).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@cosmos/form-default/\\351\\233\\266\\347\\224\\250\\351\\207\\221\\345\\240\\261\\351\\212\\267\\345\\273\\272\\347\\253\\213\\344\\275\\234\\346\\245\\255(PCMI09).form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "1043211faefcab84b75ac1d2e21b6bfbca7d5b2e", "commit_訊息": "[Web]Q00-20240401001 修正XPDL简易流程图显示主旨异常", "提交日期": "2024-04-01 11:41:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4e73959822faf43236f1281ae1e619e025d49aaa", "commit_訊息": "[組織同步] Q00-20240329002 修正組織同步User帳號啟用邏輯異常，導致部分使用者同步後帳號變為未啟用", "提交日期": "2024-03-29 15:28:10", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cac7f42ed09959743e5896d308a5c29edc188f2b", "commit_訊息": "[BPM APP]Q00-20240329001 調整行動端表單元件唯讀狀態且設置欄位驗證時顯示必填樣式", "提交日期": "2024-03-29 10:39:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5db15790cc6aa842b87b16eacb9203a9f67386cd", "commit_訊息": "[內部]調整因應新模組架構調整所需的多語系[補]", "提交日期": "2024-03-29 08:45:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5684d527b2281a466cb24b4eb8fd5c9fb33826a4", "commit_訊息": "[Web] Q00-20240328001 修正showDialog寫法異常", "提交日期": "2024-03-28 14:38:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "027fe9e0d64bf325f3ddb7aad3a3901ba5bea7b9", "commit_訊息": "[Web]Q00-20240327003 BPM首頁的工作事項主旨內容增加顯示轉派的簽核意見", "提交日期": "2024-03-27 17:27:14", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "016c26a4e998b3670ede8cd543f8925c3a39e64f", "commit_訊息": "[組織同步] Q00-20240327002 調整HR組織同步，塞入中介表資料若異常，不拋出錯誤", "提交日期": "2024-03-27 15:12:54", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b70984056fcfd682fde306e61b1b00aefd1b239", "commit_訊息": "[流程引擎] S00-20231006001 流程郵件通知信件的內容，字體不一致的問題修正", "提交日期": "2024-03-27 13:19:32", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0cab5f9e6b5c4bd319de2c4115c45fb729cfd205", "commit_訊息": "[PRODT]Q00-20240327001 修正Web流程管理工具中匯入流程無法覆蓋流程進版的問題", "提交日期": "2024-03-27 10:49:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "094772763262cd75b3f5802bfad1ccf9939d3a15", "commit_訊息": "[BPM APP]Q00-20240322003 調整BPMAPP中的追蹤流程會依系統變數預設開啟已發起的或處理的流程", "提交日期": "2024-03-27 10:19:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "021d0c093f5b570561bf35cdd246deef916b09a7", "commit_訊息": "[ORGDT]Q00-20240322002 修正Web組織管理工具中調離所有部門功能異常問題", "提交日期": "2024-03-27 10:06:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}