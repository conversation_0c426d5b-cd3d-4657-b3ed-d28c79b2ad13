# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_*******_All
- **舊版本**: release_*******
- **生成時間**: 2025-07-18 11:20:26
- **新增 Commit 數量**: 328

## 變更摘要

### lorenchang (5 commits)

- **2022-06-26 20:48:05**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
  - 變更檔案: 25 個
- **2022-07-04 12:16:08**: [內部]修正使用IDEA開啟ProcessDispatcherBean時，Java Doc出現Identifier expected
  - 變更檔案: 1 個
- **2022-07-06 17:40:48**: [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
  - 變更檔案: 21 個
- **2022-07-06 17:40:48**: [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
  - 變更檔案: 21 個
- **2022-07-04 08:58:18**: [內部]Log調整
  - 變更檔案: 2 個

### 林致帆 (47 commits)

- **2023-10-11 14:12:19**: [資安]Q00-20231011001 強化ajax-DataBaseAccessor服務邏輯以禁止非法調用
  - 變更檔案: 1 個
- **2023-06-01 10:56:27**: [Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常
  - 變更檔案: 1 個
- **2023-04-12 14:58:38**: [資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊
  - 變更檔案: 2 個
- **2023-02-16 13:59:39**: [資安]C01-20230203007 弱掃資安議題，調整Nginx設定add_header X-Content-Type-Options nosniff; 會導致BPM的登入頁無法使用，因jsp引用dwr動態產生的dwrDefault/interface/底下的js檔在產生時未指定Content-Type,導致瀏覽器無法解析。反組譯修改dwr.jar檔中的(DefaultInterfaceProcessor.class)
  - 變更檔案: 1 個
- **2023-04-12 14:58:38**: [資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊
  - 變更檔案: 2 個
- **2023-04-14 15:44:26**: [Web]Q00-20230414005 調整下載附件不該顯示This URL not have permission to download the file訊息
  - 變更檔案: 1 個
- **2023-05-05 15:42:43**: [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
  - 變更檔案: 2 個
- **2023-05-04 11:49:50**: [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
  - 變更檔案: 1 個
- **2023-04-25 10:51:33**: [ESS]Q00-20230425001 修正若使用者設定Proxy，取得的clientIP會有實際IP以及Proxy設定之IP，造成開啟ESS表單失敗
  - 變更檔案: 1 個
- **2023-04-07 15:29:33**: [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊
  - 變更檔案: 2 個
- **2023-02-17 11:33:44**: [E10]Q00-20230217002 修正子單身在Table模式下展開內容會無法完全顯示
  - 變更檔案: 1 個
- **2022-09-27 08:36:26**: [T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容
  - 變更檔案: 1 個
- **2023-03-06 15:18:27**: [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊
  - 變更檔案: 2 個
- **2023-03-06 11:33:04**: [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功
  - 變更檔案: 3 個
- **2022-08-10 15:47:45**: [Web]Q00-20220810001 修正設定模擬使用者給一般人員，用模擬使用者模擬一般人員，會出現兩筆模擬使用者的作業
  - 變更檔案: 1 個
- **2022-09-07 17:15:14**: [Web]Q00-20220907003 修正TIPTOP附件無法下載
  - 變更檔案: 1 個
- **2022-09-20 14:39:54**: [內部]Q00-20220920002 T100傳附件用http方式且未帶drivetoken的tag內容，增加log訊息提示修正T100
  - 變更檔案: 1 個
- **2022-08-09 15:40:54**: [T100]Q00-20220809005 修正T100拋單，附件為從文檔中心取得的，檔案大小與實際大小不符合
  - 變更檔案: 1 個
- **2022-10-14 17:38:20**: [WorkFlow]]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能
  - 變更檔案: 5 個
- **2022-11-03 17:41:40**: [流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信
  - 變更檔案: 3 個
- **2022-08-17 11:58:58**: [Portal]]Q00-20220817001調整有整合Portal，用查看流程圖的外部portlet，導入的畫面不是BPM而是Portal的登入頁面
  - 變更檔案: 1 個
- **2022-09-27 11:50:22**: [ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核
  - 變更檔案: 1 個
- **2022-09-05 17:48:01**: [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
  - 變更檔案: 4 個
- **2022-08-30 08:34:29**: [WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能
  - 變更檔案: 1 個
- **2022-08-22 14:11:47**: [Web]Q00-20220822001 修正BPM使用IE瀏覽器上傳附件時會失敗
  - 變更檔案: 1 個
- **2022-08-19 17:57:37**: [TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久
  - 變更檔案: 1 個
- **2022-07-29 14:34:14**: [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]
  - 變更檔案: 1 個
- **2022-08-02 11:27:26**: [Web]A00-20220802001 修正無法開啟SAP維護作業
  - 變更檔案: 1 個
- **2022-07-29 14:02:17**: [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]
  - 變更檔案: 1 個
- **2022-07-28 15:20:57**: [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
  - 變更檔案: 3 個
- **2022-07-28 15:20:57**: [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
  - 變更檔案: 3 個
- **2022-07-27 10:41:09**: [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法
  - 變更檔案: 1 個
- **2022-06-29 11:34:54**: [資安]Q00-20220629001 修正/NaNaWeb/webservice/servlet/AxisServlet要加入為WebService白名單控管範圍
  - 變更檔案: 1 個
- **2022-06-23 14:10:20**: [Web]A00-20220622002 修正流程新增關卡頁面輸入簽核意見在新增向前or向後關卡按下確定後，簽核意見內容被清除
  - 變更檔案: 1 個
- **2022-06-21 13:42:26**: [Web]Q00-20220621003修正發起流程-查詢流程清單搜尋純數字的流程名稱會報錯
  - 變更檔案: 1 個
- **2022-06-07 14:20:54**: [Web]Q00-20220607002 修正首頁待辦清單第三頁以上的流程進行派送時會報錯
  - 變更檔案: 1 個
- **2022-06-14 11:52:54**: [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯[補修正]
  - 變更檔案: 1 個
- **2022-06-10 11:42:26**: [內部]Q00-20220610001修正WorkFlow拋單log會顯示[Fatal Error] :1:1的錯誤訊息
  - 變更檔案: 1 個
- **2022-06-06 14:26:44**: [Web]Q00-20220606001 修正第二關之後的關卡預解析，流程線的條件式採用表單欄位時，預解析的關卡與派送的關卡不符合
  - 變更檔案: 1 個
- **2022-05-25 16:48:50**: [Web]Q00-20220525004 修正輸入單身資料有&#加任意數字，被轉成特殊符號，會與輸入資料不符
  - 變更檔案: 2 個
- **2022-05-25 16:32:13**: [TIPTOP]Q00-20220525003 修正拋單的單身資料有中刮號會被轉成小括號，導致資料與TIPTOP不符合
  - 變更檔案: 1 個
- **2022-05-18 10:39:03**: [Web]Q00-20220518001 修正退件表單資訊與開啟的表單關連錯誤
  - 變更檔案: 1 個
- **2022-05-05 11:00:24**: [Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢
  - 變更檔案: 1 個
- **2022-06-16 13:59:23**: [Web]Q00-20220616001 調整待辦通知信中簽核歷程的處理者欄位移除粗體樣式
  - 變更檔案: 1 個
- **2022-05-19 17:04:27**: [Web]A00-20220517004 修正調離職人員帳號更新排程預設出貨端口改成8086
  - 變更檔案: 1 個
- **2022-05-09 08:55:56**: [內部]Q00-20220509001修正設定檔ESS內網IP敘述的Oracle指令有誤
  - 變更檔案: 1 個
- **2022-05-13 17:23:56**: [流程引擎]A00-20220513001 修正ProcessMapping在Oracle遺漏attachInfo欄位導致拋單失敗
  - 變更檔案: 1 個

### waynechang (22 commits)

- **2023-05-09 17:30:59**: [ISO]Q00-20230509001 修正ISO變更單於ModDocRequester關卡載入上一版附件後，點擊下載按鈕沒有反應
  - 變更檔案: 1 個
- **2023-05-17 12:00:12**: [流程引擎]Q00-20230512001 修正關卡有啟用自動簽核跳關，當核決關卡參考該關卡時，若該關卡同時有向前或向後加簽時，自動簽核的功能就會失效
  - 變更檔案: 1 個
- **2023-05-09 17:39:13**: [Web]Q00-20230509002 修正表單附件上傳後；若重新透過附件開窗上傳新的檔案時，原先上傳的附件無法下載的異常
  - 變更檔案: 1 個
- **2023-03-23 15:06:58**: [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析[補]
  - 變更檔案: 3 個
- **2022-10-25 17:29:45**: 112/1/4 Merge
  - 變更檔案: 5 個
- **2022-09-15 17:24:04**: [流程引擎]Q00-20220915001 修正簡易流程圖若流程有設計迴圈型且線的條件剛好為兩個Gateway互為下一關時，加入防呆避免系統Crash
  - 變更檔案: 1 個
- **2022-07-21 15:36:43**: [流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送
  - 變更檔案: 1 個
- **2022-12-12 13:52:29**: [流程引擎]Q00-20221212001 調整ajax_ProcessAccessor.findFieldValueById取得表單內容方法，當流程同時掛載多表單時，偶發回傳找不到該欄位內容的錯誤
  - 變更檔案: 1 個
- **2022-11-02 18:07:03**: [流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
  - 變更檔案: 1 個
- **2022-11-24 16:24:24**: [內部]Q00-20221124005 調整downloadImage的URL服務的ContentType為png
  - 變更檔案: 1 個
- **2022-11-08 16:22:44**: [流程引擎]Q00-20221108003 修正流程引擎的加簽函式功能「addCustomParallelAndSerialActivity」，加簽出來的關卡的表單未依照「參考關卡」呈現對應的「表單元件顯示」狀態
  - 變更檔案: 1 個
- **2022-11-11 15:00:49**: [在線閱覽] Q00-20221111002 修正追蹤流程重發新流程，當第一關關卡有設定上傳附件不使用在線閱覽時，上傳附件仍會出現在線閱覽的選項
  - 變更檔案: 1 個
- **2022-11-04 11:40:39**: [內部]Q00-20221104002 調整觸發自動簽核時間點的log
  - 變更檔案: 1 個
- **2022-10-31 17:41:06**: [流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
  - 變更檔案: 1 個
- **2022-10-31 16:23:44**: [流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常
  - 變更檔案: 1 個
- **2022-10-14 14:29:50**: [流程設計師]A00-20221012001 修正流程設計師當子流程有變更代號時，流程簽入新版時，資料庫的子流程代號未更新
  - 變更檔案: 1 個
- **2022-10-03 13:59:51**: [流程引擎]Q00-20221003002 流程預先解析支持流程設計關卡型態為「活動簽核人」的活動
  - 變更檔案: 1 個
- **2022-10-03 10:27:09**: [流程設計師]Q00-20221003001 調整簽核流設計師，將流程設計師原有的「活動定義/選擇參與者/活動簽核人」重新加回簽核流設計師中
  - 變更檔案: 5 個
- **2022-08-25 14:52:12**: [流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡
  - 變更檔案: 1 個
- **2022-09-20 11:19:53**: [Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊
  - 變更檔案: 1 個
- **2022-07-21 15:36:43**: [流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送
  - 變更檔案: 1 個
- **2022-06-22 15:47:30**: [Web]Q00-20220523001 修正同瀏覽器有二次登入時，登入頁「記住我」的功能會失效
  - 變更檔案: 1 個

### wayne (4 commits)

- **2022-04-26 13:59:50**: [ISO]Q00-20220426001 調整ISO變更單，載入上一版附件時，附件的上傳時間改以系統參數設定「系統當前時間」或「原始上傳時間」
  - 變更檔案: 1 個
- **2022-05-12 16:55:22**: [Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」
  - 變更檔案: 2 個
- **2022-05-10 15:59:21**: [Web]Q00-20220510001 修正IE瀏覽器開啟產品授權註冊頁面時，畫面跑版呈現異常
  - 變更檔案: 1 個
- **2022-05-17 11:32:25**: [內部]A00-20220517002 修正DB為 Oracle時，產品授權畫面無顯示模組名稱
  - 變更檔案: 1 個

### kmin (63 commits)

- **2023-06-09 14:03:56**: Revert "[資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊"
  - 變更檔案: 2 個
- **2023-05-30 13:30:16**: [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題(補修正)
  - 變更檔案: 1 個
- **2023-05-30 13:27:55**: [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題
  - 變更檔案: 3 個
- **2023-05-08 15:47:41**: [Web]Q00-20230303002 修正人員開窗選取帶有特殊字"𤧟"的人員派送後表單會重複多長好幾個"𤧟"字
  - 變更檔案: 1 個
- **2023-05-02 11:49:44**: [Web]Q00-20230425004 修正列表搜尋條件輸入的字串包含[ 時會搜尋不到資料的問題 1.關聯單號:C01-20230421008 2.當搜尋字串為[xxx]abc時，在MSSQL中會被判斷為xxxabc -- 因[是MSSQL保留關鍵字，故搜尋不到資料 3.只調整fullSearchText 與 subject欄位 4.調整追蹤、待辦、工作通知、撤銷、草稿、取回重辦、轉派、授權的流程等列表
  - 變更檔案: 9 個
- **2023-04-12 15:07:20**: Revert "[Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法 語法: FormUtil.setScrollBarHeight("0");"
  - 變更檔案: 1 個
- **2023-03-29 15:40:37**: [Web] Q00-20230313002 修正SelectElement，Style屬性異常問題
  - 變更檔案: 1 個
- **2023-03-22 14:17:47**: [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析
  - 變更檔案: 2 個
- **2023-03-07 16:22:52**: [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功[補]
  - 變更檔案: 1 個
- **2023-03-07 16:22:01**: Revert "[ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊[補]"
  - 變更檔案: 1 個
- **2023-03-06 15:40:29**: [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊[補]
  - 變更檔案: 1 個
- **2023-02-22 11:57:27**: [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題[補]
  - 變更檔案: 1 個
- **2023-01-13 14:07:17**: [WEB] Q00-20230112001 修正T100拋單附件為URL時，不會計算點按次數。
  - 變更檔案: 2 個
- **2023-01-04 16:37:36**: Merge 112/1/14 PerformWorkItemHandlerBean.java ProcessDispatcherBean.java 移除： [流程引擎]S00-20220516002 在地化方案支援集成第三台流程主機 [流程引擎]Q00-20221209002 T100拋單若第一關與第二關的建立時間相同，自動簽核選擇與前一關相同簽核者就會無效 by kmin.
  - 變更檔案: 3 個
- **2023-01-04 16:22:49**: Revert "112/1/4 Merge"
  - 變更檔案: 5 個
- **2023-01-04 15:36:16**: PerformWorkItemHandlerBean 回歸5883標準，但移除[流程引擎]S00-20220516002 在地化方案支援集成第三台流程主機
  - 變更檔案: 1 個
- **2023-01-04 15:23:14**: ProcessDispatcherBean回歸5881標準 by kmin.
  - 變更檔案: 1 個
- **2022-12-19 16:33:41**: [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現 [補修正] 調整上傳附件後出現一堆隱藏的div框導致畫面不一致 調整Title,Qrcode元件上傳附件以及列印在隱藏狀態時會出現
  - 變更檔案: 1 個
- **2022-12-15 14:39:38**: [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現
  - 變更檔案: 1 個
- **2022-12-07 08:56:25**: Revert "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。 另外還有發現： 1. 活動逾時排程動作執行之後，後續待辦關卡也不會寄送通知信，已修正。 2. 用系統管理員從流程圖檢視上的活動關卡頁面直接執行跳過，後續待辦關卡也不會寄送通知信，已修正。"
  - 變更檔案: 1 個
- **2022-11-30 16:08:32**: [Web]Q00-20221114005絕對定位表單及RWD表單，統一可設定背景色設定。
  - 變更檔案: 4 個
- **2022-11-30 15:42:42**: [Web]Q00-20221114002修正表單設計師Barcode元件異常問題。
  - 變更檔案: 1 個
- **2022-11-30 15:41:32**: [BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題 1. 換行符號做轉換表單:行動版絕對位置、行動版相對位置 2. 樣式調整:絕對位置、相對位置與響應式表單的Grid摘要以原本空一格方式呈現, 下方內容有換行符號時則呈現換行
  - 變更檔案: 6 個
- **2022-11-30 11:28:37**: [WEB]Q00-20221128006調整絕對定位表單RadioButton顏色更清楚。
  - 變更檔案: 3 個
- **2022-11-21 15:04:41**: [Web]Q00-20221116003 修正 Checkbox、RadioButton 元件，若文字過多造成換行時，勾選按鈕會有偏移的問題
  - 變更檔案: 1 個
- **2022-11-15 10:11:48**: Revert "[流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信"
  - 變更檔案: 3 個
- **2022-11-04 15:30:30**: [Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。
  - 變更檔案: 2 個
- **2022-11-01 10:39:08**: [Web]Q00-20220805002 調整log訊息，當流程向後派送，後面關卡解析的使用者找不到或是沒有主部門時，增加log訊息
  - 變更檔案: 1 個
- **2022-11-01 10:29:10**: [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。 另外還有發現： 1. 活動逾時排程動作執行之後，後續待辦關卡也不會寄送通知信，已修正。 2. 用系統管理員從流程圖檢視上的活動關卡頁面直接執行跳過，後續待辦關卡也不會寄送通知信，已修正。
  - 變更檔案: 1 個
- **2022-10-19 11:27:34**: [WEB]Q00-20221013002:修正表單欄位有設定 "唯讀"時的欄位顏色，顯示卻都為背景顏色。
  - 變更檔案: 5 個
- **2022-10-05 15:13:19**: 移除[ESS]S00-20211208003新增ESS外網主機IP設定
  - 變更檔案: 3 個
- **2022-10-05 15:04:14**: Revert "[ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)"
  - 變更檔案: 21 個
- **2022-10-03 11:36:39**: Revert "[Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦"
  - 變更檔案: 1 個
- **2022-09-19 13:54:05**: [Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部 原單號: C01-20220527001 個人資訊>流程相關設定>工作事項顯示設定 ，當點選關注流程應該進入待辦中的關鍵流程、點選重要流程應該進入待辦的重要流程
  - 變更檔案: 1 個
- **2022-09-15 11:22:47**: [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
  - 變更檔案: 1 個
- **2022-09-15 10:33:07**: Revert "[流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯"
  - 變更檔案: 1 個
- **2022-09-15 09:47:02**: [Web]S00-20220810001簽核意見是否顯示管理員
  - 變更檔案: 3 個
- **2022-09-15 09:19:15**: [流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤
  - 變更檔案: 1 個
- **2022-09-15 09:16:41**: [流程引擎]Q00-20220627001 優化核決層級關卡解析人員緩慢問題
  - 變更檔案: 1 個
- **2022-09-06 15:55:17**: [流程引擎]Q00-20220823003 讓亂數產生的ID增加動態加簽CustomDecisionRule的開頭關鍵字，前面流程圖解析邏輯段也要新增
  - 變更檔案: 2 個
- **2022-09-06 15:52:12**: [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
  - 變更檔案: 1 個
- **2022-08-29 10:11:28**: Revert "[流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容"
  - 變更檔案: 1 個
- **2022-07-27 14:37:52**: [內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來
  - 變更檔案: 1 個
- **2022-08-10 08:56:52**: Revert "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。"
  - 變更檔案: 2 個
- **2022-08-03 16:04:39**: [Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失 RWD不會發生的原因是就算前端直是空的傳進來也是[] 移動防呆位置
  - 變更檔案: 1 個
- **2022-08-03 15:53:36**: [流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料
  - 變更檔案: 2 個
- **2022-08-03 15:38:59**: Revert "[流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料"
  - 變更檔案: 2 個
- **2022-08-01 16:38:12**: Revert "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock"
  - 變更檔案: 3 個
- **2022-08-01 13:51:55**: Revert "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況"
  - 變更檔案: 3 個
- **2022-08-01 13:50:19**: Revert "[MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]"
  - 變更檔案: 3 個
- **2022-07-25 17:12:47**: Revert "[Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部"
  - 變更檔案: 1 個
- **2022-07-18 10:06:16**: [Web]Q00-20220714003 修正Dialog元件的txt屬性如果被FormScript或其他非預期方式刪除，在產生表單畫面時報錯 如果屬性被改成null或是遺失將其防呆為空字串 內部測試將元件隱藏一關或是連續兩關以上都無法重現，應該是客戶的Script有改到元件內容
  - 變更檔案: 1 個
- **2022-07-15 08:37:59**: [Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法 語法: FormUtil.setScrollBarHeight("0");
  - 變更檔案: 1 個
- **2022-07-14 17:29:49**: [流程引擎]Q00-20220622001 修正當RWD表單的RadioButton元件與CheckBox元件選項內容太長時會斷行 相關議題單：C01-20220613009。
  - 變更檔案: 1 個
- **2022-07-14 17:27:26**: [Web]Q00-20220621001 修正非CheckBox或RadioButton的選擇元件執行到額外輸入框邏輯導致出現非預期異常
  - 變更檔案: 1 個
- **2022-07-14 16:39:42**: [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼 1.移動端原本就會把中文字轉換成Unicode字符代碼作呈現,如果再轉一次就會把&轉換掉 2.關聯紀錄:A00-20220511001
  - 變更檔案: 1 個
- **2022-07-14 16:37:51**: Revert "[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼"
  - 變更檔案: 1 個
- **2022-07-14 16:34:56**: [流程引擎]Q00-20220707002 修正表單日期元件預設值計算錯誤 相關議題單：C01-20220701004。
  - 變更檔案: 1 個
- **2022-07-14 16:28:34**: [Web]A00-20220628001 修正已簽核過的關卡，從Mail的待辦連結進入該表單時可以移除附件
  - 變更檔案: 1 個
- **2022-07-14 16:20:49**: [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
  - 變更檔案: 1 個
- **2022-07-14 16:19:46**: Revert "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開"
  - 變更檔案: 1 個
- **2022-07-14 16:14:21**: [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
  - 變更檔案: 1 個
- **2022-07-14 16:13:16**: Revert "[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘"
  - 變更檔案: 1 個

### 謝閔皓 (32 commits)

- **2023-02-10 10:54:51**: [Web]Q00-20230208001 修正 RWD 表單設計師的 CheckBox 元件，若在腳本將 CheckBox 元件的 disabled 設為 false，並且在表單存取控管設定設為 disable，CheckBox 元件按鈕顯示異常的問題
  - 變更檔案: 1 個
- **2023-03-02 15:06:41**: [Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題
  - 變更檔案: 1 個
- **2023-01-12 17:49:56**: [Web]Q00-20230112004 修正 TextBox 元件的進階設定，若設定資料型態為浮點數且顯示千分位會導致顯示值異常的問題
  - 變更檔案: 1 個
- **2023-01-16 11:01:34**: [Web]Q00-20230116001 上傳附件功能，優化使用者提示
  - 變更檔案: 2 個
- **2022-12-21 16:41:05**: [Web]Q00-20221221002 修正 TextArea 資料內容過多在列印表單時，會蓋到簽核意見的問題
  - 變更檔案: 1 個
- **2023-03-03 11:56:46**: [Web]Q00-20230303001 調整 TextBox 元件進階設定中小數點後幾位的保存方式多語系，原本為實際值與四捨五入，將實際值調整為無條件捨去
  - 變更檔案: 3 個
- **2023-02-16 18:48:18**: [表單設計師]Q00-20230216003 新增表單設計師中的元件代號與元件名稱不支持 _lbl、_txt、_txt1、_txt2
  - 變更檔案: 3 個
- **2023-02-22 17:10:25**: [Web]Q00-20230222003 修正 TextBox 設定浮點數、顯示千分位和小數點後幾位時，與 Grid 繫結會導致 FromScript 取得 Grid 資料以及 FormInstance 的 FieldValues 會有千分位的問題
  - 變更檔案: 1 個
- **2023-02-18 13:36:07**: [Web]Q00-20230218001 調整讓 Grid 支援使用 <div>
  - 變更檔案: 1 個
- **2023-02-15 08:53:35**: [Web]Q00-20230215001 修正使用 https 且為 Safari 瀏覽器下載附件時，若檔名有中文會變成亂碼的問題
  - 變更檔案: 1 個
- **2023-02-07 13:15:34**: [Web]A00-20230204001 修正追蹤流程中，查看已轉派工作且已處理的單據，點擊回到工作清單卻呈現工作通知清單的問題
  - 變更檔案: 1 個
- **2022-11-03 18:09:04**: [流程設計師]Q00-20221103003 修正流程定義/事件處理/流程完成/網頁應用程式，第一次點擊編輯時畫面空白的問題
  - 變更檔案: 1 個
- **2022-12-02 16:08:17**: [Web]Q00-20221202002 修正 Firefox 瀏覽器開啟絕對位置表單，使用列印表單的功能，畫面顯示異常的問題
  - 變更檔案: 1 個
- **2023-01-03 18:17:06**: [Web]Q00-20230103002 修正系統設定使用 LDAP 驗證，若使用者沒有設定 LDAP 驗證，從通知信連結進入 BPM 登入頁時，帳號欄位沒有自動帶入 UserId 的問題
  - 變更檔案: 1 個
- **2022-12-17 13:50:13**: [T100]Q00-20221217001 修正 T100 整合表單，退件表單資訊的放大鏡按鈕無法開啟
  - 變更檔案: 1 個
- **2022-10-26 09:06:05**: [Web]Q00-20221020004 修正 TextBox 元件進階功能的運算規則，若將已綁定的欄位值輸入後又刪除，會顯示 NaN 的問題
  - 變更檔案: 1 個
- **2022-11-02 14:53:14**: [Web]Q00-20221102001 修正checkbox設計時，若有勾選「最後一個選項額外產生輸入框」，表單中checkbox呈現與列印不一致的問題
  - 變更檔案: 1 個
- **2022-11-01 15:24:25**: [Web]Q00-20221101003 修正使用者若有離職作業維護，點選離職人員會跳到登入畫面的問題
  - 變更檔案: 1 個
- **2022-10-18 10:33:07**: [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
  - 變更檔案: 1 個
- **2022-10-06 13:39:46**: [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
  - 變更檔案: 1 個
- **2022-09-07 17:08:34**: [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題[補修正]
  - 變更檔案: 1 個
- **2022-09-07 14:59:37**: [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題
  - 變更檔案: 1 個
- **2022-10-15 11:47:52**: [TIPTOP]Q00-20221014007修正客戶從TIPTOP端udm_tree操作原稿匣撤銷流程時，選擇特定流程後，BPM仍會回傳所有可撤銷流程的清單
  - 變更檔案: 1 個
- **2022-09-30 15:47:30**: [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]
  - 變更檔案: 1 個
- **2022-09-30 12:24:25**: [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員
  - 變更檔案: 1 個
- **2022-08-08 14:57:44**: [Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題
  - 變更檔案: 1 個
- **2022-08-04 22:21:33**: [Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容
  - 變更檔案: 2 個
- **2022-08-23 15:27:14**: [流程引擎]S00-20220722001新增批次通知信件主旨內容
  - 變更檔案: 1 個
- **2022-08-08 17:43:54**: [Web]Q00-20220808003修正使用產品表單中的Date元件，並搭配TextBox元件的進階功能，資料型態整數中的時間區間運算，當遇到元件ID有使用下底線時，會導致TextBox元件無法正常運算
  - 變更檔案: 1 個
- **2022-08-11 12:56:57**: [Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題
  - 變更檔案: 1 個
- **2022-08-10 18:34:57**: [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
  - 變更檔案: 1 個
- **2022-08-01 16:26:26**: [Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料
  - 變更檔案: 1 個

### cherryliao (18 commits)

- **2023-05-05 11:13:22**: [Web]Q00-20230504003 修正流程中附件檔名包含逗號時，檔案無法下載的問題
  - 變更檔案: 1 個
- **2023-04-14 10:47:52**: [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
  - 變更檔案: 1 個
- **2023-02-14 14:03:47**: [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題
  - 變更檔案: 2 個
- **2023-04-14 10:28:30**: [Web]Q00-20230414001 修正當用戶逾時閒置過久會彈出null訊息框的問題
  - 變更檔案: 2 個
- **2023-04-13 10:27:20**: [Web]Q00-20230413001 修正在表單腳本有使用addAttachment的方法時會無法取得附件描述的問題
  - 變更檔案: 1 個
- **2022-07-14 14:58:11**: [Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法
  - 變更檔案: 1 個
- **2023-03-24 17:08:21**: [Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕
  - 變更檔案: 1 個
- **2023-02-21 16:30:41**: [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題
  - 變更檔案: 1 個
- **2022-11-11 11:07:05**: [Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息
  - 變更檔案: 1 個
- **2022-10-06 11:29:53**: [流程設計師]Q00-20221006001 調整在流程設計點擊編輯表單欄位權限時，若表單發行狀態已過期或UNDER_REVISION時會彈提示訊息
  - 變更檔案: 5 個
- **2022-09-16 13:48:45**: [Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題
  - 變更檔案: 1 個
- **2022-09-08 14:06:21**: [Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息
  - 變更檔案: 2 個
- **2022-08-10 11:01:13**: [Web]A00-20220808001 調整報表查詢產出的日期與匯出Excel的日期不一致問題
  - 變更檔案: 1 個
- **2022-07-27 17:51:33**: [Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題
  - 變更檔案: 1 個
- **2022-07-28 14:39:59**: [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]
  - 變更檔案: 1 個
- **2022-07-20 11:38:23**: [Web]A00-20220718001 修正Gird元件在某關卡隱藏時開啟表單會出現該物件沒有定義的問題
  - 變更檔案: 2 個
- **2022-07-19 18:01:01**: [登入]Q00-20220719002 修正DB為Oracle時，使用者登出登入紀錄作業中使用操作時間為查詢條件會查不到結果的問題
  - 變更檔案: 1 個
- **2022-07-12 11:15:32**: [BPM APP]Q00-20220711001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟移動端表單會報錯的問題
  - 變更檔案: 1 個

### raven.917 (34 commits)

- **2023-02-03 12:19:44**: [WEB] Q00-20230203002 修正絕對定位表單，預覽列印下，RadioButton顯示異常
  - 變更檔案: 1 個
- **2022-09-19 14:33:11**: [表單設計師]S00-20220707005系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷，四則運算及單身加總運算。(補修正)
  - 變更檔案: 1 個
- **2023-02-15 10:44:12**: [WEB] V00-20230214004 修正設置浮點數無法過濾特殊字元符號問題
  - 變更檔案: 1 個
- **2022-10-25 15:25:10**: [Web]Q00-20221006004 上傳附件功能，優化使用者提示，且上傳過程不可點擊關閉按鈕。
  - 變更檔案: 1 個
- **2023-02-13 17:07:46**: [WEB] V00-20230204001 更改經常選取對象視窗選取邏輯，每次比對時都先取最新的值。
  - 變更檔案: 1 個
- **2022-12-16 11:20:15**: [WEB]Q00-20221216001調整腳本「更改Grid欄位寬度」的提示訊息。
  - 變更檔案: 1 個
- **2022-10-13 15:36:41**: [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。(改)
  - 變更檔案: 1 個
- **2022-10-13 14:03:06**: [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。
  - 變更檔案: 1 個
- **2022-10-13 11:06:45**: [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，找不到欄位ID時的Alert訊息
  - 變更檔案: 1 個
- **2022-10-12 11:33:33**: [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，ID為null時的Alert訊息。
  - 變更檔案: 1 個
- **2022-11-25 15:25:38**: [組織設計師]Q00-20221125002調整復職時，移除通知SQL寫法。
  - 變更檔案: 1 個
- **2023-02-08 20:06:25**: [WEB] V00-20221019001 修正監控流程設置「已撤銷」，無法匯出Excel
  - 變更檔案: 1 個
- **2022-10-25 15:18:30**: [Web]V00-20221019001修正流程管理/監控流程 選擇「已撤銷」流程，匯出Excel發現多了「執行中的關卡」跟「目前處理者」的欄位。
  - 變更檔案: 1 個
- **2022-10-14 09:17:30**: [內部]V00-20221012008修正流程管理/監控流程 選擇「已關閉」流程，匯出Excel發現多了簽核時間的欄。
  - 變更檔案: 1 個
- **2023-01-04 23:48:42**: [WEB]Q00-20230104006修正列印模式下，絕對位置表單RadioButton顯示異常
  - 變更檔案: 2 個
- **2022-12-23 10:28:42**: [WEB]Q00-20221223001 流程資料查詢頁面無法下載附件
  - 變更檔案: 1 個
- **2022-10-26 16:01:07**: [Web]S00-20220510001新增運算規則可以選取到hidden元件。
  - 變更檔案: 1 個
- **2022-12-08 17:00:39**: [WEB]Q00-20221208003 修正加簽關卡不會自動帶入簽核意見且要相容自訂關卡後會被清除簽核意見問題。(補修正)
  - 變更檔案: 1 個
- **2022-12-08 16:31:48**: [WEB]Q00-20221208003 修正加簽關卡不會自動帶入簽核意見。
  - 變更檔案: 1 個
- **2022-11-29 15:12:08**: [流程引擎]Q00-20221129001修正修正nchar欄位型態錯誤比對問題，導致轉存表單存空值。
  - 變更檔案: 1 個
- **2022-09-20 18:09:03**: [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)
  - 變更檔案: 1 個
- **2022-11-08 15:25:39**: [Web]Q00-20221108001修正輸入元件設置必填後，沒勾選隱藏標籤原label標籤會出現undefined
  - 變更檔案: 1 個
- **2022-10-25 15:06:58**: [Web]S00-20220711001Textbox元件設置整數及浮點數自動進位輸入值。
  - 變更檔案: 2 個
- **2022-10-11 09:20:02**: [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(修)
  - 變更檔案: 1 個
- **2022-09-21 10:40:08**: [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)
  - 變更檔案: 1 個
- **2022-09-20 16:57:50**: [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。
  - 變更檔案: 2 個
- **2022-09-16 14:46:47**: [表單設計師]S00-20220707005系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷。
  - 變更檔案: 1 個
- **2022-10-28 15:59:37**: [WEB]A00-20221027001修正新增關卡內-經常選取對象無法第二次選取進清單。(補修正)
  - 變更檔案: 1 個
- **2022-10-28 15:20:12**: [WEB]A00-20221027001修正新增關卡內-經常選取對象無法第二次選取進清單。
  - 變更檔案: 1 個
- **2022-10-17 10:37:56**: [WEB]Q00-20221014003修正變更經常選取對象 & 變更您的關係人，更新資料後沒有即時刷新頁面問題。
  - 變更檔案: 3 個
- **2022-10-06 14:35:32**: [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。
  - 變更檔案: 2 個
- **2022-10-06 08:57:33**: [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用(補修正，增加可讀性)
  - 變更檔案: 1 個
- **2022-10-04 15:26:58**: [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用
  - 變更檔案: 1 個
- **2022-09-21 16:42:30**: [WEB] C01-20220919007 Admin 需要能開啟設計師時，直接針對該表單做復原簽出的操作行為。
  - 變更檔案: 2 個

### yamiyeh10 (15 commits)

- **2022-11-01 17:40:10**: [WEB]Q00-20221101005 修正在表單上設定運算規則時有參考單身加總的元件時不會自動觸發更新的問題
  - 變更檔案: 1 個
- **2023-02-21 15:44:41**: [WEB]Q00-20230221002 修正在行動版的清單頁面上若主旨有<br>時無法正確換行問題
  - 變更檔案: 1 個
- **2023-01-04 11:18:21**: [WEB]Q00-20230104002 修正RWD表單在TextBox元件調整字體大小後使用iOS手機查看時欄位中的字不能完整呈現
  - 變更檔案: 1 個
- **2022-11-28 12:12:45**: [流程引擎]Q00-20221128001 調整系統排程設定在新增、編輯、刪除後檢查所有排程的首次執行時間若小於當前時間時更新首次執行時間避免觸發即時執行排程機制
  - 變更檔案: 1 個
- **2022-08-16 14:49:49**: [Web]A00-20220811001 修正表單若TextBox元件設定浮點數且顯示實際值時會有偏移值問題
  - 變更檔案: 1 個
- **2022-11-03 11:24:45**: [WEB]Q00-20221103001 使用者撤銷流程，理由填空白字串時不允許撤銷流程
  - 變更檔案: 1 個
- **2022-08-09 13:51:45**: [組織同步]Q00-20220809002 修正組織同步log出現Error時改寄送失敗通知信
  - 變更檔案: 1 個
- **2022-07-25 10:22:50**: [Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題
  - 變更檔案: 1 個
- **2022-07-20 18:23:57**: [Web]Q00-20220720002 修正列印模式下附件與簽核歷程的右邊邊線會不見問題
  - 變更檔案: 1 個
- **2022-07-08 11:01:58**: [表單設計師]C01-20220706003 修正當變更表單的對齊方式並儲存後會將已設計過的行動版表單設計欄位清空問題
  - 變更檔案: 1 個
- **2022-05-26 10:06:49**: [BPM APP]C01-20220524002 修正改派通知設定整張表單時Line推播內容不會呈現表單訊息格式的問題
  - 變更檔案: 1 個
- **2022-05-10 10:49:37**: [BPM APP]C01-20220509009 修正行動端在Grid元件存在沒有繫結元件情況下點擊編輯按鈕會失敗問題
  - 變更檔案: 1 個
- **2022-05-26 09:50:36**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-16 15:15:57**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-10 18:03:59**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題
  - 變更檔案: 1 個

### 王鵬程 (13 commits)

- **2022-06-21 15:19:39**: [Web]Q00-20220621005 調整RWD Grid當單身資料太多時導致開啟表單時間較長
  - 變更檔案: 1 個
- **2022-07-29 16:49:53**: [Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示
  - 變更檔案: 1 個
- **2022-07-28 17:32:17**: [Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行
  - 變更檔案: 1 個
- **2022-07-26 18:16:29**: [Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入
  - 變更檔案: 1 個
- **2022-07-20 15:26:09**: [Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部
  - 變更檔案: 1 個
- **2022-07-13 16:33:02**: [Web]Q00-20220713003 修正在行動版面中，在表單內向下滑動時，右下角的浮動按鈕會隱藏而無法後續操作
  - 變更檔案: 1 個
- **2022-07-11 15:22:15**: [表單設計師]Q00-20220711002 修正絕對表單元件不存在某些屬性而取用該屬性導致無法開啟表單，增加防呆
  - 變更檔案: 1 個
- **2022-06-21 16:55:38**: [Web]A00-20220616003 修正SerialNumber元件的字體設25px以上，在流程中該元件的顯示會有部分被遮蔽到
  - 變更檔案: 1 個
- **2022-06-09 11:50:53**: [Web]Q00-20220609001 修正行動裝置在加簽關卡選擇參與者人員後，選擇參與者的下方欄位會顯示已選取0個項目
  - 變更檔案: 1 個
- **2022-06-13 16:14:20**: [Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾
  - 變更檔案: 1 個
- **2022-05-05 17:52:51**: [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果[補]
  - 變更檔案: 1 個
- **2022-05-05 15:24:43**: [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果
  - 變更檔案: 1 個
- **2022-04-27 16:50:57**: [Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色
  - 變更檔案: 1 個

### wencheng1208 (15 commits)

- **2022-07-29 16:30:05**: [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。
  - 變更檔案: 2 個
- **2022-09-30 10:36:45**: [Web]Q00-20220930001 執行iReport套件當發生Exception錯誤時，增加列印異常的堆疊資訊
  - 變更檔案: 1 個
- **2022-09-21 17:49:58**: [Web]Q00-20220921002 調整「必須上傳新附件」邏輯，只要存在一筆以上的附件並且符合在該「關卡名稱」上傳的附件，即可通過該驗證
  - 變更檔案: 1 個
- **2022-09-01 15:53:47**: [Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式
  - 變更檔案: 1 個
- **2022-09-12 17:59:01**: [流程引擎]Q00-20220912004 修正findProcessPackageById方法內容為取得流程包裹最新一版，以避免後續同仁遇到此坑
  - 變更檔案: 1 個
- **2022-09-12 17:39:32**: [組織同步]QQ00-20220912003 組織同步功能執行人員資料修改時，可保留人員姓名多語系關聯
  - 變更檔案: 1 個
- **2022-09-08 15:12:59**: [Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本
  - 變更檔案: 1 個
- **2022-09-14 10:34:55**: [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
  - 變更檔案: 1 個
- **2022-08-24 11:48:03**: [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
  - 變更檔案: 1 個
- **2022-08-23 14:44:52**: [流程引擎]Q00-20220823001 修正使用客製的方式執行動態加簽後，無法呈現詳細流程圖畫面的問題
  - 變更檔案: 1 個
- **2022-08-05 12:00:16**: [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]
  - 變更檔案: 1 個
- **2022-08-03 16:48:09**: [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。
  - 變更檔案: 1 個
- **2022-07-25 10:55:23**: [Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法
  - 變更檔案: 1 個
- **2022-07-29 16:30:05**: [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。
  - 變更檔案: 2 個
- **2022-07-27 12:07:05**: [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。
  - 變更檔案: 1 個

### walter_wu (24 commits)

- **2022-07-14 11:32:09**: [流程引擎]Q00-20220713005 修正核決關卡設定自動簽核，取/退回後再次簽核進核決層級時會報錯無法派送
  - 變更檔案: 1 個
- **2022-07-29 14:20:21**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]
  - 變更檔案: 2 個
- **2022-07-29 00:04:37**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
  - 變更檔案: 3 個
- **2022-07-29 00:04:37**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
  - 變更檔案: 3 個
- **2022-07-14 11:32:09**: [流程引擎]Q00-20220713005 修正核決關卡設定自動簽核，取/退回後再次簽核進核決層級時會報錯無法派送
  - 變更檔案: 1 個
- **2022-06-28 18:06:01**: [Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久
  - 變更檔案: 1 個
- **2022-07-01 11:54:21**: [Web]Q00-20220701001 調整時間元件如果輸入不是數字直接換成00
  - 變更檔案: 1 個
- **2022-06-08 09:21:56**: [報表設計器]Q00-20220607003 修正欄位字串如果含as會辨識錯欄位名稱
  - 變更檔案: 1 個
- **2022-07-08 18:24:42**: [表單設計師]A00-20220704001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟表單會報錯
  - 變更檔案: 2 個
- **2022-06-13 16:07:01**: [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯
  - 變更檔案: 1 個
- **2022-06-09 15:28:14**: [內部]Q00-20220609002 調整DWR設定讓Log不要一直出現轉換ProcessInstanceStateType的錯誤
  - 變更檔案: 1 個
- **2022-06-10 18:10:24**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
  - 變更檔案: 1 個
- **2022-06-08 15:56:57**: [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
  - 變更檔案: 1 個
- **2022-06-08 11:49:23**: [WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯
  - 變更檔案: 1 個
- **2022-06-01 13:57:59**: [Web]A00-20220526001 修正如果DB是Oralce在線閱讀浮水印管理出現無法取得EJB所提供的服務
  - 變更檔案: 1 個
- **2022-05-30 15:34:30**: [內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方
  - 變更檔案: 1 個
- **2022-05-27 17:34:09**: [Web]Q00-20220527003 修正使用者使用監控流程的最大筆數沒有根據process.default.show.records的設定
  - 變更檔案: 1 個
- **2022-05-27 17:15:41**: [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
  - 變更檔案: 1 個
- **2022-05-25 18:07:49**: [Web]A00-20220519001 修正IE加簽會加成兩次的問題
  - 變更檔案: 1 個
- **2022-05-24 19:04:27**: [系統管理工具]C01-20220524002 修正進階功能>檢查密碼如果User裡有關聯有異常的會全部撈不出來
  - 變更檔案: 1 個
- **2022-05-23 18:19:20**: [內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)
  - 變更檔案: 1 個
- **2022-05-11 14:44:02**: [內部]Q00-20220511003 調整BCL8轉檔Timeout從預設2分鐘拉長到5分鐘
  - 變更檔案: 1 個
- **2022-05-09 16:41:13**: [Web]Q00-20220509002 修正授權的流程使用closeTime(結案時間)排序會報錯的問題
  - 變更檔案: 1 個
- **2022-04-27 15:00:41**: [內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤
  - 變更檔案: 1 個

### yanann_chen (29 commits)

- **2020-08-05 10:44:55**: [Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦
  - 變更檔案: 1 個
- **2022-07-01 15:05:36**: [流程引擎]Q00-20220629004 修正流程定義設定「流程撤銷時逐級通知」，在使用者撤銷流程後，只有撤銷流程當下進行中的關卡的處理者在系統通知清單內有該筆通知資料
  - 變更檔案: 3 個
- **2022-06-29 14:31:43**: [流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料
  - 變更檔案: 2 個
- **2022-07-14 18:28:53**: [Web]Q00-20220714004 修正使用safari瀏覽器時，點選在線閱讀附件沒有反應
  - 變更檔案: 2 個
- **2022-07-06 17:43:27**: [流程引擎]Q00-20220706005 修正資料庫為MSSQL，且流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
  - 變更檔案: 1 個
- **2022-07-13 16:21:42**: [Web]Q00-20220713004 修正移動消息訂閱管理頁面無法開啟
  - 變更檔案: 1 個
- **2022-07-11 17:38:00**: [流程引擎]Q00-20220711003 修正在表單上將Excel匯入單身後，開窗畫面變成空白
  - 變更檔案: 1 個
- **2022-06-20 14:09:46**: [Web]A00-20220616001 退回重辦頁面增加退回重辦方式的說明
  - 變更檔案: 1 個
- **2022-06-28 17:17:20**: [流程引擎]Q00-20220628001 調整流程關係人與關係部門解析邏輯，若流程中沒有變更流程關係人或關係部門資料，系統在流程往下派送時就不會再解析流程關係人與關係部門
  - 變更檔案: 1 個
- **2022-07-07 16:10:43**: [流程引擎]Q00-20220707003 修正DialogInput元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容
  - 變更檔案: 1 個
- **2022-06-09 17:18:03**: [Web]A00-20220608003 修正進入追蹤流程畫面時未清除「撤銷理由」欄位內容
  - 變更檔案: 1 個
- **2022-06-09 16:32:41**: [流程引擎]Q00-20220609003 修正使用者操作個人預設代理人設定時，代理人可能有多筆相同人員的問題
  - 變更檔案: 1 個
- **2022-05-25 17:01:11**: [表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出
  - 變更檔案: 3 個
- **2022-05-25 14:01:03**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
  - 變更檔案: 1 個
- **2022-05-25 10:57:21**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
  - 變更檔案: 1 個
- **2022-05-24 17:45:41**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤
  - 變更檔案: 1 個
- **2022-05-20 15:42:50**: [流程引擎]Q00-20220520003 修正流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
  - 變更檔案: 1 個
- **2022-05-12 18:19:18**: [流程引擎]A00-20220511001 修正使用者輸入到表單TextArea的內容在儲存表單後變成亂碼的問題
  - 變更檔案: 1 個
- **2022-05-12 14:36:06**: [流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差
  - 變更檔案: 1 個
- **2022-05-12 14:12:31**: [Web]Q00-20220411005 修正使用者在絕對位置表單進行簽核時遭遇產品程式錯誤
  - 變更檔案: 1 個
- **2022-05-12 11:47:55**: [內部]Q00-20220512001 bootstrap-table-1.18.3.js更換檔案名稱
  - 變更檔案: 4 個
- **2022-05-11 15:03:45**: [Web]Q00-20220511005 修正T100拋轉單據中有舊值的單身內容沒有顯示為紅色
  - 變更檔案: 1 個
- **2022-05-11 12:07:20**: [流程引擎]Q00-20220506001 調整使用者「授權的流程」，流程清單筆數改為設定檔設定
  - 變更檔案: 1 個
- **2022-05-11 11:23:52**: [流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料
  - 變更檔案: 1 個
- **2022-05-11 11:10:44**: [流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知
  - 變更檔案: 1 個
- **2022-05-11 10:54:24**: [Web]Q00-20220511001 調整列印表單畫面簽核歷程，移除「資料代號」、「通知者」欄位
  - 變更檔案: 1 個
- **2022-06-20 16:44:21**: [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-04 14:02:42**: [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題
  - 變更檔案: 1 個
- **2022-05-20 14:08:18**: [流程引擎]A00-20220519003 修正終止前置流程時，後置流程完成流程撤銷後，前置流程出現「派送失敗」的錯誤訊息，無法正常終止
  - 變更檔案: 2 個

### pinchi_lin (1 commits)

- **2022-07-12 19:29:30**: [MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]
  - 變更檔案: 3 個

### 郭哲榮 (6 commits)

- **2022-07-12 20:01:24**: [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼
  - 變更檔案: 1 個
- **2022-05-27 19:22:55**: [BPM APP]C01-20220523004 修正移動端subTab元件使用formScript在單獨顯示時會顯示其他頁籤內容的問題
  - 變更檔案: 1 個
- **2022-05-18 18:47:47**: [BPM APP]C01-20220516002 修正行動端FormUtil.disable為true時Dropdown元件顯示異常問題
  - 變更檔案: 1 個
- **2022-05-17 15:12:44**: [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題[補]
  - 變更檔案: 1 個
- **2022-05-16 15:10:33**: [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題
  - 變更檔案: 1 個
- **2022-04-28 12:23:28**: [BPM APP]C01-20220419001 修正移動端選項元件使用動態塞值且隱藏標籤會顯示異常問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
- **Commit ID**: `da4e026f671ed73433a8fda6fc39aca6e8ba7012`
- **作者**: lorenchang
- **日期**: 2022-06-26 20:48:05
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [資安]Q00-20231011001 強化ajax-DataBaseAccessor服務邏輯以禁止非法調用
- **Commit ID**: `71555f400566acbd95d5c8b88648c0066c9fd3cd`
- **作者**: 林致帆
- **日期**: 2023-10-11 14:12:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 3. [ISO]Q00-20230509001 修正ISO變更單於ModDocRequester關卡載入上一版附件後，點擊下載按鈕沒有反應
- **Commit ID**: `c441642ba7f16a01e97d31ceb64824773ceb7553`
- **作者**: waynechang
- **日期**: 2023-05-09 17:30:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 4. [ISO]Q00-20220426001 調整ISO變更單，載入上一版附件時，附件的上傳時間改以系統參數設定「系統當前時間」或「原始上傳時間」
- **Commit ID**: `0e1f2441961fa5cd8f6412836edb4cef6b212aad`
- **作者**: wayne
- **日期**: 2022-04-26 13:59:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 5. [Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常
- **Commit ID**: `ee88a332296ecdfa1783fc23e78cb708277bb97d`
- **作者**: 林致帆
- **日期**: 2023-06-01 10:56:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`

### 6. [資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊
- **Commit ID**: `a42eeb3ad0a4e18771eb693b153babf4899ca564`
- **作者**: 林致帆
- **日期**: 2023-04-12 14:58:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 7. [資安]C01-20230203007 弱掃資安議題，調整Nginx設定add_header X-Content-Type-Options nosniff; 會導致BPM的登入頁無法使用，因jsp引用dwr動態產生的dwrDefault/interface/底下的js檔在產生時未指定Content-Type,導致瀏覽器無法解析。反組譯修改dwr.jar檔中的(DefaultInterfaceProcessor.class)
- **Commit ID**: `916cdadabcc00db9ade8c9648486a0440e9d8d3f`
- **作者**: 林致帆
- **日期**: 2023-02-16 13:59:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`

### 8. Revert "[資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊"
- **Commit ID**: `4c5035ffbb6e310d7e037aa011519c446bf84dd9`
- **作者**: kmin
- **日期**: 2023-06-09 14:03:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 9. [資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊
- **Commit ID**: `ce2e3720856b895d06c6b1786a97df917dcb65bb`
- **作者**: 林致帆
- **日期**: 2023-04-12 14:58:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 10. [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題(補修正)
- **Commit ID**: `2c294a5f72df38d67a5717b7830e39d425575327`
- **作者**: kmin
- **日期**: 2023-05-30 13:30:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 11. [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題
- **Commit ID**: `7c3d08b40d7561ad9a9045d65e2605e9809abf59`
- **作者**: kmin
- **日期**: 2023-05-30 13:27:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 12. [Web]Q00-20230208001 修正 RWD 表單設計師的 CheckBox 元件，若在腳本將 CheckBox 元件的 disabled 設為 false，並且在表單存取控管設定設為 disable，CheckBox 元件按鈕顯示異常的問題
- **Commit ID**: `c232e7e0017c74a26b09a79ae9e60ae92bc573ff`
- **作者**: 謝閔皓
- **日期**: 2023-02-10 10:54:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 13. [流程引擎]Q00-20230512001 修正關卡有啟用自動簽核跳關，當核決關卡參考該關卡時，若該關卡同時有向前或向後加簽時，自動簽核的功能就會失效
- **Commit ID**: `98e18ba8d87a0176bb3b0c65ef6689da6a98f65a`
- **作者**: waynechang
- **日期**: 2023-05-17 12:00:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 14. [Web]Q00-20230504003 修正流程中附件檔名包含逗號時，檔案無法下載的問題
- **Commit ID**: `00b01554a11d4a506175d397703ce132f9e139ea`
- **作者**: cherryliao
- **日期**: 2023-05-05 11:13:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 15. [Web]Q00-20230414005 調整下載附件不該顯示This URL not have permission to download the file訊息
- **Commit ID**: `61837682d09b3cd4f079c81913fbca3f77cd06de`
- **作者**: 林致帆
- **日期**: 2023-04-14 15:44:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 16. [Web]Q00-20230509002 修正表單附件上傳後；若重新透過附件開窗上傳新的檔案時，原先上傳的附件無法下載的異常
- **Commit ID**: `f9b0693db3230273aab492074c148cc5d23884aa`
- **作者**: waynechang
- **日期**: 2023-05-09 17:39:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

### 17. [Web]Q00-20230303002 修正人員開窗選取帶有特殊字"𤧟"的人員派送後表單會重複多長好幾個"𤧟"字
- **Commit ID**: `17a7e9973ff5368752377827a8f67f5b118a265e`
- **作者**: kmin
- **日期**: 2023-05-08 15:47:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/Dom4jUtil.java`

### 18. [WEB] Q00-20230203002 修正絕對定位表單，預覽列印下，RadioButton顯示異常
- **Commit ID**: `e36d97f7e3a998b7784aa183643370e6c1eba62d`
- **作者**: raven.917
- **日期**: 2023-02-03 12:19:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 19. [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
- **Commit ID**: `557df8dd6f60469aae8dcde1b7473847759be25c`
- **作者**: 林致帆
- **日期**: 2023-05-05 15:42:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 20. [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
- **Commit ID**: `7e4c9523cfc6b3ab37b3f43731c739c3616f5595`
- **作者**: 林致帆
- **日期**: 2023-05-04 11:49:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 21. [Web]Q00-20230425004 修正列表搜尋條件輸入的字串包含[ 時會搜尋不到資料的問題 1.關聯單號:C01-20230421008 2.當搜尋字串為[xxx]abc時，在MSSQL中會被判斷為xxxabc -- 因[是MSSQL保留關鍵字，故搜尋不到資料 3.只調整fullSearchText 與 subject欄位 4.調整追蹤、待辦、工作通知、撤銷、草稿、取回重辦、轉派、授權的流程等列表
- **Commit ID**: `62c00f344445022e27e42942b40e7c937b110862`
- **作者**: kmin
- **日期**: 2023-05-02 11:49:44
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DraftListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/StringHelper.java`

### 22. [ESS]Q00-20230425001 修正若使用者設定Proxy，取得的clientIP會有實際IP以及Proxy設定之IP，造成開啟ESS表單失敗
- **Commit ID**: `241aab7a08504a8d817b7e3fb39ab183356e84ba`
- **作者**: 林致帆
- **日期**: 2023-04-25 10:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebUtil.java`

### 23. [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
- **Commit ID**: `43949b3efb1e817bd08ad533978dd7c219ae5851`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 24. [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題
- **Commit ID**: `00c142c745a44b93909afd73994e867e86c81d41`
- **作者**: cherryliao
- **日期**: 2023-02-14 14:03:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 25. [Web]Q00-20230414001 修正當用戶逾時閒置過久會彈出null訊息框的問題
- **Commit ID**: `39ebb2b4da7c9500d75849c2164fa22b4a911fe1`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:28:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 26. [Web]Q00-20230413001 修正在表單腳本有使用addAttachment的方法時會無法取得附件描述的問題
- **Commit ID**: `716db244e56edcaa4699755ad0c252c98ec6e779`
- **作者**: cherryliao
- **日期**: 2023-04-13 10:27:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 27. [表單設計師]S00-20220707005系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷，四則運算及單身加總運算。(補修正)
- **Commit ID**: `1a0533053e202a7419e4a33bbc608b317295bed3`
- **作者**: raven.917
- **日期**: 2022-09-19 14:33:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 28. [Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法
- **Commit ID**: `f5a3c6669460400c016697214119f91a663384be`
- **作者**: cherryliao
- **日期**: 2022-07-14 14:58:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 29. Revert "[Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法 語法: FormUtil.setScrollBarHeight("0");"
- **Commit ID**: `da53b278a8b089e439ab536c620283de997ba421`
- **作者**: kmin
- **日期**: 2023-04-12 15:07:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 30. [Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題
- **Commit ID**: `c0b62e6cb8bc74b498f1eb0f13cdf05a85f95d5c`
- **作者**: 謝閔皓
- **日期**: 2023-03-02 15:06:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 31. [WEB] V00-20230214004 修正設置浮點數無法過濾特殊字元符號問題
- **Commit ID**: `dbedd9a66a532459addfaba9379bbf7eede56390`
- **作者**: raven.917
- **日期**: 2023-02-15 10:44:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 32. [Web]Q00-20230112004 修正 TextBox 元件的進階設定，若設定資料型態為浮點數且顯示千分位會導致顯示值異常的問題
- **Commit ID**: `f802b5c1490f8cbcd3469d2ff78f2bce7273c35c`
- **作者**: 謝閔皓
- **日期**: 2023-01-12 17:49:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 33. [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊
- **Commit ID**: `a8687fdb229def4e37aba11adf981d38ae0667e1`
- **作者**: 林致帆
- **日期**: 2023-04-07 15:29:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 34. [Web] Q00-20230313002 修正SelectElement，Style屬性異常問題
- **Commit ID**: `d815432c47a2664754115f204e1ddf00e9e2328e`
- **作者**: kmin
- **日期**: 2023-03-29 15:40:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 35. [Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕
- **Commit ID**: `1fbb8af270f00d0caf1b004ecc40620bdf2684a7`
- **作者**: cherryliao
- **日期**: 2023-03-24 17:08:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 36. [Web]Q00-20230116001 上傳附件功能，優化使用者提示
- **Commit ID**: `202734748d91245ad9172b23a4d88cc955260fb5`
- **作者**: 謝閔皓
- **日期**: 2023-01-16 11:01:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 37. [Web]Q00-20221006004 上傳附件功能，優化使用者提示，且上傳過程不可點擊關閉按鈕。
- **Commit ID**: `9d5ee490db3d4ca44dc95a2a966fa628fe2d504b`
- **作者**: raven.917
- **日期**: 2022-10-25 15:25:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 38. [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析[補]
- **Commit ID**: `25552b0ab029dfc8f73be65507b43c5b9f293db5`
- **作者**: waynechang
- **日期**: 2023-03-23 15:06:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherLocal.java`

### 39. [Web]Q00-20221221002 修正 TextArea 資料內容過多在列印表單時，會蓋到簽核意見的問題
- **Commit ID**: `d61ef5891a35c9a68e16f1caf30e473dc290a967`
- **作者**: 謝閔皓
- **日期**: 2022-12-21 16:41:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap-3.3.5-print.css`

### 40. [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析
- **Commit ID**: `623139e62dcf7f587f46e3e5f8aa085d85588bb8`
- **作者**: kmin
- **日期**: 2023-03-22 14:17:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/OrganizationUnit.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 41. [Web]Q00-20230303001 調整 TextBox 元件進階設定中小數點後幾位的保存方式多語系，原本為實際值與四捨五入，將實際值調整為無條件捨去
- **Commit ID**: `c50eaec3437a646cb0bcd0b4500a1a7384f2031d`
- **作者**: 謝閔皓
- **日期**: 2023-03-03 11:56:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 42. [表單設計師]Q00-20230216003 新增表單設計師中的元件代號與元件名稱不支持 _lbl、_txt、_txt1、_txt2
- **Commit ID**: `a8dcdf9c5dbe2a3c95ecb7c99bb43e1b580bb909`
- **作者**: 謝閔皓
- **日期**: 2023-02-16 18:48:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`

### 43. [WEB] V00-20230204001 更改經常選取對象視窗選取邏輯，每次比對時都先取最新的值。
- **Commit ID**: `3415bd3c79ed211a336903182acc5e6e9371168a`
- **作者**: raven.917
- **日期**: 2023-02-13 17:07:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 44. [Web]Q00-20230222003 修正 TextBox 設定浮點數、顯示千分位和小數點後幾位時，與 Grid 繫結會導致 FromScript 取得 Grid 資料以及 FormInstance 的 FieldValues 會有千分位的問題
- **Commit ID**: `59988fc16a29b0a436e23aa46436a5cb33bdf034`
- **作者**: 謝閔皓
- **日期**: 2023-02-22 17:10:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 45. [Web]Q00-20230218001 調整讓 Grid 支援使用 <div>
- **Commit ID**: `a70a708eaed8b9428b0d201d226e3880cc7655be`
- **作者**: 謝閔皓
- **日期**: 2023-02-18 13:36:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 46. [E10]Q00-20230217002 修正子單身在Table模式下展開內容會無法完全顯示
- **Commit ID**: `1ddecb7d6e2842bdfb302a1904e656e7c6846a01`
- **作者**: 林致帆
- **日期**: 2023-02-17 11:33:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 47. [WEB]Q00-20221216001調整腳本「更改Grid欄位寬度」的提示訊息。
- **Commit ID**: `9b062acb19287ec178f40f517db4734180477526`
- **作者**: raven.917
- **日期**: 2022-12-16 11:20:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 48. [WEB]Q00-20221101005 修正在表單上設定運算規則時有參考單身加總的元件時不會自動觸發更新的問題
- **Commit ID**: `26979fbf3da8e02638b30cf88ddcc9bf257864f0`
- **作者**: yamiyeh10
- **日期**: 2022-11-01 17:40:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 49. [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。(改)
- **Commit ID**: `54718d98d2a5034e92b47bb8373f0a5bfd2491a3`
- **作者**: raven.917
- **日期**: 2022-10-13 15:36:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 50. [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。
- **Commit ID**: `59041bc08e18b804729a73c85c37bf846793ab60`
- **作者**: raven.917
- **日期**: 2022-10-13 14:03:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 51. [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，找不到欄位ID時的Alert訊息
- **Commit ID**: `809e93e5f7c3c00a61b59b1f9d4f8505718a213e`
- **作者**: raven.917
- **日期**: 2022-10-13 11:06:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 52. [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，ID為null時的Alert訊息。
- **Commit ID**: `3c76b93097ef5c6f740d76f7cdf5997e55f8a8dc`
- **作者**: raven.917
- **日期**: 2022-10-12 11:33:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 53. [Web]Q00-20220621005 調整RWD Grid當單身資料太多時導致開啟表單時間較長
- **Commit ID**: `8cc2422e92f4c12458eacdc3f5e9e4bbf77c0798`
- **作者**: 王鵬程
- **日期**: 2022-06-21 15:19:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 54. [組織設計師]Q00-20221125002調整復職時，移除通知SQL寫法。
- **Commit ID**: `7d545535fa62bbfa436b325871e828b767a09e83`
- **作者**: raven.917
- **日期**: 2022-11-25 15:25:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 55. [T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容
- **Commit ID**: `ef0c03a48a270dae3c4d51ab13689b253d79afb1`
- **作者**: 林致帆
- **日期**: 2022-09-27 08:36:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`

### 56. [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功[補]
- **Commit ID**: `bee67a52716817c0db3b901e53fdf2af4df3bbce`
- **作者**: kmin
- **日期**: 2023-03-07 16:22:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 57. Revert "[ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊[補]"
- **Commit ID**: `e5652de30ec7fdf3799abb5297aa666b9c0b2c34`
- **作者**: kmin
- **日期**: 2023-03-07 16:22:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 58. [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊
- **Commit ID**: `fb6c5b59a02dbc017b26e5370f172a47d1082ccd`
- **作者**: 林致帆
- **日期**: 2023-03-06 15:18:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 59. [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊[補]
- **Commit ID**: `1e1656986bed729b59e3eb51a0d8f22f018edf89`
- **作者**: kmin
- **日期**: 2023-03-06 15:40:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 60. [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功
- **Commit ID**: `557768baa95b9699177d7c9cbada116ea581304e`
- **作者**: 林致帆
- **日期**: 2023-03-06 11:33:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 61. [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題[補]
- **Commit ID**: `31ea61e9d4c9fe832aaa8830f458745c588f25d0`
- **作者**: kmin
- **日期**: 2023-02-22 11:57:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 62. [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題
- **Commit ID**: `79336c5978d91288ec2893e853e222f6897f9d97`
- **作者**: cherryliao
- **日期**: 2023-02-21 16:30:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 63. [WEB]Q00-20230221002 修正在行動版的清單頁面上若主旨有<br>時無法正確換行問題
- **Commit ID**: `76e9d83a46a6ca72b950e264ffaca115cc5b3319`
- **作者**: yamiyeh10
- **日期**: 2023-02-21 15:44:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 64. [Web]Q00-20220810001 修正設定模擬使用者給一般人員，用模擬使用者模擬一般人員，會出現兩筆模擬使用者的作業
- **Commit ID**: `3b565d242d83e77197b7ea89f873596362058055`
- **作者**: 林致帆
- **日期**: 2022-08-10 15:47:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 65. [Web]Q00-20230215001 修正使用 https 且為 Safari 瀏覽器下載附件時，若檔名有中文會變成亂碼的問題
- **Commit ID**: `1451088e59554f3981d4c6179b1335cd2fa7f4a3`
- **作者**: 謝閔皓
- **日期**: 2023-02-15 08:53:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 66. [WEB] V00-20221019001 修正監控流程設置「已撤銷」，無法匯出Excel
- **Commit ID**: `a4bb0b0fcc4e50fd248a7e8659701c02c8b323ca`
- **作者**: raven.917
- **日期**: 2023-02-08 20:06:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 67. [Web]V00-20221019001修正流程管理/監控流程 選擇「已撤銷」流程，匯出Excel發現多了「執行中的關卡」跟「目前處理者」的欄位。
- **Commit ID**: `c72cfd3422b82f396c816f9dc17f5406bc8968e0`
- **作者**: raven.917
- **日期**: 2022-10-25 15:18:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 68. [內部]V00-20221012008修正流程管理/監控流程 選擇「已關閉」流程，匯出Excel發現多了簽核時間的欄。
- **Commit ID**: `c941ded8f51780a71f4fa0ed490b216109f25131`
- **作者**: raven.917
- **日期**: 2022-10-14 09:17:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 69. [Web]Q00-20220907003 修正TIPTOP附件無法下載
- **Commit ID**: `87d323a900391004cedbfc521c2195f3d93c8562`
- **作者**: 林致帆
- **日期**: 2022-09-07 17:15:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 70. [Web]A00-20230204001 修正追蹤流程中，查看已轉派工作且已處理的單據，點擊回到工作清單卻呈現工作通知清單的問題
- **Commit ID**: `bf59a0befd2f2709931abfcb963c1f867f2a7868`
- **作者**: 謝閔皓
- **日期**: 2023-02-07 13:15:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 71. [WEB] Q00-20230112001 修正T100拋單附件為URL時，不會計算點按次數。
- **Commit ID**: `9efa9befd21a8542c29c6aaa393b23e4ea7c36e6`
- **作者**: kmin
- **日期**: 2023-01-13 14:07:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 72. [流程設計師]Q00-20221103003 修正流程定義/事件處理/流程完成/網頁應用程式，第一次點擊編輯時畫面空白的問題
- **Commit ID**: `1921679cde8c7f707f9989196d34c0a9f757ea96`
- **作者**: 謝閔皓
- **日期**: 2022-11-03 18:09:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/FormalParametersCellEditorRenderer.java`

### 73. [內部]Q00-20220920002 T100傳附件用http方式且未帶drivetoken的tag內容，增加log訊息提示修正T100
- **Commit ID**: `6d5ce6bab5f4ca3ace5daa637d51054774010083`
- **作者**: 林致帆
- **日期**: 2022-09-20 14:39:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 74. [T100]Q00-20220809005 修正T100拋單，附件為從文檔中心取得的，檔案大小與實際大小不符合
- **Commit ID**: `6a3bfab149cf376a8481da8ae51077d30230bd22`
- **作者**: 林致帆
- **日期**: 2022-08-09 15:40:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 75. [WEB]Q00-20230104006修正列印模式下，絕對位置表單RadioButton顯示異常
- **Commit ID**: `52ce82d32aa3b251c1170c1c8e2a53c4aad77121`
- **作者**: raven.917
- **日期**: 2023-01-04 23:48:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 76. [WEB]Q00-20230104002 修正RWD表單在TextBox元件調整字體大小後使用iOS手機查看時欄位中的字不能完整呈現
- **Commit ID**: `ebddf91c71843cbcc2703b12f0baf9d19d9c8198`
- **作者**: yamiyeh10
- **日期**: 2023-01-04 11:18:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 77. [Web]Q00-20221202002 修正 Firefox 瀏覽器開啟絕對位置表單，使用列印表單的功能，畫面顯示異常的問題
- **Commit ID**: `3539c27c8f951788cf21675d1938887c9cb94b10`
- **作者**: 謝閔皓
- **日期**: 2022-12-02 16:08:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 78. Merge 112/1/14 PerformWorkItemHandlerBean.java ProcessDispatcherBean.java 移除： [流程引擎]S00-20220516002 在地化方案支援集成第三台流程主機 [流程引擎]Q00-20221209002 T100拋單若第一關與第二關的建立時間相同，自動簽核選擇與前一關相同簽核者就會無效 by kmin.
- **Commit ID**: `8c62a105416fd7ddd722fbc90200c1433129f47f`
- **作者**: kmin
- **日期**: 2023-01-04 16:37:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 79. Revert "112/1/4 Merge"
- **Commit ID**: `f3ac8f93d9414eb0b2946da70960eae178417dd0`
- **作者**: kmin
- **日期**: 2023-01-04 16:22:49
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/system/src/com/dsc/nana/util/comparator/WorkItemTimeComparator.java`

### 80. 112/1/4 Merge
- **Commit ID**: `d662acce0d881e15946dd8eb66d98b6664c33317`
- **作者**: waynechang
- **日期**: 2022-10-25 17:29:45
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/comparator/WorkItemTimeComparator.java`

### 81. [流程引擎]Q00-20220915001 修正簡易流程圖若流程有設計迴圈型且線的條件剛好為兩個Gateway互為下一關時，加入防呆避免系統Crash
- **Commit ID**: `5a816fa6fcdc1456b3649f10f177e30b6aa38fdb`
- **作者**: waynechang
- **日期**: 2022-09-15 17:24:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 82. [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。
- **Commit ID**: `b63a286731c72e7db015e1729d52a5f36f7c3f1c`
- **作者**: wencheng1208
- **日期**: 2022-07-29 16:30:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 83. PerformWorkItemHandlerBean 回歸5883標準，但移除[流程引擎]S00-20220516002 在地化方案支援集成第三台流程主機
- **Commit ID**: `8e467ee12ddc9b339fde7dcaf3814d243ca4c9d8`
- **作者**: kmin
- **日期**: 2023-01-04 15:36:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 84. [流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送
- **Commit ID**: `311ad7a03ab239683e875cd3af0cb05744d02b4c`
- **作者**: waynechang
- **日期**: 2022-07-21 15:36:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 85. [流程引擎]Q00-20220713005 修正核決關卡設定自動簽核，取/退回後再次簽核進核決層級時會報錯無法派送
- **Commit ID**: `c4b8ee02e1d7cf0c798a3f1a89d4234a879499c1`
- **作者**: walter_wu
- **日期**: 2022-07-14 11:32:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 86. [內部]修正使用IDEA開啟ProcessDispatcherBean時，Java Doc出現Identifier expected
- **Commit ID**: `79f8d23a1aa9c53f3803272521232ff7dce7303a`
- **作者**: lorenchang
- **日期**: 2022-07-04 12:16:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 87. ProcessDispatcherBean回歸5881標準 by kmin.
- **Commit ID**: `92010ae7f5d807488cace25fb39c363388b03691`
- **作者**: kmin
- **日期**: 2023-01-04 15:23:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 88. [Web]Q00-20230103002 修正系統設定使用 LDAP 驗證，若使用者沒有設定 LDAP 驗證，從通知信連結進入 BPM 登入頁時，帳號欄位沒有自動帶入 UserId 的問題
- **Commit ID**: `d75edd74a0114ec1ad47c4a2dfd4c83c7484eb2e`
- **作者**: 謝閔皓
- **日期**: 2023-01-03 18:17:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 89. [WEB]Q00-20221223001 流程資料查詢頁面無法下載附件
- **Commit ID**: `5868ad1a6f5e298c40f763070e6673ddc61b6428`
- **作者**: raven.917
- **日期**: 2022-12-23 10:28:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 90. [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現 [補修正] 調整上傳附件後出現一堆隱藏的div框導致畫面不一致 調整Title,Qrcode元件上傳附件以及列印在隱藏狀態時會出現
- **Commit ID**: `16a3b38c00b49939e025b75e7d022b10763c057e`
- **作者**: kmin
- **日期**: 2022-12-19 16:33:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 91. [T100]Q00-20221217001 修正 T100 整合表單，退件表單資訊的放大鏡按鈕無法開啟
- **Commit ID**: `96bbd50e96830c520cb63fa713640d0fe19d88c5`
- **作者**: 謝閔皓
- **日期**: 2022-12-17 13:50:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TiptopAccessor.java`

### 92. [WorkFlow]]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能
- **Commit ID**: `0417c14464a038588b4a25d57380c3463befd54a`
- **作者**: 林致帆
- **日期**: 2022-10-14 17:38:20
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 93. [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現
- **Commit ID**: `500bdb644abee06c47038bc3687a4247cc27ee2c`
- **作者**: kmin
- **日期**: 2022-12-15 14:39:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 94. [Web]S00-20220510001新增運算規則可以選取到hidden元件。
- **Commit ID**: `2f026c905bfaa2e1f56b805bd1b2b08166619693`
- **作者**: raven.917
- **日期**: 2022-10-26 16:01:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 95. [流程引擎]Q00-20221212001 調整ajax_ProcessAccessor.findFieldValueById取得表單內容方法，當流程同時掛載多表單時，偶發回傳找不到該欄位內容的錯誤
- **Commit ID**: `4c8fa77d08ab7948696a26fc2bcbb9646ffcb009`
- **作者**: waynechang
- **日期**: 2022-12-12 13:52:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 96. [WEB]Q00-20221208003 修正加簽關卡不會自動帶入簽核意見且要相容自訂關卡後會被清除簽核意見問題。(補修正)
- **Commit ID**: `4b2fbd0df71e9ad4c53adbb1fe63b303bcd8fb57`
- **作者**: raven.917
- **日期**: 2022-12-08 17:00:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 97. [WEB]Q00-20221208003 修正加簽關卡不會自動帶入簽核意見。
- **Commit ID**: `2b85b7c56625db144727cecc20af380b9e935cdf`
- **作者**: raven.917
- **日期**: 2022-12-08 16:31:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 98. [流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
- **Commit ID**: `5048f2481fd5b50a1caa963ee9e4a06525e13e7c`
- **作者**: waynechang
- **日期**: 2022-11-02 18:07:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 99. Revert "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。 另外還有發現： 1. 活動逾時排程動作執行之後，後續待辦關卡也不會寄送通知信，已修正。 2. 用系統管理員從流程圖檢視上的活動關卡頁面直接執行跳過，後續待辦關卡也不會寄送通知信，已修正。"
- **Commit ID**: `5501680a8817a0c178d9e182d23a3fe7dc61499b`
- **作者**: kmin
- **日期**: 2022-12-07 08:56:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 100. [Web]Q00-20221114005絕對定位表單及RWD表單，統一可設定背景色設定。
- **Commit ID**: `fbdd6756914a10e5cbbe5d9e67af985468cb9568`
- **作者**: kmin
- **日期**: 2022-11-30 16:08:32
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/LinkElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 101. [Web]Q00-20221114002修正表單設計師Barcode元件異常問題。
- **Commit ID**: `05f79e11e3ad223ba29c79037ff30e4d4b80b1db`
- **作者**: kmin
- **日期**: 2022-11-30 15:42:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 102. [BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題 1. 換行符號做轉換表單:行動版絕對位置、行動版相對位置 2. 樣式調整:絕對位置、相對位置與響應式表單的Grid摘要以原本空一格方式呈現, 下方內容有換行符號時則呈現換行
- **Commit ID**: `1186d3a08a6bca8a0c0deeb2022576380a6f9580`
- **作者**: kmin
- **日期**: 2022-11-30 15:41:32
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 103. [流程引擎]Q00-20221129001修正修正nchar欄位型態錯誤比對問題，導致轉存表單存空值。
- **Commit ID**: `90945229c459ae3fa7428b77cd1613033433cf73`
- **作者**: raven.917
- **日期**: 2022-11-29 15:12:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 104. [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)
- **Commit ID**: `a64c040d1285514b035fdfda83558f6c435e87c3`
- **作者**: raven.917
- **日期**: 2022-09-20 18:09:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 105. [WEB]Q00-20221128006調整絕對定位表單RadioButton顏色更清楚。
- **Commit ID**: `4619e4076f85700390a41e223ac33d85bbb97029`
- **作者**: kmin
- **日期**: 2022-11-30 11:28:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 106. [內部]Q00-20221124005 調整downloadImage的URL服務的ContentType為png
- **Commit ID**: `b057d8718295018faae56d140ab58d440eec3f17`
- **作者**: waynechang
- **日期**: 2022-11-24 16:24:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 107. [流程引擎]Q00-20221128001 調整系統排程設定在新增、編輯、刪除後檢查所有排程的首次執行時間若小於當前時間時更新首次執行時間避免觸發即時執行排程機制
- **Commit ID**: `f63cea4e989445042eb88c39a15ee6a60cf60601`
- **作者**: yamiyeh10
- **日期**: 2022-11-28 12:12:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/schedule/TimerFacadeBean.java`

### 108. [Web]Q00-20221116003 修正 Checkbox、RadioButton 元件，若文字過多造成換行時，勾選按鈕會有偏移的問題
- **Commit ID**: `9422dc4cae0369ac78e33335e84b44897b05bc8e`
- **作者**: kmin
- **日期**: 2022-11-21 15:04:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 109. [流程引擎]Q00-20221108003 修正流程引擎的加簽函式功能「addCustomParallelAndSerialActivity」，加簽出來的關卡的表單未依照「參考關卡」呈現對應的「表單元件顯示」狀態
- **Commit ID**: `2a9366d52a5f51ff42fcf1aa560791a8a653f411`
- **作者**: waynechang
- **日期**: 2022-11-08 16:22:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 110. Revert "[流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信"
- **Commit ID**: `49448bd5f1c3e47ea5d3bb412255acd31eef64e6`
- **作者**: kmin
- **日期**: 2022-11-15 10:11:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 111. [流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信
- **Commit ID**: `15484403d8a942a72f31ff1f25410e769bebc059`
- **作者**: 林致帆
- **日期**: 2022-11-03 17:41:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 112. [在線閱覽] Q00-20221111002 修正追蹤流程重發新流程，當第一關關卡有設定上傳附件不使用在線閱覽時，上傳附件仍會出現在線閱覽的選項
- **Commit ID**: `5ebdf54b181ff6f1e265932c8b150be764696700`
- **作者**: waynechang
- **日期**: 2022-11-11 15:00:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 113. [Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息
- **Commit ID**: `d649c2be8768043307fb18082c892001269b7c00`
- **作者**: cherryliao
- **日期**: 2022-11-11 11:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 114. [Web]Q00-20221108001修正輸入元件設置必填後，沒勾選隱藏標籤原label標籤會出現undefined
- **Commit ID**: `48a56b2b1d76d81bc3e02faea28809aae8dd2cd9`
- **作者**: raven.917
- **日期**: 2022-11-08 15:25:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 115. [Web]Q00-20221020004 修正 TextBox 元件進階功能的運算規則，若將已綁定的欄位值輸入後又刪除，會顯示 NaN 的問題
- **Commit ID**: `80f6096857ca87a5aa4f7a8b91c8f7caa3546c81`
- **作者**: 謝閔皓
- **日期**: 2022-10-26 09:06:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 116. [Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。
- **Commit ID**: `f90f5bfcbd40073a47bea239a4e8ebe105a5015b`
- **作者**: kmin
- **日期**: 2022-11-04 15:30:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 117. [Web]S00-20220711001Textbox元件設置整數及浮點數自動進位輸入值。
- **Commit ID**: `061cffea35b0a05887970107568d16ec0b4300a7`
- **作者**: raven.917
- **日期**: 2022-10-25 15:06:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 118. [Web]A00-20220811001 修正表單若TextBox元件設定浮點數且顯示實際值時會有偏移值問題
- **Commit ID**: `a5024d288920b6bffe429668f42a79ced21aca8c`
- **作者**: yamiyeh10
- **日期**: 2022-08-16 14:49:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 119. [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(修)
- **Commit ID**: `59b5ee12781bd224b3cc62c8aa8fae4d16785107`
- **作者**: raven.917
- **日期**: 2022-10-11 09:20:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 120. [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)
- **Commit ID**: `c1fa80f5d98c3c7aa633a4f35d4ba66ef5883003`
- **作者**: raven.917
- **日期**: 2022-09-21 10:40:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 121. [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。
- **Commit ID**: `6f45ea4537a8749711551d258b967f15fbf107ba`
- **作者**: raven.917
- **日期**: 2022-09-20 16:57:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 122. [表單設計師]S00-20220707005系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷。
- **Commit ID**: `732fec14fc760aa02f65a1863364ded5ada86d96`
- **作者**: raven.917
- **日期**: 2022-09-16 14:46:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 123. [內部]Q00-20221104002 調整觸發自動簽核時間點的log
- **Commit ID**: `55672cc826553ce717b80c4ae63d84529e66994c`
- **作者**: waynechang
- **日期**: 2022-11-04 11:40:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`

### 124. [WEB]Q00-20221103001 使用者撤銷流程，理由填空白字串時不允許撤銷流程
- **Commit ID**: `e090070f804d55e24f93005a0c50ca42f82d5771`
- **作者**: yamiyeh10
- **日期**: 2022-11-03 11:24:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`

### 125. [Web]Q00-20221102001 修正checkbox設計時，若有勾選「最後一個選項額外產生輸入框」，表單中checkbox呈現與列印不一致的問題
- **Commit ID**: `7f37cb92c8e495e546038b123ab40a948528dbe2`
- **作者**: 謝閔皓
- **日期**: 2022-11-02 14:53:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 126. [Web]Q00-20221101003 修正使用者若有離職作業維護，點選離職人員會跳到登入畫面的問題
- **Commit ID**: `85f482df8b0a1855f4ece992db3887562eeb2d32`
- **作者**: 謝閔皓
- **日期**: 2022-11-01 15:24:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`

### 127. [Web]Q00-20220805002 調整log訊息，當流程向後派送，後面關卡解析的使用者找不到或是沒有主部門時，增加log訊息
- **Commit ID**: `13d4819a597fa2a1ab2cdf7bbfa3b6b125a06b66`
- **作者**: kmin
- **日期**: 2022-11-01 10:39:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 128. [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。 另外還有發現： 1. 活動逾時排程動作執行之後，後續待辦關卡也不會寄送通知信，已修正。 2. 用系統管理員從流程圖檢視上的活動關卡頁面直接執行跳過，後續待辦關卡也不會寄送通知信，已修正。
- **Commit ID**: `c967dbf7d5cd913f9b91316eb4611e9bb9d55e64`
- **作者**: kmin
- **日期**: 2022-11-01 10:29:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 129. [流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
- **Commit ID**: `5594617131faff518be1fabb69ad4e535104bba0`
- **作者**: waynechang
- **日期**: 2022-10-31 17:41:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 130. [流程引擎]Q00-20221031001 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常
- **Commit ID**: `5136d831349ee787285632dbd0877ec77f86f5f2`
- **作者**: waynechang
- **日期**: 2022-10-31 16:23:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 131. [WEB]A00-20221027001修正新增關卡內-經常選取對象無法第二次選取進清單。(補修正)
- **Commit ID**: `5ce9c1e5e5ad4ac13ae4a5d7908ba854e028535f`
- **作者**: raven.917
- **日期**: 2022-10-28 15:59:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 132. [WEB]A00-20221027001修正新增關卡內-經常選取對象無法第二次選取進清單。
- **Commit ID**: `748f78528bb3770a0bae4ddb36e0200ebf97c700`
- **作者**: raven.917
- **日期**: 2022-10-28 15:20:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 133. [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
- **Commit ID**: `26b285519ef5414738e0c011e17be4908476e1b3`
- **作者**: 謝閔皓
- **日期**: 2022-10-18 10:33:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`

### 134. [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
- **Commit ID**: `370cdac63abe130baa5e50ebae3bfb0fd88ab31b`
- **作者**: 謝閔皓
- **日期**: 2022-10-06 13:39:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`

### 135. [WEB]Q00-20221014003修正變更經常選取對象 & 變更您的關係人，更新資料後沒有即時刷新頁面問題。
- **Commit ID**: `125743ce6c46840818b87ea92dfaef71bb53845e`
- **作者**: raven.917
- **日期**: 2022-10-17 10:37:56
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 136. [Portal]]Q00-20220817001調整有整合Portal，用查看流程圖的外部portlet，導入的畫面不是BPM而是Portal的登入頁面
- **Commit ID**: `acf5091dbd10e08f5df901b172cb35a241e75b77`
- **作者**: 林致帆
- **日期**: 2022-08-17 11:58:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 137. [流程設計師]Q00-20221006001 調整在流程設計點擊編輯表單欄位權限時，若表單發行狀態已過期或UNDER_REVISION時會彈提示訊息
- **Commit ID**: `0452686311abe9c5af39cdc698fb834f480e75a6`
- **作者**: cherryliao
- **日期**: 2022-10-06 11:29:53
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`

### 138. [Web]Q00-20220930001 執行iReport套件當發生Exception錯誤時，增加列印異常的堆疊資訊
- **Commit ID**: `72fed5ba073dac7afdcf4e941acaf2711fbf9dec`
- **作者**: wencheng1208
- **日期**: 2022-09-30 10:36:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`

### 139. [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題[補修正]
- **Commit ID**: `bed986e5897f52ebb2307d1ceec8c875e1dfd180`
- **作者**: 謝閔皓
- **日期**: 2022-09-07 17:08:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 140. [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題
- **Commit ID**: `164824c8b781dfba73cf4daf8921d354bbd01c8f`
- **作者**: 謝閔皓
- **日期**: 2022-09-07 14:59:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 141. [WEB]Q00-20221013002:修正表單欄位有設定 "唯讀"時的欄位顏色，顯示卻都為背景顏色。
- **Commit ID**: `696523b4daf9f21112a21d2640079e84887dabdd`
- **作者**: kmin
- **日期**: 2022-10-19 11:27:34
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 142. [流程設計師]A00-20221012001 修正流程設計師當子流程有變更代號時，流程簽入新版時，資料庫的子流程代號未更新
- **Commit ID**: `15afabe6fb342b2994b742df4993c5d77135714f`
- **作者**: waynechang
- **日期**: 2022-10-14 14:29:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/SubflowActivityMCERTableModel.java`

### 143. [TIPTOP]Q00-20221014007修正客戶從TIPTOP端udm_tree操作原稿匣撤銷流程時，選擇特定流程後，BPM仍會回傳所有可撤銷流程的清單
- **Commit ID**: `2e47a6b0e278d3f512ad1973e64c74a3fbdeb969`
- **作者**: 謝閔皓
- **日期**: 2022-10-15 11:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 144. [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。
- **Commit ID**: `210bff96f039a88aa6ba05ece73914aa75b538bc`
- **作者**: raven.917
- **日期**: 2022-10-06 14:35:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 145. [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用(補修正，增加可讀性)
- **Commit ID**: `ede8913d9862ba32527d0c6fc94621df89b191e3`
- **作者**: raven.917
- **日期**: 2022-10-06 08:57:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 146. [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
- **Commit ID**: `20d1e0cc6495b417056f0232f2bb5f8bf038c3e3`
- **作者**: lorenchang
- **日期**: 2022-07-06 17:40:48
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AbsSSOHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_Oracle.sql`

### 147. 移除[ESS]S00-20211208003新增ESS外網主機IP設定
- **Commit ID**: `7ac9732b65674818da20c055bb7d1630580f629c`
- **作者**: kmin
- **日期**: 2022-10-05 15:13:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`

### 148. Revert "[ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)"
- **Commit ID**: `2f3b844086e50efbcf96c4f9962911c218a551eb`
- **作者**: kmin
- **日期**: 2022-10-05 15:04:14
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AbsSSOHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_MSSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_Oracle.sql`

### 149. [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
- **Commit ID**: `6e1be2dfc456cd57a13529f8bf3ecc5c42ab3072`
- **作者**: lorenchang
- **日期**: 2022-07-06 17:40:48
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AbsSSOHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_Oracle.sql`

### 150. [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用
- **Commit ID**: `31c0c3bf260a1e1eb7593246b6a360c8db2de709`
- **作者**: raven.917
- **日期**: 2022-10-04 15:26:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 151. [流程引擎]Q00-20221003002 流程預先解析支持流程設計關卡型態為「活動簽核人」的活動
- **Commit ID**: `e8a366350092080c23c68e63cbd26bfa348d4433`
- **作者**: waynechang
- **日期**: 2022-10-03 13:59:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 152. [流程設計師]Q00-20221003001 調整簽核流設計師，將流程設計師原有的「活動定義/選擇參與者/活動簽核人」重新加回簽核流設計師中
- **Commit ID**: `f60512b795b04b346c7b25ec493af61c895cf8b3`
- **作者**: waynechang
- **日期**: 2022-10-03 10:27:09
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/BpmUserTaskInfoAcquirer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/BpmUserTaskEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskChooserController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskInfoPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/ProcessRelationshipPanel.java`

### 153. Revert "[Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦"
- **Commit ID**: `1380460fb68c3f71a06b7e7fefc2eff600317934`
- **作者**: kmin
- **日期**: 2022-10-03 11:36:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 154. [Web]C01-20200701002 修正: 迴圈行流程無法於追蹤流程中執行取回重辦
- **Commit ID**: `8b0e42b0c9af521bfdffa8db6ce9e73e5d71f5c0`
- **作者**: yanann_chen
- **日期**: 2020-08-05 10:44:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 155. [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]
- **Commit ID**: `438ebc602a888fd6c729289577b66944965cc32e`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 15:47:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 156. [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員
- **Commit ID**: `b9a3046f9032af75d99e39a7f12d1233c4d49303`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 12:24:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java`

### 157. [ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核
- **Commit ID**: `d034999419e4b2dc274cdeb392cff2edcd950946`
- **作者**: 林致帆
- **日期**: 2022-09-27 11:50:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 158. [流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡
- **Commit ID**: `7b7ed70fbb4e42de5d577b6dcc666e30b09f9880`
- **作者**: waynechang
- **日期**: 2022-08-25 14:52:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 159. [Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊
- **Commit ID**: `26483e5cc05a89246bd80aa335020f1fcb655118`
- **作者**: waynechang
- **日期**: 2022-09-20 11:19:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 160. [WEB] C01-20220919007 Admin 需要能開啟設計師時，直接針對該表單做復原簽出的操作行為。
- **Commit ID**: `9b6b59cd9aefca013eee956b0e2329f159811990`
- **作者**: raven.917
- **日期**: 2022-09-21 16:42:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefNodeState.java`

### 161. [Web]Q00-20220921002 調整「必須上傳新附件」邏輯，只要存在一筆以上的附件並且符合在該「關卡名稱」上傳的附件，即可通過該驗證
- **Commit ID**: `baa126f6709fa169ae3d3e8a59d0e6669a31787a`
- **作者**: wencheng1208
- **日期**: 2022-09-21 17:49:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 162. [Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題
- **Commit ID**: `cb3838615f63e8c71254a816520898e1570710dc`
- **作者**: 謝閔皓
- **日期**: 2022-08-08 14:57:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 163. [Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容
- **Commit ID**: `61f938322cfb5f4e24f7e700bb85cf6632a236d2`
- **作者**: 謝閔皓
- **日期**: 2022-08-04 22:21:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 164. [Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題
- **Commit ID**: `30c4edbfb6045c6621c1a283abfc45d103610bdd`
- **作者**: cherryliao
- **日期**: 2022-09-16 13:48:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 165. [Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式
- **Commit ID**: `28beda36762e0b60d7c33c1b62501d52c87e6d76`
- **作者**: wencheng1208
- **日期**: 2022-09-01 15:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 166. [流程引擎]Q00-20220912004 修正findProcessPackageById方法內容為取得流程包裹最新一版，以避免後續同仁遇到此坑
- **Commit ID**: `b912eefbdb5bd4bd4af04a495507c422236324bf`
- **作者**: wencheng1208
- **日期**: 2022-09-12 17:59:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 167. [組織同步]QQ00-20220912003 組織同步功能執行人員資料修改時，可保留人員姓名多語系關聯
- **Commit ID**: `cfb2716e35ea897eae91da2922c3dd0fd85fbcba`
- **作者**: wencheng1208
- **日期**: 2022-09-12 17:39:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 168. [Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本
- **Commit ID**: `ee321aa933c1286b752287cb2d6f1f97ce203643`
- **作者**: wencheng1208
- **日期**: 2022-09-08 15:12:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java`

### 169. [Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息
- **Commit ID**: `bfb2c7fe5e2a371a9ec214a00bf24bbc123bab13`
- **作者**: cherryliao
- **日期**: 2022-09-08 14:06:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 170. [Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部 原單號: C01-20220527001 個人資訊>流程相關設定>工作事項顯示設定 ，當點選關注流程應該進入待辦中的關鍵流程、點選重要流程應該進入待辦的重要流程
- **Commit ID**: `90b2bd43bedceddfa8b11a93e1313a1b8b76c37f`
- **作者**: kmin
- **日期**: 2022-09-19 13:54:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 171. [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
- **Commit ID**: `3aaa7d1b5065a47cb1842d944f66c544655f34bb`
- **作者**: kmin
- **日期**: 2022-09-15 11:22:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 172. Revert "[流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯"
- **Commit ID**: `6c4fa426bdbd722a183655f4fcbde85f20cee410`
- **作者**: kmin
- **日期**: 2022-09-15 10:33:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 173. [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
- **Commit ID**: `3c49dd5e00f6bc612c57f8f4cd4aec9b84c90ae6`
- **作者**: wencheng1208
- **日期**: 2022-09-14 10:34:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 174. [Web]S00-20220810001簽核意見是否顯示管理員
- **Commit ID**: `bee3b10e5f174c0d0dc9e6043db147d444449f2c`
- **作者**: kmin
- **日期**: 2022-09-15 09:47:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java`

### 175. [流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤
- **Commit ID**: `f488c06a3327d8f2ef959bc4a04d75b451055d93`
- **作者**: kmin
- **日期**: 2022-09-15 09:19:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/OrganizationUnit.java`

### 176. [流程引擎]Q00-20220627001 優化核決層級關卡解析人員緩慢問題
- **Commit ID**: `f9412cc4aa0afcb0e4000affefd3c189fed196af`
- **作者**: kmin
- **日期**: 2022-09-15 09:16:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/OrganizationUnit.java`

### 177. [流程引擎]Q00-20220823003 讓亂數產生的ID增加動態加簽CustomDecisionRule的開頭關鍵字，前面流程圖解析邏輯段也要新增
- **Commit ID**: `02f2996cb7e1dae2d5824b1745452163ebaad168`
- **作者**: kmin
- **日期**: 2022-09-06 15:55:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 178. [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
- **Commit ID**: `cea4ee7a807c2fc04e0cffda211e16cdd57e4293`
- **作者**: kmin
- **日期**: 2022-09-06 15:52:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 179. [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
- **Commit ID**: `c9834d376f751eb5088b5bb5315f288cc99f17bb`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:48:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 180. [WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能
- **Commit ID**: `3e751eefed0cc33cb84390f6349df340fde5b09a`
- **作者**: 林致帆
- **日期**: 2022-08-30 08:34:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 181. [流程引擎]S00-20220722001新增批次通知信件主旨內容
- **Commit ID**: `4e8c9a768beb91736c51c84cc09980bf939390b7`
- **作者**: 謝閔皓
- **日期**: 2022-08-23 15:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 182. Revert "[流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容"
- **Commit ID**: `8796d926d1d1d5836d4e50fbaed104775a723a1b`
- **作者**: kmin
- **日期**: 2022-08-29 10:11:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 183. [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
- **Commit ID**: `41972a69f72a677308e184ec90bb730b574fb2e7`
- **作者**: wencheng1208
- **日期**: 2022-08-24 11:48:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 184. [流程引擎]Q00-20220823001 修正使用客製的方式執行動態加簽後，無法呈現詳細流程圖畫面的問題
- **Commit ID**: `b4ce57407a210883dd7b8f09be168a285d98b856`
- **作者**: wencheng1208
- **日期**: 2022-08-23 14:44:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java`

### 185. [Web]Q00-20220822001 修正BPM使用IE瀏覽器上傳附件時會失敗
- **Commit ID**: `db915813d2d9a1eaca33c063d14ca325dbc93f3a`
- **作者**: 林致帆
- **日期**: 2022-08-22 14:11:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

### 186. [TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久
- **Commit ID**: `05bc8adab61fad4cb327ed87e1a7671961495eac`
- **作者**: 林致帆
- **日期**: 2022-08-19 17:57:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 187. [Web]Q00-20220808003修正使用產品表單中的Date元件，並搭配TextBox元件的進階功能，資料型態整數中的時間區間運算，當遇到元件ID有使用下底線時，會導致TextBox元件無法正常運算
- **Commit ID**: `7b928a6aee56db4dd73ba59714b63c2122da7f38`
- **作者**: 謝閔皓
- **日期**: 2022-08-08 17:43:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 188. [Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題
- **Commit ID**: `6e1babac34a9a0d5cd655bb880c2eb27c2b12367`
- **作者**: 謝閔皓
- **日期**: 2022-08-11 12:56:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 189. [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
- **Commit ID**: `ae5d12df75fe9bbcf6e11cdf3e7ba82239786ef7`
- **作者**: 謝閔皓
- **日期**: 2022-08-10 18:34:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 190. [Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示
- **Commit ID**: `264b574094638268c0467e857b387449c8ec32fd`
- **作者**: 王鵬程
- **日期**: 2022-07-29 16:49:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 191. [Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行
- **Commit ID**: `a6d1eafd6fa1f3a20a3893bb5a01ed8f1e1f86f6`
- **作者**: 王鵬程
- **日期**: 2022-07-28 17:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 192. [Web]A00-20220808001 調整報表查詢產出的日期與匯出Excel的日期不一致問題
- **Commit ID**: `cae9a1fb40764a1fe0e24082ab4525bbcf6aaaf0`
- **作者**: cherryliao
- **日期**: 2022-08-10 11:01:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/ChartQueryTemplate.js`

### 193. [組織同步]Q00-20220809002 修正組織同步log出現Error時改寄送失敗通知信
- **Commit ID**: `4d2114c868126858174040d219327445dcee2256`
- **作者**: yamiyeh10
- **日期**: 2022-08-09 13:51:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`

### 194. [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]
- **Commit ID**: `2458402fdcc11baed9f733ede602245303cb7697`
- **作者**: wencheng1208
- **日期**: 2022-08-05 12:00:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 195. [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。
- **Commit ID**: `80cbadca5d7bbfde80b789e5a4d160ddfa43edca`
- **作者**: wencheng1208
- **日期**: 2022-08-03 16:48:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 196. [Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料
- **Commit ID**: `584139301094ff96ec3c3bd7cad16444a99a2403`
- **作者**: 謝閔皓
- **日期**: 2022-08-01 16:26:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 197. [內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來
- **Commit ID**: `a3e333f4419f52d74bd246e466496d4e5c6b1f6b`
- **作者**: kmin
- **日期**: 2022-07-27 14:37:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/persistence/src/com/dsc/nana/persistence/JpaService.java`

### 198. [Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入
- **Commit ID**: `1b5f5da3109bfdb03891ac382a6b4830403f91fc`
- **作者**: 王鵬程
- **日期**: 2022-07-26 18:16:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 199. [Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法
- **Commit ID**: `9887b73a36466c5f1d5b156971c2fa3b63cd2ce1`
- **作者**: wencheng1208
- **日期**: 2022-07-25 10:55:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 200. [Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題
- **Commit ID**: `046f00f571ad53f4359472ce8b40aff7725353b3`
- **作者**: yamiyeh10
- **日期**: 2022-07-25 10:22:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 201. [流程引擎]Q00-20220629004 修正流程定義設定「流程撤銷時逐級通知」，在使用者撤銷流程後，只有撤銷流程當下進行中的關卡的處理者在系統通知清單內有該筆通知資料
- **Commit ID**: `09daf70e0131dc3dac6e57dfddd5743cd9434d01`
- **作者**: yanann_chen
- **日期**: 2022-07-01 15:05:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerLocal.java`

### 202. Revert "[流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。"
- **Commit ID**: `e69f03cab80ecb61cfe1304696b7799a3498afdf`
- **作者**: kmin
- **日期**: 2022-08-10 08:56:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 203. [Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失 RWD不會發生的原因是就算前端直是空的傳進來也是[] 移動防呆位置
- **Commit ID**: `da5b5c4220450b2b46cb6dd8b71d38d7657b021e`
- **作者**: kmin
- **日期**: 2022-08-03 16:04:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 204. [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。
- **Commit ID**: `c209cc1f152d305f4021c45adce456bdbc068b06`
- **作者**: wencheng1208
- **日期**: 2022-07-29 16:30:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 205. [流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送
- **Commit ID**: `678cbe117d658fd527a5e169087b8e501a670d5b`
- **作者**: waynechang
- **日期**: 2022-07-21 15:36:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 206. [流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料
- **Commit ID**: `a056660157cae186b4044ac1cdeaed29ddc34641`
- **作者**: kmin
- **日期**: 2022-08-03 15:53:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 207. Revert "[流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料"
- **Commit ID**: `756afc159b46123d50e00f9b17762758843086ea`
- **作者**: kmin
- **日期**: 2022-08-03 15:38:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 208. [流程引擎]Q00-20220629002 修正流程定義設定「取回重辦時逐級通知」或「退回重辦時逐級通知」，在使用者進行取回或退回等操作後，系統通知清單內沒有該筆通知資料
- **Commit ID**: `2c62d2a67f740dba4c4b9de5278842d290f305b5`
- **作者**: yanann_chen
- **日期**: 2022-06-29 14:31:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/notification/ProcessNotificationType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 209. [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]
- **Commit ID**: `a4b215c54e1cfea008defbe1ee0b564cf87595c1`
- **作者**: 林致帆
- **日期**: 2022-07-29 14:34:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 210. [Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題
- **Commit ID**: `754970492b7e6e5dd673fa321a62ed80f4de16cd`
- **作者**: cherryliao
- **日期**: 2022-07-27 17:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 211. [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]
- **Commit ID**: `5cbacd775df77172323d6129dbc1433953edef27`
- **作者**: cherryliao
- **日期**: 2022-07-28 14:39:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 212. [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。
- **Commit ID**: `e743ffd22d13e7c2ccdb92e89040325bf5f8609c`
- **作者**: wencheng1208
- **日期**: 2022-07-27 12:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 213. [Web]A00-20220802001 修正無法開啟SAP維護作業
- **Commit ID**: `c710bda537773a3d8b1fb37aebfcf355279c257c`
- **作者**: 林致帆
- **日期**: 2022-08-02 11:27:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 214. [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]
- **Commit ID**: `5caf081cb7708b9408fee161ab60057746a5832a`
- **作者**: 林致帆
- **日期**: 2022-07-29 14:02:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 215. [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
- **Commit ID**: `ca6439b52e14e25dd1f36a8cc1f0707d45d00df2`
- **作者**: 林致帆
- **日期**: 2022-07-28 15:20:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 216. Revert "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock"
- **Commit ID**: `b3023f7146ee8bdbe55d5fc64c9b987d9b3003d5`
- **作者**: kmin
- **日期**: 2022-08-01 16:38:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 217. [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
- **Commit ID**: `2b34ffdd46218c2bb2b3693738fc5e50439c00d9`
- **作者**: 林致帆
- **日期**: 2022-07-28 15:20:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 218. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]
- **Commit ID**: `c6484f3ab16b220889e23161032bda07d9b139c2`
- **作者**: walter_wu
- **日期**: 2022-07-29 14:20:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 219. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
- **Commit ID**: `6a34311627ddb878ac7890ff54129650d37c6dcb`
- **作者**: walter_wu
- **日期**: 2022-07-29 00:04:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 220. Revert "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況"
- **Commit ID**: `170861ad2b4d77efec6f1e396ec67cc5f902f34a`
- **作者**: kmin
- **日期**: 2022-08-01 13:51:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 221. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
- **Commit ID**: `b7ead7ae38f728c9e726e4e6b37fa13e441c3964`
- **作者**: walter_wu
- **日期**: 2022-07-29 00:04:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 222. Revert "[MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]"
- **Commit ID**: `8a24c043da8c40dcbd5078f6eb778ab0c01b9671`
- **作者**: kmin
- **日期**: 2022-08-01 13:50:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 223. [MPT]Q00-20220705002 修正點右上首頁內默認或其他首頁會出現沒有授權的錯誤頁面問題[補]
- **Commit ID**: `4fae0e3c2582cfa92d1a7a6203506a845567fb87`
- **作者**: pinchi_lin
- **日期**: 2022-07-12 19:29:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 224. [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法
- **Commit ID**: `bcd9da43d09d4664ca0a0f80a77c49583f2a5a47`
- **作者**: 林致帆
- **日期**: 2022-07-27 10:41:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 225. [內部]Log調整
- **Commit ID**: `b46a273694ad0a7d9c6f7c47b6b75dfe90b59c64`
- **作者**: lorenchang
- **日期**: 2022-07-04 08:58:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 226. [Web]Q00-20220720002 修正列印模式下附件與簽核歷程的右邊邊線會不見問題
- **Commit ID**: `685568764d2c4310fa42b5f4b2f591bdcfdc200a`
- **作者**: yamiyeh10
- **日期**: 2022-07-20 18:23:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`

### 227. Revert "[Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部"
- **Commit ID**: `992c075955e3b09290b0b18065d57f3477aca6d8`
- **作者**: kmin
- **日期**: 2022-07-25 17:12:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 228. [Web]Q00-20220720001 修正在工作事項顯示設定為不顯示工作事項視窗，在右上角的關注流程和重要流程icon點下後會進到待辦的全部
- **Commit ID**: `979c526610ee3c35a2bf43776edf8ed59927e040`
- **作者**: 王鵬程
- **日期**: 2022-07-20 15:26:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 229. [Web]A00-20220718001 修正Gird元件在某關卡隱藏時開啟表單會出現該物件沒有定義的問題
- **Commit ID**: `630b34ec1f5aa0c718d05b97d7dadfa068dc4f8f`
- **作者**: cherryliao
- **日期**: 2022-07-20 11:38:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 230. [登入]Q00-20220719002 修正DB為Oracle時，使用者登出登入紀錄作業中使用操作時間為查詢條件會查不到結果的問題
- **Commit ID**: `8a869815b15a74613153802bee16a9f13d438ce2`
- **作者**: cherryliao
- **日期**: 2022-07-19 18:01:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 231. [Web]Q00-20220714004 修正使用safari瀏覽器時，點選在線閱讀附件沒有反應
- **Commit ID**: `fa30564db8e130af2c0fd839e564c5c7ea0202f3`
- **作者**: yanann_chen
- **日期**: 2022-07-14 18:28:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 232. [Web]Q00-20220713003 修正在行動版面中，在表單內向下滑動時，右下角的浮動按鈕會隱藏而無法後續操作
- **Commit ID**: `c5a72b06c2a0b21af2089c49cf16a8ee04256618`
- **作者**: 王鵬程
- **日期**: 2022-07-13 16:33:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 233. [Web]Q00-20220714003 修正Dialog元件的txt屬性如果被FormScript或其他非預期方式刪除，在產生表單畫面時報錯 如果屬性被改成null或是遺失將其防呆為空字串 內部測試將元件隱藏一關或是連續兩關以上都無法重現，應該是客戶的Script有改到元件內容
- **Commit ID**: `e8e15006b589d43fe9b77150a07c11164481a7cc`
- **作者**: kmin
- **日期**: 2022-07-18 10:06:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 234. [流程引擎]Q00-20220713005 修正核決關卡設定自動簽核，取/退回後再次簽核進核決層級時會報錯無法派送
- **Commit ID**: `23a28bdefa5016de43ad7ba9f6bde2fa3e484682`
- **作者**: walter_wu
- **日期**: 2022-07-14 11:32:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 235. [Web]Q00-20220714002 FormUtil增加可設定表單scrollbar滾到某個位置的語法 語法: FormUtil.setScrollBarHeight("0");
- **Commit ID**: `a3a615817f444bcb70b1096a71bf3abd98bf5150`
- **作者**: kmin
- **日期**: 2022-07-15 08:37:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 236. [表單設計師]Q00-20220711002 修正絕對表單元件不存在某些屬性而取用該屬性導致無法開啟表單，增加防呆
- **Commit ID**: `c21f1f4d10ad95fdd39b3bd295bdaf0768ac5f19`
- **作者**: 王鵬程
- **日期**: 2022-07-11 15:22:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 237. [流程引擎]Q00-20220706005 修正資料庫為MSSQL，且流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
- **Commit ID**: `a5152db54620f4ecbc0aa77803a0f5eb7e875444`
- **作者**: yanann_chen
- **日期**: 2022-07-06 17:43:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 238. [Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久
- **Commit ID**: `9327480cbf619bcdde10a79f5aa2b1fa7da06eb5`
- **作者**: walter_wu
- **日期**: 2022-06-28 18:06:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 239. [流程引擎]Q00-20220622001 修正當RWD表單的RadioButton元件與CheckBox元件選項內容太長時會斷行 相關議題單：C01-20220613009。
- **Commit ID**: `b9d309c72dd36cc6448ac6ed7bb5ab0b904675dd`
- **作者**: kmin
- **日期**: 2022-07-14 17:29:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 240. [Web]Q00-20220621001 修正非CheckBox或RadioButton的選擇元件執行到額外輸入框邏輯導致出現非預期異常
- **Commit ID**: `92368e872c7d106b815248361a8fc8e934ef0786`
- **作者**: kmin
- **日期**: 2022-07-14 17:27:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 241. [Web]Q00-20220713004 修正移動消息訂閱管理頁面無法開啟
- **Commit ID**: `bdd4c6865b8c76cc3e3952aad15e2a8ebde55e7e`
- **作者**: yanann_chen
- **日期**: 2022-07-13 16:21:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`

### 242. [流程引擎]Q00-20220711003 修正在表單上將Excel匯入單身後，開窗畫面變成空白
- **Commit ID**: `a566e9feb6ac3307b6192f9603977d986fcc61c2`
- **作者**: yanann_chen
- **日期**: 2022-07-11 17:38:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`

### 243. [Web]Q00-20220701001 調整時間元件如果輸入不是數字直接換成00
- **Commit ID**: `91b05ba89cabea5201cbfc5faa894ec859f03e5a`
- **作者**: walter_wu
- **日期**: 2022-07-01 11:54:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 244. [資安]Q00-20220629001 修正/NaNaWeb/webservice/servlet/AxisServlet要加入為WebService白名單控管範圍
- **Commit ID**: `b9179954f3708f8d659b0986294af383bbe8ea8a`
- **作者**: 林致帆
- **日期**: 2022-06-29 11:34:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 245. [Web]A00-20220622002 修正流程新增關卡頁面輸入簽核意見在新增向前or向後關卡按下確定後，簽核意見內容被清除
- **Commit ID**: `e6d5832a2b9539fcf61afc755e9a4056598c44c2`
- **作者**: 林致帆
- **日期**: 2022-06-23 14:10:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 246. [Web]Q00-20220523001 修正同瀏覽器有二次登入時，登入頁「記住我」的功能會失效
- **Commit ID**: `bc1b44733c0006ee17a2c088dde79b29a1a831e5`
- **作者**: waynechang
- **日期**: 2022-06-22 15:47:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 247. [Web]A00-20220616003 修正SerialNumber元件的字體設25px以上，在流程中該元件的顯示會有部分被遮蔽到
- **Commit ID**: `6027984f5f20c23d39bf6d1cf12e20910800ab39`
- **作者**: 王鵬程
- **日期**: 2022-06-21 16:55:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 248. [Web]Q00-20220621003修正發起流程-查詢流程清單搜尋純數字的流程名稱會報錯
- **Commit ID**: `24b41f87171d72661bf64fd8f1441f30d1bae8dd`
- **作者**: 林致帆
- **日期**: 2022-06-21 13:42:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`

### 249. [Web]A00-20220616001 退回重辦頁面增加退回重辦方式的說明
- **Commit ID**: `2f96b0529d219879fe2679c40b6603e8b7309ee6`
- **作者**: yanann_chen
- **日期**: 2022-06-20 14:09:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 250. [Web]Q00-20220609001 修正行動裝置在加簽關卡選擇參與者人員後，選擇參與者的下方欄位會顯示已選取0個項目
- **Commit ID**: `6f724526dcdf1eea047294858cfecf9bd0661df9`
- **作者**: 王鵬程
- **日期**: 2022-06-09 11:50:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 251. [報表設計器]Q00-20220607003 修正欄位字串如果含as會辨識錯欄位名稱
- **Commit ID**: `b379956f95cf06d57f991b653dc6480c3f7ae7c0`
- **作者**: walter_wu
- **日期**: 2022-06-08 09:21:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`

### 252. [Web]Q00-20220607002 修正首頁待辦清單第三頁以上的流程進行派送時會報錯
- **Commit ID**: `c7b2d36dee04fd8cc047a4ce0a3f26325d62440a`
- **作者**: 林致帆
- **日期**: 2022-06-07 14:20:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 253. [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼 1.移動端原本就會把中文字轉換成Unicode字符代碼作呈現,如果再轉一次就會把&轉換掉 2.關聯紀錄:A00-20220511001
- **Commit ID**: `c19c7517a4280f2ea190a5ebb78b6d3b0d96a8f1`
- **作者**: kmin
- **日期**: 2022-07-14 16:39:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 254. Revert "[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼"
- **Commit ID**: `ff9754ebb34842d9a7b2c570f845fc1277289125`
- **作者**: kmin
- **日期**: 2022-07-14 16:37:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 255. [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼
- **Commit ID**: `d75c994b9ebf5e12ffd5a75b93f4433a24850191`
- **作者**: 郭哲榮
- **日期**: 2022-07-12 20:01:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 256. [流程引擎]Q00-20220628001 調整流程關係人與關係部門解析邏輯，若流程中沒有變更流程關係人或關係部門資料，系統在流程往下派送時就不會再解析流程關係人與關係部門
- **Commit ID**: `c969b52102ecb4def4a86fff53097d8966931a97`
- **作者**: yanann_chen
- **日期**: 2022-06-28 17:17:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 257. [流程引擎]Q00-20220707002 修正表單日期元件預設值計算錯誤 相關議題單：C01-20220701004。
- **Commit ID**: `0202ed19fb05f4ef5caced212eaf59732c4afe40`
- **作者**: kmin
- **日期**: 2022-07-14 16:34:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 258. [BPM APP]Q00-20220711001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟移動端表單會報錯的問題
- **Commit ID**: `d8ca71f47ea6d2662f690b837cbda0c4e18358f8`
- **作者**: cherryliao
- **日期**: 2022-07-12 11:15:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`

### 259. [表單設計師]A00-20220704001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟表單會報錯
- **Commit ID**: `4a7839f6112ce0f9793dc88d13827b7169f70b99`
- **作者**: walter_wu
- **日期**: 2022-07-08 18:24:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 260. [表單設計師]C01-20220706003 修正當變更表單的對齊方式並儲存後會將已設計過的行動版表單設計欄位清空問題
- **Commit ID**: `43126117cd2f58ca7ebb13c58093b4894adae349`
- **作者**: yamiyeh10
- **日期**: 2022-07-08 11:01:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`

### 261. [流程引擎]Q00-20220707003 修正DialogInput元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容
- **Commit ID**: `a91321f3fe9f0c7fe064f7c6b1867aa4406c2709`
- **作者**: yanann_chen
- **日期**: 2022-07-07 16:10:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 262. [Web]A00-20220628001 修正已簽核過的關卡，從Mail的待辦連結進入該表單時可以移除附件
- **Commit ID**: `17d2f439eed1efb31126c52c678ac5a09292b906`
- **作者**: kmin
- **日期**: 2022-07-14 16:28:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 263. [Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾
- **Commit ID**: `b351b7ac7de092698624bb3fb5339ede8495a92e`
- **作者**: 王鵬程
- **日期**: 2022-06-13 16:14:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`

### 264. [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯[補修正]
- **Commit ID**: `40b4ab1d5f46cc66c43a675819b363df40c62218`
- **作者**: 林致帆
- **日期**: 2022-06-14 11:52:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 265. [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯
- **Commit ID**: `a54aee90cc354946b0b0ecc2d6c6af223527b0a3`
- **作者**: walter_wu
- **日期**: 2022-06-13 16:07:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 266. [內部]Q00-20220610001修正WorkFlow拋單log會顯示[Fatal Error] :1:1的錯誤訊息
- **Commit ID**: `38f5c4331370bb8b639116d0a47e783d2e124193`
- **作者**: 林致帆
- **日期**: 2022-06-10 11:42:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 267. [Web]A00-20220608003 修正進入追蹤流程畫面時未清除「撤銷理由」欄位內容
- **Commit ID**: `7f0adcd722b72637ea0e2ce96e77fd7cd6848873`
- **作者**: yanann_chen
- **日期**: 2022-06-09 17:18:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 268. [流程引擎]Q00-20220609003 修正使用者操作個人預設代理人設定時，代理人可能有多筆相同人員的問題
- **Commit ID**: `865774fa9b179a00f322eaff4163c2676a7a84e0`
- **作者**: yanann_chen
- **日期**: 2022-06-09 16:32:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/user_profile/MultiDefaultSubstituteForManaging.java`

### 269. [內部]Q00-20220609002 調整DWR設定讓Log不要一直出現轉換ProcessInstanceStateType的錯誤
- **Commit ID**: `18e25acb3b7f0414ddf505687e96c5cc66c0e3ed`
- **作者**: walter_wu
- **日期**: 2022-06-09 15:28:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 270. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
- **Commit ID**: `c6ccf3fee3d00c3a620ba7c2db247a20c9cff9de`
- **作者**: walter_wu
- **日期**: 2022-06-10 18:10:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 271. [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
- **Commit ID**: `eeb41f2b2ec36cbca2e5989c272bf8b90886920d`
- **作者**: kmin
- **日期**: 2022-07-14 16:20:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 272. Revert "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開"
- **Commit ID**: `5a2411fdd14302b4f3cfbb506efe248d9d30d460`
- **作者**: kmin
- **日期**: 2022-07-14 16:19:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 273. [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
- **Commit ID**: `36869fb976f50f659b1bdd104cef8f7bc8fa0a7c`
- **作者**: walter_wu
- **日期**: 2022-06-08 15:56:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 274. [WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯
- **Commit ID**: `5a89e296bee6eef02313fc33a1c5e9da8778d7fb`
- **作者**: walter_wu
- **日期**: 2022-06-08 11:49:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 275. [Web]Q00-20220606001 修正第二關之後的關卡預解析，流程線的條件式採用表單欄位時，預解析的關卡與派送的關卡不符合
- **Commit ID**: `46257b441af7fa32405dbe75555f7b915460b7e1`
- **作者**: 林致帆
- **日期**: 2022-06-06 14:26:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 276. [Web]A00-20220526001 修正如果DB是Oralce在線閱讀浮水印管理出現無法取得EJB所提供的服務
- **Commit ID**: `a61981f8b3b11ef21fe916b79f542cbf2c15f720`
- **作者**: walter_wu
- **日期**: 2022-06-01 13:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java`

### 277. [內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方
- **Commit ID**: `c5ee178664e6b6e9b0a45d94f6255f2f860e05de`
- **作者**: walter_wu
- **日期**: 2022-05-30 15:34:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 278. [BPM APP]C01-20220523004 修正移動端subTab元件使用formScript在單獨顯示時會顯示其他頁籤內容的問題
- **Commit ID**: `bbf6cdaba99d733ce9fe7f8213e5eb67e2b27759`
- **作者**: 郭哲榮
- **日期**: 2022-05-27 19:22:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileSubTab.js`

### 279. [Web]Q00-20220527003 修正使用者使用監控流程的最大筆數沒有根據process.default.show.records的設定
- **Commit ID**: `72b64ec91f0f44e402f870bccf743f5bb820d042`
- **作者**: walter_wu
- **日期**: 2022-05-27 17:34:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 280. [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
- **Commit ID**: `a609d8da256c68226cee6c806df3edff3854069c`
- **作者**: kmin
- **日期**: 2022-07-14 16:14:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 281. Revert "[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘"
- **Commit ID**: `609cdce934fcef062a77186802a1131ffba5ef0d`
- **作者**: kmin
- **日期**: 2022-07-14 16:13:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 282. [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
- **Commit ID**: `6f10ef8d256aaf3cbae2014d7ae6712710b19dd6`
- **作者**: walter_wu
- **日期**: 2022-05-27 17:15:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 283. [BPM APP]C01-20220524002 修正改派通知設定整張表單時Line推播內容不會呈現表單訊息格式的問題
- **Commit ID**: `17062d606905ce9eb3c09a6d27037a1d5538e339`
- **作者**: yamiyeh10
- **日期**: 2022-05-26 10:06:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 284. [Web]A00-20220519001 修正IE加簽會加成兩次的問題
- **Commit ID**: `376f93870331f3b05488b75f674136eac7c976ee`
- **作者**: walter_wu
- **日期**: 2022-05-25 18:07:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 285. [表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出
- **Commit ID**: `76db9c269d9c579b4d4f27d9909c284c8328a5e5`
- **作者**: yanann_chen
- **日期**: 2022-05-25 17:01:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 286. [Web]Q00-20220525004 修正輸入單身資料有&#加任意數字，被轉成特殊符號，會與輸入資料不符
- **Commit ID**: `45407b00ec35e0dac4a3ce528e7b0d04805210cf`
- **作者**: 林致帆
- **日期**: 2022-05-25 16:48:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 287. [TIPTOP]Q00-20220525003 修正拋單的單身資料有中刮號會被轉成小括號，導致資料與TIPTOP不符合
- **Commit ID**: `d3a9fd788e9e1e16e58d81f9e19de67fe4daab79`
- **作者**: 林致帆
- **日期**: 2022-05-25 16:32:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 288. [系統管理工具]C01-20220524002 修正進階功能>檢查密碼如果User裡有關聯有異常的會全部撈不出來
- **Commit ID**: `bbfeb44c4307ea6400f09ce9d4780ac9c80497c2`
- **作者**: walter_wu
- **日期**: 2022-05-24 19:04:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/util/CheckPassDialog.java`

### 289. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
- **Commit ID**: `f00f82cb6b0be171a0174cfbd03708d88ebae3b9`
- **作者**: yanann_chen
- **日期**: 2022-05-25 14:01:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 290. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
- **Commit ID**: `a8f914520555ca73234fbf0c7fead761f49bd875`
- **作者**: yanann_chen
- **日期**: 2022-05-25 10:57:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 291. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤
- **Commit ID**: `92d2f06ff00910ede22c220a303bb88dfaa46d6b`
- **作者**: yanann_chen
- **日期**: 2022-05-24 17:45:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 292. [內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)
- **Commit ID**: `f7abe4bb2ddbd60043acc83d567a426828ab2ce3`
- **作者**: walter_wu
- **日期**: 2022-05-23 18:19:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 293. [流程引擎]Q00-20220520003 修正流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
- **Commit ID**: `c736e90c69c8718d13d0d4ed667e37eb6cc66433`
- **作者**: yanann_chen
- **日期**: 2022-05-20 15:42:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 294. [BPM APP]C01-20220516002 修正行動端FormUtil.disable為true時Dropdown元件顯示異常問題
- **Commit ID**: `e867113bc82fba87d0155404b131c6579e495721`
- **作者**: 郭哲榮
- **日期**: 2022-05-18 18:47:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 295. [Web]Q00-20220518001 修正退件表單資訊與開啟的表單關連錯誤
- **Commit ID**: `1d1f78987f576456c88b36bf6fcc7f22af35cd48`
- **作者**: 林致帆
- **日期**: 2022-05-18 10:39:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 296. [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題[補]
- **Commit ID**: `308d711ac70660457534c193ad6b766ecfca5421`
- **作者**: 郭哲榮
- **日期**: 2022-05-17 15:12:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 297. [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題
- **Commit ID**: `111da87c3aab7f8bef5481d6b2302367813faa76`
- **作者**: 郭哲榮
- **日期**: 2022-05-16 15:10:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 298. [流程引擎]A00-20220511001 修正使用者輸入到表單TextArea的內容在儲存表單後變成亂碼的問題
- **Commit ID**: `f6f7a2a2671881d90f6465235cc44fbbe470add9`
- **作者**: yanann_chen
- **日期**: 2022-05-12 18:19:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 299. [Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」
- **Commit ID**: `8e3c11ac3da316033d2f492b08f924a2b75a70f3`
- **作者**: wayne
- **日期**: 2022-05-12 16:55:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`

### 300. [流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差
- **Commit ID**: `90fcad606b409e4042b1bef7deedac699c81a377`
- **作者**: yanann_chen
- **日期**: 2022-05-12 14:36:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 301. [Web]Q00-20220411005 修正使用者在絕對位置表單進行簽核時遭遇產品程式錯誤
- **Commit ID**: `491da6dbb2e32308231f696260e999bccc245e0b`
- **作者**: yanann_chen
- **日期**: 2022-05-12 14:12:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 302. [內部]Q00-20220512001 bootstrap-table-1.18.3.js更換檔案名稱
- **Commit ID**: `feca23c9bbd4d4fa19731df8d118eaa6119f321c`
- **作者**: yanann_chen
- **日期**: 2022-05-12 11:47:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js`

### 303. [Web]Q00-20220511005 修正T100拋轉單據中有舊值的單身內容沒有顯示為紅色
- **Commit ID**: `e857eee9d14d7900dac6823a0c3d0e80875b2810`
- **作者**: yanann_chen
- **日期**: 2022-05-11 15:03:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js`

### 304. [內部]Q00-20220511003 調整BCL8轉檔Timeout從預設2分鐘拉長到5分鐘
- **Commit ID**: `362595d986a914d1d73bd29a134efdbac19a0843`
- **作者**: walter_wu
- **日期**: 2022-05-11 14:44:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 305. [流程引擎]Q00-20220506001 調整使用者「授權的流程」，流程清單筆數改為設定檔設定
- **Commit ID**: `8cdb96c57f96594b3875ade082de583f64626fa3`
- **作者**: yanann_chen
- **日期**: 2022-05-11 12:07:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 306. [流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料
- **Commit ID**: `27e790b2aac3873472de9abaeaffd2dc73806941`
- **作者**: yanann_chen
- **日期**: 2022-05-11 11:23:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 307. [流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知
- **Commit ID**: `cb108e61e86af69550310d90255b150951786861`
- **作者**: yanann_chen
- **日期**: 2022-05-11 11:10:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 308. [Web]Q00-20220511001 調整列印表單畫面簽核歷程，移除「資料代號」、「通知者」欄位
- **Commit ID**: `ee4d7001d1002b9eb3ce8c39c6ed9b186ebabbc1`
- **作者**: yanann_chen
- **日期**: 2022-05-11 10:54:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 309. [Web]Q00-20220510001 修正IE瀏覽器開啟產品授權註冊頁面時，畫面跑版呈現異常
- **Commit ID**: `b5e6b24502de1f68fa32e7b5a8c18577599e2e0e`
- **作者**: wayne
- **日期**: 2022-05-10 15:59:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`

### 310. [BPM APP]C01-20220509009 修正行動端在Grid元件存在沒有繫結元件情況下點擊編輯按鈕會失敗問題
- **Commit ID**: `94ed24fd530b825fd409ddf9be9980e440c9f68c`
- **作者**: yamiyeh10
- **日期**: 2022-05-10 10:49:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 311. [Web]Q00-20220509002 修正授權的流程使用closeTime(結案時間)排序會報錯的問題
- **Commit ID**: `a4a56b5209e3ae4e8b658eb18dfcb56fa7bc2e19`
- **作者**: walter_wu
- **日期**: 2022-05-09 16:41:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 312. [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果[補]
- **Commit ID**: `0b32f722e9b9f876c594e612079f917c99549eb3`
- **作者**: 王鵬程
- **日期**: 2022-05-05 17:52:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 313. [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果
- **Commit ID**: `f0ac924b4b31ad4e2cd50ed2318ed58e8e9c30b9`
- **作者**: 王鵬程
- **日期**: 2022-05-05 15:24:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 314. [Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢
- **Commit ID**: `2e43780e2adce65ebb4eb223e1162da0309ee205`
- **作者**: 林致帆
- **日期**: 2022-05-05 11:00:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 315. [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]
- **Commit ID**: `f7bc2ccbe0e53fcce61966ae4652c9af75b0869c`
- **作者**: yanann_chen
- **日期**: 2022-06-20 16:44:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 316. [Web]Q00-20220616001 調整待辦通知信中簽核歷程的處理者欄位移除粗體樣式
- **Commit ID**: `3e92541da2e6f767fafd2b39762b71fabca29cc7`
- **作者**: 林致帆
- **日期**: 2022-06-16 13:59:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 317. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
- **Commit ID**: `6e8c290af7d49313599bc9d2c487f1a1b0f0be7c`
- **作者**: yamiyeh10
- **日期**: 2022-05-26 09:50:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 318. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
- **Commit ID**: `4ecab37c0ba800aff656e15697922a7905333a38`
- **作者**: yamiyeh10
- **日期**: 2022-05-16 15:15:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 319. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題
- **Commit ID**: `c83c05c6362fbafaf767d8fbae44c8f085e1d43d`
- **作者**: yamiyeh10
- **日期**: 2022-05-10 18:03:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 320. [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題
- **Commit ID**: `5241a7266e699eae9ce7df37a8318f455eba5260`
- **作者**: yanann_chen
- **日期**: 2022-05-04 14:02:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 321. [BPM APP]C01-20220419001 修正移動端選項元件使用動態塞值且隱藏標籤會顯示異常問題
- **Commit ID**: `cbb1f75cdd68334a25f6bd6afaacff11663fed80`
- **作者**: 郭哲榮
- **日期**: 2022-04-28 12:23:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 322. [Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色
- **Commit ID**: `99882ef3e28f0fe40d995613bfc079c3cebef6f9`
- **作者**: 王鵬程
- **日期**: 2022-04-27 16:50:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 323. [內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤
- **Commit ID**: `e21554874ff8f1a19e7913cd46373a366085b4d5`
- **作者**: walter_wu
- **日期**: 2022-04-27 15:00:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 324. [流程引擎]A00-20220519003 修正終止前置流程時，後置流程完成流程撤銷後，前置流程出現「派送失敗」的錯誤訊息，無法正常終止
- **Commit ID**: `b6ce7ebaa673892bb38fbbc04eb1bb7883c9bc30`
- **作者**: yanann_chen
- **日期**: 2022-05-20 14:08:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 325. [Web]A00-20220517004 修正調離職人員帳號更新排程預設出貨端口改成8086
- **Commit ID**: `d2588982d05156ee7d73b94fd6796fb2c74ec775`
- **作者**: 林致帆
- **日期**: 2022-05-19 17:04:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaJobs.xml`

### 326. [內部]Q00-20220509001修正設定檔ESS內網IP敘述的Oracle指令有誤
- **Commit ID**: `d8c4aa3f68bc813e3f9153809738802f6159904e`
- **作者**: 林致帆
- **日期**: 2022-05-09 08:55:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 327. [流程引擎]A00-20220513001 修正ProcessMapping在Oracle遺漏attachInfo欄位導致拋單失敗
- **Commit ID**: `5cabb6a193ce4d7930008bd9863bf345c2e22b34`
- **作者**: 林致帆
- **日期**: 2022-05-13 17:23:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 328. [內部]A00-20220517002 修正DB為 Oracle時，產品授權畫面無顯示模組名稱
- **Commit ID**: `36049cb4b8d31b5684bcc014bc16f8b2c192f97f`
- **作者**: wayne
- **日期**: 2022-05-17 11:32:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

