{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.7.7.1", "date": "tag 5.7.7.1\nTagger: 吳至賢 <<EMAIL>>\n\n2020-03-05 14:11:18", "message": "[BPM APP]Q00-20200303004 修正在Android手機上的時間、日期元件icon樣式跑版", "author": "yamiyeh10"}, "舊分支": {"branch_name": "5.7.6.3", "date": "tag 5.7.6.3\nTagger: 張詠威 <<EMAIL>>\n\n20191028 17:30 lastbuild2019-10-28 15:21:22", "message": "增加 mtsmodule00000000000000000000001 之模組icon", "author": "<PERSON>"}, "比較時間": "2025-07-28 17:50:28", "新增commit數量": 280, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "79ea56750a13997991dccba3315bee3f697fee7a", "commit_訊息": "[BPM APP]Q00-20200303004 修正在Android手機上的時間、日期元件icon樣式跑版", "提交日期": "2020-03-05 14:11:18", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08d922ba1cbe94ad11b4d94bfa321d5d1d729bbf", "commit_訊息": "[Web]A00-20200303002 修正簡易簽核頁面的流程主旨有中文會亂碼", "提交日期": "2020-03-04 18:53:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformSimpleWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0609a45994dda13e9a45dce65aa6c1500e94d6e", "commit_訊息": "[BPM APP]Q00-20200303002 修正優化後的必填樣式跑版問題", "提交日期": "2020-03-03 17:49:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd7d822478f14a4fc3f8a7edcd6d60fbd481e1b9", "commit_訊息": "[Web]A00-20200227002 修正表單頁簽元件被流程設定為invisible時，列印表單時會報錯空白畫面", "提交日期": "2020-03-03 14:58:31", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ec3c00208b5fb943651b927ef50c6b04bc510315", "commit_訊息": "[BPM APP]Q00-20200227001 調整模擬簽核按鈕，使用手機模擬模式下不支持", "提交日期": "2020-03-03 10:56:30", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1ab65b8255d26b4ffb3fee4aa90adc02d9ff3913", "commit_訊息": "[流程引擎]A00-20200212002 調整流程終止做逐級通知時，寄信對象為流程線上的所有關卡處理者", "提交日期": "2020-02-27 19:24:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e128541582496b42d590ebda8f0c09107c2d979", "commit_訊息": "[內部]Q00-20200224001 更正DDL,DML名稱", "提交日期": "2020-02-27 16:09:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DDL_MSSQL.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DDL_Oracle_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_MSSQL_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_Oracle_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 4}, {"commit_hash": "f485ca8ac1234440f75468f6193a12b779bcff5d", "commit_訊息": "[TIPTOP整合]A00-*********** 修正TIPTOP用RestFul接口不支持舊有2參數或3參數呼叫[補]", "提交日期": "2020-02-27 10:47:22", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/TIPTOPMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecafd842cf659dfbabc2232407d0767e761eb5dc", "commit_訊息": "[表單設計師] Q00-20200217002 修正Web表單設計師行動下拉元件輸入框文字對齊沒有效果問題 [補]", "提交日期": "2020-02-26 09:41:05", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6056985025f89419bcaa48e2172294b96fde4834", "commit_訊息": "[BPM APP]Q00-20200225004 修正Web端行動表單預覽規劃模擬簽核功能多語系異常", "提交日期": "2020-02-25 19:37:36", "作者": "詩雅", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5771.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "acbd187d7eac116339711ca30e06ba84689499ba", "commit_訊息": "[BPM APP]Q00-20200225005 修正微信進階按鈕附件清單圖示", "提交日期": "2020-02-25 19:18:07", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1df433d6b9b17ef3d6e711488a1f5094cc3e9d9f", "commit_訊息": "[BPM APP]Q00-20200225008 修正附件清單資料擺放位置", "提交日期": "2020-02-25 19:10:10", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0521a3d4d8dc6e643f419732ee87ecd8fa7c3f87", "commit_訊息": "[BPM APP]Q00-20200225009 調整附件清單資訊位置初始化，返回表單後，再進清單，會記憶上次操作位置", "提交日期": "2020-02-25 19:06:24", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba83741dfa472e3c81f8b4bc05e6f948574a372f", "commit_訊息": "[Web]A00-20200212001 修正資料選取註冊器頁面無法搜尋出符合的資料", "提交日期": "2020-02-24 17:05:40", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "81cfb1d05161a7105ec7444ca6bf187640b88739", "commit_訊息": "[BPM APP]Q00-20200220002 優化行動表單的必填提示樣式改將*號移至標籤文字後顯示", "提交日期": "2020-02-21 16:29:35", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "6d277006f6d0e3ecbddb6502959b7b0a005ece24", "commit_訊息": "[BPM APP]Q00-20200218002 修正行動模擬簽核表單樣式", "提交日期": "2020-02-20 19:29:15", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74279b25abb03da99e45c1bd9f6f57c745545f53", "commit_訊息": "[BPM APP]Q00-20200219003 修正IMG進詳情表單有警示訊息時遮罩會消失問題", "提交日期": "2020-02-20 19:25:51", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7071dc604973687c08c39ba2266d6c6eb3e8ac9b", "commit_訊息": "[BPM APP]Q00-20200218009 修正IMG從智能快簽上點明或後天提醒會失敗問題", "提交日期": "2020-02-20 19:24:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "重新命名", "狀態代碼": "R060"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.7.1_mobile_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.7.1_mobile_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "77b46ea4da5423db6b6a68c8435099796d65fd23", "commit_訊息": "[BPM APP]Q00-20200218003 調整BPM APP的create SQL成DDL與DML", "提交日期": "2020-02-20 19:19:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "重新命名", "狀態代碼": "R077"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "重新命名", "狀態代碼": "R075"}], "變更檔案數量": 4}, {"commit_hash": "50c0c2cc210a1a2a0757b5f8469381dff0e42126", "commit_訊息": "[表單設計師]Q00-20200217002 修正Web表單設計師行動下拉元件輸入框文字對齊沒有效果問題", "提交日期": "2020-02-20 19:18:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "41e9f132e06b7efa23f6e49b0712f11e143012f6", "commit_訊息": "[Web]修正base多語系表的key", "提交日期": "2020-02-20 17:39:25", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "322e2ba02107f8f6a82a785d72505f81a473909c", "commit_訊息": "[組織設計師]C01-20200218003 調整組織設計師在(部門，公司，群組)部分欄位允許特殊字元判斷", "提交日期": "2020-02-20 17:33:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/GroupEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OrgEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OrgUnitEditor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "aab83a7e57d281acbbe1fd03725ef352eab21c9e", "commit_訊息": "[E10] 流程範本垃圾資料移除", "提交日期": "2020-02-19 18:55:07", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df2ea109414294a1cc27655a2abf2129bfb647a9", "commit_訊息": "[TipTop]修正******* TipTop錯誤的UpdateSQL", "提交日期": "2020-02-19 18:53:25", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/*******_TIPTOP_DML_MSSQL_1_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6742f83878145affb4ed7be5455da1b6af03aedb", "commit_訊息": "[Web]Q00-20200219004 修正當使用者同時在部門與專案，發起流程時無法選擇從專案發起", "提交日期": "2020-02-19 18:45:50", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1cf64e0cfa79e67048f8b1a117b5bceb6ac3acc8", "commit_訊息": "[ESS]A00-20191231001 修正ESS整合表單如果該關卡有執行過通知其他使用者的動作，操作報表列印功能出現異常", "提交日期": "2020-02-19 17:09:44", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/AppFormIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WebServiceUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a1a052f1b2b541f5b34023dbcfef17581034be5d", "commit_訊息": "[ISO]A00-20200218001 修正發起調閱單時，若session存在工作紀錄時，則會造成開單異常", "提交日期": "2020-02-19 14:44:52", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f44275c500f8955c0449f3a02232a226918c7948", "commit_訊息": "[內部]Q00-20200217001 移除出貨ISO的ISOAutoDailyJob排程", "提交日期": "2020-02-17 11:33:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaJobs.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b05ac8936b8064ccf73943363847f62fef5e61f1", "commit_訊息": "[Web]Q00-20200213005 修正問題: 當通知原因有換行時，畫面無法正常呈現", "提交日期": "2020-02-15 14:28:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9141d7c5100ad2e1e7c85ea466251fa0178d23f", "commit_訊息": "[流程引擎]Q00-20200213009 修正問題: 第一關向後加簽選擇\"表單唯讀\"，仍套用表單必填欄位驗證", "提交日期": "2020-02-15 14:25:34", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3f7c83336797c34fdc88b936f0b230639d65d8a6", "commit_訊息": "C01-20200206004 修正客制開窗在某筆欄位資料有null，上下選單資料有不同步的狀況[補修正]", "提交日期": "2020-02-14 19:17:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "10b5e740a843f7cb0d7f8d01850e616a80219424", "commit_訊息": "[Web]C01-20190917006 工作轉派過而原處理者取回處理後在mail中也會顯示(代)", "提交日期": "2020-02-14 18:42:42", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16e4cfe18659e8c5d01d7ba983eaef07198b138b", "commit_訊息": "[Web]C01-20200206004 修正客制開窗在某筆欄位資料有null，上下選單資料有不同步的狀況", "提交日期": "2020-02-14 18:39:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fb850911bb0de8c699f51652cce590605e0bf3d1", "commit_訊息": "[Web]BPMRsrcBundle.xls中將大小寫調為一致", "提交日期": "2020-02-14 10:28:46", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "18ba573eee84758ef028a3471d20d92b3ad349aa", "commit_訊息": "[Web]Q00-20200213007 BPMRsrcBundle.xls中key對應到的英文欄位是填寫中文修改為英文", "提交日期": "2020-02-13 19:24:07", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2919fe5e96d4a2401bbb7ab4f3023c66e51e6622", "commit_訊息": "[BPM APP]在流程設計師中將Attachment元件設定唯讀狀態時行動表單畫面上改成不顯示附件按鈕元件", "提交日期": "2020-02-13 16:24:35", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f734cf28f087fc72b412c07fbdc49fe3b58f119", "commit_訊息": "[Web]A00-20191213001修正在英文介面下操作撤銷流程，出現的提示訊息為中文[補修正]", "提交日期": "2020-02-13 15:33:34", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f461ca5ddf8ea5dbebadbf2727a90a3975b633f6", "commit_訊息": "[Web]將外部連結的頁面加上登出按鈕及登入者資訊[補修正]", "提交日期": "2020-02-13 11:37:38", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7304c1e40d095d981ae082465a95ca16f2a789df", "commit_訊息": "[BPMMPT]Q00-20200213002 將首頁方案包的URL連結，調整至SystemVariable", "提交日期": "2020-02-13 10:50:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "39275f8d8fa28669e0000515424c5568084efcac", "commit_訊息": "[BPM APP]Q00-20200212002 修正加簽選擇人員若用快搜選擇完後會取代原先已選擇的人員清單", "提交日期": "2020-02-12 15:30:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "63c7dcce57d9ff6148e9d78a60659c69f303bd9f", "commit_訊息": "[BPM APP]Q00-20200211004 修正使用Android手機在待辦填寫資料時下方會出現同意派送區塊", "提交日期": "2020-02-12 12:17:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c206b9f08c01776d5a386f94e2e37186d2a61672", "commit_訊息": "[BPM APP]Q00-20200211003 修正待辦詳情簽核畫面在選擇派送部門彈窗按取消後遮罩不會關閉", "提交日期": "2020-02-12 10:50:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1d969a1c35db04548d833993d9f1919ef1109d22", "commit_訊息": "[BPM APP]Q00-20200211001 修正浮動按鈕樣式相關問題", "提交日期": "2020-02-12 10:26:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 17}, {"commit_hash": "f9d15f3e2fe2fe36dd4c4516f6b4b7670a042d68", "commit_訊息": "[BPM APP]Q00-20200210005 修正iOS手機在附件清單畫面上的刪除按鈕跑版", "提交日期": "2020-02-11 13:53:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5dee999fdaa64bc859f8e663943655d82c04edeb", "commit_訊息": "[表單設計師]Web表單設計師行動端新增輸入框文字對齊功能", "提交日期": "2020-02-10 17:02:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "80e0c867a9f3c1a773fb03c6ffe68c5152ed5691", "commit_訊息": "[表單設計師]Q00-20200210001 修正行動相對位置表單的RadioButton和CheckBox元件樣式跑版", "提交日期": "2020-02-10 14:49:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "87e586e06efbe6134fc976547ae8d15137f3d6dc", "commit_訊息": "[BPM APP]Q00-20200204001 修正行動表單樣式Grid元件最後一筆單身資料跑版問題", "提交日期": "2020-02-07 17:44:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b34c825f5a378a70da6970dfd7d411d6beca0274", "commit_訊息": "[BPM APP]Q00-20191204001 修正IMG表單詳情中的提醒功能窗在開啟後再點進階鈕後導致不會關閉問題", "提交日期": "2020-02-07 16:02:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "471e48b45272569a1d0ec11c4491831c795fd51d", "commit_訊息": "[BPM APP]Q00-20200115001 修正IMG推播消息若為從中間層在進詳情不會出現訂閱消息按鈕問題", "提交日期": "2020-02-07 15:22:14", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f644e31124fe8b44d059f387dc41134895b27209", "commit_訊息": "[BPM APP]Q00-20200115002 修正IMG推播消息若為通知流程中間層不會出現表單詳情按鈕問題", "提交日期": "2020-02-07 14:02:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f80f79fa22ebdd5634a5844592cdf3ef2b7ef1e9", "commit_訊息": "[BPM APP]調整附件列表架構與操作方式[補]", "提交日期": "2020-02-07 10:41:50", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "950db4abedc4e8140a5f27c7f731e75d1e1ac303", "commit_訊息": "[BPM APP]Q00-20200102002 修正行動模擬簽核按鈕在工作通知上出現問題", "提交日期": "2020-02-07 10:39:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88f827e662e703db5bfbbe0e2f075f2db1f3684f", "commit_訊息": "[BPM APP]調整附件列表架構與操作方式", "提交日期": "2020-02-06 20:21:20", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "19adef9f6763ebd0dffa3a7a580e716d8888f4b6", "commit_訊息": "[BPM APP]C01-20200203002 修正行動版產品開窗元件為自定義開窗時，該元件顯示值為null或空白", "提交日期": "2020-02-06 17:37:58", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eef936bc6a942cf841fa88c42c159c14b0c66fa8", "commit_訊息": "[BPM APP]Q00-20200116001 修正IMG的詳情表單頁面當有智能示警時提示元件的背景顏色跑版", "提交日期": "2020-02-06 15:48:26", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e16bce674b2d3d9681f0677c58bcbafdcf33c6e4", "commit_訊息": "[表單設計師]優化Web表單設計師行動版相對位置的表單元件樣式", "提交日期": "2020-02-06 14:05:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2f2dd2c6772e6f7a604ab7eaa531012bc5b07ca3", "commit_訊息": "[Web]A00-20200120002修正在手機版的撤銷流程頁面顯示撤銷理由欄位", "提交日期": "2020-02-05 19:01:33", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c36b164ca2de3538c6cce970b98fad39f0da57c", "commit_訊息": "[Web]C01-20191218005修正當FormInstance有maskFieldValue時，上傳附件後，附件顯示異常問題", "提交日期": "2020-02-05 16:47:46", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ac8399c47f3747a20ed6899965a86ea6452a1ae0", "commit_訊息": "[流程引擎]C01-20191231001 修正客戶資料庫無故多一筆BatchMailNoticeHandler導致發信錯誤的問題[補修正]", "提交日期": "2020-02-05 14:53:16", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "590f9e038e106cb3b5c6586929d3f3563123b95e", "commit_訊息": "[Web]Q00-20200120001修正絕對位置中grid若先點擊欄位在新增資料造成的錯誤[補修正]", "提交日期": "2020-02-04 18:36:47", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cd484b29da8650d155de9df38350393c87998dfb", "commit_訊息": "[TIPTOP整合]A00-*********** 修正TIPTOP用RestFul接口不支持舊有2參數或3參數呼叫", "提交日期": "2020-02-04 18:32:34", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TiptopDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/TIPTOPMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.7.1_TIPTOP_DML_MSSQL_1_Check.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.7.1_TIPTOP_DML_Oracle_1_Check.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "a321a784e5cccc2097633a1eca68421fa1dea49f", "commit_訊息": "[BPM APP]行動表單元件樣式調整", "提交日期": "2020-02-04 18:17:06", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9edf7c474c163e531fad1ce99b7f0357a9f3c5a9", "commit_訊息": "[流程引擎]修正客戶資料庫無故多一筆BatchMailNoticeHandler導致發信錯誤的問題", "提交日期": "2020-02-04 16:23:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c88b0f10fa7305e8294b7d304fa724bce3129a4", "commit_訊息": "[BPM APP]調整行動版表單畫面上浮動按鈕的顯示機制", "提交日期": "2020-02-04 16:04:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "b07e203131266372855ba3146edafe1db186abc1", "commit_訊息": "[ISO]Q00-20200204002 修正ISO 新增絕對表單，自動編碼規則產生流水號會一直重覆", "提交日期": "2020-02-04 14:02:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocSnGenerator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99fa4c75e93ebe7b6261a1d5367513b3d3d7cedb", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-02-04 10:44:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9821027e16e7e3047b1ea27ad7002e355c9fc914", "commit_訊息": "Web表單設計師行動端新增輸入框文字對齊功能", "提交日期": "2020-02-03 11:53:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4df371ac871516ee72f3ea7fbae6fd2fe76dcb47", "commit_訊息": "C01-20200113002 調整login頁面，避免產生兩次request", "提交日期": "2020-02-03 10:15:41", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99c7d2e86eac40e6661a89a52d2aa2b2147d258e", "commit_訊息": "Q00-20190827002 修正主部門資料在編輯時，主部門勾選會有異常", "提交日期": "2020-01-31 19:05:12", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/employee_panel/OwnerOrgUnitPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/table/OwnerUnitTableController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a54eb5bd65bbe545079d6c9abb963956940466b3", "commit_訊息": "S00-20191225001 當系統無法解析關卡負責人時，顯示提示\"資訊不足，無法解析\"", "提交日期": "2020-01-31 15:56:28", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e75cdc05e20da91be4b755011e253b3a7c1e5599", "commit_訊息": "調整Web表單設計師行動端的部分元件禁止支援多欄位擺放", "提交日期": "2020-01-31 11:03:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2304d919c8428b32b06bbb184771ec05c7a46f2a", "commit_訊息": "A00-20200114001 流程草稿取出時，以流程定義ID判斷是否為ESS流程草稿", "提交日期": "2020-01-31 11:00:57", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5e61e417cb2e42fdcf076e5a9e27bc65b15b0861", "commit_訊息": "調整Web表單設計師行動端的元件樣式", "提交日期": "2020-01-30 16:58:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "cb02e5beb69ea7aa8fb24005fb816339ae3d1022", "commit_訊息": "A00-20191213001 修正在英文介面下操作撤銷流程，出現的提示訊息為中文", "提交日期": "2020-01-30 15:38:21", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0cf3e39fc9613902cde6d043b5eb362d40708cb1", "commit_訊息": "行動表單樣式調整", "提交日期": "2020-01-21 11:55:57", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "fbcb4eae789ddf5f16c7eafa7194fafc3b5d91ce", "commit_訊息": "Q00-20200121001 Web端行動模擬簽核樣式調整", "提交日期": "2020-01-21 10:36:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "06b31fd7ca184edf0720ad29239c87efeda26620", "commit_訊息": "Q00-20200120001 修正絕對位置中grid若先點擊欄位在新增資料造成的錯誤", "提交日期": "2020-01-20 19:24:27", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e38c3bcb1a2e1e55048f5eb8650d513aff350d93", "commit_訊息": "行動預覽表單樣式調整", "提交日期": "2020-01-20 18:48:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "4b1c2588cb01972c3218de174debbff77e5a1564", "commit_訊息": "S00-20200106001 支持大陸首頁方案包，在畫面上新增首頁清單的按鈕(增加多語系及增加apServerUrl參數)", "提交日期": "2020-01-20 17:22:57", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dbfe95e6020962b6df0977d4b6858415d5b11d04", "commit_訊息": "[補修正]A00-20200113003", "提交日期": "2020-01-20 14:19:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Dept.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Role.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Title.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "09b4a51e9746d546dd9aca679b726e230d2123c8", "commit_訊息": "行動表單樣式調整", "提交日期": "2020-01-17 18:44:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "118eac525bef04898c302b111f87821ef0ffacb3", "commit_訊息": "調整行動版表單自動轉換時部分元件不支援多欄位顯示", "提交日期": "2020-01-17 18:33:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1e8c67688f3b96337f5d4200508eaeca231c9dfb", "commit_訊息": "A00-20200113003 調整組織同步log訊息", "提交日期": "2020-01-17 17:55:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Dept.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/DeptLvDef.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Emp.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/FuncLvDef.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Group.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/GroupUser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Role.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/Title.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/TitleDef.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/User.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "645eaeb3f44debf0bb7210a649fc99a113d2be1e", "commit_訊息": "A00-20200114003 修正XPDL流程 核決關卡第一關派往第二關 流程圖有缺少一關", "提交日期": "2020-01-17 16:32:06", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "05f953d84e1974a2f22716729b4d5b97fa192359", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2020-01-17 15:41:00", "作者": "peng_cheng_wang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "b5e9e4399cbc5989a3fe09138fe0c86ea73ad2a9", "commit_訊息": "Q00-20191122001 補修正", "提交日期": "2020-01-17 15:36:25", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c8626ce64ed18093e61e5ef976d130465486a84f", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2020-01-17 14:53:06", "作者": "peng_cheng_wang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "17072e91d21aa02c3800aa51768dd3dbef6de0a2", "commit_訊息": "C01-20191017001 修正Grid用流水號排序的排序問題", "提交日期": "2020-01-17 14:52:23", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7679f65741e01a556e2aacfb713da6d307083269", "commit_訊息": "Q00-20200117001 調整程式V58支援ISO絕對位置表單", "提交日期": "2020-01-17 14:21:58", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocCmItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a96540a1eec6fb86382bae3e919331798e37d33e", "commit_訊息": "調整5.7.6.4 Update Sql寫法與5.8一致", "提交日期": "2020-01-17 13:40:02", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "69c479eb13c4b82bda37309d80b16c604ccccb79", "commit_訊息": "補A00-20191224002修正誤刪的內容", "提交日期": "2020-01-17 11:08:06", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5c888717afa119fb92bf4b0c747ee1232a8e7bc0", "commit_訊息": "行動表單樣式調整", "提交日期": "2020-01-16 19:04:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 24}, {"commit_hash": "40051a737ad507aac4755b56fb3fb0ee6c11ed20", "commit_訊息": "A00-20200113002 修正\"追蹤轉匯流程\"流程主旨顯示異常", "提交日期": "2020-01-16 17:14:47", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd9ce2eade52b81f18a5ce6f1868abf1e6ebf66e", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-16 14:08:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b76791c8078a611e1266d75dfdf21de2f19df37", "commit_訊息": "下修58 Log設定，調整某些第3方套件Log層級", "提交日期": "2020-01-16 10:11:08", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9632691d61db1ae06094b99021b650ccbcc8c81e", "commit_訊息": "行動表單浮動按鈕調整", "提交日期": "2020-01-15 15:55:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "a35b17f89f9940e4cad8aadb858557977d52a6b1", "commit_訊息": "新增腳本樣版內容", "提交日期": "2020-01-15 15:07:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "66cdef53f8700838677f594d1d5e7611fb41ba8a", "commit_訊息": "C01-20191224005 ESS單據回寫增加log資訊。(另外增加判斷當AppFormActivityRecord沒有記錄時，直接拋錯)", "提交日期": "2020-01-14 15:39:08", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "00f67212487c4de44cf79a877f941baa0b8d7c7d", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-14 15:06:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/BpmFormatMaterialize.css", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FixMaterializeCssExtruded.css", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/mobile-UI-commonExtruded.css", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 21}, {"commit_hash": "b110da332a65e9f70c74784d249913fb6f17c5c8", "commit_訊息": "C01-20191224005 ESS單據回寫增加log資訊。", "提交日期": "2020-01-14 14:29:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5a62015b5b55f6efecbf0f85e735081b35d1bcc", "commit_訊息": "C01-20191224005 調整ESS單據回寫增加log資訊。", "提交日期": "2020-01-14 14:25:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1c198179a3fa91ea349216c420fb8740a3cc5406", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-14 10:55:06", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bf125e53f2abf3e410fb3cbe0a5160495329799e", "commit_訊息": "C01-20190619005 修正\"搖旗吶喊小助手登入EFGP\"時，ESS相關流程異常", "提交日期": "2020-01-13 16:30:07", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "679b4617c91f8cfe7b97dc21be604940be0dc427", "commit_訊息": "C01-20191203004 修正使用者無法透過點擊鎖頭圖示查看附件權限", "提交日期": "2020-01-13 15:54:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f216f62237a6aa55e02a03955932c744b25ddb2a", "commit_訊息": "[補修正]S00-***********", "提交日期": "2020-01-13 15:10:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e342c4ec41d0f272a9bfba8fe00c500582fe1df6", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2020-01-13 14:09:15", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "fd465db68ef69779fe9f8aeb4b6d61c26dcf8658", "commit_訊息": "[二次修正] title顯示多語系設定", "提交日期": "2020-01-13 13:38:40", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c3b493747c985dc126155613d2956fbf7de02faf", "commit_訊息": "[二次修正] MobileCalendar引用套件", "提交日期": "2020-01-13 13:37:37", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapCalendar/bootstrap-datetimepicker.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapCalendar/moment-with-locales.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapCalendar/moment.js", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap.4.0.0.min.js", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/bootstrap-datepicker.min.css", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 5}, {"commit_hash": "b0666b296934f987918c36a9e7c78597a37c052a", "commit_訊息": "修改IMG移動端模組Action串模組別、title顯示多語系設定 以及 MTSMobileCalendar引用套件", "提交日期": "2020-01-13 10:52:32", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-4.3.1.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapCalendar/bootstrap-datetimepicker.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapCalendar/moment-with-locales.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapCalendar/moment.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/bootstrap-datepicker.min.css", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "1f50dc750639a3b2bf3d2c3efc7286bec05f206f", "commit_訊息": "C01-20190513001 ESS呼叫更新代理人 改為排隊 避免批簽同User同時更新導致其中一個Invoke卡住", "提交日期": "2020-01-13 10:48:28", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormSynchronized.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "f4432c715172b3dd88a2a3ed9c79fde7b613d27c", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-10 11:56:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "1160a76f613164bbceb6e362c3d9d9f2bac0b17d", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-10 11:05:58", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79d96f83747abdfaa0e74cc11825c2cadbbab3f8", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-09 18:53:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cf67b0803e628e1edc742858e07eb3f918dc3731", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-09 18:05:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "78f98b9b3273ae5c68f7d52dd612bfe2c5d1efe8", "commit_訊息": "S00-20200106001 (更改首頁路徑)支持大陸首頁方案包，在畫面上新增首頁清單的按鈕", "提交日期": "2020-01-09 18:00:28", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4c29b72076a6f5cddfe86d25ff07290a30c23e4e", "commit_訊息": "Q00-20200109005 調整ISO檢查文件版號語法", "提交日期": "2020-01-09 17:56:48", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/access_control/hibernate/ISODocCmItemDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63157b4a9a1fff548dd5f73dfa4dbf672a01b2e8", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-09 17:41:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "865bfa29de079bef8555e50b7dd6f5052fe0a2d1", "commit_訊息": "A00-20190805005 正DialogInputMulti元件產生的隱藏欄位(取的id_hdn)在第二次按傳送後，沒有把前次的值先清掉。", "提交日期": "2020-01-09 17:21:06", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/DialogInputMultiTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "862f0365b96ee6ab912001c60e28545f431dac97", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-09 15:51:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5db1ea54f4f08f3ce2b157184869e91d6d64d1d7", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-09 11:24:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "807c7a86b5fb1e2e0e4b70eef4980d82b67b7a5c", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-09 10:51:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d9dc4b7203f01fefbbe281879896a486e397cec0", "commit_訊息": "行動表單元件雙欄樣式調整", "提交日期": "2020-01-09 10:28:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "76cb16c882c87f57449db78a938778f364598da8", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-08 19:32:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "03924e9a6f82cf9d68460f4bbaac1e3a8acebced", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-08 18:34:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d3830f1851367df46b5a1da5b5c37178408eade3", "commit_訊息": "C01-20191220003 修正TIPTOP 拋單超過1000筆單身會出現 StackOverflowError", "提交日期": "2020-01-08 16:49:48", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "270557a8d8774a3711c1f4a2a3de60fdd4f65969", "commit_訊息": "行動表單元件雙欄樣式調整", "提交日期": "2020-01-08 16:35:18", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1cc4b18615cc04b554c8dd680cf9976b9fa17512", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-08 12:02:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d6e3e1a11e6a919a826ada1c4bc94060389cb3f6", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-07 19:28:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c048a52bee8cd3f964d19a199414202dde8c2f8d", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2020-01-07 18:25:29", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "e3365a017a2cc69e42ddcdb8f8147cd56760c493", "commit_訊息": "[補修正]S00-***********", "提交日期": "2020-01-07 18:25:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "246f16b45d70602410462b286c8ebc429cf2dfc7", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-07 16:54:18", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "68182684f472c2a3c8d6d275ab3ddcd4e9f0e13e", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-07 13:46:57", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9dfcb1097c4a7f7b24e0d6f94f6cd3996a2d8cee", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2020-01-07 11:46:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4652e76bae64e993669a2f8e3838ed06e5b6898c", "commit_訊息": "S00-20200106001 支持大陸首頁方案包，在畫面上新增首頁清單的按鈕", "提交日期": "2020-01-06 16:36:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-homepage-list-hover.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-homepage-list.png", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "daacded0e0e8994c251f89bc5ba7a02246a3459a", "commit_訊息": "S00-20190917001 關注類別維護讓設定多語系設定的按鈕維持在頁面上", "提交日期": "2020-01-03 15:49:06", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a8c9863402617881515f7b1f7bcb1b183bb9bd91", "commit_訊息": "C01-20191209001 修正同意派送RESTFul接口若不給formValue(不更新表單欄位)會造成表單空白問題", "提交日期": "2019-12-31 10:45:41", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "21ea547452ea2ec21cde293ea08ea30dbc932bd4", "commit_訊息": "A00-20191002001 修正Mcloud工作事項出現已簽核過的單據", "提交日期": "2019-12-31 09:49:53", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/GroupWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4e56347890164ede7d5f162e521b94ce1c9ccf80", "commit_訊息": "[補修正]S00-***********", "提交日期": "2019-12-30 21:01:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b50c184151078f139f9f61924252204721123e95", "commit_訊息": "S00-*********** 調整流程開窗增加搜尋條件", "提交日期": "2019-12-30 20:57:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "f752bec5cb9c6b39a5432965e7c221e509888759", "commit_訊息": "補修正<第一次>A00-*********** 原本的修正方式有其他異常，取消並下修部分v58使此功能正常的code", "提交日期": "2019-12-30 16:40:58", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/main/DesignerIFrame.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3f8ed4335a969b327e105de8b1dc8a15e77c83ca", "commit_訊息": "A00-20191227001 調整key:faborities -> favorites 共4處", "提交日期": "2019-12-27 18:11:20", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34fc128a32a066f763a547480560f34b38657567", "commit_訊息": "Q00-20191209002 修正特殊字元(如:Em<PERSON>ji、生僻字...等)顯示異常", "提交日期": "2019-12-27 18:05:52", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/StringUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4923656ac85f916ca23262544cd0eecfdb51a954", "commit_訊息": "A00-20191204004 修正流程負責人監控流程的流程篩選顯示異常", "提交日期": "2019-12-27 17:55:59", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b5314dec174d8ab081a5f11a17773210ca898e1e", "commit_訊息": "A00-20191226002 修正ESSQ單查詢作業若在程式授權使用授權「所有人員」無法正常使用", "提交日期": "2019-12-27 11:22:06", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "78aa9a838f3c6dcb17f0369014c032926d95aee1", "commit_訊息": "Q00-20191129006 修正附件下載RESTFul接口若檔名是簡體中文時會出現亂碼問題", "提交日期": "2019-12-27 09:59:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ba70d3e3e3c39edf09cd38e794f0e1021929db1d", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-26 16:10:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3c2c639419aa87889183422baa615da08e145a2d", "commit_訊息": "調整行動端表單函式庫功能", "提交日期": "2019-12-26 15:31:45", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "64d747e676037e835b6dc39ed4d6b9ccc5e381a3", "commit_訊息": "A00-*********** 修正XPDL轉BPMN如果選擇排版轉換完後，全選移動會造成箭頭方向錯誤", "提交日期": "2019-12-26 15:16:12", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f245b611b2d661807c7fb6d948a7f550a38d1a9b", "commit_訊息": "S00-20191225002 調整待辦工作清單接口  增加 模糊查詢欄位(流程名稱,發起人)", "提交日期": "2019-12-26 10:07:45", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "baf452945b2c4c202f7ef6ea90a68ea37ae9c815", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-25 15:58:15", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e97d93492121804b34a5008ee2d6d6ffb0aee51a", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-25 15:01:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea3874422fac8457efaf6944d42d0169eae5a614", "commit_訊息": "A00-20191203004 加簽時，選擇表單權限\"唯讀\"，不套用表單必填欄位驗證", "提交日期": "2019-12-25 14:42:00", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "385a39cd759e7c55f552aa8150290af673b8f978", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-25 14:33:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "5eee9ae7f80d3f8822521c5f216448fe4f7ee5f9", "commit_訊息": "A00-20191224002 欄位IndicatorCalculationType 第一個字母 誤植為大寫", "提交日期": "2019-12-25 11:56:48", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e19a707b92710d24d8ac3efbcf0e26617b07b9de", "commit_訊息": "A00-20191224002 欄位IndicatorCalculationType 第一個字母 誤植為大寫", "提交日期": "2019-12-25 11:54:07", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "18c7f9876c15423e02c907a7e4044bddcd2224f6", "commit_訊息": "C01-20191220003 修正組合SQ時的錯誤", "提交日期": "2019-12-25 11:15:29", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b73c280f357bf794da491472593ba61c10a54243", "commit_訊息": "A00-20191125001 修正流程設計師在新增流程時，不可退回關卡清單顯示的關卡數量異常", "提交日期": "2019-12-25 10:05:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/ProcessHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a6f3e41d652f4c007045d4db9602414098e707d", "commit_訊息": "S00-20180917003 簽核意見置左對齊", "提交日期": "2019-12-23 18:42:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d5da906c94e8f965fc552010dbd5188960828aa", "commit_訊息": "新增行動端表單函式庫功能", "提交日期": "2019-12-23 16:28:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c13fce831ad32344a1b7c316c98d99449c7d8404", "commit_訊息": "[二次修正]行動表單共用方法調整", "提交日期": "2019-12-23 15:04:57", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f3ccd469c738e5d7d059c19a2708add17272cd5", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-23 11:26:22", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b45a6ad5e23ea8b510acc7f3b80209c0ca2540ed", "commit_訊息": "行動表單共用方法調整", "提交日期": "2019-12-23 10:51:55", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e80868644c62bd419da85fca15034c498cbc4e6c", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-20 17:29:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ca4c9a135eecf326083f1442c7163be1a410efc", "commit_訊息": "C01-20191220002 調整信件內容替換的log層級", "提交日期": "2019-12-20 15:35:18", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3949ed92635286a862dd84e314b3e6b256217696", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-20 14:21:34", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "02dc6fb2ede19ceeb415f5ee059b749760e6e550", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-20 10:56:15", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7387c1e192d1cd23bd590b0aedb6b4994b3a6fe3", "commit_訊息": "行動表單元件樣式調整", "提交日期": "2019-12-20 10:36:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileSubTab.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/BpmFormatMaterialize.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FixMaterializeCssExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/mobile-UI-commonExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 22}, {"commit_hash": "f1f97179684fcba60ed4cf531e4599a028a7202e", "commit_訊息": "[補修正]A00-20190724003", "提交日期": "2019-12-19 19:59:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f71eb8156f2cb5f3d9d15c1bf8c3e4b32896674d", "commit_訊息": "將e10流程範本上的測試表單移除", "提交日期": "2019-12-19 17:48:04", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f28841595c658867951c770621571241be2ec613", "commit_訊息": "[補修正]Q00-20191031001 調整ReceiverOID為null，就不存入Mails", "提交日期": "2019-12-19 12:12:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb64ea1a49ef0087c3a09078abf3b775b2e0e70a", "commit_訊息": "Q00-20191031001 調整ISO生失效寄信，在寄信失敗的情況下就不存入Mails", "提交日期": "2019-12-19 11:43:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb49e2cc8b53cf8ce1065659c68c1f49e41b8a08", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-12-19 11:14:56", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "ad437b1bd52e66cfa1077d90b2cf2290a5273555", "commit_訊息": "[補修正]C01-20191216001 調整若使用匿名驗證方式，必需傳入null，以免mail.jar誤判為非匿名", "提交日期": "2019-12-19 11:14:28", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9a89d90135b6a9c61b52861c2599daa4098b1cd", "commit_訊息": "LOG文字微調", "提交日期": "2019-12-19 11:07:06", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f3251bf3a608b616d634bbdfc1ffa7aa3a1110a", "commit_訊息": "A00-20191217001 增加LOG描述", "提交日期": "2019-12-18 11:02:39", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f211d9a12efc50293934e57c3b3187757a828dc5", "commit_訊息": "A00-20191204002 修正日期開窗後和Grid的欄位及欄位間重疊無法選擇日期", "提交日期": "2019-12-17 17:56:13", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/css/calendar/calendar-win2k-cold-1.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a7cbf0a3341cf5508bc4758dd61abb137f855369", "commit_訊息": "[二次修正] A00-20191206003 調整各個應用列表共用的日期格式字段 [多語系]", "提交日期": "2019-12-17 15:50:45", "作者": "詩雅", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "898a14bf9bbad7c031c81212f23741d820ec0de5", "commit_訊息": "C01-20191216001 調整若使用匿名驗證方式，不讓mail.jar判斷為是執行帳號驗證動作", "提交日期": "2019-12-17 14:07:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "714e615b20ebef1247e6616c53d0c2c1d8e5cdf9", "commit_訊息": "A00-20191206003 調整各個應用列表共用的日期格式字段", "提交日期": "2019-12-17 11:23:52", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "8b5d8c44af95c8e031a7dc22ba4c41381542665e", "commit_訊息": "S00-20191023002 流程代理人的解析，從 發起人部門調整為關係人部門", "提交日期": "2019-12-16 16:13:13", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "89ca09a6f5906def768f04870803bec21a3ade86", "commit_訊息": "A00-20191204001 調整PDF轉檔功能,設定為synchronized，避免資源互搶", "提交日期": "2019-12-13 18:22:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7e34c75cc8b625e868bf981350cd0b3cb605c60b", "commit_訊息": "S00-***********[補修正] 修改在工作通知詳細內容中取得通知原因及通知人的方法", "提交日期": "2019-12-12 11:06:04", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "3ce3202cddfffd9d24797e9a1197e958e1d531be", "commit_訊息": "[補修正]A00-20190724003", "提交日期": "2019-12-12 08:43:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "404deba25a5197892b2d6bc6e1cb3b3ec0559a63", "commit_訊息": "A00-20190724003 修正小於符號造成grid資料顯示異常", "提交日期": "2019-12-11 20:22:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c2dd22d60cae35a2ac611b8e9ce2eeb67ea20f54", "commit_訊息": "修正行動的create SQL中Oracle少commit指令", "提交日期": "2019-12-11 19:09:01", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6512e6f2ad9adfc110dc761094602d86fbf04f98", "commit_訊息": "Q00-20191211007 修正整合時因欄位為空字串導致oracle無法新增該入口平台整合資訊問題", "提交日期": "2019-12-11 19:03:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb5d743fb63148d460f88ec014ad07afd00dc159", "commit_訊息": "[補修正]A00-20191015001", "提交日期": "2019-12-11 18:40:12", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6752f2aef742436df71e19615fa487eac7072adc", "commit_訊息": "S00-20191108001[補修正] 修正IE無法使用中文做為查詢條件", "提交日期": "2019-12-11 17:09:57", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c5f062572e4d2ea970fefbfa5f4cf554d32fda2", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-12-10 19:01:31", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "62e7ecae3b7609ff8a82584f57354cff728822ed", "commit_訊息": "[補修正]C01-20191029004", "提交日期": "2019-12-10 19:00:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f80bf5a8554306afea03c6756a6b27984eb19b5e", "commit_訊息": "Q00-20190910007 修正一般使用者只有閱讀表單設計師權限卻可匯入表單", "提交日期": "2019-12-10 15:38:34", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9084a4936e87871147eca77acf51ec5936e9af0", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-12-10 14:06:19", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "f5de2e631a1969cc665d1ead12ea2bfe01ab9694", "commit_訊息": "C01-20191029004 修正用戶每天都會收到重複的信件", "提交日期": "2019-12-10 14:05:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/notification/MailingFrequencyType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2d89ef03aa51402960ca98ae15186fcd64d560fe", "commit_訊息": "A00-20191209002 修正無法設定代理人順序的錯誤", "提交日期": "2019-12-09 18:51:00", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e63ce8ed287c25ad2163bf69fc51f6367005f35a", "commit_訊息": "Q00-20191128002 二次修正附件管理按鈕在iPhone X手機上跑版問題", "提交日期": "2019-12-09 17:58:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "551e05d91f021dcfb1073cb06ad0a7be4d602732", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-12-09 16:07:31", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "ea91cae28b819ed5072d4da283bc2ea5db56a01b", "commit_訊息": "[補修正]A00-20191015001", "提交日期": "2019-12-09 16:06:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "33480fd023927cb5a38e759cc9018f87e64626f3", "commit_訊息": "S00-***********[補修正] 通知內容頁面顯示通知人", "提交日期": "2019-12-09 11:18:00", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "41277593b849d60ec665eae667719a178f02e1dd", "commit_訊息": "S00-20191108001[補修正] 固定開窗寬度", "提交日期": "2019-12-09 10:11:30", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/OpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "62da48940539b1b835d116b87c9a51a9887dd549", "commit_訊息": "Q00-20191208001 統一調整nana.database.type設定取出後的值為大寫(MSSQL/ORACLE)，判斷時請特別注意", "提交日期": "2019-12-08 23:14:31", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7644463e006ff983040056a7873ff0b32dc39b26", "commit_訊息": "C01-20191205003 打包時將bpm-tools.jar放至copyfiles\\@designer，提供debug時使用", "提交日期": "2019-12-08 22:43:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/properties.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2655e6949a37b043cc83860c140e26d54f9d6755", "commit_訊息": "Q00-20191204003[補修正] 移除重複的Code", "提交日期": "2019-12-06 09:57:43", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "144942fd1af2397c6e219a609805f24c7ab2b22d", "commit_訊息": "C01-20191204001 修正MAC電腦下載檔案時，無法呈現原始檔名", "提交日期": "2019-12-05 16:34:51", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AttachmentHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ProcessDocViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SignImageUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AttachmentViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormInstanceViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "3ebbcf349d00332d2c4e2484b0748d99b702fad9", "commit_訊息": "S00-***********[補修正] 調整SQL語法與通知發送人名稱的取得方法", "提交日期": "2019-12-05 11:17:16", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkAssignment.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3caaf195b1b2c29aa57e4fa27ac61f8b6fd4e8a8", "commit_訊息": "Q00-20191128006 調整TextBox與TextArea元件顯示資訊功能", "提交日期": "2019-12-04 20:13:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d39212355bfb1d1861380bcddf85ce1ae30ca611", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-12-04 20:07:16", "作者": "yamiyeh10", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "170478fb981cde21ad4d34d85898d4460b94218c", "commit_訊息": "Q00-20191129002 調整行動表單字體與配色樣式", "提交日期": "2019-12-04 20:06:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4d6d9e708bb5f08be3ddd1bd82d446d023ab8ce9", "commit_訊息": "將外部連結的頁面加上登出按鈕及登入者資訊", "提交日期": "2019-12-04 20:04:10", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "1dad2d435a4127f4819cd2ae9c9e774bbb62776a", "commit_訊息": "Q00-20191204003 修正如果新增的監控流程故意選已初始化會報錯，調整用監控起始日代替上次排成執行日", "提交日期": "2019-12-04 19:02:46", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c780e574de8f97d7673e6bd347a3c7ba5f369242", "commit_訊息": "[補修正3]A00-*********** 修正程式碼在監控流程的進階查詢中日期開窗會錯誤", "提交日期": "2019-12-04 17:48:00", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d5afa18ce218a398e6955d124c6da3f5b9cb85d5", "commit_訊息": "故加入識別碼，以區分被呼叫的appId AND 進來哪個排程服務", "提交日期": "2019-12-04 16:59:11", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/schedule/TimerFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6c90a8ecf467e5e678c64e4fc91da713aceaf95f", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-12-04 15:22:57", "作者": "peng_cheng_wang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c069456e8b4d8178de62fa3c583ecda499a4b5b8", "commit_訊息": "S00-*********** 工作通知清單顯示通知人及通知原因", "提交日期": "2019-12-04 15:13:49", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkAssignment.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "1cae1349eaaebe143357b477b113be8bd642bf25", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-12-04 15:11:11", "作者": "peng_cheng_wang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "fe78c92a55f07bc5f7a3a9f20b9634d298ef2d51", "commit_訊息": "A00-20191114001 修正在手機上用監控流程開表單ESSF21畫面會被截斷", "提交日期": "2019-12-04 15:09:08", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "519be9fa186150b735af782985891531f8227e43", "commit_訊息": "S00-20191108001[補修正] 修正人員名稱排序異常", "提交日期": "2019-12-04 11:09:01", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d77ab3f1a4d9e857e6c8510778632e0b0cef52e", "commit_訊息": "S00-20191108001[補修正] 修正IE無法正常顯示樹狀圖", "提交日期": "2019-12-04 11:02:08", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/TreeViewDataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4ef1d896581620d9bcc4c840dba9af31ef15b9c9", "commit_訊息": "A00-20191105001 修正流程主旨參考DialogInputLabel元件會額外顯示_null的情況", "提交日期": "2019-12-03 19:33:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "618fbf6da8f4e9a4c087a10120bd311fd0447353", "commit_訊息": "[補修正]Q00-20190826006 簡化程式碼", "提交日期": "2019-12-03 15:35:00", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "383d3cae2c2af45b91cd322527070e1b297c3c68", "commit_訊息": "[補修正]A00-20191015001", "提交日期": "2019-12-03 14:27:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "321aba57bdb1412628aabedc1d35b7fe7c86e6e5", "commit_訊息": "Q00-20190826006 修正password元件不能使用backspace問題", "提交日期": "2019-12-02 22:19:23", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e226eca7421a7a23d7c400887634d431a1f216e5", "commit_訊息": "A00-20191015001修正RWD表單的html特別字元未轉換", "提交日期": "2019-12-02 17:44:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "647ec7f03afb939033a7eaa7eed2aa9b441ad70a", "commit_訊息": "Q00-20191128003 二次調整附件管理按鈕在行動上機制", "提交日期": "2019-11-29 09:02:46", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb4f8844855369d5676d02dd9122960232f23575", "commit_訊息": "Q00-20191128004 調整IMG在詳請表單附件查看方式", "提交日期": "2019-11-28 19:30:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/jdajia.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "0210fabb59cab83f0d0ab6d28e90824856073b6c", "commit_訊息": "A00-20190514004 修正點簽核歷程資訊中右邊的「放大鏡」查看表單，表單上附件無法下載", "提交日期": "2019-11-28 17:40:14", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c9eff4cafbda2b19aa2c61bde112f729914b8044", "commit_訊息": "Q00-20190906001 再次調整BPM的PDFConverter 功能增加 BCL8 的轉檔機制", "提交日期": "2019-11-28 15:57:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18152634a44d97375b4ff141613395b7717113d4", "commit_訊息": "Q00-20191128003 調整附件管理按鈕在行動上機制", "提交日期": "2019-11-28 15:14:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b6d86c06ca30cafc1063f5ada052ba85bf764aee", "commit_訊息": "Q00-20191128002 修正附件管理按鈕在iPhone X手機上跑版問題", "提交日期": "2019-11-28 15:06:46", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "60e608e80e03373964d016eb175a57a7403fc8c9", "commit_訊息": "A00-20190927001 用外部連結URL至流程 , 顯示登出選項及使用者資訊", "提交日期": "2019-11-27 18:45:31", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f384c5c46495557979860651cc2e9bf39d35639f", "commit_訊息": "[補修正2]A00-*********** 調整程式碼", "提交日期": "2019-11-27 17:48:38", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5760e7a323bdf9f59dca1af4643254f4aa167ce2", "commit_訊息": "C01-*********** 調整TIPTOP流程的updateSQL，調整為只針對BPMN的TIPTOP流程作變更", "提交日期": "2019-11-27 15:26:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/*******_TIPTOP_DML_MSSQL_1_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/*******_TIPTOP_DML_MSSQL_2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/*******_TIPTOP_DML_Oracle_1_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@tiptop/update/*******_TIPTOP_DML_Oracle_2.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "8a3815c7bcc05deb053e6873b29531c63319e045", "commit_訊息": "S00-20191108001 二次更新", "提交日期": "2019-11-26 19:22:44", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/OpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a603d9ab91f9e143e8e3094f890bb40a19de2afd", "commit_訊息": "新增IMG移動端模組Action機制與IMG表單Action防止access_token過期風險", "提交日期": "2019-11-26 17:56:38", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "d98d3869a2b0e9ca7041995643716c1c549a2ead", "commit_訊息": "[補修正]A00-*********** 調整程式碼註解", "提交日期": "2019-11-26 17:51:12", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "477f1cac85f2ac77f8b4d088741b513fd62cd4e5", "commit_訊息": "Q00-20191122001 補修正，移入base", "提交日期": "2019-11-26 11:46:01", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@E10/update/5.7.6.4_E10_DML_MSSQL_1.sql", "修改狀態": "重新命名", "狀態代碼": "R086"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@E10/update/5.7.6.4_E10_DML_Oracle_1.sql", "修改狀態": "重新命名", "狀態代碼": "R086"}], "變更檔案數量": 2}, {"commit_hash": "0e10b9425437f7c1bee94f5a3d0ee532fefeb4e5", "commit_訊息": "Q00-20191126001 已刪除", "提交日期": "2019-11-26 11:33:36", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "398c064662531f7850b8d002d204635c5d303104", "commit_訊息": "Q00-20191125001 補上createSQL T100整合文檔中心Url的欄位", "提交日期": "2019-11-25 17:02:21", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "521a6e20ee82f7dcd7d80a46d21dbda614fc934c", "commit_訊息": "Q00-20191122001 開會決議Invoke呼叫RestFul參數Id固定 調整參數Id需為ProcessSN", "提交日期": "2019-11-22 17:15:23", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@E10/update/5.7.6.4_E10_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@E10/update/5.7.6.4_E10_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "8ecce5db5e11cf715983f3ae9b2f6935ff037234", "commit_訊息": "A00-*********** 修正在IE下使用日期開窗後頁面會跑到最上面", "提交日期": "2019-11-22 14:56:31", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7f35225f191944c26c117cb0b1cb2b61f9ca2d68", "commit_訊息": "S00-20191108001 人員樹狀開窗增加使用者輸入條件查詢", "提交日期": "2019-11-22 14:38:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2aae969841bd05c2899fad9dd4e1c6f672e1f225", "commit_訊息": "A00-20191106001 增加刪除附件時呼叫表單function deleteAttachmentByOriginalFileName()", "提交日期": "2019-11-21 19:06:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2fd6cb78e12c12c28532752e3d6de5dc10b04067", "commit_訊息": "Q00-20191121001 不管有沒有MCloud整合都會去呼叫MCloud 此接口不整合MCloud因此移除此段程式", "提交日期": "2019-11-21 18:48:42", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "403758ec5f59b0d6ce4ed948b807759d22b5fdbd", "commit_訊息": "A00-20191121001 將許多favorities -> favorites", "提交日期": "2019-11-21 18:39:13", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "461ae4cbf16cdc876c16834a96d5caca2cbd0007", "commit_訊息": "A00-20191121001 將許多favorities -> favorites", "提交日期": "2019-11-21 18:31:52", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ProcessModule/CreateProcessModule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "ede0e3f142140a26c8960f87beecd2fd2e2b4e43", "commit_訊息": "[補修正2]A00-*********** 補上import StringUtil.js", "提交日期": "2019-11-21 18:07:52", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a2138fb5eae061cbdb1f74b7ad802bd57f34f187", "commit_訊息": "新增Web端行動模擬簽核功能", "提交日期": "2019-11-21 17:07:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "36b2f76a7a96abd05f42d4f5d2ac77a166013dec", "commit_訊息": "C01-20191121002 修正ISO變更單載入上一版附件後，若刪除上一版附件時，grid未更新", "提交日期": "2019-11-21 15:31:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc26cc4a68c8b6218217a9c12d28f47a0de9f3e6", "commit_訊息": "調整移動端流程主旨跟簽核意見文字顏色", "提交日期": "2019-11-21 13:59:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "48d6d14e34900505fde5f7a849d93c2242c1a483", "commit_訊息": "新增移動端自動帶出近期簽核記錄功能", "提交日期": "2019-11-21 13:55:32", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "f2fbc9b4ac0a69782db5f82db1814ea8fc177582", "commit_訊息": "A00-20191112001 修正組織設計師抓取部門人員會一同顯示離職人員", "提交日期": "2019-11-21 12:57:15", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d2d279cf7ebfcd312ed078a53e0613c249865d6", "commit_訊息": "[補修正]A00-*********** 修正處理單引號的方式", "提交日期": "2019-11-18 19:28:37", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a857e705b2d4bf2ea9283938d8f7678efd8820e6", "commit_訊息": "S00-*********** 調整加簽形成關卡的執行率都為預設的100%", "提交日期": "2019-11-18 18:35:02", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b68b62ddd25f4f8286731d660790b02235df92b7", "commit_訊息": "A00-*********** 修正流程關卡名稱有單引號時在企業流程監控會錯", "提交日期": "2019-11-15 19:07:34", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fff676f931f82948e4ec8c9fc9e4298b552ee46f", "commit_訊息": "A00-*********** 調整多語系內容", "提交日期": "2019-11-15 12:39:59", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5764.xls", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "65f218bacd07567881e9ad1e46a1c6d000158b8f", "commit_訊息": "讓快搜功能支援透過鍵盤上下鍵選擇資料", "提交日期": "2019-11-14 10:35:18", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ccaaa881ed2b5364befd7ab740750943b6b326a8", "commit_訊息": "Q00-20191113001 修正WF整合問題 BPM回寫狀態修正與產生/更新表單功能，欄位都是黑的", "提交日期": "2019-11-13 18:59:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "196098422bb5397e7a6bd2604034e6628344aff6", "commit_訊息": "修正在地化無法點擊Server2的我的最愛到填單頁面", "提交日期": "2019-11-13 18:00:53", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MainMenuManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "af275c370a55e7186d58b8257e599c6e9e2e2e52", "commit_訊息": "調整將行動版絕對位置表單的預覽功能隱藏", "提交日期": "2019-11-12 17:06:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "24aa9ad3892d2ad16b815685230d7e4f86b0e0bd", "commit_訊息": "C01-20191106002 <二次修正> 還原誤簽上去的程式碼", "提交日期": "2019-11-12 16:50:35", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/BpmAppContact.css", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/BpmAppWorkMenu.css", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/BpmAppWorkMenuExtruded.css", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/FixMaterializeCss.css", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 6}, {"commit_hash": "2b8262f53bb661859a41838c255b9d193e41f2ef", "commit_訊息": "C01-20191106002 修正當附件檔名帶有$字號時會造成異常", "提交日期": "2019-11-12 15:44:30", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/AttachmentContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/BpmAppContact.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/BpmAppWorkMenu.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/BpmAppWorkMenuExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/FixAbsoluteFormStyle.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/FixMaterializeCss.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/FixMaterializeCssExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/V6_logo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/V6_logo_perform.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/WechatManagePage.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/bootstrap_3_3_7.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/bootstrap_4_0_0.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/addPostActivity.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/addPreActivity.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/announcement/announce_default.jpg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/announcement/announce_default_slide.jpg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/appOops.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/Bell_60x90.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/Float_return.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/StartUp.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/add_sign.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/agree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/announce.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/announce_dot.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/arrow_left.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/arrow_right.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/attach.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/attachment.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/cancel.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/common.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/confirm_retrieve.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/dot.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/double_left_icon.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/double_right_icon.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/end.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/ended.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/exclamation.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/favorite.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/file_thumbnail.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/go_back.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/important.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/infoicon.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/invoke_process.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/left_slide.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/light_sign_click.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/back.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/backto.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/backto_.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/contact.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/contact_.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/more_infor.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/notice.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/notice.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/setting.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/setting_.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/todo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/todo_.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/todo_dot.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/work.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/menu/work_.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/minus-symbol.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/minus.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/more.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/newLogo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/newNoticeItem.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/newTaskManage.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/newTraceProcess.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/newWorkItem.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/next_record.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/no_read.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/normal.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/notfavorite.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/plus.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/previous_record.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/process.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/process_folder.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/process_invoke.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/process_kind.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/process_notice.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/process_perform.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/process_trace.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/processing.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/read.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/remind_calendar.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/retrieve.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/return.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/revoke.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/right_agree.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/right_slide.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/round-add-button.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/running.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/save.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/save_form.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/search.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/send.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/sign.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/sign_.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/sign_click.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/sign_over.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/stop_process.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/todo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/trace_process.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/unread.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/common/urgent_only.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/contact/default_info.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/contact/email.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/contact/phone.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/doNoticeItem.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/doTaskManage.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/doTraceProcess.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/doWorkItem.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/draft_versions-38.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/draft_versions.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/efgpBigLogo.jpg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/error-page-image.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/historyDierect.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/historyDierect_before.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/historyDierect_select.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/historySequence.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/historySequence_before.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/mReexecuteAsNew.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/mReexecuteAsNew2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/mReexecuteFollow.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/mReexecuteFollow2.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/newCount.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/newDemo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/newLogo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/newNoticeItem.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/newTaskManage.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/newTraceProcess.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/newWorkItem.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/phonecall.gif", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/prsInsLvl_high.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/prsInsLvl_low.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/images/prsInsLvl_normal.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/material_Icons/2fcrYFNaTjcS6g4U3t-Y5RV6cRhDpPC5P4GCEJpqGoc.woff", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/material_Icons/material_Icons.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/mobile-UI-common.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/mobile-UI-icon.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/BpmAppContact.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/BpmAppWorkMenu.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/FixMaterializeCssExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/font-awesome.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/fonts/FontAwesome.otf", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/fonts/fontawesome-webfont.eot", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/fonts/fontawesome-webfont.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/fonts/fontawesome-webfont.ttf", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/fonts/fontawesome-webfont.woff", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/fonts/fontawesome-webfont.woff2", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/fonts/glyphicons-halflings-regular.woff2", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/bpmApp/v2/mobileSelect.css", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 155}, {"commit_hash": "7a394d67ba2f2ac639286f505f84dfd90097e240", "commit_訊息": "S00-20191105001 退回重辦的使用率比終止流程高，故移出。", "提交日期": "2019-11-08 17:38:05", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aaeaea20425d4c291600053927fd3b4ceb1d7dca", "commit_訊息": "C01-20191106001 修正IMG在進入待辦顯示錯誤頁面時隱藏上方導覽列防止點擊發生錯誤", "提交日期": "2019-11-08 15:09:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a84ea93561767667101a7659d69725c037f60b99", "commit_訊息": "A00-20191025001 修正一般使用者無法看到他人的重要流程維護", "提交日期": "2019-11-07 18:49:05", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e13b6d52eed8c0f74e8801cf6ed190aefe59571a", "commit_訊息": "表單透過Excel匯入Grid的上傳頁面樣式改成RWD頁面", "提交日期": "2019-11-06 14:14:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "af9390bdd133d83d86ba8a85018056e6292ad64d", "commit_訊息": "[補修正]C01-20190925001", "提交日期": "2019-11-06 14:03:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52aafbdde3afcbabcdeffccea9b0651034f79837", "commit_訊息": "A00-20191002002 修正有設定不可退回關卡有核決權限關卡，但在關卡的退回清單還是看到核決權限關卡", "提交日期": "2019-11-05 15:40:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e995b283e179887f3969765238a16e30721a8872", "commit_訊息": "C01-20190924003 於待辦事項中點選\"處理上一個工作\"後，會轉導至與清單順序不相符的待辦事項中", "提交日期": "2019-11-04 10:27:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d90b96decae35c2fa43274c7456f9f1572ab71d7", "commit_訊息": "[補修正]C01-20190925001", "提交日期": "2019-11-01 19:56:04", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "df724eb5cde19297cecdf4abbef0cfb9c7e94dd5", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-11-01 10:34:49", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "fe2d36683e0f7eda56e9c112a779baf4b20e003b", "commit_訊息": "C01-20190925001 修正IE匯入Excel會因為url過長導致出現錯誤訊息", "提交日期": "2019-11-01 10:34:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "14560b1485daac05e25c3f20b268af869fe09ae7", "commit_訊息": "C01-20190726003 修正WebService簽核歷程如果是多人並簽時終止/退回會有少資料", "提交日期": "2019-10-31 19:31:10", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/PerformDetail.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/PerformInfo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c0302f9ee479730ce0c40dd96bb06dcd4b1b6a6f", "commit_訊息": "C01-20191009001 修正BCL無法轉橫向的word文件(取消書籤功能，避免轉檔失敗)", "提交日期": "2019-10-31 17:17:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "af7b14551fba3b6c50adc8fd6b193217e98fc327", "commit_訊息": "C01-20191009001 修正BCL無法轉橫向的word文件", "提交日期": "2019-10-31 11:50:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "df0779cb4caba8e7b4f74ce51c8186ca90639a40", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-10-30 11:25:26", "作者": "詩雅", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "f218d2156a08b7db75abf89af43bfa1b3bca10e0", "commit_訊息": "C01-20191024005修改Grid自動生成按鈕，設定invisible時拋錯問題", "提交日期": "2019-10-30 11:11:53", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9dfa915dc32fd1afa98f55fab334c6fce7bedc75", "commit_訊息": "A00-20191008001 讓儲存上傳檔案的名稱的欄位以字元計算", "提交日期": "2019-10-30 11:02:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.4_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "9784df164baf52725e3686f6f64c29fbfcb633f8", "commit_訊息": "S00-20190808001 讓表單裡的時間元件可以自行輸入時間", "提交日期": "2019-10-29 16:26:26", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/RwdDateTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/RwdTimeTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "76dbd949752136b1e2c38736299ae3aca09bc4c1", "commit_訊息": "A00-20191024001 修正樹狀開窗切換分類後，再切回原本的分類時，會自動取消勾選原本的分類中已選擇的選項", "提交日期": "2019-10-29 15:49:25", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e75ca1025502593e28edaca3ffe8f601365cbbc8", "commit_訊息": "C01-20191023002 修正移動端Grid的欄位名稱有雙引號時會發生錯誤問題", "提交日期": "2019-10-29 10:02:03", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "06a30955210f10e871dee332390cd71ec8a9f41e", "commit_訊息": "Q00-20191028001 上傳附件問題", "提交日期": "2019-10-28 18:03:33", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}