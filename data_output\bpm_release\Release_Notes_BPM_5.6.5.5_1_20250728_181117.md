# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.5.5_1
- **舊版本**: 5.6.5.4_1
- **生成時間**: 2025-07-28 18:11:17
- **新增 Commit 數量**: 115

## 變更摘要

### 施廷緯 (16 commits)

- **2018-09-10 11:34:05**: A00-20180905001 修權限不一致問題，關卡流程表單頁面沒有設定權限，但是開啟Attachement按鈕卻可以看到附件的問題。
  - 變更檔案: 2 個
- **2018-08-30 14:44:20**: A00-20180829001 SQL註冊器的SQL語法卡控限制長度為兩千個字元。
  - 變更檔案: 3 個
- **2018-08-29 15:20:30**: A00-20180820001 修正 掛兩張表單，表單欄位都disable並在formopen寫入值無法將資料帶入下一個關卡問題。
  - 變更檔案: 1 個
- **2018-08-24 17:26:10**: C01-20180813002 修正設定個人資訊的代理期間Time元件開窗會偏移問題
  - 變更檔案: 1 個
- **2018-08-16 18:52:35**: C01-20180807002  修正 Textbox進階設定為浮點數且流程表單定義的Textbox為Invisible時，無法隱藏。
  - 變更檔案: 1 個
- **2018-08-09 14:45:32**: A00-20180530008  修正進階元件的多選開窗異常
  - 變更檔案: 1 個
- **2018-07-19 19:04:53**: 同Q00-20180620001 組織設計師如果使用者開啟人員多語系後按下Cancle或是關閉視窗，暫存將被清空。
  - 變更檔案: 3 個
- **2018-07-19 18:32:54**: C01-20180613003 修正表單設計師CheckBox與畫面呈現的不一致問題，主要是因為checkbox組成html時css已有規範<td>字體大小了。故將font-size放到<label>的Style內。
  - 變更檔案: 1 個
- **2018-07-16 18:06:04**: Q00-20180620001修改組織設計師按下多語系的開窗後，再次開啟沒有暫存問題。
  - 變更檔案: 2 個
- **2018-07-04 16:12:18**: Q00-20180704002 調整Forminstance.fieldValues的XML排版1.不會隨派送過程上下tag間距增加2.產生的XML的排版至左。
  - 變更檔案: 1 個
- **2018-06-19 15:26:12**: 同A00-20180606005 修正刪除子部門後，會發生找不到帳號的錯誤訊息。
  - 變更檔案: 3 個
- **2018-06-14 16:28:01**: A00-20180606005	新增備註及修改縮排問題
  - 變更檔案: 1 個
- **2018-06-13 20:06:24**: A00-20180606005 修正刪除子部門會發生找不到此帳號的錯誤訊息
  - 變更檔案: 2 個
- **2018-06-13 15:59:37**: 修正User在ProcessID少於兩碼無法發起流程問題。
  - 變更檔案: 1 個
- **2018-06-13 10:44:06**: 修正刪除子部門會發生找不到此帳號的錯誤訊息。
  - 變更檔案: 1 個
- **2018-06-13 10:37:25**: 修正HR小助手 檢查部門失效且離職人員有主部門條件(微調)
  - 變更檔案: 1 個

### joseph (6 commits)

- **2018-09-10 10:30:41**: 新增 : 調整流程序號長度 SQL
  - 變更檔案: 4 個
- **2018-09-10 10:27:43**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2018-09-10 10:27:16**: 新增: ESSQ92、ESSQ93 維護作業 SQL指令 及流程序號長度調整
  - 變更檔案: 4 個
- **2018-09-03 18:41:44**: C01-20180724004 <V56>調整 ESS發起流程 僅限IE顯示 儲存草稿功能
  - 變更檔案: 1 個
- **2018-06-29 10:06:43**: A00-20180416001 修正 : 當Grid欄位設定與日期元件代號相同,而且剛好先產生Grid資料後產生Date資料時,會導致 FormData XML 中 Grid的資料型態錯誤 ,導致轉存表單無法存入
  - 變更檔案: 5 個
- **2018-06-25 10:01:02**: Q00-20180619001 <V56>修正 : WebService fetchWorkFlowDiagram 接口 取URL不支援 Https
  - 變更檔案: 3 個

### 治傑 (16 commits)

- **2018-09-07 13:55:26**: 修正安裝mobile的oracle語法錯誤
  - 變更檔案: 1 個
- **2018-09-06 18:30:39**: 修正鼎捷移動發起流程連結缺少流程序號
  - 變更檔案: 1 個
- **2018-09-05 11:06:13**: A00-20180903001 修正行動版關卡權限Grid元件設為Invisible，單身明細在中間層仍會顯示
  - 變更檔案: 1 個
- **2018-09-03 18:34:22**: 調整APP發起連結從流程OID改為流程序號
  - 變更檔案: 9 個
- **2018-08-31 18:54:00**: 增加ESS表單在鼎捷移動平台中間層可以顯示並打開附件清單
  - 變更檔案: 2 個
- **2018-08-31 13:45:18**: 將5731的調整項目調整到56版 1.修正BPMApp未啟用時，提示畫面沒有多語系 2.修正行動版Gird沒綁定新增鈕時會出現undefined按鈕
  - 變更檔案: 3 個
- **2018-08-28 11:52:21**: 修正APP鼎捷移動端使用管理員配置圖表應用異常問題
  - 變更檔案: 1 個
- **2018-08-22 16:43:33**: 修正鼎捷移動Grid欄位過多時,無法顯示全部欄位
  - 變更檔案: 5 個
- **2018-08-21 18:47:50**: C01-20180816002 修正行動版Grid欄位過多時,無法顯示全部欄位
  - 變更檔案: 6 個
- **2018-07-30 09:38:54**: C01-20180724003 修正使用Android時TextArea元件無法滑動
  - 變更檔案: 1 個
- **2018-07-26 10:54:55**: C01-20180720006 修正行動版sql command元件無法進行update
  - 變更檔案: 1 個
- **2018-07-18 13:56:23**: 修正鼎捷移動中間層簽核後沒有返回到列表
  - 變更檔案: 1 個
- **2018-07-18 11:52:23**: A00-20180615003 修正鼎捷移動列表使用時間過濾異常
  - 變更檔案: 1 個
- **2018-07-18 10:37:07**: C01-20180713003 修正鼎捷移動使用中間層退回重辦時無簽核意見
  - 變更檔案: 1 個
- **2018-07-03 18:39:54**: C01-20180624007 修正使用Android點選客製開窗時，畫面會滑動
  - 變更檔案: 1 個
- **2018-06-25 17:32:43**: C01-20180620003 修正企業微信使用者管理頁面的啟用狀態顯示為2
  - 變更檔案: 1 個

### ChinRong (16 commits)

- **2018-09-07 11:47:44**: 調整行動版流程沒掛載表單的提示畫面
  - 變更檔案: 28 個
- **2018-09-06 17:50:08**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2018-09-06 17:49:50**: 修正議題
  - 變更檔案: 13 個
- **2018-09-03 18:15:27**: 將5731的調整項目調整到56版
  - 變更檔案: 13 個
- **2018-08-30 17:41:28**: 修正行動表單拉到最頂端或最底端無法滑動的問題
  - 變更檔案: 11 個
- **2018-08-29 18:57:55**: 將5731的調整項目調整到56版
  - 變更檔案: 58 個
- **2018-08-09 10:40:50**: 調整行動版表單設計器Label元件不支援中間層表單
  - 變更檔案: 1 個
- **2018-07-20 18:40:24**: 修正行動表單設計器多欄位但有空欄位時，整列會消失的問題
  - 變更檔案: 1 個
- **2018-07-17 14:19:10**: 調整APP的鼎捷移動推播消息跳轉至詳情應用
  - 變更檔案: 9 個
- **2018-07-17 10:18:29**: 修正議題
  - 變更檔案: 3 個
- **2018-07-10 15:58:12**: C01-20180704002 修正鼎捷移動處理的流程透過流程分類篩選後取不到資料的議題
  - 變更檔案: 1 個
- **2018-06-28 10:19:06**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2018-06-28 10:18:32**: 修正安卓手機打開表單會出現undefined is not a function的問題
  - 變更檔案: 2 個
- **2018-06-25 16:40:46**: A00-20180615004 修正鼎捷移動流程草稿取出會出現"工作取回失敗，請洽系統管理員"的議題
  - 變更檔案: 2 個
- **2018-06-15 17:52:55**: 修正議題
  - 變更檔案: 2 個
- **2018-06-13 18:46:42**: C01-20180514004 修正BPM重啟後使用企業微信登入App會取不到產品序號的問題
  - 變更檔案: 1 個

### yamiyeh10 (8 commits)

- **2018-09-06 17:48:00**: 修正議題 1.產品/客製開窗的"全選"、"全不選"英文語系過長 2.待辦的詳情表單畫面為灰底 3.ESS直連表單附件資訊空白
  - 變更檔案: 4 個
- **2018-09-06 16:00:08**: 修正議題 1.企業微信使用者管理頁面的啟用狀態顯示為2 2.入口平台整合設定中微信使用者管理搜尋功能異常
  - 變更檔案: 1 個
- **2018-08-30 14:29:05**: 將5731的調整項目調整到56版 1.調整微信與鼎捷的多語系 2.表單元件不足時背景設為灰底(絕對位置一樣是白底) 3.發起流程接口在未設計行動版時不會回寫
  - 變更檔案: 31 個
- **2018-08-27 18:07:39**: C01-20180816002 二次修正鼎捷移動與企業微信Grid欄位過多時無法顯示全部欄位
  - 變更檔案: 12 個
- **2018-07-19 18:20:13**: C01-20180719001 修正RadioButton與CheckBox元件初始值與選取值異常問題
  - 變更檔案: 1 個
- **2018-07-04 10:55:58**: A00-20180615004 修正草稿流程議題後導致發單畫面無法開啟問題
  - 變更檔案: 1 個
- **2018-06-28 10:27:49**: C01-20180626002 修正入口平台整合設定中微信使用者管理搜尋功能異常
  - 變更檔案: 2 個
- **2018-06-20 13:42:25**: C01-20180620001 修正因console未刪除導致用IE開啟行动签核管理中心時異常問題
  - 變更檔案: 1 個

### 顏伸儒 (22 commits)

- **2018-09-06 16:09:03**: A00-20180824001 修正BPM56版本,TIPTOP端原稿夾看不到可撤銷流程。
  - 變更檔案: 1 個
- **2018-09-05 16:46:19**: A00-20180830002 修正BPM56版本,在TT開單時解析xml裡轉換字元的部分加入防呆。
  - 變更檔案: 1 個
- **2018-08-21 13:46:47**: C01-20180814003-1 修正BPM56版本,Grid欄位是否有值都加入Grid的標籤。
  - 變更檔案: 1 個
- **2018-08-20 16:49:21**: C01-20180709002 修正BPM56版本,將多的錯誤提示窗註解掉。
  - 變更檔案: 1 個
- **2018-08-20 10:56:22**: C01-20180814003 修正BPM56版本,Grid欄位為空時加入Grid的標籤,才能將之前的資料覆蓋。
  - 變更檔案: 1 個
- **2018-08-14 16:51:32**: A00-20180704002 修正BPM56版本,從簡易流程圖輸入完密碼後無法正常跳關的問題。
  - 變更檔案: 2 個
- **2018-08-10 17:08:12**: C01-20180426002 修正BPM56版本,待辦清單最後一筆點擊處理下個工作會導回待辦清單的問題。
  - 變更檔案: 1 個
- **2018-08-06 17:08:01**: C01-20180620002 修正BPM56版本,表單和流程設計師欄位皆選擇驗證時,將html元件加入提示紅框。
  - 變更檔案: 2 個
- **2018-07-31 17:26:25**: A00-20180712001-2 修正BPM56版本,新增多語系的泰國語系改為en_TH,將原系統時間改回語系時間。
  - 變更檔案: 7 個
- **2018-07-26 14:28:34**: A00-20180625002 修正BPM56版本,流程取回重辦時工作通知的來源改為取回重辦。
  - 變更檔案: 1 個
- **2018-07-25 14:11:44**: A00-20180712001 修正BPM56版本,將追蹤、待辦、我的關注、撤銷流程、流程草稿、取回重辦、系統通知的時間格式改為系統時間。
  - 變更檔案: 6 個
- **2018-07-25 11:35:29**: Q00-20180725001 修正BPM56版本,若使用非系統本身支援的語系開啟流程設計師,就將語系設為英語系。
  - 變更檔案: 1 個
- **2018-07-24 11:44:30**: A00-20180510005 修正BPM56版本,Excel匯入開窗結果回傳值的判斷。
  - 變更檔案: 1 個
- **2018-07-17 19:23:23**: Q00-20180627001 修正BPM56版本,離職人員ID含有單引號開窗會發生異常。
  - 變更檔案: 2 個
- **2018-07-09 18:20:58**: S00-20180611005 公用片語管理的重設按鈕,易造成使用者混淆故刪除。
  - 變更檔案: 1 個
- **2018-07-02 12:12:14**: A00-20180510003 修正退回重瓣時,將取得參予者的資料加上防呆。
  - 變更檔案: 1 個
- **2018-06-28 19:30:17**: A00-20180628001 修正T100操作刪除簽核流程時會執行的LOG。
  - 變更檔案: 1 個
- **2018-06-27 14:28:37**: A00-20180625001 修改從T100端欲重建BPM流程時會執行到的判斷。
  - 變更檔案: 1 個
- **2018-06-26 16:34:36**: A00-20180620001 修正設定個人資訊裡的預設代理人設定,和流程代理人設定裡面的起始與結束時間選取異常。
  - 變更檔案: 2 個
- **2018-06-25 13:03:56**: A00-20180612001 修正查詢開窗輸入搜尋條件後,查詢的資料排序消失的問題。
  - 變更檔案: 1 個
- **2018-06-22 15:06:26**: [C01-20180530003]修改核決關卡沒有人簽核過,不顯示在退回重瓣的清單裡。
  - 變更檔案: 1 個
- **2018-06-13 17:50:22**: 修正向前(後)加簽關卡時無法正常發單的問題。
  - 變更檔案: 3 個

### walter_wu (7 commits)

- **2018-09-05 16:51:44**: A00-20180503001 修正系統權限管理員中可存取範圍無法查找到離職人員工號
  - 變更檔案: 1 個
- **2018-09-04 11:42:30**: A00-20180808001修正管理流程流程發起人或目前處理者開窗後，輸入查詢條件並按下查詢鈕後，視窗即變成空白
  - 變更檔案: 1 個
- **2018-08-23 11:50:35**: 優化ExcelImporter的匯入列資料更寬
  - 變更檔案: 1 個
- **2018-08-17 10:40:45**: C01-20180803002修改T100傳入傳出程式段，依據xml標準規範作字串替換
  - 變更檔案: 2 個
- **2018-08-10 11:05:20**: C01-20180706002修正prepareStatement使用過多未關閉造成SQLException問題
  - 變更檔案: 1 個
- **2018-08-07 14:39:36**: C01-20180713002修正前次修改IE無法使用的問題
  - 變更檔案: 2 個
- **2018-07-31 15:47:29**: C01-20180713002修正關卡設定必填簽核意見(繼續派送、退回重辦)時，空白也能通過
  - 變更檔案: 2 個

### waynechang (11 commits)

- **2018-08-23 14:32:22**: A00-20180823001 修正ESS單據發起後，沒有簽核關卡直接結案失效
  - 變更檔案: 1 個
- **2018-08-01 15:08:56**: A00-20180726001 修正流程關係人部門取表單欄位，該欄位有勾選前置組織代號，無法發起流程
  - 變更檔案: 1 個
- **2018-07-30 14:54:38**: A00-20180724001 修正流程掛三張表單並將一張表單設定唯讀並設定表單欄位為流程主旨時 會發生錯誤無法發單
  - 變更檔案: 1 個
- **2018-07-27 13:49:42**: A00-20180724005 修正ISO文件一覽表，查詢結果與查詢條件不一致
  - 變更檔案: 1 個
- **2018-06-25 17:15:50**: C01-20180529002 修正多重登入時記憶密碼功能異常
  - 變更檔案: 1 個
- **2018-06-25 14:18:19**: A00-20180615002 修正文件總管，搜尋文件打開閱讀後(沒閱讀不會異常)，在搜尋時會發生找不到任何文件
  - 變更檔案: 1 個
- **2018-06-25 13:43:30**: A00-20180615001 修正ISO文件權限屬性管理作業異常
  - 變更檔案: 1 個
- **2018-06-25 11:35:49**: A00-20180613001 修正核決關卡取回重辦功能判斷異常
  - 變更檔案: 1 個
- **2018-06-15 11:06:08**: C01-20180611002 - 移除不要的log
  - 變更檔案: 1 個
- **2018-06-15 11:00:21**: C01-20180611002 增加轉檔PDF需要有書籤、交互參照(超連結)的功能
  - 變更檔案: 3 個
- **2018-06-14 10:41:55**: A00-20180426001 修正admin及文管人員開啟檔案異常
  - 變更檔案: 1 個

### pinchi_lin (5 commits)

- **2018-08-21 17:03:28**: 調整詳情應用ID沒設定時，值給空字串
  - 變更檔案: 1 個
- **2018-08-21 16:35:17**: 修正鼎捷移動推播時向互連取使用者access token時失敗導致送出的token異常問題
  - 變更檔案: 1 個
- **2018-07-05 12:23:47**: A00-20180705001 修正鼎捷移動推播網址錯誤導致無法開啟表單畫面問題
  - 變更檔案: 1 個
- **2018-06-25 14:10:59**: C01-20180624008 修正發起時能發單但有ReferenceError:goMenu is not defined的錯誤
  - 變更檔案: 4 個
- **2018-06-13 14:51:49**: 新增鼎捷移動列表轉派資訊概要字段
  - 變更檔案: 1 個

### jerry1218 (5 commits)

- **2018-07-19 15:01:39**: 修正刪除註冊資訊後,tatolCount未減掉的問題
  - 變更檔案: 1 個
- **2018-07-18 17:17:22**: 調整mail排版(merge for V57)
  - 變更檔案: 1 個
- **2018-07-18 15:14:46**: 授權邏輯調整
  - 變更檔案: 3 個
- **2018-07-17 17:29:40**: Q00-20180717002 修正安裝密碼註冊-無法刪除不屬於此Mac的License(取消註冊失敗)
  - 變更檔案: 1 個
- **2018-07-13 15:29:47**: Q00-*********** Q00-*********** 修正借貨功能導致的異常 1.借貨邏輯異常 2.VIP所計算的總授權數異常(未考慮到期借貨) 3.借貨到期的情境administrator未強制導到註冊頁面 4.APP借貨到期依然可以登入
  - 變更檔案: 8 個

### jd (3 commits)

- **2018-07-12 15:10:33**: 修正互聯溝通格式增加屬性時，取使用者對照表會發生解析錯誤問題
  - 變更檔案: 1 個
- **2018-07-12 15:09:56**: 修正鼎捷移動服務，列表篩選功能的重要性沒有多語系
  - 變更檔案: 2 個
- **2018-07-12 14:54:37**: 因為互聯應用平台新增溝通參數,導致無法取得歸戶表而驗證失敗
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. A00-20180905001 修權限不一致問題，關卡流程表單頁面沒有設定權限，但是開啟Attachement按鈕卻可以看到附件的問題。
- **Commit ID**: `a5dc468f6037ff06a5eca1feb3166db18096633a`
- **作者**: 施廷緯
- **日期**: 2018-09-10 11:34:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AttachmentUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`

### 2. 新增 : 調整流程序號長度 SQL
- **Commit ID**: `a980b3ffdc3eb86aac9064ebd73d81211fa9aff4`
- **作者**: joseph
- **日期**: 2018-09-10 10:30:41
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.5_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.5_updateSQL_SQLServer.sql`

### 3. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `1d1e03964780e1ac369e206eb923ef27f15f4130`
- **作者**: joseph
- **日期**: 2018-09-10 10:27:43
- **變更檔案數量**: 0

### 4. 新增: ESSQ92、ESSQ93 維護作業 SQL指令 及流程序號長度調整
- **Commit ID**: `454f08323c69d42f1a26f730f281ea497a4bea09`
- **作者**: joseph
- **日期**: 2018-09-10 10:27:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.6.5.5_AppForm_UpdateSQL_ORACLE.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.6.5.5_AppForm_UpdateSQL_SQLServer.sql`

### 5. 修正安裝mobile的oracle語法錯誤
- **Commit ID**: `21b8c3fcb01137511f4e814ad51d7476ad230ced`
- **作者**: 治傑
- **日期**: 2018-09-07 13:55:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`

### 6. 調整行動版流程沒掛載表單的提示畫面
- **Commit ID**: `35e119a747f8e3cf985accaef165309ecb07f07c`
- **作者**: ChinRong
- **日期**: 2018-09-07 11:47:44
- **變更檔案數量**: 28
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5655.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/error-page-image.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 7. 修正鼎捷移動發起流程連結缺少流程序號
- **Commit ID**: `6565f20a70b69487fca8f207714578a110a831fc`
- **作者**: 治傑
- **日期**: 2018-09-06 18:30:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 8. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `67abee5f9bc8d95432ead6d20beb2acbafd7fc4e`
- **作者**: ChinRong
- **日期**: 2018-09-06 17:50:08
- **變更檔案數量**: 0

### 9. 修正議題
- **Commit ID**: `a7389359858ad8cea9561255ff6386b67add3fa2`
- **作者**: ChinRong
- **日期**: 2018-09-06 17:49:50
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 10. 修正議題 1.產品/客製開窗的"全選"、"全不選"英文語系過長 2.待辦的詳情表單畫面為灰底 3.ESS直連表單附件資訊空白
- **Commit ID**: `bff3db21dd5dbff7bcc973c8d9ca31878b072f9a`
- **作者**: yamiyeh10
- **日期**: 2018-09-06 17:48:00
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5655.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 11. A00-20180824001 修正BPM56版本,TIPTOP端原稿夾看不到可撤銷流程。
- **Commit ID**: `cdd8baedca96641cbbf0098faf6f67748bd3924a`
- **作者**: 顏伸儒
- **日期**: 2018-09-06 16:09:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 12. 修正議題 1.企業微信使用者管理頁面的啟用狀態顯示為2 2.入口平台整合設定中微信使用者管理搜尋功能異常
- **Commit ID**: `c688d905a0d346ba1ce9cb90029b4e48d5ee0390`
- **作者**: yamiyeh10
- **日期**: 2018-09-06 16:00:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 13. A00-20180503001 修正系統權限管理員中可存取範圍無法查找到離職人員工號
- **Commit ID**: `ec0e123f22c998220bf4233863e6b0524b8541a7`
- **作者**: walter_wu
- **日期**: 2018-09-05 16:51:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/sys-authority/src/com/dsc/nana/user_interface/apps/authority/view/ChooseTargetDialog.java`

### 14. A00-20180830002 修正BPM56版本,在TT開單時解析xml裡轉換字元的部分加入防呆。
- **Commit ID**: `5c8b558705a1946ad48bdf77c6e6968a31bed468`
- **作者**: 顏伸儒
- **日期**: 2018-09-05 16:46:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 15. A00-20180903001 修正行動版關卡權限Grid元件設為Invisible，單身明細在中間層仍會顯示
- **Commit ID**: `595f80770231cd55e85d6a3f69e2b323b09ef2f2`
- **作者**: 治傑
- **日期**: 2018-09-05 11:06:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 16. A00-20180808001修正管理流程流程發起人或目前處理者開窗後，輸入查詢條件並按下查詢鈕後，視窗即變成空白
- **Commit ID**: `0a411718fd1880fc0227b66fdd9935e493011d5d`
- **作者**: walter_wu
- **日期**: 2018-09-04 11:42:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 17. C01-20180724004 <V56>調整 ESS發起流程 僅限IE顯示 儲存草稿功能
- **Commit ID**: `c4aa485f005ac2e8983f88c42fffc6261526db58`
- **作者**: joseph
- **日期**: 2018-09-03 18:41:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 18. 調整APP發起連結從流程OID改為流程序號
- **Commit ID**: `2996fdd8654801ee5f13597381e1d20550eb3479`
- **作者**: 治傑
- **日期**: 2018-09-03 18:34:22
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`

### 19. 將5731的調整項目調整到56版
- **Commit ID**: `b8d9a62bf3c5e507b74de3ff03f73e55abbb4837`
- **作者**: ChinRong
- **日期**: 2018-09-03 18:15:27
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 20. 增加ESS表單在鼎捷移動平台中間層可以顯示並打開附件清單
- **Commit ID**: `80312f0410af1325be6de5cc3b23df8713539344`
- **作者**: 治傑
- **日期**: 2018-08-31 18:54:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 21. 將5731的調整項目調整到56版 1.修正BPMApp未啟用時，提示畫面沒有多語系 2.修正行動版Gird沒綁定新增鈕時會出現undefined按鈕
- **Commit ID**: `c6f39a96a7160712ee82488c3e9a22b3d167de12`
- **作者**: 治傑
- **日期**: 2018-08-31 13:45:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 22. 修正行動表單拉到最頂端或最底端無法滑動的問題
- **Commit ID**: `1bb2ed4f028b0488c7426162a39e2b121d57d98e`
- **作者**: ChinRong
- **日期**: 2018-08-30 17:41:28
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 23. A00-20180829001 SQL註冊器的SQL語法卡控限制長度為兩千個字元。
- **Commit ID**: `584d2385b60553f39cd5ea223285b65b8a1080ee`
- **作者**: 施廷緯
- **日期**: 2018-08-30 14:44:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5655.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`

### 24. 將5731的調整項目調整到56版 1.調整微信與鼎捷的多語系 2.表單元件不足時背景設為灰底(絕對位置一樣是白底) 3.發起流程接口在未設計行動版時不會回寫
- **Commit ID**: `76c5fdf0515a1dd72818a9ce8f6d7d3564e1c692`
- **作者**: yamiyeh10
- **日期**: 2018-08-30 14:29:05
- **變更檔案數量**: 31
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5655.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 25. 將5731的調整項目調整到56版
- **Commit ID**: `0edd19c0e8ff6d303caf902870964ffd64d1f7ca`
- **作者**: ChinRong
- **日期**: 2018-08-29 18:57:55
- **變更檔案數量**: 58
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileInvokableProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5655.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileDatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmApp.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileIntegrate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.2_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_Oracle.sql`

### 26. A00-20180820001 修正 掛兩張表單，表單欄位都disable並在formopen寫入值無法將資料帶入下一個關卡問題。
- **Commit ID**: `5c39c740dd2d64783ac396d8f0e8de40f360d4a3`
- **作者**: 施廷緯
- **日期**: 2018-08-29 15:20:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 27. 修正APP鼎捷移動端使用管理員配置圖表應用異常問題
- **Commit ID**: `24ec1aca5cc003cb1aeb8b730e647086c4ac7576`
- **作者**: 治傑
- **日期**: 2018-08-28 11:52:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 28. C01-20180816002 二次修正鼎捷移動與企業微信Grid欄位過多時無法顯示全部欄位
- **Commit ID**: `2cc7647a8106bca9088d4251607eb2a2ef6428e9`
- **作者**: yamiyeh10
- **日期**: 2018-08-27 18:07:39
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 29. C01-20180813002 修正設定個人資訊的代理期間Time元件開窗會偏移問題
- **Commit ID**: `4a4891e413474a798de9a2b7e4e8ffb875bfecb6`
- **作者**: 施廷緯
- **日期**: 2018-08-24 17:26:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/popup.js`

### 30. A00-20180823001 修正ESS單據發起後，沒有簽核關卡直接結案失效
- **Commit ID**: `b7e2911a5eff91f5233b77cdc6828236c2946107`
- **作者**: waynechang
- **日期**: 2018-08-23 14:32:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 31. 優化ExcelImporter的匯入列資料更寬
- **Commit ID**: `aff187abbf5d1f21dbcafa26cc265aad52f36e2a`
- **作者**: walter_wu
- **日期**: 2018-08-23 11:50:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 32. 修正鼎捷移動Grid欄位過多時,無法顯示全部欄位
- **Commit ID**: `22743bb32b88aaffbafceebccf5f2bb8d8a6d338`
- **作者**: 治傑
- **日期**: 2018-08-22 16:43:33
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`

### 33. C01-20180816002 修正行動版Grid欄位過多時,無法顯示全部欄位
- **Commit ID**: `d0d58a1de55cb80e94ca800cfb2efcc130ca2805`
- **作者**: 治傑
- **日期**: 2018-08-21 18:47:50
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 34. 調整詳情應用ID沒設定時，值給空字串
- **Commit ID**: `581d58ade983b26229443c354bf9d5fbb1dceabd`
- **作者**: pinchi_lin
- **日期**: 2018-08-21 17:03:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 35. 修正鼎捷移動推播時向互連取使用者access token時失敗導致送出的token異常問題
- **Commit ID**: `3465457019061d4003a14e035fcf6f5d60070b47`
- **作者**: pinchi_lin
- **日期**: 2018-08-21 16:35:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 36. C01-20180814003-1 修正BPM56版本,Grid欄位是否有值都加入Grid的標籤。
- **Commit ID**: `efeea3ef75658f1e8de83a1056bbf2655892751a`
- **作者**: 顏伸儒
- **日期**: 2018-08-21 13:46:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 37. C01-20180709002 修正BPM56版本,將多的錯誤提示窗註解掉。
- **Commit ID**: `93c92da24f9e4804191fa85d62c4e2cf5eac05d4`
- **作者**: 顏伸儒
- **日期**: 2018-08-20 16:49:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 38. C01-20180814003 修正BPM56版本,Grid欄位為空時加入Grid的標籤,才能將之前的資料覆蓋。
- **Commit ID**: `92ceefef90fa74890d7dad58ec519d7ba2636b10`
- **作者**: 顏伸儒
- **日期**: 2018-08-20 10:56:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 39. C01-20180803002修改T100傳入傳出程式段，依據xml標準規範作字串替換
- **Commit ID**: `86f6aa410c2c73a84a54d569fa5be7ccd7d347cc`
- **作者**: walter_wu
- **日期**: 2018-08-17 10:40:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopUtil.java`

### 40. C01-20180807002  修正 Textbox進階設定為浮點數且流程表單定義的Textbox為Invisible時，無法隱藏。
- **Commit ID**: `d5e32bd227bba226a208900b61e42d0747c5f4d6`
- **作者**: 施廷緯
- **日期**: 2018-08-16 18:52:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 41. A00-20180704002 修正BPM56版本,從簡易流程圖輸入完密碼後無法正常跳關的問題。
- **Commit ID**: `7f29aec0ddf4b8cdad053a268bb6ab4cae1695db`
- **作者**: 顏伸儒
- **日期**: 2018-08-14 16:51:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordForByPass.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceParticipantActivity.jsp`

### 42. C01-20180426002 修正BPM56版本,待辦清單最後一筆點擊處理下個工作會導回待辦清單的問題。
- **Commit ID**: `1032f4a96136811b6f49e5cc190d249f9c0babd8`
- **作者**: 顏伸儒
- **日期**: 2018-08-10 17:08:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 43. C01-20180706002修正prepareStatement使用過多未關閉造成SQLException問題
- **Commit ID**: `119232914145b6e373db5183f581517e24656541`
- **作者**: walter_wu
- **日期**: 2018-08-10 11:05:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 44. A00-20180530008  修正進階元件的多選開窗異常
- **Commit ID**: `b30f4fb51d0605e7e0be0a793a2a30064b66ddd8`
- **作者**: 施廷緯
- **日期**: 2018-08-09 14:45:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`

### 45. 調整行動版表單設計器Label元件不支援中間層表單
- **Commit ID**: `7512f2871541b169ad187b5d0e2e644d0bca9bec`
- **作者**: ChinRong
- **日期**: 2018-08-09 10:40:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 46. C01-20180713002修正前次修改IE無法使用的問題
- **Commit ID**: `ec13e5a1c4207dbbabeba3c902094083d63e8cb5`
- **作者**: walter_wu
- **日期**: 2018-08-07 14:39:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 47. C01-20180620002 修正BPM56版本,表單和流程設計師欄位皆選擇驗證時,將html元件加入提示紅框。
- **Commit ID**: `f658b8e5cfe6a48297a71f5b0f7424bb3e99f976`
- **作者**: 顏伸儒
- **日期**: 2018-08-06 17:08:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 48. A00-20180726001 修正流程關係人部門取表單欄位，該欄位有勾選前置組織代號，無法發起流程
- **Commit ID**: `96760f00f3fe93ae8df4bf3dc8d71aec4997f175`
- **作者**: waynechang
- **日期**: 2018-08-01 15:08:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 49. A00-20180712001-2 修正BPM56版本,新增多語系的泰國語系改為en_TH,將原系統時間改回語系時間。
- **Commit ID**: `c4ebad07cf85d0f9ce52ad5f372343faa5c9e9ff`
- **作者**: 顏伸儒
- **日期**: 2018-07-31 17:26:25
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AbortProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java`

### 50. C01-20180713002修正關卡設定必填簽核意見(繼續派送、退回重辦)時，空白也能通過
- **Commit ID**: `9ac26fde67706a564c0117b4ff95941898925474`
- **作者**: walter_wu
- **日期**: 2018-07-31 15:47:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 51. A00-20180724001 修正流程掛三張表單並將一張表單設定唯讀並設定表單欄位為流程主旨時 會發生錯誤無法發單
- **Commit ID**: `7051fba139534a0bcd5785b5a950a33e25788b48`
- **作者**: waynechang
- **日期**: 2018-07-30 14:54:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 52. C01-20180724003 修正使用Android時TextArea元件無法滑動
- **Commit ID**: `cb7403374c9ca06a4d5c8df1b65256a66ca883d5`
- **作者**: 治傑
- **日期**: 2018-07-30 09:38:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 53. A00-20180724005 修正ISO文件一覽表，查詢結果與查詢條件不一致
- **Commit ID**: `1a936e56db412cb8251056cf9ec5dd63f1b2a9f5`
- **作者**: waynechang
- **日期**: 2018-07-27 13:49:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java`

### 54. A00-20180625002 修正BPM56版本,流程取回重辦時工作通知的來源改為取回重辦。
- **Commit ID**: `67f0c0d8d127f1129989b790756a6b7913f069a6`
- **作者**: 顏伸儒
- **日期**: 2018-07-26 14:28:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 55. C01-20180720006 修正行動版sql command元件無法進行update
- **Commit ID**: `6dd7373dbd2246b4206e4c52776f21d1a2ff8b10`
- **作者**: 治傑
- **日期**: 2018-07-26 10:54:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/dsMobile.js`

### 56. A00-20180712001 修正BPM56版本,將追蹤、待辦、我的關注、撤銷流程、流程草稿、取回重辦、系統通知的時間格式改為系統時間。
- **Commit ID**: `f3f4f6403f229e6f39fe9dacb110311f148019ff`
- **作者**: 顏伸儒
- **日期**: 2018-07-25 14:11:44
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AbortProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 57. Q00-20180725001 修正BPM56版本,若使用非系統本身支援的語系開啟流程設計師,就將語系設為英語系。
- **Commit ID**: `35f49b376d7ed6aaf6c40f20a577d3828af17b11`
- **作者**: 顏伸儒
- **日期**: 2018-07-25 11:35:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`

### 58. A00-20180510005 修正BPM56版本,Excel匯入開窗結果回傳值的判斷。
- **Commit ID**: `ea46d51a2ead64817721530c841674e214057b0b`
- **作者**: 顏伸儒
- **日期**: 2018-07-24 11:44:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 59. 修正行動表單設計器多欄位但有空欄位時，整列會消失的問題
- **Commit ID**: `d7cfd5ac090451bba3a87911ec9aaf0c1d392450`
- **作者**: ChinRong
- **日期**: 2018-07-20 18:40:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 60. 同Q00-20180620001 組織設計師如果使用者開啟人員多語系後按下Cancle或是關閉視窗，暫存將被清空。
- **Commit ID**: `1114852d591269c11687232d0d136d9d6deaf7ad`
- **作者**: 施廷緯
- **日期**: 2018-07-19 19:04:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/AbstractDesignerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/UserNameMultiLanguageDialog.java`

### 61. C01-20180613003 修正表單設計師CheckBox與畫面呈現的不一致問題，主要是因為checkbox組成html時css已有規範<td>字體大小了。故將font-size放到<label>的Style內。
- **Commit ID**: `ac0f03609d98d23cf7882037433ffc8bac910d70`
- **作者**: 施廷緯
- **日期**: 2018-07-19 18:32:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 62. C01-20180719001 修正RadioButton與CheckBox元件初始值與選取值異常問題
- **Commit ID**: `d3f28a9d40e45993090deece460ea57b5f73701a`
- **作者**: yamiyeh10
- **日期**: 2018-07-19 18:20:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 63. 修正刪除註冊資訊後,tatolCount未減掉的問題
- **Commit ID**: `7ed3010e9f70fc3bed4a6525a0f326300c990c61`
- **作者**: jerry1218
- **日期**: 2018-07-19 15:01:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`

### 64. 調整mail排版(merge for V57)
- **Commit ID**: `9de6380313d1a0b371dc519934a3255db3d91923`
- **作者**: jerry1218
- **日期**: 2018-07-18 17:17:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 65. 授權邏輯調整
- **Commit ID**: `398b51b893bbe2c9b7fd33012178871bf4e17def`
- **作者**: jerry1218
- **日期**: 2018-07-18 15:14:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 66. 修正鼎捷移動中間層簽核後沒有返回到列表
- **Commit ID**: `914c7ea593cde49871898ec34cf811f4299fe3ee`
- **作者**: 治傑
- **日期**: 2018-07-18 13:56:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationButtonRes.java`

### 67. A00-20180615003 修正鼎捷移動列表使用時間過濾異常
- **Commit ID**: `c616f6aff1158c8f70cd718cf13318e0e18e737b`
- **作者**: 治傑
- **日期**: 2018-07-18 11:52:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 68. C01-20180713003 修正鼎捷移動使用中間層退回重辦時無簽核意見
- **Commit ID**: `4d492fd3bfe5a7c03a2e223ae2a69c8ef84aa871`
- **作者**: 治傑
- **日期**: 2018-07-18 10:37:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 69. Q00-20180627001 修正BPM56版本,離職人員ID含有單引號開窗會發生異常。
- **Commit ID**: `225cb2f7ab36837ac03af561eba3c43335dab5e4`
- **作者**: 顏伸儒
- **日期**: 2018-07-17 19:23:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/ResultObjectForDataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`

### 70. Q00-20180717002 修正安裝密碼註冊-無法刪除不屬於此Mac的License(取消註冊失敗)
- **Commit ID**: `babae1303965b0ee2bfc6e3a2342e76601d3f376`
- **作者**: jerry1218
- **日期**: 2018-07-17 17:29:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`

### 71. 調整APP的鼎捷移動推播消息跳轉至詳情應用
- **Commit ID**: `b266326eaa4793372628ffb2ff6aef8dd5a0f9e6`
- **作者**: ChinRong
- **日期**: 2018-07-17 14:19:10
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5654.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 72. 修正議題
- **Commit ID**: `522aacf3fcd6d1d2085bf0c9ea935614ffa88bec`
- **作者**: ChinRong
- **日期**: 2018-07-17 10:18:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 73. Q00-20180620001修改組織設計師按下多語系的開窗後，再次開啟沒有暫存問題。
- **Commit ID**: `276399340b7936262a77263532ac488d2c2aec69`
- **作者**: 施廷緯
- **日期**: 2018-07-16 18:06:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/AbstractDesignerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`

### 74. Q00-*********** Q00-*********** 修正借貨功能導致的異常 1.借貨邏輯異常 2.VIP所計算的總授權數異常(未考慮到期借貨) 3.借貨到期的情境administrator未強制導到註冊頁面 4.APP借貨到期依然可以登入
- **Commit ID**: `1715b6d998dd38d9ea542ebb1b269c01f24fa3fa`
- **作者**: jerry1218
- **日期**: 2018-07-13 15:29:47
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/license/ModuleKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 75. 修正互聯溝通格式增加屬性時，取使用者對照表會發生解析錯誤問題
- **Commit ID**: `a5cc0e6c385916c78f0d44a55a18790de78048ce`
- **作者**: jd
- **日期**: 2018-07-12 15:10:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`

### 76. 修正鼎捷移動服務，列表篩選功能的重要性沒有多語系
- **Commit ID**: `2d547ecd2054f69d6a582626e0bda315844c9e06`
- **作者**: jd
- **日期**: 2018-07-12 15:09:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BPMPerformRequestTool.java`

### 77. 因為互聯應用平台新增溝通參數,導致無法取得歸戶表而驗證失敗
- **Commit ID**: `139a7d58b4b2697312bf070b1bbf37a6037d7f12`
- **作者**: jd
- **日期**: 2018-07-12 14:54:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/RemoteUser.java`

### 78. C01-20180704002 修正鼎捷移動處理的流程透過流程分類篩選後取不到資料的議題
- **Commit ID**: `90abe0206c87117fcd22899c473a01ebd87c9d4f`
- **作者**: ChinRong
- **日期**: 2018-07-10 15:58:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 79. S00-20180611005 公用片語管理的重設按鈕,易造成使用者混淆故刪除。
- **Commit ID**: `5557be7da2f7ca4cb34ef37e3c8635b1d45f64f0`
- **作者**: 顏伸儒
- **日期**: 2018-07-09 18:20:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`

### 80. A00-20180705001 修正鼎捷移動推播網址錯誤導致無法開啟表單畫面問題
- **Commit ID**: `81e1c0063133eb0c95bce1cf3a7d2a0a0402896e`
- **作者**: pinchi_lin
- **日期**: 2018-07-05 12:23:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 81. Q00-20180704002 調整Forminstance.fieldValues的XML排版1.不會隨派送過程上下tag間距增加2.產生的XML的排版至左。
- **Commit ID**: `eafd551e32dc16f655417cf6b185dec7cf7b3644`
- **作者**: 施廷緯
- **日期**: 2018-07-04 16:12:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/Dom4jUtil.java`

### 82. A00-20180615004 修正草稿流程議題後導致發單畫面無法開啟問題
- **Commit ID**: `e4eaa100fac81384c9cbb7e5d8e5e6d768b0b0e0`
- **作者**: yamiyeh10
- **日期**: 2018-07-04 10:55:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 83. C01-20180624007 修正使用Android點選客製開窗時，畫面會滑動
- **Commit ID**: `25bbabc4079630d87c3854d4883e9a8fa2e98c70`
- **作者**: 治傑
- **日期**: 2018-07-03 18:39:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 84. A00-20180510003 修正退回重瓣時,將取得參予者的資料加上防呆。
- **Commit ID**: `a0dd41499e6d986d31511431bb3da91d4a925f6c`
- **作者**: 顏伸儒
- **日期**: 2018-07-02 12:12:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 85. A00-20180416001 修正 : 當Grid欄位設定與日期元件代號相同,而且剛好先產生Grid資料後產生Date資料時,會導致 FormData XML 中 Grid的資料型態錯誤 ,導致轉存表單無法存入
- **Commit ID**: `fab061043bc74d1460357ae2d5d611f2108d31b7`
- **作者**: joseph
- **日期**: 2018-06-29 10:06:43
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/BarcodeElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 86. A00-20180628001 修正T100操作刪除簽核流程時會執行的LOG。
- **Commit ID**: `0030aa8e103af9d53f4d9aa532c56ace2d9ed395`
- **作者**: 顏伸儒
- **日期**: 2018-06-28 19:30:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPIBean.java`

### 87. C01-20180626002 修正入口平台整合設定中微信使用者管理搜尋功能異常
- **Commit ID**: `46f5aadb6882b55533a9745a050bb9b94189b3d1`
- **作者**: yamiyeh10
- **日期**: 2018-06-28 10:27:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 88. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `41c0af1b103945938740ec8905b6cc2f388643a5`
- **作者**: ChinRong
- **日期**: 2018-06-28 10:19:06
- **變更檔案數量**: 0

### 89. 修正安卓手機打開表單會出現undefined is not a function的問題
- **Commit ID**: `7f3dcbf71df20738e049b26946916e641bd42cc8`
- **作者**: ChinRong
- **日期**: 2018-06-28 10:18:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 90. A00-20180625001 修改從T100端欲重建BPM流程時會執行到的判斷。
- **Commit ID**: `251580775083f7e737105a79139c4d14b4cc0680`
- **作者**: 顏伸儒
- **日期**: 2018-06-27 14:28:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 91. A00-20180620001 修正設定個人資訊裡的預設代理人設定,和流程代理人設定裡面的起始與結束時間選取異常。
- **Commit ID**: `2bab34c215358e1568b21a38afa09d0c8209ac68`
- **作者**: 顏伸儒
- **日期**: 2018-06-26 16:34:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`

### 92. C01-20180620003 修正企業微信使用者管理頁面的啟用狀態顯示為2
- **Commit ID**: `7f56e9748a4a3e147b49913c0a4a5f0af6d6115e`
- **作者**: 治傑
- **日期**: 2018-06-25 17:32:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 93. C01-20180529002 修正多重登入時記憶密碼功能異常
- **Commit ID**: `9ab342cec37e9b55e2a349460d572ebb31684cc5`
- **作者**: waynechang
- **日期**: 2018-06-25 17:15:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 94. A00-20180615004 修正鼎捷移動流程草稿取出會出現"工作取回失敗，請洽系統管理員"的議題
- **Commit ID**: `f2716d4d2ce1e1352beee225cfa77e4576a7de0f`
- **作者**: ChinRong
- **日期**: 2018-06-25 16:40:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`

### 95. A00-20180615002 修正文件總管，搜尋文件打開閱讀後(沒閱讀不會異常)，在搜尋時會發生找不到任何文件
- **Commit ID**: `202a015168c1a27301271fad80d179c1bffd22c7`
- **作者**: waynechang
- **日期**: 2018-06-25 14:18:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 96. C01-20180624008 修正發起時能發單但有ReferenceError:goMenu is not defined的錯誤
- **Commit ID**: `f775a041dcd7739a935b74cdefe0aac3692215c2`
- **作者**: pinchi_lin
- **日期**: 2018-06-25 14:10:59
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`

### 97. A00-20180615001 修正ISO文件權限屬性管理作業異常
- **Commit ID**: `fe8ca4724bcee231192b526b417cdbd29721ab90`
- **作者**: waynechang
- **日期**: 2018-06-25 13:43:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageAccessRightAction.java`

### 98. A00-20180612001 修正查詢開窗輸入搜尋條件後,查詢的資料排序消失的問題。
- **Commit ID**: `9b96e68cf6d201091981ea4f02c331021679106d`
- **作者**: 顏伸儒
- **日期**: 2018-06-25 13:03:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 99. A00-20180613001 修正核決關卡取回重辦功能判斷異常
- **Commit ID**: `8b0d830fe44383bb8fb59adc3e981f809c3f5d14`
- **作者**: waynechang
- **日期**: 2018-06-25 11:35:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 100. Q00-20180619001 <V56>修正 : WebService fetchWorkFlowDiagram 接口 取URL不支援 Https
- **Commit ID**: `ffd3e17be8af30559c4615c32689e5451c44dc2e`
- **作者**: joseph
- **日期**: 2018-06-25 10:01:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/SystemConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopModelManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`

### 101. [C01-20180530003]修改核決關卡沒有人簽核過,不顯示在退回重瓣的清單裡。
- **Commit ID**: `38cf01dc70ce8063c59d5abda253aa8f3209b068`
- **作者**: 顏伸儒
- **日期**: 2018-06-22 15:06:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java`

### 102. C01-20180620001 修正因console未刪除導致用IE開啟行动签核管理中心時異常問題
- **Commit ID**: `cc6db9a3ee0e4fc58c449172efc0e03d5e57bc5d`
- **作者**: yamiyeh10
- **日期**: 2018-06-20 13:42:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`

### 103. 同A00-20180606005 修正刪除子部門後，會發生找不到帳號的錯誤訊息。
- **Commit ID**: `301e778abdbf9834b21eead1d9a3c43946ead7ad`
- **作者**: 施廷緯
- **日期**: 2018-06-19 15:26:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`

### 104. 修正議題
- **Commit ID**: `90ebfec30d44a8b54ab0c6a7819ff5dc0c63e03f`
- **作者**: ChinRong
- **日期**: 2018-06-15 17:52:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 105. C01-20180611002 - 移除不要的log
- **Commit ID**: `e49a46260933020a218371bb8d1583e7b5101fe7`
- **作者**: waynechang
- **日期**: 2018-06-15 11:06:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 106. C01-20180611002 增加轉檔PDF需要有書籤、交互參照(超連結)的功能
- **Commit ID**: `b2f97957e333c61c97057af980ea552fc37049bb`
- **作者**: waynechang
- **日期**: 2018-06-15 11:00:21
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 107. A00-20180606005	新增備註及修改縮排問題
- **Commit ID**: `44c5656056ac64ce1c2dca95a75b9e7ecf427fc7`
- **作者**: 施廷緯
- **日期**: 2018-06-14 16:28:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`

### 108. A00-20180426001 修正admin及文管人員開啟檔案異常
- **Commit ID**: `f9b146cdb610475493028312e09968909ad94999`
- **作者**: waynechang
- **日期**: 2018-06-14 10:41:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 109. A00-20180606005 修正刪除子部門會發生找不到此帳號的錯誤訊息
- **Commit ID**: `d279b07e734cd651622f48744a6e67f22e5613f2`
- **作者**: 施廷緯
- **日期**: 2018-06-13 20:06:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`

### 110. C01-20180514004 修正BPM重啟後使用企業微信登入App會取不到產品序號的問題
- **Commit ID**: `32f7b14dbf15c28396bf686f58746a31d5d9ea36`
- **作者**: ChinRong
- **日期**: 2018-06-13 18:46:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 111. 修正向前(後)加簽關卡時無法正常發單的問題。
- **Commit ID**: `964656bc4963bd0336a25d22244cafc331511868`
- **作者**: 顏伸儒
- **日期**: 2018-06-13 17:50:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 112. 修正User在ProcessID少於兩碼無法發起流程問題。
- **Commit ID**: `199dba04a0b4616eea86a17b141115e42850a8b4`
- **作者**: 施廷緯
- **日期**: 2018-06-13 15:59:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 113. 新增鼎捷移動列表轉派資訊概要字段
- **Commit ID**: `5c74a1640adbda164496e2f15fc485cd0156c4c9`
- **作者**: pinchi_lin
- **日期**: 2018-06-13 14:51:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 114. 修正刪除子部門會發生找不到此帳號的錯誤訊息。
- **Commit ID**: `a868234188e4428c6f2721c494da1b5242b85ce4`
- **作者**: 施廷緯
- **日期**: 2018-06-13 10:44:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java`

### 115. 修正HR小助手 檢查部門失效且離職人員有主部門條件(微調)
- **Commit ID**: `800508339f0de89dd369a838475c01df9a347c98`
- **作者**: 施廷緯
- **日期**: 2018-06-13 10:37:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

