# Release Notes - BPM-ISO

## 版本資訊
- **新版本**: release_5.8.10.4
- **舊版本**: release_5.8.10.3
- **生成時間**: 2025-07-18 11:45:19
- **新增 Commit 數量**: 18

## 變更摘要

### lorenchang (5 commits)

- **2024-12-06 13:46:11**: [ISO]C01-20240820007 調整ISO新增單、變更單、作廢單JS(支援IMG簽核)(補)
  - 變更檔案: 2 個
- **2024-12-05 16:50:27**: [文件總結助手]文件智能家更名為文件總結助手(補)
  - 變更檔案: 6 個
- **2024-10-30 08:54:52**: [ISO]C01-20241028001 ISO文件匯入時清除同步資料表中 'docNo' 欄位的前後空白字元，避免填寫時不小心產生的多餘空白造成匯入資料異常(補)
  - 變更檔案: 1 個
- **2024-10-29 10:38:48**: [ISO]C01-20241028001 ISO文件匯入時清除同步資料表中 'docNo' 欄位的前後空白字元，避免填寫時不小心產生的多餘空白造成匯入資料異常
  - 變更檔案: 2 個
- **2024-10-23 14:04:45**: [ISO]C01-20241021008 修正並優化ISO文件匯入時無法透過SYN_ISODocCmItem的docNo取出匹配的SYN_ISODocument的錯誤訊息 (原訊息不易理解：The argument 'pLastVersion' must greater than 0!)
  - 變更檔案: 1 個

### 周权 (1 commits)

- **2024-12-05 11:45:11**: [ISO]C01-20241204004 调整ISO新增单，文管新增单机密等级没有必填error提示
  - 變更檔案: 2 個

### kmin (8 commits)

- **2024-11-25 11:17:52**: [ISO]C01-20241122005 修正ISO新增變更單缺少txtPeriod元件造成invoke卡住問題
  - 變更檔案: 2 個
- **2024-11-20 13:51:57**: [ISO]C01-20241118008 修正ISO作廢單缺少記錄version造成ISOformSave儲存異常
  - 變更檔案: 1 個
- **2024-10-22 11:48:19**: [內部]Q00-20241022001 調整ISO模組產品開窗與Base模組大小一致
  - 變更檔案: 1 個
- **2024-10-08 10:16:53**: [內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)
  - 變更檔案: 309 個
- **2024-10-07 16:23:37**: Revert "[內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)"
  - 變更檔案: 304 個
- **2024-10-07 16:23:22**: Revert "[內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)[補]"
  - 變更檔案: 1 個
- **2024-10-07 16:17:54**: [內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)[補]
  - 變更檔案: 1 個
- **2024-10-07 16:17:09**: [內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)
  - 變更檔案: 304 個

### davidhr (3 commits)

- **2024-11-21 16:16:16**: [資安]Q00-20241113001 bootstrap-3.3.4.min.css更換bootstrap-c.c.d.min.css
  - 變更檔案: 44 個
- **2024-11-18 15:43:47**: [資安]Q00-20241113001 bootstrap-3.3.5.min.css更換為bootstrap-c.c.e.min.css
  - 變更檔案: 45 個
- **2024-11-18 15:30:11**: [資安]Q00-20241113001 bootstrap-3.3.5.min.css更換,增加bootstrap-c.c.e.min.css
  - 變更檔案: 2 個

### 張詠威 (1 commits)

- **2024-10-14 15:38:59**: [ISO]C01-20240820007 調整ISO新增單、變更單、作廢單JS(支援IMG簽核)
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. [ISO]C01-20240820007 調整ISO新增單、變更單、作廢單JS(支援IMG簽核)(補)
- **Commit ID**: `277c18d53a1d364f4cae6a0231db71c8af5c9fd8`
- **作者**: lorenchang
- **日期**: 2024-12-06 13:46:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOMod.js`

### 2. [文件總結助手]文件智能家更名為文件總結助手(補)
- **Commit ID**: `82acd622f50980a109bddd5661c4e146da07e296`
- **作者**: lorenchang
- **日期**: 2024-12-05 16:50:27
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOUtil.js`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOAjaxController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocCanceMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java`

### 3. [ISO]C01-20241204004 调整ISO新增单，文管新增单机密等级没有必填error提示
- **Commit ID**: `de5b196008b13285c33a4f5e45ee33635a3ad96e`
- **作者**: 周权
- **日期**: 2024-12-05 11:45:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`

### 4. [ISO]C01-20241122005 修正ISO新增變更單缺少txtPeriod元件造成invoke卡住問題
- **Commit ID**: `074f12da735ddf86c1f6abe7172c2b536f28394c`
- **作者**: kmin
- **日期**: 2024-11-25 11:17:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java`

### 5. [資安]Q00-20241113001 bootstrap-3.3.4.min.css更換bootstrap-c.c.d.min.css
- **Commit ID**: `a5d17c9737bdb9917eed0fddfe002144d43d8934`
- **作者**: davidhr
- **日期**: 2024-11-21 16:16:16
- **變更檔案數量**: 44
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/AccessRightEntity.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/CustomISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/CustomReadDocumentInfo.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/DocNoIndexFile.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/DocNoReserved.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOClause.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocLevel.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocType.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOProperties.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOVettingRecord.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOVettingRule.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocCategory.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocCategoryType.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocCategoryTypeAttribute.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/NotificationContent.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ReadDocumentInfo.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/SecurityLevel.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/SnGenRule.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/UpdateDocumentInfo.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOCloudWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCloudApplyRecord.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCloudMailDesign.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCloudProperties.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCloudRecord.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCompany.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityMailDesign.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityRecord.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOChangeFileList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOClauseDocList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOFileQueryList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOFileReadingList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOReleaseDocList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/DocCategoryChooser.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/DocumentChooser.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/ShowDocument.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/ShowDocumentsWithAll.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/ShowISOClause.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/SingleDocCategoryChooser.jsp`

### 6. [ISO]C01-20241118008 修正ISO作廢單缺少記錄version造成ISOformSave儲存異常
- **Commit ID**: `de70f285aea7cae7bbbcea91f2ae230476cccc1b`
- **作者**: kmin
- **日期**: 2024-11-20 13:51:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCancel.js`

### 7. [資安]Q00-20241113001 bootstrap-3.3.5.min.css更換為bootstrap-c.c.e.min.css
- **Commit ID**: `4cf21b81dcb8252073678cbff2d81b6b6810036c`
- **作者**: davidhr
- **日期**: 2024-11-18 15:43:47
- **變更檔案數量**: 45
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/AccessRightEntity.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/CustomISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/CustomReadDocumentInfo.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/DocNoIndexFile.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/DocNoReserved.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOClause.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocLevel.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocType.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOProperties.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOVettingRecord.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOVettingRule.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocCategory.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocCategoryType.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocCategoryTypeAttribute.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ManageDocFileMain.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/NotificationContent.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ReadDocumentInfo.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/SecurityLevel.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/SnGenRule.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/UpdateDocumentInfo.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOCloudWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCloudApplyRecord.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCloudMailDesign.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCloudProperties.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCloudRecord.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityCompany.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityMailDesign.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOPortabilityRecord.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOChangeFileList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOClauseDocList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOFileQueryList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOFileReadingList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoReport/ISOReleaseDocList.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/DocCategoryChooser.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/DocumentChooser.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/ShowDocument.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/ShowDocumentsWithAll.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/ShowISOClause.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/openWin/SingleDocCategoryChooser.jsp`

### 8. [資安]Q00-20241113001 bootstrap-3.3.5.min.css更換,增加bootstrap-c.c.e.min.css
- **Commit ID**: `ceedadab0e8ae628d211d4e24440069bf82987aa`
- **作者**: davidhr
- **日期**: 2024-11-18 15:30:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/css/bootstrap/bootstrap-3.3.5.min.css`
  - ➕ **新增**: `ISOModule/WebContent/ISOModule/css/bootstrap/bootstrap-c.c.e.min.css`

### 9. [ISO]C01-20241028001 ISO文件匯入時清除同步資料表中 'docNo' 欄位的前後空白字元，避免填寫時不小心產生的多餘空白造成匯入資料異常(補)
- **Commit ID**: `e77025a129f069ce40debae2f31605ea6546bbec`
- **作者**: lorenchang
- **日期**: 2024-10-30 08:54:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/SyncISODocCmItemDao.java`

### 10. [ISO]C01-20241028001 ISO文件匯入時清除同步資料表中 'docNo' 欄位的前後空白字元，避免填寫時不小心產生的多餘空白造成匯入資料異常
- **Commit ID**: `26238b0550ef7314b06344278f1a99a136afa14c`
- **作者**: lorenchang
- **日期**: 2024-10-29 10:38:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/SyncISODocCmItemDao.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISODocManagerController.java`

### 11. [ISO]C01-20241021008 修正並優化ISO文件匯入時無法透過SYN_ISODocCmItem的docNo取出匹配的SYN_ISODocument的錯誤訊息 (原訊息不易理解：The argument 'pLastVersion' must greater than 0!)
- **Commit ID**: `8e9830bc40f990b2cd16c0ddb811ed6784730a4f`
- **作者**: lorenchang
- **日期**: 2024-10-23 14:04:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/SyncISODocumentMgr.java`

### 12. [內部]Q00-20241022001 調整ISO模組產品開窗與Base模組大小一致
- **Commit ID**: `36bb811a76939e7ed1d2ada379e2ea261c4925f3`
- **作者**: kmin
- **日期**: 2024-10-22 11:48:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/js/OpenWin.js`

### 13. [ISO]C01-20240820007 調整ISO新增單、變更單、作廢單JS(支援IMG簽核)
- **Commit ID**: `a94c0622335e357d5641e0cb0b6bd23928540c54`
- **作者**: 張詠威
- **日期**: 2024-10-14 15:38:59
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCancel.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOMod.js`

### 14. [內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)
- **Commit ID**: `45e92d592465c2490930397719fa725b8c1cac27`
- **作者**: kmin
- **日期**: 2024-10-08 10:16:53
- **變更檔案數量**: 309
- **檔案變更詳細**:
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/build/pdf.js`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/build/pdf.mjs`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/build/pdf.mjs.map`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/build/pdf.sandbox.mjs`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/build/pdf.sandbox.mjs.map`
  - 📄 **重新命名**: `ISOModule/WebContent/PDFWebView/build/pdf.worker.js`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/build/pdf.worker.mjs.map`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.html`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.js`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs.map`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/debugger.css`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/debugger.js`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/debugger.mjs`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_add.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_disclaimer.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_done.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_spinner.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_warning.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/annotation-paperclip.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/annotation-pushpin.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorFreeHighlight.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorFreeText.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorInk.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorTextHighlight.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/editor-toolbar-delete.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/findbarButton-next.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/findbarButton-previous.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/gv-toolbarButton-download.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/loading-dark.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/messageBar_closingButton.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/messageBar_warning.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-documentProperties.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-firstPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-handTool.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-lastPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-rotateCcw.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-rotateCw.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollHorizontal.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollVertical.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollWrapped.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-selectTool.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadEven.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadNone.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadOdd.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/shadow.png`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-bookmark.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-currentOutlineItem.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-download.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorFreeText.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorHighlight.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorInk.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorStamp.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-menuArrow.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-openFile.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-pageDown.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-pageUp.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-presentationMode.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-print.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-search.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-sidebarToggle.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewAttachments.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewLayers.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewOutline.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewThumbnail.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-zoomIn.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-zoomOut.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/BPMlocale.json`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/BPMlocale.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ach/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ach/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/af/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/af/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/an/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/an/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ar/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ar/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ast/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ast/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/az/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/az/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/be/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/be/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bg/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bg/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bn/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bn/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bo/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bo/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/br/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/br/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/brx/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/brx/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bs/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bs/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ca/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ca/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cak/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cak/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ckb/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ckb/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cs/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cs/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cy/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cy/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/da/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/da/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/de/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/de/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/dsb/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/dsb/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/el/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/el/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-CA/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-CA/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-GB/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-GB/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/BPMviewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/eo/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/eo/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-AR/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-AR/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-CL/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-CL/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-ES/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-ES/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-MX/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-MX/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/et/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/et/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/eu/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/eu/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fa/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fa/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ff/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ff/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fi/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fi/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fur/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fy-NL/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fy-NL/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ga-IE/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ga-IE/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gd/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gd/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gn/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gn/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gu-IN/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gu-IN/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/he/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/he/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hi-IN/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hi-IN/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hsb/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hsb/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hu/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hu/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hy-AM/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hy-AM/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hye/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hye/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ia/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ia/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/id/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/id/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/is/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/is/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/it/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/it/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ja/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ja/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ka/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ka/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kab/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kab/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kk/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kk/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/km/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/km/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kn/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kn/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ko/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ko/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lij/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lij/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lo/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lo/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/locale.json`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/locale.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lt/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lt/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ltg/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ltg/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lv/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lv/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/meh/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/meh/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/mk/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/mk/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/mr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/mr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ms/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ms/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/my/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/my/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nb-NO/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nb-NO/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ne-NP/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ne-NP/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nn-NO/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nn-NO/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/oc/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/oc/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pa-IN/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pa-IN/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pt-BR/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pt-BR/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pt-PT/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pt-PT/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/rm/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/rm/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ro/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ro/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ru/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ru/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sat/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sat/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sc/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sc/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/scn/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/scn/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sco/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sco/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/si/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/si/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sk/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sk/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/skr/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/son/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/son/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sq/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sq/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sv-SE/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sv-SE/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/szl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/szl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ta/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ta/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/te/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/te/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tg/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tg/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/th/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/th/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/trs/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/trs/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/uk/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/uk/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ur/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ur/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/uz/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/uz/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/vi/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/vi/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/wo/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/wo/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/xh/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/xh/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/BPMviewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/BPMviewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/BPMviewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/BPMviewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSans.pfb`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansBold.pfb`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansBoldItalic.pfb`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansItalic.pfb`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/viewer.css`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/viewer.html`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/viewer.js`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/viewer.js.map`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/viewer.mjs`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/viewer.mjs.map`
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/web.xml`

### 15. Revert "[內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)"
- **Commit ID**: `b89db55c7bd6f248479614d653d485101514e734`
- **作者**: kmin
- **日期**: 2024-10-07 16:23:37
- **變更檔案數量**: 304
- **檔案變更詳細**:
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/build/pdf.js`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/build/pdf.worker.js`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.html`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.js`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs.map`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/debugger.css`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/debugger.js`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/debugger.mjs`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/altText_add.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/altText_disclaimer.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/altText_done.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/altText_spinner.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/altText_warning.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/annotation-paperclip.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/annotation-pushpin.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorFreeHighlight.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorFreeText.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorInk.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorTextHighlight.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/editor-toolbar-delete.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/findbarButton-next.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/findbarButton-previous.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/gv-toolbarButton-download.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/loading-dark.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/messageBar_closingButton.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/messageBar_warning.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-documentProperties.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-firstPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-handTool.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-lastPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-rotateCcw.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-rotateCw.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollHorizontal.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollVertical.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollWrapped.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-selectTool.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadEven.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadNone.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadOdd.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/shadow.png`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-bookmark.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-currentOutlineItem.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-download.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorFreeText.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorHighlight.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorInk.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorStamp.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-menuArrow.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-openFile.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-pageDown.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-pageUp.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-presentationMode.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-print.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-search.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-sidebarToggle.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewAttachments.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewLayers.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewOutline.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewThumbnail.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-zoomIn.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-zoomOut.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/BPMlocale.json`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/BPMlocale.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ach/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ach/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/af/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/af/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/an/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/an/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ar/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ar/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ast/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ast/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/az/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/az/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/be/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/be/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bg/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bg/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bn/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bn/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bo/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bo/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/br/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/br/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/brx/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/brx/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bs/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bs/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ca/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ca/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cak/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cak/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ckb/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ckb/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cs/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cs/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cy/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cy/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/da/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/da/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/de/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/de/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/dsb/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/dsb/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/el/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/el/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-CA/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-CA/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-GB/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-GB/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/BPMviewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/eo/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/eo/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-AR/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-AR/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-CL/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-CL/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-ES/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-ES/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-MX/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-MX/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/et/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/et/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/eu/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/eu/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fa/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fa/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ff/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ff/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fi/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fi/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fr/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fr/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fur/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fy-NL/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fy-NL/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ga-IE/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ga-IE/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gd/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gd/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gl/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gl/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gn/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gn/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gu-IN/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gu-IN/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/he/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/he/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hi-IN/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hi-IN/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hr/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hr/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hsb/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hsb/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hu/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hu/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hy-AM/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hy-AM/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hye/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hye/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ia/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ia/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/id/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/id/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/is/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/is/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/it/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/it/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ja/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ja/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ka/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ka/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kab/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kab/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kk/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kk/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/km/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/km/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kn/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kn/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ko/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ko/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lij/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lij/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lo/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lo/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/locale.json`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/locale.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lt/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lt/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ltg/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ltg/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lv/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lv/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/meh/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/meh/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/mk/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/mk/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/mr/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/mr/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ms/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ms/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/my/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/my/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nb-NO/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nb-NO/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ne-NP/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ne-NP/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nl/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nl/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nn-NO/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nn-NO/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/oc/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/oc/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pa-IN/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pa-IN/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pl/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pl/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pt-BR/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pt-BR/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pt-PT/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pt-PT/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/rm/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/rm/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ro/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ro/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ru/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ru/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sat/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sat/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sc/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sc/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/scn/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/scn/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sco/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sco/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/si/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/si/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sk/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sk/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/skr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sl/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sl/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/son/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/son/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sq/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sq/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sr/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sr/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sv-SE/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sv-SE/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/szl/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/szl/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ta/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ta/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/te/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/te/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tg/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tg/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/th/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/th/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tl/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tl/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tr/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tr/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/trs/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/trs/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/uk/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/uk/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ur/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ur/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/uz/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/uz/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/vi/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/vi/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/wo/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/wo/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/xh/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/xh/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/BPMviewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/BPMviewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/BPMviewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/BPMviewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSans.pfb`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansBold.pfb`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansBoldItalic.pfb`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansItalic.pfb`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/viewer.css`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/viewer.html`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/viewer.js`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/viewer.js.map`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/viewer.mjs`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/viewer.mjs.map`
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/web.xml`

### 16. Revert "[內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)[補]"
- **Commit ID**: `753a1e702b2bbf9288d6dd2b7b841d3ca74cd48c`
- **作者**: kmin
- **日期**: 2024-10-07 16:23:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs`

### 17. [內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)[補]
- **Commit ID**: `b09ef2a1bd504a048300402f705574de065caa82`
- **作者**: kmin
- **日期**: 2024-10-07 16:17:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs`

### 18. [內部]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)
- **Commit ID**: `367950d025345a1c305473db6ab94b932e238e4c`
- **作者**: kmin
- **日期**: 2024-10-07 16:17:09
- **變更檔案數量**: 304
- **檔案變更詳細**:
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/build/pdf.js`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/build/pdf.worker.js`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.html`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.js`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs.map`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/debugger.css`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/debugger.js`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/debugger.mjs`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_add.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_disclaimer.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_done.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_spinner.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/altText_warning.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/annotation-paperclip.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/annotation-pushpin.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorFreeHighlight.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorFreeText.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorInk.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/cursor-editorTextHighlight.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/editor-toolbar-delete.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/findbarButton-next.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/findbarButton-previous.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/gv-toolbarButton-download.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/loading-dark.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/messageBar_closingButton.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/messageBar_warning.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-documentProperties.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-firstPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-handTool.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-lastPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-rotateCcw.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-rotateCw.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollHorizontal.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollPage.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollVertical.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-scrollWrapped.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-selectTool.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadEven.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadNone.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/secondaryToolbarButton-spreadOdd.svg`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/images/shadow.png`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-bookmark.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-currentOutlineItem.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-download.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorFreeText.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorHighlight.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorInk.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-editorStamp.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-menuArrow.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-openFile.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-pageDown.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-pageUp.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-presentationMode.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-print.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-search.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-sidebarToggle.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewAttachments.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewLayers.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewOutline.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-viewThumbnail.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-zoomIn.svg`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/images/toolbarButton-zoomOut.svg`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/BPMlocale.json`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/BPMlocale.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ach/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ach/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/af/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/af/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/an/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/an/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ar/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ar/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ast/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ast/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/az/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/az/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/be/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/be/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bg/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bg/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bn/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bn/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bo/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bo/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/br/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/br/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/brx/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/brx/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/bs/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/bs/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ca/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ca/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cak/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cak/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ckb/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ckb/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cs/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cs/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/cy/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/cy/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/da/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/da/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/de/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/de/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/dsb/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/dsb/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/el/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/el/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-CA/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-CA/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-GB/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-GB/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/BPMviewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/en-US/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/eo/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/eo/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-AR/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-AR/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-CL/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-CL/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-ES/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-ES/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/es-MX/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/es-MX/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/et/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/et/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/eu/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/eu/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fa/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fa/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ff/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ff/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fi/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fi/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fur/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/fy-NL/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/fy-NL/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ga-IE/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ga-IE/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gd/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gd/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gn/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gn/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/gu-IN/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/gu-IN/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/he/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/he/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hi-IN/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hi-IN/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hsb/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hsb/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hu/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hu/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hy-AM/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hy-AM/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/hye/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/hye/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ia/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ia/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/id/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/id/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/is/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/is/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/it/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/it/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ja/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ja/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ka/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ka/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kab/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kab/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kk/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kk/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/km/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/km/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/kn/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/kn/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ko/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ko/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lij/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lij/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lo/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lo/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/locale.json`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/locale.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lt/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lt/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ltg/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ltg/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/lv/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/lv/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/meh/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/meh/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/mk/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/mk/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/mr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/mr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ms/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ms/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/my/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/my/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nb-NO/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nb-NO/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ne-NP/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ne-NP/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/nn-NO/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/nn-NO/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/oc/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/oc/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pa-IN/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pa-IN/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pt-BR/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pt-BR/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/pt-PT/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/pt-PT/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/rm/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/rm/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ro/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ro/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ru/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ru/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sat/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sat/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sc/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sc/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/scn/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/scn/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sco/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sco/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/si/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/si/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sk/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sk/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/skr/viewer.ftl`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/son/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/son/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sq/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sq/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/sv-SE/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/sv-SE/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/szl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/szl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ta/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ta/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/te/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/te/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tg/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tg/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/th/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/th/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tl/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tl/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/tr/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/tr/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/trs/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/trs/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/uk/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/uk/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/ur/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/ur/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/uz/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/uz/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/vi/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/vi/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/wo/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/wo/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/xh/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/xh/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/BPMviewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/BPMviewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-CN/viewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/BPMviewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/BPMviewer.properties`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/viewer.ftl`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/locale/zh-TW/viewer.properties`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSans.pfb`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansBold.pfb`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansBoldItalic.pfb`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/standard_fonts/FoxitSansItalic.pfb`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/viewer.css`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/viewer.html`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/viewer.js`
  - ❌ **刪除**: `ISOModule/WebContent/PDFWebView/web/viewer.js.map`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/viewer.mjs`
  - ➕ **新增**: `ISOModule/WebContent/PDFWebView/web/viewer.mjs.map`
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/web.xml`

