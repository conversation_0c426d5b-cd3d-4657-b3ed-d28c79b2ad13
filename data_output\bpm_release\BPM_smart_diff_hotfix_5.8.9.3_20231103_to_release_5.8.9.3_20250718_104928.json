{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.3_20231103", "date": "2023-11-03 11:12:20", "message": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)", "author": "邱郁晏"}, "舊分支": {"branch_name": "release_5.8.9.3", "date": "2023-08-23 13:15:30", "message": "[EBG]S00-20230808002 新增EBG電子簽章專案 [補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 10:49:28", "新增commit數量": 90, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "aa09f62b64a2b907e650f067b16b87bb162be173", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)", "提交日期": "2023-11-03 11:12:20", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad4bb2ce3a93571fe90e0d3e76e772026cd8523e", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補修正)", "提交日期": "2023-10-25 18:14:01", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4fe066ce39e270b54c4e96b1d24737f6a507e82c", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題", "提交日期": "2023-10-25 16:35:30", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d13f8945580ce29c97c02142d0493438a228d7d4", "commit_訊息": "[T100] Q00-20231102002 T100的签核历程沒有权限的显示提示告知登入者", "提交日期": "2023-11-02 13:12:36", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessInfoGet.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8cc9736a639768914dab9892188731823263cc35", "commit_訊息": "[Web] Q00-20231101005 修正表單欄位為invisible且設定顯示千分位，開啟追蹤流程在F12顯示錯誤", "提交日期": "2023-11-01 15:50:29", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "20feced941434a2919c3ba7cf1e24e86f4c8f99c", "commit_訊息": "[DT]Q00-20231101004 修正Web化流程管理工具中服務任務的應用程式加入表單型態參數後會有無法簽入的問題", "提交日期": "2023-11-01 15:38:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bfbfe27f24eae061e549a130bdf2ac8ae907a592", "commit_訊息": "[雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯[補修正]", "提交日期": "2023-11-01 14:10:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d02ea7663b9ef80dafd1fc80241c85c30c59fe5", "commit_訊息": "[雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯", "提交日期": "2023-11-01 14:04:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "cf7baf754637258ac8524e00f2099d83cd72b484", "commit_訊息": "[Web]S00-20220929001 新增BPM外部連結-進入BPM首頁", "提交日期": "2023-10-05 14:23:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/PortletEntry.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d225d3257b4a4748a1e92d6fed3b695881930956", "commit_訊息": "[Web]Q00-20230919001 調整URL從/NaNaWeb/Login.jsp?type=admin登入時會報閒置過久的訊息改成\"請輸入正確的代號或密碼!\"", "提交日期": "2023-09-19 12:02:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cdb46378b5dcfd3530572f971f6b374352fa7fa9", "commit_訊息": "[Web] Q00-20231025001 调整隐藏栏位为單身欄位加總的运算操作验证", "提交日期": "2023-10-25 11:01:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63ae946ba9eecd48cf48d49067ff6a8e3ebb660d", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]", "提交日期": "2023-09-28 17:59:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a94486a23d4d1c32d8636c5c0341141a7ae72c9", "commit_訊息": "[Web] Q00-20231031001 修正缩小ESSPlus管理页面时，查询出来的结果在grid显示不全", "提交日期": "2023-10-31 13:39:56", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4cbd31501863505d3bf2ad6dd454da4fab65229b", "commit_訊息": "[Web] Q00-20231030004 调整预览列印会带出表單名稱與表單代號Tab的问题", "提交日期": "2023-10-30 15:54:31", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c94cbb4bb6dc6249ef6a6baeb676dd207f966bd", "commit_訊息": "[Web]Q00-20231023001 修正列印预览grid标题字体颜色为白色", "提交日期": "2023-10-23 09:15:57", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df88b8a0570dd9ba09811456434bd06abb79425d", "commit_訊息": "[流程引擎]Q00-20231030001 修正流程發起者於追蹤流程頁面進入表單畫面點擊撤銷流程後，詳細流程圖的流程詳細資訊應為是「流程發起者」而非「流程負責人」撤銷", "提交日期": "2023-10-30 14:41:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "94bcbf65368ea07dcf42c7be0b6e51786f4a929c", "commit_訊息": "[流程引擎]Q00-20231027003 修正流程結案清除附件的服務，當表單附件OID屬性為空時，會被系統移除附件，此調整為避免移除表單實例的附件的OID屬性為空的附件", "提交日期": "2023-10-27 14:39:03", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d1cba1b0c9639bfecc92261e6b91b0d8b74335f", "commit_訊息": "[資安] Q00-20231017003 修改因paloalto內部防火牆把aes.js當成spyware Malicious JavaScript Files Detection攻擊，將aes.js進行加密編碼。", "提交日期": "2023-10-26 16:16:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/aes.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7f8b7f1acb793176366607b87882dd24f840e47e", "commit_訊息": "[Web]Q00-20231026001 修正转由他人处理 > 经常选取对象 无资料时显示错误页面", "提交日期": "2023-10-26 14:29:58", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fda832304a2ade4c262d5d477479848a405b7c3b", "commit_訊息": "[Web]Q00-20231023002 修正流程第一關有設定必須上傳新附件，若從流程草稿開啟時，系統沒有卡控必須上傳新附件", "提交日期": "2023-10-23 11:10:11", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88e1cf417dde9e02c3d2795a5d0a99a8cd0a7d1f", "commit_訊息": "[WEB]Q00-20231018001 调整Select元件有textbox輸入格时的点击事件逻辑", "提交日期": "2023-10-18 11:13:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/resources/html/SelectElementTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c6bf4837a6cfd7bad3b6a81c36d796bdec078d92", "commit_訊息": "[流程引擎] Q00-20231017004 修正工作受託者<#allAssigneesIDnName>沒有帶出資料的問題", "提交日期": "2023-10-17 14:56:38", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f5b181b0fb37aa98758b066724a737d7a2d0842d", "commit_訊息": "[表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗", "提交日期": "2023-10-12 15:30:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0e06874d13a0247e42e7812e52620c7f27bdf16c", "commit_訊息": "[T100] Q00-20231004004 修正手寫元件造成拋單失敗，新增判斷略過手寫元件", "提交日期": "2023-10-04 14:56:04", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "266c5f2638566cd340f0073a429382aea76b4bed", "commit_訊息": "[WEB]Q00-20231004003 修正在手機瀏覽器以及RWD窄畫面上沒有附件檔名的URL連結導致無法下載問題", "提交日期": "2023-10-04 14:11:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "873ea01a3965a369c5174a58cf1abbc67680f8d0", "commit_訊息": "[Web] V00-20231011001 修正轉存表單日期格式異常問題，支持常用格式。", "提交日期": "2023-10-11 14:21:33", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4cfae325dcd47ae0219d75a4a203d1cbf4ef777", "commit_訊息": "[Web] V00-20231004001 修正匯出表單，日期格式(yy/M/d)被判斷為異常格式問題。", "提交日期": "2023-10-04 12:04:19", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23fa935ff54e6acaffbae18056b1b85cacbee415", "commit_訊息": "[Web]Q00-20231004001 修正TextBox設定數字轉繁體文字在列印表單時顯示簡體文字的問題", "提交日期": "2023-10-04 10:09:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b0cd56a4c4394613c2121c6b57bbd6e0cd3c0c4f", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]", "提交日期": "2023-09-26 14:32:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "499f79d8aea409f6381f69ca47585429f375312a", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。", "提交日期": "2023-09-25 17:24:13", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "12f3c8c72f6ae5c9d8f53c90722be953166a8502", "commit_訊息": "[Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题[补修正]", "提交日期": "2023-08-24 13:39:10", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "db2688d4318c7af76af0e9bea23c548a629a5461", "commit_訊息": "[Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题", "提交日期": "2023-08-24 09:32:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c0857b653f8e3532fbc1d57dae9e6a0b8648f30", "commit_訊息": "[SAP]Q00-20231003003 修正SAP整合作業-SAP欄位對應設定，新增整合設定頁面當選擇完表單後，無法載入表單元件", "提交日期": "2023-10-03 17:39:22", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f6ff06040667e7f310884134c1b5bff2046574a4", "commit_訊息": "[DT]Q00-20230928003 修正從Web化流程管理工具入版後的流程圖在Swing中開啟時流程圖會被截斷問題", "提交日期": "2023-09-28 17:56:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd81e5cec621012c141bd295942f76440117bf07", "commit_訊息": "[web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复", "提交日期": "2023-09-27 17:19:26", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84c40844c933817f27b210d50918399c4b8ef837", "commit_訊息": "[DT]Q00-20230926002 修正Web流程管理工具儲存流程因找不到ActivityType導致儲存失敗問題", "提交日期": "2023-09-26 17:58:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2aa04710165d3b86229e2fe26d0e61a2aba081a9", "commit_訊息": "[DT]Q00-20230926003 修正Web流程管理工具的流程圖進版後有條件的連接線顏色變黑色問題", "提交日期": "2023-09-26 17:48:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4791c5e879dcab0752914f3990cef0b6080ee193", "commit_訊息": "[DT]Q00-20230926004 修正Web流程管理工具的流程圖進版後連接線上的名稱消失問題", "提交日期": "2023-09-26 17:44:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29ab55e8f507ab48ed31050044b2889bc9012ffc", "commit_訊息": "[在線閱覽]Q00-20230926001 修正在線閱讀檔案設定不可下載，在待辦表單頁面點擊閱讀檔案的頁面，仍可以下載PDF閱讀檔的異常", "提交日期": "2023-09-26 16:01:14", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "243f0dee28fcd78c01dc355e5ea1ebdeea2cd690", "commit_訊息": "[T100]S00-20220513001 T100取簽核歷程新增是否為代理人標籤", "提交日期": "2023-09-05 10:16:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTag.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "89cd1b054764b60e4d9fc1bd4af233cbda0116e4", "commit_訊息": "[Web] Q00-20230922004 在没有添加grid按钮却使用grid方法时，新增防呆，防止focus报错", "提交日期": "2023-09-22 14:57:46", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "849272149d02c8c88a944b12c8c9f07e07f6bfbb", "commit_訊息": "[Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效", "提交日期": "2023-09-22 11:21:43", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e2a961c69ca53882f902a4b2c5780c0b62f12999", "commit_訊息": "[Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加", "提交日期": "2023-09-22 10:42:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7d7e7b251a1cbb609289e4b2a03c760c508123ad", "commit_訊息": "[Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错", "提交日期": "2023-09-22 10:27:58", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16cd4611bfd70d7fc616a2c79cf4cf6239800dd0", "commit_訊息": "[組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號(補修正)", "提交日期": "2023-09-21 17:05:57", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f8404cd5b3f7d7cf2bedd01f370900297e73bd4b", "commit_訊息": "[DT]Q00-20230921001 修正Web流程管理工具中參與者選職務或職稱後儲存再開啟其值會消失的問題", "提交日期": "2023-09-21 16:13:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c505588e3f0b6265145c0da461f5cdfbd0fd9f21", "commit_訊息": "[DT]Q00-20230920002 修正Web系統權限管理員上儲存按鈕後畫面會一直loading的問題", "提交日期": "2023-09-20 16:38:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a4a32ad287ed0d2f080d30b26516b16ab9bd884", "commit_訊息": "[組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號。", "提交日期": "2023-09-20 13:44:52", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b77ad2a05e9dd1f4e1cc36a9f2993fc0242c044e", "commit_訊息": "[DT]A00-20230919001 修正Web流程管理工具中事件處理在流程儲存後會消失問題", "提交日期": "2023-09-20 12:01:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed3c3700c858b38d0c7ea39c4a8d3ae08e846af4", "commit_訊息": "[ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為\"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程\"[補修正]", "提交日期": "2023-09-19 13:42:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "11bcd57876342d6dba5b2e9c59d5f2cc20ba1c42", "commit_訊息": "[DT]Q00-20230918002 修正Web流程管理工具中連接線編輯後儲存流程再開啟會變成藍色的問題", "提交日期": "2023-09-18 14:40:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d87cb6df57f8577ed09e5ac875957d9a26f508b3", "commit_訊息": "[ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為\"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程\"", "提交日期": "2023-09-18 14:02:51", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "159fcb0154095ae0f17ff8904322b71dbab2cd95", "commit_訊息": "[web]Q00-20230913001 沒有未閱讀的工作通知，但右上角鈴鐺還是一直有紅點點问题修复", "提交日期": "2023-09-18 09:24:41", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a74f2c89c9a72bc332b5ce748f2ca0c6e0e24341", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】", "提交日期": "2023-09-11 16:08:36", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "36ba303dce5da0c92ced190573e26c5ab3d576d2", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】", "提交日期": "2023-09-07 17:54:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d83f57a6e0c67ad479d43287fcbc2e07a80e78d2", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面", "提交日期": "2023-08-25 15:40:09", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0b0eee29550a12feff3945b8fa4a5ebee9608db5", "commit_訊息": "[Web] Q00-20230915001 修正 SQL注册器点击资料返回到新增页面，数据错误带回显示", "提交日期": "2023-09-15 11:09:32", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5bbbc2bfd84cf10a5a41fdc952a6fb589756a43", "commit_訊息": "[表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]", "提交日期": "2023-09-15 10:22:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8bad3e313c81ff1176821fcf0d2a8e8b7b699bc9", "commit_訊息": "[Web]Q00-20230914001 修正RadioButton，checkbox元件帶入值到grid时报错，新增防呆。", "提交日期": "2023-09-14 16:07:26", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ce886ded44b84dd6da4ed224f1fe92c53f58751", "commit_訊息": "[Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。", "提交日期": "2023-09-12 15:29:48", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b7af9bb3ab4fbec5ab05bb6d4035cf3f357a4e91", "commit_訊息": "[Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)", "提交日期": "2023-09-12 10:23:55", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c6d4f2abad238d9b424702b482757ba602e6687", "commit_訊息": "[Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法", "提交日期": "2023-09-12 10:06:27", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a249f8c450b6fcbc70a198e10e2a75766225daf", "commit_訊息": "[Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。", "提交日期": "2023-09-11 15:39:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd50740a44f0ef78967b45dd36f416f141b77b76", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)", "提交日期": "2023-09-12 17:23:00", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ac3b5dec2f5d3ee78d2db6e267b9c9db3f9a5f8", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)", "提交日期": "2023-09-04 10:53:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c574ec137870e563b59669ce40cc6ff52a5f5cc3", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題", "提交日期": "2023-08-31 15:41:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad68936dee6a03a4c9f9582304298e76779aa498", "commit_訊息": "[表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題", "提交日期": "2023-09-08 16:39:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cf50118ad353688beb07bbc27986b8b3ac05330", "commit_訊息": "[Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]", "提交日期": "2023-09-08 10:14:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "20b08849dc32bb291a1c771427bda3f7a555954b", "commit_訊息": "[SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗", "提交日期": "2023-09-08 09:24:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ffb260bbb2c54195929376f607925ad7fc6ccd4f", "commit_訊息": "[Web]Q00-20230907001 读取自定义background,Banner,logo图片时，新增图片格式防呆。", "提交日期": "2023-09-07 11:31:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7dea21018ff84f649cce05eed470cd4c4a1beb1e", "commit_訊息": "[流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常", "提交日期": "2023-09-07 10:31:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e699a42126c703329ef58e96c2b657ae20675adc", "commit_訊息": "[Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。", "提交日期": "2023-09-06 13:19:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a3fe8f78be2406373a59e4933ea3d472d5e0eb6", "commit_訊息": "[Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。", "提交日期": "2023-09-06 12:03:56", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29767c102033fb3b27fb7a0909f338d0650aee9f", "commit_訊息": "[Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误", "提交日期": "2023-09-11 11:47:04", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63bc842520f9344ddd0c495ec22b77270b53536b", "commit_訊息": "[Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。", "提交日期": "2023-09-05 18:01:25", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4412bd5a36a8723fdb9d2c7b9843ee27bdfe35fe", "commit_訊息": "[Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白", "提交日期": "2023-09-06 11:11:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a459de8d88f4afe86a63758b5f5fce19e63561bb", "commit_訊息": "[DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題", "提交日期": "2023-09-01 19:47:59", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "721c74a695e0e0a37765d71963ebca50e5fab6b8", "commit_訊息": "打包用", "提交日期": "2023-08-31 14:47:24", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/lib/Json/json.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "7edf85fb3aaae049bc59fd0591ea66848d1cef12", "commit_訊息": "[Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件", "提交日期": "2023-08-31 09:16:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "48fecbee1144a6d2fe423dd3f4ad4ae8e1f605f5", "commit_訊息": "[Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題", "提交日期": "2023-08-30 11:47:29", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b3892d1623b3636b81e6130827e7d2d13eafc60f", "commit_訊息": "[TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷", "提交日期": "2023-08-30 10:43:55", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e1a8c44961c5907be02fe8fbdc1b861b7958e60", "commit_訊息": "[流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常", "提交日期": "2023-08-29 16:50:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b30e78a2b454939864ee2810855877513097fe36", "commit_訊息": "[ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗", "提交日期": "2023-08-29 15:50:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f72c90f6b7c0d2f7c468a8d0dd2c43ba93369fab", "commit_訊息": "[web]Q00-20230829003 列印時附件資訊會超出邊界问题修复", "提交日期": "2023-08-29 14:02:09", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "feae55e055644d8903d73585988148566eb72137", "commit_訊息": "[流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據", "提交日期": "2023-08-29 10:32:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f7d42cd47ebe4f0fa47038313eee19d638b4ee3c", "commit_訊息": "[SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤", "提交日期": "2023-08-28 16:57:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c4e8abf77a2d5851011fb9f7df99123b4b5d5713", "commit_訊息": "[DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題", "提交日期": "2023-08-28 11:15:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e931eef3588ea08db7b75a46fbc9e1083303403", "commit_訊息": "[web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复", "提交日期": "2023-08-25 17:38:15", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ce60fb865619dd15ff23dc0db3378472fda363c", "commit_訊息": "[Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。", "提交日期": "2023-08-24 13:43:54", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "39f901b07448b8ebf85b6a97a2a949267ac915bf", "commit_訊息": "[Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常", "提交日期": "2023-08-23 15:27:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}]}