# Release Notes - BPM

## 版本資訊
- **新版本**: 5.7.6.3
- **舊版本**: 5.7.6.2_1
- **生成時間**: 2025-07-28 17:55:23
- **新增 Commit 數量**: 180

## 變更摘要

### <PERSON> (14 commits)

- **2019-10-28 15:21:22**: 增加 mtsmodule00000000000000000000001 之模組icon
  - 變更檔案: 1 個
- **2019-10-25 18:08:47**: A00-20190726001 調整響應式表單於mobile時，仍出現主旨欄位讓使用者可填寫
  - 變更檔案: 1 個
- **2019-10-17 15:13:47**: A00-***********調整英文字
  - 變更檔案: 3 個
- **2019-10-15 15:07:55**: Q00-20191015001 光碟內容移除bcl相關 jar
  - 變更檔案: 1 個
- **2019-10-15 15:07:25**: Q00-20191015001 光碟內容移除bcl相關 jar
  - 變更檔案: 3 個
- **2019-10-15 11:41:18**: 更新Oracle jdbc的版本與設定
  - 變更檔案: 5 個
- **2019-10-14 17:43:05**: 調整錯誤資訊
  - 變更檔案: 1 個
- **2019-09-20 16:37:50**: 整理程式邏輯
  - 變更檔案: 1 個
- **2019-09-02 16:57:55**: traceProcessMain.script.alert4 英文語系沒寫對
  - 變更檔案: 2 個
- **2019-08-30 12:21:33**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-30 12:20:34**: 補上 apmt530
  - 變更檔案: 1 個
- **2019-08-23 10:54:11**: A00-20190823002 修正語法錯誤
  - 變更檔案: 1 個
- **2019-08-20 18:22:23**: A00-20190816001 範例程式調整
  - 變更檔案: 1 個
- **2019-08-20 16:13:59**: A00-20190816001 範例程式調整
  - 變更檔案: 3 個

### 林致帆 (20 commits)

- **2019-10-28 10:26:26**: A00-20190215003 組織設計師的部門核決層級修改，層級數字會亂跳
  - 變更檔案: 1 個
- **2019-10-24 12:31:41**: [補修正]Q00-20190826005
  - 變更檔案: 1 個
- **2019-10-24 12:23:56**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-10-24 12:23:37**: Q00-20190826005 表單設計師RWD使用title元件，顯示文字為空，再點選字體大小，LOGO會看不到
  - 變更檔案: 3 個
- **2019-10-21 18:41:00**: [補修正]A00-20181129001
  - 變更檔案: 1 個
- **2019-10-21 18:20:42**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-10-21 18:20:25**: [補修正]A00-20181129001
  - 變更檔案: 1 個
- **2019-10-21 18:04:28**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-10-21 18:03:57**: [補修正]A00-***********
  - 變更檔案: 1 個
- **2019-10-18 10:38:43**: A00-20181129001 使用Ladp帳號，從Tiptop點選簽核狀況，連結至BPM上登入會出現請洽系統管理員
  - 變更檔案: 1 個
- **2019-10-17 10:46:18**: A00-*********** 修正組織設計師在部門人員多的情況點選離職，離職日期視窗等很久才出現
  - 變更檔案: 1 個
- **2019-10-16 11:03:18**: [補修正]A00-20190709001
  - 變更檔案: 1 個
- **2019-10-16 10:59:35**: A00-20190709001 修正連接線從否則變條件型別無法更改為黑色
  - 變更檔案: 1 個
- **2019-10-05 16:48:37**: A00-20190531001 修正流程代理人儲存筆數異常
  - 變更檔案: 1 個
- **2019-09-16 18:50:37**: A00-20190430002修正客製程式-表單選取人員出現無法派送的原因
  - 變更檔案: 1 個
- **2019-09-11 19:53:36**: A00-20190730008修正列印RWD表單title元件的Image列印問題
  - 變更檔案: 1 個
- **2019-09-09 15:22:59**: Q00-20190816001 修改樣版在使用上下移按鈕，單身資料無法帶入對應之欄位
  - 變更檔案: 1 個
- **2019-08-29 10:42:48**: A00-20190718002[補修正]
  - 變更檔案: 1 個
- **2019-08-28 18:08:11**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-28 18:07:00**: A00-20190718002 處理逾時活動時，先判斷是否當前時間逾時活動被處理掉了
  - 變更檔案: 1 個

### walter_wu (21 commits)

- **2019-10-25 18:15:52**: 補修正<第一次> S00-20180611002 從SQL代號排序調整成>>DB代號加SQL代號排序
  - 變更檔案: 1 個
- **2019-10-25 15:54:42**: A00-20191023002 簽核流程設計師增加儲存時檢查畫面與後端物件是否一致才能儲存
  - 變更檔案: 2 個
- **2019-10-24 11:12:44**: 補修正<第三次>S00-*********** 增加首次Trigger功能與發起結案紀錄、修正資料庫欄位長度
  - 變更檔案: 11 個
- **2019-10-23 18:42:45**: 補修正<第三次>S00-*********** 修正SQL、修正排程異常、維護作業日期改為可手動輸入
  - 變更檔案: 8 個
- **2019-10-23 09:41:35**: 補修正<第二次>S00-*********** 調整Log、解決跨交易鎖表、解決時間顯示問題
  - 變更檔案: 9 個
- **2019-10-21 18:02:56**: 補修正<第一次>S00-*********** 測試Code忘記拿掉
  - 變更檔案: 1 個
- **2019-10-21 17:49:32**: S00-*********** 監控流程設定、異動流程清單、整理異動流程清單排程
  - 變更檔案: 18 個
- **2019-09-23 17:36:34**: 補修正<第二次>C01-20190917001 調整變數範圍避免操作兩次轉XML
  - 變更檔案: 1 個
- **2019-09-23 16:55:36**: C01-20190917001 補上呼叫WS失敗LOG 原本並無流程資訊無法分辨是哪次req失敗
  - 變更檔案: 1 個
- **2019-09-20 11:01:44**: A00-20190425002 修正活動的進階只有勾選允許使用者增加前一活動卻可以向後加
  - 變更檔案: 1 個
- **2019-09-18 11:02:24**: S00-20180611002 SQL註冊器的排序改用SQL代號做排序
  - 變更檔案: 1 個
- **2019-09-16 19:18:29**: S00-20180515001 程式權限設定 套用範圍新增所有人員選項 另外修正三個BUG
  - 變更檔案: 3 個
- **2019-09-12 16:06:37**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-09-12 16:06:19**: ISO新版取消ISO.prop設定檔
  - 變更檔案: 1 個
- **2019-09-10 17:46:39**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-09-10 17:45:16**: 補修正<第二次>A00-20190826001 開會後決議補上附件相關操作到有機會用到的頁面
  - 變更檔案: 2 個
- **2019-09-09 16:06:55**: A00-20190826001 修正點擊下載TipTop Url類型附件沒有反應的問題(代辦/追蹤)
  - 變更檔案: 3 個
- **2019-09-05 12:01:11**: A00-20190729001 調整條件順序 避免多人每人都要處理 已有一人處理完導致 關卡設定不能取回卻可以取回重辦
  - 變更檔案: 1 個
- **2019-08-21 19:04:06**: A00-20190805001 修正如果隱藏Grid 無法發起或儲存表單(絕對位置/RWD)
  - 變更檔案: 3 個
- **2019-08-20 18:24:39**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-20 18:23:24**: Q00-20190820002 修正樹狀開窗多選 縮小視窗沒有checkBox可勾選的問題
  - 變更檔案: 1 個

### yamiyeh10 (27 commits)

- **2019-10-25 11:38:30**: V00-20191025001 修正移動端查看ESS表單附件時會卡控表單名稱
  - 變更檔案: 1 個
- **2019-10-22 17:02:23**: V00-*********** 調整企業微信詳情頁面浮動按鈕畫面
  - 變更檔案: 1 個
- **2019-10-22 16:12:12**: Q00-20190719001 二次調整Web表單設計師的行動版表單預覽註明彈窗裝置螢幕尺寸
  - 變更檔案: 1 個
- **2019-10-22 09:37:37**: V00-20191021002 修正在IMG中間層上開啟ESS附件會顯示錯誤訊息
  - 變更檔案: 1 個
- **2019-10-18 17:53:59**: V00-20191018001 修正因單選隱藏導致多選按鈕區塊消失
  - 變更檔案: 1 個
- **2019-10-18 09:40:39**: 優化IMG取得中間層資訊效能
  - 變更檔案: 1 個
- **2019-10-09 09:53:46**: 新增腳本樣版內容
  - 變更檔案: 2 個
- **2019-10-08 11:03:05**: C01-20190912003 修正企業微信在iOS手機查看附件時若包含不合法字元會urlencode導致異常
  - 變更檔案: 5 個
- **2019-10-08 10:46:26**: C01-20190912003 修正鼎捷移動在iOS手機查看附件時若包含不合法字元會urlencode導致異常
  - 變更檔案: 7 個
- **2019-10-07 13:59:11**: Q00-20190625001 <57>修正手機端在日期元件設定比對今天條件時功能異常問題
  - 變更檔案: 6 個
- **2019-09-23 19:18:37**: Q00-20190917001 修正行動端預覽功能
  - 變更檔案: 5 個
- **2019-09-23 10:12:28**: Q00-20190903001 補上漏修正IMG我的關注篩選條件接口
  - 變更檔案: 1 個
- **2019-09-20 15:38:01**: Q00-20190903001 修正IMG我的關注統計元件數量與處理的流程列表筆數不一致問題
  - 變更檔案: 1 個
- **2019-09-17 11:23:21**: Q00-20190916002 調整IMG快速簽核頁面上一關處理者的顯示機制
  - 變更檔案: 7 個
- **2019-09-16 11:28:34**: Q00-20190719001 調整Web表單設計師的行動版表單預覽註明彈窗裝置螢幕尺寸
  - 變更檔案: 3 個
- **2019-09-11 18:32:38**: 新增移動授權中間層連線資訊管理前端驗證連線狀態畫面
  - 變更檔案: 3 個
- **2019-09-10 16:52:14**: 調整IMG在快速簽核與詳情的上一關卡機制
  - 變更檔案: 12 個
- **2019-09-10 16:14:02**: 調整移動授權中間層使用者匯入功能使用者改抓對應的多語系姓名
  - 變更檔案: 1 個
- **2019-09-10 15:51:30**: Q00-20190910001 修正移動授權中間層使用者刪除後資料不會刷新問題
  - 變更檔案: 1 個
- **2019-09-09 18:43:05**: C01-20190904002 修正IMG處理的流程列表無法顯示問題
  - 變更檔案: 1 個
- **2019-09-03 10:58:04**: C01-20190902003 修正移動端ESS表單退回重辦時無簽核意見(詳情)
  - 變更檔案: 1 個
- **2019-08-28 14:49:31**: 新增移動授權中間層連線資訊與使用者資訊支援BPM功能
  - 變更檔案: 4 個
- **2019-08-23 18:09:38**: Q00-20190719002 修正Web表單設計師的行動版表單預覽功能
  - 變更檔案: 3 個
- **2019-08-22 17:45:40**: C01-20190821003 修正移動端多欄位跑版問題
  - 變更檔案: 1 個
- **2019-08-22 17:06:01**: 新增表單函式庫功能
  - 變更檔案: 4 個
- **2019-08-22 17:02:00**: 新增移動端支持客製Json開窗
  - 變更檔案: 12 個
- **2019-08-22 16:58:20**: 新增移動端支持Dialog元件自定義開窗
  - 變更檔案: 2 個

### yanann_chen (25 commits)

- **2019-10-24 17:19:07**: Q00-20191024001 補修正
  - 變更檔案: 1 個
- **2019-10-24 16:00:52**: Q00-20191024001 從被代理通知信進入BPM，無法取得流程及表單內容、無法取回工作
  - 變更檔案: 1 個
- **2019-10-21 18:18:21**: 修正英文列印表單失敗
  - 變更檔案: 2 個
- **2019-10-17 16:03:44**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-10-17 16:03:23**: ISO新版取消ISO.prop設定檔
  - 變更檔案: 1 個
- **2019-10-08 15:13:47**: C01-20190929001 簽核流程設計師多語系調整
  - 變更檔案: 12 個
- **2019-10-02 18:01:43**: A00-20190923001 修正重複執行登入request，造成使用者登入後畫面無法正確導入
  - 變更檔案: 1 個
- **2019-09-26 14:22:38**: C01-20190904001 修正第二次mail登入時，關卡設定簽核意見必填異常
  - 變更檔案: 2 個
- **2019-09-23 09:46:27**: C01-20190920001 修正CRM開單時單身空白處理邏輯異常導致表單畫面單身資料無法載入問題
  - 變更檔案: 1 個
- **2019-09-20 11:12:12**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-09-20 11:11:43**: Q00-20190920001 修正列印關卡中，絕對位置表單列印失效
  - 變更檔案: 1 個
- **2019-09-18 18:01:21**: A00-20190903002 修正人工任務關卡加簽，預覽流程圖無法顯示
  - 變更檔案: 1 個
- **2019-09-16 15:52:16**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-09-16 15:51:28**: Q00-20190827001 修正元件SubTab在頁籤點擊+時，會產生大量欄位樣版
  - 變更檔案: 1 個
- **2019-09-11 11:05:12**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-09-11 11:04:44**: C01-20190321001 修正轉派後發送通知，顯示工作內容失效
  - 變更檔案: 1 個
- **2019-09-05 18:34:12**: A00-20190829001 修正發起流程時，預覽流程圖無法正常顯示
  - 變更檔案: 5 個
- **2019-08-30 17:50:28**: C01-20190624003 修正核決層級關卡自動簽核異常
  - 變更檔案: 1 個
- **2019-08-30 11:02:12**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-30 11:01:48**: A00-20190523001 修正核決權限關卡參考自定義屬性異常
  - 變更檔案: 1 個
- **2019-08-22 16:02:38**: A00-20190409002[補修正] log調整
  - 變更檔案: 1 個
- **2019-08-22 14:40:18**: A00-20190409002 修正FormInstance裡的maskFieldValues是空字串導致管理流程報錯
  - 變更檔案: 1 個
- **2019-08-22 13:51:03**: A00-20190717001 修正單身加總異常
  - 變更檔案: 1 個
- **2019-08-22 11:43:29**: A00-20190704003 修正處理表單時，流程名稱多語系顯示失效問題
  - 變更檔案: 5 個
- **2019-08-15 11:07:42**: A00-20190806001 修正SUBTAB元件增加完頁籤之後，無法增加模板
  - 變更檔案: 1 個

### jerry1218 (11 commits)

- **2019-10-24 16:40:06**: 調整專案order順序讓設計師能直接以eclipse開啟
  - 變更檔案: 3 個
- **2019-10-03 17:48:57**: mail_sso功能-T100相關程式微調整
  - 變更檔案: 2 個
- **2019-10-03 10:44:59**: mail_sso功能-conf微調
  - 變更檔案: 1 個
- **2019-10-02 17:20:51**: 移除多餘System.out.println
  - 變更檔案: 1 個
- **2019-10-02 17:20:02**: mail_SSO功能
  - 變更檔案: 6 個
- **2019-09-26 14:05:16**: Q00-20190926003 修正簽核流設計器-刪除T100簽核樣板跟簽核樣板分類的訊息多語系消失
  - 變更檔案: 5 個
- **2019-09-20 16:07:17**: 還原T100FormMerge.java
  - 變更檔案: 1 個
- **2019-09-16 16:10:19**: V00-20190912004 修正T100整合,如原已轉換為RWD表單,又執行重要欄位同步的話,整個表單會因為轉回絕對位置,排版變很亂
  - 變更檔案: 1 個
- **2019-09-11 15:55:29**: V00-20190909036 修正撤銷流程功能,關卡有多處理者時顯示筆數不對
  - 變更檔案: 1 個
- **2019-09-11 15:54:27**: 修正待辦開簽核畫面優化導致的畫面顯示問題
  - 變更檔案: 2 個
- **2019-08-22 14:33:19**: 大幅提升待辦清單進入表單畫面速度
  - 變更檔案: 16 個

### pinchi_lin (19 commits)

- **2019-10-22 15:57:30**: V00-20191021003 修正BPM RESTful API問題
  - 變更檔案: 6 個
- **2019-10-22 11:54:02**: Q00-20191008001 修正IMG測試社區在智能快簽應用無法使用同意或不同意按鈕問題
  - 變更檔案: 3 個
- **2019-10-08 13:59:24**: A00-20191007001 修正IMG的提醒功能標題時間日期錯誤問題
  - 變更檔案: 1 個
- **2019-09-26 14:09:41**: Q00-20190926001 修正移動授權中間層依token取使用者的方法異常導致無法登入問題
  - 變更檔案: 2 個
- **2019-09-26 14:06:14**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-09-26 14:05:52**: Q00-20190926001 修正移動授權中間層依token取使用者的方法異常導致無法登入問題
  - 變更檔案: 2 個
- **2019-09-24 18:13:27**: 新增IMG批次簽核列表接口與批次簽核功能
  - 變更檔案: 16 個
- **2019-09-24 15:43:54**: Q00-20190812004 修正IMG從快簽進祥情表單後擱置按鈕不會出現的問題
  - 變更檔案: 1 個
- **2019-09-20 14:41:08**: Q00-20190909001 修正已轉派流程發起時間顯示異常問題
  - 變更檔案: 1 個
- **2019-09-19 15:14:54**: Q00-20190916001 調整移動授權中間層使用者維護匯入結果字體顏色
  - 變更檔案: 3 個
- **2019-09-19 11:07:42**: Q00-20190823001 調整行動簽核管理中心內各模組顯示卡控
  - 變更檔案: 2 個
- **2019-09-12 16:04:06**: 新增移動授權中間層連線資訊管理後端驗證連線功能
  - 變更檔案: 10 個
- **2019-09-10 15:05:39**: 新增移動授權中間層使用者匯入功能
  - 變更檔案: 13 個
- **2019-09-10 10:50:38**: 修正支援BPM驗證功能merge錯誤問題
  - 變更檔案: 1 個
- **2019-08-28 10:08:05**: A00-20190823003 修正在IMG中間層無法開啟CRM附件的問題
  - 變更檔案: 1 個
- **2019-08-27 18:37:29**: Q00-20190827003 修正相對位置表單grid有設定第一個欄位為流水號時點新增資料會有編輯畫面出不來的問題
  - 變更檔案: 2 個
- **2019-08-27 10:57:42**: 新增移動授權中間層BPM驗證登入功能
  - 變更檔案: 1 個
- **2019-08-20 15:49:11**: Q00-20190820001 修正移動授權中間層登入H5畫面與消息通知的多語系問題
  - 變更檔案: 4 個
- **2019-08-20 10:01:23**: 授權中間層還原調整前內容
  - 變更檔案: 1 個

### waynechang (17 commits)

- **2019-10-21 11:51:47**: 獨立模組增加透過session傳遞參數的方法
  - 變更檔案: 1 個
- **2019-10-17 16:47:25**: 移除NaNaISO.properties
  - 變更檔案: 1 個
- **2019-10-17 15:54:41**: C00-*********** 修正透過Sync同步ISO文件後，執行文件變更單時，無法載入附件
  - 變更檔案: 6 個
- **2019-09-23 14:12:15**: A00-20190911001 調整ESS-Invoke 判斷setStatus03邏輯
  - 變更檔案: 1 個
- **2019-09-17 16:33:05**: 調整BPM信件連結ExtraLogin帶使用者Id參數時，允許修改UserId欄位
  - 變更檔案: 1 個
- **2019-09-16 15:36:29**: 修正bpm tool切換成英文語系時，帳號設定的多語系位置顯示不一致
  - 變更檔案: 1 個
- **2019-09-16 14:21:23**: C00-20190910001 修正離職人員維護作業，若點擊人員下一頁時，系統發生錯誤。
  - 變更檔案: 1 個
- **2019-09-09 16:30:42**: C01-20190903002 調整PDFConverter timeOut時間
  - 變更檔案: 2 個
- **2019-09-06 14:08:00**: Q00-20190906001 增加BCL8 的PDFConverter服務
  - 變更檔案: 9 個
- **2019-08-30 14:20:32**: Q00-20190830001 waynechang 調整為取得release的流程OID
  - 變更檔案: 1 個
- **2019-08-28 11:01:13**: A00-20190801002 修正CRM整合流程設定流程變數PlantID時無效果
  - 變更檔案: 1 個
- **2019-08-26 10:56:25**: A00-20190823001 修正ISO透過工具匯入文件時，文件製作索引會失敗(取消從attachment取得資料的邏輯)
  - 變更檔案: 1 個
- **2019-08-26 10:32:49**: A00-20190823001] 修正ISO透過工具匯入文件時，文件製作索引會失敗
  - 變更檔案: 1 個
- **2019-08-22 15:28:18**: A00-20190813001 增加TIPTOP processTerminated的Restful服務
  - 變更檔案: 1 個
- **2019-08-22 09:52:26**: Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證(調整log)
  - 變更檔案: 1 個
- **2019-08-22 09:51:12**: Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證(調整log顯示)
  - 變更檔案: 1 個
- **2019-08-21 18:04:38**: Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證
  - 變更檔案: 1 個

### lorenchang (10 commits)

- **2019-10-16 00:37:19**: nana.database.type設定(MSSQL/Oracle)改成從standalone-full.xml內NaNaDS的driver取
  - 變更檔案: 3 個
- **2019-10-15 18:03:24**: 補上update sql遺漏的--，統一寫法
  - 變更檔案: 2 個
- **2019-10-15 18:01:04**: 移除update sql內--base on *******的備註
  - 變更檔案: 34 個
- **2019-10-03 11:21:48**: 取消S00-20190925003的相關調整，57版不會有問題，還原回先前版本
  - 變更檔案: 1 個
- **2019-09-26 11:34:50**: //S00-20190925003 lorenchang T100流程樣版人員及通知任務改成支持掛載T100表單
  - 變更檔案: 1 個
- **2019-09-18 17:27:05**: V00-20190918001 修正T100整合表單加入Image元件後會導致發單失敗
  - 變更檔案: 1 個
- **2019-09-12 10:09:21**: 將更新T100整設合設定的語法直接放到create Sql內
  - 變更檔案: 2 個
- **2019-09-11 10:42:32**: 調整widfly出貨預設上傳附件大小為100MB 補Oracle版
  - 變更檔案: 1 個
- **2019-09-11 10:39:00**: 調整widfly出貨預設上傳附件大小為100MB
  - 變更檔案: 1 個
- **2019-09-10 22:51:19**: V00-20190909016 修正某些電腦在[選取使用權限]的Dialog高度不夠導致看不到[是否包含子目錄]
  - 變更檔案: 1 個

### 王鵬程 (8 commits)

- **2019-10-05 11:24:29**: A00-20190828001 修正上傳的附件不符會出現上傳成功的訊息
  - 變更檔案: 2 個
- **2019-09-25 11:47:09**: A00-20190612002 修正小畫面時模擬流程的下關使用者沒更新左邊區塊上面的使用者資料
  - 變更檔案: 1 個
- **2019-09-12 18:46:55**: A00-20190614001 處理手持裝置操作時，點取回重辦無法操作
  - 變更檔案: 1 個
- **2019-09-09 19:18:03**: A00-20190614001 修正手持裝置操作時，點取回重辦後沒有出現提示訊息的問題。
  - 變更檔案: 1 個
- **2019-09-09 18:45:47**: A00-20190715003 修正系統設定為不可更改密碼時，但還能改密碼的問題。
  - 變更檔案: 1 個
- **2019-09-04 17:09:44**: A00-20190903003 在多語系上新增LDAP帳號設置相同時的資料
  - 變更檔案: 2 個
- **2019-08-30 10:55:04**: Q00-20190826007 在 BPMRsrcBundle5763 補上 reexecuteActivityMain.script.alert2這個Key
  - 變更檔案: 1 個
- **2019-08-26 18:36:15**: Q00-20190826007 修正 BPMRsrcBundle 中 缺少reexecuteActivityMain.script.alert2這個Key
  - 變更檔案: 1 個

### 詩雅 (2 commits)

- **2019-09-25 11:13:37**: Q00-20190821002 調整移動端客製開窗多選開窗下方按鈕跑版調整
  - 變更檔案: 1 個
- **2019-09-23 10:41:33**: Q00-20190821002 調整移動端客製開窗滑動到最下方要查看資料會無法看到完整資訊
  - 變更檔案: 1 個

### gaspard.shih (3 commits)

- **2019-09-12 11:42:16**: V00-20190909038 (v58下修至v57)修正關注流程無法匯出Excel的異常
  - 變更檔案: 2 個
- **2019-08-30 13:49:37**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-30 13:49:16**: A00-20190821003 修正T100表單轉換成RWD表單時的異常
  - 變更檔案: 1 個

### peng_cheng_wang (1 commits)

- **2019-08-29 19:17:57**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57

### BPM (2 commits)

- **2019-08-22 16:13:51**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-22 16:02:36**: A00-20190422002 修正流程設計師中無法設定行動關卡問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. 增加 mtsmodule00000000000000000000001 之模組icon
- **Commit ID**: `8db9dec6c5051dae89ef9bf2fe181cb73ad22595`
- **作者**: Catherine
- **日期**: 2019-10-28 15:21:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/_getModuleIcon.js`

### 2. A00-20190215003 組織設計師的部門核決層級修改，層級數字會亂跳
- **Commit ID**: `773e27e08748c906089494474897a6c319d4822f`
- **作者**: 林致帆
- **日期**: 2019-10-28 10:26:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java`

### 3. 補修正<第一次> S00-20180611002 從SQL代號排序調整成>>DB代號加SQL代號排序
- **Commit ID**: `acfac406c13139764aee36ff9c87c6ed5d0a8fea`
- **作者**: walter_wu
- **日期**: 2019-10-25 18:15:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`

### 4. A00-20190726001 調整響應式表單於mobile時，仍出現主旨欄位讓使用者可填寫
- **Commit ID**: `c204222a61ed0c54f3adde9769596af6da5573db`
- **作者**: Catherine
- **日期**: 2019-10-25 18:08:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 5. A00-20191023002 簽核流程設計師增加儲存時檢查畫面與後端物件是否一致才能儲存
- **Commit ID**: `7bfa3dd15bd8a7fc4b5c6e6b172ff46c2271c828`
- **作者**: walter_wu
- **日期**: 2019-10-25 15:54:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/CMManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`

### 6. V00-20191025001 修正移動端查看ESS表單附件時會卡控表單名稱
- **Commit ID**: `d0ad650db66959c88855ca24eae2a028601ddd9e`
- **作者**: yamiyeh10
- **日期**: 2019-10-25 11:38:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 7. Q00-20191024001 補修正
- **Commit ID**: `cfd46fdb0eecdd0cb5fc740673997ff9e37b8b15`
- **作者**: yanann_chen
- **日期**: 2019-10-24 17:19:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 8. 調整專案order順序讓設計師能直接以eclipse開啟
- **Commit ID**: `9087ef75559eda52c6154e3b4c83b4319e9d6d16`
- **作者**: jerry1218
- **日期**: 2019-10-24 16:40:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/.classpath`

### 9. Q00-20191024001 從被代理通知信進入BPM，無法取得流程及表單內容、無法取回工作
- **Commit ID**: `6ea65ff6239964c0dab4a6bfc3287bb246bd287a`
- **作者**: yanann_chen
- **日期**: 2019-10-24 16:00:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 10. [補修正]Q00-20190826005
- **Commit ID**: `e04bb96b6c4d58347895295902bdab12adaa1583`
- **作者**: 林致帆
- **日期**: 2019-10-24 12:31:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 11. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `f66e2b9280f4d61846107042a69d254a822894cb`
- **作者**: 林致帆
- **日期**: 2019-10-24 12:23:56
- **變更檔案數量**: 0

### 12. Q00-20190826005 表單設計師RWD使用title元件，顯示文字為空，再點選字體大小，LOGO會看不到
- **Commit ID**: `e57fd6309bacd11d1dab86dcf3de8b2577383fd2`
- **作者**: 林致帆
- **日期**: 2019-10-24 12:23:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 13. 補修正<第三次>S00-*********** 增加首次Trigger功能與發起結案紀錄、修正資料庫欄位長度
- **Commit ID**: `259e59d9f4c4ae1f10020b86f621d6c56e8f73cc`
- **作者**: walter_wu
- **日期**: 2019-10-24 11:12:44
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/monitor/BamProcessRecord.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_Oracle_1.sql`

### 14. 補修正<第三次>S00-*********** 修正SQL、修正排程異常、維護作業日期改為可手動輸入
- **Commit ID**: `be91bf208602c9c4d1353d72c0b8c4db2e721900`
- **作者**: walter_wu
- **日期**: 2019-10-23 18:42:45
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_bpm.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_Oracle_1.sql`

### 15. 補修正<第二次>S00-*********** 調整Log、解決跨交易鎖表、解決時間顯示問題
- **Commit ID**: `fb49a83b027e6662eedc54d0f042563d402062c0`
- **作者**: walter_wu
- **日期**: 2019-10-23 09:41:35
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BamManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Bam.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 16. V00-*********** 調整企業微信詳情頁面浮動按鈕畫面
- **Commit ID**: `de6493b1522f8bacecb6a3076cbfe66291f0c055`
- **作者**: yamiyeh10
- **日期**: 2019-10-22 17:02:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`

### 17. Q00-20190719001 二次調整Web表單設計師的行動版表單預覽註明彈窗裝置螢幕尺寸
- **Commit ID**: `aba84fed8017c8d779deb6ea5a0e5680959735da`
- **作者**: yamiyeh10
- **日期**: 2019-10-22 16:12:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`

### 18. V00-20191021003 修正BPM RESTful API問題
- **Commit ID**: `d71381304616bd28e8a0d217a6bee6647cb95cce`
- **作者**: pinchi_lin
- **日期**: 2019-10-22 15:57:30
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessTraceListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessTraceTotalParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 19. Q00-20191008001 修正IMG測試社區在智能快簽應用無法使用同意或不同意按鈕問題
- **Commit ID**: `9469206e91c3490b770f0342448a97efc09e3b9a`
- **作者**: pinchi_lin
- **日期**: 2019-10-22 11:54:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 20. V00-20191021002 修正在IMG中間層上開啟ESS附件會顯示錯誤訊息
- **Commit ID**: `432e6e7960bb7ccebd5096f193201d26f69a034b`
- **作者**: yamiyeh10
- **日期**: 2019-10-22 09:37:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 21. [補修正]A00-20181129001
- **Commit ID**: `22d6eb7f371529bd187bab7e7f18d8819f093abb`
- **作者**: 林致帆
- **日期**: 2019-10-21 18:41:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 22. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `03f9d5d42dfa2134daafdf0e3c5a14585820f7b6`
- **作者**: 林致帆
- **日期**: 2019-10-21 18:20:42
- **變更檔案數量**: 0

### 23. [補修正]A00-20181129001
- **Commit ID**: `f29773dc5336acda8b151e7a35428c2f17bb11af`
- **作者**: 林致帆
- **日期**: 2019-10-21 18:20:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 24. 修正英文列印表單失敗
- **Commit ID**: `76a86cd63d7ee3366881e0d0e23dfb28c7ef583d`
- **作者**: yanann_chen
- **日期**: 2019-10-21 18:18:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`

### 25. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `2a23315893b7d5b5745b04cb747aa7d6e50d0b5a`
- **作者**: 林致帆
- **日期**: 2019-10-21 18:04:28
- **變更檔案數量**: 0

### 26. [補修正]A00-***********
- **Commit ID**: `0f5d6ea2ec8f33a426b39f4b6d5578031d3522b5`
- **作者**: 林致帆
- **日期**: 2019-10-21 18:03:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`

### 27. 補修正<第一次>S00-*********** 測試Code忘記拿掉
- **Commit ID**: `3cb85c4de240b5d716592452d6fb63c6a36da8b7`
- **作者**: walter_wu
- **日期**: 2019-10-21 18:02:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java`

### 28. S00-*********** 監控流程設定、異動流程清單、整理異動流程清單排程
- **Commit ID**: `83d489f058f0a067dc021973836ba4e8b0a87270`
- **作者**: walter_wu
- **日期**: 2019-10-21 17:49:32
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BamManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/monitor/BamProcessRecord.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/monitor/BamSetting.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Bam.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BamMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_bpm.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql`

### 29. 獨立模組增加透過session傳遞參數的方法
- **Commit ID**: `25548c7712724fcb4816006c8a5b944403a941e5`
- **作者**: waynechang
- **日期**: 2019-10-21 11:51:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 30. V00-20191018001 修正因單選隱藏導致多選按鈕區塊消失
- **Commit ID**: `b1cb06a49c81ee56b7465ada83491a7ee79cd5fc`
- **作者**: yamiyeh10
- **日期**: 2019-10-18 17:53:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 31. A00-20181129001 使用Ladp帳號，從Tiptop點選簽核狀況，連結至BPM上登入會出現請洽系統管理員
- **Commit ID**: `19d5dbbb6ed82634d940683d14e5ae432bd683a8`
- **作者**: 林致帆
- **日期**: 2019-10-18 10:38:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 32. 優化IMG取得中間層資訊效能
- **Commit ID**: `1197758bf674d16860cddc9952f3dfbb5c4c4420`
- **作者**: yamiyeh10
- **日期**: 2019-10-18 09:40:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 33. 移除NaNaISO.properties
- **Commit ID**: `704679ffa566b299a8010c89a170ae4c3a26bf37`
- **作者**: waynechang
- **日期**: 2019-10-17 16:47:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaISO.properties`

### 34. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `c8f627cb3e4f611645c8436beadf8541bd722626`
- **作者**: yanann_chen
- **日期**: 2019-10-17 16:03:44
- **變更檔案數量**: 0

### 35. ISO新版取消ISO.prop設定檔
- **Commit ID**: `0f2aa8f189274ba3c5bfc5b3078f9a47a4b74a64`
- **作者**: yanann_chen
- **日期**: 2019-10-17 16:03:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 36. C00-*********** 修正透過Sync同步ISO文件後，執行文件變更單時，無法載入附件
- **Commit ID**: `d96954cef8e2cb4055e4631ed8e35c50f74b966e`
- **作者**: waynechang
- **日期**: 2019-10-17 15:54:41
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/DocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 37. A00-***********調整英文字
- **Commit ID**: `eebaa74aa44d0ee3fa061ef46181539027f2efb7`
- **作者**: Catherine
- **日期**: 2019-10-17 15:13:47
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 38. A00-*********** 修正組織設計師在部門人員多的情況點選離職，離職日期視窗等很久才出現
- **Commit ID**: `53f622ac6ea4a02fadc337be947056f5d56e732a`
- **作者**: 林致帆
- **日期**: 2019-10-17 10:46:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`

### 39. [補修正]A00-20190709001
- **Commit ID**: `02d64919bea0a242c3acd683f9a521754f3ae251`
- **作者**: 林致帆
- **日期**: 2019-10-16 11:03:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 40. A00-20190709001 修正連接線從否則變條件型別無法更改為黑色
- **Commit ID**: `28619cf9bfff950c6477dc10f2d2fcd5a530f370`
- **作者**: 林致帆
- **日期**: 2019-10-16 10:59:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 41. nana.database.type設定(MSSQL/Oracle)改成從standalone-full.xml內NaNaDS的driver取
- **Commit ID**: `0687f3729d60d1da849b99fd48be06604db3dd5d`
- **作者**: lorenchang
- **日期**: 2019-10-16 00:37:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql`

### 42. 補上update sql遺漏的--，統一寫法
- **Commit ID**: `d40df264df2b0ac0632ffd54d0203811b3d2df19`
- **作者**: lorenchang
- **日期**: 2019-10-15 18:03:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql`

### 43. 移除update sql內--base on *******的備註
- **Commit ID**: `9645bd81475803fa4195f0666b35c9b515381c48`
- **作者**: lorenchang
- **日期**: 2019-10-15 18:01:04
- **變更檔案數量**: 34
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.2.2_AppForm_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.2.2_AppForm_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@plm/update/5.7.2.2_PLM_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@plm/update/5.7.2.2_PLM_DDL_Oracle_1.sql`

### 44. Q00-20191015001 光碟內容移除bcl相關 jar
- **Commit ID**: `a818c9c35ecf0e98adcbba031c1c73c132e52e8f`
- **作者**: Catherine
- **日期**: 2019-10-15 15:07:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/module.xml`

### 45. Q00-20191015001 光碟內容移除bcl相關 jar
- **Commit ID**: `93b82d2c2b98f12d1fa246432bd94a1fb5fd44b5`
- **作者**: Catherine
- **日期**: 2019-10-15 15:07:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/easypdf-jacob.jar`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/easypdf.jar`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/jacob.jar`

### 46. 更新Oracle jdbc的版本與設定
- **Commit ID**: `b1460c10528d8f99b5f06eba9256efa5499b5ba0`
- **作者**: Catherine
- **日期**: 2019-10-15 11:41:18
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/module.xml`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/ojdbc6.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/ojdbc6_11g_releae2.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/ojdbc7_12c_release1.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/ojdbc8_12c_release2.jar`

### 47. 調整錯誤資訊
- **Commit ID**: `543aad7471e4bdcc3fed32d0ecb2b5a6fa74d089`
- **作者**: Catherine
- **日期**: 2019-10-14 17:43:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql`

### 48. 新增腳本樣版內容
- **Commit ID**: `dc920c13a57acc8596d7750a41aac09fe485a6a1`
- **作者**: yamiyeh10
- **日期**: 2019-10-09 09:53:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql`

### 49. C01-20190929001 簽核流程設計師多語系調整
- **Commit ID**: `e1846bf295743749dc2699178e3f30786a9e19d5`
- **作者**: yanann_chen
- **日期**: 2019-10-08 15:13:47
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_TW.properties`

### 50. A00-20191007001 修正IMG的提醒功能標題時間日期錯誤問題
- **Commit ID**: `1fb7c97c058977e785c18f7df5859d68e21cee0e`
- **作者**: pinchi_lin
- **日期**: 2019-10-08 13:59:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 51. C01-20190912003 修正企業微信在iOS手機查看附件時若包含不合法字元會urlencode導致異常
- **Commit ID**: `e4bb7defc019e532c1da93caf0e53e2a55c75b06`
- **作者**: yamiyeh10
- **日期**: 2019-10-08 11:03:05
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 52. C01-20190912003 修正鼎捷移動在iOS手機查看附件時若包含不合法字元會urlencode導致異常
- **Commit ID**: `30c59e869be4ddb07d0a9737f2a1d4014bfee8a8`
- **作者**: yamiyeh10
- **日期**: 2019-10-08 10:46:26
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 53. Q00-20190625001 <57>修正手機端在日期元件設定比對今天條件時功能異常問題
- **Commit ID**: `9a551d0a8ea9b6c95c2bc3993d825ff459d50087`
- **作者**: yamiyeh10
- **日期**: 2019-10-07 13:59:11
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDateTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 54. A00-20190531001 修正流程代理人儲存筆數異常
- **Commit ID**: `3f0acea75a112d166f691d858918b3abd9c9ee5c`
- **作者**: 林致帆
- **日期**: 2019-10-05 16:48:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/table/FlowSubstituteTableController.java`

### 55. A00-20190828001 修正上傳的附件不符會出現上傳成功的訊息
- **Commit ID**: `4a74c81937842e1c7c2296043e63e9ad4a335ee5`
- **作者**: 王鵬程
- **日期**: 2019-10-05 11:24:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 56. mail_sso功能-T100相關程式微調整
- **Commit ID**: `45d8cb7673b19f11d49f9162700530a41644637d`
- **作者**: jerry1218
- **日期**: 2019-10-03 17:48:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 57. 取消S00-20190925003的相關調整，57版不會有問題，還原回先前版本
- **Commit ID**: `9d23d895452bd01947ed04798c2ee09e06cacf6d`
- **作者**: lorenchang
- **日期**: 2019-10-03 11:21:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 58. mail_sso功能-conf微調
- **Commit ID**: `bf372fcde7dad14cd9628d54d477fe4cb7d109f7`
- **作者**: jerry1218
- **日期**: 2019-10-03 10:44:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties`

### 59. A00-20190923001 修正重複執行登入request，造成使用者登入後畫面無法正確導入
- **Commit ID**: `fa26c83ffb787fb5b5d08a98080b89673858dc65`
- **作者**: yanann_chen
- **日期**: 2019-10-02 18:01:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 60. 移除多餘System.out.println
- **Commit ID**: `2be2bac6c861d59afae86732fc260617e9bdb33c`
- **作者**: jerry1218
- **日期**: 2019-10-02 17:20:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 61. mail_SSO功能
- **Commit ID**: `884e642180f7d7e368f8a9987e3b7c58a26dd33a`
- **作者**: jerry1218
- **日期**: 2019-10-02 17:20:02
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties`

### 62. C01-20190904001 修正第二次mail登入時，關卡設定簽核意見必填異常
- **Commit ID**: `a930d85251f3c668c96dc2c3653e1ad79b46d49b`
- **作者**: yanann_chen
- **日期**: 2019-09-26 14:22:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 63. Q00-20190926001 修正移動授權中間層依token取使用者的方法異常導致無法登入問題
- **Commit ID**: `bf5f96e6a87adc3e5fc634e68f9afa91b9c874f0`
- **作者**: pinchi_lin
- **日期**: 2019-09-26 14:09:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java`

### 64. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `d1b2d8c7d0709fbe4f77f07135061ef15ff5f54a`
- **作者**: pinchi_lin
- **日期**: 2019-09-26 14:06:14
- **變更檔案數量**: 0

### 65. Q00-20190926001 修正移動授權中間層依token取使用者的方法異常導致無法登入問題
- **Commit ID**: `839a7c50e0be8152cb770ea1323ba979d6b3bfa2`
- **作者**: pinchi_lin
- **日期**: 2019-09-26 14:05:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java`

### 66. Q00-20190926003 修正簽核流設計器-刪除T100簽核樣板跟簽核樣板分類的訊息多語系消失
- **Commit ID**: `5f81bc34c5f0db3882a4d0027d94febac0275485`
- **作者**: jerry1218
- **日期**: 2019-09-26 14:05:16
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_zh_TW.properties`

### 67. //S00-20190925003 lorenchang T100流程樣版人員及通知任務改成支持掛載T100表單
- **Commit ID**: `36eb13deb74ff3d9ca16193818572b3f71462995`
- **作者**: lorenchang
- **日期**: 2019-09-26 11:34:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 68. A00-20190612002 修正小畫面時模擬流程的下關使用者沒更新左邊區塊上面的使用者資料
- **Commit ID**: `86d7b2305fecb8f42c3f3a490923012e0d8bc0c5`
- **作者**: 王鵬程
- **日期**: 2019-09-25 11:47:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 69. Q00-20190821002 調整移動端客製開窗多選開窗下方按鈕跑版調整
- **Commit ID**: `de5108f582ac5b9d95c35028f8c4b40d4658a870`
- **作者**: 詩雅
- **日期**: 2019-09-25 11:13:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 70. 新增IMG批次簽核列表接口與批次簽核功能
- **Commit ID**: `16a6809d84867a1f61b584247fea5502cb4c49dc`
- **作者**: pinchi_lin
- **日期**: 2019-09-24 18:13:27
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleBatchOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleBatchOpRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchOpMsgRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchOpRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataBatchOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataBatchOpRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 71. Q00-20190812004 修正IMG從快簽進祥情表單後擱置按鈕不會出現的問題
- **Commit ID**: `e79c8527959949ed93cbf8d225c74cf3c0a24d08`
- **作者**: pinchi_lin
- **日期**: 2019-09-24 15:43:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 72. Q00-20190917001 修正行動端預覽功能
- **Commit ID**: `8a9d6fb6b44164472f90211707315f81b9349e7d`
- **作者**: yamiyeh10
- **日期**: 2019-09-23 19:18:37
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 73. 補修正<第二次>C01-20190917001 調整變數範圍避免操作兩次轉XML
- **Commit ID**: `eaa7c29ea9f0f606f22b08be3eb0fecfb8020c74`
- **作者**: walter_wu
- **日期**: 2019-09-23 17:36:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 74. C01-20190917001 補上呼叫WS失敗LOG 原本並無流程資訊無法分辨是哪次req失敗
- **Commit ID**: `9954c9c0cdd11623af3547870ca57d182a8f928d`
- **作者**: walter_wu
- **日期**: 2019-09-23 16:55:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 75. A00-20190911001 調整ESS-Invoke 判斷setStatus03邏輯
- **Commit ID**: `47c8dedddef22a3c74b8c89ade1f41da6cfcabe7`
- **作者**: waynechang
- **日期**: 2019-09-23 14:12:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 76. Q00-20190821002 調整移動端客製開窗滑動到最下方要查看資料會無法看到完整資訊
- **Commit ID**: `35b138eef60f7969adf215d912a98c72f71877fd`
- **作者**: 詩雅
- **日期**: 2019-09-23 10:41:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 77. Q00-20190903001 補上漏修正IMG我的關注篩選條件接口
- **Commit ID**: `573a8d82a641f8c4d691476609fed1d6227c7519`
- **作者**: yamiyeh10
- **日期**: 2019-09-23 10:12:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 78. C01-20190920001 修正CRM開單時單身空白處理邏輯異常導致表單畫面單身資料無法載入問題
- **Commit ID**: `7f12be0ef00ea24636f82b8037d06a73ddf1bc5e`
- **作者**: yanann_chen
- **日期**: 2019-09-23 09:46:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java`

### 79. 整理程式邏輯
- **Commit ID**: `84f9f628b8a19195044fa32323d5ac12d37ab697`
- **作者**: Catherine
- **日期**: 2019-09-20 16:37:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`

### 80. 還原T100FormMerge.java
- **Commit ID**: `f9eac61a33cc72154c85f0400f7296feffdae14e`
- **作者**: jerry1218
- **日期**: 2019-09-20 16:07:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/T100FormMerge.java`

### 81. Q00-20190903001 修正IMG我的關注統計元件數量與處理的流程列表筆數不一致問題
- **Commit ID**: `2beef4396d5b60cbbd52a3f4b600a4d2215dc0d8`
- **作者**: yamiyeh10
- **日期**: 2019-09-20 15:38:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 82. Q00-20190909001 修正已轉派流程發起時間顯示異常問題
- **Commit ID**: `d9a3a55fa851a78920a826a70b5d9bcd33af352e`
- **作者**: pinchi_lin
- **日期**: 2019-09-20 14:41:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 83. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `addba78682aeb98ad83b9e99526070c068610267`
- **作者**: yanann_chen
- **日期**: 2019-09-20 11:12:12
- **變更檔案數量**: 0

### 84. Q00-20190920001 修正列印關卡中，絕對位置表單列印失效
- **Commit ID**: `2afd67a22e999d93e60d15dc7d7e5d53e7d8a916`
- **作者**: yanann_chen
- **日期**: 2019-09-20 11:11:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`

### 85. A00-20190425002 修正活動的進階只有勾選允許使用者增加前一活動卻可以向後加
- **Commit ID**: `0737a50d18318668e7cafb835d196d05013ec921`
- **作者**: walter_wu
- **日期**: 2019-09-20 11:01:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 86. Q00-20190916001 調整移動授權中間層使用者維護匯入結果字體顏色
- **Commit ID**: `9e216d3357b33a9015435c05dac9bb63a1eced32`
- **作者**: pinchi_lin
- **日期**: 2019-09-19 15:14:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp`

### 87. Q00-20190823001 調整行動簽核管理中心內各模組顯示卡控
- **Commit ID**: `65df2fdc94b537e305122e81125fd44b7d727e85`
- **作者**: pinchi_lin
- **日期**: 2019-09-19 11:07:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 88. A00-20190903002 修正人工任務關卡加簽，預覽流程圖無法顯示
- **Commit ID**: `a6dafd69713f80f0c2ed1023643acc084547b5ae`
- **作者**: yanann_chen
- **日期**: 2019-09-18 18:01:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 89. V00-20190918001 修正T100整合表單加入Image元件後會導致發單失敗
- **Commit ID**: `fc4921aec2317b9300ac300244fc83928ffd854d`
- **作者**: lorenchang
- **日期**: 2019-09-18 17:27:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 90. S00-20180611002 SQL註冊器的排序改用SQL代號做排序
- **Commit ID**: `12fd3ef7586b471de2d888ac8d01d14867a767d8`
- **作者**: walter_wu
- **日期**: 2019-09-18 11:02:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`

### 91. 調整BPM信件連結ExtraLogin帶使用者Id參數時，允許修改UserId欄位
- **Commit ID**: `78592947954a3cb5e229e5071b030a48be9a3dd6`
- **作者**: waynechang
- **日期**: 2019-09-17 16:33:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`

### 92. Q00-20190916002 調整IMG快速簽核頁面上一關處理者的顯示機制
- **Commit ID**: `e7b93c33cc0cef7691c9ba89eccead5ec791e2f0`
- **作者**: yamiyeh10
- **日期**: 2019-09-17 11:23:21
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 93. S00-20180515001 程式權限設定 套用範圍新增所有人員選項 另外修正三個BUG
- **Commit ID**: `7ba1a4f74eefeabd2569198b30954ab26b3b0e79`
- **作者**: walter_wu
- **日期**: 2019-09-16 19:18:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ProgramAccessRightVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`

### 94. A00-20190430002修正客製程式-表單選取人員出現無法派送的原因
- **Commit ID**: `2148ca21318784ec2b0eed3cfd9f0b404381a4d1`
- **作者**: 林致帆
- **日期**: 2019-09-16 18:50:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`

### 95. V00-20190912004 修正T100整合,如原已轉換為RWD表單,又執行重要欄位同步的話,整個表單會因為轉回絕對位置,排版變很亂
- **Commit ID**: `fa2317e223caecfd87b6aeb49d9c5f5cf9580725`
- **作者**: jerry1218
- **日期**: 2019-09-16 16:10:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/T100FormMerge.java`

### 96. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a8b9dbb9605e4f610ce8ac4d466e86f4eff813da`
- **作者**: yanann_chen
- **日期**: 2019-09-16 15:52:16
- **變更檔案數量**: 0

### 97. Q00-20190827001 修正元件SubTab在頁籤點擊+時，會產生大量欄位樣版
- **Commit ID**: `3c03a5c96bbf1b43933d478205e8094449233b04`
- **作者**: yanann_chen
- **日期**: 2019-09-16 15:51:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 98. 修正bpm tool切換成英文語系時，帳號設定的多語系位置顯示不一致
- **Commit ID**: `93edd6298b6e49448e925edd13138aa0ea599f40`
- **作者**: waynechang
- **日期**: 2019-09-16 15:36:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties`

### 99. C00-20190910001 修正離職人員維護作業，若點擊人員下一頁時，系統發生錯誤。
- **Commit ID**: `8ff8c6d413d63cf9ae01b0412d6e5548a16dd7be`
- **作者**: waynechang
- **日期**: 2019-09-16 14:21:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java`

### 100. Q00-20190719001 調整Web表單設計師的行動版表單預覽註明彈窗裝置螢幕尺寸
- **Commit ID**: `4ac0fe0113db2f68f6fb71e3e0170f6d12771a4d`
- **作者**: yamiyeh10
- **日期**: 2019-09-16 11:28:34
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 101. A00-20190614001 處理手持裝置操作時，點取回重辦無法操作
- **Commit ID**: `60884f2adb9965b67f7de817b5ca613d95339339`
- **作者**: 王鵬程
- **日期**: 2019-09-12 18:46:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`

### 102. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `e9b02b204cd92b7757054b349a46561e15dd423b`
- **作者**: walter_wu
- **日期**: 2019-09-12 16:06:37
- **變更檔案數量**: 0

### 103. ISO新版取消ISO.prop設定檔
- **Commit ID**: `a0c5e293e4e193e9221a4979567e4b1c6c1475e4`
- **作者**: walter_wu
- **日期**: 2019-09-12 16:06:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 104. 新增移動授權中間層連線資訊管理後端驗證連線功能
- **Commit ID**: `f128c45a95513c745bab84bdab9efaf4227a9a28`
- **作者**: pinchi_lin
- **日期**: 2019-09-12 16:04:06
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 105. V00-20190909038 (v58下修至v57)修正關注流程無法匯出Excel的異常
- **Commit ID**: `673433aa92db4fa7155fe9ab715517c1aa96e107`
- **作者**: gaspard.shih
- **日期**: 2019-09-12 11:42:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`

### 106. 將更新T100整設合設定的語法直接放到create Sql內
- **Commit ID**: `decdd9da0ad4aa5d1a68599fb138dbb1544b2ac8`
- **作者**: lorenchang
- **日期**: 2019-09-12 10:09:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@t100/create/InitT100DB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@t100/create/InitT100DB_SQLServer.sql`

### 107. A00-20190730008修正列印RWD表單title元件的Image列印問題
- **Commit ID**: `342a8e595bb9a589d662eb30d1649cc0ab290831`
- **作者**: 林致帆
- **日期**: 2019-09-11 19:53:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 108. 新增移動授權中間層連線資訊管理前端驗證連線狀態畫面
- **Commit ID**: `791a33be20e4763a23ee73d724b040e9de07b638`
- **作者**: yamiyeh10
- **日期**: 2019-09-11 18:32:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 109. V00-20190909036 修正撤銷流程功能,關卡有多處理者時顯示筆數不對
- **Commit ID**: `b8298b4e5bf0b3a8d893908cf1556bbab1cf783e`
- **作者**: jerry1218
- **日期**: 2019-09-11 15:55:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 110. 修正待辦開簽核畫面優化導致的畫面顯示問題
- **Commit ID**: `7400c86bd7ed2726dbbefea39508b1dce48cf7bb`
- **作者**: jerry1218
- **日期**: 2019-09-11 15:54:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`

### 111. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `999b5aaf6b32bb607d0ec017a65eb79f7f770a4c`
- **作者**: yanann_chen
- **日期**: 2019-09-11 11:05:12
- **變更檔案數量**: 0

### 112. C01-20190321001 修正轉派後發送通知，顯示工作內容失效
- **Commit ID**: `fc19f8d23928fe4a34da5854abbd33c739d9be29`
- **作者**: yanann_chen
- **日期**: 2019-09-11 11:04:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 113. 調整widfly出貨預設上傳附件大小為100MB 補Oracle版
- **Commit ID**: `ba59b5b4d086735db75d9c4feae1cdf36849f233`
- **作者**: lorenchang
- **日期**: 2019-09-11 10:42:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 114. 調整widfly出貨預設上傳附件大小為100MB
- **Commit ID**: `052890df96cac217af7fc75cabb49a9e1d501edb`
- **作者**: lorenchang
- **日期**: 2019-09-11 10:39:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full.xml`

### 115. V00-20190909016 修正某些電腦在[選取使用權限]的Dialog高度不夠導致看不到[是否包含子目錄]
- **Commit ID**: `6ebc79d0631faab5cafd81a2a46289f0032b9dd5`
- **作者**: lorenchang
- **日期**: 2019-09-10 22:51:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/accessCtrl/AccessCtrlMgrPanel.java`

### 116. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `888673b2ff574764a938b77bbf45f106cdd82eb8`
- **作者**: walter_wu
- **日期**: 2019-09-10 17:46:39
- **變更檔案數量**: 0

### 117. 補修正<第二次>A00-20190826001 開會後決議補上附件相關操作到有機會用到的頁面
- **Commit ID**: `2c5fb88a6643a9424699e0acb0a73ad65d5f6033`
- **作者**: walter_wu
- **日期**: 2019-09-10 17:45:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 118. 調整IMG在快速簽核與詳情的上一關卡機制
- **Commit ID**: `a76a7999db5095630aad2a8cea0d8ed9921abd36`
- **作者**: yamiyeh10
- **日期**: 2019-09-10 16:52:14
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 119. 調整移動授權中間層使用者匯入功能使用者改抓對應的多語系姓名
- **Commit ID**: `d058fd0108b727ab7530f0e49516c6f3f7fed22d`
- **作者**: yamiyeh10
- **日期**: 2019-09-10 16:14:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`

### 120. Q00-20190910001 修正移動授權中間層使用者刪除後資料不會刷新問題
- **Commit ID**: `b26333728e5e4cbc14e901afb174a5082cc92cd9`
- **作者**: yamiyeh10
- **日期**: 2019-09-10 15:51:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp`

### 121. 新增移動授權中間層使用者匯入功能
- **Commit ID**: `6cd56bcd049b75a23d69b928a521987f02372363`
- **作者**: pinchi_lin
- **日期**: 2019-09-10 15:05:39
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 122. 修正支援BPM驗證功能merge錯誤問題
- **Commit ID**: `984816b345bdb89359c4f72a5ef4c32c8c270b66`
- **作者**: pinchi_lin
- **日期**: 2019-09-10 10:50:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js`

### 123. A00-20190614001 修正手持裝置操作時，點取回重辦後沒有出現提示訊息的問題。
- **Commit ID**: `028fc3c674fab3fe9f10ac6a0d87903ea1316322`
- **作者**: 王鵬程
- **日期**: 2019-09-09 19:18:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`

### 124. A00-20190715003 修正系統設定為不可更改密碼時，但還能改密碼的問題。
- **Commit ID**: `4154f6b977221c5c7fde952af8de5607079f3dcd`
- **作者**: 王鵬程
- **日期**: 2019-09-09 18:45:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 125. C01-20190904002 修正IMG處理的流程列表無法顯示問題
- **Commit ID**: `d1e63928282b7fc5816f31b715eb223ea73988af`
- **作者**: yamiyeh10
- **日期**: 2019-09-09 18:43:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 126. C01-20190903002 調整PDFConverter timeOut時間
- **Commit ID**: `a6675b0d6080ff25461bae88faaada69370966de`
- **作者**: waynechang
- **日期**: 2019-09-09 16:30:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 127. A00-20190826001 修正點擊下載TipTop Url類型附件沒有反應的問題(代辦/追蹤)
- **Commit ID**: `33fa2a56d1de120db8ca612fba0ed2f73d58ff8b`
- **作者**: walter_wu
- **日期**: 2019-09-09 16:06:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 128. Q00-20190816001 修改樣版在使用上下移按鈕，單身資料無法帶入對應之欄位
- **Commit ID**: `8ee8062b723fe85f9076c5ce7ab770ac8c36e5e8`
- **作者**: 林致帆
- **日期**: 2019-09-09 15:22:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 129. Q00-20190906001 增加BCL8 的PDFConverter服務
- **Commit ID**: `600d77b846ba7971edb63c8bf29150bf2989f336`
- **作者**: waynechang
- **日期**: 2019-09-06 14:08:00
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/PDF/easypdf.jar`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDFConverter.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/easypdf.jar`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/module.xml`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql`

### 130. A00-20190829001 修正發起流程時，預覽流程圖無法正常顯示
- **Commit ID**: `2737c418e403c25d8b73feb5bf2e60d6b9ddcf2a`
- **作者**: yanann_chen
- **日期**: 2019-09-05 18:34:12
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 131. A00-20190729001 調整條件順序 避免多人每人都要處理 已有一人處理完導致 關卡設定不能取回卻可以取回重辦
- **Commit ID**: `cf99869f42e619545e3c5ac9feec2e9965429bbe`
- **作者**: walter_wu
- **日期**: 2019-09-05 12:01:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 132. A00-20190903003 在多語系上新增LDAP帳號設置相同時的資料
- **Commit ID**: `1d3894195643c81ca85f121d90b37d4141138218`
- **作者**: 王鵬程
- **日期**: 2019-09-04 17:09:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 133. C01-20190902003 修正移動端ESS表單退回重辦時無簽核意見(詳情)
- **Commit ID**: `30e4ed988ba613eeb72777f2660aeaf1e42e5377`
- **作者**: yamiyeh10
- **日期**: 2019-09-03 10:58:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 134. traceProcessMain.script.alert4 英文語系沒寫對
- **Commit ID**: `d7b1cd43c9fe867a37a9741a87440dbabd23ce0a`
- **作者**: Catherine
- **日期**: 2019-09-02 16:57:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 135. C01-20190624003 修正核決層級關卡自動簽核異常
- **Commit ID**: `3b8809fdad8b12a4ce72526a98aab71648a4e4e1`
- **作者**: yanann_chen
- **日期**: 2019-08-30 17:50:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 136. Q00-20190830001 waynechang 調整為取得release的流程OID
- **Commit ID**: `248ec20752ec3caf4e75a17cc66a00dbd01f2b22`
- **作者**: waynechang
- **日期**: 2019-08-30 14:20:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 137. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a46bbccaf696f71c0a15e4a7e3727df49010935d`
- **作者**: gaspard.shih
- **日期**: 2019-08-30 13:49:37
- **變更檔案數量**: 0

### 138. A00-20190821003 修正T100表單轉換成RWD表單時的異常
- **Commit ID**: `99a3322044ea71883ac8ee3013680acd2683ac44`
- **作者**: gaspard.shih
- **日期**: 2019-08-30 13:49:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`

### 139. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `419897a53f56d9ab292b880a8d54956eded1aa0f`
- **作者**: Catherine
- **日期**: 2019-08-30 12:21:33
- **變更檔案數量**: 0

### 140. 補上 apmt530
- **Commit ID**: `415e2e5e585de1df3970fa4a6e797fdb48d58b6e`
- **作者**: Catherine
- **日期**: 2019-08-30 12:20:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form-default-t100.zip`

### 141. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `baa6532604afc9b29cd3bacba6e22296698db5ec`
- **作者**: yanann_chen
- **日期**: 2019-08-30 11:02:12
- **變更檔案數量**: 0

### 142. A00-20190523001 修正核決權限關卡參考自定義屬性異常
- **Commit ID**: `e92b55793f444227174411e537510caa107a53ad`
- **作者**: yanann_chen
- **日期**: 2019-08-30 11:01:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 143. Q00-20190826007 在 BPMRsrcBundle5763 補上 reexecuteActivityMain.script.alert2這個Key
- **Commit ID**: `7c9cdb03ebd6babf1f99ebd6243d6aebd2b0a2a9`
- **作者**: 王鵬程
- **日期**: 2019-08-30 10:55:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 144. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `e4595cf05dc5d1cb1aa889030137d4910fc2ddf2`
- **作者**: peng_cheng_wang
- **日期**: 2019-08-29 19:17:57
- **變更檔案數量**: 0

### 145. A00-20190718002[補修正]
- **Commit ID**: `92f2a3e2b9131f024a128ac5fdffe448a34d4c5e`
- **作者**: 林致帆
- **日期**: 2019-08-29 10:42:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 146. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `1e7cf0ed3857830e42c7484dba401132324ce2c1`
- **作者**: 林致帆
- **日期**: 2019-08-28 18:08:11
- **變更檔案數量**: 0

### 147. A00-20190718002 處理逾時活動時，先判斷是否當前時間逾時活動被處理掉了
- **Commit ID**: `66cfea313a0a7c39b070666d1bb13820b6adccbb`
- **作者**: 林致帆
- **日期**: 2019-08-28 18:07:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 148. 新增移動授權中間層連線資訊與使用者資訊支援BPM功能
- **Commit ID**: `52c038679d4ce18fa912e3378f74bdb082bdbfa6`
- **作者**: yamiyeh10
- **日期**: 2019-08-28 14:49:31
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js`

### 149. A00-20190801002 修正CRM整合流程設定流程變數PlantID時無效果
- **Commit ID**: `ecd2d2d1364af74ffb81769780d194dd8b5c0a28`
- **作者**: waynechang
- **日期**: 2019-08-28 11:01:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java`

### 150. A00-20190823003 修正在IMG中間層無法開啟CRM附件的問題
- **Commit ID**: `6867c7b309db6caf990a3b7354381fa9f2341e16`
- **作者**: pinchi_lin
- **日期**: 2019-08-28 10:08:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 151. Q00-20190827003 修正相對位置表單grid有設定第一個欄位為流水號時點新增資料會有編輯畫面出不來的問題
- **Commit ID**: `aa25172a209ab3707031fc7d194beebb9c1d62c4`
- **作者**: pinchi_lin
- **日期**: 2019-08-27 18:37:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 152. 新增移動授權中間層BPM驗證登入功能
- **Commit ID**: `d8c31fd3d29f27faff99c70c7c4bed191aab1922`
- **作者**: pinchi_lin
- **日期**: 2019-08-27 10:57:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`

### 153. Q00-20190826007 修正 BPMRsrcBundle 中 缺少reexecuteActivityMain.script.alert2這個Key
- **Commit ID**: `cc9c3a9d37b3d5dc892508566ec23b3da9f93c2e`
- **作者**: 王鵬程
- **日期**: 2019-08-26 18:36:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 154. A00-20190823001 修正ISO透過工具匯入文件時，文件製作索引會失敗(取消從attachment取得資料的邏輯)
- **Commit ID**: `386eb08624a4e8f39523539c63e72e6c873980eb`
- **作者**: waynechang
- **日期**: 2019-08-26 10:56:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`

### 155. A00-20190823001] 修正ISO透過工具匯入文件時，文件製作索引會失敗
- **Commit ID**: `fe1668555e93b27425ec9946b228df942fa9f6dd`
- **作者**: waynechang
- **日期**: 2019-08-26 10:32:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`

### 156. Q00-20190719002 修正Web表單設計師的行動版表單預覽功能
- **Commit ID**: `7fb65c65fdde82ebf880462945c56594b6a3e2a1`
- **作者**: yamiyeh10
- **日期**: 2019-08-23 18:09:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 157. A00-20190823002 修正語法錯誤
- **Commit ID**: `b065f2d0615a663303461c707c21b123a24abf3b`
- **作者**: Catherine
- **日期**: 2019-08-23 10:54:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 158. C01-20190821003 修正移動端多欄位跑版問題
- **Commit ID**: `3ce8388e484105c772d4c5892976df65af527abd`
- **作者**: yamiyeh10
- **日期**: 2019-08-22 17:45:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css`

### 159. 新增表單函式庫功能
- **Commit ID**: `54b8fef33f0c14690c7419889b5b9abe549b3b71`
- **作者**: yamiyeh10
- **日期**: 2019-08-22 17:06:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 160. 新增移動端支持客製Json開窗
- **Commit ID**: `058924e137602020885baf19d1932aa61de678bf`
- **作者**: yamiyeh10
- **日期**: 2019-08-22 17:02:00
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 161. 新增移動端支持Dialog元件自定義開窗
- **Commit ID**: `6f8b1547cfdc8a00ba3cc861c44b73bd99dd0dca`
- **作者**: yamiyeh10
- **日期**: 2019-08-22 16:58:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 162. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `d9cc25bc08fafa2f609c7b547a8c74f10218feb1`
- **作者**: BPM
- **日期**: 2019-08-22 16:13:51
- **變更檔案數量**: 0

### 163. A00-20190409002[補修正] log調整
- **Commit ID**: `0b905556e5c7ad29ac75927d9772e6228c918404`
- **作者**: yanann_chen
- **日期**: 2019-08-22 16:02:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/absFormInstance.java`

### 164. A00-20190422002 修正流程設計師中無法設定行動關卡問題
- **Commit ID**: `8ecd116ef40875e6996b28fa28e6e356da87de8c`
- **作者**: BPM
- **日期**: 2019-08-22 16:02:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`

### 165. A00-20190813001 增加TIPTOP processTerminated的Restful服務
- **Commit ID**: `8cbc4a0f1e0e70f28d11b3727622126333e3ac87`
- **作者**: waynechang
- **日期**: 2019-08-22 15:28:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/TIPTOP.java`

### 166. A00-20190409002 修正FormInstance裡的maskFieldValues是空字串導致管理流程報錯
- **Commit ID**: `c71a68f6feede71b08368abeb76db88e16943a37`
- **作者**: yanann_chen
- **日期**: 2019-08-22 14:40:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/absFormInstance.java`

### 167. 大幅提升待辦清單進入表單畫面速度
- **Commit ID**: `c7e2c2a3ab5476d650a94cdeb31f9f119bbef036`
- **作者**: jerry1218
- **日期**: 2019-08-22 14:33:19
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessContext.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/background_service/PrefetchProcessInstService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp`

### 168. A00-20190717001 修正單身加總異常
- **Commit ID**: `d51925c5b038738212f316dc719a352011e71442`
- **作者**: yanann_chen
- **日期**: 2019-08-22 13:51:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 169. A00-20190704003 修正處理表單時，流程名稱多語系顯示失效問題
- **Commit ID**: `9212bf9ae7aef74105aa732d47d36ca16ea299d8`
- **作者**: yanann_chen
- **日期**: 2019-08-22 11:43:29
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`

### 170. Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證(調整log)
- **Commit ID**: `70dd464af3e6743e0e3058d94415d7d46f2e5fbe`
- **作者**: waynechang
- **日期**: 2019-08-22 09:52:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 171. Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證(調整log顯示)
- **Commit ID**: `78febba72a69da62be1f135cad74978552bfe8b3`
- **作者**: waynechang
- **日期**: 2019-08-22 09:51:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 172. A00-20190805001 修正如果隱藏Grid 無法發起或儲存表單(絕對位置/RWD)
- **Commit ID**: `9f7b5171e1a11609f1c4dae9e79294ed9a454b98`
- **作者**: walter_wu
- **日期**: 2019-08-21 19:04:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`

### 173. Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證
- **Commit ID**: `3d6b97a11693730755c7f7cb3d8c74c76ec5200e`
- **作者**: waynechang
- **日期**: 2019-08-21 18:04:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 174. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `2a596f3bf16bbdb52d729d11a564e4736aed66dd`
- **作者**: walter_wu
- **日期**: 2019-08-20 18:24:39
- **變更檔案數量**: 0

### 175. Q00-20190820002 修正樹狀開窗多選 縮小視窗沒有checkBox可勾選的問題
- **Commit ID**: `afd5a9eb329151bfc000b678acb75a1ac21f67ad`
- **作者**: walter_wu
- **日期**: 2019-08-20 18:23:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`

### 176. A00-20190816001 範例程式調整
- **Commit ID**: `b3419c9b862cc148208cc75af4da8e8406f76e14`
- **作者**: Catherine
- **日期**: 2019-08-20 18:22:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`

### 177. A00-20190816001 範例程式調整
- **Commit ID**: `432c81f5b05643667cbcd26a20828d3036b88bad`
- **作者**: Catherine
- **日期**: 2019-08-20 16:13:59
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/Document/Form/RadioButtonExample.jsp`

### 178. Q00-20190820001 修正移動授權中間層登入H5畫面與消息通知的多語系問題
- **Commit ID**: `93bb9c1d1cc5a3f4cc11529fbc18c72b31be515c`
- **作者**: pinchi_lin
- **日期**: 2019-08-20 15:49:11
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls`

### 179. 授權中間層還原調整前內容
- **Commit ID**: `845d781b00b162b3148c01c7c62839da0e7d06c6`
- **作者**: pinchi_lin
- **日期**: 2019-08-20 10:01:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`

### 180. A00-20190806001 修正SUBTAB元件增加完頁籤之後，無法增加模板
- **Commit ID**: `6dd918b3c32343a7a55a5a52f413536367034ca4`
- **作者**: yanann_chen
- **日期**: 2019-08-15 11:07:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

