{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.5.2_cnce", "date": "2022-12-07 11:50:51", "message": "[BPM APP]C01-20221202005 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題", "author": "郭哲榮"}, "舊分支": {"branch_name": "release_5.8.5.2", "date": "2022-06-26 22:20:59", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2", "author": "lorenchang"}, "比較時間": "2025-07-18 11:23:10", "新增commit數量": 38, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "0daef0ca08c957d1af1943a4f54757a17c8445bd", "commit_訊息": "[BPM APP]C01-20221202005 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題", "提交日期": "2022-12-07 11:50:51", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cb4ed3e8026abc758552f90d69afafe3bad19bc5", "commit_訊息": "[BPM APP]C01-20220627005 修正IMG中間層Grid元件的itemOrder有0時會造成欄位順序錯亂問題", "提交日期": "2022-07-04 18:02:57", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d353b1f4a32883ef51838fae436af4616a640e63", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2", "提交日期": "2022-07-04 16:40:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "edfe5207a9b545afef4b85cdeba9bd250b4c8c12", "commit_訊息": "[內部]Q00-20220624001 調整AutomaticSignOffMaintanceManagerBean NaNaLog內容", "提交日期": "2022-06-24 15:43:20", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1722e0a94ed5e05b18e24857febfa2feca441cc2", "commit_訊息": "[流程引擎]S00-*********** 新增「自動簽核異常處理」功能", "提交日期": "2022-05-03 12:01:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AutomaticSignOffMaintanceDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManagerLocal.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/AutomaticSignOffMaintance.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5.8.9.1.xlsx", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.9.8.1_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 10}, {"commit_hash": "1b9fd51d92ff4e2c57113a91d96f7d5aac61d1f3", "commit_訊息": "[流程引擎]提高58版Queue併發承載量", "提交日期": "2022-03-28 16:25:37", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocConvertWithFileHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/MessageHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/QueueHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/BatchNoticeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/DeleteClosedProcessInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/E10SendSignInfoBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/EventDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/FormInstanceTransformerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/McloudPushInvokeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/NoCmDocumentsBackgroundServiceBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/TiptopCleanDocumentBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 20}, {"commit_hash": "3122c6bd4d79bfb078936f53426028c38e26df99", "commit_訊息": "[ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態", "提交日期": "2022-02-10 17:51:10", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82a64edb761fb6f75dd0a296c591ebeb53ab454d", "commit_訊息": "[流程引擎]Q00-20220111002 修正多人關卡在執行自動簽核時，偶發的沒有押上簽核意見或簽核意見押到正常簽核的工作上的問題", "提交日期": "2022-01-11 16:58:59", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "baddbd809183ce0df08a0070edb34a0b16c48af1", "commit_訊息": "[流程引擎]Q00-20211220002 修正客戶附件遺失問題", "提交日期": "2022-01-10 17:19:15", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "41829c522a3c80c9bd7abd55c6bee3c74c4e2977", "commit_訊息": "[ESS]Q00-20211026002 調整BPM呼叫ESS存檔前的判斷，防止同單據在ESS與BPM狀態不一致", "提交日期": "2021-10-27 14:42:30", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "55b9a5cff7b44b93041575de93cf2441042f2e14", "commit_訊息": "[Web]Q00-20211020003 當響應式表單的下拉式選單元件設定為動態生成選項時，列印表單無法顯示欄位值", "提交日期": "2021-10-20 14:50:46", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f58b57ef16eef7c17f638ad815254ae4fc5c22f", "commit_訊息": "[流程引擎]Q00-20210930003 修正因有特製流程定義的資料，導致原本的流程定義刪除後無法再匯入相同流程", "提交日期": "2021-09-30 16:04:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dccc5b68a113354162b42cb74776619d23b79e83", "commit_訊息": "[ESS]Q00-20210924006 在呼叫ESS存檔前增加判斷，防止同單據在ESS與BPM狀態不一致", "提交日期": "2021-09-24 18:25:51", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/dao/OJBAppFormActivityRecordDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "cd7bc07b5a0ce75fa389dbc9219e640d34b7a977", "commit_訊息": "[流程引擎]Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回[補修正]", "提交日期": "2021-08-16 13:48:13", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "015974aaee922b5f656b809102a4a578b6ddf216", "commit_訊息": "Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回", "提交日期": "2021-08-13 19:23:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "574d447665e767f98f22d6509650daf999206234", "commit_訊息": "[流程引擎]Q00-20210730002 修正關卡設定「只有一個人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核", "提交日期": "2021-07-30 17:52:04", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70c23e5dcfcde3bd123db158d7307d2c5f4aa68b", "commit_訊息": "[流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能[補]", "提交日期": "2021-07-20 09:00:46", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c605441c91d239dee696a5f30d0842e75e62560", "commit_訊息": "[BPM APP]C01-20210511002 修正當啟用動態渲染，且使用Oracle資料庫時要取得IMG的流程列表會顯示無資料問題", "提交日期": "2021-07-09 14:00:08", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f5066d1f508328c590c8f449b4566d9696424dd0", "commit_訊息": "[流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能", "提交日期": "2021-07-08 17:23:53", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e6b6f9f20f2e83c1dcac36a61759e2294e464bf", "commit_訊息": "[內部]C01-20210707003 編輯線出錯時導致空白畫面，增加Log", "提交日期": "2021-07-08 14:56:23", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessPackageManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/model/ProcessDefinitionModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/model/ProcessPackageModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "38332b293d0ae526ed24f38bd15fcf66d606522e", "commit_訊息": "[內部]Q00-20210630002 ESS回傳狀態及更新AppFormActivityRecord的事件優化log[補修正]", "提交日期": "2021-07-01 09:18:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7e5ff3c344dfcfc15da75d5f8322350c0e1aa1c", "commit_訊息": "[ESS]Q00-20210630003 調整ESS單據發起流程時如果在資料表已有紀錄，就不該往下繼續派送", "提交日期": "2021-06-30 19:25:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8dd7adefbb1e67b851e1310107c0d68aa69115a", "commit_訊息": "[內部]Q00-20210630002 ESS回傳狀態及更新AppFormActivityRecord的事件優化log", "提交日期": "2021-06-30 19:13:51", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/dao/OJBAppFormActivityRecordDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "555532df47c0789eb76b2de27712729294ec5a5d", "commit_訊息": "[流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題", "提交日期": "2021-06-07 11:43:59", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7dc4f15ebfc43f50677fd563bf818cb818999f9", "commit_訊息": "[流程設計師]Q00-20210531001 修正「複製有連接線的關卡造成實際流程派送發生異常」的問題", "提交日期": "2021-06-02 15:44:58", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "86c2c3e687024e01352a6061a4b5bfad2e9a66c8", "commit_訊息": "[流程引擎]A00-20210527001 修正同關卡加簽兩次以上會出現流程壞掉的異常", "提交日期": "2021-06-01 20:29:53", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7cf4bc79bc31e9c24c32409a542d1f7f9d008eab", "commit_訊息": "[簽核流程設計師]Q00-20210416001 修正在簽出ProcessPackage時會誤取到CustomProcessPackage", "提交日期": "2021-04-16 11:15:25", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c6a404c0997d687d52ab35061c4d38c408536619", "commit_訊息": "[Web]A00-20210317002 修正進入多個處理者僅需一人處理的待辦事項，若使用者未接收工作就返回待辦清單，畫面卡住問題", "提交日期": "2021-03-17 16:18:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea5296f811f603280a205946cbe9abe657fe906a", "commit_訊息": "[流程引擎]Q00-20210520005 組織同步時，一併同步職務/職稱/角色的簡稱", "提交日期": "2021-05-20 15:30:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "17b031d933b5b6e6ced78a838d7fc98cc7ba4a50", "commit_訊息": "[流程引擎]Q00-20210517001 調整DealOvertimeProcessHandler排程", "提交日期": "2021-05-17 15:06:33", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "92e259a1806ecdbac13b068e68973c81ae5c79b5", "commit_訊息": "[組織設計師]S00-20210222001 組織設計師優化 取消父類別開啟時載入整個組織 [補]", "提交日期": "2021-05-14 14:43:28", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainFunctionLevelDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "94412321a56cf8a66b55a7695b525ac8918f18fa", "commit_訊息": "[組織設計師]S00-20210222001 組織設計師優化 取消父類別開啟時載入整個組織", "提交日期": "2021-05-13 15:44:04", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/AbstractTableDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainFunctionLevelDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "17ec4c7a636418625dd5757f9dae88578fff870f", "commit_訊息": "Merge branch 'hotfix_5.8.5.2_cnce' of http://10.40.41.229/BPM_Group/BPM.git into hotfix_5.8.5.2_cnce", "提交日期": "2021-05-13 15:33:56", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "df98e5d0e8e780301cfb9088ec737a5822f8a225", "commit_訊息": "[內部]優化取得OrgDTO方式", "提交日期": "2021-05-13 15:33:06", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "acf46dbb0c613d17fdf6d71b697f753528fb33a7", "commit_訊息": "[內部]排程DealOvertimeProcessHandler加入排查用的log", "提交日期": "2021-05-13 15:09:13", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "61c251689177d4ff76dbcb10e82e7e83648b7062", "commit_訊息": "[內部]Q00-20210426002 調整活動逾時排程SQL，MSSQL加入WITH NOLOCK指令", "提交日期": "2021-04-26 18:21:18", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f60dc326b10b8e1b55a15dc1fbe402a63f3649f", "commit_訊息": "[流程引擎]Q00-20210303003 修正「活動逾時排程執行過程中發生資料庫鎖定」問題", "提交日期": "2021-04-26 15:05:46", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a6e541888959fc6c23d76a0eaf6f0b496e6fd90b", "commit_訊息": "[流程引擎]Q00-20210426001 修正「流程設定流程撤銷事件，於多人關卡尚未處理時撤銷流程，發生資料庫鎖定(DB Lock)」問題", "提交日期": "2021-04-26 11:49:07", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}