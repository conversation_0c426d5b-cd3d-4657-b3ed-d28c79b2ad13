"""
客戶連線管理路由
"""
from fastapi import APIRouter, Request, Form, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from pathlib import Path
import json
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
import sys
import pytz

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.config import BPM_CUSTOMER_DIR

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# 客戶資料檔案路徑
CUSTOMER_DATA_FILE = BPM_CUSTOMER_DIR / "customer_connections.json"

@router.get("/", response_class=HTMLResponse)
async def customer_connections_page(request: Request):
    """客戶連線管理主頁面"""
    # 載入客戶資料
    customers = load_customer_data()
    
    context = {
        "request": request,
        "page_title": "客戶連線管理系統",
        "customers": customers,
        "total_customers": len(customers)
    }
    
    return templates.TemplateResponse("customers/index.html", context)

@router.get("/add", response_class=HTMLResponse)
async def add_customer_page(request: Request):
    """新增客戶頁面"""
    context = {
        "request": request,
        "page_title": "新增客戶連線",
        "product_types": get_product_types()
    }
    
    return templates.TemplateResponse("customers/add.html", context)

@router.post("/add")
async def add_customer(
    company_name: str = Form(...),
    product_type: str = Form(...),
    server_ip: str = Form(...),
    database_name: str = Form(...),
    username: str = Form(...),
    password: str = Form(...),
    port: Optional[str] = Form(None),
    description: Optional[str] = Form(None)
):
    """新增客戶連線"""
    try:
        # 載入現有資料
        customers = load_customer_data()
        
        # 建立新的客戶記錄
        new_customer = {
            "oid": str(uuid.uuid4()),
            "company_name": company_name,
            "product_type": product_type,
            "server_ip": server_ip,
            "database_name": database_name,
            "username": username,
            "password": password,
            "port": port or "1433",  # 預設 SQL Server 埠
            "description": description or "",
            "created_at": datetime.now(pytz.timezone('Asia/Taipei')).isoformat(),
            "updated_at": datetime.now(pytz.timezone('Asia/Taipei')).isoformat()
        }
        
        # 新增到列表
        customers.append(new_customer)
        
        # 儲存資料
        save_customer_data(customers)
        
        return RedirectResponse(url="/customers", status_code=303)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"新增客戶時發生錯誤: {str(e)}")

@router.get("/edit/{oid}", response_class=HTMLResponse)
async def edit_customer_page(request: Request, oid: str):
    """編輯客戶頁面"""
    customers = load_customer_data()
    customer = next((c for c in customers if c["oid"] == oid), None)
    
    if not customer:
        raise HTTPException(status_code=404, detail="找不到指定的客戶")
    
    context = {
        "request": request,
        "page_title": "編輯客戶連線",
        "customer": customer,
        "product_types": get_product_types()
    }
    
    return templates.TemplateResponse("customers/edit.html", context)

@router.post("/edit/{oid}")
async def edit_customer(
    oid: str,
    company_name: str = Form(...),
    product_type: str = Form(...),
    server_ip: str = Form(...),
    database_name: str = Form(...),
    username: str = Form(...),
    password: str = Form(...),
    port: Optional[str] = Form(None),
    description: Optional[str] = Form(None)
):
    """更新客戶連線"""
    try:
        customers = load_customer_data()
        customer_index = next((i for i, c in enumerate(customers) if c["oid"] == oid), None)
        
        if customer_index is None:
            raise HTTPException(status_code=404, detail="找不到指定的客戶")
        
        # 更新客戶資料
        customers[customer_index].update({
            "company_name": company_name,
            "product_type": product_type,
            "server_ip": server_ip,
            "database_name": database_name,
            "username": username,
            "password": password,
            "port": port or "1433",
            "description": description or "",
            "updated_at": datetime.now(pytz.timezone('Asia/Taipei')).isoformat()
        })
        
        # 儲存資料
        save_customer_data(customers)
        
        return RedirectResponse(url="/customers", status_code=303)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新客戶時發生錯誤: {str(e)}")

@router.post("/delete/{oid}")
async def delete_customer(oid: str):
    """刪除客戶連線"""
    try:
        customers = load_customer_data()
        customers = [c for c in customers if c["oid"] != oid]
        
        save_customer_data(customers)
        
        return {"success": True, "message": "客戶已成功刪除"}
        
    except Exception as e:
        return {"success": False, "error": f"刪除客戶時發生錯誤: {str(e)}"}

@router.get("/detail/{oid}", response_class=HTMLResponse)
async def customer_detail_page(request: Request, oid: str):
    """客戶詳細資訊頁面"""
    customers = load_customer_data()
    customer = next((c for c in customers if c["oid"] == oid), None)
    
    if not customer:
        raise HTTPException(status_code=404, detail="找不到指定的客戶")
    
    context = {
        "request": request,
        "page_title": f"客戶詳情 - {customer['company_name']}",
        "customer": customer
    }
    
    return templates.TemplateResponse("customers/detail.html", context)

def load_customer_data() -> List[Dict[str, Any]]:
    """載入客戶資料"""
    if not CUSTOMER_DATA_FILE.exists():
        return []
    
    try:
        with open(CUSTOMER_DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return []

def save_customer_data(customers: List[Dict[str, Any]]):
    """儲存客戶資料"""
    # 確保目錄存在
    CUSTOMER_DATA_FILE.parent.mkdir(parents=True, exist_ok=True)
    
    with open(CUSTOMER_DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(customers, f, ensure_ascii=False, indent=2)

def get_product_types() -> List[str]:
    """取得產品類型列表"""
    return [
        "BPM",
        "HRM",
        "Tiptop",
        "其他"
    ]
