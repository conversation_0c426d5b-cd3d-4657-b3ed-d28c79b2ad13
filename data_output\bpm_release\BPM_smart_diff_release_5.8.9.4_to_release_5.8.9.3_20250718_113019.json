{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.9.4", "date": "2024-11-22 11:40:05", "message": "Revert \"[ORGDT]C01-20240517013 調整Web化設計工具在打開後隔一段時間會發生操作錯誤問題\"", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.9.3", "date": "2023-08-23 13:15:30", "message": "[EBG]S00-20230808002 新增EBG電子簽章專案 [補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 11:30:19", "新增commit數量": 331, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "a454a5b93fe5d687adcafb6a5fea1b60fbb60dc8", "commit_訊息": "Revert \"[ORGDT]C01-20240517013 調整Web化設計工具在打開後隔一段時間會發生操作錯誤問題\"", "提交日期": "2024-11-22 11:40:05", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "f78e2d2dc789d22a7a1574346a8defa465c0af93", "commit_訊息": "[ORGDT]C01-20240517013 調整Web化設計工具在打開後隔一段時間會發生操作錯誤問題", "提交日期": "2024-06-05 17:25:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "676d1c0ac9a944e86df817cbdc18fd078cc0eeaa", "commit_訊息": "[Web] V00-20231204001 修正工作轉派使用者，原處理者無法查閱已轉派的工作清單問題", "提交日期": "2023-12-04 18:08:08", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d6263d434d32baf36d456d1131b3dca1bae0f4f8", "commit_訊息": "[附件擴充] 修正參數未開啟時判斷邏輯異常問題", "提交日期": "2023-12-05 17:46:00", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad059304595f77059b8e6dccda4bf346a1033feb", "commit_訊息": "[附件擴充] 修正參數未開啟時判斷邏輯異常問題", "提交日期": "2023-12-05 15:01:32", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "4af87b2d22ca54eb5807c689255963381c227fb0", "commit_訊息": "[附件擴充] 在線閱讀新增發布檔下載接口", "提交日期": "2023-11-28 17:29:23", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "76ee22c03755b8fb7e1711388335567834b7fa5f", "commit_訊息": "[流程封存]修正處理者包含 AutoAgent 時導致封存異常", "提交日期": "2023-11-23 12:02:07", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "710f683e334375f1dcaae5a3281da14f1bf68a49", "commit_訊息": "[T100]Q00-20231123001 修正T100整合設定會多新增一筆資料造成發單判斷異常", "提交日期": "2023-11-23 10:01:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SysintegrationSetAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3da65b8a11b49bd0dbd70b0851502d11e93f3530", "commit_訊息": "[Web] V00-20231123001 修正查看監控流程開啟IMG表單異常問題", "提交日期": "2023-11-23 08:37:12", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dfde4b68f20cb68e03bc7ffc381f815ccb5fb5d1", "commit_訊息": "[內部]修正 NanaDsUtil Oracle NaNaDS 跟 ProcessArchiveDS 的比對邏輯", "提交日期": "2023-11-23 08:31:24", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f994776de2a8b7090ed3f4c9e1ec19307c6cacfd", "commit_訊息": "[DT]Q00-20231122002 修正系統權限管理員中可存取的範圍設定權限範圍(部門/專案主管)後儲存會報後端接口調用失敗的問題", "提交日期": "2023-11-22 20:01:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "86cd21d2a63d06de74b03adee868c11ededf0549", "commit_訊息": "[ESS]Q00-20231114004 調整標準、流通、法尊表單行動表單[補]", "提交日期": "2023-11-22 19:44:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF01\\346\\216\\222\\347\\217\\255\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF03\\350\\243\\234\\345\\210\\267\\345\\215\\241\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF04\\345\\212\\240\\347\\217\\255\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF05\\345\\212\\240\\347\\217\\255\\350\\250\\210\\345\\212\\203\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF06\\345\\212\\240\\347\\217\\255\\350\\252\\277\\344\\274\\221\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF07\\350\\253\\213\\345\\201\\207\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF17\\351\\212\\267\\345\\201\\207\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF20\\345\\207\\272\\345\\267\\256\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF21\\345\\207\\272\\345\\267\\256\\347\\231\\273\\350\\250\\230.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF22\\350\\252\\277\\350\\201\\267\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF23\\350\\252\\277\\350\\201\\267\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF24\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF25\\350\\275\\211\\346\\255\\243\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF26\\347\\215\\216\\346\\207\\262\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF27\\351\\233\\242\\350\\201\\267\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF28\\344\\272\\272\\345\\212\\233\\351\\234\\200\\346\\261\\202\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF29\\350\\275\\211\\346\\255\\243\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF08\\347\\251\\215\\344\\274\\221\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 18}, {"commit_hash": "13d3c71cfb13b923d0fb5c082a30e1fa8a6fd336", "commit_訊息": "[附件擴充] 附件上傳下載新增參數", "提交日期": "2023-11-22 16:48:14", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "bbed6b8a1ec78d2a4dd51cf85e61c054c57e114a", "commit_訊息": "[內部]移除 NanaDsUtil StringUtils 邏輯，避免 NaNaXWeb 使用時出現異常", "提交日期": "2023-11-22 15:48:11", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99bf60353ef6ea46158012ef6130c30626c0d9f9", "commit_訊息": "[內部]更新5894patch", "提交日期": "2023-11-22 15:23:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "841ca9348be0510a0d6326f55e9cc4f1898a8332", "commit_訊息": "[內部]更新5894patch", "提交日期": "2023-11-22 11:17:02", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "365a56e271ada0c360f03defd92427c4916030ad", "commit_訊息": "[<PERSON><PERSON><PERSON>]Q00-20231117004 Oauth專案修正綁定頁從開窗改成導頁處理[補修正]", "提交日期": "2023-11-22 08:54:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e92cb49213dfa0ec76b918a78af035d5d2b2a1a3", "commit_訊息": "[流程封存]新增 FTP 支持及檔案讀寫測試", "提交日期": "2023-11-21 18:44:18", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/lib/JakartaCommons/commons-net-3.10.0.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/FtpUtil.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "80a0786aa1e1c51cd8737c2def9ab62053c8d500", "commit_訊息": "[BPM APP]Q00-20231121009 修正行動端的顯示流程在發起關卡與下一關卡建立時間相同時排序異常問題", "提交日期": "2023-11-21 18:39:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0685da2fae2b2eb4122828e7152de6bf0d820904", "commit_訊息": "[流程封存]添加信件通知排程[补修正] 1.调整流程封存通知多语系", "提交日期": "2023-11-21 17:07:33", "作者": "刘旭", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b53b8ca82e841b6f48e092f69ef0f04703d6e81b", "commit_訊息": "[資安] Q00-20231121005 關閉/NaNaWeb/dwrDefault/index.html的瀏覽", "提交日期": "2023-11-21 16:21:22", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a7c8cc20fc31ffc8589427b5534338cad4f4ff47", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-11-21 16:16:31", "作者": "邱郁晏", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "1a46c0946e1b9972f9e5d3b37d84e2a458c3935d", "commit_訊息": "[附件擴充] 附件上傳下載新增參數", "提交日期": "2023-11-21 16:16:11", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DocFileUploader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "08404c8398bec3ce17a4e1ef6159383aa2f0942b", "commit_訊息": "[DT]Q00-20231121006 修正在Web化流程管理工具中活動參與者組織相關找不到離職日設定當天的使用者的問題", "提交日期": "2023-11-21 16:14:16", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "533767770eb6397b9c8ed3e4ce62579a9f885764", "commit_訊息": "[Web]Q00-20231121004 修正外部Portlet追蹤連結searchSingleFormDetail使用在ESS單據上無法顯示ESS畫面", "提交日期": "2023-11-21 15:22:10", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0e9d227426b96cddb75d901540709a947ecd08ba", "commit_訊息": "[在線閱覽]Q00-20231121003 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(2.14.305)", "提交日期": "2023-11-21 14:58:11", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/CNS2-V.bcmap", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/ETenms-B5-H.bcmap", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/GB-H.bcmap", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-next.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-previous.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/loading-dark.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/loading.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-documentProperties.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-firstPage.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-handTool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-lastPage.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCcw.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCw.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollHorizontal.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollPage.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollVertical.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollWrapped.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-selectTool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadEven.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadNone.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadOdd.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/shadow.png", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-bookmark.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-currentOutlineItem.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-download.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-menuArrow.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-openFile.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageDown.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageUp.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-presentationMode.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-print.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-search.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-sidebarToggle.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewAttachments.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewLayers.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewOutline.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewThumbnail.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomIn.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomOut.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/treeitem-collapsed.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/treeitem-expanded.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/BPMlocale.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ach/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/af/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/an/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ar/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ast/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/az/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/be/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bg/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bo/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/br/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/brx/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bs/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ca/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cak/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ckb/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cs/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cy/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/da/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/de/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/dsb/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/el/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-CA/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-GB/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-US/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eo/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-AR/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-CL/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-ES/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-MX/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/et/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eu/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fa/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ff/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fi/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fy-NL/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ga-IE/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gd/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gn/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gu-IN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/he/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hi-IN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hsb/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hu/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hy-AM/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hye/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ia/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/id/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/is/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/it/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ja/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ka/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kab/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kk/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/km/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kn/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ko/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lij/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lo/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/locale.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lt/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ltg/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lv/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/meh/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mk/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ms/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/my/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nb-NO/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ne-NP/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nn-NO/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/oc/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pa-IN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-BR/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-PT/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rm/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ro/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ru/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sat/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sc/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/scn/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sco/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/si/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sk/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/son/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sq/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sv-SE/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/szl/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ta/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/te/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tg/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/th/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tl/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tr/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/trs/viewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uk/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ur/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uz/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/vi/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/wo/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/xh/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/BPMviewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/BPMviewer.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/viewer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitDingbats.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitFixed.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitFixedBold.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitFixedBoldItalic.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitFixedItalic.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSans.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansBold.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansBoldItalic.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansItalic.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSerif.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSerifBold.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSerifBoldItalic.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSerifItalic.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSymbol.pfb", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/LICENSE_FOXIT", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/LICENSE_LIBERATION", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/LiberationSans-Bold.ttf", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/LiberationSans-BoldItalic.ttf", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/LiberationSans-Italic.ttf", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/LiberationSans-Regular.ttf", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js.map", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 183}, {"commit_hash": "989160fdfd0fb8fbccf0894198188e92b9aa960a", "commit_訊息": "[Web]Q00-20231121002 修正流程關係人設定表單部門欄位選擇dialog元件時會無法發起的問題", "提交日期": "2023-11-21 14:19:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bc04865cfce1c0bc08fbba46210e5cc113f34e40", "commit_訊息": "[SAP]Q00-20231121001 修正SAP整合，當mapping內容有Grid時，可能會有GridColumnId與GridValue順序錯誤的異常", "提交日期": "2023-11-21 11:55:25", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bcd9836197e48397a989c6d0bcc946d6b108fe8c", "commit_訊息": "[流程引擎]Q00-20231120003 修正監控流程的詳細流程圖頁面，當按下「跳過此關卡」功能時，下一關處理者無法收到代辦通知信件", "提交日期": "2023-11-20 17:25:48", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1512e0bbec1c2cecb9f57cbb7d24299c2960b3f2", "commit_訊息": "[流程封存]新增索引[補]", "提交日期": "2023-11-20 16:37:02", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93406e157a41e5d977ec95a3f35421a57a53beff", "commit_訊息": "[流程封存]新增索引", "提交日期": "2023-11-20 16:28:49", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0af258c631509e4ab82cfb3a41790e1b9218786a", "commit_訊息": "[流程封存]優化錯誤處理機制：統一使用 Root 的錯誤訊息", "提交日期": "2023-11-20 15:43:24", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3a1701e6bd5d71cba39d8250dda3b349c16fb54d", "commit_訊息": "[流程封存]添加信件通知排程[补修正] 1.添加流程封存通知多语系", "提交日期": "2023-11-20 15:26:47", "作者": "刘旭", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7d2b62e5aac3b4f11a5c036fc860c3b09b04b4c", "commit_訊息": "[內部]修正打包設定，移除殘留設定及合併差異", "提交日期": "2023-11-20 15:15:04", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/metadata/application.xml", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/persistence.xml", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "7ff288843fa42be1fd0c9aec46841dbc206d61c9", "commit_訊息": "[Web]S00-20231027001 布景主题页新增可自订首页浏览器标题及首页底部备注(补修正)", "提交日期": "2023-11-20 14:36:59", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d17455ed15f189b5a01bd9972fff9e8f986b1ec6", "commit_訊息": "[BPM APP]Q00-20231120002 修正行動端通知列表在沒有任何流程分類情況下會拋undefined錯誤問題", "提交日期": "2023-11-20 14:29:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "69db1584727b3c38e197bf3206860e4a744ab1f8", "commit_訊息": "[流程封存]優化錯誤處理機制並增加驗證：NaNaDS 跟 ProcessArchiveDS 連到同一個 DB 時不允許封存、還原", "提交日期": "2023-11-20 14:19:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IProcessArchiveManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ProcessArchiveManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "59392d27fe3e600c869a15e5ffe03246eff66e5a", "commit_訊息": "[流程封存]調整刪除流程的判斷條件，NaNaDS 跟 ProcessArchiveDS 都存在時才會刪除", "提交日期": "2023-11-17 16:29:05", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eead7b59f139554924f8e037be2352f30dd5993c", "commit_訊息": "[流程封存]DTO 的流程處理者內容加入流程撤銷者", "提交日期": "2023-11-17 16:25:14", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f82c9bc128d554f088f5905fedfe90d1edfdedc8", "commit_訊息": "[Web] Q00-20231120001 修正BPM注册序号中 BPM流程引擎有过期序号导致其他功能无法正常使用", "提交日期": "2023-11-20 11:01:06", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d44c1fd7bea76005923c5910188ded3d68c16f8b", "commit_訊息": "[<PERSON>aut<PERSON>]Q00-20231117004 Oauth專案修正綁定頁從開窗改成導頁處理", "提交日期": "2023-11-17 18:00:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/oauthModule/OauthAuthentication.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthAuthenticationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthSettingManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/SSOCallBack.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "173af6ae75ca6b04b8c3049ba2ae226d937231bf", "commit_訊息": "[Web]S00-20231027001 布景主题页新增可自订首页浏览器标题及首页底部备注", "提交日期": "2023-11-17 17:23:38", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "cc2e78d93dea0ebcc71af5b00e777c01005784e5", "commit_訊息": "[流程引擎]Q00-20231117003 調整流程簡易流程圖預解析，當關卡的處理者設定為直屬主管或部門主管，且指定參考的關卡有多個處理者時，此情境為流程錯誤設計，預解析應顯示「無法解析」", "提交日期": "2023-11-17 15:29:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "16f344e25232a28b3c6d1f9e20ecd29c94ed0756", "commit_訊息": "[Web] V00-20231103003 修改密码前后端加密传输[补]", "提交日期": "2023-11-17 13:56:55", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "58228849192ece254a4d5c13799aa2264b516a87", "commit_訊息": "[Web] Q00-20231117002 修正 首次登录跳转到变更密码页面显示异常", "提交日期": "2023-11-17 13:48:06", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65b763794fda9e931f66603fb4872752fc0e7c1b", "commit_訊息": "[Web] Q00-20231117001 调整簡易流程圖跳過時_輸入密碼後alert的訊息框太小的问题", "提交日期": "2023-11-17 13:42:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6894b8c3b81ae386d97abfbf7f7440aba22885ad", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-11-17 11:39:51", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "1286d62c62ae5f6840ba6f0098047edb860f8dd8", "commit_訊息": "[Web] S00-20230213002 流程分类Menu新增根据“此分类全部” 筛选查询功能[补修正]", "提交日期": "2023-11-17 11:39:28", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9803977a7a0a8352be0be79486abb7c5f46f9fc9", "commit_訊息": "[DT]V00-20231115002 修正流程管理工具活動中的通知設定儲存後不顯示問題", "提交日期": "2023-11-17 11:35:16", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1cea7ae4891cea428ab294424a288b098b163b00", "commit_訊息": "[Web] S00-20230213002 流程分类Menu新增根据“此分类全部” 筛选查询功能[补修正]", "提交日期": "2023-11-17 11:19:36", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b14f34b7e5f8169077a5d5f347cbdfbe47028a7e", "commit_訊息": "[Web] V00-20231113001 修正 使用者输错密码一次Users.passwordWrongTimes栏位一次就加2", "提交日期": "2023-11-16 15:56:15", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82e98b41f66c32a931bea1dbcf80699dc9d8c2ca", "commit_訊息": "[內部]更新5894patch", "提交日期": "2023-11-16 15:44:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f89c01afd12c965f71ba7cf6a23df8e70c64b8bd", "commit_訊息": "[內部]更新5894patch", "提交日期": "2023-11-16 14:22:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d91f17e26a5b56d434ed058680973bdcfc46f37b", "commit_訊息": "[內部]自動簽核邏輯優化", "提交日期": "2023-11-16 14:09:03", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "1fc8480e99223d9e7398437449bb6fdadd8115b1", "commit_訊息": "[BPM APP]Q00-20231116001 優化移動端在簽核時偶發串單問題", "提交日期": "2023-11-16 09:22:27", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a774b75d104f154314b41e896f0490b441d55093", "commit_訊息": "[流程封存]增加整合及電子簽章相關資料封存、還原邏輯", "提交日期": "2023-11-16 00:23:42", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/EFGPIntePLMInfoForArchive.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDto.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fc733ffb15843dea032c6aacde6c81b4c9c445bd", "commit_訊息": "[流程封存]調整模組維護作業顯示順序", "提交日期": "2023-11-15 20:38:41", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "903bfec56604491c8db7fdffc7ebf4e942f7dbb4", "commit_訊息": "[流程封存]調整檔案讀寫接口至 MainDsManager", "提交日期": "2023-11-15 20:05:36", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonManager.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonManagerBean.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 4}, {"commit_hash": "3f5d9a7e5d01b46c1a77b89a7c493540dbb23058", "commit_訊息": "[內部]修正 nana-process-archive.ear 未排除 OauthSettingManagerBean 導致部署失敗", "提交日期": "2023-11-15 19:55:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71845118aa918cd5a084b809ca33494844b57098", "commit_訊息": "[流程封存]修正封存異常，如果 DocType 不存在就新增", "提交日期": "2023-11-15 10:53:08", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "86128a8489dae95262ddb97d8afeb5156078648a", "commit_訊息": "[流程封存]修正流程加簽後封存、還原異常", "提交日期": "2023-11-15 09:55:46", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5b5710c11de5583283b666d3004e1982994713e", "commit_訊息": "[流程封存]修正封存異常，如果核決層級不存在就新增，同時加入組織新增的邏輯", "提交日期": "2023-11-14 14:33:55", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDto.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "572f3ba1378446fd687ab6af5bb5967beb946406", "commit_訊息": "[流程封存]修正封存異常，移除 User 不需要的關聯", "提交日期": "2023-11-14 10:50:57", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c45934b8e6c05a5f105393b52fce0c63afe0f110", "commit_訊息": "[流程封存]修正封存異常，如果 PrsInsLvl 不存在就新增", "提交日期": "2023-11-14 09:26:30", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f762d99493e1e2bf7bda250c0dc95a62ed1efb2", "commit_訊息": "[流程封存]邏輯優化及結構調整", "提交日期": "2023-11-09 15:10:18", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ArchiveRestoreCommonImplement.java", "修改狀態": "重新命名", "狀態代碼": "R078"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerLocal.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDto.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "e825cc7d14e1a3ffab71a5b3de09182f9a8d7c9b", "commit_訊息": "[<PERSON><PERSON><PERSON>]20231115初版", "提交日期": "2023-11-15 19:22:44", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/oauthModule/OauthAuthenticationDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/oauthModule/OauthSettingDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/oauthModule/OauthAuthentication.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/oauthModule/OauthSetting.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthAuthenticationManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthAuthenticationManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthSettingManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthSettingManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Oauth.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/OauthMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/JSPFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/SSOCallBack.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 33}, {"commit_hash": "fd8a35616353d4306714d37e622d1a815beeeeec", "commit_訊息": "[MPT]V00-20231115003 調整S00-20230906001的功能在登入後若為過期公告則提示公告已過期", "提交日期": "2023-11-15 15:58:31", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "54bfa7831306c58e5a20df335b9458cb540ba70b", "commit_訊息": "[BPM APP]Q00-20231115002 修正整合企業微信時使用行動絕對位置表單的附件元件會無法使用微信提供的閱讀器下載附件問題", "提交日期": "2023-11-15 14:19:22", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88beed0ef6a9f79b7fbc4b23631344ea85626885", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-11-15 14:06:02", "作者": "lorenchang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "077361caf3ff7fd59dadbd9a270e6b93e0972474", "commit_訊息": "[內部]流程主機及程式定義的 BPMDT 改為各模組共用 NaNaXWeb", "提交日期": "2023-11-15 14:05:22", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4c41a4b13b9548651277c991deca80cf0e3da4f1", "commit_訊息": "[Web] Q00-20231115001 调整流程代理人页面选择流程开窗，流程名称以多语系显示", "提交日期": "2023-11-15 13:51:34", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74dc2274c585009da3481d3dac4564fd75bb6dd1", "commit_訊息": "[Web]V00-20231115001 列印時附件資訊前后边框颜色不对问题修复", "提交日期": "2023-11-15 11:08:48", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2407fc15b8ba11baae0e040926b72cbf1e526700", "commit_訊息": "[ESS]Q00-20231114004 調整標準、流通、法尊表單行動表單", "提交日期": "2023-11-14 17:08:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF31\\346\\213\\233\\350\\201\\230\\350\\250\\210\\347\\225\\253.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF32\\346\\207\\211\\350\\201\\230\\344\\272\\272\\345\\223\\241\\351\\235\\242\\350\\251\\246.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF33\\346\\207\\211\\350\\201\\230\\344\\272\\272\\345\\223\\241\\347\\255\\206\\350\\251\\246.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF34\\351\\214\\204\\347\\224\\250\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF43\\350\\277\\260\\350\\201\\267\\345\\240\\261\\345\\221\\212.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF44\\350\\200\\203\\346\\240\\270\\350\\251\\225\\345\\210\\206.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF46\\350\\200\\203\\346\\240\\270\\347\\224\\263\\350\\250\\264.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF47\\350\\200\\203\\346\\240\\270\\346\\224\\271\\351\\200\\262.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF50\\347\\217\\255\\346\\254\\241\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF51\\345\\212\\240\\347\\217\\255\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213(\\345\\244\\232\\346\\231\\202\\346\\256\\265\\345\\244\\232\\344\\272\\272).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF52C1\\347\\217\\255\\346\\254\\241\\344\\272\\222\\346\\217\\233.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF60\\350\\254\\233\\345\\270\\253\\350\\263\\207\\346\\240\\274\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF61\\350\\252\\262\\347\\250\\213\\351\\226\\213\\347\\231\\274\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF62\\345\\237\\271\\350\\250\\223\\351\\240\\220\\347\\256\\227\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF63\\345\\237\\271\\350\\250\\223\\351\\234\\200\\346\\261\\202\\346\\216\\241\\351\\233\\206.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF64\\345\\237\\271\\350\\250\\223\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF66\\345\\237\\271\\350\\250\\223\\350\\251\\225\\344\\274\\260.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF67\\345\\237\\271\\350\\250\\223\\345\\240\\261\\345\\220\\215.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF68\\345\\217\\226\\346\\266\\210\\345\\237\\271\\350\\250\\223\\345\\240\\261\\345\\220\\215.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF69\\345\\223\\241\\345\\267\\245\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF72\\345\\223\\241\\345\\267\\245\\345\\240\\261\\345\\210\\260\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF74\\350\\263\\207\\346\\272\\220\\347\\224\\263\\351\\240\\230.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF75\\350\\263\\207\\346\\272\\220\\346\\255\\270\\351\\202\\204.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF76\\345\\217\\254\\345\\213\\237\\346\\224\\271\\351\\200\\262\\345\\273\\272\\350\\255\\260.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF52C1\\347\\217\\255\\346\\254\\241\\344\\272\\222\\346\\217\\233.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/\\346\\263\\225\\351\\201\\265/ESSF93\\344\\270\\215\\345\\212\\240\\347\\217\\255\\345\\216\\237\\345\\233\\240\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF52C1\\347\\217\\255\\346\\254\\241\\344\\272\\222\\346\\217\\233.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF52C2\\347\\217\\255\\346\\254\\241\\350\\256\\212\\346\\233\\264.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF52\\346\\212\\225\\347\\217\\255\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF53\\346\\216\\222\\347\\217\\255\\347\\242\\272\\350\\252\\215.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 30}, {"commit_hash": "06555f5e2486bb644accf2f02e0a7292d164304e", "commit_訊息": "[DT]V00-20231110004 修正Q00-20230926003修正錯誤的問題", "提交日期": "2023-11-14 16:32:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a318b815a73f15f609c63f32f502fd5af33116ad", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-11-14 15:54:44", "作者": "邱郁晏", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "eaafd63bcdb0e547e5886a587fefc4d66ed45763", "commit_訊息": "[Web] Q00-20231114003 修正寄件人重複顯示問題", "提交日期": "2023-11-14 15:54:22", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f06fa36c33610897ed1cdb8ac35da0691735490", "commit_訊息": "[MPT]V00-20230928001 修正模擬使用者時啟用訊息首頁模組就關閉原BPM首頁功能會異常的問題", "提交日期": "2023-11-14 15:23:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a1ef32947fce27762bf23b0e2bf4f6a43305d0b", "commit_訊息": "[附件擴充] 附件上傳下載擴充機制", "提交日期": "2023-11-14 14:48:53", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "89d6e5127acdbfbbc5f4ba0277d1f336b7d65d68", "commit_訊息": "[Web] Q00-20231114002 修正 ESSF63培訓需求採集.form 表单的代号", "提交日期": "2023-11-14 13:23:46", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF63\\345\\237\\271\\350\\250\\223\\351\\234\\200\\346\\261\\202\\346\\216\\241\\351\\233\\206.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9cbbee60a460daff0cab2cce4e39b1172a916445", "commit_訊息": "[MPT]S00-20230313006 首頁模組中自定義鏈接新增jsp鏈接類型並直接顯示於首頁模塊上", "提交日期": "2023-11-14 10:42:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "65a5f63f8a154457f2b986a27f37e8f0109ce1cf", "commit_訊息": "[Web]Q00-20231110002 調整待辦表單頁面的退回重辦開窗，開窗時增加檢查當前關卡是否能夠退回重辦，若無法退回重辦則提示訊息", "提交日期": "2023-11-13 16:08:10", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReexecuteActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1e337c03192d8dfa4a7283535c4b55947113ed36", "commit_訊息": "[TFG] TFG專案上傳下載附件加解密", "提交日期": "2023-11-06 15:11:52", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/tfgModule/TFGDelegate.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tfgModule/TFGManager.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tfgModule/TFGManagerBean.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/EbgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DocFileUploader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFileManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 19}, {"commit_hash": "0344f81c7f14af44cfbd4a022ccda8e49ae953bb", "commit_訊息": "[雙因素模組]Q00-*********** 修正未放入不驗證清單的使用者若未綁定，登入後不會跳出需綁定的提示視窗", "提交日期": "2023-11-10 17:14:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TFAConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e55a3cbc48ab3a1986996b16e42fcddff79f6fc2", "commit_訊息": "[資安]V00-*********** 修正登入任一使用者可透過URL下載附件", "提交日期": "2023-11-10 17:07:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0eb0a58ae3783f6b2bb4dcfd28e0b2ce2510c1dc", "commit_訊息": "[流程封存]修正封存模組未載入系統設定導致已封存流程作業異常", "提交日期": "2023-11-10 11:50:44", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b2c0f8a60b77e66a3c489509410adb576818f43", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-11-10 11:36:47", "作者": "刘旭", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "509921db0ff7d44a84cfb050ede119e5d8105f58", "commit_訊息": "[Web]V00-20231110002 BPM文件\\開發教學索引\\Ajax ExtOrg 范例调整,Ajax ExtOrgAccessor页面描述“職務核決層級的名稱：：”、“核決層級值：：”多出一个冒号，共有三处问题修复", "提交日期": "2023-11-10 11:36:18", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ff99a77011ffce956b8d960f8e2e927bb9c01776", "commit_訊息": "[WEB]V00-20231110001 修正当流程分类只有一个时，追踪和待办会提示null报错", "提交日期": "2023-11-10 11:29:31", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a4b1971f39db9fd875ae1aa31e66bdbc4323deb6", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-11-09 19:25:22", "作者": "lorenchang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "794c9934becd2d662d3ff760e8663a15d465b901", "commit_訊息": "[流程封存]修正封存 DB 完全沒資料時會造成啟動異常", "提交日期": "2023-11-09 19:25:06", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8be5aabdd7f916a73ed3a89049a1bf22641864d6", "commit_訊息": "[DT]V00-20231109001 修正Web化組織管理工具中預設行事曆功能異常與缺少多語系問題", "提交日期": "2023-11-09 18:45:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/WorkCalendarListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2c3d9d82dadfb0b60a5ecf583e6e11e6739abb22", "commit_訊息": "[內部]更新5894patch", "提交日期": "2023-11-09 14:15:32", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f39624c4eefa8397a31c75bcf16e898625fee501", "commit_訊息": "[DT]S00-20230801002 新增Web化組織管理工具中預設行事曆功能[補]", "提交日期": "2023-11-09 13:25:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "139eaa98c11d4474d7dc8213556ef0ef620b1cf8", "commit_訊息": "[DT]S00-20230801002 新增Web化組織管理工具中預設行事曆功能", "提交日期": "2023-11-09 13:19:34", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/WorkCalendarForListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/WorkCalendarListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "f44f2677a721daac6842050a40ee3fa7bb9d4e50", "commit_訊息": "[Web] V00-20231103002 調整detailWorkItem的URL連結使用者無權限訪問時，應導向錯誤頁面(補)", "提交日期": "2023-11-09 12:06:37", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "42e5fa381c8613410d1b09e1b106cd0cdf194cd6", "commit_訊息": "[資安]V00-20231103004 SonarQube安全性議題修正：Vulnerable JS Library,js版本過舊", "提交日期": "2023-11-09 10:28:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-3.3.4.min.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-3.3.5.min.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "066e1f2e0fbff52a4a1ce8446866789ca1134d91", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-11-09 10:24:47", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "b89753037841ac0550ebfa0bc27f2eae6dcdeb6b", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-11-09 09:10:53", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "179909b7834e5675d83b288941c5dc39d7416eb1", "commit_訊息": "[流程封存]20231108初版", "提交日期": "2023-11-08 23:23:08", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/field_handler/listener/RemoteServerSlcSyncListener.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/IActivityType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/IDataType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/RelevantDataDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkItem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ArchivePropertiesForNana.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ArchiveRestoreCommonImplement.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDto.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IProcessArchiveManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/RmiRegistry.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ProcessArchiveManagerImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/RsrcBundleCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL_ProcessArchive.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL_ProcessArchive.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 43}, {"commit_hash": "1fe98ec6a4608a7711918712ec8a81c47aa8ddaa", "commit_訊息": "[Web] V00-20231103002 調整detailWorkItem的URL連結使用者無權限訪問時，應導向錯誤頁面。", "提交日期": "2023-11-08 22:59:33", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08f48d25de62bc32ca17ea85e45724196cc3a18b", "commit_訊息": "[BPM APP]Q00-20231108004 修正在選擇發起部門時主部門沒有發起權限時會導致無法發送表單問題[補]", "提交日期": "2023-11-08 16:45:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4062a3d86d3d43fa951ab77a785b42f66fcdf3ee", "commit_訊息": "[BPM APP]Q00-20231108004 修正在選擇發起部門時主部門沒有發起權限時會導致無法發送表單問題", "提交日期": "2023-11-08 16:43:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9bca06a541ddd816d76d2db5b9ea6e0e51999454", "commit_訊息": "[WEB] Q00-20231108002 修正待辦事項中表單serialNumber元件欄位資料顯示位置異常的問題", "提交日期": "2023-11-08 13:21:40", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "62251fbe69fb028c270f99ab05bdfe05767bb29e", "commit_訊息": "[Web] Q00-20231108001 調整客製開窗新增註解以及改回原本與服務確認之邏輯", "提交日期": "2023-11-08 11:27:29", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8dbb9810e4e833e9489d518f275bdb3f0f137797", "commit_訊息": "[web]Q00-20230919002 「系統設定」內有不少的Key項目沒有對應的中文描述，若這些設定不是要開放給管理者設定使用的，則請隱藏該Key項目 问题修复[補修正]", "提交日期": "2023-11-08 09:28:28", "作者": "刘旭", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18700afe11cab6dc91943784f5a74a202a57fca7", "commit_訊息": "[DT]V00-20231106001 修正Web化流程管理工具中更新流程分類名稱未更動儲存報錯問題", "提交日期": "2023-11-07 18:10:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6840d9c431df6a911b49a33c7f4e37e38f262a13", "commit_訊息": "[Web] Q00-20231107005 修正客製開窗包子查詢，查詢欄位名稱有別名導致比對計數異常問題", "提交日期": "2023-11-07 17:20:53", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e0ca073a6d1b485bf8b93d77e19c1b5545a77fee", "commit_訊息": "[DT]V00-20231106002 修正Web化流程管理工具中表單存取控管設定的唯讀多語系錯誤問題", "提交日期": "2023-11-07 16:05:14", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "737bc589eed660532f218fd0dffbc00aa404c476", "commit_訊息": "[Web] Q00-20231107004 修正客製開窗包子查詢，多條件篩選時，別名沒有被過濾導致查詢條件異常", "提交日期": "2023-11-07 14:44:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c43b98717849a7ea565bccb77935aa84ae61bcdc", "commit_訊息": "[Web] V00-20231103003 修改密码前后端加密传输", "提交日期": "2023-11-07 14:39:31", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageUserProfile-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "fb6fa0303723f38049562308ad55c91da2846679", "commit_訊息": "[DT]Q00-20231107002 修正Web化流程管理工具中活動定義編輯器內表單及附件的模式調整儲存後不會變更的問題", "提交日期": "2023-11-07 13:55:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a804722bcfde8a36efdef6fe9c4a11e38cdd2ecd", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)", "提交日期": "2023-11-06 17:47:59", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "239ab09d8c8f8282070860a44e932ede648142c4", "commit_訊息": "[BPM APP]Q00-20231106002 調整行動表單客製開窗在重組SQL指令改呼叫Web端的邏輯", "提交日期": "2023-11-06 13:55:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileDatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "327cf2e57c0d77cb1a286b54e2eb827a729834b5", "commit_訊息": "[WEB]Q00-20231106001 调整Select元件在设置背景色时列印却显示唯读背景色的问题", "提交日期": "2023-11-06 11:11:50", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ef8585b89900289090c19b69af74036cf3a77cdb", "commit_訊息": "[ESS]Q00-*********** ESSF07請假單表單更新", "提交日期": "2023-11-03 11:46:13", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF07\\350\\253\\213\\345\\201\\207\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dbd95392e61892a150741e4c856ebe38a15016cc", "commit_訊息": "[Web]V00-20231031001 表单设计器中，自定义开窗使用【资料选取设置】选择【资料选取注册器】时在不重新打开表单设计器时选不到新增加的资料，增加刷新按钮，以此来选到新增加的资料选取注册器。[补修正]", "提交日期": "2023-11-03 11:32:05", "作者": "刘旭", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a295b5a626ae8f1bc17ad115ee4920ffd6e95bbc", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)", "提交日期": "2023-11-03 11:12:20", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "924869ff40e745aca99f2d761f37b84ab27bfa69", "commit_訊息": "[Web] S00-*********** 新增js捞取多语系资料，且同步多AP多语系资料缓存[补]", "提交日期": "2023-11-02 13:35:12", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1221d90fcb03bd2fa6fd29af58ab1f37b6e525c9", "commit_訊息": "[T100] Q00-20231102002 T100的签核历程沒有权限的显示提示告知登入者", "提交日期": "2023-11-02 13:12:36", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessInfoGet.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0a09c39507cbcf2c3ed9d3eb505a28cbe827b656", "commit_訊息": "[Web] Q00-20231102001 修正Excel匯入功能，特殊字元斜線導致異常問題", "提交日期": "2023-11-02 09:11:11", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c77b885c51c9e308f9329b7980d4ff40f77ea8ce", "commit_訊息": "[Web] Q00-20231101005 修正表單欄位為invisible且設定顯示千分位，開啟追蹤流程在F12顯示錯誤", "提交日期": "2023-11-01 15:50:29", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee48ba65e1c78af6050dbf790e9b2b374b87d109", "commit_訊息": "[DT]Q00-20231101004 修正Web化流程管理工具中服務任務的應用程式加入表單型態參數後會有無法簽入的問題", "提交日期": "2023-11-01 15:38:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1be1f2669a46e3bc71368052a2916a94fc23a6be", "commit_訊息": "[雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯[補修正]", "提交日期": "2023-11-01 14:10:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ea47d1f02b13b504347c54f590635bfe728a6c4", "commit_訊息": "[雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯", "提交日期": "2023-11-01 14:04:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "0e3d2cda4a3e2796eed3719d21cb3c3dc5341b1d", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-11-01 11:11:30", "作者": "刘旭", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a78875e8790afbe1c975d7bab8929d3f212a7f83", "commit_訊息": "[Web]Q00-20231101001 开启单点登录接口后，点击邮件进入单点log里会提示警告问题修复", "提交日期": "2023-11-01 11:10:57", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe2fd76ecfe5465f7bb85fb8ecef970c50ab1e29", "commit_訊息": "[MPT]S00-20230906001 首頁模組的公告申請單流程加入通知活動後收到的流程連結可以直連到公告詳情", "提交日期": "2023-11-01 11:10:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ebdf2050c9606508e40d862d08554b471f3f3917", "commit_訊息": "[Web]V00-20231031001 表单设计器中，自定义开窗使用【资料选取设置】选择【资料选取注册器】时在不重新打开表单设计器时选不到新增加的资料，增加刷新按钮，以此来选到新增加的资料选取注册器。 1.刷新按鈕風格與\"資料選擇設定\"按鈕设定一致。2.按下刷新之後增加提示訊息。", "提交日期": "2023-11-01 09:05:06", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "24c03487234a44ed065852bfce7b0115ed020b7d", "commit_訊息": "[Web] Q00-20231031001 修正缩小ESSPlus管理页面时，查询出来的结果在grid显示不全", "提交日期": "2023-10-31 13:39:56", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a6040acc017769b7dcf65ebd4a2a90420fdd714", "commit_訊息": "[DT]Q00-20231030005 修正組織管理工具在部門加入使用者的查詢使用者會提示錯誤問題", "提交日期": "2023-10-30 18:11:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6214e2030de393041eb86b7cf9dd1aed4af1bae5", "commit_訊息": "[Web] Q00-20231030004 调整预览列印会带出表單名稱與表單代號Tab的问题", "提交日期": "2023-10-30 15:54:31", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f8dd8b75de0c4cd3a862947c749b5ed9e25caa41", "commit_訊息": "[Web] Q00-20231030003 修正Excel匯入，單引號導致內容截斷問題", "提交日期": "2023-10-30 14:58:14", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "feefaf2e09dd8dad5fac6f1624bace798d6bf6ba", "commit_訊息": "[流程引擎]Q00-20231030001 修正流程發起者於追蹤流程頁面進入表單畫面點擊撤銷流程後，詳細流程圖的流程詳細資訊應為是「流程發起者」而非「流程負責人」撤銷", "提交日期": "2023-10-30 14:41:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "94adb29722c7518de170a6f3e7032834f9596dbf", "commit_訊息": "[T100]Q00-20231030002 調整T100拋單傳入\"𡘙\"字會造成BPM取得XML內容是被截斷的", "提交日期": "2023-10-30 14:15:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/BpmRmiServiceForT100Impl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "10f36bbef9d7f438c6b67ea3ad99bac3c50acfc1", "commit_訊息": "[Web]S00-20230423001 表单设计器中，自定义开窗使用【资料选取设置】选择【资料选取注册器】时在不重新打开表单设计器时选不到新增加的资料，增加刷新按钮，以此来选到新增加的资料选取注册器。", "提交日期": "2023-10-30 14:01:01", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "db90e3f9635fd89c47a2c25f1cea85a70edad059", "commit_訊息": "[Web] S00-20230213002 流程分类Menu新增根据“此分类全部” 筛选查询功能", "提交日期": "2023-10-30 12:09:39", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "0f168b8d67b19c0fa6c337752aca0f07e2ee1225", "commit_訊息": "[內部]調整因應新模組架構調整所需的多語系", "提交日期": "2023-10-27 18:46:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cf06065c06e1793eb95982ab19f3607623eefde5", "commit_訊息": "[流程引擎]Q00-20231027003 修正流程結案清除附件的服務，當表單附件OID屬性為空時，會被系統移除附件，此調整為避免移除表單實例的附件的OID屬性為空的附件", "提交日期": "2023-10-27 14:39:03", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9427c108a37dc599905a4528d0229c50a2e2a6f8", "commit_訊息": "[ISO] S00-20230818001 新增ISO可自定義文件浮水印字體", "提交日期": "2023-10-27 14:03:14", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "672c6d5d46cfd8f15932dbd1f678e216d66b98a4", "commit_訊息": "[Web]Q00-20231027002 模組的序號全註冊上去時，再次登入就會呈現空白畫面问题修复", "提交日期": "2023-10-27 10:46:35", "作者": "刘旭", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9682271a62e8a8b634adadb4b97305d1d74b9d15", "commit_訊息": "[內部]Q00-20231027001 增加log,核決關卡取得最後一個處理關卡時，若有多個最後一關時，將相關屬性印出", "提交日期": "2023-10-27 10:34:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/AbstractActivityDefContainer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09facb1634e82748d8db72b0e1ecdfd74e74a0c2", "commit_訊息": "[Web]V00-20231024001 BPM文件\\開發教學索引\\Ajax ExtOrg 范例调整", "提交日期": "2023-10-27 10:14:51", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1a4d878ba454415f3af786b627450ae4bc34871", "commit_訊息": "[資安] Q00-20231017003 修改因paloalto內部防火牆把aes.js當成spyware Malicious JavaScript Files Detection攻擊，將aes.js進行加密編碼。", "提交日期": "2023-10-26 16:16:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/aes.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3f2edf2420c1c00bd9a30172e0661f73ff4da491", "commit_訊息": "[Web]Q00-20231026001 修正转由他人处理 > 经常选取对象 无资料时显示错误页面", "提交日期": "2023-10-26 14:29:58", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa2d557c21cc1f80055ff0578be9e37cedf9c198", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補修正)", "提交日期": "2023-10-25 18:14:01", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0d96fa0043db1250d3616f4c7097c0449241551", "commit_訊息": "[MPT]S00-20230505006 首頁調整公告信息維護列表與查詢條件增加發佈狀態與發佈時間區間[補]", "提交日期": "2023-10-25 17:54:52", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "071085d3798726cbcd5d2cf94a88974f5a2e46dc", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題", "提交日期": "2023-10-25 16:35:30", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ae94cf60ee89b89fe7caa26e7441b601d865df8f", "commit_訊息": "[BPM APP]Q00-20231025002 行動端待辦關卡向後加簽時填入的簽核意見沒有帶回主畫面的問題", "提交日期": "2023-10-25 13:49:10", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b543be28585af4ba05a2d388b92e51e8d39309c4", "commit_訊息": "[Web] Q00-20231025001 调整隐藏栏位为單身欄位加總的运算操作验证", "提交日期": "2023-10-25 11:01:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a5b8722fb2e920bb961c0127b47ab58d8c9d8ef2", "commit_訊息": "[MPT]S00-20230906001 首頁模組的公告申請單流程加入通知活動後收到的流程連結可以直連到公告詳情[補]", "提交日期": "2023-10-25 10:32:59", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2958d33131d39f95a4c898396e7e27d219aa3552", "commit_訊息": "[MPT]S00-20230906001 首頁模組的公告申請單流程加入通知活動後收到的流程連結可以直連到公告詳情[補]", "提交日期": "2023-10-24 18:52:42", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a77e70a26f576e45599cb96838a4a94ff5ff8940", "commit_訊息": "[BPM APP]Q00-20231024002 調整ESS表單透過ajax撈取資料載入回Grid元件時發生欄位找不到的問題", "提交日期": "2023-10-24 18:14:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d59a89d215a7a9457d04cdc37e0d900976fe3cb", "commit_訊息": "[ESS]調整ESSF52C1班次互換表單", "提交日期": "2023-10-24 17:15:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF52C1\\347\\217\\255\\346\\254\\241\\344\\272\\222\\346\\217\\233.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF52C1\\347\\217\\255\\346\\254\\241\\344\\272\\222\\346\\217\\233.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF52C1\\347\\217\\255\\346\\254\\241\\344\\272\\222\\346\\217\\233.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b7673911cf0d08816392611015bf7821b37a61ed", "commit_訊息": "[Web] V00-20230906005 SonarQube安全性議題 : 修復潛在ReDos正規表達式效率攻擊問題(補修正)", "提交日期": "2023-10-24 15:37:38", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cb57fa5725be7062cc0961a28be8f08dc378831a", "commit_訊息": "[MPT]S00-20230906001 首頁模組的公告申請單流程加入通知活動後收到的流程連結可以直連到公告詳情", "提交日期": "2023-10-24 15:01:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@mpt/default-process/\\345\\205\\254\\345\\221\\212\\347\\224\\263\\350\\257\\267\\345\\215\\225.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0f94d0b417a482b6f206ac832aab8b25a655de44", "commit_訊息": "[MPT]S00-20230505006 首頁調整公告信息維護列表與查詢條件增加發佈狀態與發佈時間區間[補]", "提交日期": "2023-10-24 14:15:24", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6b353cf0aa00c5fd6ca1d176e232a84ebc915bf9", "commit_訊息": "[MPT]S00-20230505006 首頁調整公告信息維護列表與查詢條件增加發佈狀態與發佈時間區間", "提交日期": "2023-10-24 11:13:54", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e98a861a3625832653cd996072afe0c36fd48473", "commit_訊息": "[組織同步] Q00-20231023004 修正T100同步使用者預設行事曆時，未忽略行事曆初始值。", "提交日期": "2023-10-23 15:24:27", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1241896eca9ce8f89148df92aaf565f629ab086d", "commit_訊息": "[BPM APP]S00-20230720004 IMG直連表單增加可以透過手指放大縮小畫面功能", "提交日期": "2023-10-23 12:00:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "3a4c5fd67aa50402ed38b403f0f5198739e1880d", "commit_訊息": "[Web]Q00-20231023002 修正流程第一關有設定必須上傳新附件，若從流程草稿開啟時，系統沒有卡控必須上傳新附件", "提交日期": "2023-10-23 11:10:11", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e028d59a0d3f064eeb01d1beddf93ccfbb805875", "commit_訊息": "[SSO]QO0-20231023003 修正SSO連結登入後未切換成對應的使用者", "提交日期": "2023-10-23 11:05:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/PortletEntry.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "40e72dfd673542b92f5e86d4ba26b2409330e457", "commit_訊息": "[Web] V00-20230906005 SonarQube安全性議題 : 修復潛在ReDos正規表達式效率攻擊問題(補修正)", "提交日期": "2023-10-23 11:02:47", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "8ce0b743facafbac1d297623fc33dd9f8c464775", "commit_訊息": "[Web]Q00-20231023001 修正列印预览grid标题字体颜色为白色", "提交日期": "2023-10-23 09:15:57", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "225dd262f700d7faad4cdbf1498954163d5023ea", "commit_訊息": "[內部]因應新模組架構調整，併入流程封存模組相關內容", "提交日期": "2023-10-22 00:43:57", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/metadata/nana-app/application.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/nana-app/persistence.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/nana-process-archive/application.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/nana-process-archive/jboss-all.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/nana-process-archive/jboss-deployment-structure.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/nana-process-archive/persistence.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkItem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/audit_data/ActivityInstanceAuditData.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/audit_data/ProcessInstanceAuditData.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/IPersistentService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/JpaService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/JpaServiceForProcessArchive.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/SessionBeanHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/EarUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL_ProcessArchive.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL_ProcessArchive.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 29}, {"commit_hash": "177364295d69f07fef56308b94ff4a662b9786a1", "commit_訊息": "[Web] V00-20230906001 SonarQube安全性議題 : 修复'PWD','Password','PASSWORD'安全检测问题[补修正]", "提交日期": "2023-10-20 16:08:44", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/data/SqlCommander.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed91bde7d8fd14f2fc9db48fb15a2db253bf8d5e", "commit_訊息": "[SSO]S00-*********** 新增AthenaSSO登入機制[補修正]", "提交日期": "2023-10-20 11:56:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/AthenaSSO.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "eb41d344535daa92f7a0f5b617bcf4dd5cd4c49e", "commit_訊息": "[web]Q00-20231011002 登录英语语系，打开监控流程汇出Excel的文件中，流程名称列内容是中文，并不是英文问题修复[补修正]", "提交日期": "2023-10-19 16:24:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e24dc309c06638f44c69b41c86eff4c985390526", "commit_訊息": "[Web] V00-20230906001 SonarQube安全性議題 : 修复'PWD','Password','PASSWORD'安全检测问题[补修正]", "提交日期": "2023-10-19 15:44:57", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1039d42d2711ad3489e8be9aa68870df0d53e279", "commit_訊息": "[Web] V00-20230906001 SonarQube安全性議題 : 修复'PWD','Password','PASSWORD'安全检测问题", "提交日期": "2023-10-19 15:32:02", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/data/SqlCommander.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/FormElementUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c2b6ab3bdab0fd1f219b3aa1a2dc6faeab8d479a", "commit_訊息": "[BPM APP]Q00-20231019001 修正行動端的發起流程時先執行formOpen才執行formCreate的問題", "提交日期": "2023-10-19 14:53:06", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileTool.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d42668841c4b81cbca79dc9c889101d467b24b5e", "commit_訊息": "[BPM APP]S00-20231017001 IMG中待辦列表的轉派資訊字段增加顯示轉派意見[補]", "提交日期": "2023-10-18 18:04:34", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fae1a3c943586e49dcef03384425e6db902389fe", "commit_訊息": "[資安]V00-20230906002 SonarQube安全性議題修正：'TOKEN' detected in this expression, review this potentially hard-coded secret", "提交日期": "2023-10-18 15:11:18", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "7f57932e9312f012ba141b350fc384e23be46f8f", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-10-18 14:54:16", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "79503d9c296c37d53f7c0b0bd082879a292ac222", "commit_訊息": "[资安]V00-20230906003 SonarQube安全性議題修正：'AUTH' detected in this expression, review this potentially hard-coded secret[补修正]", "提交日期": "2023-10-18 14:18:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7b7f55f6a1544f8f87edb591a6a20e31ca3b240a", "commit_訊息": "[資安]V00-20230906004 SonarQube安全性議題修正：Make sure using a dynamically formatted SQL query is safe here[补修正]", "提交日期": "2023-10-18 14:18:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "21f0eec1e1647f58310fa325dcf5d74c49191d73", "commit_訊息": "[BPM APP]S00-20231017001 IMG中待辦列表的轉派資訊字段增加顯示轉派意見", "提交日期": "2023-10-18 11:32:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e50c4d6640f9d24c1df96d3ec2fd0643d90a546a", "commit_訊息": "[WEB]Q00-20231018001 调整Select元件有textbox輸入格时的点击事件逻辑", "提交日期": "2023-10-18 11:13:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/resources/html/SelectElementTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "136f499380ec79977dc7a006b296dc625cf64529", "commit_訊息": "[流程引擎] Q00-20231017004 修正工作受託者<#allAssigneesIDnName>沒有帶出資料的問題", "提交日期": "2023-10-17 14:56:38", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f67fb73e27529e6cb07d25adc3ebbdd48a7e0de8", "commit_訊息": "[DT]Q00-20231017001 修正資料使用權限管理中新增時啟用包含子目錄儲存後會還原的問題", "提交日期": "2023-10-17 13:48:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e705fed93e8625aa81617a00bc9968c035a4db68", "commit_訊息": "[Web] Q00-20231017002 WorkItem表添加Index_WorkItem_limits索引", "提交日期": "2023-10-17 13:46:46", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "63c3b49bc4471613949b5a670e1da4fb9aaeb135", "commit_訊息": "[資安]V00-20230906004 SonarQube安全性議題修正：Make sure using a dynamically formatted SQL query is safe here[補修正]", "提交日期": "2023-10-17 12:04:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISOListExcelServlet.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/mail_timer/MailTimerTaskManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fe7acbcaf410e8f9db9eab9c8f06ab56e6f83c45", "commit_訊息": "[資安]V00-20230906004 SonarQube安全性議題修正：Make sure using a dynamically formatted SQL query is safe here", "提交日期": "2023-10-17 11:59:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/dmm/rdb/QInstanceDao.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionSearchReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimpleExpenseAccountItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimplePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/archive/CopyOrg.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/OrganizationService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "89502ef5fd2d089c2549b455b8cb61899f8249df", "commit_訊息": "[资安]V00-20230906003 SonarQube安全性議題修正：'AUTH' detected in this expression, review this potentially hard-coded secret", "提交日期": "2023-10-16 17:29:52", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "aa68c739bb63d26c53522e0d74e28da8636563cb", "commit_訊息": "[Web] V00-20230906006 SonarQube安全性議題 : 修复Random()安全检测问题", "提交日期": "2023-10-16 13:46:25", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/SchemaType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TiptopAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/background_service/BaseWorkerThread.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/weixin/mp/aes/WXBizMsgCrypt.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "c7d1d09927c35997f1b192395a25f32bd34d2650", "commit_訊息": "[內部]新增Web流程管理工具中批次匯出功能", "提交日期": "2023-10-13 18:00:40", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7d4d812d2fb7df777136fff317d1ae8ac65cb6eb", "commit_訊息": "[Web] S00-*********** 新增js捞取多语系资料，且同步多AP多语系资料缓存[补]", "提交日期": "2023-10-13 14:35:22", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ed938e515048739e5a5deb4be9233d4f8cf1e192", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-10-13 13:43:41", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "388bc350b39b22e52b0c98a696589e5254b34664", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-10-13 13:27:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterDingtalkTodoTaskAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoComplete.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 15}, {"commit_hash": "d2065f4ea058fac8a0f6321ef020478c10c176ac", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補] 1.新增多語系 2.新增sql资料 3.新增维护作业JSP页面 4.新增批次执行、批次删除结果JSP页面 5.调整流程主旨为空是显示【无主旨】 6.调整刪除釘釘待辦同步資訊判断逻辑", "提交日期": "2023-10-13 13:27:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterDingtalkTodoTaskAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoComplete.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 15}, {"commit_hash": "350388dc14066b4b6112f9fe1c0d9f5455560760", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-10-13 09:09:43", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fc6b346b6df01552be5d37e1f16fcfa71df7fae4", "commit_訊息": "[表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗", "提交日期": "2023-10-12 15:30:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ef533b1e165e22da3f37204b69fb281ffc61af82", "commit_訊息": "[Web] Q00-20231012003 ESSF63培训需求采集 增加ESSXQCJ026(所屬公司 Id)、ESSXQCJ027(所屬公司)栏位", "提交日期": "2023-10-12 13:34:52", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@appfrom-essplus/form-default/ESSF63\\345\\237\\271\\350\\250\\223\\351\\234\\200\\346\\261\\202\\346\\216\\241\\351\\233\\206.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75bd7822987d9ad4000d01c97dd150e0932a9fb7", "commit_訊息": "[web]Q00-20231011002 登录英语语系，打开监控流程汇出Excel的文件中，流程名称列内容是中文，并不是英文问题修复", "提交日期": "2023-10-11 17:29:48", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e6993e7d661040b26336497a308aaf2e16ebe8f", "commit_訊息": "[Web] V00-20230906005 SonarQube安全性議題 : 修復潛在ReDos正規表達式效率攻擊問題(補修正)", "提交日期": "2023-10-11 17:03:34", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodSetStatus.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b62e0ed02b3fa2ea3906accd3a4318f452998823", "commit_訊息": "[Web] V00-20230906005 SonarQube安全性議題 : 修復潛在ReDos正規表達式效率攻擊問題", "提交日期": "2023-10-11 16:41:15", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodSetStatus.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "998b1a2a2584d7426eb99368a8969c8002fefd71", "commit_訊息": "[Web] S00-*********** 新增js捞取多语系资料，且同步多AP多语系资料缓存[补]", "提交日期": "2023-10-11 16:33:23", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c4bcd24af65a6d3d3a65d1e34040ef4af4cd8d3b", "commit_訊息": "[Web] V00-20231011001 修正轉存表單日期格式異常問題，支持常用格式。", "提交日期": "2023-10-11 14:21:33", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6afb9bb3f45762e7b601c492093b6ea348b197f9", "commit_訊息": "[資安]Q00-20231011001 強化ajax-DataBaseAccessor服務邏輯以禁止非法調用", "提交日期": "2023-10-11 14:12:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c3d3b94db1261e0cd4945602f80ef356340f1d22", "commit_訊息": "[Web]Q00-20231010001 修正页面大小在宽度在接近768px时，loginBanner图片大小超出显示范围的问题", "提交日期": "2023-10-10 17:28:28", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37db2e3711b195f26cffd794d29613e6924eda0d", "commit_訊息": "[Web] S00-*********** 新增js捞取多语系资料，且同步多AP多语系资料缓存", "提交日期": "2023-10-09 16:03:44", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RsrcBundleDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IServerCacheManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/ISysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LanguageMaintainAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 15}, {"commit_hash": "1e3a5a18c5e041a903c00277c7b3ff22afd17d7d", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-10-06 18:47:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "84031ad00944d07bfdb6f795ece6c17e8e6890f9", "commit_訊息": "[Web]Q00-20231006001 調整ActionFilter在進入的URL中該使用者無權限訪問，應導向錯誤頁面並顯示無權限訊息，而不是回到登入頁重新登入", "提交日期": "2023-10-06 11:52:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ErrorPage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1b92bd287ba86686c31522438206ccb5f76b9cd9", "commit_訊息": "[Web] S00-20221208001 新增清除全系統二階快取系統排程(補)", "提交日期": "2023-10-06 09:54:59", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IServerCacheManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "3ca8c02dbd84b67e249698d12c7d6d711cf435aa", "commit_訊息": "[Web]S00-20220929001 新增BPM外部連結-進入BPM首頁", "提交日期": "2023-10-05 14:23:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/PortletEntry.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e4a87cd2c536add947bc27bc46dbfb9dccf62614", "commit_訊息": "[T100] Q00-20231004004 修正手寫元件造成拋單失敗，新增判斷略過手寫元件", "提交日期": "2023-10-04 14:56:04", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "afa36ce02db5689c825364dca7893737f85bb7a1", "commit_訊息": "[WEB]Q00-20231004003 修正在手機瀏覽器以及RWD窄畫面上沒有附件檔名的URL連結導致無法下載問題", "提交日期": "2023-10-04 14:11:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8458ac85a4df586c269e349f0c351eee841ed1e1", "commit_訊息": "[Web] V00-20231004001 修正匯出表單，日期格式(yy/M/d)被判斷為異常格式問題。", "提交日期": "2023-10-04 12:04:19", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4de477a9492c07697eeaf2d42b7eb021861b27f6", "commit_訊息": "[Web] Q00-20231004002 修正SQL註冊器，關鍵字串判讀異常問題。", "提交日期": "2023-08-01 13:58:32", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f4e10123880fe4785bfecff4c7575881e32a070c", "commit_訊息": "[Web]Q00-20231004001 修正TextBox設定數字轉繁體文字在列印表單時顯示簡體文字的問題", "提交日期": "2023-10-04 10:09:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c11274b1da995151778fdf3ad3695161b8ab4823", "commit_訊息": "[SAP]Q00-20231003003 修正SAP整合作業-SAP欄位對應設定，新增整合設定頁面當選擇完表單後，無法載入表單元件", "提交日期": "2023-10-03 17:39:22", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "487c30fb2daaab6e33b4ee8e79745124e89bee3f", "commit_訊息": "[SSO]S00-*********** 新增AthenaSSO登入機制", "提交日期": "2023-10-03 17:23:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/JSPFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/AthenaSSO.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 19}, {"commit_hash": "e326d458a06340e7457367403c6e308703d2b204", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-09-28 18:36:22", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/CompleteWorkItemEventBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/GenerateNextWorkItemEventBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "561254ba41efaf36946244d3586a795c9ccb3e5d", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]", "提交日期": "2023-09-28 17:59:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d2f81486c59563b07323128bc2599902f34648f0", "commit_訊息": "[DT]Q00-20230928003 修正從Web化流程管理工具入版後的流程圖在Swing中開啟時流程圖會被截斷問題", "提交日期": "2023-09-28 17:56:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99d427a30820c9d2ab4b1ace80d1b6597605a205", "commit_訊息": "[內部]調整流程管理工具開窗驗證機制", "提交日期": "2023-09-28 17:22:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d934760bab851f2b63459823909a4151f162a5dc", "commit_訊息": "[流程引擎]Q00-20230928002 修正核決關卡參考的關卡的處理者為代理人簽核時，核決層級預解析的參考關卡應以原處理者的身分往下解析；而非以代理人的身分往下解析", "提交日期": "2023-09-28 17:07:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "af2241261ce9722bc085faafcbde6a5fd771a0c8", "commit_訊息": "[BPM APP]Q00-20230928001 修正行動端FormUtil.setValue部分設定時會發生異常問題", "提交日期": "2023-09-28 11:22:18", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "282419966c093b96c94d9ed8eaee03c828d9f248", "commit_訊息": "[DT]S00-20230209002 新增Web化組織管理工具中變更主管作業功能[補]", "提交日期": "2023-09-28 11:11:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e65d7594a961e75c6592af3c38d8bb5a2c647f83", "commit_訊息": "[web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复", "提交日期": "2023-09-27 17:19:26", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8760da02a2e4835f65115b580d2dab3b637e48eb", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-09-27 15:28:58", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "de6789a27f12d07e12de8b31610b19d48eacea43", "commit_訊息": "[Web]Q00-20230927002 调整报表设计器sql查询条件中判断Sql Injection相关逻辑", "提交日期": "2023-09-27 15:28:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bef8a0b87fee6a45821dd75c2bcb7062458a5926", "commit_訊息": "[BPM APP]Q00-20230927001 修正行動端的客製開窗不會根據篩選條件搜尋問題", "提交日期": "2023-09-27 14:08:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2cbf17c3917af4ef0aeccefabf96e27e824667ac", "commit_訊息": "[DT]Q00-20230926002 修正Web流程管理工具儲存流程因找不到ActivityType導致儲存失敗問題", "提交日期": "2023-09-26 17:58:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d994b6b2cee910d6be7a9d664277aae4b70bc71", "commit_訊息": "[DT]Q00-20230926003 修正Web流程管理工具的流程圖進版後有條件的連接線顏色變黑色問題", "提交日期": "2023-09-26 17:48:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "550ce1764ee11f75646ab4b0c9e65bf1a067a7bf", "commit_訊息": "[DT]Q00-20230926004 修正Web流程管理工具的流程圖進版後連接線上的名稱消失問題", "提交日期": "2023-09-26 17:44:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d73a7ec68db64ea476b14d86fb4dff44ffe81d26", "commit_訊息": "[在線閱覽]Q00-20230926001 修正在線閱讀檔案設定不可下載，在待辦表單頁面點擊閱讀檔案的頁面，仍可以下載PDF閱讀檔的異常", "提交日期": "2023-09-26 16:01:14", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cea028409e603099554bd532d44462450bc7420e", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]", "提交日期": "2023-09-26 14:32:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b2cf4fe4559d2d58c09c16374cc43db06ee42d50", "commit_訊息": "[DT]Q00-20230925003 修正通知編輯範本中變數清單缺少問題", "提交日期": "2023-09-25 17:36:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3a4453d0b7d7d0fe63c9f5c12c33462ef598107b", "commit_訊息": "[DT]S00-20230209002 新增Web化組織管理工具中變更主管作業功能[補]", "提交日期": "2023-09-25 17:34:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b4365f0d4a540a482e545c3d8d01184d34e008b3", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。", "提交日期": "2023-09-25 17:24:13", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "763e56568aa8473e6ee047d210a1169814a76821", "commit_訊息": "[DT]S00-20230209002 新增Web化組織管理工具中變更主管作業功能[補]", "提交日期": "2023-09-25 10:47:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "43a373e1bc0cbbba1a96fb89c9132ec27da5f2a1", "commit_訊息": "[Web]S00-20230731001 调整簽核意見「片語清單」視窗大小。", "提交日期": "2023-09-25 10:12:47", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f4800722a9596550d6148f03fc192bef9d7d3279", "commit_訊息": "[Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。[补修正]", "提交日期": "2023-09-25 09:55:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f648359eac8c874007f10c057d23706eebf0bb25", "commit_訊息": "[Web] S00-20221208001 新增清除全系統二階快取系統排程", "提交日期": "2023-09-25 09:21:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "765f243c574e3ec1ed5185f98110bf85944cb6e5", "commit_訊息": "[Web] V00-20230925001 調整開發者工具清除快取資料類別名稱應不須加上後綴名.class", "提交日期": "2023-09-25 09:13:18", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29bc7a5cd381a2e9dcf7cc8720fee5deded01e33", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-09-25 09:00:00", "作者": "刘旭", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 3}, {"commit_hash": "f248da8981f7369fcf245c532b870b4e9fcef2e4", "commit_訊息": "[web]Q00-20230919002 「系統設定」內有不少的Key項目沒有對應的中文描述，若這些設定不是要開放給管理者設定使用的，則請隱藏該Key項目 问题修复[補修正]", "提交日期": "2023-09-23 19:31:08", "作者": "刘旭", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "05ba82761bb080136b0287fea0199e5774f07672", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-09-23 18:21:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/mobile/AdapterDingtalkTodoTaskDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterDingtalkTodoTask.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterDingtalkTodoTaskAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "6fe5cc48d1e3dcc0c99120f451486455cc39b71f", "commit_訊息": "[ISO] 專案-ISO全文檢索版本升版", "提交日期": "2023-09-23 16:59:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.4_DDL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "367c9c3b4175ddae1d7ff20e20793ec158e0994d", "commit_訊息": "[組織同步] S00-20230201001 調整HR組織同步，郵件設置改取系統管理中的郵件設定(補修正)", "提交日期": "2023-09-23 15:09:11", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/SendMailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailDTO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cfa5f561b56b71a4b0299c06b6274c60f4a476a3", "commit_訊息": "[Web] Q00-20230922005 調整系統多語系錯誤的英文拼字與缺少的簡體字", "提交日期": "2023-09-22 17:31:54", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f8cf61ca39f3518e7a65bc2d38bb84a5ed2fc381", "commit_訊息": "[Web] Q00-20230922004 在没有添加grid按钮却使用grid方法时，新增防呆，防止focus报错", "提交日期": "2023-09-22 14:57:46", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6fd0de07a9bc4c928bf0fe3a572e14effe672e19", "commit_訊息": "[Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效", "提交日期": "2023-09-22 11:21:43", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "60b18c6727a47772d50a6f6719400dd1d84f77f5", "commit_訊息": "[Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加", "提交日期": "2023-09-22 10:42:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fad6b3a3d1a6ffb15fab31be61759998d54bba7", "commit_訊息": "[Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错", "提交日期": "2023-09-22 10:27:58", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37b6dbcb32a575a22c6cbb352adecf063af7e3aa", "commit_訊息": "[組織同步] S00-20230201001 調整HR組織同步，郵件設置改取系統管理中的郵件設定(補修正)", "提交日期": "2023-09-22 10:25:25", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/SendMailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cabd9a8ea61752315efe67127f2cad59fabb1eba", "commit_訊息": "[組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號(補修正)", "提交日期": "2023-09-21 17:05:57", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18d62cd487f16d002090621f90c51b2a25183828", "commit_訊息": "[DT]Q00-20230921001 修正Web流程管理工具中參與者選職務或職稱後儲存再開啟其值會消失的問題", "提交日期": "2023-09-21 16:13:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "98763264268b5f6ef77397deb50ab283451f88c9", "commit_訊息": "[Web] S00-20230202001 表单脚本开发时新增填单者多语系名称userNameByLocale。[補]", "提交日期": "2023-09-21 12:08:28", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "00945d870008a1ffb3c6adcf380edae5507b494a", "commit_訊息": "[組織同步] S00-20230201001 調整HR組織同步，郵件設置改取系統管理中的郵件設定", "提交日期": "2023-09-21 11:16:21", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/SendMailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/syncorg/SyncTable.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c96e4cb32070c48f6820a26844333c9e716270e5", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-09-21 10:06:14", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/mobile/AdapterDingtalkTodoTaskDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterDingtalkTodoTask.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterDingtalkTodoTaskAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "36cbd294cb1455bd9f801247d95028c376aca1a0", "commit_訊息": "[DT]Q00-20230920002 修正Web系統權限管理員上儲存按鈕後畫面會一直loading的問題", "提交日期": "2023-09-20 16:38:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa48fe2e6c470c9dad8079d1af21be064169fa13", "commit_訊息": "[組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號。", "提交日期": "2023-09-20 13:44:52", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b393682573d105da434abc0c923c207f6df64cd3", "commit_訊息": "[DT]A00-20230919001 修正Web流程管理工具中事件處理在流程儲存後會消失問題", "提交日期": "2023-09-20 12:01:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "97881688d622174e57290abd070363261b26ede2", "commit_訊息": "[TFG] Base部分新增TFG專案。", "提交日期": "2023-08-24 10:32:36", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/tfgModule/TFGDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tfgModule/TFGManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tfgModule/TFGManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/EbgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DocFileUploader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFileManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/FileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 25}, {"commit_hash": "5600cab9d478d7c3eed2abc3b4a67b437a9415ae", "commit_訊息": "[ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為\"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程\"[補修正]", "提交日期": "2023-09-19 13:42:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e91324fd76c46eaa6e422e844ee1b3bc6edd53e0", "commit_訊息": "[web]Q00-20230919002 「系統設定」內有不少的Key項目沒有對應的中文描述，若這些設定不是要開放給管理者設定使用的，則請隱藏該Key項目 问题修复", "提交日期": "2023-09-19 13:06:02", "作者": "刘旭", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "44f216c634bab17748520e9ad347bc49ad41d4bc", "commit_訊息": "[Web]Q00-20230919001 調整URL從/NaNaWeb/Login.jsp?type=admin登入時會報閒置過久的訊息改成\"請輸入正確的代號或密碼!\"", "提交日期": "2023-09-19 12:02:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "971882837c53d59d495bde35a95ac340806f1c34", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案[補]", "提交日期": "2023-09-18 17:05:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterDingtalkServiceType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterDingtalkTodoTaskAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "cee3165ff121281f59466ebeacab3c46bc609a29", "commit_訊息": "[Web]S00-20230202001 表单脚本开发时新增填单者多语系名称userNameByLocale。[补修正]", "提交日期": "2023-09-18 16:51:38", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e61668816b2333ae41ae92d0be6068f5f182cdf3", "commit_訊息": "[Web] S00-20230202001 表单脚本开发时新增填单者多语系名称userNameByLocale。", "提交日期": "2023-09-18 15:25:58", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/Constants.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "0990155336fadabc52e093fd41bf3f6afa8921e0", "commit_訊息": "[DT]Q00-20230918002 修正Web流程管理工具中連接線編輯後儲存流程再開啟會變成藍色的問題", "提交日期": "2023-09-18 14:40:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad05f7ddfc8d42a2c04b6fe8f42dcb17aa19766e", "commit_訊息": "[ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為\"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程\"", "提交日期": "2023-09-18 14:02:51", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "94a178851c64f5a24304bc7370438fd4235b0695", "commit_訊息": "[web]Q00-20230913001 沒有未閱讀的工作通知，但右上角鈴鐺還是一直有紅點點问题修复", "提交日期": "2023-09-18 09:24:41", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "05c32cd26dc19919845db734f6806e2cde50c20b", "commit_訊息": "[DT]S00-20230209002 新增Web化組織管理工具中變更主管作業功能[補]", "提交日期": "2023-09-15 18:32:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07b238c5654530a2ba1a6c3318b5baf4b6a8eeee", "commit_訊息": "[DT]S00-20230209002 新增Web化組織管理工具中變更主管作業功能", "提交日期": "2023-09-15 18:23:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b2822505b2d235bba42aa0bcea166d8f3ed7c103", "commit_訊息": "[Web] Q00-20230915001 修正 SQL注册器点击资料返回到新增页面，数据错误带回显示", "提交日期": "2023-09-15 11:09:32", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "da1832e67044cb0b4f093a8db28ae49490648483", "commit_訊息": "[內部] 增加完成簽核工作(workItem)時要執行的jndi服務及啟動下一關工作(workItem)時要執行的jndi服務", "提交日期": "2023-09-15 11:00:08", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/CompleteWorkItemEventBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/GenerateNextWorkItemEventBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.9.4_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "f2c6b643abb53fbe0ab98f1a7920e11e3577d91e", "commit_訊息": "[表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]", "提交日期": "2023-09-15 10:22:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "afdafff3cb31581e8882d2e4760b35a9b492354a", "commit_訊息": "[Web]Q00-20230914001 修正RadioButton，checkbox元件帶入值到grid时报错，新增防呆。", "提交日期": "2023-09-14 16:07:26", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb3f3ce852da1d70061222760bcc911e95fa16ed", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)", "提交日期": "2023-09-12 17:23:00", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "64d33c6e4c0cd92e7d1562336acfdafa536986f6", "commit_訊息": "[Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。", "提交日期": "2023-09-12 15:29:48", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1e22d43d247ba4b8a6deacc6de4a27dbf1ee96ca", "commit_訊息": "[Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)", "提交日期": "2023-09-12 10:23:55", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b907b8a675d3b4fb9e2fad8158662aa1781014ad", "commit_訊息": "[MPT]S00-20230210001 調整公告信息維護作業增加公告立即下架功能[補]", "提交日期": "2023-09-12 10:15:04", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d25f7b178fc6b1d0fc7885360ba71715d639eadd", "commit_訊息": "[Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法", "提交日期": "2023-09-12 10:06:27", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ed2789dff14d03cc4c9d4a8446b94420f5022db", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】", "提交日期": "2023-09-11 16:08:36", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e6d4ef423af29b4fe9f669f70a5e404c1ec53f7b", "commit_訊息": "[Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。", "提交日期": "2023-09-11 15:39:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "100117fc1dc5c87fbe2a5ef672b9904b88203e3e", "commit_訊息": "[Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误", "提交日期": "2023-09-11 11:47:04", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "750cfe24e4057c04d7a53511dfd4e28ccbca79bc", "commit_訊息": "[表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題", "提交日期": "2023-09-08 16:39:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bda5bcc002effc96a9feab90a9c9a1b977111c99", "commit_訊息": "[Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]", "提交日期": "2023-09-08 10:14:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "87fdbb6207f3f8490d240de23de6bbbc50a734db", "commit_訊息": "[SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗", "提交日期": "2023-09-08 09:24:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a862f1c58039284b60dbc5661b92d47ca1e75890", "commit_訊息": "[BPM APP]新增釘釘待辦整合專案", "提交日期": "2023-09-07 21:40:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/lib/DingTalk/dingtalk-2.0.28.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/mobile/AdapterDingtalkTodoTaskDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/field_handler/database/AdapterDingtalkServiceType2StringFieldConversion.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/field_handler/database/AdapterDingtalkStatusType2StringFieldConversion.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterDingtalkServiceType.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterDingtalkStatusType.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterDingtalkTodoTask.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MobileAuthorizeUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterDingtalkTodoTaskAccessor.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 31}, {"commit_hash": "a3f0a1eafc87630474fa45a913af29797370aa73", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】", "提交日期": "2023-09-07 17:54:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "472a432f8b4a991daf054f22da3cc18c4d1ab405", "commit_訊息": "[流程引擎]S00-20230130001 調整信件區分流程轉派給代理人", "提交日期": "2023-09-07 14:14:10", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "036423e0469e06ea66c77747bf425b8c4ed53db9", "commit_訊息": "[Web]Q00-20230907001 读取自定义background,Banner,logo图片时，新增图片格式防呆。", "提交日期": "2023-09-07 11:31:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "acbb0dfa29e5c6e382c1fa441a220d14862af88b", "commit_訊息": "[流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常", "提交日期": "2023-09-07 10:31:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a64ec7f8cc1084831d1ece4c85851d7735fa272c", "commit_訊息": "[Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。", "提交日期": "2023-09-06 13:19:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d497ca525c215ecfb8b57ad3e676f1856214ee71", "commit_訊息": "[Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。", "提交日期": "2023-09-06 12:03:56", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9d5ce9b43eca0abbc0f455967a0a8d4a172c2966", "commit_訊息": "[Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白", "提交日期": "2023-09-06 11:11:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "25eb9dd29332c3b8bc72d8d5a09159127ce7906b", "commit_訊息": "[Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。", "提交日期": "2023-09-05 18:01:25", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "67385a4c6989c6e58ce6aa6d38d4bc6bf9a5cded", "commit_訊息": "[MPT]Q00-20230905003 修正公告申請單的公告內文(富文本)在列印模式仍可修改的問題", "提交日期": "2023-09-05 11:58:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "Release/copyfiles/@mpt/default-form/MptAncApply.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb5879f6e8bb70e6a9459bcfbaebaa4da5a0dcf9", "commit_訊息": "[Web] Q00-20230905002 修正Gird元件在追蹤與監控流程下，關卡設置隱藏時開啟表單會彈出null訊息的問題", "提交日期": "2023-09-05 11:48:13", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3894ac291fc0b2286b001b7291594253f10cfe8d", "commit_訊息": "[T100]S00-20220513001 T100取簽核歷程新增是否為代理人標籤", "提交日期": "2023-09-05 10:16:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTag.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1809a0d6145213e9114afcd3f34da5dd00fd7d4c", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)", "提交日期": "2023-09-04 10:53:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f2b16c1742375b9f04589dad8b0bc6d9521db8fd", "commit_訊息": "[DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題", "提交日期": "2023-09-01 19:47:59", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bea248021ab47ce16aeedd214f301860cffb7ae3", "commit_訊息": "[流程引擎] Q00-20230901008 修正使用者A在待辦關卡發送關卡通知給B，同時又將工作轉派給B時，B透過待辦清單進入表單頁面時，表單畫面有時為通知畫面，有時為待辦畫面的異常", "提交日期": "2023-09-01 16:59:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cace10307b7a4152426dfaf7b43659bd1afd1d15", "commit_訊息": "[WorkFlow]Q00-20230901003 修正附件URL帶有#字號會無法下載附件", "提交日期": "2023-09-01 14:46:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/TiptopUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dfff947eaa294bb26eca87eb632ffee0d3bd4d37", "commit_訊息": "[BPM APP]S00-20230614001 移動端的聯絡人頁面增加電話欄位", "提交日期": "2023-09-01 14:19:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContactV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListContact.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmAppContact.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "15ace27b3085366e2e90720d4ebfce9d23616196", "commit_訊息": "[MTS]Q00-20230901001 調整不允許取消已逾時會議時簡體中文提示訊息有誤問題", "提交日期": "2023-09-01 11:35:03", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d8eab08d20428e76f334a50404869c896227160e", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題", "提交日期": "2023-08-31 15:41:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53e72848ee88b4dd576c0ce7b3094cdf79d64a80", "commit_訊息": "[Web] Q00-20230831002 checkbox元件未输入显示值，在追踪或者打印页面显示异常", "提交日期": "2023-08-31 13:44:25", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-form-component.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22cda498d53a59530e569b84dced736b57750b18", "commit_訊息": "[BPM APP]S00-20230609003 在整合企業微信與釘釘時推播訊息改用郵件主旨當標題", "提交日期": "2023-08-31 11:43:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b670b78a79d4d89c79cff5b3047671262369f65d", "commit_訊息": "[Web]Q00-20230831001 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件", "提交日期": "2023-08-31 09:16:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6debd188a26a5ca3a7179027636a9b34ed7db253", "commit_訊息": "[Web]Q00-20230830002 調整上傳附件畫面樣式與附件資訊無法呈現的問題", "提交日期": "2023-08-30 11:47:29", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b94077ee25724462ee313b1af838feaa8c3c1e2d", "commit_訊息": "[TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷", "提交日期": "2023-08-30 10:43:55", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76d7cbb0ce1a10df42c0fc64074933d06cd3b337", "commit_訊息": "[流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常", "提交日期": "2023-08-29 16:50:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ff275bd44c4a296cfa897e87591700895ffb5ebb", "commit_訊息": "[ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗", "提交日期": "2023-08-29 15:50:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c109922c311c8d18809c5d1cf590c52d4d24df08", "commit_訊息": "[Web] Q00-20230829002 修正資料選取註冊器查詢條件異常問題，調整為包子查詢的方式。", "提交日期": "2023-08-29 14:59:58", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "351aead2b10b71e61914b1d8496587d8be622439", "commit_訊息": "[web]Q00-20230829003 列印時附件資訊會超出邊界问题修复", "提交日期": "2023-08-29 14:02:09", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bff544a4395438c27277e7bc83fa1971abdf257a", "commit_訊息": "[流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據", "提交日期": "2023-08-29 10:32:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "729a4159d56267856502d48014513ad78c74a136", "commit_訊息": "[SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤", "提交日期": "2023-08-28 16:57:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "80a58ae82b1b9c01168e200faa84c0ad623d92d7", "commit_訊息": "[其他] Q00-20230828002 报表设计器>【SQL条件清单】 增大 ReportDesignerDefinition表的sqlConditionLists栏位的长度 [补修正]", "提交日期": "2023-08-28 14:21:46", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f81601cb95244b801a00d79a04dea0002480092", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-28 11:32:49", "作者": "liu<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "728b2fb2e2552714e5bd811c84ab206810f13c30", "commit_訊息": "[其他] Q00-20230828002 报表设计器>【SQL条件清单】 增大 ReportDesignerDefinition表的sqlConditionLists栏位的长度", "提交日期": "2023-08-28 11:32:12", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.9.3_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "9bdba8daf929442c933849fcec3da1826d5cd1bc", "commit_訊息": "[DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題", "提交日期": "2023-08-28 11:15:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "45473efca1f9f71f71d25e3a4f7392bcaccbefe1", "commit_訊息": "[web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复", "提交日期": "2023-08-25 17:38:15", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ec244faaeac9ad7f0f743bfee03cc89ef24d5eee", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面", "提交日期": "2023-08-25 15:40:09", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "524890483260684a97ace08b9578ce86aa2e92bb", "commit_訊息": "[Web]V00-20230413001 修正grid中Grid_add,Grid_edit,Grid_delete按钮使用FormUtil.disable语法无效。", "提交日期": "2023-08-25 11:40:29", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70339f9af431838286ef27fcd1d12517f402e904", "commit_訊息": "[MPT]S00-20230210001 調整公告信息維護作業增加公告立即下架功能[補]", "提交日期": "2023-08-25 10:58:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4a476f7366758c40f04ddc8f1312524f448597b6", "commit_訊息": "[MPT]S00-20230210001 調整公告信息維護作業增加公告立即下架功能", "提交日期": "2023-08-25 10:16:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_5.8.9.4_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "01642262f1802e1da06bac3fb6c4a6c5233f0fd8", "commit_訊息": "[web]Q00-20230821001 FormUtil.setValue方式给隐藏栏位赋值，结果不对问题修复", "提交日期": "2023-08-24 16:22:37", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "51fffc7cbc51424ca7252d24abbe009520295e6d", "commit_訊息": "[Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。", "提交日期": "2023-08-24 13:43:54", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8c378272f6a70e3d53f960d2ae4cd48cbb19f14", "commit_訊息": "[Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题[补修正]", "提交日期": "2023-08-24 13:39:10", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "24382464e30721dad8c790a199353061004ef708", "commit_訊息": "[BPM APP]Q00-20230818001 調整釘釘顯示的錯誤訊息增加多語系", "提交日期": "2023-08-24 11:09:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d12358a12a598c0edbaf4ba2ba4595aa2a7be9c6", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2023-08-24 11:00:35", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "db955a9c1df82e1a7de145af4d89a45c981ffd94", "commit_訊息": "[流程引擎] Q00-20230424002 RMI服务注册端口占用报错信息优化", "提交日期": "2023-08-24 10:21:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/RmiRegistry.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b377ca7c7503d24c1aa64502ecd6b2238bc35904", "commit_訊息": "[Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题", "提交日期": "2023-08-24 09:32:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "80eedc7dd57453f9c69047091cd9ba1d3afeb099", "commit_訊息": "[Web] V00-20230330001 修正报表设计器>Step2.sql语法设定回显值错误", "提交日期": "2023-08-23 17:50:31", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc4c613caaf6a3556cec99cc3726574e09186b50", "commit_訊息": "[web]S00-20230420001 由工作通知進表單後，更多中的\"發送通知\"功能可由流程设计师中通知任务进阶中的允许转寄通知来控制开或关", "提交日期": "2023-08-23 17:37:07", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bf030c395a9e7a0092848fbd50afc3368f7ef83f", "commit_訊息": "[E10]Q00-20230817003 修正表單同步無法新增元件", "提交日期": "2023-08-23 17:10:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f5e68b7e7186df1e3c92efaf8a25a10789cf8ff2", "commit_訊息": "[Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常", "提交日期": "2023-08-23 15:27:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}]}