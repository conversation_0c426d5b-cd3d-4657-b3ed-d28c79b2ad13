# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.3.3
- **舊版本**: release_5.8.3.2
- **生成時間**: 2025-07-18 11:44:50
- **新增 Commit 數量**: 39

## 變更摘要

### lorenchang (1 commits)

- **2022-06-26 22:58:30**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.3.3
  - 變更檔案: 25 個

### cherryliao (1 commits)

- **2020-07-29 19:57:59**: [BPM APP]Q00-20200729001修正移動授權中間層整合釘釘時無法登入系統，取RedirectUrl時拋NullpointerException
  - 變更檔案: 1 個

### yamiyeh10 (2 commits)

- **2020-07-28 17:02:01**: [BPM APP]Q00-20200617003 修正行動端在使用表單函式庫的設定元件字體顏色功能沒有效果問題[補]
  - 變更檔案: 1 個
- **2020-07-06 14:09:46**: [BPM APP]Q00-20200617003 修正行動端在使用表單函式庫的設定元件字體顏色功能沒有效果問題
  - 變更檔案: 1 個

### waynechang (5 commits)

- **2020-07-28 11:04:11**: [ISO] C01-20200727009修正BCL8轉檔時，未釋放bepprint.exe資源
  - 變更檔案: 1 個
- **2020-07-13 17:17:55**: [流程引擎]A00-20200512001 優化刪除系統排程時，不須透過重啟Server的方式就能停止排程
  - 變更檔案: 1 個
- **2020-07-10 17:42:01**: [流程設計師]Q00-20200710003 修正流程設計師，當核決關卡設定參考活動為自行定義關卡後，簽入簽出後就無法重新開啟核決關卡
  - 變更檔案: 1 個
- **2020-07-10 17:35:54**: [流程引擎]A00-20200527001 修正核決關卡設定參考活動設定自行定義後，流程要派送到核決關卡會出現錯誤
  - 變更檔案: 1 個
- **2020-07-07 16:06:23**: [Web]Q00-20200707001 調整Backspace控制，讓OA使用的文件編輯套件可以使用
  - 變更檔案: 1 個

### walter_wu (2 commits)

- **2020-07-27 16:20:51**: [Web]Q00-20200727001 修正監控流程功能按下產生統計圖，會有sql報錯
  - 變更檔案: 1 個
- **2020-06-24 16:34:31**: [Web]C01-*********** 修正企業流程監控系列功能流程統計的異常
  - 變更檔案: 7 個

### pinchi_lin (3 commits)

- **2020-07-24 17:59:47**: [BPM APP]Q00-20200710001 修正中間層簽核歷程退回重辦意見覆蓋顯示問題
  - 變更檔案: 1 個
- **2020-07-24 14:55:00**: [BPM APP]Q00-20200428001 修正產品與客製開窗與加簽畫面中欄位增加導致checkbox樣式跑版問題
  - 變更檔案: 1 個
- **2020-07-07 19:39:35**: [BPM APP]Q00-20200601001 修正IMG應用角標不出現問題
  - 變更檔案: 1 個

### 林致帆 (13 commits)

- **2020-07-24 14:49:31**: [Web]A00-20200715002 修正表單欄位有設定運算規則，當其中某個欄位有負數，造成js報錯[補修正]
  - 變更檔案: 1 個
- **2020-07-24 14:41:46**: [Web]A00-20200715001 修正表單欄位為invisible且設定顯示千分位，開起表單在F12顯示會報錯
  - 變更檔案: 1 個
- **2020-07-24 08:40:26**: [Web]A00-20200720002修正手機端點選檢視附件，附件頁面無檔名也無法查看附件內容
  - 變更檔案: 2 個
- **2020-07-23 18:06:42**: [流程引擎]A00-20200717002 修正從TipTop點擊簽核狀況，導入畫面為Portal登入頁面而不是BPM單據頁面
  - 變更檔案: 2 個
- **2020-07-22 18:36:58**: A00-20200720003 修正點擊追蹤流程左測的流程分類下的流程，清單頁顯示異常
  - 變更檔案: 1 個
- **2020-07-20 15:43:04**: [Web]A00-20200716001 修正從TT傳回來的表單有欄位為千分位，儲存表單後數字會不正確
  - 變更檔案: 1 個
- **2020-07-16 17:54:53**: [Web]A00-20200715002 修正表單欄位有設定運算規則，當其中某個欄位有負數，造成js報錯
  - 變更檔案: 1 個
- **2020-07-14 18:15:31**: [Web]A00-20200710001修正RWD響應式中的Radio元件屬性設定「最後一個選項額外產生輸入框」的功能，在流程執行到第三關時，該TextBox欄位內容消失
  - 變更檔案: 1 個
- **2020-07-08 14:06:11**: [流程設計師]A00-20200324002 修正當流程未進版時，若更改核決權限關卡的Id，會造成核決關卡無法點開[補修正]
  - 變更檔案: 1 個
- **2020-07-08 11:24:01**: [流程設計師]A00-20200324002 修正當流程未進版時，若更改核決權限關卡的Id，會造成核決關卡無法點開
  - 變更檔案: 1 個
- **2020-07-07 18:09:02**: [流程引擎]A00-20200618001 修正從Mail待辦事項的連結進入時，如果該使用者待辦為0筆，開啟的畫面會是待辦清單的異常
  - 變更檔案: 1 個
- **2020-07-02 18:22:10**: [Web]A00-20200617001修正流程關卡派送到下一關為多人處理關卡畫面時,TextArea元件為可編輯狀態
  - 變更檔案: 2 個
- **2020-07-01 18:21:06**: [Web]A00-20200608001修正列出工作受託者清單在流程點擊派送後，若下一關無人處理，瀏覽器的開發者工具會出現gBpmList未經定義的錯誤
  - 變更檔案: 1 個

### yanann_chen (4 commits)

- **2020-07-23 11:40:17**: [Tiptop]A00-20200528001 修正問題: tpform包含單獨的label導致表單定義更新失敗
  - 變更檔案: 1 個
- **2020-07-20 14:30:49**: [表單設計師]S00-20170531001 在RWD表單設計師中，複合元件的開窗類型為"部門"、"專案"或"群組"時，預設勾選"前置組織代號"
  - 變更檔案: 1 個
- **2020-07-03 10:12:14**: [流程引擎]Q00-20200702001 修正追蹤流程清單統計接口邏輯
  - 變更檔案: 1 個
- **2020-07-02 11:01:59**: [流程引擎]Q00-20200618001 修正問題: 追蹤流程清單api未回傳執行中的處理者
  - 變更檔案: 5 個

### 王鵬程 (5 commits)

- **2020-07-22 17:59:22**: [Web]Q00-20200722001 修正模組程式維護中刪除一筆程式資料並點擊一筆Row，則欄位上的值顯示不正確，而最後一筆會無法帶回欄位
  - 變更檔案: 1 個
- **2020-07-21 16:46:10**: [Web]A00-20200713002 將模組程式維護頁面中，已加入的程式資料不可更改程式代號。
  - 變更檔案: 1 個
- **2020-07-17 16:27:20**: [Web]C01-20200716001 修正資料庫為Oracle時，設一般使用者為某流程的負責人，當使用者進入監控流程頁面中左側顯示的筆數異常
  - 變更檔案: 1 個
- **2020-07-08 18:30:50**: [表單設計師]C01-20200629004 產品開窗增加過濾條件可以使用組織代號和組織名稱，且開窗的條件也可選這兩個新增的條件
  - 變更檔案: 1 個
- **2020-06-29 18:28:18**: [Web]C01-20200618005 修正在系統設定中的password.policy.rule，輸入規則以外的值導致變更密碼頁面開啟會全白
  - 變更檔案: 2 個

### 詩雅 (3 commits)

- **2020-06-30 14:50:22**: [BPM APP]C01-20200624003調整產生直連表單表單畫面判斷邏輯
  - 變更檔案: 2 個
- **2020-06-30 14:46:41**: [BPM APP]C01-20200624003調整鼎捷移動直連表單網址串上流程OID[補-還原程式邏輯]
  - 變更檔案: 1 個
- **2020-06-30 09:43:10**: [BPM APP]C01-20200624003調整鼎捷移動直連表單網址串上流程OID
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.3.3
- **Commit ID**: `c0c210b7f9f7681079b38fb39f8d629c50b1d17b`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:58:30
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [BPM APP]Q00-20200729001修正移動授權中間層整合釘釘時無法登入系統，取RedirectUrl時拋NullpointerException
- **Commit ID**: `5b5462031f65356b32c6ff95658a9c52e1a05ce5`
- **作者**: cherryliao
- **日期**: 2020-07-29 19:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`

### 3. [BPM APP]Q00-20200617003 修正行動端在使用表單函式庫的設定元件字體顏色功能沒有效果問題[補]
- **Commit ID**: `bdd8476dfe03419eab8a4b682bec55d898223e47`
- **作者**: yamiyeh10
- **日期**: 2020-07-28 17:02:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 4. [ISO] C01-20200727009修正BCL8轉檔時，未釋放bepprint.exe資源
- **Commit ID**: `d90a1f9945e0de3efe719ef8e23987605131b858`
- **作者**: waynechang
- **日期**: 2020-07-28 11:04:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 5. [Web]Q00-20200727001 修正監控流程功能按下產生統計圖，會有sql報錯
- **Commit ID**: `5c49ddd1d5f737183d2fd1ee7c13fa4bffa6e71e`
- **作者**: walter_wu
- **日期**: 2020-07-27 16:20:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`

### 6. [BPM APP]Q00-20200710001 修正中間層簽核歷程退回重辦意見覆蓋顯示問題
- **Commit ID**: `4d9aa021beeb73e1b6f524120b8db84a5744d256`
- **作者**: pinchi_lin
- **日期**: 2020-07-24 17:59:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 7. [BPM APP]Q00-20200428001 修正產品與客製開窗與加簽畫面中欄位增加導致checkbox樣式跑版問題
- **Commit ID**: `1f8bb8bba01e7483ebab2811967a497afd34df2c`
- **作者**: pinchi_lin
- **日期**: 2020-07-24 14:55:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 8. [Web]A00-20200715002 修正表單欄位有設定運算規則，當其中某個欄位有負數，造成js報錯[補修正]
- **Commit ID**: `695a08fef0da84ccdd72667c9904fa918418f0c6`
- **作者**: 林致帆
- **日期**: 2020-07-24 14:49:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 9. [Web]A00-20200715001 修正表單欄位為invisible且設定顯示千分位，開起表單在F12顯示會報錯
- **Commit ID**: `9aa6656847ae2e0db643745f74ee7ed5ac66f6d1`
- **作者**: 林致帆
- **日期**: 2020-07-24 14:41:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 10. [Web]A00-20200720002修正手機端點選檢視附件，附件頁面無檔名也無法查看附件內容
- **Commit ID**: `b180fe47ea0fe594636f4a9f9e6c2fc025f91224`
- **作者**: 林致帆
- **日期**: 2020-07-24 08:40:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 11. [流程引擎]A00-20200717002 修正從TipTop點擊簽核狀況，導入畫面為Portal登入頁面而不是BPM單據頁面
- **Commit ID**: `b8e4789fbc1b41fe47d7ad1ce5e5d5dc0d3b1c9d`
- **作者**: 林致帆
- **日期**: 2020-07-23 18:06:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 12. [Tiptop]A00-20200528001 修正問題: tpform包含單獨的label導致表單定義更新失敗
- **Commit ID**: `fde1b99fe24388ea0c4e1fd01b4bfa3fbc1212e3`
- **作者**: yanann_chen
- **日期**: 2020-07-23 11:40:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-importer/src/com/dsc/nana/user_interface/apps/form_importer/TiptopLabelElementTransfer.java`

### 13. A00-20200720003 修正點擊追蹤流程左測的流程分類下的流程，清單頁顯示異常
- **Commit ID**: `e84ee2e33bffe01e96c896581c83033a16aca1c6`
- **作者**: 林致帆
- **日期**: 2020-07-22 18:36:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 14. [Web]Q00-20200722001 修正模組程式維護中刪除一筆程式資料並點擊一筆Row，則欄位上的值顯示不正確，而最後一筆會無法帶回欄位
- **Commit ID**: `74b10c17be95feae24d395553708c3534a9e4ab0`
- **作者**: 王鵬程
- **日期**: 2020-07-22 17:59:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 15. [Web]A00-20200713002 將模組程式維護頁面中，已加入的程式資料不可更改程式代號。
- **Commit ID**: `4b9245c37f2f86242dab4fbe33e60ea37b57bb8e`
- **作者**: 王鵬程
- **日期**: 2020-07-21 16:46:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`

### 16. [Web]A00-20200716001 修正從TT傳回來的表單有欄位為千分位，儲存表單後數字會不正確
- **Commit ID**: `9733db082b3946b115f07f6cfbe8023ce37e8563`
- **作者**: 林致帆
- **日期**: 2020-07-20 15:43:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 17. [表單設計師]S00-20170531001 在RWD表單設計師中，複合元件的開窗類型為"部門"、"專案"或"群組"時，預設勾選"前置組織代號"
- **Commit ID**: `c21ce1f7a06ac8559e3245304868dc33e97fb842`
- **作者**: yanann_chen
- **日期**: 2020-07-20 14:30:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 18. [Web]C01-20200716001 修正資料庫為Oracle時，設一般使用者為某流程的負責人，當使用者進入監控流程頁面中左側顯示的筆數異常
- **Commit ID**: `6ad325b84536688befe92863802bf4612c28cf25`
- **作者**: 王鵬程
- **日期**: 2020-07-17 16:27:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 19. [Web]A00-20200715002 修正表單欄位有設定運算規則，當其中某個欄位有負數，造成js報錯
- **Commit ID**: `c0e99c213e8b65004d94b121dd708681cd6cbe17`
- **作者**: 林致帆
- **日期**: 2020-07-16 17:54:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 20. [Web]A00-20200710001修正RWD響應式中的Radio元件屬性設定「最後一個選項額外產生輸入框」的功能，在流程執行到第三關時，該TextBox欄位內容消失
- **Commit ID**: `846a9b0c7f12f614049ff6da0afa6e35c4ec6d0d`
- **作者**: 林致帆
- **日期**: 2020-07-14 18:15:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 21. [流程引擎]A00-20200512001 優化刪除系統排程時，不須透過重啟Server的方式就能停止排程
- **Commit ID**: `b7b6ca1909a8cfea4769d0f6d31f8f778f0b44b1`
- **作者**: waynechang
- **日期**: 2020-07-13 17:17:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SystemScheduleAction.java`

### 22. [流程設計師]Q00-20200710003 修正流程設計師，當核決關卡設定參考活動為自行定義關卡後，簽入簽出後就無法重新開啟核決關卡
- **Commit ID**: `3e7191bec1bc45802d6e4dab29a0663b30ff996f`
- **作者**: waynechang
- **日期**: 2020-07-10 17:42:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/TransitionRestriction.java`

### 23. [流程引擎]A00-20200527001 修正核決關卡設定參考活動設定自行定義後，流程要派送到核決關卡會出現錯誤
- **Commit ID**: `ff9b806a6d7a35fddb79519659b838911d69d383`
- **作者**: waynechang
- **日期**: 2020-07-10 17:35:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 24. [表單設計師]C01-20200629004 產品開窗增加過濾條件可以使用組織代號和組織名稱，且開窗的條件也可選這兩個新增的條件
- **Commit ID**: `f7abf0e719ba2e4fbac965ff46334bf971d409b0`
- **作者**: 王鵬程
- **日期**: 2020-07-08 18:30:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`

### 25. [流程設計師]A00-20200324002 修正當流程未進版時，若更改核決權限關卡的Id，會造成核決關卡無法點開[補修正]
- **Commit ID**: `e94919a19e7fb3c9a6763df88421181280f07e9c`
- **作者**: 林致帆
- **日期**: 2020-07-08 14:06:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 26. [流程設計師]A00-20200324002 修正當流程未進版時，若更改核決權限關卡的Id，會造成核決關卡無法點開
- **Commit ID**: `0d0c0f426690b3a7810f45428cbcf56a09d4e037`
- **作者**: 林致帆
- **日期**: 2020-07-08 11:24:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 27. [BPM APP]Q00-20200601001 修正IMG應用角標不出現問題
- **Commit ID**: `f2a06ed6aa54d18249a62a1faa3bc969dd947a83`
- **作者**: pinchi_lin
- **日期**: 2020-07-07 19:39:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`

### 28. [流程引擎]A00-20200618001 修正從Mail待辦事項的連結進入時，如果該使用者待辦為0筆，開啟的畫面會是待辦清單的異常
- **Commit ID**: `19959150e748c1bd5a53cfb96b3f70838006e6e5`
- **作者**: 林致帆
- **日期**: 2020-07-07 18:09:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 29. [Web]Q00-20200707001 調整Backspace控制，讓OA使用的文件編輯套件可以使用
- **Commit ID**: `7c4a10c9d932902ffaff722f6d9f44d97ef6b1d7`
- **作者**: waynechang
- **日期**: 2020-07-07 16:06:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 30. [BPM APP]Q00-20200617003 修正行動端在使用表單函式庫的設定元件字體顏色功能沒有效果問題
- **Commit ID**: `b5520e728c4180520db81aa89c4ed44cf6714d84`
- **作者**: yamiyeh10
- **日期**: 2020-07-06 14:09:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 31. [流程引擎]Q00-20200702001 修正追蹤流程清單統計接口邏輯
- **Commit ID**: `7cd638d57624ba9cdac8b2c91dc32e95734be134`
- **作者**: yanann_chen
- **日期**: 2020-07-03 10:12:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 32. [Web]A00-20200617001修正流程關卡派送到下一關為多人處理關卡畫面時,TextArea元件為可編輯狀態
- **Commit ID**: `d4faa9d8fdd554a4340fe338b94368540f3f4c4f`
- **作者**: 林致帆
- **日期**: 2020-07-02 18:22:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`

### 33. [流程引擎]Q00-20200618001 修正問題: 追蹤流程清單api未回傳執行中的處理者
- **Commit ID**: `19c9b0e03affcdd3a357c3f5dabbe57e54431933`
- **作者**: yanann_chen
- **日期**: 2020-07-02 11:01:59
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`

### 34. [Web]A00-20200608001修正列出工作受託者清單在流程點擊派送後，若下一關無人處理，瀏覽器的開發者工具會出現gBpmList未經定義的錯誤
- **Commit ID**: `a6647b6fd30e34cfe51303bdce98051b095acd6b`
- **作者**: 林致帆
- **日期**: 2020-07-01 18:21:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`

### 35. [BPM APP]C01-20200624003調整產生直連表單表單畫面判斷邏輯
- **Commit ID**: `36a158cf3bd794cae6d3434799cf3263bf13d234`
- **作者**: 詩雅
- **日期**: 2020-06-30 14:50:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 36. [BPM APP]C01-20200624003調整鼎捷移動直連表單網址串上流程OID[補-還原程式邏輯]
- **Commit ID**: `fc009820cfa59422caf16f42f1dc15188ea27d74`
- **作者**: 詩雅
- **日期**: 2020-06-30 14:46:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 37. [BPM APP]C01-20200624003調整鼎捷移動直連表單網址串上流程OID
- **Commit ID**: `3efc96a8b6783c43f488032846f15b44805d8c46`
- **作者**: 詩雅
- **日期**: 2020-06-30 09:43:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 38. [Web]C01-20200618005 修正在系統設定中的password.policy.rule，輸入規則以外的值導致變更密碼頁面開啟會全白
- **Commit ID**: `82de0a3b13398181d0333e89a7146fc126c3bb0f`
- **作者**: 王鵬程
- **日期**: 2020-06-29 18:28:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 39. [Web]C01-*********** 修正企業流程監控系列功能流程統計的異常
- **Commit ID**: `c2974355bcf0820d55fd04a8bea88fadd5855736`
- **作者**: walter_wu
- **日期**: 2020-06-24 16:34:31
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BamManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java`

