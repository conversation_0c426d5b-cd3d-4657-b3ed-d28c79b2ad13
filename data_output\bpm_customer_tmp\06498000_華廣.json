{"company_id": "06498000", "company_name": "華廣", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06498000_華廣\\01客戶基本資料", "files": [{"filename": "連線資訊.txt", "raw_content": "連線方式：VPN + TeamViewer\r\nVPN 隨時可連，但若要連到主機上要請客戶開 TeamViewer\r\nVPN 軟體：Cisco AnyConnect Secure Mobility Client\r\n\r\n1.VPN連線方式\r\nVPN IP:************\r\ngroup請選擇:noncompany\r\n帳號：VPN-IT-SUPPORTER1\r\n密碼：TT2023-G123tt\r\n \r\n2. EFGP 連線方式 (使用TeamViewer連線連線前請告知我司，我司將提供連線密碼)\r\n測試機 IP 為*********** \r\n網頁：administrator / SAS-8O31O225\r\n\r\n正式機 IP 為***********     \r\n網頁：administrator / SE5-8o3E0zzS@Dm\r\n外網網址：https://easyflowgp.bionime.com:20103/NaNaWeb/\r\n\r\n轉檔主機IP為 ***********\r\n\r\n", "structured_data": {"連線方式": "VPN + TeamViewer", "vpn 軟體": "Cisco AnyConnect Secure Mobility Client", "username": "VPN-IT-SUPPORTER1", "password": "TT2023-G123tt", "網頁": "administrator / SE5-8o3E0zzS@Dm", "外網網址": "https://easyflowgp.bionime.com:20103/NaNaWeb/", "vpn ip": "************", "group請選擇": "noncompany", "外網網址：https": "//easyflowgp.bionime.com:20103/NaNaWeb/", "host": "************"}, "source_path": "C1.客戶維護相關\\06498000_華廣\\01客戶基本資料\\連線資訊.txt", "file_size": 630, "encoding_used": "UTF-8-SIG", "processed_at": "2025-08-26T10:46:24.814216"}], "total_files": 1, "processed_at": "2025-08-26T10:46:24.814224"}