#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客戶資料自動化擷取工具

功能說明：
1. 遞迴掃描 Windows 網路芳鄰中所有客戶的「01客戶基本資料」資料夾
2. 讀取 .txt 檔案，同時保存原始內容與結構化解析結果
3. 以公司為單位輸出為 JSON 檔案

作者：BPM Easy Tools
版本：1.0.0
日期：2025-08-15
"""

import os
import re
import json
import uuid
import logging
import chardet
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dotenv import load_dotenv

try:
    from smbprotocol.connection import Connection
    from smbprotocol.session import Session
    from smbprotocol.tree import TreeConnect
    from smbprotocol.open import (
        Open, CreateDisposition, FileAttributes, ImpersonationLevel,
        CreateOptions, DirectoryAccessMask, FilePipePrinterAccessMask
    )
    from smbprotocol.file_info import FileInformationClass
    SMB_AVAILABLE = True
except ImportError:
    SMB_AVAILABLE = False
    print("警告：smbprotocol 套件未安裝，請執行：pip install smbprotocol")


class CustomerDataFetcher:
    """客戶資料擷取器主類別"""
    
    def __init__(self, config_path: str = "config/.env"):
        """
        初始化客戶資料擷取器
        
        Args:
            config_path: 環境變數檔案路徑
        """
        # 載入環境變數
        load_dotenv(config_path)
        
        # SMB 連線設定
        self.smb_host = os.getenv('SMB_HOST', '************')
        self.smb_username = os.getenv('SMB_USERNAME', '')
        self.smb_password = os.getenv('SMB_PASSWORD', '')
        self.smb_domain = os.getenv('SMB_DOMAIN', '')
        self.smb_share_name = os.getenv('SMB_SHARE_NAME', '顧問推廣')
        self.smb_base_path = os.getenv('SMB_BASE_PATH', 'C1.客戶維護相關')
        
        # 掃描設定
        self.customer_data_folder = os.getenv('CUSTOMER_DATA_FOLDER', '01客戶基本資料')
        self.target_file_patterns = os.getenv('TARGET_FILE_PATTERNS', '*.txt').split(',')
        self.encoding_fallback = os.getenv('ENCODING_FALLBACK', 'cp950,utf-8,big5').split(',')
        
        # 輸出設定
        self.output_dir = Path("data_output/bpm_customer_tmp")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 設定日誌
        self._setup_logging()
        
        # SMB 連線物件
        self.connection = None
        self.session = None
        self.tree = None
        
    def _setup_logging(self):
        """設定日誌系統"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"customer_data_fetch_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def connect_smb(self) -> bool:
        """
        建立 SMB 連線
        
        Returns:
            bool: 連線是否成功
        """
        if not SMB_AVAILABLE:
            self.logger.error("smbprotocol 套件未安裝，無法建立 SMB 連線")
            return False
            
        try:
            self.logger.info(f"正在連線到 SMB 伺服器：{self.smb_host}")
            
            # 建立連線
            self.connection = Connection(uuid.uuid4(), self.smb_host, 445)
            self.connection.connect()
            
            # 建立會話
            self.session = Session(self.connection, self.smb_username, self.smb_password, self.smb_domain)
            self.session.connect()
            
            # 連接到共享資料夾
            self.tree = TreeConnect(self.session, f"\\\\{self.smb_host}\\{self.smb_share_name}")
            self.tree.connect()
            
            self.logger.info("SMB 連線建立成功")
            return True
            
        except Exception as e:
            self.logger.error(f"SMB 連線失敗：{str(e)}")
            return False
    
    def disconnect_smb(self):
        """關閉 SMB 連線"""
        try:
            if self.tree:
                self.tree.disconnect()
            if self.session:
                self.session.disconnect()
            if self.connection:
                self.connection.disconnect()
            self.logger.info("SMB 連線已關閉")
        except Exception as e:
            self.logger.warning(f"關閉 SMB 連線時發生錯誤：{str(e)}")
    
    def detect_encoding(self, file_content: bytes) -> str:
        """
        自動偵測檔案編碼
        
        Args:
            file_content: 檔案二進位內容
            
        Returns:
            str: 偵測到的編碼格式
        """
        # 使用 chardet 偵測
        detected = chardet.detect(file_content)
        if detected['confidence'] > 0.7:
            return detected['encoding']
        
        # 回退到預設編碼列表
        for encoding in self.encoding_fallback:
            try:
                file_content.decode(encoding.strip())
                return encoding.strip()
            except UnicodeDecodeError:
                continue
        
        # 最後回退到 utf-8 並忽略錯誤
        return 'utf-8'
    
    def read_file_content(self, file_path: str) -> Tuple[Optional[str], str]:
        """
        讀取檔案內容並自動處理編碼
        
        Args:
            file_path: SMB 檔案路徑
            
        Returns:
            Tuple[Optional[str], str]: (檔案內容, 使用的編碼)
        """
        try:
            # 開啟檔案
            file_open = Open(self.tree, file_path)
            file_open.create(
                impersonation_level=ImpersonationLevel.Impersonation,
                desired_access=FilePipePrinterAccessMask.GENERIC_READ,
                file_attributes=FileAttributes.FILE_ATTRIBUTE_NORMAL,
                share_access=0x00000001 | 0x00000002,  # FILE_SHARE_READ | FILE_SHARE_WRITE
                create_disposition=CreateDisposition.FILE_OPEN,
                create_options=CreateOptions.FILE_NON_DIRECTORY_FILE
            )
            
            # 讀取檔案內容
            file_content = file_open.read(0, 1024 * 1024)  # 最大讀取 1MB
            file_open.close()
            
            # 偵測編碼
            encoding = self.detect_encoding(file_content)
            
            # 解碼內容
            try:
                content = file_content.decode(encoding)
                self.logger.debug(f"成功讀取檔案 {file_path}，使用編碼：{encoding}")
                return content, encoding
            except UnicodeDecodeError:
                # 使用 utf-8 並忽略錯誤
                content = file_content.decode('utf-8', errors='ignore')
                self.logger.warning(f"檔案 {file_path} 編碼解析失敗，使用 utf-8 忽略錯誤模式")
                return content, 'utf-8-ignore'
                
        except Exception as e:
            self.logger.error(f"讀取檔案 {file_path} 失敗：{str(e)}")
            return None, 'error'

    def parse_structured_data(self, content: str, filename: str) -> Dict[str, Any]:
        """
        解析文字內容為結構化資料

        Args:
            content: 檔案文字內容
            filename: 檔案名稱（用於錯誤記錄）

        Returns:
            Dict[str, Any]: 結構化資料
        """
        structured_data = {}
        parsing_error = False

        try:
            # 常見的鍵值對模式
            patterns = [
                # 中文模式：主機位址：*************
                r'([^：\n]+)：([^\n]+)',
                # 英文模式：Host: *************
                r'([^:\n]+):([^\n]+)',
                # 等號模式：host=*************
                r'([^=\n]+)=([^\n]+)',
            ]

            # 標準欄位映射
            field_mapping = {
                # 主機相關
                '主機位址': 'host', '主機': 'host', 'host': 'host', 'server': 'host',
                'ip': 'host', 'ip位址': 'host', '伺服器': 'host',

                # 連接埠相關
                '連接埠': 'port', '埠號': 'port', 'port': 'port', '端口': 'port',

                # 使用者相關
                '使用者': 'username', '帳號': 'username', '用戶名': 'username',
                'username': 'username', 'user': 'username', 'account': 'username',

                # 密碼相關
                '密碼': 'password', 'password': 'password', 'pwd': 'password',

                # 資料庫相關
                '資料庫': 'database', '資料庫名稱': 'database', 'database': 'database',
                'db': 'database', 'dbname': 'database', '庫名': 'database',

                # 其他常見欄位
                '公司': 'company', '公司名稱': 'company', 'company': 'company',
                '備註': 'note', '說明': 'description', 'note': 'note',
                '版本': 'version', 'version': 'version',
            }

            # 嘗試各種模式解析
            for pattern in patterns:
                matches = re.findall(pattern, content, re.MULTILINE)
                for key, value in matches:
                    key = key.strip()
                    value = value.strip()

                    # 跳過空值
                    if not key or not value:
                        continue

                    # 映射到標準欄位名稱
                    standard_key = field_mapping.get(key.lower(), key.lower())
                    structured_data[standard_key] = value

            # 特殊處理：嘗試提取 IP 位址
            ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
            ip_matches = re.findall(ip_pattern, content)
            if ip_matches and 'host' not in structured_data:
                structured_data['host'] = ip_matches[0]

            # 特殊處理：嘗試提取連接埠
            port_pattern = r'\b(?:port|埠|端口)[：:=]\s*(\d+)\b'
            port_matches = re.findall(port_pattern, content, re.IGNORECASE)
            if port_matches and 'port' not in structured_data:
                structured_data['port'] = port_matches[0]

            # 檢查是否成功解析到有用資訊
            if not structured_data:
                parsing_error = True
                self.logger.warning(f"檔案 {filename} 無法解析出結構化資料")
            else:
                self.logger.info(f"檔案 {filename} 成功解析出 {len(structured_data)} 個欄位")

        except Exception as e:
            parsing_error = True
            self.logger.error(f"解析檔案 {filename} 時發生錯誤：{str(e)}")

        if parsing_error:
            structured_data['parsing_error'] = True

        return structured_data

    def is_example_folder(self, folder_name: str) -> bool:
        """
        檢查是否為範例資料夾

        Args:
            folder_name: 資料夾名稱

        Returns:
            bool: 是否為範例資料夾
        """
        example_folders = {
            '客服代號_客戶名稱(常用客戶名)',
            '客服代號_客戶名稱',
            '範例資料夾',
            'example',
            'sample',
            'template'
        }
        return folder_name in example_folders

    def is_special_logic_company(self, folder_name: str) -> bool:
        """
        檢查是否為特殊邏輯公司（不計入失敗統計）

        Args:
            folder_name: 資料夾名稱

        Returns:
            bool: 是否為特殊邏輯公司
        """
        # 特殊邏輯公司：聯廣和台北博報堂共用連線資訊
        special_companies = {
            '43103344_聯廣',
            '43104086_台北博報堂'
        }
        return folder_name in special_companies

    def should_exclude_from_statistics(self, folder_name: str) -> bool:
        """
        檢查是否應從統計中排除

        Args:
            folder_name: 資料夾名稱

        Returns:
            bool: 是否應排除
        """
        return self.is_example_folder(folder_name) or self.is_special_logic_company(folder_name)

    def extract_company_info(self, path: str) -> Tuple[Optional[str], Optional[str]]:
        """
        從路徑中提取公司資訊

        Args:
            path: 檔案路徑

        Returns:
            Tuple[Optional[str], Optional[str]]: (公司代碼, 公司名稱)
        """
        try:
            # 模式1：數字-公司名稱 (如：01148600-美德向邦)
            pattern1 = r'\\(\d+)-([^\\]+)\\'
            match1 = re.search(pattern1, path)

            if match1:
                company_id = match1.group(1)
                company_name = match1.group(2)
                return company_id, company_name

            # 模式2：數字_公司名稱 (如：06100204_恆勁)
            pattern2 = r'\\(\d+)_([^\\]+)\\'
            match2 = re.search(pattern2, path)

            if match2:
                company_id = match2.group(1)
                company_name = match2.group(2)
                return company_id, company_name

            # 模式3：純數字資料夾 (如：10100881)
            pattern3 = r'\\(\d{6,})\\'
            match3 = re.search(pattern3, path)

            if match3:
                company_id = match3.group(1)
                # 嘗試從路徑中找到公司名稱
                parts = path.split('\\')
                for part in parts:
                    if company_id in part:
                        if '-' in part:
                            company_name = part.split('-', 1)[1]
                            return company_id, company_name
                        elif '_' in part:
                            company_name = part.split('_', 1)[1]
                            return company_id, company_name
                return company_id, f"公司_{company_id}"

            # 模式4：特殊格式處理 (如：02100501_Focal)
            pattern4 = r'\\(\d{8})_([^\\]+)\\'
            match4 = re.search(pattern4, path)

            if match4:
                company_id = match4.group(1)
                company_name = match4.group(2)
                return company_id, company_name

            self.logger.warning(f"無法從路徑提取公司資訊：{path}")
            return None, None

        except Exception as e:
            self.logger.error(f"提取公司資訊時發生錯誤：{str(e)}")
            return None, None

    def list_directory(self, path: str) -> List[Dict[str, Any]]:
        """
        列出目錄內容

        Args:
            path: SMB 目錄路徑

        Returns:
            List[Dict[str, Any]]: 目錄項目列表
        """
        try:
            # 開啟目錄
            dir_open = Open(self.tree, path)
            dir_open.create(
                impersonation_level=ImpersonationLevel.Impersonation,
                desired_access=DirectoryAccessMask.GENERIC_READ,
                file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY,
                share_access=0x00000001 | 0x00000002,  # FILE_SHARE_READ | FILE_SHARE_WRITE
                create_disposition=CreateDisposition.FILE_OPEN,
                create_options=CreateOptions.FILE_DIRECTORY_FILE
            )

            # 查詢目錄內容
            query_result = dir_open.query_directory(
                "*",
                FileInformationClass.FILE_DIRECTORY_INFORMATION
            )

            dir_open.close()

            items = []
            for item in query_result:
                # 解碼檔案名稱
                file_name = item['file_name'].get_value().decode('utf-16le').rstrip('\x00')

                if file_name in ['.', '..']:
                    continue

                items.append({
                    'name': file_name,
                    'is_directory': bool(item['file_attributes'].get_value() & FileAttributes.FILE_ATTRIBUTE_DIRECTORY),
                    'size': item['end_of_file'].get_value(),
                    'path': f"{path}\\{file_name}"
                })

            return items

        except Exception as e:
            self.logger.error(f"列出目錄 {path} 失敗：{str(e)}")
            return []

    def find_customer_data_folders(self, base_path: str = None) -> List[str]:
        """
        遞迴搜尋所有客戶基本資料資料夾

        Args:
            base_path: 搜尋起始路徑，預設為設定的基礎路徑

        Returns:
            List[str]: 找到的客戶基本資料資料夾路徑列表
        """
        if base_path is None:
            base_path = self.smb_base_path

        customer_folders = []

        def recursive_search(current_path: str, depth: int = 0):
            """遞迴搜尋函式"""
            if depth > 10:  # 防止無限遞迴
                return

            try:
                items = self.list_directory(current_path)

                for item in items:
                    if item['is_directory']:
                        # 檢查是否為目標資料夾
                        if item['name'] == self.customer_data_folder:
                            customer_folders.append(item['path'])
                            self.logger.info(f"找到客戶資料夾：{item['path']}")
                        else:
                            # 繼續遞迴搜尋
                            recursive_search(item['path'], depth + 1)

            except Exception as e:
                self.logger.warning(f"搜尋目錄 {current_path} 時發生錯誤：{str(e)}")

        self.logger.info(f"開始遞迴搜尋客戶資料夾，起始路徑：{base_path}")
        recursive_search(base_path)
        self.logger.info(f"搜尋完成，共找到 {len(customer_folders)} 個客戶資料夾")

        return customer_folders

    def process_customer_folder(self, folder_path: str) -> Optional[Dict[str, Any]]:
        """
        處理單一客戶資料夾

        Args:
            folder_path: 客戶資料夾路徑

        Returns:
            Optional[Dict[str, Any]]: 處理結果，包含公司資訊和檔案資料
        """
        try:
            # 檢查是否為範例資料夾或特殊邏輯公司
            folder_name = folder_path.split('\\')[-2] if '\\' in folder_path else folder_path
            if self.is_example_folder(folder_name):
                self.logger.info(f"跳過範例資料夾：{folder_path}")
                return None
            elif self.is_special_logic_company(folder_name):
                self.logger.info(f"跳過特殊邏輯公司（共用連線資訊）：{folder_path}")
                return None

            # 提取公司資訊
            company_id, company_name = self.extract_company_info(folder_path)
            if not company_id or not company_name:
                self.logger.warning(f"無法提取公司資訊，跳過資料夾：{folder_path}")
                return None

            # 列出資料夾中的檔案
            items = self.list_directory(folder_path)
            txt_files = [item for item in items if not item['is_directory'] and item['name'].lower().endswith('.txt')]

            if not txt_files:
                self.logger.info(f"資料夾 {folder_path} 中沒有找到 .txt 檔案")
                return None

            # 處理每個 .txt 檔案
            files_data = []
            for file_item in txt_files:
                self.logger.info(f"處理檔案：{file_item['name']}")

                # 讀取檔案內容
                content, encoding = self.read_file_content(file_item['path'])
                if content is None:
                    continue

                # 解析結構化資料
                structured_data = self.parse_structured_data(content, file_item['name'])

                # 組裝檔案資料
                file_data = {
                    'filename': file_item['name'],
                    'raw_content': content,
                    'structured_data': structured_data,
                    'source_path': file_item['path'],
                    'file_size': file_item['size'],
                    'encoding_used': encoding,
                    'processed_at': datetime.now().isoformat()
                }

                files_data.append(file_data)

            if not files_data:
                self.logger.warning(f"資料夾 {folder_path} 中沒有成功處理的檔案")
                return None

            # 組裝公司資料
            company_data = {
                'company_id': company_id,
                'company_name': company_name,
                'data_source': self.customer_data_folder,
                'folder_path': folder_path,
                'files': files_data,
                'total_files': len(files_data),
                'processed_at': datetime.now().isoformat()
            }

            self.logger.info(f"成功處理公司 {company_name}({company_id})，共 {len(files_data)} 個檔案")
            return company_data

        except Exception as e:
            self.logger.error(f"處理客戶資料夾 {folder_path} 時發生錯誤：{str(e)}")
            return None

    def save_company_data(self, company_data: Dict[str, Any]) -> bool:
        """
        儲存公司資料為 JSON 檔案

        Args:
            company_data: 公司資料字典

        Returns:
            bool: 儲存是否成功
        """
        try:
            company_id = company_data['company_id']
            company_name = company_data['company_name']

            # 清理檔案名稱中的特殊字元
            safe_company_name = re.sub(r'[<>:"/\\|?*]', '_', company_name)
            filename = f"{company_id}_{safe_company_name}.json"
            filepath = self.output_dir / filename

            # 儲存 JSON 檔案
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(company_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"成功儲存公司資料：{filepath}")
            return True

        except Exception as e:
            self.logger.error(f"儲存公司資料時發生錯誤：{str(e)}")
            return False

    def run(self) -> Dict[str, Any]:
        """
        執行完整的客戶資料擷取流程

        Returns:
            Dict[str, Any]: 執行結果統計
        """
        start_time = datetime.now()
        self.logger.info("=== 開始執行客戶資料擷取 ===")

        # 檢查必要套件
        if not SMB_AVAILABLE:
            self.logger.error("smbprotocol 套件未安裝，請執行：pip install smbprotocol")
            return {'success': False, 'error': 'smbprotocol 套件未安裝'}

        # 檢查設定
        if not self.smb_username or not self.smb_password:
            self.logger.error("SMB 認證資訊未設定，請檢查 .env 檔案")
            return {'success': False, 'error': 'SMB 認證資訊未設定'}

        try:
            # 建立 SMB 連線
            if not self.connect_smb():
                return {'success': False, 'error': 'SMB 連線失敗'}

            # 搜尋客戶資料夾
            customer_folders = self.find_customer_data_folders()
            if not customer_folders:
                self.logger.warning("未找到任何客戶資料夾")
                return {'success': True, 'processed_companies': 0, 'message': '未找到客戶資料夾'}

            # 處理每個客戶資料夾
            processed_companies = 0
            failed_companies = 0

            for folder_path in customer_folders:
                self.logger.info(f"處理客戶資料夾：{folder_path}")

                company_data = self.process_customer_folder(folder_path)
                if company_data:
                    if self.save_company_data(company_data):
                        processed_companies += 1
                    else:
                        failed_companies += 1
                else:
                    failed_companies += 1

            # 計算執行時間
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            # 產生執行報告
            result = {
                'success': True,
                'total_folders_found': len(customer_folders),
                'processed_companies': processed_companies,
                'failed_companies': failed_companies,
                'execution_time_seconds': execution_time,
                'output_directory': str(self.output_dir),
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }

            self.logger.info(f"=== 執行完成 ===")
            self.logger.info(f"找到客戶資料夾：{len(customer_folders)} 個")
            self.logger.info(f"成功處理公司：{processed_companies} 家")
            self.logger.info(f"處理失敗：{failed_companies} 家")
            self.logger.info(f"執行時間：{execution_time:.2f} 秒")
            self.logger.info(f"輸出目錄：{self.output_dir}")

            return result

        except Exception as e:
            self.logger.error(f"執行過程中發生錯誤：{str(e)}")
            return {'success': False, 'error': str(e)}

        finally:
            # 關閉 SMB 連線
            self.disconnect_smb()


class FailedReportGenerator:
    """失敗報告產生器"""

    def __init__(self):
        """初始化失敗報告產生器"""
        self.output_dir = Path("data_output")
        self.output_dir.mkdir(exist_ok=True)

    def is_example_folder(self, folder_name: str) -> bool:
        """
        檢查是否為範例資料夾

        Args:
            folder_name: 資料夾名稱

        Returns:
            bool: 是否為範例資料夾
        """
        example_folders = {
            '客服代號_客戶名稱(常用客戶名)',
            '客服代號_客戶名稱',
            '範例資料夾',
            'example',
            'sample',
            'template'
        }
        return folder_name in example_folders

    def is_special_logic_company(self, folder_name: str) -> bool:
        """
        檢查是否為特殊邏輯公司（不計入失敗統計）

        Args:
            folder_name: 資料夾名稱

        Returns:
            bool: 是否為特殊邏輯公司
        """
        # 特殊邏輯公司：聯廣和台北博報堂共用連線資訊
        special_companies = {
            '43103344_聯廣',
            '43104086_台北博報堂'
        }
        return folder_name in special_companies

    def should_exclude_from_statistics(self, folder_name: str) -> bool:
        """
        檢查是否應從統計中排除

        Args:
            folder_name: 資料夾名稱

        Returns:
            bool: 是否應排除
        """
        return self.is_example_folder(folder_name) or self.is_special_logic_company(folder_name)

    def extract_company_info(self, folder_name: str) -> Tuple[str, str, str]:
        """
        從資料夾名稱提取公司資訊

        Args:
            folder_name: 資料夾名稱

        Returns:
            Tuple[str, str, str]: (公司代碼, 公司名稱, 格式類型)
        """
        # 模式1：數字-公司名稱
        pattern1 = r'^(\d+)-(.+)$'
        match1 = re.match(pattern1, folder_name)
        if match1:
            return match1.group(1), match1.group(2), "數字-公司名稱"

        # 模式2：數字_公司名稱
        pattern2 = r'^(\d+)_(.+)$'
        match2 = re.match(pattern2, folder_name)
        if match2:
            return match2.group(1), match2.group(2), "數字_公司名稱"

        # 模式3：純數字
        pattern3 = r'^(\d{6,})$'
        match3 = re.match(pattern3, folder_name)
        if match3:
            return match3.group(1), f"公司_{match3.group(1)}", "純數字"

        # 無法解析
        return "未知", folder_name, "無法解析"

    def extract_failed_companies(self, content: str) -> List[Dict[str, Any]]:
        """
        提取失敗的公司清單

        Args:
            content: 日誌檔案內容

        Returns:
            List[Dict[str, Any]]: 失敗公司清單
        """
        failed_list = []

        # 定義要過濾的範例資料夾名稱
        example_folders = {
            '客服代號_客戶名稱(常用客戶名)',
            '客服代號_客戶名稱',
            '範例資料夾',
            'example',
            'sample',
            'template'
        }

        # 1. 路徑格式無法解析的客戶
        pattern1 = r'WARNING - 無法從路徑提取公司資訊：C1\.客戶維護相關\\([^\\]+)\\'
        matches1 = re.findall(pattern1, content)
        for match in matches1:
            # 過濾範例資料夾和特殊邏輯公司
            if self.should_exclude_from_statistics(match):
                continue

            company_id, company_name, format_type = self.extract_company_info(match)
            failed_list.append({
                'folder_name': match,
                'company_id': company_id,
                'company_name': company_name,
                'format_type': format_type,
                'failure_reason': '路徑格式無法解析'
            })

        # 2. 沒有成功處理檔案的客戶
        pattern2 = r'WARNING - 資料夾 C1\.客戶維護相關\\([^\\]+)\\01客戶基本資料 中沒有成功處理的檔案'
        matches2 = re.findall(pattern2, content)
        for match in matches2:
            # 過濾範例資料夾和特殊邏輯公司
            if self.should_exclude_from_statistics(match):
                continue

            company_id, company_name, format_type = self.extract_company_info(match)
            failed_list.append({
                'folder_name': match,
                'company_id': company_id,
                'company_name': company_name,
                'format_type': format_type,
                'failure_reason': '沒有可處理的檔案'
            })

        # 3. 檔案讀取錯誤
        pattern3 = r'ERROR - 讀取檔案 C1\.客戶維護相關\\([^\\]+)\\01客戶基本資料\\'
        matches3 = re.findall(pattern3, content)
        for match in matches3:
            # 過濾範例資料夾和特殊邏輯公司
            if self.should_exclude_from_statistics(match):
                continue

            company_id, company_name, format_type = self.extract_company_info(match)
            failed_list.append({
                'folder_name': match,
                'company_id': company_id,
                'company_name': company_name,
                'format_type': format_type,
                'failure_reason': '檔案讀取錯誤'
            })

        # 4. 沒有找到txt檔案
        pattern4 = r'資料夾 C1\.客戶維護相關\\([^\\]+)\\01客戶基本資料 中沒有找到 \.txt 檔案'
        matches4 = re.findall(pattern4, content)
        for match in matches4:
            # 過濾範例資料夾和特殊邏輯公司
            if self.should_exclude_from_statistics(match):
                continue

            company_id, company_name, format_type = self.extract_company_info(match)
            failed_list.append({
                'folder_name': match,
                'company_id': company_id,
                'company_name': company_name,
                'format_type': format_type,
                'failure_reason': '資料夾中沒有找到.txt檔案'
            })

        # 去重複（按資料夾名稱）
        seen = set()
        unique_failed = []
        for company in failed_list:
            if company['folder_name'] not in seen:
                seen.add(company['folder_name'])
                unique_failed.append(company)

        # 按公司代碼排序
        unique_failed.sort(key=lambda x: x['company_id'])

        return unique_failed

    def generate_failed_report(self, log_file_path: Path = None) -> bool:
        """
        產生失敗公司清單報告

        Args:
            log_file_path: 指定的日誌檔案路徑，若為 None 則使用最新的日誌檔案

        Returns:
            bool: 報告產生是否成功
        """
        try:
            print("🔍 分析客戶資料擷取失敗案例")
            print("=" * 50)

            # 尋找日誌檔案
            if log_file_path is None:
                log_dir = Path("logs")
                log_files = list(log_dir.glob("customer_data_fetch_*.log"))

                if not log_files:
                    print("❌ 找不到日誌檔案")
                    return False

                # 使用最新的日誌檔案
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            else:
                latest_log = log_file_path

            print(f"📄 分析日誌檔案：{latest_log}")

            # 讀取日誌內容
            with open(latest_log, "r", encoding="utf-8") as f:
                content = f.read()

            # 提取統計資訊
            stats_pattern = r"找到客戶資料夾：(\d+) 個.*?成功處理公司：(\d+) 家.*?處理失敗：(\d+) 家"
            stats_match = re.search(stats_pattern, content, re.DOTALL)

            if stats_match:
                total_folders = int(stats_match.group(1))
                processed_companies = int(stats_match.group(2))
                failed_companies = int(stats_match.group(3))
            else:
                print("❌ 無法從日誌中提取統計資訊")
                total_folders = processed_companies = failed_companies = 0

            # 提取失敗的公司詳細資訊
            failed_companies_list = self.extract_failed_companies(content)

            # 計算實際失敗數量（排除特殊邏輯公司和範例資料夾）
            actual_failed = len(failed_companies_list)

            # 產生報告檔案名稱
            report_filename = f"處理失敗的{actual_failed}家公司清單.md"
            report_path = self.output_dir / report_filename

            # 處理特殊情況：聯廣和台北博報堂使用同一組連線資訊
            processed_companies_list = []
            for company in failed_companies_list:
                if company['company_id'] == '43104086' and company['company_name'] == '台北博報堂':
                    # 台北博報堂改為使用聯廣的連線資訊
                    company['special_note'] = '✅ 使用聯廣(43103344)的連線資訊'
                    company['reference_path'] = '\\\\************\\顧問推廣\\C1.客戶維護相關\\43103344_聯廣\\01客戶基本資料'
                    company['json_file'] = 'data_output\\bpm_customer\\43103344_聯廣.json'
                processed_companies_list.append(company)

            # 按失敗原因分組
            reason_groups = {}
            for company in processed_companies_list:
                reason = company['failure_reason']
                if reason not in reason_groups:
                    reason_groups[reason] = []
                reason_groups[reason].append(company)

            # 產生報告內容
            report_content = self._generate_report_content(
                total_folders, processed_companies, actual_failed,
                failed_companies_list, processed_companies_list, reason_groups
            )

            # 儲存報告
            with open(report_path, "w", encoding="utf-8") as f:
                f.write(report_content)

            # 顯示結果
            print(f"\n📊 分析結果：")
            print(f"  📁 總掃描資料夾：{total_folders} 個")
            print(f"  ✅ 成功處理公司：{processed_companies} 家")
            print(f"  ❌ 處理失敗：{actual_failed} 家")
            if total_folders > 0:
                print(f"  🎯 成功率：{processed_companies/total_folders*100:.1f}%")
            print(f"\n💾 失敗公司清單已產生：{report_path}")

            return True

        except Exception as e:
            print(f"❌ 產生失敗報告時發生錯誤：{str(e)}")
            return False

    def _generate_report_content(self, total_folders: int, processed_companies: int,
                               actual_failed: int, failed_companies_list: List[Dict[str, Any]],
                               processed_companies_list: List[Dict[str, Any]],
                               reason_groups: Dict[str, List[Dict[str, Any]]]) -> str:
        """
        產生報告內容

        Args:
            total_folders: 總資料夾數
            processed_companies: 成功處理公司數
            actual_failed: 實際失敗數
            failed_companies_list: 失敗公司清單
            processed_companies_list: 處理後的公司清單
            reason_groups: 按失敗原因分組的資料

        Returns:
            str: 報告內容
        """
        # 計算排除的特殊情況數量
        excluded_special = total_folders - processed_companies - actual_failed

        report_content = f"""# 處理失敗的{actual_failed}家公司清單

**產生時間：** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**資料來源：** 客戶資料自動化擷取工具分析結果

---

## 📊 統計摘要

- **總掃描客戶資料夾：** {total_folders} 個
- **成功處理公司：** {processed_companies} 家 ({processed_companies/total_folders*100:.1f}%)
- **實際處理失敗：** {actual_failed} 家 ({actual_failed/total_folders*100:.1f}%)
- **特殊邏輯/範例資料夾：** {excluded_special} 個 (已排除統計)

---

## ❌ 處理失敗的公司詳細清單

"""

        # 按失敗原因分組顯示
        overall_index = 1
        for reason, companies in reason_groups.items():
            # 計算該原因的統計
            count = len(companies)
            percentage = (count / len(failed_companies_list)) * 100 if failed_companies_list else 0

            # 設定優先級圖標
            priority_icon = "🔴" if "讀取錯誤" in reason else "🟡" if "沒有可處理" in reason or "無法解析" in reason else "🟢"

            report_content += f"""### {priority_icon} {reason} ({count}家，{percentage:.1f}%)

| 序號 | 公司代碼 | 公司名稱 | 資料夾格式 | 完整路徑 | 特殊說明 |
|------|----------|----------|------------|----------|----------|
"""

            # 按公司代碼排序
            companies.sort(key=lambda x: x['company_id'])

            for company in companies:
                # 生成完整路徑
                full_path = f"\\\\************\\顧問推廣\\C1.客戶維護相關\\{company['folder_name']}\\01客戶基本資料"

                # 檢查是否有特殊說明
                special_note = company.get('special_note', '-')
                if 'reference_path' in company:
                    special_note += f"<br>📁 參考路徑：`{company['reference_path']}`"
                if 'json_file' in company:
                    special_note += f"<br>📄 JSON檔案：`{company['json_file']}`"

                report_content += f"| {overall_index} | `{company['company_id']}` | **{company['company_name']}** | {company['format_type']} | `{full_path}` | {special_note} |\n"
                overall_index += 1

            report_content += "\n"

        # 添加統計表格和處理指南
        report_content += self._generate_statistics_and_guide(
            failed_companies_list, processed_companies_list, total_folders, processed_companies
        )

        return report_content

    def _generate_statistics_and_guide(self, failed_companies_list: List[Dict[str, Any]],
                                     processed_companies_list: List[Dict[str, Any]],
                                     total_folders: int, processed_companies: int) -> str:
        """
        產生統計表格和處理指南

        Args:
            failed_companies_list: 失敗公司清單
            processed_companies_list: 處理後的公司清單
            total_folders: 總資料夾數
            processed_companies: 成功處理公司數

        Returns:
            str: 統計表格和處理指南內容
        """
        content = f"""---

## 📈 失敗原因統計與處理優先級

| 失敗原因 | 數量 | 比例 | 處理優先級 | 建議處理方式 |
|----------|------|------|------------|--------------|
"""

        # 定義處理優先級和建議
        priority_map = {
            '檔案讀取錯誤': ('🔴 高', '檢查檔案權限或檔案損壞'),
            '沒有可處理的檔案': ('🟡 中', '確認檔案是否存在或格式正確'),
            '資料夾中沒有找到.txt檔案': ('🟢 低', '聯絡客戶提供連線資訊檔案'),
            '路徑格式無法解析': ('🟡 中', '修正資料夾命名格式')
        }

        # 按數量排序統計
        reason_counts = {}
        for company in failed_companies_list:
            reason = company['failure_reason']
            reason_counts[reason] = reason_counts.get(reason, 0) + 1

        sorted_reasons = sorted(reason_counts.items(), key=lambda x: x[1], reverse=True)

        for reason, count in sorted_reasons:
            percentage = (count / len(failed_companies_list)) * 100 if failed_companies_list else 0
            priority, suggestion = priority_map.get(reason, ('🟢 低', '需要進一步分析'))
            content += f"| {reason} | {count}家 | {percentage:.1f}% | {priority} | {suggestion} |\n"

        content += f"""
---

## 🔧 快速處理指南

### 🔴 高優先級：檔案讀取錯誤
"""

        high_priority = [c for c in processed_companies_list if c['failure_reason'] == '檔案讀取錯誤']
        if high_priority:
            for company in high_priority:
                full_path = f"\\\\************\\顧問推廣\\C1.客戶維護相關\\{company['folder_name']}\\01客戶基本資料"
                content += f"- **{company['company_id']} {company['company_name']}**\n"
                content += f"  - 路徑：`{full_path}`\n"

                if 'special_note' in company:
                    content += f"  - 特殊說明：{company['special_note']}\n"
                    if 'reference_path' in company:
                        content += f"  - 📁 參考路徑：`{company['reference_path']}`\n"
                    if 'json_file' in company:
                        content += f"  - 📄 已擷取JSON：`{company['json_file']}`\n"

                content += f"  - 處理：檢查檔案權限和編碼格式\n\n"
        else:
            content += "✅ 目前沒有檔案讀取錯誤的案例\n\n"

        content += f"""### 🟡 中優先級：檔案格式或路徑問題
"""

        medium_priority = [c for c in processed_companies_list if c['failure_reason'] in ['沒有可處理的檔案', '路徑格式無法解析']]
        if medium_priority:
            for company in medium_priority:
                full_path = f"\\\\************\\顧問推廣\\C1.客戶維護相關\\{company['folder_name']}\\01客戶基本資料"
                content += f"- **{company['company_id']} {company['company_name']}** - {company['failure_reason']}\n"
                content += f"  - 路徑：`{full_path}`\n"
                if company['failure_reason'] == '沒有可處理的檔案':
                    content += f"  - 處理：檢查是否有其他格式檔案(.ini, .xlsx等)\n\n"
                else:
                    content += f"  - 處理：修正資料夾命名格式\n\n"
        else:
            content += "✅ 目前沒有中優先級問題\n\n"

        content += f"""### 🟢 低優先級：缺少連線資訊檔案
"""

        low_priority = [c for c in processed_companies_list if c['failure_reason'] == '資料夾中沒有找到.txt檔案']
        if low_priority:
            content += f"共 {len(low_priority)} 家公司需要業務部門聯絡提供連線資訊：\n\n"
            for company in low_priority:
                full_path = f"\\\\************\\顧問推廣\\C1.客戶維護相關\\{company['folder_name']}\\01客戶基本資料"
                content += f"- `{company['company_id']}` **{company['company_name']}** - `{full_path}`\n"
        else:
            content += "✅ 所有公司都有連線資訊檔案\n"

        content += f"""
---

## 📝 備註

1. **資料時效性：** 本清單基於 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 的掃描結果
2. **路徑說明：** 所有路徑都可以直接複製到檔案總管的位址列中開啟
3. **統計排除邏輯：**
   - **特殊邏輯公司：** 聯廣(43103344)和台北博報堂(43104086)共用連線資訊，不計入失敗統計
   - **範例資料夾：** 「客服代號_客戶名稱(常用客戶名)」等範例資料夾已自動排除
4. **技術日誌：** 詳細錯誤資訊請參考 `logs/` 目錄
5. **更新頻率：** 建議每週執行一次完整掃描
6. **成功率計算：** 成功率 = 成功處理公司數 ÷ 總掃描資料夾數 = {processed_companies/total_folders*100:.1f}%
7. **目標成功率：** 95% 以上（當前已達 {processed_companies/total_folders*100:.1f}%）
8. **實際需處理：** 本清單包含 {len(failed_companies_list)} 家真正需要處理的失敗公司
"""

        return content


def main():
    """主程式進入點"""
    import argparse

    parser = argparse.ArgumentParser(description='客戶資料自動化擷取工具')
    parser.add_argument('--report-only', action='store_true',
                       help='僅產生失敗報告，不執行資料擷取')
    parser.add_argument('--log-file', type=str,
                       help='指定要分析的日誌檔案路徑')

    args = parser.parse_args()

    print("🚀 客戶資料自動化擷取工具")
    print("=" * 50)

    try:
        if args.report_only:
            # 僅產生失敗報告
            report_generator = FailedReportGenerator()
            log_file = Path(args.log_file) if args.log_file else None
            success = report_generator.generate_failed_report(log_file)

            if success:
                print("\n✅ 失敗報告產生完成！")
            else:
                print("\n❌ 失敗報告產生失敗！")
        else:
            # 執行完整流程
            # 建立擷取器實例
            fetcher = CustomerDataFetcher()

            # 執行擷取流程
            result = fetcher.run()

            # 顯示結果
            if result['success']:
                print("\n✅ 資料擷取執行成功！")
                print(f"📁 找到客戶資料夾：{result.get('total_folders_found', 0)} 個")
                print(f"🏢 成功處理公司：{result.get('processed_companies', 0)} 家")
                if result.get('failed_companies', 0) > 0:
                    print(f"❌ 處理失敗：{result.get('failed_companies', 0)} 家")
                print(f"⏱️  執行時間：{result.get('execution_time_seconds', 0):.2f} 秒")
                print(f"📂 輸出目錄：{result.get('output_directory', '')}")

                # 自動產生失敗報告
                if result.get('failed_companies', 0) > 0:
                    print("\n🔍 正在產生失敗公司清單報告...")
                    report_generator = FailedReportGenerator()
                    if report_generator.generate_failed_report():
                        print("✅ 失敗報告已自動產生！")
                    else:
                        print("❌ 失敗報告產生失敗！")
            else:
                print(f"\n❌ 資料擷取執行失敗：{result.get('error', '未知錯誤')}")

    except KeyboardInterrupt:
        print("\n\n⚠️  使用者中斷執行")
    except Exception as e:
        print(f"\n❌ 程式執行錯誤：{str(e)}")


if __name__ == "__main__":
    main()
