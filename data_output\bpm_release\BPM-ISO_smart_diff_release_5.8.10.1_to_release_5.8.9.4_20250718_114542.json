{"比較資訊": {"專案ID": "BPM-ISO", "倉庫路徑": "D:\\IDEA_workspace\\BPM-ISO", "新分支": {"branch_name": "release_5.8.10.1", "date": "2024-03-26 16:59:30", "message": "[Secudocx] V00-20240326005 修正ISO攜出段，若為PDF加密時，會重複加密導致攜出失敗。", "author": "邱郁晏"}, "舊分支": {"branch_name": "release_5.8.9.4", "date": "2023-12-05 15:26:01", "message": "[附件擴充] 修正參數未開啟時判斷邏輯異常問題", "author": "邱郁晏"}, "比較時間": "2025-07-18 11:45:42", "新增commit數量": 43, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "a8f642bec90c0ec15cda49affb353862adbe05c2", "commit_訊息": "[Secudocx] V00-20240326005 修正ISO攜出段，若為PDF加密時，會重複加密導致攜出失敗。", "提交日期": "2024-03-26 16:59:30", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7de1e798272d6b7bd97fc0913ff7e943f4655f12", "commit_訊息": "[Secudocx] V00-20240326004 修正以柔ISO攜出段，批次下載沒有加密", "提交日期": "2024-03-26 16:36:43", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "31a7beb9494b67fcedc0699f2a120d3055936fff", "commit_訊息": "[Secudocx] V00-20240326003 新增以柔轉檔卡控副檔名", "提交日期": "2024-03-26 14:44:55", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "9661d83c57a798f2d82e515e0fce7844e67014a4", "commit_訊息": "[Secudocx] V00-20240326002 修正以柔加密，ISO攜出段無下載加密問題", "提交日期": "2024-03-26 13:55:27", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "713f2ea2e75c6696177c49a38d4ae47ad1d2949c", "commit_訊息": "[ISO] Q00-20240326001 修正刪除人員類別權限改為使用truncate", "提交日期": "2024-03-26 09:11:47", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOAuthorityDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "370c18a70267793cc06e0946ab7ccc394608d32f", "commit_訊息": "[ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題(補修正)", "提交日期": "2024-03-21 13:49:58", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/DocCategoryDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4abc2dff9f0ed6e2813ea784247e87fe55564820", "commit_訊息": "[ISO]V00-20240320001 修正ISO文件新增單，變更單儲存表單失敗", "提交日期": "2024-03-20 15:23:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreate.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOMod.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "35d76859462df74cd265866e56faa3483f3204e3", "commit_訊息": "[SecuDocx] 以柔整合調整寫法", "提交日期": "2024-03-19 18:26:14", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "929c87c4fa2aab34868f389f37b0db8e57e526ae", "commit_訊息": "[ISO]V00-20240319001 修正閱讀浮水印未輸入文字造成文字滿版效果報錯", "提交日期": "2024-03-19 11:33:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d24f22d2f412982d2563600d4fbdd0b62a4a510", "commit_訊息": "[ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題", "提交日期": "2024-03-19 11:22:06", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/DocCategoryDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08aa0d7a56c3b703b9f8b33baf1dd84150147dc6", "commit_訊息": "[ISO]V00-20240313003 修正文管，新增單生效日期有填寫的狀況，儲存時會報\"請填寫生效日期'", "提交日期": "2024-03-19 10:53:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreateManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70f7fc145a466834f47941b6f3d7a914169bc95b", "commit_訊息": "[ISO]V00-20240313003 修正文管，新增單生效日期有填寫的狀況，儲存時會報\"請填寫生效日期'", "提交日期": "2024-03-13 18:09:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreateManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee3df9ae7f88259cf42c50c9beeba8e0466843c2", "commit_訊息": "[ISO]Q00-20240311001 調整ISO變更單的描述欄位，當描述欄位(TextArea_DocAbstract)有換行符號時<br\\>,畫面需以實際換行呈現", "提交日期": "2024-03-11 16:24:21", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOMod.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "48615812a98125683692cc8b73d672ee12fb41ac", "commit_訊息": "[SecuDocx] ISO段發布檔下載(補)", "提交日期": "2024-03-08 08:41:26", "作者": "raven.917", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/PDFWebView/web/BPMviewer.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29742eebdad44c2ff0ad7f01b04c7d07713aeb62", "commit_訊息": "[ISO]S00-20230703001 ISO閱讀文件支持同時開啟多份閱讀文件[補]", "提交日期": "2024-03-07 15:01:53", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ec31cc412c774905e81f86dc1b0f139981c3f240", "commit_訊息": "[ISO]S00-20230703001 ISO閱讀文件支持同時開啟多份閱讀文件", "提交日期": "2024-03-06 14:49:11", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/PDFWebView/web/BPMviewer.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "adfbd47ad48cf7328a6cb99362599a9606f670f4", "commit_訊息": "[ISO]S00-20231006002 修正文件類別因群組資料異常造成開啟失敗", "提交日期": "2024-03-06 11:33:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/DocCategoryController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb00f38b19a1f5c1ae3176b2f07706b50db5d20e", "commit_訊息": "Merge branch 'DS'", "提交日期": "2024-03-05 15:26:13", "作者": "邱郁晏", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7a6cf538e0b49eaa208387593f268d8ea2151b68", "commit_訊息": "[SecuDox] 替換nana-services.jar", "提交日期": "2024-03-05 15:25:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c0143e73374ebc2e7e9d1bfd7c885187f01649cf", "commit_訊息": "[SecuDox] 新增ISO段下載發布檔", "提交日期": "2024-02-20 17:29:29", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/PDFWebView/web/BPMviewer.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/PDFWebView/web/BPMviewer.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "302c6a034d499f53672064bcd73a687d9bf37514", "commit_訊息": "[ISO]S00-20230612002 文管首頁新增匯出Excel功能[補修正]", "提交日期": "2024-02-19 13:43:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df68ada3bfdf83bc0ec05bf89b65f6b849dbf3ad", "commit_訊息": "[ISO]S00-20230612002 文管首頁新增匯出Excel功能[補修正]", "提交日期": "2024-02-16 10:36:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f86db0b6b0baaea975bd372aff703dfa8d7f6f3", "commit_訊息": "[ISO]S00-20230612002 文管首頁新增匯出Excel功能", "提交日期": "2024-02-16 10:26:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ManageDocumentMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2d851a922bcdb87030ad2e4e07c86c5957c6fb1f", "commit_訊息": "[ISO]Q00-20231031002 修正ISO文件的制定單位或保管單位為群組時，組通知信件時會發生錯誤，導致生失效通知信無法寄送的異常[補]", "提交日期": "2024-01-31 16:48:07", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocMailMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2569bfee0bb0718622590d3741da0b0844306a74", "commit_訊息": "[ISO] V00-20240111001 修正PDF預覽浮水印缺少WordStyleValue屬性問題(補)", "提交日期": "2024-01-29 17:49:38", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a95331a669a72cdae256c1dbc1b7ecfd42bfb73", "commit_訊息": "[ISO]Q00-20240124002 調整ISO閱讀PDF檔案的頁面的下載原始檔的URL網址由內網IP改為文件主機設定的webAddress，避免瀏覽器的同源政策導致檔案無法下載", "提交日期": "2024-01-24 15:41:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/PDFWebView/web/BPMviewer.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a23627a5fa08e3f871419d5f35047a03ab30a689", "commit_訊息": "[ISO]S00-20230725001 閱讀浮水印新增\"旋轉角度\"設定", "提交日期": "2024-01-23 13:38:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f005458e4453751882e6fccbb0f37b8b53e1a1d", "commit_訊息": "Revert \"[ISO]S00-20230627002 \"ISO部門資訊批次更新\"表單調整原部門可選取到失效部門\"", "提交日期": "2024-01-18 18:03:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISODeptBatchUpdate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "80e8210e0781b5d4fb87e7285d38e92daa383a54", "commit_訊息": "[ISO] V00-20240111001 修正PDF預覽浮水印缺少WordStyleValue屬性問題", "提交日期": "2024-01-11 15:09:12", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8021af9b450051899cae82de9e82274f91e30c5b", "commit_訊息": "[ISO]S00-20230602002 ISO文管首頁、ISO文件屬性管理增加「保管單位」的查詢條件", "提交日期": "2024-01-08 17:05:08", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOSearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/SearchCondiction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "defa4936dcc15f96e2525513d8f536ad6be24a42", "commit_訊息": "[ISO]S00-20230627002 \"ISO部門資訊批次更新\"表單調整原部門可選取到失效部門", "提交日期": "2024-01-08 15:26:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISODeptBatchUpdate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a2f55554399174997cde4ade2d477b9e26379748", "commit_訊息": "[ISO]A00-20240105001 修正文件類別管理，當類別權限包含專案時，若再次編輯權限儲存後，重新點擊類別會提示「取得文管權限失敗」的錯誤", "提交日期": "2024-01-08 11:44:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/DocCategoryController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e4107a47c16c93a4cd2e704367dd2a308b2974fb", "commit_訊息": "[ISO]Q00-20231228001 增加ISO評審服務任務的log", "提交日期": "2023-12-28 15:31:18", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISODocVettingRecordController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocVettingMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3a3c0a662c38274b501f337617a8072caaebb983", "commit_訊息": "[ISO]S00-20230725001 閱讀浮水印新增\"旋轉角度\"設定", "提交日期": "2023-12-28 14:53:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c2a25b9de8c1d5e34f522bfe0eaef632994fb384", "commit_訊息": "[ISO]Q00-20231220007 取消ISOFile的sourceFileOID的強關聯，避免NoCmDocument被移除時，關聯ISOFile時會報錯", "提交日期": "2023-12-20 16:16:45", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFile.hbm.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d80901b3b086affc3a9108ae10c4c9eb0ba01211", "commit_訊息": "[ISO]Q00-20231220006 調整ISO索引卡，當文件為第一版時，隱藏「變更原因」欄位，其餘版本應顯示「變更原因」欄位", "提交日期": "2023-12-20 16:07:23", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ReadDocumentInfo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/UpdateDocumentInfo.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4b57f547fc5ebb16675fe8be1ced7eb1b036b07b", "commit_訊息": "[ISO]Q00-20231218002 優化ISO-V8重新建立索引排程，增加相關log及非預期錯誤防呆", "提交日期": "2023-12-18 15:40:42", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "90b3dfae9723539dcbf3a34b078064e8ff5274b4", "commit_訊息": "[附件擴充] PDF轉檔機制調整", "提交日期": "2023-12-11 14:39:56", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "650e50494a1768fea0616ecc735aa230057d0c84", "commit_訊息": "[ISO] S00-20231026002 新增鼎新轉檔工具上傳PNG功能(補)", "提交日期": "2023-12-08 15:36:20", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "69d75e09b47d65fc95508663765854af2b2a19a8", "commit_訊息": "[ISO] S00-20231026002 新增鼎新轉檔工具上傳PNG功能", "提交日期": "2023-12-07 16:29:23", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOWatermarkPatternMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/util/ImageUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b4b0e116496389d03f31df18785f105f3075feb2", "commit_訊息": "[附件擴充] 修正參數未開啟時判斷邏輯異常問題", "提交日期": "2023-12-05 15:26:01", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "36a8a499fd06f4e70d49f0f07585b5727aea7117", "commit_訊息": "[ISO]A00-20231129001 修正使用者若組織資料包含專案時，在開啟ISO文管首頁或清單時，讀取會失敗的異常", "提交日期": "2023-11-29 17:52:16", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOAuthorityDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "83c47a37355b856b0df302306ff5039773662c50", "commit_訊息": "[ISO] Q00-20231124006 修正評審規則觸發變更單InputLabel_Author欄位空值問題", "提交日期": "2023-11-27 09:59:57", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocVettingMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}