# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.2.2
- **舊版本**: 5.6.2.1
- **生成時間**: 2025-07-28 18:21:44
- **新增 Commit 數量**: 158

## 變更摘要

### <PERSON>ne (22 commits)

- **2017-02-21 16:12:13**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-21 16:11:47**: 修正模擬使用者呼叫ESS logout 時，ESS無法應導致GP報錯
  - 變更檔案: 1 個
- **2017-02-21 15:07:22**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-21 15:06:49**: 修正模擬使用者登出時，當ESS站台無回應時導致GP報錯
  - 變更檔案: 1 個
- **2017-02-16 15:31:32**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-16 15:30:53**: 調整錯字
  - 變更檔案: 1 個
- **2017-02-15 14:28:12**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-15 14:26:00**: 重新merge版本
  - 變更檔案: 1 個
- **2017-02-15 10:59:40**: C01-20170203001 修正通知信夾帶附件總容量限制
  - 變更檔案: 1 個
- **2017-02-13 14:56:47**: 縮小版本資訊的開窗大小
  - 變更檔案: 1 個
- **2017-02-09 14:39:13**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-09 14:38:15**: C00-20170206001 修正app退回重辦後，關卡狀態變更為暫停中，流程也無法結案
  - 變更檔案: 1 個
- **2017-02-06 15:36:01**: 修正追蹤流程連結使用ldap驗證時，無法正常登入
  - 變更檔案: 1 個
- **2017-02-06 15:33:19**: A00-20170111001 修正T100變更通知信內容導致發單失敗
  - 變更檔案: 2 個
- **2017-01-06 15:28:53**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-01-06 15:28:12**: S00-20170105002 調整為顯示所有分類
  - 變更檔案: 1 個
- **2017-01-06 11:00:37**: Q00-20170105006  增加ISO dailyJob
  - 變更檔案: 1 個
- **2017-01-06 10:57:34**: 增加流程設計師的update SQL
  - 變更檔案: 2 個
- **2016-12-28 17:16:35**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-28 17:15:48**: S00-20161122003 調整匯入流程及新建流程的有效日期(2100/12/31)
  - 變更檔案: 2 個
- **2016-12-20 18:23:11**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-20 18:22:12**: 修正開啟追蹤流程-發起的流程SQL錯誤
  - 變更檔案: 1 個

### joseph (28 commits)

- **2017-02-21 15:38:48**: Q00-*********** 對拋出錯做小修改
  - 變更檔案: 4 個
- **2017-02-21 15:01:18**: Q00-*********** 2次修正:阻擋使用者不登入及正常管道就下載ISO檔案
  - 變更檔案: 12 個
- **2017-02-15 14:20:24**: C01-*********** 修正: WorkflowServicefetchFullProcInstanceWithSerialNo 取得之簽核歷程無法區分公開轉寄或私人轉寄問題
  - 變更檔案: 2 個
- **2017-02-09 10:17:39**: Q00-*********** 阻擋使用者透過 ISO文件download連結 不透過登入 直接下載文件
  - 變更檔案: 2 個
- **2017-02-08 11:09:39**: Q00-20170124002 :2次修正Grid與TextArea Binding在TextArea內容有換行流程發起後 點選Grid欄位後TextArea內容會有<br />
  - 變更檔案: 1 個
- **2017-02-08 10:02:01**: C01-20170116001 :2次修正 控件会粘着鼠标移动，只能关闭设计师，重新开启
  - 變更檔案: 2 個
- **2017-02-08 09:58:43**: A00-20170124001 修正:在舊版的流程，管理员手动跳过关卡处理者，返回画面及流程图都报错
  - 變更檔案: 5 個
- **2017-02-03 11:53:21**: C01-20170116001 :修正 控件会粘着鼠标移动，只能关闭设计师，重新开启
  - 變更檔案: 1 個
- **2017-02-03 11:49:15**: Q00-20170124002 : 修正Grid與TextArea Binding在TextArea內容有換行流程發起後 點選Grid欄位後TextArea內容會有<br />
  - 變更檔案: 1 個
- **2017-02-03 11:47:46**: Q00-20170120003 : 修正Grid 操作：新增 然後 刪除 送出流程 會留下 ] 符號
  - 變更檔案: 1 個
- **2017-02-03 11:41:07**: A00-20170112001:WEB設計師的預設值有輸入值，填單時，有輸入欄位按下儲存表單或儲存草稿後，於草稿區簽出此表單，欄位值會變成設定的預設，但是於於草稿畫面按下左上角的表單頁籤後，值又會跑回來
  - 變更檔案: 1 個
- **2017-01-20 16:49:00**: 修改Email連結 Link 登入語系  如果Session存在，取Session語系，不存在則取瀏覽器語系
  - 變更檔案: 1 個
- **2017-01-20 16:48:19**: 修改Email link 登入語系 如果Session存在 取Session語系，不存在則取瀏覽器語系
  - 變更檔案: 1 個
- **2017-01-20 14:15:35**: C01-20161222003使用maillink進到簽核畫面後，但該表單先不進行簽核，使用待辦功能選取其他張表單進行簽核後，不是跳到下一筆表單，反而是跑出最上筆的表單(有設定簽核後至下一個工作項目(nextWorkItem))
  - 變更檔案: 1 個
- **2017-01-18 09:51:39**: A00-*********** 修正:關卡處理者為群組時，解析處理者失敗。
  - 變更檔案: 1 個
- **2017-01-18 09:36:28**: A00-*********** 修正:點入系統管理工具-SQL註冊器出現無法取得EJB服務DB為ORACLE
  - 變更檔案: 1 個
- **2017-01-13 16:59:52**: Q00-20170105003使用者從待辦事項中，點選一張單據，切換到流程圖頁簽，點選關卡的「發送通知」，成功後再切換回到表單內容頁簽時，會報錯。
  - 變更檔案: 2 個
- **2017-01-12 15:06:57**: S00-*********** 修正 mail link 進入BPM系統，預設語系選項與直接開啟browser進入BPM系統不一致；請設定為一致
  - 變更檔案: 1 個
- **2017-01-11 13:35:52**: Q00-20170110001 新增工作排程的清單畫面應該要顯示間隔時間
  - 變更檔案: 1 個
- **2017-01-06 16:27:44**: Q00-20170105010 修正:工作排程點選"立即執行"後，沒有跳出任何成功或完成的訊息，無法判別是否有作用
  - 變更檔案: 2 個
- **2017-01-06 16:22:19**: Q00-20170105008 & Q00-20170105009 修正追蹤流程>發起的流程或處理的流程>選擇三個月內>選擇"所有可追蹤的流程">選擇某個流程分類>報錯(此問題可於Emma機器重現)
  - 變更檔案: 1 個
- **2017-01-06 16:14:46**: Q00-20170105001 修正追蹤流程->任選目前處理者(進階)->選擇「已關閉的流程」或「所有流程」都無效
  - 變更檔案: 1 個
- **2017-01-06 16:12:49**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-01-06 16:11:53**: Q00-20170105001 修正追蹤流成->任選目前處理者(進階)->選擇「已關閉的流程」或「所有流程」都無效
  - 變更檔案: 1 個
- **2017-01-06 16:08:12**: Q00-20170105001 修正追蹤流程->任選目前處理者(進階)->選擇「已關閉的流程」或「所有流程」都無效
  - 變更檔案: 1 個
- **2017-01-05 14:00:49**: Q00-20161230001 修正組織設計師在進入修改員工資料，設定員工離職時間是未來時間，確認後再次開啟，未來離職時間會消失。
  - 變更檔案: 9 個
- **2017-01-05 13:47:02**: S00-20161228001 修正工作行事曆中，新增或修改適用日期及特殊工作日，如果是過去時間，無法變更。
  - 變更檔案: 7 個
- **2016-12-28 10:13:10**: S00-*********** 新增進入ESSPlus管理作業類程式，會將該程式的權限值，在XML組成傳遞過去的功能。
  - 變更檔案: 7 個

### 張容倫 (1 commits)

- **2017-02-21 15:18:37**: 無效文件存放區
  - 變更檔案: 1 個

### jerry1218 (18 commits)

- **2017-02-20 15:19:47**: 修正已發起的流程預測流程(instance) ,遇到解析類行為主管或代理人 , 會在後台出現exception , 導致該關卡無法解析(definition預測 , 程式部分簽入 , 但源頭mark不會呼叫)
  - 變更檔案: 6 個
- **2017-02-17 16:21:57**: 移除ProcessDispatcherBean開發階段多餘System.out
  - 變更檔案: 1 個
- **2017-02-17 16:09:35**: 移除BpmProcessInstanceSubTraceResult.jsp多餘alert
  - 變更檔案: 1 個
- **2017-02-16 16:27:45**: Q00-20170216001 修正[預測]關卡時,遇到服務任務會出現ClassCastException,流程圖開啟失敗
  - 變更檔案: 1 個
- **2017-02-16 14:30:46**: Q00-20170215003 因為T100整合透過流程樣板建立簽核流,目前Invoke會有異常,故先改回原本的(以結案事件呼叫)
  - 變更檔案: 1 個
- **2017-02-16 14:29:10**: 修正加簽的關卡於查看完整流程圖時大小與非加簽關卡不一致問題
  - 變更檔案: 1 個
- **2017-02-16 11:51:39**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-16 11:51:09**: 移除以前未刪除的開發段LOG
  - 變更檔案: 4 個
- **2017-02-15 15:53:21**: 移除BpmProcessWorkbox的System.out.println
  - 變更檔案: 1 個
- **2017-02-15 14:41:36**: 修正流程內容(已發起流程)橫向scrollbar未顯示問題
  - 變更檔案: 2 個
- **2017-02-15 12:02:27**: 修正BpmProcessInstanceTraceResult.jsp如果選擇顯示全流程,左上角會有一個小黑點 最後使用invoke修正BpmProcessInstanceTraceResult.jsp如果選擇顯示全流程,左上角會有一個小黑點 新增判斷預測後續關卡當中,檢核List是否已有相同的ActivityDefinition(for全流程預測)
  - 變更檔案: 2 個
- **2017-02-14 17:47:48**: 修改T100整合-流程樣板 , 將回寫事件改為最後使用invoke
  - 變更檔案: 1 個
- **2017-02-14 15:55:18**: 1.修正追蹤流程->子流程->返回主流程會出現錯誤問題 2.修正簽核時->流程內容如果選擇顯示[完整流程圖],左方zTreeDiv沒有把display設成none 3.修正預測流程時,預測的[第一階]關卡有多個的時候,只有第一階的第一個關卡會繼續往下預測問題 4.簡易流程圖關卡加上簽核意見 5.新增完整流程圖的流程狀態,如果是終止的流程加上終止人員
  - 變更檔案: 6 個
- **2017-02-13 11:49:55**: SOO-20170213001 再流程圖上方狀態新增已終止流程是由何人終止
  - 變更檔案: 4 個
- **2017-02-09 18:01:06**: S00-*********** - 流程內容預先解析功能 1.流程內容可依需求顯示[簡易流程圖]or[完整流程圖] 2.多分支流程人員預解析 3.修正Q單 Q00-*********** Q00-*********** Q00-*********** Q00-*********** Q00-***********
  - 變更檔案: 18 個
- **2017-01-04 10:59:40**: S00-20161121001 新功能需求-BPM服務調整(取代T100 awsi013),邏輯修改 1.支援預設流程,作業不分單別流程,作業不分據點流程,營運據點+作業代號+單別流程 (FormFlowCreate&FormFlowDelete) 2.取得流程清單 (ProcessListGet)為支援1之修改 3.FormFlowCreate服務所產生的流程名稱修改
  - 變更檔案: 5 個
- **2016-12-30 10:19:33**: A00-20161215002 修正1.並簽後流程圖無法開啟問題 , 2.將流程內容-檢視完整流程資訊的Route大小調小
  - 變更檔案: 2 個
- **2016-12-26 11:20:25**: T100新增支援送簽單據asft801,apst300   修改aint311
  - 變更檔案: 3 個

### ChinRong (1 commits)

- **2017-02-18 16:28:45**: 修正[陽明醫院]BPMAPP測試相關問題:進階查詢第一次查詢沒有動作，第二次查詢才有反應
  - 變更檔案: 2 個

### arielshih (8 commits)

- **2017-02-16 17:04:17**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-16 16:14:09**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-16 15:02:16**: S00-*********** 刪除舊版流程part4
  - 變更檔案: 31 個
- **2017-02-16 10:15:34**: S00-*********** 刪除舊版流程part3
  - 變更檔案: 396 個
- **2017-02-16 10:08:32**: S00-*********** 刪除舊版流程part2
  - 變更檔案: 236 個
- **2017-02-16 10:07:08**: S00-*********** 刪除舊版process流程
  - 變更檔案: 52 個
- **2017-02-08 15:16:14**: 二次修正舊有表單中元件cssStyle，原本是color:#;和background-color:#;轉型語法應使用nvarchar
  - 變更檔案: 1 個
- **2016-12-20 15:22:00**: TableSchema整理(From v5.6.2.1)
  - 變更檔案: 14 個

### LALA (29 commits)

- **2017-02-16 16:17:54**: 修正Update Oracle的sql指令，replace錯誤標籤與語系大小寫。
  - 變更檔案: 1 個
- **2017-02-10 10:29:34**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-10 10:28:08**: A00-***********[普萊德]修正請求無XMLFile的標籤導致開單失敗的問題。
  - 變更檔案: 1 個
- **2017-02-07 16:15:38**: C01-20161222001[紅崴]修正篩選的下拉式選單，在Win10 IE11操作發生異常。
  - 變更檔案: 1 個
- **2017-02-06 12:58:25**: C01-20161208001[時碩工業股]修正TT送簽的表單欄位資料有斷行，grid顯示異常的問題
  - 變更檔案: 2 個
- **2017-02-06 12:00:27**: Revert "C01-20161212001[艾沃意特]修正TT送簽的表單欄位資料有斷行，grid顯示異常的問題"
  - 變更檔案: 2 個
- **2017-02-06 11:58:59**: C01-20161212001[艾沃意特]修正TT送簽的表單欄位資料有斷行，grid顯示異常的問題
  - 變更檔案: 2 個
- **2017-02-03 15:23:27**: A00-20161228002[全一]修正表單formDispatchr()回傳false時表單呈現空白的問題。
  - 變更檔案: 2 個
- **2017-02-03 14:19:20**: A00-20161228002[全一]修正表單formDispatchr()回傳false時表單呈現空白的問題。
  - 變更檔案: 1 個
- **2017-02-02 11:19:51**: 新增5.6.2.2_updateSQL_Oracle.sql(修正C01-20170124002[瑞儀]修正跳關通知信有誤)
  - 變更檔案: 1 個
- **2017-01-23 18:29:21**: Q00-20170123002[內部]修正M-Cloud無法下載附件
  - 變更檔案: 1 個
- **2017-01-20 14:28:44**: Q00-20170120001[內部]待辦清單、待辦數目、通知數目效能調整。
  - 變更檔案: 2 個
- **2017-01-19 12:58:56**: A00-***********[欣興]修正設定排程每日執行且首次時間設定在中午12點，但該排程不正常執行的異常問題
  - 變更檔案: 4 個
- **2017-01-16 17:47:05**: C01-20161202002[建邦精密]修正企業流程監控頁面，日期控件中月份翻頁按鈕顯示異常。
  - 變更檔案: 13 個
- **2017-01-13 11:55:21**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-01-13 11:54:45**: C01-20170110001[誼山新]修正T100因通知信夾帶附件異常造成發起流程失敗
  - 變更檔案: 2 個
- **2017-01-11 16:06:10**: A00-20161220001、A00-20161220002[紅崴]修正篩選流程分類或流程名稱，資料筆數異常。
  - 變更檔案: 10 個
- **2017-01-09 17:39:12**: A00-20161122001[內部]修正從上下筆按鈕進入已接受的工作事項，會跳出無法接收此工作的失敗訊息
  - 變更檔案: 1 個
- **2017-01-06 16:35:22**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-01-06 16:34:41**: Q00-20170106002[內部]修正模組程式維護修改資料會造成連線URL增加多個amp;使得連結錯誤
  - 變更檔案: 1 個
- **2017-01-06 16:01:24**: Q00-***********[內部]修正簡易查詢搜尋編號為特殊符號(% 和 _)時會找不到文件
  - 變更檔案: 2 個
- **2017-01-06 12:04:00**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-01-06 11:32:22**: Q00-20170105004[內部]新版流程圖點重啟invoke關卡後異常
  - 變更檔案: 1 個
- **2017-01-05 17:11:12**: A00-20161228001[杰發]新關卡接舊有連接線造成流程異常。
  - 變更檔案: 1 個
- **2016-12-30 09:41:58**: C01-20161007001[台灣精星]修正ISO文件生失效造成類別樹異常
  - 變更檔案: 1 個
- **2016-12-26 17:13:13**: 修正merge錯誤
  - 變更檔案: 1 個
- **2016-12-26 17:11:37**: 修正merge錯誤
  - 變更檔案: 1 個
- **2016-12-26 17:08:38**: A00-20161215001[互動寬頻]因舊表單CSS屬性值有異常，版更5611表單無法正確載入
  - 變更檔案: 1 個
- **2016-12-26 11:35:04**: A00-20161006001[良維]原RCP指令有誤，造成TIPTOP夾有附件的單據送簽失敗，將俊宏哥修正程式簽入
  - 變更檔案: 1 個

### pinchi_lin (15 commits)

- **2017-02-14 16:47:52**: C00-20170206001 修正app退回重辦後，關卡狀態變更為暫停中，流程也無法結案
  - 變更檔案: 1 個
- **2017-02-14 16:47:30**: C00-20170206001 修正app退回重辦後，關卡狀態變更為暫停中，流程也無法結案
  - 變更檔案: 1 個
- **2017-02-14 16:46:30**: C00-20170206001 修正app退回重辦後，關卡狀態變更為暫停中，流程也無法結案
  - 變更檔案: 1 個
- **2017-02-14 16:38:54**: 修正BPMAPP通知列表中，搜尋圖示多一張問題
  - 變更檔案: 1 個
- **2017-01-10 09:41:45**: 修正BPMAPP中的產品與客製開窗，點擊畫面向左或向右滑會出現loading問題
  - 變更檔案: 2 個
- **2017-01-09 14:57:25**: APP新UI第二階段 待辦流程(類別與名稱)過濾顯示筆數-修正顯示筆數與多語系
  - 變更檔案: 2 個
- **2017-01-06 17:04:44**: 簡化微信歸戶流程-多語系修正
  - 變更檔案: 4 個
- **2017-01-06 16:33:47**: 簡化微信歸戶流程-修正立即同步BUG
  - 變更檔案: 1 個
- **2017-01-04 14:54:09**: APP新UI第二階段 待辦流程(類別與名稱)過濾顯示筆數
  - 變更檔案: 6 個
- **2017-01-03 10:10:11**: 修正BPMAPP，ESS表單的grid多一格欄位問題
  - 變更檔案: 1 個
- **2016-12-29 14:24:27**: BPMAPP新UI-附件下載列表輸出基本樣式
  - 變更檔案: 1 個
- **2016-12-28 17:56:51**: 修正ESS表單用APP開啟時，gird內欄位顯示問題
  - 變更檔案: 1 個
- **2016-12-27 14:01:28**: 簡化微信歸戶流程-修正查詢撈取資料方式與同步資訊顯示
  - 變更檔案: 7 個
- **2016-12-22 16:08:41**: 簡化微信歸戶流程-產生網址與連線驗證功能修改
  - 變更檔案: 2 個
- **2016-12-22 10:53:10**: 簡化微信歸戶流程功能
  - 變更檔案: 11 個

### Gaspard (10 commits)

- **2017-02-12 17:52:03**: 修正LABEL設為隱藏時，不會選取到該元件
  - 變更檔案: 2 個
- **2017-02-03 12:06:44**: 修正客製JSP無法使用SQL註冊器語法的議題
  - 變更檔案: 3 個
- **2017-02-03 11:57:08**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-01-20 18:17:42**: C01-20170119002 修正表單script清空的議題
  - 變更檔案: 5 個
- **2017-01-13 14:14:19**: 增加使用SQL註冊器時的query與update語法說明
  - 變更檔案: 1 個
- **2017-01-05 16:41:34**: 修正於客製JSP上無法使用客製開窗的問題
  - 變更檔案: 2 個
- **2016-12-23 16:29:34**: 使TextArea不可設計其欄位屬性，強制只能使用字串
  - 變更檔案: 1 個
- **2016-12-22 20:22:34**: 修正舊有表單中元件cssStyle可能是color:#;和background-color:#;
  - 變更檔案: 2 個
- **2016-12-21 10:09:45**: 修正表單定義中的元件在新表單設計師開啟時，某些元件會是黑底
  - 變更檔案: 2 個
- **2016-12-20 13:37:14**: 修正多選開窗情境，在已有資料的情況下開窗時會造成已選取的資料因為「排序」欄位而亂掉
  - 變更檔案: 1 個

### Joe (20 commits)

- **2017-02-10 17:58:56**: 新增多語系
  - 變更檔案: 1 個
- **2017-02-07 10:01:20**: 修改BPM APP部分UI
  - 變更檔案: 5 個
- **2017-01-13 11:47:31**: 微信歸戶優化:部分修改
  - 變更檔案: 4 個
- **2017-01-10 09:53:30**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-01-10 09:53:07**: BPM APP新UI第二階段:改善部分UI
  - 變更檔案: 10 個
- **2017-01-05 10:23:17**: APP新UI第二階段多語系敘述調整
  - 變更檔案: 1 個
- **2017-01-04 17:37:22**: BPM APP新UI第二階段 補上遺漏的追蹤流程的獨立顯示流程
  - 變更檔案: 1 個
- **2017-01-04 15:28:14**: 微信歸戶優化微調
  - 變更檔案: 5 個
- **2017-01-03 16:42:53**: APP新UI第二階段 待辦流程過濾新方法
  - 變更檔案: 3 個
- **2017-01-03 11:07:27**: BPM APP 新UI第二階段:顯示流程圖獨立並置中
  - 變更檔案: 4 個
- **2016-12-29 16:47:19**: BPM APP新UI 簽和歷程畫面修改
  - 變更檔案: 4 個
- **2016-12-29 14:44:49**: BPM APP 新UI 附件列表樣式修改
  - 變更檔案: 2 個
- **2016-12-29 13:44:01**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-12-29 10:24:43**: 微信歸戶優化 EXCEL開窗修改
  - 變更檔案: 4 個
- **2016-12-27 18:48:55**: 微信歸戶優化 新增多語系
  - 變更檔案: 4 個
- **2016-12-27 15:33:13**: 微信歸戶優化-MERGE CODE
  - 變更檔案: 4 個
- **2016-12-27 14:11:29**: 微信歸戶優化修改
  - 變更檔案: 2 個
- **2016-12-22 18:22:20**: 微信歸戶優化-部分BUG修正
  - 變更檔案: 4 個
- **2016-12-22 17:18:18**: 微信歸戶優化
  - 變更檔案: 1 個
- **2016-12-22 16:36:08**: 微信歸戶優化
  - 變更檔案: 5 個

### WenCheng (6 commits)

- **2017-02-08 18:15:38**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-08 18:14:59**: Q00-20170208001 「線上人數查詢」頁面，文字描述修正(e.g.各主機登入連線紀錄查詢)，以避免客戶誤解。
  - 變更檔案: 2 個
- **2017-01-23 11:31:06**: Q00-20170120002 修正Vip功能其驗證授權人數計算方式。
  - 變更檔案: 2 個
- **2017-01-20 15:11:17**: A00-20170120001 重新撰寫removeNotOnlineUser排程邏輯。
  - 變更檔案: 2 個
- **2017-01-11 15:12:15**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-01-11 15:11:29**: A00-20170111003 修正當客戶整合標準SP7產品時，預設為另開新視窗功能。
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a2b5e7f1363768d0c314fa36f4bc882a348a0ef1`
- **作者**: wayne
- **日期**: 2017-02-21 16:12:13
- **變更檔案數量**: 0

### 2. 修正模擬使用者呼叫ESS logout 時，ESS無法應導致GP報錯
- **Commit ID**: `efe8d2bc2ebbfdee381da6052f44e17425d0548c`
- **作者**: wayne
- **日期**: 2017-02-21 16:11:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`

### 3. Q00-*********** 對拋出錯做小修改
- **Commit ID**: `7b8a5473a4e9abf6191faba919194c56fe8cff75`
- **作者**: joseph
- **日期**: 2017-02-21 15:38:48
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ServerCacheManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java`

### 4. 無效文件存放區
- **Commit ID**: `d36e669056e5cb86745be1812fb42bd31ecaa8c5`
- **作者**: 張容倫
- **日期**: 2017-02-21 15:18:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `z.UnvalidFile/.gitkeep`

### 5. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `0570e18298b244309a6b2b6e486a72f9d9bf6c60`
- **作者**: wayne
- **日期**: 2017-02-21 15:07:22
- **變更檔案數量**: 0

### 6. 修正模擬使用者登出時，當ESS站台無回應時導致GP報錯
- **Commit ID**: `33baaf1d17ef96121751ccdbeb479833f36aeec9`
- **作者**: wayne
- **日期**: 2017-02-21 15:06:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`

### 7. Q00-*********** 2次修正:阻擋使用者不登入及正常管道就下載ISO檔案
- **Commit ID**: `dc683529629afd81f82f729794addb57a684aa79`
- **作者**: joseph
- **日期**: 2017-02-21 15:01:18
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ServerCacheManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISODocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODownloadKeyCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 8. 修正已發起的流程預測流程(instance) ,遇到解析類行為主管或代理人 , 會在後台出現exception , 導致該關卡無法解析(definition預測 , 程式部分簽入 , 但源頭mark不會呼叫)
- **Commit ID**: `37b0826a4c590e8046d374001a64a454062c9821`
- **作者**: jerry1218
- **日期**: 2017-02-20 15:19:47
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 9. 修正[陽明醫院]BPMAPP測試相關問題:進階查詢第一次查詢沒有動作，第二次查詢才有反應
- **Commit ID**: `ff284114d339fa64ad63bbfdcc36b43b31e0c5f1`
- **作者**: ChinRong
- **日期**: 2017-02-18 16:28:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`

### 10. 移除ProcessDispatcherBean開發階段多餘System.out
- **Commit ID**: `ccbc19949d5cd8d020dbc7f9f8a2d2c6d9acf76e`
- **作者**: jerry1218
- **日期**: 2017-02-17 16:21:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 11. 移除BpmProcessInstanceSubTraceResult.jsp多餘alert
- **Commit ID**: `e402f34da7548cc7556b4f05ea64b38ad4c2b8fd`
- **作者**: jerry1218
- **日期**: 2017-02-17 16:09:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`

### 12. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `1fa3d4c9d247e72fed973d74a0daa99340f99c35`
- **作者**: arielshih
- **日期**: 2017-02-16 17:04:17
- **變更檔案數量**: 0

### 13. Q00-20170216001 修正[預測]關卡時,遇到服務任務會出現ClassCastException,流程圖開啟失敗
- **Commit ID**: `f94a97947e23f9e8e21546daeb0d968e5805d6af`
- **作者**: jerry1218
- **日期**: 2017-02-16 16:27:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 14. 修正Update Oracle的sql指令，replace錯誤標籤與語系大小寫。
- **Commit ID**: `dd0ee82e797234c3170236735e2234f089946562`
- **作者**: LALA
- **日期**: 2017-02-16 16:17:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.2_updateSQL_Oracle.sql`

### 15. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `d96b0dbf851829a7a86811445868a81bdccf5d72`
- **作者**: arielshih
- **日期**: 2017-02-16 16:14:09
- **變更檔案數量**: 0

### 16. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f3fafff28cd32c797e3c60a7ae28c0d191606132`
- **作者**: wayne
- **日期**: 2017-02-16 15:31:32
- **變更檔案數量**: 0

### 17. 調整錯字
- **Commit ID**: `844d3aab03dfd4706d8f723414bee3ce4ded4181`
- **作者**: wayne
- **日期**: 2017-02-16 15:30:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties`

### 18. S00-*********** 刪除舊版流程part4
- **Commit ID**: `f1a913091b2b44fe89040fe4fd778d06a2255c6a`
- **作者**: arielshih
- **日期**: 2017-02-16 15:02:16
- **變更檔案數量**: 31
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@installtest/form-default/testForm.form`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@installtest/process-default/bpmn/testProcess.bpmn`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@installtest/process-default/xpdl/testProcess.process`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt/form-default/\345\212\240\347\217\255\347\224\263\350\253\213\345\226\256.form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt/form-default/\346\264\275\345\205\254\347\224\263\350\253\213\345\226\256.form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt/form-default/\350\253\213\345\201\207\347\224\263\350\253\213\345\226\256.form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt/form-default/\351\212\267\345\201\207\347\224\263\350\253\213\345\226\256.form"`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomJsLib/attendance.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomJsLib/ds_j.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomMultilanguage/language.en_US.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomMultilanguage/language.zh_CN.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomMultilanguage/language.zh_TW.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomMultilanguage/multilanguageUtil.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomOpenWin/Matt_LeaveRecord.jsp`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomOpenWin/Matt_UnitLeaveRecord.jsp`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt/openWin-source/CustomOpenWin/Matt_VisitRecord.jsp`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt/process-default/xpdl/\345\212\240\347\217\255\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt/process-default/xpdl/\346\264\275\345\205\254\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt/process-default/xpdl/\350\253\213\345\201\207\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt/process-default/xpdl/\351\212\267\345\201\207\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@matt-tiptop/Readme.txt`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/form-default/ManageIssue.form`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/form-default/QualityIssue.form`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/openWin-source/CustomJsLib/wards_j.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/openWin-source/CustomMultilanguage/language.en_US.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/openWin-source/CustomMultilanguage/language.zh_CN.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/openWin-source/CustomMultilanguage/language.zh_TW.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/openWin-source/CustomMultilanguage/multilanguageUtil.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/openWin-source/CustomOpenWin/WarOpenWinSingle.jsp`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/process-default/xpdl/Manage Issue.process`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom/process-default/xpdl/Quality Issue.process`

### 19. Q00-20170215003 因為T100整合透過流程樣板建立簽核流,目前Invoke會有異常,故先改回原本的(以結案事件呼叫)
- **Commit ID**: `ec6f0a2683f4c715e51a2a62293b520b879d45b0`
- **作者**: jerry1218
- **日期**: 2017-02-16 14:30:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/process-default/template/\345\205\251\351\227\234\345\210\266\347\260\275\346\240\270\346\250\243\346\235\277.bpmn"`

### 20. 修正加簽的關卡於查看完整流程圖時大小與非加簽關卡不一致問題
- **Commit ID**: `f47bfae22433e376d84556916b40a60bc0fcc820`
- **作者**: jerry1218
- **日期**: 2017-02-16 14:29:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 21. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `718f11d87364d5bd850bd6d1d237d4d3c53fb673`
- **作者**: jerry1218
- **日期**: 2017-02-16 11:51:39
- **變更檔案數量**: 0

### 22. 移除以前未刪除的開發段LOG
- **Commit ID**: `4bcc80b6f6763ce4ac7b84da3eb71a22c57dc57f`
- **作者**: jerry1218
- **日期**: 2017-02-16 11:51:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimpleExpenseAccountItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 23. S00-*********** 刪除舊版流程part3
- **Commit ID**: `bdb08afe62326f9ce70b69d391bf5473e41e5efd`
- **作者**: arielshih
- **日期**: 2017-02-16 10:15:34
- **變更檔案數量**: 396
- **檔案變更詳細**:
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/WIP \351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/WIP \351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/WIP \351\233\234\351\240\205\347\231\274\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\344\270\200\350\210\254\345\202\263\347\245\250\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\344\270\200\350\210\254\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\344\273\230\346\254\276\346\262\226\345\270\263\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\344\273\243\350\262\267\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\200\211\345\272\253\351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\200\211\345\272\253\351\233\234\351\240\205\347\231\274\346\226\231\344\275\234.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\212\240\347\217\255\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\226\256\344\270\200\344\270\273\344\273\266\345\267\245\347\250\213\350\256\212\347\225\260\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\233\272\345\256\232\350\263\207\347\224\242\346\224\271\350\211\257\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\233\272\345\256\232\350\263\207\347\224\242\350\252\277\346\225\264\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\233\272\345\256\232\350\263\207\347\224\242\351\207\215\344\274\260\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\240\261\345\203\271\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\244\226\351\200\201\350\263\207\347\224\242\346\224\266\345\233\236\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\244\232\344\270\273\344\273\266\345\267\245\347\250\213\350\256\212\347\225\260\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\244\232\350\247\222\350\262\277\346\230\223\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\244\232\350\247\222\350\262\277\346\230\223\351\212\267\351\200\200\347\266\255\350\255\267(\345\244\232\345\267\245\345\273\240).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\247\224\345\244\226\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\256\242\346\210\266\345\220\210\347\264\204\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\272\253\345\255\230\351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\273\240\345\225\206D.M.\346\254\276\351\240\205\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\273\240\345\225\206\351\200\200\350\262\250\346\212\230\350\256\223\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\273\240\345\225\206\351\200\262\350\262\250\347\231\274\347\245\250\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\345\273\240\345\225\206\351\240\220\344\273\230\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\346\207\211\346\224\266\345\270\263\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\346\216\241\350\263\274\346\226\231\344\273\266\346\240\270\345\203\271\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\346\224\266\346\254\276\346\262\226\345\270\263\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\250\255\350\250\210\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\253\213\345\201\207\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\263\207\347\224\242\344\275\215\347\275\256\347\247\273\350\275\211\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\263\207\347\224\242\345\207\272\345\224\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\263\207\347\224\242\345\240\261\345\273\242\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\263\207\347\224\242\345\244\226\351\200\201\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\263\207\347\224\242\351\203\250\351\226\200\347\247\273\350\275\211\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\350\263\207\347\224\242\351\212\267\345\270\263\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/3.x/\351\233\234\351\240\205\346\207\211\344\273\230\346\254\276\351\240\205\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/WIP \351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/WIP \351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/WIP \351\233\234\351\240\205\347\231\274\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\344\270\200\350\210\254\345\202\263\347\245\250\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\344\270\200\350\210\254\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\344\273\230\346\254\276\346\262\226\345\270\263\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\344\273\243\350\262\267\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\344\276\233\346\207\211\345\225\206\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\200\211\345\272\253\351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\200\211\345\272\253\351\233\234\351\240\205\347\231\274\346\226\231\344\275\234.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\200\237\346\224\257\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\200\237\350\262\250\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\200\237\350\262\250\347\224\263\350\253\213\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\212\240\347\217\255\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\223\241\345\267\245\345\200\237\346\254\276\345\240\261\351\212\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\226\256\344\270\200\344\270\273\344\273\266\345\267\245\347\250\213\350\256\212\347\225\260\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\233\272\345\256\232\350\263\207\347\224\242\346\224\271\350\211\257\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\233\272\345\256\232\350\263\207\347\224\242\350\252\277\346\225\264\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\233\272\345\256\232\350\263\207\347\224\242\351\207\215\344\274\260\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\240\261\345\203\271\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\244\226\351\200\201\350\263\207\347\224\242\346\224\266\345\233\236\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\244\232\344\270\273\344\273\266\345\267\245\347\250\213\350\256\212\347\225\260\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\244\232\350\247\222\350\262\277\346\230\223\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\244\232\350\247\222\350\262\277\346\230\223\351\212\267\351\200\200\347\266\255\350\255\267(\345\244\232\345\267\245\345\273\240).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\247\224\345\244\226\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\247\224\345\244\226\346\216\241\350\263\274\346\226\231\344\273\266\346\240\270\345\203\271\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\256\242\346\210\266\345\220\210\347\264\204\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\256\242\346\210\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\256\242\346\210\266\347\260\275\346\224\266\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\256\242\346\210\266\351\251\227\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\257\251\350\250\210\350\252\277\346\225\264\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\267\245\345\226\256\350\256\212\346\233\264\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\272\253\345\255\230\351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\273\240\345\225\206D.M.\346\254\276\351\240\205\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\273\240\345\225\206\351\200\200\350\262\250\346\212\230\350\256\223\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\273\240\345\225\206\351\200\262\350\262\250\347\231\274\347\245\250\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\345\273\240\345\225\206\351\240\220\344\273\230\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\207\211\346\224\266\345\270\263\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\207\211\350\250\210\345\202\263\347\245\250\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\216\241\350\263\274\346\226\231\344\273\266\346\240\270\345\203\271\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\224\266\346\254\276\346\262\226\345\270\263\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\226\231\344\273\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\232\253\344\274\260\345\205\245\345\272\253\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\232\253\344\274\260\351\200\200\350\262\250\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\250\241\345\205\267\346\255\270\351\202\204\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\346\250\241\345\205\267\351\240\230\347\224\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\250\255\350\250\210\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\253\213\345\201\207\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\263\207\347\224\242\344\275\215\347\275\256\347\247\273\350\275\211\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\263\207\347\224\242\345\207\272\345\224\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\263\207\347\224\242\345\240\261\345\273\242\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\263\207\347\224\242\345\244\226\351\200\201\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\263\207\347\224\242\351\203\250\351\226\200\347\247\273\350\275\211\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\350\263\207\347\224\242\351\212\267\345\270\263\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\351\233\234\351\240\205\346\207\211\344\273\230\346\254\276\351\240\205\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.0/\351\233\266\347\224\250\351\207\221\344\273\230\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/WIP \351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/WIP \351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/WIP \351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/WIP \351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/WIP \351\233\234\351\240\205\347\231\274\346\226\231\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/WIP \351\233\234\351\240\205\347\231\274\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/Wafer\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/Wafer\346\216\241\350\263\274\345\205\245\345\272\253\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/Wafer\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/Wafer\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/Wafer\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\344\270\200\350\210\254\345\202\263\347\245\250\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\344\270\200\350\210\254\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\344\270\200\350\210\254\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\344\273\230\346\254\276\346\262\226\345\270\263\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\344\273\243\350\262\267\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\344\276\233\346\207\211\345\225\206\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\211\345\272\253\351\226\223\347\233\264\346\216\245\350\252\277\346\222\245\344\275\234\346\245\255(\345\244\232\350\241\214)(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\211\345\272\253\351\226\223\347\233\264\346\216\245\350\252\277\346\222\245\344\275\234\346\245\255(\345\244\232\350\241\214).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\211\345\272\253\351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\211\345\272\253\351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\211\345\272\253\351\233\234\351\240\205\347\231\274\346\226\231\344\275\234.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\211\345\272\253\351\233\234\351\240\205\347\231\274\346\226\231\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\237\346\224\257\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\237\350\262\250\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\237\350\262\250\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\200\237\350\262\250\347\224\263\350\253\213\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\212\240\347\217\255\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\223\241\345\267\245\345\200\237\346\254\276\345\240\261\351\212\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\226\256\344\270\200\344\270\273\344\273\266\345\267\245\347\250\213\350\256\212\347\225\260\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\233\272\345\256\232\350\263\207\347\224\242\346\224\271\350\211\257\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\233\272\345\256\232\350\263\207\347\224\242\350\252\277\346\225\264\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\233\272\345\256\232\350\263\207\347\224\242\351\207\215\344\274\260\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\240\261\345\203\271\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\240\261\351\212\267-\351\202\204\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\226\351\200\201\350\263\207\347\224\242\346\224\266\345\233\236\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\344\270\273\344\273\266\345\267\245\347\250\213\350\256\212\347\225\260\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\350\247\222\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\350\247\222\346\216\241\350\263\274\345\272\253\345\255\230\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\350\247\222\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\350\247\222\350\262\277\346\230\223\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\350\247\222\350\262\277\346\230\223\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\244\232\350\247\222\350\262\277\346\230\223\351\212\267\351\200\200\347\266\255\350\255\267(\345\244\232\345\267\245\345\273\240).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\216\241\350\263\274\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\216\241\350\263\274\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\216\241\350\263\274\346\226\231\344\273\266\346\240\270\345\203\271\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\216\241\350\263\274\351\251\227\351\200\200\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\216\241\350\263\274\351\251\227\351\200\200\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\224\266\350\262\250\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\247\224\345\244\226\346\224\266\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\256\214\345\267\245\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(\351\207\215\350\244\207\346\200\247\347\224\237\347\224\242).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\256\242\346\210\266\345\220\210\347\264\204\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\256\242\346\210\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\256\242\346\210\266\347\260\275\346\224\266\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\257\251\350\250\210\350\252\277\346\225\264\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\344\270\200\350\210\254\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\344\270\200\350\210\254\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\346\210\220\345\245\227\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\346\210\220\345\245\227\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\346\210\220\345\245\227\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\346\210\220\345\245\227\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\346\254\240\346\226\231\350\243\234\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\346\254\240\346\226\231\350\243\234\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\350\256\212\346\233\264\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\350\266\205\351\240\230\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\350\266\205\351\240\230\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\350\266\205\351\240\230\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\350\266\205\351\240\230\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\351\240\230\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\351\240\230\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\351\240\230\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\267\245\345\226\256\351\240\230\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\272\253\345\255\230\351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\273\240\345\225\206D.M.\346\254\276\351\240\205\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\273\240\345\225\206\351\200\200\350\262\250\346\212\230\350\256\223\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\273\240\345\225\206\351\200\262\350\262\250\347\231\274\347\245\250\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\345\273\240\345\225\206\351\240\220\344\273\230\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\207\211\346\224\266\345\270\263\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\207\211\350\250\210\345\202\263\347\245\250\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\345\205\245\345\272\253\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\345\205\245\345\272\253\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\346\226\231\344\273\266\346\240\270\345\203\271\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\224\266\346\254\276\346\262\226\345\270\263\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\226\231\344\273\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\232\253\344\274\260\345\205\245\345\272\253\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\232\253\344\274\260\351\200\200\350\262\250\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\250\241\345\205\267\346\255\270\351\202\204\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\346\250\241\345\205\267\351\240\230\347\224\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(\351\207\215\350\244\207\346\200\247\347\224\237\347\224\242).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\250\255\350\250\210\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\253\213\345\201\207\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\263\207\347\224\242\344\275\215\347\275\256\347\247\273\350\275\211\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\263\207\347\224\242\345\207\272\345\224\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\263\207\347\224\242\345\240\261\345\273\242\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\263\207\347\224\242\345\244\226\351\200\201\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\263\207\347\224\242\351\203\250\351\226\200\347\247\273\350\275\211\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\350\263\207\347\224\242\351\212\267\345\270\263\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(\351\207\215\350\244\207\346\200\247\347\224\237\347\224\242).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\351\233\234\351\240\205\346\207\211\344\273\230\346\254\276\351\240\205\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\351\233\266\347\224\250\351\207\221\344\273\230\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.1/\351\240\230\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(\351\207\215\350\244\207\346\200\247\347\224\237\347\224\242).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/WIP \351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/WIP \351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/WIP \351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/WIP \351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/WIP \351\233\234\351\240\205\347\231\274\346\226\231\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/WIP \351\233\234\351\240\205\347\231\274\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/Wafer\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/Wafer\346\216\241\350\263\274\345\205\245\345\272\253\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/Wafer\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/Wafer\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/Wafer\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\344\270\200\350\210\254\345\202\263\347\245\250\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\344\270\200\350\210\254\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\344\270\200\350\210\254\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\344\273\230\346\254\276\346\262\226\345\270\263\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\344\273\243\350\262\267\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\344\276\233\346\207\211\345\225\206\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\211\345\272\253\351\226\223\347\233\264\346\216\245\350\252\277\346\222\245\344\275\234\346\245\255(\345\244\232\350\241\214)(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\211\345\272\253\351\226\223\347\233\264\346\216\245\350\252\277\346\222\245\344\275\234\346\245\255(\345\244\232\350\241\214).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\211\345\272\253\351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\211\345\272\253\351\233\234\351\240\205\346\224\266\346\226\231\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\211\345\272\253\351\233\234\351\240\205\347\231\274\346\226\231\344\275\234.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\211\345\272\253\351\233\234\351\240\205\347\231\274\346\226\231\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\237\346\224\257\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\237\350\262\250\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\237\350\262\250\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\200\237\350\262\250\347\224\263\350\253\213\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\223\241\345\267\245\345\200\237\346\254\276\345\240\261\351\212\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\226\256\344\270\200\344\270\273\344\273\266\345\267\245\347\250\213\350\256\212\347\225\260\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\233\272\345\256\232\350\263\207\347\224\242\346\224\271\350\211\257\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\233\272\345\256\232\350\263\207\347\224\242\350\252\277\346\225\264\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\233\272\345\256\232\350\263\207\347\224\242\351\207\215\344\274\260\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\240\261\345\203\271\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\226\351\200\201\350\263\207\347\224\242\346\224\266\345\233\236\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\344\270\273\344\273\266\345\267\245\347\250\213\350\256\212\347\225\260\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\350\247\222\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\350\247\222\346\216\241\350\263\274\345\272\253\345\255\230\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\350\247\222\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\350\247\222\350\262\277\346\230\223\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\350\247\222\350\262\277\346\230\223\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\244\232\350\247\222\350\262\277\346\230\223\351\212\267\351\200\200\347\266\255\350\255\267(\345\244\232\345\267\245\345\273\240).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\216\241\350\263\274\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\216\241\350\263\274\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\216\241\350\263\274\346\226\231\344\273\266\346\240\270\345\203\271\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\216\241\350\263\274\351\251\227\351\200\200\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\216\241\350\263\274\351\251\227\351\200\200\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\224\266\350\262\250\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\247\224\345\244\226\346\224\266\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\256\214\345\267\245\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(\351\207\215\350\244\207\346\200\247\347\224\237\347\224\242).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\256\242\346\210\266\345\220\210\347\264\204\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\256\242\346\210\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\256\242\346\210\266\347\260\275\346\224\266\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\257\251\350\250\210\350\252\277\346\225\264\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\344\270\200\350\210\254\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\344\270\200\350\210\254\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\346\210\220\345\245\227\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\346\210\220\345\245\227\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\346\210\220\345\245\227\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\346\210\220\345\245\227\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\346\254\240\346\226\231\350\243\234\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\346\254\240\346\226\231\350\243\234\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\350\256\212\346\233\264\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\350\266\205\351\240\230\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\350\266\205\351\240\230\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\350\266\205\351\240\230\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\350\266\205\351\240\230\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\351\240\230\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\351\240\230\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\351\240\230\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\267\245\345\226\256\351\240\230\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\272\253\345\255\230\351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255 (ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\272\253\345\255\230\351\233\234\351\240\205\345\240\261\345\273\242\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\273\240\345\225\206D.M.\346\254\276\351\240\205\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\273\240\345\225\206\351\200\200\350\262\250\346\212\230\350\256\223\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\273\240\345\225\206\351\200\262\350\262\250\347\231\274\347\245\250\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\345\273\240\345\225\206\351\240\220\344\273\230\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\207\211\346\224\266\345\270\263\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\207\211\350\250\210\345\202\263\347\245\250\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\345\205\245\345\272\253\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\345\205\245\345\272\253\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\346\226\231\344\273\266\346\240\270\345\203\271\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(GP5.1).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\224\266\346\254\276\346\262\226\345\270\263\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\226\231\344\273\266\346\211\277\350\252\215\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\226\231\344\273\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\232\253\344\274\260\345\205\245\345\272\253\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\232\253\344\274\260\351\200\200\350\262\250\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\250\241\345\205\267\346\255\270\351\202\204\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\346\250\241\345\205\267\351\240\230\347\224\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(\351\207\215\350\244\207\346\200\247\347\224\237\347\224\242).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\243\275\351\200\240\351\200\232\347\237\245\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(ICD).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\250\255\350\250\210\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\253\213\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\263\207\347\224\242\344\275\215\347\275\256\347\247\273\350\275\211\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\263\207\347\224\242\345\207\272\345\224\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\263\207\347\224\242\345\240\261\345\273\242\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\263\207\347\224\242\345\244\226\351\200\201\350\263\207\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\263\207\347\224\242\351\203\250\351\226\200\347\247\273\350\275\211\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\350\263\207\347\224\242\351\212\267\345\270\263\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\351\200\200\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(\351\207\215\350\244\207\346\200\247\347\224\237\347\224\242).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\351\233\234\351\240\205\346\207\211\344\273\230\346\254\276\351\240\205\350\253\213\346\254\276\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\351\233\266\347\224\250\351\207\221\344\273\230\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.25/\351\240\230\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(\351\207\215\350\244\207\346\200\247\347\224\237\347\224\242).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\344\270\200\350\210\254\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\344\273\243\350\262\267\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\200\211\345\272\253\351\226\223\347\233\264\346\216\245\350\252\277\346\222\245\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\200\211\345\272\253\351\233\234\346\224\266\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\200\211\345\272\253\351\233\234\347\231\274\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\200\211\345\272\253\351\233\234\351\240\205\345\240\261\345\273\242\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\244\232\350\247\222\346\216\241\350\263\274\345\200\211\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\244\232\350\247\222\346\216\241\350\263\274\345\272\253\345\255\230\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\244\232\350\247\222\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\244\232\350\247\222\350\262\277\346\230\223\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\244\232\350\247\222\350\262\277\346\230\223\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\244\232\350\247\222\350\262\277\346\230\223\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\244\232\350\247\222\350\262\277\346\230\223\351\212\267\351\200\200\347\266\255\350\255\267(\345\244\232\347\207\237\351\201\213\344\270\255\345\277\203).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\345\267\245\345\226\256\350\256\212\346\233\264\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\346\216\241\350\263\274\345\205\245\345\272\253\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\346\216\241\350\263\274\351\200\200\350\262\250\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\346\216\241\350\263\274\351\251\227\351\200\200\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\347\231\274\346\226\231\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\350\253\213\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/xpdl/5.3/\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.process"`

### 24. S00-*********** 刪除舊版流程part2
- **Commit ID**: `b45c47ddafe0b4aba905888e5f55c249dbb36e6f`
- **作者**: arielshih
- **日期**: 2017-02-16 10:08:32
- **變更檔案數量**: 236
- **檔案變更詳細**:
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF01\346\216\222\347\217\255\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF03\350\243\234\345\210\267\345\215\241\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF04B\345\212\240\347\217\255\347\224\263\350\253\213(\346\211\271\351\207\217).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF04\345\212\240\347\217\255\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF05\345\212\240\347\217\255\350\250\210\345\212\203\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF06\345\212\240\347\217\255\350\252\277\344\274\221\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF07\350\253\213\345\201\207\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF08\347\251\215\344\274\221\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF17\351\212\267\345\201\207\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF20\345\207\272\345\267\256\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF21\345\207\272\345\267\256\347\231\273\350\250\230.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF22\350\252\277\350\201\267\350\252\277\350\226\252\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF23B\350\252\277\350\201\267\347\224\263\350\253\213(\346\211\271\351\207\217).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF23\350\252\277\350\201\267\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF24\350\252\277\350\226\252\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF25\350\275\211\346\255\243\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF26B\347\215\216\346\207\262\347\224\263\350\253\213(\346\211\271\351\207\217).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF26\347\215\216\346\207\262\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF27\351\233\242\350\201\267\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF28\344\272\272\345\212\233\351\234\200\346\261\202\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF29\350\275\211\346\255\243\350\252\277\350\226\252\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF30\350\243\234\345\210\267\345\215\241\347\224\263\350\253\213(\346\211\271\351\207\217).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF31\346\213\233\350\201\230\350\250\210\347\225\253.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF32\346\207\211\350\201\230\344\272\272\345\223\241\351\235\242\350\251\246.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF33\346\207\211\350\201\230\344\272\272\345\223\241\347\255\206\350\251\246.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF34\351\214\204\347\224\250\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF41\350\200\203\346\240\270\350\250\210\345\212\203.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF42\350\207\252\345\256\232\347\276\251\350\200\203\346\240\270\346\214\207\346\250\231.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF43\350\277\260\350\201\267\345\240\261\345\221\212.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF44\350\200\203\346\240\270\350\251\225\345\210\206.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF46\350\200\203\346\240\270\347\224\263\350\250\264.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF47\350\200\203\346\240\270\346\224\271\351\200\262.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF50\347\217\255\346\254\241\350\256\212\346\233\264\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF52C1\347\217\255\346\254\241\344\272\222\346\217\233.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF52C2\347\217\255\346\254\241\350\256\212\346\233\264.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF52\346\212\225\347\217\255\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF53\346\216\222\347\217\255\347\242\272\350\252\215.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF60\350\254\233\345\270\253\350\263\207\346\240\274\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF61\350\252\262\347\250\213\351\226\213\347\231\274\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF62\345\205\254\345\217\270\345\271\264\345\272\246\350\246\217\345\212\203\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF63\345\237\271\350\250\223\351\234\200\346\261\202\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF64\345\237\271\350\250\223\350\250\210\347\225\253\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF66\345\237\271\350\250\223\350\251\225\344\274\260.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF67\345\237\271\350\250\223\345\240\261\345\220\215.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF68\345\217\226\346\266\210\345\237\271\350\250\223\345\240\261\345\220\215.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF69\345\223\241\345\267\245\347\225\260\345\213\225\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF71\350\253\213\345\201\207\347\224\263\350\253\213(\346\211\271\351\207\217).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF72\345\223\241\345\267\245\345\240\261\345\210\260\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF74\350\263\207\346\272\220\347\224\263\351\240\230.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF75\350\263\207\346\272\220\346\255\270\351\202\204.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSF76\346\213\233\345\213\237\346\224\271\351\200\262\345\273\272\350\255\260.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/xpdl/ESSQ27\345\270\270\347\224\250\344\270\213\350\274\211.process"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\256\242\346\210\266\346\234\215\345\213\231\347\231\273\351\214\204\350\231\225\347\220\206\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\344\274\260\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\217\253\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\256\211\350\243\235\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\256\211\350\243\235\351\251\227\346\224\266\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\256\242\346\210\266\344\277\235\351\244\212\347\264\200\351\214\204\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\256\242\346\210\266\345\220\210\347\264\204\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\345\267\245\344\275\234\350\250\230\351\214\204.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\346\250\243\345\223\201\347\224\263\350\253\213.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\347\266\255\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\350\262\273\347\224\250\347\224\263\350\253\213\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\351\200\201\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\351\200\201\345\216\237\345\273\240\347\266\255\344\277\256\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\351\200\201\345\273\240\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@crm/process-default/xpdl/\351\212\267\345\224\256\346\251\237\346\234\203\347\266\255\350\255\267\344\275\234\346\245\255.process"`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOCancel001.process`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOCancelBatch001.process`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOInv001.process`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOMod001.process`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOModBatch001.process`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISONew001.process`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOPaperApply.process`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOPaperWriteOff.process`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\347\265\204\345\220\210\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI05).process"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI23).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/BOM\347\224\250\351\207\217\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI02).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/BOM\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI04).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/E-BOM\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI12).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/PACKING LIST \345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI06).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/PACKING LIST\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_IDLI43).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/SI\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_IPSI04).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/WAFER BANK\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_IDLI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/WAFER \350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_IDLI15).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\344\273\230\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACPI03).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\207\272\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI17).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\223\201\351\201\213\345\233\236\351\200\262\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI18).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\344\277\235\347\250\205\346\251\237\345\231\250\350\250\255\345\202\231\351\200\262\345\207\272\345\217\243\347\225\260\345\213\225\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCHI14).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCHI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\344\277\235\347\250\205\350\262\250\345\223\201\345\207\272\345\273\240\344\277\256\347\220\206\346\252\242\346\270\254\346\210\226\346\240\270\346\250\243\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI15).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\344\277\241\347\224\250\347\213\200\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI19).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\205\245\346\255\270\351\202\204\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI12).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI20).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\205\266\344\273\226\345\207\272\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI13).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\207\272\345\217\243\350\262\273\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI10).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCHI09).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI12).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI62).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\212\240\345\267\245\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_MOCI10).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI58).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI19).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI59).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI20).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\223\201\350\231\237\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI24).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\244\232\345\270\263\346\234\254\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACTI62).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\247\224\345\244\226\345\267\245\345\226\256\351\226\213\347\253\213(WorkFlowERP_IDLI33).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI06).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\256\242\346\210\266\350\263\207\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI15).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\345\272\253\345\255\230\347\225\260\345\213\225\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\207\211\344\273\230\346\206\221\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACPI02).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\213\206\350\247\243\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI06).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\216\241\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI07).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\224\266\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACRI03).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\240\261\345\220\215\344\275\234\346\245\255(WorkFlowERP_HRSI34).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\226\260\345\256\242\346\210\266\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI21).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACTI10).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI03).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\346\264\276\350\273\212\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI14).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\347\265\204\345\220\210\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\347\265\220\345\270\263\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACRI02).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI07).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\250\223\347\267\264\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_HRSI23.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\251\242\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI14).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\252\277\346\225\264\346\262\226\351\212\267\345\210\206\351\214\204\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_FCSI04).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\253\213\350\263\274\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI16).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\262\250\351\201\213\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI07).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\345\240\261\345\273\242\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\345\244\226\351\200\201\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI13).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\346\212\230\350\210\212\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\346\216\241\350\263\274\350\256\212\346\233\264\344\275\234\346\245\255(WorkFlowERP_ASTI24).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\346\216\241\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI22).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\346\224\266\345\233\236\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI14).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\346\224\271\350\211\257\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI06).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\346\270\233\346\220\215\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI25).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\347\247\273\350\275\211\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI12).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\350\251\242\345\203\271\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI20).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\350\252\277\346\225\264\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI10).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI19).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI02).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI23).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\263\207\347\224\242\351\207\215\344\274\260\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI07).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\350\275\211\346\222\245\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\351\200\200\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI09).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\351\212\267\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\351\212\267\351\200\200\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI09).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\351\240\220\347\256\227\346\214\252\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACTI23).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8080Port/\351\240\220\347\256\227\350\277\275\345\212\240\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACTI22).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/BOM\347\224\250\351\207\217\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI02).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/BOM\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI04).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/E-BOM\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI12).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/PACKING LIST \345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI06).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/PACKING LIST\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_IDLI43).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/SI\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_IPSI04).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/WAFER BANK\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_IDLI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/WAFER \350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_IDLI15).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\344\273\230\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACPI03).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\207\272\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI17).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\223\201\351\201\213\345\233\236\351\200\262\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI18).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\344\277\235\347\250\205\346\251\237\345\231\250\350\250\255\345\202\231\351\200\262\345\207\272\345\217\243\347\225\260\345\213\225\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCHI14).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCHI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\344\277\235\347\250\205\350\262\250\345\223\201\345\207\272\345\273\240\344\277\256\347\220\206\346\252\242\346\270\254\346\210\226\346\240\270\346\250\243\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI15).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\344\277\241\347\224\250\347\213\200\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI19).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\205\245\346\255\270\351\202\204\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI12).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI20).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\205\266\344\273\226\345\207\272\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI13).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\207\272\345\217\243\350\262\273\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI10).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCHI09).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BCSI12).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI62).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\212\240\345\267\245\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_MOCI10).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI58).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI19).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(WorkFlowERP_IDLI59).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI20).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\223\201\350\231\237\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI24).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\244\232\345\270\263\346\234\254\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACTI62).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\247\224\345\244\226\345\267\245\345\226\256\351\226\213\347\253\213(WorkFlowERP_IDLI33).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI06).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\256\242\346\210\266\350\263\207\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI15).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\345\272\253\345\255\230\347\225\260\345\213\225\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\207\211\344\273\230\346\206\221\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACPI02).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\213\206\350\247\243\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_BOMI06).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\216\241\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI07).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\224\266\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACRI03).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\240\261\345\220\215\344\275\234\346\245\255(WorkFlowERP_HRSI34).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\226\260\345\256\242\346\210\266\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI21).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACTI10).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI03).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\346\264\276\350\273\212\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI14).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\347\265\220\345\270\263\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACRI02).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI07).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\250\223\347\267\264\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_HRSI23.process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\251\242\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI14).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\252\277\346\225\264\346\262\226\351\212\267\345\210\206\351\214\204\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_FCSI04).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI05).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\253\213\350\263\274\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI16).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\262\250\351\201\213\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_EPSI07).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\345\240\261\345\273\242\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\345\244\226\351\200\201\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI13).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\346\212\230\350\210\212\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\346\216\241\350\263\274\350\256\212\346\233\264\344\275\234\346\245\255(WorkFlowERP_ASTI24).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\346\216\241\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI22).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\346\224\266\345\233\236\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI14).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\346\224\271\350\211\257\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI06).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\346\270\233\346\220\215\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI25).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\347\247\273\350\275\211\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI12).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\350\251\242\345\203\271\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI20).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\350\252\277\346\225\264\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI10).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI19).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI02).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\263\207\347\224\242\351\207\215\344\274\260\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ASTI07).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\350\275\211\346\222\245\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_INVI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\351\200\200\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI11).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI09).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\351\212\267\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI08).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\351\212\267\351\200\200\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_COPI09).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\351\240\220\347\256\227\346\214\252\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACTI23).process"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/xpdl/process_8086Port/\351\240\220\347\256\227\350\277\275\345\212\240\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_ACTI22).process"`

### 25. S00-*********** 刪除舊版process流程
- **Commit ID**: `dc4d6b496394a065f6e7b8a6bdd38524bcfd584c`
- **作者**: arielshih
- **日期**: 2017-02-16 10:07:08
- **變更檔案數量**: 52
- **檔案變更詳細**:
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF01\346\216\222\347\217\255\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF03\350\243\234\345\210\267\345\215\241\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF04B\345\212\240\347\217\255\347\224\263\350\253\213(\346\211\271\351\207\217).bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF04\345\212\240\347\217\255\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF05\345\212\240\347\217\255\350\250\210\345\212\203\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF06\345\212\240\347\217\255\350\252\277\344\274\221\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF08\347\251\215\344\274\221\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF17\351\212\267\345\201\207\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF20\345\207\272\345\267\256\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF21\345\207\272\345\267\256\347\231\273\350\250\230.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF22\350\252\277\350\201\267\350\252\277\350\226\252\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF23B\350\252\277\350\201\267\347\224\263\350\253\213(\346\211\271\351\207\217).bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF23\350\252\277\350\201\267\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF24\350\252\277\350\226\252\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF25\350\275\211\346\255\243\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF26B\347\215\216\346\207\262\347\224\263\350\253\213(\346\211\271\351\207\217).bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF26\347\215\216\346\207\262\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF27\351\233\242\350\201\267\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF28\344\272\272\345\212\233\351\234\200\346\261\202\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF29\350\275\211\346\255\243\350\252\277\350\226\252\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF30\350\243\234\345\210\267\345\215\241\347\224\263\350\253\213(\346\211\271\351\207\217).bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF31\346\213\233\350\201\230\350\250\210\347\225\253.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF32\346\207\211\350\201\230\344\272\272\345\223\241\351\235\242\350\251\246.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF33\346\207\211\350\201\230\344\272\272\345\223\241\347\255\206\350\251\246.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF34\351\214\204\347\224\250\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF41\350\200\203\346\240\270\350\250\210\345\212\203.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF42\350\207\252\345\256\232\347\276\251\350\200\203\346\240\270\346\214\207\346\250\231.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF43\350\277\260\350\201\267\345\240\261\345\221\212.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF44\350\200\203\346\240\270\350\251\225\345\210\206.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF46\350\200\203\346\240\270\347\224\263\350\250\264.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF47\350\200\203\346\240\270\346\224\271\351\200\262.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF50\347\217\255\346\254\241\350\256\212\346\233\264\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF52C1\347\217\255\346\254\241\344\272\222\346\217\233.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF52C2\347\217\255\346\254\241\350\256\212\346\233\264.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF52\346\212\225\347\217\255\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF53\346\216\222\347\217\255\347\242\272\350\252\215.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF60\350\254\233\345\270\253\350\263\207\346\240\274\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF61\350\252\262\347\250\213\351\226\213\347\231\274\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF62\345\205\254\345\217\270\345\271\264\345\272\246\350\246\217\345\212\203\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF63\345\237\271\350\250\223\351\234\200\346\261\202\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF64\345\237\271\350\250\223\350\250\210\347\225\253\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF66\345\237\271\350\250\223\350\251\225\344\274\260.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF67\345\237\271\350\250\223\345\240\261\345\220\215.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF68\345\217\226\346\266\210\345\237\271\350\250\223\345\240\261\345\220\215.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF69\345\223\241\345\267\245\347\225\260\345\213\225\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF71\350\253\213\345\201\207\347\224\263\350\253\213(\346\211\271\351\207\217).bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF72\345\223\241\345\267\245\345\240\261\345\210\260\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF74\350\263\207\346\272\220\347\224\263\351\240\230.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF75\350\263\207\346\272\220\346\255\270\351\202\204.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSF76\346\213\233\345\213\237\346\224\271\351\200\262\345\273\272\350\255\260.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/process-default/bpmn/ESSQ27\345\270\270\347\224\250\344\270\213\350\274\211.bpmn"`

### 26. 移除BpmProcessWorkbox的System.out.println
- **Commit ID**: `b122053c68db9e0ff98c5d7b4adfc57a384acc4c`
- **作者**: jerry1218
- **日期**: 2017-02-15 15:53:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 27. 修正流程內容(已發起流程)橫向scrollbar未顯示問題
- **Commit ID**: `0a900088c494b9cb3a66de59d12b037e1488f394`
- **作者**: jerry1218
- **日期**: 2017-02-15 14:41:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`

### 28. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `1b45d32931bd4b94d10662696eca8d239cd810da`
- **作者**: wayne
- **日期**: 2017-02-15 14:28:12
- **變更檔案數量**: 0

### 29. 重新merge版本
- **Commit ID**: `2f21309d7b43456d6c9e274446fa463979733a25`
- **作者**: wayne
- **日期**: 2017-02-15 14:26:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 30. C01-*********** 修正: WorkflowServicefetchFullProcInstanceWithSerialNo 取得之簽核歷程無法區分公開轉寄或私人轉寄問題
- **Commit ID**: `56b438e0a2ad5b44b59ebccb989f75e7776e6df9`
- **作者**: joseph
- **日期**: 2017-02-15 14:20:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 31. 修正BpmProcessInstanceTraceResult.jsp如果選擇顯示全流程,左上角會有一個小黑點 最後使用invoke修正BpmProcessInstanceTraceResult.jsp如果選擇顯示全流程,左上角會有一個小黑點 新增判斷預測後續關卡當中,檢核List是否已有相同的ActivityDefinition(for全流程預測)
- **Commit ID**: `e28c9be31879143dcc3c987561f17bccbbf47264`
- **作者**: jerry1218
- **日期**: 2017-02-15 12:02:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`

### 32. C01-20170203001 修正通知信夾帶附件總容量限制
- **Commit ID**: `118a859e9633843fdf851eb58fb51cf5375a11da`
- **作者**: wayne
- **日期**: 2017-02-15 10:59:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`

### 33. 修改T100整合-流程樣板 , 將回寫事件改為最後使用invoke
- **Commit ID**: `6bc13729c3f542458221db49619db4354b9b8230`
- **作者**: jerry1218
- **日期**: 2017-02-14 17:47:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/process-default/template/\345\205\251\351\227\234\345\210\266\347\260\275\346\240\270\346\250\243\346\235\277.bpmn"`

### 34. C00-20170206001 修正app退回重辦後，關卡狀態變更為暫停中，流程也無法結案
- **Commit ID**: `df10036515564224c3580ca6911532ebc33df957`
- **作者**: pinchi_lin
- **日期**: 2017-02-14 16:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`

### 35. C00-20170206001 修正app退回重辦後，關卡狀態變更為暫停中，流程也無法結案
- **Commit ID**: `ddcb34df5b23b4cb376a0b8913651e2f424e9206`
- **作者**: pinchi_lin
- **日期**: 2017-02-14 16:47:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`

### 36. C00-20170206001 修正app退回重辦後，關卡狀態變更為暫停中，流程也無法結案
- **Commit ID**: `61200295e9b2bc09f6a70a02cb6e1fccd065d1e6`
- **作者**: pinchi_lin
- **日期**: 2017-02-14 16:46:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`

### 37. 修正BPMAPP通知列表中，搜尋圖示多一張問題
- **Commit ID**: `37dcfdecc0c24b3a06e7e4f7954b68fe4b7aa89d`
- **作者**: pinchi_lin
- **日期**: 2017-02-14 16:38:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`

### 38. 1.修正追蹤流程->子流程->返回主流程會出現錯誤問題 2.修正簽核時->流程內容如果選擇顯示[完整流程圖],左方zTreeDiv沒有把display設成none 3.修正預測流程時,預測的[第一階]關卡有多個的時候,只有第一階的第一個關卡會繼續往下預測問題 4.簡易流程圖關卡加上簽核意見 5.新增完整流程圖的流程狀態,如果是終止的流程加上終止人員
- **Commit ID**: `3ee38538c0b300f897d51a6d129763f9047e948d`
- **作者**: jerry1218
- **日期**: 2017-02-14 15:55:18
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessPreviewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`

### 39. 縮小版本資訊的開窗大小
- **Commit ID**: `a25a926b73f3011e475a05b01060e4cbabd46fed`
- **作者**: wayne
- **日期**: 2017-02-13 14:56:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 40. SOO-20170213001 再流程圖上方狀態新增已終止流程是由何人終止
- **Commit ID**: `bb871dd8e9d42fff5d41dd60779afe689b39aac1`
- **作者**: jerry1218
- **日期**: 2017-02-13 11:49:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`

### 41. 修正LABEL設為隱藏時，不會選取到該元件
- **Commit ID**: `084a52850a2bcfd40b0fbc6869661512317dbdb4`
- **作者**: Gaspard
- **日期**: 2017-02-12 17:52:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`

### 42. 新增多語系
- **Commit ID**: `99ff6d02a94a4745d81b57996ef8bad58b4b6035`
- **作者**: Joe
- **日期**: 2017-02-10 17:58:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle_nextVersion.xls`

### 43. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `0a14d70e908e7cfcb646f22a4ce3297d9b74539c`
- **作者**: LALA
- **日期**: 2017-02-10 10:29:34
- **變更檔案數量**: 0

### 44. A00-***********[普萊德]修正請求無XMLFile的標籤導致開單失敗的問題。
- **Commit ID**: `8b9b43ded9aa2fe2bc747bbc6f38948d87a48930`
- **作者**: LALA
- **日期**: 2017-02-10 10:28:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java`

### 45. S00-*********** - 流程內容預先解析功能 1.流程內容可依需求顯示[簡易流程圖]or[完整流程圖] 2.多分支流程人員預解析 3.修正Q單 Q00-*********** Q00-*********** Q00-*********** Q00-*********** Q00-***********
- **Commit ID**: `3cacebc070bffd98a8fa587d66da51d74a1651c9`
- **作者**: jerry1218
- **日期**: 2017-02-09 18:01:06
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcher.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`

### 46. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `12a4eaaa1cfe72777f735efaf413b0312ac22acc`
- **作者**: wayne
- **日期**: 2017-02-09 14:39:13
- **變更檔案數量**: 0

### 47. C00-20170206001 修正app退回重辦後，關卡狀態變更為暫停中，流程也無法結案
- **Commit ID**: `9522d5775c72c58a2fe92e6cca38fcf0e60ad5a1`
- **作者**: wayne
- **日期**: 2017-02-09 14:38:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManageLib.jsp`

### 48. Q00-*********** 阻擋使用者透過 ISO文件download連結 不透過登入 直接下載文件
- **Commit ID**: `c61e63df6c9a74f430bc0836f004c7c51650de54`
- **作者**: joseph
- **日期**: 2017-02-09 10:17:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 49. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `dcbe318fdf0f21c588c38d4d719dc49fc2179819`
- **作者**: WenCheng
- **日期**: 2017-02-08 18:15:38
- **變更檔案數量**: 0

### 50. Q00-20170208001 「線上人數查詢」頁面，文字描述修正(e.g.各主機登入連線紀錄查詢)，以避免客戶誤解。
- **Commit ID**: `0ea48278a041dbd9938a0069ece084868f4f9c0b`
- **作者**: WenCheng
- **日期**: 2017-02-08 18:14:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle_nextVersion.xls`

### 51. 二次修正舊有表單中元件cssStyle，原本是color:#;和background-color:#;轉型語法應使用nvarchar
- **Commit ID**: `90f8f52423d55f603daec4c2f0edb88226b48460`
- **作者**: arielshih
- **日期**: 2017-02-08 15:16:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 52. Q00-20170124002 :2次修正Grid與TextArea Binding在TextArea內容有換行流程發起後 點選Grid欄位後TextArea內容會有<br />
- **Commit ID**: `030888ca00d51618ce5ddd4fa835241c4cb63ddb`
- **作者**: joseph
- **日期**: 2017-02-08 11:09:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 53. C01-20170116001 :2次修正 控件会粘着鼠标移动，只能关闭设计师，重新开启
- **Commit ID**: `d9e025438a1e6307350fee1e1c98ded77b58874a`
- **作者**: joseph
- **日期**: 2017-02-08 10:02:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`

### 54. A00-20170124001 修正:在舊版的流程，管理员手动跳过关卡处理者，返回画面及流程图都报错
- **Commit ID**: `5bd442d899914cc3d6a335fbfb571e55b2835a10`
- **作者**: joseph
- **日期**: 2017-02-08 09:58:43
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_zh_TW.properties`

### 55. C01-20161222001[紅崴]修正篩選的下拉式選單，在Win10 IE11操作發生異常。
- **Commit ID**: `7598c72a9ce0f06d90e9e8e5ac72ea0e9752a2ad`
- **作者**: LALA
- **日期**: 2017-02-07 16:15:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/demojs/jquery.hoverIntent.minified.js`

### 56. 修改BPM APP部分UI
- **Commit ID**: `0e83b591d0f61a5d5422bcf9b3dcd747e63be46e`
- **作者**: Joe
- **日期**: 2017-02-07 10:01:20
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 57. 修正追蹤流程連結使用ldap驗證時，無法正常登入
- **Commit ID**: `5690c07f0cbc610cc1425d9808df6cd9bdc6a3ab`
- **作者**: wayne
- **日期**: 2017-02-06 15:36:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`

### 58. A00-20170111001 修正T100變更通知信內容導致發單失敗
- **Commit ID**: `a86b3c7e4b6710e1caccd1ade91570291e09de68`
- **作者**: wayne
- **日期**: 2017-02-06 15:33:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 59. C01-20161208001[時碩工業股]修正TT送簽的表單欄位資料有斷行，grid顯示異常的問題
- **Commit ID**: `8fdd5411d86af79354a7df70368daeff98b4d86f`
- **作者**: LALA
- **日期**: 2017-02-06 12:58:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 60. Revert "C01-20161212001[艾沃意特]修正TT送簽的表單欄位資料有斷行，grid顯示異常的問題"
- **Commit ID**: `99e1815a3d324eedfd833f3fb6584321164885ef`
- **作者**: LALA
- **日期**: 2017-02-06 12:00:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 61. C01-20161212001[艾沃意特]修正TT送簽的表單欄位資料有斷行，grid顯示異常的問題
- **Commit ID**: `261be97956eff464dc4c56e231641654a795bb33`
- **作者**: LALA
- **日期**: 2017-02-06 11:58:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 62. A00-20161228002[全一]修正表單formDispatchr()回傳false時表單呈現空白的問題。
- **Commit ID**: `0c61ec4fb7757c0c084b2a8be49ec59fffbe3752`
- **作者**: LALA
- **日期**: 2017-02-03 15:23:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 63. A00-20161228002[全一]修正表單formDispatchr()回傳false時表單呈現空白的問題。
- **Commit ID**: `11b8aa859b7f5b260d8fe6c0dec3f5f9fb66fa30`
- **作者**: LALA
- **日期**: 2017-02-03 14:19:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 64. 修正客製JSP無法使用SQL註冊器語法的議題
- **Commit ID**: `af7477e874533da8817d670b0588bad35ae2ff8e`
- **作者**: Gaspard
- **日期**: 2017-02-03 12:06:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 65. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f29904b850574f1dfd3ec5b2191b71b0a6b9b8aa`
- **作者**: Gaspard
- **日期**: 2017-02-03 11:57:08
- **變更檔案數量**: 0

### 66. C01-20170116001 :修正 控件会粘着鼠标移动，只能关闭设计师，重新开启
- **Commit ID**: `99af22381467ffd03da022132ff60db5043c3dba`
- **作者**: joseph
- **日期**: 2017-02-03 11:53:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`

### 67. Q00-20170124002 : 修正Grid與TextArea Binding在TextArea內容有換行流程發起後 點選Grid欄位後TextArea內容會有<br />
- **Commit ID**: `ea9bf3363a8564bfc402bd520b1755192c79d76b`
- **作者**: joseph
- **日期**: 2017-02-03 11:49:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 68. Q00-20170120003 : 修正Grid 操作：新增 然後 刪除 送出流程 會留下 ] 符號
- **Commit ID**: `fc34df071168ddf1c4a4e96b56beb4b2acdd0c7f`
- **作者**: joseph
- **日期**: 2017-02-03 11:47:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 69. A00-20170112001:WEB設計師的預設值有輸入值，填單時，有輸入欄位按下儲存表單或儲存草稿後，於草稿區簽出此表單，欄位值會變成設定的預設，但是於於草稿畫面按下左上角的表單頁籤後，值又會跑回來
- **Commit ID**: `464cb1fbb937ac2d612573417bc5a08c849c9107`
- **作者**: joseph
- **日期**: 2017-02-03 11:41:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 70. 新增5.6.2.2_updateSQL_Oracle.sql(修正C01-20170124002[瑞儀]修正跳關通知信有誤)
- **Commit ID**: `92d4382ed70d019d7bb2482e8b6ad137788e5485`
- **作者**: LALA
- **日期**: 2017-02-02 11:19:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.2_updateSQL_Oracle.sql`

### 71. Q00-20170123002[內部]修正M-Cloud無法下載附件
- **Commit ID**: `db178ebc9ada56b92a2dcf9bc6fd9805076f318f`
- **作者**: LALA
- **日期**: 2017-01-23 18:29:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 72. Q00-20170120002 修正Vip功能其驗證授權人數計算方式。
- **Commit ID**: `d5105937715daca334eb04e4fecbcd20782705a1`
- **作者**: WenCheng
- **日期**: 2017-01-23 11:31:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ConnectedUserInfoListReader.java`

### 73. C01-20170119002 修正表單script清空的議題
- **Commit ID**: `ffa0d4969533e749a731f4330478a00cf22e902a`
- **作者**: Gaspard
- **日期**: 2017-01-20 18:17:42
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle_nextVersion.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`

### 74. 修改Email連結 Link 登入語系  如果Session存在，取Session語系，不存在則取瀏覽器語系
- **Commit ID**: `4fccae00bb6e859a736fbe4df9b35e24f3f0295f`
- **作者**: joseph
- **日期**: 2017-01-20 16:49:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 75. 修改Email link 登入語系 如果Session存在 取Session語系，不存在則取瀏覽器語系
- **Commit ID**: `5852fa1074d6fbd5ab50a6ba7ab92baa0ab21bbf`
- **作者**: joseph
- **日期**: 2017-01-20 16:48:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 76. A00-20170120001 重新撰寫removeNotOnlineUser排程邏輯。
- **Commit ID**: `7aa43fb021376351594740ae0bab79ad41b40630`
- **作者**: WenCheng
- **日期**: 2017-01-20 15:11:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`

### 77. Q00-20170120001[內部]待辦清單、待辦數目、通知數目效能調整。
- **Commit ID**: `3edc279ef02c281852d8de6190a4bfe04a5776c6`
- **作者**: LALA
- **日期**: 2017-01-20 14:28:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`

### 78. C01-20161222003使用maillink進到簽核畫面後，但該表單先不進行簽核，使用待辦功能選取其他張表單進行簽核後，不是跳到下一筆表單，反而是跑出最上筆的表單(有設定簽核後至下一個工作項目(nextWorkItem))
- **Commit ID**: `e8fc2748fb43630c261fbc7b209951ba86332a57`
- **作者**: joseph
- **日期**: 2017-01-20 14:15:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 79. A00-***********[欣興]修正設定排程每日執行且首次時間設定在中午12點，但該排程不正常執行的異常問題
- **Commit ID**: `1b6a7e9db06af98218343157c7e90736b6c71dea`
- **作者**: LALA
- **日期**: 2017-01-19 12:58:56
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/lib/Quartz/quartz.jar`
  - 📝 **修改**: `3.Implementation/subproject/domain/lib/Quartz/quartz.jar`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/schedule/SystematicJob.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Quartz/quartz.jar`

### 80. A00-*********** 修正:關卡處理者為群組時，解析處理者失敗。
- **Commit ID**: `f6a6a0151562ce74f6e43f51a1269fa90d3e32a5`
- **作者**: joseph
- **日期**: 2017-01-18 09:51:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 81. A00-*********** 修正:點入系統管理工具-SQL註冊器出現無法取得EJB服務DB為ORACLE
- **Commit ID**: `7ea8d45675c003f2289e2536d0a83918fd90c956`
- **作者**: joseph
- **日期**: 2017-01-18 09:36:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java`

### 82. C01-20161202002[建邦精密]修正企業流程監控頁面，日期控件中月份翻頁按鈕顯示異常。
- **Commit ID**: `48ed4d74f0f19e86415ef18e0063468a13e48dfb`
- **作者**: LALA
- **日期**: 2017-01-16 17:47:05
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-bg_diagonals-thick_18_b81900_40x40.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-bg_diagonals-thick_20_666666_40x40.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-bg_glass_100_f6f6f6_1x400.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-bg_glass_100_fdf5ce_1x400.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-bg_glass_65_ffffff_1x400.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-bg_gloss-wave_35_f6a828_500x100.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-bg_highlight-soft_100_eeeeee_1x100.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-bg_highlight-soft_75_ffe45c_1x100.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-icons_222222_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-icons_228ef1_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-icons_ef8c08_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-icons_ffd27a_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/images/ui-icons_ffffff_256x240.png`

### 83. Q00-20170105003使用者從待辦事項中，點選一張單據，切換到流程圖頁簽，點選關卡的「發送通知」，成功後再切換回到表單內容頁簽時，會報錯。
- **Commit ID**: `4a5ca2d43361d83de6897875087d35aba309cb40`
- **作者**: joseph
- **日期**: 2017-01-13 16:59:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ForwardNotificationAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 84. 增加使用SQL註冊器時的query與update語法說明
- **Commit ID**: `8ebed340d23f15b34318415651c813cf13db2b77`
- **作者**: Gaspard
- **日期**: 2017-01-13 14:14:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`

### 85. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `7c76ab84b85fda9e18fd81369ac636b765905253`
- **作者**: LALA
- **日期**: 2017-01-13 11:55:21
- **變更檔案數量**: 0

### 86. C01-20170110001[誼山新]修正T100因通知信夾帶附件異常造成發起流程失敗
- **Commit ID**: `6c765b9e393e9df4369307534211b941dc2cf842`
- **作者**: LALA
- **日期**: 2017-01-13 11:54:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 87. 微信歸戶優化:部分修改
- **Commit ID**: `2c1067f8179c97999a9b9ed38bd7018336b1b77f`
- **作者**: Joe
- **日期**: 2017-01-13 11:47:31
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 88. S00-*********** 修正 mail link 進入BPM系統，預設語系選項與直接開啟browser進入BPM系統不一致；請設定為一致
- **Commit ID**: `717d96ab8a62f56c4eeab3cdf1397f812ec8f916`
- **作者**: joseph
- **日期**: 2017-01-12 15:06:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 89. A00-20161220001、A00-20161220002[紅崴]修正篩選流程分類或流程名稱，資料筆數異常。
- **Commit ID**: `d2dfc441e2f678ececdec760f0ff37fe341d437e`
- **作者**: LALA
- **日期**: 2017-01-11 16:06:10
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 90. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f34b9c2de98169625e1aeb20a8cc4582fee61115`
- **作者**: WenCheng
- **日期**: 2017-01-11 15:12:15
- **變更檔案數量**: 0

### 91. A00-20170111003 修正當客戶整合標準SP7產品時，預設為另開新視窗功能。
- **Commit ID**: `bbac2f5ae65aa855f83ea00dd85a3fea19768c5b`
- **作者**: WenCheng
- **日期**: 2017-01-11 15:11:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`

### 92. Q00-20170110001 新增工作排程的清單畫面應該要顯示間隔時間
- **Commit ID**: `d3f84fee364310066eda47781cc50bdf72a1c540`
- **作者**: joseph
- **日期**: 2017-01-11 13:35:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`

### 93. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a35a784aeeb1f91b84be91470470089c15fd3b60`
- **作者**: Joe
- **日期**: 2017-01-10 09:53:30
- **變更檔案數量**: 0

### 94. BPM APP新UI第二階段:改善部分UI
- **Commit ID**: `4adea690b01af2944ac53487cf5a8a8a0621cd45`
- **作者**: Joe
- **日期**: 2017-01-10 09:53:07
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileContactUserAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileWeChatService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 95. 修正BPMAPP中的產品與客製開窗，點擊畫面向左或向右滑會出現loading問題
- **Commit ID**: `6455b2f042dfdd15a2fc0e49506e77902f3a894d`
- **作者**: pinchi_lin
- **日期**: 2017-01-10 09:41:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileProductOpenWin.js`

### 96. A00-20161122001[內部]修正從上下筆按鈕進入已接受的工作事項，會跳出無法接收此工作的失敗訊息
- **Commit ID**: `a18f72efdbc06a16b3801fedf9ec9a08f2a30bb3`
- **作者**: LALA
- **日期**: 2017-01-09 17:39:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 97. APP新UI第二階段 待辦流程(類別與名稱)過濾顯示筆數-修正顯示筆數與多語系
- **Commit ID**: `e0c74d3bf00b2f9306b486375627f99b5ab5eaf4`
- **作者**: pinchi_lin
- **日期**: 2017-01-09 14:57:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`

### 98. 簡化微信歸戶流程-多語系修正
- **Commit ID**: `769c78da37ed2d9198d619a2bf91051d4fde3aeb`
- **作者**: pinchi_lin
- **日期**: 2017-01-06 17:04:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 99. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `329d9ababed57cc3bb4e9740d17d4b5c46e08a57`
- **作者**: LALA
- **日期**: 2017-01-06 16:35:22
- **變更檔案數量**: 0

### 100. Q00-20170106002[內部]修正模組程式維護修改資料會造成連線URL增加多個amp;使得連結錯誤
- **Commit ID**: `44f219f851201d40d2caa58b225930cf49478471`
- **作者**: LALA
- **日期**: 2017-01-06 16:34:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`

### 101. 簡化微信歸戶流程-修正立即同步BUG
- **Commit ID**: `3cc8b02d3a26eaa8e5bcb715070ad6f39cf081d4`
- **作者**: pinchi_lin
- **日期**: 2017-01-06 16:33:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`

### 102. Q00-20170105010 修正:工作排程點選"立即執行"後，沒有跳出任何成功或完成的訊息，無法判別是否有作用
- **Commit ID**: `aafdbd2079b487bd67b596d1fdbff39a9b05b778`
- **作者**: joseph
- **日期**: 2017-01-06 16:27:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SystemScheduleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`

### 103. Q00-20170105008 & Q00-20170105009 修正追蹤流程>發起的流程或處理的流程>選擇三個月內>選擇"所有可追蹤的流程">選擇某個流程分類>報錯(此問題可於Emma機器重現)
- **Commit ID**: `f67c76941b594f69d346c0d1199c3667e6e18f52`
- **作者**: joseph
- **日期**: 2017-01-06 16:22:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 104. Q00-20170105001 修正追蹤流程->任選目前處理者(進階)->選擇「已關閉的流程」或「所有流程」都無效
- **Commit ID**: `bd7d53e86572627435991a406b352a29b418a94c`
- **作者**: joseph
- **日期**: 2017-01-06 16:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 105. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `2ef5a051810b04926d6afec6f560218b9f7a717a`
- **作者**: joseph
- **日期**: 2017-01-06 16:12:49
- **變更檔案數量**: 0

### 106. Q00-20170105001 修正追蹤流成->任選目前處理者(進階)->選擇「已關閉的流程」或「所有流程」都無效
- **Commit ID**: `3fb1cccdbec97d175ad5736639349dde53509c0b`
- **作者**: joseph
- **日期**: 2017-01-06 16:11:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 107. Q00-20170105001 修正追蹤流程->任選目前處理者(進階)->選擇「已關閉的流程」或「所有流程」都無效
- **Commit ID**: `2bca613e47d2c931bbdb7b5d4f3d244045ebf263`
- **作者**: joseph
- **日期**: 2017-01-06 16:08:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 108. Q00-***********[內部]修正簡易查詢搜尋編號為特殊符號(% 和 _)時會找不到文件
- **Commit ID**: `72249ac08f42e9471da61004cc0ee7c2e09afa71`
- **作者**: LALA
- **日期**: 2017-01-06 16:01:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 109. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `51fd6e40e879f4dc0a3a9ba2e5968c1b13d81c32`
- **作者**: wayne
- **日期**: 2017-01-06 15:28:53
- **變更檔案數量**: 0

### 110. S00-20170105002 調整為顯示所有分類
- **Commit ID**: `051feff90599705678a14a0dea0167e9080d436f`
- **作者**: wayne
- **日期**: 2017-01-06 15:28:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 111. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `871b97f74f193b1819312e82e6d366dba6d0f0ce`
- **作者**: LALA
- **日期**: 2017-01-06 12:04:00
- **變更檔案數量**: 0

### 112. Q00-20170105004[內部]新版流程圖點重啟invoke關卡後異常
- **Commit ID**: `5ca05c8a506f1559541ddfbe9e50216b96e3eb47`
- **作者**: LALA
- **日期**: 2017-01-06 11:32:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`

### 113. Q00-20170105006  增加ISO dailyJob
- **Commit ID**: `59a9d5bbf59ec8e772f07e5ad383b5cb1446c704`
- **作者**: wayne
- **日期**: 2017-01-06 11:00:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaJobs.xml`

### 114. 增加流程設計師的update SQL
- **Commit ID**: `c2f618ac7ec8b8d70c8724aaaa94347193c8c474`
- **作者**: wayne
- **日期**: 2017-01-06 10:57:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 115. A00-20161228001[杰發]新關卡接舊有連接線造成流程異常。
- **Commit ID**: `eaf9908ce63b59a862ceb0123ebbc975028a86ff`
- **作者**: LALA
- **日期**: 2017-01-05 17:11:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 116. 修正於客製JSP上無法使用客製開窗的問題
- **Commit ID**: `64d098eb906206c785e043709dbf71b724d0adcf`
- **作者**: Gaspard
- **日期**: 2017-01-05 16:41:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 117. Q00-20161230001 修正組織設計師在進入修改員工資料，設定員工離職時間是未來時間，確認後再次開啟，未來離職時間會消失。
- **Commit ID**: `fc0de3e8f0258130ac4bd0b5740a199effb49494`
- **作者**: joseph
- **日期**: 2017-01-05 14:00:49
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/OrgMemberListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`

### 118. S00-20161228001 修正工作行事曆中，新增或修改適用日期及特殊工作日，如果是過去時間，無法變更。
- **Commit ID**: `499cb358c0e3be09b72744320a930e4392c97ae4`
- **作者**: joseph
- **日期**: 2017-01-05 13:47:02
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/view/dialog/DurationDateChooserDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/calendar/WorkCalendarEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/WorkCalendarEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/WorkCalendarEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/WorkCalendarEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/WorkCalendarEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/WorkCalendarEditor_zh_TW.properties`

### 119. APP新UI第二階段多語系敘述調整
- **Commit ID**: `6b3992ceefd2219ca9bce17413cd28408daaaea2`
- **作者**: Joe
- **日期**: 2017-01-05 10:23:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 120. BPM APP新UI第二階段 補上遺漏的追蹤流程的獨立顯示流程
- **Commit ID**: `0d9ac23bf26460e712949526eea73e3e12007efb`
- **作者**: Joe
- **日期**: 2017-01-04 17:37:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`

### 121. 微信歸戶優化微調
- **Commit ID**: `84e0ff75e17c46e1d8c1345240e2875fff92d45e`
- **作者**: Joe
- **日期**: 2017-01-04 15:28:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 122. APP新UI第二階段 待辦流程(類別與名稱)過濾顯示筆數
- **Commit ID**: `21fa0c16f69740def5ba9e171c60364e90a6cb7e`
- **作者**: pinchi_lin
- **日期**: 2017-01-04 14:54:09
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbstractPageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListResultsTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 123. S00-20161121001 新功能需求-BPM服務調整(取代T100 awsi013),邏輯修改 1.支援預設流程,作業不分單別流程,作業不分據點流程,營運據點+作業代號+單別流程 (FormFlowCreate&FormFlowDelete) 2.取得流程清單 (ProcessListGet)為支援1之修改 3.FormFlowCreate服務所產生的流程名稱修改
- **Commit ID**: `c50f2cc3d938e04411a04a9ceea152dc2a7c6c6f`
- **作者**: jerry1218
- **日期**: 2017-01-04 10:59:40
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/SecurityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IProcessPackageCmItemDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBProcessPackageCmItemDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 124. APP新UI第二階段 待辦流程過濾新方法
- **Commit ID**: `46d04f35ed802ed091c73b04643123eb85f4a0fd`
- **作者**: Joe
- **日期**: 2017-01-03 16:42:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 125. BPM APP 新UI第二階段:顯示流程圖獨立並置中
- **Commit ID**: `840f54bb73517eb80ae75be87d0b849dcfa92971`
- **作者**: Joe
- **日期**: 2017-01-03 11:07:27
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp`

### 126. 修正BPMAPP，ESS表單的grid多一格欄位問題
- **Commit ID**: `c8c7529049342b5f4fbe5384b5a58f177641d932`
- **作者**: pinchi_lin
- **日期**: 2017-01-03 10:10:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 127. A00-20161215002 修正1.並簽後流程圖無法開啟問題 , 2.將流程內容-檢視完整流程資訊的Route大小調小
- **Commit ID**: `3492365c83f3c500b8dca702061b3ec7e3a08910`
- **作者**: jerry1218
- **日期**: 2016-12-30 10:19:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 128. C01-20161007001[台灣精星]修正ISO文件生失效造成類別樹異常
- **Commit ID**: `3c0f9118c24c5bd89ba7ce57f91b49ecced88a66`
- **作者**: LALA
- **日期**: 2016-12-30 09:41:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/bakjob/ISOAutoJobDaoWorker.java`

### 129. BPM APP新UI 簽和歷程畫面修改
- **Commit ID**: `7f5b2c75ae2ad4bffe27e5b07d90cca2832260a3`
- **作者**: Joe
- **日期**: 2016-12-29 16:47:19
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 130. BPM APP 新UI 附件列表樣式修改
- **Commit ID**: `b3d3c71a7bb16b1e04a46d5d2ac8ba2939301cc5`
- **作者**: Joe
- **日期**: 2016-12-29 14:44:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 131. BPMAPP新UI-附件下載列表輸出基本樣式
- **Commit ID**: `c7fd264691b7b3477f4190602acd29e4a6f79d93`
- **作者**: pinchi_lin
- **日期**: 2016-12-29 14:24:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 132. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `c0a9fda2d47f25055863d66aed086475c3663858`
- **作者**: Joe
- **日期**: 2016-12-29 13:44:01
- **變更檔案數量**: 0

### 133. 微信歸戶優化 EXCEL開窗修改
- **Commit ID**: `d48a21181060f3e8b72b2a17ccd8f053c316a010`
- **作者**: Joe
- **日期**: 2016-12-29 10:24:43
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileExcelImporter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 134. 修正ESS表單用APP開啟時，gird內欄位顯示問題
- **Commit ID**: `cf25f9d9768772db29a46073454c7e56c4e885c7`
- **作者**: pinchi_lin
- **日期**: 2016-12-28 17:56:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 135. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `615611259abed3a6a0516352d75cb9c3ddfa4274`
- **作者**: wayne
- **日期**: 2016-12-28 17:16:35
- **變更檔案數量**: 0

### 136. S00-20161122003 調整匯入流程及新建流程的有效日期(2100/12/31)
- **Commit ID**: `92f9b672f113f0af60a67ba91579c1b2c476979d`
- **作者**: wayne
- **日期**: 2016-12-28 17:15:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/action/OpenFromXMLAction.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/DomainObjectFactory.java`

### 137. S00-*********** 新增進入ESSPlus管理作業類程式，會將該程式的權限值，在XML組成傳遞過去的功能。
- **Commit ID**: `9025ab33f483b8c4e0fc3bbe1e62ec933099cdbd`
- **作者**: joseph
- **日期**: 2016-12-28 10:13:10
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProgramDefManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/appform/AppFormKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/appform_module/AppFormModuleAction.java`

### 138. 微信歸戶優化 新增多語系
- **Commit ID**: `15d6cbe989cc6733a88262eed511746b0cf33abd`
- **作者**: Joe
- **日期**: 2016-12-27 18:48:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 139. 微信歸戶優化-MERGE CODE
- **Commit ID**: `3bc6a2163e29e4b1e2d80212135d49d70972772c`
- **作者**: Joe
- **日期**: 2016-12-27 15:33:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 140. 微信歸戶優化修改
- **Commit ID**: `470df304b89710ccfbb7877118bce2918186d0e6`
- **作者**: Joe
- **日期**: 2016-12-27 14:11:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`

### 141. 簡化微信歸戶流程-修正查詢撈取資料方式與同步資訊顯示
- **Commit ID**: `3a11e7201398e959ea1653037f52f5d48a8382f4`
- **作者**: pinchi_lin
- **日期**: 2016-12-27 14:01:28
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`

### 142. 修正merge錯誤
- **Commit ID**: `18631973bd79e423bc398280f48929245c7f7583`
- **作者**: LALA
- **日期**: 2016-12-26 17:13:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 143. 修正merge錯誤
- **Commit ID**: `eb55413d8231c902ea51066bbab283edbcb4b861`
- **作者**: LALA
- **日期**: 2016-12-26 17:11:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 144. A00-20161215001[互動寬頻]因舊表單CSS屬性值有異常，版更5611表單無法正確載入
- **Commit ID**: `c40f75a95f7dd51714b3da147045c76f91b2bd2a`
- **作者**: LALA
- **日期**: 2016-12-26 17:08:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 145. A00-20161006001[良維]原RCP指令有誤，造成TIPTOP夾有附件的單據送簽失敗，將俊宏哥修正程式簽入
- **Commit ID**: `b83733f064841eb5658d8d9a82ab9c67eac677b2`
- **作者**: LALA
- **日期**: 2016-12-26 11:35:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/TiptopUtil.java`

### 146. T100新增支援送簽單據asft801,apst300   修改aint311
- **Commit ID**: `13405b08aa9845637e9d2dda56d33983b3ee4ed2`
- **作者**: jerry1218
- **日期**: 2016-12-26 11:20:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\243\275\347\250\213\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(asft801).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\272\253\345\255\230\345\240\261\345\273\242\351\231\244\345\270\263(aint311).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\215\250\347\253\213\351\234\200\346\261\202\347\266\255\350\255\267\344\275\234\346\245\255(apst300).form"`

### 147. 使TextArea不可設計其欄位屬性，強制只能使用字串
- **Commit ID**: `d0e52daaed58c19f2de62fc2cab17e945bb7d0a6`
- **作者**: Gaspard
- **日期**: 2016-12-23 16:29:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 148. 修正舊有表單中元件cssStyle可能是color:#;和background-color:#;
- **Commit ID**: `cf4eb1c3dd07326c4d6784fc1db70b3b8c08ee12`
- **作者**: Gaspard
- **日期**: 2016-12-22 20:22:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 149. 微信歸戶優化-部分BUG修正
- **Commit ID**: `0a1b8f0bed3eefaeb2f02b872e283e7a7a2e671f`
- **作者**: Joe
- **日期**: 2016-12-22 18:22:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 150. 微信歸戶優化
- **Commit ID**: `ad3597b864b5cc5d89c5c23cc7a0ceaf75602685`
- **作者**: Joe
- **日期**: 2016-12-22 17:18:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 151. 微信歸戶優化
- **Commit ID**: `49d5639566668b2472831f448fb1d1973740d124`
- **作者**: Joe
- **日期**: 2016-12-22 16:36:08
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 152. 簡化微信歸戶流程-產生網址與連線驗證功能修改
- **Commit ID**: `af197179b56d0ca919307ad0ec9cfbfbc82e258c`
- **作者**: pinchi_lin
- **日期**: 2016-12-22 16:08:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java`

### 153. 簡化微信歸戶流程功能
- **Commit ID**: `7853c1ecd1f57c55309f5d68bab613ad11c7a63f`
- **作者**: pinchi_lin
- **日期**: 2016-12-22 10:53:10
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatSchedule.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/dataformat/MobileDataTrasnferService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 154. 修正表單定義中的元件在新表單設計師開啟時，某些元件會是黑底
- **Commit ID**: `b98942850cb46b3f52a41fd9ee0edd4c270f760f`
- **作者**: Gaspard
- **日期**: 2016-12-21 10:09:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_updateSQL_SQLServer.sql`

### 155. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `170336986fafba6f92496ec0e8f0a1dcd0bf0857`
- **作者**: wayne
- **日期**: 2016-12-20 18:23:11
- **變更檔案數量**: 0

### 156. 修正開啟追蹤流程-發起的流程SQL錯誤
- **Commit ID**: `d3c99761eed6ccb451af898fd2c9ab9276bfd523`
- **作者**: wayne
- **日期**: 2016-12-20 18:22:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 157. TableSchema整理(From v5.6.2.1)
- **Commit ID**: `9b8a1e87ffb9b79e54ba4e89027ad42423d21aa6`
- **作者**: arielshih
- **日期**: 2016-12-20 15:22:00
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - ➕ **新增**: `9.TableSchemaERM/APP.erm`
  - ➕ **新增**: `9.TableSchemaERM/Attachment.erm`
  - ➕ **新增**: `9.TableSchemaERM/Bam.erm`
  - ➕ **新增**: `9.TableSchemaERM/Document.erm`
  - ➕ **新增**: `9.TableSchemaERM/Form.erm`
  - ➕ **新增**: `9.TableSchemaERM/ISO.erm`
  - ➕ **新增**: `9.TableSchemaERM/Integration.erm`
  - ➕ **新增**: `9.TableSchemaERM/Module.erm`
  - ➕ **新增**: `9.TableSchemaERM/Organization.erm`
  - ➕ **新增**: `9.TableSchemaERM/Others.erm`
  - ➕ **新增**: `9.TableSchemaERM/WorkFlow_Def.erm`
  - ➕ **新增**: `9.TableSchemaERM/WorkFlow_Engine.erm`
  - ➕ **新增**: `9.TableSchemaERM/audit_data.erm`
  - ➕ **新增**: `9.TableSchemaERM/notification.erm`

### 158. 修正多選開窗情境，在已有資料的情況下開窗時會造成已選取的資料因為「排序」欄位而亂掉
- **Commit ID**: `19397e7556abe1f933d424c1504cd25bc8b24be6`
- **作者**: Gaspard
- **日期**: 2016-12-20 13:37:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

