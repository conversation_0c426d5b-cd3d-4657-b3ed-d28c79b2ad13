# 🛠️ BPM Easy Tools

> **BPM 服務部好用工具集** - 提升開發效率的一站式解決方案

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Streamlit](https://img.shields.io/badge/Streamlit-1.47.0+-red.svg)](https://streamlit.io/)
[![License](https://img.shields.io/badge/License-Internal-green.svg)]()
[![Version](https://img.shields.io/badge/Version-1.0.0-orange.svg)]()

## 📋 專案概述

BPM Easy Tools 是專為 BPM 服務部開發的綜合性工具集，提供直觀的 Web 介面來簡化日常開發和維護工作。透過 Streamlit 框架構建，支援多種實用功能，幫助團隊提升工作效率。

### 🎯 核心功能

- **📊 產品 Release 記錄查詢工具**
  - 支援 BPM、BPM-ISO、NaNaXWeb 專案的 release 記錄查詢
  - 依照 branch_name 瀏覽 commit 記錄
  - 關鍵字搜尋 commit 訊息
  - 檔案名稱搜尋功能
  - 支援排序與分類顯示

- **🔍 檔案索引路徑查詢工具**
  - 查詢 class、jsp、js 檔案位置
  - 支援關鍵字模糊搜尋
  - 上傳壓縮檔案批量查詢
  - 顯示檔案修改時間
  - 多版本索引支援

## 🚀 快速開始

### 系統需求

- **作業系統**: Windows 10/11
- **Python**: 3.8 或以上版本
- **記憶體**: 建議 4GB 以上
- **磁碟空間**: 至少 500MB 可用空間

### 安裝步驟

#### 方法一：自動安裝（推薦）

1. **下載專案檔案**
   ```bash
   # 解壓縮專案檔案到目標目錄
   # 例如：D:\bpm_easy_tools\
   ```

2. **執行環境設定**
   ```cmd
   # 雙擊執行或在命令提示字元中執行
   setup_environment.cmd
   ```

3. **啟動應用程式**
   ```cmd
   # 雙擊執行或在命令提示字元中執行
   start_application.cmd
   ```

#### 方法二：手動安裝

1. **建立虛擬環境**
   ```bash
   python -m venv venv
   venv\Scripts\activate
   ```

2. **安裝相依套件**
   ```bash
   pip install -r requirements.txt
   ```

3. **啟動應用程式**
   ```bash
   streamlit run streamlit_home.py --server.port 8888
   ```

### 🌐 存取應用程式

安裝完成後，應用程式將在以下位址啟動：

- **本機存取**: http://localhost:8888
- **網路存取**: http://[您的IP位址]:8888

## 📁 專案結構

```
bpm_easy_tools/
├── 📄 streamlit_home.py          # 主應用程式入口
├── 📄 requirements.txt           # Python 相依套件清單
├── 📄 setup_environment.cmd      # 環境設定腳本
├── 📄 start_application.cmd      # 應用程式啟動腳本
├── 📄 deploy_to_target.py        # 部署工具
├── 📄 create_deployment_package.py # 打包工具
├── 📁 pages/                     # Streamlit 頁面
│   ├── 📄 release_query.py       # Release 記錄查詢頁面
│   └── 📄 file_search.py         # 檔案搜尋頁面
├── 📁 config/                    # 設定檔案
│   └── 📄 projects_config.json   # 專案設定
├── 📁 tools/                     # 工具腳本
│   ├── 📄 smart_branch_diff.py   # 分支差異分析
│   ├── 📄 generate_deployment_index.py # 部署索引生成
│   ├── 📄 fetch_customer_data.py # 客戶資料自動化擷取工具
│   └── 📄 README_customer_data_fetch.md # 客戶資料擷取工具說明
├── 📁 data_output/               # 資料輸出目錄
│   ├── 📁 bpm_path/             # 檔案路徑索引
│   └── 📁 bpm_release/          # Release 記錄
├── 📁 .streamlit/                # Streamlit 設定
│   └── 📄 config.toml           # 應用程式設定檔
└── 📁 venv/                      # Python 虛擬環境
```

## ⚙️ 設定說明

### 專案設定檔 (config/projects_config.json)

```json
{
  "projects": {
    "BPM": {
      "name": "BPM 主專案",
      "repo_path": "D:\\IDEA_workspace\\BPM",
      "branch_patterns": {
        "include_starts_with": ["release_", "hotfix_"],
        "include_ends_with": [],
        "exclude_branches": ["develop_v58"]
      }
    }
  }
}
```

### 主要設定項目

- **repo_path**: 專案儲存庫路徑
- **branch_patterns**: 分支篩選規則
- **exclude_branches**: 排除的分支清單

## 🔧 使用指南

### 產品 Release 記錄查詢

1. 在主頁面點擊「📊 產品Release記錄查詢工具」
2. 選擇要查詢的專案（BPM、BPM-ISO、NaNaXWeb）
3. 選擇分支或使用搜尋功能
4. 查看 commit 記錄和相關資訊

### 檔案索引路徑查詢

1. 在主頁面點擊「🔍 檔案索引路徑查詢工具」
2. 輸入檔案名稱或關鍵字進行搜尋
3. 或上傳壓縮檔案進行批量查詢
4. 查看搜尋結果和檔案路徑資訊

## 🛠️ 開發指南

### 相依套件

主要相依套件清單：

```txt
streamlit>=1.47.0          # Web 應用框架
python-dotenv>=1.0.0       # 環境變數管理
rarfile>=4.2               # RAR 檔案處理
st_file_uploader>=0.2.2    # 檔案上傳元件
pandas>=2.0.0              # 資料處理
```

### 開發環境設定

1. **複製專案**
   ```bash
   git clone [repository-url]
   cd bpm_easy_tools
   ```

2. **建立開發環境**
   ```bash
   python -m venv venv
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **執行開發伺服器**
   ```bash
   streamlit run streamlit_home.py --server.port 8888
   ```

### 程式碼結構

- **streamlit_home.py**: 主頁面，包含工具導航和系統資訊
- **pages/**: 各功能頁面模組
- **tools/**: 後端工具和腳本
- **config/**: 設定檔案和參數

## 📦 部署說明

### 建立部署套件

```bash
python create_deployment_package.py
```

此命令會建立包含所有必要檔案的部署套件，排除開發相關檔案。

### 部署到目標主機

1. 將部署套件複製到目標主機
2. 執行 `setup_environment.cmd` 進行環境設定
3. 執行 `start_application.cmd` 啟動應用程式

## 🔍 疑難排解

### 常見問題

**Q: 應用程式無法啟動**
- 檢查 Python 版本是否為 3.8 以上
- 確認虛擬環境已正確建立
- 檢查 8888 埠是否被其他程式佔用

**Q: 找不到資料檔案**
- 確認 `data_output/bpm_path` 和 `data_output/bpm_release` 目錄存在
- 檢查設定檔中的路徑是否正確

**Q: 網路存取問題**
- 檢查防火牆設定
- 確認網路連線正常
- 驗證 IP 位址設定

### 日誌檢查

應用程式執行時會在終端機顯示詳細日誌，包括：
- 啟動狀態
- 錯誤訊息
- 存取記錄

## 📞 支援與聯絡

- **開發團隊**: BPM 服務部
- **版本**: 1.0.0
- **最後更新**: 2025年7月

如有問題或建議，請聯繫開發團隊。

## 📄 授權資訊

本專案為內部使用工具，僅供 BPM 服務部團隊使用。

---

*© 2025 BPM 服務部 - 版權所有*
