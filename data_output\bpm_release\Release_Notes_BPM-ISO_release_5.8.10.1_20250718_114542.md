# Release Notes - BPM-ISO

## 版本資訊
- **新版本**: release_5.8.10.1
- **舊版本**: release_5.8.9.4
- **生成時間**: 2025-07-18 11:45:42
- **新增 Commit 數量**: 43

## 變更摘要

### 邱郁晏 (18 commits)

- **2024-03-26 16:59:30**: [Secudocx] V00-20240326005 修正ISO攜出段，若為PDF加密時，會重複加密導致攜出失敗。
  - 變更檔案: 1 個
- **2024-03-26 16:36:43**: [Secudocx] V00-20240326004 修正以柔ISO攜出段，批次下載沒有加密
  - 變更檔案: 1 個
- **2024-03-26 14:44:55**: [Secudocx] V00-20240326003 新增以柔轉檔卡控副檔名
  - 變更檔案: 5 個
- **2024-03-26 13:55:27**: [Secudocx] V00-20240326002 修正以柔加密，ISO攜出段無下載加密問題
  - 變更檔案: 2 個
- **2024-03-26 09:11:47**: [ISO] Q00-20240326001 修正刪除人員類別權限改為使用truncate
  - 變更檔案: 1 個
- **2024-03-21 13:49:58**: [ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題(補修正)
  - 變更檔案: 1 個
- **2024-03-19 18:26:14**: [SecuDocx] 以柔整合調整寫法
  - 變更檔案: 3 個
- **2024-03-19 11:22:06**: [ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題
  - 變更檔案: 1 個
- **2024-03-05 15:26:13**: Merge branch 'DS'
- **2024-03-05 15:25:28**: [SecuDox] 替換nana-services.jar
  - 變更檔案: 1 個
- **2024-02-20 17:29:29**: [SecuDox] 新增ISO段下載發布檔
  - 變更檔案: 5 個
- **2024-01-29 17:49:38**: [ISO] V00-20240111001 修正PDF預覽浮水印缺少WordStyleValue屬性問題(補)
  - 變更檔案: 1 個
- **2024-01-11 15:09:12**: [ISO] V00-20240111001 修正PDF預覽浮水印缺少WordStyleValue屬性問題
  - 變更檔案: 3 個
- **2023-12-11 14:39:56**: [附件擴充] PDF轉檔機制調整
  - 變更檔案: 1 個
- **2023-12-08 15:36:20**: [ISO] S00-20231026002 新增鼎新轉檔工具上傳PNG功能(補)
  - 變更檔案: 1 個
- **2023-12-07 16:29:23**: [ISO] S00-20231026002 新增鼎新轉檔工具上傳PNG功能
  - 變更檔案: 4 個
- **2023-12-05 15:26:01**: [附件擴充] 修正參數未開啟時判斷邏輯異常問題
  - 變更檔案: 4 個
- **2023-11-27 09:59:57**: [ISO] Q00-20231124006 修正評審規則觸發變更單InputLabel_Author欄位空值問題
  - 變更檔案: 1 個

### 林致帆 (12 commits)

- **2024-03-20 15:23:46**: [ISO]V00-20240320001 修正ISO文件新增單，變更單儲存表單失敗
  - 變更檔案: 2 個
- **2024-03-19 11:33:05**: [ISO]V00-20240319001 修正閱讀浮水印未輸入文字造成文字滿版效果報錯
  - 變更檔案: 1 個
- **2024-03-19 10:53:52**: [ISO]V00-20240313003 修正文管，新增單生效日期有填寫的狀況，儲存時會報"請填寫生效日期'
  - 變更檔案: 1 個
- **2024-03-13 18:09:00**: [ISO]V00-20240313003 修正文管，新增單生效日期有填寫的狀況，儲存時會報"請填寫生效日期'
  - 變更檔案: 1 個
- **2024-03-06 11:33:31**: [ISO]S00-20231006002 修正文件類別因群組資料異常造成開啟失敗
  - 變更檔案: 1 個
- **2024-02-19 13:43:50**: [ISO]S00-20230612002 文管首頁新增匯出Excel功能[補修正]
  - 變更檔案: 1 個
- **2024-02-16 10:36:20**: [ISO]S00-20230612002 文管首頁新增匯出Excel功能[補修正]
  - 變更檔案: 1 個
- **2024-02-16 10:26:32**: [ISO]S00-20230612002 文管首頁新增匯出Excel功能
  - 變更檔案: 3 個
- **2024-01-23 13:38:09**: [ISO]S00-20230725001 閱讀浮水印新增"旋轉角度"設定
  - 變更檔案: 1 個
- **2024-01-18 18:03:26**: Revert "[ISO]S00-20230627002 "ISO部門資訊批次更新"表單調整原部門可選取到失效部門"
  - 變更檔案: 1 個
- **2024-01-08 15:26:50**: [ISO]S00-20230627002 "ISO部門資訊批次更新"表單調整原部門可選取到失效部門
  - 變更檔案: 1 個
- **2023-12-28 14:53:48**: [ISO]S00-20230725001 閱讀浮水印新增"旋轉角度"設定
  - 變更檔案: 2 個

### waynechang (12 commits)

- **2024-03-11 16:24:21**: [ISO]Q00-20240311001 調整ISO變更單的描述欄位，當描述欄位(TextArea_DocAbstract)有換行符號時<br\>,畫面需以實際換行呈現
  - 變更檔案: 1 個
- **2024-03-07 15:01:53**: [ISO]S00-20230703001 ISO閱讀文件支持同時開啟多份閱讀文件[補]
  - 變更檔案: 3 個
- **2024-03-06 14:49:11**: [ISO]S00-20230703001 ISO閱讀文件支持同時開啟多份閱讀文件
  - 變更檔案: 4 個
- **2024-01-31 16:48:07**: [ISO]Q00-20231031002 修正ISO文件的制定單位或保管單位為群組時，組通知信件時會發生錯誤，導致生失效通知信無法寄送的異常[補]
  - 變更檔案: 1 個
- **2024-01-24 15:41:34**: [ISO]Q00-20240124002 調整ISO閱讀PDF檔案的頁面的下載原始檔的URL網址由內網IP改為文件主機設定的webAddress，避免瀏覽器的同源政策導致檔案無法下載
  - 變更檔案: 1 個
- **2024-01-08 17:05:08**: [ISO]S00-20230602002 ISO文管首頁、ISO文件屬性管理增加「保管單位」的查詢條件
  - 變更檔案: 5 個
- **2024-01-08 11:44:59**: [ISO]A00-20240105001 修正文件類別管理，當類別權限包含專案時，若再次編輯權限儲存後，重新點擊類別會提示「取得文管權限失敗」的錯誤
  - 變更檔案: 1 個
- **2023-12-28 15:31:18**: [ISO]Q00-20231228001 增加ISO評審服務任務的log
  - 變更檔案: 2 個
- **2023-12-20 16:16:45**: [ISO]Q00-20231220007 取消ISOFile的sourceFileOID的強關聯，避免NoCmDocument被移除時，關聯ISOFile時會報錯
  - 變更檔案: 1 個
- **2023-12-20 16:07:23**: [ISO]Q00-20231220006 調整ISO索引卡，當文件為第一版時，隱藏「變更原因」欄位，其餘版本應顯示「變更原因」欄位
  - 變更檔案: 2 個
- **2023-12-18 15:40:42**: [ISO]Q00-20231218002 優化ISO-V8重新建立索引排程，增加相關log及非預期錯誤防呆
  - 變更檔案: 1 個
- **2023-11-29 17:52:16**: [ISO]A00-20231129001 修正使用者若組織資料包含專案時，在開啟ISO文管首頁或清單時，讀取會失敗的異常
  - 變更檔案: 1 個

### raven.917 (1 commits)

- **2024-03-08 08:41:26**: [SecuDocx] ISO段發布檔下載(補)
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [Secudocx] V00-20240326005 修正ISO攜出段，若為PDF加密時，會重複加密導致攜出失敗。
- **Commit ID**: `a8f642bec90c0ec15cda49affb353862adbe05c2`
- **作者**: 邱郁晏
- **日期**: 2024-03-26 16:59:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java`

### 2. [Secudocx] V00-20240326004 修正以柔ISO攜出段，批次下載沒有加密
- **Commit ID**: `7de1e798272d6b7bd97fc0913ff7e943f4655f12`
- **作者**: 邱郁晏
- **日期**: 2024-03-26 16:36:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java`

### 3. [Secudocx] V00-20240326003 新增以柔轉檔卡控副檔名
- **Commit ID**: `31a7beb9494b67fcedc0699f2a120d3055936fff`
- **作者**: 邱郁晏
- **日期**: 2024-03-26 14:44:55
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java`

### 4. [Secudocx] V00-20240326002 修正以柔加密，ISO攜出段無下載加密問題
- **Commit ID**: `9661d83c57a798f2d82e515e0fce7844e67014a4`
- **作者**: 邱郁晏
- **日期**: 2024-03-26 13:55:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java`

### 5. [ISO] Q00-20240326001 修正刪除人員類別權限改為使用truncate
- **Commit ID**: `713f2ea2e75c6696177c49a38d4ae47ad1d2949c`
- **作者**: 邱郁晏
- **日期**: 2024-03-26 09:11:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOAuthorityDaoImpl.java`

### 6. [ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題(補修正)
- **Commit ID**: `370c18a70267793cc06e0946ab7ccc394608d32f`
- **作者**: 邱郁晏
- **日期**: 2024-03-21 13:49:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/DocCategoryDaoImpl.java`

### 7. [ISO]V00-20240320001 修正ISO文件新增單，變更單儲存表單失敗
- **Commit ID**: `4abc2dff9f0ed6e2813ea784247e87fe55564820`
- **作者**: 林致帆
- **日期**: 2024-03-20 15:23:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOMod.js`

### 8. [SecuDocx] 以柔整合調整寫法
- **Commit ID**: `35d76859462df74cd265866e56faa3483f3204e3`
- **作者**: 邱郁晏
- **日期**: 2024-03-19 18:26:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 9. [ISO]V00-20240319001 修正閱讀浮水印未輸入文字造成文字滿版效果報錯
- **Commit ID**: `929c87c4fa2aab34868f389f37b0db8e57e526ae`
- **作者**: 林致帆
- **日期**: 2024-03-19 11:33:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/util/PDFBoxConverter.java`

### 10. [ISO] Q00-20240319002 修正ISO文件類別管理，類別筆數超過1000後查詢異常問題
- **Commit ID**: `8d24f22d2f412982d2563600d4fbdd0b62a4a510`
- **作者**: 邱郁晏
- **日期**: 2024-03-19 11:22:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/DocCategoryDaoImpl.java`

### 11. [ISO]V00-20240313003 修正文管，新增單生效日期有填寫的狀況，儲存時會報"請填寫生效日期'
- **Commit ID**: `08aa0d7a56c3b703b9f8b33baf1dd84150147dc6`
- **作者**: 林致帆
- **日期**: 2024-03-19 10:53:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`

### 12. [ISO]V00-20240313003 修正文管，新增單生效日期有填寫的狀況，儲存時會報"請填寫生效日期'
- **Commit ID**: `70f7fc145a466834f47941b6f3d7a914169bc95b`
- **作者**: 林致帆
- **日期**: 2024-03-13 18:09:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`

### 13. [ISO]Q00-20240311001 調整ISO變更單的描述欄位，當描述欄位(TextArea_DocAbstract)有換行符號時<br\>,畫面需以實際換行呈現
- **Commit ID**: `ee3df9ae7f88259cf42c50c9beeba8e0466843c2`
- **作者**: waynechang
- **日期**: 2024-03-11 16:24:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOMod.js`

### 14. [SecuDocx] ISO段發布檔下載(補)
- **Commit ID**: `48615812a98125683692cc8b73d672ee12fb41ac`
- **作者**: raven.917
- **日期**: 2024-03-08 08:41:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.html`

### 15. [ISO]S00-20230703001 ISO閱讀文件支持同時開啟多份閱讀文件[補]
- **Commit ID**: `29742eebdad44c2ff0ad7f01b04c7d07713aeb62`
- **作者**: waynechang
- **日期**: 2024-03-07 15:01:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp`

### 16. [ISO]S00-20230703001 ISO閱讀文件支持同時開啟多份閱讀文件
- **Commit ID**: `ec31cc412c774905e81f86dc1b0f139981c3f240`
- **作者**: waynechang
- **日期**: 2024-03-06 14:49:11
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.html`

### 17. [ISO]S00-20231006002 修正文件類別因群組資料異常造成開啟失敗
- **Commit ID**: `adfbd47ad48cf7328a6cb99362599a9606f670f4`
- **作者**: 林致帆
- **日期**: 2024-03-06 11:33:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/DocCategoryController.java`

### 18. Merge branch 'DS'
- **Commit ID**: `bb00f38b19a1f5c1ae3176b2f07706b50db5d20e`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 15:26:13
- **變更檔案數量**: 0

### 19. [SecuDox] 替換nana-services.jar
- **Commit ID**: `7a6cf538e0b49eaa208387593f268d8ea2151b68`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 15:25:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`

### 20. [SecuDox] 新增ISO段下載發布檔
- **Commit ID**: `c0143e73374ebc2e7e9d1bfd7c885187f01649cf`
- **作者**: 邱郁晏
- **日期**: 2024-02-20 17:29:29
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.html`
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.js`
  - 📝 **修改**: `ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 21. [ISO]S00-20230612002 文管首頁新增匯出Excel功能[補修正]
- **Commit ID**: `302c6a034d499f53672064bcd73a687d9bf37514`
- **作者**: 林致帆
- **日期**: 2024-02-19 13:43:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`

### 22. [ISO]S00-20230612002 文管首頁新增匯出Excel功能[補修正]
- **Commit ID**: `df68ada3bfdf83bc0ec05bf89b65f6b849dbf3ad`
- **作者**: 林致帆
- **日期**: 2024-02-16 10:36:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`

### 23. [ISO]S00-20230612002 文管首頁新增匯出Excel功能
- **Commit ID**: `9f86db0b6b0baaea975bd372aff703dfa8d7f6f3`
- **作者**: 林致帆
- **日期**: 2024-02-16 10:26:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ManageDocumentMgr.java`

### 24. [ISO]Q00-20231031002 修正ISO文件的制定單位或保管單位為群組時，組通知信件時會發生錯誤，導致生失效通知信無法寄送的異常[補]
- **Commit ID**: `2d851a922bcdb87030ad2e4e07c86c5957c6fb1f`
- **作者**: waynechang
- **日期**: 2024-01-31 16:48:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocMailMgr.java`

### 25. [ISO] V00-20240111001 修正PDF預覽浮水印缺少WordStyleValue屬性問題(補)
- **Commit ID**: `2569bfee0bb0718622590d3741da0b0844306a74`
- **作者**: 邱郁晏
- **日期**: 2024-01-29 17:49:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java`

### 26. [ISO]Q00-20240124002 調整ISO閱讀PDF檔案的頁面的下載原始檔的URL網址由內網IP改為文件主機設定的webAddress，避免瀏覽器的同源政策導致檔案無法下載
- **Commit ID**: `2a95331a669a72cdae256c1dbc1b7ecfd42bfb73`
- **作者**: waynechang
- **日期**: 2024-01-24 15:41:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.html`

### 27. [ISO]S00-20230725001 閱讀浮水印新增"旋轉角度"設定
- **Commit ID**: `a23627a5fa08e3f871419d5f35047a03ab30a689`
- **作者**: 林致帆
- **日期**: 2024-01-23 13:38:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/util/PDFBoxConverter.java`

### 28. Revert "[ISO]S00-20230627002 "ISO部門資訊批次更新"表單調整原部門可選取到失效部門"
- **Commit ID**: `2f005458e4453751882e6fccbb0f37b8b53e1a1d`
- **作者**: 林致帆
- **日期**: 2024-01-18 18:03:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISODeptBatchUpdate.js`

### 29. [ISO] V00-20240111001 修正PDF預覽浮水印缺少WordStyleValue屬性問題
- **Commit ID**: `80e8210e0781b5d4fb87e7285d38e92daa383a54`
- **作者**: 邱郁晏
- **日期**: 2024-01-11 15:09:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java`

### 30. [ISO]S00-20230602002 ISO文管首頁、ISO文件屬性管理增加「保管單位」的查詢條件
- **Commit ID**: `8021af9b450051899cae82de9e82274f91e30c5b`
- **作者**: waynechang
- **日期**: 2024-01-08 17:05:08
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePage.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOSearchCondictionKey.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/listreader/SearchCondiction.java`

### 31. [ISO]S00-20230627002 "ISO部門資訊批次更新"表單調整原部門可選取到失效部門
- **Commit ID**: `defa4936dcc15f96e2525513d8f536ad6be24a42`
- **作者**: 林致帆
- **日期**: 2024-01-08 15:26:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISODeptBatchUpdate.js`

### 32. [ISO]A00-20240105001 修正文件類別管理，當類別權限包含專案時，若再次編輯權限儲存後，重新點擊類別會提示「取得文管權限失敗」的錯誤
- **Commit ID**: `a2f55554399174997cde4ade2d477b9e26379748`
- **作者**: waynechang
- **日期**: 2024-01-08 11:44:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/DocCategoryController.java`

### 33. [ISO]Q00-20231228001 增加ISO評審服務任務的log
- **Commit ID**: `e4107a47c16c93a4cd2e704367dd2a308b2974fb`
- **作者**: waynechang
- **日期**: 2023-12-28 15:31:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISODocVettingRecordController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocVettingMgr.java`

### 34. [ISO]S00-20230725001 閱讀浮水印新增"旋轉角度"設定
- **Commit ID**: `3a3c0a662c38274b501f337617a8072caaebb983`
- **作者**: 林致帆
- **日期**: 2023-12-28 14:53:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/util/PDFBoxConverter.java`

### 35. [ISO]Q00-20231220007 取消ISOFile的sourceFileOID的強關聯，避免NoCmDocument被移除時，關聯ISOFile時會報錯
- **Commit ID**: `c2a25b9de8c1d5e34f522bfe0eaef632994fb384`
- **作者**: waynechang
- **日期**: 2023-12-20 16:16:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFile.hbm.xml`

### 36. [ISO]Q00-20231220006 調整ISO索引卡，當文件為第一版時，隱藏「變更原因」欄位，其餘版本應顯示「變更原因」欄位
- **Commit ID**: `d80901b3b086affc3a9108ae10c4c9eb0ba01211`
- **作者**: waynechang
- **日期**: 2023-12-20 16:07:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ReadDocumentInfo.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/UpdateDocumentInfo.jsp`

### 37. [ISO]Q00-20231218002 優化ISO-V8重新建立索引排程，增加相關log及非預期錯誤防呆
- **Commit ID**: `4b57f547fc5ebb16675fe8be1ced7eb1b036b07b`
- **作者**: waynechang
- **日期**: 2023-12-18 15:40:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 38. [附件擴充] PDF轉檔機制調整
- **Commit ID**: `90b3dfae9723539dcbf3a34b078064e8ff5274b4`
- **作者**: 邱郁晏
- **日期**: 2023-12-11 14:39:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 39. [ISO] S00-20231026002 新增鼎新轉檔工具上傳PNG功能(補)
- **Commit ID**: `650e50494a1768fea0616ecc735aa230057d0c84`
- **作者**: 邱郁晏
- **日期**: 2023-12-08 15:36:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp`

### 40. [ISO] S00-20231026002 新增鼎新轉檔工具上傳PNG功能
- **Commit ID**: `69d75e09b47d65fc95508663765854af2b2a19a8`
- **作者**: 邱郁晏
- **日期**: 2023-12-07 16:29:23
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkImagePattern.jsp`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOWatermarkPatternMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/util/ImageUtil.java`

### 41. [附件擴充] 修正參數未開啟時判斷邏輯異常問題
- **Commit ID**: `b4b0e116496389d03f31df18785f105f3075feb2`
- **作者**: 邱郁晏
- **日期**: 2023-12-05 15:26:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java`

### 42. [ISO]A00-20231129001 修正使用者若組織資料包含專案時，在開啟ISO文管首頁或清單時，讀取會失敗的異常
- **Commit ID**: `36a8a499fd06f4e70d49f0f07585b5727aea7117`
- **作者**: waynechang
- **日期**: 2023-11-29 17:52:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOAuthorityDaoImpl.java`

### 43. [ISO] Q00-20231124006 修正評審規則觸發變更單InputLabel_Author欄位空值問題
- **Commit ID**: `83c47a37355b856b0df302306ff5039773662c50`
- **作者**: 邱郁晏
- **日期**: 2023-11-27 09:59:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocVettingMgr.java`

