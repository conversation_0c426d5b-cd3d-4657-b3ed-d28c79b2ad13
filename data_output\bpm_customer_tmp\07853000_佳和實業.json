{"company_id": "07853000", "company_name": "佳和實業", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\07853000_佳和實業\\01客戶基本資料", "files": [{"filename": "佳和.txt", "raw_content": "VPN連線:(連線前建議先通知客戶)\r\nforti client: *************:50443\r\n帳戶:digiwind.ser\r\n密碼:man@CHG720\r\nos:administrator/admin#DSC2023\r\n-\r\n正式機:\r\n172.17.101.18\r\n\r\n測試機:\r\n172.17.101.19\r\n帳密:administrator/1234\r\n\r\nDB:\r\n172.17.101.18\r\n帳密:sa/D!g!w!n@2023\r\n", "structured_data": {"vpn連線": "(連線前建議先通知客戶)", "forti client": "*************:50443", "帳戶": "digiwind.ser", "password": "man@CHG720", "os": "administrator/admin#DSC2023", "帳密": "sa/D!g!w!n@2023", "host": "*************"}, "source_path": "C1.客戶維護相關\\07853000_佳和實業\\01客戶基本資料\\佳和.txt", "file_size": 283, "encoding_used": "utf-8", "processed_at": "2025-08-26T10:46:29.280002"}], "total_files": 1, "processed_at": "2025-08-26T10:46:29.280010"}