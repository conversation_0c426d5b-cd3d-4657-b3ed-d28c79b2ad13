{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_8.1.1.2_All", "date": "2025-07-10 11:15:37", "message": "[ESS]C01-20250604003 增加避免串單的卡控機制：1.檢查當前流程與Session內的ProcessSerialNumber是否匹配，2.檢查Identifier有沒有被其它流程用過", "author": "lorenchang"}, "舊分支": {"branch_name": "release_8.1.1.2", "date": "2025-06-30 17:58:53", "message": "[智能表單設計助手] 新增多语系", "author": "周权"}, "比較時間": "2025-07-18 10:39:25", "新增commit數量": 1, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "53258e95951181ea7192be0abc703ae3ddaff734", "commit_訊息": "[ESS]C01-20250604003 增加避免串單的卡控機制：1.檢查當前流程與Session內的ProcessSerialNumber是否匹配，2.檢查Identifier有沒有被其它流程用過", "提交日期": "2025-07-10 11:15:37", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/exception/AppFormProcessMismatchException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormCacheSingletonCollection.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AppFormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/appform/EPIBasePageJS.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}]}