# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.0.3
- **舊版本**: 5.6.0.2
- **生成時間**: 2025-07-28 18:23:28
- **新增 Commit 數量**: 69

## 變更摘要

### <PERSON><PERSON> (23 commits)

- **2016-09-09 16:03:22**: 增加設定檔 判斷是否需驗證VM(GuardService)
  - 變更檔案: 2 個
- **2016-09-09 14:25:13**: 修正設定檔黑名單為空時開啟的語法錯誤
  - 變更檔案: 1 個
- **2016-09-07 15:03:52**: 修正統計流程資訊-待處理工作量統計時會出現型態轉換錯誤問題(測試bug修正)
  - 變更檔案: 1 個
- **2016-09-07 14:04:58**: 修正pdfviwer 無法開啟問題
  - 變更檔案: 2 個
- **2016-09-07 14:01:07**: 修正TT發單時如果設定mcloud推播時會發生錯誤導致無法開單
  - 變更檔案: 1 個
- **2016-09-05 17:31:35**: [泓記精密股份有限公司]修正ISO文件新增、變更、作廢時，開窗的類別並無依據分區控管權限顯示的問題
  - 變更檔案: 1 個
- **2016-09-05 11:50:23**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-05 11:40:24**: [泓記精密股份有限公司] 修正文件新增、變更、作廢時，開窗的類別並無依據分區控管權限顯示的問題
  - 變更檔案: 1 個
- **2016-09-02 14:35:58**: 修正自動簽核進入核決層級有設置代理人時產生無窮迴圈
  - 變更檔案: 1 個
- **2016-09-02 14:33:00**: 批次簽核取得片語資料
  - 變更檔案: 1 個
- **2016-09-02 14:28:58**: 批次簽核取得片語資料
  - 變更檔案: 1 個
- **2016-08-30 16:47:24**: [Q]修正多選附件上傳時，無法取得附件ID,OID
  - 變更檔案: 1 個
- **2016-08-30 16:46:00**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-08-30 16:43:38**: [Q]修正多選附件上傳時，無法取得附件ID,OID
  - 變更檔案: 1 個
- **2016-08-30 16:38:23**: [Q]修正多選附件上傳時，無法取得附件ID,OID
  - 變更檔案: 1 個
- **2016-08-30 16:32:49**: [Q]修正多選附件上傳時，無法取得附件ID,OID
  - 變更檔案: 1 個
- **2016-08-30 14:05:01**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-08-30 14:01:26**: [Q]卡控session重複登入議題多語系
  - 變更檔案: 1 個
- **2016-08-29 17:33:41**: [Q]卡控session重複登入議題
  - 變更檔案: 1 個
- **2016-08-29 17:29:46**: [Q]卡控session重複登入議題
  - 變更檔案: 7 個
- **2016-08-29 17:22:44**: [Q]發起流程時因session覆蓋導致發起錯誤流程
  - 變更檔案: 1 個
- **2016-08-29 17:20:51**: [Q]修正多選附件上傳時，無法取得附件ID,OID
  - 變更檔案: 2 個
- **2016-08-29 17:14:27**: [Q]SAP功能影響ajax取得簽核意見
  - 變更檔案: 2 個

### loren (6 commits)

- **2016-09-05 11:00:56**: T100整合表單微調CSSStyle
  - 變更檔案: 99 個
- **2016-09-02 16:13:17**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-02 16:10:45**: Q00-20160902001 修正HRM組織同步在ETL設定檔sync-def.xml存在且未設定時，會因為ETL工具無法取得資料庫連線導致組織同步失敗的問題
  - 變更檔案: 1 個
- **2016-09-02 09:12:15**: Eclipse開發環境調整，讓設定新版本Eclipse或JDK時可以更方便
  - 變更檔案: 19 個
- **2016-08-26 15:29:04**: Eclipse BPM Project增加Resource Filters設定，隱藏不顯示的目錄
  - 變更檔案: 1 個
- **2016-08-25 16:02:58**: Eclipse開發環境加入整個BPM目錄的Project
  - 變更檔案: 2 個

### LALA (15 commits)

- **2016-09-05 10:27:19**: C01-20160819002[虹揚]ISO高安全性文件可從IE瀏覽器另存新檔
  - 變更檔案: 1 個
- **2016-09-05 10:18:20**: (尚未立單)[欣興]ISO高安全性但非pdf檔的文件不以pdfViewer開啟，改以網頁開啟
  - 變更檔案: 1 個
- **2016-09-05 10:14:54**: (尚未立單)[欣興]ISO高安全性但非pdf檔的文件不以pdfViewer開啟，改以網頁開啟
  - 變更檔案: 1 個
- **2016-09-02 11:57:41**: C01-20160728002[地樺]修正ECP整合畫面異常
  - 變更檔案: 11 個
- **2016-08-31 17:37:08**: A00-20160816002[宏丰]修正舊版表單中元件無背景顏色的問題
  - 變更檔案: 1 個
- **2016-08-24 17:24:31**: 新增填寫表單時，點選欄位及輸入值時，會重新計時。
  - 變更檔案: 1 個
- **2016-08-23 17:41:42**: 修正iReport的classpath
  - 變更檔案: 1 個
- **2016-08-23 15:23:25**: [欣興]登入異常問題(代哲瑋哥簽入)
  - 變更檔案: 1 個
- **2016-08-23 15:00:44**: 修正點選設計師畫面會回首頁的問題
  - 變更檔案: 2 個
- **2016-08-22 11:01:22**: C01-20160720008[兆遠]流程代理人筆數多，設定離職按下確認跑很久，則重複點選確認鍵造成錯誤。
  - 變更檔案: 2 個
- **2016-08-19 11:33:49**: A00-20160815001[仁愛醫院]簽核意見過多造成畫面異常
  - 變更檔案: 1 個
- **2016-08-19 11:22:47**: Q00-20160811001 iReport修復
  - 變更檔案: 2 個
- **2016-08-19 11:19:31**: Q00-20160811001 iReport修復
  - 變更檔案: 1 個
- **2016-08-19 11:03:44**: A00-20160725001[虹揚]修正通知信件夾帶附件異常
  - 變更檔案: 1 個
- **2016-08-19 10:40:55**: 修正登入首頁因應jdk1.5轉型
  - 變更檔案: 1 個

### jerry1218 (13 commits)

- **2016-09-02 17:09:43**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-02 17:05:16**: T100整合LOG優化
  - 變更檔案: 16 個
- **2016-08-30 14:32:34**: 修正T100發單邏輯 , 在最上方增加全域變數否使用遮罩功能 , 但目前因T100尚無規劃支援遮罩 , 故強制給false , 即不會有maskFaildValues , 此舉可大幅增加效能
  - 變更檔案: 1 個
- **2016-08-30 10:55:29**: A00-20160830001 修正SessionBeanToolAgent判斷T100回寫失敗時依據錯誤(description改為)code
  - 變更檔案: 1 個
- **2016-08-24 14:45:46**: A00-20160824001 修正^字元會導致PDF Viewer檔案開啟失敗問題
  - 變更檔案: 1 個
- **2016-08-24 11:32:26**: C01-20160819006 創元協助anmt480回寫waiting時間拉長
  - 變更檔案: 1 個
- **2016-08-19 14:10:24**: A00-20160819001 於menu清單中加回[清除系統整合cache]
  - 變更檔案: 1 個
- **2016-08-17 16:36:24**: A00-*********** 企業流程監控 - 修正T100整合單據會出異常 , 原因為T100預設主旨的逗號會與JSON相衝突
  - 變更檔案: 1 個
- **2016-08-17 16:34:28**: A00-*********** 企業流程監控 - 匯出excel異常 , 原因為JSON格式錯誤
  - 變更檔案: 1 個
- **2016-08-17 16:25:01**: A00-*********** 修正統計流程資訊-待處理工作量統計時會出現型態轉換錯誤問題
  - 變更檔案: 1 個
- **2016-08-17 16:23:35**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-08-17 16:22:41**: A00-*********** 修正統計流程資訊-待處理工作量統計時會出現型態轉換錯誤問題
  - 變更檔案: 1 個
- **2016-08-17 16:19:09**: A00-*********** 修正[統計流程資訊] - [待處理工作量]出現型態轉換錯誤問題
  - 變更檔案: 1 個

### WenCheng (8 commits)

- **2016-09-02 10:53:27**: [內部]HRM產品，其HR小助手功能上要執行組織同步時，需要此搭配的EFGP.xml檔案，因此我們這邊也協助留存一份標準的版本。
  - 變更檔案: 1 個
- **2016-09-02 10:00:51**: A00-20160812001 增加SQL指令，可將客戶現存資料BamProInstData，補上正確的結案時的「狀態、結案時間」欄位
  - 變更檔案: 2 個
- **2016-09-01 17:05:45**: A00-20160824003 一般使用者使用「我的最愛」功能增加「常用流程」項目之後，點按連結時會發生空白畫面及請洽系統管理員的問題已修正。
  - 變更檔案: 1 個
- **2016-08-30 16:41:07**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-08-30 16:22:07**: A00-20160829001 預設提供的排程「清理不在線使用者記錄」，調整其邏輯內容，以避免導致資料lock住的問題。
  - 變更檔案: 1 個
- **2016-08-17 16:57:09**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-08-17 16:54:55**: A00-20160815002 流程草稿查詢頁面上，計算「資料總筆數」問題修正
  - 變更檔案: 1 個
- **2016-08-17 14:20:22**: A00-20160812001 流程撤銷、完成、中止均會執行的QueueHelper動作，修改為呼叫正確的FinsihProInst
  - 變更檔案: 1 個

### lorenchang (1 commits)

- **2016-08-30 08:40:29**: Merge branch 'workspace_modify' into 'develop'

### pinchi_lin (2 commits)

- **2016-08-29 18:26:52**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-08-29 17:21:58**: 20160829001 逾時通知微信消息，直接點連結應為待辦事項，而非工作通知
  - 變更檔案: 2 個

### Noah.Jhuang (1 commits)

- **2016-08-16 15:42:55**: [立訊精密] A00-*********** 2016.07.29 Add 新增流程代理人時對於選擇流程時的效能改善 by 駿緯.
  - 變更檔案: 6 個

## 詳細變更記錄

### 1. 增加設定檔 判斷是否需驗證VM(GuardService)
- **Commit ID**: `c351a3dcc9f4f55c3b793a73eb24738dfd6bf603`
- **作者**: wayne
- **日期**: 2016-09-09 16:03:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`

### 2. 修正設定檔黑名單為空時開啟的語法錯誤
- **Commit ID**: `743c5ca21a9cc3e149c883603fc2d46ab66a28eb`
- **作者**: wayne
- **日期**: 2016-09-09 14:25:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`

### 3. 修正統計流程資訊-待處理工作量統計時會出現型態轉換錯誤問題(測試bug修正)
- **Commit ID**: `cf6fe5d1900f3839d9f4ea5b2a4d24f116c766ef`
- **作者**: wayne
- **日期**: 2016-09-07 15:03:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GatherWfStatisticsAction.java`

### 4. 修正pdfviwer 無法開啟問題
- **Commit ID**: `b80d2ad97daf866c97669ba468015b308de16eca`
- **作者**: wayne
- **日期**: 2016-09-07 14:04:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/MainFilePDFViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/ToolSuite.jsp`

### 5. 修正TT發單時如果設定mcloud推播時會發生錯誤導致無法開單
- **Commit ID**: `3d469069423f4080e1deef2beaeb7598f961ae0b`
- **作者**: wayne
- **日期**: 2016-09-07 14:01:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mcloud/McloudMgr.java`

### 6. [泓記精密股份有限公司]修正ISO文件新增、變更、作廢時，開窗的類別並無依據分區控管權限顯示的問題
- **Commit ID**: `8e3699198b2c20742ea2a6567d0eecac32dc36e7`
- **作者**: wayne
- **日期**: 2016-09-05 17:31:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java`

### 7. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a406e1b6c6f1d298cddecc25571e6c053bd32674`
- **作者**: wayne
- **日期**: 2016-09-05 11:50:23
- **變更檔案數量**: 0

### 8. [泓記精密股份有限公司] 修正文件新增、變更、作廢時，開窗的類別並無依據分區控管權限顯示的問題
- **Commit ID**: `a3bd4a6e42265db59f5dba5d3bc6c5c51b2c2eab`
- **作者**: wayne
- **日期**: 2016-09-05 11:40:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/CreateDocumentAction.java`

### 9. T100整合表單微調CSSStyle
- **Commit ID**: `791254ade614959a44b0904befb1511ceaff471d`
- **作者**: loren
- **日期**: 2016-09-05 11:00:56
- **變更檔案數量**: 99
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/ECR\347\224\263\350\253\213\344\275\234\346\245\255(abmt500).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\270\200\350\210\254\345\224\256\345\203\271\350\252\277\345\203\271\344\275\234\346\245\255(aprt112).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\270\200\350\210\254\351\200\262\345\203\271\350\252\277\345\203\271\344\275\234\346\245\255(aprt111).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\272\244\346\230\223\345\260\215\350\261\241\347\224\263\350\253\213\345\226\256(apmt100).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\345\207\206\345\205\245\345\226\256(apmt800).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\347\265\220\347\256\227\345\226\256(astt340).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\350\262\273\347\224\250\345\226\256(astt320).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\277\203\351\212\267\350\252\277\345\203\271\344\275\234\346\245\255(aprt113).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\200\237\350\262\250\350\250\202\345\226\256(axmt501).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\200\237\350\262\250\351\202\204\351\207\217\345\226\256(axmt591).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\205\247\351\203\250\347\265\220\347\256\227\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt740).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\207\272\350\262\250\345\226\256(axmt540).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\207\272\350\262\250\347\260\275\346\224\266\345\226\256(axmt580).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256(axmt520).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\210\206\351\212\267\345\220\210\347\264\204\347\224\263\350\253\213\344\275\234\346\245\255(astt601).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\210\206\351\212\267\350\250\202\345\226\256(adbt500).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\210\206\351\212\267\350\250\202\345\226\256\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(adbt510).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\210\206\351\212\267\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt600).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\210\270\347\250\256\345\237\272\346\234\254\350\263\207\346\226\231\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(agct300).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\223\201\350\263\252\346\252\242\351\251\227\345\226\256(aqct300).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\225\206\345\223\201\345\207\206\345\205\245\347\224\263\350\253\213\345\226\256(artt300).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\226\256\344\270\200\344\270\273\344\273\266ECN(abmt300).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\240\261\345\273\242\347\224\263\350\253\213\345\226\256(aint310).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\244\232\344\270\273\344\273\266ECN(abmt301).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\347\266\255\350\255\267\344\275\234\346\245\255(apmt501).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\256\242\346\210\266\345\207\206\345\205\245\345\217\212\350\256\212\346\233\264\344\275\234\346\245\255(axmt800).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\257\246\345\234\260\347\233\244\351\273\236\350\250\210\347\225\253\347\266\255\350\255\267\344\275\234\346\245\255(aint820).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\344\270\200\350\210\254\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft323).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\345\200\222\346\211\243\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft324).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\345\200\222\346\211\243\351\240\230\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft314).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\345\234\250\350\243\275\344\270\213\351\232\216\346\226\231\345\240\261\345\273\242\344\275\234\346\245\255(asft339).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\346\210\220\345\245\227\347\231\274\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft311).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\346\210\220\345\245\227\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft321).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\346\254\240\346\226\231\350\243\234\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft313).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\243\275\347\250\213\351\207\215\345\267\245\350\275\211\345\207\272\344\275\234\346\245\255(asft338).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\256\212\346\233\264(asft800).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\266\205\351\240\230\347\266\255\350\255\267\344\275\234\346\245\255(asft312).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\266\205\351\240\230\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft322).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\270\202\345\240\264\346\216\250\345\273\243\346\264\273\345\213\225\346\240\270\351\212\267\345\226\256(astt605).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\270\202\345\240\264\346\216\250\345\273\243\346\264\273\345\213\225\347\224\263\350\253\213\345\226\256(astt604).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\272\253\345\255\230\345\240\261\345\273\242\351\231\244\345\270\263(aint311).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\272\253\345\255\230\346\234\211\346\225\210\346\234\237\350\256\212\346\233\264\344\275\234\346\245\255(aint180).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\272\253\345\255\230\347\225\260\345\270\270\350\256\212\346\233\264\344\275\234\346\245\255(aint170).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\275\210\346\200\247\346\216\241\350\263\274\345\203\271\346\240\274\347\224\263\350\253\213\344\275\234\346\245\255(apmt128).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\275\210\346\200\247\351\212\267\345\224\256\345\203\271\346\240\274\347\224\263\350\253\213\344\275\234\346\245\255(axmt128).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\207\211\344\273\230\345\214\257\346\254\276\347\225\260\345\213\225\344\275\234\346\245\255(anmt480).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\207\211\344\273\230\345\270\263\346\254\276\346\206\221\345\226\256(aapt300).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\207\211\344\273\230\346\240\270\351\212\267\345\226\256(aapt420).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\207\211\346\224\266\346\262\226\351\212\267\346\206\221\350\255\211(axrt400).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\200\211\351\200\200\345\226\256(apmt580).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\200\211\351\200\200\345\226\256(apmt890).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\203\271\346\240\274\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(apmt129).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\205\245\345\272\253\345\226\256(apmt570).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\205\245\345\272\253\345\226\256(apmt880).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\220\210\347\264\204\345\226\256(apmt480).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\226\256(apmt500).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\226\256(apmt840).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256(apmt862).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256(apmt520).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256(apmt860).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\350\243\234\345\267\256\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aprt601).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256(apmt510).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt850).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\351\251\227\351\200\200\345\226\256(apmt560).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\351\251\227\351\200\200\345\226\256(apmt870).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\226\231\344\273\266\346\211\277\350\252\215\347\224\263\350\253\213\344\275\234\346\245\255(abmt400).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\226\231\344\273\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(aimt300).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\234\203\345\223\241\345\215\241\347\250\256\347\224\263\350\253\213\345\226\256(ammt320).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\240\270\345\203\271\345\226\256(apmt440).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\204\241\346\216\241\350\263\274\345\205\245\345\272\253\345\226\256(apmt863).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256(apmt861).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\224\237\351\256\256\345\203\271\346\240\274\350\252\277\346\225\264\344\275\234\346\245\255(aprt121).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\225\266\347\253\231\344\270\213\347\267\232\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(asft337).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\266\223\351\212\267\345\225\206\344\273\243\345\242\212\350\262\273\347\224\250\345\240\261\351\212\267\347\266\255\350\255\267\344\275\234\346\245\255(astt606).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\266\223\351\212\267\345\225\206\347\265\220\347\256\227\345\226\256(astt640).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\266\223\351\212\267\345\225\206\350\262\273\347\224\250\345\226\256(astt620).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\207\252\346\234\211\346\226\260\345\225\206\345\223\201\345\274\225\351\200\262\345\226\256(artt406).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\207\252\347\207\237\345\220\210\347\264\204\347\225\260\345\213\225\347\224\263\350\253\213\345\226\256(astt301).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\207\252\347\207\237\346\226\260\347\224\242\345\223\201\345\274\225\351\200\262(artt405).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\246\201\350\262\250\345\226\256(apmt830).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\246\201\350\262\250\345\226\256\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(apmt835).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\250\202\345\226\256(axmt500).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\250\202\345\226\256\350\256\212\346\233\264(axmt510).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\251\242\345\203\271\345\226\256(apmt420).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\252\277\346\222\245\347\224\263\350\253\213\345\226\256(aint320).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\253\213\350\263\274\345\226\256(apmt400).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\253\213\350\263\274\350\256\212\346\233\264\345\226\256(apmt410).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\262\273\347\224\250\345\240\261\346\224\257\345\226\256(aapt330).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\262\273\347\224\250\351\240\220\346\224\257\347\224\263\350\253\213\345\226\256(aapt331).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\263\207\347\224\242\351\203\250\351\226\200\350\275\211\347\247\273\347\266\255\350\255\267\344\275\234\346\245\255(afat421).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\203\271\346\240\274\350\241\250\347\224\263\350\253\213\344\275\234\346\245\255(axmt129).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\220\210\347\264\204\345\226\256(axmt440).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\240\261\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt410).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\346\240\270\345\203\271\345\226\256(axmt420).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\350\243\234\345\267\256\347\266\255\350\255\267\344\275\234\346\245\255(aprt602).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\351\200\200\345\226\256(axmt600).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\213\252\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt832).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\233\234\351\240\205\345\272\253\345\255\230\346\224\266\346\226\231\345\226\256(aint302).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\233\234\351\240\205\345\272\253\345\255\230\347\231\274\346\226\231\345\226\256(aint301).form"`

### 10. C01-20160819002[虹揚]ISO高安全性文件可從IE瀏覽器另存新檔
- **Commit ID**: `dbebe757b51ac815f6980b7b8e763b0a47328435`
- **作者**: LALA
- **日期**: 2016-09-05 10:27:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/MainFileViewerSecurity.jsp`

### 11. (尚未立單)[欣興]ISO高安全性但非pdf檔的文件不以pdfViewer開啟，改以網頁開啟
- **Commit ID**: `9ba9b5cc61415d107d1aaa63fff4b94807712922`
- **作者**: LALA
- **日期**: 2016-09-05 10:18:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 12. (尚未立單)[欣興]ISO高安全性但非pdf檔的文件不以pdfViewer開啟，改以網頁開啟
- **Commit ID**: `eb306054ae8d52752369ecabeb93091a6ce2476e`
- **作者**: LALA
- **日期**: 2016-09-05 10:14:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 13. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `cf06fa10d58ec91a4efc613af53ce5575eb4fd0d`
- **作者**: jerry1218
- **日期**: 2016-09-02 17:09:43
- **變更檔案數量**: 0

### 14. T100整合LOG優化
- **Commit ID**: `ace8fab708fb7c0c427a6e7b62e397a8151fb8e2`
- **作者**: jerry1218
- **日期**: 2016-09-02 17:05:16
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/AbstractNewTiptopMethod.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodCustomerNotifyProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessInfoGet.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodWorkItemGet.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopUserImageSyncBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopToolBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopFormTransfer.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/ProcessUtil.java`

### 15. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `aa70937d250c62d9c30f316b37f0ce7c4f76b445`
- **作者**: loren
- **日期**: 2016-09-02 16:13:17
- **變更檔案數量**: 0

### 16. Q00-20160902001 修正HRM組織同步在ETL設定檔sync-def.xml存在且未設定時，會因為ETL工具無法取得資料庫連線導致組織同步失敗的問題
- **Commit ID**: `3415cb8f7328404f5f706848256f67c4ed26cf42`
- **作者**: loren
- **日期**: 2016-09-02 16:10:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/etl/SyncOrgEtl.java`

### 17. 修正自動簽核進入核決層級有設置代理人時產生無窮迴圈
- **Commit ID**: `f6f51356cf724ab734dec3ea5d55c96c246cb03d`
- **作者**: wayne
- **日期**: 2016-09-02 14:35:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 18. 批次簽核取得片語資料
- **Commit ID**: `7a5e25cbd8a5ec7454ed7d7369e38598e1c6adcc`
- **作者**: wayne
- **日期**: 2016-09-02 14:33:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`

### 19. 批次簽核取得片語資料
- **Commit ID**: `3922903044f241a23355db5da5356589b6eb19ba`
- **作者**: wayne
- **日期**: 2016-09-02 14:28:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`

### 20. C01-20160728002[地樺]修正ECP整合畫面異常
- **Commit ID**: `05b3f25c6781efdb77377494aabc5fef6512dc78`
- **作者**: LALA
- **日期**: 2016-09-02 11:57:41
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 21. [內部]HRM產品，其HR小助手功能上要執行組織同步時，需要此搭配的EFGP.xml檔案，因此我們這邊也協助留存一份標準的版本。
- **Commit ID**: `3d218191e247176a4c71978fb2755d0d6f6d72a1`
- **作者**: WenCheng
- **日期**: 2016-09-02 10:53:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@syncorg-hrm/MappingXML/EFGP.xml`

### 22. A00-20160812001 增加SQL指令，可將客戶現存資料BamProInstData，補上正確的結案時的「狀態、結案時間」欄位
- **Commit ID**: `3740c2c68d24f9bcf0e55465bf1c21fd820e8e92`
- **作者**: WenCheng
- **日期**: 2016-09-02 10:00:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.3_updateSQL_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.3_updateSQL_Oracle.sql`

### 23. Eclipse開發環境調整，讓設定新版本Eclipse或JDK時可以更方便
- **Commit ID**: `459bf772c227b00e0635de0a687911ed7a62c72e`
- **作者**: loren
- **日期**: 2016-09-02 09:12:15
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/domain/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/dto/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/efgp-pdfViewer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/persistence/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/service/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/system/.settings/org.eclipse.jdt.core.prefs`
  - 📝 **修改**: `3.Implementation/subproject/webapp/.project`

### 24. A00-20160824003 一般使用者使用「我的最愛」功能增加「常用流程」項目之後，點按連結時會發生空白畫面及請洽系統管理員的問題已修正。
- **Commit ID**: `3956e4ff85d7164f64dd61bee74b6ce24cddab12`
- **作者**: WenCheng
- **日期**: 2016-09-01 17:05:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 25. A00-20160816002[宏丰]修正舊版表單中元件無背景顏色的問題
- **Commit ID**: `5769d0e6649a83fe7ef5d78bc49f8a6d7928391b`
- **作者**: LALA
- **日期**: 2016-08-31 17:37:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 26. [Q]修正多選附件上傳時，無法取得附件ID,OID
- **Commit ID**: `fb593774b453c337c00a1c9b3224126c13300f52`
- **作者**: wayne
- **日期**: 2016-08-30 16:47:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 27. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `66f27cfd081d52953636f406fb795c65d67a66ce`
- **作者**: wayne
- **日期**: 2016-08-30 16:46:00
- **變更檔案數量**: 0

### 28. [Q]修正多選附件上傳時，無法取得附件ID,OID
- **Commit ID**: `7f07dd61b22ed8ac3e7a75ac75ad6f6374a2cc18`
- **作者**: wayne
- **日期**: 2016-08-30 16:43:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 29. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `09415b49b907cd1711481dc8d74c6fbd0a2b0863`
- **作者**: WenCheng
- **日期**: 2016-08-30 16:41:07
- **變更檔案數量**: 0

### 30. [Q]修正多選附件上傳時，無法取得附件ID,OID
- **Commit ID**: `b6eccdde71e5cf2ab0c8ae388ea5e7d49c6739af`
- **作者**: wayne
- **日期**: 2016-08-30 16:38:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 31. [Q]修正多選附件上傳時，無法取得附件ID,OID
- **Commit ID**: `c6f477f6c07caed895765a0b7ddebd92e1eaedaa`
- **作者**: wayne
- **日期**: 2016-08-30 16:32:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 32. A00-20160829001 預設提供的排程「清理不在線使用者記錄」，調整其邏輯內容，以避免導致資料lock住的問題。
- **Commit ID**: `ddd8f023ee512243ff14f6dc4718e8eaf7874145`
- **作者**: WenCheng
- **日期**: 2016-08-30 16:22:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 33. 修正T100發單邏輯 , 在最上方增加全域變數否使用遮罩功能 , 但目前因T100尚無規劃支援遮罩 , 故強制給false , 即不會有maskFaildValues , 此舉可大幅增加效能
- **Commit ID**: `e549fbea57b426c136403b9aa51fa4b840bc1dcf`
- **作者**: jerry1218
- **日期**: 2016-08-30 14:32:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 34. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f00cde10ed80fe031626d4d851eed996e387da0f`
- **作者**: wayne
- **日期**: 2016-08-30 14:05:01
- **變更檔案數量**: 0

### 35. [Q]卡控session重複登入議題多語系
- **Commit ID**: `b73e5b798065849a3f9d1ae4322b9ecfcf75ab75`
- **作者**: wayne
- **日期**: 2016-08-30 14:01:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 36. A00-20160830001 修正SessionBeanToolAgent判斷T100回寫失敗時依據錯誤(description改為)code
- **Commit ID**: `fb9a5dfc8eb382eaa3ab159315c11f3eb66948da`
- **作者**: jerry1218
- **日期**: 2016-08-30 10:55:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/SessionBeanToolAgent.java`

### 37. Merge branch 'workspace_modify' into 'develop'
- **Commit ID**: `52b3a81d4bdb68d31d3805d6deba3a39d4aea5f5`
- **作者**: lorenchang
- **日期**: 2016-08-30 08:40:29
- **變更檔案數量**: 0

### 38. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b151dd35cbf3a0e7c647a186644da2f99578b8d8`
- **作者**: pinchi_lin
- **日期**: 2016-08-29 18:26:52
- **變更檔案數量**: 0

### 39. [Q]卡控session重複登入議題
- **Commit ID**: `1a7bd0057b0d23206d96337a322cec0d0b5c621c`
- **作者**: wayne
- **日期**: 2016-08-29 17:33:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5603.xls`

### 40. [Q]卡控session重複登入議題
- **Commit ID**: `ebf8f0d782941c9e73174e7c2e9a7f3c0c898ff5`
- **作者**: wayne
- **日期**: 2016-08-29 17:29:46
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/UserSessionNotice.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 41. [Q]發起流程時因session覆蓋導致發起錯誤流程
- **Commit ID**: `d7b56c0894ef9461871fbf37894a3ada04cf4783`
- **作者**: wayne
- **日期**: 2016-08-29 17:22:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 42. 20160829001 逾時通知微信消息，直接點連結應為待辦事項，而非工作通知
- **Commit ID**: `c8a4a830f5e57d83d89b1170edc59afb8cc82851`
- **作者**: pinchi_lin
- **日期**: 2016-08-29 17:21:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 43. [Q]修正多選附件上傳時，無法取得附件ID,OID
- **Commit ID**: `fac1db46b19fdf7d3c1351ffe18562626093b170`
- **作者**: wayne
- **日期**: 2016-08-29 17:20:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 44. [Q]SAP功能影響ajax取得簽核意見
- **Commit ID**: `eb718b55209a3797d387ec3945128382507cf851`
- **作者**: wayne
- **日期**: 2016-08-29 17:14:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 45. Eclipse BPM Project增加Resource Filters設定，隱藏不顯示的目錄
- **Commit ID**: `d75db01491f25f1c84117cc25e69f7a385df72b0`
- **作者**: loren
- **日期**: 2016-08-26 15:29:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `.project`

### 46. Eclipse開發環境加入整個BPM目錄的Project
- **Commit ID**: `6eccb0b64843d666b32c7ae611f3f1777887aeeb`
- **作者**: loren
- **日期**: 2016-08-25 16:02:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `.project`

### 47. 新增填寫表單時，點選欄位及輸入值時，會重新計時。
- **Commit ID**: `08e4c18e94313804bdcd2cb79812de0d28171216`
- **作者**: LALA
- **日期**: 2016-08-24 17:24:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 48. A00-20160824001 修正^字元會導致PDF Viewer檔案開啟失敗問題
- **Commit ID**: `6428240398a3a7e53f1675684d11dbcfc0f6f9c1`
- **作者**: jerry1218
- **日期**: 2016-08-24 14:45:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`

### 49. C01-20160819006 創元協助anmt480回寫waiting時間拉長
- **Commit ID**: `331d397dac8f0955408e306fb54097f9b67cd068`
- **作者**: jerry1218
- **日期**: 2016-08-24 11:32:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPIBean.java`

### 50. 修正iReport的classpath
- **Commit ID**: `b36de48f8bfbcd32176b9f6d420a64eb9dd45073`
- **作者**: LALA
- **日期**: 2016-08-23 17:41:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`

### 51. [欣興]登入異常問題(代哲瑋哥簽入)
- **Commit ID**: `abf0227c824beb2274fe4344202b6542b3f4516d`
- **作者**: LALA
- **日期**: 2016-08-23 15:23:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 52. 修正點選設計師畫面會回首頁的問題
- **Commit ID**: `6f145bc5d9cc3a0ce1b7e93030d1bda7a6fde070`
- **作者**: LALA
- **日期**: 2016-08-23 15:00:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 53. C01-20160720008[兆遠]流程代理人筆數多，設定離職按下確認跑很久，則重複點選確認鍵造成錯誤。
- **Commit ID**: `50e75c5e5af5bacef4a1233e8e963b7b59ad3429`
- **作者**: LALA
- **日期**: 2016-08-22 11:01:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/AbstractDesignerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/table/FlowSubstituteTableController.java`

### 54. A00-20160819001 於menu清單中加回[清除系統整合cache]
- **Commit ID**: `e703b85b63e7e5c0833b3685e5d99b9c721e8a18`
- **作者**: jerry1218
- **日期**: 2016-08-19 14:10:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 55. A00-20160815001[仁愛醫院]簽核意見過多造成畫面異常
- **Commit ID**: `aec5ed15991e4f0506995ebb2062d138cef32816`
- **作者**: LALA
- **日期**: 2016-08-19 11:33:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 56. Q00-20160811001 iReport修復
- **Commit ID**: `297128ad089b721a9b1f0ea1734ca965628b0d6d`
- **作者**: LALA
- **日期**: 2016-08-19 11:22:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/iReport/jasperreports-3.7.4.jar`

### 57. Q00-20160811001 iReport修復
- **Commit ID**: `60add065c7d0596c2bca97943d393fed563e8575`
- **作者**: LALA
- **日期**: 2016-08-19 11:19:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`

### 58. A00-20160725001[虹揚]修正通知信件夾帶附件異常
- **Commit ID**: `b463a7ec017249481cefad2d463b4209ba15cab1`
- **作者**: LALA
- **日期**: 2016-08-19 11:03:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 59. 修正登入首頁因應jdk1.5轉型
- **Commit ID**: `223c00bee2f3624e5aad7037ea6d141054395e21`
- **作者**: LALA
- **日期**: 2016-08-19 10:40:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 60. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `80a75196ca6c2d4f5926c0b780b62e8aac0b8f32`
- **作者**: WenCheng
- **日期**: 2016-08-17 16:57:09
- **變更檔案數量**: 0

### 61. A00-20160815002 流程草稿查詢頁面上，計算「資料總筆數」問題修正
- **Commit ID**: `2a71d69852bac17d48b4ebe22991b54f48120041`
- **作者**: WenCheng
- **日期**: 2016-08-17 16:54:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DraftListReader.java`

### 62. A00-*********** 企業流程監控 - 修正T100整合單據會出異常 , 原因為T100預設主旨的逗號會與JSON相衝突
- **Commit ID**: `24519768994c413ef1aa34f99221b5fa2920a7bf`
- **作者**: jerry1218
- **日期**: 2016-08-17 16:36:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`

### 63. A00-*********** 企業流程監控 - 匯出excel異常 , 原因為JSON格式錯誤
- **Commit ID**: `d525b917de354fef4ed70801b96d274908acc43c`
- **作者**: jerry1218
- **日期**: 2016-08-17 16:34:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`

### 64. A00-*********** 修正統計流程資訊-待處理工作量統計時會出現型態轉換錯誤問題
- **Commit ID**: `35dd906b39a63694f688c130cb1b7cf7bcbe6b99`
- **作者**: jerry1218
- **日期**: 2016-08-17 16:25:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GatherWfStatisticsAction.java`

### 65. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b09b0c772ab23aa8ab92bd31bb91a084fc6b1d30`
- **作者**: jerry1218
- **日期**: 2016-08-17 16:23:35
- **變更檔案數量**: 0

### 66. A00-*********** 修正統計流程資訊-待處理工作量統計時會出現型態轉換錯誤問題
- **Commit ID**: `2a92a4f77a6f684df2c0f7939513e48961f88335`
- **作者**: jerry1218
- **日期**: 2016-08-17 16:22:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GatherWfStatisticsAction.java`

### 67. A00-*********** 修正[統計流程資訊] - [待處理工作量]出現型態轉換錯誤問題
- **Commit ID**: `c9e245e48d07df05f838404169e9cd123116b6ae`
- **作者**: jerry1218
- **日期**: 2016-08-17 16:19:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GatherWfStatisticsAction.java`

### 68. A00-20160812001 流程撤銷、完成、中止均會執行的QueueHelper動作，修改為呼叫正確的FinsihProInst
- **Commit ID**: `e6f69aeb576ab53d71e2708b394d4944bb328590`
- **作者**: WenCheng
- **日期**: 2016-08-17 14:20:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`

### 69. [立訊精密] A00-*********** 2016.07.29 Add 新增流程代理人時對於選擇流程時的效能改善 by 駿緯.
- **Commit ID**: `8d5855745ad329ca18a55a7ebac56de4b8ad1f6a`
- **作者**: Noah.Jhuang
- **日期**: 2016-08-16 15:42:55
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`

