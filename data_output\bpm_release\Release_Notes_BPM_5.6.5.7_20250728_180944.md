# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.5.7
- **舊版本**: 5.6.5.6
- **生成時間**: 2025-07-28 18:09:44
- **新增 Commit 數量**: 77

## 變更摘要

### 劉建德 (8 commits)

- **2019-04-18 19:40:35**: Q0020190418001-修正常用、最愛發起流程錯誤
  - 變更檔案: 1 個
- **2019-04-18 16:13:31**: Q00-20190118009 key統一為mobile_token
  - 變更檔案: 1 個
- **2019-04-15 10:47:14**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2019-04-15 10:43:09**: Q0020190225004-移動簽核詳情頁,在流程圖畫面,可以看到簡易流程圖; 1.簡易流程圖上的icon,點選實應該不能有任何動作,現在會跳轉到流程詳細畫面 2.簡易流程圖上的任何連結,點選實應該不能有任何動作
  - 變更檔案: 3 個
- **2019-04-15 10:41:35**: Revert "Q0020190225004-移動簽核詳情頁,在流程圖畫面,可以看到簡易流程圖; 1.簡易流程圖上的icon,點選實應該不能有任何動作,現在會跳轉到流程詳細畫面 2.簡易流程圖上的任何連結,點選實應該不能有任何動作"
  - 變更檔案: 1 個
- **2019-04-15 10:39:30**: C0120190103002-ESS單身明細展開後無法縮回去。 ESS表單詳情的展開action無法使用，點了沒反應，但在TIPTOP的表單，點該action是可以正常收合的。 再麻煩產中夥伴協助查看此問題，謝謝。
  - 變更檔案: 1 個
- **2019-04-15 10:38:23**: Q0020190225004-移動簽核詳情頁,在流程圖畫面,可以看到簡易流程圖; 1.簡易流程圖上的icon,點選實應該不能有任何動作,現在會跳轉到流程詳細畫面 2.簡易流程圖上的任何連結,點選實應該不能有任何動作
  - 變更檔案: 1 個
- **2019-04-15 10:37:40**: Q0020190221001-鼎捷移動推播功能需得等鼎捷移動平台回應後程式才會繼續執行，因目前簽核與推播在同一條執行緒上，如果鼎捷移動平台回應緩慢的話會影響到簽核的效能
  - 變更檔案: 2 個

### pinchi_lin (4 commits)

- **2019-04-18 14:18:50**: A00-20190411006 修正移動推播消息若為代理通知則過濾掉此類型推播
  - 變更檔案: 2 個
- **2019-01-10 15:45:17**: C01-20190110001 修正TT拋單後IMG收不到推播的問題
  - 變更檔案: 8 個
- **2019-01-04 15:45:54**: C01-20190103008 修正某些bean因沒寫MobileManager的ejb-ref導致呼叫推播方法失敗報錯
  - 變更檔案: 4 個
- **2018-12-18 16:08:38**: C01-20181214001 修正IMG中處理的流程依重要性排序時載入資料時會撈到重複的問題
  - 變更檔案: 1 個

### yamiyeh10 (21 commits)

- **2019-04-17 15:44:26**: Q00-20190415009 第二次修正<56>App畫面在Android手機上樣式跑版，提示框的位置中間偏右
  - 變更檔案: 1 個
- **2019-04-17 15:38:36**: Q00-20190417001 修正<56>移動端當Grid資料欄位是純數字會導致呼叫失敗問題
  - 變更檔案: 1 個
- **2019-04-16 19:39:55**: Q00-20190415008 第二次修正＜56＞企業微信批次簽核簽核意見輸入框位置中間偏右下
  - 變更檔案: 1 個
- **2019-04-16 10:41:38**: Q00-20190415005 修正<56>若已有設計行動版絕對位置表單,在沒啟用App情況下還是會顯示
  - 變更檔案: 1 個
- **2019-04-15 19:36:33**: Q00-20190415009 修正＜56＞App畫面在Android手機上樣式跑版，提示框的位置中間偏右
  - 變更檔案: 1 個
- **2019-04-15 19:35:55**: Q00-20190415008 修正＜56＞企業微信批次簽核簽核意見輸入框位置中間偏右下
  - 變更檔案: 1 個
- **2019-04-15 19:34:31**: Q00-20190415006 修正<56>移動端在日期元件勾選顯示時間時，中間層與詳情頁面會顯示異常
  - 變更檔案: 2 個
- **2019-04-15 19:31:16**: Q00-20190415007 修正<56>簽核流設計器-設定行動版表單存取限制,活動定義編輯器中的表單存取限制會出現設定行動版表單存取限制按鈕,行動簽核失效後，按鈕還是會出現
  - 變更檔案: 1 個
- **2019-04-15 19:29:19**: Q0020190221001-二次修正鼎捷移動推播功能需得等鼎捷移動平台回應後程式才會繼續執行，因目前簽核與推播在同一條執行緒上，如果鼎捷移動平台回應緩慢的話會影響到簽核的效能
  - 變更檔案: 2 個
- **2019-04-15 11:48:36**: Q00-20190412007 修正IMG發起流程清單會載入重複的資料
  - 變更檔案: 1 個
- **2019-04-15 11:45:08**: Q00-20190415002 修正web表單設計師在APP未啟用時一樣會作行動版表單初始化問題
  - 變更檔案: 3 個
- **2019-04-15 11:41:57**: Q00-20190415001 調整簽核流設計師中的支援手持裝置選項卡控APP序號註冊或過期
  - 變更檔案: 1 個
- **2019-04-12 18:04:09**: Q00-20190412006 修正<56>移動端SerialNumber元件有label時高度沒有對齊
  - 變更檔案: 1 個
- **2019-04-12 17:49:54**: Q00-20190412005 修正<56>表單Grid clearBinding後下拉與選項元件不會清空的問題
  - 變更檔案: 3 個
- **2019-04-12 16:44:18**: Q00-20190412004 修正<56>修正行動版IOS預覽pdf檔案調整大小往下滑後圖片會消失的問題
  - 變更檔案: 12 個
- **2019-03-11 11:42:32**: Q00-20190221004 調整App<56>Grid明細欄位與接收資料欄位數對不上時顯示空值
  - 變更檔案: 1 個
- **2019-01-23 15:03:24**: 還原誤簽的C01-20190119003程式
  - 變更檔案: 2 個
- **2019-01-23 14:28:28**: C01-20190119003 修正IMG在iOS裝置預覽PDF附件內含電子印章與簽署時異常問題
  - 變更檔案: 2 個
- **2019-01-02 14:40:57**: A00-20181225003 修正因連續點擊退回重辦按鈕導致關卡異常問題
  - 變更檔案: 1 個
- **2018-12-27 10:40:47**: 修正企業微信的Android手機在喚醒鍵盤時遮擋住填寫欄位 --可參考C01-20181221002紀錄
  - 變更檔案: 6 個
- **2018-12-27 10:33:55**: C01-20181221002 修正IMG的Android手機再喚醒鍵盤時遮擋住填寫欄位
  - 變更檔案: 8 個

### waynechang (7 commits)

- **2019-04-16 14:14:19**: Q00-20190416001 修正ISO PDFView無法開啟檔案
  - 變更檔案: 2 個
- **2019-03-20 09:36:17**: C01-20190311003 ISO文件總管下載原始檔出現異常-將ISOFile跟SourceFile兩個方法拆開
  - 變更檔案: 4 個
- **2019-03-13 11:49:41**: C01-20181220005 修正通知信夾帶附件若檔名超過10字會亂碼
  - 變更檔案: 1 個
- **2019-02-26 14:30:38**: A00-20190215002 修正T100送簽單據後，關卡解析失敗回傳失敗的XML，但流程仍然產生
  - 變更檔案: 1 個
- **2019-01-30 09:32:53**: C01-20190123004 修正關卡退回重辦後，後續派送關卡新增為兩個代辦
  - 變更檔案: 1 個
- **2019-01-22 11:30:59**: C01-20181211001 修正ISO文件總管(user為管理程式權限設定權限的人員)下載原始檔出現異常-補上註解
  - 變更檔案: 1 個
- **2019-01-22 11:30:14**: C01-20181211001 修正ISO文件總管(user為管理程式權限設定權限的人員)下載原始檔出現異常
  - 變更檔案: 1 個

### walter_wu (20 commits)

- **2019-04-15 14:45:25**: 補修正<第二次>C01-20180418003 修正:模擬使用者時就算有授權使用者設計師也不應該看到(符合5621之前)
  - 變更檔案: 1 個
- **2019-04-11 16:31:52**: 補修正C01-20180418003 修正:模擬使用者時就算有授權使用者設計師也不應該看到(符合5621之前)
  - 變更檔案: 1 個
- **2019-04-09 19:17:54**: A00-20181109001 修正上傳附件後表單全域變數的locale會跑掉的錯誤
  - 變更檔案: 1 個
- **2019-04-08 16:52:38**: A00-20181023001 修正通知關卡的進階設定不允許列印 但從通知信連結進入 畫面上有列印紐
  - 變更檔案: 1 個
- **2019-04-02 16:20:48**: 二次修正 <V56> C01-20181113002 修正 :ESS單上按儲存草稿，會跳出呼叫AppForm網路服務失敗的錯誤訊息
  - 變更檔案: 1 個
- **2019-03-25 15:23:32**: Q00-20190315010 修正資料庫是Oracle的時候 使用流程監控資訊封存 不管是封存資料或是解封存資料都會報錯
  - 變更檔案: 1 個
- **2019-02-25 19:03:06**: A00-20190111001-2 修正使用WorkflowService.invokeProcess發起流程內含附件 清單上不會有附件的迴紋針
  - 變更檔案: 1 個
- **2019-02-25 18:11:21**: A00-20190111001 修正使用WorkflowService.invokeProcess發起流程內含附件 清單上不會有附件的迴紋針
  - 變更檔案: 1 個
- **2019-02-18 17:12:46**: A00-20190129004 修正TextBox的唯讀，只要設定顯示小數點後N位數就會發生 顏色無法正確顯示
  - 變更檔案: 1 個
- **2019-02-13 17:39:48**: 補修正C01-20190107001 C01-20190109005
  - 變更檔案: 2 個
- **2019-01-30 12:19:44**: C01-20190107001 修正Grid資料過多會蓋到簽核意見的問題
  - 變更檔案: 2 個
- **2019-01-28 17:28:47**: Q00-20190128001 修正用Excel匯入多語系 如果值有('單引號)會匯入失敗
  - 變更檔案: 1 個
- **2019-01-24 18:07:46**: Q00-20190124002 修正員工工作批次轉派 開窗輸入密碼後沒有成功轉派
  - 變更檔案: 2 個
- **2019-01-21 18:42:09**: C01-20190114002 修正列印模式 表單設定不顯示簽核意見還是有顯示
  - 變更檔案: 1 個
- **2019-01-19 15:13:49**: Q00-20190119001 修正離職人員批次轉派 開窗輸入密碼後沒有成功轉派
  - 變更檔案: 1 個
- **2019-01-11 16:49:39**: C01-20190108003 將自動簽核的通知信範本修改為與跳過關卡用同一個範本
  - 變更檔案: 1 個
- **2019-01-10 17:51:33**: A00-20181025003 修正建立作業程序書 WorkStepList 是null報錯
  - 變更檔案: 1 個
- **2019-01-03 17:25:10**: C01-20180418003 修正:模擬使用者時就算有授權使用者設計師也不應該看到(符合5621之前)
  - 變更檔案: 1 個
- **2019-01-03 15:53:06**: Q00-20190103001 將流程代理人選擇來源部門/專案的開窗從多選改為單選 避免選擇太多部門與太多流程 查詢帶給資料庫太大負擔
  - 變更檔案: 1 個
- **2018-12-24 10:12:17**: C01-20181210004 修正表單上的簽核歷程的簽核意見文字會被截掉
  - 變更檔案: 3 個

### 施翔耀 (10 commits)

- **2019-03-25 16:47:01**: <V56> C01-20181113002 修正 :ESS單上按儲存草稿，會跳出呼叫AppForm網路服務失敗的錯誤訊息
  - 變更檔案: 3 個
- **2019-03-20 14:58:34**: <V56>C01-20170809003 修正:組織同步執行順序異常,導致新增部門時會找不到部門核決層級
  - 變更檔案: 1 個
- **2019-03-20 14:26:35**: <V56>A00-20190130003 調整:新增組織同步時,因DB資料被刪時可以識別的錯誤訊息
  - 變更檔案: 1 個
- **2019-02-20 15:25:55**: 二次修正 <V56>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤
- **2019-02-20 15:25:55**: 二次修正 <V56>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤
  - 變更檔案: 1 個
- **2019-02-20 15:14:20**: <V56>A00-20180725001 修正:ESS流程只能加簽通知關卡，但修改模式下確可以選到會辦
  - 變更檔案: 1 個
- **2019-02-20 14:54:10**: <V56>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤
  - 變更檔案: 1 個
- **2019-01-07 15:35:48**: <V56>C01-20181218014 調整 :發起時前端流程發起人與Session中不一致,不與許發起,避免蓋單
  - 變更檔案: 5 個
- **2019-01-04 18:15:13**: <V56>二次調整 S00-20181224001:調整ESS流程發起時會有lodaing畫面，避免使用者重複發起
  - 變更檔案: 2 個
- **2019-01-04 13:49:32**: <V56>S00-20181224001 發起ESS流程時,將發單按鈕鎖住,避免使用者重複發單造成單據異常 如果ESS有回饋錯誤訊息,再將發單按鈕開啟。
  - 變更檔案: 2 個

### jerry1218 (2 commits)

- **2019-03-06 17:27:11**: 新增delete SQL,移除工作流程設計器
  - 變更檔案: 2 個
- **2019-03-06 15:10:04**: A00-20190305001 修正CustomDataChoooser手持裝置使用無法帶出清單的異常
  - 變更檔案: 1 個

### ChinRong (2 commits)

- **2019-02-26 12:15:36**: C01-20190222003 修正表單設計中間層標記多欄位時，最右邊標記的欄位在IMG上不會顯示
  - 變更檔案: 1 個
- **2019-01-10 17:36:30**: 調整鼎捷移動登入時更新MobileOAuthWeChatUser表的服務
  - 變更檔案: 3 個

### josephshih (3 commits)

- **2018-12-24 17:25:50**: C01-20181113001 修正 :因為每30秒會向後端詢問是否有訊息要通知，所以重新啟動BPM站台時，有呼叫後端的行為會彈出異常訊息
  - 變更檔案: 1 個
- **2018-12-21 17:00:45**: <V56>A00-20181207002修正:簽核流設計師中設定關卡處理者為角色或職稱時，會因中文字無法帶出資料
  - 變更檔案: 1 個
- **2018-12-20 17:34:36**: <V56>C01-20181127001 修正:客戶用LDAP登入 ,因判斷離職的邏輯有問題,導致無法登入
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. Q0020190418001-修正常用、最愛發起流程錯誤
- **Commit ID**: `7345e474f4f389c618c33f047389fe3ab880539a`
- **作者**: 劉建德
- **日期**: 2019-04-18 19:40:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`

### 2. Q00-20190118009 key統一為mobile_token
- **Commit ID**: `9d3225b31450f5e5adf6d88e452790ea79b1dacc`
- **作者**: 劉建德
- **日期**: 2019-04-18 16:13:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 3. A00-20190411006 修正移動推播消息若為代理通知則過濾掉此類型推播
- **Commit ID**: `78badaf28d08072629b20d3b9a0f1b510f255df0`
- **作者**: pinchi_lin
- **日期**: 2019-04-18 14:18:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 4. Q00-20190415009 第二次修正<56>App畫面在Android手機上樣式跑版，提示框的位置中間偏右
- **Commit ID**: `db8e328d9030089deb9b01ff817ab274007d43b2`
- **作者**: yamiyeh10
- **日期**: 2019-04-17 15:44:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 5. Q00-20190417001 修正<56>移動端當Grid資料欄位是純數字會導致呼叫失敗問題
- **Commit ID**: `d2e36f6cb19ce81b231465428d2520712972ebd2`
- **作者**: yamiyeh10
- **日期**: 2019-04-17 15:38:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 6. Q00-20190415008 第二次修正＜56＞企業微信批次簽核簽核意見輸入框位置中間偏右下
- **Commit ID**: `098c3ad336f8e9c058494e5d5d48fd3940d88e77`
- **作者**: yamiyeh10
- **日期**: 2019-04-16 19:39:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 7. Q00-20190416001 修正ISO PDFView無法開啟檔案
- **Commit ID**: `c608b8804af1d0aac26d0747c2eb2a69c133032d`
- **作者**: waynechang
- **日期**: 2019-04-16 14:14:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 8. Q00-20190415005 修正<56>若已有設計行動版絕對位置表單,在沒啟用App情況下還是會顯示
- **Commit ID**: `b9ab1efb72ca6048dd7eac8b6bdc1889aae77fb1`
- **作者**: yamiyeh10
- **日期**: 2019-04-16 10:41:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`

### 9. Q00-20190415009 修正＜56＞App畫面在Android手機上樣式跑版，提示框的位置中間偏右
- **Commit ID**: `ef5dffd5b587dceabf29c92cae3253da48084234`
- **作者**: yamiyeh10
- **日期**: 2019-04-15 19:36:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 10. Q00-20190415008 修正＜56＞企業微信批次簽核簽核意見輸入框位置中間偏右下
- **Commit ID**: `f3e3c8ba9b164a0cd9b49331063f79de124995b8`
- **作者**: yamiyeh10
- **日期**: 2019-04-15 19:35:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 11. Q00-20190415006 修正<56>移動端在日期元件勾選顯示時間時，中間層與詳情頁面會顯示異常
- **Commit ID**: `ade0348fb039a44e54a21f52dc31cb00c7d34c4b`
- **作者**: yamiyeh10
- **日期**: 2019-04-15 19:34:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 12. Q00-20190415007 修正<56>簽核流設計器-設定行動版表單存取限制,活動定義編輯器中的表單存取限制會出現設定行動版表單存取限制按鈕,行動簽核失效後，按鈕還是會出現
- **Commit ID**: `fbb512f1a26ef1f73d9f7d19d5979b31e773407e`
- **作者**: yamiyeh10
- **日期**: 2019-04-15 19:31:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/FormSelectDialog.java`

### 13. Q0020190221001-二次修正鼎捷移動推播功能需得等鼎捷移動平台回應後程式才會繼續執行，因目前簽核與推播在同一條執行緒上，如果鼎捷移動平台回應緩慢的話會影響到簽核的效能
- **Commit ID**: `fe71c18da7d40c3f6b5fa09f00dcb03b6cf44dfb`
- **作者**: yamiyeh10
- **日期**: 2019-04-15 19:29:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`

### 14. 補修正<第二次>C01-20180418003 修正:模擬使用者時就算有授權使用者設計師也不應該看到(符合5621之前)
- **Commit ID**: `cea35b7bb1203b7df65f9a9caa00746a3bd92a12`
- **作者**: walter_wu
- **日期**: 2019-04-15 14:45:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 15. Q00-20190412007 修正IMG發起流程清單會載入重複的資料
- **Commit ID**: `b79dbf33932de7da4fafa259a376884706b73eef`
- **作者**: yamiyeh10
- **日期**: 2019-04-15 11:48:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 16. Q00-20190415002 修正web表單設計師在APP未啟用時一樣會作行動版表單初始化問題
- **Commit ID**: `4286f43b1a39215c01549716149fe30ef1115ebb`
- **作者**: yamiyeh10
- **日期**: 2019-04-15 11:45:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 17. Q00-20190415001 調整簽核流設計師中的支援手持裝置選項卡控APP序號註冊或過期
- **Commit ID**: `550560a8e77329f0aef83b55e288159d3c2c4e0d`
- **作者**: yamiyeh10
- **日期**: 2019-04-15 11:41:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`

### 18. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `d9f1a08a044c55556af82f26eb3ee52545dd8cf0`
- **作者**: 劉建德
- **日期**: 2019-04-15 10:47:14
- **變更檔案數量**: 0

### 19. Q0020190225004-移動簽核詳情頁,在流程圖畫面,可以看到簡易流程圖; 1.簡易流程圖上的icon,點選實應該不能有任何動作,現在會跳轉到流程詳細畫面 2.簡易流程圖上的任何連結,點選實應該不能有任何動作
- **Commit ID**: `eb84f4aafca0939525fffd64ac5d85fbe3f276a4`
- **作者**: 劉建德
- **日期**: 2019-04-15 10:43:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp`

### 20. Revert "Q0020190225004-移動簽核詳情頁,在流程圖畫面,可以看到簡易流程圖; 1.簡易流程圖上的icon,點選實應該不能有任何動作,現在會跳轉到流程詳細畫面 2.簡易流程圖上的任何連結,點選實應該不能有任何動作"
- **Commit ID**: `30871e125b31791d5a07715a10253b60c65817e8`
- **作者**: 劉建德
- **日期**: 2019-04-15 10:41:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 21. C0120190103002-ESS單身明細展開後無法縮回去。 ESS表單詳情的展開action無法使用，點了沒反應，但在TIPTOP的表單，點該action是可以正常收合的。 再麻煩產中夥伴協助查看此問題，謝謝。
- **Commit ID**: `dd0362022af457d78867f48136296f2db1e4fa8a`
- **作者**: 劉建德
- **日期**: 2019-04-15 10:39:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 22. Q0020190225004-移動簽核詳情頁,在流程圖畫面,可以看到簡易流程圖; 1.簡易流程圖上的icon,點選實應該不能有任何動作,現在會跳轉到流程詳細畫面 2.簡易流程圖上的任何連結,點選實應該不能有任何動作
- **Commit ID**: `a9b557ca4766ea2a11025a8839b1b42e2395257c`
- **作者**: 劉建德
- **日期**: 2019-04-15 10:38:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 23. Q0020190221001-鼎捷移動推播功能需得等鼎捷移動平台回應後程式才會繼續執行，因目前簽核與推播在同一條執行緒上，如果鼎捷移動平台回應緩慢的話會影響到簽核的效能
- **Commit ID**: `ddc6de12d17e3f0a5ca7663473511c3955c52ef5`
- **作者**: 劉建德
- **日期**: 2019-04-15 10:37:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`

### 24. Q00-20190412006 修正<56>移動端SerialNumber元件有label時高度沒有對齊
- **Commit ID**: `06f257b2ab2d3fa4b38aca7e714685e1068aa73c`
- **作者**: yamiyeh10
- **日期**: 2019-04-12 18:04:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 25. Q00-20190412005 修正<56>表單Grid clearBinding後下拉與選項元件不會清空的問題
- **Commit ID**: `8e85b61b0826c683f4273c63bdda81c932e82958`
- **作者**: yamiyeh10
- **日期**: 2019-04-12 17:49:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppGridTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 26. Q00-20190412004 修正<56>修正行動版IOS預覽pdf檔案調整大小往下滑後圖片會消失的問題
- **Commit ID**: `87f234222ef2d1ba3f039e7ef8ed886b43b2d4f5`
- **作者**: yamiyeh10
- **日期**: 2019-04-12 16:44:18
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 27. 補修正C01-20180418003 修正:模擬使用者時就算有授權使用者設計師也不應該看到(符合5621之前)
- **Commit ID**: `40a16a157ea30c26e6768b42166c5767489f7f38`
- **作者**: walter_wu
- **日期**: 2019-04-11 16:31:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 28. A00-20181109001 修正上傳附件後表單全域變數的locale會跑掉的錯誤
- **Commit ID**: `101b6d55d3469c570c0428cb11111dda9041fd0c`
- **作者**: walter_wu
- **日期**: 2019-04-09 19:17:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 29. A00-20181023001 修正通知關卡的進階設定不允許列印 但從通知信連結進入 畫面上有列印紐
- **Commit ID**: `9ad854a63f497500f0b01e56bcd2cb807d0ef22a`
- **作者**: walter_wu
- **日期**: 2019-04-08 16:52:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 30. 二次修正 <V56> C01-20181113002 修正 :ESS單上按儲存草稿，會跳出呼叫AppForm網路服務失敗的錯誤訊息
- **Commit ID**: `120947c1e9592e9c40f4f74a8fccd9c74b844a18`
- **作者**: walter_wu
- **日期**: 2019-04-02 16:20:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 31. <V56> C01-20181113002 修正 :ESS單上按儲存草稿，會跳出呼叫AppForm網路服務失敗的錯誤訊息
- **Commit ID**: `b5879c2afd5a122551c9027bc5cdf35479ef1ee5`
- **作者**: 施翔耀
- **日期**: 2019-03-25 16:47:01
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 32. Q00-20190315010 修正資料庫是Oracle的時候 使用流程監控資訊封存 不管是封存資料或是解封存資料都會報錯
- **Commit ID**: `9e93a296071620e36f4813deb55c252e674f2c6b`
- **作者**: walter_wu
- **日期**: 2019-03-25 15:23:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`

### 33. <V56>C01-20170809003 修正:組織同步執行順序異常,導致新增部門時會找不到部門核決層級
- **Commit ID**: `566cbb7c38e7ca2271ca58b4e7d652241fc82efa`
- **作者**: 施翔耀
- **日期**: 2019-03-20 14:58:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ws/ImportOrgDocBuilder.java`

### 34. <V56>A00-20190130003 調整:新增組織同步時,因DB資料被刪時可以識別的錯誤訊息
- **Commit ID**: `c871bfdca3f6865fd39c8f748c864445291ab851`
- **作者**: 施翔耀
- **日期**: 2019-03-20 14:26:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 35. C01-20190311003 ISO文件總管下載原始檔出現異常-將ISOFile跟SourceFile兩個方法拆開
- **Commit ID**: `3cab770f32b7ca05b7092c6cee479d85de96c66c`
- **作者**: waynechang
- **日期**: 2019-03-20 09:36:17
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/isoModule/struts-manageDocument-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`

### 36. C01-20181220005 修正通知信夾帶附件若檔名超過10字會亂碼
- **Commit ID**: `ae441b4b232776abed8e0092c12c4a7da3055d80`
- **作者**: waynechang
- **日期**: 2019-03-13 11:49:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`

### 37. Q00-20190221004 調整App<56>Grid明細欄位與接收資料欄位數對不上時顯示空值
- **Commit ID**: `0dce54e2573fd87ee9246ed23a5659f90685efce`
- **作者**: yamiyeh10
- **日期**: 2019-03-11 11:42:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 38. 新增delete SQL,移除工作流程設計器
- **Commit ID**: `9fe1efbba82efa8376edf69d4a7dcda54a4e0d0e`
- **作者**: jerry1218
- **日期**: 2019-03-06 17:27:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.7_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.7_updateSQL_SQLServer.sql`

### 39. A00-20190305001 修正CustomDataChoooser手持裝置使用無法帶出清單的異常
- **Commit ID**: `6a15650372ad28a0c25808d86114c7f18aa9b2fe`
- **作者**: jerry1218
- **日期**: 2019-03-06 15:10:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 40. A00-20190215002 修正T100送簽單據後，關卡解析失敗回傳失敗的XML，但流程仍然產生
- **Commit ID**: `1df3df40009320aa9529d789f9a3cc99e0d29629`
- **作者**: waynechang
- **日期**: 2019-02-26 14:30:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/AbstractNewTiptopMethod.java`

### 41. C01-20190222003 修正表單設計中間層標記多欄位時，最右邊標記的欄位在IMG上不會顯示
- **Commit ID**: `f037e2dd7e36fce574b467a37e260c9f88932a9a`
- **作者**: ChinRong
- **日期**: 2019-02-26 12:15:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 42. A00-20190111001-2 修正使用WorkflowService.invokeProcess發起流程內含附件 清單上不會有附件的迴紋針
- **Commit ID**: `221d09bbb486bbbae96480bb106f463d54a4415b`
- **作者**: walter_wu
- **日期**: 2019-02-25 19:03:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceController.java`

### 43. A00-20190111001 修正使用WorkflowService.invokeProcess發起流程內含附件 清單上不會有附件的迴紋針
- **Commit ID**: `818c2ce575b239981b05c7e838b646cf78de5963`
- **作者**: walter_wu
- **日期**: 2019-02-25 18:11:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceController.java`

### 44. 二次修正 <V56>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤
- **Commit ID**: `d70615d6a603a4b341c1c07c431a806c9a25a5bb`
- **作者**: 施翔耀
- **日期**: 2019-02-20 15:25:55
- **變更檔案數量**: 0

### 45. 二次修正 <V56>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤
- **Commit ID**: `a9c75663cf26bcf8f06022d52b290a8b69d308b9`
- **作者**: 施翔耀
- **日期**: 2019-02-20 15:25:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 46. <V56>A00-20180725001 修正:ESS流程只能加簽通知關卡，但修改模式下確可以選到會辦
- **Commit ID**: `a2c195447b3ad52fcde77e3cf497b0a07ed9dab5`
- **作者**: 施翔耀
- **日期**: 2019-02-20 15:14:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 47. <V56>C01-20190213001 調整:移除多表單自動儲存功能，該功能不符合應用場景，且後端容易發生物件修改時報出Cannotlock的錯誤
- **Commit ID**: `5d1a88091274c6ee2a5b1cda9466753db8500bb5`
- **作者**: 施翔耀
- **日期**: 2019-02-20 14:54:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 48. A00-20190129004 修正TextBox的唯讀，只要設定顯示小數點後N位數就會發生 顏色無法正確顯示
- **Commit ID**: `8596aa6058836ee497f9a3f626c24c105db34729`
- **作者**: walter_wu
- **日期**: 2019-02-18 17:12:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 49. 補修正C01-20190107001 C01-20190109005
- **Commit ID**: `beebb821497b97e5d2a7442bb1e73cf6cee66183`
- **作者**: walter_wu
- **日期**: 2019-02-13 17:39:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 50. C01-20190107001 修正Grid資料過多會蓋到簽核意見的問題
- **Commit ID**: `5d2b0e20376324a792a63cd3f2e546c871d9873c`
- **作者**: walter_wu
- **日期**: 2019-01-30 12:19:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 51. C01-20190123004 修正關卡退回重辦後，後續派送關卡新增為兩個代辦
- **Commit ID**: `b84a18ae36d1cd0164ce49d70e7cca1dbca70416`
- **作者**: waynechang
- **日期**: 2019-01-30 09:32:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 52. Q00-20190128001 修正用Excel匯入多語系 如果值有('單引號)會匯入失敗
- **Commit ID**: `089b83e148939a4f6270563f93d28f9525caaeaa`
- **作者**: walter_wu
- **日期**: 2019-01-28 17:28:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`

### 53. Q00-20190124002 修正員工工作批次轉派 開窗輸入密碼後沒有成功轉派
- **Commit ID**: `8aa1630929f2b0571fe2fd1189062115d341d951`
- **作者**: walter_wu
- **日期**: 2019-01-24 18:07:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5657.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 54. 還原誤簽的C01-20190119003程式
- **Commit ID**: `99f0eed5f20d26bc035685181db128936497ec06`
- **作者**: yamiyeh10
- **日期**: 2019-01-23 15:03:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileMethodControl.js`

### 55. C01-20190119003 修正IMG在iOS裝置預覽PDF附件內含電子印章與簽署時異常問題
- **Commit ID**: `417a5be411f4e09a7226ed8229bfa7c973784a38`
- **作者**: yamiyeh10
- **日期**: 2019-01-23 14:28:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileMethodControl.js`

### 56. C01-20181211001 修正ISO文件總管(user為管理程式權限設定權限的人員)下載原始檔出現異常-補上註解
- **Commit ID**: `23789a4efdf87e809f3079d8efafa35ad89d8fdb`
- **作者**: waynechang
- **日期**: 2019-01-22 11:30:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 57. C01-20181211001 修正ISO文件總管(user為管理程式權限設定權限的人員)下載原始檔出現異常
- **Commit ID**: `6ce24438f46f5775ed2fb9ca3a97112ed50bae34`
- **作者**: waynechang
- **日期**: 2019-01-22 11:30:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 58. C01-20190114002 修正列印模式 表單設定不顯示簽核意見還是有顯示
- **Commit ID**: `055505ad565fc90e3bba57af4a3edb5c03268e4e`
- **作者**: walter_wu
- **日期**: 2019-01-21 18:42:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`

### 59. Q00-20190119001 修正離職人員批次轉派 開窗輸入密碼後沒有成功轉派
- **Commit ID**: `e1f0d63ae268a991debaf6716b21418ff174237f`
- **作者**: walter_wu
- **日期**: 2019-01-19 15:13:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 60. C01-20190108003 將自動簽核的通知信範本修改為與跳過關卡用同一個範本
- **Commit ID**: `07b0d4f06dc52a2aecb8184d9237207a9237e7c8`
- **作者**: walter_wu
- **日期**: 2019-01-11 16:49:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 61. A00-20181025003 修正建立作業程序書 WorkStepList 是null報錯
- **Commit ID**: `a4d892b1ed9d60c8627a2861763426d4d9b3e7e4`
- **作者**: walter_wu
- **日期**: 2019-01-10 17:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 62. 調整鼎捷移動登入時更新MobileOAuthWeChatUser表的服務
- **Commit ID**: `a4a8748534fd03dffd19bb4354203588721a0835`
- **作者**: ChinRong
- **日期**: 2019-01-10 17:36:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 63. C01-20190110001 修正TT拋單後IMG收不到推播的問題
- **Commit ID**: `fd75f39c0ba22fd1954b695bcaff542034218ec9`
- **作者**: pinchi_lin
- **日期**: 2019-01-10 15:45:17
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java`

### 64. <V56>C01-20181218014 調整 :發起時前端流程發起人與Session中不一致,不與許發起,避免蓋單
- **Commit ID**: `c18af93b185f49b0fbe9cdceb88617a0d85ea4e9`
- **作者**: 施翔耀
- **日期**: 2019-01-07 15:35:48
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5657.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 65. <V56>二次調整 S00-20181224001:調整ESS流程發起時會有lodaing畫面，避免使用者重複發起
- **Commit ID**: `7f2cc7ff38b2e478586be8aec8ef695c88db737a`
- **作者**: 施翔耀
- **日期**: 2019-01-04 18:15:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 66. C01-20190103008 修正某些bean因沒寫MobileManager的ejb-ref導致呼叫推播方法失敗報錯
- **Commit ID**: `ef5f17962fb768cdf48cd41c4a54b297ea1cbfd3`
- **作者**: pinchi_lin
- **日期**: 2019-01-04 15:45:54
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java`

### 67. <V56>S00-20181224001 發起ESS流程時,將發單按鈕鎖住,避免使用者重複發單造成單據異常 如果ESS有回饋錯誤訊息,再將發單按鈕開啟。
- **Commit ID**: `d27032c9c4d8ea0a93856ab5707f7a51ea429ae0`
- **作者**: 施翔耀
- **日期**: 2019-01-04 13:49:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 68. C01-20180418003 修正:模擬使用者時就算有授權使用者設計師也不應該看到(符合5621之前)
- **Commit ID**: `22a77fe8a9cc836217f066622f4356acd558be92`
- **作者**: walter_wu
- **日期**: 2019-01-03 17:25:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 69. Q00-20190103001 將流程代理人選擇來源部門/專案的開窗從多選改為單選 避免選擇太多部門與太多流程 查詢帶給資料庫太大負擔
- **Commit ID**: `a298108d37421e5d4dd048ae1cd57f4ddde66d6f`
- **作者**: walter_wu
- **日期**: 2019-01-03 15:53:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`

### 70. A00-20181225003 修正因連續點擊退回重辦按鈕導致關卡異常問題
- **Commit ID**: `491d3011a264934e4fa1d7d23977b1044c1d0a97`
- **作者**: yamiyeh10
- **日期**: 2019-01-02 14:40:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 71. 修正企業微信的Android手機在喚醒鍵盤時遮擋住填寫欄位 --可參考C01-20181221002紀錄
- **Commit ID**: `64478fc38180a403116699ba1487af54b6b87185`
- **作者**: yamiyeh10
- **日期**: 2018-12-27 10:40:47
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 72. C01-20181221002 修正IMG的Android手機再喚醒鍵盤時遮擋住填寫欄位
- **Commit ID**: `7d55b9803295b1c67ee22ac7017f15dd19e4500f`
- **作者**: yamiyeh10
- **日期**: 2018-12-27 10:33:55
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 73. C01-20181113001 修正 :因為每30秒會向後端詢問是否有訊息要通知，所以重新啟動BPM站台時，有呼叫後端的行為會彈出異常訊息
- **Commit ID**: `baba3b097bb10f0c737e34daf8221615271f3d5c`
- **作者**: josephshih
- **日期**: 2018-12-24 17:25:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 74. C01-20181210004 修正表單上的簽核歷程的簽核意見文字會被截掉
- **Commit ID**: `051a0c43eb484f8395bb7918ef1ebe67c913331b`
- **作者**: walter_wu
- **日期**: 2018-12-24 10:12:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/FormViewer.jsp`

### 75. <V56>A00-20181207002修正:簽核流設計師中設定關卡處理者為角色或職稱時，會因中文字無法帶出資料
- **Commit ID**: `be3d439045fd62fe1170ad3ac0d8d470fdccdabf`
- **作者**: josephshih
- **日期**: 2018-12-21 17:00:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 76. <V56>C01-20181127001 修正:客戶用LDAP登入 ,因判斷離職的邏輯有問題,導致無法登入
- **Commit ID**: `a2830258bd50eff190a9ca2672cde8c2d3101261`
- **作者**: josephshih
- **日期**: 2018-12-20 17:34:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 77. C01-20181214001 修正IMG中處理的流程依重要性排序時載入資料時會撈到重複的問題
- **Commit ID**: `3a32d0dc2672cb1d9a0389b229b18ff8ce4458b3`
- **作者**: pinchi_lin
- **日期**: 2018-12-18 16:08:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

