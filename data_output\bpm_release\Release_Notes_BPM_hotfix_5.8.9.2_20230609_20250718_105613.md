# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.2_20230609
- **舊版本**: release_5.8.9.2
- **生成時間**: 2025-07-18 10:56:13
- **新增 Commit 數量**: 25

## 變更摘要

### 林致帆 (11 commits)

- **2023-06-07 15:58:33**: [Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系
  - 變更檔案: 1 個
- **2023-06-06 14:44:56**: [ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持"通知"選項
  - 變更檔案: 1 個
- **2023-06-02 11:45:57**: [WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料
  - 變更檔案: 1 個
- **2023-06-02 10:23:40**: [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]
  - 變更檔案: 1 個
- **2023-06-01 10:56:27**: [Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常
  - 變更檔案: 1 個
- **2023-06-01 12:04:53**: [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息
  - 變更檔案: 1 個
- **2023-05-31 17:47:43**: [WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤
  - 變更檔案: 1 個
- **2023-05-26 16:38:31**: [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
  - 變更檔案: 3 個
- **2023-05-24 17:36:47**: [流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常
  - 變更檔案: 1 個
- **2023-05-24 17:01:45**: [Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加
  - 變更檔案: 1 個
- **2023-05-26 10:36:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個

### cherryliao (1 commits)

- **2023-06-07 14:51:50**: [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題
  - 變更檔案: 1 個

### raven.917 (5 commits)

- **2023-06-05 17:14:49**: [流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求
  - 變更檔案: 1 個
- **2023-06-02 10:54:49**: [Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題
  - 變更檔案: 3 個
- **2023-05-26 14:59:32**: [Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題
  - 變更檔案: 1 個
- **2023-05-25 10:16:09**: [Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題
  - 變更檔案: 1 個
- **2023-05-25 19:35:08**: [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
  - 變更檔案: 1 個

### develop_20274 (5 commits)

- **2023-06-05 10:37:25**: [Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示
  - 變更檔案: 1 個
- **2023-05-30 10:27:14**: [Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態
  - 變更檔案: 1 個
- **2023-05-29 17:06:25**: [Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)
  - 變更檔案: 1 個
- **2023-05-29 16:54:46**: [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正
  - 變更檔案: 2 個
- **2023-05-26 15:02:51**: [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題
  - 變更檔案: 1 個

### yamiyeh10 (2 commits)

- **2023-06-01 15:10:16**: [BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題
  - 變更檔案: 1 個
- **2023-05-26 10:10:30**: [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]
  - 變更檔案: 1 個

### waynechang (1 commits)

- **2023-06-01 11:24:59**: [其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容
  - 變更檔案: 2 個

## 詳細變更記錄

### 1. [Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系
- **Commit ID**: `8c565b7e43091d7c353185f535ac2d264dcb5760`
- **作者**: 林致帆
- **日期**: 2023-06-07 15:58:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java`

### 2. [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題
- **Commit ID**: `8812df4c09c48db0f2d506641f0d4239796cdba6`
- **作者**: cherryliao
- **日期**: 2023-06-07 14:51:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`

### 3. [ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持"通知"選項
- **Commit ID**: `05a01ebc45a6add25c0f63a8346dd8b052a31834`
- **作者**: 林致帆
- **日期**: 2023-06-06 14:44:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 4. [流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求
- **Commit ID**: `ecc88a61dbc4ea0d5ae16956dfac7e9c842d04e7`
- **作者**: raven.917
- **日期**: 2023-06-05 17:14:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/WebApplicationAgent.java`

### 5. [Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示
- **Commit ID**: `49a235dc520fd84652e53353c19fa8885e68c652`
- **作者**: develop_20274
- **日期**: 2023-06-05 10:37:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`

### 6. [Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題
- **Commit ID**: `8088ac8fef882d3642e763766e45acdc61474dd9`
- **作者**: raven.917
- **日期**: 2023-06-02 10:54:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 7. [WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料
- **Commit ID**: `f48fac77aa1f751759f325043641f43783dbc0e1`
- **作者**: 林致帆
- **日期**: 2023-06-02 11:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 8. [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]
- **Commit ID**: `acdfd7b7d457a9d312a383bdfdcf3d542c86ff76`
- **作者**: 林致帆
- **日期**: 2023-06-02 10:23:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 9. [BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題
- **Commit ID**: `01f34b18f3cb2674099a28bbc63dd503bc57ceea`
- **作者**: yamiyeh10
- **日期**: 2023-06-01 15:10:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 10. [其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容
- **Commit ID**: `8186af7b5bb50188f75c03ec114653c012364ad8`
- **作者**: waynechang
- **日期**: 2023-06-01 11:24:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`

### 11. [Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常
- **Commit ID**: `dee1ff9a9f49a64f2036216c91549582832b47c5`
- **作者**: 林致帆
- **日期**: 2023-06-01 10:56:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`

### 12. [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息
- **Commit ID**: `bbba77b6293efa1c468c03bbea5681d634c91537`
- **作者**: 林致帆
- **日期**: 2023-06-01 12:04:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 13. [WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤
- **Commit ID**: `7465c9e3fae294f55777e734f7262a6de032f0fd`
- **作者**: 林致帆
- **日期**: 2023-05-31 17:47:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 14. [Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態
- **Commit ID**: `f1654eb781f177acdabd651a99072bebbfb3464f`
- **作者**: develop_20274
- **日期**: 2023-05-30 10:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 15. [Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)
- **Commit ID**: `256583ba35be24e43684df0f48927bbe9ab01313`
- **作者**: develop_20274
- **日期**: 2023-05-29 17:06:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 16. [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正
- **Commit ID**: `f879995c811268ee03a7fa0f5bb44fb8b838acc1`
- **作者**: develop_20274
- **日期**: 2023-05-29 16:54:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 17. [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
- **Commit ID**: `eb4fcda9bed15bec18820491e2c67e769a24a91d`
- **作者**: 林致帆
- **日期**: 2023-05-26 16:38:31
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 18. [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題
- **Commit ID**: `d72accd32ca2ac64d731380966e8b8f53529744d`
- **作者**: develop_20274
- **日期**: 2023-05-26 15:02:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 19. [Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題
- **Commit ID**: `4ae609cd95bff0a2b8728345da221270ec06eafb`
- **作者**: raven.917
- **日期**: 2023-05-26 14:59:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 20. [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]
- **Commit ID**: `0eabc66d8706bfec8d0f937d7f5ad593bfbc736b`
- **作者**: yamiyeh10
- **日期**: 2023-05-26 10:10:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 21. [Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題
- **Commit ID**: `64fa32e7bc15301766f00da2e16ea1dbe87fdc20`
- **作者**: raven.917
- **日期**: 2023-05-25 10:16:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 22. [流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常
- **Commit ID**: `ab7a426ee61294822f61d9d19fb01ace9ac934c4`
- **作者**: 林致帆
- **日期**: 2023-05-24 17:36:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java`

### 23. [Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加
- **Commit ID**: `cb8973a1085888406960b5ae2c4373b226bd9b14`
- **作者**: 林致帆
- **日期**: 2023-05-24 17:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 24. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `ed1a7b3d587b5d6d7a041912c9f502e537d2eff5`
- **作者**: 林致帆
- **日期**: 2023-05-26 10:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 25. [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
- **Commit ID**: `798a76611aaaefd9e7ca40e01e571b75e5b99e7a`
- **作者**: raven.917
- **日期**: 2023-05-25 19:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

