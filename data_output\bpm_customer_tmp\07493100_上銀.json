{"company_id": "07493100", "company_name": "上銀", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\07493100_上銀\\01客戶基本資料", "files": [{"filename": "連線資訊.txt", "raw_content": "2023 更新 改用 VPN 連線\r\n使用 GlobalProtect VPN Clinet\r\naddress 輸入vpn.hiwin.tw\r\n帳號:chiayi\r\n密碼:Aa@00017448\r\n\r\n登入指定遠端主機\r\n[開始功能表]-[chrome]\r\n輸入URL：https://rdslb.hiwin.tw\r\n  帳號:chiayi\r\n  密碼:Aa@00017448\r\n\r\n點選遠端VNC連線\r\n*************\r\n密碼 123456\r\n\r\n\r\nBPM AP\r\n\r\n10.177.200.133 正式機\r\n10.177.200.134 測試機\r\n\r\nOracle DB: 123 \r\nWindows環境\r\n\r\nSELECT * From workflowserver\r\ncommit; (下commit就是把資料寫進去 Oracle DB)\r\n\r\n[正式機DB] IP:123\r\nSID(資料庫名稱)：nanadsc \r\n帳密：nana / dsc5678\r\n\r\n[測試機DB] IP:123\r\nSID：nanadsc\r\n帳密：nana2_test / dsc5678\r\n\r\n11/1上銀正式環境準備 步驟紀錄\r\n1. 先將133(正式機)這台的所有流程撤銷or結案\r\n2. 將正式機所有流程刪除，要把所有流程都清空\r\n3. 確認系統管理員的測試\r\n\r\n\r\n郭南麟(資訊-VPN連線、組織同步相關事宜；經理)\r\n04-2359-4510 #1190\r\n<EMAIL>\r\n\r\n更新:明道來信說之後相關窗口都對他(包含開連線都是找明道副理)\r\n上銀科技股份有限公司 資訊二部三組 蔡明道 (副理)\r\nTel: +886-4-23594510  #1127\r\ne-Mail: <EMAIL>\r\n \r\n \r\n上銀的Oracle DB版本: 11.2.4\r\n\r\n組織資料同步相關事宜 主要MIS窗口為益昌(經理)", "structured_data": {"輸入url": "https://rdslb.hiwin.tw", "sid(資料庫名稱)": "nanadsc", "帳密": "nana2_test / dsc5678", "sid": "nanadsc", "username": "chia<PERSON>", "password": "Aa@00017448", "輸入url：https": "//rdslb.hiwin.tw", "oracle db": "123", "[正式機db] ip": "123", "[測試機db] ip": "123", "更新": "明道來信說之後相關窗口都對他(包含開連線都是找明道副理)", "tel": "+886-4-23594510  #1127", "e-mail": "<EMAIL>", "上銀的oracle db版本": "11.2.4", "host": "*************"}, "source_path": "C1.客戶維護相關\\07493100_上銀\\01客戶基本資料\\連線資訊.txt", "file_size": 1295, "encoding_used": "UTF-8-SIG", "processed_at": "2025-08-26T10:46:25.082398"}], "total_files": 1, "processed_at": "2025-08-26T10:46:25.082407"}