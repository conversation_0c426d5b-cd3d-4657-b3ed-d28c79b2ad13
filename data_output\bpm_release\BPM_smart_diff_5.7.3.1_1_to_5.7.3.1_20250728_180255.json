{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.7.3.1_1", "date": "tag 5.7.3.1_1\nTagger: 張詠威 <<EMAIL>>\n\n因調整出貨SQL，故重新壓5.7.3.1_1 tag2018-08-27 17:09:18", "message": "調整DB將ntext 調整為 clob", "author": "wayne<PERSON>"}, "舊分支": {"branch_name": "5.7.3.1", "date": "tag 5.7.3.1\nTagger: 張詠威 <<EMAIL>>\n\n20180827-11:00 last build2018-08-27 10:41:25", "message": "調整 : Ajax的query方法轉呼叫queryWithCondition", "author": "jose<PERSON>"}, "比較時間": "2025-07-28 18:02:55", "新增commit數量": 2, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "1e612e82746eda39de03c1cc81b1c7224287618b", "commit_訊息": "調整DB將ntext 調整為 clob", "提交日期": "2018-08-27 17:09:18", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9302e3a613cdc2ed393f265467e048a66e5347f", "commit_訊息": "調整DB Create 語法", "提交日期": "2018-08-27 16:03:57", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}