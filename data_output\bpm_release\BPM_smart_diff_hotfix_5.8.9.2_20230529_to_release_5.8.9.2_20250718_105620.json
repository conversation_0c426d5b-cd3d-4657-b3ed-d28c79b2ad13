{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.2_20230529", "date": "2023-05-26 16:38:31", "message": "[WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起", "author": "林致帆"}, "舊分支": {"branch_name": "release_5.8.9.2", "date": "2023-05-26 10:36:32", "message": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 10:56:20", "新增commit數量": 9, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "eb4fcda9bed15bec18820491e2c67e769a24a91d", "commit_訊息": "[WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起", "提交日期": "2023-05-26 16:38:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d72accd32ca2ac64d731380966e8b8f53529744d", "commit_訊息": "[Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題", "提交日期": "2023-05-26 15:02:51", "作者": "develop_20274", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ae609cd95bff0a2b8728345da221270ec06eafb", "commit_訊息": "[Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題", "提交日期": "2023-05-26 14:59:32", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0eabc66d8706bfec8d0f937d7f5ad593bfbc736b", "commit_訊息": "[WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]", "提交日期": "2023-05-26 10:10:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "64fa32e7bc15301766f00da2e16ea1dbe87fdc20", "commit_訊息": "[Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題", "提交日期": "2023-05-25 10:16:09", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ab7a426ee61294822f61d9d19fb01ace9ac934c4", "commit_訊息": "[流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常", "提交日期": "2023-05-24 17:36:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cb8973a1085888406960b5ae2c4373b226bd9b14", "commit_訊息": "[Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加", "提交日期": "2023-05-24 17:01:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed1a7b3d587b5d6d7a041912c9f502e537d2eff5", "commit_訊息": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "提交日期": "2023-05-26 10:36:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "798a76611aaaefd9e7ca40e01e571b75e5b99e7a", "commit_訊息": "[組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題", "提交日期": "2023-05-25 19:35:08", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}