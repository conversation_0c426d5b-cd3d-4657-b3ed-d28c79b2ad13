{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.3.3", "date": "2022-06-26 22:58:30", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.3.3", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.3.2", "date": "2022-06-26 22:52:34", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.3.2", "author": "lorenchang"}, "比較時間": "2025-07-18 11:44:50", "新增commit數量": 39, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "c0c210b7f9f7681079b38fb39f8d629c50b1d17b", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.3.3", "提交日期": "2022-06-26 22:58:30", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "5b5462031f65356b32c6ff95658a9c52e1a05ce5", "commit_訊息": "[BPM APP]Q00-20200729001修正移動授權中間層整合釘釘時無法登入系統，取RedirectUrl時拋NullpointerException", "提交日期": "2020-07-29 19:57:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bdd8476dfe03419eab8a4b682bec55d898223e47", "commit_訊息": "[BPM APP]Q00-20200617003 修正行動端在使用表單函式庫的設定元件字體顏色功能沒有效果問題[補]", "提交日期": "2020-07-28 17:02:01", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d90a1f9945e0de3efe719ef8e23987605131b858", "commit_訊息": "[ISO] C01-20200727009修正BCL8轉檔時，未釋放bepprint.exe資源", "提交日期": "2020-07-28 11:04:11", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5c49ddd1d5f737183d2fd1ee7c13fa4bffa6e71e", "commit_訊息": "[Web]Q00-20200727001 修正監控流程功能按下產生統計圖，會有sql報錯", "提交日期": "2020-07-27 16:20:51", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d9aa021beeb73e1b6f524120b8db84a5744d256", "commit_訊息": "[BPM APP]Q00-20200710001 修正中間層簽核歷程退回重辦意見覆蓋顯示問題", "提交日期": "2020-07-24 17:59:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f8bb8bba01e7483ebab2811967a497afd34df2c", "commit_訊息": "[BPM APP]Q00-20200428001 修正產品與客製開窗與加簽畫面中欄位增加導致checkbox樣式跑版問題", "提交日期": "2020-07-24 14:55:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "695a08fef0da84ccdd72667c9904fa918418f0c6", "commit_訊息": "[Web]A00-20200715002 修正表單欄位有設定運算規則，當其中某個欄位有負數，造成js報錯[補修正]", "提交日期": "2020-07-24 14:49:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9aa6656847ae2e0db643745f74ee7ed5ac66f6d1", "commit_訊息": "[Web]A00-20200715001 修正表單欄位為invisible且設定顯示千分位，開起表單在F12顯示會報錯", "提交日期": "2020-07-24 14:41:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b180fe47ea0fe594636f4a9f9e6c2fc025f91224", "commit_訊息": "[Web]A00-20200720002修正手機端點選檢視附件，附件頁面無檔名也無法查看附件內容", "提交日期": "2020-07-24 08:40:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b8e4789fbc1b41fe47d7ad1ce5e5d5dc0d3b1c9d", "commit_訊息": "[流程引擎]A00-20200717002 修正從TipTop點擊簽核狀況，導入畫面為Portal登入頁面而不是BPM單據頁面", "提交日期": "2020-07-23 18:06:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fde1b99fe24388ea0c4e1fd01b4bfa3fbc1212e3", "commit_訊息": "[Tiptop]A00-20200528001 修正問題: tpform包含單獨的label導致表單定義更新失敗", "提交日期": "2020-07-23 11:40:17", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-importer/src/com/dsc/nana/user_interface/apps/form_importer/TiptopLabelElementTransfer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e84ee2e33bffe01e96c896581c83033a16aca1c6", "commit_訊息": "A00-20200720003 修正點擊追蹤流程左測的流程分類下的流程，清單頁顯示異常", "提交日期": "2020-07-22 18:36:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74b10c17be95feae24d395553708c3534a9e4ab0", "commit_訊息": "[Web]Q00-20200722001 修正模組程式維護中刪除一筆程式資料並點擊一筆Row，則欄位上的值顯示不正確，而最後一筆會無法帶回欄位", "提交日期": "2020-07-22 17:59:22", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4b9245c37f2f86242dab4fbe33e60ea37b57bb8e", "commit_訊息": "[Web]A00-20200713002 將模組程式維護頁面中，已加入的程式資料不可更改程式代號。", "提交日期": "2020-07-21 16:46:10", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9733db082b3946b115f07f6cfbe8023ce37e8563", "commit_訊息": "[Web]A00-20200716001 修正從TT傳回來的表單有欄位為千分位，儲存表單後數字會不正確", "提交日期": "2020-07-20 15:43:04", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c21ce1f7a06ac8559e3245304868dc33e97fb842", "commit_訊息": "[表單設計師]S00-20170531001 在RWD表單設計師中，複合元件的開窗類型為\"部門\"、\"專案\"或\"群組\"時，預設勾選\"前置組織代號\"", "提交日期": "2020-07-20 14:30:49", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ad325b84536688befe92863802bf4612c28cf25", "commit_訊息": "[Web]C01-20200716001 修正資料庫為Oracle時，設一般使用者為某流程的負責人，當使用者進入監控流程頁面中左側顯示的筆數異常", "提交日期": "2020-07-17 16:27:20", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c0e99c213e8b65004d94b121dd708681cd6cbe17", "commit_訊息": "[Web]A00-20200715002 修正表單欄位有設定運算規則，當其中某個欄位有負數，造成js報錯", "提交日期": "2020-07-16 17:54:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "846a9b0c7f12f614049ff6da0afa6e35c4ec6d0d", "commit_訊息": "[Web]A00-20200710001修正RWD響應式中的Radio元件屬性設定「最後一個選項額外產生輸入框」的功能，在流程執行到第三關時，該TextBox欄位內容消失", "提交日期": "2020-07-14 18:15:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7b6ca1909a8cfea4769d0f6d31f8f778f0b44b1", "commit_訊息": "[流程引擎]A00-20200512001 優化刪除系統排程時，不須透過重啟Server的方式就能停止排程", "提交日期": "2020-07-13 17:17:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SystemScheduleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3e7191bec1bc45802d6e4dab29a0663b30ff996f", "commit_訊息": "[流程設計師]Q00-20200710003 修正流程設計師，當核決關卡設定參考活動為自行定義關卡後，簽入簽出後就無法重新開啟核決關卡", "提交日期": "2020-07-10 17:42:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/TransitionRestriction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ff9b806a6d7a35fddb79519659b838911d69d383", "commit_訊息": "[流程引擎]A00-20200527001 修正核決關卡設定參考活動設定自行定義後，流程要派送到核決關卡會出現錯誤", "提交日期": "2020-07-10 17:35:54", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f7abf0e719ba2e4fbac965ff46334bf971d409b0", "commit_訊息": "[表單設計師]C01-20200629004 產品開窗增加過濾條件可以使用組織代號和組織名稱，且開窗的條件也可選這兩個新增的條件", "提交日期": "2020-07-08 18:30:50", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e94919a19e7fb3c9a6763df88421181280f07e9c", "commit_訊息": "[流程設計師]A00-20200324002 修正當流程未進版時，若更改核決權限關卡的Id，會造成核決關卡無法點開[補修正]", "提交日期": "2020-07-08 14:06:11", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0d0c0f426690b3a7810f45428cbcf56a09d4e037", "commit_訊息": "[流程設計師]A00-20200324002 修正當流程未進版時，若更改核決權限關卡的Id，會造成核決關卡無法點開", "提交日期": "2020-07-08 11:24:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f2a06ed6aa54d18249a62a1faa3bc969dd947a83", "commit_訊息": "[BPM APP]Q00-20200601001 修正IMG應用角標不出現問題", "提交日期": "2020-07-07 19:39:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "19959150e748c1bd5a53cfb96b3f70838006e6e5", "commit_訊息": "[流程引擎]A00-20200618001 修正從Mail待辦事項的連結進入時，如果該使用者待辦為0筆，開啟的畫面會是待辦清單的異常", "提交日期": "2020-07-07 18:09:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c4a10c9d932902ffaff722f6d9f44d97ef6b1d7", "commit_訊息": "[Web]Q00-20200707001 調整Backspace控制，讓OA使用的文件編輯套件可以使用", "提交日期": "2020-07-07 16:06:23", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b5520e728c4180520db81aa89c4ed44cf6714d84", "commit_訊息": "[BPM APP]Q00-20200617003 修正行動端在使用表單函式庫的設定元件字體顏色功能沒有效果問題", "提交日期": "2020-07-06 14:09:46", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7cd638d57624ba9cdac8b2c91dc32e95734be134", "commit_訊息": "[流程引擎]Q00-20200702001 修正追蹤流程清單統計接口邏輯", "提交日期": "2020-07-03 10:12:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4faa9d8fdd554a4340fe338b94368540f3f4c4f", "commit_訊息": "[Web]A00-20200617001修正流程關卡派送到下一關為多人處理關卡畫面時,TextArea元件為可編輯狀態", "提交日期": "2020-07-02 18:22:10", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "19c9b0e03affcdd3a357c3f5dabbe57e54431933", "commit_訊息": "[流程引擎]Q00-20200618001 修正問題: 追蹤流程清單api未回傳執行中的處理者", "提交日期": "2020-07-02 11:01:59", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "a6647b6fd30e34cfe51303bdce98051b095acd6b", "commit_訊息": "[Web]A00-20200608001修正列出工作受託者清單在流程點擊派送後，若下一關無人處理，瀏覽器的開發者工具會出現gBpmList未經定義的錯誤", "提交日期": "2020-07-01 18:21:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36a158cf3bd794cae6d3434799cf3263bf13d234", "commit_訊息": "[BPM APP]C01-20200624003調整產生直連表單表單畫面判斷邏輯", "提交日期": "2020-06-30 14:50:22", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fc009820cfa59422caf16f42f1dc15188ea27d74", "commit_訊息": "[BPM APP]C01-20200624003調整鼎捷移動直連表單網址串上流程OID[補-還原程式邏輯]", "提交日期": "2020-06-30 14:46:41", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3efc96a8b6783c43f488032846f15b44805d8c46", "commit_訊息": "[BPM APP]C01-20200624003調整鼎捷移動直連表單網址串上流程OID", "提交日期": "2020-06-30 09:43:10", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82de0a3b13398181d0333e89a7146fc126c3bb0f", "commit_訊息": "[Web]C01-20200618005 修正在系統設定中的password.policy.rule，輸入規則以外的值導致變更密碼頁面開啟會全白", "提交日期": "2020-06-29 18:28:18", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c2974355bcf0820d55fd04a8bea88fadd5855736", "commit_訊息": "[Web]C01-*********** 修正企業流程監控系列功能流程統計的異常", "提交日期": "2020-06-24 16:34:31", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BamManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}]}