# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_*******_haers
- **舊版本**: release_*******
- **生成時間**: 2025-07-18 11:23:24
- **新增 Commit 數量**: 26

## 變更摘要

### yamiyeh10 (3 commits)

- **2024-11-18 13:39:20**: [BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題[補]
  - 變更檔案: 1 個
- **2024-11-11 16:08:16**: [BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題
  - 變更檔案: 10 個
- **2022-09-07 14:30:59**: [Web]Q00-20220825002 調整模組程式維護作業加入系統語系供使用者設定
  - 變更檔案: 5 個

### 周权 (1 commits)

- **2024-11-04 14:00:15**: [Web] C01-20241104003 调整从pHttpServletRequest获取ip及port
  - 變更檔案: 1 個

### waynechang (2 commits)

- **2024-01-25 13:48:06**: [內部]Q00-20240125001 調整清除二階快取的log層級由warn改為debug
  - 變更檔案: 1 個
- **2023-03-01 14:43:10**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
  - 變更檔案: 1 個

### raven.917 (1 commits)

- **2022-10-26 14:13:48**: [流程引擎]C01-20241030009 修正轉由他人處理之待辦通知信內容簽核歷程缺失問題
  - 變更檔案: 1 個

### lorenchang (4 commits)

- **2021-08-20 09:06:16**: [內部]新增清除所有Server二階快取的相關EJB及RMI接口
  - 變更檔案: 4 個
- **2022-06-26 22:27:16**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
  - 變更檔案: 25 個
- **2021-02-04 18:06:42**: [DotJ]Q00-20210204004 修正修正如果流程有核決權限表，送簽會出現LazyInitializationException
  - 變更檔案: 1 個
- **2021-04-07 12:54:58**: [DotJ]Q00-20210407001 修正流程派送異常
  - 變更檔案: 2 個

### 林致帆 (2 commits)

- **2021-04-26 18:43:06**: [內部]Q00-20210426003 多AP主機狀況，在清除二階快取下，若AP呼叫失敗，增加log訊息讓錯誤更明確[補]
  - 變更檔案: 1 個
- **2021-04-26 18:39:53**: [內部]Q00-20210426003 多AP主機狀況，在清除二階快取下，若AP呼叫失敗，增加log訊息讓錯誤更明確
  - 變更檔案: 1 個

### cherryliao (5 commits)

- **2023-06-20 15:54:17**: [內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息
  - 變更檔案: 1 個
- **2023-04-14 10:47:52**: [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
  - 變更檔案: 1 個
- **2023-02-14 14:03:47**: [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題
  - 變更檔案: 2 個
- **2022-11-11 11:07:05**: [Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息
  - 變更檔案: 1 個
- **2022-09-08 14:06:21**: [Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息
  - 變更檔案: 2 個

### walter_wu (6 commits)

- **2022-07-29 14:20:21**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]
  - 變更檔案: 2 個
- **2022-07-29 00:04:37**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
  - 變更檔案: 3 個
- **2022-06-10 18:10:24**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
  - 變更檔案: 1 個
- **2022-04-19 16:50:47**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
  - 變更檔案: 1 個
- **2022-03-24 17:25:19**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出
  - 變更檔案: 1 個
- **2022-04-25 16:59:12**: 哈爾斯目前所有下修<<一線提供
  - 變更檔案: 16 個

### pinchi_lin (2 commits)

- **2022-01-18 18:52:13**: [BPM APP]20210519哈爾斯個案客製功能-第三方APP整合功能[補]
  - 變更檔案: 2 個
- **2022-01-18 18:48:37**: [BPM APP]20210519哈爾斯個案客製功能-第三方APP整合功能
  - 變更檔案: 38 個

## 詳細變更記錄

### 1. [BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題[補]
- **Commit ID**: `ece33c45215f2f4a68914652f6926ae0935f066b`
- **作者**: yamiyeh10
- **日期**: 2024-11-18 13:39:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 2. [BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題
- **Commit ID**: `b76de9a4a5047cb3fd309866eee60c4d1429e359`
- **作者**: yamiyeh10
- **日期**: 2024-11-11 16:08:16
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 3. [Web] C01-20241104003 调整从pHttpServletRequest获取ip及port
- **Commit ID**: `82ea502bf640d3cd46c44b94ee7e21671bbf1954`
- **作者**: 周权
- **日期**: 2024-11-04 14:00:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 4. [內部]Q00-20240125001 調整清除二階快取的log層級由warn改為debug
- **Commit ID**: `72b47d3b87a9138dd4323276ad38fb2976fe41e4`
- **作者**: waynechang
- **日期**: 2024-01-25 13:48:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`

### 5. [流程引擎]C01-20241030009 修正轉由他人處理之待辦通知信內容簽核歷程缺失問題
- **Commit ID**: `7236f7ad0b59ad0015252718be1c545cf2610d97`
- **作者**: raven.917
- **日期**: 2022-10-26 14:13:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 6. [內部]新增清除所有Server二階快取的相關EJB及RMI接口
- **Commit ID**: `14effa70d77d53e1b3dd18b8e5ff68b2007256df`
- **作者**: lorenchang
- **日期**: 2021-08-20 09:06:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IServerCacheManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java`

### 7. [內部]Q00-20210426003 多AP主機狀況，在清除二階快取下，若AP呼叫失敗，增加log訊息讓錯誤更明確[補]
- **Commit ID**: `7fd5f9b6b87fa81bed91c3a66897a909e6ea4f41`
- **作者**: 林致帆
- **日期**: 2021-04-26 18:43:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`

### 8. [內部]Q00-20210426003 多AP主機狀況，在清除二階快取下，若AP呼叫失敗，增加log訊息讓錯誤更明確
- **Commit ID**: `3a62153608237953b3c64408c49c91cfa640edb8`
- **作者**: 林致帆
- **日期**: 2021-04-26 18:39:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`

### 9. [內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息
- **Commit ID**: `84e8955e40c68947eab3ec12d2a35fb5a3a2df01`
- **作者**: cherryliao
- **日期**: 2023-06-20 15:54:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 10. [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
- **Commit ID**: `dd980842ac6e703d2dadf3ccda28d2079550a3bc`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 11. [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題
- **Commit ID**: `5b79b1ab6b9fc4e4b5dc6588f3d8d0bf20758cdf`
- **作者**: cherryliao
- **日期**: 2023-02-14 14:03:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 12. [Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息
- **Commit ID**: `ed44caa78bd5df217e554b5949593a7531945623`
- **作者**: cherryliao
- **日期**: 2022-11-11 11:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 13. [Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息
- **Commit ID**: `036062b78928b00cf83a473c5dc3f174167921a2`
- **作者**: cherryliao
- **日期**: 2022-09-08 14:06:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 14. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]
- **Commit ID**: `481aa483cfe8e2800bd13fb57490fe7b34d75059`
- **作者**: walter_wu
- **日期**: 2022-07-29 14:20:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 15. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
- **Commit ID**: `55de2b76829189816421168db50b10cb78ebc7d3`
- **作者**: walter_wu
- **日期**: 2022-07-29 00:04:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 16. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
- **Commit ID**: `dc5af06ad4d64f53793359b9f6adcdd7351a0e8b`
- **作者**: walter_wu
- **日期**: 2022-06-10 18:10:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 17. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
- **Commit ID**: `917839ee0694d1c6fe85965ad83e2afc34915984`
- **作者**: walter_wu
- **日期**: 2022-04-19 16:50:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 18. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出
- **Commit ID**: `ac6c6d1bc4c8b84b3c503fe0251578d64fadf272`
- **作者**: walter_wu
- **日期**: 2022-03-24 17:25:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 19. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
- **Commit ID**: `e23535b97848438e33f96f7685006a39b262789e`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:27:16
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 20. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
- **Commit ID**: `17abfef5eef2e5f60dc5068d76e617e19b606134`
- **作者**: waynechang
- **日期**: 2023-03-01 14:43:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 21. [Web]Q00-20220825002 調整模組程式維護作業加入系統語系供使用者設定
- **Commit ID**: `14c4d55bddf849368c2c1ff28b98221735087bc4`
- **作者**: yamiyeh10
- **日期**: 2022-09-07 14:30:59
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProgramViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`

### 22. [DotJ]Q00-20210204004 修正修正如果流程有核決權限表，送簽會出現LazyInitializationException
- **Commit ID**: `aad9c34702497282a7169a699c72dbbe27fbba9d`
- **作者**: lorenchang
- **日期**: 2021-02-04 18:06:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 23. [DotJ]Q00-20210407001 修正流程派送異常
- **Commit ID**: `a9cc2361d9bb5cb0395a52e90454ea69d9fcbe6a`
- **作者**: lorenchang
- **日期**: 2021-04-07 12:54:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`

### 24. 哈爾斯目前所有下修<<一線提供
- **Commit ID**: `3f442e7f3ee3ce455fe754f8e3db78dccdd33dbb`
- **作者**: walter_wu
- **日期**: 2022-04-25 16:59:12
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramAction.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Organization.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/AbstractTableDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainFunctionLevelDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/TreeViewDataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 25. [BPM APP]20210519哈爾斯個案客製功能-第三方APP整合功能[補]
- **Commit ID**: `1bc74b93927cc81f0e38e558cd8ab3418f1e958f`
- **作者**: pinchi_lin
- **日期**: 2022-01-18 18:52:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CustomAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 26. [BPM APP]20210519哈爾斯個案客製功能-第三方APP整合功能
- **Commit ID**: `313349b5991b12bd87ad14cac166bad7144b5165`
- **作者**: pinchi_lin
- **日期**: 2022-01-18 18:48:37
- **變更檔案數量**: 38
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileCustomClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CustomExtraServiceParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CustomExtraServiceReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CustomExtraServiceStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CustomExtraServiceExecutionRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CustomExtraServiceParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CustomExtraServiceRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CustomExtraServiceStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/CustomRESTful.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/CustomServiceAuthenticate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/CustomRESTfulMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomAppListContact.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomAppListNotice.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomAppListToDo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomAppListTraceInvoked.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomAppListTracePerformed.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomMobileFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomMobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomMobileTraceInvokedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/CustomMobileTracePerformedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomAppCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomAppListContact.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomAppListNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomAppListToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomAppListTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomAppListTracePerformed.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomMobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomMobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomMobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomMobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/CustomMobileTracePerform.js`

