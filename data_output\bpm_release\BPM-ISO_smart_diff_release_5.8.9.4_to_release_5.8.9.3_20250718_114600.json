{"比較資訊": {"專案ID": "BPM-ISO", "倉庫路徑": "D:\\IDEA_workspace\\BPM-ISO", "新分支": {"branch_name": "release_5.8.9.4", "date": "2023-12-05 15:26:01", "message": "[附件擴充] 修正參數未開啟時判斷邏輯異常問題", "author": "邱郁晏"}, "舊分支": {"branch_name": "release_5.8.9.3", "date": "2023-08-10 10:42:52", "message": "[ISO]Q00-20230809001 ISO文件新增單的invoke服務任務增加ISO相關屬性log", "author": "wayne<PERSON>"}, "比較時間": "2025-07-18 11:46:00", "新增commit數量": 35, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "449e8e4de676c2ee31023d833efdaf3da70dc77f", "commit_訊息": "[附件擴充] 修正參數未開啟時判斷邏輯異常問題", "提交日期": "2023-12-05 15:26:01", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "63224f76d78377b169c4214aefef3f418d3ed477", "commit_訊息": "[附件擴充] 上傳下載附件擴充機制: PDF閱讀機制", "提交日期": "2023-11-23 14:08:18", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37cf416d8d97eeb4c234a02af777d860389d33ab", "commit_訊息": "[附件擴充] 上傳下載附件擴充機制: PDF轉檔下載事件", "提交日期": "2023-11-23 11:31:39", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a7b8191d368d41646b6fbd986548159bf4d2f22", "commit_訊息": "[附件擴充] 上傳下載附件擴充機制:下載檔案機制", "提交日期": "2023-11-23 10:34:45", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4ab8a203781a0355e785411a85b5c422437a8b67", "commit_訊息": "[附件擴充] 上傳下載附件擴充機制", "提交日期": "2023-11-22 17:08:49", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "40ef2014b68475b6d16be54d4c695e03d24381e5", "commit_訊息": "[ISO] Q00-20231122001 新增評審規則刪除前檢查是否有文件引用", "提交日期": "2023-11-22 12:05:07", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOVettingRule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISODocCmItem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOVettingRuleController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOVettingRuleMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1e45e5196483ba868e51d2fd26323430135c5c4e", "commit_訊息": "[ISO]Q00-20231121007 修正ISO文件屬性管理-索引卡的相關單位無法同時新增「部門、專案、群組」這三種類型的單位", "提交日期": "2023-11-21 17:21:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/UpdateDocumentInfo.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8193914e295e1b54964cb3103b902bd963d43b81", "commit_訊息": "[ISO] Q00-20231116004 調整文件歸還申請單，隱藏欄位空值導致通知關卡未觸發問題", "提交日期": "2023-11-16 17:15:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOPaperWriteOff.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d936328c0149331783bce49dbb668418e61f7bc2", "commit_訊息": "[ISO] S00-20230703004 文件屬性管理新增全文檢索功能", "提交日期": "2023-11-16 15:09:57", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISODocUpdate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISODocCmItemDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0fc1459adbdfcaf80a2d979cf85cf0937d6dd69d", "commit_訊息": "[ISO] TFG專案", "提交日期": "2023-11-13 14:35:17", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "22afdb6224510c3ab1deeb8a58647f8528209740", "commit_訊息": "[ISO] TFG專案", "提交日期": "2023-11-10 16:38:57", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "56df686862973a81ef4e41c645092f0057944178", "commit_訊息": "[ISO] TFG專案", "提交日期": "2023-11-09 14:58:58", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/WEB-INF/lib/nana-services-client.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileReadController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocDeployMgr2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "11c0cf2e553db8e6abb5513e80e80e8ff40f7645", "commit_訊息": "[ISO]Q00-20231108003 調整ISO閱讀PDF檔案頁面，增加封鎖ctrl+s 下載檔案功能，只允許透過頁面上的下載按鈕", "提交日期": "2023-11-08 16:38:03", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/PDFWebView/web/BPMviewer.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/PDFWebView/web/BPMviewer.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "58f7313acc286292a43ceb13d91d790b712f1ad0", "commit_訊息": "[ISO]Q00-20231031002 修正ISO文件的制定單位或保管單位為群組時，組通知信件時會發生錯誤，導致生失效通知信無法寄送的異常", "提交日期": "2023-10-31 14:42:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODocMailMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "36eeb51edaa0368da531d50ceb7a189d3fbe8404", "commit_訊息": "Revert \"[ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)\"", "提交日期": "2023-10-31 14:18:25", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "197cd972757d5a2252ba155e21e3d1db74a1a801", "commit_訊息": "[ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)", "提交日期": "2023-10-31 14:15:29", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "40aeca1a855c4de9513a358dee870c978761d113", "commit_訊息": "[ISO] S00-20230818001 新增ISO可自定義文件浮水印字體(補)", "提交日期": "2023-10-30 17:33:22", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6518b50b6c4bea55112c41484451e7d380b6c9a4", "commit_訊息": "[ISO] S00-20230818001 新增ISO可自定義文件浮水印字體", "提交日期": "2023-10-27 14:03:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f945afba2cd8c0801f254a996518be02b18e27f4", "commit_訊息": "[ISO] Q00-20231025003 修正行業別變更單據開窗選擇文件編號異常問題", "提交日期": "2023-10-25 14:10:36", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/CustomISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0ea331b50b520bf70e09e0175e78bdf228525fc9", "commit_訊息": "[ISO] Q00-20231024001 修正行業別文管開窗選取到正在變更中的文件，新增查詢條件(補)", "提交日期": "2023-10-24 18:08:20", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/CustomISOHomePage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/openWin/DocumentChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/CustomISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ManageDocumentMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "6ecfeeccfdac1e38b6b6fff9f626e9c40c8afae8", "commit_訊息": "[ISO] Q00-20231024001 修正行業別文管開窗選取到正在變更中的文件，新增查詢條件", "提交日期": "2023-10-24 14:44:20", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/CustomISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2290762a55df45aa8cd973207bffcb80435e788a", "commit_訊息": "[ISO]Q00-20230925001 調整ISO新增單、文管新增單，當編碼規則設定為自定義時，只有在Requester、ISODocManager關卡時，才允許重新編輯「文件編號」欄位[補]", "提交日期": "2023-10-23 11:24:08", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreateManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "634c5a4a91b08054212e14bfb71ff491b6009c32", "commit_訊息": "[資安] V00-20230906001 SonarQube安全性議題 : 修复'PWD','Password','PASSWORD'安全检测问题", "提交日期": "2023-10-19 15:36:30", "作者": "刘旭", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/isoPortability/ISOPortabilityMailDesign.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3db3a4d8df05cc6be116457c3c123dedf8c9fd44", "commit_訊息": "[資安]V00-20230906002 SonarQube安全性議題修正：'TOKEN' detected in this expression, review this potentially hard-coded secret", "提交日期": "2023-10-19 15:28:59", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ApplicationToken.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/util/cache/LicenseRegCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "03b60a9600c1dffd7530d79cc62d3c0c642a9c14", "commit_訊息": "[ISO] 專案-ISO全文檢索版本升版[補]", "提交日期": "2023-10-17 15:35:35", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/IndexingHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "64f3e2363b787ad80f58ad15a042839b70591c6e", "commit_訊息": "[ISO]Q00-20231003001 修正ISO調閱單，當單身有多筆資料按下刪除時，F12會有script錯誤的異常", "提交日期": "2023-10-03 13:41:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOAccess.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2847fbf2ede88ecde8e97cdb7c988d5d58a95414", "commit_訊息": "[ISO]Q00-20230925001 調整ISO新增單、文管新增單，當編碼規則設定為自定義時，只有在Requester、ISODocManager關卡時，才允許重新編輯「文件編號」欄位", "提交日期": "2023-09-25 13:42:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreate.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreateManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0786eb05d5f0f4876d659c67d1d8b3372aa830fd", "commit_訊息": "[ISO] 專案-ISO全文檢索版本升版", "提交日期": "2023-09-23 16:35:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOFileDocumentsDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFile.hbm.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOFile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOFileDocumentsController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOFileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/IndexingHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "2b4f2455552827b23431efa81491560150d2efa1", "commit_訊息": "[ISO]V00-20230913001 修正ISO文件攜出申請流程，若攜出的郵件發送失敗時，想透過攜出申請表單的下載檔案功能時，會提示未結案無法下載的異常", "提交日期": "2023-09-21 11:51:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "73217112818152edb88492807a7745f2f88e185d", "commit_訊息": "[ISO]Q00-20230831003 修正ISO文件類別管理，使用者若只有子類別的權限，而沒有父類別的權限時，使用者仍可以看到父類別的文件的異常[補]", "提交日期": "2023-09-19 10:13:35", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09dab005dfd6868db8e8620b6818bf20858fffad", "commit_訊息": "[ISO]Q00-20230913002 修正ISO文件變更單浮水印設定的內容變成圖片浮水印的最後一組文字內容", "提交日期": "2023-09-15 11:11:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISOModifyDocManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "01b9a5d2700781738415f1532fe6027cfbd27ce1", "commit_訊息": "[ISO]Q00-20230912002 調整ISO新增單的檢核文件編號是否重複邏輯由非同步改為同步，避免檢核文件編號是否重複還沒完成流程就已經往下派送", "提交日期": "2023-09-12 15:21:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreate.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreateManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3ebb4c51e2330b6fb031c507a30c9e230474e757", "commit_訊息": "[ISO]Q00-20230831003 修正ISO文件類別管理，使用者若只有子類別的權限，而沒有父類別的權限時，使用者仍可以看到父類別的文件的異常", "提交日期": "2023-08-31 13:57:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bf1569bf0fc98e50a4d0dd0a9d9cf21a7f392d0b", "commit_訊息": "[ISO]Q00-20230824002 修正ISO文件類別管理，當權限的套用範圍設定為部門但不包含子部門時，子部門的人員仍可以看到這份文件的異常", "提交日期": "2023-08-24 14:37:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOAuthorityDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a6f8d61395104dbb6089fa4b248c59d02929ff22", "commit_訊息": "[ISO]S00-20230807002 ISO生失效郵件範本管理增加「變更原因」及「作廢原因」兩個信件樣版變數", "提交日期": "2023-08-24 14:11:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/NotificationContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}