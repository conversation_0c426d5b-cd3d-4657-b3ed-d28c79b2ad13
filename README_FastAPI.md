# 🛠️ BPM Easy Tools - FastAPI 版本

> **BPM 服務部好用工具集** - FastAPI + Jinja2 架構版本

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![License](https://img.shields.io/badge/License-Internal-green.svg)]()
[![Version](https://img.shields.io/badge/Version-2.0.0-orange.svg)]()

## 📋 專案概述

這是 BPM Easy Tools 的 FastAPI 版本，從原本的 Streamlit 架構重新設計為 FastAPI + Jinja2 架構，提供更好的效能和擴展性。

### 🎯 核心功能

- **📊 產品 Release 記錄查詢工具**
  - 支援 BPM、BPM-ISO、NaNaXWeb 專案的 release 記錄查詢
  - 依照 branch_name 瀏覽 commit 記錄
  - 關鍵字搜尋 commit 訊息
  - 檔案名稱搜尋功能
  - 支援排序與分類顯示

- **🔍 檔案索引路徑查詢工具**
  - 查詢 class、jsp、js 檔案位置
  - 支援關鍵字模糊搜尋
  - 上傳壓縮檔案批量查詢
  - 顯示檔案修改時間
  - 多版本索引支援

- **🏢 客戶連線管理系統**
  - 管理客戶公司連線資訊
  - 支援BPM、HRM、Tiptop等多種產品
  - 現代化響應式介面設計
  - 完整的CRUD操作
  - 安全的密碼管理

## 🚀 快速開始

### 環境需求

- Python 3.8 或以上版本
- Windows 10/11 或 Linux/macOS
- 至少 2GB 可用記憶體

### 安裝步驟

1. **建立虛擬環境**（如果尚未建立）
   ```bash
   python -m venv venv
   ```

2. **啟動虛擬環境**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/macOS
   source venv/bin/activate
   ```

3. **安裝依賴套件**
   ```bash
   pip install -r requirements.txt
   ```

4. **啟動應用程式**
   ```bash
   # 使用提供的啟動腳本（推薦）
   start_fastapi.cmd
   
   # 或手動啟動
   uvicorn app.main:app --host 0.0.0.0 --port 8888 --reload
   ```

5. **存取應用程式**
   - 本機存取：http://localhost:8888
   - 網路存取：http://0.0.0.0:8888
   - API 文件：http://localhost:8888/docs

## 📁 專案結構

```
bpm_easy_tools/
├── 📄 README_FastAPI.md          # FastAPI 版本說明
├── 📄 start_fastapi.cmd          # FastAPI 啟動腳本
├── 📄 requirements.txt           # Python 相依套件清單
├── 📁 app/                       # FastAPI 應用程式
│   ├── 📄 main.py               # 主應用程式
│   ├── 📄 config.py             # 應用程式配置
│   ├── 📁 routers/              # 路由模組
│   │   ├── 📄 home.py           # 首頁路由
│   │   ├── 📄 release_query.py  # Release 查詢路由
│   │   ├── 📄 file_search.py    # 檔案搜尋路由
│   │   └── 📄 customer_connections.py # 客戶管理路由
│   ├── 📁 models/               # 資料模型
│   └── 📁 services/             # 業務邏輯服務
├── 📁 templates/                # Jinja2 模板
│   ├── 📄 base.html            # 基礎模板
│   ├── 📄 home.html            # 首頁模板
│   ├── 📁 releases/            # Release 查詢模板
│   ├── 📁 files/               # 檔案搜尋模板
│   └── 📁 customers/           # 客戶管理模板
├── 📁 static/                   # 靜態檔案
│   ├── 📁 css/                 # CSS 樣式
│   └── 📁 js/                  # JavaScript 檔案
├── 📁 data_output/             # 資料輸出目錄
├── 📁 config/                  # 設定檔案
└── 📁 venv/                    # Python 虛擬環境
```

## 🔧 技術架構

### 後端技術
- **FastAPI**: 現代、快速的 Web 框架
- **Uvicorn**: ASGI 伺服器
- **Jinja2**: 模板引擎
- **Python-multipart**: 檔案上傳支援
- **Aiofiles**: 異步檔案操作

### 前端技術
- **Bootstrap 5**: 響應式 UI 框架
- **Font Awesome**: 圖示庫
- **jQuery**: JavaScript 函式庫
- **現代化 CSS**: 漸層、動畫、響應式設計

### 主要特色
- **異步處理**: 支援高併發請求
- **API 文件**: 自動生成的 OpenAPI 文件
- **熱重載**: 開發時自動重啟
- **響應式設計**: 支援各種螢幕尺寸
- **模組化架構**: 易於維護和擴展

## 🎨 介面特色

### 現代化設計
- 漸層背景和卡片式設計
- 平滑的動畫效果
- 響應式布局
- 直觀的導航系統

### 使用者體驗
- 快速載入和響應
- 即時搜尋和過濾
- 友善的錯誤提示
- 完整的表單驗證

## 📊 API 端點

### 主要路由
- `GET /` - 首頁
- `GET /release` - Release 查詢頁面
- `POST /release/search` - Release 搜尋
- `GET /files` - 檔案搜尋頁面
- `POST /files/search` - 檔案搜尋
- `GET /customers` - 客戶管理頁面
- `POST /customers/add` - 新增客戶
- `GET /customers/edit/{oid}` - 編輯客戶
- `DELETE /customers/delete/{oid}` - 刪除客戶

### API 文件
訪問 http://localhost:8888/docs 查看完整的 API 文件

## 🔄 從 Streamlit 版本遷移

### 主要變更
1. **架構變更**: 從 Streamlit 改為 FastAPI + Jinja2
2. **介面改進**: 更現代化的響應式設計
3. **效能提升**: 異步處理和更快的響應速度
4. **API 支援**: 提供 RESTful API 端點

### 資料相容性
- 所有現有的資料檔案完全相容
- 設定檔案格式保持不變
- 資料目錄結構相同

## 🛠️ 開發說明

### 開發模式啟動
```bash
uvicorn app.main:app --host 127.0.0.1 --port 8888 --reload
```

### 新增功能
1. 在 `app/routers/` 中建立新的路由檔案
2. 在 `templates/` 中建立對應的模板
3. 在 `app/main.py` 中註冊新路由

### 自定義樣式
- 修改 `templates/base.html` 中的 CSS
- 在 `static/css/` 中新增自定義樣式檔案

## 🔍 疑難排解

### 常見問題

**Q: 應用程式無法啟動**
- 檢查 Python 版本是否為 3.8 以上
- 確認虛擬環境已正確建立和啟動
- 檢查 8888 埠是否被其他程式佔用

**Q: 模板無法載入**
- 確認 `templates/` 目錄存在
- 檢查模板檔案路徑是否正確

**Q: 靜態檔案無法載入**
- 確認 `static/` 目錄存在
- 檢查檔案權限設定

### 日誌檢查
FastAPI 會在終端機顯示詳細的請求日誌和錯誤訊息。

## 📞 支援與聯絡

- **開發團隊**: BPM 服務部
- **版本**: 2.0.0 (FastAPI)
- **最後更新**: 2025年9月

如有問題或建議，請聯繫開發團隊。

## 📄 授權資訊

本專案為內部使用工具，僅供 BPM 服務部團隊使用。

---

*🎉 恭喜！您已成功將 Streamlit 專案轉換為 FastAPI + Jinja2 架構！*
