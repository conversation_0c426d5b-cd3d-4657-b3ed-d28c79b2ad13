{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.10.2_All", "date": "2024-12-03 17:24:14", "message": "[流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.10.2", "date": "2024-06-25 16:34:02", "message": "[內部]更新58102patch", "author": "lorenchang"}, "比較時間": "2025-07-18 10:40:01", "新增commit數量": 12, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "a1601f13b624a4878f07c16dae62a82bf104a3a9", "commit_訊息": "[流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常", "提交日期": "2024-12-03 17:24:14", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea9d1f9fd997ea9b17f119d851f4bf2a2197f67c", "commit_訊息": "[內部]Q00-20241029001 優化寄信mail log的記錄判讀", "提交日期": "2024-10-29 16:12:55", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "afc632c3d76699cb0bbebc8c0a06409647402900", "commit_訊息": "[流程引擎]A00-20241008001 修正通知信過濾非HTML標籤主旨值問題", "提交日期": "2024-10-09 10:49:16", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/HtmlUtils.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5ccc2ad5092e67121a90d0b32aec13b90fab01b8", "commit_訊息": "[流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]", "提交日期": "2024-10-01 10:53:47", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7d1b804102db2566f0d4d9af347176e0afb2e56", "commit_訊息": "[流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題", "提交日期": "2024-08-12 11:48:38", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "20408298f3672e91f83a0f1982eab792d23b825d", "commit_訊息": "[ISO]修正歸檔浮水印新增的字型設定產生的設定值與BCL8不相容造成中文字變方框", "提交日期": "2024-09-25 17:28:45", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c03dee7f8d679633886409a3899ea4de529c9a11", "commit_訊息": "[雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常", "提交日期": "2024-09-24 10:23:34", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "df862f7f636bb22f87170bdd238ca53d0f01564b", "commit_訊息": "[雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙(補)", "提交日期": "2024-08-19 16:04:11", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6763577874bf0ff46505e18ac3dd7ddd14458698", "commit_訊息": "[雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙因素認證直接登入的異常", "提交日期": "2024-08-09 17:34:25", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "941128be96ea842f5d3aeed5a17dec83c48c2cd1", "commit_訊息": "[Web]C01-20240827002 修正當資料庫類型為 MSSQL_AZURE，使用 SQL 註冊器應用於查詢樣板，模糊查詢條件包含特殊中文字符時無法返回結果", "提交日期": "2024-08-29 09:43:41", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c2a15a9403f841bc5e7d7e22212c2647a4467df3", "commit_訊息": "[流程引擎]C01-20240815002 修正用戶登入授權數不足時，未啟用系統郵件通知也會觸發寄送Email給管理員，改為依照啟用設定通知及增加Log記錄", "提交日期": "2024-08-19 16:04:11", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7472c04434f94d50d61daa06058c49f6782578e1", "commit_訊息": "C01-20240806003 修正 XPDL 流程核決層級名稱變成「Decision Lv1.」的異常", "提交日期": "2024-08-07 16:00:53", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}