# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.2_20230529
- **舊版本**: release_5.8.9.2
- **生成時間**: 2025-07-18 10:56:20
- **新增 Commit 數量**: 9

## 變更摘要

### 林致帆 (4 commits)

- **2023-05-26 16:38:31**: [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
  - 變更檔案: 3 個
- **2023-05-24 17:36:47**: [流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常
  - 變更檔案: 1 個
- **2023-05-24 17:01:45**: [Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加
  - 變更檔案: 1 個
- **2023-05-26 10:36:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個

### develop_20274 (1 commits)

- **2023-05-26 15:02:51**: [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題
  - 變更檔案: 1 個

### raven.917 (3 commits)

- **2023-05-26 14:59:32**: [Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題
  - 變更檔案: 1 個
- **2023-05-25 10:16:09**: [Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題
  - 變更檔案: 1 個
- **2023-05-25 19:35:08**: [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
  - 變更檔案: 1 個

### yamiyeh10 (1 commits)

- **2023-05-26 10:10:30**: [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
- **Commit ID**: `eb4fcda9bed15bec18820491e2c67e769a24a91d`
- **作者**: 林致帆
- **日期**: 2023-05-26 16:38:31
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 2. [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題
- **Commit ID**: `d72accd32ca2ac64d731380966e8b8f53529744d`
- **作者**: develop_20274
- **日期**: 2023-05-26 15:02:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 3. [Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題
- **Commit ID**: `4ae609cd95bff0a2b8728345da221270ec06eafb`
- **作者**: raven.917
- **日期**: 2023-05-26 14:59:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 4. [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]
- **Commit ID**: `0eabc66d8706bfec8d0f937d7f5ad593bfbc736b`
- **作者**: yamiyeh10
- **日期**: 2023-05-26 10:10:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 5. [Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題
- **Commit ID**: `64fa32e7bc15301766f00da2e16ea1dbe87fdc20`
- **作者**: raven.917
- **日期**: 2023-05-25 10:16:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 6. [流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常
- **Commit ID**: `ab7a426ee61294822f61d9d19fb01ace9ac934c4`
- **作者**: 林致帆
- **日期**: 2023-05-24 17:36:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java`

### 7. [Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加
- **Commit ID**: `cb8973a1085888406960b5ae2c4373b226bd9b14`
- **作者**: 林致帆
- **日期**: 2023-05-24 17:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 8. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `ed1a7b3d587b5d6d7a041912c9f502e537d2eff5`
- **作者**: 林致帆
- **日期**: 2023-05-26 10:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 9. [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
- **Commit ID**: `798a76611aaaefd9e7ca40e01e571b75e5b99e7a`
- **作者**: raven.917
- **日期**: 2023-05-25 19:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

