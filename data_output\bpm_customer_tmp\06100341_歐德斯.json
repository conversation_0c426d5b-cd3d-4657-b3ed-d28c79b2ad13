{"company_id": "06100341", "company_name": "歐德斯", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06100341-歐德斯\\01客戶基本資料", "files": [{"filename": "歐德斯連線資訊.txt", "raw_content": "客戶窗口：\r\nWaler Liu 劉哲君\r\nTel: 02-2901-8000#2720\r\n0920256310\r\n<EMAIL>\r\n\r\nSmart VPN\r\nVPN軟件：\r\nhttps://www.draytek.com/products/smart-vpn-client/\r\n\r\nSSL VPN 協定：\r\nip: **************\r\nid: tiptop\r\n密碼: JL42h54Qay228aCErtZ\r\n\r\n正式機AP主機\r\n遠端連線至 ************ 當天改成 ************\r\nid: administator\r\npwd: admin#DSC2023\r\n\r\n \r\n測試機AP主機\r\n   請用windows遠端連線，連至************\r\n   Id: administrator\r\n   Pwd: admin#DSC2023\r\n   webpassword：admin#DSC\r\nDB的連線資訊\r\n   使用TIPTOP DB: toptest\r\n   HOST = ************)(PORT = 1521)\r\n   SERVICE_NAME = toptest\r\n   Instance: efgp2023test\r\n   id: efgp2023test\r\n   pwd: efgp2023test\r\n\r\n---------------------------------------\r\n請用AnyDesk連到跳板機：\r\nid: 1 955 687 126 密碼: c5sTv3dro\r\nAnyDesk: 459 351 606  pwd: Ods@00168 (12/13用這組連線)\r\n\r\n", "structured_data": {"webpassword": "admin#DSC", "tel": "02-2901-8000#2720", "https": "//www.draytek.com/products/smart-vpn-client/", "host": "************)(PORT = 1521)", "id": "1 955 687 126 密碼: c5sTv3dro", "password": "efgp2023test", "使用tiptop db": "toptest", "instance": "efgp2023test", "anydesk": "459 351 606  pwd: Ods@00168 (12/13用這組連線)", "service_name": "toptest"}, "source_path": "C1.客戶維護相關\\06100341-歐德斯\\01客戶基本資料\\歐德斯連線資訊.txt", "file_size": 810, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:31.890681"}], "total_files": 1, "processed_at": "2025-08-26T10:46:31.890708"}