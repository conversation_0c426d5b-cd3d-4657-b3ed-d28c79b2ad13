{"company_id": "05114010", "company_name": "宜蘭食品", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\05114010_宜蘭食品\\01客戶基本資料", "files": [{"filename": "[宜蘭食品] 連線資訊.txt", "raw_content": "Anydesk\r\n1010986692\r\n\r\n\r\nAnydesk的號碼是1 *********** \r\n然後去執行Microsoft Remote Desktop 程式\r\n\r\nGCP的IP˙ˊ˙ˊ˙˙˙˙˙˙˙˙˙˙˙˙˙˙˙˙˙˙˙˙\r\nAP主機：********** 帳號：.\\user 密碼：W@ntwant\r\nDB主機：********** 帳號：.\\user 密碼：W@ntwant\r\n資料庫帳號：sa 密碼：Want1111\r\n\r\n新測試機***********(00-0C-29-05-F3-56)\r\n\r\n\r\nDB NaNa \r\nwantbpm/W@ntwant\r\n\r\n\r\n原測試\r\n10.225.2.68\r\n.\\user\r\nW@ntwant\r\n\r\nBPM (00-0C-29-05-F3-56)\r\n***********\r\nhttp://***********:8086/NaNaWeb/\r\nadministrator/t1w2b3p4m5\r\n\r\nGS\r\n10.225.2.91\r\n\r\n\r\n劉智偉\r\n<EMAIL>\r\n02 2554 5300#112469", "structured_data": {"ap主機": "********** 帳號：.\\user 密碼：W@ntwant", "db主機": "********** 帳號：.\\user 密碼：W@ntwant", "資料庫帳號": "sa 密碼：Want1111", "http": "//***********:8086/NaNaWeb/", "host": "**********"}, "source_path": "C1.客戶維護相關\\05114010_宜蘭食品\\01客戶基本資料\\[宜蘭食品] 連線資訊.txt", "file_size": 623, "encoding_used": "utf-8", "processed_at": "2025-08-26T10:46:29.784933"}], "total_files": 1, "processed_at": "2025-08-26T10:46:29.784942"}