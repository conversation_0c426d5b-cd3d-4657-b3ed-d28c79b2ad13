{"比較資訊": {"專案ID": "NaNaXWeb", "倉庫路徑": "D:\\IDEA_workspace\\NaNaXWeb", "新分支": {"branch_name": "5.8.9.3_hotfix", "date": "2024-04-17 09:33:23", "message": "[PRODT]Q00-20240417001 修正Web流程管理工具中流程定義進版後不會更新作者資訊問題", "author": "yamiyeh10"}, "舊分支": {"branch_name": "5.8.9.3", "date": "2023-08-22 20:50:07", "message": "[DT]V00-20230822004 修正有瀏覽權限的使用者開啟簽出的流程仍可修改與儲存的問題", "author": "pinchi_lin"}, "比較時間": "2025-07-18 15:49:44", "新增commit數量": 46, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "dc4b81723a6b1c20281714318184a09a057e4e07", "commit_訊息": "[PRODT]Q00-20240417001 修正Web流程管理工具中流程定義進版後不會更新作者資訊問題", "提交日期": "2024-04-17 09:33:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8887fd1dbb61889c2ac618557a3789b20bd7b724", "commit_訊息": "[PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況", "提交日期": "2024-09-26 08:21:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "805acf0d5bf4291c97945abfc47c19adfe2efda7", "commit_訊息": "[PRODT]C01-20240619001 修正Web流程管理工具中服務關卡的參數功能切換到第二頁後存在實際參數卻出現未指定參數的訊息", "提交日期": "2024-06-20 17:22:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/formal-parameter/formal-parameter.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/formal-parameter/formal-parameter.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5e3eea66c0d267770cf1739258c6aa6e6dc8356c", "commit_訊息": "[PRODT]Q00-20240131001 修正Web流程管理工具中元件放到連接線上會自動生成或合併線的問題", "提交日期": "2024-01-31 15:50:36", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DropOnFlowBehavior.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f780ed76dfbe69ecb2a9d6c2b20a114dac755cde", "commit_訊息": "[PRODT]Q00-20240125003 修正Web流程管理工具中流程模型屬性設定無法點確定的問題", "提交日期": "2024-01-25 16:05:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1e415fdaeea62d59c6d7d8d434145d4b14fdd31c", "commit_訊息": "[PRODT]Q00-20231222001 調整流程管理工具中流程圖連接線允許編輯的邏輯", "提交日期": "2024-01-19 10:08:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e0e3bb64a44bf656039cf839632d02e927cdb8f3", "commit_訊息": "[PRODT]Q00-20240110001 修正Web流程管理工具中儲存進版後的建立日期非當前日期的問題", "提交日期": "2024-01-10 10:51:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/save-process-package-drawer/save-process-package-drawer.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/save-process-package-drawer/save-process-package-drawer.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "05d8b95adad5e03bef0fec307423801c7ccbe4c2", "commit_訊息": "[PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題", "提交日期": "2023-12-21 17:01:34", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed4170ccd742d1b62ce83d8caedca16d6e23d5e7", "commit_訊息": "Revert \"[PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題\"", "提交日期": "2023-12-21 17:00:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d755617ef9aeeee369b774bedfc7f9bd3155011", "commit_訊息": "[PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題", "提交日期": "2023-12-21 16:58:20", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "85ad4220acb45dd40de3d901a43088b45bd43c16", "commit_訊息": "[PRODT]Q00-20231220002 修正Web流程管理工具中核決活動的核決規則其層級選擇異常的問題", "提交日期": "2023-12-20 10:19:36", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-rule-list-editor/decision-rule-list-editor.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "774fe99d0da8edadb5a33a5ec508953e5899fb4d", "commit_訊息": "[PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題", "提交日期": "2023-12-14 18:35:51", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/services/home-button-event-emitter-collection.service.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/services/process-package-manage.service.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/tabs/tabs.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "src/com/digiwin/bpm/DTModule/services/ProcessDesignMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "13131ce8599aea64f5349835ac9445da6ca02cbf", "commit_訊息": "[PRODT]Q00-20231205006 修正Web流程管理工具中流程圖有缺少連接線時仍可儲存的問題", "提交日期": "2023-12-05 19:08:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "86c0f3b6fab1c94cdd2963536d2943909b5141e5", "commit_訊息": "[ORGDT]Q00-20231201005 修正組織管理工具從首頁使用者清單調離並轉調其他單位時不會帶入原單位任職資料的問題", "提交日期": "2023-12-01 17:07:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/hosting-department-openwin/hosting-department-openwin.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/hosting-project-openwin/hosting-project-openwin.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/tables/user-table/user-table.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "99d9a703ffbbbb0efced70ed356fdcb7eaaeaefc", "commit_訊息": "[ORGDT]Q00-20231201003 修正組織管理工具從搜尋修改使用者資料後返回首頁使用者清單未更新的問題", "提交日期": "2023-12-01 10:51:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8fd244ac63c8c2b76dde0acc81d220349e9f3d94", "commit_訊息": "[ORGDT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題", "提交日期": "2023-12-01 10:28:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a1efaa5c85a6503add0a1e8bc59d1f99f51ce69", "commit_訊息": "Revert \"[PRODT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題\"", "提交日期": "2023-12-01 10:26:31", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9431cf4182b53592ee55f48859464be66052da3b", "commit_訊息": "[PRODT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題", "提交日期": "2023-12-01 09:34:59", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "975a2f1c95f797d34ddc9c968f52194fcb8d3e2e", "commit_訊息": "[PRODT]Q00-20231201001 修正流程管理工具進階按鈕禁用選項點擊後仍會觸發問題", "提交日期": "2023-12-01 09:34:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4efa8d2828c1a8656fc6be451510d0615cf3a2d1", "commit_訊息": "[PRODT]Q00-20231128002 修正流程樹節點禁用選項點擊後仍會觸發問題", "提交日期": "2023-11-28 12:01:08", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36e196910a58873800615fc91f88e0976d5bda10", "commit_訊息": "[DT]Q00-20231121008 修正核決層級中選擇參考活動的一般活動無資料問題", "提交日期": "2023-11-21 18:58:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-chooser/activity-chooser.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71bf03571183a770c9773f0a81c3961d07a43cbd", "commit_訊息": "[DT]Q00-20231117005 修正流程管理工具中應用程式管理員修改或刪除session bean中的參數後仍是舊資料的問題", "提交日期": "2023-11-21 18:56:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/basic-type-inspector/basic-type-inspector.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/formal-parameter/formal-parameter.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6e00be5194b13a8ffdbe391d17c9a2978e16eda1", "commit_訊息": "[DT]Q00-20231116003 修正流程管理工具中活動關卡的不可退回活動設定存在髒資料導致雙擊無法開啟編輯頁面的問題", "提交日期": "2023-11-16 15:54:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/unreexecute-attributes/unreexecute-attributes.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8df6b3481d678884b867fbc197e6bacbacb78ee0", "commit_訊息": "[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題", "提交日期": "2023-11-07 14:43:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6fa10b29c823950f5d4cf9dd1f25cee7063ba76d", "commit_訊息": "Revert \"[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題[補]\"", "提交日期": "2023-11-07 14:42:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "067d7610ec2a963f705f6e1c7562cefd03d81718", "commit_訊息": "[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題[補]", "提交日期": "2023-11-07 14:16:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "017af50d12d5ceca60556f0eac1ad7f43066e574", "commit_訊息": "Revert \"[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題\"", "提交日期": "2023-11-07 14:14:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f29840e9df75c7d3619001046f3f85f873e57808", "commit_訊息": "[DT]Q00-20231107003 修正流程管理工具中匯入已存在id的流程會有錯誤的請求提示的問題", "提交日期": "2023-11-07 14:06:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1ed269fc5c197b363d9ef16622e30146bc254f26", "commit_訊息": "[DT]Q00-20231107001 修正發起權限設定屬性在新增使用者後變成解析錯誤問題", "提交日期": "2023-11-07 09:10:01", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/add-invoke-aurhority-openwin/add-invoke-aurhority-openwin.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d542d33ca71a915fb7171a6d065a5ff77e00197d", "commit_訊息": "Revert \"[DT]Q00-20231106003 修正發起權限設定屬性在新增使用者後變成解析錯誤問題\"", "提交日期": "2023-11-07 09:07:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/add-invoke-aurhority-openwin/add-invoke-aurhority-openwin.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5d5fedf93f87fe24fefa35d470eb05165456271", "commit_訊息": "[DT]Q00-20231106003 修正發起權限設定屬性在新增使用者後變成解析錯誤問題", "提交日期": "2023-11-07 09:07:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/add-invoke-aurhority-openwin/add-invoke-aurhority-openwin.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be178bf5945df9ca7ba1cfb2cc8fe966ce24bc29", "commit_訊息": "[DT]Q00-20231106003 修正發起權限設定屬性沒有資料下在編輯狀態進行新增設定時儲存沒有效果問題", "提交日期": "2023-11-06 17:31:38", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b00c405d1c765e8cef467a1017c90f6c9da1515", "commit_訊息": "[DT]Q00-20231031003 修正組織管理工具中組織樹根節點下無組織資料時展開會有後端接口調用失敗的問題", "提交日期": "2023-10-31 17:26:14", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/shared/components/organization-tree/organization-tree.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7aee9f11e4720153d48dd8a92799faad41fa75f", "commit_訊息": "[DT]Q00-20231023005 修正組織管理工具中首頁使用者表格的換頁按鈕被隱藏的問題", "提交日期": "2023-10-23 15:59:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/tables/user-table/user-table.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/tables/user-table/user-table.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4d30c532249f2c0ff70f7b885ab23057526afb1a", "commit_訊息": "[DT]Q00-20230918003 修正開起流程後活動與連接線消失的問題[補]", "提交日期": "2023-10-17 20:38:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/bpmn-js/package.json", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/package.json", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/package-lock.json", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c2c25c1532a31309b32dd6a5c160db34e4a48f8d", "commit_訊息": "[DT]Q00-20231017001 修正資料使用權限管理中新增時啟用包含子目錄儲存後會還原的問題", "提交日期": "2023-10-17 13:47:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/access-control/access-contorl-table/access-contorl-table.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/access-control/access-control-create/access-control-create.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/access-control/access-control-create/access-control-create.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3a202a59c42140da91b386c79141565f8559bb31", "commit_訊息": "[DT]Q00-20231012005 修正使用套索元件選取多個流程圖元件刪除後流程模型定義會異常的問題", "提交日期": "2023-10-12 17:59:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-custom/customContextPad.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "eaa8357557292e4558e7064aa4a2b0171b1866f4", "commit_訊息": "[DT]Q00-20231012001 修正匯出流程不會取當前流程圖內容的問題", "提交日期": "2023-10-12 14:13:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/buttons/view-edit-page-button/view-edit-page-button.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07e66ea71c15a1d469749bcb34b992712827e8ac", "commit_訊息": "[DT]Q00-20231003002 修正流程管理工具中因註解元件固定長寬導致顯示異常的問題", "提交日期": "2023-10-03 14:01:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/ElementFactory.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-custom/customRenderer.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4a8d51ee42182450d37dbf63e18f33a230e19bca", "commit_訊息": "[DT]Q00-20230925003 修正通知編輯範本中變數清單缺少問題", "提交日期": "2023-09-25 17:29:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/pattern-editor/pattern-editor.component.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "552990b16aed4ed092f1ea3e6b838625f1d13025", "commit_訊息": "[DT]Q00-20230923001 修正活動定義編輯器進階屬性中限制欄位的單位顯示異常問題", "提交日期": "2023-09-23 10:57:14", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/activity-definition-manager/activity-definition-manager.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/activity-definition-manager/activity-definition-manager.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-advanced-attributes/activity-advanced-attributes.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-advanced-attributes/activity-advanced-attributes.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d71236f51acd254f55e135df77da5d45246f35fb", "commit_訊息": "[DT]Q00-20230918003 修正開起流程後活動與連接線消失的問題", "提交日期": "2023-09-21 11:23:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/bpmn-js/LICENSE", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/README.md", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/css/bpmn.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.eot", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.ttf", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.woff", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-font/font/bpmn.woff2", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/bpmn-js.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/assets/diagram-js.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/bpmn-modeler.development.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/bpmn-modeler.production.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/bpmn-navigated-viewer.development.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/bpmn-navigated-viewer.production.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/bpmn-viewer.development.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/dist/bpmn-viewer.production.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/BaseModeler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/BaseViewer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/Modeler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/NavigatedViewer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/Viewer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/core/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/draw/BpmnRenderUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/draw/BpmnRenderer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/draw/PathMap.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/draw/TextRenderer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/draw/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/AlignElementsContextPadProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/AlignElementsIcons.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/AlignElementsMenuProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/BpmnAlignElements.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-bottom-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-horizontal-center-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-left-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-right-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-top-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/align-elements/resources/align-vertical-center-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/auto-place/BpmnAutoPlace.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/auto-place/BpmnAutoPlaceUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/auto-place/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/auto-resize/BpmnAutoResize.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/auto-resize/BpmnAutoResizeProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/auto-resize/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/context-pad/ContextPadProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/context-pad/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/copy-paste/BpmnCopyPaste.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/copy-paste/ModdleCopy.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/copy-paste/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/di-ordering/BpmnDiOrdering.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/di-ordering/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/BpmnDistributeElements.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/DistributeElementsIcons.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/DistributeElementsMenuProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/resources/distribute-horizontally-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/distribute-elements/resources/distribute-vertically-tool.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/drilldown/DrilldownBreadcrumbs.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/drilldown/DrilldownCentering.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/drilldown/DrilldownOverlayBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/drilldown/SubprocessCompatibility.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/drilldown/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/editor-actions/BpmnEditorActions.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/editor-actions/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/BpmnGridSnapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/behavior/GridSnappingAutoPlaceBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/behavior/GridSnappingLayoutConnectionBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/behavior/GridSnappingParticipantBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/behavior/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/grid-snapping/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/interaction-events/BpmnInteractionEvents.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/interaction-events/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/keyboard/BpmnKeyboardBindings.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/keyboard/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/label-editing/LabelEditingPreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/label-editing/LabelEditingProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/label-editing/LabelUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/label-editing/cmd/UpdateLabelHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/label-editing/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/BpmnFactory.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/BpmnLayouter.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/BpmnUpdater.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/ElementFactory.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/Modeling.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/AdaptiveLabelPositioningBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/AppendBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/AssociationBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/AttachEventBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/BoundaryEventBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/CreateBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/CreateDataObjectBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/CreateParticipantBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DataInputAssociationBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DataStoreBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DeleteLaneBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DetachEventBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DropOnFlowBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/EventBasedGatewayBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/FixHoverBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/GroupBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ImportDockingFix.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/IsHorizontalFix.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/LabelBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/LayoutConnectionBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/MessageFlowBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ModelingFeedback.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/RemoveElementBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/RemoveEmbeddedLabelBoundsBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/RemoveParticipantBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ReplaceConnectionBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ReplaceElementBehaviour.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ResizeBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ResizeLaneBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/RootElementReferenceBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/SpaceToolBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/SubProcessPlaneBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/SubProcessStartEventBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ToggleCollapseConnectionBehaviour.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/ToggleElementCollapseBehaviour.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/UnclaimIdBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/UnsetDefaultFlowBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/UpdateFlowNodeRefsBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/CategoryUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/ConnectionLayoutUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/GeometricUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/LabelLayoutUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/LayoutUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/LineAttachmentUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/LineIntersect.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/util/ResizeUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/AddLaneHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/IdClaimHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/ResizeLaneHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/SetColorHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/SplitLaneHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdateCanvasRootHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdateFlowNodeRefsHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdateModdlePropertiesHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdatePropertiesHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/cmd/UpdateSemanticParentHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/util/LaneUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/modeling/util/ModelingUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/ordering/BpmnOrderingProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/ordering/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/palette/PaletteProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/palette/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/popup-menu/ReplaceMenuProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/popup-menu/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/popup-menu/util/TypeUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/replace-preview/BpmnReplacePreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/replace-preview/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/replace/BpmnReplace.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/replace/ReplaceOptions.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/replace/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/rules/BpmnRules.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/rules/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/search/BpmnSearchProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/search/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/snapping/BpmnConnectSnapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/snapping/BpmnCreateMoveSnapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/snapping/BpmnSnappingUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/snapping/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/space-tool/BpmnSpaceTool.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/features/space-tool/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/import/BpmnImporter.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/import/BpmnTreeWalker.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/import/Importer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/import/Util.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/import/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/util/CompatibilityUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/util/DiUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/util/DrilldownUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/util/LabelUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/util/ModelUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/lib/util/PoweredByUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/package.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/helper/TranslationCollector.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/helper/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/matchers/BoundsMatchers.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/matchers/ConnectionMatchers.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/matchers/JSONMatcher.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/util/KeyEvents.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/util/MockEvents.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/util/custom-rules/CustomRules.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-js/test/util/custom-rules/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/LICENSE", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/README.md", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/dist/bpmn-moddle.umd.cjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/dist/bpmn-moddle.umd.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/dist/bpmn-moddle.umd.prod.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/dist/index.cjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/dist/index.esm.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/dist/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/package.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn-io/json/bioc.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/json/bpmn.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/json/bpmndi.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/json/dc.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/json/di.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/BPMN20.xsd", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/BPMNDI.xsd", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/DC.xsd", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/DI.xsd", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/bpmn-moddle/resources/bpmn/xsd/Semantic.xsd", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/LICENSE", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/README.md", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/assets/diagram-js.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/Diagram.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/command/CommandHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/command/CommandInterceptor.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/command/CommandStack.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/command/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/core/Canvas.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/core/ElementFactory.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/core/ElementRegistry.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/core/EventBus.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/core/GraphicsFactory.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/core/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/draw/BaseRenderer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/draw/DefaultRenderer.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/draw/Styles.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/draw/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/align-elements/AlignElements.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/align-elements/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/attach-support/AttachSupport.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/attach-support/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-place/AutoPlace.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-place/AutoPlaceSelectionBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-place/AutoPlaceUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-place/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-resize/AutoResize.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-resize/AutoResizeProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-resize/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-scroll/AutoScroll.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/auto-scroll/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/bendpoints/BendpointMove.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/bendpoints/BendpointMovePreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/bendpoints/BendpointSnapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/bendpoints/BendpointUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/bendpoints/Bendpoints.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/bendpoints/ConnectionSegmentMove.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/bendpoints/GeometricUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/bendpoints/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/change-support/ChangeSupport.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/change-support/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/clipboard/Clipboard.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/clipboard/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/connect/Connect.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/connect/ConnectPreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/connect/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/connection-preview/ConnectionPreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/connection-preview/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/context-pad/ContextPad.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/context-pad/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/copy-paste/CopyPaste.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/copy-paste/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/create/Create.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/create/CreateConnectPreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/create/CreatePreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/create/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/distribute-elements/DistributeElements.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/distribute-elements/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/dragging/Dragging.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/dragging/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/editor-actions/EditorActions.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/editor-actions/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/global-connect/GlobalConnect.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/global-connect/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/GridSnapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/GridUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/behavior/ResizeBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/behavior/SpaceToolBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/behavior/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/visuals/Grid.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/grid-snapping/visuals/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/hand-tool/HandTool.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/hand-tool/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/hover-fix/HoverFix.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/hover-fix/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/interaction-events/InteractionEvents.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/interaction-events/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/keyboard-move-selection/KeyboardMoveSelection.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/keyboard-move-selection/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/keyboard/Keyboard.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/keyboard/KeyboardBindings.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/keyboard/KeyboardUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/keyboard/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/label-support/LabelSupport.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/label-support/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/lasso-tool/LassoTool.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/lasso-tool/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/Modeling.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/AlignElementsHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/AppendShapeHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/CreateConnectionHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/CreateElementsHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/CreateLabelHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/CreateShapeHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/DeleteConnectionHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/DeleteElementsHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/DeleteShapeHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/DistributeElementsHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/LayoutConnectionHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/MoveConnectionHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/MoveElementsHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/MoveShapeHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/NoopHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/ReconnectConnectionHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/ReplaceShapeHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/ResizeShapeHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/SpaceToolHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/ToggleShapeCollapseHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/UpdateAttachmentHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/UpdateWaypointsHandler.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/helper/AnchorsHelper.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/helper/MoveClosure.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/cmd/helper/MoveHelper.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/modeling/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/mouse/Mouse.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/mouse/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/move/Move.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/move/MovePreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/move/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/ordering/OrderingProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/outline/Outline.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/outline/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/overlays/Overlays.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/overlays/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/palette/Palette.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/palette/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenu.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenuComponent.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenuItem.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenuList.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/popup-menu/PopupMenuProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/popup-menu/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/preview-support/PreviewSupport.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/preview-support/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/replace/Replace.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/replace/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/resize/Resize.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/resize/ResizeHandles.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/resize/ResizePreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/resize/ResizeUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/resize/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/root-elements/RootElementsBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/root-elements/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/rules/RuleProvider.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/rules/Rules.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/rules/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/search-pad/SearchPad.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/search-pad/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/selection/Selection.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/selection/SelectionBehavior.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/selection/SelectionVisuals.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/selection/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/snapping/CreateMoveSnapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/snapping/ResizeSnapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/snapping/SnapContext.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/snapping/SnapUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/snapping/Snapping.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/snapping/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/space-tool/SpaceTool.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/space-tool/SpaceToolPreview.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/space-tool/SpaceUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/space-tool/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/tool-manager/ToolManager.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/tool-manager/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/tooltips/Tooltips.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/tooltips/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/touch/TouchFix.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/touch/TouchInteractionEvents.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/features/touch/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/i18n/I18N.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/i18n/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/i18n/translate/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/i18n/translate/translate.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/layout/BaseLayouter.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/layout/ConnectionDocking.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/layout/CroppingConnectionDocking.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/layout/LayoutUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/layout/ManhattanLayout.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/model/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/navigation/keyboard-move/KeyboardMove.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/navigation/keyboard-move/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/navigation/movecanvas/MoveCanvas.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/navigation/movecanvas/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/navigation/touch/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/navigation/zoomscroll/ZoomScroll.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/navigation/zoomscroll/ZoomUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/navigation/zoomscroll/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/ui/index.d.ts", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/ui/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/AttachUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/ClickTrap.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Collections.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Cursor.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Elements.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/EscapeUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Event.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Geometry.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/GraphicsUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/IdGenerator.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/LineIntersection.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Math.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Mouse.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Platform.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/PositionUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Removal.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/RenderUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/SvgTransformUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/lib/util/Text.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/package.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/test/helper/index.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/test/matchers/BoundsMatchers.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/test/matchers/BoundsMatchersSpec.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/test/matchers/ConnectionMatchers.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/test/matchers/ConnectionMatchersSpec.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/test/util/KeyEvents.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/diagram-js/test/util/MockEvents.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/moddle-xml/LICENSE", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/moddle-xml/README.md", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/moddle-xml/dist/index.cjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/moddle-xml/dist/index.cjs.map", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/moddle-xml/dist/index.esm.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/moddle-xml/dist/index.esm.js.map", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/moddle-xml/dist/moddle-xml.umd.cjs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/moddle-xml/package.json", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "AngularProjects/DTModule/package.json", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 437}, {"commit_hash": "826078283913aa189c5ea0e14c7b7df73088d791", "commit_訊息": "[DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題", "提交日期": "2023-09-18 17:00:51", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/models/process-definition-header.model.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32bbb8229af2523386b81b4678bba6b0a45f5566", "commit_訊息": "[DT]Q00-20230901002 修正流程管理工具中主流程屬性設定的可重定義屬性中關係人選擇表單無法單選人員的問題", "提交日期": "2023-09-01 19:42:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/main-process-definition-drawer/main-process-definition-drawer.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-element/form-element.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-definition-redefinable-header/process-definition-redefinable-header.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-definition-redefinable-header/process-definition-redefinable-header.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "81f0ae649bc8b1a566da5eb45b9605812478d20e", "commit_訊息": "[DT]A00-20230828001 修正組織管理工具中設定代理人資訊在第二頁點修改開啟頁面資料會取錯的問題", "提交日期": "2023-08-29 11:06:42", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/operations/substitute-operation/substitute-operation.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/operations/substitute-operation/substitute-operation.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0c743951e57b7f611b2534bf7edd3770f91ed7e0", "commit_訊息": "[DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題", "提交日期": "2023-08-28 11:13:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/print-openwin/print-openwin.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/shared/models/print.model.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}]}