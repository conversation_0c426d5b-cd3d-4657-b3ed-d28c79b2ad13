# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.10.4_20250310
- **舊版本**: release_5.8.10.4
- **生成時間**: 2025-07-18 10:39:36
- **新增 Commit 數量**: 19

## 變更摘要

### 周权 (11 commits)

- **2025-03-05 13:40:19**: [帳號安全管理]C01-20250304009 修正Oauth登入後顯示的語系是環境語系而不是登入頁選取的語系
  - 變更檔案: 1 個
- **2025-03-05 13:58:54**: [Web]Q00-20250305001 修正系統設定"nana.performworkItem.html.character.filter"=true,追蹤、待辦等主旨顯示會異常(補)
  - 變更檔案: 4 個
- **2025-03-05 13:40:19**: [Web]Q00-20250305001 修正系統設定"nana.performworkItem.html.character.filter"=true,追蹤、待辦等主旨顯示會異常
  - 變更檔案: 6 個
- **2025-01-17 14:25:09**: [Web]C01-20250116003 修正SubjectTitle导致主旨显示異常
  - 變更檔案: 4 個
- **2025-01-07 17:27:19**: [資安]Q00-20241227001 多選開窗查詢資安問題修正
  - 變更檔案: 4 個
- **2025-01-05 22:34:45**: [資安]Q00-20241226001 Sql Injection問題，调整多选开窗参数txtConditionValue
  - 變更檔案: 1 個
- **2025-02-14 10:26:37**: [Web] C01-20250206008 調整助閲讀、找經驗分頁查詢
  - 變更檔案: 4 個
- **2024-12-20 14:11:20**: [資安]Q00-20241217004 調整個人訊息頁欄位安全性問題
  - 變更檔案: 2 個
- **2024-12-19 10:43:41**: [資安]Q00-20241217003 調整查詢欄位可以輸入查詢條件支持查詢的問題，防止SQL注入
  - 變更檔案: 1 個
- **2024-12-19 10:23:42**: [資安]Q00-20241217002 调整登入错误讯息[補]
  - 變更檔案: 3 個
- **2024-12-18 16:10:44**: [資安]Q00-20241217002 调整登入错误讯息
  - 變更檔案: 4 個

### lorenchang (6 commits)

- **2025-03-06 16:44:33**: [帳號安全管理]C01-20250304008 修正驗證碼通知樣版使用的語系是使用者環境語系而不是登入畫面選擇的
  - 變更檔案: 1 個
- **2025-03-06 16:44:23**: [帳號安全管理]C01-20250304008 修正缺少使用者語系的通知樣板導致雙因素驗證出現null提示的異常
  - 變更檔案: 1 個
- **2025-02-24 14:53:50**: [文件總結助手]Q00-20250224001 修正找經驗關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)
  - 變更檔案: 1 個
- **2025-02-17 16:14:46**: [文件總結助手]Q00-20250220001 修正助閱讀關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)
  - 變更檔案: 1 個
- **2025-02-17 16:14:46**: [雙因素認證]C01-20250217001 修正啟用帳號鎖定次數時雙因素驗出現異常：Argument pSqlString cannot be null or empty string
  - 變更檔案: 1 個
- **2024-12-03 17:24:14**: [流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常
  - 變更檔案: 1 個

### kmin (1 commits)

- **2024-12-18 14:50:52**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]
  - 變更檔案: 1 個

### 張詠威 (1 commits)

- **2024-10-17 14:03:53**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [帳號安全管理]C01-20250304009 修正Oauth登入後顯示的語系是環境語系而不是登入頁選取的語系
- **Commit ID**: `22ac0a92dda77e24238e7a8e6b6350394bd08177`
- **作者**: 周权
- **日期**: 2025-03-05 13:40:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 2. [Web]Q00-20250305001 修正系統設定"nana.performworkItem.html.character.filter"=true,追蹤、待辦等主旨顯示會異常(補)
- **Commit ID**: `cd74bb1a451eea2df4d88ca52911a5952e9feb7d`
- **作者**: 周权
- **日期**: 2025-03-05 13:58:54
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 3. [Web]Q00-20250305001 修正系統設定"nana.performworkItem.html.character.filter"=true,追蹤、待辦等主旨顯示會異常
- **Commit ID**: `c5943553d1e957e15ce92be2e951c7146876aab8`
- **作者**: 周权
- **日期**: 2025-03-05 13:40:19
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 4. [Web]C01-20250116003 修正SubjectTitle导致主旨显示異常
- **Commit ID**: `e1aecc428c3af8b74e4ba14cdbb472531f1b8de6`
- **作者**: 周权
- **日期**: 2025-01-17 14:25:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 5. [帳號安全管理]C01-20250304008 修正驗證碼通知樣版使用的語系是使用者環境語系而不是登入畫面選擇的
- **Commit ID**: `a82f8ec5d3947dd218d9518961c7400a0a52a013`
- **作者**: lorenchang
- **日期**: 2025-03-06 16:44:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 6. [帳號安全管理]C01-20250304008 修正缺少使用者語系的通知樣板導致雙因素驗證出現null提示的異常
- **Commit ID**: `784bb05c9504b5602a0d3b341299b81b61248efe`
- **作者**: lorenchang
- **日期**: 2025-03-06 16:44:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 7. [文件總結助手]Q00-20250224001 修正找經驗關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)
- **Commit ID**: `6495e4bfaae71685a9a17bdd51c98ed95366286f`
- **作者**: lorenchang
- **日期**: 2025-02-24 14:53:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java`

### 8. [資安]Q00-20241227001 多選開窗查詢資安問題修正
- **Commit ID**: `3695ed504b75d4ecc105a65634332031373d21fd`
- **作者**: 周权
- **日期**: 2025-01-07 17:27:19
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml`

### 9. [資安]Q00-20241226001 Sql Injection問題，调整多选开窗参数txtConditionValue
- **Commit ID**: `936e8e74d87ee61442249aefee6ccb40d16c9d9a`
- **作者**: 周权
- **日期**: 2025-01-05 22:34:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`

### 10. [文件總結助手]Q00-20250220001 修正助閱讀關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)
- **Commit ID**: `0cde96b7a2ebf4794deec704cc26d80453b995dd`
- **作者**: lorenchang
- **日期**: 2025-02-17 16:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java`

### 11. [Web] C01-20250206008 調整助閲讀、找經驗分頁查詢
- **Commit ID**: `bfa89d43ba1fc46d634871a31c4b0d8882bf8d21`
- **作者**: 周权
- **日期**: 2025-02-14 10:26:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmCompanyMappingDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmInitiateProcessProfileDaoImpl.java`

### 12. [雙因素認證]C01-20250217001 修正啟用帳號鎖定次數時雙因素驗出現異常：Argument pSqlString cannot be null or empty string
- **Commit ID**: `29b17307ef79920fbb65df9cdc2cf196d9d57b17`
- **作者**: lorenchang
- **日期**: 2025-02-17 16:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 13. [流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常
- **Commit ID**: `d4d8575854e8290ddafbe37de771e82b88882878`
- **作者**: lorenchang
- **日期**: 2024-12-03 17:24:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 14. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]
- **Commit ID**: `9ada8bfcd645ad277e6b269ba063e136c95fa4a1`
- **作者**: kmin
- **日期**: 2024-12-18 14:50:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 15. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
- **Commit ID**: `99f505c932f7a7aaad751b5288746d64d8c914cf`
- **作者**: 張詠威
- **日期**: 2024-10-17 14:03:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 16. [資安]Q00-20241217004 調整個人訊息頁欄位安全性問題
- **Commit ID**: `41af72642d65968e8b55b08d83b5141104e0622c`
- **作者**: 周权
- **日期**: 2024-12-20 14:11:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 17. [資安]Q00-20241217003 調整查詢欄位可以輸入查詢條件支持查詢的問題，防止SQL注入
- **Commit ID**: `9287c65390def0fc662156d80d63c15409f86a92`
- **作者**: 周权
- **日期**: 2024-12-19 10:43:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 18. [資安]Q00-20241217002 调整登入错误讯息[補]
- **Commit ID**: `b936fce4c7cf078b8d73edb70008982606709af1`
- **作者**: 周权
- **日期**: 2024-12-19 10:23:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 19. [資安]Q00-20241217002 调整登入错误讯息
- **Commit ID**: `bf1ec218ad7e5cdfa1e1d216b5b9235afc764ce1`
- **作者**: 周权
- **日期**: 2024-12-18 16:10:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

