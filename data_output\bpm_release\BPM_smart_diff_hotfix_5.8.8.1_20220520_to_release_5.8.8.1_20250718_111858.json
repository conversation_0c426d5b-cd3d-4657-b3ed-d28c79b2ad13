{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.8.1_20220520", "date": "2022-06-26 20:48:05", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.8.1", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.8.1", "date": "2022-06-26 20:48:05", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.8.1", "author": "lorenchang"}, "比較時間": "2025-07-18 11:18:58", "新增commit數量": 6, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "8ef1a9a8d619cbfa2f13b88db8c3770969f9e0d2", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.8.1", "提交日期": "2022-06-26 20:48:05", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "b6ce7ebaa673892bb38fbbc04eb1bb7883c9bc30", "commit_訊息": "[流程引擎]A00-20220519003 修正終止前置流程時，後置流程完成流程撤銷後，前置流程出現「派送失敗」的錯誤訊息，無法正常終止", "提交日期": "2022-05-20 14:08:18", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d2588982d05156ee7d73b94fd6796fb2c74ec775", "commit_訊息": "[Web]A00-20220517004 修正調離職人員帳號更新排程預設出貨端口改成8086", "提交日期": "2022-05-19 17:04:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaJobs.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d8c4aa3f68bc813e3f9153809738802f6159904e", "commit_訊息": "[內部]Q00-20220509001修正設定檔ESS內網IP敘述的Oracle指令有誤", "提交日期": "2022-05-09 08:55:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cabb6a193ce4d7930008bd9863bf345c2e22b34", "commit_訊息": "[流程引擎]A00-20220513001 修正ProcessMapping在Oracle遺漏attachInfo欄位導致拋單失敗", "提交日期": "2022-05-13 17:23:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36049cb4b8d31b5684bcc014bc16f8b2c192f97f", "commit_訊息": "[內部]A00-20220517002 修正DB為 Oracle時，產品授權畫面無顯示模組名稱", "提交日期": "2022-05-17 11:32:25", "作者": "wayne", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}