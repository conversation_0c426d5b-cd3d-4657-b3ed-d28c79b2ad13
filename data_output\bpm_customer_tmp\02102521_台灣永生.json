{"company_id": "02102521", "company_name": "台灣永生", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\02102521_台灣永生\\01客戶基本資料", "files": [{"filename": "[台灣永生]連線資訊.txt", "raw_content": "[台灣永生]\r\n\r\nVPN FortiClient:\r\nVPN使用Forticlient\r\nIP: ************\r\nPort: 10443\r\nUser：sct-xavier\r\n密碼：Sctz/ru8\r\n\r\n\r\nBPM 測試機:\r\n00-50-56-BE-C5-5C\r\n*************:8086\r\nadministrator/Dscth@2212\r\n1234\r\n\r\nBPM 正式機:\r\n00-50-56-BE-3B-DA\r\n*************:8086\r\nadministrator\r\nDscth@2212\r\n\r\n\r\nDB:\r\n*************\r\nadministrator\r\nDscth@2212\r\n\r\n\r\nWF DB主機的IP: *************\r\nWF 資料庫名稱: StemCyte_TEST\r\nWF 登入帳密: sa/dsc@1624\r\n\r\n正式機\r\n*************\r\n\r\n測試機\r\n*************\r\n\r\nDB\r\n*************", "structured_data": {"username": "sct-xavier", "password": "Sctz/ru8", "host": "************", "port": "10443", "*************": "8086", "*************": "8086", "wf db主機的ip": "*************", "wf 資料庫名稱": "StemCyte_TEST", "wf 登入帳密": "sa/dsc@1624"}, "source_path": "C1.客戶維護相關\\02102521_台灣永生\\01客戶基本資料\\[台灣永生]連線資訊.txt", "file_size": 530, "encoding_used": "utf-8", "processed_at": "2025-08-26T10:46:32.501168"}], "total_files": 1, "processed_at": "2025-08-26T10:46:32.501178"}