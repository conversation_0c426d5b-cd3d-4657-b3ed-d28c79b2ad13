{"company_id": "06331001", "company_name": "三陽", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06331001_三陽\\01客戶基本資料", "files": [{"filename": "SYM-bpmdb資料庫主機登入資訊.txt", "raw_content": "\r\n\r\nGuest OS:\tRed Hat Enterprise Linux 7 (64-bit)\r\nCompatibility:\tESXi 6.0 and later (VM version 11)\r\nVMware Tools:\tRunning, version:10309 (Guest Managed)\r\nMORE INFO\r\nDNS Name:\tbpmdb.sym.com.tw\r\nIP Addresses:\t***********\r\n\r\nCPU:4CPU\r\nMemory:16G\r\n\r\n登入帳密\r\nOS \r\nroot/僅供SM人員,鼎新若有需求請提申請\r\noracle/dba#BPM\r\n\r\nDB正式區\r\nbpm/bpm#prod\r\n\r\nDB測試區\r\nbpm/bpm#test\r\n\r\n\r\nOracle Database 12c Standard Edition Release ********.0 - 64bit Production\r\n\r\nORACLE_HOME :/u2/oracle/12c\r\nFilesystem                   Size  Used Avail Use% Mounted on\r\n/dev/mapper/oravg-u2lv        10G  3.3G  6.8G  33% /u2\r\n/dev/mapper/oravg-ora12clv    30G   13G   18G  43% /u2/oracle/12c\r\n/dev/mapper/oravg-oradatalv  100G   12G   89G  12% /u2/oradata (原有erp2prod使用,先保留)\r\n/dev/mapper/oravg-archlv      50G  231M   50G   1% /u2/arch (原有erp2prod使用,先保留)\r\n\r\n正式區 ORACLE_SID=bpmprod (SGA:4G)\r\nFilesystem                   Size  Used Avail Use% Mounted on\r\n/dev/mapper/bpmvg-u6lv       150G   22G  129G  15% /u6\r\n/dev/mapper/bpmvg-u8lv       100G  678M  100G   1% /u8\r\n\r\n/u2/oracle/12c/network/admin/tnsnames.ora\r\nBPMPROD.SYM.COM.TW =\r\n  (DESCRIPTION =\r\n    (ADDRESS = (PROTOCOL = TCP)(HOST = bpmdb)(PORT = 1521))\r\n    (CONNECT_DATA =\r\n      (SERVER = DEDICATED)\r\n      (SERVICE_NAME = bpmprod )\r\n    )\r\n  )\r\n\r\nSQL> archive log list\r\nDatabase log mode              Archive Mode\r\nAutomatic archival             Enabled\r\nArchive destination            /u8/arch\r\nOldest online log sequence     7\r\nNext log sequence to archive   9\r\nCurrent log sequence           9\r\n\r\ncreate user bpm identified by bpm#prod\r\n      default tablespace bpmdbs1\r\n      temporary tablespace temp;\r\ngrant create session,create table to bpm;\r\ngrant resource to bpm;\r\ngrant create synonym to bpm;\r\ngrant create view to bpm;\r\ngrant unlimited tablespace to bpm;\r\n \r\n測試區 ORACLE_SID=bpmtest(SGA:2G)\r\nFilesystem                   Size  Used Avail Use% Mounted on\r\n/dev/mapper/bpmtestvg-t2lv   100G   22G   79G  22% /t2\r\n\r\n/u2/oracle/12c/network/admin/tnsnames.ora\r\nBPMTEST.SYM.COM.TW =\r\n  (DESCRIPTION =\r\n    (ADDRESS = (PROTOCOL = TCP)(HOST = bpmdb)(PORT = 1521))\r\n    (CONNECT_DATA ", "structured_data": {"guest os": "Red Hat Enterprise Linux 7 (64-bit)", "compatibility": "ESXi 6.0 and later (VM version 11)", "vmware tools": "Running, version:10309 (Guest Managed)", "dns name": "bpmdb.sym.com.tw", "ip addresses": "***********", "cpu": "4CPU", "memory": "16G", "oracle_home": "/u2/oracle/12c", "正式區 oracle_sid=bpmprod (sga": "4G)", "測試區 oracle_sid=bpmtest(sga": "2G)", "正式區 oracle_sid": "bpmprod (SGA:4G)", "(address": "(PROTOCOL = TCP)(HOST = bpmdb)(PORT = 1521))", "(server": "DEDICATED)", "(service_name": "bpmprod )", "測試區 oracle_sid": "bpmtest(SGA:2G)", "host": "***********"}, "source_path": "C1.客戶維護相關\\06331001_三陽\\01客戶基本資料\\SYM-bpmdb資料庫主機登入資訊.txt", "file_size": 2140, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:24.356881"}, {"filename": "三陽工業聯絡窗口.txt", "raw_content": "每次發版都要要寄 ReleaseNote\r\n\r\n\r\n三陽工業股份有限公司\r\nSanyang Motor Co., Ltd.\r\n0000176852\r\n新竹縣新竹工業區中華路3號\r\n\r\nTIPTOP GP 1X\r\nOracle ERP系統\r\n\r\n資訊系統室 資訊二課課長 \r\n廖志獻先生\r\n03-5981911 #1628\r\n<EMAIL>\r\n0933-982052\r\n\r\nBPM\r\nOS/DB客戶自行安裝\r\nWindows\r\n\r\nOracle 12c\r\n\r\nSanyang Motor Co., Ltd.\r\n三陽工業資訊系統室資訊二課\r\n廖志獻課長\r\nMail to:<EMAIL>\r\nWeb Site: www.sym.com.tw\r\nPhone: 03-5981911 ext:1542\r\nMobil: 0933982052\r\nAddress:新竹縣新竹工業區中華路3號\r\n\r\n\r\nVPN連線方式\r\nGlobal Protect\r\nvpn2.sym.com.tw\r\ndsc\r\nYCdZYtf5\r\n\r\nhttp://bpmprod.sym.com.tw:8086/NaNaWeb\r\nhttp://bpmtest.sym.com.tw:8086/NaNaWeb\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "structured_data": {"mail to": "<EMAIL>", "web site": "www.sym.com.tw", "phone": "03-5981911 ext:1542", "mobil": "0933982052", "address": "新竹縣新竹工業區中華路3號", "http": "//bpmtest.sym.com.tw:8086/NaNaWeb"}, "source_path": "C1.客戶維護相關\\06331001_三陽\\01客戶基本資料\\三陽工業聯絡窗口.txt", "file_size": 676, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:24.369751"}, {"filename": "版更務必記得.txt", "raw_content": "以下版更會被還原，要再改一次\r\n\r\n1. 因客戶有需求需連線 oracle 8 的 DB（資料來源設定）\r\n   有調整過\r\n   D:\\BPM\\wildfly-15.0.0.Final\\standalone\\deployments\\NaNaWeb.war\\WEB-INF\\jboss-deployment-structure.xml\r\n   移除<module name=\"NaNa.lib.jdbc.oracle\" export=\"true\" />\r\n   並將要使用的jar(ojdbc14.jar)直接放在 NaNaWeb.war\\WEB-INF\\lib 下 \r\n\r\n2. By 劉震，因客製需求，SQL 註冊器會超過上限字元\r\n   有調整過   \r\n   D:\\BPM\\wildfly-15.0.0.Final\\standalone\\deployments\\NaNaWeb.war\\BPMModule\\ModuleForm\\FormSqlClause.jsp\r\n   原本 maxLength = 1333;要改為5000\r\n\r\n   if(dbType === 'ORACLE'){\r\n   maxLength = 5000;\r\n   }", "structured_data": {"d": "\\BPM\\wildfly-15.0.0.Final\\standalone\\deployments\\NaNaWeb.war\\BPMModule\\ModuleForm\\FormSqlClause.jsp", "移除<module name": "\"NaNa.lib.jdbc.oracle\" export=\"true\" />", "原本 maxlength": "1333;要改為5000", "if(dbtype": "== 'ORACLE'){", "maxlength": "5000;"}, "source_path": "C1.客戶維護相關\\06331001_三陽\\01客戶基本資料\\版更務必記得.txt", "file_size": 603, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:24.381436"}], "total_files": 3, "processed_at": "2025-08-26T10:46:24.381444"}