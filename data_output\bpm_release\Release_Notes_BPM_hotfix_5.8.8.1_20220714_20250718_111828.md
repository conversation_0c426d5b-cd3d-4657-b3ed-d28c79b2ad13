# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.8.1_20220714
- **舊版本**: release_5.8.8.1
- **生成時間**: 2025-07-18 11:18:28
- **新增 Commit 數量**: 94

## 變更摘要

### lorenchang (1 commits)

- **2022-06-26 20:48:05**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.8.1
  - 變更檔案: 25 個

### 王鵬程 (7 commits)

- **2022-07-11 15:22:15**: [表單設計師]Q00-20220711002 修正絕對表單元件不存在某些屬性而取用該屬性導致無法開啟表單，增加防呆
  - 變更檔案: 1 個
- **2022-06-21 16:55:38**: [Web]A00-20220616003 修正SerialNumber元件的字體設25px以上，在流程中該元件的顯示會有部分被遮蔽到
  - 變更檔案: 1 個
- **2022-06-09 11:50:53**: [Web]Q00-20220609001 修正行動裝置在加簽關卡選擇參與者人員後，選擇參與者的下方欄位會顯示已選取0個項目
  - 變更檔案: 1 個
- **2022-06-13 16:14:20**: [Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾
  - 變更檔案: 1 個
- **2022-05-05 17:52:51**: [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果[補]
  - 變更檔案: 1 個
- **2022-05-05 15:24:43**: [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果
  - 變更檔案: 1 個
- **2022-04-27 16:50:57**: [Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色
  - 變更檔案: 1 個

### yanann_chen (25 commits)

- **2022-07-06 17:43:27**: [流程引擎]Q00-20220706005 修正資料庫為MSSQL，且流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
  - 變更檔案: 1 個
- **2022-07-13 16:21:42**: [Web]Q00-20220713004 修正移動消息訂閱管理頁面無法開啟
  - 變更檔案: 1 個
- **2022-07-11 17:38:00**: [流程引擎]Q00-20220711003 修正在表單上將Excel匯入單身後，開窗畫面變成空白
  - 變更檔案: 1 個
- **2022-06-20 14:09:46**: [Web]A00-20220616001 退回重辦頁面增加退回重辦方式的說明
  - 變更檔案: 1 個
- **2022-06-28 17:17:20**: [流程引擎]Q00-20220628001 調整流程關係人與關係部門解析邏輯，若流程中沒有變更流程關係人或關係部門資料，系統在流程往下派送時就不會再解析流程關係人與關係部門
  - 變更檔案: 1 個
- **2022-07-07 16:10:43**: [流程引擎]Q00-20220707003 修正DialogInput元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容
  - 變更檔案: 1 個
- **2022-06-09 17:18:03**: [Web]A00-20220608003 修正進入追蹤流程畫面時未清除「撤銷理由」欄位內容
  - 變更檔案: 1 個
- **2022-06-09 16:32:41**: [流程引擎]Q00-20220609003 修正使用者操作個人預設代理人設定時，代理人可能有多筆相同人員的問題
  - 變更檔案: 1 個
- **2022-05-25 17:01:11**: [表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出
  - 變更檔案: 3 個
- **2022-05-25 14:01:03**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
  - 變更檔案: 1 個
- **2022-05-25 10:57:21**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
  - 變更檔案: 1 個
- **2022-05-24 17:45:41**: [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤
  - 變更檔案: 1 個
- **2022-05-20 15:42:50**: [流程引擎]Q00-20220520003 修正流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
  - 變更檔案: 1 個
- **2022-05-12 18:19:18**: [流程引擎]A00-20220511001 修正使用者輸入到表單TextArea的內容在儲存表單後變成亂碼的問題
  - 變更檔案: 1 個
- **2022-05-12 14:36:06**: [流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差
  - 變更檔案: 1 個
- **2022-05-12 14:12:31**: [Web]Q00-20220411005 修正使用者在絕對位置表單進行簽核時遭遇產品程式錯誤
  - 變更檔案: 1 個
- **2022-05-12 11:47:55**: [內部]Q00-20220512001 bootstrap-table-1.18.3.js更換檔案名稱
  - 變更檔案: 4 個
- **2022-05-11 15:03:45**: [Web]Q00-20220511005 修正T100拋轉單據中有舊值的單身內容沒有顯示為紅色
  - 變更檔案: 1 個
- **2022-05-11 12:07:20**: [流程引擎]Q00-20220506001 調整使用者「授權的流程」，流程清單筆數改為設定檔設定
  - 變更檔案: 1 個
- **2022-05-11 11:23:52**: [流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料
  - 變更檔案: 1 個
- **2022-05-11 11:10:44**: [流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知
  - 變更檔案: 1 個
- **2022-05-11 10:54:24**: [Web]Q00-20220511001 調整列印表單畫面簽核歷程，移除「資料代號」、「通知者」欄位
  - 變更檔案: 1 個
- **2022-06-20 16:44:21**: [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-04 14:02:42**: [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題
  - 變更檔案: 1 個
- **2022-05-20 14:08:18**: [流程引擎]A00-20220519003 修正終止前置流程時，後置流程完成流程撤銷後，前置流程出現「派送失敗」的錯誤訊息，無法正常終止
  - 變更檔案: 2 個

### walter_wu (19 commits)

- **2022-06-28 18:06:01**: [Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久
  - 變更檔案: 1 個
- **2022-07-01 11:54:21**: [Web]Q00-20220701001 調整時間元件如果輸入不是數字直接換成00
  - 變更檔案: 1 個
- **2022-06-08 09:21:56**: [報表設計器]Q00-20220607003 修正欄位字串如果含as會辨識錯欄位名稱
  - 變更檔案: 1 個
- **2022-07-08 18:24:42**: [表單設計師]A00-20220704001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟表單會報錯
  - 變更檔案: 2 個
- **2022-06-13 16:07:01**: [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯
  - 變更檔案: 1 個
- **2022-06-09 15:28:14**: [內部]Q00-20220609002 調整DWR設定讓Log不要一直出現轉換ProcessInstanceStateType的錯誤
  - 變更檔案: 1 個
- **2022-06-10 18:10:24**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
  - 變更檔案: 1 個
- **2022-06-08 15:56:57**: [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
  - 變更檔案: 1 個
- **2022-06-08 11:49:23**: [WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯
  - 變更檔案: 1 個
- **2022-06-01 13:57:59**: [Web]A00-20220526001 修正如果DB是Oralce在線閱讀浮水印管理出現無法取得EJB所提供的服務
  - 變更檔案: 1 個
- **2022-05-30 15:34:30**: [內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方
  - 變更檔案: 1 個
- **2022-05-27 17:34:09**: [Web]Q00-20220527003 修正使用者使用監控流程的最大筆數沒有根據process.default.show.records的設定
  - 變更檔案: 1 個
- **2022-05-27 17:15:41**: [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
  - 變更檔案: 1 個
- **2022-05-25 18:07:49**: [Web]A00-20220519001 修正IE加簽會加成兩次的問題
  - 變更檔案: 1 個
- **2022-05-24 19:04:27**: [系統管理工具]C01-20220524002 修正進階功能>檢查密碼如果User裡有關聯有異常的會全部撈不出來
  - 變更檔案: 1 個
- **2022-05-23 18:19:20**: [內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)
  - 變更檔案: 1 個
- **2022-05-11 14:44:02**: [內部]Q00-20220511003 調整BCL8轉檔Timeout從預設2分鐘拉長到5分鐘
  - 變更檔案: 1 個
- **2022-05-09 16:41:13**: [Web]Q00-20220509002 修正授權的流程使用closeTime(結案時間)排序會報錯的問題
  - 變更檔案: 1 個
- **2022-04-27 15:00:41**: [內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤
  - 變更檔案: 1 個

### kmin (10 commits)

- **2022-07-14 17:29:49**: [流程引擎]Q00-20220622001 修正當RWD表單的RadioButton元件與CheckBox元件選項內容太長時會斷行 相關議題單：C01-20220613009。
  - 變更檔案: 1 個
- **2022-07-14 17:27:26**: [Web]Q00-20220621001 修正非CheckBox或RadioButton的選擇元件執行到額外輸入框邏輯導致出現非預期異常
  - 變更檔案: 1 個
- **2022-07-14 16:39:42**: [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼 1.移動端原本就會把中文字轉換成Unicode字符代碼作呈現,如果再轉一次就會把&轉換掉 2.關聯紀錄:A00-20220511001
  - 變更檔案: 1 個
- **2022-07-14 16:37:51**: Revert "[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼"
  - 變更檔案: 1 個
- **2022-07-14 16:34:56**: [流程引擎]Q00-20220707002 修正表單日期元件預設值計算錯誤 相關議題單：C01-20220701004。
  - 變更檔案: 1 個
- **2022-07-14 16:28:34**: [Web]A00-20220628001 修正已簽核過的關卡，從Mail的待辦連結進入該表單時可以移除附件
  - 變更檔案: 1 個
- **2022-07-14 16:20:49**: [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
  - 變更檔案: 1 個
- **2022-07-14 16:19:46**: Revert "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開"
  - 變更檔案: 1 個
- **2022-07-14 16:14:21**: [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
  - 變更檔案: 1 個
- **2022-07-14 16:13:16**: Revert "[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘"
  - 變更檔案: 1 個

### 林致帆 (15 commits)

- **2022-06-29 11:34:54**: [資安]Q00-20220629001 修正/NaNaWeb/webservice/servlet/AxisServlet要加入為WebService白名單控管範圍
  - 變更檔案: 1 個
- **2022-06-23 14:10:20**: [Web]A00-20220622002 修正流程新增關卡頁面輸入簽核意見在新增向前or向後關卡按下確定後，簽核意見內容被清除
  - 變更檔案: 1 個
- **2022-06-21 13:42:26**: [Web]Q00-20220621003修正發起流程-查詢流程清單搜尋純數字的流程名稱會報錯
  - 變更檔案: 1 個
- **2022-06-07 14:20:54**: [Web]Q00-20220607002 修正首頁待辦清單第三頁以上的流程進行派送時會報錯
  - 變更檔案: 1 個
- **2022-06-14 11:52:54**: [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯[補修正]
  - 變更檔案: 1 個
- **2022-06-10 11:42:26**: [內部]Q00-20220610001修正WorkFlow拋單log會顯示[Fatal Error] :1:1的錯誤訊息
  - 變更檔案: 1 個
- **2022-06-06 14:26:44**: [Web]Q00-20220606001 修正第二關之後的關卡預解析，流程線的條件式採用表單欄位時，預解析的關卡與派送的關卡不符合
  - 變更檔案: 1 個
- **2022-05-25 16:48:50**: [Web]Q00-20220525004 修正輸入單身資料有&#加任意數字，被轉成特殊符號，會與輸入資料不符
  - 變更檔案: 2 個
- **2022-05-25 16:32:13**: [TIPTOP]Q00-20220525003 修正拋單的單身資料有中刮號會被轉成小括號，導致資料與TIPTOP不符合
  - 變更檔案: 1 個
- **2022-05-18 10:39:03**: [Web]Q00-20220518001 修正退件表單資訊與開啟的表單關連錯誤
  - 變更檔案: 1 個
- **2022-05-05 11:00:24**: [Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢
  - 變更檔案: 1 個
- **2022-06-16 13:59:23**: [Web]Q00-20220616001 調整待辦通知信中簽核歷程的處理者欄位移除粗體樣式
  - 變更檔案: 1 個
- **2022-05-19 17:04:27**: [Web]A00-20220517004 修正調離職人員帳號更新排程預設出貨端口改成8086
  - 變更檔案: 1 個
- **2022-05-09 08:55:56**: [內部]Q00-20220509001修正設定檔ESS內網IP敘述的Oracle指令有誤
  - 變更檔案: 1 個
- **2022-05-13 17:23:56**: [流程引擎]A00-20220513001 修正ProcessMapping在Oracle遺漏attachInfo欄位導致拋單失敗
  - 變更檔案: 1 個

### waynechang (1 commits)

- **2022-06-22 15:47:30**: [Web]Q00-20220523001 修正同瀏覽器有二次登入時，登入頁「記住我」的功能會失效
  - 變更檔案: 1 個

### 郭哲榮 (6 commits)

- **2022-07-12 20:01:24**: [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼
  - 變更檔案: 1 個
- **2022-05-27 19:22:55**: [BPM APP]C01-20220523004 修正移動端subTab元件使用formScript在單獨顯示時會顯示其他頁籤內容的問題
  - 變更檔案: 1 個
- **2022-05-18 18:47:47**: [BPM APP]C01-20220516002 修正行動端FormUtil.disable為true時Dropdown元件顯示異常問題
  - 變更檔案: 1 個
- **2022-05-17 15:12:44**: [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題[補]
  - 變更檔案: 1 個
- **2022-05-16 15:10:33**: [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題
  - 變更檔案: 1 個
- **2022-04-28 12:23:28**: [BPM APP]C01-20220419001 修正移動端選項元件使用動態塞值且隱藏標籤會顯示異常問題
  - 變更檔案: 1 個

### cherryliao (1 commits)

- **2022-07-12 11:15:32**: [BPM APP]Q00-20220711001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟移動端表單會報錯的問題
  - 變更檔案: 1 個

### yamiyeh10 (6 commits)

- **2022-07-08 11:01:58**: [表單設計師]C01-20220706003 修正當變更表單的對齊方式並儲存後會將已設計過的行動版表單設計欄位清空問題
  - 變更檔案: 1 個
- **2022-05-26 10:06:49**: [BPM APP]C01-20220524002 修正改派通知設定整張表單時Line推播內容不會呈現表單訊息格式的問題
  - 變更檔案: 1 個
- **2022-05-10 10:49:37**: [BPM APP]C01-20220509009 修正行動端在Grid元件存在沒有繫結元件情況下點擊編輯按鈕會失敗問題
  - 變更檔案: 1 個
- **2022-05-26 09:50:36**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-16 15:15:57**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-10 18:03:59**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題
  - 變更檔案: 1 個

### wayne (3 commits)

- **2022-05-12 16:55:22**: [Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」
  - 變更檔案: 2 個
- **2022-05-10 15:59:21**: [Web]Q00-20220510001 修正IE瀏覽器開啟產品授權註冊頁面時，畫面跑版呈現異常
  - 變更檔案: 1 個
- **2022-05-17 11:32:25**: [內部]A00-20220517002 修正DB為 Oracle時，產品授權畫面無顯示模組名稱
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.8.1
- **Commit ID**: `95c50e4449ca6155f630e61120799c721f617ae9`
- **作者**: lorenchang
- **日期**: 2022-06-26 20:48:05
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [表單設計師]Q00-20220711002 修正絕對表單元件不存在某些屬性而取用該屬性導致無法開啟表單，增加防呆
- **Commit ID**: `c21f1f4d10ad95fdd39b3bd295bdaf0768ac5f19`
- **作者**: 王鵬程
- **日期**: 2022-07-11 15:22:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 3. [流程引擎]Q00-20220706005 修正資料庫為MSSQL，且流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
- **Commit ID**: `a5152db54620f4ecbc0aa77803a0f5eb7e875444`
- **作者**: yanann_chen
- **日期**: 2022-07-06 17:43:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 4. [Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久
- **Commit ID**: `9327480cbf619bcdde10a79f5aa2b1fa7da06eb5`
- **作者**: walter_wu
- **日期**: 2022-06-28 18:06:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 5. [流程引擎]Q00-20220622001 修正當RWD表單的RadioButton元件與CheckBox元件選項內容太長時會斷行 相關議題單：C01-20220613009。
- **Commit ID**: `b9d309c72dd36cc6448ac6ed7bb5ab0b904675dd`
- **作者**: kmin
- **日期**: 2022-07-14 17:29:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 6. [Web]Q00-20220621001 修正非CheckBox或RadioButton的選擇元件執行到額外輸入框邏輯導致出現非預期異常
- **Commit ID**: `92368e872c7d106b815248361a8fc8e934ef0786`
- **作者**: kmin
- **日期**: 2022-07-14 17:27:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 7. [Web]Q00-20220713004 修正移動消息訂閱管理頁面無法開啟
- **Commit ID**: `bdd4c6865b8c76cc3e3952aad15e2a8ebde55e7e`
- **作者**: yanann_chen
- **日期**: 2022-07-13 16:21:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`

### 8. [流程引擎]Q00-20220711003 修正在表單上將Excel匯入單身後，開窗畫面變成空白
- **Commit ID**: `a566e9feb6ac3307b6192f9603977d986fcc61c2`
- **作者**: yanann_chen
- **日期**: 2022-07-11 17:38:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`

### 9. [Web]Q00-20220701001 調整時間元件如果輸入不是數字直接換成00
- **Commit ID**: `91b05ba89cabea5201cbfc5faa894ec859f03e5a`
- **作者**: walter_wu
- **日期**: 2022-07-01 11:54:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 10. [資安]Q00-20220629001 修正/NaNaWeb/webservice/servlet/AxisServlet要加入為WebService白名單控管範圍
- **Commit ID**: `b9179954f3708f8d659b0986294af383bbe8ea8a`
- **作者**: 林致帆
- **日期**: 2022-06-29 11:34:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 11. [Web]A00-20220622002 修正流程新增關卡頁面輸入簽核意見在新增向前or向後關卡按下確定後，簽核意見內容被清除
- **Commit ID**: `e6d5832a2b9539fcf61afc755e9a4056598c44c2`
- **作者**: 林致帆
- **日期**: 2022-06-23 14:10:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 12. [Web]Q00-20220523001 修正同瀏覽器有二次登入時，登入頁「記住我」的功能會失效
- **Commit ID**: `bc1b44733c0006ee17a2c088dde79b29a1a831e5`
- **作者**: waynechang
- **日期**: 2022-06-22 15:47:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 13. [Web]A00-20220616003 修正SerialNumber元件的字體設25px以上，在流程中該元件的顯示會有部分被遮蔽到
- **Commit ID**: `6027984f5f20c23d39bf6d1cf12e20910800ab39`
- **作者**: 王鵬程
- **日期**: 2022-06-21 16:55:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 14. [Web]Q00-20220621003修正發起流程-查詢流程清單搜尋純數字的流程名稱會報錯
- **Commit ID**: `24b41f87171d72661bf64fd8f1441f30d1bae8dd`
- **作者**: 林致帆
- **日期**: 2022-06-21 13:42:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`

### 15. [Web]A00-20220616001 退回重辦頁面增加退回重辦方式的說明
- **Commit ID**: `2f96b0529d219879fe2679c40b6603e8b7309ee6`
- **作者**: yanann_chen
- **日期**: 2022-06-20 14:09:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 16. [Web]Q00-20220609001 修正行動裝置在加簽關卡選擇參與者人員後，選擇參與者的下方欄位會顯示已選取0個項目
- **Commit ID**: `6f724526dcdf1eea047294858cfecf9bd0661df9`
- **作者**: 王鵬程
- **日期**: 2022-06-09 11:50:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 17. [報表設計器]Q00-20220607003 修正欄位字串如果含as會辨識錯欄位名稱
- **Commit ID**: `b379956f95cf06d57f991b653dc6480c3f7ae7c0`
- **作者**: walter_wu
- **日期**: 2022-06-08 09:21:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`

### 18. [Web]Q00-20220607002 修正首頁待辦清單第三頁以上的流程進行派送時會報錯
- **Commit ID**: `c7b2d36dee04fd8cc047a4ce0a3f26325d62440a`
- **作者**: 林致帆
- **日期**: 2022-06-07 14:20:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 19. [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼 1.移動端原本就會把中文字轉換成Unicode字符代碼作呈現,如果再轉一次就會把&轉換掉 2.關聯紀錄:A00-20220511001
- **Commit ID**: `c19c7517a4280f2ea190a5ebb78b6d3b0d96a8f1`
- **作者**: kmin
- **日期**: 2022-07-14 16:39:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 20. Revert "[BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼"
- **Commit ID**: `ff9754ebb34842d9a7b2c570f845fc1277289125`
- **作者**: kmin
- **日期**: 2022-07-14 16:37:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 21. [BPM APP]C01-20220707002 修正移動端TextArea元件顯示中文時會變成Unicode字符代碼
- **Commit ID**: `d75c994b9ebf5e12ffd5a75b93f4433a24850191`
- **作者**: 郭哲榮
- **日期**: 2022-07-12 20:01:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 22. [流程引擎]Q00-20220628001 調整流程關係人與關係部門解析邏輯，若流程中沒有變更流程關係人或關係部門資料，系統在流程往下派送時就不會再解析流程關係人與關係部門
- **Commit ID**: `c969b52102ecb4def4a86fff53097d8966931a97`
- **作者**: yanann_chen
- **日期**: 2022-06-28 17:17:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 23. [流程引擎]Q00-20220707002 修正表單日期元件預設值計算錯誤 相關議題單：C01-20220701004。
- **Commit ID**: `0202ed19fb05f4ef5caced212eaf59732c4afe40`
- **作者**: kmin
- **日期**: 2022-07-14 16:34:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 24. [BPM APP]Q00-20220711001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟移動端表單會報錯的問題
- **Commit ID**: `d8ca71f47ea6d2662f690b837cbda0c4e18358f8`
- **作者**: cherryliao
- **日期**: 2022-07-12 11:15:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`

### 25. [表單設計師]A00-20220704001 修正將綁定存放TextBox數字轉文字結果的欄位刪除，開啟表單會報錯
- **Commit ID**: `4a7839f6112ce0f9793dc88d13827b7169f70b99`
- **作者**: walter_wu
- **日期**: 2022-07-08 18:24:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 26. [表單設計師]C01-20220706003 修正當變更表單的對齊方式並儲存後會將已設計過的行動版表單設計欄位清空問題
- **Commit ID**: `43126117cd2f58ca7ebb13c58093b4894adae349`
- **作者**: yamiyeh10
- **日期**: 2022-07-08 11:01:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`

### 27. [流程引擎]Q00-20220707003 修正DialogInput元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容
- **Commit ID**: `a91321f3fe9f0c7fe064f7c6b1867aa4406c2709`
- **作者**: yanann_chen
- **日期**: 2022-07-07 16:10:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 28. [Web]A00-20220628001 修正已簽核過的關卡，從Mail的待辦連結進入該表單時可以移除附件
- **Commit ID**: `17d2f439eed1efb31126c52c678ac5a09292b906`
- **作者**: kmin
- **日期**: 2022-07-14 16:28:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 29. [Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾
- **Commit ID**: `b351b7ac7de092698624bb3fb5339ede8495a92e`
- **作者**: 王鵬程
- **日期**: 2022-06-13 16:14:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`

### 30. [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯[補修正]
- **Commit ID**: `40b4ab1d5f46cc66c43a675819b363df40c62218`
- **作者**: 林致帆
- **日期**: 2022-06-14 11:52:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 31. [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯
- **Commit ID**: `a54aee90cc354946b0b0ecc2d6c6af223527b0a3`
- **作者**: walter_wu
- **日期**: 2022-06-13 16:07:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 32. [內部]Q00-20220610001修正WorkFlow拋單log會顯示[Fatal Error] :1:1的錯誤訊息
- **Commit ID**: `38f5c4331370bb8b639116d0a47e783d2e124193`
- **作者**: 林致帆
- **日期**: 2022-06-10 11:42:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 33. [Web]A00-20220608003 修正進入追蹤流程畫面時未清除「撤銷理由」欄位內容
- **Commit ID**: `7f0adcd722b72637ea0e2ce96e77fd7cd6848873`
- **作者**: yanann_chen
- **日期**: 2022-06-09 17:18:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 34. [流程引擎]Q00-20220609003 修正使用者操作個人預設代理人設定時，代理人可能有多筆相同人員的問題
- **Commit ID**: `865774fa9b179a00f322eaff4163c2676a7a84e0`
- **作者**: yanann_chen
- **日期**: 2022-06-09 16:32:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/user_profile/MultiDefaultSubstituteForManaging.java`

### 35. [內部]Q00-20220609002 調整DWR設定讓Log不要一直出現轉換ProcessInstanceStateType的錯誤
- **Commit ID**: `18e25acb3b7f0414ddf505687e96c5cc66c0e3ed`
- **作者**: walter_wu
- **日期**: 2022-06-09 15:28:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 36. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
- **Commit ID**: `c6ccf3fee3d00c3a620ba7c2db247a20c9cff9de`
- **作者**: walter_wu
- **日期**: 2022-06-10 18:10:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 37. [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
- **Commit ID**: `eeb41f2b2ec36cbca2e5989c272bf8b90886920d`
- **作者**: kmin
- **日期**: 2022-07-14 16:20:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 38. Revert "[Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開"
- **Commit ID**: `5a2411fdd14302b4f3cfbb506efe248d9d30d460`
- **作者**: kmin
- **日期**: 2022-07-14 16:19:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 39. [Web]A00-20220608002 修正日期元件getTextValue如果是null表單會打不開
- **Commit ID**: `36869fb976f50f659b1bdd104cef8f7bc8fa0a7c`
- **作者**: walter_wu
- **日期**: 2022-06-08 15:56:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 40. [WebService]A00-20220608001 修正如果DB為Oracle白名單沒設定，呼叫WebService會直接報錯
- **Commit ID**: `5a89e296bee6eef02313fc33a1c5e9da8778d7fb`
- **作者**: walter_wu
- **日期**: 2022-06-08 11:49:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 41. [Web]Q00-20220606001 修正第二關之後的關卡預解析，流程線的條件式採用表單欄位時，預解析的關卡與派送的關卡不符合
- **Commit ID**: `46257b441af7fa32405dbe75555f7b915460b7e1`
- **作者**: 林致帆
- **日期**: 2022-06-06 14:26:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 42. [Web]A00-20220526001 修正如果DB是Oralce在線閱讀浮水印管理出現無法取得EJB所提供的服務
- **Commit ID**: `a61981f8b3b11ef21fe916b79f542cbf2c15f720`
- **作者**: walter_wu
- **日期**: 2022-06-01 13:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java`

### 43. [內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方
- **Commit ID**: `c5ee178664e6b6e9b0a45d94f6255f2f860e05de`
- **作者**: walter_wu
- **日期**: 2022-05-30 15:34:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 44. [BPM APP]C01-20220523004 修正移動端subTab元件使用formScript在單獨顯示時會顯示其他頁籤內容的問題
- **Commit ID**: `bbf6cdaba99d733ce9fe7f8213e5eb67e2b27759`
- **作者**: 郭哲榮
- **日期**: 2022-05-27 19:22:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileSubTab.js`

### 45. [Web]Q00-20220527003 修正使用者使用監控流程的最大筆數沒有根據process.default.show.records的設定
- **Commit ID**: `72b64ec91f0f44e402f870bccf743f5bb820d042`
- **作者**: walter_wu
- **日期**: 2022-05-27 17:34:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 46. [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
- **Commit ID**: `a609d8da256c68226cee6c806df3edff3854069c`
- **作者**: kmin
- **日期**: 2022-07-14 16:14:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 47. Revert "[內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘"
- **Commit ID**: `609cdce934fcef062a77186802a1131ffba5ef0d`
- **作者**: kmin
- **日期**: 2022-07-14 16:13:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 48. [內部]Q00-20220527002 調整BCL8轉檔Timeout從5分鐘拉長到10分鐘
- **Commit ID**: `6f10ef8d256aaf3cbae2014d7ae6712710b19dd6`
- **作者**: walter_wu
- **日期**: 2022-05-27 17:15:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 49. [BPM APP]C01-20220524002 修正改派通知設定整張表單時Line推播內容不會呈現表單訊息格式的問題
- **Commit ID**: `17062d606905ce9eb3c09a6d27037a1d5538e339`
- **作者**: yamiyeh10
- **日期**: 2022-05-26 10:06:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 50. [Web]A00-20220519001 修正IE加簽會加成兩次的問題
- **Commit ID**: `376f93870331f3b05488b75f674136eac7c976ee`
- **作者**: walter_wu
- **日期**: 2022-05-25 18:07:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 51. [表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出
- **Commit ID**: `76db9c269d9c579b4d4f27d9909c284c8328a5e5`
- **作者**: yanann_chen
- **日期**: 2022-05-25 17:01:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 52. [Web]Q00-20220525004 修正輸入單身資料有&#加任意數字，被轉成特殊符號，會與輸入資料不符
- **Commit ID**: `45407b00ec35e0dac4a3ce528e7b0d04805210cf`
- **作者**: 林致帆
- **日期**: 2022-05-25 16:48:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 53. [TIPTOP]Q00-20220525003 修正拋單的單身資料有中刮號會被轉成小括號，導致資料與TIPTOP不符合
- **Commit ID**: `d3a9fd788e9e1e16e58d81f9e19de67fe4daab79`
- **作者**: 林致帆
- **日期**: 2022-05-25 16:32:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 54. [系統管理工具]C01-20220524002 修正進階功能>檢查密碼如果User裡有關聯有異常的會全部撈不出來
- **Commit ID**: `bbfeb44c4307ea6400f09ce9d4780ac9c80497c2`
- **作者**: walter_wu
- **日期**: 2022-05-24 19:04:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/util/CheckPassDialog.java`

### 55. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
- **Commit ID**: `f00f82cb6b0be171a0174cfbd03708d88ebae3b9`
- **作者**: yanann_chen
- **日期**: 2022-05-25 14:01:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 56. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤[補]
- **Commit ID**: `a8f914520555ca73234fbf0c7fead761f49bd875`
- **作者**: yanann_chen
- **日期**: 2022-05-25 10:57:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 57. [Web]Q00-20220524001 修正表單欄位設定小數點後四捨五入，當欄位值為負數時，四捨五入計算有誤
- **Commit ID**: `92d2f06ff00910ede22c220a303bb88dfaa46d6b`
- **作者**: yanann_chen
- **日期**: 2022-05-24 17:45:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 58. [內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)
- **Commit ID**: `f7abe4bb2ddbd60043acc83d567a426828ab2ce3`
- **作者**: walter_wu
- **日期**: 2022-05-23 18:19:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 59. [流程引擎]Q00-20220520003 修正流程關卡設定「不寄送待辦通知信」時，無法執行流程逾時跳過功能
- **Commit ID**: `c736e90c69c8718d13d0d4ed667e37eb6cc66433`
- **作者**: yanann_chen
- **日期**: 2022-05-20 15:42:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 60. [BPM APP]C01-20220516002 修正行動端FormUtil.disable為true時Dropdown元件顯示異常問題
- **Commit ID**: `e867113bc82fba87d0155404b131c6579e495721`
- **作者**: 郭哲榮
- **日期**: 2022-05-18 18:47:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 61. [Web]Q00-20220518001 修正退件表單資訊與開啟的表單關連錯誤
- **Commit ID**: `1d1f78987f576456c88b36bf6fcc7f22af35cd48`
- **作者**: 林致帆
- **日期**: 2022-05-18 10:39:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 62. [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題[補]
- **Commit ID**: `308d711ac70660457534c193ad6b766ecfca5421`
- **作者**: 郭哲榮
- **日期**: 2022-05-17 15:12:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 63. [BPM APP]C01-20220511002 修正行動端Grid元件在編輯後未繫結元件欄位會變成空值的問題
- **Commit ID**: `111da87c3aab7f8bef5481d6b2302367813faa76`
- **作者**: 郭哲榮
- **日期**: 2022-05-16 15:10:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 64. [流程引擎]A00-20220511001 修正使用者輸入到表單TextArea的內容在儲存表單後變成亂碼的問題
- **Commit ID**: `f6f7a2a2671881d90f6465235cc44fbbe470add9`
- **作者**: yanann_chen
- **日期**: 2022-05-12 18:19:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 65. [Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」
- **Commit ID**: `8e3c11ac3da316033d2f492b08f924a2b75a70f3`
- **作者**: wayne
- **日期**: 2022-05-12 16:55:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`

### 66. [流程引擎]Q00-20220512002 修正針對同一筆待辦事項，使用者從郵件進入畫面與從首頁進入畫面的速度有明顯落差
- **Commit ID**: `90fcad606b409e4042b1bef7deedac699c81a377`
- **作者**: yanann_chen
- **日期**: 2022-05-12 14:36:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 67. [Web]Q00-20220411005 修正使用者在絕對位置表單進行簽核時遭遇產品程式錯誤
- **Commit ID**: `491da6dbb2e32308231f696260e999bccc245e0b`
- **作者**: yanann_chen
- **日期**: 2022-05-12 14:12:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 68. [內部]Q00-20220512001 bootstrap-table-1.18.3.js更換檔案名稱
- **Commit ID**: `feca23c9bbd4d4fa19731df8d118eaa6119f321c`
- **作者**: yanann_chen
- **日期**: 2022-05-12 11:47:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js`

### 69. [Web]Q00-20220511005 修正T100拋轉單據中有舊值的單身內容沒有顯示為紅色
- **Commit ID**: `e857eee9d14d7900dac6823a0c3d0e80875b2810`
- **作者**: yanann_chen
- **日期**: 2022-05-11 15:03:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js`

### 70. [內部]Q00-20220511003 調整BCL8轉檔Timeout從預設2分鐘拉長到5分鐘
- **Commit ID**: `362595d986a914d1d73bd29a134efdbac19a0843`
- **作者**: walter_wu
- **日期**: 2022-05-11 14:44:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 71. [流程引擎]Q00-20220506001 調整使用者「授權的流程」，流程清單筆數改為設定檔設定
- **Commit ID**: `8cdb96c57f96594b3875ade082de583f64626fa3`
- **作者**: yanann_chen
- **日期**: 2022-05-11 12:07:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 72. [流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料
- **Commit ID**: `27e790b2aac3873472de9abaeaffd2dc73806941`
- **作者**: yanann_chen
- **日期**: 2022-05-11 11:23:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 73. [流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知
- **Commit ID**: `cb108e61e86af69550310d90255b150951786861`
- **作者**: yanann_chen
- **日期**: 2022-05-11 11:10:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 74. [Web]Q00-20220511001 調整列印表單畫面簽核歷程，移除「資料代號」、「通知者」欄位
- **Commit ID**: `ee4d7001d1002b9eb3ce8c39c6ed9b186ebabbc1`
- **作者**: yanann_chen
- **日期**: 2022-05-11 10:54:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 75. [Web]Q00-20220510001 修正IE瀏覽器開啟產品授權註冊頁面時，畫面跑版呈現異常
- **Commit ID**: `b5e6b24502de1f68fa32e7b5a8c18577599e2e0e`
- **作者**: wayne
- **日期**: 2022-05-10 15:59:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`

### 76. [BPM APP]C01-20220509009 修正行動端在Grid元件存在沒有繫結元件情況下點擊編輯按鈕會失敗問題
- **Commit ID**: `94ed24fd530b825fd409ddf9be9980e440c9f68c`
- **作者**: yamiyeh10
- **日期**: 2022-05-10 10:49:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 77. [Web]Q00-20220509002 修正授權的流程使用closeTime(結案時間)排序會報錯的問題
- **Commit ID**: `a4a56b5209e3ae4e8b658eb18dfcb56fa7bc2e19`
- **作者**: walter_wu
- **日期**: 2022-05-09 16:41:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 78. [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果[補]
- **Commit ID**: `0b32f722e9b9f876c594e612079f917c99549eb3`
- **作者**: 王鵬程
- **日期**: 2022-05-05 17:52:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 79. [Web]Q00-20220505001 修正欄位有設單身加總且有設『轉換文字至對應欄位』，在新增到grid後，欲顯示文字的欄位不會顯示結果
- **Commit ID**: `f0ac924b4b31ad4e2cd50ed2318ed58e8e9c30b9`
- **作者**: 王鵬程
- **日期**: 2022-05-05 15:24:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 80. [Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢
- **Commit ID**: `2e43780e2adce65ebb4eb223e1162da0309ee205`
- **作者**: 林致帆
- **日期**: 2022-05-05 11:00:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 81. [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]
- **Commit ID**: `f7bc2ccbe0e53fcce61966ae4652c9af75b0869c`
- **作者**: yanann_chen
- **日期**: 2022-06-20 16:44:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 82. [Web]Q00-20220616001 調整待辦通知信中簽核歷程的處理者欄位移除粗體樣式
- **Commit ID**: `3e92541da2e6f767fafd2b39762b71fabca29cc7`
- **作者**: 林致帆
- **日期**: 2022-06-16 13:59:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 83. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
- **Commit ID**: `6e8c290af7d49313599bc9d2c487f1a1b0f0be7c`
- **作者**: yamiyeh10
- **日期**: 2022-05-26 09:50:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 84. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
- **Commit ID**: `4ecab37c0ba800aff656e15697922a7905333a38`
- **作者**: yamiyeh10
- **日期**: 2022-05-16 15:15:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 85. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題
- **Commit ID**: `c83c05c6362fbafaf767d8fbae44c8f085e1d43d`
- **作者**: yamiyeh10
- **日期**: 2022-05-10 18:03:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 86. [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題
- **Commit ID**: `5241a7266e699eae9ce7df37a8318f455eba5260`
- **作者**: yanann_chen
- **日期**: 2022-05-04 14:02:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 87. [BPM APP]C01-20220419001 修正移動端選項元件使用動態塞值且隱藏標籤會顯示異常問題
- **Commit ID**: `cbb1f75cdd68334a25f6bd6afaacff11663fed80`
- **作者**: 郭哲榮
- **日期**: 2022-04-28 12:23:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 88. [Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色
- **Commit ID**: `99882ef3e28f0fe40d995613bfc079c3cebef6f9`
- **作者**: 王鵬程
- **日期**: 2022-04-27 16:50:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 89. [內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤
- **Commit ID**: `e21554874ff8f1a19e7913cd46373a366085b4d5`
- **作者**: walter_wu
- **日期**: 2022-04-27 15:00:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 90. [流程引擎]A00-20220519003 修正終止前置流程時，後置流程完成流程撤銷後，前置流程出現「派送失敗」的錯誤訊息，無法正常終止
- **Commit ID**: `b6ce7ebaa673892bb38fbbc04eb1bb7883c9bc30`
- **作者**: yanann_chen
- **日期**: 2022-05-20 14:08:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 91. [Web]A00-20220517004 修正調離職人員帳號更新排程預設出貨端口改成8086
- **Commit ID**: `d2588982d05156ee7d73b94fd6796fb2c74ec775`
- **作者**: 林致帆
- **日期**: 2022-05-19 17:04:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaJobs.xml`

### 92. [內部]Q00-20220509001修正設定檔ESS內網IP敘述的Oracle指令有誤
- **Commit ID**: `d8c4aa3f68bc813e3f9153809738802f6159904e`
- **作者**: 林致帆
- **日期**: 2022-05-09 08:55:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.1_DML_Oracle_1.sql`

### 93. [流程引擎]A00-20220513001 修正ProcessMapping在Oracle遺漏attachInfo欄位導致拋單失敗
- **Commit ID**: `5cabb6a193ce4d7930008bd9863bf345c2e22b34`
- **作者**: 林致帆
- **日期**: 2022-05-13 17:23:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 94. [內部]A00-20220517002 修正DB為 Oracle時，產品授權畫面無顯示模組名稱
- **Commit ID**: `36049cb4b8d31b5684bcc014bc16f8b2c192f97f`
- **作者**: wayne
- **日期**: 2022-05-17 11:32:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.1_DML_Oracle_1.sql`

