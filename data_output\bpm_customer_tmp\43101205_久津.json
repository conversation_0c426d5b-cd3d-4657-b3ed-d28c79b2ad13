{"company_id": "43101205", "company_name": "久津", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\43101205_久津\\01客戶基本資料", "files": [{"filename": "久津.txt", "raw_content": "久津\r\n\r\n新正式機 AP\r\n**************\r\n8086port\r\n\r\n新測試機 AP\r\n192.168.24.69\r\nhttp://**************:8080/NaNaWeb/\r\nadminsitrator/1234\r\n\r\n新 DB 主機(含正式+測試DB)\r\n192.168.254.68\r\nsa/Bomy8502\r\n測試機DB名稱為：EFGPTestNEW2\r\n正式機DB名稱為：NaNaNEW1\r\n\r\n-----------------------------------------------------------------------------\r\n測試機team viewer密碼有重新設定固定的Flow8502\r\nID: *********   密碼: Flow8502\r\nID: ***********   密碼: Flow8502(固定)\r\nEFGP AP測試機IP:**************  \r\n帳號:FGP (網域:本機), 密碼:Flow8502\r\nEFGP 測試機AP與測試DB是不同台.\r\n測試DB連線資料如下:**************\r\n測試DB 是:EFGPTestNEW2\r\n\r\n\r\n\r\n\r\n正式機: Teamviewer連線,ID: ********* 密碼: Flow8502\r\n正式資料庫DB name : NaNaNEW1\r\n ID  : sa\r\n 密碼: Bomy1030\r\n \r\n\r\n 新測試機已經安裝下列軟體完成.\r\n1. winrar\r\n2. notepad++\r\n3. teamview (連線ID:   ***********   密碼:Flow8502 )\r\n4.原測試機D:\\EFGP\\jboss-4.2.3.GA複制到新測試機\r\n5.版更軟體已放在光碟機中\r\n(提醒,新測試機是64位元的.記憶體16G,Jboss記憶體要調整))\r\n6.新測試機規格如下, IP為**************\r\nPs:測試資料庫的位置沒有變更(IP:**************, DBname:EFGPTestNEW2)\r\n\r\n\r\n\r\n\r\n", "structured_data": {"測試機db名稱為": "EFGPTestNEW2", "正式機db名稱為": "NaNaNEW1", "http": "//**************:8080/NaNaWeb/", "id": "sa", "efgp ap測試機ip": "**************", "username": "FGP (網域:本機), 密碼:Flow8502", "測試db連線資料如下": "**************", "測試db 是": "EFGPTestNEW2", "正式機": "Teamviewer連線,ID: ********* 密碼: Flow8502", "正式資料庫db name": "NaNaNEW1", "password": "Bomy1030", "3. teamview (連線id": "***********   密碼:Flow8502 )", "4.原測試機d": "\\EFGP\\jboss-4.2.3.GA複制到新測試機", "ps": "測試資料庫的位置沒有變更(IP:**************, DBname:EFGPTestNEW2)", "host": "**************"}, "source_path": "C1.客戶維護相關\\43101205_久津\\01客戶基本資料\\久津.txt", "file_size": 1092, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:25.563683"}], "total_files": 1, "processed_at": "2025-08-26T10:46:25.563692"}