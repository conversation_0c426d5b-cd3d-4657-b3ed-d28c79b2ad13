{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.5.1", "date": "2022-06-26 22:27:16", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.1", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.4.3", "date": "2022-06-26 22:44:16", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.3", "author": "lorenchang"}, "比較時間": "2025-07-18 11:43:18", "新增commit數量": 188, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "e7917333eea06db96446ff072ea8f0ea122ad7e9", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.1", "提交日期": "2022-06-26 22:27:16", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "08a8fd57c614918597938ff94606924115cab4fb", "commit_訊息": "[BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]", "提交日期": "2020-12-18 17:56:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f7ed638573a0a53f41adead5a52e83956de8370", "commit_訊息": "[BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]", "提交日期": "2020-12-18 13:40:04", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "19ba24efe4df604deb1731756b5cf2fd8667fc5f", "commit_訊息": "[BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]", "提交日期": "2020-12-18 11:12:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0c6c754e96b352ebe36dc36a362977873fae14e5", "commit_訊息": "[BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得", "提交日期": "2020-12-17 20:06:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "039d550165748dc4473f140d93a7d59366cc0117", "commit_訊息": "[Web]S00-20201120001 調整報表註冊器設定SQL註冊器時，若SQL語法使用系統變數時，須加上單引號(此邏輯同資料選取器、SQL註冊器、SQLCommand)", "提交日期": "2020-12-17 17:39:31", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b4480150bf3af22dea00acd80b9e998b83021a3c", "commit_訊息": "[內部]修正IMG從推播通知進入簽核後無法提交派送問題", "提交日期": "2020-12-17 11:28:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5d6ae270723d3d3f9e086a4809fb55f9f96e90a", "commit_訊息": "[BPM APP]調整IMG的互聯認證接口支持互聯3.0.1版本", "提交日期": "2020-12-17 11:24:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformDAPService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleModuleFeatures.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 20}, {"commit_hash": "7956673034e75bdfe10f521d42f58fdbac01029c", "commit_訊息": "[內部]Q00-20201211002 修正在IE在入口平台整合設定，新增鼎捷移動連線資訊，\"是否整合鼎捷雲\"的選項元件點擊時發生錯誤", "提交日期": "2020-12-11 12:12:19", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a66347255ac780ed0cc120aebb7d5a0b5b5c1585", "commit_訊息": "[內部]Ｑ00-20201211001 修正在IE和Safari瀏覽器頁面使用入口平台整合設定可新增多組問題", "提交日期": "2020-12-11 10:27:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75e709bd84742a991dccb0069f30acf5d5dc5e29", "commit_訊息": "[內部]Q00-20201210005 修正動態渲染表單向前加簽後無法連續簽核問題", "提交日期": "2020-12-10 20:59:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d61483ff5c80f1973aa93aa09344e50a1c4921dd", "commit_訊息": "[內部]Q00-20201210004 修正入口平台整合設定沒有任何連線資訊出現紅字訊息的問題", "提交日期": "2020-12-10 19:10:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "da323615587964e8e087907e40c1af0b6f36b51d", "commit_訊息": "[內部]Q00-20201210003 修正使用者管理維護作業中新增使用者，選擇一使用者後不會自動關閉視窗問題", "提交日期": "2020-12-10 18:50:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6caf01956775523176b8f1fac80a2668532047c", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2020-12-10 18:49:48", "作者": "詩雅", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "72607db590111ce47fc436a5dd95f8a9f7921a45", "commit_訊息": "[內部]調整入口平台整合設定測試連線，整合鼎捷雲時，排除建議步驟說明", "提交日期": "2020-12-10 18:48:42", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "620633fc3dab0ea1ad8e75661dbad2775d2d4ece", "commit_訊息": "[BPM APP]Q00-20201210002 調整動態渲染表單的附件支持內網使用", "提交日期": "2020-12-10 18:47:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c8873743a4e75864b6798e708e9011928b333cb5", "commit_訊息": "[BPM APP]調整SystemVariable中BPM APP平台整合與服務位址設定的描述說明[補]", "提交日期": "2020-12-10 18:30:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6a19642a9e5cc1aa722e959779ff2c77c932b36e", "commit_訊息": "[流程引擎]Q00-20201210001 增加log：整合接口的Request XMl", "提交日期": "2020-12-10 11:51:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7e58771e8addfe4cb63692282e8e207c0bbc9bf2", "commit_訊息": "[Web]Q00-20201209004 修正RWD的Grid欄位數太多時，用產品提供欄位寬度修改語法，欄寬未改變", "提交日期": "2020-12-09 18:24:24", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0877f8a1c3233d8584b568d15ad707fbaabcc579", "commit_訊息": "[BPM APP]Q00-20200827002 修正LINE的部分推播通知會顯示郵件內文範本tag問題", "提交日期": "2020-12-08 18:44:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d324c12dcef2330baf982f802e8cc21e1c45b9b0", "commit_訊息": "[內部]Q00-20201207001 修正入口平台整合設定連線管理資訊出現scroll bar時畫面跑版[補]", "提交日期": "2020-12-08 10:01:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6a1e7dbb537b8cce5ba5a103fdebddda1466d0ee", "commit_訊息": "[Web]Q00-20201207005 修正舊客購入越南語系模組，版更後模組及程式缺少越南語系", "提交日期": "2020-12-07 19:22:37", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8d937d23b8a92a6dd38bd5634662081eae15350e", "commit_訊息": "[內部]Q00-*********** 調整使用者批次匯入，增加判斷excel的內容資料格式正確性[補]", "提交日期": "2020-12-07 19:16:39", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "95f2310da8e0fab45733fe822f3c4452d406d0a6", "commit_訊息": "[內部]Q00-20201202002 調整入口平台整合企業微信與鼎捷移動時，微信使用者不可操作IMG相關功能[補]", "提交日期": "2020-12-07 19:10:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b697807cfcc10366e9608129bc9095940411d804", "commit_訊息": "[BPM APP]Q00-20201202007 調整使用者管理新增使用者時的多語系[補]", "提交日期": "2020-12-07 17:09:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88edfb0a38e97c233f4ae799c9c9a612eae93b9e", "commit_訊息": "[內部]Q00-20201207004 修正企業微信使用者管理頁面的啟用狀態顯示為2", "提交日期": "2020-12-07 16:39:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4f295de643958fe38e16a67b84f87a048d491e9", "commit_訊息": "[BPM APP]Q00-20201207002 修正在企業微信編輯我的最愛時會將返回不儲存的紀錄一併儲存問題", "提交日期": "2020-12-07 16:25:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9464faf9e433488de45fd5eab807bba0c7204e44", "commit_訊息": "[內部]Q00-20201207001 修正入口平台整合設定連線管理資訊出現scroll bar時畫面跑版", "提交日期": "2020-12-07 15:22:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "195dba7acf1fc965f5ff0a966a146c83185427d5", "commit_訊息": "[BPM APP]Q00-*********** 修正LINE在終止流程的推播通知會顯示簽核歷程問題", "提交日期": "2020-12-04 20:21:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "036e2147106d921e8b857269e66cbc423a184112", "commit_訊息": "[BPM APP]Q00-*********** 修正釘釘與企業微信登入驗證時若BPM用戶未啟用的錯誤訊息顯示不全問題", "提交日期": "2020-12-04 19:58:05", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4d5344fe7d9fcead725f109c9356a6001c71298e", "commit_訊息": "[內部]Q00-*********** 調整使用者批次匯入，增加判斷excel的內容資料格式正確性", "提交日期": "2020-12-04 13:41:07", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "64aeee3786cba2d172e8f1fae76df51dbf9a8620", "commit_訊息": "[內部]Q00-*********** 修正Mobile的SQLcreate語法", "提交日期": "2020-12-03 17:20:53", "作者": "詩雅", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "37fadf45693ed711654d9d94a24823921c2c6b42", "commit_訊息": "[內部]Q00-20201202013 調整入口平台無整合資訊時使用者維護管理不該顯示批次維護頁籤的問題", "提交日期": "2020-12-03 17:12:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2f09f44590354f78ad417422cdd3808a467d7751", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2020-12-03 16:19:41", "作者": "詩雅", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "51b4219b67b6a3f17b76e4c49e7fe655bb334b9f", "commit_訊息": "[內部]Q00-20201202017 修正會議簽到列表字段接口，在IMG應用配置顯示亂碼", "提交日期": "2020-12-03 16:16:47", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "496d4408841d2356d2b20d31cd38b80931b50687", "commit_訊息": "[內部]Q00-20201202012 調整若入口平台無任何連線資訊時其他工具佈署提示資訊的畫面", "提交日期": "2020-12-03 16:16:19", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7dc9839517e1eb3d5ee27e5d0470df6b6fe8fb5d", "commit_訊息": "[BPM APP]Q00-20200926005 修正ESS表單grid欄位在中間層顯示順序異常問題", "提交日期": "2020-12-03 15:44:08", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "9a06a756b765eb3a1c05a6d097061fbc1280a9e1", "commit_訊息": "[內部]Q00-20201202006 調整系統設定中BPMAPP平台整合設定為1或2時若入口平台含已停用的資訊其提示訊息沒出現的問題", "提交日期": "2020-12-03 14:45:01", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0c80876e722ab2b937b59e5dfba5a558bcc48d19", "commit_訊息": "[BPM APP]Q00-20201202008 修正使用者管理頁面微信使用者匯出樣版手機號碼範例格式錯誤問題", "提交日期": "2020-12-02 20:11:18", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f18089b05078b7e3d255872e855fe7cfa402bdda", "commit_訊息": "[BPM APP]Q00-20201202007 調整使用者管理新增使用者時的多語系", "提交日期": "2020-12-02 20:00:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4aa2d8f6927690d8b39d9c525c089dd6f937b8e2", "commit_訊息": "[內部]Q00-20201202004 調整入口平台整合單一方案時其他工具佈署網址應顯示對應設定資訊問題", "提交日期": "2020-12-02 19:44:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4e7cb1f3c8e46b2b74e0be0217f642a04d767539", "commit_訊息": "[內部]Q00-20201202003 修正入口平台整合企業微信與鼎捷移動時，匯入功能異常問題", "提交日期": "2020-12-02 19:08:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c640fca02b09f62eefda6042e4390126b81df842", "commit_訊息": "[內部]Q00-20201202002 調整入口平台整合企業微信與鼎捷移動時，微信使用者不可操作IMG相關功能", "提交日期": "2020-12-02 18:40:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a2cebeadf0fd28cedeece0e326677dc09b7373bc", "commit_訊息": "[BPM APP]Q00-20201202010 修正系統設定的BPMAPP鼎捷移動平台整合設定必須重啟才生效問題", "提交日期": "2020-12-02 17:11:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4c913721f527f847d6ee8cfe681186e61cd35672", "commit_訊息": "[BPM APP]Q00-20201202005 調整系統設定中BPMAPP平台整合設定為0時不應顯示入口平台整合設定", "提交日期": "2020-12-02 17:06:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a115e30b1899474d8c37f3ddab9ef38a7c96faa", "commit_訊息": "[內部]Q00-20201202001 修正其他工具佈署網址中的統計元件配置點擊儲存時會儲存失敗問題", "提交日期": "2020-12-02 15:18:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "03b73c087b7464fbfa23c8b98765dbd4f26a2ff4", "commit_訊息": "[內部]合併5.8.5.1 brach多語系至總表", "提交日期": "2020-12-01 17:37:36", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 2}, {"commit_hash": "8f8fe0707f24cb6f0b880f4330417a9a1d885693", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-12-01 11:17:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "729fab7adf2bc17cdc8772423c733caea67fa306", "commit_訊息": "[BPM APP]Q00-20200926003 修正IMG中間層Grid不會依照流程設定顯示問題", "提交日期": "2020-12-01 11:02:08", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3da7e276a36c7a4a9db1b27b616af55ad0e476f0", "commit_訊息": "[BPM APP]Q00-20201109001 調整IMG統計元件文字顏色", "提交日期": "2020-12-01 10:08:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5b2e4365a01f1cb51fb1fc1e9ecada85c023d8a6", "commit_訊息": "[Web]C01-20200722004 顯示流程清單頁之流程筆數調整為採用設定檔之設定[補]", "提交日期": "2020-11-30 16:05:46", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e7fd7bca97345625e52b8a016f4211d9c97dfef", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-27 20:08:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterDesignFormReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8012807d8968c3de4bab1bd515c6fc27c3003a4a", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能[補]", "提交日期": "2020-11-27 20:06:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e91734bdada259117bfe73fc3d576971976cceb7", "commit_訊息": "[BPM APP]調整入口平台整合資訊新增後需重啟才會讀到設定資料", "提交日期": "2020-11-27 19:17:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "177d987964698d775ae4a1c8d60a1a806b6d93d6", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-27 19:14:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3394191efbf1a83179643a1b8d9addc101a61a70", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-27 19:04:20", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "6994cc0ee91f1c578f2c2a6caf1cfb8bdaf5a7ac", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-27 18:52:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "45681c473e58c88888fd6ed9eb45168390ae1e0d", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能[補]", "提交日期": "2020-11-27 18:12:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7e3c25f4e4d68080e6fb94a918365b6c85c39a2", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能[補]", "提交日期": "2020-11-27 18:10:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ReassignData.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SubmitInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "16f48e9915d66e1bed5702da8787bfc4d137b4b2", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能[補]", "提交日期": "2020-11-26 20:07:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f7cbfa9722ff2a0b29cbc35d4f07bd88fbd9f66c", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能[補]", "提交日期": "2020-11-26 17:38:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7287d008647ed96f5a57578f1955aaa7cb156054", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-26 17:07:11", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "492479579ae9feba602a449d68781dd676356986", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能[補]", "提交日期": "2020-11-26 16:52:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleReassignEmployeeRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReassignEmpRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataReassignEmpRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "e19463b550fa7e60ef6099007fea4ac4a037fbf7", "commit_訊息": "[BPM APP]調整SystemVariable中BPM APP平台整合與服務位址設定的描述說明", "提交日期": "2020-11-26 15:13:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dfabb5a8cf4ab717eab9ed539795962c86c8027e", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-26 12:03:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9e95993ade535794c15deeb0c97fcf6d3f1ce59b", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-26 11:30:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c42e1c927967ed3a256af73764e7aa4173bfdadd", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-26 11:17:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "024833a274fd840b39229c27590e6487145348c7", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-25 18:22:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e40d8d157e4f6073b5b64cc130e97c84aaa96f83", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-25 17:35:38", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformConfig.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "200f77d744d1648124b252816cbde692117f586b", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-25 15:31:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "106d811cbd573d55c009ad9a74e176e60d55128c", "commit_訊息": "[BPM APP]IMG動態渲染表單新增加簽功能[補]", "提交日期": "2020-11-25 10:28:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5dc74ced07abb6fc2a1ad5fa2c715508b77d4b33", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-25 09:17:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "571c1a371f908547b6b168607347fe49d1945460", "commit_訊息": "[BPM APP]IMG動態渲染表單新增加簽功能[補]", "提交日期": "2020-11-24 19:59:45", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1e52d04ef62b558e5059fa0da18c9713bc9653a2", "commit_訊息": "[BPM APP]IMG動態渲染表單新增加簽功能[補]", "提交日期": "2020-11-24 18:38:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "22ccbc62fba66fb32f9ed39dfbc03717957a0c00", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-24 14:46:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7f3fbeb547e5fa97f94bd76c7f6366e97c03eb3b", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-24 14:11:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ca62a0c324d9a9145066184001ca75cd9357da9b", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能[補]", "提交日期": "2020-11-23 18:45:06", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DataArrayContent.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DataSetList.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDatasetList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "16f206a9144517cc4dadbc40502151ec6636c781", "commit_訊息": "[BPM APP]IMG動態渲染表單新增加簽功能", "提交日期": "2020-11-23 10:48:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/AddActivityData.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/EmployeeInfo.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleAddActEmployeeRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterAddActEmpRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataAddActEmpRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SubmitInfo.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "25a051bf3f612ab4521f97007936243260328078", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-20 18:51:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "13c4e36fa15d97193035b081d7582c68c69d69a8", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-20 17:20:51", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "dc2d6630ac2bed65e2ef5d233fd405fec96f1661", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-19 14:37:42", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "77df18aaa6c94201ec474a0115d9ab14cb773cab", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-19 14:03:43", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "9b374b59e269df8318e9a8e151b814f61f599691", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能[補]", "提交日期": "2020-11-19 10:28:33", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b237d1195f14808a0516364011cfa673599da792", "commit_訊息": "[BPM APP]新增動態渲染表單轉派功能", "提交日期": "2020-11-18 20:29:31", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "bb676fe75ee9f28496fb1e76a80bbac00e037de1", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-18 20:25:41", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "6448aa3abf543cb11cc51942e0dcf5f9f16c19fe", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-18 14:00:07", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b63634a46353feba864632d3a5b87ae9e4185889", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-18 13:33:54", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "b86d097a11f11edc53500718afef4405d26e9a1f", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-17 15:23:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a8a581077f02d4fb60c6408a5ee7e81fd08453db", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-16 19:14:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "1a3b3b30d688b2a9bb47a42f70d42d70a2dbd0b8", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-16 14:41:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformConfig.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "65f0e8546bc9cee6833fd0c93c4575dee35da27c", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-16 14:07:27", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DML_InitMobileDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DML_InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "cc565cbaaffeaca9b24ae2568b14085c508c73f9", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-13 18:41:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "8ea3d45d7fccb689379fa277af0b23ffa40c79ad", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-13 09:53:52", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-restful-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "f8f41509d2e386b6d26c2a3fe5d2b8fab0a2838b", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-12 20:11:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ae58d734eb481ab73568f1389f47d48ddda8fbf0", "commit_訊息": "[BPM APP]優化IMG列表接口取動態渲染表單應用ID時的速度", "提交日期": "2020-11-12 14:35:37", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessInstanceForListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ReassignWorkAssignForListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstForTracing.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "5f83dc5e45793cc81b676c4c50e1f732f456a5c3", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-12 12:02:16", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f6a3c2830f6d125435120700a9154d8b9d5304b", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-12 11:59:13", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "20c87923df00b26ca6f30e3a6648aa2b0def6bf7", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-12 11:20:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d555c92b41aef260cfe859a97b24a060480762e", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-12 11:16:30", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "fc8022a7fce5e34e751a6cbdc33bf4941f6286ba", "commit_訊息": "[BPM APP]Q00-20201103004 調整行動端腳本樣版設定元件字體顏色的說明", "提交日期": "2020-11-06 18:35:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0cb879c99d8219bcdc5f702881b58cc099070e10", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-11-05 16:20:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "55c9b5081104894c690a8e00e3d3802aaa183cb8", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-04 20:36:15", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fa3c05a41b1f17db77f48046acf0badecffcd36a", "commit_訊息": "Q00-20201104003 修正「攻略雲指標維護作業」點「確定」按鈕後出現錯誤Invalid column name 'indicatorCalculationType'", "提交日期": "2020-11-04 17:39:07", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed648adaeed7411c2f3d6f84b21ee23b32c2c0b7", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-04 15:13:13", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e412189d0ee278ecdd10ca00f2cb3acf1e81cb84", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-04 14:08:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "8c2f050a087ef098a877e8e63daa1e208cf7b29a", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-02 18:28:15", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "573ec9d180179efc747694cf739fc3e985fa81a1", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-11-03 16:54:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c119a37d78a8937c5f6f183b81cb1d116e6ef8c2", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-10-30 09:35:27", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "93821c10480932ed177d0c17ba432718fd88e505", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]", "提交日期": "2020-10-29 19:18:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "aeb96e311b8bf861960938f90f1e111d5fe4b13c", "commit_訊息": "[BPM APP]修正多語系檔案", "提交日期": "2020-10-29 17:08:03", "作者": "詩雅", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xls", "修改狀態": "重新命名", "狀態代碼": "R096"}], "變更檔案數量": 1}, {"commit_hash": "07067aad28905270313897a6811e086720cf7218", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-10-29 15:59:41", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c1f2608599eeedc280a389f9cb4e54c1b7338ba5", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-10-29 15:42:38", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "763ece41980af787b5468878835d0478158c9730", "commit_訊息": "[內部]Q00-20201029003 StartNextActInstBean調整log內容", "提交日期": "2020-10-29 15:01:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82e43edf80bf8e704532c9126483337098784d92", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-10-28 14:26:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "91b967f726c7991c06a1f042d5d22b44956699c9", "commit_訊息": "[流程引擎]Q00-20201026001 修正流程派送到下一關，流程下一關會有偶發性的狀況無法啟動", "提交日期": "2020-10-26 16:28:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "253eb5b40c481db1a880d648d168cecb73443575", "commit_訊息": "[BPM APP]調整IMG接口與推播功能可支持雲整合方案", "提交日期": "2020-10-23 10:52:58", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "9857d9062554b8d2a5f596dd2133d41191f81b5b", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]", "提交日期": "2020-10-22 14:59:10", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8ee21ec23d2e7a27efb0bdedf7b81e5bd909efad", "commit_訊息": "[BPM APP]調整行動簽核管理中心新增支持雲移動平台整合", "提交日期": "2020-10-22 14:52:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "0e626c49137916d7f08c0901a467632976b660ed", "commit_訊息": "[BPM APP]S00-20200724001 調整IMG詳情表單中的簽核歷程排序問題", "提交日期": "2020-10-20 12:01:47", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "3aa5770e35ce4ee706c8d5063465f0c2084a32e4", "commit_訊息": "[BPM APP]新增行動端會議簽到功能[補]", "提交日期": "2020-10-13 09:47:40", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "720246db8bca197a38d13af43e1fdb23016dcebb", "commit_訊息": "[BPM APP]Q00-20200929001 調整行動端以表單函式庫設定元件標籤無法載入所設定標籤文字的問題", "提交日期": "2020-10-08 17:19:34", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c44d730355e9bf3fe6ecf45119501803c9a1cce5", "commit_訊息": "[BPM APP]新增行動端會議簽到功能[補]", "提交日期": "2020-10-07 18:12:40", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/dataformat/MobileModuleDataTrasnferService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleModuleFeatures.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "a19dfe11506fc56817ebae28c1c886a54b96ccfe", "commit_訊息": "[內部]Q00-20201007001 調整 SystemVariable設定檔，流程主旨標示作廢的的四個設定値的敘述內容", "提交日期": "2020-10-07 15:03:16", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8ddf105377428ba3f0cd4bc1f4ac3c2e9a4143d6", "commit_訊息": "[組織設計師]A00-20200929001 修正無法透過介面刪除組織資料", "提交日期": "2020-09-30 14:02:09", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "5d5d37c3cd5fc3f53ca9f68cf0c96f524d5feb5b", "commit_訊息": "[BPM APP]新增行動端會議簽到功能[補]", "提交日期": "2020-09-30 12:16:56", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformModuleTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "8346286a22dc76623d0a3a132e9386aa007dcf9d", "commit_訊息": "[BPM APP]新增行動端會議簽到功能[補]", "提交日期": "2020-09-29 18:38:34", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9b5b6500bba1a7df439e47547eeeae112c0f0b8f", "commit_訊息": "[BPM APP]新增行動端會議簽到功能[補]", "提交日期": "2020-09-26 10:08:47", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f3f77d5cfbf6fc2fd3855861ca79315b680a07b8", "commit_訊息": "[BPM APP]新增行動端會議簽到功能[補]", "提交日期": "2020-09-25 19:10:59", "作者": "詩雅", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xls", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "e93c4ea378c4bf524f8bc30317f2d7c389310b40", "commit_訊息": "[BPM APP]新增行動端會議簽到功能[補]", "提交日期": "2020-09-25 17:03:58", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "54664540b2ccc2fc9ac296a8e8b74320e0c4bb56", "commit_訊息": "[BPM APP]新增行動端會議簽到功能", "提交日期": "2020-09-25 16:56:11", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldButton.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListDataset.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "0d9518a27964d5a285674eb5c4d72698f6125d39", "commit_訊息": "[Web]S00-20200713001 調整功能清單中掛載模組的排序方式[補]", "提交日期": "2020-09-21 11:30:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "db45a790bf2116c771c9628ac03d06a491183c90", "commit_訊息": "[Web]C01-20200722004 顯示流程清單頁之流程筆數調整為採用設定檔之設定", "提交日期": "2020-09-08 18:41:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/SQLHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "69117d7cd80811e24240b62d5f7c322fc9a3d7c4", "commit_訊息": "[流程引擎]A00-20200812003 修正當要做組織同步的資料量太大時，會導致組織同步出現錯誤[補]", "提交日期": "2020-09-18 14:22:37", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d92d202f7df7c9d6e8e38d34a41f2e5b60c03ed2", "commit_訊息": "[流程引擎]A00-20200812003 修正當要做組織同步的資料量太大時，會導致組織同步出現錯誤", "提交日期": "2020-09-04 09:49:33", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/syncorg/SyncTable.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ad4a75b781a4859298cc9fbc7241b1d6ea3714a9", "commit_訊息": "[Web]Q00-20201127004 修正從外部連結進流程圖無法完整顯示", "提交日期": "2020-11-27 18:01:45", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "57bcad10e803cdada57df466692d11cddb0cbd50", "commit_訊息": "[Web]Q00-20201127003修正使用者名稱有底線符號，造成流程加簽後，流程實例查看異常", "提交日期": "2020-11-27 17:20:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5aa2df2fd0e3012d6a2dae7e18b2285905bb2428", "commit_訊息": "[流程引擎]C01-***********修正簽核時，系統會有卡住的狀況[補]", "提交日期": "2020-11-27 16:43:36", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "45db233449e66e28ba823fb9baddc65c261855ad", "commit_訊息": "[ISO]Q00-20201127002 提供ISO文件檔案的URL下載服務", "提交日期": "2020-11-27 16:06:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d36b8eacef30e1ae971478d697128d46b7ddba81", "commit_訊息": "[流程引擎]A00-20201118001 修正使用者追蹤流程篩選流程發起人查詢無效", "提交日期": "2020-11-26 15:41:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "91014e3436f4d4949a486f712035f143ec5213ee", "commit_訊息": "[Web]C01-20201117001 修正在模組程式維護中修改系統預設模組頁面，將輸入框和按鈕改為禁用，儲存鈕隱藏", "提交日期": "2020-11-26 11:54:09", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ce0add84451975e7faadf6bb7f1d79f3bf66f38f", "commit_訊息": "[組織設計] 簡稱為空白在某些環境開啟職稱維護作業會出現異常", "提交日期": "2020-11-25 13:23:38", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/OrgDesigner.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5486aaf298434ac17fa34efddc128798eb899b73", "commit_訊息": "Revert \"[流程引擎] 修正多AP(A/B)時流程在A進核決層級，在另一主機簽核該流程時會出現找不到Entity的問題\"", "提交日期": "2020-11-25 12:03:02", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivitySetDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CustomProcessPackage.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2c07ecd464d5d7a7e212e6c9a81aa7d299aea53e", "commit_訊息": "[流程設計師]Q00-20201124002 修正流程有設可重定義屬性參考表單元件時，當刪除該元件後將無法點選可重定義屬性", "提交日期": "2020-11-24 18:08:17", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d572efc91c542e02aef5495d1e699c678aa541b8", "commit_訊息": "[流程引擎]C01-***********修正簽核時，系統會有卡住的狀況", "提交日期": "2020-11-24 16:00:49", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/LocalDocManagerDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/DocManagerDelegate.java", "修改狀態": "重新命名", "狀態代碼": "R081"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/CreateDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/background_service/GoodsHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/AttachmentHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/AttachmentViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DocFileUploader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 21}, {"commit_hash": "6da42ea74fa04b79c0586d8df0df47f5efd3272b", "commit_訊息": "[內部]Q00-20201124001 調整5.5.6.1_MSSQL.sql", "提交日期": "2020-11-24 14:47:11", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.6.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "83a1fae73712d583ff7402bd573423f7cf6e9983", "commit_訊息": "[內部]Q00-20201123001 調整5.8.2.1_DML_Oracle_1.sql", "提交日期": "2020-11-23 16:03:58", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.2.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9bd026d01552969dc15d81ed42e1b498b4d1c8f2", "commit_訊息": "[流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確", "提交日期": "2020-11-20 15:48:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "30724b45d01e2aa177f953fd002494d2da2853d2", "commit_訊息": "[Web]Q00-20201120002 修正在批次簽核的完成頁面，主旨有html時只能以文字呈現而不能以html呈現", "提交日期": "2020-11-20 15:24:31", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70253ef7bafe1b19cfc585ab6dcbb3d614fe48e3", "commit_訊息": "[組織設計師] 調整組織設計師，避免因為Cache資料有問題導致的非預期的異常", "提交日期": "2020-11-19 16:53:15", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ba84dfca6a03627ba402d2741d2659b3d8ee24e4", "commit_訊息": "[流程引擎] 修正多AP(A/B)時流程在A進核決層級，在另一主機簽核該流程時會出現找不到Entity的問題", "提交日期": "2020-11-19 16:40:50", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivitySetDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CustomProcessPackage.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f8ce21518e41d1649285c1f6b63522b6f0187d51", "commit_訊息": "[流程引擎] 修正5.8版SessionBeanApplication重覆更新的異常", "提交日期": "2020-11-19 16:33:48", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/app/SessionBeanApplication.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ed90573d96b4ea620f8bf6ba6a49d408daca107", "commit_訊息": "[流程引擎]Q00-20201111001 修正點選發起流程報錯、我的最愛打不開[補]", "提交日期": "2020-11-19 16:02:55", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/AbsAuthority.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f94615e5bbfaecfe7ee15f97005d319acf630471", "commit_訊息": "[內部]Q00-20201119001 調整SQL指令，補上\";--\"", "提交日期": "2020-11-19 14:58:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7d735d11acf1ade21574154757727a052fc3a45", "commit_訊息": "[TipTop]Q00-20201118004 修正TIPTOP端簽核表單，開啟表單就報錯", "提交日期": "2020-11-18 16:37:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8720a512c72a04b2549f80c1dac498a4c0edaaac", "commit_訊息": "[Web]Q00-20201118003 修正流程點選取回重辦會報錯", "提交日期": "2020-11-18 16:02:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "960f118d5f940c60b778f19abb984c22724ddbef", "commit_訊息": "[流程引擎]Q00-20201118001 修正流程實例刪除，造成流程定義開啓異常", "提交日期": "2020-11-18 08:36:33", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "959c9a4ee8e257cba646969edb5f599c9397cab8", "commit_訊息": "[BPM APP]C01-20201111003 修正在自訂義開窗搜尋時會異常的問題", "提交日期": "2020-11-16 19:17:06", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5982443bb69b35b70315f034a5c59f05120c6045", "commit_訊息": "[Web]Q00-20201116002 修正Check Box最後一個選項沒有額外輸入框時，Check Box必填判斷功能異常", "提交日期": "2020-11-16 17:55:35", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f3ccb640d6fff5914a61e4aa02dae255047a3021", "commit_訊息": "[Web]Q00-20201116001 修正Radio Button最後一個選項沒有額外輸入框時，Radio Button必填判斷功能異常", "提交日期": "2020-11-16 14:34:02", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f79b25e6ffc2aac19585a5f1d19fb648966bb2a5", "commit_訊息": "[表單設計師]Q00-20201113002 修正在IE中用FormUtil提供dropdown取選取值和checkbox設定值的方法會報錯", "提交日期": "2020-11-13 12:01:35", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0e403646fa077a9deb7c2f69f239961044063400", "commit_訊息": "[流程引擎]Q00-20201111001 修正點選發起流程報錯、我的最愛打不開", "提交日期": "2020-11-11 16:16:16", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/model/ProcessPackageModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/AbsAuthority.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/AuthorityUnits.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/PackageInvokeAuthority.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/model/ProcessPackageModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/src/com/dsc/nana/user_interface/apps/authority/model/AuthorityGroupClientModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "de07f82e89a5c6cc0defaa9434c5344d4c00c73f", "commit_訊息": "[Web]作業程序書的關卡清單改用關卡ID進行排序", "提交日期": "2020-11-11 14:07:29", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c03c1e850006c67937b38438525b2376048c7589", "commit_訊息": "[Web]A00-20201110001 修正追蹤轉會流程異常", "提交日期": "2020-11-11 13:42:54", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7020e992539a4832a8dd4c675e8093076dd94fba", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2020-11-11 11:55:00", "作者": "gaspard.shih", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c2586590b335370f64fb99813fe6ce50ed7374da", "commit_訊息": "[Web]BPM首頁於小螢幕時可顯示「可用時間」資訊", "提交日期": "2020-11-11 11:54:32", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0b77d42971b756213b2d89a15a4d1311f0aa836", "commit_訊息": "[BPM APP]C01-20201110002 修正企業微信在直接進入表單畫面時會因使用者尚未登入而導致畫面異常問題", "提交日期": "2020-11-10 19:58:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "429d9d62e1e19712ca5e50104bf4197d05fb614e", "commit_訊息": "[Web]Q00-20201110002 修正Ajax Service沒有帶出員工所屬部門資料", "提交日期": "2020-11-10 16:57:00", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/UserVo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1480fd9a3fe694d00711536a4930bb12c701f80c", "commit_訊息": "[Web]Q00-20201110001 修正舊版絕對位置表單轉成RWD會異常", "提交日期": "2020-11-10 16:26:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cc114644037e11eb47245ce329d7d1dfb08896cd", "commit_訊息": "[內部]Q00-20201104002 調整update sql", "提交日期": "2020-11-10 11:33:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.0.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.0.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.1.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.1.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.2.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.2.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.4.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.4.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.5.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.5.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.6.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.5.6.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_MSSQL.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_MSSQL_2.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_MSSQL_3.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle_2.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle_3.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.3_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.0.3_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_MSSQL.sql", "修改狀態": "重新命名", "狀態代碼": "R095"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.2.2_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/db/@base/update/5.6.2.3_\\344\\277\\256\\346\\224\\271\\350\\241\\250\\345\\226\\256\\345\\205\\203\\344\\273\\266\\351\\241\\217\\350\\211\\262\\347\\225\\260\\345\\270\\270_MSSQL.sql\"", "修改狀態": "重新命名", "狀態代碼": "R080"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/db/@base/update/5.6.2.3_\\344\\277\\256\\346\\224\\271\\350\\241\\250\\345\\226\\256\\345\\205\\203\\344\\273\\266\\351\\241\\217\\350\\211\\262\\347\\225\\260\\345\\270\\270_Oracle.sql\"", "修改狀態": "重新命名", "狀態代碼": "R076"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_useForCritical_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_useForCritical_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.5.2_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.5.2_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.5.3_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.5.3_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql", "修改狀態": "重新命名", "狀態代碼": "R084"}], "變更檔案數量": 74}, {"commit_hash": "dc1110925d35092362b37c7dcd775cf3b4947378", "commit_訊息": "[Web]Q00-20201109004 修正當鎖定工具列時，在簽核意見欄位和流程主旨欄位下方顯示的片語會被擋住", "提交日期": "2020-11-09 18:39:18", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "839943665f7e99378d5c5d2baca24b2ceaccc34c", "commit_訊息": "[表單設計師]Q00-20201109003 修正當Date元件勾選顯示時間並有和其他Date元件比較時，時間不會被比較", "提交日期": "2020-11-09 17:49:45", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/RwdDateTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "015a09e45e4135817ef04b5bbf32184b29b4c8e4", "commit_訊息": "[BPM APP]Q00-20201106002 修正行動端表單元件同時設定字體顏色和文字對齊樣式時，文字對齊樣式會失效的問題", "提交日期": "2020-11-09 11:22:49", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f12a852a6c9a5d2a075534c601cecb8bdd02bbf", "commit_訊息": "[Web]Q00-20201109002 修正流程設定關卡的表單為唯讀時，formSave後，不會再次執行formOpen", "提交日期": "2020-11-09 11:05:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "98fac441eec5de458819e86965f36ba3e018e0f5", "commit_訊息": "[Web]Q00-20201105004 修正表單自適應寬度調整時，Grid沒有重新渲染", "提交日期": "2020-11-05 17:11:32", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3e074f79a24b7c3c4a6aa5ab4af37a2b7254f7f8", "commit_訊息": "[Web]Q00-20201105002 修正流程中核決關卡沒有處理者，在跳過核決關卡後，查看流程圖會無法開啟", "提交日期": "2020-11-05 14:20:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/BlockActivityBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4dd7719eb760dffdb361579907cd092884623cd4", "commit_訊息": "[流程引擎]Q00-20201105001 修正選擇發起流程的組織單位清單中顯示已失效部門", "提交日期": "2020-11-05 11:49:30", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba6331c35d0f9bac078057577f76251cfb153ce9", "commit_訊息": "[Web]A00-20201014001 修正當Grid繫結ListBox且多選加到Grid，點選有多選的Row沒有mark繫結元件選取值", "提交日期": "2020-11-04 15:49:38", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9d45dbc6a65f2a7875789b2f7dc3cdf18bd668f", "commit_訊息": "[ISO]A00-20201103002 修正ISO絕對位置變更單，文件開窗若透過簡易查詢時會撈出申請變更中的文件", "提交日期": "2020-11-04 13:53:47", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/CreateDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "257585532b6a826d3541b83d5ef1820ed30517ea", "commit_訊息": "[ISO]Q00-20201103002 調整BCL8 轉檔錯誤時的log，以便分析錯誤原因", "提交日期": "2020-11-03 16:27:19", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4c8ec9e67a8395381d50cd2de9df3aa92751d762", "commit_訊息": "[BPM APP]C01-20201016007 修正Grid操作按鈕(新刪修)可依各個Grid設定顯示/隱藏", "提交日期": "2020-11-03 11:16:55", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fb1e45a42a019e57a7080c723e3554a6df421018", "commit_訊息": "[內部]Q00-20201102002 調整程式排版", "提交日期": "2020-11-02 17:17:44", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fbc91daa2f58de9681644aca13875f09f6d7a572", "commit_訊息": "[Web]S00-20200812003 調整多人簽核關卡含離職人員時仍持續通知管理員的問題", "提交日期": "2020-11-02 15:50:41", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d2d0285eb1986201dbd0571256d0adac236d7378", "commit_訊息": "[BPM APP]Q00-20201030005 在企業微信使用iOS手機若附件是掃描產生的PDF有時會發生模糊問題", "提交日期": "2020-10-30 18:53:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/WeChatFileCacheMap.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 17}, {"commit_hash": "ee8adf5c91cb89a763f67c90264c1be0af02832e", "commit_訊息": "[流程引擎]Q00-20201030004 修正排程AutoRegetWorkAssignmentHandler執行時出現錯誤", "提交日期": "2020-10-30 17:14:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c2e93d4322129dc9c90d8930b05fc380b94ae02b", "commit_訊息": "[內部]Q00-20201030003 調整程式排版", "提交日期": "2020-10-30 15:57:40", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dfd48f5061e1c49541fb6716a73f361644672a24", "commit_訊息": "[Web]Q00-20201030002 修正表單點擊Attachment元件，上傳附件時設定權限，套用範圍選擇關卡，無法選到核決權限關卡", "提交日期": "2020-10-30 14:36:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ActivityDefNormalListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e46d0168de252ff1ff01c7d00b5eaafd4f8453ec", "commit_訊息": "[Web]C01-20201019003 調整首頁模塊嵌入BPM代辦畫面時，表單Grid顯示異常", "提交日期": "2020-10-30 11:41:58", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8996e6530e8bc3efbab8eb957628b5c8467d7e42", "commit_訊息": "[BPM APP]C01-20201028004 修正若表單有設定文字對齊下拉選單無法點擊展開的問題", "提交日期": "2020-10-30 11:23:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}