{"company_id": "07135600", "company_name": "百利通亞陶", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\07135600_百利通亞陶\\01客戶基本資料", "files": [{"filename": "autocad.txt", "raw_content": "Dear <PERSON>,\r\n\r\n請再下載一個檔案  http://efgp.info.tm/gp/D2P.exe \r\n\r\n將此檔案放至\\jboss-4.0.5.GA\\bin 底下\r\n\r\n並重新啟動jboss\r\n\r\n \r\n\r\n若急著要測試的話\r\n\r\n請先用下面的帳號測試\r\n\r\n以下是AutoCAD轉檔的註冊畫面，註冊名稱及序號如下。\r\n\r\nYour license name is: Catherine Hou\r\n\r\nYour license code is: f8e93333fdd838fd01af3c2612", "structured_data": {"請再下載一個檔案  http": "//efgp.info.tm/gp/D2P.exe", "your license name is": "<PERSON>", "your license code is": "f8e93333fdd838fd01af3c2612"}, "source_path": "C1.客戶維護相關\\07135600_百利通亞陶\\01客戶基本資料\\autocad.txt", "file_size": 315, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:24.931374"}, {"filename": "連線資訊.txt", "raw_content": "20211110更新\r\n\r\nVPN連線資訊如下，請參考附件。\r\nVPN網址：https://twsslvpn1.diodes.com\r\nVPN帳號：CLAT_DSC211\r\nVPN密碼：Cl@t1234\r\n\r\n\r\nhttp://***********:8080/NaNaWeb\r\nadministrator / 1234\r\nFTP 帳密a root / psetw_wftest\r\n\r\n\r\nEFGP網頁：https://workflow.ecera.com.tw:8443/NaNaWeb\r\nhttp://10.40.10.60:8080/NaNaWeb/\r\nadministrator 密：u0eted;zu8n\r\n\r\n12/22\r\n若你是要直接使用putty進去，請先用oracle帳號登入，然後再『su – root』。\r\n\r\n\r\n正式AP: 密碼：u0eted;zu8n\r\nPCL : http://*************:8080/NaNaWeb  FTP: oracle/Pcl&GpWf2017   root/pid@fz@0xf\r\n\r\nPSD : http://*************:8080/NaNaWeb FTP: oracle/Psd#GpWf2017   root/a6g.1fg.1eu1\r\n\r\nPSH : http://*************:8080/NaNaWeb FTP: oracle/Psh!GpWf2017   root/vu,4a/4fu6\r\n\r\nPCA : http://*************:8080/NaNaWeb FTP: oracle/Pca@GpWf2017   root/h@r;pmee.kom,\r\n\r\n \r\n\r\n測試AP：密碼：1234\r\nhttp://192.168.10.163:8080/NaNaWeb/  FTP: oracle/pcl!gpwf2017   root/psetw_wftest\r\n\r\n\r\n\r\n\r\n\r\nVPN\r\nhttps://web.saronix-ecera.com.tw/special/ \r\nhttps://mobile.saronix-ecera.com.tw/special/\r\nVPN帳號：dsc\r\nVPN密碼：1234@sre\r\n\r\n\r\nPSETW(台灣):   *************\r\nPSESD(山東):   *************\r\nPericom(美國): *************\r\nPTI(上海):     *************\r\n\r\nSITE  USER_NAME 2015/07/05\r\n----- --------- -------------------\r\nPSETW root      pidfz0xf\r\nPSESD root      pid5/dq5p8\r\nPTI   root      pidx9ex5ab. \r\nPSC   root      pidsl5tm1\r\n\r\nVNC Level  \r\nSite         Password\r\n-----       ----------\r\nPSETW       r.nnem.cdj\r\nPSESD       r.nnem.cdj\r\nPTI         r.nnem.cdj\r\nPSCSJ       x94t/6m,4\r\n\r\n\r\nDB帳號：EFGP1\r\nDB密碼：pericom\r\n\r\n測試區\r\nftp的帳號：root\r\nftp的密碼：psetw_wftest\r\n\r\nhttps://workflow.ecera.com.tw:8443/NaNaWeb/\r\nhttp://*************:8080/NaNaWeb/\r\n密碼:88vpifo.o1\r\n\r\n\r\n測試區好了\r\nhttp://**************:8080/NaNaWeb/\r\nadministrator/1234\r\n\r\nAP: **************:1/psetw_wftest\r\n\r\nDB: EFGP1/4518888\r\nWFTEST2 =\r\n  (DESCRIPTION =\r\n    (ADDRESS = (PROTOCOL = TCP)(HOST = **************)(PORT = 1521))\r\n    (CONNECT_DATA = (SID = WFDB))\r\n  )\r\n\r\n\r\nDB帳號：EFGP1\r\nDB密碼：sre4518888\r\n\r\n\r\nFTP:\r\n帳號：root\r\n密碼：u0eted;zu8n\r\n\r\nLog路徑: \r\n/opt/jboss-4.0.5.GA/NaNa/log/\r\n\r\nFTP與AP的密碼相同\r\nAP:\r\nhttp://*************:8080/NaNaWeb\r\n\r\n\r\n附上10.70的資訊~\r\nAP：\r\nhttp://*************:8080/NaNaWeb/\r\n\r\nFTP：\r\n帳號：root\r\n密碼：h/dtbg/rf\r\n--------------------------------------------------------------------\r\n\r\n測試區好了\r\nhttp://**************:8080/NaNaWeb/\r\nadministrator/1234\r\n\r\nAP: **************:1/psetw_wftest\r\n\r\nDB: EFGP1/4518888\r\nWFTEST2 =\r\n  (DESCRIPTION =\r\n    (ADDRESS = (PROTOCOL = TCP)(HOST = **************)(PORT = 1521))\r\n    (CONNECT_DATA = (SID = WFDB))\r\n  )\r\n", "structured_data": {"vpn網址": "https://twsslvpn1.diodes.com", "vpn帳號": "dsc", "vpn密碼": "1234@sre", "efgp網頁": "https://workflow.ecera.com.tw:8443/NaNaWeb", "administrator 密": "u0eted;zu8n", "正式ap: 密碼": "u0eted;zu8n", "測試ap": "密碼：1234", "db帳號": "EFGP1", "db密碼": "sre4518888", "ftp的帳號": "root", "ftp的密碼": "psetw_wftest", "username": "root", "password": "88vpifo.o1", "vpn網址：https": "//twsslvpn1.diodes.com", "http": "//**************:8080/NaNaWeb/", "efgp網頁：https": "//workflow.ecera.com.tw:8443/NaNaWeb", "正式ap": "密碼：u0eted;zu8n", "pcl": "http://*************:8080/NaNaWeb  FTP: oracle/Pcl&GpWf2017   root/pid@fz@0xf", "psd": "http://*************:8080/NaNaWeb FTP: oracle/Psd#GpWf2017   root/a6g.1fg.1eu1", "psh": "http://*************:8080/NaNaWeb FTP: oracle/Psh!GpWf2017   root/vu,4a/4fu6", "pca": "http://*************:8080/NaNaWeb FTP: oracle/Pca@GpWf2017   root/h@r;pmee.kom,", "https": "//workflow.ecera.com.tw:8443/NaNaWeb/", "psetw(台灣)": "*************", "psesd(山東)": "*************", "pericom(美國)": "*************", "pti(上海)": "*************", "ap": "**************:1/psetw_wftest", "database": "EFGP1/4518888", "(address": "(PROTOCOL = TCP)(HOST = **************)(PORT = 1521))", "(connect_data": "(SID = WFDB))", "host": "***********"}, "source_path": "C1.客戶維護相關\\07135600_百利通亞陶\\01客戶基本資料\\連線資訊.txt", "file_size": 2591, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:24.968980"}], "total_files": 2, "processed_at": "2025-08-26T10:46:24.968989"}