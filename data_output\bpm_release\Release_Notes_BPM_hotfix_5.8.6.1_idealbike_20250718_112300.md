# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_*******_idealbike
- **舊版本**: release_*******
- **生成時間**: 2025-07-18 11:23:00
- **新增 Commit 數量**: 11

## 變更摘要

### lorenchang (3 commits)

- **2022-06-26 22:15:41**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
  - 變更檔案: 25 個
- **2022-07-18 10:32:24**: [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)[補]
  - 變更檔案: 2 個
- **2022-07-06 17:40:48**: [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
  - 變更檔案: 21 個

### walter_wu (3 commits)

- **2021-07-27 16:50:00**: [Web]A00-20210727001 修正XPDL的流程在監控流程中跳過關卡時，驗證密碼視窗會一片空白
  - 變更檔案: 1 個
- **2021-05-21 16:00:58**: [Web]A00-20210521001 修正撤銷流程與重啟服務驗證密碼的視窗一片空白
  - 變更檔案: 1 個
- **2021-06-04 16:42:53**: [ESS]A00-20210521002 修正ESS刪除EFGP缺席紀錄的session bean無作用
  - 變更檔案: 3 個

### 林致帆 (3 commits)

- **2021-12-08 17:40:19**: [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤[補修正]
  - 變更檔案: 2 個
- **2021-12-02 16:41:02**: [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤
  - 變更檔案: 3 個
- **2021-12-02 16:41:02**: [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤
  - 變更檔案: 3 個

### kmin (2 commits)

- **2022-08-23 10:47:44**: Revert "[Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤"
  - 變更檔案: 3 個
- **2022-08-17 14:28:19**: [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)[補]
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
- **Commit ID**: `aa86adeec05c22d10f647387eef8b706dc8e9ce8`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:15:41
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [Web]A00-20210727001 修正XPDL的流程在監控流程中跳過關卡時，驗證密碼視窗會一片空白
- **Commit ID**: `c832d4b235a8187e894da3e39ff0748a304b31f6`
- **作者**: walter_wu
- **日期**: 2021-07-27 16:50:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordForByPass.jsp`

### 3. [Web]A00-20210521001 修正撤銷流程與重啟服務驗證密碼的視窗一片空白
- **Commit ID**: `057dd2519038b4ceac0a97805a857380e31ec118`
- **作者**: walter_wu
- **日期**: 2021-05-21 16:00:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`

### 4. [ESS]A00-20210521002 修正ESS刪除EFGP缺席紀錄的session bean無作用
- **Commit ID**: `f2e11ec4df99730f976d159e6cdc09ae79544394`
- **作者**: walter_wu
- **日期**: 2021-06-04 16:42:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 5. [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤[補修正]
- **Commit ID**: `f098e80afdf3e0bf7c253b37df721d84ea5bdfc7`
- **作者**: 林致帆
- **日期**: 2021-12-08 17:40:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`

### 6. [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤
- **Commit ID**: `22fced06af0e6cd7565eb9aaa0988cecf46c37d6`
- **作者**: 林致帆
- **日期**: 2021-12-02 16:41:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 7. Revert "[Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤"
- **Commit ID**: `1966786570c23bb60b44b59131a07bea50a54ae0`
- **作者**: kmin
- **日期**: 2022-08-23 10:47:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 8. [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤
- **Commit ID**: `43cf1410591ac76caa7061589143c035170ea5f6`
- **作者**: 林致帆
- **日期**: 2021-12-02 16:41:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 9. [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)[補]
- **Commit ID**: `c3b469e2f538a8e423a18fb0132fd07e0af19ffe`
- **作者**: kmin
- **日期**: 2022-08-17 14:28:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 10. [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)[補]
- **Commit ID**: `029e750db234d413d6bc99ea76b26fef2b6df58c`
- **作者**: lorenchang
- **日期**: 2022-07-18 10:32:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_Oracle.sql`

### 11. [ESS]S00-20220701002 ESS整合integration.ess.host設為SameSite時，將自動產生同源網址(需搭配Nginx等反向代理系統)
- **Commit ID**: `0a5463ca8796a6b88317444a6a23d39fceac7ef0`
- **作者**: lorenchang
- **日期**: 2022-07-06 17:40:48
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AbsSSOHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DML_Oracle.sql`

