{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_8.1.1.2", "date": "2025-06-30 17:58:53", "message": "[智能表單設計助手] 新增多语系", "author": "周权"}, "舊分支": {"branch_name": "release_8.1.1.1", "date": "2025-04-01 11:58:26", "message": "[SQL]8.1.1.1更新(-59_ininDB.patch)", "author": "davidhr-2997"}, "比較時間": "2025-07-18 11:23:53", "新增commit數量": 90, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "533e7359847701a8eb995c2641ebf07d0652b30c", "commit_訊息": "[智能表單設計助手] 新增多语系", "提交日期": "2025-06-30 17:58:53", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cad0db9abd7567e079b95b775fa236b96a1754b7", "commit_訊息": "[patch更新]8.1.1.2", "提交日期": "2025-06-30 15:56:05", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee7ba6aeed26d8d91fa164eec4905aa560845d39", "commit_訊息": "[智能表單設計助手]更新Prompt", "提交日期": "2025-06-27 17:36:50", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4d6b562056a1288704f9b92e8051afd06c6246f2", "commit_訊息": "[Web]C01-20250623005 新增ExtOrgAccessor取得使用者所屬組織單元方法可取到members", "提交日期": "2025-06-27 17:06:34", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/OrgUnitVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/UserVo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "28f92a13405fc4d437b21118ee87ae6d66a2134a", "commit_訊息": "[T100]C01-20250521001 調整回写T100签核历程ApproveLogGet，偶发会少最后一个签核人历程[補]", "提交日期": "2025-06-25 15:58:05", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "91e982d3f45e2c33e0eaf22c71438ca58ff1bc17", "commit_訊息": "[T100]C01-20250521001 調整回写T100签核历程ApproveLogGet，偶发会少最后一个签核人历程", "提交日期": "2025-06-25 10:35:39", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ff99c333cd2bab458f3851a3c9dc406e6b6b754a", "commit_訊息": "[智能表單設計助手]新增解析成功已扣點但生成失敗的補償機制", "提交日期": "2025-06-25 08:51:52", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/FormDesignAssistant.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryOperator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "cba7cc3477a7b5b0dc8d862140dc9873fb5108d8", "commit_訊息": "[智能表單設計助手]數智員工畫面啓用連結調整", "提交日期": "2025-06-23 15:59:46", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "072b640ccd397700a7b6ee564150da001bb68e32", "commit_訊息": "[MPT]C01-20250623001 修正公告申請單中選擇發布範圍時若名稱過長時無法儲存問題", "提交日期": "2025-06-23 15:48:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "f83663e30d2922c9881a60098c4338cafab50858", "commit_訊息": "[patch更新] BPM8112版patch更新", "提交日期": "2025-06-23 11:10:13", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22a4c75dab5d9ef29299997b370e5670784fc499", "commit_訊息": "[PRODT]A00-20250620001 修正Web流程管理工具中服務關卡的網路服務沒有填寫名稱時無法儲存問題", "提交日期": "2025-06-20 17:03:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "80e982b38f59e0f59b7786b29ef9b26e09644b88", "commit_訊息": "[資安]Q00-20250612002 原碼掃描安全熱點:Weak Cryptography ，針對程式內容:Math.random()(補)", "提交日期": "2025-06-19 14:44:03", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/raphael.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34b7f5bba230e5d1f87d7cc5537d77f5eb241309", "commit_訊息": "[智能表單設計助手]新增數智員工參數設定畫面多語系", "提交日期": "2025-06-18 17:57:48", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "31c9270917994a485a25d519b2f8c36df6070cbd", "commit_訊息": "[資安]Q00-20250618001 SonarQube安全性議題修正：Specify a target origin for this message", "提交日期": "2025-06-18 17:07:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "afc8a8b1a1368982d4d55f75e3bccfd89eab97ab", "commit_訊息": "[智能表單設計助手]新增限制上傳檔案大小功能[補]", "提交日期": "2025-06-18 16:52:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a167dcb2e14cf6ce95ae2762fd7c4afa52c028b4", "commit_訊息": "[智能表單設計助手]配合驗證更新、修正SonarQube掃出之加解密安全性議題", "提交日期": "2025-06-18 15:58:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/CryptoUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3f009947e6b83febb466cc7bc49c5bd714886a28", "commit_訊息": "[智能表單設計助手]新增数智員工參數設定頁", "提交日期": "2025-06-18 13:55:18", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "784352701fa3bff517de16ec17162abc039fa9a5", "commit_訊息": "[智能表單設計助手]初版(補)", "提交日期": "2025-06-17 13:52:52", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8_AIFormDesign.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL_AIFormDesign.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle_AIFormDesign.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_DM8_AIFormDesign.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_MSSQL_AIFormDesign.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_Oracle_AIFormDesign.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8_AIFormDesign.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL_AIFormDesign.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle_AIFormDesign.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "8bd3dec47d27f4f5305bde0413f1b495c83ce1f5", "commit_訊息": "[Web]C01-20250613004 修正Radio有勾選「最後一個選項額外產生輸入框」用FormUtil.setValue設定不了值問題", "提交日期": "2025-06-16 17:34:58", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e52000d029bc12687c0871f05827826a268a6b4c", "commit_訊息": "[智能表單設計助手]初版", "提交日期": "2025-05-22 17:17:22", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFilePropertiesDao.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/FormDesignAssistantDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/FormDesignAssistantDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/FormDesignAssistant.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/request/PlatformApiRequest.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_DM8_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_MSSQL_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DDL_Oracle_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle_AIFormDesign.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 16}, {"commit_hash": "0fd5fd9e0919c4372ffc71e7f3bcdaa5c5eab02d", "commit_訊息": "[資安]Q00-20250612002 原碼掃描安全熱點:Weak Cryptography ，針對程式內容:Math.random()", "提交日期": "2025-06-13 11:52:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/test.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v1/system/utab.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/utab.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/utab.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3_BPMcustomized.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.18.3_BPMcustomized.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/dojo/src/collections/SkipList.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/dojo/src/io/RepubsubIO.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/dojo/src/io/cometd.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/dojo/src/math.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/dojo/src/uuid/LightweightGenerator.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/dojo/src/uuid/TimeBasedGenerator.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/dojo/src/widget/TreeDemo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jGrid/jgrid.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/materialize/materialize.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/raphael.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/spectrum/spectrum.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/utab.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/windows_js_1.3/documentation/javascripts/effects.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/windows_js_1.3/documentation/javascripts/window.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/windows_js_1.3/javascripts/effects.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/windows_js_1.3/javascripts/window.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 23}, {"commit_hash": "5e86830af48303952e172baaa2134aa4eb4985e7", "commit_訊息": "[MPT]S00-20250402001 首頁模組增加越南語系內容[補]", "提交日期": "2025-06-13 11:44:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a855c28e9c0298c4ef9c2d07567fe4cef2aaf6a0", "commit_訊息": "[費用報銷]C01-20250611005 T100 與費用報銷整合，會出現本幣分攤金額不相同的錯誤,表單更新(EPM_ExpenseForm、EPM_VendorRequestForm)", "提交日期": "2025-06-12 16:34:25", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/EPM_ExpenseForm.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/EPM_VendorRequestForm.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5bb725dd23997107c1380c5c6f3a5e925367d8ce", "commit_訊息": "[流程設計師]C01-20250611002 修正舊版流程設計師開啟表單存取控管頁面時發生異常問題", "提交日期": "2025-06-12 15:49:00", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessTableHeader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessCellEditorRenderer.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessCellEditorRenderer_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessCellEditorRenderer_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessCellEditorRenderer_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "3c0e5e3e85c7eab9cc76078b4d261833164d9528", "commit_訊息": "[MPT]S00-20250402001 首頁模組增加越南語系內容[補]", "提交日期": "2025-06-12 11:34:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "aeec72b7365838a3c20fbd25004b450824262220", "commit_訊息": "[BPM APP]C01-20250609005 修正IMG報表分析中，當無法找到最新生效版本流程時畫面出現空白的問題。", "提交日期": "2025-06-10 17:01:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c385c6c413dabcfec021745842999f4e61b39e2", "commit_訊息": "[Workflow] WF工程包RWD表單36張", "提交日期": "2025-06-10 16:02:24", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/ASTI08.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/ASTI09.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/ASTI12.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/ASTI13.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/ASTI14.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI02.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI03.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI06.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI07.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI11.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI15.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI16.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI17.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI19.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI20.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI21.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI22.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI29.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI30.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI34.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI35.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI36.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI37.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI43.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI52.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI54.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI55.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI56.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI57.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI58.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI62.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/EPMI63.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/NOTI02.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlow/PURI05.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlowERP/EPMI45.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@workflow/form-default/WF\\345\\267\\245\\347\\250\\213\\345\\214\\205RWD\\350\\241\\250\\345\\226\\256/WorkFlowERP/EPMI64.form\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 36}, {"commit_hash": "e7e92185e9bf4072828292a96d571a1eae355564", "commit_訊息": "[資安]C01-20250520005 更新completeVerifyInternal()的位置[補]", "提交日期": "2025-06-10 11:55:32", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordForByPass.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "329c154967cd5267601ff10815b4747aa3dc5ff4", "commit_訊息": "[表單設計師]C01-20250528002 修正絕對位置表單轉RWD後，標籤取消隱藏後輸入文字不顯示的問題", "提交日期": "2025-06-06 14:31:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0e055d55f37942cc7350424908015bfc105a553b", "commit_訊息": "[流程串接套件]C01-20250602005 修正調整後置流程發起部門的前置表單欄位元件位置", "提交日期": "2025-06-05 11:11:59", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0c808c6c3c2ba9fa6d049777d47ca7c520970a86", "commit_訊息": "[BPM APP]C01-20250603001 修正企業微信在綁定使用者時微信帳號重複的提示文字錯誤問題", "提交日期": "2025-06-04 09:45:56", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9f319fe6d5e739abd86b65f76f6b5cb8595cd19b", "commit_訊息": "[Web]C01-20250320008 修正無論是否已儲存表單皆往後加簽(addPostCustomActivityAlways)的接口未在dwr-default.xml定義導致加簽失敗的異常", "提交日期": "2025-06-03 15:43:33", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70c6651b8bdd901887571fd3a28575b919c1ec03", "commit_訊息": "[Web]C01-20250320008 修正Ajax加簽異常時不會顯示原始異常", "提交日期": "2025-05-23 16:48:49", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "173cceee2b20e3e3e752727241b0f684c2ba820f", "commit_訊息": "[Web]C01-20250523007 修正RWD表單Grid元件使用凍結欄位功能時選中後背景色異常問題", "提交日期": "2025-05-28 16:13:44", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.16.0.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.18.3_BPMcustomized.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9552e850246574d805c9895a38d5e1ee3579f91a", "commit_訊息": "[Web]C01-20250527001 修正设置密码策略后，变更密码画面显示不出来的问题", "提交日期": "2025-05-28 14:10:33", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "46bce58261e3e2a81c25b182d1ce9f4f5ee6ec16", "commit_訊息": "[ESS]C01-20250514003 加入Log及JDBC連接已關閉之重取機制，避免出現無法發起ESS流程的異常", "提交日期": "2025-05-26 14:27:21", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/JDBCReadingHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ee38aea9c7a29fa06b8c539aee1a1835ab8fb113", "commit_訊息": "[資安]C01-20250520005 漏洞一:一般使用者可呼叫出管理者功能\"跳過\",透過開發者工具執行前端函式bypassActivity()。漏洞二：密碼驗證後執行的管理者功能也在前端，透過開發者工具執行前端函式completeVerify()跳過驗證。", "提交日期": "2025-05-22 15:38:39", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordForByPass.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "14a2a29c34fe68c3eaffa07560a10067456dd290", "commit_訊息": "[Web]C01-20250320008 補Ajax Service說明：無論是否已儲存表單皆往後加簽(addPostCustomActivityAlways)", "提交日期": "2025-05-22 11:17:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2d1177c5f10645e395eac89c513ea1a469bfdcc7", "commit_訊息": "[T100]組織同步新增SYN_ExtUser異常訊息優化", "提交日期": "2025-05-21 17:37:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8875876e7de210d3fc929c53885ee75fc4278487", "commit_訊息": "[Web]C01-20250519003 E10签核历程url增加权限控制", "提交日期": "2025-05-21 16:38:20", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0be98f86eff9b534420cd97162150c57bc66bd56", "commit_訊息": "[資安]Q00-20250519001 防止SQLInjectionURL攻擊 uerId有sql语法", "提交日期": "2022-03-11 16:49:37", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa90b52df3c3bb313b03018fc52b0159e1ab00dd", "commit_訊息": "[MPT]S00-20250402001 首頁模組增加越南語系內容", "提交日期": "2025-05-16 10:13:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DDL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/MPT_8.1.1.2_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "9bfb58a3b377d237244ab5429695685c4bd48c5d", "commit_訊息": "[Web]Q00-20250515001 增加OpenStack 平台的VM判斷", "提交日期": "2025-05-15 14:40:13", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/GuardServiceUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3c97cc23b9c3496d3bf1cd9bb0ed5c0ae47671c", "commit_訊息": "[Web]C01-20250514002 優化檢測「偵測到網路連線異常，請確認您的網路是否正常運作」訊息", "提交日期": "2025-05-14 16:18:05", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e371d04c8379b962d7eca7e8a6b48228b1de1a4c", "commit_訊息": "[SQL註冊器]S00-20250514001 SQL註冊器新增查詢SQL(登入失敗查詢、未啟用雙因素認證數量跟資料、密碼超時未修改數量跟資料)", "提交日期": "2025-05-14 16:31:33", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b2791be9856772b34f8d3bc6c511f3e285726f77", "commit_訊息": "[Web]C01-20250508004 調整個人首頁-主管 逾期工作事項顯示數量不對的問題", "提交日期": "2025-05-14 08:56:41", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6b29a179ba914e4d0d2905f7faa635f3387281ac", "commit_訊息": "[流程引擎]C01-20250429006 修正單身中繫結 Checkbox 或 RadioButton 的欄位，若透過非標準方式（如 Excel 匯入）產生表單資料後再編輯第 2 筆以後的內容時，因欄位不存在導致轉存表單失敗的問題(補)", "提交日期": "2025-05-12 15:39:44", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d3a8cd601d20190f53e80555544b7f1fac1c6fd", "commit_訊息": "[Web]C01-20250509003 調整測試發信無法正常接收到新建的問題", "提交日期": "2025-05-12 15:24:26", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5d40e93ff98273b15a243fcbaf0d4d643e4c48b0", "commit_訊息": "[Web]C01-20250512002 调整绝对位置发单会显示红线外栏位的问题", "提交日期": "2025-05-12 15:13:45", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a005f44f264d9e67cb1533167480406a2782d1b5", "commit_訊息": "[Web]C01-20250502004 修正一般使用者用模擬使用者時某些作業會出現無權限問題", "提交日期": "2025-05-09 16:46:22", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b08b7b42ea8d24afc10cb0cdef15b8f24c14ab5a", "commit_訊息": "[流程引擎]Q00-20250509001 優化啟動下一關為核決關卡失敗訊息不明確", "提交日期": "2025-05-09 14:43:57", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "db1010d1a1c613ee59e8b4ba6df2083275838bba", "commit_訊息": "[Web]C01-20250508005 調整B2B文件管理模组/PDF浮水印属性管理作业授權卡控", "提交日期": "2025-05-09 10:44:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7bb84188eea428d18069e61964fdf3a8d7503ada", "commit_訊息": "[BPM APP]C01-20250508002 調整企業微信與釘釘整合時追蹤流程列表的預設時間區間改系統變數控制", "提交日期": "2025-05-09 10:16:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb82e68f01c4270c476f19a3da1caf620d0d9331", "commit_訊息": "[行業表單庫]新增範例表單檔案", "提交日期": "2025-05-07 17:18:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@base/form-repository/SO2\\346\\252\\242\\351\\251\\227\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\344\\270\\215\\345\\220\\210\\346\\240\\274\\345\\223\\201\\345\\240\\261\\345\\273\\242\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\344\\272\\213\\346\\225\\205\\345\\240\\261\\345\\221\\212\\346\\234\\203\\350\\276\\246\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\345\\223\\201\\350\\263\\252\\347\\225\\260\\345\\270\\270\\347\\265\\261\\350\\250\\210\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\345\\267\\245\\345\\273\\240\\350\\251\\225\\346\\240\\270\\347\\237\\257\\346\\255\\243\\346\\216\\252\\346\\226\\275\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\345\\267\\245\\345\\273\\240\\350\\251\\225\\346\\240\\270\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\205\\245\\345\\272\\253\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\206\\267\\346\\260\\243\\346\\251\\237\\346\\277\\276\\347\\266\\262\\346\\270\\205\\346\\264\\227\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\206\\267\\350\\227\\217(\\345\\207\\215)\\345\\272\\253\\346\\272\\253\\345\\272\\246\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\206\\267\\350\\227\\217(\\345\\207\\215)\\345\\272\\253\\350\\255\\246\\345\\240\\261\\347\\263\\273\\347\\265\\261\\346\\270\\254\\350\\251\\246\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\216\\237\\347\\211\\251\\346\\226\\231\\345\\223\\201\\350\\263\\252\\350\\246\\217\\346\\240\\274\\346\\233\\270.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\216\\237\\347\\211\\251\\346\\226\\231\\347\\225\\260\\345\\270\\270-\\345\\247\\224\\345\\244\\226\\346\\220\\215\\345\\244\\261\\350\\262\\254\\344\\273\\273\\345\\210\\244\\345\\256\\232\\346\\233\\270.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\216\\237\\347\\211\\251\\346\\226\\231\\351\\251\\227\\346\\224\\266\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\217\\257\\351\\235\\240\\345\\272\\246\\345\\257\\246\\351\\251\\227\\345\\210\\206\\346\\236\\220\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\217\\257\\351\\235\\240\\345\\272\\246\\350\\251\\225\\345\\203\\271\\347\\265\\220\\346\\236\\234\\345\\240\\261\\345\\221\\212\\346\\233\\270.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\217\\257\\351\\235\\240\\345\\272\\246\\350\\251\\246\\351\\251\\227\\346\\270\\205\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\220\\212\\346\\216\\233\\344\\275\\234\\346\\245\\255\\350\\250\\261\\345\\217\\257\\350\\255\\211.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\223\\201\\346\\272\\253\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\223\\201\\350\\263\\252\\346\\216\\247\\345\\210\\266\\345\\210\\244\\351\\200\\200\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\223\\201\\350\\263\\252\\347\\225\\260\\345\\270\\270\\350\\231\\225\\347\\220\\206\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\223\\201\\350\\263\\252\\347\\225\\260\\345\\270\\270\\351\\200\\232\\347\\237\\245\\345\\226\\256(\\345\\223\\201\\344\\277\\235\\347\\250\\213\\345\\272\\217).formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\233\\272\\345\\256\\232\\345\\274\\217\\350\\265\\267\\351\\207\\215\\346\\251\\237\\344\\275\\234\\346\\245\\255\\345\\211\\215\\346\\252\\242\\346\\237\\245\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\247\\224\\345\\244\\226\\345\\212\\240\\345\\267\\245\\345\\205\\245\\345\\272\\253\\351\\251\\227\\346\\224\\266\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\247\\224\\350\\250\\227\\346\\270\\254\\350\\251\\246\\345\\210\\206\\346\\236\\220\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\256\\242\\346\\210\\266\\346\\273\\277\\346\\204\\217\\345\\272\\246\\350\\252\\277\\346\\237\\245\\347\\265\\220\\346\\236\\234\\346\\224\\271\\345\\226\\204\\345\\240\\261\\345\\221\\212.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\256\\242\\350\\250\\264\\346\\224\\271\\345\\226\\204\\346\\216\\252\\346\\226\\275\\345\\240\\261\\345\\221\\212.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\267\\245\\344\\275\\234\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\267\\245\\345\\205\\267\\351\\233\\266\\344\\273\\266\\344\\277\\256\\347\\271\\225\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\271\\264\\345\\272\\246\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\251\\225\\346\\240\\270\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\345\\273\\240\\345\\225\\206(\\344\\276\\233\\346\\207\\211\\345\\225\\206)\\347\\225\\260\\345\\270\\270\\345\\240\\261\\345\\221\\212\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\210\\220\\345\\223\\201\\345\\206\\267\\345\\215\\273\\346\\272\\253\\345\\272\\246\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\210\\220\\345\\223\\201\\345\\223\\201\\350\\263\\252\\350\\246\\217\\346\\240\\274\\346\\233\\270.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\210\\220\\345\\223\\201\\346\\252\\242\\351\\251\\227\\347\\264\\200\\351\\214\\204\\347\\265\\261\\350\\250\\210\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\210\\220\\345\\223\\201\\346\\252\\242\\351\\251\\227\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\210\\220\\345\\223\\201\\347\\256\\241\\345\\210\\266\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\210\\220\\345\\223\\201\\351\\205\\215\\351\\200\\201\\346\\272\\253\\345\\272\\246\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\211\\277\\346\\224\\254\\345\\225\\206\\345\\212\\240\\345\\205\\245\\345\\215\\224\\350\\255\\260\\347\\265\\204\\347\\271\\224\\347\\224\\263\\350\\253\\213\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\211\\277\\346\\224\\254\\345\\225\\206\\346\\226\\275\\345\\267\\245\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\211\\277\\346\\224\\254\\345\\225\\206\\351\\200\\262\\345\\273\\240\\346\\226\\275\\345\\267\\245\\344\\272\\272\\345\\223\\241\\345\\220\\215\\345\\206\\212.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\211\\277\\346\\224\\254\\345\\225\\206\\351\\201\\225\\350\\246\\217\\345\\221\\212\\347\\231\\274\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\214\\201\\347\\272\\214\\346\\224\\271\\345\\226\\204\\346\\217\\220\\346\\241\\210\\350\\250\\210\\347\\225\\253\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\216\\241\\350\\263\\274\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\217\\220\\346\\241\\210\\347\\265\\220\\346\\241\\210\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\217\\220\\347\\253\\213\\346\\241\\210\\347\\215\\216\\345\\213\\265\\351\\207\\221\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\226\\260\\344\\276\\233\\346\\207\\211\\345\\225\\206\\345\\257\\251\\346\\237\\245\\350\\251\\225\\346\\240\\270\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\227\\245\\346\\234\\237\\350\\223\\213\\345\\215\\260\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\251\\237\\346\\242\\260\\350\\250\\255\\345\\202\\231\\345\\241\\227\\346\\212\\271\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\251\\237\\346\\242\\260\\350\\250\\255\\345\\202\\231\\345\\241\\227\\346\\212\\271\\350\\274\\252\\345\\200\\274\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\251\\237\\346\\242\\260\\350\\250\\255\\345\\202\\231\\346\\270\\205\\346\\264\\227\\346\\266\\210\\346\\257\\222\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\251\\237\\346\\242\\260\\350\\250\\255\\345\\202\\231\\347\\266\\255\\344\\277\\256\\351\\200\\262\\345\\272\\246\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\251\\237\\346\\242\\260\\350\\250\\255\\345\\202\\231\\350\\207\\252\\344\\270\\273\\346\\252\\242\\346\\240\\270\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\256\\272\\350\\217\\214\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\260\\264\\350\\263\\252\\345\\276\\256\\347\\224\\237\\347\\211\\251\\346\\252\\242\\351\\251\\227\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\261\\241\\346\\260\\264\\346\\265\\201\\351\\207\\217\\347\\256\\241\\345\\210\\266\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\261\\241\\346\\260\\264\\347\\263\\273\\347\\265\\261\\350\\241\\233\\347\\224\\237\\347\\256\\241\\347\\220\\206\\346\\227\\245\\350\\252\\214.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\263\\225\\350\\246\\217\\345\\257\\251\\346\\237\\245\\346\\270\\205\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\266\\210\\351\\230\\262\\350\\250\\255\\346\\226\\275(\\347\\263\\273\\347\\265\\261)\\344\\270\\255\\346\\226\\267\\350\\250\\261\\345\\217\\257\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\267\\273\\345\\212\\240\\347\\211\\251\\346\\212\\225\\346\\226\\231\\347\\242\\272\\350\\252\\215\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\270\\205\\346\\275\\224\\346\\266\\210\\346\\257\\222\\347\\224\\250\\345\\223\\201\\351\\240\\230\\347\\224\\250\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\272\\253\\346\\277\\225\\345\\272\\246\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\272\\253\\346\\277\\225\\345\\272\\246\\350\\250\\230\\351\\214\\204\\350\\241\\250(\\351\\243\\237\\345\\223\\201\\345\\256\\211\\345\\205\\250\\347\\256\\241\\347\\220\\206\\347\\263\\273\\347\\265\\261).formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\346\\277\\276\\345\\277\\203\\346\\233\\264\\346\\217\\233\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\222\\260\\345\\242\\203\\350\\210\\207\\350\\201\\267\\346\\245\\255\\345\\256\\211\\345\\205\\250\\350\\241\\233\\347\\224\\237\\347\\256\\241\\347\\220\\206\\346\\226\\271\\346\\241\\210\\347\\265\\220\\346\\241\\210\\345\\240\\261\\345\\221\\212\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\222\\260\\345\\242\\203\\350\\210\\207\\350\\201\\267\\346\\245\\255\\345\\256\\211\\345\\205\\250\\350\\241\\233\\347\\224\\237\\347\\256\\241\\347\\220\\206\\346\\226\\271\\346\\241\\210\\350\\246\\217\\345\\212\\203\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\224\\237\\347\\224\\242\\346\\223\\215\\344\\275\\234\\346\\252\\242\\346\\237\\245\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\224\\242\\345\\223\\201\\345\\204\\200\\345\\231\\250\\345\\205\\247\\346\\240\\241\\345\\240\\261\\345\\221\\212\\346\\233\\270.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\224\\242\\345\\223\\201\\345\\204\\200\\345\\231\\250\\346\\240\\241\\346\\255\\243\\351\\200\\232\\347\\237\\245\\345\\217\\212\\345\\273\\266\\346\\234\\237\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\224\\242\\345\\223\\201\\345\\204\\200\\345\\231\\250\\346\\240\\241\\351\\251\\227\\345\\271\\264\\345\\272\\246\\350\\250\\210\\347\\225\\253\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\224\\242\\345\\223\\201\\345\\204\\200\\345\\231\\250\\346\\240\\241\\351\\251\\227\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\224\\242\\345\\223\\201\\345\\204\\200\\345\\231\\250\\347\\225\\260\\345\\270\\270\\351\\200\\232\\347\\237\\245\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\224\\250\\345\\215\\260(\\345\\200\\237\\345\\207\\272)\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\225\\260\\347\\211\\251\\345\\210\\206\\346\\236\\220\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\250\\275\\346\\240\\270\\346\\237\\245\\346\\252\\242\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\250\\275\\346\\240\\270\\347\\237\\257\\346\\255\\243\\346\\216\\252\\346\\226\\275\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\266\\255\\344\\277\\256\\347\\224\\263\\350\\253\\213\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\347\\274\\272\\346\\260\\247(\\345\\220\\253\\345\\257\\206\\351\\226\\211)\\347\\251\\272\\351\\226\\223\\344\\275\\234\\346\\245\\255\\350\\250\\261\\345\\217\\257\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\201\\267\\346\\245\\255\\345\\256\\211\\345\\205\\250\\350\\241\\233\\347\\224\\237\\347\\233\\256\\346\\250\\231\\346\\250\\231\\347\\232\\204\\351\\221\\221\\345\\210\\245\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\243\\275\\347\\250\\213\\347\\256\\241\\345\\210\\266\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\243\\275\\347\\250\\213\\350\\241\\233\\347\\224\\237\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\250\\255\\345\\202\\231\\346\\227\\245\\345\\270\\270\\344\\277\\235\\351\\244\\212\\345\\215\\241.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\252\\277\\346\\237\\245\\345\\240\\261\\345\\221\\212\\345\\217\\212\\345\\206\\215\\347\\231\\274\\351\\230\\262\\346\\255\\242\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\253\\213\\351\\240\\230\\346\\226\\231\\351\\233\\231\\351\\207\\215\\347\\242\\272\\350\\252\\215\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\350\\273\\237\\346\\260\\264\\346\\252\\242\\346\\270\\254\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\200\\200\\350\\262\\250\\350\\231\\225\\347\\220\\206\\345\\226\\256.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\200\\262\\345\\207\\272\\345\\272\\253\\345\\255\\230\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\205\\222\\347\\262\\276\\346\\234\\211\\346\\225\\210\\346\\277\\203\\345\\272\\246\\347\\233\\243\\346\\216\\247\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\207\\215\\345\\212\\240\\345\\267\\245\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\207\\221\\346\\252\\242\\346\\251\\237\\346\\270\\254\\350\\251\\246\\350\\250\\230\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\240\\230\\347\\224\\250\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\243\\237\\345\\223\\201\\345\\256\\211\\345\\205\\250\\351\\230\\262\\350\\255\\267\\350\\251\\225\\344\\274\\260\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\243\\262\\346\\260\\264\\346\\251\\237\\346\\277\\276\\345\\277\\203\\346\\233\\264\\346\\217\\233\\347\\264\\200\\351\\214\\204\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@base/form-repository/\\351\\253\\230\\346\\236\\266\\344\\275\\234\\346\\245\\255\\345\\256\\211\\345\\205\\250\\350\\250\\261\\345\\217\\257\\350\\241\\250.formrepository\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 92}, {"commit_hash": "4a111a015f216936b4eb425630e73bfae379441b", "commit_訊息": "[PRODT]C01-20250505007 調整Web流程管理工具中批次匯出流程機制", "提交日期": "2025-05-07 17:09:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageDigger.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "d60797e7a8ba13e1f20edd504d611b5e5c7726e7", "commit_訊息": "[Web]C01-20250505001 修正流程主旨內容含HTML標籤時，將標籤內的雙引號改為單引號避免顯示異常", "提交日期": "2025-05-06 14:24:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "8ab1f1e54e02624f9c15e2985905290f7f409114", "commit_訊息": "[流程引擎]C01-20250429006 修正單身中繫結 Checkbox 或 RadioButton 的欄位，若透過非標準方式（如 Excel 匯入）產生表單資料後再編輯第 2 筆以後的內容時，因欄位不存在導致轉存表單失敗的問題", "提交日期": "2025-05-05 14:32:09", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "86b6f2844238882e14229989e538342ce07d9982", "commit_訊息": "[MPT]C01-20250428003 修正從首頁的本月發起流程進入查看表單內容時畫面右側截斷問題", "提交日期": "2025-05-02 13:39:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d1caa14f93c194276a7139fd2d0116d729557c42", "commit_訊息": "[Web]C01-20250428001 单身加总栏位若设置千分位，加总异常的问题", "提交日期": "2025-04-29 16:06:16", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5635a403a124e3ae4450b88950718067232decf6", "commit_訊息": "[ORGDT]Q00-20250429001 修正Web組織管理工具無法新增部門問題", "提交日期": "2025-04-29 13:47:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eeb7ceace89afa9b95092fa5c5fc56f08e9dbcca", "commit_訊息": "[PRODT]C01-20250425004 修正Web流程管理工具設置流程截止日時間還沒到畫面上卻已經顯示失效的圖示問題", "提交日期": "2025-04-28 17:14:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPkgCategoryListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "425d41177fadc87d4347b99c24e10e041a5419b4", "commit_訊息": "[Web]A00-20250423001 修正更改系統設定「系統是否要顯示開啟片語清單的按鈕」沒有生效問題", "提交日期": "2025-04-25 15:00:42", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fa4a9c872d8b292559e7c8f68fd26f169a5e367d", "commit_訊息": "[在線閱覽]C01-20250227005 調整pdf屬性若被旋轉，導致浮水印方向被旋轉的問題", "提交日期": "2025-04-25 11:13:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9c6dc16628b1125b8f1ba6c41bb441c1cda7f475", "commit_訊息": "[組織同步]C01-20250415009 修正同步一個生效子部門到失效父部門內，父部門仍為失效(中介為生效)", "提交日期": "2025-04-22 15:02:50", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "656fd6371aabeea02d2c65c7ebccb24fa85a8e2d", "commit_訊息": "[费用报销]C01-20250418003 调整出货光碟一般費用報銷單流程", "提交日期": "2025-04-21 16:03:32", "作者": "周权", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a967586fd4cc16bcd351c1c11a700c2865c013d1", "commit_訊息": "[Web]C01-20250415005 修正追蹤流程圖取回重辦時可列出相同人員多關卡清單", "提交日期": "2025-04-21 14:14:20", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "907ce0626d09a0d889d88664cfd2fd491db65c1a", "commit_訊息": "[Web]C01-20250418001 当userName或userId为空时，优化HR小助手同步失敗log", "提交日期": "2023-08-25 15:40:09", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/User.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4cda5b8f294533efa5265511915e745cd7072588", "commit_訊息": "[流程引擎]Q00-20250418001 優化流程派送因建立BamWorkAssignment失敗，加印出異常使用者資料", "提交日期": "2025-04-18 15:12:17", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0930633d6100a426d30824c66aaae41c2a085da9", "commit_訊息": "[PRODT]Q00-20250416001 修正Web流程管理中設定關卡的表單存取控管設定無法開啟問題[補]", "提交日期": "2025-04-18 10:29:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "363d4756b740c1c55a8ffe801e46e5983c206949", "commit_訊息": "[Web]Q00-*********** 優化主管首頁自訂時間中的結束時間帶入右側待辦處理量數量做為條件之一", "提交日期": "2025-04-17 17:07:14", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "95d93434f11aa23ae7bc283b22ca660173cac36e", "commit_訊息": "[BPM APP]S00-*********** 行動版Grid元件增加按鈕支持rowClick事件", "提交日期": "2025-04-16 14:40:14", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "38bbaa2f2f19557873c9072baeaacbb2999c9dcf", "commit_訊息": "[BPM APP]C01-20250311001 修正行動端在元件非單欄模板情況下使用FormUtil.disable語法會發生無效狀況", "提交日期": "2025-04-14 09:34:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed179acc49f8cec968f7b1b44e84b4c1748c5d45", "commit_訊息": "[資安]Q00-20250410002 資安問題在URL中不能有jsessionid參數,移除jsessionid=session.getId()", "提交日期": "2025-04-11 16:03:02", "作者": "davidhr-2997", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3486657e2779d90e8937293b09a66ebd4ab9f73d", "commit_訊息": "A00-20250410001 移除需要購買才可掛載的ESS整合作業ESSQ96", "提交日期": "2025-04-10 17:02:42", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "\"Release/db/optional/@appform-essplus/HR\\346\\263\\225\\344\\273\\244\\350\\246\\217\\347\\253\\240\\345\\212\\251\\346\\211\\213/Init_AppForm_Data_DM8.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/HR\\346\\263\\225\\344\\273\\244\\350\\246\\217\\347\\253\\240\\345\\212\\251\\346\\211\\213/Init_AppForm_Data_MSSQL.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/db/optional/@appform-essplus/HR\\346\\263\\225\\344\\273\\244\\350\\246\\217\\347\\253\\240\\345\\212\\251\\346\\211\\213/Init_AppForm_Data_Oracle.sql\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "d479422b9e9a6079c09acd03460f886e158d9b82", "commit_訊息": "S00-20250410003 ESSQ96更名，由HR法令知識家改為HR法令規章助手", "提交日期": "2025-04-10 16:42:44", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.2_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/8.1.1.2_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "814e062230da65e5b1d88b34b6a3036c4c170830", "commit_訊息": "[PRODT]C01-20250408010 修正Web流程設計中關卡進階的第二次逾時通知處理人員主管異常勾選問題", "提交日期": "2025-04-09 15:24:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4fdb5374215d916797e4850ab617f4e5508239e0", "commit_訊息": "[Web]C01-20250408009 修正主管首頁切換部門時右側待辦處理量數量沒有變動", "提交日期": "2025-04-09 14:43:44", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e044f496d7a987733bf6ff7a2baa8b0c83454c82", "commit_訊息": "[ISO]A0020250325001 修正元件代號超出30導致轉存表單到OracleDB失敗", "提交日期": "2025-03-26 10:50:51", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/copyfiles/@iso/default-form/ISOMod001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001.form", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/copyfiles/@iso/default-form/ISONew001Manager.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dbb727d18b7867f56185e3ebce3140231626a4b7", "commit_訊息": "[CRM]修正CRM呼叫BPM取得檢視簽核意見的網頁URL不支持流程主機協定為https的異常", "提交日期": "2025-03-25 09:27:10", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodGetApproveOpinion.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd94786f8d730ee685a4de9a548bb35d157cf33f", "commit_訊息": "[內部]優化併行閘道後設定條件且不是全部的線都符合時的錯誤訊息", "提交日期": "2025-04-02 14:25:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f51f0619a8ce32a7a5e97a1ad4de4e0c8fbc690", "commit_訊息": "[Web]C01-20250401001 修正使用者開窗內送要求的參數太多問題", "提交日期": "2025-04-07 10:35:56", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "240fd41e039fb72cb81527a0e4a201df8e5f1efa", "commit_訊息": "[Web表單設計師]C01-20250328005 修正表單定義起始有效日時間呈現異常問題", "提交日期": "2025-04-02 14:59:32", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/JSONConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/TimestampAdapter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "16a57a73704f4d5ff317a0e0683b9b9e1f926014", "commit_訊息": "[Web]C01-20250317001 修正客製取得表單元件寫入簽核意見後沒有換行問題", "提交日期": "2025-04-02 14:45:00", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/StringUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7244ce3f66e6d056af0b2ab15b78a8223b65a26d", "commit_訊息": "[Web]C01-20250325004 修正行動裝置待辦派送需要使用者指定發送的組織單位時，沒有確認按鈕問題", "提交日期": "2025-04-02 14:39:45", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6a2a5083ccafa5402112f493290851769056473", "commit_訊息": "[Web]C01-20250326006 修正授權流程無法呈現作廢字樣的T100流程", "提交日期": "2025-04-02 14:26:49", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9b49d212f668668d7c1eddb0f7c7475249a551ed", "commit_訊息": "[Web]A00-20250326001 修正自訂驗證函式比較數值大小因勾選千分位異常問題", "提交日期": "2025-04-02 14:14:08", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c8a16e5b03233c24cd10adc5c019bf36de6a4f50", "commit_訊息": "[流程引擎]C01-20250327003 修正ERP拋單有附件NoCmDocument缺少formInstanceOID欄位值問題", "提交日期": "2025-04-02 13:48:59", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3219843a6aac7a9c45e33d2e49bc46140b5007c3", "commit_訊息": "[Web]C01-20250327004 修正刪除附件後全域變數locale異動問題", "提交日期": "2025-04-02 11:56:27", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "85854d1fab87fba9b5f3ecce091155a0fad98ffe", "commit_訊息": "[BPM APP]C01-20250328006 修正行動端的已轉派清單撈取異常問題", "提交日期": "2025-04-02 11:50:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "19dfcf92aea435eeae361a4845a338f188cdaa59", "commit_訊息": "[欧陆通]C01-20250321001 調整企業微信推播過長時會顯示HTML tag問題", "提交日期": "2025-04-02 11:46:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}