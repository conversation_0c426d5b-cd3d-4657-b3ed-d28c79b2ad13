{% extends "base.html" %}

{% block title %}{{ page_title }} - BPM服務部好用工具{% endblock %}

{% block extra_css %}
<style>
    .search-form {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .results-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        min-height: 400px;
    }
    
    .file-item {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .file-item:hover {
        background-color: #f8f9fa;
        border-color: #007bff;
    }
    
    .file-path {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        color: #6c757d;
        word-break: break-all;
    }
    
    .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .upload-area:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    
    .upload-area.dragover {
        border-color: #007bff;
        background-color: #e3f2fd;
    }
    
    .batch-results {
        max-height: 500px;
        overflow-y: auto;
    }
    
    .batch-file-group {
        margin-bottom: 1.5rem;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
    }
    
    .batch-file-name {
        font-weight: bold;
        color: #007bff;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fas fa-search me-3"></i>
                {{ page_title }}
            </h1>
            <p class="page-subtitle">查詢class、jsp、js檔案位置</p>
        </div>
    </div>

    <!-- 搜尋方式選擇 -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills nav-fill" id="searchTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="single-tab" data-bs-toggle="pill" 
                            data-bs-target="#single-search" type="button" role="tab">
                        <i class="fas fa-search me-2"></i>單一檔案搜尋
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="batch-tab" data-bs-toggle="pill" 
                            data-bs-target="#batch-search" type="button" role="tab">
                        <i class="fas fa-upload me-2"></i>批量檔案搜尋
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- 搜尋內容 -->
    <div class="tab-content" id="searchTabContent">
        <!-- 單一檔案搜尋 -->
        <div class="tab-pane fade show active" id="single-search" role="tabpanel">
            <div class="search-form">
                <form id="singleSearchForm">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="version" class="form-label">版本</label>
                            <select class="form-select" id="version" name="version" required>
                                <option value="">請選擇版本</option>
                                {% for version in versions %}
                                <option value="{{ version }}">{{ version }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="search_query" class="form-label">檔案名稱</label>
                            <input type="text" class="form-control" id="search_query" name="search_query" 
                                   placeholder="輸入檔案名稱（不含副檔名）" required>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="search_type" class="form-label">搜尋類型</label>
                            <select class="form-select" id="search_type" name="search_type">
                                <option value="fuzzy">模糊搜尋</option>
                                <option value="exact">精確搜尋</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>搜尋
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="clearSingleForm">
                                <i class="fas fa-times me-2"></i>清除
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 批量檔案搜尋 -->
        <div class="tab-pane fade" id="batch-search" role="tabpanel">
            <div class="search-form">
                <form id="batchSearchForm" enctype="multipart/form-data">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="batch_version" class="form-label">版本</label>
                            <select class="form-select" id="batch_version" name="version" required>
                                <option value="">請選擇版本</option>
                                {% for version in versions %}
                                <option value="{{ version }}">{{ version }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">上傳壓縮檔</label>
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p class="mb-2">拖拽檔案到此處或點擊選擇</p>
                                <p class="text-muted small">支援 ZIP 和 RAR 格式</p>
                                <input type="file" class="d-none" id="fileInput" name="file" 
                                       accept=".zip,.rar" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>批量搜尋
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="clearBatchForm">
                                <i class="fas fa-times me-2"></i>清除
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 載入中 -->
    <div class="loading" id="loading">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">載入中...</span>
        </div>
        <p class="mt-2">正在搜尋檔案...</p>
    </div>

    <!-- 搜尋結果 -->
    <div class="results-container" id="resultsContainer">
        <div class="text-center text-muted">
            <i class="fas fa-search fa-3x mb-3"></i>
            <p>請選擇版本並輸入檔案名稱，然後點擊搜尋按鈕</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 單一檔案搜尋
    $('#singleSearchForm').submit(function(e) {
        e.preventDefault();
        performSingleSearch();
    });

    // 批量檔案搜尋
    $('#batchSearchForm').submit(function(e) {
        e.preventDefault();
        performBatchSearch();
    });

    // 清除表單
    $('#clearSingleForm').click(function() {
        $('#singleSearchForm')[0].reset();
        showDefaultMessage();
    });

    $('#clearBatchForm').click(function() {
        $('#batchSearchForm')[0].reset();
        $('#uploadArea').removeClass('dragover');
        showDefaultMessage();
    });

    // 檔案上傳區域事件
    const uploadArea = $('#uploadArea');
    const fileInput = $('#fileInput');

    uploadArea.click(function() {
        fileInput.click();
    });

    fileInput.change(function() {
        const file = this.files[0];
        if (file) {
            uploadArea.html(`
                <i class="fas fa-file-archive fa-3x text-success mb-3"></i>
                <p class="mb-0">${file.name}</p>
                <p class="text-muted small">點擊重新選擇檔案</p>
            `);
        }
    });

    // 拖拽事件
    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    uploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            fileInput[0].files = files;
            fileInput.trigger('change');
        }
    });

    function performSingleSearch() {
        const formData = new FormData($('#singleSearchForm')[0]);
        
        $('#loading').show();
        $('#resultsContainer').hide();

        $.ajax({
            url: '/files/search',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false
        })
        .done(function(data) {
            if (data.success) {
                displaySingleResults(data.data, data.total, data.version, data.query);
            } else {
                showError(data.error);
            }
        })
        .fail(function() {
            showError('搜尋時發生網路錯誤');
        })
        .always(function() {
            $('#loading').hide();
            $('#resultsContainer').show();
        });
    }

    function performBatchSearch() {
        const formData = new FormData($('#batchSearchForm')[0]);
        
        $('#loading').show();
        $('#resultsContainer').hide();

        $.ajax({
            url: '/files/batch-search',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false
        })
        .done(function(data) {
            if (data.success) {
                displayBatchResults(data.data, data.total_files, data.found_files, data.version);
            } else {
                showError(data.error);
            }
        })
        .fail(function() {
            showError('批量搜尋時發生網路錯誤');
        })
        .always(function() {
            $('#loading').hide();
            $('#resultsContainer').show();
        });
    }

    function displaySingleResults(files, total, version, query) {
        if (files.length === 0) {
            $('#resultsContainer').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <p>沒有找到符合條件的檔案</p>
                    <p class="small">版本：${version}，查詢：${query}</p>
                </div>
            `);
            return;
        }

        let html = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>搜尋結果 (共 ${total} 筆)</h5>
                <small class="text-muted">版本：${version}，查詢：${query}</small>
            </div>
        `;

        files.forEach(function(file) {
            html += `
                <div class="file-item">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-1">${file.file_name}</h6>
                            <div class="file-path">${file.path}</div>
                        </div>
                        <div class="col-md-4 text-end">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>${file.modified_time || '未知'}
                            </small>
                            ${file.size ? `<br><small class="text-muted">${formatFileSize(file.size)}</small>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        $('#resultsContainer').html(html);
    }

    function displayBatchResults(results, totalFiles, foundFiles, version) {
        if (Object.keys(results).length === 0) {
            $('#resultsContainer').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <p>沒有找到任何檔案</p>
                    <p class="small">版本：${version}，總檔案數：${totalFiles}</p>
                </div>
            `);
            return;
        }

        let html = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>批量搜尋結果</h5>
                <small class="text-muted">版本：${version}，找到 ${foundFiles}/${totalFiles} 個檔案</small>
            </div>
            <div class="batch-results">
        `;

        Object.keys(results).forEach(function(fileName) {
            const files = results[fileName];
            html += `
                <div class="batch-file-group">
                    <div class="batch-file-name">${fileName} (${files.length} 個位置)</div>
            `;
            
            files.forEach(function(file) {
                html += `
                    <div class="file-item">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="file-path">${file.path}</div>
                            </div>
                            <div class="col-md-4 text-end">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>${file.modified_time || '未知'}
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
        });

        html += '</div>';
        $('#resultsContainer').html(html);
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showError(message) {
        $('#resultsContainer').html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `);
    }

    function showDefaultMessage() {
        $('#resultsContainer').html(`
            <div class="text-center text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>請選擇版本並輸入檔案名稱，然後點擊搜尋按鈕</p>
            </div>
        `);
    }
});
</script>
{% endblock %}
