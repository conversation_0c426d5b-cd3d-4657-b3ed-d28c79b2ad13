{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.6.2", "date": "2022-06-26 22:09:39", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.2", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.6.1", "date": "2022-06-26 22:15:41", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.1", "author": "lorenchang"}, "比較時間": "2025-07-18 11:41:35", "新增commit數量": 244, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "00e58a46f66a33af74205dc9926e66558737aba5", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.2", "提交日期": "2022-06-26 22:09:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "b04396550d00f4aae8213285541c9d2182b6ae10", "commit_訊息": "[內部]Q00-20210705009 修正移動端選項元件使用動態塞值會顯示異常問題[補]", "提交日期": "2021-07-08 14:51:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b2c344b1042709b7602f6b695e50a4729000f2a", "commit_訊息": "[內部]Q00-20210708001 修正移動端FormUtil.setTextAlign在doubleTextbox異常問題", "提交日期": "2021-07-08 10:46:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "919d820e1e1321df7b9157d275b26a8effe77c71", "commit_訊息": "[內部]Q00-20210706011 調整移動端Time元件比較大小功能在安卓上設定完值後點其他地方才會有提示的問題", "提交日期": "2021-07-08 10:14:46", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53311c14c04700eb2eff4ee92aad5eab72a5c6b9", "commit_訊息": "[BPM APP]Q00-20210707002 調整取回重辦時，若有拋錯取回重辦失敗，應顯示錯誤資訊", "提交日期": "2021-07-07 19:50:33", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6fee78796369ba957ef7e67c1ba0fddadfb4b611", "commit_訊息": "[內部]Q00-20210705015 修正取回重辦後，再次點擊取回重辦，會無選擇關卡彈窗", "提交日期": "2021-07-07 19:39:03", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d1fc34a69a2f41e3c4616d196bf113c2e181d418", "commit_訊息": "[內部]Q00-20210705009 修正移動端選項元件使用動態塞值會顯示異常問題", "提交日期": "2021-07-07 18:06:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74b7a3d19168ae1715b17b6c1559c42ca372ccc4", "commit_訊息": "[內部]Q00-20210706013 調整行動版RWD表單設計師的SubTab和Grid元件文字過長時區塊會跑版問題", "提交日期": "2021-07-07 17:41:19", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "43f3a16e72f77be8efb0ed46f6dd86fab4f053a3", "commit_訊息": "[BPM APP]Q00-20210706010 修正當使用Oracle資料庫時要取得IMG的待辦列表會顯示無資料問題", "提交日期": "2021-07-07 16:01:50", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9c3384c82bc184f876ade2a03f5ceb1b85ed57a4", "commit_訊息": "[內部]Q00-20210705014 新增DialogInputMuti元件在唯讀狀態下，元件設定為無標籤且沒有填寫內容值，要隱藏的機制", "提交日期": "2021-07-07 15:44:32", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "fc054553ee1fe4aa5e712ac3bd636ce730c787cc", "commit_訊息": "[Web]A00-20210630002 修正在監控和追蹤流程內，工作歷程的通知者有一人以上會出現<br>", "提交日期": "2021-07-07 12:36:03", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "04c51ec36b143e86887292a5adbab88e11c9154c", "commit_訊息": "[BPM APP]Q00-20210706015 修正移動端有多個grid時在其一grid編輯或刪除會連動另一grid問題", "提交日期": "2021-07-07 12:08:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0767ea700ea9a0dc991c96f90e7af817f3cde5e6", "commit_訊息": "[內部]Q00-20210705010 修正行動表單在選擇部門區塊若點擊遮罩時選擇部門彈窗不會隱藏問題", "提交日期": "2021-07-07 11:35:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "428d2b1e02fdb1c8319acc5021e96293df2d56be", "commit_訊息": "[內部]Q00-20210706012 修正移動端subTab元件已選中頁籤的文字顏色", "提交日期": "2021-07-07 10:57:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "01f3543313e00931908421765a5d07c2bd71768c", "commit_訊息": "[內部]Q00-20210706016 修正移動端加簽的人員標籤刪除按鈕跑版問題", "提交日期": "2021-07-07 10:49:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b4a5f2cc67a8d7ffda3bc541e24aa82fea6de28d", "commit_訊息": "Q00-20210705013 修正移動端使用FormUtil.labelCss在PassWord元件沒效果問題", "提交日期": "2021-07-07 10:10:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbb4ee81e91c82ee883e86fd861fb98b56533576", "commit_訊息": "[BPM APP]Q00-20210707001 修正中間層追蹤已發起已結案流程，無郵戳顯示流程狀態資訊的問題", "提交日期": "2021-07-07 10:00:43", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba15fc5b196c47f756cac1fe3b0fdd66e56eeb9f", "commit_訊息": "[內部]Q00-20210705006 調整動態渲染表單郵戳內容", "提交日期": "2021-07-07 09:02:41", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88e35ac849908868a8b10b52a09727105517826c", "commit_訊息": "[內部]Q00-20210705007 修正CheckBox跟RadioButton元件的標籤文字太長的話會有截半問題", "提交日期": "2021-07-06 21:17:44", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c497be6e55e3e248ba75f374569041baaffe4aca", "commit_訊息": "[內部]Q00-20210705001 修正發起關卡的加簽頁面異常問題", "提交日期": "2021-07-06 20:31:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d0a8e122fe64c5bafffb8c6c5a7b7c1bedc092d1", "commit_訊息": "[內部]Q00-20210706020 修正移動端FormUtil.disable在各元件與同時用其他方法會異常問題", "提交日期": "2021-07-06 19:04:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3d63e10c515b947f472d6799b7f3663462e0fc2", "commit_訊息": "[內部]Q00-20210706018 修正移動端FormUtil.setTextAlign在Dialog元件的右邊欄位沒效果", "提交日期": "2021-07-06 17:34:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "48199ef9207752b52472e343581041c0cc32d801", "commit_訊息": "[內部]Q00-20210706019 修正移動端使用FormUtil.setFontColor在DialogInput沒效果問題", "提交日期": "2021-07-06 17:30:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b43a64a96cc2bd1bbf5cbffa0a3bd1dcfbfe761", "commit_訊息": "[Web]Q00-20210706021修正作業程序書點擊確定鈕畫面顯示異常", "提交日期": "2021-07-06 15:55:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3b7874bea3e8b8ab5505e023f219ddb68daadf5b", "commit_訊息": "[內部]Q00-20210706009 調整移動端表單Gird元件必填提示文字樣式", "提交日期": "2021-07-06 14:50:29", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4acb9e1ec7f13bac908687317392bf66f50bf291", "commit_訊息": "[流程引擎]S00-20210419002 增加BPM登入頁面使用者密碼加密傳輸功能[補]", "提交日期": "2021-06-30 20:17:49", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/aes.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "2dee8094caec93a0fa86ac84b644875c08eb22f4", "commit_訊息": "[流程引擎]S00-20210419002 增加BPM登入頁面使用者密碼加密傳輸功能", "提交日期": "2021-04-22 18:32:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "365043964057a4a706f883a7ca41164039dd8328", "commit_訊息": "[內部]Q00-20210705005 修正表單設計師中的絕對位置表單有拉日期或時間元件時儲存表單會發生錯誤的問題", "提交日期": "2021-07-06 14:07:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "302b294484fbb01cde89d8a03fd98cca6bf2626d", "commit_訊息": "[組織設計師]S00-20210419001 登入檢查密碼規則功能，額外開放組織設計師可更改User密碼[補]", "提交日期": "2021-07-06 11:37:57", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.7.1_DML_MSSQL_1.sql", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 1}, {"commit_hash": "5ea6394f693c82f0f155c9e5b2d0eb3605205f8f", "commit_訊息": "[組織設計師]S00-20210419001 登入檢查密碼規則功能，額外開放組織設計師可更改User密碼", "提交日期": "2021-04-22 15:24:27", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.7.1_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "1ba806f6404c39b07401e6fad3b4d5b1dcc13733", "commit_訊息": "[內部]S00-20210419005 調整BPM RESTful API存在敏感文件問題", "提交日期": "2021-04-21 16:24:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AcceptWorkItemBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ActivityDefinitionForClientListBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AddCustomActivityBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkItemBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkStepBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CriticalBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ElementBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchProcessCommentsBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchReexecuteActivityBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchWorkStepsBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ListReaderBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ReexecuteActivityBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/RsrcbundleBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/TerminateProcessBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/UserFormValueBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/XmlBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/AttachmentBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/BuildFormBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/DeleteFileBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/DownloadFileBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeFavoriteReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeProcessBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeWorkItemReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/NoticeProcessBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/RollbackActivityBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SaveFormBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SearchProcessListBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/TraceProcessBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/UploadFileBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/UploadFileInfoBeanReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/config/Swagger.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/FormV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileFormV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystemV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/OrgV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/UserClientBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 51}, {"commit_hash": "edf96835c73295c42a138bec657f6bb1d994958a", "commit_訊息": "更新5.8.6.1_mobile_ui多語系至總檔", "提交日期": "2021-07-02 13:47:26", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 2}, {"commit_hash": "15710851d54778cc766200a45d6684952cc07a91", "commit_訊息": "[BPM APP]調整FormUtil寫法符合新樣式[補]", "提交日期": "2021-07-01 23:43:27", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "376426ba99f35837d1f39e832b8371d7bcd79856", "commit_訊息": "[BPM APP]S00-20200603001 BPM APP表單發起頁面加入加簽功能", "提交日期": "2021-06-30 19:16:42", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "705b35307ae96eee3f0aafa1c61628a28f40ae95", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-29 11:31:01", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "25e627947884e3e5670e5af5891a556b60cec39c", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-28 15:51:58", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "efe2c296ab9607cb3826943fac14b9d4dbaa86ee", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-25 18:26:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e93e3d1b12675a24a6a300f6c487bf3a5dc60f5e", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-24 12:10:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd8a871d3e8a61e885c81c92b33c93c25870a179", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-23 14:48:33", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "05d1ea65c6bb0eb6d80f67199fc9290d50252b80", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-18 10:33:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "97a7d678bb2fd534a5f97d0f8d965f3cc73929be", "commit_訊息": "[表單設計師]調整Web表單設計師行動版相對位置的表單元件符合新樣式", "提交日期": "2021-06-16 16:59:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "38fc3d6ad55922911ca423f017e7a49701a9784d", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-16 13:49:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "7a01925f1184eaf7a4ee0db4ca7fcbfaa0575faf", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-15 12:04:46", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "09052b600e4a39dbf30411c831fd2bf0b5133f88", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-10 14:54:42", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7bdef1b92fc9141cadc9b71f3e42b948612552c1", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-10 11:32:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "27fa549d44b09225af822b15fe231b493eb025c9", "commit_訊息": "[BPM APP]調整FormUtil寫法符合新樣式[補]", "提交日期": "2021-06-09 16:29:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e81f30f4953c6017bc54db4274807da47fea8baa", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-09 11:11:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "480263100120e3f95cf170719113705e5fdc2439", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-08 19:22:50", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "af31f6a96c85f1ac9ab44cea17e31826276813b6", "commit_訊息": "[BPM APP]調整FormUtil寫法符合新樣式", "提交日期": "2021-06-07 11:51:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dae51873affbfebcd6072ec91ed933ccaa4c3b0a", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-07 09:58:21", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d517a508fa14e4f39a99856cecf67a72ff77a1c9", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-03 10:28:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8593084a12577a168a0b82377d6df56c89087485", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-02 18:47:36", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d6cea66c91eb4f6d342cf31c6322179fbcbc0d00", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-02 15:33:27", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b83243b72afe277067abe9973ff526ac82966d04", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-02 13:40:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "434aca70ee5c8b97168138cc14831bb5f40bfad4", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-02 10:56:35", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32f836dd579e95d022bf2b89cc84658c4bc0defb", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-01 19:47:37", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "762fe6f509e6956268ed5e7ac7889b6e2b90f9c6", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-01 14:52:18", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "9e5cb0fb9b83c9ccc3bf580d3071059eea092640", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-01 11:59:22", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d24de12534aa580b5fb78a0a810e25482d70102", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-06-01 10:15:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4136ee6a1c969b02bddcf829dbf2ed636a95603b", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-31 09:31:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bf73ef0a7efd65e1d019478b9b602502df584c71", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-28 17:39:05", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2014b31bc606d57aac970bdfa781d8a2182d912b", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-28 16:15:01", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "85411ac5e122faf9666dba0a3e1c65a0ddb64b01", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-28 11:59:46", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3c8090bd429ba64e82802498d8a9969d5594ba40", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-28 11:09:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 17}, {"commit_hash": "9db0130296c848204126bc9bf41b41078d2054f0", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-27 10:40:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "1eff63ec0f829c9c34f0bded66a7ba709ea59742", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-26 18:55:50", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "86713810eb77df7181e5795c16fc3da1e1a790a0", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-26 18:01:59", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "62124101303dd4994623749ed8f4eb0c8272ae56", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-26 14:44:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e6f5eb3e6614f7bac98f3a59cddf9b1b81c3024d", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-26 14:38:58", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "18387d6174057257e344e6ba4cb54f0743292a33", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-26 11:49:51", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "85f84d080e7343c1bc1c59149f67120ec2585954", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-26 09:43:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "44b20c73f45af040e862ac7fa812aaab8b2bbf9f", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-24 22:36:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/AttachmentContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/digiwhale_img/defaultIcon.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/digiwhale_img/excel.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/digiwhale_img/mov.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/digiwhale_img/mp4.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/digiwhale_img/pdf.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/digiwhale_img/ppt.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/digiwhale_img/word.svg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 34}, {"commit_hash": "afb1aedfcdcc9fb274422b7a0c2f77542f7dc6c7", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-24 18:49:08", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89f2b5bf8c60a45a363a3c9d83b2e4ca36419235", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-24 16:14:10", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1eef4e4829f60993151bf725580a540348b3d1c3", "commit_訊息": "[BPM APP]調整優化移動表單樣式影響的部分Grid使用的form script語法", "提交日期": "2021-05-21 17:27:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65d908665e65d5234fae1590b9e6da68d2605a0d", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-21 13:53:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "3f5726fa787919d763c921ef46e2539597cd58c8", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-18 17:57:49", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "82adcdf5a039504822a35071e2b7ca3f8dd5e5d3", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-17 16:58:48", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8cfee79eb0f72bb5113864acfe1df197becbe7df", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-17 14:36:48", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "8db34f9a4ee12b5b7be6327159feeac3d8d9892e", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-17 09:49:07", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d4cf8f21739e1d04c81cf9f15157100534e5bc90", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-14 17:34:46", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "2669a90eeb5856e2fe3f2ab6d852738fac99aeb5", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-14 16:27:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmInvokeWorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "160fa4b16407f16c972d42654cc528a51f1a7c24", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-14 13:55:23", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "9dd6518d60a231fd32b2d417bab7bfe1bd977171", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-13 17:10:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmInvokeWorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "6a34444b02299322a283b68c958ccfa2d0c6981e", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-12 18:22:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cf7d99a77e93c50248771f9c7cf15053742d60cf", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-12 14:02:41", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "0a15aadad883f5dfd07e3211073c0f075e47d575", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-12 11:49:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1aed7d5a526001d7e7bd0a3f5f9b4f0e3911ace5", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-11 17:42:47", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "94a0aad03007cf5739f8b6a1d1495870465d97eb", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-11 13:44:23", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 23}, {"commit_hash": "edd8e39dc564d6cbee94ed7166187479af317b20", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-11 10:32:22", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "6df8bb2d0acbd2be6ae36d1de689cf87accf5a31", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-07 09:42:47", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "4cd3f9a99b4d1c2aa0ccdc5bf87a7c72dfb7cc6b", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-06 16:09:46", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmProcessInstForTracingVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "b3724b8f3f84fc44b408bb457de39f6429389e7e", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-04 15:57:27", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "55a3d46c0459b8344deae707ae007c42d16b0423", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-04 15:29:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "5bc80436633e873f59db4c032a9b34d1223f74f2", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-04 10:23:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "bb099259ef5c745ba2cd87d1dd84174f067f063f", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-03 16:17:56", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "b21146701ebdc25dffd5278235ea682ffcbb0d6d", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-03 15:28:30", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 20}, {"commit_hash": "b74b0a4fd7c9c8c37c96dbf9b8d0758954b7c8a7", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-05-03 10:15:38", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "3b4d231bd969615851879baf9ce44e9c0f2aba33", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-04-29 18:41:07", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c61e2c4c4e3e6e796363e98e089c5ddaeba09674", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-04-28 18:41:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "8e6510c660e49cccfaa0e5a0a146cc394a646397", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-04-28 18:01:22", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ff23ab4bb673c350fd0daa699e9e60566209185", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-04-27 16:04:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "8d03c49b46ff49837dcbfc55c62707a0893ed4cd", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-04-27 14:10:14", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c710e80e02344890d5c9c14e01dd50d3039b4a84", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-04-27 11:06:18", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5755183e92de27909535ef0125cf03e1997af7f0", "commit_訊息": "[BPM APP]優化移動表單樣式[補]", "提交日期": "2021-04-27 09:40:57", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "59e38d69e1c80121cd9164c61e55d393abab78d9", "commit_訊息": "[BPM APP]優化移動表單樣式", "提交日期": "2021-04-26 18:02:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/FormContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileResigendServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppQRCode.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/material_Icons/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_mobile_ui.xlsx", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 47}, {"commit_hash": "184dea864e77e1d882a99a2897a18ccaf2016f3a", "commit_訊息": "[BPM APP]C01-20210629010 修正Line推播訊息在通知設定表單內容且Grid綁定選項元件時無法顯示問題[補]", "提交日期": "2021-07-01 19:12:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "118380fbd4f962fca713efcc6007464da0830bfa", "commit_訊息": "[內部]Q00-20210630002 ESS回傳狀態及更新AppFormActivityRecord的事件優化log[補修正]", "提交日期": "2021-07-01 09:18:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "42fb2e33a8acc66d83ed19766bea3d163cb0db4d", "commit_訊息": "Q00-20210419001 修正针对帳密认证错误的情况提示准确的信息(簡體有誤)", "提交日期": "2021-06-30 19:58:48", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "49a1502d94743bc0b7ba025c98898f73673674f3", "commit_訊息": "[內部]S00-20210419003 安全性議題 登入前後sessionId一樣有被攻擊的風險", "提交日期": "2021-04-22 18:13:27", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7f40b4b27735a17bf68fb7cf706c1ac76a9d94bc", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-06-30 19:43:25", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "83c4ba360a720a75bbde037d47cf64b8726e1d88", "commit_訊息": "[內部]Q00-20210419002 存在任意文件上传漏洞個人簽名檔上傳處", "提交日期": "2021-04-21 10:59:40", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9f3853f1cf7df6aac95849ee339a3685a352bb15", "commit_訊息": "[ESS]Q00-20210630003 調整ESS單據發起流程時如果在資料表已有紀錄，就不該往下繼續派送", "提交日期": "2021-06-30 19:25:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4053d451ceed4132723e5615bb42a16c02aeb5fa", "commit_訊息": "[內部]Q00-20210630002 ESS回傳狀態及更新AppFormActivityRecord的事件優化log", "提交日期": "2021-06-30 19:13:51", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/dao/OJBAppFormActivityRecordDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e4ae7a4f1cb51693b77b334d29ba89b8f86ac8c2", "commit_訊息": "[Web]Q00-20210630001 修正點選Grid的標頭欄位時，會導致畫面上Grid消失", "提交日期": "2021-06-30 18:35:11", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "020775feaf582f668e1d942a49f1d598a2ccd561", "commit_訊息": "[BPM APP]C01-20210629010 修正Line推播訊息在通知設定表單內容且Grid綁定選項元件時無法顯示問題", "提交日期": "2021-06-30 16:30:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "273396b0ebf455695a9aae461b9ac27e8a638fbb", "commit_訊息": "[Web]S00-20200917003 調整載入表單時,若表單含有Grid元件則自動重載其資料", "提交日期": "2021-06-29 14:00:42", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7d05039fbbc6c55312dd90e364fd78400b4f41bf", "commit_訊息": "[ESS]Q00-20210512001 調整ESSF03補刷卡申請表單formscript空白問題", "提交日期": "2021-06-28 11:20:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF03\\350\\243\\234\\345\\210\\267\\345\\215\\241\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5f7aa8cf38cd377c64f85e903233958db336a404", "commit_訊息": "[流程引擎]Q00-20210625002 修正流程送出後，簡易流程圖部門核決層級預解析不正確", "提交日期": "2021-06-25 18:42:10", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "81b34758b3121b7d85b3e597cd02dcc9a70d1518", "commit_訊息": "[ESS]S00-20210609003 ESS表單高度 調整採用表單設計師的表單資訊中的\"表單高度\"[補修正]", "提交日期": "2021-06-25 15:59:47", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF05\\345\\212\\240\\347\\217\\255\\350\\250\\210\\345\\212\\203\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF20\\345\\207\\272\\345\\267\\256\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF21\\345\\207\\272\\345\\267\\256\\347\\231\\273\\350\\250\\230.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF22\\350\\252\\277\\350\\201\\267\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF24\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF28\\344\\272\\272\\345\\212\\233\\351\\234\\200\\346\\261\\202\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF31\\346\\213\\233\\350\\201\\230\\350\\250\\210\\347\\225\\253.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF51\\345\\212\\240\\347\\217\\255\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213(\\345\\244\\232\\346\\231\\202\\346\\256\\265\\345\\244\\232\\344\\272\\272).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF60\\350\\254\\233\\345\\270\\253\\350\\263\\207\\346\\240\\274\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF62\\345\\237\\271\\350\\250\\223\\351\\240\\220\\347\\256\\227\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF63\\345\\237\\271\\350\\250\\223\\351\\234\\200\\346\\261\\202\\346\\216\\241\\351\\233\\206.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF64\\345\\237\\271\\350\\250\\223\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "adcf56843a9054c52fe953a84df3babee703dd89", "commit_訊息": "[流程引擎]Q00-20210625001修正流程派送到離職人員，會造成BamWorkAssignmentData的關卡OID及派送紀錄OID為null", "提交日期": "2021-06-25 15:35:39", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bfc5a296a7506c17a8d65148917447d8c4f985d2", "commit_訊息": "[表單設計師]S00-20201215002 調整RWD表單日期或時間元件多增加一個可比較欄位的功能", "提交日期": "2021-06-25 12:07:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DateElementDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TimeElementDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppDateTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppTimeTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/RwdDateTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/RwdTimeTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "592bcb20ee9ce4e4d909eb51606b6ab3f6b61529", "commit_訊息": "[流程引擎]Q00-20210624001 修正Amazon的VM無法判斷是VM的問題", "提交日期": "2021-06-24 18:41:01", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/GuardServiceUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "466ff1fefc181c21dee0b256e523d13d4c5c0332", "commit_訊息": "[BPM APP]S00-20201221001 新增IMG待辦列表可依關卡建立時間排序功能", "提交日期": "2021-06-24 11:26:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "b6ddfb90cc00391116455b0eb8ce25e3d23f2077", "commit_訊息": "[Web]Q00-20210623002 修正關注項目內查詢維護作業，在行動版畫面下，右下角的滑到頁面頂部按鈕沒作用", "提交日期": "2021-06-23 20:02:40", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6bb897303085adf6e6af6aea4899115c8d4499d", "commit_訊息": "[Web]Q00-20210623001 修正透過報表設計器產生的頁面，在行動版畫面下，右下角的滑到頁面頂部按鈕沒作用", "提交日期": "2021-06-23 19:25:36", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/ChartQueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1afd83ac73867a417f309d8881e918bcd0f1566c", "commit_訊息": "[設計師]A00-20210616002 修正設計師工具用https方式登入，無法正常顯示伺服器位址", "提交日期": "2021-06-23 14:02:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/SecurityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/control/AbstractDesignerSystemManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/AdmStartUp.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c6dc62372e75f646a1f328db388854e1e38f60aa", "commit_訊息": "[Web]A00-*********** 修正Grid設定小螢幕時使用名片式呈現並設定改變Grid欄位顏色，從追蹤流程進入未能呈現顏色", "提交日期": "2021-06-22 21:01:52", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e2312b67bd29d73b88c3e44a87114c2397550454", "commit_訊息": "[內部]移除MobileScheduleManageBean舊版與Home相關的程式", "提交日期": "2021-06-22 17:42:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageHome.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageLocalHome.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 2}, {"commit_hash": "cc1ce0e952230e799506042fb20425fffb94af2c", "commit_訊息": "[BPM APP]S00-*********** 新增IMG呼叫行事曆刪除接口功能", "提交日期": "2021-06-22 17:37:14", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "ba856845352c2a7c525f44573064f9b79a404598", "commit_訊息": "[BPM APP]C01-20210618004 Grid欄位未填寫資訊派送會拋exception，導致派送失敗問題", "提交日期": "2021-06-22 17:26:32", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cc3304093194b26df9f3263945a460a323c15a5", "commit_訊息": "[內部]Q00-20210622001 調整BCL8轉檔機制為synchronized", "提交日期": "2021-06-22 11:08:07", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "71639b798bb6b8860608119df851c628166de699", "commit_訊息": "[BPM APP]C01-20210621003 調整移動表單畫面的grid元件在繫結元件未放到區塊時會有提示問題", "提交日期": "2021-06-22 09:55:55", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7954f5792a3ff8c69a31ca230703f446c5c45ecb", "commit_訊息": "[內部]Q00-*********** 優化log結案訊息，增加流程序號以便排查[補修正]", "提交日期": "2021-06-21 18:42:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TiptopDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "586cb565976e0dc39725e767f844b86071739bb5", "commit_訊息": "[內部]Q00-*********** 優化log結案訊息，增加流程序號以便排查", "提交日期": "2021-06-21 18:32:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "efa119cfe01417de3b6a3b31ee8a9857bbc6dbc1", "commit_訊息": "[流程引擎]Q00-***********調整當前關卡及下個關卡處理者都是該使用者，且下一關卡設定為自動跳過關卡，派送對像要改成為該使用者，而不是代理人", "提交日期": "2021-06-21 17:05:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74b2e1080b80f8501dacb741f904cfa1cab6bca5", "commit_訊息": "[BPM APP]C01-20210616001 調整附件清單上傳後跑版的問題", "提交日期": "2021-06-21 13:56:32", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc8bc65e5a365e1f6ddb3185c0b33bfe08afe961", "commit_訊息": "[ESS]Q00-20210618002 鵬程 修正在IOS上進入ESS管理作業查詢類，設定一頁顯示500筆，Grid資料會滑不到底部", "提交日期": "2021-06-18 19:28:12", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c83802612ad3f4aa766d6dcec603b1da0e6cbffc", "commit_訊息": "[Web]S00-20201230002 調整RWD表單checkBox和radioButton元件在唯讀狀態下的已選擇樣式", "提交日期": "2021-06-18 18:17:48", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-form-component.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "102d0b84cd53693809a4c44225a343ddbd9e4ec1", "commit_訊息": "[設計師]A00-20210616002 修正設計師工具用https方式登入，無法正常顯示伺服器位址", "提交日期": "2021-06-17 14:39:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/SecurityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/control/AbstractDesignerSystemManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/AdmStartUp.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f20312747eaeb9d1896790f90d7fd2b2972ac3a0", "commit_訊息": "Revert \"[流程引擎]A00-20210615001修正流程下一關卡設定是\"與前一同簽核者，則跳過\"，若下一關為自己，因為不為上班日自動轉派為代理人時，該關卡會自動跳過\"", "提交日期": "2021-06-17 11:16:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5e4f8a99cd1ba96d75cde6b1fa672a8b799d4cc5", "commit_訊息": "Revert \"[流程引擎]A00-20210615001修正流程下一關卡設定是\"與前一同簽核者，則跳過\"，若下一關為自己，因為不為上班日自動轉派為代理人時，該關卡會自動跳過[補修正]\"", "提交日期": "2021-06-17 11:15:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7447b053f74b2e1277937da8103699f9843bdfe", "commit_訊息": "[流程引擎]A00-20210615001修正流程下一關卡設定是\"與前一同簽核者，則跳過\"，若下一關為自己，因為不為上班日自動轉派為代理人時，該關卡會自動跳過[補修正]", "提交日期": "2021-06-16 17:45:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d52baa9b592812aa7e0002eca51d9a378cff46d", "commit_訊息": "[流程引擎]A00-20210615001修正流程下一關卡設定是\"與前一同簽核者，則跳過\"，若下一關為自己，因為不為上班日自動轉派為代理人時，該關卡會自動跳過", "提交日期": "2021-06-16 17:32:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc30d0ebc8a8d1520abd0ea24151aba240dbd791", "commit_訊息": "[Web]A00-20210611003 修正chrome於追蹤流程無法開啟在線閱讀檔案", "提交日期": "2021-06-16 15:03:13", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7d4dfc47a51e6aacb7f43fce01cc319e910272d", "commit_訊息": "[TIPTOP]Q00-20210615001 修正TIPTOP拋單，SerialNumber元件沒有內容值", "提交日期": "2021-06-15 17:17:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f240503b57e9892ceac0b32753fbe2406b62151b", "commit_訊息": "[流程引擎]Q00-20210415001修正Collections.sort在1.7版在資料異常的狀況下會報IllegalArgumentException[補]", "提交日期": "2021-06-15 13:46:30", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/bin/standalone.conf", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8fbd940db4a491e71ad54d51e87f8fbab6ba121a", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-06-15 11:39:29", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7046b99ffc59069876430fc4fb449585dc835a08", "commit_訊息": "[Web]A00-20210611004修正表單頁籤簽核歷程置放位置選擇\"top\"且表單設計師設定\"顯示流程簽核意見\"為\"NOT_SHOW\"，待辦跟發起畫面的表單上方會顯示\"簽核意見\"的文字", "提交日期": "2021-06-15 11:39:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "378577d38ab50128b0b8948c0b4b279effed8a03", "commit_訊息": "[BPM APP]新增IMG簽核片語功能", "提交日期": "2021-06-15 10:30:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "12e5f33d7d9df7052c8d3705e0a5f77459e1f6b6", "commit_訊息": "[Web]Q00-20210611001 修正Grid設定小螢幕時使用名片式呈現且綁定Textarea，有輸入換行時從追蹤進入表單是呈現未換行", "提交日期": "2021-06-11 19:38:36", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "461575c6b61b33a73e63557f3427599efad328b4", "commit_訊息": "[Web]A00-20210608001 修正當checkBox或radioButton binding到Grid時再下一關點選Row會出現錯誤", "提交日期": "2021-06-10 23:25:05", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee9691cfada267d0fd906b9b85c7d53a0ea8e63f", "commit_訊息": "[ESS]S00-20210610001 回寫ESS服務任務關卡回傳錯誤訊息顯示在簽核畫面上", "提交日期": "2021-06-10 16:53:54", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "22aa33f961c817381ebaa5d8e2f120763d49109b", "commit_訊息": "Revert \"[ESS]S00-*********** 回寫ESS服務任務關卡回傳錯誤訊息顯示在簽核畫面上\"", "提交日期": "2021-06-10 16:49:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "d8f71b5352a25b9556343d5083a8d69e6479231e", "commit_訊息": "[ESS]S00-*********** 回寫ESS服務任務關卡回傳錯誤訊息顯示在簽核畫面上", "提交日期": "2021-06-10 16:33:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "56b632a8464dd92cf786015eeba524d9f9071774", "commit_訊息": "Revert \"[ESS]S00-*********** 回寫ESS服務任務關卡回傳錯誤訊息顯示在簽核畫面上\"", "提交日期": "2021-06-10 16:27:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "c7ca9e0b967ca7544d1bb6f2758f92aa409be34f", "commit_訊息": "[ESS]S00-*********** 回寫ESS服務任務關卡回傳錯誤訊息顯示在簽核畫面上", "提交日期": "2021-06-10 16:12:12", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "7b16153d4eb32b0256ad0586595eb39b835838e3", "commit_訊息": "[ESS]S00-20210528002 結案回寫給ESS，ESS回饋錯誤訊息，通知系統管理員的Mail會顯示ESS回饋的訊息[補修正]", "提交日期": "2021-06-09 18:29:10", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b734d8587464d17d019a989ea61dab44903451f5", "commit_訊息": "[ESS]S00-20210609003 ESS表單高度 調整採用表單設計師的表單資訊中的\"表單高度\"", "提交日期": "2021-06-09 14:52:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c8418c5e497b9706708b8f2f1ea119d7bc2c34f1", "commit_訊息": "[BPM APP]Q00-20210520002 調整上傳完附件HandWriting手寫板內容會消失問題[補]", "提交日期": "2021-06-09 09:59:22", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "28121c132d6101b11ce4306ac236c003a0b0fbeb", "commit_訊息": "[BPM APP]Q00-20210607001 調整上傳完附件DoubleTextBox會無formScript設定的效果問題", "提交日期": "2021-06-09 09:04:26", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f15af77ba11a062c38b63b855dfd3353c2855ec", "commit_訊息": "[Web]Q00-20210608001 修正人員名稱有新的特殊字(𥜥)時，進入ESS流程會報錯", "提交日期": "2021-06-08 15:42:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e46ec430d4eb0d4f9462bf682c36757df5435d1", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-06-07 10:31:32", "作者": "peng_cheng_wang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "19801b2399b18fac2e5e7993241008b7b5106ec8", "commit_訊息": "[ESS]Q00-20210604002 修正IOS的chrome在操作ESSQ管理作業會導致錯誤", "提交日期": "2021-06-07 10:30:59", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d385e91ffc1d726cbc2625640a38162edbf137d8", "commit_訊息": "[BPM APP]Q00-20210520002 調整上傳完附件HandWriting手寫板內容會消失問題[補]", "提交日期": "2021-06-07 10:30:49", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ba9be1d06d419bcf98627c09655f56dafa62d6c3", "commit_訊息": "[Web]A00-20210528001 修正透過URL開啟『追蹤流程實例內單一表單資料』，在行動裝置版面下無法查看附件[補]", "提交日期": "2021-06-04 15:54:44", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dcfd37bc3bb638cd8ff2dea4d7bfd52a7e8e7bc4", "commit_訊息": "[BPM APP]Q00-20210603001 修正因新增登入資訊功能導致IMG直連表單載入緩慢問題", "提交日期": "2021-06-03 17:03:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3dd718178db916bd7152a56e6d8c9b4c3d42089", "commit_訊息": "[Web]A00-20210602002 修正ISO生失效通知信連結無法登入BPM問題", "提交日期": "2021-06-03 14:50:04", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "079b0c617a4ee92fed650c1e7dd6aab2a94085ca", "commit_訊息": "[表單設計師]S00-20200917001 調整在表單設計師中匯入相同表單代號時彈出提示訊息", "提交日期": "2021-06-02 17:16:05", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5ffb7fa7e066beb4374a0a6937bd3f7c273feda5", "commit_訊息": "[Web]Q00-20210602001 修正Grid有綁定Radio時，關卡通知信設定以表單元件時，通知信的Grid會跑版", "提交日期": "2021-06-02 15:27:22", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d4342cb0ffd67a457d733e196be34f3849517a2", "commit_訊息": "[流程引擎]A00-20210527001 修正同關卡加簽兩次以上會出現流程壞掉的異常", "提交日期": "2021-06-01 20:29:53", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63a0c43636144a4108bf459691decdc4d0f0e5bf", "commit_訊息": "[ESS]新增與更新ESS整合表單", "提交日期": "2021-06-01 17:44:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF03\\350\\243\\234\\345\\210\\267\\345\\215\\241\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF04\\345\\212\\240\\347\\217\\255\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF05\\345\\212\\240\\347\\217\\255\\350\\250\\210\\345\\212\\203\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF06\\345\\212\\240\\347\\217\\255\\350\\252\\277\\344\\274\\221\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF07\\350\\253\\213\\345\\201\\207\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF17\\351\\212\\267\\345\\201\\207\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF20\\345\\207\\272\\345\\267\\256\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF21\\345\\207\\272\\345\\267\\256\\347\\231\\273\\350\\250\\230.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF22\\350\\252\\277\\350\\201\\267\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF23\\350\\252\\277\\350\\201\\267\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF24\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF25\\350\\275\\211\\346\\255\\243\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF26\\347\\215\\216\\346\\207\\262\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF27\\351\\233\\242\\350\\201\\267\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF29\\350\\275\\211\\346\\255\\243\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF50\\347\\217\\255\\346\\254\\241\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF51\\345\\212\\240\\347\\217\\255\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213(\\345\\244\\232\\346\\231\\202\\346\\256\\265\\345\\244\\232\\344\\272\\272).form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF52C1\\347\\217\\255\\346\\254\\241\\344\\272\\222\\346\\217\\233.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF64\\345\\237\\271\\350\\250\\223\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF69\\345\\223\\241\\345\\267\\245\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF77\\350\\226\\252\\350\\263\\207\\347\\265\\220\\346\\236\\234\\345\\257\\251\\346\\240\\270.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF80_\\345\\212\\240\\347\\217\\255\\350\\250\\210\\345\\212\\203\\346\\230\\216\\347\\264\\260\\346\\222\\244\\351\\212\\267.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 22}, {"commit_hash": "ddd139b94dd66ec523cf3cf248fc3630a384e72f", "commit_訊息": "[Web]A00-20210528001 修正透過URL開啟『追蹤流程實例內單一表單資料』，在行動裝置版面下無法查看附件", "提交日期": "2021-05-31 17:41:49", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "44dcf6a74d20d4ca87a9c39950be2a456d300a20", "commit_訊息": "[BPM APP]Q00-20210520002 調整上傳完附件HandWriting手寫板內容會消失問題", "提交日期": "2021-05-31 14:28:33", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e2dc37335117f96333187bebecac837e45ef18d6", "commit_訊息": "[ESS]S00-20210528002 結案回寫給ESS，ESS回饋錯誤訊息，通知系統管理員的Mail會顯示ESS回饋的訊息", "提交日期": "2021-05-28 23:44:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b01ba2d89675d35cfc582b730d2fa6dea71a26b2", "commit_訊息": "[內部][E10]Q00-20210528001 修正接口/v1/process/definitionlist/get撈出有異常卻未拋錯", "提交日期": "2021-05-28 14:57:42", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed6fc2e6ded6875a3ef7ce872da6529c39a5721b", "commit_訊息": "[流程引擎]Q00-*********** 調整DealOvertimeProcessHandler排程", "提交日期": "2021-05-17 15:06:33", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7525de4e45aee78963561a58b917d7a12fe60236", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-05-26 15:18:20", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "5ad81ed2d9f2881d64f1385b0a1e12fbc2fcf38f", "commit_訊息": "[流程引擎]A00-20210519001 流程設定「終止時逐級通知」，流程上的所有處理者都可以在系統通知中找到該筆流程通知", "提交日期": "2021-05-26 15:17:49", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "67185af202d555daf505aa3b6ef01152c767baad", "commit_訊息": "[ESS]S00-20210526001ESS表單執行報表列印，回傳簽核意見用CDATA包裝，讓ESS能解析該簽核意見內容", "提交日期": "2021-05-26 15:17:05", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/AppFormIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e102936a9726ef806ead51d7e788e6672663e87b", "commit_訊息": "[CRM]Q00-20210526002 修正BPM回傳CRM單據給CRM，經過cross中台就會報錯", "提交日期": "2021-05-26 13:54:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/cross/CrossInvokeServiceXMLUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b5f6e1eba3cc39eefcfe66c2242ccc1aabc2ca89", "commit_訊息": "[E10]C01-20210524004 修正E10表單同步如果修改Grid欄位名稱或欄位多語系未成功同步", "提交日期": "2021-05-24 18:04:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6836a2517846e2f0030fa2898289ab1d89fdeb06", "commit_訊息": "[Web]Q00-20210524001 修正佈景主題中，還原預設值按鈕的title只會以中文呈現", "提交日期": "2021-05-24 15:40:27", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0f122e5cffc3907ae07f66ebc11efdd0c97976b0", "commit_訊息": "[Web]A00-20210521001 修正撤銷流程與重啟服務驗證密碼的視窗一片空白", "提交日期": "2021-05-21 16:00:58", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "46d18f09134bc9c47a04ceb3904160bb1ed3887b", "commit_訊息": "[ISO]S00-20210519002 優化ISO閱讀浮水印支持文字換行功能(BCL8)", "提交日期": "2021-05-20 16:47:30", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "55ceaca447803767f33938e74644867deea1fcba", "commit_訊息": "[Web]A00-*********** 修正RWD表單透過產品Grid新增按鈕新增，若有欄位未繫結會導致無法發起流程", "提交日期": "2021-05-20 16:46:26", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "babdca678db32bcfca99727731bad301792fe8b9", "commit_訊息": "[流程引擎]Q00-20210520005 組織同步時，一併同步職務/職稱/角色的簡稱", "提交日期": "2021-05-20 15:30:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e94cb6486547d3d3d9f764af319de5e1923483d", "commit_訊息": "[Web]A00-20210518001 修正Script有formTerminateSave()要在終止流程前儲存表單，終止理由會無法儲存", "提交日期": "2021-05-20 11:39:54", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "094add2fffdabcb7428998b7ee8e1b6fd03b00d3", "commit_訊息": "[BPM APP]新增動態渲染表單顯示工作來源功能", "提交日期": "2021-05-19 17:37:45", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PostmarkContent.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2ef88a61c5718246a8690e64cf0ecb20e985ce70", "commit_訊息": "[TIPTOP]Q00-20210511004 修正TIPTOP拋單回傳給TIPTOP失敗，流程可發起成功[補修正]", "提交日期": "2021-05-19 17:19:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9092afa69e6f9cd1e47bee328a6f7d8dec10973b", "commit_訊息": "[Web]Q00-20210519005 修正IE在RWD中的時間元件開窗，表單會捲到最上方，要將捲軸捲回開窗前高度", "提交日期": "2021-05-19 14:35:19", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8417999eb5d480ec79ff5f837e0e78a056bc439a", "commit_訊息": "[組織設計師]S00-20210222001 組織設計師優化 取消父類別開啟時載入整個組織", "提交日期": "2021-05-19 11:35:57", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/AbstractTableDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainFunctionLevelDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ecd7f8a8e1cbbb82b707c3de5a95288481ca74dc", "commit_訊息": "[內部]Q00-20210519003 優化取組織接口getOrgDTO", "提交日期": "2021-05-19 11:11:25", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "31b630cce022ea124a3a5ed13ee414308a9733d3", "commit_訊息": "[流程引擎]Q00-20210518002 修正追蹤流程上的流程定義開窗可以使用簡體中文作為查詢條件", "提交日期": "2021-05-18 18:21:11", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3a3047d781ae6ffb78fa5c49701431181e3b5eb", "commit_訊息": "[流程引擎]A00-20210514003 樹狀開窗查詢功能可以使用中文作為查詢條件", "提交日期": "2021-05-18 14:14:07", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/TreeViewDataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "561a3df7e458c950a6038bff6fa70113ac556bf1", "commit_訊息": "A00-20210514001 修正組織同步會修改到User時，會讓lastUptPwdDate的值會被清空", "提交日期": "2021-05-17 16:51:57", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7336dd16617cacb8a730996dc4a4f93060cd14e", "commit_訊息": "[ISO]S00-20210121001 ISO浮水印新增字體顏色屬性(歸檔浮水印顏色僅支持BCL8以上)", "提交日期": "2021-05-14 14:44:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "456c69b46a9b74fa71d376a6d0b8da37f967ce0d", "commit_訊息": "[BPM APP]C01-20210512008 修正當使用Oracle資料庫時進入IMG的處理流程列表篩選流程名稱會顯示無資料問題", "提交日期": "2021-05-14 11:37:47", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3625326b1df3c1940de459d46fa6e75bf7031dc2", "commit_訊息": "[Web]A00-20210512001 表單設計師中若TextBox欄位有設定顯示小數點後幾位，在預覽表單時會多出現一個欄位", "提交日期": "2021-05-12 17:37:52", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ac698438cd92ff38de9cf17b63fb5c0bd5effee", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-05-12 13:46:20", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "daa274d9f6f5d94fd3a2b70d313e7ccdbfd291c6", "commit_訊息": "[TIPTOP]Q00-20210511004 修正TIPTOP拋單回傳給TIPTOP失敗，流程可發起成功", "提交日期": "2021-05-12 13:46:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5358372aaec50e0db8aa272743882a7ccc01311", "commit_訊息": "[BPM APP]C01-20210511002 修正當使用Oracle資料庫時要取得IMG的待辦、通知、轉派列表會顯示無資料問題", "提交日期": "2021-05-12 12:02:41", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8ead92b59d7a6653f6c741918ed0a138d0e98a3d", "commit_訊息": "[流程引擎]Q00-20210511002 調整在線閱讀下載檔案、壓浮水印、會導致系統IO未釋放的修正[補]", "提交日期": "2021-05-11 16:19:13", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "60828c75923c665c1454c0e3c2c49ace97a83ffd", "commit_訊息": "[流程引擎]Q00-20210511002 調整在線閱讀下載檔案、壓浮水印、會導致系統IO未釋放的修正", "提交日期": "2021-05-11 14:22:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/FileManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7b2265799da543de6c9f201efc768abaed1820d4", "commit_訊息": "[BPM APP]C01-20210506002 修正若客戶在BPM T100集成表單中加入自定義開窗元件，行動表單也可以正常派送流程", "提交日期": "2021-05-11 11:04:16", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "557905d5574f6c0e16aef51810d4e6517f37fc74", "commit_訊息": "[E10]調整E10整合表單在行動端IMG上(中間層、動態渲染表單)的呈現資訊(提示到電腦版或詳情表單查看)", "提交日期": "2021-05-10 18:42:00", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "96a812d3b8edaf23591b6bd06906bb3d7248b111", "commit_訊息": "[內部]Q00-20210510002增加TIPTOP單據有附件時，在建立流程後將附件新增至NoCmDocument時，新增到NoCmDocument的參數是否有問題", "提交日期": "2021-05-10 16:47:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0500454e52829decb25f3581ea400a4dc5962d42", "commit_訊息": "[流程引擎]A00-20210506001 修正流程判斷使用表單CheckBox元件失效問題", "提交日期": "2021-05-10 11:22:34", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/ConditionEvaluator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "575a303f304504d5a7a6f5603f0ce5e9440a5345", "commit_訊息": "[流程引擎]Q00-20210510001 自動儲存草稿功能調整為如果是ESS表單就不使用", "提交日期": "2021-05-10 11:07:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f3655fb17772ce571a6504401a4f49df8685f871", "commit_訊息": "[ESS]Q00-20210420003 修正ESS表單在手機版頁面會無法移動到最底部[補修正]", "提交日期": "2021-05-07 10:25:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1f512bca7950e8cb8406deb5f2ee3ffb73becff8", "commit_訊息": "[Web]A00-20210505001 修正流程名稱開窗查詢條件用開始包含或結束包含時，查詢後一片空白", "提交日期": "2021-05-05 18:48:47", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "06d6819ad008f434dff08013872bcb7e8d96ba02", "commit_訊息": "[內部]Q00-20210505002 增加Log印出表單Id與SQL內容在表單使用DataSource方式寫SQL時", "提交日期": "2021-05-05 17:24:00", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/data/DataServlet.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c39e6167ddbbc691087bf8d8f66dc84344b49597", "commit_訊息": "[Web]A00-20210503001 修正流程名稱開窗有重複資料的問題", "提交日期": "2021-05-05 15:23:45", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5c7b38a706b8db34fe799f038fe70b33f0e25b9c", "commit_訊息": "[內部]Q00-20210504003 移除重複的html元件「hdnDraftHeaderOID」", "提交日期": "2021-05-04 15:55:27", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "731c9bda97924a72f639261b25a1d81279277aa7", "commit_訊息": "[流程引擎]Q00-20210504002 Tiptop拋單，表單上的自定義開窗沒有呈現值", "提交日期": "2021-05-04 14:27:56", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4159c0d89a14f3f8318df9374840d16a6855998d", "commit_訊息": "[Web]Q00-20210504001 防止使用iPad點擊CheckBox時彈出鍵盤", "提交日期": "2021-05-04 14:08:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1e7339d6060d0d5bf357334c4b294fca58fcb2e3", "commit_訊息": "[內部]Q00-20210503002 新增T100取得BPM流程簽核歷程(ApproveLogGet)的log[補修正]", "提交日期": "2021-05-03 15:52:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d2e61c0684cad71f00aaca0a457cd58a01d18359", "commit_訊息": "[內部]Q00-20210503002 新增T100取得BPM流程簽核歷程(ApproveLogGet)的log", "提交日期": "2021-05-03 15:47:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "263a4094ce6ffbd89111d74a56474631e3715d7a", "commit_訊息": "[表單設計器]C01-20210428007調整表單設計器偶發拋\"TypeError:無法取得未定義或Null參考的屬性ElementGroup\"的議題", "提交日期": "2021-05-03 13:52:23", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c437efec8bf2faeec8fb58b37eee26d3790ee0b4", "commit_訊息": "[E10]Q00-20210429001 修正E10同步表單沒有同步更新子單身欄位", "提交日期": "2021-04-29 16:05:39", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b2cc02165e5a5a42c0f3238d9cc7d5326a44b0b", "commit_訊息": "[Web]Q00-20210428001 修正人員名稱有新的特殊字(𡒝)時，進入ESS流程會報錯", "提交日期": "2021-04-28 14:27:03", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7bde230c690f28e0c52b6923363b80072f27c697", "commit_訊息": "[Web]Q00-20210427006 使用者撤銷流程，理由填空白字串時不允許撤銷流程", "提交日期": "2021-04-27 18:05:26", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "55607050703f32d7b7fe9731332399b51da3927f", "commit_訊息": "[流程引擎]Q00-20210427005 修正搖旗吶喊小助手退回重辦與未處理的待辦事項數量顯示異常問題", "提交日期": "2021-04-27 16:21:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NotifierListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a1e956d124f2c754e2e5a2e30da0c57e72471c33", "commit_訊息": "[Web]Q00-20210427004 優化如果取登入圖片出現異常會有錯誤log並避免登入頁全白", "提交日期": "2021-04-27 15:41:31", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ee69e649c0070317f015f42a739026a8707e8e36", "commit_訊息": "[內部]Q00-20210427003 增加BPM回寫給TIPTOP的地方，增加呼叫CROSS中台的Response訊息", "提交日期": "2021-04-27 11:34:08", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/WebServiceForTiptop.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5e69130c4e6b824103de2512a40f963affa15a2e", "commit_訊息": "[Web]S00-20210414001 表單在附件上傳增加過濾高危險字符功能", "提交日期": "2021-04-27 09:02:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6c275862a0e11446c022796ae21d438d10d9f7ad", "commit_訊息": "[內部]Q00-20210426003 多AP主機狀況，在清除二階快取下，若AP呼叫失敗，增加log訊息讓錯誤更明確[補]", "提交日期": "2021-04-26 18:43:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ff3dae4d9685ae301c2fc3c38bcc7ea49c799d40", "commit_訊息": "[內部]Q00-20210426003 多AP主機狀況，在清除二階快取下，若AP呼叫失敗，增加log訊息讓錯誤更明確", "提交日期": "2021-04-26 18:39:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "18b5b811eaf070973a7913559ecfb047b557f4cd", "commit_訊息": "[內部]Q00-20210426002 調整活動逾時排程SQL，MSSQL加入WITH NOLOCK指令", "提交日期": "2021-04-26 18:21:18", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f153d57c2c09dcb3da894a63cc7517fcb251a2b", "commit_訊息": "[BPM APP]C01-20210422006 修正在入口平台整合設定在修改會有\"编辑失败:0\"的問題", "提交日期": "2021-04-26 18:14:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9b252e81175f43ab22c1e409a6d5460307442d58", "commit_訊息": "[流程引擎]Q00-20210303003 修正「活動逾時排程執行過程中發生資料庫鎖定」問題", "提交日期": "2021-04-26 15:05:46", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2facb3319022d68479156950f27608f8ec3cc889", "commit_訊息": "[流程引擎]Q00-20210426001 修正「流程設定流程撤銷事件，於多人關卡尚未處理時撤銷流程，發生資料庫鎖定(DB Lock)」問題", "提交日期": "2021-04-26 11:49:07", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "22d3dd04c621c598ebc4edf05e1a23894bd4f473", "commit_訊息": "[Web]Q00-20210423002 修正用任何語系登入，表單按鈕開窗用資料選取註冊器，Grid標籤跟模糊查詢標籤內容都只會顯示預設值", "提交日期": "2021-04-23 18:39:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9175bb5a81c93d881e26f93d4e11542f08371247", "commit_訊息": "[BPM APP]Q00-20210422003 修正移動端表單畫面在取回重辦後再次點取回重辦不會顯示關卡問題", "提交日期": "2021-04-23 17:27:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "deed59c79577ea23cf4373bc21ceaa1039996b74", "commit_訊息": "[Web]Q00-20210416002 修正點選常用流程後畫面會先閃一下回原畫面後才進入流程[補修正]", "提交日期": "2021-04-21 18:56:50", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "783f5d89b38c096ed178340f90d61480d3b398bb", "commit_訊息": "[ESS]Q00-20210420003 修正ESS表單在手機版頁面會無法移動到最底部[補修正]", "提交日期": "2021-04-21 17:34:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c2af9c4c4d9d1a4d166d66bdb995c6781487d3a5", "commit_訊息": "[BPM APP] Q00-20210420004 絕對位置表單中Grid綁定有設定顯示千分位的textBox，編輯修改完後，單身摘要無法依千分位做顯示", "提交日期": "2021-04-20 18:31:57", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be47cf1226b5807135b1a7b2e9ee47660c608926", "commit_訊息": "[ISO]S00-20210419004 優化PDF浮水印屬性管理旋轉角度，由0~180度調整為-180~180度", "提交日期": "2021-04-20 16:47:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1d9fece796771c044d78600e27f1ae8b0f045a70", "commit_訊息": "[ESS]Q00-20210420003 修正ESS表單在手機版頁面會無法移動到最底部", "提交日期": "2021-04-20 14:48:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6e80b8aa511a54a337d141e1a6ded5a6533295df", "commit_訊息": "[流程引擎]Q00-20210420002 若客戶在BPM T100集成表單中加入自定義開窗元件，在BPM裡也可以正常派送流程", "提交日期": "2021-04-20 14:38:11", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "780751298abb7ae02cef4fdeecd6973efd94113d", "commit_訊息": "[BPM APP] Q00-20210420001 修正響應式表單在相對位置的行動表單中，從Grid取值做加總時運算值異常的問題", "提交日期": "2021-04-20 14:29:54", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be85df77e1a5345e63bb52cafb6add4b613c0924", "commit_訊息": "[流程引擎]A00-20210413001 若關卡有設定自動簽核，則核決層級內有重複處理者的情況下會啟動自動簽核", "提交日期": "2021-04-20 14:16:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/BlockActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f1bd98aec22b50a10d804b2aaa8c35c45cb35cdc", "commit_訊息": "[BPM APP] C01-20210419007 修正絕對位置表單在相對位置的行動表單中，從Grid取值做加總時運算值異常的問題", "提交日期": "2021-04-20 11:24:06", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74854a425195fe58f33053f11f6fae8803e91397", "commit_訊息": "[BPM APP] Q00-20210401002修正textBox驗證必填時，有設定轉換文字&浮點數小數點後x位時，到簽核頁面欄位顯示空白問題", "提交日期": "2021-04-19 18:17:46", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}