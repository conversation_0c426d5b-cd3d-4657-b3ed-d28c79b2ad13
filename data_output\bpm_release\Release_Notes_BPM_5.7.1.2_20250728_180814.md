# Release Notes - BPM

## 版本資訊
- **新版本**: *******
- **舊版本**: *******
- **生成時間**: 2025-07-28 18:08:14
- **新增 Commit 數量**: 158

## 變更摘要

### jerry1218 (25 commits)

- **2018-04-30 11:16:45**: 畫面微調
  - 變更檔案: 4 個
- **2018-04-27 17:38:30**: 5712問題修正
  - 變更檔案: 10 個
- **2018-04-27 11:52:36**: 修正1.TOPTOP整合swing遇到trinity的異常 2.TIPTOP整合預設流程更新 3.模擬使用者小視窗scrollbar
  - 變更檔案: 3 個
- **2018-04-26 16:53:53**: 新增mCustomScrollbar-part4
  - 變更檔案: 23 個
- **2018-04-26 15:18:23**: 修正個人化首頁異常
  - 變更檔案: 1 個
- **2018-04-26 14:55:56**: 新增mCustomScrollbar套件-part3
  - 變更檔案: 23 個
- **2018-04-25 17:51:59**: 新增mCustomScrollbar-part2系統管理員
  - 變更檔案: 34 個
- **2018-04-25 15:40:54**: 移除bpm-bootstrap-util.js多餘alert
  - 變更檔案: 1 個
- **2018-04-25 14:43:36**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 1 個
- **2018-04-25 14:43:10**: 新增mCustomScrollbar-part1 一般使用者頁面
  - 變更檔案: 15 個
- **2018-04-23 18:00:43**: 修改-我的最愛-常用流程維護.常用功能維護2功能頁面重新設
  - 變更檔案: 2 個
- **2018-04-23 17:01:10**: 修正異常
  - 變更檔案: 2 個
- **2018-04-23 16:02:34**: 修改 - 簽核畫面(簽核意見)及追蹤畫面(撤銷理由)與上一行的icon合併
  - 變更檔案: 7 個
- **2018-04-20 17:03:29**: 發起流程拿掉關注功能導致的錯誤修正
  - 變更檔案: 1 個
- **2018-04-20 14:29:22**: 修改查詢條件section右邊按鈕padding , 單選窗頁面微調
  - 變更檔案: 20 個
- **2018-04-18 15:44:18**: 5712測試問題調整
  - 變更檔案: 9 個
- **2018-04-17 11:37:07**: 5712測試問題調整
  - 變更檔案: 5 個
- **2018-04-16 16:27:17**: 修正安裝密碼註冊格式正確但註冊失敗時 , 造成頁面空白問題
  - 變更檔案: 1 個
- **2018-04-16 11:51:57**: A00-***********.A00-*********** 修正單選開窗異常顯示搜尋區塊問題
  - 變更檔案: 1 個
- **2018-04-16 11:51:33**: A00-*********** 修正員工工作轉派作業，點選「批次轉派」跳出選擇接收者的視窗時，點擊「原處理者的預設代理人有:」會跳出請洽系統管理員的錯誤。
  - 變更檔案: 1 個
- **2018-04-12 18:27:13**: (A00-20180412001)修正模組程式維護作業，點選一筆程式資料，修改多語系名稱後，按下「修改」按鈕卻跳出「程式代號重複」的訊息錯誤 (A00-20180412002)修正模組程式維護作業，點選一筆程式資料，修改程式代號.名稱.URL含有&<>這幾個符號會替換為HTML碼的錯誤
  - 變更檔案: 1 個
- **2018-04-02 14:22:31**: Q00-20180330006 修正訊息中心點擊新系統通知時畫面loadding問題
  - 變更檔案: 1 個
- **2018-04-02 14:20:17**: Q00-20180402001 修正加簽關卡頁面，關卡名稱為填寫時未提式問題
  - 變更檔案: 3 個
- **2018-04-02 10:24:48**: Q00-20180330004 修正撤銷流程.工作通知.取回重瓣.追蹤流程.系統通知頁面模糊查詢使用單引號出現的頁面異常
  - 變更檔案: 5 個
- **2018-03-31 15:21:09**: Q00-20180330005 修正待辦清單主旨有單引號時導致的畫面顯示異常
  - 變更檔案: 1 個

### lorenchang (4 commits)

- **2018-04-30 10:36:58**: 移除ext module及相關參考
  - 變更檔案: 2 個
- **2018-04-27 09:48:08**: 將commons-logging.jar還原為原先版本
  - 變更檔案: 5 個
- **2018-04-25 19:01:59**: 修正匯入流程及update SQL更新SessionBean JNDI Name(Local)的異常
  - 變更檔案: 6 個
- **2018-04-25 16:49:00**: 更新SessionBeanApplication中T100邏輯專屬的JDNI Name
  - 變更檔案: 1 個

### yamiyeh10 (10 commits)

- **2018-04-29 00:37:16**: 修正議題 頁籤元件
  - 變更檔案: 18 個
- **2018-04-26 16:55:33**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-26 16:53:37**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-26 16:53:00**: 修正Password元件樣式跑版問題 修正水平線的class名稱
  - 變更檔案: 7 個
- **2018-04-25 17:01:59**: C01-20180416001 修正Grid在有特殊符號內容時會有符號轉換問題
  - 變更檔案: 1 個
- **2018-04-25 13:15:41**: 調整BPMAPP新模板樣式
  - 變更檔案: 18 個
- **2018-04-18 18:57:12**: 調整BPMAPP新模板樣式 1.新增 Title、水平線、doubletextbox元件模板 2.修正時間日期元件少了外層div 3.修正部分元件的樣式 補上InvokeServiceTool少了草稿方法
  - 變更檔案: 8 個
- **2018-04-17 08:58:38**: A00-20180409001 修正客製開窗在字數過多時會產生重疊問題
  - 變更檔案: 1 個
- **2018-04-11 11:06:10**: 修正表單畫面多語系問題 將jQuery的使用變成jBPM
  - 變更檔案: 10 個
- **2018-03-31 15:25:23**: Q00-20180330003 修正測試模擬使用者開窗異常議題
  - 變更檔案: 1 個

### ChinRong (20 commits)

- **2018-04-27 20:25:28**: 修正鼎捷移動改版後不會傳OpenId過來，更新MobieOAuthWeChatUser失敗的問題
  - 變更檔案: 1 個
- **2018-04-27 18:55:20**: 修正議題
  - 變更檔案: 6 個
- **2018-04-27 13:11:11**: 修正議題
  - 變更檔案: 2 個
- **2018-04-26 16:51:45**: 調整行動版表單設計器
  - 變更檔案: 6 個
- **2018-04-24 18:21:31**: Q00-20180416001 客製開窗服務會受PC端的服務影響，需獨立拉一隻新的class供行動端使用
  - 變更檔案: 27 個
- **2018-04-24 17:20:01**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-24 17:19:32**: 新增RWD行動版腳本編輯器
  - 變更檔案: 8 個
- **2018-04-24 11:53:20**: 修正設計器merge錯誤
  - 變更檔案: 1 個
- **2018-04-24 11:06:44**: 新增rwd行動版表單設計器
  - 變更檔案: 7 個
- **2018-04-23 16:55:53**: 移除測試的程式碼
  - 變更檔案: 1 個
- **2018-04-23 16:50:30**: 調整action新機制
  - 變更檔案: 6 個
- **2018-04-17 18:09:48**: 修正pdfjs議題中錯誤的程式碼
  - 變更檔案: 2 個
- **2018-04-17 17:51:48**: 行動表單Action新架構
  - 變更檔案: 28 個
- **2018-04-17 14:06:08**: 調整入口平台整合設定畫面
  - 變更檔案: 8 個
- **2018-04-16 10:08:46**: C01-*********** 微信帳號的搜索功能失效
  - 變更檔案: 1 個
- **2018-04-11 16:01:24**: 修正升版後統計元件的顏色還是彩色的問題
  - 變更檔案: 2 個
- **2018-04-10 17:23:00**: C01-*********** 微信帳號的搜索功能失效
  - 變更檔案: 7 個
- **2018-03-31 10:36:52**: Q00-*********** 修正入口平台頁面鼎捷使用者與微信使用者頁面取得的資料異常
  - 變更檔案: 7 個
- **2018-03-30 14:31:00**: A00-20180329002 新增行動版Grid reload方法
  - 變更檔案: 1 個
- **2018-03-29 18:05:08**: 修正上次議題修正時Merge錯誤
  - 變更檔案: 1 個

### 治傑 (7 commits)

- **2018-04-27 18:27:15**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-27 18:26:17**: 修正產品開窗在字數過多時會產生重疊問題
  - 變更檔案: 1 個
- **2018-04-26 15:21:37**: 調整RESTful服務說明
  - 變更檔案: 3 個
- **2018-04-25 19:18:17**: 調整RESTful服務說明
  - 變更檔案: 12 個
- **2018-04-25 12:51:16**: 新增流程、組織、表單、鼎捷移動RESTful服務說明
  - 變更檔案: 41 個
- **2018-04-18 11:16:00**: 修正BPM APP退回重辦時有預設模式,仍需點選退回模式才能退簽問題
  - 變更檔案: 2 個
- **2018-04-11 15:48:28**: 修正將流程設定為不支援行動版，在行動版的常用發起仍然可以看到該流程問題
  - 變更檔案: 1 個

### Gaspard (43 commits)

- **2018-04-27 17:27:13**: 修改「離職作業維護」、「個人資訊頁面」，加入自訂的scrollbar
  - 變更檔案: 10 個
- **2018-04-27 10:14:04**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-27 10:13:49**: 修改silderbar樣式從正方形變為圓形
  - 變更檔案: 1 個
- **2018-04-26 16:28:05**: 增加瀏覽器左上角的圖案顯示16x16
  - 變更檔案: 1 個
- **2018-04-26 11:40:30**: 修正「檢視參與者型式的關卡」頁面中，找不到跳過此關卡按鈕的異常
  - 變更檔案: 1 個
- **2018-04-26 11:09:15**: C01-20180330002 修正儲存表單後，表單大小恢復成預設值的異常
  - 變更檔案: 1 個
- **2018-04-26 09:15:23**: 更新流程的預設圖示
  - 變更檔案: 1 個
- **2018-04-25 16:59:09**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-25 16:58:52**: 修正無法將未讀取的待辦標粗體的異常
  - 變更檔案: 1 個
- **2018-04-25 16:37:21**: 調配大中小字體切換時，最適化Grid內顯示兩行的高度與line-height
  - 變更檔案: 1 個
- **2018-04-25 16:36:15**: 詳細流程資訊的開窗從原本800增加到960
  - 變更檔案: 1 個
- **2018-04-25 16:34:27**: 修改預設CHROME開啟bpm時，視窗高度縮小約window工具列的高度
  - 變更檔案: 1 個
- **2018-04-25 14:42:15**: 修正改變系統文字大小時，最小型的字體不會同步生效
  - 變更檔案: 1 個
- **2018-04-25 14:41:31**: 修正主旨超過30字時直接切字串的做法，改用超過兩行就隱藏，避免主旨有HTML CODE的可能性
  - 變更檔案: 1 個
- **2018-04-25 13:41:53**: 增加__jQuery取代成jBPM的SQL語法
  - 變更檔案: 2 個
- **2018-04-25 11:13:04**: 修正Grid內包table時，內層的table會吃到外層Grid的CSS屬性之議題
  - 變更檔案: 4 個
- **2018-04-25 11:11:46**: 修正查詢樣板，若Grid再包Table時，index計算不正確
  - 變更檔案: 1 個
- **2018-04-25 11:10:43**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-25 11:10:26**: 修正表單中的隱藏元件若於流程設計器中又設定隱藏，造成無法開啟表單的異常
  - 變更檔案: 1 個
- **2018-04-24 17:53:54**: 增加文字式菜單的佈景主題功能
  - 變更檔案: 9 個
- **2018-04-24 10:40:13**: 修改ScrollBar樣式
  - 變更檔案: 4 個
- **2018-04-24 08:48:50**: 修改發起流程時的預設圖示
  - 變更檔案: 1 個
- **2018-04-23 17:01:58**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-23 17:00:41**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-23 17:00:24**: 修改追蹤、待辦、發起流程頁面左右比例為2:10
  - 變更檔案: 4 個
- **2018-04-20 17:46:09**: 產品統一使用微軟雅黑
  - 變更檔案: 1 個
- **2018-04-20 17:33:44**: 頁面文字統一樣式
  - 變更檔案: 8 個
- **2018-04-20 17:16:19**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-20 17:16:03**: 修改bpm-panel下的收合按鈕統一靠右15px
  - 變更檔案: 1 個
- **2018-04-20 16:33:05**: 清單頁的操作按鈕統一收合到「更多」按鈕中
  - 變更檔案: 8 個
- **2018-04-20 16:10:17**: 改變發起流程預設圖示
  - 變更檔案: 1 個
- **2018-04-20 15:49:25**: 修改DialogInputLabel元件的篩選條件使其可使用動態變數查詢
  - 變更檔案: 3 個
- **2018-04-18 04:38:38**: 修正追蹤流程頁面有設定RWD表單顯示比例時，失效的議題
  - 變更檔案: 1 個
- **2018-04-18 04:15:51**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-18 04:15:22**: 修正無法開啟流程樹
  - 變更檔案: 1 個
- **2018-04-17 18:06:52**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-17 18:06:28**: 測試議題修正
  - 變更檔案: 9 個
- **2018-04-16 18:07:32**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-16 18:06:55**: 測試議題修正
  - 變更檔案: 12 個
- **2018-04-16 09:36:23**: 修正全系統ICON統一用font-awesome產生，並將元件加入圓角效果
  - 變更檔案: 34 個
- **2018-04-03 14:54:08**: 修正加入Grid控制項無法使用的異常
  - 變更檔案: 1 個
- **2018-03-30 17:01:34**: 修正無法設定關注條件的異常
  - 變更檔案: 1 個
- **2018-03-30 16:58:53**: 移除多餘的測試程式
  - 變更檔案: 1 個

### jd (8 commits)

- **2018-04-26 17:06:19**: 修正Title和Image無法自動調整大小問題
  - 變更檔案: 2 個
- **2018-04-26 15:08:12**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-26 15:07:44**: 修正DoubleTextBox問題
  - 變更檔案: 3 個
- **2018-04-19 17:17:42**: 1.新增水平線、標題的App表單產生架構
  - 變更檔案: 3 個
- **2018-04-19 16:35:13**: 1.新增DoubleTextBox中間層顯示 2.新增中間層多語系顯示
  - 變更檔案: 3 個
- **2018-04-19 14:40:02**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 3 個
- **2018-04-17 11:32:06**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 1 個
- **2018-04-17 11:26:22**: 1.新增RESTful API說明機制 ,RWD表單轉換機制
  - 變更檔案: 18 個

### pinchi_lin (12 commits)

- **2018-04-25 20:16:07**: 調整表單設計師的title和水平線元件不可以加入中間層
  - 變更檔案: 4 個
- **2018-04-24 17:18:25**: 增加新元件樣式到RWD表單設計師中的行動版設計中
  - 變更檔案: 2 個
- **2018-04-23 17:43:11**: 修正BPMAPP新樣板若為隱藏元件時會產生內容錯誤問題
  - 變更檔案: 2 個
- **2018-04-19 19:36:56**: 修正RWD表單自動轉換BPMAPP表單問題
  - 變更檔案: 1 個
- **2018-04-19 17:42:39**: 修正merge錯誤
  - 變更檔案: 1 個
- **2018-04-18 14:45:36**: 1.調整判斷機制,調整PHONE_RESPONSIVE為RWD_PC,原先RWD則繼續沿用 2.調整表單生成機制,支持自動轉換功能
  - 變更檔案: 8 個
- **2018-04-18 14:45:36**: 調整BPMAPP新模板
  - 變更檔案: 3 個
- **2018-04-17 14:47:15**: 新增BPMAPP新模板遺漏的圖片元件
  - 變更檔案: 2 個
- **2018-04-17 11:13:05**: 新增BPMAPP解析RWD表單
  - 變更檔案: 1 個
- **2018-04-12 17:29:13**: 調整BPMAPP新模板樣式
  - 變更檔案: 14 個
- **2018-04-12 16:15:20**: C01-20180409002 修正BPMAPP向後加簽多語系錯誤
  - 變更檔案: 2 個
- **2018-03-30 14:06:07**: A00-20180320001 修正TT流程再中間層簽核時會派送失敗問題
  - 變更檔案: 1 個

### 顏伸儒 (3 commits)

- **2018-04-25 10:07:54**: 新增HTML標籤轉回普通文字和普通文字轉回HTML標籤的方法。
  - 變更檔案: 1 個
- **2018-04-25 10:04:43**: [C01-20180301001]修正BPM57版本,TipTop發單到BPM,附件無寫入NoCmDocument導致無法開啟的問題。
  - 變更檔案: 1 個
- **2018-04-16 17:49:11**: [A00-***********]修正流程簽核掛外部連結畫面,使用Chrome瀏覽器無法正常派送流程的問題。
  - 變更檔案: 1 個

### Loren (17 commits)

- **2018-04-25 09:29:36**: 因struts-manageSystemConfig-config.xml檔案異常造成部署失敗，重建該檔案
  - 變更檔案: 1 個
- **2018-04-25 09:28:55**: 因struts-manageSystemConfig-config.xml檔案異常造成部署失敗，故先移除
  - 變更檔案: 1 個
- **2018-04-25 08:46:50**: 將nana-app依賴的module還原為ear內的lib，解決使用module造成的WebService異常
  - 變更檔案: 162 個
- **2018-04-18 17:30:28**: 修正WildFly上呼叫APP的action出現NoClassDefFoundError的異常
  - 變更檔案: 1 個
- **2018-04-18 17:00:33**: 修正WildFly上呼叫APP的action出現NoClassDefFoundError的異常
  - 變更檔案: 1 個
- **2018-04-17 17:36:22**: 修正WEB表單設計師絕對定位表單DB Connection元件JSONObject取值異常
  - 變更檔案: 1 個
- **2018-04-17 11:58:52**: 加入jboss-all.xml讓部署順序為nana-app.ear>NaNaWeb.war
  - 變更檔案: 1 個
- **2018-04-16 22:46:37**: 調整NaNaWeb.war及nana-app.ear取得產品資訊的方式
  - 變更檔案: 4 個
- **2018-04-16 18:02:22**: 移除TrinityServieEJB.jar及調用的程式區塊
  - 變更檔案: 6 個
- **2018-04-15 23:37:26**: 移除global-modules
  - 變更檔案: 4 個
- **2018-04-13 17:56:27**: 修正未知的差異：<?導致無法正確部署的問題
  - 變更檔案: 1 個
- **2018-04-13 17:23:13**: 更新Patch為5712
  - 變更檔案: 1 個
- **2018-04-13 16:37:42**: UI PLUS版本Merge至v57
  - 變更檔案: 231 個
- **2018-04-13 01:46:25**: 將nana-app.ear參考的lib改為Module，調整開發環境及修正數項異常
  - 變更檔案: 322 個
- **2018-04-02 15:17:24**: 更新XX:MaxMetaspaceSize=512m，避免版更很容易就出現Metaspace不足的錯誤
  - 變更檔案: 2 個
- **2018-04-02 14:47:32**: 修正部署在Linux時，引用到JGo會產生異常
  - 變更檔案: 2 個
- **2018-03-31 09:00:44**: 更新打包程式JAVA_HOME路徑
  - 變更檔案: 1 個

### 施廷緯 (2 commits)

- **2018-04-23 20:59:01**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-04-23 20:58:24**: 修改調閱ISO文件瀏覽開窗權限判斷
  - 變更檔案: 1 個

### 張詠威 (7 commits)

- **2018-04-12 18:18:03**: A00-20180412009 協助commit 修正在退回重辦需要逐級通知的前提下當流程關卡包含核決關卡
  - 變更檔案: 1 個
- **2018-04-10 16:52:36**: 修正開窗無法使用(重複命名method問題)，故先移除原本轉呼叫的code及 移除 限制撈取500筆的code
  - 變更檔案: 1 個
- **2018-04-10 16:39:09**: Q00-20180410001 修正文件總管新增文件異常 及 ISO檔案無法下載議題
  - 變更檔案: 4 個
- **2018-04-02 18:45:38**: 調整RESTFul開窗的參數值
  - 變更檔案: 1 個
- **2018-04-02 18:28:13**: 增加RESTFul開窗允許自帶參數
  - 變更檔案: 3 個
- **2018-03-30 14:42:25**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-03-30 14:41:43**: 客製開窗增加RESTFul功能
  - 變更檔案: 4 個

## 詳細變更記錄

### 1. 畫面微調
- **Commit ID**: `8359843545086af491e1d5ddb02991e088f0e52e`
- **作者**: jerry1218
- **日期**: 2018-04-30 11:16:45
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 2. 移除ext module及相關參考
- **Commit ID**: `f442a53a749bc16d62a8085f7a3df265fdcee367`
- **作者**: lorenchang
- **日期**: 2018-04-30 10:36:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/ext/main/module.xml`

### 3. 修正議題 頁籤元件
- **Commit ID**: `e63dd82d8fddda91a0b087ab1d9a1a52ffa324bc`
- **作者**: yamiyeh10
- **日期**: 2018-04-29 00:37:16
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/FormContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/FormElementUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 4. 修正鼎捷移動改版後不會傳OpenId過來，更新MobieOAuthWeChatUser失敗的問題
- **Commit ID**: `e0c9a64a700f0dd9d5f971c878f126a89a78ca41`
- **作者**: ChinRong
- **日期**: 2018-04-27 20:25:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 5. 修正議題
- **Commit ID**: `ff41c403ef72b413de5ab10e7cd059aca6ea79ed`
- **作者**: ChinRong
- **日期**: 2018-04-27 18:55:20
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`

### 6. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `78c4cf1bc8a6434b71005e01df5a8252d0ac1e1a`
- **作者**: 治傑
- **日期**: 2018-04-27 18:27:15
- **變更檔案數量**: 0

### 7. 修正產品開窗在字數過多時會產生重疊問題
- **Commit ID**: `6945716d18451e1b5b6833a92ead428bf90c28f0`
- **作者**: 治傑
- **日期**: 2018-04-27 18:26:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js`

### 8. 5712問題修正
- **Commit ID**: `c941392bb96c9a35be81942d3b77757c34c1a969`
- **作者**: jerry1218
- **日期**: 2018-04-27 17:38:30
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AbortProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`

### 9. 修改「離職作業維護」、「個人資訊頁面」，加入自訂的scrollbar
- **Commit ID**: `ce52b74546b07b28ce0d2b87489ab7807f00beda`
- **作者**: Gaspard
- **日期**: 2018-04-27 17:27:13
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 10. 修正議題
- **Commit ID**: `fb7adce29355c748d55eb4cb79195176dfaf7f6b`
- **作者**: ChinRong
- **日期**: 2018-04-27 13:11:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 11. 修正1.TOPTOP整合swing遇到trinity的異常 2.TIPTOP整合預設流程更新 3.模擬使用者小視窗scrollbar
- **Commit ID**: `be9398a791839bc1e1db8b136d3973042bfaf3c0`
- **作者**: jerry1218
- **日期**: 2018-04-27 11:52:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/src/com/dsc/nana/user_interface/apps/syscfg/view/trina/TrinityMainPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/bpmn/5.25/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.bpmn"`

### 12. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `39211d5208a7e6a8a95119082922e68f51348c61`
- **作者**: Gaspard
- **日期**: 2018-04-27 10:14:04
- **變更檔案數量**: 0

### 13. 修改silderbar樣式從正方形變為圓形
- **Commit ID**: `e957ec703a4c57988ee06655e72e4364d54dabe1`
- **作者**: Gaspard
- **日期**: 2018-04-27 10:13:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 14. 將commons-logging.jar還原為原先版本
- **Commit ID**: `2506b9856d8e28607acd986badf10212d4248b2c`
- **作者**: lorenchang
- **日期**: 2018-04-27 09:48:08
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-logging-jboss-logmanager-1.0.2.Final.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-logging.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/lib/JakartaCommons/commons-logging-jboss-logmanager-1.0.2.Final.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JakartaCommons/commons-logging.jar`

### 15. 修正Title和Image無法自動調整大小問題
- **Commit ID**: `3c9663d6c3a7596c79096a4b181ea00d5707d8db`
- **作者**: jd
- **日期**: 2018-04-26 17:06:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 16. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `1db3029196cf5500c9f15c93bf1bd7452f4c8430`
- **作者**: yamiyeh10
- **日期**: 2018-04-26 16:55:33
- **變更檔案數量**: 0

### 17. 新增mCustomScrollbar-part4
- **Commit ID**: `40b6a7d175b3c4204790b1fc73b756ff1f7490a5`
- **作者**: jerry1218
- **日期**: 2018-04-26 16:53:53
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/AttachmentExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/ButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DialogExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/RadioButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TextboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TimeExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Index.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 18. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `6960e83da5276199b4937a686fe8ccb331774617`
- **作者**: yamiyeh10
- **日期**: 2018-04-26 16:53:37
- **變更檔案數量**: 0

### 19. 修正Password元件樣式跑版問題 修正水平線的class名稱
- **Commit ID**: `92eac7e7ef904ac87446a74584a214b423f905cb`
- **作者**: yamiyeh10
- **日期**: 2018-04-26 16:53:00
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 20. 調整行動版表單設計器
- **Commit ID**: `2f8c92ac79f7fc9ba1e9cb6898cc48a9fd9d11c7`
- **作者**: ChinRong
- **日期**: 2018-04-26 16:51:45
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5712.xls`

### 21. 增加瀏覽器左上角的圖案顯示16x16
- **Commit ID**: `d8aaee14cff84cdbbeef8399914c9398b0c01429`
- **作者**: Gaspard
- **日期**: 2018-04-26 16:28:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/welcome-content/favicon.ico`

### 22. 調整RESTful服務說明
- **Commit ID**: `ef7c2996a83b079ede5fa3a0f1b366bb7b98a262`
- **作者**: 治傑
- **日期**: 2018-04-26 15:21:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystemV2.java`

### 23. 修正個人化首頁異常
- **Commit ID**: `b2a1ac0f5d5da3619eb04d1038f360166a5bfda6`
- **作者**: jerry1218
- **日期**: 2018-04-26 15:18:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 24. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `aca787f04b097a46d6093ca47027f8aa8a5bf110`
- **作者**: jd
- **日期**: 2018-04-26 15:08:12
- **變更檔案數量**: 0

### 25. 修正DoubleTextBox問題
- **Commit ID**: `a25c5fccaf9c6e7fedeb3d3dbd522e705796f804`
- **作者**: jd
- **日期**: 2018-04-26 15:07:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`

### 26. 新增mCustomScrollbar套件-part3
- **Commit ID**: `927bf3b4ef3afb07f86c3ba3cffc559a03a5051f`
- **作者**: jerry1218
- **日期**: 2018-04-26 14:55:56
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/doResize.js`

### 27. 修正「檢視參與者型式的關卡」頁面中，找不到跳過此關卡按鈕的異常
- **Commit ID**: `c521a329480e4e0094b4f85a22cae54d82867e8e`
- **作者**: Gaspard
- **日期**: 2018-04-26 11:40:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 28. C01-20180330002 修正儲存表單後，表單大小恢復成預設值的異常
- **Commit ID**: `1e8279ccdbe1f75d07888179b5ee077e1302db09`
- **作者**: Gaspard
- **日期**: 2018-04-26 11:09:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`

### 29. 更新流程的預設圖示
- **Commit ID**: `14bd3b1a3750a2523b7fbb9e81a51ada8cff01a6`
- **作者**: Gaspard
- **日期**: 2018-04-26 09:15:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/process.png`

### 30. 調整表單設計師的title和水平線元件不可以加入中間層
- **Commit ID**: `d7388fad6ea905e1a44cd2704a95f270bdb23b5e`
- **作者**: pinchi_lin
- **日期**: 2018-04-25 20:16:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/horizontalLineTemp.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/titleTemp.png`

### 31. 調整RESTful服務說明
- **Commit ID**: `292193721e0d05a5be1a8441f3a841fd8a8eafc7`
- **作者**: 治傑
- **日期**: 2018-04-25 19:18:17
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/FormV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileFormV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 32. 修正匯入流程及update SQL更新SessionBean JNDI Name(Local)的異常
- **Commit ID**: `806160eafdd41f3b1790e92a70799475da9e1ceb`
- **作者**: lorenchang
- **日期**: 2018-04-25 19:01:59
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/action/OpenFromXMLAction.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/action/OpenFromXMLAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 33. 新增mCustomScrollbar-part2系統管理員
- **Commit ID**: `79549c5f6678a90b1f3440017a7a594cbfb69ead`
- **作者**: jerry1218
- **日期**: 2018-04-25 17:51:59
- **變更檔案數量**: 34
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/CreateProcessModule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/ManageProcessModuleMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/doResize.js`

### 34. C01-20180416001 修正Grid在有特殊符號內容時會有符號轉換問題
- **Commit ID**: `97bb25a7e0bb05c51212d5f375b25505967fe259`
- **作者**: yamiyeh10
- **日期**: 2018-04-25 17:01:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 35. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `d8f3fc83d7dc1cb8d41124b5038aad83ed4baac1`
- **作者**: Gaspard
- **日期**: 2018-04-25 16:59:09
- **變更檔案數量**: 0

### 36. 修正無法將未讀取的待辦標粗體的異常
- **Commit ID**: `ad09c92bc554e371e16c649bdc85538fe77419f9`
- **作者**: Gaspard
- **日期**: 2018-04-25 16:58:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 37. 更新SessionBeanApplication中T100邏輯專屬的JDNI Name
- **Commit ID**: `af371558750c800ab40eb0c075da64e678aca0da`
- **作者**: lorenchang
- **日期**: 2018-04-25 16:49:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/SessionBeanToolAgent.java`

### 38. 調配大中小字體切換時，最適化Grid內顯示兩行的高度與line-height
- **Commit ID**: `58e6d2466ff80ada46c006ee669106445ee5fefe`
- **作者**: Gaspard
- **日期**: 2018-04-25 16:37:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 39. 詳細流程資訊的開窗從原本800增加到960
- **Commit ID**: `c0e99d2a5cd4b0e57558a0b0282bdb436ab8c9f5`
- **作者**: Gaspard
- **日期**: 2018-04-25 16:36:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`

### 40. 修改預設CHROME開啟bpm時，視窗高度縮小約window工具列的高度
- **Commit ID**: `5fc4bed64da54ff32705fb157bba043663b465c0`
- **作者**: Gaspard
- **日期**: 2018-04-25 16:34:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Index.jsp`

### 41. 移除bpm-bootstrap-util.js多餘alert
- **Commit ID**: `c60beb3535afbf923c2fdc1728a8fdde4a0b5f4a`
- **作者**: jerry1218
- **日期**: 2018-04-25 15:40:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 42. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `371a1240eb502a8e6380c3983878fa91322ee2a3`
- **作者**: jerry1218
- **日期**: 2018-04-25 14:43:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 43. 新增mCustomScrollbar-part1 一般使用者頁面
- **Commit ID**: `8b12eacf177793cfda7d4d0b4e55142065584253`
- **作者**: jerry1218
- **日期**: 2018-04-25 14:43:10
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 44. 修正改變系統文字大小時，最小型的字體不會同步生效
- **Commit ID**: `2f131fe5734cc6aef3110f4f681f42a72e1ad484`
- **作者**: Gaspard
- **日期**: 2018-04-25 14:42:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 45. 修正主旨超過30字時直接切字串的做法，改用超過兩行就隱藏，避免主旨有HTML CODE的可能性
- **Commit ID**: `32a574e69e4b55f4ef67f9cac3feaf4ee5659a2b`
- **作者**: Gaspard
- **日期**: 2018-04-25 14:41:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 46. 增加__jQuery取代成jBPM的SQL語法
- **Commit ID**: `2acfa51dbebd2e9de425d2288a20e1b0e725d94c`
- **作者**: Gaspard
- **日期**: 2018-04-25 13:41:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 47. 調整BPMAPP新模板樣式
- **Commit ID**: `c3544c11e66596277e811ac12d7087d0b1011b23`
- **作者**: yamiyeh10
- **日期**: 2018-04-25 13:15:41
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HorizontalLineElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/FormElementUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 48. 新增流程、組織、表單、鼎捷移動RESTful服務說明
- **Commit ID**: `bd17aa40a0a83ca47f7268adcf200898fefb3094`
- **作者**: 治傑
- **日期**: 2018-04-25 12:51:16
- **變更檔案數量**: 41
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AcceptWorkItemBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ActivityDefinitionForClientListBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AddCustomActivityBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkItemBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CompleteWorkStepBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/CriticalBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ElementBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchProcessCommentsBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchReexecuteActivityBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchWorkStepsBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ListReaderBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ReexecuteActivityBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/TerminateProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/UserFormValueBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/XmlBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/AttachmentBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/BuildFormBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/DeleteFileBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/DownloadFileBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeFavoriteReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeWorkItemReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/NoticeProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/RollbackActivityBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SaveFormBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SearchProcessListBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/TraceProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/UploadFileBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/UploadFileInfoBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/FormV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileFormV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobileClient/UserClientBean.java`

### 49. 修正Grid內包table時，內層的table會吃到外層Grid的CSS屬性之議題
- **Commit ID**: `22785d3ffc78ccf9cdff7bcb9906d09d6b1493fb`
- **作者**: Gaspard
- **日期**: 2018-04-25 11:13:04
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 50. 修正查詢樣板，若Grid再包Table時，index計算不正確
- **Commit ID**: `e06c34792cef250c8b5aa1fbd1c385c6d983fc88`
- **作者**: Gaspard
- **日期**: 2018-04-25 11:11:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 51. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a1a3306a1d1b1292a54e474beaa9727b6e000315`
- **作者**: Gaspard
- **日期**: 2018-04-25 11:10:43
- **變更檔案數量**: 0

### 52. 修正表單中的隱藏元件若於流程設計器中又設定隱藏，造成無法開啟表單的異常
- **Commit ID**: `6646b13929d0ee7e06a85adc1edf74d46b71df61`
- **作者**: Gaspard
- **日期**: 2018-04-25 11:10:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 53. 新增HTML標籤轉回普通文字和普通文字轉回HTML標籤的方法。
- **Commit ID**: `7c034ba4f994d992fb3cbcaadddcd0bbbfceddd1`
- **作者**: 顏伸儒
- **日期**: 2018-04-25 10:07:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 54. [C01-20180301001]修正BPM57版本,TipTop發單到BPM,附件無寫入NoCmDocument導致無法開啟的問題。
- **Commit ID**: `7f7fe6dc7f3e03da0bc815ccbe06e94b8bf99347`
- **作者**: 顏伸儒
- **日期**: 2018-04-25 10:04:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 55. 因struts-manageSystemConfig-config.xml檔案異常造成部署失敗，重建該檔案
- **Commit ID**: `aee163e6e18baafa1b6b6ca75f61e658760611b8`
- **作者**: Loren
- **日期**: 2018-04-25 09:29:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml`

### 56. 因struts-manageSystemConfig-config.xml檔案異常造成部署失敗，故先移除
- **Commit ID**: `a31c0f31765e94677a5104a655e1d4996bddc426`
- **作者**: Loren
- **日期**: 2018-04-25 09:28:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml`

### 57. 將nana-app依賴的module還原為ear內的lib，解決使用module造成的WebService異常
- **Commit ID**: `e69b1cac8f64c4fbea2dd5db3779b89ee7964356`
- **作者**: Loren
- **日期**: 2018-04-25 08:46:50
- **變更檔案數量**: 162
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/properties.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/dto/.settings/org.eclipse.core.resources.prefs`
  - 📝 **修改**: `3.Implementation/subproject/dto/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/dto/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/persistence/.settings/org.eclipse.core.resources.prefs`
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Axis/axis.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Axis/commons-discovery.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Axis/jaxrpc.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Axis/saaj.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Axis/wsdl4j.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/BouncyCastle/bcprov-jdk15on-1.56.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Bsf/bsf.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Bsf/bsh.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Bsf/js.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Cactus/aspectjrt-1.5.3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Cactus/cactus-1.8.1.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Cactus/cactus-ant.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Cactus/commons-httpclient.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Cactus/commons-logging.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Castor/castor-xml.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Castor/castor.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Comm/ASIM.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/CrossIntgmsg/cross-intgmsg-api-fordsc-1.0.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/CrossIntgmsg/cross-intgmsg-api-fordsc-1.3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Dom4J/dom4j-1.6.1-changed_serialization.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/ETL/jaxb-api.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/ETL/jaxb-impl.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/ETL/jsr173_1.0_api.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/ETL/scriptella.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Hibernate/antlr.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Hibernate/asm.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Hibernate/cglib.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Hibernate/ehcache.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Hibernate/hibernate.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/DBF_JDBC30.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/Merlia.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/db2java.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/db2jcc.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/db2jcc_license_cu.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/ifxjdbc.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/jconn3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/jt400.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/jtds.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/mysql-connector-java.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/ojdbc6.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/postgresql_jdbc3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDBCDrivers/sqljdbc.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JDiagram/JDiagram-4.1.4.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JUnit/junit.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-beanutils.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-codec.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-collections.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-dbcp.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-digester.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-httpclient.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-io.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-lang.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-logging-jboss-logmanager-1.0.2.Final.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-pool.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/which.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaOJB/antlr.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaOJB/db-ojb.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JavaMail/activation.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JavaMail/mail.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Jaxen/jaxen.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Json/json.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Jython/jython.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Log4J/log4j.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/MimeType.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/PDFBox-0.7.2.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/Tidy.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/easypdf-jacob.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/jacob.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/jaxen-core.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/jaxen-jdom.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/jdom.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/jxl.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/lius-lucene.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/lucene-core-2.1.0.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/lucene-highlighter-2.1.0.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/poi-3.12-20150511.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/poi-examples-3.12-20150511.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/poi-excelant-3.12-20150511.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/poi-ooxml-3.12-20150511.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/poi-ooxml-schemas-3.12-20150511.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/poi-scratchpad-3.12-20150511.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/saxpath.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Lucene/xmlbeans-2.6.0.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Msv/isorelax.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Msv/msv.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Msv/relaxngDatatype.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Msv/xmlgen.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Msv/xsdlib.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/PDF/Jbepprint.dll`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/PDF/Jbepprint.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/PDF/Jbepproc.dll`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/PDF/Jbepproc.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/PDF/easypdf-jacob.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/PDF/ezjcom18.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/PDF/jacob.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/PatchRunner/DsPatch.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Poi/poi-3.2-FINAL-20081019.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Sap/sapjco3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Sdo/sdo2_1.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Security/iFjTwcaSsClntLib.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Security/iFjTwcaSsClntRequiredAll.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Security/jsse.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Servlet/servlet.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Spring/spring.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/T100/t100-rmi-service-client.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TestingUtility/testing-utility.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Trinity/TrinityServiceEJB.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/PAPIS.api`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/PapisBridge.dll`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/commons-dbcp-1.1.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/commons-dbcp-1.2.1.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/commons-pool-1.1.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/commons-pool-1.2.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/jacob.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/jakarta-regexp-1.3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/jsafe.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/tv40.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/xercesImpl.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/xml-apis.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/TrustView/xmlParserAPIs.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Tyrex/tyrex.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Weblogic/ClientLibrary/wlclient-10.0.1.0.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/WildFly/jboss-client.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/XStream/xpp3_min.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/XStream/xstream.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Xalan/xalan.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Xerces/resolver.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Xerces/serializer.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Xerces/xercesImpl.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Xerces/xml-apis.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/asm-3.1.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/asm-attrs-1.5.3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/cglib-nodep-2.1_3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/commons-digester.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/groovy-all-1.7.5.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/iTextAsian.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/itext-2.1.7.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/jasperreports-4.1.3.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/iReport/jasperreports-htmlcomponent-4.1.1.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/ldap/ldap.jar`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/utils-0.1.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/BpmInfoSingleton.java`
  - ❌ **刪除**: `3.Implementation/subproject/system/.settings/org.eclipse.core.resources.prefs`
  - 📝 **修改**: `3.Implementation/subproject/system/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/system/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebInfoFetcher.java`
  - ❌ **刪除**: `3.Implementation/targets-service.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/ext/main/module.xml`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/module.xml`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/service/main/module.xml`

### 58. Q00-20180416001 客製開窗服務會受PC端的服務影響，需獨立拉一隻新的class供行動端使用
- **Commit ID**: `5e738deffa664ee8aaac12a217ea0f1871c95038`
- **作者**: ChinRong
- **日期**: 2018-04-24 18:21:31
- **變更檔案數量**: 27
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileDatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmApp.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileIntegrate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 59. 增加文字式菜單的佈景主題功能
- **Commit ID**: `457033f8fb5d8263b298d92a308293b0c20bb347`
- **作者**: Gaspard
- **日期**: 2018-04-24 17:53:54
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5712.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 60. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `040e06b36653799944b26468ba65d5a82f74286d`
- **作者**: ChinRong
- **日期**: 2018-04-24 17:20:01
- **變更檔案數量**: 0

### 61. 新增RWD行動版腳本編輯器
- **Commit ID**: `ec962b753ffeae2cdadacfb6ad6d195b4a80f5b4`
- **作者**: ChinRong
- **日期**: 2018-04-24 17:19:32
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-undoManager.js`

### 62. 增加新元件樣式到RWD表單設計師中的行動版設計中
- **Commit ID**: `7ec6fd4af1cb4f17680654347746117c5dfebcf7`
- **作者**: pinchi_lin
- **日期**: 2018-04-24 17:18:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css`

### 63. 修正設計器merge錯誤
- **Commit ID**: `e21f9647f4037699a7a3aeb8b571b65af51d08b4`
- **作者**: ChinRong
- **日期**: 2018-04-24 11:53:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 64. 新增rwd行動版表單設計器
- **Commit ID**: `af0beec053fd39db19166121e0becdbeee9d072b`
- **作者**: ChinRong
- **日期**: 2018-04-24 11:06:44
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileFormScriptEditor.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 65. 修改ScrollBar樣式
- **Commit ID**: `e80e39a4bdfaab0c3d0af01553349666dca0f69c`
- **作者**: Gaspard
- **日期**: 2018-04-24 10:40:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomCssLib/bpm-global-style-custom.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-global-style-template.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 66. 修改發起流程時的預設圖示
- **Commit ID**: `abd49f05ab710057c178fd8276fcbaefea841d34`
- **作者**: Gaspard
- **日期**: 2018-04-24 08:48:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/process.png`

### 67. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `ebb2ef311b0e810889519e041818c6ef8fbe0d6a`
- **作者**: 施廷緯
- **日期**: 2018-04-23 20:59:01
- **變更檔案數量**: 0

### 68. 修改調閱ISO文件瀏覽開窗權限判斷
- **Commit ID**: `cbc4231f209c53254e7d113d60a0c2a9434d3fdf`
- **作者**: 施廷緯
- **日期**: 2018-04-23 20:58:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 69. 修改-我的最愛-常用流程維護.常用功能維護2功能頁面重新設
- **Commit ID**: `24ef76d20a5d9657b7f5f5b2abb9b86b2ba0bfc4`
- **作者**: jerry1218
- **日期**: 2018-04-23 18:00:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`

### 70. 修正BPMAPP新樣板若為隱藏元件時會產生內容錯誤問題
- **Commit ID**: `94191ff55e9649dc3d4a14f274011fbfca953e95`
- **作者**: pinchi_lin
- **日期**: 2018-04-23 17:43:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 71. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `81fece7fcbb6665ea21ed91458d3d4c43bdb813e`
- **作者**: Gaspard
- **日期**: 2018-04-23 17:01:58
- **變更檔案數量**: 0

### 72. 修正異常
- **Commit ID**: `e6a29f067d43e13d6663b028cea0ac471d2b5350`
- **作者**: jerry1218
- **日期**: 2018-04-23 17:01:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`

### 73. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `8af44d07e39c026818bd882d8760a73cdd162bfa`
- **作者**: Gaspard
- **日期**: 2018-04-23 17:00:41
- **變更檔案數量**: 0

### 74. 修改追蹤、待辦、發起流程頁面左右比例為2:10
- **Commit ID**: `8b28c1a1bfacae2480bf2a195ca94924ff05c555`
- **作者**: Gaspard
- **日期**: 2018-04-23 17:00:24
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 75. 移除測試的程式碼
- **Commit ID**: `23b5fb11aa9b810da2d705e69ab3deb5c716e17c`
- **作者**: ChinRong
- **日期**: 2018-04-23 16:55:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 76. 調整action新機制
- **Commit ID**: `ec020e08440384935060d854f5158b4bfedd10ef`
- **作者**: ChinRong
- **日期**: 2018-04-23 16:50:30
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`

### 77. 修改 - 簽核畫面(簽核意見)及追蹤畫面(撤銷理由)與上一行的icon合併
- **Commit ID**: `da84331348e574dc0185206361084f51c24a8d3f`
- **作者**: jerry1218
- **日期**: 2018-04-23 16:02:34
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 78. 產品統一使用微軟雅黑
- **Commit ID**: `058619a88b0e9ea12bd4e9292f5ffaedd90f203b`
- **作者**: Gaspard
- **日期**: 2018-04-20 17:46:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 79. 頁面文字統一樣式
- **Commit ID**: `d24f0e623ff1828dd3499b03228f535fc0b4cacc`
- **作者**: Gaspard
- **日期**: 2018-04-20 17:33:44
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 80. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `4d537c98e997d61652d82dbe3c36c4a207969cf8`
- **作者**: Gaspard
- **日期**: 2018-04-20 17:16:19
- **變更檔案數量**: 0

### 81. 修改bpm-panel下的收合按鈕統一靠右15px
- **Commit ID**: `6a655e5d2de32696b690c7dca8135fb67c1f9abe`
- **作者**: Gaspard
- **日期**: 2018-04-20 17:16:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 82. 發起流程拿掉關注功能導致的錯誤修正
- **Commit ID**: `ec64a81f88ed08c8a1e59c4c83fa726a971ec890`
- **作者**: jerry1218
- **日期**: 2018-04-20 17:03:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 83. 清單頁的操作按鈕統一收合到「更多」按鈕中
- **Commit ID**: `bcdc843d9b6623cb2d51baa67c44b9aaabf62d58`
- **作者**: Gaspard
- **日期**: 2018-04-20 16:33:05
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`

### 84. 改變發起流程預設圖示
- **Commit ID**: `abe96ffe35359d841f0237938ac1916e8d808a9b`
- **作者**: Gaspard
- **日期**: 2018-04-20 16:10:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/process.png`

### 85. 修改DialogInputLabel元件的篩選條件使其可使用動態變數查詢
- **Commit ID**: `94b40ed9bb9f32a89e3764d7c6e431aff6312241`
- **作者**: Gaspard
- **日期**: 2018-04-20 15:49:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DialogInputMultiTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DialogInputTemplate.txt`

### 86. 修改查詢條件section右邊按鈕padding , 單選窗頁面微調
- **Commit ID**: `79ef08ad681ecbe5b6f9a771307c214a6dc13330`
- **作者**: jerry1218
- **日期**: 2018-04-20 14:29:22
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 87. 修正RWD表單自動轉換BPMAPP表單問題
- **Commit ID**: `04bed2576dde3adfe78780972f2eb99aa2f8f92a`
- **作者**: pinchi_lin
- **日期**: 2018-04-19 19:36:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 88. 修正merge錯誤
- **Commit ID**: `ea5000739229313cf35c33477cffbbc0982bb81d`
- **作者**: pinchi_lin
- **日期**: 2018-04-19 17:42:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 89. 1.新增水平線、標題的App表單產生架構
- **Commit ID**: `dd3dfaa0566e01323486179c060f281ecf772204`
- **作者**: jd
- **日期**: 2018-04-19 17:17:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HorizontalLineElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElementMobile.java`

### 90. 1.新增DoubleTextBox中間層顯示 2.新增中間層多語系顯示
- **Commit ID**: `74dcb35d328c4af05497e8a763ab09bef9e74b3d`
- **作者**: jd
- **日期**: 2018-04-19 16:35:13
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 91. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `4921ef9803e5f2ac24083083c6aa5eab8f1b6bda`
- **作者**: jd
- **日期**: 2018-04-19 14:40:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`

### 92. 1.調整判斷機制,調整PHONE_RESPONSIVE為RWD_PC,原先RWD則繼續沿用 2.調整表單生成機制,支持自動轉換功能
- **Commit ID**: `fc68c50093119d0f47a97759116b8cf06bffebab`
- **作者**: pinchi_lin
- **日期**: 2018-04-18 14:45:36
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ClientDeviceType.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/AttachmentContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 93. 調整BPMAPP新模板樣式 1.新增 Title、水平線、doubletextbox元件模板 2.修正時間日期元件少了外層div 3.修正部分元件的樣式 補上InvokeServiceTool少了草稿方法
- **Commit ID**: `b3cf7219ad27f03b77a5272eb75a7f04d01555eb`
- **作者**: yamiyeh10
- **日期**: 2018-04-18 18:57:12
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/FormElementUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 94. 修正WildFly上呼叫APP的action出現NoClassDefFoundError的異常
- **Commit ID**: `9b31538b33d8eb73b57ebb6c2c62369c38179da6`
- **作者**: Loren
- **日期**: 2018-04-18 17:30:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.xml`

### 95. 修正WildFly上呼叫APP的action出現NoClassDefFoundError的異常
- **Commit ID**: `7c4111715fee2db351d18d6aeb9b62876633d6c9`
- **作者**: Loren
- **日期**: 2018-04-18 17:00:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.xml`

### 96. 5712測試問題調整
- **Commit ID**: `62a599d976246b362dc0fbb1e80838340d479949`
- **作者**: jerry1218
- **日期**: 2018-04-18 15:44:18
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 97. 調整BPMAPP新模板
- **Commit ID**: `5705f3b0a5341082f1308c3e78514ff143ca9dd6`
- **作者**: pinchi_lin
- **日期**: 2018-04-18 14:45:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/AttachmentContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 98. 修正BPM APP退回重辦時有預設模式,仍需點選退回模式才能退簽問題
- **Commit ID**: `7bd090180cdc41e628544e89002e598478868a4f`
- **作者**: 治傑
- **日期**: 2018-04-18 11:16:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 99. 修正追蹤流程頁面有設定RWD表單顯示比例時，失效的議題
- **Commit ID**: `a67fff19dd14d3e2ef0e858b9e302330d85575f2`
- **作者**: Gaspard
- **日期**: 2018-04-18 04:38:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 100. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `1fb5f772a530f712265a87ab86efecb28522c22f`
- **作者**: Gaspard
- **日期**: 2018-04-18 04:15:51
- **變更檔案數量**: 0

### 101. 修正無法開啟流程樹
- **Commit ID**: `84ff15ed1851d6c07b0623651586bdd57acfca50`
- **作者**: Gaspard
- **日期**: 2018-04-18 04:15:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 102. 修正pdfjs議題中錯誤的程式碼
- **Commit ID**: `a6054ba1a108bfeadb0194e58c06bc35ce74d110`
- **作者**: ChinRong
- **日期**: 2018-04-17 18:09:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`

### 103. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `509dbea3d4220a4f5fd371e73874696dc25c9e71`
- **作者**: Gaspard
- **日期**: 2018-04-17 18:06:52
- **變更檔案數量**: 0

### 104. 測試議題修正
- **Commit ID**: `3ef9cb8b183b0372c396a949453f23e5f3ae6d2b`
- **作者**: Gaspard
- **日期**: 2018-04-17 18:06:28
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 105. 行動表單Action新架構
- **Commit ID**: `f130562d24b638a3fb46bb4c9d0afec77485ae2c`
- **作者**: ChinRong
- **日期**: 2018-04-17 17:51:48
- **變更檔案數量**: 28
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 106. 修正WEB表單設計師絕對定位表單DB Connection元件JSONObject取值異常
- **Commit ID**: `54ebec68647afcfbea19d8dc66e3bf1f68d6d6b4`
- **作者**: Loren
- **日期**: 2018-04-17 17:36:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 107. 新增BPMAPP新模板遺漏的圖片元件
- **Commit ID**: `536145959bcdf32c14e6b08c0796b3d470fc69e1`
- **作者**: pinchi_lin
- **日期**: 2018-04-17 14:47:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 108. 調整入口平台整合設定畫面
- **Commit ID**: `13d0965cdd6ce0ce6dd1edef066e53aebcbff263`
- **作者**: ChinRong
- **日期**: 2018-04-17 14:06:08
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChateUser.js`

### 109. 加入jboss-all.xml讓部署順序為nana-app.ear>NaNaWeb.war
- **Commit ID**: `85b8cae0bfe1cb04c8fbf263fd2291c3c469da1b`
- **作者**: Loren
- **日期**: 2018-04-17 11:58:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/jboss-all.xml`

### 110. 5712測試問題調整
- **Commit ID**: `6bac0e37bfb9bbd107c47a943bd22d7690a75f8d`
- **作者**: jerry1218
- **日期**: 2018-04-17 11:37:07
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_2_Check.sql`

### 111. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `900b11b08bad63877e2046fb83fc9a2e7c5cac4d`
- **作者**: jd
- **日期**: 2018-04-17 11:32:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 112. 1.新增RESTful API說明機制 ,RWD表單轉換機制
- **Commit ID**: `e1b93d8718c80400542238b9cf2815d8d7e2d0b6`
- **作者**: jd
- **日期**: 2018-04-17 11:26:22
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/classmate-1.3.4.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/spring-plugin-core-1.2.0.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/spring-plugin-metadata-1.2.0.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/springfox-core-2.7.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/springfox-schema-2.7.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/springfox-spi-2.7.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/springfox-spring-web-2.7.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/springfox-swagger-common-2.7.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/springfox-swagger-ui-2.7.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/springfox-swagger2-2.7.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/swagger-annotations-1.5.14.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/swagger-models-1.5.14.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/RsrcbundleBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/config/Swagger.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`

### 113. 新增BPMAPP解析RWD表單
- **Commit ID**: `a1b1d5d4a11cc84a2885aa13ab961ef52d21df9e`
- **作者**: pinchi_lin
- **日期**: 2018-04-17 11:13:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 114. A00-20180409001 修正客製開窗在字數過多時會產生重疊問題
- **Commit ID**: `0c20629d4118b28ebce53e43084ca325823d55a4`
- **作者**: yamiyeh10
- **日期**: 2018-04-17 08:58:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 115. 調整NaNaWeb.war及nana-app.ear取得產品資訊的方式
- **Commit ID**: `4eb26e0bfc113757686e612f3ba8ca405c518549`
- **作者**: Loren
- **日期**: 2018-04-16 22:46:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/BpmInfoSingleton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebInfoFetcher.java`

### 116. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `6a2df5012f56c0db2511c57b964bff28a22d8768`
- **作者**: Gaspard
- **日期**: 2018-04-16 18:07:32
- **變更檔案數量**: 0

### 117. 測試議題修正
- **Commit ID**: `70e359f1972140fba601522ecc50c828f7275430`
- **作者**: Gaspard
- **日期**: 2018-04-16 18:06:55
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SuspendedInvokeActListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/WfNotificationListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 118. 移除TrinityServieEJB.jar及調用的程式區塊
- **Commit ID**: `4813b535bd36c6ab638de4fa4b43cb188e451c5f`
- **作者**: Loren
- **日期**: 2018-04-16 18:02:22
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/business-delegate/lib/Trinity/TrinityServiceEJB.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/Trinity/TrinityServiceEJB.jar`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/trinity/TrinityManagerBean.java`

### 119. [A00-***********]修正流程簽核掛外部連結畫面,使用Chrome瀏覽器無法正常派送流程的問題。
- **Commit ID**: `3a14270215d38959a875343b05f7032b761e8846`
- **作者**: 顏伸儒
- **日期**: 2018-04-16 17:49:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 120. 修正安裝密碼註冊格式正確但註冊失敗時 , 造成頁面空白問題
- **Commit ID**: `3760d8597ab1dcd8bd56358c1b7ce1915f6b31ef`
- **作者**: jerry1218
- **日期**: 2018-04-16 16:27:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`

### 121. A00-***********.A00-*********** 修正單選開窗異常顯示搜尋區塊問題
- **Commit ID**: `845c01a239df864a952b46b486e6195a6df21974`
- **作者**: jerry1218
- **日期**: 2018-04-16 11:51:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`

### 122. A00-*********** 修正員工工作轉派作業，點選「批次轉派」跳出選擇接收者的視窗時，點擊「原處理者的預設代理人有:」會跳出請洽系統管理員的錯誤。
- **Commit ID**: `0550f29ff434111fb6a23d5159a90abb2d0e716d`
- **作者**: jerry1218
- **日期**: 2018-04-16 11:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`

### 123. C01-*********** 微信帳號的搜索功能失效
- **Commit ID**: `c813880f40f347ad5fae512c505d2b88febed0bb`
- **作者**: ChinRong
- **日期**: 2018-04-16 10:08:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`

### 124. 修正全系統ICON統一用font-awesome產生，並將元件加入圓角效果
- **Commit ID**: `4adc17da764d377de8f7e4470a90bb3511273f30`
- **作者**: Gaspard
- **日期**: 2018-04-16 09:36:23
- **變更檔案數量**: 34
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-global-style-template.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 125. 移除global-modules
- **Commit ID**: `4f1b2efdcb7a4ad630997930f813fd45a68f1a21`
- **作者**: Loren
- **日期**: 2018-04-15 23:37:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/jboss-deployment-structure.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 126. 修正未知的差異：<?導致無法正確部署的問題
- **Commit ID**: `3040dda1606610dcc547a2ff7061afa5a77f7e7d`
- **作者**: Loren
- **日期**: 2018-04-13 17:56:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml`

### 127. 更新Patch為5712
- **Commit ID**: `3803c8e02a92aba405e3884d9aed04cf0721d32b`
- **作者**: Loren
- **日期**: 2018-04-13 17:23:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 128. UI PLUS版本Merge至v57
- **Commit ID**: `66faf0e35013dc3260a2fa47d79d9de443ab52a6`
- **作者**: Loren
- **日期**: 2018-04-13 16:37:42
- **變更檔案數量**: 231
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/.settings/org.eclipse.wst.common.project.facet.core.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SystemVariableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomCssLib/bpm-global-style-custom.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/AttachmentExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/ButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DialogExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/RadioButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TextboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TimeExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Index.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ProductManifest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/LogoImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ShowSignImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AdjustActivityOrder.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteReferProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ViewReassignHistory.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/CreateProcessModule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/ManageProcessModuleMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessModule/SetModuleAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessPicture.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/QueryDesinger.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-global-style-template.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-global-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChatDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChateUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ModalDialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/OpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/doResize.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery.nicescroll.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/cropped-logo_favicon.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/efgplogo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/critical-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/critical.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/focus-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-critical-Process-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-critical-Process.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-focus-Process-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-focus-Process.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-notice-list-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-notice-list.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-todo-list-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-todo-list.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-user-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-user.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/my_trace-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/trace-hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/formExplorer.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 129. 將nana-app.ear參考的lib改為Module，調整開發環境及修正數項異常
- **Commit ID**: `263a1aa56d18b368c485e23b63adf9a274b8528c`
- **作者**: Loren
- **日期**: 2018-04-13 01:46:25
- **變更檔案數量**: 322
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - 📝 **修改**: `.project`
  - 📝 **修改**: `3.Implementation/build.bat`
  - 📝 **修改**: `3.Implementation/properties.xml`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/crm-configure/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/domain/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/domain/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/Crypto.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/.settings/org.eclipse.core.resources.prefs`
  - 📝 **修改**: `3.Implementation/subproject/dto/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/dto/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/dto/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/form-importer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/org-importer/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/persistence/.settings/org.eclipse.core.resources.prefs`
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/persistence/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/service/.settings/org.eclipse.core.resources.prefs`
  - ❌ **刪除**: `3.Implementation/subproject/service/NaNa/lib/Ext/readme.txt`
  - 📝 **修改**: `3.Implementation/subproject/service/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/service/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-logging.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/util/sap/CustDestinationDataProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/build.bat`
  - ➕ **新增**: `3.Implementation/subproject/system/.settings/org.eclipse.core.resources.prefs`
  - 📝 **修改**: `3.Implementation/subproject/system/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/system/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/system/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SingleSignOnUtil.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/.settings/org.eclipse.core.resources.prefs`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.bat`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JakartaCommons/commons-logging-jboss-logmanager-1.0.2.Final.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/lib/JakartaCommons/commons-logging.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFileManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`
  - ➕ **新增**: `3.Implementation/targets-service.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/NaNaCustomConn.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/NaNaDesigner.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/NaNaEJB.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/NaNaISO.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/NaNaISOLog.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/NaNaLog.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/NaNaPlugIn.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/Trinity.property`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/castor/CastorXMLMapping.dtd`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/castor/ImportCustomXPDLMapping.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/castor/XPDLExportToCustomXPDLMapping.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/castor/XpdlMapping.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/dmm/DataTypeDefXSD.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/dmm/dmm_db_metadata.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/dmm/dmm_service.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/easyflow_cfg.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/hibernate/main/data-source.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/hibernate/main/hibernate.cfg.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/hibernate/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/hibernate/main/session-factory.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/hibernate/main/spring-bo.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/hibernate/main/spring-dao.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/hibernate/main/transaction-manager.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/OJB.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository.dtd`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_appform.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_authority.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_bpm.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_crm.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_database.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_database_Oracle.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_dmm.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_internal.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_tiptop.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/liusconfig.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/syncorg/SyncStep_config.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/syncorg/SyncTable.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/syncorg/SyncTable_Oracle.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/syncorg/etl.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/syncorg/sync-def.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/conf/syncorg/sync-def_Oracle.xml`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/ext/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/as400/main/jt400.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/as400/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/db2/main/db2java.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/db2/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/foxpro/main/DBF_JDBC30.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/foxpro/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/informix/main/ifxjdbc.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/informix/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/mssql/inetmerlia/main/Merlia.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/mssql/inetmerlia/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/mssql/jtds/main/jtds.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/mssql/jtds/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/mssql/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/mssql/main/sqljdbc.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/mysql/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/mysql/main/mysql-connector-java.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/oracle/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/oracle/main/ojdbc6.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/postgresql/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/postgresql/main/postgresql_jdbc3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/sybase/main/jconn3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/lib/jdbc/sybase/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Axis/axis.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Axis/commons-discovery.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Axis/jaxrpc.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Axis/saaj.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Axis/wsdl4j.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/BouncyCastle/bcprov-jdk15on-1.56.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Bsf/bsf.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Bsf/bsh.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Bsf/js.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Cactus/aspectjrt-1.5.3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Cactus/cactus-1.8.1.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Cactus/cactus-ant.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Cactus/commons-httpclient.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Cactus/commons-logging.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Castor/castor-xml.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Castor/castor.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Comm/ASIM.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/CrossIntgmsg/cross-intgmsg-api-fordsc-1.0.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/CrossIntgmsg/cross-intgmsg-api-fordsc-1.3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Dom4J/dom4j-1.6.1-changed_serialization.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/ETL/jaxb-api.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/ETL/jaxb-impl.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/ETL/jsr173_1.0_api.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/ETL/scriptella.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Hibernate/antlr.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Hibernate/asm.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Hibernate/cglib.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Hibernate/ehcache.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Hibernate/hibernate.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/DBF_JDBC30.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/Merlia.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/db2java.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/db2jcc.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/db2jcc_license_cu.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/ifxjdbc.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/jconn3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/jt400.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/jtds.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/mysql-connector-java.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/ojdbc6.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/postgresql_jdbc3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDBCDrivers/sqljdbc.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JDiagram/JDiagram-4.1.4.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JUnit/junit.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-beanutils.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-codec.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-collections.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-dbcp.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-digester.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-httpclient.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-io.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-lang.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/JakartaCommons/commons-logging-jboss-logmanager-1.0.2.Final.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-pool.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaCommons/which.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaOJB/antlr.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JakartaOJB/db-ojb.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JavaMail/activation.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/JavaMail/mail.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Jaxen/jaxen.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Json/json.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Jython/jython.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Log4J/log4j.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/MimeType.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/PDFBox-0.7.2.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/Tidy.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/easypdf-jacob.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/jacob.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/jaxen-core.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/jaxen-jdom.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/jdom.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/jxl.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/lius-lucene.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/lucene-core-2.1.0.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/lucene-highlighter-2.1.0.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/poi-3.12-20150511.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/poi-examples-3.12-20150511.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/poi-excelant-3.12-20150511.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/poi-ooxml-3.12-20150511.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/poi-ooxml-schemas-3.12-20150511.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/poi-scratchpad-3.12-20150511.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/saxpath.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Lucene/xmlbeans-2.6.0.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Msv/isorelax.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Msv/msv.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Msv/relaxngDatatype.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Msv/xmlgen.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Msv/xsdlib.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/PDF/Jbepprint.dll`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/PDF/Jbepprint.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/PDF/Jbepproc.dll`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/PDF/Jbepproc.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/PDF/easypdf-jacob.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/PDF/ezjcom18.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/PDF/jacob.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/PatchRunner/DsPatch.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Poi/poi-3.2-FINAL-20081019.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Sap/sapjco3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Sdo/sdo2_1.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Security/iFjTwcaSsClntLib.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Security/iFjTwcaSsClntRequiredAll.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Security/jsse.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Servlet/servlet.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Spring/spring.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/T100/t100-rmi-service-client.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TestingUtility/testing-utility.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/Trinity/TrinityServiceEJB.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/PAPIS.api`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/PapisBridge.dll`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/commons-dbcp-1.1.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/commons-dbcp-1.2.1.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/commons-pool-1.1.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/commons-pool-1.2.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/jacob.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/jakarta-regexp-1.3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/jsafe.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/tv40.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/xercesImpl.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/xml-apis.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/TrustView/xmlParserAPIs.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Tyrex/tyrex.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Weblogic/ClientLibrary/wlclient-10.0.1.0.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/WildFly/jboss-client.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/XStream/xpp3_min.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/XStream/xstream.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Xalan/xalan.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Xerces/resolver.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Xerces/serializer.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Xerces/xercesImpl.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/Xerces/xml-apis.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/asm-3.1.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/asm-attrs-1.5.3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/cglib-nodep-2.1_3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/commons-digester.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/groovy-all-1.7.5.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/iTextAsian.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/itext-2.1.7.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/jasperreports-4.1.3.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/iReport/jasperreports-htmlcomponent-4.1.1.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/ldap/ldap.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/lib/utils-0.1.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/nana-app/service/main/module.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/service/NaNa/report/ReadMe.txt`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/01.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/02.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/03.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/04.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/def.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/overtime.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/send.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/title_def.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/title_send.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/title_work.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/BpmEmailImage/work.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5601.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5602.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5603.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5621.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5622.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5623.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5631.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5642.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5651.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5653.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5701.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5712.xls`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/UserLogoImages/EFGP_banner_default.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/UserLogoImages/EFGP_default.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/UserLogoImages/EFGP_logo_default.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/UserLogoImages/user_saved.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/UserLogoImages/user_uploaded.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/conf/MappingFile.txt`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaJobs.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaSchedule.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaSecurityLog.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWebLog.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/conf/Portal.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/conf/cht.properties`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/picName/EFGPPrintMode.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/picName/EFGPSignImage.jpg`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/picName/process.png`

### 130. (A00-20180412001)修正模組程式維護作業，點選一筆程式資料，修改多語系名稱後，按下「修改」按鈕卻跳出「程式代號重複」的訊息錯誤 (A00-20180412002)修正模組程式維護作業，點選一筆程式資料，修改程式代號.名稱.URL含有&<>這幾個符號會替換為HTML碼的錯誤
- **Commit ID**: `3ca901e4017943cdb8da17aa86b687f0229a959a`
- **作者**: jerry1218
- **日期**: 2018-04-12 18:27:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`

### 131. A00-20180412009 協助commit 修正在退回重辦需要逐級通知的前提下當流程關卡包含核決關卡
- **Commit ID**: `f36390f8eb5542ee97e30e3df867598c34f82bb8`
- **作者**: 張詠威
- **日期**: 2018-04-12 18:18:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 132. 調整BPMAPP新模板樣式
- **Commit ID**: `589fbf01091d4a169bc9fb573fc81241540c84a7`
- **作者**: pinchi_lin
- **日期**: 2018-04-12 17:29:13
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/FormContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/SubElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/mobile/FormElementUtil.java`

### 133. C01-20180409002 修正BPMAPP向後加簽多語系錯誤
- **Commit ID**: `17bbf0b9b5a5cacdcce8cc50343f8083392dc787`
- **作者**: pinchi_lin
- **日期**: 2018-04-12 16:15:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5712.xls`

### 134. 修正升版後統計元件的顏色還是彩色的問題
- **Commit ID**: `9b79ea0d450918d9ecd6646d50d3622829d7c94a`
- **作者**: ChinRong
- **日期**: 2018-04-11 16:01:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 135. 修正將流程設定為不支援行動版，在行動版的常用發起仍然可以看到該流程問題
- **Commit ID**: `7ce7f677647bf6cc5a41288afb7b84fab7e7a9a2`
- **作者**: 治傑
- **日期**: 2018-04-11 15:48:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java`

### 136. 修正表單畫面多語系問題 將jQuery的使用變成jBPM
- **Commit ID**: `c3c9b81ba1e1614969d2b26fc7bc90bb893d79ad`
- **作者**: yamiyeh10
- **日期**: 2018-04-11 11:06:10
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`

### 137. C01-*********** 微信帳號的搜索功能失效
- **Commit ID**: `4757b85f833f1528b3b6b74ee8fabc87e2b3f3df`
- **作者**: ChinRong
- **日期**: 2018-04-10 17:23:00
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp`

### 138. 修正開窗無法使用(重複命名method問題)，故先移除原本轉呼叫的code及 移除 限制撈取500筆的code
- **Commit ID**: `45a97d7115e09a2393f37384a183b18a0d109d45`
- **作者**: 張詠威
- **日期**: 2018-04-10 16:52:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 139. Q00-20180410001 修正文件總管新增文件異常 及 ISO檔案無法下載議題
- **Commit ID**: `fda1e70e2fdb69bb7ab9384f722af9e5939b0553`
- **作者**: 張詠威
- **日期**: 2018-04-10 16:39:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOInv001.form`

### 140. 修正加入Grid控制項無法使用的異常
- **Commit ID**: `49b79d4e99f868eca63ffb7c001b114391dca8c4`
- **作者**: Gaspard
- **日期**: 2018-04-03 14:54:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 141. 調整RESTFul開窗的參數值
- **Commit ID**: `3e31fb49aa2cae173422b5d45cac84970b4f5aff`
- **作者**: 張詠威
- **日期**: 2018-04-02 18:45:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 142. 增加RESTFul開窗允許自帶參數
- **Commit ID**: `139d6f8a78881c112464c9476e928130b86e1083`
- **作者**: 張詠威
- **日期**: 2018-04-02 18:28:13
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 143. 更新XX:MaxMetaspaceSize=512m，避免版更很容易就出現Metaspace不足的錯誤
- **Commit ID**: `fcd708de6389dcef5095ca97d79f5a9f62800da7`
- **作者**: Loren
- **日期**: 2018-04-02 15:17:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf.bat`

### 144. 修正部署在Linux時，引用到JGo會產生異常
- **Commit ID**: `51be2c74016419cd50c76b3ff6d04a870f53292a`
- **作者**: Loren
- **日期**: 2018-04-02 14:47:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.conf`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/standalone.sh`

### 145. Q00-20180330006 修正訊息中心點擊新系統通知時畫面loadding問題
- **Commit ID**: `8d1864c86868dc2e988b58225b927d0e58106586`
- **作者**: jerry1218
- **日期**: 2018-04-02 14:22:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 146. Q00-20180402001 修正加簽關卡頁面，關卡名稱為填寫時未提式問題
- **Commit ID**: `9abb5f2aeeb93c2fa055c1361f07bd3a8b414291`
- **作者**: jerry1218
- **日期**: 2018-04-02 14:20:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5712.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 147. Q00-20180330004 修正撤銷流程.工作通知.取回重瓣.追蹤流程.系統通知頁面模糊查詢使用單引號出現的頁面異常
- **Commit ID**: `12a279d1ceee67b3cc1c3101a40c9a795bd2b208`
- **作者**: jerry1218
- **日期**: 2018-04-02 10:24:48
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/WfNotificationListReader.java`

### 148. Q00-20180330003 修正測試模擬使用者開窗異常議題
- **Commit ID**: `b8b2d1640cd9048f4f400419f4ebd7a6462a893c`
- **作者**: yamiyeh10
- **日期**: 2018-03-31 15:25:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 149. Q00-20180330005 修正待辦清單主旨有單引號時導致的畫面顯示異常
- **Commit ID**: `34a0379f1ea1732654922c52d413a57653883a92`
- **作者**: jerry1218
- **日期**: 2018-03-31 15:21:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemViewer.java`

### 150. Q00-*********** 修正入口平台頁面鼎捷使用者與微信使用者頁面取得的資料異常
- **Commit ID**: `3c326aaa6c1088064e8a3699e30a2fcd7e10cadf`
- **作者**: ChinRong
- **日期**: 2018-03-31 10:36:52
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`

### 151. 更新打包程式JAVA_HOME路徑
- **Commit ID**: `9767c6deb79d1a210064c99c2bbee627faa82442`
- **作者**: Loren
- **日期**: 2018-03-31 09:00:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/build.bat`

### 152. 修正無法設定關注條件的異常
- **Commit ID**: `caf43b09c9c4cdf0a64c4f2af5016dd52bd0d078`
- **作者**: Gaspard
- **日期**: 2018-03-30 17:01:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp`

### 153. 移除多餘的測試程式
- **Commit ID**: `86ce606cff132501729dac3a00156a868d174210`
- **作者**: Gaspard
- **日期**: 2018-03-30 16:58:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 154. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `5a75f2f59c6dd1583b197b346e0f5d3d44141e33`
- **作者**: 張詠威
- **日期**: 2018-03-30 14:42:25
- **變更檔案數量**: 0

### 155. 客製開窗增加RESTFul功能
- **Commit ID**: `3d8877be16ca5f6f40f4bf77bd0397031865de8e`
- **作者**: 張詠威
- **日期**: 2018-03-30 14:41:43
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 156. A00-20180329002 新增行動版Grid reload方法
- **Commit ID**: `32b9f3dfa5509361c7d7651229a22c34854e2f87`
- **作者**: ChinRong
- **日期**: 2018-03-30 14:31:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 157. A00-20180320001 修正TT流程再中間層簽核時會派送失敗問題
- **Commit ID**: `6fee974901277e7256489d0ab359546d0ceb2d54`
- **作者**: pinchi_lin
- **日期**: 2018-03-30 14:06:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 158. 修正上次議題修正時Merge錯誤
- **Commit ID**: `753337e35f06dd409e28a59c8ea38edd76606ea6`
- **作者**: ChinRong
- **日期**: 2018-03-29 18:05:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

