{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.7.4.2", "date": "tag 5.7.4.2\nTagger: 施翔耀 <jose<PERSON><PERSON><PERSON>@digiwin.biz>\n\nLast build 2019/01/28  17:002019-01-28 17:13:24", "message": "Q00-20190128001 修正用Excel匯入多語系 如果值有('單引號)會匯入失敗", "author": "walter_wu"}, "舊分支": {"branch_name": "5.7.4.1", "date": "tag 5.7.4.1\nTagger: 施翔耀 <jose<PERSON><PERSON><PERSON>@digiwin.biz>\n\nLast Build 11/282018-11-28 13:41:16", "message": "修正 :RESTFul撤銷註記寄信的主旨沒有多語系", "author": "jose<PERSON>"}, "比較時間": "2025-07-28 18:01:19", "新增commit數量": 172, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "3d9ebb1e9eee9cc24535bb9007f60cd5d08c36af", "commit_訊息": "Q00-20190128001 修正用Excel匯入多語系 如果值有('單引號)會匯入失敗", "提交日期": "2019-01-28 17:13:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9d809df37bad6ac3cbadcfa335f2efec4564f44", "commit_訊息": "<V57>C01-20190128001 調整 :將T100表單移至不維運區塊", "提交日期": "2019-01-28 15:28:56", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/ECR\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(abmt500).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\270\\200\\350\\210\\254\\345\\224\\256\\345\\203\\271\\350\\252\\277\\345\\203\\271\\344\\275\\234\\346\\245\\255(aprt112).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\270\\200\\350\\210\\254\\351\\200\\262\\345\\203\\271\\350\\252\\277\\345\\203\\271\\344\\275\\234\\346\\245\\255(aprt111).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\345\\207\\206\\345\\205\\245\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt801).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\347\\224\\263\\350\\253\\213\\345\\226\\256(apmt100).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\272\\244\\346\\230\\223\\345\\260\\215\\350\\261\\241\\350\\255\\211\\347\\205\\247\\347\\225\\260\\345\\213\\225\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt820).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\345\\207\\206\\345\\205\\245\\345\\226\\256(apmt800).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt200).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\265\\220\\347\\256\\227\\345\\226\\256(astt340).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\270\\276\\346\\225\\210\\350\\251\\225\\346\\240\\270\\345\\256\\232\\346\\200\\247\\350\\251\\225\\345\\210\\206\\345\\226\\256(apmt811).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\347\\270\\276\\346\\225\\210\\350\\251\\225\\346\\240\\270\\347\\266\\234\\345\\220\\210\\345\\276\\227\\345\\210\\206\\350\\252\\277\\346\\225\\264\\345\\226\\256(apmt814).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\262\\250\\346\\254\\276\\345\\260\\215\\345\\270\\263\\344\\275\\234\\346\\245\\255(aapt110).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\276\\233\\346\\207\\211\\345\\225\\206\\350\\262\\273\\347\\224\\250\\345\\226\\256(astt320).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\277\\203\\351\\212\\267\\350\\252\\277\\345\\203\\271\\344\\275\\234\\346\\245\\255(aprt113).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\344\\277\\203\\351\\212\\267\\350\\253\\207\\345\\210\\244\\347\\224\\263\\350\\253\\213(aprt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\200\\237\\350\\262\\250\\345\\207\\272\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt542).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\200\\237\\350\\262\\250\\350\\250\\202\\345\\226\\256(axmt501).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\200\\237\\350\\262\\250\\351\\202\\204\\351\\207\\217\\345\\226\\256(axmt591).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\205\\247\\351\\203\\250\\347\\265\\220\\347\\256\\227\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt740).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\205\\266\\344\\273\\226\\346\\207\\211\\344\\273\\230\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt301).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\207\\272\\350\\262\\250\\345\\226\\256(axmt540).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\207\\272\\350\\262\\250\\347\\260\\275\\346\\224\\266\\345\\226\\256(axmt580).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\207\\272\\350\\262\\250\\351\\200\\232\\347\\237\\245\\345\\226\\256(axmt520).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\345\\220\\210\\347\\264\\204\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt601).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\350\\250\\202\\345\\226\\256(adbt500).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt510).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\210\\206\\351\\212\\267\\351\\212\\267\\351\\200\\200\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(adbt600).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\210\\270\\347\\250\\256\\345\\237\\272\\346\\234\\254\\350\\263\\207\\346\\226\\231\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(agct300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\223\\201\\350\\263\\252\\346\\252\\242\\351\\251\\227\\345\\226\\256(aqct300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\223\\201\\350\\263\\252\\347\\225\\260\\345\\270\\270\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(aqct310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\225\\206\\345\\223\\201\\345\\207\\206\\345\\205\\245\\347\\224\\263\\350\\253\\213\\345\\226\\256(artt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\226\\256\\344\\270\\200\\344\\270\\273\\344\\273\\266ECN(abmt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\240\\261\\345\\273\\242\\347\\224\\263\\350\\253\\213\\345\\226\\256(aint310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\244\\232\\344\\270\\273\\344\\273\\266ECN(abmt301).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\345\\200\\211\\351\\200\\200\\345\\226\\256(apmt581).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt571).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt481).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\344\\275\\234\\346\\245\\255(apmt521).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt501).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\350\\251\\242\\345\\203\\271\\344\\275\\234\\346\\245\\255(apmt421).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\344\\275\\234\\346\\245\\255(apmt561).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\247\\224\\345\\244\\226\\346\\240\\270\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt441).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\256\\242\\346\\210\\266\\345\\207\\206\\345\\205\\245\\345\\217\\212\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(axmt800).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\256\\242\\346\\210\\266\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt200).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\257\\246\\345\\234\\260\\347\\233\\244\\351\\273\\236\\350\\250\\210\\347\\225\\253\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aint820).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\260\\210\\346\\253\\203\\345\\220\\210\\347\\264\\204\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213(astt401).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\260\\210\\346\\253\\203\\346\\226\\260\\345\\225\\206\\345\\223\\201\\345\\274\\225\\351\\200\\262\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(artt407).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\344\\270\\200\\350\\210\\254\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft323).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\345\\200\\222\\346\\211\\243\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft324).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\345\\200\\222\\346\\211\\243\\351\\240\\230\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft314).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\345\\234\\250\\350\\243\\275\\344\\270\\213\\351\\232\\216\\346\\226\\231\\345\\240\\261\\345\\273\\242\\344\\275\\234\\346\\245\\255(asft339).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\346\\210\\220\\345\\245\\227\\347\\231\\274\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft311).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\346\\210\\220\\345\\245\\227\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft321).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\346\\254\\240\\346\\226\\231\\350\\243\\234\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft313).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\243\\275\\347\\250\\213\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft801).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\243\\275\\347\\250\\213\\351\\207\\215\\345\\267\\245\\350\\275\\211\\345\\207\\272\\344\\275\\234\\346\\245\\255(asft338).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\256\\212\\346\\233\\264(asft800).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\266\\205\\351\\240\\230\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft312).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\267\\245\\345\\226\\256\\350\\266\\205\\351\\240\\230\\351\\200\\200\\346\\226\\231\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft322).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\270\\202\\345\\240\\264\\346\\216\\250\\345\\273\\243\\346\\264\\273\\345\\213\\225\\346\\240\\270\\351\\212\\267\\345\\226\\256(astt605).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\270\\202\\345\\240\\264\\346\\216\\250\\345\\273\\243\\346\\264\\273\\345\\213\\225\\347\\224\\263\\350\\253\\213\\345\\226\\256(astt604).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\272\\253\\345\\255\\230\\345\\240\\261\\345\\273\\242\\351\\231\\244\\345\\270\\263(aint311).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\272\\253\\345\\255\\230\\346\\234\\211\\346\\225\\210\\346\\234\\237\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(aint180).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\272\\253\\345\\255\\230\\347\\225\\260\\345\\270\\270\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(aint170).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\275\\210\\346\\200\\247\\346\\216\\241\\350\\263\\274\\345\\203\\271\\346\\240\\274\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(apmt128).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\345\\275\\210\\346\\200\\247\\351\\212\\267\\345\\224\\256\\345\\203\\271\\346\\240\\274\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt128).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\345\\214\\257\\346\\254\\276\\347\\225\\260\\345\\213\\225\\344\\275\\234\\346\\245\\255(anmt480).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\345\\214\\257\\346\\254\\276\\351\\226\\213\\347\\253\\213\\344\\275\\234\\346\\245\\255(anmt460).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\345\\270\\263\\346\\254\\276\\346\\206\\221\\345\\226\\256(aapt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\345\\276\\205\\346\\212\\265\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt340).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\207\\211\\344\\273\\230\\346\\240\\270\\351\\212\\267\\345\\226\\256(aapt420).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\207\\211\\346\\224\\266\\345\\270\\263\\346\\254\\276\\346\\206\\221\\345\\226\\256(axrt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\207\\211\\346\\224\\266\\346\\262\\226\\351\\212\\267\\346\\206\\221\\350\\255\\211(axrt400).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\345\\273\\266\\346\\234\\237\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt803).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt801).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\347\\265\\202\\346\\255\\242\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(astt805).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\350\\262\\273\\347\\224\\250\\345\\204\\252\\346\\203\\240\\347\\224\\263\\350\\253\\213(astt802).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\350\\262\\273\\347\\224\\250\\346\\250\\231\\346\\272\\226\\350\\256\\212\\346\\233\\264\\344\\275\\234\\346\\245\\255(astt806).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\213\\233\\345\\225\\206\\347\\247\\237\\350\\263\\203\\345\\220\\210\\347\\264\\204\\351\\235\\242\\347\\251\\215\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213(astt804).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\200\\211\\351\\200\\200\\345\\226\\256(apmt580).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\200\\211\\351\\200\\200\\345\\226\\256(apmt890).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\203\\271\\346\\240\\274\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt129).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt570).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt880).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\345\\226\\256(apmt480).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt490).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\226\\256(apmt500).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\345\\226\\256(apmt840).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt862).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt530).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256(apmt520).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256(apmt860).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\350\\243\\234\\345\\267\\256\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aprt601).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256(apmt510).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt850).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\351\\240\\220\\344\\273\\230\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aapt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\345\\226\\256(apmt560).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\216\\241\\350\\263\\274\\351\\251\\227\\351\\200\\200\\345\\226\\256(apmt870).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\226\\231\\344\\273\\266\\346\\211\\277\\350\\252\\215\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(abmt400).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\226\\231\\344\\273\\266\\347\\224\\263\\350\\253\\213\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aimt300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\226\\231\\344\\273\\266\\350\\243\\275\\347\\250\\213\\350\\263\\207\\346\\226\\231\\346\\226\\260\\345\\242\\236\\343\\200\\201\\344\\277\\256\\346\\224\\271\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(aect801).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\234\\203\\345\\223\\241\\345\\215\\241\\347\\250\\256\\347\\224\\263\\350\\253\\213\\345\\226\\256(ammt320).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\346\\240\\270\\345\\203\\271\\345\\226\\256(apmt440).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\345\\205\\245\\345\\272\\253\\345\\226\\256(apmt863).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\205\\245\\345\\272\\253\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt532).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256(apmt861).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\204\\241\\346\\216\\241\\350\\263\\274\\346\\224\\266\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt522).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\204\\241\\350\\250\\202\\345\\226\\256\\345\\207\\272\\350\\262\\250\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt541).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\207\\237\\351\\201\\213\\346\\223\\232\\351\\273\\236ECN\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(abmt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\207\\237\\351\\201\\213\\346\\223\\232\\351\\273\\236\\345\\244\\232\\344\\270\\273\\344\\273\\266ECN\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(abmt311).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\215\\250\\347\\253\\213\\351\\234\\200\\346\\261\\202\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apst300).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\224\\237\\351\\256\\256\\345\\203\\271\\346\\240\\274\\350\\252\\277\\346\\225\\264\\344\\275\\234\\346\\245\\255(aprt121).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\225\\266\\347\\253\\231\\344\\270\\213\\347\\267\\232\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(asft337).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\231\\274\\347\\245\\250\\350\\253\\213\\346\\254\\276\\345\\226\\256(aapt415).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\344\\273\\243\\345\\242\\212\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(astt606).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\347\\265\\220\\347\\256\\227\\345\\226\\256(astt640).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\347\\266\\223\\351\\212\\267\\345\\225\\206\\350\\262\\273\\347\\224\\250\\345\\226\\256(astt620).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\207\\252\\346\\234\\211\\346\\226\\260\\345\\225\\206\\345\\223\\201\\345\\274\\225\\351\\200\\262\\345\\226\\256(artt406).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\207\\252\\347\\207\\237\\345\\220\\210\\347\\264\\204\\347\\225\\260\\345\\213\\225\\347\\224\\263\\350\\253\\213\\345\\226\\256(astt301).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\207\\252\\347\\207\\237\\346\\226\\260\\347\\224\\242\\345\\223\\201\\345\\274\\225\\351\\200\\262(artt405).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\236\\215\\350\\263\\207\\347\\224\\263\\350\\253\\213\\345\\226\\256(afmt015).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\246\\201\\350\\262\\250\\345\\226\\256(apmt830).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\246\\201\\350\\262\\250\\345\\226\\256\\350\\256\\212\\346\\233\\264\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt835).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\250\\202\\345\\226\\256(axmt500).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\250\\202\\345\\226\\256\\350\\256\\212\\346\\233\\264(axmt510).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\251\\242\\345\\203\\271\\345\\226\\256(apmt420).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\252\\277\\346\\222\\245\\347\\224\\263\\350\\253\\213\\345\\226\\256(aint320).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\253\\213\\350\\263\\274\\345\\226\\256(apmt400).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\253\\213\\350\\263\\274\\350\\256\\212\\346\\233\\264\\345\\226\\256(apmt410).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\262\\273\\347\\224\\250\\345\\240\\261\\346\\224\\257\\345\\226\\256(aapt330).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\262\\273\\347\\224\\250\\351\\240\\220\\346\\224\\257\\347\\224\\263\\350\\253\\213\\345\\226\\256(aapt331).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\346\\224\\266\\345\\233\\236\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat450).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\345\\244\\226\\351\\200\\201\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat440).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\346\\224\\271\\350\\211\\257\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat508).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\263\\207\\347\\224\\242\\351\\203\\250\\351\\226\\200\\350\\275\\211\\347\\247\\273\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(afat421).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\350\\275\\211\\345\\270\\263\\345\\202\\263\\347\\245\\250(aglt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\200\\345\\255\\230\\346\\224\\266\\346\\224\\257\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(anmt310).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\344\\274\\260\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt400).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\345\\203\\271\\346\\240\\274\\350\\241\\250\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(axmt129).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\345\\220\\210\\347\\264\\204\\345\\226\\256(axmt440).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\345\\220\\210\\347\\264\\204\\350\\256\\212\\346\\233\\264\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt450).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\345\\240\\261\\345\\203\\271\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axmt410).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\346\\240\\270\\345\\203\\271\\345\\226\\256(axmt420).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\267\\345\\224\\256\\350\\243\\234\\345\\267\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(aprt602).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\212\\267\\351\\200\\200\\345\\226\\256(axmt600).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\213\\252\\350\\262\\250\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(apmt832).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\226\\200\\345\\272\\227\\350\\263\\207\\346\\272\\220\\345\\215\\224\\350\\255\\260\\347\\224\\263\\350\\253\\213\\344\\275\\234\\346\\245\\255(artt230).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\233\\234\\351\\240\\205\\345\\272\\253\\345\\255\\230\\346\\224\\266\\346\\226\\231\\345\\226\\256(aint302).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\233\\234\\351\\240\\205\\345\\272\\253\\345\\255\\230\\347\\231\\274\\346\\226\\231\\345\\226\\256(aint301).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\\351\\233\\234\\351\\240\\205\\345\\276\\205\\346\\212\\265\\345\\226\\256\\347\\266\\255\\350\\255\\267\\344\\275\\234\\346\\245\\255(axrt341).form\"", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 151}, {"commit_hash": "1467a8cb257bf88af6a52f1b9f14b154f896f722", "commit_訊息": "Q00-20190125005 修正行動表單頁籤元件設定多語系沒效果", "提交日期": "2019-01-25 21:21:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "29b5a5a12bf8b06a681d0843880515d47033d0ad", "commit_訊息": "Q00-20190125001 修正行動表單發起、待辦選擇主部門的提示訊息點取消後遮罩跟讀取圖示不會消失", "提交日期": "2019-01-25 20:19:00", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "322784c4edef44368294aec7070a9e18a5da0ffe", "commit_訊息": "Q00-20190125006 修正行動表單流程主旨與選擇部門欄位在英文語系時沒對齊", "提交日期": "2019-01-25 20:06:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a0b2a1c23c5af083dd72aefbeed51c87e9845a81", "commit_訊息": "<V57>Q00-20190124004 二次 修正:ISO新增單的文件類別開窗,原先有選資料,但再次開窗時資料沒有帶回類別樹裡", "提交日期": "2019-01-25 17:04:16", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ac9f3606ef6bdd0d8a54745b8271a92d6f4fee27", "commit_訊息": "bpmTable沒有依據gridPadding預設值載入", "提交日期": "2019-01-25 14:45:05", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65a8ab325952a58ace439741a4916685f2032f7c", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-01-25 10:42:12", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "36f459604fd8f7ff45840fb0db1396909af4bc59", "commit_訊息": "S00-*********** 修正在WEB表單設計師設計絕對位置表單，按下發佈腳本編輯過的*沒有消失", "提交日期": "2019-01-25 10:39:26", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "920681ff7204cc0f383a8e7509da691d5da852ee", "commit_訊息": "C01-20190104006 Grid如果載入太多資料，標頭(Header)欄位會些許位移、不對齊修正", "提交日期": "2019-01-25 10:33:14", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "323bd1952a92d4a315098c488e61dcaea51284fe", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-01-25 09:37:44", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7a547a913f61f207d299eb3235096cd05738bcab", "commit_訊息": "<V57>Q00-20190124004 修正:ISO新增單的文件類別開窗,原先有選資料,但再次開窗時資料沒有帶回類別樹裡", "提交日期": "2019-01-24 16:42:49", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76a32dce0af36cbb6c18edff98217ea4bbc9c246", "commit_訊息": "Q00-20190124003 修正代辦清單頁面的關注事項的內容值有誤", "提交日期": "2019-01-24 13:55:32", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e9f274a237539c28f4cd2f7e1e81ed402f6a59fd", "commit_訊息": "Q00-20190124002-2 補上少檢查checkbox勾選", "提交日期": "2019-01-24 11:39:51", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c64d4e9e580e93cda91eed41c480084c22c84f2a", "commit_訊息": "調整表單設計器程式碼樣版行動版相關名稱", "提交日期": "2019-01-24 10:17:14", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b349d4c3f6bb8db2ca979c9cc4499e8a67da846f", "commit_訊息": "Q00-20190124001 調整入口平台整合設定中新增按鈕與過濾分類按鈕顯示邏輯 --當有資料時隱藏新增按鈕 --依照整合平台顯示左上角過濾分類按鈕", "提交日期": "2019-01-24 10:09:46", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4d11cb993a88ee4cd3f6fb7d3ef427086250a5bd", "commit_訊息": "Q00-20190124002 修正員工工作批次轉派 開窗輸入密碼後沒有成功轉派", "提交日期": "2019-01-24 10:03:29", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6c2ecfb85734ac0a077afcdc25a8c23eaeaa89a7", "commit_訊息": "Q00-20190123002 修正一般表單Grid clearBinding後下拉與選項元件不會清空的問題", "提交日期": "2019-01-23 19:16:37", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppGridTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "96b8a2531955eb4bfae561d1e7b1ccf032f6c14d", "commit_訊息": "C01-20190103005 調整寫法", "提交日期": "2019-01-23 17:39:47", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d4b7ce498d1664c28b60abbad77cc24c9c9ce61", "commit_訊息": "Q00-20190123001 行動版不支援產品開窗類型\"自訂義開窗\"", "提交日期": "2019-01-23 17:14:56", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22326e12572e5ed1f34727f6eba7689bf5141b1e", "commit_訊息": "Q00-20190123004 修正企業微信聯絡人英文語系時列表會有跑版問題", "提交日期": "2019-01-23 17:12:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContactV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cb509c996ce17f4291b97f350d04fac01eb97da0", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-01-23 16:20:53", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "8930d4d1f77e46e1042c41fd6b84661da47a4ebb", "commit_訊息": "Q00-20190123005 修正IMG處理的流程過濾功能當流程緊急度與流程名稱都有選擇時會撈到空的資料", "提交日期": "2019-01-23 16:20:11", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7528ff1d9b72dff5ac9aba2f1095a528f33ed8a4", "commit_訊息": "Q00-20190123003 修正企業微信批次簽核簽核意見輸入框位置跑版", "提交日期": "2019-01-23 15:43:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba7433516f213294227259e70c1a4a90fd9f6c8f", "commit_訊息": "Q00-20190123006 修正IMG發起流程清單會載入重複的資料", "提交日期": "2019-01-23 14:45:25", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b37847c9c1b253919116c596da2fd43da2945021", "commit_訊息": "Q00-20190118009 二次修正:移除IMG發起流程清單回傳網址中串的mobile_token參數", "提交日期": "2019-01-23 14:22:24", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b84860f280d5e40bd94e30e35c2062a7f41d7223", "commit_訊息": "修正IMG在流程設定使用詳情簽核但後台未配置跳轉鏈接時呼叫返回會返回到快速簽核畫面 --IMG總層數大於3層時返回至第一層 --IMG總層數小於3層時返回兩層", "提交日期": "2019-01-23 11:01:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cd5bfc403af7068873671a52d5928e572c0f5ee3", "commit_訊息": "C01-20181211001 修正ISO文件總管(user為管理程式權限設定權限的人員)下載原始檔出現異常", "提交日期": "2019-01-22 11:33:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8b5092faab8581a40c1d1e8279ad874c37ee168c", "commit_訊息": "Q00-20190121001 修正代辦上方工具列的列印按鈕顯示邏輯問題", "提交日期": "2019-01-21 17:02:14", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e112478fbbd442917151f8417361eaa226eacef8", "commit_訊息": "C01-20190114002 修正列印模式 表單設定不顯示簽核意見還是有顯示", "提交日期": "2019-01-21 16:32:03", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "862fcf31695549a0c0764e17933e3f56bf91f1c9", "commit_訊息": "Q00-20190118010 修正鼎捷推播、行事曆的待辦表單打開一片空白的議題", "提交日期": "2019-01-21 16:29:38", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e656746344bc94a065113a5b5ff3a2dd52cdb4ec", "commit_訊息": "Q00-20190118002 修正行動版IOS預覽pdf檔案調整大小往下滑後圖片會消失的問題", "提交日期": "2019-01-21 16:27:49", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "99aea86f1291925c51d20cffc6ce8dcb7d9fd46f", "commit_訊息": "Q00-20190118009 從IMG中間層打開詳情都會顯示互聯認證失敗", "提交日期": "2019-01-19 19:25:13", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "751c6a7d2ac3aa8d0110665d94b827bf07921958", "commit_訊息": "Q00-20190118008 IMG處理的流程重要性過濾無效", "提交日期": "2019-01-19 19:22:38", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71d9b1e17c024d4cdcf11c54491b9bf95e9fcff9", "commit_訊息": "Q00-20190118003 修正發起流程無掛載表單時的提示訊息有誤的問題", "提交日期": "2019-01-19 19:17:14", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f74620a17746a282cb877b795059a15d1211f65", "commit_訊息": "Q00-20190118005 修正移動端ESS通知詳情表單的顯示流程無法顯示問題 --參考ajax寫法", "提交日期": "2019-01-19 19:10:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9479a1580464827f66ef6acb414d0bfa3016c4e9", "commit_訊息": "Q00-20190118004 修正移動端TextArea進入表單畫面樣式異常 -- v57版本應為一開始進入只顯示一行", "提交日期": "2019-01-19 18:20:55", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6750afa0d573c7f30456a6203c8fe695adf8a672", "commit_訊息": "Q00-20190118006 修正Android手機訊息框顯示位置偏右", "提交日期": "2019-01-19 17:29:35", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f76e61e4cf8ac6bd2ce3ab5b4898079d113f8658", "commit_訊息": "Q00-20190118007 修正移動端SerialNumber元件有label時高度沒有對齊", "提交日期": "2019-01-19 17:22:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c2226e35f044998b37c030c43020722ccc3636f2", "commit_訊息": "Q00-20190118011 修正追蹤流程清單與已轉派流程清單RESTful服務更換頁數參數page沒效果", "提交日期": "2019-01-19 14:47:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "b3d8783cf8519b47d7b6066ffb69fa2cc9a845fc", "commit_訊息": "Q00-20190119001 修正離職人員批次轉派 開窗輸入密碼後沒有成功轉派", "提交日期": "2019-01-19 14:40:30", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93a7e4162a3c89e534d450c0bc98f4f1edbc1d5d", "commit_訊息": "將這次發版不發佈的功能註解 1.IMG詳情表單畫面轉由他人處理功能 2.IMG詳情表單畫面簽核歷程上一關卡與當前關卡", "提交日期": "2019-01-19 14:09:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4b62c1d0f6fffa1652c689812421f27b7d00fa29", "commit_訊息": "將這次發版不發佈的功能註解", "提交日期": "2019-01-19 13:50:30", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "b74afd38ba0d25e1afbd2ce6190bdff7533472e7", "commit_訊息": "C01-20190107003 修正V57ISO下載文件原始檔異常", "提交日期": "2019-01-19 11:04:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5f5f1ca83fe9b1d6924cfc679934338af0d73f12", "commit_訊息": "將這次發版不發佈的功能註解 1.IMG追蹤、通知、待辦列表增加區分中間層與詳情提示訊息 -- 此功能移除因樣式尚未決定", "提交日期": "2019-01-19 10:45:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1ea273457e28bb200f3feb5f5890003ab0205034", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-01-18 19:06:15", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "6b385b6ae1b2056c45416adf56db45e2d6143a50", "commit_訊息": "將這次發版不發佈的功能註解", "提交日期": "2019-01-18 19:05:44", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWinUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.4.2_updateSQL_Oracle.sql", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.4.2_updateSQL_SQLServer.sql", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 13}, {"commit_hash": "4abb728fa0947066e1983b69c6d18246a4afbb15", "commit_訊息": "Q00-20190118014 修正看不到OA首頁的問題", "提交日期": "2019-01-18 18:58:57", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0eea5f94bdc04ed4b40ec3cba13f67493d4e9be1", "commit_訊息": "Q00-20190118012 修正發起關卡的workItem的mcloud屬性錯誤", "提交日期": "2019-01-18 13:58:56", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0d3ab1501756378ec4ee3bac8e56f4e15022aadd", "commit_訊息": "C01-20190118001 BPMv57 掛服務，無法停止服務", "提交日期": "2019-01-18 11:25:07", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/jboss-cli.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/service/service.bat", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0d84ccf54e67dd20a39d0433798cca7791892ccc", "commit_訊息": "<V57>Q00-20190115003 修正:關注項目的重要流程維護作業 1.不提供修改的功能確可以填寫資料,因將按鈕Lock 2.調整只有administrator可以選擇使用者", "提交日期": "2019-01-17 18:39:33", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1450dfc2d8a7506740363ba7c34b3b790e738a36", "commit_訊息": "<V57>Q00-20190117002 修正 :ISO文件使用記錄查詢 ,DB有資料,查詢後畫面卻無任何資料", "提交日期": "2019-01-17 15:54:55", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5541ab323cba7f5a5ec887a6345c70a359be5cff", "commit_訊息": "C01-20190109005 修正追蹤流程表單列印會多出一塊很奇怪的空白", "提交日期": "2019-01-16 18:33:15", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3aeb878920c776b387b1fe6137b5ee689d0fb5ab", "commit_訊息": "修正v57版本移動端會因連續點擊發生異常 --v56修正在A00-20181225003", "提交日期": "2019-01-16 17:26:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "abb5efcd61ee6c96026471b702312c7962511c36", "commit_訊息": "C01-20190104005 讓ESS流程圖顯示完整(原本Iframe沒張開)", "提交日期": "2019-01-16 11:07:03", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a646e87b21d06406594a777f6cdd640ef8efc4b3", "commit_訊息": "Q00-20190115001 針對獨立模組的url做權限控制", "提交日期": "2019-01-15 16:17:21", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/CustomModuleUserInfoCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e82ae5c569006818953ee0b767e3005efbc6a01a", "commit_訊息": "更新ESS表單 --HR同仁協助調整表單內容 --C01-20181101002 BPM移動簽核上單身欄位順序錯亂問題", "提交日期": "2019-01-15 15:59:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF05\\345\\212\\240\\347\\217\\255\\350\\250\\210\\345\\212\\203\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF17\\351\\212\\267\\345\\201\\207\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF20\\345\\207\\272\\345\\267\\256\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF21\\345\\207\\272\\345\\267\\256\\347\\231\\273\\350\\250\\230.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF24\\350\\252\\277\\350\\226\\252\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF28\\344\\272\\272\\345\\212\\233\\351\\234\\200\\346\\261\\202\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF31\\346\\213\\233\\350\\201\\230\\350\\250\\210\\347\\225\\253.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF32\\346\\207\\211\\350\\201\\230\\344\\272\\272\\345\\223\\241\\351\\235\\242\\350\\251\\246.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF33\\346\\207\\211\\350\\201\\230\\344\\272\\272\\345\\223\\241\\347\\255\\206\\350\\251\\246.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF50\\347\\217\\255\\346\\254\\241\\350\\256\\212\\346\\233\\264\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF51\\345\\212\\240\\347\\217\\255\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213(\\345\\244\\232\\346\\231\\202\\346\\256\\265\\345\\244\\232\\344\\272\\272).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF60\\350\\254\\233\\345\\270\\253\\350\\263\\207\\346\\240\\274\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF62\\345\\237\\271\\350\\250\\223\\351\\240\\220\\347\\256\\227\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF63\\345\\237\\271\\350\\250\\223\\351\\234\\200\\346\\261\\202\\346\\216\\241\\351\\233\\206.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF64\\345\\237\\271\\350\\250\\223\\350\\250\\210\\347\\225\\253\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF66\\345\\237\\271\\350\\250\\223\\350\\251\\225\\344\\274\\260.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF74\\350\\263\\207\\346\\272\\220\\347\\224\\263\\351\\240\\230.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF75\\350\\263\\207\\346\\272\\220\\346\\255\\270\\351\\202\\204.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF04B\\345\\212\\240\\347\\217\\255\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF23B\\350\\252\\277\\350\\201\\267\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF26B\\347\\215\\216\\346\\207\\262\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF30\\350\\243\\234\\345\\210\\267\\345\\215\\241\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\\346\\227\\227\\350\\211\\246/ESSF71\\350\\253\\213\\345\\201\\207\\347\\224\\263\\350\\253\\213(\\346\\211\\271\\351\\207\\217).form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF77\\350\\226\\252\\350\\263\\207\\347\\265\\220\\346\\236\\234\\345\\257\\251\\346\\240\\270.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF80_\\345\\212\\240\\347\\217\\255\\350\\250\\210\\345\\212\\203\\346\\230\\216\\347\\264\\260\\346\\222\\244\\351\\212\\267.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF52C2\\347\\217\\255\\346\\254\\241\\350\\256\\212\\346\\233\\264.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF52\\346\\212\\225\\347\\217\\255\\347\\224\\263\\350\\253\\213.form\"", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\\346\\265\\201\\351\\200\\232\\347\\211\\210/ESSF53\\346\\216\\222\\347\\217\\255\\347\\242\\272\\350\\252\\215.form\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 28}, {"commit_hash": "7de45fadc80ffbf07365444bc51e128843378d35", "commit_訊息": "還原誤簽的程式碼", "提交日期": "2019-01-14 11:11:38", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "242d6426a256ce00e0bf91118e1ec0fc9b659615", "commit_訊息": "修正在找不到工作項目時，表單一片空白的問題", "提交日期": "2019-01-11 18:56:48", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "467510b460b85101abac8e3e5d0c4f442776d1a9", "commit_訊息": "修正行動版外部開窗方法在回傳多筆時資料錯誤", "提交日期": "2019-01-11 18:54:24", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWinUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5097040e8616888db23f6cfa6af1b758d0ec0e35", "commit_訊息": "C01-20190108003 將自動簽核的通知信範本修改為與跳過關卡用同一個範本", "提交日期": "2019-01-11 16:39:06", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "53659dd19a51546c081e96c7bc4fe35a16a5e687", "commit_訊息": "修正中間層取工作來源與流程狀態為空時造成的錯誤", "提交日期": "2019-01-11 14:38:05", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "59bae1dea90ecc1e0f961a91ffc9fb64391feabc", "commit_訊息": "調整鼎捷移動登入時更新MobileOAuthWeChatUser表的服務", "提交日期": "2019-01-10 17:20:33", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "39bbbd1ed128ffdf7a80b1485bd5b960c219e4d2", "commit_訊息": "C01-20190102002 待辦-重要流程沒有正確顯示", "提交日期": "2019-01-10 15:07:33", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "171a84dbf82a730a00e4d2a042ed9d8a93e21aa5", "commit_訊息": "Q00-20190110001 頁籤(SubTab) 補上 多語系", "提交日期": "2019-01-10 11:57:50", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "66e909229a771a68ad376d721b356743e868e47c", "commit_訊息": "調整行動版外部開窗返回值的功能", "提交日期": "2019-01-10 10:38:01", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWinUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/jdajia.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.4.2_updateSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.4.2_updateSQL_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "9aafd5cfe32778128b06b34ace4557d2e111eb55", "commit_訊息": "Q00-20190109001 調整js function 順序", "提交日期": "2019-01-09 16:40:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc1f5a44b56a5fcaf967ca57739098fcd50c1484", "commit_訊息": "新增IMG詳情表單畫面增加轉由他人處理功能", "提交日期": "2019-01-09 11:51:03", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "2e280f2db26bcb7b4589afdf367f3a6face655af", "commit_訊息": "調整因Android喚起鍵盤時會再呼叫resize導致高度改變問題", "提交日期": "2019-01-09 11:37:23", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "9061ea72178e6f6e6a2afa9d4c167fc301bd3367", "commit_訊息": "C01-20181228001 修正關卡剩餘時間顯示只有\"小時\"的問題", "提交日期": "2019-01-08 18:12:20", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b27893bb4eebce30c31721a876e5f15dc782a383", "commit_訊息": "調整行動版轉派功能、取經常選取人員清單ajax格式", "提交日期": "2019-01-08 10:43:51", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a82e4c9f03d9323ed65f3da1f72856a4213916d", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-01-07 19:10:59", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "85a9850564db4ea0b549e6ef9f6db6794ea4270e", "commit_訊息": "調整詳情預計關卡資訊樣式", "提交日期": "2019-01-07 19:10:39", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "2e6abcbe3bacb86ac43141d2d24b2bb6d5a849bc", "commit_訊息": "調整IMG詳情表單新增上一關資訊的通知關卡顯示異常", "提交日期": "2019-01-07 19:08:43", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "521279344e12397f96bfcb8a17edf8f80a71119c", "commit_訊息": "修正企業微信的ESS表單展開/收合按鈕異常", "提交日期": "2019-01-07 17:59:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b011f7e66fc3c442e13144fd69c0097cab7be385", "commit_訊息": "C01-20190103002 修正IMG的ESS表單展開/收合按鈕異常 --之前將ESS表單鎖定編輯時一併將所有事件還原導致 --此次調整判斷展開/收合按鈕不做還原動作", "提交日期": "2019-01-07 17:56:14", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "64ad74011fb2c03c6ccf21a4ec06b85eeccbde05", "commit_訊息": "C01-20190103003 修正TT流程使用IMG中間層退簽會失敗", "提交日期": "2019-01-07 16:46:20", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "01df5de470c438fbc31d91c497c49dc0da5bef17", "commit_訊息": "<V57>C01-20181218014 調整 :發起時前端流程發起人與Session中不一致,不與許發起,避免蓋單", "提交日期": "2019-01-07 15:14:51", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "cf32d268b9f0ded97bf8edd8ad3182aad064743c", "commit_訊息": "<V57>二次調整 S00-20181224001:調整ESS流程發起時會有lodaing畫面，避免使用者重複發起", "提交日期": "2019-01-04 18:13:21", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0df0ac2b058c502723c851a1c11960de75f74ee0", "commit_訊息": "C01-20181221003 追加修正TT拋單後IMG收不到推播的問題", "提交日期": "2019-01-04 17:51:31", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa1679b2b6ce171b6d9382c93a781c709a07d343", "commit_訊息": "C01-20190104002 修正如果流程設定為列印模式絕對位置表單按下列印後開啟預覽畫面表單排版會亂掉", "提交日期": "2019-01-04 17:27:30", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "347339f62ba290b31fb4638ff46981c3e7dff742", "commit_訊息": "Q00-20190104001 修正畫面太小不會有橫向ScrollBar的問題", "提交日期": "2019-01-04 14:10:12", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8e19174f27269875f0ced6f285b3b5af221cb90", "commit_訊息": "新增行動版轉由他人處理,經常選取人員ajax服務", "提交日期": "2019-01-04 12:16:36", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmWorkItemDataVo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "31b8d95ce5c7690f5b11ba55ee997cb8aaa2189b", "commit_訊息": "<V57>S00-20181224001 發起ESS流程時,將發單按鈕鎖住,避免使用者重複發單造成單據異常 如果ESS有回饋錯誤訊息,再將發單按鈕開啟。", "提交日期": "2019-01-04 11:51:43", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "927a8ec479d3560843cf5032a063ed1b9655f496", "commit_訊息": "移除多餘的log", "提交日期": "2019-01-04 11:27:31", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d996405d5dab7745235e44c246cbbd1305ad2a5", "commit_訊息": "新增IMG中已發起追蹤流程的快速簽核加入流程狀態資訊", "提交日期": "2019-01-03 20:06:31", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e5720b1c9a341ff4973c1192e3a6e77bc528cc1f", "commit_訊息": "新增IMG中詳情表單畫面加入流程狀態", "提交日期": "2019-01-03 20:04:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "ad6f9a0d4871cb032f0d37bf20505bef26c38898", "commit_訊息": "C01-20190103005 若退回重辦關卡或者方式為選擇需提示", "提交日期": "2019-01-03 19:09:36", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "773f696d31bd3a37db524cd14a58d3261bf9694c", "commit_訊息": "Q00-20190103001 將流程代理人選擇來源部門/專案的開窗從多選改為單選 避免選擇太多部門與太多流程 查詢帶給資料庫太大負擔", "提交日期": "2019-01-03 14:56:16", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df079faa920481b9389f169ecfb5a9e5dac2c458", "commit_訊息": "C01-20180606001 調整批次簽核片語選擇方式與一般簽核一致、調整批次簽核片語開窗大小", "提交日期": "2019-01-02 18:32:02", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6c5aa0d579feeda3a5cd848ebc90e89ed4eca55c", "commit_訊息": "調整App的錯誤頁面樣式與顯示資訊", "提交日期": "2019-01-02 17:12:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/NotRegisterApp.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 15}, {"commit_hash": "0c8dd3a36f1016b447ccf84ccbb672bec11df935", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-01-02 13:47:40", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "8f124753442e3815ba632bdb024361b8d6d34563", "commit_訊息": "調整詳情預解析工作來源參數名稱與簽核歷程的一致", "提交日期": "2019-01-02 13:47:10", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "36cb1e4bdd76e70682952b56fafcb065d3b2dd2d", "commit_訊息": "補上詳情表單畫面加入工作來源漏調整的樣式 --選擇發起/派送部門樣式iphobe5跑版", "提交日期": "2019-01-02 11:23:47", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ffb5adeffa29c4a67f6faf763834745a70b06bd", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2018-12-28 17:15:40", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0c7a059ccae3c8f66aa289af482ab04c24bdfb8b", "commit_訊息": "C01-20181227004 降低左侧menu因載入速率F12有錯誤", "提交日期": "2018-12-28 17:15:12", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "bdbf4e2d2320236d42244ab050f1b417fcb0604b", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2018-12-28 15:33:28", "作者": "<PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "d61c3babd46a907a674321df03ed15ba64c167d7", "commit_訊息": "修改越南多語系", "提交日期": "2018-12-28 15:33:08", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/DeleteCMProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/MoveProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/NewProcessPackageFromToolBarAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/NewProcessPackageFromTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenApplicationManagerAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenProcessViewAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/RemoveProcessDefAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/RemoveProcessDefinitionAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveAllPackagesAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SavePackageTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/ShowHistoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoCheckoutPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "6b36d87dc9972a62a4dd12af67fd5483967ab4fe", "commit_訊息": "C01-20181108003 前一筆修正有些小問題 radio checked狀態不見 重新修正", "提交日期": "2018-12-28 15:02:57", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a90f971d5ad1f6634573ba44637077f0bb47e7d", "commit_訊息": "調整IMG詳情表單新增上一關,預測下一關資訊 --Action增加多語系 --調整當前處理者樣式 --調整展開/收和資訊", "提交日期": "2018-12-28 14:35:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "799dad88152aa866a90fcd643b3b5e47cdfcb9ea", "commit_訊息": "IMG新增客制開窗返回值的方法", "提交日期": "2018-12-28 13:33:12", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWinUtil.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d4f6a6f4ac219317fabf0c2cd8be1e1d491ec244", "commit_訊息": "IMG詳情表單新增上一關,預測下一關資訊", "提交日期": "2018-12-28 13:32:36", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentBeanRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/MobileProcessCommentVo.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "9dfbce0467f98de4b6d219209db87d3390173e2b", "commit_訊息": "新增IMG中上一關卡資訊與工作來源相關語系", "提交日期": "2018-12-28 13:15:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f93fd8f89de838a72c4516b2af39555f3045b9ed", "commit_訊息": "新增IMG中詳情表單畫面加入工作來源", "提交日期": "2018-12-28 12:15:30", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e54d6a0fe1d12600c8ded2e3cb9979331208ac20", "commit_訊息": "C01-20181108003 修正模擬使用者多人關卡不管選擇哪個人 都會跳成最後一個", "提交日期": "2018-12-28 10:14:39", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "04aa16c510d47b873ad383a5fd77bd68c0e532e1", "commit_訊息": "A00-20181220002-2 補上上次Commit少Commit一隻程式", "提交日期": "2018-12-27 14:37:01", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cd5f5d00b22d2fcd7a9aa0c72d4d3501a0663f76", "commit_訊息": "新增IMG中待辦流程的快速簽核加入工作來源資訊", "提交日期": "2018-12-27 13:49:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0e9a64c0ac1aeaea2e121d4b6137ba4b0c1c1bf5", "commit_訊息": "C01-20181225005 修正BPMAPP中若為ESS表單不應該出現儲存表單按鈕的問題", "提交日期": "2018-12-26 18:42:05", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6eec77f7a9efc3da9ecc8d00cc2b612159b53f38", "commit_訊息": "Q00-20181226001 修正如果表單TextBox/TextArea/Password在流程設定隱藏下一關資料會遺失", "提交日期": "2018-12-26 18:01:48", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d8986bb62256c5ed24977a357c3e8a9d51da8ed6", "commit_訊息": "C01-20181221003 修正TT拋單後IMG收不到推播的問題", "提交日期": "2018-12-26 12:14:12", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "abeffacc963b5f797fbce866e0d88b5208a546dd", "commit_訊息": "修正IMG中取標題字段的邏輯判斷錯誤問題", "提交日期": "2018-12-25 17:45:15", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "24e4aa8316f99c45d8f4e32016e10354178620aa", "commit_訊息": "修正IMG的已轉派清單應用不會出現處理人資訊問題", "提交日期": "2018-12-25 17:37:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fafa25b3e4f354e4b271117c136fdec0f5578cda", "commit_訊息": "A00-20181225001", "提交日期": "2018-12-25 15:31:08", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "75aec1c2a9da864b3f39d90b0a8474c69d5d5167", "commit_訊息": "還原預設icon", "提交日期": "2018-12-25 14:58:38", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/_getModuleIcon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "45b650a4904ce3df9b4efa2cb50b1c84c5617aea", "commit_訊息": "新增 :E10收尾項目-付款申請單呼叫E10開窗功能", "提交日期": "2018-12-24 18:21:09", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/StringHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/E10.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/E10Mgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/E10Form.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/SubGridTransfer.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "a8d93a77135bdf6fb05f3c4837d38c7cd2c60e0f", "commit_訊息": "<V57>C01-*********** 修正 :因為每30秒會向後端詢問是否有訊息要通知，重新啟動BPM站台時，有呼叫後端的行為會彈出異常訊息", "提交日期": "2018-12-24 17:29:10", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e294ea7ec532269b6dabc56ea9d07380bfa503d", "commit_訊息": "Q00-20181002002 二次修正 :有些驗證功能的表單元件，設定需要驗證時，但表單畫面沒有產生紅色星星的標示", "提交日期": "2018-12-24 16:52:49", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e774185ca80b408a8c5b4bb7f3cce76c01208568", "commit_訊息": "Q00-20181002002 修正 :有些驗證功能的表單元件，設定需要驗證時，但表單畫面沒有產生紅色星星的標示", "提交日期": "2018-12-24 16:00:30", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "885140ad81d9609857ad3d4aec301ece1473918b", "commit_訊息": "新增IMG最常處理的流程功能", "提交日期": "2018-12-24 13:56:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "20a05635fe31ab6109a899e0bcff41120785b5de", "commit_訊息": "修正mobile的待辦、通知、追蹤listReader用條件mutilProcessId查詢時會取不出資料問題", "提交日期": "2018-12-24 12:18:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d9735d1a10e9f6efdab0c4c185fa752042b568d5", "commit_訊息": "A00-20181220002 避免有些情境轉碼太多次 最後反轉一次不夠 要轉之前先反轉一次", "提交日期": "2018-12-22 11:30:50", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/StringUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "57c318bf32565631e7005842c34876272ea40b01", "commit_訊息": "<V57> A00-20181122001 二次 修正 :多語系匯入功能，因Key大小寫導致無法匯入。", "提交日期": "2018-12-21 18:59:47", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "522df69723e40141febda38d0b2699dc02f943bd", "commit_訊息": "<V57>Q00-20181221003 調整 :攻略雲維護作業中的工作圈，可透過開窗選取", "提交日期": "2018-12-21 17:15:36", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/IndicatorDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/IndicatorListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/iwc/IWCMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/IWC.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IWCMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/IWCIndicatorDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaIntSys.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "8c79b6505807cbb6b9e084bae3d19a9e2b5a71fd", "commit_訊息": "<V57>A00-20181207002修正:簽核流設計師中設定關卡處理者為角色或職稱時，會因中文字無法帶出資料", "提交日期": "2018-12-21 17:00:08", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "864a7be58a5892eb00c7a93481262d13b9dd2bad", "commit_訊息": "修正企業微信的Android手機使用加簽頁面\"清空、全選、確定\"三個按鈕會跑版", "提交日期": "2018-12-20 17:34:47", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5aec515c0bf58f24a681dbc6264ae7382a37e709", "commit_訊息": "C01-20181218002 修正IMG的Android手機使用加簽頁面\"清空、全選、確定\"三個按鈕會跑版 --型號:ASUS_Z012DA", "提交日期": "2018-12-20 17:11:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a8b77d9a1ada1f49684c5de610a4ba9b6cb3a3d", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2018-12-20 17:08:19", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "506efaa928fa9f96a7e6258b232bb701628bf974", "commit_訊息": "C01-20181127001 修正:客戶用LDAP登入 ,因判斷離職的邏輯有問題,導致無法登入", "提交日期": "2018-12-20 17:08:07", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0d644601756de71fb82ef9f4bf704f79886a2f0e", "commit_訊息": "C01-20181218007 修正Android手機流程主旨 簽核意見畫面跑版 --型號:HTC U11+(6吋手機)", "提交日期": "2018-12-20 17:00:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "26aa3ac55b3871febb4717c63388812ce823c481", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2018-12-20 16:52:57", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "1b44d9a245c55a6158057f136c91630a9e9f4e38", "commit_訊息": "Q00-20181220003 調整:呼叫E10的RESTful API中Header 要傳入的BPM IP 改從WorkFlowServer抓取", "提交日期": "2018-12-20 16:52:08", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e10bf0cc5dc903aea4ac21676802287ad3e3552", "commit_訊息": "優化越南多語系內容", "提交日期": "2018-12-20 16:46:52", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CreateCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CreateCMProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/EditCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/GridAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessDefinitionAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromToolBarAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenFromXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/RemoveProcessDefAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoCheckoutPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoCheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/common/common_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/DesignerController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/DesignerIFrameController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/SecurityManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/swingext/AbstractMCERDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/AutoDeliveryEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BlockActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BlockActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BlockActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BpmUserTaskEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlineConditionCellEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlineDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlineMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlinesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DealOvertimeActivityTypePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DecisionActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DecisionActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormAttachmentsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormSelectDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/InvokeActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/RouteActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/RouteActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/UnReexecuteAbleAct_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/ConditionCellEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionConditionConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionRuleListEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ApplicationManagerPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ApplicationsToolsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/FormalParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/HttpCodesSelectorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeSessionBeanToolMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeSesstionBeanToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeWebApplicationToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeWebServicesToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/MailApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ScriptEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/TiptopSessionBeanDialogChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationWizardPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WebApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/batchio/BatchIO_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ActivityChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ActualParameterChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ApplicationChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ApprovalLevelChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ApprovalLevelComboBoxRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/BpmUserTaskChooserPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/BpmUserTaskDefinitionChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/CalendarChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/EntireFormChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/I18NEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/OrganizationBpmUserTaskChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/OrgnizationRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/PackageCategoryChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/StrategyAssignmentPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/UserRelationshipChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/ActualParameterCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/PatternEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/PattternEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/XMLInstanceGeneratorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/property_editor/PropertyEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/composite/CompositeEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/composite/CompositeEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/composite/CompositeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionActivitySimplePropertiesShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionRuleListEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/ConditionExpressionPatternBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/DecisionConditionBatchConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/transition/ConditionExpressionEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/AbolishDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/CMPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/CheckInDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerSignOnDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DiagramEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/ReleaseDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/SaveAllDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/SysintegrationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/AttachmentAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormalParameterMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/AttachmentAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/AttachmentTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/BasicTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/RelevantDataRemover_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessTemplateModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/UserDefineModeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/TransitionDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/TransitionDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/EmployeeCreator_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/FunctionViewer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/GroupEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MaintainAbstractRoleDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgUnitEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerGroupPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerOrgUnitEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerOrgUnitPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchProcessDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/UserCompanyEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/DepartmentNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/view/OrgSelectorTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/AbstractController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/AbstractStep_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/CfgTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Config_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ImporterMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Login_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ModelMainTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/NewOrgDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/StepTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Term_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/TreeMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/status_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CreateCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CreateCMProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/EditCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/NewProcessDefinitionAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenFromXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/RedoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/RemoveProcessDefAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveAllPackagesAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SimpleFDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoCheckoutPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoCheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/ZoomToFitAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/AccessRightController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/SystemConfigController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/ToolAuthController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/DataAcsDefDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/DataAcsDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/LdapDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/LdapDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/LdapTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/LdapValidateDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/MailTestDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/SystemConfigPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/TimerWorkScheduleDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/util/CheckPassDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/util/CheckPrsDueDate_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/common/common_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/DesignerController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/DesignerIFrameController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/swingext/AbstractMCERDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/DueDateEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/AutoDeliveryEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BlockActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BlockActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BlockActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DeadlineDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DeadlineMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DeadlinesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DealOvertimeActivityTypePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DecisionActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/FormAttachmentsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/FormSelectDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/InvokeActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/InvokeActivityMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ParticipantEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/RouteActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/SubflowActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/SubflowActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/UnReexecuteAbleAct_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/ConditionCellEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionConditionConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionRuleListEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ApplicationManagerPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ApplicationsToolsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/FormalParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/HttpCodesSelectorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeSessionBeanToolMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeSesstionBeanToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeWebApplicationToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeWebServicesToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/MailApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ScriptEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSInvocationEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSInvocationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSInvocationWizardPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/batchio/BatchIO_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ActivityChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ActualParameterChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ApplicationChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ApprovalLevelChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/CalendarChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/I18NEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/OrganizationParticipantChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/OrgnizationRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/PackageCategoryChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ParticipantChooserPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ParticipantDefinitionChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ParticipantInfoPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ProcessRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/StrategyAssignmentPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/UserRelationshipChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/ActualParameterCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/ApplicationTypeCellRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/PatternEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/PattternEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/XMLInstanceGeneratorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/XMLInstanceGeneratorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/composite/CompositeEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/composite/CompositeEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/composite/CompositeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/DecisionActivitySimplePropertiesShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/DecisionRuleListEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/batchChange/ConditionExpressionPatternBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/transition/ConditionExpressionEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/AbolishDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/CMPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/CheckInDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerIFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerSignOnDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/ReleaseDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/notification/NotificationInfoPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/notification/NotificationManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/RelationManEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/AttachmentAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormalParameterMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/AttachmentAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/AttachmentTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/BasicTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/RelevantDataRemover_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessTemplateModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/VariableNamesList_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/ConditionExpressionEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/TransitionDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/TransitionDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/tree/packagetree/ProcessTreeNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/src/resource/SysAuthorityRsrc_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/Language_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/MainRsrcProperties_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/Rsrc_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/tiptopcfg_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 342}, {"commit_hash": "0b3db446635ac211402f22e1a6d2e45b56af9464", "commit_訊息": "還原昨天調整的repository_user.xml與DML_SQL", "提交日期": "2018-12-20 11:25:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a19e6c2c2e98870751c39e2e46069662ba79fd7e", "commit_訊息": "Q00-20181220001 修正有WIN2016的VM無法判斷是VM的問題", "提交日期": "2018-12-20 10:09:40", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/GuardServiceUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "376431f804eb843a87e84c7ed4467e60c60bdbb6", "commit_訊息": "因mobilitySignOff新增欄位更改成可為null，調整repository_user.xml與SQL", "提交日期": "2018-12-19 19:21:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5d6db7b48a787cadfbd1230b73574cfe19781f79", "commit_訊息": "新增欄位,不允許設定 default", "提交日期": "2018-12-19 14:45:44", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "6ce7da6572edc6816614214f7367967e2b5542ad", "commit_訊息": "新增鼎捷移動中間層按鈕依照關卡權限顯示功能", "提交日期": "2018-12-18 18:19:27", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/Operation.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "10d53e359bfe6db2ed4456cf8d6992be369bbc12", "commit_訊息": "C01-20181214001 修正IMG中處理的流程依重要性排序時載入資料時會撈到重複的問題", "提交日期": "2018-12-18 15:47:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f35c88a3a2700401fe9611e8b0c983e100c747c9", "commit_訊息": "修正行動版已轉派清單在追蹤類型為所有流程時UNION前後的查詢欄位不對等的問題", "提交日期": "2018-12-18 12:08:18", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "56c8e0145268a853ee6d9f6962acc177248a3f5c", "commit_訊息": "在設計器中加入越南多語系", "提交日期": "2018-12-18 11:07:17", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/AdvancedFDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CopyAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CreateCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CreateCMProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/DeleteCMProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/EditCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/GridAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/MoveProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessDefinitionAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromToolBarAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromXPDLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenFromXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenNotificationManagerAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenObjectAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenProcessViewAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenSoftscoreXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/RemoveProcessDefAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/RemoveProcessDefinitionAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveAllPackagesAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveAsObjectAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveCMProcessPackageActionFromTree_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SavePackageTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/ShowHistoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SimpleLDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoCheckoutPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoCheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ADMIDValidator_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/OnlineUserMgtController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/SystemConfigController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/controller/ToolAuthController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/main/ADMMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/onlinemgt/SendMessageDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DataAcsDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/DocServerTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/LdapValidateDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/MailTestDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/SystemConfigPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/TimerWorkScheduleTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/sysconf/WorkflowServerTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/ADMProgressDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPassDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/adm/view/util/CheckPrsDueDate_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/common/SignOnManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/common/common_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/DataAcsDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/DesignerController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/DesignerIFrameController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/SecurityManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/AskActivityReexecuteTypeEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/AutoDeliveryEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BlockActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BlockActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BlockActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BpmUserTaskEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlineDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlinesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DealOvertimeActivityTypePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DecisionActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DecisionActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormAttachmentsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormSelectDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/InvokeActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/InvokeActivityMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/LimitCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/RouteActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/RouteActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/UnReexecuteAbleAct_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionConditionConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionRuleListCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionRuleListEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/subflow/SubflowActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ApplicationManagerDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ApplicationManagerPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ApplicationsToolsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/FormalParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/HttpCodesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/HttpCodesSelectorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/HttpCodesSelectorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeSessionBeanToolMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeSesstionBeanToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeWebApplicationToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeWebServicesToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/MailApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/RequestPropertiesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ScriptCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ScriptEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ScriptEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ScriptingApplicationTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/TiptopSessionBeanDialogChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationWizardPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSMsgMappingEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSPortOperationListCellRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WebApplicationMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WebApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/batchio/BatchIO_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ActivityChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ActualParameterChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ApprovalLevelChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/BpmUserTaskChooserPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/BpmUserTaskInfoPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/CalendarChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/EntireFormChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/I18NEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/OrganizationBpmUserTaskChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/OrgnizationRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/PackageCategoryChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/StrategyAssignmentPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/UserRelationshipChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/ActualParameterCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/PatternEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/PattternEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/XMLInstanceGeneratorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/XMLInstanceGeneratorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/composite/CompositeEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/composite/CompositeEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/composite/CompositeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/ConditionCellEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionActivitySimplePropertiesShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionConfigShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionRuleListCellSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionRuleListEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/ConditionExpressionPatternBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/ConditionExpressionPatternBatchEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/ConditionPatternCellBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/DecisionConditionBatchConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/transition/ConditionExpressionEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/transition/ConditionExpressionShareEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/CMPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/CheckInDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerIFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrameAboutBox_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerProgressBar_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerProgressDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerSignOnDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerStatusPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DiagramEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/ProcessPackageHistoryDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/ReleaseDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/SaveAllDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationInfoPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/popmenu/ObjectPopupMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/popmenu/ProcessViewPopupMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ResponsiblesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/SysintegrationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/XMLStringCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/AttachmentAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/AttachmentTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/AttachmentTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/BasicTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/BasicTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormalParameterDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormalParameterMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormalParameterMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/AttachmentAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/AttachmentTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/AttachmentTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/AttachmentTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/BasicTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/BasicTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/CompositeTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/RelevantDatasDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/RelevantDatasMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/InvokeAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessTemplateModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/UserDefineModeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/sysintegration/T100TemplateGenMappingList_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionDefinitionCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/TransitionDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/tree/formtree/FormTreeModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/tree/formtree/FormTree_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/tree/orgtree/OrganizationTreeNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/tree/orgtree/OrganizationUnitTreeNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AboutDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/Actions_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/DefaultSubstitudeEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/EmployeeCreator_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/FunctionViewer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/GroupEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainDeptDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainToolBar_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MaintainAbstractRoleDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/ManageWorkCalendars_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/ManagerSelectorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgChooserDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgGraphDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgUnitEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerGroupPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerOrgPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerOrgUnitEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerOrgUnitPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/ProcessSubstitudeEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/RelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchProcessDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SubstitutePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/TransferUserDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/UnitInfoPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/UserCompanyEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/WorkCalendarEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/plugin/ISOPlugInDecoder_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/AbstractOrgTreeNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/DepartmentNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/OrgNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/OrgRootNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/OrgTree_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/OrganizationTreeModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/ProjectNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/TypeNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/view/OrgSelectorTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/view/UnitTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/view/UserTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/AbstractController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/AbstractStep_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Action_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/CfgTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Config_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/DataCfgTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ImporterMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ImporterProgressDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ImporterStatusPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Login_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ModelMainTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/NewOrgDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/StepTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Term_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/TreeMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/WebServerDBConn_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/common_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/status_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/AdvancedFDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CopyAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CreateCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CreateCMProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/DeleteCMProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/EditCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/GridAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/MoveProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/NewProcessDefinitionAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/NewProcessPackageFromToolBarAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/NewProcessPackageFromTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/NewProcessPackageFromXPDLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenFromXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenNotificationManagerAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenObjectAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenProcessViewAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/RemoveProcessDefAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/RemoveProcessDefinitionAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveAllPackagesAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveAsObjectAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveCMProcessPackageActionFromTree_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SavePackageTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/ShowHistoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SimpleLDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoCheckoutPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoCheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/ADMIDValidator_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/SystemConfigController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/DocServerTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/LdapTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/SystemConfigPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/toolauth/OrgAuthConfPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/toolauth/ToolAuthConfPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/util/ADMProgressDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/util/CheckPassDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/util/CheckPrsDueDate_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/common/SignOnManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/common/common_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/DataAcsDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/DesignerController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/DesignerIFrameController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/SecurityManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/DueDateEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/AskActivityReexecuteTypeEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/AutoDeliveryEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BlockActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BlockActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BlockActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DeadlineDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DeadlinesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DealOvertimeActivityTypePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DecisionActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DecisionActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/FormAttachmentsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/FormSelectDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/InvokeActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/InvokeActivityMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/LimitCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/RouteActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/RouteActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/SubflowActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/SubflowActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/SubflowActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/UnReexecuteAbleAct_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionRuleListCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionRuleListEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/subflow/SubflowActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ApplicationManagerDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ApplicationManagerPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ApplicationsToolsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/FormalParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/HttpCodesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/HttpCodesSelectorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/HttpCodesSelectorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeSessionBeanToolMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeSesstionBeanToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeWebApplicationToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeWebServicesToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/MailApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/RequestPropertiesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ScriptCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ScriptEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ScriptEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ScriptingApplicationTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSInvocationEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSInvocationEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSInvocationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSInvocationWizardPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSMsgMappingEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSPortOperationListCellRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WebApplicationMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WebApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/batchio/BatchIO_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ActivityChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ActualParameterChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ApprovalLevelChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/CalendarChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/I18NEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/OrgnizationRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/PackageCategoryChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ProcessRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/StrategyAssignmentPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/UserRelationshipChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/ActualParameterCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/ApplicationTypeCellRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/PatternEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/PattternEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/XMLInstanceGeneratorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/XMLInstanceGeneratorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/composite/CompositeEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/composite/CompositeEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/composite/CompositeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/ConditionCellEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/DecisionActivitySimplePropertiesShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/DecisionConfigShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/DecisionRuleListCellSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/DecisionRuleListEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/batchChange/ConditionExpressionPatternBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/batchChange/ConditionExpressionPatternBatchEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/batchChange/ConditionPatternCellBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/batchChange/DecisionConditionBatchConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/transition/ConditionExpressionEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/transition/ConditionExpressionShareEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/CMPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/CheckInDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerIFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerMainFrameAboutBox_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerProgressBar_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerProgressDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerSignOnDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerStatusPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/ProcessPackageHistoryDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/ReleaseDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/SaveAllDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/notification/NotificationInfoPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/notification/NotificationManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/popmenu/ObjectPopupMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/popmenu/ProcessViewPopupMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/RelationManEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ResponsiblesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/XMLStringCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/AttachmentAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/AttachmentTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/AttachmentTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/BasicTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/BasicTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormalParameterDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormalParameterMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormalParameterMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/AttachmentAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/AttachmentTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/AttachmentTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/AttachmentTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/BasicTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/BasicTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/CompositeTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/RelevantDatasDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/RelevantDatasMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/InvokeAuthorityEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessTemplateModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/UserDefineModeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/VariableNamesList_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/ConditionDefinitionCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/ConditionExpressionEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/ConditionExpressionEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/ConditionTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/TransitionDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/tree/formtree/FormTreeModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/tree/formtree/FormTree_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/tree/orgtree/OrganizationTreeNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/tree/orgtree/OrganizationUnitTreeNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/src/resource/SysAuthorityRsrc_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/Language_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/MainRsrcProperties_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/Rsrc_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/tiptopcfg_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 518}, {"commit_hash": "7ec07603459c0150c06ca5495d9a2cbab4cb3710", "commit_訊息": "新增IMG快速簽核時會取出上一關卡的簽核意見與狀況", "提交日期": "2018-12-18 10:05:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0bb1d7888474b17552a9326005eb2c49ee35fc30", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2018-12-14 18:14:33", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "017cb8b627434256af7871b5f48e7ffee125cdbf", "commit_訊息": "C01-20181128001 因frame載入速度問題導致錯誤，故將module移出成共用js,避免錯誤", "提交日期": "2018-12-14 17:09:13", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/_getModuleIcon.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "02cb88f45a91f7cf9444cfd1c77f6d5642f7c4aa", "commit_訊息": "A00-20181211003 修正Grid隱藏表單問題、修正資料選取器儲存資料錯誤問題", "提交日期": "2018-12-14 17:09:01", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "05c2ef20d00ae335f3d57b87180ada8d3872727d", "commit_訊息": "C01-20181130001 修正textarea展開功能必須在點擊一次後才能觸發", "提交日期": "2018-12-13 10:19:07", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4bd1baa6eb4f4658ac8ebdc109c667fc02615c9c", "commit_訊息": "C01-20181126002 調整FormUtil.getValue為:如果目標是有設定千分位的textbox欄位，則回傳去除千分位後的結果", "提交日期": "2018-12-12 16:44:33", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad3153cb29a82e12127cd58d63f260ff04f1b519", "commit_訊息": "S00-*********** 開會決議後更改", "提交日期": "2018-12-12 13:59:26", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "244574477a8e1ab940999fb983c497c0046ad5d4", "commit_訊息": "A00-20181211001 ESSQ16點了沒有反應", "提交日期": "2018-12-11 20:39:04", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f8252ba33b524cb2d22dafe61ed9909d2e735c38", "commit_訊息": "調整行動版手寫元件翻轉icon圖示", "提交日期": "2018-12-11 16:15:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cf6921622fd6c0af1e3674b307faca1c657bf956", "commit_訊息": "Q00-20181211001 支持ESS使用https連線", "提交日期": "2018-12-11 14:16:19", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "234d518d9bd7e09a9e999af66e41502f0dd0ab9c", "commit_訊息": "C01-20181210001 修正WEB表單設計師會出現 無法取得未定義或NULL參考的屬性'isHideLabel' 訊息", "提交日期": "2018-12-10 15:13:50", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7a1524ad2465885f1fedc26d33efa9d974e6bc1f", "commit_訊息": "修正系統未啟用APP或是沒註冊APP序號時，直連表單還是可以正常打開", "提交日期": "2018-12-10 13:51:34", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb18038926baa3802d3d2060fd77b6c2e83c814b", "commit_訊息": "修正鼎捷已轉派清單取得中間層資料異常", "提交日期": "2018-12-10 13:48:00", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "646bc05a6d8fda55099f1935944b50c2da9e0856", "commit_訊息": "A00-20181127001 修正多人群組在最後一關終止流程後，流程圖顯示異常", "提交日期": "2018-12-07 16:08:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05b52f73bf95e66bc0f650e2ee82b484e26678f2", "commit_訊息": "C01-20181206001 修正在IMG中當待辦已被簽核過時會有\"myFormValue\" is undefined問題", "提交日期": "2018-12-07 14:00:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f5a6280726d5909ef16cd9d457da16cccc7663e5", "commit_訊息": "S00-*********** 將儲存腳本替換成發佈腳本 新增儲存表單同時會更新所有流程主機表單定義的功能", "提交日期": "2018-12-05 19:29:15", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IServerCacheManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "3637483499d5153fbd9babd715aba36b4347622c", "commit_訊息": "新增鼎捷移動中間層下一關卡資訊欄位", "提交日期": "2018-12-05 17:58:42", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a2d646618dc9573f3303d5dd54ae1cded7c68097", "commit_訊息": "C01-20181204003 修正IMG中的提醒功能因整合設定中的詳情代號未調整過可能導致新增失敗問題", "提交日期": "2018-12-04 14:27:05", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "179f6ac1b5931f94439fe69ba12a49e13160ffa0", "commit_訊息": "修正入口平台刪除時，使用者綁定表中的configOID會錯亂問題", "提交日期": "2018-12-03 16:26:10", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "46f2a232e2f8f1871385273c22b34f42b4d0a23b", "commit_訊息": "修正待辦工作清單的restful因取不到user的工作流程主機設定資訊導致NullPointException", "提交日期": "2018-12-03 16:13:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9394839117bece13fb57c20d3373699071217a44", "commit_訊息": "調整行動版元件css --Label元件最高呈現2行高度", "提交日期": "2018-12-03 14:06:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd4f921d49b10b6309c36491e3b5b11845a45fa2", "commit_訊息": "IMG追蹤、通知、待辦列表增加區分中間層與詳情提示訊息", "提交日期": "2018-12-03 14:03:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1788d3b31b6520ca78d74979b08017c39cd06c10", "commit_訊息": "修正鼎捷移動已轉派流程當字段中有發起時間跟流程狀態時會有型別轉換異常的問題", "提交日期": "2018-11-30 17:34:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6787a2d5348d30da89f7f24d9f13faed8435b152", "commit_訊息": "新增鼎捷移動取得已轉派清單功能", "提交日期": "2018-11-30 17:06:29", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ReassignWorkAssignForListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileResigendServiceTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5742.xls", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 27}, {"commit_hash": "3e5ca05e98c388a41e3ce971744f85b4555cf3f7", "commit_訊息": "Q00-20181130001 programid誤植，導致驗證權限失效", "提交日期": "2018-11-30 15:34:19", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3b3418ad2db8df58d7848923b3aa9e964cc19182", "commit_訊息": "新增:5741DBpatch檔", "提交日期": "2018-11-30 14:24:16", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ffab0f2ba51a5bee64a1bfca5781f0354b1bdfa", "commit_訊息": "S00-20180910001 表單終止時，儲存表單，加入 開發樣板", "提交日期": "2018-11-30 10:40:09", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "f1eb49b9d4207092ef6270fc3d39b0facc4c3851", "commit_訊息": "C01-20181126003 調整SyncOrg及組織設計師開放UserId使用-符號", "提交日期": "2018-11-30 10:26:15", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/UserCreator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d7dc1dee4d2080fd128e738f7bc002bbcb746ebf", "commit_訊息": "A00-20181121001 修正ISO報表一覽表的簽出時間及簽出人員資料未顯示", "提交日期": "2018-11-30 10:22:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocForReportViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f646bb3bf4e8a0086466c432f5cb33b0adc217a4", "commit_訊息": "C01-20181129003 修正IMG聯絡人無顯示電話資訊", "提交日期": "2018-11-29 19:11:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookData.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5e999b683231024717f36092726524088b98637d", "commit_訊息": "修正鼎捷移動整合設定頁面沒有切換頁面按鈕", "提交日期": "2018-11-29 19:04:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7a0b31fb7db37d03eb5b508ac9729e88dc8770ac", "commit_訊息": "C01-20181128002 修正微信整合設定頁面沒有切換頁面按鈕", "提交日期": "2018-11-29 19:01:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChateUser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}