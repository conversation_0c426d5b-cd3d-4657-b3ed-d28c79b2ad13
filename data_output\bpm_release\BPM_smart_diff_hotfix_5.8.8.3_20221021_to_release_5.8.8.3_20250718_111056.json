{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.8.3_20221021", "date": "2022-09-22 18:04:54", "message": "[流程引擎]Q00-20220922001 調整流程撈取工作通知內容機制", "author": "yamiyeh10"}, "舊分支": {"branch_name": "release_5.8.8.3", "date": "2022-07-21 16:48:19", "message": "[ESS]Q00-20220721001 修正ESS儲存草稿後並沒有成功新增到草稿上[補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 11:10:56", "新增commit數量": 54, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "4c70bc058519e01c5ff9dd8eb4fe5b3f5dd3478d", "commit_訊息": "[流程引擎]Q00-20220922001 調整流程撈取工作通知內容機制", "提交日期": "2022-09-22 18:04:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8f2cd5b727b6279ccbdcc9801c1100523ce63da4", "commit_訊息": "[WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能", "提交日期": "2022-08-30 08:34:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4fc80f7d30528eba647048cc361f2615d22ee7d", "commit_訊息": "[流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡", "提交日期": "2022-08-25 14:52:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "187a01a450096335cfddb6d9cd5fe01ffe310fd9", "commit_訊息": "[Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題", "提交日期": "2022-09-16 13:48:45", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70e20589de1e6380bb8db41580767a701319f95d", "commit_訊息": "[Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息", "提交日期": "2022-09-08 14:06:21", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6edfad84b4870f18fcd0bb2fe7dc3fe9f145d6a7", "commit_訊息": "[Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式", "提交日期": "2022-09-01 15:53:47", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2deb154ee2a298613b746629a232ae4eb0bbe18e", "commit_訊息": "[Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題", "提交日期": "2022-08-08 14:57:44", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe7c7baf1ce5752ce812ed26436650f9fe0d8b41", "commit_訊息": "[Web]Q00-20220805001 修正作業程序書沒有顯示核決層級關卡的作業名稱", "提交日期": "2022-08-05 14:06:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b9cb004af2a64969ef876ad4c31bbb6cfc592551", "commit_訊息": "[Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]", "提交日期": "2022-08-05 12:00:16", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5566de4bb5e48c00c76623456788a328fc411ed6", "commit_訊息": "[Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容", "提交日期": "2022-08-04 22:21:33", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1e9b5d8170bb0d910e5203e6de597d1ac0241c81", "commit_訊息": "[Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。", "提交日期": "2022-08-03 16:48:09", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6795bce2ae26f731debaeb8ca4026e1402c0317f", "commit_訊息": "[Web]A00-20220802001 修正無法開啟SAP維護作業", "提交日期": "2022-08-02 11:27:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ccd01e01bc1c1786268d563b53eea3cec1093c1", "commit_訊息": "[Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料", "提交日期": "2022-08-01 16:26:26", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "83103c693dfd40577952a070155d39958a92d988", "commit_訊息": "[Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失", "提交日期": "2022-07-29 17:03:53", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dabeec206d6b9a5f9a806cb49d66c67556423a82", "commit_訊息": "[WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]", "提交日期": "2022-07-29 14:34:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "25fccf5a90a6f950c56666f5f7cb3607bd9dcc76", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]", "提交日期": "2022-07-29 14:20:21", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e3bef8cc761f81a01effc33d7be1ee9d8feb72e8", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]", "提交日期": "2022-07-29 14:02:17", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e555a7073db8475d347c12b24cc3d24f9aa3e12a", "commit_訊息": "[WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock", "提交日期": "2022-07-28 15:20:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4540890d84bf7a1e42ecd0bf5a9203059a18b2ff", "commit_訊息": "[Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題", "提交日期": "2022-07-27 17:51:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f34e47c07ca38c7f170b2fd8adab8c4b43218e4c", "commit_訊息": "[WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法", "提交日期": "2022-07-27 10:41:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3b1113437428848fdd84f283df82e236033d638", "commit_訊息": "[內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來", "提交日期": "2022-07-26 18:17:44", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/JpaService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a72ee76cb0a71d142418b56c6c28d55a56e901af", "commit_訊息": "[Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入", "提交日期": "2022-07-26 18:16:29", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "11d33d2a0bca063e9ea892dbe6254b39d055b395", "commit_訊息": "[Web]S00-20220129003 調整當表單元件的Label內容過長時完整顯示出Label內容", "提交日期": "2022-07-25 15:44:06", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "543e0e8af97c45360faf25f1459bffe3231ef745", "commit_訊息": "[Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法", "提交日期": "2022-07-25 10:55:23", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9be41fcb0b6aee939a84d3ac1430e7ee6cdff237", "commit_訊息": "[Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題", "提交日期": "2022-07-25 10:22:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93ee140a29d77f76a79f042bfeaf54db8b79ffaa", "commit_訊息": "[WorkFlow]]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能", "提交日期": "2022-10-14 17:38:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3f43409161dc86f2a819a1ef1bf852969b442131", "commit_訊息": "[web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。(補修正)", "提交日期": "2022-09-27 10:51:44", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "99dfdbb65fea26882b837f2ad91eaf1d303c8bd1", "commit_訊息": "[web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。", "提交日期": "2022-09-26 15:02:43", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "84b31f877699a2b66381cc0e56a3e1c52838c8b1", "commit_訊息": "[Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本", "提交日期": "2022-09-08 15:12:59", "作者": "wencheng1208", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "219ec0e544b0e5181758553dd372f62f55baf13c", "commit_訊息": "[WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。", "提交日期": "2022-10-06 14:35:32", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "952e2a312ef78e285340a7a5091ca97760896d3c", "commit_訊息": "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況", "提交日期": "2022-07-29 00:04:37", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0b8e5a0e715d3cc068eead2fda9dd4d390f75930", "commit_訊息": "[ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視", "提交日期": "2022-10-06 13:39:46", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e49bd4e6c24218ad20ef9accbb6f27c54092b79", "commit_訊息": "[WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限\"沒有作用(補修正，增加可讀性)", "提交日期": "2022-10-06 08:57:33", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9bd4e833688ab6f32960e161dc6436ed474c52d3", "commit_訊息": "[WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限\"沒有作用", "提交日期": "2022-10-04 15:26:58", "作者": "raven.917", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d1e492310f26356f137c1ddf4bb400249494fc9", "commit_訊息": "[Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊", "提交日期": "2022-09-20 11:19:53", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba8b04b0f684772273c2221d8ef1d06727bac06a", "commit_訊息": "[Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]", "提交日期": "2022-09-30 15:47:30", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "914b16ce2ac8e95f7a7296f3b59a785ed7d368d0", "commit_訊息": "[流程引擎]S00-20220722001新增批次通知信件主旨內容", "提交日期": "2022-08-23 15:27:14", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fd41c94a6424069892033852aadb2a5f325cdf6", "commit_訊息": "[Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題", "提交日期": "2022-08-11 12:56:57", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "50a9d56980d442453795eb0ed72d5f472c03b1b2", "commit_訊息": "[Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題", "提交日期": "2022-08-10 18:34:57", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "877997712019745dd8c4455892c709e3de1b7917", "commit_訊息": "[Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示", "提交日期": "2022-07-29 16:49:53", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aebbd71392ecf3a06f077f5d682427042618ff22", "commit_訊息": "[Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行", "提交日期": "2022-07-28 17:32:17", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6b1d14f80cfe1bb3eacb992d74c89626125fa23", "commit_訊息": "[Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員", "提交日期": "2022-09-30 12:24:25", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de0d3ebdc8b2af539490605dfc6cfb0ea6456c6e", "commit_訊息": "[T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容", "提交日期": "2022-09-27 08:36:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ebd9678095c9cadf9210f3e21557b4117f3b6b6c", "commit_訊息": "[流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤", "提交日期": "2022-08-18 11:43:00", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71aa2f2a2d3926391c9d9e2570b3345b475d88b0", "commit_訊息": "Revert \"[內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題\"", "提交日期": "2022-09-22 14:48:45", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/design_tool_web/SystemManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a2cc011c161988a25b2e04dbcdfdc95857b6dedb", "commit_訊息": "\\\\解決build問題", "提交日期": "2022-09-22 14:17:24", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MFAConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bba87ab56dcf5ae63b8cd9bee5982f3de8f6aa4d", "commit_訊息": "//因系統權限管理頁面開啟緩慢問題暫時把系統管理打開", "提交日期": "2022-09-22 13:35:57", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6919e0e16020bf366b2d034cf58367450b80c63c", "commit_訊息": "[TIPTOP]A00-*********** 新增TIPTOP整合設定，當夾帶附件型態為http,根據TIPTOP附件主機的port號取得附件", "提交日期": "2022-09-20 14:55:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "068fc48aa0f6e962ddf7aade573e2c2b329fe5c3", "commit_訊息": "[內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題", "提交日期": "2022-09-15 10:23:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/design_tool_web/SystemManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c6557868b263216f2e9c200fe70f496ace493633", "commit_訊息": "[TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久", "提交日期": "2022-08-19 17:57:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "668493fea9bced6d17164564040c7334c9a637ce", "commit_訊息": "[Web]S00-20220810001簽核意見是否顯示管理員", "提交日期": "2022-08-19 10:49:46", "作者": "謝閔皓", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.8.4_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "06e9149b26113fcd318a6c0c026a2fd291a5bd10", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]", "提交日期": "2022-08-18 17:50:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "320137581f657a4131ec7aa2604ee7d042b347e7", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]", "提交日期": "2022-08-18 17:47:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8037c65f3dc300d41af2dddf23592991d71230df", "commit_訊息": "[流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到", "提交日期": "2022-08-18 17:41:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}]}