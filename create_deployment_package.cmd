@echo off
setlocal enabledelayedexpansion
title BPM Easy Tools - Create Deployment Package

echo.
echo ==========================================
echo    BPM Easy Tools - Create Deployment Package
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.8 or higher
    echo Download URL: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python is installed:
python --version

echo.
echo Starting deployment package creation...
echo.

REM Execute packaging script
python create_deployment_package.py

if errorlevel 1 (
    echo.
    echo Error occurred during packaging
    pause
    exit /b 1
)

echo.
echo Deployment package created successfully!
echo.
echo Next steps:
echo 1. Copy the created .zip file to target machine
echo 2. Extract the files
echo 3. Run setup_environment.cmd to setup environment
echo 4. Run start_application.cmd to start the application
echo.

pause
