{"company_id": "06100172", "company_name": "勤美達", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06100172_勤美達\\01客戶基本資料", "files": [{"filename": "[勤美達] 連線資訊.txt", "raw_content": "勤美達測試機密碼 admin#DSC\r\n\r\n正式機+DB *************** (00-50-56-89-38-67)\r\n***************:80\r\nadministrator / Dsc#0347#Cmi\r\nEFGP\r\nsa/sql#DSC\r\n遠端桌面：Tmkstgk#168\r\n(正式機外網：***************:80)\r\n\r\n20190919 administrator / admin#DSC\r\n\r\n舊測試機 ***************:8086\r\n新測試機+DB *************** (00-50-56-85-DA-72)\r\n***************:8086\r\nadministrator / admin#DSC\r\nEFGPtest\r\nsa/sql#DSC\r\n\r\nadministrator / admin#DSC\r\n\r\n\r\n使用者帳密\r\npeter.feng / peter.feng\r\n\r\n\r\n\r\n", "structured_data": {"遠端桌面": "Tmkstgk#168", "(正式機外網": "***************:80)", "***************": "80", "(正式機外網：***************": "80)", "舊測試機 ***************": "8086", "***************": "8086", "host": "***************"}, "source_path": "C1.客戶維護相關\\06100172_勤美達\\01客戶基本資料\\[勤美達] 連線資訊.txt", "file_size": 469, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:30.664822"}], "total_files": 1, "processed_at": "2025-08-26T10:46:30.664831"}