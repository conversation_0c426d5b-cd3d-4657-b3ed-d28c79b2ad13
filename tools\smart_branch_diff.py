#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多專案智能 Git Branch 差異比較工具

功能：
1. 支援多個專案配置
2. 自動掃描指定的分支模式
3. 判斷哪些分支還沒產生 JSON 記錄
4. 自動產生缺少的差異記錄
5. 智能版本比較邏輯
"""

import json
import subprocess
import sys
import re
import glob
import os
from datetime import datetime
from pathlib import Path


def ensure_output_directory():
    """確保輸出目錄存在"""
    # 調整為新的專案結構
    project_root = Path(__file__).parent.parent
    output_dir = project_root / "data_output" / "bpm_release"
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir


def run_git_command(command, repo_path):
    """執行 Git 命令"""
    try:
        result = subprocess.run(
            command,
            cwd=repo_path,
            capture_output=True,
            text=True,
            encoding='utf-8',
            shell=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"命令執行失敗: {command}")
        print(f"錯誤: {e.stderr}")
        return None


def run_git_command_silent(command, repo_path):
    """執行 Git 命令（靜默模式，不顯示錯誤訊息）"""
    try:
        result = subprocess.run(
            command,
            cwd=repo_path,
            capture_output=True,
            text=True,
            encoding='utf-8',
            shell=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError:
        return None


def load_projects_config():
    """載入專案配置"""
    # 調整為新的專案結構
    project_root = Path(__file__).parent.parent
    config_file = project_root / "config" / "projects_config.json"
    if not config_file.exists():
        print(f"❌ 找不到專案配置檔案: {config_file}")
        return None

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 載入專案配置失敗: {e}")
        return None


def get_target_branches(repo_path, branch_patterns):
    """根據分支模式取得目標分支"""
    print("🌿 取得目標 branches...")

    # 取得本地分支
    local_branches_cmd = 'git branch'
    local_branches_output = run_git_command(local_branches_cmd, repo_path)

    # 取得遠端分支
    remote_branches_cmd = 'git branch -r'
    remote_branches_output = run_git_command(remote_branches_cmd, repo_path)

    if not local_branches_output and not remote_branches_output:
        print("❌ 無法取得 branches")
        return []

    # 合併並去重
    all_branches = set()

    # 添加本地分支
    if local_branches_output:
        for line in local_branches_output.split('\n'):
            line = line.strip().replace('*', '').strip()
            if line and line not in branch_patterns.get('exclude_branches', []):
                all_branches.add(line)

    # 添加遠端分支
    if remote_branches_output:
        for line in remote_branches_output.split('\n'):
            line = line.strip()
            if line and not line.endswith('/HEAD'):
                branch_name = line.replace('origin/', '').strip()
                if branch_name not in branch_patterns.get('exclude_branches', []):
                    all_branches.add(branch_name)

    # 根據模式篩選分支
    target_branches = []
    for branch in all_branches:
        # 檢查開頭模式
        starts_match = False
        for pattern in branch_patterns.get('include_starts_with', []):
            if branch.startswith(pattern):
                starts_match = True
                break

        # 檢查結尾模式
        ends_match = False
        for pattern in branch_patterns.get('include_ends_with', []):
            if branch.endswith(pattern):
                ends_match = True
                break

        # 如果沒有結尾模式，只檢查開頭
        if not branch_patterns.get('include_ends_with'):
            if starts_match:
                target_branches.append(branch)
        else:
            # 有結尾模式時，需要滿足開頭或結尾其中一個
            if starts_match or ends_match:
                target_branches.append(branch)

    print(f"📊 找到 {len(target_branches)} 個目標 branches")
    return target_branches


def get_target_tags(repo_path, tag_patterns):
    """根據 tag 模式取得目標 tags"""
    print("🏷️  取得目標 tags...")

    # 取得所有 tags
    tags_cmd = 'git tag -l'
    tags_output = run_git_command(tags_cmd, repo_path)

    if not tags_output:
        print("❌ 無法取得 tags")
        return []

    all_tags = []
    for line in tags_output.split('\n'):
        tag = line.strip()
        if tag:
            all_tags.append(tag)

    # 根據模式篩選 tags
    target_tags = []
    for tag in all_tags:
        # 檢查開頭模式
        for pattern in tag_patterns.get('include_starts_with', []):
            if tag.startswith(pattern):
                target_tags.append(tag)
                break

    print(f"📊 找到 {len(target_tags)} 個目標 tags")
    return target_tags


def get_branch_commit_date(repo_path, branch_name):
    """取得分支最新 commit 的日期（用於 hotfix 分支排序）"""
    try:
        # 嘗試多種方式取得分支最新 commit 的時間戳

        # 1. 先嘗試本地分支
        date_cmd = f'git log -1 --format="%ct" {branch_name}'
        date_output = run_git_command_silent(date_cmd, repo_path)

        if date_output:
            return int(date_output.strip())

        # 2. 如果本地分支不存在，嘗試遠端分支
        date_cmd = f'git log -1 --format="%ct" origin/{branch_name}'
        date_output = run_git_command_silent(date_cmd, repo_path)

        if date_output:
            return int(date_output.strip())

        # 3. 如果都不存在，嘗試使用 git show 命令
        date_cmd = f'git show -s --format="%ct" {branch_name}'
        date_output = run_git_command_silent(date_cmd, repo_path)

        if date_output:
            return int(date_output.strip())

        # 4. 最後嘗試遠端的 git show
        date_cmd = f'git show -s --format="%ct" origin/{branch_name}'
        date_output = run_git_command_silent(date_cmd, repo_path)

        if date_output:
            return int(date_output.strip())

        # 5. 嘗試使用 git rev-parse 命令
        date_cmd = f'git rev-parse --verify {branch_name}'
        commit_hash = run_git_command_silent(date_cmd, repo_path)

        if commit_hash:
            date_cmd = f'git show -s --format="%ct" {commit_hash}'
            date_output = run_git_command_silent(date_cmd, repo_path)
            if date_output:
                return int(date_output.strip())

        # 如果所有方法都失敗，靜默返回 0
        # print(f"⚠️  無法取得分支 {branch_name} 的提交日期，使用預設值 0")
        return 0

    except (ValueError, TypeError):
        # print(f"⚠️  解析分支 {branch_name} 的提交日期時發生錯誤，使用預設值 0")
        return 0


def is_special_hotfix_branch(branch_name):
    """判斷是否為特殊任務 hotfix 分支（如 hotfix_X.X.X.X_unimicron）"""
    # 匹配 hotfix_X.X.X.X_非數字後綴 格式
    pattern = r'hotfix_(\d+)\.(\d+)\.(\d+)\.(\d+)_([a-zA-Z]\w*)'
    match = re.match(pattern, branch_name)
    if match:
        suffix = match.group(5)
        # 排除純數字日期和 "All" 後綴
        if not re.match(r'^\d{8}$', suffix) and suffix.lower() != 'all':
            return True
    return False



def parse_version(branch_name):
    """解析版本號（支援多種格式）"""
    # 支援的格式：
    # 1. release_X.X.X.X, hotfix_X.X.X.X
    # 2. release_X.X.X.X_日期, hotfix_X.X.X.X_日期
    # 3. release_X.X.X.X_All, hotfix_X.X.X.X_All
    # 4. X.X.X.X_hotfix (NaNaXWeb 特殊格式)

    # 先嘗試匹配 X.X.X.X_hotfix 格式（NaNaXWeb 特殊格式）
    match_hotfix_suffix = re.match(r'(\d+)\.(\d+)\.(\d+)\.(\d+)_hotfix$', branch_name)
    if match_hotfix_suffix:
        version = tuple(map(int, match_hotfix_suffix.groups()))
        return ('hotfix', version, 0)  # 將 _hotfix 後綴視為 hotfix 類型

    # 嘗試匹配帶日期後綴的格式
    match_with_suffix = re.match(r'(release|hotfix)_(\d+)\.(\d+)\.(\d+)\.(\d+)_(.+)', branch_name)
    if match_with_suffix:
        prefix = match_with_suffix.group(1)
        version = tuple(map(int, match_with_suffix.groups()[1:5]))  # 只取版本號部分
        suffix = match_with_suffix.group(6)  # 日期或其他後綴

        # 如果後綴是日期格式（8位數字），將其轉換為數字用於排序
        if re.match(r'^\d{8}$', suffix):  # YYYYMMDD 格式
            date_num = int(suffix)
            return (prefix, version, date_num)
        else:
            # 其他後綴（如 "All"），給予特殊排序值
            # "All" 通常表示最新版本，給予最大值
            special_value = 99999999 if suffix.lower() == 'all' else 0
            return (prefix, version, special_value)

    # 再嘗試匹配基本格式（無後綴）
    match_basic = re.match(r'(release|hotfix)_(\d+)\.(\d+)\.(\d+)\.(\d+)$', branch_name)
    if match_basic:
        prefix = match_basic.group(1)
        version = tuple(map(int, match_basic.groups()[1:]))
        return (prefix, version, 0)  # 無日期後綴，給予 0

    return None


def parse_tag_version(tag_name):
    """解析 tag 版本號（支援多種格式）"""
    # 支援的格式：
    # 1. X.X.X.X (基本版本號)
    # 2. X.X.X.X_N (帶數字後綴)
    # 3. X.X_Merge_X.X.X.X-X.X.X.X (合併標記)
    # 4. X.X_RWD_Merge_X.X.X.X-X.X.X.X (RWD合併標記)

    # 跳過合併標記的 tag
    if '_Merge_' in tag_name or '_RWD_Merge_' in tag_name:
        return None

    # 嘗試匹配帶數字後綴的格式 X.X.X.X_N
    match_with_suffix = re.match(r'(\d+)\.(\d+)\.(\d+)\.(\d+)_(\d+)$', tag_name)
    if match_with_suffix:
        version = tuple(map(int, match_with_suffix.groups()[:4]))  # 只取版本號部分
        suffix = int(match_with_suffix.group(5))  # 數字後綴
        return ('tag', version, suffix)

    # 嘗試匹配基本格式 X.X.X.X
    match_basic = re.match(r'(\d+)\.(\d+)\.(\d+)\.(\d+)$', tag_name)
    if match_basic:
        version = tuple(map(int, match_basic.groups()))
        return ('tag', version, 0)  # 無後綴，給予 0

    return None


def sort_branches_by_version(branches, repo_path=None):
    """按版本號排序分支（由大至小，考慮日期後綴）"""
    version_branches = []

    for branch in branches:
        parsed = parse_version(branch)
        if parsed:
            if len(parsed) == 3:  # 有日期後綴
                prefix, version, date_suffix = parsed

                # 對於 hotfix 分支，如果有 repo_path，嘗試使用實際提交日期排序
                if prefix == 'hotfix' and repo_path and ('hotfix_' in branch or '_hotfix' in branch):
                    commit_date = get_branch_commit_date(repo_path, branch)
                    # 如果成功取得提交日期（不為 0），使用提交日期排序
                    if commit_date > 0:
                        sort_key = (0 if prefix == 'release' else 1, version, commit_date)
                    else:
                        # 如果無法取得提交日期，回退到使用日期後綴排序
                        sort_key = (0 if prefix == 'release' else 1, version, date_suffix)
                else:
                    # 使用 (prefix, version, date_suffix) 作為排序鍵
                    # release 優先於 hotfix，版本號由大至小，日期由新至舊
                    sort_key = (0 if prefix == 'release' else 1, version, date_suffix)
            else:  # 無日期後綴（舊格式）
                prefix, version = parsed

                # 對於 hotfix 分支，如果有 repo_path，嘗試使用實際提交日期排序
                if prefix == 'hotfix' and repo_path and ('hotfix_' in branch or '_hotfix' in branch):
                    commit_date = get_branch_commit_date(repo_path, branch)
                    # 如果成功取得提交日期（不為 0），使用提交日期排序
                    if commit_date > 0:
                        sort_key = (0 if prefix == 'release' else 1, version, commit_date)
                    else:
                        # 如果無法取得提交日期，使用預設值 0
                        sort_key = (0 if prefix == 'release' else 1, version, 0)
                else:
                    sort_key = (0 if prefix == 'release' else 1, version, 0)

            version_branches.append((sort_key, branch))

    # 按版本號排序（由大至小）
    version_branches.sort(key=lambda x: x[0], reverse=True)

    return [branch for sort_key, branch in version_branches]


def sort_tags_by_version(tags):
    """按版本號排序 tags（由大至小，考慮數字後綴）"""
    version_tags = []

    for tag in tags:
        parsed = parse_tag_version(tag)
        if parsed:
            prefix, version, suffix = parsed
            # 使用 (version, suffix) 作為排序鍵
            # 版本號由大至小，後綴由大至小
            sort_key = (version, suffix)
            version_tags.append((sort_key, tag))

    # 按版本號排序（由大至小）
    version_tags.sort(key=lambda x: x[0], reverse=True)

    return [tag for sort_key, tag in version_tags]


def filter_tags_by_version_range(tags, start_version, end_version):
    """根據版本範圍篩選 tags"""
    filtered_tags = []

    for tag in tags:
        parsed = parse_tag_version(tag)
        if not parsed:
            continue

        prefix, version, suffix = parsed

        # 檢查是否在指定版本範圍內
        if start_version <= version <= end_version:
            filtered_tags.append(tag)

    return filtered_tags


def find_corresponding_release_for_hotfix(hotfix_branch, all_branches):
    """為任何 hotfix 分支找到對應的 release 分支"""
    # 從各種 hotfix 格式中提取版本號

    # 1. 處理 hotfix_X.X.X.X_後綴 格式
    match = re.match(r'hotfix_(\d+)\.(\d+)\.(\d+)\.(\d+)_(.+)', hotfix_branch)
    if match:
        version_parts = match.groups()[:4]  # 取前4個版本號部分
        target_release = f"release_{'.'.join(version_parts)}"

        # 在所有分支中尋找對應的 release 分支
        for branch in all_branches:
            if branch == target_release:
                return branch

    # 2. 處理 hotfix_X.X.X.X 格式（無後綴）
    match = re.match(r'hotfix_(\d+)\.(\d+)\.(\d+)\.(\d+)$', hotfix_branch)
    if match:
        version_parts = match.groups()
        target_release = f"release_{'.'.join(version_parts)}"

        # 在所有分支中尋找對應的 release 分支
        for branch in all_branches:
            if branch == target_release:
                return branch

    # 3. 處理 X.X.X.X_hotfix 格式（NaNaXWeb 特殊格式）
    match = re.match(r'(\d+)\.(\d+)\.(\d+)\.(\d+)_hotfix$', hotfix_branch)
    if match:
        version_parts = match.groups()
        target_release = f"release_{'.'.join(version_parts)}"

        # 在所有分支中尋找對應的 release 分支
        for branch in all_branches:
            if branch == target_release:
                return branch

    return None


def find_nanaxweb_tag_for_hotfix(hotfix_branch, repo_path):
    """為 NaNaXWeb 專案的 hotfix 分支找到對應的 tag"""
    # 從 X.X.X.X_hotfix 格式中提取版本號
    match = re.match(r'(\d+)\.(\d+)\.(\d+)\.(\d+)_hotfix$', hotfix_branch)
    if not match:
        return None

    version_parts = match.groups()
    version_str = '.'.join(version_parts)

    # 定義版本對應的 tag 規則
    tag_mappings = {
        # 有 autobuild 後綴的版本
        '5.8.10.4': '5.8.10.4_202412101833_autobuild',
        '5.8.10.3': '5.8.10.3_202409241414_autobuild',
        '5.8.10.2': '5.8.10.2_202406261435_autobuild',
        '5.8.10.1': '5.8.10.1_202403261726_autobuild',
        # 純版本號的 tag
        '5.8.9.4': '5.8.9.4',
        '5.8.9.3': '5.8.9.3',
    }

    target_tag = tag_mappings.get(version_str)
    if target_tag:
        # 檢查 tag 是否存在
        check_tag_cmd = f'git tag -l {target_tag}'
        tag_output = run_git_command_silent(check_tag_cmd, repo_path)
        if tag_output and target_tag in tag_output:
            print(f"🔄 Hotfix 分支 {hotfix_branch} 將與對應的 tag {target_tag} 比較")
            return target_tag

    return None


def find_previous_version(target_branch, all_branches, repo_path=None):
    """找到指定分支的上一個版本"""

    # 解析目標分支的前綴
    target_version = parse_version(target_branch)
    if not target_version:
        return None

    target_prefix = target_version[0]  # release 或 hotfix

    # 特殊處理：NaNaXWeb 專案的 hotfix 分支
    if '_hotfix' in target_branch and repo_path:
        # 檢查是否為 NaNaXWeb 專案（通過路徑判斷）
        if 'NaNaXWeb' in repo_path:
            # 檢查是否為舊版本（需要與 tag 比較）
            corresponding_tag = find_nanaxweb_tag_for_hotfix(target_branch, repo_path)
            if corresponding_tag:
                return corresponding_tag
            # 如果沒有對應的 tag，則使用原有邏輯（適用於 release_8.1.1.1 之後的版本）

    # 新邏輯：所有 hotfix 分支都與對應的 release 分支比較
    if target_prefix == 'hotfix' or '_hotfix' in target_branch:
        corresponding_release = find_corresponding_release_for_hotfix(target_branch, all_branches)
        if corresponding_release:
            print(f"🔄 Hotfix 分支 {target_branch} 將與對應的 release 分支 {corresponding_release} 比較")
            return corresponding_release
        else:
            print(f"⚠️  找不到 {target_branch} 對應的 release 分支，使用原有邏輯")

    # 原有邏輯：只篩選相同前綴的分支（主要用於 release 分支之間的比較）
    same_prefix_branches = []
    for branch in all_branches:
        branch_version = parse_version(branch)
        if branch_version and branch_version[0] == target_prefix:
            same_prefix_branches.append(branch)

    # 對相同前綴的分支進行排序（傳入 repo_path 以支援按提交日期排序）
    sorted_branches = sort_branches_by_version(same_prefix_branches, repo_path)

    try:
        current_index = sorted_branches.index(target_branch)
        if current_index + 1 < len(sorted_branches):
            previous_branch = sorted_branches[current_index + 1]
            return previous_branch
        else:
            return None
    except ValueError:
        return None


def find_previous_tag_version(target_tag, all_tags):
    """找到指定 tag 的上一個版本"""
    # 對所有 tags 進行排序
    sorted_tags = sort_tags_by_version(all_tags)

    try:
        current_index = sorted_tags.index(target_tag)
        if current_index + 1 < len(sorted_tags):
            previous_tag = sorted_tags[current_index + 1]
            return previous_tag
        else:
            return None
    except ValueError:
        return None


def check_existing_json(project_id, new_version, old_version):
    """檢查是否已存在對應的 JSON 檔案"""
    # 確保輸出目錄存在
    output_dir = ensure_output_directory()

    # 檔名格式：專案ID_smart_diff_新版本_to_舊版本_*.json
    new_version_safe = new_version.replace('/', '_').replace(':', '_')
    old_version_safe = old_version.replace('/', '_').replace(':', '_')
    pattern = f"{project_id}_smart_diff_{new_version_safe}_to_{old_version_safe}_*.json"

    # 在 output 目錄中搜尋
    search_pattern = output_dir / pattern
    existing_files = glob.glob(str(search_pattern))
    return len(existing_files) > 0


def get_missing_comparisons(project_id, all_branches, repo_path):
    """取得還沒產生 JSON 記錄的比較組合"""
    sorted_branches = sort_branches_by_version(all_branches, repo_path)
    missing_comparisons = []

    for i, branch in enumerate(sorted_branches):
        previous_branch = find_previous_version(branch, all_branches, repo_path)
        if previous_branch:
            if not check_existing_json(project_id, branch, previous_branch):
                missing_comparisons.append((branch, previous_branch))

    return missing_comparisons


def get_missing_tag_comparisons(project_id, all_tags, version_ranges):
    """取得還沒產生 JSON 記錄的 tag 比較組合"""
    missing_comparisons = []

    for version_range in version_ranges:
        start_version = version_range['start']
        end_version = version_range['end']

        # 篩選指定版本範圍的 tags
        filtered_tags = filter_tags_by_version_range(all_tags, start_version, end_version)
        sorted_tags = sort_tags_by_version(filtered_tags)

        print(f"🏷️  版本範圍 {start_version} ~ {end_version}: 找到 {len(sorted_tags)} 個 tags")

        for i, tag in enumerate(sorted_tags):
            previous_tag = find_previous_tag_version(tag, sorted_tags)
            if previous_tag:
                if not check_existing_json(project_id, tag, previous_tag):
                    missing_comparisons.append((tag, previous_tag))

    return missing_comparisons


def get_branch_info(repo_path, branch_name):
    """取得 branch 的詳細資訊"""
    # 嘗試多種方式取得分支資訊，不需要切換分支

    # 1. 先嘗試本地分支
    branch_info_cmd = f'git log -1 --format="%ad|%s|%an" --date=format:"%Y-%m-%d %H:%M:%S" {branch_name}'
    branch_info = run_git_command_silent(branch_info_cmd, repo_path)

    if not branch_info:
        # 2. 嘗試遠端分支
        branch_info_cmd = f'git log -1 --format="%ad|%s|%an" --date=format:"%Y-%m-%d %H:%M:%S" origin/{branch_name}'
        branch_info = run_git_command_silent(branch_info_cmd, repo_path)

    if not branch_info:
        # 3. 嘗試使用 git show 命令
        branch_info_cmd = f'git show -s --format="%ad|%s|%an" --date=format:"%Y-%m-%d %H:%M:%S" {branch_name}'
        branch_info = run_git_command_silent(branch_info_cmd, repo_path)

    if not branch_info:
        # 4. 最後嘗試遠端的 git show
        branch_info_cmd = f'git show -s --format="%ad|%s|%an" --date=format:"%Y-%m-%d %H:%M:%S" origin/{branch_name}'
        branch_info = run_git_command_silent(branch_info_cmd, repo_path)

    if branch_info:
        try:
            date, message, author = branch_info.split('|', 2)
            return {
                'branch_name': branch_name,
                'date': date,
                'message': message,
                'author': author
            }
        except ValueError:
            return {
                'branch_name': branch_name,
                'date': 'Unknown',
                'message': 'Unknown',
                'author': 'Unknown'
            }

    print(f"❌ 無法取得分支資訊: {branch_name}")
    return None


def get_tag_info(repo_path, tag_name):
    """取得 tag 的詳細資訊"""
    # 使用 git show 命令取得 tag 資訊
    tag_info_cmd = f'git show -s --format="%ad|%s|%an" --date=format:"%Y-%m-%d %H:%M:%S" {tag_name}'
    tag_info = run_git_command_silent(tag_info_cmd, repo_path)

    if tag_info:
        try:
            date, message, author = tag_info.split('|', 2)
            return {
                'branch_name': tag_name,  # 保持與 branch_info 相同的欄位名稱以便統一處理
                'date': date,
                'message': message,
                'author': author
            }
        except ValueError:
            return {
                'branch_name': tag_name,
                'date': 'Unknown',
                'message': 'Unknown',
                'author': 'Unknown'
            }

    print(f"❌ 無法取得 tag 資訊: {tag_name}")
    return None


def get_diff_commits_with_details(repo_path, new_branch, old_branch):
    """取得兩個 branch 之間的差異 commit 及詳細資訊"""
    print(f"📋 比較 {new_branch} 和 {old_branch} 之間的差異...")

    # 嘗試多種方式取得差異 commit
    diff_output = None

    # 1. 先嘗試本地分支
    diff_cmd = f'git log {old_branch}..{new_branch} --format="%H"'
    diff_output = run_git_command_silent(diff_cmd, repo_path)

    if not diff_output:
        # 2. 嘗試遠端分支
        diff_cmd = f'git log origin/{old_branch}..origin/{new_branch} --format="%H"'
        diff_output = run_git_command_silent(diff_cmd, repo_path)

    if not diff_output:
        # 3. 嘗試混合模式（本地新分支，遠端舊分支）
        diff_cmd = f'git log origin/{old_branch}..{new_branch} --format="%H"'
        diff_output = run_git_command_silent(diff_cmd, repo_path)

    if not diff_output:
        # 4. 嘗試混合模式（遠端新分支，本地舊分支）
        diff_cmd = f'git log {old_branch}..origin/{new_branch} --format="%H"'
        diff_output = run_git_command_silent(diff_cmd, repo_path)

    if not diff_output:
        print("ℹ️  兩個分支之間沒有差異 commit")
        return []

    commit_hashes = diff_output.split('\n')
    commit_hashes = [hash.strip() for hash in commit_hashes if hash.strip()]

    if len(commit_hashes) == 0:
        print("ℹ️  兩個分支之間沒有差異 commit")
        return []

    print(f"📊 找到 {len(commit_hashes)} 個新增的 commit")
    
    commits_data = []
    
    for i, commit_hash in enumerate(commit_hashes, 1):
        if i % 10 == 0 or i == 1:
            print(f"⚙️  處理差異 commit {i}/{len(commit_hashes)}: {commit_hash[:8]}...")
        
        # 取得基本資訊
        basic_info_cmd = f'git log -1 --date=format:"%Y-%m-%d %H:%M:%S" --format="%s|%ad|%an" {commit_hash}'
        basic_info = run_git_command(basic_info_cmd, repo_path)
        
        if not basic_info:
            continue
        
        try:
            subject, date, author = basic_info.split('|', 2)
        except ValueError:
            print(f"⚠️  跳過格式異常的 commit: {commit_hash[:8]}")
            continue
        
        # 取得檔案變更資訊
        files_cmd = f'git show --name-status --format="" {commit_hash}'
        files_output = run_git_command(files_cmd, repo_path)
        
        files_changed = []
        if files_output:
            for line in files_output.split('\n'):
                line = line.strip()
                if not line:
                    continue
                
                if '\t' in line:
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        status_code = parts[0]
                        file_path = parts[1]
                        
                        status_map = {
                            'A': '新增',
                            'M': '修改', 
                            'D': '刪除',
                            'R': '重新命名',
                            'C': '複製'
                        }
                        
                        status = status_map.get(status_code[0], status_code)
                        
                        files_changed.append({
                            '檔案路徑': file_path,
                            '修改狀態': status,
                            '狀態代碼': status_code
                        })
        
        commit_data = {
            'commit_hash': commit_hash,
            'commit_訊息': subject,
            '提交日期': date,
            '作者': author,
            '檔案變更': files_changed,
            '變更檔案數量': len(files_changed)
        }
        
        commits_data.append(commit_data)
    
    return commits_data


def save_smart_diff_results(project_id, commits_data, new_branch_info, old_branch_info, repo_path):
    """儲存智能差異結果"""
    # 確保輸出目錄存在
    output_dir = ensure_output_directory()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_branch_safe = new_branch_info['branch_name'].replace('/', '_').replace(':', '_')
    old_branch_safe = old_branch_info['branch_name'].replace('/', '_').replace(':', '_')
    # 檔名格式：專案ID_smart_diff_新版本_to_舊版本_時間戳
    filename = f"{project_id}_smart_diff_{new_branch_safe}_to_{old_branch_safe}_{timestamp}.json"
    filepath = output_dir / filename

    output_data = {
        '比較資訊': {
            '專案ID': project_id,
            '倉庫路徑': repo_path,
            '新分支': new_branch_info,
            '舊分支': old_branch_info,
            '比較時間': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '新增commit數量': len(commits_data),
            '比較方式': '多專案智能版本比較'
        },
        '新增commit記錄': commits_data
    }

    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    print(f"📄 差異記錄已儲存至: {filepath}")
    return str(filepath)


def format_branch_name_for_display(branch_name):
    """格式化分支名稱用於顯示，確保一致性"""
    # 如果是 X.X.X.X_hotfix 格式，轉換為 hotfix_X.X.X.X 格式顯示
    if re.match(r'^\d+\.\d+\.\d+\.\d+_hotfix$', branch_name):
        version_part = branch_name.replace('_hotfix', '')
        return f"hotfix_{version_part}"

    # 其他格式保持原樣
    return branch_name


def process_single_comparison(project_id, repo_path, new_version, old_version, test_mode=False, is_tag=False):
    """處理單一比較（支援 branch 和 tag）"""
    # 格式化版本名稱用於顯示
    if not is_tag:
        new_version_display = format_branch_name_for_display(new_version)
        old_version_display = format_branch_name_for_display(old_version)
    else:
        new_version_display = new_version
        old_version_display = old_version

    comparison_type = "Tag" if is_tag else "Branch"
    print(f"\n🔄 處理{comparison_type}比較: {new_version_display} vs {old_version_display}")

    # 取得兩個版本的資訊
    if is_tag:
        new_version_info = get_tag_info(repo_path, new_version)
        old_version_info = get_tag_info(repo_path, old_version)
    else:
        new_version_info = get_branch_info(repo_path, new_version)
        old_version_info = get_branch_info(repo_path, old_version)

    if not new_version_info or not old_version_info:
        print(f"❌ 無法取得版本資訊: {new_version} 或 {old_version}")
        return False

    print(f"🆕 新版本: {new_version_info['date']} - {new_version_info['message'][:50]}...")
    print(f"🔄 舊版本: {old_version_info['date']} - {old_version_info['message'][:50]}...")

    # 取得差異 commit
    commits_data = get_diff_commits_with_details(repo_path, new_version, old_version)

    # 儲存結果
    if not commits_data:
        print("📊 新增 commit 數量: 0")
        print("ℹ️  兩個版本之間沒有新增的 commit，版本內容相同")
        output_file = save_smart_diff_results(project_id, [], new_version_info, old_version_info, repo_path)
        print(f"📄 輸出檔案: {output_file}")

        # 生成 Release Notes
        release_notes_file = generate_release_notes_from_data(project_id, [], new_version_info, old_version_info, repo_path)

        print(f"✅ 完成比較！（無差異）")
    else:
        output_file = save_smart_diff_results(project_id, commits_data, new_version_info, old_version_info, repo_path)

        # 生成 Release Notes
        release_notes_file = generate_release_notes_from_data(project_id, commits_data, new_version_info, old_version_info, repo_path)

        # 顯示統計資訊
        total_files = sum(commit['變更檔案數量'] for commit in commits_data)
        print(f"✅ 完成比較！")
        print(f"📊 新增 commit 數量: {len(commits_data)}")
        print(f"📁 總檔案變更數量: {total_files}")
        print(f"📄 JSON 輸出檔案: {output_file}")
        print(f"📝 Release Notes: {release_notes_file}")

        # 顯示作者統計
        author_count = {}
        for commit in commits_data:
            author = commit['作者']
            author_count[author] = author_count.get(author, 0) + 1

        print(f"👥 主要貢獻者: ", end="")
        top_authors = sorted(author_count.items(), key=lambda x: x[1], reverse=True)[:3]
        print(", ".join([f"{author}({count})" for author, count in top_authors]))

    return True


def select_project(config):
    """選擇專案"""
    projects = list(config['projects'].keys())

    if not projects:
        print("❌ 沒有找到任何專案配置")
        return None

    print("📋 可用專案:")
    for i, project_id in enumerate(projects, 1):
        project = config['projects'][project_id]
        repo_exists = Path(project['repo_path']).exists()
        status = "✅" if repo_exists else "❌"
        print(f"  {i}. {status} {project_id} - {project['name']}")
        print(f"     路徑: {project['repo_path']}")
        print(f"     分支模式: 開頭={project['branch_patterns']['include_starts_with']}, 結尾={project['branch_patterns']['include_ends_with']}")
        print()

    while True:
        try:
            choice = input(f"請選擇專案 (1-{len(projects)}) 或 'all' 處理所有專案: ").strip()

            if choice.lower() == 'all':
                return 'all'

            project_index = int(choice) - 1
            if 0 <= project_index < len(projects):
                return projects[project_index]
            else:
                print("❌ 無效選擇，請重新輸入")
        except ValueError:
            print("❌ 請輸入有效數字或 'all'")


def process_project(project_id, project_config, test_mode=False):
    """處理單一專案"""
    print(f"\n{'='*60}")
    print(f"🚀 處理專案: {project_config['name']} ({project_id})")
    print(f"📁 倉庫路徑: {project_config['repo_path']}")

    repo_path = project_config['repo_path']

    # 檢查倉庫路徑
    if not Path(repo_path).exists():
        print(f"❌ 倉庫路徑不存在: {repo_path}")
        return False

    # 檢查是否為 Git 倉庫
    if not (Path(repo_path) / '.git').exists():
        print(f"❌ 不是有效的 Git 倉庫: {repo_path}")
        return False

    try:
        total_success_count = 0

        # 1. 處理分支比較
        print("\n🌿 處理分支比較...")
        all_branches = get_target_branches(repo_path, project_config['branch_patterns'])
        if all_branches:
            missing_branch_comparisons = get_missing_comparisons(project_id, all_branches, repo_path)

            if missing_branch_comparisons:
                print(f"📊 找到 {len(missing_branch_comparisons)} 個還沒產生記錄的分支比較組合")

                if test_mode:
                    print("🧪 測試模式：只處理第一筆分支比較")
                    missing_branch_comparisons = missing_branch_comparisons[:1]

                # 處理分支比較
                for i, (new_branch, old_branch) in enumerate(missing_branch_comparisons, 1):
                    print(f"\n處理第 {i}/{len(missing_branch_comparisons)} 個分支比較")
                    success = process_single_comparison(project_id, repo_path, new_branch, old_branch, test_mode, is_tag=False)
                    if success:
                        total_success_count += 1
                    else:
                        print(f"❌ 處理失敗: {new_branch} vs {old_branch}")

                    if test_mode:
                        print(f"\n🧪 測試模式完成，只處理了第一筆分支比較")
                        break
            else:
                print("✅ 所有分支比較記錄都已存在！")
        else:
            print("❌ 無法取得目標 branches")

        # 2. 處理 tag 比較（僅針對 BPM 專案）
        if project_id == 'BPM':
            print("\n🏷️  處理 tag 比較...")

            # 定義版本範圍
            tag_version_ranges = [
                {'start': (5, 7, 0, 1), 'end': (5, 7, 7, 2)},  # 5.7.0.1 ~ 5.7.7.2
                {'start': (5, 6, 0, 1), 'end': (5, 6, 5, 8)}   # 5.6.0.1 ~ 5.6.5.8_1
            ]

            # 取得所有 tags
            tag_patterns = {'include_starts_with': ['5.7', '5.6']}
            all_tags = get_target_tags(repo_path, tag_patterns)

            if all_tags:
                missing_tag_comparisons = get_missing_tag_comparisons(project_id, all_tags, tag_version_ranges)

                if missing_tag_comparisons:
                    print(f"📊 找到 {len(missing_tag_comparisons)} 個還沒產生記錄的 tag 比較組合")

                    if test_mode:
                        print("🧪 測試模式：只處理第一筆 tag 比較")
                        missing_tag_comparisons = missing_tag_comparisons[:1]

                    # 處理 tag 比較
                    for i, (new_tag, old_tag) in enumerate(missing_tag_comparisons, 1):
                        print(f"\n處理第 {i}/{len(missing_tag_comparisons)} 個 tag 比較")
                        success = process_single_comparison(project_id, repo_path, new_tag, old_tag, test_mode, is_tag=True)
                        if success:
                            total_success_count += 1
                        else:
                            print(f"❌ 處理失敗: {new_tag} vs {old_tag}")

                        if test_mode:
                            print(f"\n🧪 測試模式完成，只處理了第一筆 tag 比較")
                            break
                else:
                    print("✅ 所有 tag 比較記錄都已存在！")
            else:
                print("❌ 無法取得目標 tags")

        print(f"\n✅ 專案 {project_id} 完成！總共成功處理 {total_success_count} 個比較")
        return True

    except Exception as e:
        print(f"❌ 處理專案 {project_id} 時發生錯誤: {e}")
        return False


def generate_release_notes_from_data(project_id, commits_data, new_branch_info, old_branch_info, repo_path):
    """從比較資料直接生成 Release Notes"""
    # 確保輸出目錄存在
    output_dir = ensure_output_directory()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_branch = new_branch_info['branch_name']
    old_branch = old_branch_info['branch_name']

    # 清理分支名稱用於檔名
    new_branch_safe = new_branch.replace('/', '_').replace(':', '_')

    output_filename = f"Release_Notes_{project_id}_{new_branch_safe}_{timestamp}.md"
    output_filepath = output_dir / output_filename

    try:
        with open(output_filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Release Notes - {project_id}\n\n")
            f.write(f"## 版本資訊\n")
            f.write(f"- **新版本**: {new_branch}\n")
            f.write(f"- **舊版本**: {old_branch}\n")
            f.write(f"- **生成時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"- **新增 Commit 數量**: {len(commits_data)}\n\n")

            # 檢查是否有新增的 commit
            commit_count = len(commits_data)

            if commit_count == 0:
                f.write(f"## 變更摘要\n\n")
                f.write(f"🔄 **無變更**：此版本與前一版本內容完全相同，沒有新增任何 commit。\n\n")
                f.write(f"## 說明\n\n")
                f.write(f"- 兩個版本之間沒有程式碼差異\n")
                f.write(f"- 可能是版本標籤或分支管理的調整\n")
                f.write(f"- 建議確認版本發布的目的和必要性\n\n")
            else:
                f.write(f"## 變更摘要\n\n")

                # 按作者分組
                author_commits = {}
                for commit in commits_data:
                    author = commit['作者']
                    if author not in author_commits:
                        author_commits[author] = []
                    author_commits[author].append(commit)

                for author, commits in author_commits.items():
                    f.write(f"### {author} ({len(commits)} commits)\n\n")
                    for commit in commits:
                        commit_date = commit.get('提交日期', commit.get('日期', 'N/A'))
                        commit_msg = commit.get('commit_訊息', commit.get('訊息', 'N/A'))
                        f.write(f"- **{commit_date}**: {commit_msg}\n")
                        file_count = len(commit.get('檔案變更', []))
                        if file_count > 0:
                            f.write(f"  - 變更檔案: {file_count} 個\n")
                    f.write("\n")

                f.write(f"## 詳細變更記錄\n\n")
                for i, commit in enumerate(commits_data, 1):
                    commit_msg = commit.get('commit_訊息', commit.get('訊息', 'N/A'))
                    commit_id = commit.get('commit_hash', commit.get('commit_id', 'N/A'))
                    commit_author = commit.get('作者', 'N/A')
                    commit_date = commit.get('提交日期', commit.get('日期', 'N/A'))
                    file_changes = commit.get('檔案變更', [])
                    file_count = len(file_changes)

                    f.write(f"### {i}. {commit_msg}\n")
                    f.write(f"- **Commit ID**: `{commit_id}`\n")
                    f.write(f"- **作者**: {commit_author}\n")
                    f.write(f"- **日期**: {commit_date}\n")
                    f.write(f"- **變更檔案數量**: {file_count}\n")

                    # 添加檔案變更詳細資料
                    if file_changes:
                        f.write(f"- **檔案變更詳細**:\n")
                        for file_change in file_changes:
                            file_path = file_change.get('檔案路徑', 'N/A')
                            change_type = file_change.get('修改狀態', 'N/A')
                            status_code = file_change.get('狀態代碼', '')

                            # 根據狀態代碼添加圖示
                            status_icon = {
                                'M': '📝',  # 修改
                                'A': '➕',  # 新增
                                'D': '❌',  # 刪除
                                'R': '🔄',  # 重新命名
                                'C': '📋'   # 複製
                            }.get(status_code, '📄')

                            f.write(f"  - {status_icon} **{change_type}**: `{file_path}`\n")

                    f.write("\n")

        print(f"📝 Release Notes 已生成: {output_filepath}")
        return str(output_filepath)

    except Exception as e:
        print(f"❌ 生成 Release Notes 失敗: {e}")
        return None


def generate_release_notes_for_project(project_id, json_files):
    """為專案生成 Release Notes"""
    if not json_files:
        print(f"⚠️  {project_id} 沒有找到 JSON 檔案")
        return None

    # 選擇最新的 JSON 檔案
    latest_json = max(json_files, key=lambda x: os.path.getmtime(x))
    print(f"📄 使用檔案: {os.path.basename(latest_json)}")

    try:
        with open(latest_json, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 生成 Release Notes
        output_dir = ensure_output_directory()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_branch = data['比較資訊']['新分支']['branch_name']
        old_branch = data['比較資訊']['舊分支']['branch_name']

        # 清理分支名稱用於檔名
        new_branch_safe = new_branch.replace('/', '_').replace(':', '_')

        output_filename = f"Release_Notes_{project_id}_{new_branch_safe}_{timestamp}.md"
        output_filepath = output_dir / output_filename

        with open(output_filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Release Notes - {project_id}\n\n")
            f.write(f"## 版本資訊\n")
            f.write(f"- **新版本**: {new_branch}\n")
            f.write(f"- **舊版本**: {old_branch}\n")
            f.write(f"- **生成時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"- **新增 Commit 數量**: {data['比較資訊']['新增commit數量']}\n\n")

            # 檢查是否有新增的 commit
            commit_count = data['比較資訊']['新增commit數量']

            if commit_count == 0:
                f.write(f"## 變更摘要\n\n")
                f.write(f"🔄 **無變更**：此版本與前一版本內容完全相同，沒有新增任何 commit。\n\n")
                f.write(f"## 說明\n\n")
                f.write(f"- 兩個版本之間沒有程式碼差異\n")
                f.write(f"- 可能是版本標籤或分支管理的調整\n")
                f.write(f"- 建議確認版本發布的目的和必要性\n\n")
            else:
                f.write(f"## 變更摘要\n\n")

                # 按作者分組
                author_commits = {}
                for commit in data['新增commit記錄']:
                    author = commit['作者']
                    if author not in author_commits:
                        author_commits[author] = []
                    author_commits[author].append(commit)

                for author, commits in author_commits.items():
                    f.write(f"### {author} ({len(commits)} commits)\n\n")
                    for commit in commits:
                        commit_date = commit.get('提交日期', commit.get('日期', 'N/A'))
                        commit_msg = commit.get('commit_訊息', commit.get('訊息', 'N/A'))
                        f.write(f"- **{commit_date}**: {commit_msg}\n")
                        file_count = len(commit.get('檔案變更', []))
                        if file_count > 0:
                            f.write(f"  - 變更檔案: {file_count} 個\n")
                    f.write("\n")

                f.write(f"## 詳細變更記錄\n\n")
                for i, commit in enumerate(data['新增commit記錄'], 1):
                    commit_msg = commit.get('commit_訊息', commit.get('訊息', 'N/A'))
                    commit_id = commit.get('commit_hash', commit.get('commit_id', 'N/A'))
                    commit_author = commit.get('作者', 'N/A')
                    commit_date = commit.get('提交日期', commit.get('日期', 'N/A'))
                    file_changes = commit.get('檔案變更', [])
                    file_count = len(file_changes)

                    f.write(f"### {i}. {commit_msg}\n")
                    f.write(f"- **Commit ID**: `{commit_id}`\n")
                    f.write(f"- **作者**: {commit_author}\n")
                    f.write(f"- **日期**: {commit_date}\n")
                    f.write(f"- **變更檔案數量**: {file_count}\n")

                    # 添加檔案變更詳細資料
                    if file_changes:
                        f.write(f"- **檔案變更詳細**:\n")
                        for file_change in file_changes:
                            file_path = file_change.get('檔案路徑', 'N/A')
                            change_type = file_change.get('修改狀態', 'N/A')
                            status_code = file_change.get('狀態代碼', '')

                            # 根據狀態代碼添加圖示
                            status_icon = {
                                'M': '📝',  # 修改
                                'A': '➕',  # 新增
                                'D': '❌',  # 刪除
                                'R': '🔄',  # 重新命名
                                'C': '📋'   # 複製
                            }.get(status_code, '📄')

                            f.write(f"  - {status_icon} **{change_type}**: `{file_path}`\n")

                    f.write("\n")

        print(f"✅ Release Notes 已生成: {output_filepath}")
        return str(output_filepath)

    except Exception as e:
        print(f"❌ 生成 Release Notes 失敗: {e}")
        return None


def main():
    """主程式"""
    print("🚀 多專案 Release 記錄產生工具")
    print("=" * 60)

    # 載入專案配置
    config = load_projects_config()
    if not config:
        print("請先建立 projects_config.json 配置檔案")
        sys.exit(1)

    # 檢查是否為測試模式
    test_mode = len(sys.argv) > 1 and sys.argv[1] == '--test'
    if test_mode:
        print("🧪 測試模式啟用")

    # 選擇專案
    selected = select_project(config)
    if not selected:
        sys.exit(1)

    try:
        if selected == 'all':
            # 處理所有專案
            print(f"\n🌟 處理所有 {len(config['projects'])} 個專案")
            success_count = 0

            for project_id, project_config in config['projects'].items():
                if process_project(project_id, project_config, test_mode):
                    success_count += 1

            print(f"\n{'='*60}")
            print(f"🎉 全部完成！成功處理 {success_count}/{len(config['projects'])} 個專案")
            print(f"📝 每個比較都已自動生成對應的 Release Notes")
        else:
            # 處理單一專案
            project_config = config['projects'][selected]
            if process_project(selected, project_config, test_mode):
                print(f"\n📝 每個比較都已自動生成對應的 Release Notes")

    except KeyboardInterrupt:
        print("\n\n👋 使用者中斷操作")
    except Exception as e:
        print(f"❌ 執行過程中發生錯誤: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 使用者中斷操作")
        sys.exit(0)
