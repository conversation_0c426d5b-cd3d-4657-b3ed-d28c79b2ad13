{"company_id": "06100153", "company_name": "媚登峰", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06100153-媚登峰\\01客戶基本資料", "files": [{"filename": "媚登峰連線資訊.txt", "raw_content": "FortiClient\r\n**************\r\n自定義端口 10443\r\n帳號：erp9\r\n密碼：E58813238gP\r\n\r\n\r\n新EFGP 測試機 \r\n*************\r\nEFGPTest\r\nWin2022\r\nadministrator / admin#DSC2023\r\n\r\nhttp://*************:8086/NaNaWeb \r\nadministrator / admin#DSC\r\n\r\n新EFGP 正式機\r\n*************\r\nWin2022 / MSSQL2022\r\nadministrator / admin#DSC2023\r\n\r\nhttp://*************:8086/NaNaWeb \r\nadministrator / admin#DSC\r\n\r\n連線可用Anydesk  , 在遠端桌面連到EFGP主機\r\nAnydesk\t\r\n1132639420\r\ndsc@28682266\r\n", "structured_data": {"username": "erp9", "password": "E58813238gP", "http": "//*************:8086/NaNaWeb", "host": "**************"}, "source_path": "C1.客戶維護相關\\06100153-媚登峰\\01客戶基本資料\\媚登峰連線資訊.txt", "file_size": 458, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:31.790947"}], "total_files": 1, "processed_at": "2025-08-26T10:46:31.790954"}