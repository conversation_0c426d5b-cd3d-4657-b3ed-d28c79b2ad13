# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.10.3
- **舊版本**: release_5.8.10.2
- **生成時間**: 2025-07-18 11:26:46
- **新增 Commit 數量**: 333

## 變更摘要

### lorenchang (45 commits)

- **2024-10-01 10:53:47**: [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]
  - 變更檔案: 1 個
- **2024-09-10 09:11:31**: [內部]更新Word套表授權碼及多語系[補]
  - 變更檔案: 2 個
- **2024-09-05 16:53:16**: [ESS]新增管理類作業：HR法令知識家(ESSQ96)，
  - 變更檔案: 3 個
- **2024-09-05 16:13:06**: [T100]新增表單：付款條件變更申請單作業(aapt360)、收款條件變更申請單作業(axrt360)、合約維護作業(axmt480)
  - 變更檔案: 6 個
- **2024-09-04 11:04:55**: [流程引擎]C01-20240902005 修正系統設定workitem.list.orderby為空時，點選工作通知出現異常(Server Log：如果已指定 SELECT DISTINCT，則 ORDER BY 項目必須顯示於選取清單中。)
  - 變更檔案: 1 個
- **2024-08-30 15:04:39**: [內部]更新Word套表授權碼及多語系
  - 變更檔案: 5 個
- **2024-08-29 09:43:41**: [Web]C01-20240827002 修正當資料庫類型為 MSSQL_AZURE，使用 SQL 註冊器應用於查詢樣板，模糊查詢條件包含特殊中文字符時無法返回結果
  - 變更檔案: 1 個
- **2024-08-19 16:04:11**: [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙(補)
  - 變更檔案: 1 個
- **2024-08-27 10:00:02**: [E10、回收謝慧功能]BPM用户设置签名图档时，同步给E10(補)
  - 變更檔案: 2 個
- **2024-08-22 16:12:11**: [流程引擎]C01-20240820003 修正流程解析異常時，未啟用系統郵件通知也會觸發寄送Email給管理員
  - 變更檔案: 1 個
- **2024-08-19 16:04:11**: [流程引擎]C01-20240815002 修正用戶登入授權數不足時，未啟用系統郵件通知也會觸發寄送Email給管理員，改為依照啟用設定通知及增加Log記錄
  - 變更檔案: 1 個
- **2024-08-09 17:34:25**: [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙因素認證直接登入的異常
  - 變更檔案: 11 個
- **2024-08-07 16:00:53**: [Web]C01-20240807007 Grid 增加支持 Html 標籤 <p>
  - 變更檔案: 1 個
- **2024-08-07 16:00:53**: C01-20240806003 修正 XPDL 流程核決層級名稱變成「Decision Lv1.」的異常
  - 變更檔案: 1 個
- **2024-08-06 14:56:47**: [內部]修正 Update SQL 存在非法字元，導致版更工具在特定環境執行 SQL 時出現異常
  - 變更檔案: 1 個
- **2024-08-06 13:32:32**: [流程引擎]C01-20240715002 修正 RESTful 轉存表單出現 NullPointerException 的異常
  - 變更檔案: 3 個
- **2024-07-11 17:51:03**: [流程引擎]C01-20240711003 修正特定情境導致工作通知內的批次閱讀出現Oops的異常
  - 變更檔案: 1 個
- **2024-07-05 14:51:54**: [T100]C01-20240704005 修正工單完工入庫作業 (asft340) 元件異常，導致轉換為 RWD 後更改對齊方式時 Grid 及 Attachment 元件消失的問題
  - 變更檔案: 2 個
- **2024-06-25 16:34:02**: [內部]更新58102patch
  - 變更檔案: 1 個
- **2024-06-25 16:05:28**: [文件智能家]修正多語系：文件智能家應用管理改為文件智能家模組
  - 變更檔案: 3 個
- **2024-06-25 15:44:52**: [文件智能家]修正因流程主機位址不是127.0.0.1或localhost導致取得AccessToken失敗，間接導致不會觸發ChatFile接口
  - 變更檔案: 4 個
- **2024-06-25 15:36:17**: [文件智能家]修正DB為Oracle時，取得最近5筆歷史問答紀錄因為欄位reponseData為Clob無法使用查詢EQUAL的查詢條件，改為EXACT_NOT_LIKE
  - 變更檔案: 2 個
- **2024-06-24 15:40:06**: [文件智能家]修正使用首頁模組進入的表單畫面中的附件圖示打開後不會顯示助閱讀
  - 變更檔案: 1 個
- **2024-06-24 13:51:03**: [文件智能家]修正附件圖示無法每次都正常顯示的異常
  - 變更檔案: 3 個
- **2024-06-20 19:53:49**: [文件智能家]更新表單流程及多語系
  - 變更檔案: 5 個
- **2024-06-14 17:57:41**: [文件智能家]更新表單及多語系
  - 變更檔案: 4 個
- **2024-06-14 11:33:04**: [流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗[補]
  - 變更檔案: 2 個
- **2024-06-12 17:12:45**: [文件智能家]更新多語系
  - 變更檔案: 6 個
- **2024-06-12 17:11:13**: [文件智能家]增加卡控：長知識關聯作業有ISO授權才會顯示
  - 變更檔案: 1 個
- **2024-06-07 17:31:26**: [文件智能家]修正上傳檔案到ChatFile失敗的異常
  - 變更檔案: 1 個
- **2024-06-07 14:22:23**: [文件智能家]修正ISO表單相關元件名稱
  - 變更檔案: 3 個
- **2024-06-06 18:03:49**: [內部]更新Patch及Create SQL
  - 變更檔案: 4 個
- **2024-03-25 16:33:46**: [文件智能家]新增模組
  - 變更檔案: 65 個
- **2024-05-03 09:42:25**: [PLM]增加 Digiwin PLM 整合，與 Open PLM 共用序號[補]
  - 變更檔案: 3 個
- **2024-05-02 16:49:39**: [PLM]增加 Digiwin PLM 整合，與 Open PLM 共用序號
  - 變更檔案: 9 個
- **2024-08-27 10:00:02**: [E10、回收謝慧功能]BPM用户设置签名图档时，同步给E10(補)
  - 變更檔案: 2 個
- **2024-08-22 16:12:11**: [流程引擎]C01-20240820003 修正流程解析異常時，未啟用系統郵件通知也會觸發寄送Email給管理員
  - 變更檔案: 1 個
- **2024-08-19 16:04:11**: [流程引擎]C01-20240815002 修正用戶登入授權數不足時，未啟用系統郵件通知也會觸發寄送Email給管理員，改為依照啟用設定通知及增加Log記錄
  - 變更檔案: 1 個
- **2024-08-09 17:34:25**: [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙因素認證直接登入的異常
  - 變更檔案: 11 個
- **2024-08-07 16:00:53**: [Web]C01-20240807007 Grid 增加支持 Html 標籤 <p>
  - 變更檔案: 1 個
- **2024-08-07 16:00:53**: C01-20240806003 修正 XPDL 流程核決層級名稱變成「Decision Lv1.」的異常
  - 變更檔案: 1 個
- **2024-08-06 14:56:47**: [內部]修正 Update SQL 存在非法字元，導致版更工具在特定環境執行 SQL 時出現異常
  - 變更檔案: 1 個
- **2024-08-06 13:32:32**: [流程引擎]C01-20240715002 修正 RESTful 轉存表單出現 NullPointerException 的異常
  - 變更檔案: 3 個
- **2024-07-11 17:51:03**: [流程引擎]C01-20240711003 修正特定情境導致工作通知內的批次閱讀出現Oops的異常
  - 變更檔案: 1 個
- **2024-07-05 14:51:54**: [T100]C01-20240704005 修正工單完工入庫作業 (asft340) 元件異常，導致轉換為 RWD 後更改對齊方式時 Grid 及 Attachment 元件消失的問題
  - 變更檔案: 2 個

### 張詠威 (18 commits)

- **2024-10-01 10:16:11**: [Web]C01-20240927004 修正58103版本流程的簽核歷程和簡易流程圖畫面沒有顯示進行中的關卡的資訊
  - 變更檔案: 1 個
- **2024-09-19 11:31:42**: [流程引擎]C01-20240704001 修正系統排程AutoRegetWorkAssignmentHandler未能正常取回代理轉派的關卡[補]
  - 變更檔案: 1 個
- **2024-09-13 16:50:27**: [Web]C01-20240910004 調整Restful 發單接口的form_id參數的長度上限由50改為100
  - 變更檔案: 1 個
- **2024-09-13 11:36:02**: [其他]Q00-20240913001 修正DPC轉檔工具於系統啟動時，未能清除轉檔暫存檔案
  - 變更檔案: 1 個
- **2024-09-13 11:28:15**: [Web]C01-20240911004 修正待辦清單、追蹤流程清單，當流程主旨設定textarea元件且內容有換行時，流程主機的Title屬性未呈現換行效果
  - 變更檔案: 5 個
- **2024-09-11 11:18:50**: [Web]Q00-20240418001 修正资料选取器回传栏位Disable状态下，双击清空栏位仍生效的问题實際操作時沒有效果[補]
  - 變更檔案: 2 個
- **2024-09-03 14:10:31**: [WEB]C01-20240830004 調整「資料選取註冊器」開窗的行動版頁面新增全選按鈕
  - 變更檔案: 1 個
- **2024-08-30 14:52:46**: [TIPTOP]C01-20240828005 修正流程設定授權人員，當可存取的範圍有設定部門時，透過外部連結查看時沒有參考設定的部門權限，導致作業畫面仍可開啟的異常
  - 變更檔案: 1 個
- **2024-08-27 17:24:26**: [流程引擎]C01-20240816003 優化服務任務觸發機制，降低重複執行導致狀態不一致的異常
  - 變更檔案: 2 個
- **2024-08-27 15:17:47**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM into develop_v58
- **2024-08-27 11:43:37**: [資安]Q00-20240827002 修正登入頁面的UserId欄位存在SQL injection的風險
  - 變更檔案: 1 個
- **2024-08-14 14:52:46**: [ISO]C01-20240813003 調整安裝光碟ISO文件調閱申請流程，因流程ApplyEmp關卡閘道未對稱，導致流程簽出後無法再次簽入
  - 變更檔案: 1 個
- **2024-08-12 14:28:37**: [流程引擎]C01-20240704001 修正系統排程AutoRegetWorkAssignmentHandler未能正常取回代理轉派的關卡
  - 變更檔案: 1 個
- **2024-08-07 17:16:29**: [Web]Q00-20240718001 修正部分流程的簽核歷程資訊未能顯示通知關卡的異常
  - 變更檔案: 2 個
- **2024-08-27 11:43:37**: [資安]Q00-20240827002 修正登入頁面的UserId欄位存在SQL injection的風險
  - 變更檔案: 1 個
- **2024-08-14 14:52:46**: [ISO]C01-20240813003 調整安裝光碟ISO文件調閱申請流程，因流程ApplyEmp關卡閘道未對稱，導致流程簽出後無法再次簽入
  - 變更檔案: 1 個
- **2024-08-12 14:28:37**: [流程引擎]C01-20240704001 修正系統排程AutoRegetWorkAssignmentHandler未能正常取回代理轉派的關卡
  - 變更檔案: 1 個
- **2024-08-07 17:16:29**: [Web]Q00-20240718001 修正部分流程的簽核歷程資訊未能顯示通知關卡的異常
  - 變更檔案: 2 個

### kmin (58 commits)

- **2024-10-01 10:12:03**: [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息[補]
  - 變更檔案: 1 個
- **2024-09-20 10:50:05**: [Web表單設計師]A00-20240902001 修正pairId重覆多組導致元件消失以及代號異常
  - 變更檔案: 1 個
- **2024-09-20 10:24:02**: [Web]A00-20240911001 修正TraceProcessForSearchForm接口當參數是有傳isTraceOtherProcess時導致附件權限資訊載入異常問題
  - 變更檔案: 2 個
- **2024-09-20 10:00:31**: [派送關聯模組]C01-20240903013 修正派送流程關聯的後置流程單身資缺少問題
  - 變更檔案: 1 個
- **2024-09-18 16:28:03**: [Web]V00-20240918001 修正當活動關卡設定有勾選「允許輸入密碼」造成批次終止跟轉派異常
  - 變更檔案: 1 個
- **2024-09-18 14:40:51**: [Web]A00-20240916001 修正lbl_開頭的元件ID卻是InputElement元件轉型異常問題
  - 變更檔案: 1 個
- **2024-09-13 10:16:03**: [流程引擎]C01-20240909004 修正呼叫WorkflowService的fetchFullProcInstanceWithSerialNo有進核決關卡時異常問題
  - 變更檔案: 1 個
- **2024-09-04 14:16:44**: [Web]C01-20240903001 修正監控流程匯出Excel異常問題
  - 變更檔案: 1 個
- **2024-08-29 11:51:54**: [Web]A00-20240828001 修正下拉式選項(Dropdown)-disable的狀態高度顯示異常
  - 變更檔案: 1 個
- **2024-08-28 08:26:56**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-08-27 17:26:46**: [Web]C01-20240826002 修正加簽後流程圖片的分支線有重疊的狀況
  - 變更檔案: 1 個
- **2024-08-23 14:50:37**: [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息
  - 變更檔案: 1 個
- **2024-08-21 15:03:06**: [Web表單設計師]C01-20240819004 修正編輯多語系顯示存入html格式與預設值不符
  - 變更檔案: 1 個
- **2024-08-19 09:03:14**: [Web]C01-20240815003 修正SQL註冊器的查詢條件開始包含跟結束包含的查詢結果相反
  - 變更檔案: 1 個
- **2024-08-16 10:40:37**: [Web]C01-20240814001 修正增加Grid若綁定的元件是需要key：「元件ID_txt」的判斷防止必填檢查功能失效
  - 變更檔案: 1 個
- **2024-08-15 16:52:05**: [T100]Q00-20240815001 優化T100拋單若流程第一關非流程發起者用warn訊息警告
  - 變更檔案: 1 個
- **2024-08-14 09:47:09**: [T100]Q00-20240814001 修正T100錯誤訊息判讀多語系時異常問題
  - 變更檔案: 1 個
- **2024-08-13 11:45:57**: [Web]C01-20240808001 修正formValidation.validateGrid邏輯異常
  - 變更檔案: 1 個
- **2024-08-12 11:48:38**: [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題
  - 變更檔案: 1 個
- **2024-08-08 17:25:43**: [內部]Q00-20240808001 優化TIPTOP(WF)拋單處理附件時異常的錯誤訊息
  - 變更檔案: 1 個
- **2024-08-06 10:49:28**: [內部]Q00-20240806001 優化BpmProcessWorkbox log訊息方便排除日常問題
  - 變更檔案: 1 個
- **2024-07-31 14:19:23**: [內部]Q00-20240731001 優化若角色為administrator就直接回傳true，不需要再額外做事判斷關卡是否可以列印
  - 變更檔案: 1 個
- **2024-07-29 11:14:18**: [E10]C01-20240722003 修正E10拋單發起人為離職人員不該成功
  - 變更檔案: 1 個
- **2024-07-29 10:37:00**: [ESS]A00-20240726001 修正有判斷重復的在其他流程實例，就彈出提示訊息的多語系問題
  - 變更檔案: 1 個
- **2024-07-19 12:05:04**: [內部]Q00-20240719001 優化流程關卡轉派代理人的log機制
  - 變更檔案: 2 個
- **2024-07-18 17:03:25**: [SAP]C01-20240712005 修正SAP欄位對應設定若固定值跟說明欄位輸入小於符號呈現異常問題
  - 變更檔案: 2 個
- **2024-07-18 15:23:39**: [SAP]C01-20240712005 優化SAP的log訊息，目前是遇到SapFormMapping.mappingXML欄位內容格式異常
  - 變更檔案: 1 個
- **2024-07-15 14:28:25**: [SAP]C01-20240712005 優化SAP的log訊息
  - 變更檔案: 1 個
- **2024-07-08 17:39:55**: [Web]Q00-20240702001 修正移除重覆多餘的批次終止、轉派項目 當活動關卡設定有勾選「允許輸入密碼」就會出現重覆，但實際上點了之後是無法正常運作的
  - 變更檔案: 1 個
- **2024-07-08 11:22:58**: [流程引擎]C01-20240703009 修正簡易流程預解析核決內加簽出來的關卡取回重辦後流程圖出現null訊息
  - 變更檔案: 2 個
- **2024-07-04 16:44:04**: [其他]Q00-20240704001 優化預解析Log訊息加上簡易的Log
  - 變更檔案: 1 個
- **2024-07-04 11:40:24**: [流程引擎]C01-20240701002 修正並簽流程的其中一關終止流程，並簽流程其他關卡的簽核意見會顯示取回重辦 客戶情境：並簽的關卡中做終止流程的動作，其他並簽關卡的簽核意見出問題，現在改為「已終止(UserId-UserName)」 修正程式完後並測試情境有： 1.其他並簽已簽完 2.退回重辦 3.取回重辦 4.向前加簽
  - 變更檔案: 1 個
- **2024-07-03 10:56:17**: [流程引擎]C01-20240625006 修正WebService退回重辦額外收到追蹤通知信問題 客戶情境：A->B->C，C用webservice退回B，A、B都會收到信，但用Web介面就只有B會收到信，沒有開退回逐級通知。 程式改完並4個情境測試結果： 1.一般關卡退一般關卡=>測試正常 2.核決退一般關卡=>測試正常 3.開逐級通知的一般關卡退一般關卡=>測試正常 4.開逐級通知的核決退一般關卡=>測試正常
  - 變更檔案: 1 個
- **2024-06-17 11:41:59**: [ISO] V00-20240612011 修正文管首頁調閱後進文件調閱申請後session未清除問題 再從發起流程入口進入後，因wms_manageDocument_documentOID未清除，而預設代入文件編號。
  - 變更檔案: 1 個
- **2024-06-07 16:39:55**: [表單設計師] C01-20240605002 修正絕對位置表單的label以及TextBox的label轉換為RWD表單，底色沒有跟著過去
  - 變更檔案: 1 個
- **2024-06-04 11:16:33**: [Web] C01-202410603001 檢核表單元件資料型態與DB欄位型態是否一致
  - 變更檔案: 1 個
- **2024-08-23 14:50:37**: [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息
  - 變更檔案: 1 個
- **2024-08-21 15:03:06**: [Web表單設計師]C01-20240819004 修正編輯多語系顯示存入html格式與預設值不符
  - 變更檔案: 1 個
- **2024-08-19 09:03:14**: [Web]C01-20240815003 修正SQL註冊器的查詢條件開始包含跟結束包含的查詢結果相反
  - 變更檔案: 1 個
- **2024-08-16 10:40:37**: [Web]C01-20240814001 修正增加Grid若綁定的元件是需要key：「元件ID_txt」的判斷防止必填檢查功能失效
  - 變更檔案: 1 個
- **2024-08-15 16:52:05**: [T100]Q00-20240815001 優化T100拋單若流程第一關非流程發起者用warn訊息警告
  - 變更檔案: 1 個
- **2024-08-14 09:47:09**: [T100]Q00-20240814001 修正T100錯誤訊息判讀多語系時異常問題
  - 變更檔案: 1 個
- **2024-08-13 11:45:57**: [Web]C01-20240808001 修正formValidation.validateGrid邏輯異常
  - 變更檔案: 1 個
- **2024-08-12 11:48:38**: [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題
  - 變更檔案: 1 個
- **2024-08-08 17:25:43**: [內部]Q00-20240808001 優化TIPTOP(WF)拋單處理附件時異常的錯誤訊息
  - 變更檔案: 1 個
- **2024-08-06 10:49:28**: [內部]Q00-20240806001 優化BpmProcessWorkbox log訊息方便排除日常問題
  - 變更檔案: 1 個
- **2024-07-31 14:19:23**: [內部]Q00-20240731001 優化若角色為administrator就直接回傳true，不需要再額外做事判斷關卡是否可以列印
  - 變更檔案: 1 個
- **2024-07-29 11:14:18**: [E10]C01-20240722003 修正E10拋單發起人為離職人員不該成功
  - 變更檔案: 1 個
- **2024-07-29 10:37:00**: [ESS]A00-20240726001 修正有判斷重復的在其他流程實例，就彈出提示訊息的多語系問題
  - 變更檔案: 1 個
- **2024-07-19 12:05:04**: [內部]Q00-20240719001 優化流程關卡轉派代理人的log機制
  - 變更檔案: 2 個
- **2024-07-18 17:03:25**: [SAP]C01-20240712005 修正SAP欄位對應設定若固定值跟說明欄位輸入小於符號呈現異常問題
  - 變更檔案: 2 個
- **2024-07-18 15:23:39**: [SAP]C01-20240712005 優化SAP的log訊息，目前是遇到SapFormMapping.mappingXML欄位內容格式異常
  - 變更檔案: 1 個
- **2024-07-15 14:28:25**: [SAP]C01-20240712005 優化SAP的log訊息
  - 變更檔案: 1 個
- **2024-07-08 17:39:55**: [Web]Q00-20240702001 修正移除重覆多餘的批次終止、轉派項目 當活動關卡設定有勾選「允許輸入密碼」就會出現重覆，但實際上點了之後是無法正常運作的
  - 變更檔案: 1 個
- **2024-07-08 11:22:58**: [流程引擎]C01-20240703009 修正簡易流程預解析核決內加簽出來的關卡取回重辦後流程圖出現null訊息
  - 變更檔案: 2 個
- **2024-07-04 16:44:04**: [其他]Q00-20240704001 優化預解析Log訊息加上簡易的Log
  - 變更檔案: 1 個
- **2024-07-04 11:40:24**: [流程引擎]C01-20240701002 修正並簽流程的其中一關終止流程，並簽流程其他關卡的簽核意見會顯示取回重辦 客戶情境：並簽的關卡中做終止流程的動作，其他並簽關卡的簽核意見出問題，現在改為「已終止(UserId-UserName)」 修正程式完後並測試情境有： 1.其他並簽已簽完 2.退回重辦 3.取回重辦 4.向前加簽
  - 變更檔案: 1 個
- **2024-07-03 10:56:17**: [流程引擎]C01-20240625006 修正WebService退回重辦額外收到追蹤通知信問題 客戶情境：A->B->C，C用webservice退回B，A、B都會收到信，但用Web介面就只有B會收到信，沒有開退回逐級通知。 程式改完並4個情境測試結果： 1.一般關卡退一般關卡=>測試正常 2.核決退一般關卡=>測試正常 3.開逐級通知的一般關卡退一般關卡=>測試正常 4.開逐級通知的核決退一般關卡=>測試正常
  - 變更檔案: 1 個

### davidhr (12 commits)

- **2024-09-23 16:28:14**: 更新58103patch
  - 變更檔案: 1 個
- **2024-09-23 13:10:05**: [整合]新安裝T100預設表單(請購單),放錯位置,重新放到form-default
  - 變更檔案: 1 個
- **2024-09-19 18:11:34**: [整合]配合安裝程式匯入表單的內容,增加Release\copyfiles\@t100\form-default\請購單(apmt400).form Release\copyfiles\@workflow\form-default\請購單建立作業(PURI05)[GP25(PR)].form
  - 變更檔案: 2 個
- **2024-09-13 18:38:14**: 更新58103patch
  - 變更檔案: 1 個
- **2024-09-11 14:06:19**: [資安]新增robots.txt檔，阻止所有搜索引擎索引站台內容
  - 變更檔案: 1 個
- **2024-09-06 18:46:42**: [資安]C01-20240813004 javascript框架庫漏洞,將jquery.js內容中的版本號換成a.k.c跟a.j.c
  - 變更檔案: 2 個
- **2024-07-22 11:45:16**: [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
  - 變更檔案: 68 個
- **2024-07-22 11:37:54**: [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
  - 變更檔案: 6 個
- **2024-06-18 10:37:39**: [內部]更新58102patch
  - 變更檔案: 1 個
- **2024-06-06 11:07:55**: [內部]更新58102patch
  - 變更檔案: 1 個
- **2024-07-22 11:45:16**: [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
  - 變更檔案: 68 個
- **2024-07-22 11:37:54**: [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
  - 變更檔案: 6 個

### 周权 (57 commits)

- **2024-09-23 15:28:50**: [文件智能家] chatfile设定档资料新增chatfile接口授权令牌
  - 變更檔案: 9 個
- **2024-09-18 16:44:36**: [文件智能家] chatfile预览接口调整
  - 變更檔案: 1 個
- **2024-09-12 17:57:34**: [Web] V00-20240912001 调整页面闲置过久alert提示
  - 變更檔案: 2 個
- **2024-09-12 15:43:01**: [Word套表] 更新word套表多语系
  - 變更檔案: 1 個
- **2024-09-11 14:21:27**: [Word套表] 调整word套表相关sql文件
  - 變更檔案: 3 個
- **2024-09-11 09:40:14**: [Word套表] 调整部分文件，ISOWatermarkPattern新增栏位
  - 變更檔案: 3 個
- **2024-06-07 17:07:35**: [ESS]C01-20240902004 修正ESS流程若有退回、取回重辦至第一關時，當系統有開啟非同步簽核時，ESS觸發結案的服務任務會因為Identifier相同而導致無法回寫結案的異常
  - 變更檔案: 3 個
- **2024-08-30 17:50:25**: [文件智能家] 調整助閱讀附件上傳邏輯
  - 變更檔案: 6 個
- **2024-08-12 17:25:45**: [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題[补]
  - 變更檔案: 1 個
- **2024-08-05 10:02:14**: [流程引擎]C01-20240801004 修正监控流程中删除流程逻辑
  - 變更檔案: 1 個
- **2024-08-02 15:25:21**: [Web]C01-20240731001 修正同瀏覽器先後登入兩位使用者，都會被登出的問題
  - 變更檔案: 2 個
- **2024-08-01 17:53:33**: [Web]C01-20240729003 修正切換頁籤Grid寬度異常問題
  - 變更檔案: 3 個
- **2024-07-23 17:16:35**: [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題[补]
  - 變更檔案: 1 個
- **2024-07-23 11:04:57**: [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題
  - 變更檔案: 1 個
- **2024-07-12 11:45:55**: [Web]C01-20240711002 修正回传栏位含有“_txt”"_lbl"，若勾选双击清空栏位，表单开启报错的问题
  - 變更檔案: 1 個
- **2024-07-03 17:17:49**: [Web]C01-20240702005 列印模式下Grid顯示千分位異常
  - 變更檔案: 2 個
- **2024-07-03 11:02:17**: [Web]C01-20240702001 修正firefox下載的附件名稱前后都會有_的问题
  - 變更檔案: 1 個
- **2024-07-01 14:35:55**: [Web]C01-20240628004 修正“/NaNaWeb/GP/ForwardIndex”會導頁到錯誤頁面
  - 變更檔案: 2 個
- **2024-06-27 11:35:58**: [Web]C01-20240626002 修正列印模式下Grid無法正常顯示千分位的问题
  - 變更檔案: 1 個
- **2024-06-25 10:56:29**: [Web]C01-20240620009 判斷URL是否符合產品的模組URL新增防呆
  - 變更檔案: 1 個
- **2024-06-24 11:12:01**: [Web]C01-20240620007 修正點簽核狀態報錯
  - 變更檔案: 4 個
- **2024-06-17 10:44:47**: [Web]C01-20240613007 修正外部連結登入BPM，若密碼輸入錯誤再重新登入BPM後，會呈現首頁的畫面的问题
  - 變更檔案: 1 個
- **2024-06-14 15:56:31**: [内部]V00-20240612012 修正終止流程按取消會跳下一張單據，不會停在原單據
  - 變更檔案: 1 個
- **2024-06-07 17:10:26**: [流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗[補]
  - 變更檔案: 1 個
- **2024-06-07 17:07:35**: [Web]C01-20240522002 修正系统权限管理员[可存取的範圍]设定无效的问题
  - 變更檔案: 4 個
- **2024-06-05 15:15:20**: [流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗
  - 變更檔案: 1 個
- **2024-06-03 09:00:11**: [Web]C01-20240531001 修正列印表單報錯 _self.table.bootstrapTable is not a function
  - 變更檔案: 1 個
- **2024-05-29 09:04:58**: [Web]C01-20240528001 修正Grid无资料时栏位宽度异常的问题
  - 變更檔案: 1 個
- **2024-05-21 17:59:32**: [Web]C01-20240516002 修正流程設定授權人員，外部連結查看表單資訊會顯示此人員無權限訪問
  - 變更檔案: 1 個
- **2024-05-21 10:50:43**: [Web]C01-20240517012 修正开窗类型为使用者可以勾选"前置组织代号"的问题
  - 變更檔案: 1 個
- **2024-05-17 10:52:46**: [Web]C01-20240516006 修正简易sql被过滤表名导致查询失败的问题
  - 變更檔案: 1 個
- **2024-05-16 15:45:10**: [内部]Q00-20240516001 修正查询sql包含多组"order by",导致资料选取注册器及SQL注册器查询失败问题
  - 變更檔案: 1 個
- **2024-05-16 08:57:01**: [Web]C01-20240515001 修正客制開窗sql语句包含多组order by，sql會查詢失敗的問題
  - 變更檔案: 1 個
- **2024-05-15 15:06:00**: [Web]C01-20240514002 修正企業流程監控→加總→已處理工作量,選取多人或多部門產生統計圖報錯的問題[補]
  - 變更檔案: 1 個
- **2024-05-15 10:00:05**: [Web]C01-20240514002 修正企業流程監控→加總→已處理工作量,選取多人或多部門產生統計圖報錯的問題
  - 變更檔案: 1 個
- **2024-05-14 10:21:01**: [内部]Q00-20240514001 修改系統設定trace.process.hidden.process.package.ids.prefix的描述
  - 變更檔案: 3 個
- **2024-05-10 17:31:42**: [Web]C01-20240509004 修正grid多栏位格线对不齐的问题
  - 變更檔案: 1 個
- **2024-05-10 14:04:21**: [Web]V00-20240508001 修正流程主旨範本設定<#ActivityId>、 <#ActivityName>显示N.A.
  - 變更檔案: 1 個
- **2024-05-08 11:43:06**: [流程引擎]C01-20240502001 修正在核決權限關卡加簽，預解析会重复出现核決權限表名稱的问题
  - 變更檔案: 1 個
- **2024-05-08 10:43:00**: [Web]Q00-20240508001 修正系統設定發起人看不到追蹤流程之設定
  - 變更檔案: 1 個
- **2024-04-26 10:35:22**: [Web]Q00-20240426003 修正栏位过多，设置栏位宽度没效果的问题
  - 變更檔案: 1 個
- **2024-04-25 15:32:11**: [Web]Q00-20240425002 修正开启绝对位置表单偶发报错TypeError: Cannot read properties of undefined (reading 'ElementGroup')的问题
  - 變更檔案: 1 個
- **2024-04-24 15:39:24**: [内部]Q00-20240423002 登陸頁面新增個資安全宣告，需通過系統設定personal.data.protection.web開啓
  - 變更檔案: 1 個
- **2024-08-27 13:16:14**: [文件智能家] 调整新增长知识根目录可以问问题
  - 變更檔案: 1 個
- **2024-08-12 17:25:45**: [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題[补]
  - 變更檔案: 1 個
- **2024-08-05 10:02:14**: [流程引擎]C01-20240801004 修正监控流程中删除流程逻辑
  - 變更檔案: 1 個
- **2024-08-02 15:25:21**: [Web]C01-20240731001 修正同瀏覽器先後登入兩位使用者，都會被登出的問題
  - 變更檔案: 2 個
- **2024-08-01 17:53:33**: [Web]C01-20240729003 修正切換頁籤Grid寬度異常問題
  - 變更檔案: 3 個
- **2024-07-23 17:16:35**: [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題[补]
  - 變更檔案: 1 個
- **2024-07-23 11:04:57**: [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題
  - 變更檔案: 1 個
- **2024-07-12 11:45:55**: [Web]C01-20240711002 修正回传栏位含有“_txt”"_lbl"，若勾选双击清空栏位，表单开启报错的问题
  - 變更檔案: 1 個
- **2024-07-03 17:17:49**: [Web]C01-20240702005 列印模式下Grid顯示千分位異常
  - 變更檔案: 2 個
- **2024-07-03 11:02:17**: [Web]C01-20240702001 修正firefox下載的附件名稱前后都會有_的问题
  - 變更檔案: 1 個
- **2024-07-01 14:35:55**: [Web]C01-20240628004 修正“/NaNaWeb/GP/ForwardIndex”會導頁到錯誤頁面
  - 變更檔案: 2 個
- **2024-06-27 11:35:58**: [Web]C01-20240626002 修正列印模式下Grid無法正常顯示千分位的问题
  - 變更檔案: 1 個
- **2024-06-27 08:53:55**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-06-25 10:56:29**: [Web]C01-20240620009 判斷URL是否符合產品的模組URL新增防呆
  - 變更檔案: 1 個

### xiehui (5 commits)

- **2024-09-13 15:27:29**: 【补】工作转派同步至钉钉待办中，转派取消工作通知
  - 變更檔案: 1 個
- **2024-08-20 17:56:43**: [BPM APP、回收謝慧功能]工作转派同步至钉钉待办中
  - 變更檔案: 1 個
- **2024-08-20 17:11:38**: [E10、回收謝慧功能]BPM用户设置签名图档时，同步给E10
  - 變更檔案: 12 個
- **2024-08-20 17:56:43**: [BPM APP、回收謝慧功能]工作转派同步至钉钉待办中
  - 變更檔案: 1 個
- **2024-08-20 17:11:38**: [E10、回收謝慧功能]BPM用户设置签名图档时，同步给E10
  - 變更檔案: 12 個

### yamiyeh10 (49 commits)

- **2024-09-20 09:06:12**: [PRODT]Q00-20240626002 修正Web流程管理工具中關卡與連接線存在髒資料卻無提示的問題
  - 變更檔案: 2 個
- **2024-08-29 11:00:33**: [BPM APP]C01-20240726001 修正行動表單的RadioButton和CheckBox元件唯讀狀態下不會顯示額外輸入框內容問題[補]
  - 變更檔案: 1 個
- **2024-08-21 14:35:27**: [行業表單庫]新增範例表單檔案
  - 變更檔案: 118 個
- **2024-08-19 15:26:08**: [BPM APP]C01-20240816007 修正頁籤元件內的頁籤代號有相似時後端取內容會發生錯亂問題
  - 變更檔案: 1 個
- **2024-08-07 17:36:56**: [BPM APP]Q00-20240807002 修正移動端已轉派列表有資料時會卡在loading畫面問題
  - 變更檔案: 1 個
- **2024-08-07 17:18:07**: [BPM APP]C01-20240807002 調整行動端的主旨支持換行符號
  - 變更檔案: 20 個
- **2024-08-07 11:35:53**: [內部]Q00-*********** 調整企業微信推播邏輯並新增錯誤log訊息
  - 變更檔案: 2 個
- **2024-07-30 17:12:00**: [BPM APP]修正行動表單的RadioButton和CheckBox元件唯讀狀態下不會顯示額外輸入框內容問題
  - 變更檔案: 3 個
- **2024-07-26 11:59:59**: [PRODT]Q00-20240710001 調整Web流程管理工具中儲存流程定義模型前增加檢核參與者機制
  - 變更檔案: 1 個
- **2024-07-10 13:41:28**: [PRODT]C01-20240614001 調整Web流程管理工具中主流程設定屬性的作為活動處理者代理人機制[補]
  - 變更檔案: 1 個
- **2024-07-09 16:03:39**: [BPM APP]C01-20240708002 修正使用自定義開窗類型時透過FormUtil.disable方法變更元件編輯狀態會發生找不到onclick事件問題
  - 變更檔案: 1 個
- **2024-07-04 16:55:32**: [BPM APP]C01-20240703004 修正企業微信值接從菜單進入到工作首頁時畫面異常問題
  - 變更檔案: 1 個
- **2024-07-02 15:41:46**: [MPT]C01-20240627004 調整MPT公告申請單中刊登時間未選擇日期前先上傳附件會導致日期欄位出現NaN問題
  - 變更檔案: 1 個
- **2024-07-01 17:26:32**: [BPM APP]C01-20240527003 修正整合企業微信在進行使用者登入返回的錯誤訊息都是undefined問題[補]
  - 變更檔案: 2 個
- **2024-06-27 08:23:04**: [PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題
  - 變更檔案: 1 個
- **2024-06-20 11:23:12**: [行業表單庫]V00-20240620001 增加卡控使用的表單代號必須為英文字母開頭，並且由英文字母、數字或底線組成[補]
  - 變更檔案: 1 個
- **2024-06-19 10:20:58**: [SYSDT]Q00-20240605001 調整系統管理工具中測試系統郵件後的提示訊息
  - 變更檔案: 1 個
- **2024-06-11 17:18:42**: [BPM APP]C01-20240611001 修正行動簽核管理中心的釘釘待辦同步頁面無法開窗選擇流程名稱與處理者問題
  - 變更檔案: 1 個
- **2024-06-06 16:48:44**: [行業表單庫]新增範例表單檔案
  - 變更檔案: 42 個
- **2024-06-05 17:25:04**: [ORGDT]C01-20240517013 調整Web化設計工具在打開後隔一段時間會發生操作錯誤問題
  - 變更檔案: 5 個
- **2024-06-04 14:15:18**: [PRODT]C01-20240603003 修正流程實例中的詳細流程圖中活動關卡與連接線消失問題
  - 變更檔案: 1 個
- **2024-05-30 17:05:50**: [BPM APP]C01-20240527003 修正整合企業微信在進行使用者登入返回的錯誤訊息都是undefined問題
  - 變更檔案: 2 個
- **2024-05-29 16:47:14**: [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
  - 變更檔案: 1 個
- **2024-05-29 16:09:42**: [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息
  - 變更檔案: 1 個
- **2024-05-20 12:06:56**: [PRODT]C01-20240513002 修正Web流程管理工具的核決層級關卡中設定過參考活動為自定義時殘留的贓資料會導致流程無法簽入的問題
  - 變更檔案: 1 個
- **2024-05-17 15:26:09**: [PRODT]C01-20240509001 修正流程實例中的詳細流程圖中連接線條件名稱會覆蓋問題
  - 變更檔案: 1 個
- **2024-05-09 17:07:45**: [BPM APP]C01-20240508004 修正結案的流程從企業微信推播進入不會導向追蹤表單畫面問題
  - 變更檔案: 1 個
- **2024-05-09 11:29:33**: [內部]Q00-20240509001 調整重新向資料庫取SystemVariable內容發生錯誤時的log訊息
  - 變更檔案: 1 個
- **2024-05-06 16:23:29**: [MPT]Q00-20240506001 調整ecp_source_connect表ip欄位上限
  - 變更檔案: 6 個
- **2024-05-03 10:00:57**: [SYSDT]C01-20240502002 修正設計師使用權限管理中若人員的最後工作日設為未來日期時會無法顯示使用者問題
  - 變更檔案: 1 個
- **2024-05-02 16:34:00**: [SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限
  - 變更檔案: 6 個
- **2024-05-02 16:17:51**: Revert "[SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限"
  - 變更檔案: 6 個
- **2024-05-02 16:04:56**: [SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限
  - 變更檔案: 6 個
- **2024-05-02 14:09:00**: [BPM APP]C01-20240430007 調整當郵件內容為空時補上空格讓企業微信可以正常推播
  - 變更檔案: 1 個
- **2024-04-30 15:47:31**: [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制
  - 變更檔案: 1 個
- **2024-04-24 14:08:07**: [PRODT]Q00-20240424002 修正Web流程設計師中發起權限設定屬性的職務資料在編輯狀態後儲存會遺失問題
  - 變更檔案: 1 個
- **2024-08-21 14:35:27**: [行業表單庫]新增範例表單檔案
  - 變更檔案: 118 個
- **2024-08-19 15:26:08**: [BPM APP]C01-20240816007 修正頁籤元件內的頁籤代號有相似時後端取內容會發生錯亂問題
  - 變更檔案: 1 個
- **2024-08-07 17:36:56**: [BPM APP]Q00-20240807002 修正移動端已轉派列表有資料時會卡在loading畫面問題
  - 變更檔案: 1 個
- **2024-08-07 17:18:07**: [BPM APP]C01-20240807002 調整行動端的主旨支持換行符號
  - 變更檔案: 20 個
- **2024-08-07 11:35:53**: [內部]Q00-*********** 調整企業微信推播邏輯並新增錯誤log訊息
  - 變更檔案: 2 個
- **2024-07-30 17:12:00**: [BPM APP]修正行動表單的RadioButton和CheckBox元件唯讀狀態下不會顯示額外輸入框內容問題
  - 變更檔案: 3 個
- **2024-07-26 11:59:59**: [PRODT]Q00-20240710001 調整Web流程管理工具中儲存流程定義模型前增加檢核參與者機制
  - 變更檔案: 1 個
- **2024-07-10 13:41:28**: [PRODT]C01-20240614001 調整Web流程管理工具中主流程設定屬性的作為活動處理者代理人機制[補]
  - 變更檔案: 1 個
- **2024-07-09 16:03:39**: [BPM APP]C01-20240708002 修正使用自定義開窗類型時透過FormUtil.disable方法變更元件編輯狀態會發生找不到onclick事件問題
  - 變更檔案: 1 個
- **2024-07-04 16:55:32**: [BPM APP]C01-20240703004 修正企業微信值接從菜單進入到工作首頁時畫面異常問題
  - 變更檔案: 1 個
- **2024-07-02 15:41:46**: [MPT]C01-20240627004 調整MPT公告申請單中刊登時間未選擇日期前先上傳附件會導致日期欄位出現NaN問題
  - 變更檔案: 1 個
- **2024-07-01 17:26:32**: [BPM APP]C01-20240527003 修正整合企業微信在進行使用者登入返回的錯誤訊息都是undefined問題[補]
  - 變更檔案: 2 個
- **2024-06-27 08:23:04**: [PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題
  - 變更檔案: 1 個

### liuyun (10 commits)

- **2024-09-19 16:53:07**: [文件智能家] 调整localStorage英文语系值
  - 變更檔案: 1 個
- **2024-09-19 14:50:10**: [文件智能家] chatfile预览接口调整(补)
  - 變更檔案: 1 個
- **2024-09-12 17:49:59**: [Word套表] 列印模版不显示 列印人员和列印时间
  - 變更檔案: 1 個
- **2024-09-12 10:22:55**: [Word套表] 修正查询报表管理数据重复(修改流程名称场景下)
  - 變更檔案: 1 個
- **2024-09-02 11:35:32**: [Word套表] 调整部分逻辑
  - 變更檔案: 1 個
- **2024-08-28 16:57:10**: [Word套表]20240828合併版[補]
  - 變更檔案: 1 個
- **2024-06-14 17:45:10**: [Word套表]20240828合併版
  - 變更檔案: 70 個
- **2024-06-18 15:13:13**: [文件智能家]调整问答窗助阅读显示逻辑
  - 變更檔案: 5 個
- **2024-06-14 15:22:06**: [文件智能家] 长知识关联、ISO抛转记录新增字段
  - 變更檔案: 11 個
- **2024-06-11 14:45:50**: [文件智能家] 问答使用非流式
  - 變更檔案: 1 個

### davidhr-2997 (1 commits)

- **2024-09-09 14:34:10**: 更新58103patch
  - 變更檔案: 1 個

### 林致帆 (29 commits)

- **2024-06-21 16:01:45**: [Oauth]修正登入頁使用ldap驗證時，Oauth登入都會失敗
  - 變更檔案: 1 個
- **2024-06-17 11:16:48**: [T100]C01-20240605004 修正T100傳遞換行符號，Grid呈現上沒有換行效果[補修正]
  - 變更檔案: 1 個
- **2024-06-17 11:12:03**: [T100]C01-20240605004 修正T100傳遞換行符號，Grid呈現上沒有換行效果
  - 變更檔案: 1 個
- **2024-06-07 14:35:11**: [流程引擎]C01-20240603004 修正連接線條件式帶有","逗號會被系統過濾造成派送異常
  - 變更檔案: 1 個
- **2024-06-05 17:45:06**: [T100]C01-20240531008 調整表單同步增加Script防呆語法
  - 變更檔案: 1 個
- **2024-06-04 11:52:08**: [Oauth]Q00-20240604003 增加程式註解及移除不必要的程式片段
  - 變更檔案: 1 個
- **2024-06-04 11:51:30**: [EBG]]Q00-20240604002 增加程式註解及移除不必要的程式片段
  - 變更檔案: 3 個
- **2024-06-03 13:35:31**: [雙因素認證]Q00-20240603001 補上清除信任端點資訊按鈕的多語系
  - 變更檔案: 1 個
- **2024-05-30 10:58:31**: [CRM]Q00-20240530001 CRM表單轉成RWD響應式表單[補修正]
  - 變更檔案: 36 個
- **2024-05-30 10:55:44**: [CRM]Q00-20240530001 CRM表單轉成RWD響應式表單
  - 變更檔案: 18 個
- **2024-05-29 11:21:06**: [E10]Q00-20240529001 移除E10回寫服務多餘的程式內容
  - 變更檔案: 1 個
- **2024-05-28 16:08:49**: [WorkFlow]C01-20240520001 修正因ProcessMappingKey的wfRuntimeValueOID未更新造成iReport列印失敗
  - 變更檔案: 1 個
- **2024-05-28 11:04:37**: [Web]C01-20240524002 修正逾時關卡排程新增的工作轉派稽核紀錄表的狀態碼應改成為2逾時未處裡轉派
  - 變更檔案: 1 個
- **2024-05-23 16:47:04**: [EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件[補修正]
  - 變更檔案: 1 個
- **2024-05-22 16:41:01**: [EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件[補修正]
  - 變更檔案: 2 個
- **2024-05-22 16:28:52**: [流程引擎]C01-20240516004 修正TIPTOP拋單，log出現ESS的相關內容[補修正]
  - 變更檔案: 2 個
- **2024-05-21 15:17:02**: [WorkFlow]Q00-20240521001 WorkFlow表單轉成RWD響應式表單
  - 變更檔案: 178 個
- **2024-05-20 10:37:38**: [流程引擎]C01-20240516004 修正TIPTOP拋單，log出現ESS的相關內容
  - 變更檔案: 2 個
- **2024-05-17 18:05:12**: [EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件
  - 變更檔案: 5 個
- **2024-05-17 12:06:35**: [Athena]Q00-20240517001 應用Token出貨改成固定參數
  - 變更檔案: 3 個
- **2024-05-14 13:56:45**: [TIPTOP]C01-20240510004 修正流程設定授權人員，外部連結查看該流程會顯示此人員無權限訪問
  - 變更檔案: 1 個
- **2024-05-09 17:21:23**: [TIPTOP]C01-20240429008 修正回寫失敗訊息無法顯示在畫面上
  - 變更檔案: 1 個
- **2024-05-09 10:35:56**: [流程設計師]C01-20240416006 修正流程走到核決關卡後點擊待辦清單的流程會無法正常打開
  - 變更檔案: 1 個
- **2024-04-29 14:12:41**: [T100]Q00-20240429001 新增RWD響應式表單
  - 變更檔案: 427 個
- **2024-04-26 08:56:49**: [PLM]Q00-20240426001 調整PLM歷程接口返回內容
  - 變更檔案: 3 個
- **2024-04-26 08:21:34**: [Web]Q00-20240425005 修正加簽關卡選取經常對像在吳資料的狀況下會顯示錯誤頁面
  - 變更檔案: 1 個
- **2024-04-25 15:45:57**: [流程引擎]Q00-20240425003 修正核決關卡設置前置關係人造成前置關係人關卡無法繼續簽核
  - 變更檔案: 1 個
- **2024-04-25 09:09:30**: [TIPTOP]Q00-20240425001 調整TIPTOP接口呼叫封存的PORT號改成取流程主機設定
  - 變更檔案: 1 個
- **2024-04-24 11:15:43**: [TIPTOP]Q00-20240424001 修正流程封存邏輯影響到TIPTOP流程結案異常
  - 變更檔案: 1 個

### wencheng1208 (8 commits)

- **2024-08-12 11:18:06**: [流程引擎]A00-20240730001 TT向BPM溝通取得的「可撤銷清單」功能，修正出現多筆重覆資料問題。
  - 變更檔案: 1 個
- **2024-08-08 14:37:47**: [組織同步] C01-20240729002 當產品組織與中介組織的部門關聯整體資料不一致時才可增加更新記錄
  - 變更檔案: 1 個
- **2024-07-03 11:55:17**: [ESS]C01-20240701005 將人員姓名中有難字內容「𡶧」替換掉為空字串，讓XML可與ESS溝通正常
  - 變更檔案: 1 個
- **2024-06-07 11:32:31**: [流程引擎]C01-20240606005 當解析表單欄位參與者型態選用「部門主管」組織卻沒有設定該部門的主管，目前會拋出錯誤訊息告知。
  - 變更檔案: 1 個
- **2024-08-12 11:18:06**: [流程引擎]A00-20240730001 TT向BPM溝通取得的「可撤銷清單」功能，修正出現多筆重覆資料問題。
  - 變更檔案: 1 個
- **2024-08-08 14:37:47**: [組織同步] C01-20240729002 當產品組織與中介組織的部門關聯整體資料不一致時才可增加更新記錄
  - 變更檔案: 1 個
- **2024-07-03 12:01:29**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM into develop_v58
- **2024-07-03 11:55:17**: [ESS]C01-20240701005 將人員姓名中有難字內容「𡶧」替換掉為空字串，讓XML可與ESS溝通正常
  - 變更檔案: 1 個

### 邱郁晏 (32 commits)

- **2024-07-04 08:48:16**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補多語系)
  - 變更檔案: 1 個
- **2024-07-02 16:08:41**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
  - 變更檔案: 13 個
- **2024-07-02 10:17:10**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
  - 變更檔案: 3 個
- **2024-06-26 16:34:06**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能
  - 變更檔案: 1 個
- **2024-06-21 09:15:14**: [WEB] C01-20240607001 修正已結案流程，出現元素為Null錯誤，新增防呆
  - 變更檔案: 1 個
- **2024-06-18 15:17:08**: [WEB] C01-20240606009 修正表單formDispatch為false時，重複執行formSave行為
  - 變更檔案: 1 個
- **2024-06-23 03:01:36**: [HR同步] V00-20240613002 修正匯入中介表檢查職務定義邏輯異常問題
  - 變更檔案: 1 個
- **2024-06-13 16:02:20**: [在線閱讀] C01-20240613002 新增在線閱讀模組越南語系
  - 變更檔案: 3 個
- **2024-06-11 10:34:27**: [Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載(補)
  - 變更檔案: 1 個
- **2024-06-07 17:40:30**: [Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載(補)
  - 變更檔案: 1 個
- **2024-06-07 15:09:53**: [Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載
  - 變更檔案: 1 個
- **2024-05-27 09:01:28**: [雙因素模組] C01-20240523005 修正若開啟全景系統時，不應判斷jndiName，新增防呆
  - 變更檔案: 1 個
- **2024-05-27 08:51:48**: [雙因素模組] C01-20240523005 修正若無開啟全景系統時，不應判斷jndiName，新增防呆
  - 變更檔案: 1 個
- **2024-05-24 17:49:04**: [ISO] C01-20240523004 優化轉檔工具異常Log訊息
  - 變更檔案: 1 個
- **2024-05-24 16:52:06**: [Web] C01-20240521003 修正流程表單元件設置invisible時，前端表單會生成元件，導致安全性問題
  - 變更檔案: 1 個
- **2024-05-22 16:09:40**: [流程引擎] C01-20240516007 修正因核決關卡名稱導致流程派送異常問題
  - 變更檔案: 1 個
- **2024-05-21 11:43:24**: [組織同步] C01-20240517001 HR同步，新增職務定義檢查
  - 變更檔案: 1 個
- **2024-05-16 17:28:25**: [流程引擎] C01-20240508002 修正流程在併簽關卡，刪除流程時未釋放連線數問題
  - 變更檔案: 1 個
- **2024-05-16 13:39:01**: [流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法(補)
  - 變更檔案: 1 個
- **2024-05-10 09:17:16**: [流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法(補)
  - 變更檔案: 2 個
- **2024-05-09 15:09:21**: [流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法
  - 變更檔案: 29 個
- **2024-05-08 17:29:08**: [流程引擎] C01-20240508002 修正加簽關卡後，沒有釋放連線數
  - 變更檔案: 1 個
- **2024-05-08 10:24:27**: [Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log(補)
  - 變更檔案: 1 個
- **2024-05-07 18:01:08**: [Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log
  - 變更檔案: 1 個
- **2024-05-07 14:57:51**: [流程引擎] Q00-20240507001 新增單筆封存流程還原接口的防呆，若無流程封存模組則不可調用
  - 變更檔案: 2 個
- **2024-04-26 11:03:40**: [Web] Q00-20240426002 修正主管首頁待辦處理量OracleDB異常
  - 變更檔案: 1 個
- **2024-04-25 17:48:22**: [Web] Q00-20240425004 修正絕對位置表單，調整表單大小導致ScrollBar異常增加問題
  - 變更檔案: 1 個
- **2024-04-23 15:15:41**: [Web]Q00-20240423004 在觸發排程Trigger加入睡眠機制，以避免排程執行過快導致重複觸發狀況
  - 變更檔案: 1 個
- **2024-07-04 08:48:16**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補多語系)
  - 變更檔案: 1 個
- **2024-07-02 16:08:41**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
  - 變更檔案: 13 個
- **2024-07-02 10:17:10**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
  - 變更檔案: 3 個
- **2024-06-26 16:34:06**: [ISO] C01-20240625002 新增全文檢索查詢結果上限功能
  - 變更檔案: 1 個

### 刘旭 (9 commits)

- **2024-05-23 11:18:25**: [Web] C01-20240510002 處理人員收到兩封待辦通知信问题修正
  - 變更檔案: 1 個
- **2024-05-22 18:27:31**: [Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正[补]
  - 變更檔案: 4 個
- **2024-05-21 18:58:27**: [Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正[补]
  - 變更檔案: 2 個
- **2024-05-21 13:39:19**: [Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正
  - 變更檔案: 3 個
- **2024-05-11 09:50:36**: [Web] C01-20240509007 "模擬使用者"可以授權給一般使用者，會讓一般使用者可以模擬最高權限的administrator问题修正
  - 變更檔案: 1 個
- **2024-05-10 11:06:37**: [Web] C01-20240509002 系统设定中变更密码参数需要重启服务才生效问题修正
  - 變更檔案: 1 個
- **2024-04-29 17:39:16**: [Web] Q00-20240429004 報表設計器儲存報 SqlConditionList is too long问题修正
  - 變更檔案: 1 個
- **2024-04-28 14:22:18**: [Web] Q00-20240428001 開窗資料條件財產名稱輸入[PL3/雙驅動改造]，資料帶回gird，儲存草稿/儲存表單後gird資料顯示異常问题修正
  - 變更檔案: 1 個
- **2024-04-23 09:14:36**: [Web] Q00-20240423001 客户5521版到5894 將流程從XPDL轉BPMN會失敗问题修正
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]
- **Commit ID**: `a42bcdc42771e45d6b77bc4d1a46d036e9ced213`
- **作者**: lorenchang
- **日期**: 2024-10-01 10:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 2. [Web]C01-20240927004 修正58103版本流程的簽核歷程和簡易流程圖畫面沒有顯示進行中的關卡的資訊
- **Commit ID**: `b0fc576b48f9b2d318550350785c72a51cdfb349`
- **作者**: 張詠威
- **日期**: 2024-10-01 10:16:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 3. [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息[補]
- **Commit ID**: `828526623a6afa60d6e8a74666a0cb479aca7323`
- **作者**: kmin
- **日期**: 2024-10-01 10:12:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`

### 4. 更新58103patch
- **Commit ID**: `be113de501dc5bddff45f86e9090a048f2751dd6`
- **作者**: davidhr
- **日期**: 2024-09-23 16:28:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 5. [文件智能家] chatfile设定档资料新增chatfile接口授权令牌
- **Commit ID**: `21b4a379489cfd53a6b80258884333ef58ac00dc`
- **作者**: 周权
- **日期**: 2024-09-23 15:28:50
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_Oracle.sql`

### 6. [整合]新安裝T100預設表單(請購單),放錯位置,重新放到form-default
- **Commit ID**: `1debd7a2b0ac4485e0f66fd4d0702fb3af68fa01`
- **作者**: davidhr
- **日期**: 2024-09-23 13:10:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **重新命名**: `"Release/copyfiles/@t100/\350\253\213\350\263\274\345\226\256(apmt400).form"`

### 7. 【补】工作转派同步至钉钉待办中，转派取消工作通知
- **Commit ID**: `27be61c9ea952e205d9c47435d6da58c62f19d9b`
- **作者**: xiehui
- **日期**: 2024-09-13 15:27:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`

### 8. [Web表單設計師]A00-20240902001 修正pairId重覆多組導致元件消失以及代號異常
- **Commit ID**: `e85ae1e1217f65da89c55e0e9cc5eb44dae22efb`
- **作者**: kmin
- **日期**: 2024-09-20 10:50:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 9. [Web]A00-20240911001 修正TraceProcessForSearchForm接口當參數是有傳isTraceOtherProcess時導致附件權限資訊載入異常問題
- **Commit ID**: `fb7c1d81bf2a90efab55419c0349705f519d6fc7`
- **作者**: kmin
- **日期**: 2024-09-20 10:24:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 10. [派送關聯模組]C01-20240903013 修正派送流程關聯的後置流程單身資缺少問題
- **Commit ID**: `57de854343ef1134da6973c1f03675454def914b`
- **作者**: kmin
- **日期**: 2024-09-20 10:00:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`

### 11. [PRODT]Q00-20240626002 修正Web流程管理工具中關卡與連接線存在髒資料卻無提示的問題
- **Commit ID**: `da3c4d038120a5e8bd5eae27261c1c4968226887`
- **作者**: yamiyeh10
- **日期**: 2024-09-20 09:06:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 12. [整合]配合安裝程式匯入表單的內容,增加Release\copyfiles\@t100\form-default\請購單(apmt400).form Release\copyfiles\@workflow\form-default\請購單建立作業(PURI05)[GP25(PR)].form
- **Commit ID**: `95fbeb3e8fddb91ce0286772936689fa454b3e1a`
- **作者**: davidhr
- **日期**: 2024-09-19 18:11:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@t100/\350\253\213\350\263\274\345\226\256(apmt400).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI05)[GP25(PR)].form"`

### 13. [文件智能家] 调整localStorage英文语系值
- **Commit ID**: `633b3ee3959f09366218b32d1e3f12e574e9a38f`
- **作者**: liuyun
- **日期**: 2024-09-19 16:53:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 14. [文件智能家] chatfile预览接口调整(补)
- **Commit ID**: `aa92d5d8f8532d313ac39212b4beee3d218b7181`
- **作者**: liuyun
- **日期**: 2024-09-19 14:50:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 15. [流程引擎]C01-20240704001 修正系統排程AutoRegetWorkAssignmentHandler未能正常取回代理轉派的關卡[補]
- **Commit ID**: `8a480230c5622ad1fffde130f9b647a179ad1338`
- **作者**: 張詠威
- **日期**: 2024-09-19 11:31:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 16. [文件智能家] chatfile预览接口调整
- **Commit ID**: `ba6921944464c8af2f6be8211d289524da2670f7`
- **作者**: 周权
- **日期**: 2024-09-18 16:44:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 17. [Web]V00-20240918001 修正當活動關卡設定有勾選「允許輸入密碼」造成批次終止跟轉派異常
- **Commit ID**: `091b3cbe0bee49367c48f893c72e9ca5afef1039`
- **作者**: kmin
- **日期**: 2024-09-18 16:28:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 18. [Web]A00-20240916001 修正lbl_開頭的元件ID卻是InputElement元件轉型異常問題
- **Commit ID**: `4286f8848a90d1f1ea48e80574dc3371116728a2`
- **作者**: kmin
- **日期**: 2024-09-18 14:40:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 19. 更新58103patch
- **Commit ID**: `d9dbecc51ec9f28f9dd254afa5c8b499a2cff420`
- **作者**: davidhr
- **日期**: 2024-09-13 18:38:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 20. [Web]C01-20240910004 調整Restful 發單接口的form_id參數的長度上限由50改為100
- **Commit ID**: `f478986240532b7a1d62e931a661ddf3d3010139`
- **作者**: 張詠威
- **日期**: 2024-09-13 16:50:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/AbsLocalRelevantData.java`

### 21. [其他]Q00-20240913001 修正DPC轉檔工具於系統啟動時，未能清除轉檔暫存檔案
- **Commit ID**: `6c283df0c4c54607db5b5a268f406a4383c4b61a`
- **作者**: 張詠威
- **日期**: 2024-09-13 11:36:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`

### 22. [Web]C01-20240911004 修正待辦清單、追蹤流程清單，當流程主旨設定textarea元件且內容有換行時，流程主機的Title屬性未呈現換行效果
- **Commit ID**: `454e14956416bbd89f3da3a4acb5c99143759737`
- **作者**: 張詠威
- **日期**: 2024-09-13 11:28:15
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/StringUtil.js`

### 23. [流程引擎]C01-20240909004 修正呼叫WorkflowService的fetchFullProcInstanceWithSerialNo有進核決關卡時異常問題
- **Commit ID**: `1e45191657c041d220882d1984fd5b1749bc5171`
- **作者**: kmin
- **日期**: 2024-09-13 10:16:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 24. [Web] V00-20240912001 调整页面闲置过久alert提示
- **Commit ID**: `a471b4dbd711a9111cf9e200613316163ab6946f`
- **作者**: 周权
- **日期**: 2024-09-12 17:57:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 25. [Word套表] 列印模版不显示 列印人员和列印时间
- **Commit ID**: `49c3385c9d385c9d3f072dc72e60c745374333fe`
- **作者**: liuyun
- **日期**: 2024-09-12 17:49:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/impl/WordReportMappingDaoImpl.java`

### 26. [Word套表] 更新word套表多语系
- **Commit ID**: `8a5fa4d1877fc5be492594d7e4b80bac352b0538`
- **作者**: 周权
- **日期**: 2024-09-12 15:43:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 27. [Word套表] 修正查询报表管理数据重复(修改流程名称场景下)
- **Commit ID**: `dbbfc9988a716deecd1af1625e137a0beead8739`
- **作者**: liuyun
- **日期**: 2024-09-12 10:22:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`

### 28. [Word套表] 调整word套表相关sql文件
- **Commit ID**: `da81ad6e77e5f0899a677eef21b7b5ecec51e712`
- **作者**: 周权
- **日期**: 2024-09-11 14:21:27
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_Oracle.sql`

### 29. [資安]新增robots.txt檔，阻止所有搜索引擎索引站台內容
- **Commit ID**: `512810e73ffa26f8bb2f5e89382451ed1e145a94`
- **作者**: davidhr
- **日期**: 2024-09-11 14:06:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/robots.txt`

### 30. [Web]Q00-20240418001 修正资料选取器回传栏位Disable状态下，双击清空栏位仍生效的问题實際操作時沒有效果[補]
- **Commit ID**: `0a77d92669a07af307c212b7881bfbdb89fe7c53`
- **作者**: 張詠威
- **日期**: 2024-09-11 11:18:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/resources/html/CustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 31. [Word套表] 调整部分文件，ISOWatermarkPattern新增栏位
- **Commit ID**: `107e4c8a74c65eb6be5d14d7a9bdf111337f9ea1`
- **作者**: 周权
- **日期**: 2024-09-11 09:40:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`

### 32. [內部]更新Word套表授權碼及多語系[補]
- **Commit ID**: `85107a4eaf2b02d9db4453c900d463b96bd80b8b`
- **作者**: lorenchang
- **日期**: 2024-09-10 09:11:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_Oracle.sql`

### 33. 更新58103patch
- **Commit ID**: `7987d3ae971bc87f5eca7aa7647c572a1fd13b9d`
- **作者**: davidhr-2997
- **日期**: 2024-09-09 14:34:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 34. [Oauth]修正登入頁使用ldap驗證時，Oauth登入都會失敗
- **Commit ID**: `3e8417f0f2aab7caeea68bb8319e7ff651d35885`
- **作者**: 林致帆
- **日期**: 2024-06-21 16:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 35. [資安]C01-20240813004 javascript框架庫漏洞,將jquery.js內容中的版本號換成a.k.c跟a.j.c
- **Commit ID**: `26251df2ac4a2ae9e8eadf774b0f5a1e77c95adc`
- **作者**: davidhr
- **日期**: 2024-09-06 18:46:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/jquery-a.k.c.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/jquery-ui-a.j.c.custom.js`

### 36. [ESS]C01-20240902004 修正ESS流程若有退回、取回重辦至第一關時，當系統有開啟非同步簽核時，ESS觸發結案的服務任務會因為Identifier相同而導致無法回寫結案的異常
- **Commit ID**: `e208b45d9fc63523994ec77dbe9d7313b1013403`
- **作者**: 周权
- **日期**: 2024-06-07 17:07:35
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AsyncDispatchWorkItemBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`

### 37. [ESS]新增管理類作業：HR法令知識家(ESSQ96)，
- **Commit ID**: `dec45d9f5da99701bf3609081c3be8de3e3c1b1a`
- **作者**: lorenchang
- **日期**: 2024-09-05 16:53:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_Oracle.sql`

### 38. [T100]新增表單：付款條件變更申請單作業(aapt360)、收款條件變更申請單作業(axrt360)、合約維護作業(axmt480)
- **Commit ID**: `93d107e11e10d7047aadbc07850b7a65198c2ff6`
- **作者**: lorenchang
- **日期**: 2024-09-05 16:13:06
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\273\230\346\254\276\346\242\235\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256\344\275\234\346\245\255(aapt360).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\220\210\347\264\204\347\266\255\350\255\267\344\275\234\346\245\255(axmt480).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\224\266\346\254\276\346\242\235\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256\344\275\234\346\245\255(axrt360).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/\347\265\225\345\260\215\344\275\215\347\275\256\350\241\250\345\226\256/\344\273\230\346\254\276\346\242\235\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256\344\275\234\346\245\255(aapt360).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/\347\265\225\345\260\215\344\275\215\347\275\256\350\241\250\345\226\256/\345\220\210\347\264\204\347\266\255\350\255\267\344\275\234\346\245\255(axmt480).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/\347\265\225\345\260\215\344\275\215\347\275\256\350\241\250\345\226\256/\346\224\266\346\254\276\346\242\235\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256\344\275\234\346\245\255(axrt360).form"`

### 39. [Web]C01-20240903001 修正監控流程匯出Excel異常問題
- **Commit ID**: `4cfc05d5de03ffcc1fea9557a93b8cc3fd689eea`
- **作者**: kmin
- **日期**: 2024-09-04 14:16:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 40. [流程引擎]C01-20240902005 修正系統設定workitem.list.orderby為空時，點選工作通知出現異常(Server Log：如果已指定 SELECT DISTINCT，則 ORDER BY 項目必須顯示於選取清單中。)
- **Commit ID**: `a3b247103029ea7a5568867b301d8703e97db66c`
- **作者**: lorenchang
- **日期**: 2024-09-04 11:04:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`

### 41. [WEB]C01-20240830004 調整「資料選取註冊器」開窗的行動版頁面新增全選按鈕
- **Commit ID**: `2bb1e0b234d59e1b19c4d2abb4e4f1aea530bfae`
- **作者**: 張詠威
- **日期**: 2024-09-03 14:10:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 42. [Word套表] 调整部分逻辑
- **Commit ID**: `bda19861d79fe053c3b1e702a080cdfa1ad028e2`
- **作者**: liuyun
- **日期**: 2024-09-02 11:35:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/impl/WordReportMappingDaoImpl.java`

### 43. [文件智能家] 調整助閱讀附件上傳邏輯
- **Commit ID**: `cf50f075daaa43290a68664c33eac7cc4d3e566c`
- **作者**: 周权
- **日期**: 2024-08-30 17:50:25
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileToolDao.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileTransferRecordsDao.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileToolDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileTransferRecordsDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 44. [內部]更新Word套表授權碼及多語系
- **Commit ID**: `0512b310d4f274c47a8a3ac37105beceac95e195`
- **作者**: lorenchang
- **日期**: 2024-08-30 15:04:39
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`

### 45. [TIPTOP]C01-20240828005 修正流程設定授權人員，當可存取的範圍有設定部門時，透過外部連結查看時沒有參考設定的部門權限，導致作業畫面仍可開啟的異常
- **Commit ID**: `69af7b11b6ed8ed5f1a6372ad1fbf32d8f9882f7`
- **作者**: 張詠威
- **日期**: 2024-08-30 14:52:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 46. [Web]A00-20240828001 修正下拉式選項(Dropdown)-disable的狀態高度顯示異常
- **Commit ID**: `52162f9c72e4b8fbbc2900e45836c7189774e3d1`
- **作者**: kmin
- **日期**: 2024-08-29 11:51:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 47. [BPM APP]C01-20240726001 修正行動表單的RadioButton和CheckBox元件唯讀狀態下不會顯示額外輸入框內容問題[補]
- **Commit ID**: `0430f8605edc3d759485d5a3c298422750484eac`
- **作者**: yamiyeh10
- **日期**: 2024-08-29 11:00:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 48. [Web]C01-20240827002 修正當資料庫類型為 MSSQL_AZURE，使用 SQL 註冊器應用於查詢樣板，模糊查詢條件包含特殊中文字符時無法返回結果
- **Commit ID**: `dbd739cb533a1e99850f4e6aa3620c4d2160f1f1`
- **作者**: lorenchang
- **日期**: 2024-08-29 09:43:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 49. [Word套表]20240828合併版[補]
- **Commit ID**: `d6c67aa500a2a5d6f9157830884d39132f2c686a`
- **作者**: liuyun
- **日期**: 2024-08-28 16:57:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 50. [Word套表]20240828合併版
- **Commit ID**: `87b426b92bb2ec5452e78ed6b4ecb3041fbff334`
- **作者**: liuyun
- **日期**: 2024-06-14 17:45:10
- **變更檔案數量**: 70
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/lib/ZXing/core-3.5.3.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/ZXing/javase-3.5.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/WordReportMappingDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/impl/WordReportMappingDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/domain/WordReportMapping.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/vo/WorkItemForTracingVO.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/vo/WorkItemViewerVO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/server_manager/IOFileService.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/server_manager/OFileServiceImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/server_manager/ServerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/report/ReportDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/report/ReportUploadDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/persistence/AbstractPersistentObject.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/IFileService.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IMultiDocServerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IPDFHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/MultiDocServerManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/PDFHandlerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/MultiDocServerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/MultiDocServerManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/StrutsFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/IReportUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportPrint.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_Oracle.sql`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/aborted_en_us.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/aborted_vi_vn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/aborted_zh_cn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/aborted_zh_tw.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/completed_en_us.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/completed_vi_vn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/completed_zh_cn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/completed_zh_tw.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/custom/README.txt`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/inprogress_en_us.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/inprogress_vi_vn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/inprogress_zh_cn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/inprogress_zh_tw.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/paused_en_us.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/paused_vi_vn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/paused_zh_cn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/paused_zh_tw.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/terminated_en_us.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/terminated_vi_vn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/terminated_zh_cn.png`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/report/closeImages/terminated_zh_tw.png`

### 51. [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙(補)
- **Commit ID**: `e74648fd80e2c74bc2858706457ee1c8ee17f3d8`
- **作者**: lorenchang
- **日期**: 2024-08-19 16:04:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 52. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `6b506f3493413f853765f501ebfa9a859886c0e0`
- **作者**: kmin
- **日期**: 2024-08-28 08:26:56
- **變更檔案數量**: 0

### 53. [Web]C01-20240826002 修正加簽後流程圖片的分支線有重疊的狀況
- **Commit ID**: `01c1ce97735188a8e6da16f6d13341247a2379b9`
- **作者**: kmin
- **日期**: 2024-08-27 17:26:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java`

### 54. [流程引擎]C01-20240816003 優化服務任務觸發機制，降低重複執行導致狀態不一致的異常
- **Commit ID**: `f2e50c353c826110a1bb5679c67cbf12c92af1c2`
- **作者**: 張詠威
- **日期**: 2024-08-27 17:24:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java`

### 55. Merge branch 'develop_v58' of http://************/BPM_Group/BPM into develop_v58
- **Commit ID**: `6f91f01a726c9e1e6c4fef85b233454ca3005c68`
- **作者**: 張詠威
- **日期**: 2024-08-27 15:17:47
- **變更檔案數量**: 0

### 56. [資安]Q00-20240827002 修正登入頁面的UserId欄位存在SQL injection的風險
- **Commit ID**: `73101b0b3184086b043a9f0e371ee9079e193232`
- **作者**: 張詠威
- **日期**: 2024-08-27 11:43:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 57. [E10、回收謝慧功能]BPM用户设置签名图档时，同步给E10(補)
- **Commit ID**: `0dcb63f45e50d204deb3051c246516466a823e1e`
- **作者**: lorenchang
- **日期**: 2024-08-27 10:00:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_Oracle.sql`

### 58. [BPM APP、回收謝慧功能]工作转派同步至钉钉待办中
- **Commit ID**: `f01826c27b8b776c7336b23ef406ed2b324935aa`
- **作者**: xiehui
- **日期**: 2024-08-20 17:56:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 59. [E10、回收謝慧功能]BPM用户设置签名图档时，同步给E10
- **Commit ID**: `14f4f5ce2835a8e5dfc48e641e4c95f0c2d0ceb8`
- **作者**: xiehui
- **日期**: 2024-08-20 17:11:38
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/E10UserImageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/NewE10UserImageSync.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/NewE10UserImageSyncBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/IgnoreFilterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`

### 60. [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息
- **Commit ID**: `483169effc0dd5c176bb89146a7099d409fc11a9`
- **作者**: kmin
- **日期**: 2024-08-23 14:50:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`

### 61. [流程引擎]C01-20240820003 修正流程解析異常時，未啟用系統郵件通知也會觸發寄送Email給管理員
- **Commit ID**: `3595064ede8f4b4a51bc4cd54ef6ac883f058db8`
- **作者**: lorenchang
- **日期**: 2024-08-22 16:12:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 62. [Web表單設計師]C01-20240819004 修正編輯多語系顯示存入html格式與預設值不符
- **Commit ID**: `a321bddfe3c3c179f6c30c177b08947af5230b4a`
- **作者**: kmin
- **日期**: 2024-08-21 15:03:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 63. [行業表單庫]新增範例表單檔案
- **Commit ID**: `c71fac740260ce43011cbf723e210b8f767e993c`
- **作者**: yamiyeh10
- **日期**: 2024-08-21 14:35:27
- **變更檔案數量**: 118
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\270\215\350\211\257\345\223\201\346\227\245\345\240\261\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\270\215\350\211\257\345\223\201\351\200\200\345\272\253\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\272\272\345\223\241\350\252\215\350\255\211\350\246\217\347\257\204\350\250\223\347\267\264\350\252\277\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\343\200\201\345\244\226\345\214\205\345\273\240\346\224\271\345\226\204\346\216\252\346\226\275\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\345\223\201\350\263\252\344\270\215\350\211\257\350\201\257\347\265\241\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\345\223\201\350\263\252\344\277\235\350\255\211\345\210\266\345\272\246\350\251\225\351\221\221\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\345\271\264\345\272\246\347\250\275\346\240\270\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\350\251\225\351\221\221\347\250\275\346\240\270\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\350\251\225\351\221\221\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\350\263\207\346\226\231\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\200\213\344\272\272\350\252\215\350\255\211\350\250\230\351\214\204.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\215\346\240\241\346\252\242\346\270\254\350\250\255\345\202\231.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\345\223\201\350\263\252\347\250\275\346\240\270\346\237\245\346\240\270\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\345\223\201\350\263\252\347\250\275\346\240\270\347\237\257\346\255\243\346\216\252\346\226\275\347\256\241\345\210\266\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\345\223\201\350\263\252\347\250\275\346\240\270\347\274\272\351\273\236\345\240\261\345\221\212\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\345\223\201\350\263\252\347\250\275\346\240\270\350\250\210\345\212\203.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\351\200\243\347\265\241\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\206\215\350\252\215\351\200\232\347\237\245\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\210\206\347\231\274\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\215\212\346\210\220\345\223\201\345\244\226\350\247\200\346\252\242\351\251\227\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\215\224\345\212\233\345\273\240\345\225\206\345\237\272\346\234\254\350\263\207\346\226\231\350\252\277\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\215\224\345\212\233\345\273\240\345\225\206\347\222\260\345\242\203\347\256\241\347\220\206\350\246\217\345\212\203\345\225\217\345\215\267\350\252\277\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\216\237\347\211\251\346\226\231\347\222\260\345\242\203\350\200\203\351\207\217\351\235\242\351\221\221\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\216\237\347\211\251\346\226\231\351\200\262\346\226\231\346\252\242\351\251\227\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\220\210\347\264\204(\350\250\202\345\226\256)\350\256\212\346\233\264\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\345\217\257\351\235\240\345\272\246\347\233\256\346\250\231\351\240\220\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\347\225\260\345\270\270\350\201\257\347\265\241\345\226\256(\345\223\201\350\263\252\347\256\241\347\220\206).formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\347\225\260\345\270\270\350\201\257\347\265\241\345\226\256\347\256\241\345\210\266\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\347\225\260\345\270\270\351\200\232\347\237\245\345\226\256(\345\223\201\347\256\241\351\200\262\346\226\231\346\252\242\351\251\227).formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\347\233\256\346\250\231\347\256\241\345\210\266\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\241\345\267\245\345\217\227\350\250\223\346\234\215\345\213\231\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\241\345\267\245\346\225\231\350\202\262\350\250\223\347\267\264\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\241\345\267\245\350\252\215\350\255\211\347\250\213\345\272\217\345\226\256.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\345\234\213\345\244\226\345\207\272\345\267\256\350\250\274\347\205\247\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\240\261\345\273\242\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\244\226\345\214\205\345\273\240\347\270\276\346\225\210\350\251\225\346\257\224.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\244\226\351\203\250\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\255\230\350\262\250\350\252\277\346\225\264\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\344\276\206\346\250\243\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\350\262\241\347\224\242\347\256\241\345\210\266\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\351\200\201\346\250\243\347\224\263\350\253\213\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\351\221\221\345\210\245\347\264\200\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\345\257\251\346\237\245\345\240\261\345\221\212.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\345\257\251\346\237\245\351\240\205\347\233\256\346\270\205\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\267\241\346\252\242\347\274\272\345\244\261\347\237\257\346\255\243\346\216\252\346\226\275\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\267\245\347\250\213\350\251\225\344\274\260\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\267\245\347\250\213\350\256\212\346\233\264\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\267\245\347\250\213\351\203\250\345\260\210\346\241\210\351\200\262\345\272\246\351\200\261\345\240\261\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\271\264\345\272\246\346\225\231\350\202\262\350\250\223\347\267\264\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\205\247\350\243\275\344\273\244\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\344\277\235\351\244\212\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\345\247\224\345\244\226\344\277\235\351\244\212\347\266\255\344\277\256\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\345\267\245\347\250\213\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\346\224\271\345\226\204\345\260\215\347\255\226\350\251\225\344\274\260\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\347\225\260\345\270\270\346\224\271\345\226\204\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\350\250\255\345\202\231\347\256\241\347\220\206\345\215\241.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\350\250\255\346\226\275\351\234\200\346\261\202\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\210\220\345\223\201\345\205\245\345\272\253\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\210\220\345\223\201\345\207\272\350\262\250\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\211\277\350\252\215\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\217\220\346\241\210\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\225\231\350\202\262\350\250\223\347\267\264\345\277\203\345\276\227\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\207\344\273\266\344\277\256\345\273\242\347\224\263\350\253\213\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\207\344\273\266\346\224\266\347\231\274\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\260\344\272\272\346\225\231\345\255\270\345\233\236\351\245\213\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\260\347\224\242\345\223\201\350\250\255\350\250\210\351\226\213\347\231\274\347\224\263\350\253\213\350\274\270\345\205\245\345\257\251\346\237\245\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\346\226\260\351\200\262\344\272\272\345\223\241\346\225\231\350\202\262\350\250\223\347\267\264\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\251\237\345\231\250\350\250\255\345\202\231\345\256\232\346\234\237\344\277\235\351\244\212\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\251\237\345\231\250\350\250\255\345\202\231\347\256\241\347\220\206\345\270\263\345\215\241.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\346\270\254\350\250\255\345\202\231\346\240\241\351\251\227\345\240\261\345\221\212\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\346\270\254\350\250\255\345\202\231\346\240\241\351\251\227\345\271\264\345\272\246\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\346\270\254\350\250\255\345\202\231\347\256\241\347\220\206\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\346\270\254\350\250\255\345\202\231\350\263\274\345\205\245\347\224\263\350\253\213\345\226\256.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\346\270\254\350\251\246\350\250\210\347\225\253\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\272\253\345\272\246\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\272\253\346\277\225\345\272\246\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\211\251\346\226\231\350\231\225\347\275\256\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\211\271\346\216\241\345\257\251\346\237\245\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\222\260\344\277\235\346\263\225\350\246\217\351\221\221\345\256\232\347\231\273\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\222\260\345\242\203\350\200\203\351\207\217\351\235\242\347\231\273\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\222\260\345\242\203\350\200\203\351\207\217\351\235\242\351\221\221\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\222\260\345\242\203\350\210\207\350\201\267\346\245\255\345\256\211\345\205\250\350\241\233\347\224\237\347\233\256\346\250\231\347\256\241\347\220\206\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\224\237\347\224\242\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\233\264\346\216\245\344\272\272\345\223\241\350\252\215\350\255\211\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\240\224\347\231\274\345\260\210\346\241\210\350\250\230\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\347\250\275\346\240\270\347\264\200\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\347\250\275\346\240\270\350\250\210\347\225\253\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\253\213\346\241\210\345\257\251\346\240\270-\347\265\220\346\241\210\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\266\255\344\277\256\350\250\230\351\214\204\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\201\267\346\245\255\345\256\211\345\205\250\350\241\233\347\224\237\346\263\225\350\246\217\351\221\221\345\256\232\347\231\273\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\350\243\275\347\250\213\345\267\241\346\252\242\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\202\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\202\350\263\274\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\223\347\267\264\345\223\241\346\264\245\350\262\274\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\255\350\250\210\345\257\251\346\240\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\255\350\250\210\350\274\270\345\207\272\345\257\251\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\255\350\250\210\351\234\200\346\261\202\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\255\350\250\210\351\251\227\350\255\211\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\252\262\345\276\214\350\241\214\345\213\225\346\226\271\346\241\210\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\252\262\347\250\213\350\250\223\347\267\264\347\260\275\345\210\260\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\344\275\234\346\245\255\347\224\263\350\253\213\345\226\256.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\345\256\211\345\205\250\347\233\256\346\250\231\350\250\255\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\347\263\273\347\265\261\345\217\212\350\250\255\345\202\231\347\266\255\350\255\267\347\264\200\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\350\276\246\345\205\254\347\224\250\345\223\201\345\205\245\345\272\253\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\200\200\350\262\250\345\240\261\345\273\242\345\220\214\346\204\217\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\200\262\346\226\231\346\252\242\351\251\227\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\200\262\350\262\250\351\251\227\346\224\266\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\207\215\345\244\247\347\222\260\345\242\203\350\200\203\351\207\217\351\235\242\350\246\217\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\207\217\347\224\242\347\231\274\344\275\210\351\200\232\347\237\245.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\226\223\346\216\245\350\200\203\351\207\217\351\235\242\347\222\260\345\242\203\350\241\235\346\223\212\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\240\220\351\230\262\346\216\252\346\226\275\350\231\225\347\275\256\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\240\230\346\226\231\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\246\226\344\273\266\345\267\241\350\277\264\346\252\242\351\251\227\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DML_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 64. [流程引擎]C01-20240815002 修正用戶登入授權數不足時，未啟用系統郵件通知也會觸發寄送Email給管理員，改為依照啟用設定通知及增加Log記錄
- **Commit ID**: `727526713f4d8a2fa7fd8cb7293d34dc2a9e3fd7`
- **作者**: lorenchang
- **日期**: 2024-08-19 16:04:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 65. [BPM APP]C01-20240816007 修正頁籤元件內的頁籤代號有相似時後端取內容會發生錯亂問題
- **Commit ID**: `aad4036310047bc3dbd3259ff21bd46edbe788ec`
- **作者**: yamiyeh10
- **日期**: 2024-08-19 15:26:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 66. [Web]C01-20240815003 修正SQL註冊器的查詢條件開始包含跟結束包含的查詢結果相反
- **Commit ID**: `30159611a465a5b7a2271458f98080391b513286`
- **作者**: kmin
- **日期**: 2024-08-19 09:03:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`

### 67. [Web]C01-20240814001 修正增加Grid若綁定的元件是需要key：「元件ID_txt」的判斷防止必填檢查功能失效
- **Commit ID**: `8cc4d7249424680f77aa36d0b8327f2b2a401d56`
- **作者**: kmin
- **日期**: 2024-08-16 10:40:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 68. [T100]Q00-20240815001 優化T100拋單若流程第一關非流程發起者用warn訊息警告
- **Commit ID**: `4c6bbf6279b859c4647c077814cbc5db5205ace6`
- **作者**: kmin
- **日期**: 2024-08-15 16:52:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 69. [ISO]C01-20240813003 調整安裝光碟ISO文件調閱申請流程，因流程ApplyEmp關卡閘道未對稱，導致流程簽出後無法再次簽入
- **Commit ID**: `3c55d54dbbd8b3f176e6948b4c28569830526866`
- **作者**: 張詠威
- **日期**: 2024-08-14 14:52:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"Release/copyfiles/@iso/default-process/ISO\346\226\207\344\273\266\350\252\277\351\226\261\347\224\263\350\253\213.bpmn"`

### 70. [T100]Q00-20240814001 修正T100錯誤訊息判讀多語系時異常問題
- **Commit ID**: `a8e2fc14e00d6c38e8cfa0fa3c1b0772d58ee15d`
- **作者**: kmin
- **日期**: 2024-08-14 09:47:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/AbstractNewTiptopMethod.java`

### 71. [Web]C01-20240808001 修正formValidation.validateGrid邏輯異常
- **Commit ID**: `32f252df6c825712807ddff238080fc64e74cc07`
- **作者**: kmin
- **日期**: 2024-08-13 11:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 72. [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題
- **Commit ID**: `e9082d3a5e71ba57e08c3bd502752e0a9290237b`
- **作者**: kmin
- **日期**: 2024-08-12 11:48:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 73. [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題[补]
- **Commit ID**: `e6a6d375d5bf823236b63e3a220a0785bfd30282`
- **作者**: 周权
- **日期**: 2024-08-12 17:25:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 74. [流程引擎]C01-20240704001 修正系統排程AutoRegetWorkAssignmentHandler未能正常取回代理轉派的關卡
- **Commit ID**: `afd20f560521fd43b0e2446b17158097a6f8f8ec`
- **作者**: 張詠威
- **日期**: 2024-08-12 14:28:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 75. [流程引擎]A00-20240730001 TT向BPM溝通取得的「可撤銷清單」功能，修正出現多筆重覆資料問題。
- **Commit ID**: `630538920a452b42643f2077cfee52247afeac3a`
- **作者**: wencheng1208
- **日期**: 2024-08-12 11:18:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 76. [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙因素認證直接登入的異常
- **Commit ID**: `e12185996da0a62eb2c0cac5972aafe30e7aaf88`
- **作者**: lorenchang
- **日期**: 2024-08-09 17:34:25
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 77. [內部]Q00-20240808001 優化TIPTOP(WF)拋單處理附件時異常的錯誤訊息
- **Commit ID**: `1841d358217061aaa36d0a217297e13de2c62c56`
- **作者**: kmin
- **日期**: 2024-08-08 17:25:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 78. [Web]C01-20240807007 Grid 增加支持 Html 標籤 <p>
- **Commit ID**: `eb7b24dd30b00a8959789859b0979bcecf922e2c`
- **作者**: lorenchang
- **日期**: 2024-08-07 16:00:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 79. [組織同步] C01-20240729002 當產品組織與中介組織的部門關聯整體資料不一致時才可增加更新記錄
- **Commit ID**: `75b9fc20f61841bf15f344e241802836c7d97129`
- **作者**: wencheng1208
- **日期**: 2024-08-08 14:37:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`

### 80. [BPM APP]Q00-20240807002 修正移動端已轉派列表有資料時會卡在loading畫面問題
- **Commit ID**: `efb78f87e8716ffbf3578a319f459ec2cfa3b2f5`
- **作者**: yamiyeh10
- **日期**: 2024-08-07 17:36:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 81. [BPM APP]C01-20240807002 調整行動端的主旨支持換行符號
- **Commit ID**: `a2024e5d1313d32cf831e4798f53ca910bdf0416`
- **作者**: yamiyeh10
- **日期**: 2024-08-07 17:18:07
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileResigendServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 82. [Web]Q00-20240718001 修正部分流程的簽核歷程資訊未能顯示通知關卡的異常
- **Commit ID**: `79e13ca22aa2beffaaa5efcb11d47a10cdab12dd`
- **作者**: 張詠威
- **日期**: 2024-08-07 17:16:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 83. C01-20240806003 修正 XPDL 流程核決層級名稱變成「Decision Lv1.」的異常
- **Commit ID**: `75b002c589a69bb22d4be09eb76c7248a4452220`
- **作者**: lorenchang
- **日期**: 2024-08-07 16:00:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 84. [內部]Q00-*********** 調整企業微信推播邏輯並新增錯誤log訊息
- **Commit ID**: `c54da209a3a0e63248eca84d29868d8dc6a23823`
- **作者**: yamiyeh10
- **日期**: 2024-08-07 11:35:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 85. [內部]修正 Update SQL 存在非法字元，導致版更工具在特定環境執行 SQL 時出現異常
- **Commit ID**: `5c95ef2f8184414b0e22839dd02949aab1581a98`
- **作者**: lorenchang
- **日期**: 2024-08-06 14:56:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`

### 86. [流程引擎]C01-20240715002 修正 RESTful 轉存表單出現 NullPointerException 的異常
- **Commit ID**: `42160fbc9264f5bf68d2c84a91a204b3164668f3`
- **作者**: lorenchang
- **日期**: 2024-08-06 13:32:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java`

### 87. [內部]Q00-20240806001 優化BpmProcessWorkbox log訊息方便排除日常問題
- **Commit ID**: `88b373958ce980e2eeaa963d649c025c45e709d4`
- **作者**: kmin
- **日期**: 2024-08-06 10:49:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 88. [流程引擎]C01-20240801004 修正监控流程中删除流程逻辑
- **Commit ID**: `c062234764e4496cb1a8d796bd6c5063238c627b`
- **作者**: 周权
- **日期**: 2024-08-05 10:02:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 89. [Web]C01-20240731001 修正同瀏覽器先後登入兩位使用者，都會被登出的問題
- **Commit ID**: `41c3f593ea34d7410c0e2bbadb2a458c26c0004f`
- **作者**: 周权
- **日期**: 2024-08-02 15:25:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 90. [Web]C01-20240729003 修正切換頁籤Grid寬度異常問題
- **Commit ID**: `9e21f5e5e803181948f4f3f8e1b884797da068bb`
- **作者**: 周权
- **日期**: 2024-08-01 17:53:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/resources/html/RwdSubTabTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 91. [內部]Q00-20240731001 優化若角色為administrator就直接回傳true，不需要再額外做事判斷關卡是否可以列印
- **Commit ID**: `568070c74f6eb7c6d445300effa11111952dc150`
- **作者**: kmin
- **日期**: 2024-07-31 14:19:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 92. [BPM APP]修正行動表單的RadioButton和CheckBox元件唯讀狀態下不會顯示額外輸入框內容問題
- **Commit ID**: `8de52f70e11b66caa1e018f2e4a09d1b54273d4d`
- **作者**: yamiyeh10
- **日期**: 2024-07-30 17:12:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 93. [E10]C01-20240722003 修正E10拋單發起人為離職人員不該成功
- **Commit ID**: `7623c556976d0c442667e7e1b454771ae356a654`
- **作者**: kmin
- **日期**: 2024-07-29 11:14:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 94. [ESS]A00-20240726001 修正有判斷重復的在其他流程實例，就彈出提示訊息的多語系問題
- **Commit ID**: `fb9c070af08e78d3c18c9208ab1b02632b4fb0a6`
- **作者**: kmin
- **日期**: 2024-07-29 10:37:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 95. [PRODT]Q00-20240710001 調整Web流程管理工具中儲存流程定義模型前增加檢核參與者機制
- **Commit ID**: `06ad90c87b29a87476c76fc9ba9794e79317fb6f`
- **作者**: yamiyeh10
- **日期**: 2024-07-26 11:59:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 96. [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題[补]
- **Commit ID**: `3160b1dc69c0f889398ce5d47cbf4dea550c807f`
- **作者**: 周权
- **日期**: 2024-07-23 17:16:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 97. [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題
- **Commit ID**: `656fac1854205a4604ffda6fce717c5f37f225db`
- **作者**: 周权
- **日期**: 2024-07-23 11:04:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 98. [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
- **Commit ID**: `19c6bddacf3a1eafed2e4ac1111e974100f50eaa`
- **作者**: davidhr
- **日期**: 2024-07-22 11:45:16
- **變更檔案數量**: 68
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/AbsoluteFormBluePrint.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoComplete.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/OnlineReadFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseMutilPrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp`

### 99. [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
- **Commit ID**: `0b66a7f62eb823bf540e0968826296b7611ecc00`
- **作者**: davidhr
- **日期**: 2024-07-22 11:37:54
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`

### 100. [內部]Q00-20240719001 優化流程關卡轉派代理人的log機制
- **Commit ID**: `e086ddb512147e0caf83dba681354317a3218f11`
- **作者**: kmin
- **日期**: 2024-07-19 12:05:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/conf/NaNaLog.properties`

### 101. [SAP]C01-20240712005 修正SAP欄位對應設定若固定值跟說明欄位輸入小於符號呈現異常問題
- **Commit ID**: `2804e73acb3e8ece6d290b3e613cf35667f95a37`
- **作者**: kmin
- **日期**: 2024-07-18 17:03:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`

### 102. [SAP]C01-20240712005 優化SAP的log訊息，目前是遇到SapFormMapping.mappingXML欄位內容格式異常
- **Commit ID**: `dd5f7e196185f4e46155f6b5a24c51413a09813a`
- **作者**: kmin
- **日期**: 2024-07-18 15:23:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`

### 103. [SAP]C01-20240712005 優化SAP的log訊息
- **Commit ID**: `84b451d34193d8a5b5e4329128ed15e5b658f016`
- **作者**: kmin
- **日期**: 2024-07-15 14:28:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`

### 104. [Web]C01-20240711002 修正回传栏位含有“_txt”"_lbl"，若勾选双击清空栏位，表单开启报错的问题
- **Commit ID**: `63fa77aa049b7d8c4e3c4ddd4db91af956f4ae0c`
- **作者**: 周权
- **日期**: 2024-07-12 11:45:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 105. [流程引擎]C01-20240711003 修正特定情境導致工作通知內的批次閱讀出現Oops的異常
- **Commit ID**: `220834977e17986c3c12b7aa621803caf93ccd8a`
- **作者**: lorenchang
- **日期**: 2024-07-11 17:51:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 106. [PRODT]C01-20240614001 調整Web流程管理工具中主流程設定屬性的作為活動處理者代理人機制[補]
- **Commit ID**: `163ef8e2282e478909d6a0b310a45102f5b74fc7`
- **作者**: yamiyeh10
- **日期**: 2024-07-10 13:41:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 107. [BPM APP]C01-20240708002 修正使用自定義開窗類型時透過FormUtil.disable方法變更元件編輯狀態會發生找不到onclick事件問題
- **Commit ID**: `d56d3563e89a3d0e7303a2469abad9f915f5fc01`
- **作者**: yamiyeh10
- **日期**: 2024-07-09 16:03:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 108. [Web]Q00-20240702001 修正移除重覆多餘的批次終止、轉派項目 當活動關卡設定有勾選「允許輸入密碼」就會出現重覆，但實際上點了之後是無法正常運作的
- **Commit ID**: `21186855662614afea59be970d0abc8cdc1d580a`
- **作者**: kmin
- **日期**: 2024-07-08 17:39:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 109. [流程引擎]C01-20240703009 修正簡易流程預解析核決內加簽出來的關卡取回重辦後流程圖出現null訊息
- **Commit ID**: `98c396d825f9718111567e1a22e32befb1f0fefc`
- **作者**: kmin
- **日期**: 2024-07-08 11:22:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 110. [T100]C01-20240704005 修正工單完工入庫作業 (asft340) 元件異常，導致轉換為 RWD 後更改對齊方式時 Grid 及 Attachment 元件消失的問題
- **Commit ID**: `b899b54e6f1e3cc84d030415865f8087f687e56b`
- **作者**: lorenchang
- **日期**: 2024-07-05 14:51:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\344\275\234\346\245\255(asft340).form"`
  - 📝 **修改**: `"Release/copyfiles/@t100/form-default/\347\265\225\345\260\215\344\275\215\347\275\256\350\241\250\345\226\256/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\344\275\234\346\245\255(asft340).form"`

### 111. [BPM APP]C01-20240703004 修正企業微信值接從菜單進入到工作首頁時畫面異常問題
- **Commit ID**: `7f08dcca1be2790deeabf692a6724bad22038a51`
- **作者**: yamiyeh10
- **日期**: 2024-07-04 16:55:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 112. [其他]Q00-20240704001 優化預解析Log訊息加上簡易的Log
- **Commit ID**: `b4b4bbdbabdf7d9164fbff7a41ad8d93886e32ef`
- **作者**: kmin
- **日期**: 2024-07-04 16:44:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 113. [流程引擎]C01-20240701002 修正並簽流程的其中一關終止流程，並簽流程其他關卡的簽核意見會顯示取回重辦 客戶情境：並簽的關卡中做終止流程的動作，其他並簽關卡的簽核意見出問題，現在改為「已終止(UserId-UserName)」 修正程式完後並測試情境有： 1.其他並簽已簽完 2.退回重辦 3.取回重辦 4.向前加簽
- **Commit ID**: `954e1d1530b4e46e9676e737ddc38fa5b22fe304`
- **作者**: kmin
- **日期**: 2024-07-04 11:40:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 114. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補多語系)
- **Commit ID**: `0292ac06f394ec5e531f0f9d65440dd5ea350576`
- **作者**: 邱郁晏
- **日期**: 2024-07-04 08:48:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 115. [Web]C01-20240702005 列印模式下Grid顯示千分位異常
- **Commit ID**: `fd3d5712d1076864492f280338d9e149f8713787`
- **作者**: 周权
- **日期**: 2024-07-03 17:17:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 116. [Web]C01-20240702001 修正firefox下載的附件名稱前后都會有_的问题
- **Commit ID**: `10867f94844dbbf6c79852d56e2e60f1b03ef606`
- **作者**: 周权
- **日期**: 2024-07-03 11:02:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 117. [流程引擎]C01-20240625006 修正WebService退回重辦額外收到追蹤通知信問題 客戶情境：A->B->C，C用webservice退回B，A、B都會收到信，但用Web介面就只有B會收到信，沒有開退回逐級通知。 程式改完並4個情境測試結果： 1.一般關卡退一般關卡=>測試正常 2.核決退一般關卡=>測試正常 3.開逐級通知的一般關卡退一般關卡=>測試正常 4.開逐級通知的核決退一般關卡=>測試正常
- **Commit ID**: `e92933d38cb2c03820e9fc8e0064661080de5aaf`
- **作者**: kmin
- **日期**: 2024-07-03 10:56:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 118. [ESS]C01-20240701005 將人員姓名中有難字內容「𡶧」替換掉為空字串，讓XML可與ESS溝通正常
- **Commit ID**: `bc7654f6cb3753e29262089edfcc69db2f102278`
- **作者**: wencheng1208
- **日期**: 2024-07-03 11:55:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 119. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
- **Commit ID**: `2415cce1acd66e3276605496212bf6b0fa48918e`
- **作者**: 邱郁晏
- **日期**: 2024-07-02 16:08:41
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ISOManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/listreader/ISOListHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/listreader/ISOListHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IISOListHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ISOListHandlerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/standalone/configuration/standalone-full.xml`

### 120. [MPT]C01-20240627004 調整MPT公告申請單中刊登時間未選擇日期前先上傳附件會導致日期欄位出現NaN問題
- **Commit ID**: `2607d9e23a1cd0c1cdb82d975002b078b2bec2cd`
- **作者**: yamiyeh10
- **日期**: 2024-07-02 15:41:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@mpt/default-form/MptAncApply.form`

### 121. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
- **Commit ID**: `4b07b3923ab33400dc3ecf9de15dac3a59058749`
- **作者**: 邱郁晏
- **日期**: 2024-07-02 10:17:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `Release/db/update/5.8.10.3_DDL_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DDL_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`

### 122. [BPM APP]C01-20240527003 修正整合企業微信在進行使用者登入返回的錯誤訊息都是undefined問題[補]
- **Commit ID**: `6297da10df2f13c54996898b20997601248ed402`
- **作者**: yamiyeh10
- **日期**: 2024-07-01 17:26:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`

### 123. [Web]C01-20240628004 修正“/NaNaWeb/GP/ForwardIndex”會導頁到錯誤頁面
- **Commit ID**: `a4d69a0f503171937c5fb90efb40aa5fb34e3e01`
- **作者**: 周权
- **日期**: 2024-07-01 14:35:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/action/ActionServlet.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ExceptionCatcher.java`

### 124. [Web]C01-20240626002 修正列印模式下Grid無法正常顯示千分位的问题
- **Commit ID**: `687315d8c812ede84891a6e62ba5aa2bbdfb04f9`
- **作者**: 周权
- **日期**: 2024-06-27 11:35:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 125. [PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題
- **Commit ID**: `0d096e541b705ca600ea4bfc566e9df621e62528`
- **作者**: yamiyeh10
- **日期**: 2024-06-27 08:23:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 126. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能
- **Commit ID**: `ca94c23b76fbf70893b53d7c16716120a5a83513`
- **作者**: 邱郁晏
- **日期**: 2024-06-26 16:34:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 127. [內部]更新58102patch
- **Commit ID**: `68e4f980d2c23b85bf334dfc74c4777c20082f6e`
- **作者**: lorenchang
- **日期**: 2024-06-25 16:34:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 128. [文件智能家]修正多語系：文件智能家應用管理改為文件智能家模組
- **Commit ID**: `0d5316759c3d74b9dce054df32c100c9a22b9ba4`
- **作者**: lorenchang
- **日期**: 2024-06-25 16:05:28
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_Oracle.sql`

### 129. [文件智能家]修正因流程主機位址不是127.0.0.1或localhost導致取得AccessToken失敗，間接導致不會觸發ChatFile接口
- **Commit ID**: `9f5f2cb8ce1660abbc86a9a9b5afe29a730c8c2a`
- **作者**: lorenchang
- **日期**: 2024-06-25 15:44:52
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/SystemInfoUtils.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/NaNaXWebHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 130. [文件智能家]修正DB為Oracle時，取得最近5筆歷史問答紀錄因為欄位reponseData為Clob無法使用查詢EQUAL的查詢條件，改為EXACT_NOT_LIKE
- **Commit ID**: `4c3c8da5514b88b10e3d1e4779d435449f33926b`
- **作者**: lorenchang
- **日期**: 2024-06-25 15:36:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryOperator.java`

### 131. [Web]C01-20240620009 判斷URL是否符合產品的模組URL新增防呆
- **Commit ID**: `c7baa022341b901fb24afc375537b9ee4efded8c`
- **作者**: 周权
- **日期**: 2024-06-25 10:56:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 132. [文件智能家]修正使用首頁模組進入的表單畫面中的附件圖示打開後不會顯示助閱讀
- **Commit ID**: `e65a3002853c8e0fdfe6bec113ac96b2ef01aabc`
- **作者**: lorenchang
- **日期**: 2024-06-24 15:40:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 133. [文件智能家]修正附件圖示無法每次都正常顯示的異常
- **Commit ID**: `215822b903f6ce3dc8c140e140ec1251d9e768b9`
- **作者**: lorenchang
- **日期**: 2024-06-24 13:51:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`

### 134. [Web]C01-20240620007 修正點簽核狀態報錯
- **Commit ID**: `6a59137e26dcf01f7ac0be243e4b958ca2f8be4e`
- **作者**: 周权
- **日期**: 2024-06-24 11:12:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/AuthorityHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java`

### 135. [WEB] C01-20240607001 修正已結案流程，出現元素為Null錯誤，新增防呆
- **Commit ID**: `72f20f7b74906d86953671e51dc1a3bfc6facaca`
- **作者**: 邱郁晏
- **日期**: 2024-06-21 09:15:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 136. [文件智能家]更新表單流程及多語系
- **Commit ID**: `b25c4b0278e9cdb00ea0f448aaa93da169e1db48`
- **作者**: lorenchang
- **日期**: 2024-06-20 19:53:49
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISOMod001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001Manager.form`
  - 📝 **修改**: `"Release/copyfiles/@iso/default-process/\346\226\207\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 137. [行業表單庫]V00-20240620001 增加卡控使用的表單代號必須為英文字母開頭，並且由英文字母、數字或底線組成[補]
- **Commit ID**: `255d7828281d87876ed1562e1f2e9af74737f9d5`
- **作者**: yamiyeh10
- **日期**: 2024-06-20 11:23:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 138. [SYSDT]Q00-20240605001 調整系統管理工具中測試系統郵件後的提示訊息
- **Commit ID**: `8e21101b5f8b520dd377f90f881276f089ffa233`
- **作者**: yamiyeh10
- **日期**: 2024-06-19 10:20:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 139. [WEB] C01-20240606009 修正表單formDispatch為false時，重複執行formSave行為
- **Commit ID**: `3506de9f90e3394658d39b045447eacc7ad32821`
- **作者**: 邱郁晏
- **日期**: 2024-06-18 15:17:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 140. [文件智能家]调整问答窗助阅读显示逻辑
- **Commit ID**: `33ab73c632f2afa727d0cda77ac24ca1fea70572`
- **作者**: liuyun
- **日期**: 2024-06-18 15:13:13
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileToolDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 141. [內部]更新58102patch
- **Commit ID**: `f8f33c4b2502863a1a046b2d5a7aa7c0e5d9307a`
- **作者**: davidhr
- **日期**: 2024-06-18 10:37:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 142. [HR同步] V00-20240613002 修正匯入中介表檢查職務定義邏輯異常問題
- **Commit ID**: `b670a3951f35e6168380091568f94c31f5f6b2ac`
- **作者**: 邱郁晏
- **日期**: 2024-06-23 03:01:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 143. [ISO] V00-20240612011 修正文管首頁調閱後進文件調閱申請後session未清除問題 再從發起流程入口進入後，因wms_manageDocument_documentOID未清除，而預設代入文件編號。
- **Commit ID**: `522313b8e4afdbf4bf238a59cbf9e028e3711546`
- **作者**: kmin
- **日期**: 2024-06-17 11:41:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 144. [T100]C01-20240605004 修正T100傳遞換行符號，Grid呈現上沒有換行效果[補修正]
- **Commit ID**: `861551dad0c100aa1b9ee5e2e88eb1d2dac22582`
- **作者**: 林致帆
- **日期**: 2024-06-17 11:16:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 145. [T100]C01-20240605004 修正T100傳遞換行符號，Grid呈現上沒有換行效果
- **Commit ID**: `f3d0c683898a9ff7d6a8018135fd7c6a45c530dc`
- **作者**: 林致帆
- **日期**: 2024-06-17 11:12:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 146. [Web]C01-20240613007 修正外部連結登入BPM，若密碼輸入錯誤再重新登入BPM後，會呈現首頁的畫面的问题
- **Commit ID**: `1f05fd9cf9196a5670b48d4b2b91f1aa6f3435f3`
- **作者**: 周权
- **日期**: 2024-06-17 10:44:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 147. [文件智能家]更新表單及多語系
- **Commit ID**: `fd157540fe608a6af163829d8f3e55021f60d385`
- **作者**: lorenchang
- **日期**: 2024-06-14 17:57:41
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISOMod001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001Manager.form`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 148. [内部]V00-20240612012 修正終止流程按取消會跳下一張單據，不會停在原單據
- **Commit ID**: `9a614eb38915d73e05aa886c3a8e25a8d5e8d56c`
- **作者**: 周权
- **日期**: 2024-06-14 15:56:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 149. [文件智能家] 长知识关联、ISO抛转记录新增字段
- **Commit ID**: `310085d99a179e50688dcc854ceed4ec1b8f62c2`
- **作者**: liuyun
- **日期**: 2024-06-14 15:22:06
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileISOTransferRecordsDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileISOTransferRecords.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileKnowledge.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 150. [流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗[補]
- **Commit ID**: `744bc1f6d3da486cfb4adb11da2c26b2afb7bcc7`
- **作者**: lorenchang
- **日期**: 2024-06-14 11:33:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 151. [在線閱讀] C01-20240613002 新增在線閱讀模組越南語系
- **Commit ID**: `b96697b6ed81d6228c57815c49dd12dd1677e311`
- **作者**: 邱郁晏
- **日期**: 2024-06-13 16:02:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_Oracle.sql`

### 152. [文件智能家]更新多語系
- **Commit ID**: `a1427b17dbb06fb35258538d207bea56b3843c39`
- **作者**: lorenchang
- **日期**: 2024-06-12 17:12:45
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 153. [文件智能家]增加卡控：長知識關聯作業有ISO授權才會顯示
- **Commit ID**: `89609f6df131359dc9e172a210fd65dd207113ad`
- **作者**: lorenchang
- **日期**: 2024-06-12 17:11:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

### 154. [BPM APP]C01-20240611001 修正行動簽核管理中心的釘釘待辦同步頁面無法開窗選擇流程名稱與處理者問題
- **Commit ID**: `509bdd7faa810486d7500fd3f155312d6437c62c`
- **作者**: yamiyeh10
- **日期**: 2024-06-11 17:18:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp`

### 155. [文件智能家] 问答使用非流式
- **Commit ID**: `f5c6ace3e9c21adc298e75060b3c98e4e486e045`
- **作者**: liuyun
- **日期**: 2024-06-11 14:45:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 156. [Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載(補)
- **Commit ID**: `81791f7c5b9279593273d14be4622f2c060dbaef`
- **作者**: 邱郁晏
- **日期**: 2024-06-11 10:34:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 157. [Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載(補)
- **Commit ID**: `5a609acfb892364e422587f8db1e481bbf931e8e`
- **作者**: 邱郁晏
- **日期**: 2024-06-07 17:40:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 158. [文件智能家]修正上傳檔案到ChatFile失敗的異常
- **Commit ID**: `cd916b9c69d17ae27a2bd19f095b31f97f73442d`
- **作者**: lorenchang
- **日期**: 2024-06-07 17:31:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 159. [表單設計師] C01-20240605002 修正絕對位置表單的label以及TextBox的label轉換為RWD表單，底色沒有跟著過去
- **Commit ID**: `676962f95d6619dc5d316fec03d7cbe27a3382ea`
- **作者**: kmin
- **日期**: 2024-06-07 16:39:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`

### 160. [流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗[補]
- **Commit ID**: `8c179bb3fc7b34e30fbd6a44744cc01de86e83f9`
- **作者**: 周权
- **日期**: 2024-06-07 17:10:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 161. [Web]C01-20240522002 修正系统权限管理员[可存取的範圍]设定无效的问题
- **Commit ID**: `1fd79092ef8b9c1990b3e906395da16228f8f38e`
- **作者**: 周权
- **日期**: 2024-06-07 17:07:35
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/AuthorityHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java`

### 162. [Web] C01-20240529008 修正Safari瀏覽器因檔名過長導致無法下載附件，調整檔名固定為日期時間下載
- **Commit ID**: `0b40bf2ef3c904c06854b2b2f1ed9da08f760a6d`
- **作者**: 邱郁晏
- **日期**: 2024-06-07 15:09:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 163. [流程引擎]C01-20240603004 修正連接線條件式帶有","逗號會被系統過濾造成派送異常
- **Commit ID**: `ab2f175b95fdf6c3a30c31a0fb56b462a959b4da`
- **作者**: 林致帆
- **日期**: 2024-06-07 14:35:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/util/ConditionEvaluator.java`

### 164. [文件智能家]修正ISO表單相關元件名稱
- **Commit ID**: `f8706fe6b52ea1ed4adbcfb7f94551f44332191e`
- **作者**: lorenchang
- **日期**: 2024-06-07 14:22:23
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISOMod001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001Manager.form`

### 165. [流程引擎]C01-20240606005 當解析表單欄位參與者型態選用「部門主管」組織卻沒有設定該部門的主管，目前會拋出錯誤訊息告知。
- **Commit ID**: `4ce2487f0f62ec4c681af87b634dbae8c8d1842c`
- **作者**: wencheng1208
- **日期**: 2024-06-07 11:32:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 166. [內部]更新Patch及Create SQL
- **Commit ID**: `f8bd249847c9f4892fec6b7e9d43faf3e536fde8`
- **作者**: lorenchang
- **日期**: 2024-06-06 18:03:49
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`

### 167. [行業表單庫]新增範例表單檔案
- **Commit ID**: `3eb1fd700beb8258c1be3342d68a6b00b60bf30b`
- **作者**: yamiyeh10
- **日期**: 2024-06-06 16:48:44
- **變更檔案數量**: 42
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\270\215\345\220\210\346\240\274\345\223\201\345\215\241.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\350\251\225\351\221\221\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\347\250\275\346\240\270\346\237\245\346\252\242\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\346\252\242\351\251\227\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\345\233\236\351\245\213\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\346\212\200\350\241\223\350\263\207\346\226\231\347\247\273\350\275\211\346\234\203\350\255\260.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\257\251\346\237\245\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\257\251\346\237\245\351\240\205\347\233\256\346\270\205\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\224\266\346\226\207\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\231\350\231\237\347\224\263\350\253\213\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\260\344\276\233\346\207\211\345\225\206\350\251\225\344\274\260\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\260\347\211\251\346\226\231\351\234\200\346\261\202\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\351\251\227\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\270\254\350\251\246\350\250\210\347\225\253\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\204\241\347\267\232\347\266\262\350\267\257\345\270\263\350\231\237\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\224\242\345\223\201\350\250\255\350\250\210\350\246\217\346\240\274\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\227\233\345\277\253\346\234\215\345\213\231\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\250\213\345\274\217\347\225\260\345\270\270\345\217\215\346\207\211\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\250\275\346\240\270\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\250\275\346\240\270\350\250\210\347\225\253\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\256\241\345\210\266\345\215\200\345\237\237\351\200\262\345\207\272\347\256\241\345\210\266\347\231\273\350\250\230.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\263\273\347\265\261\344\270\273\346\251\237\345\256\211\345\205\250\346\252\242\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\263\273\347\265\261\346\254\212\351\231\220\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\263\273\347\265\261\351\226\213\347\231\274\344\270\212\347\267\232\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\266\255\344\277\256\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\266\255\344\277\256\350\231\225\347\220\206\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\274\272\345\244\261\346\224\271\345\226\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\243\275\347\250\213\345\267\241\346\252\242\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\251\246\347\224\242\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\251\246\351\207\217\347\224\242\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\256\212\346\233\264\350\250\230\351\214\204\351\200\232\347\237\245\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\256\212\346\233\264\351\234\200\346\261\202\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\345\256\211\347\256\241\347\220\206\345\257\251\346\237\245\346\234\203\350\255\260\347\264\200\351\214\204.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\344\275\234\346\245\255\347\225\260\345\270\270\350\231\225\347\220\206\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\345\256\211\345\205\250\344\272\213\344\273\266\345\240\261\345\221\212\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\345\256\211\345\205\250\347\233\256\346\250\231\350\250\255\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\350\263\207\347\224\242\346\224\234\345\270\266\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\273\237\351\253\224\345\256\211\350\243\235\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\206\253\347\231\202\345\231\250\346\235\220\344\270\215\350\211\257\344\272\213\344\273\266\351\200\232\345\240\261\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\226\213\346\241\210\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\230\262\347\201\253\347\211\206\351\200\243\347\267\232\346\234\215\345\213\231\347\225\260\345\213\225\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\246\226\344\273\266\346\252\242\346\237\245\347\264\200\351\214\204\350\241\250.formrepository"`

### 168. [內部]更新58102patch
- **Commit ID**: `6538e366cf3a77c1cc381131369e116b3b7ea4f8`
- **作者**: davidhr
- **日期**: 2024-06-06 11:07:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 169. [文件智能家]新增模組
- **Commit ID**: `79ec1535e76140bf2ad11ae7d61dc5b360ca3f8b`
- **作者**: lorenchang
- **日期**: 2024-03-25 16:33:46
- **變更檔案數量**: 65
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileAssistedReadingDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileISOTransferRecordsDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileKnowledgeCategoryDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileKnowledgeDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFilePropertiesDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileQARecordDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileToolDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileTransferRecordsDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileUserManagementDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileUserTokenDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileISOTransferRecordsDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileKnowledgeCategoryDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileKnowledgeDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFilePropertiesDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileQARecordDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileToolDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileTransferRecordsDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileUserManagementDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileUserTokenDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileAssistedReading.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileISOFile.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileISOTransferRecords.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileKnowledge.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileKnowledgeCategory.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileProperties.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileQARecord.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileTransferRecords.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileUserManagement.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileUserToken.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/service/ChatFileCommonService.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileCommonServiceImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/PagingList.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryCondition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryOperator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/SortCondition.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/SortOrder.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/ValueType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/response/PlatformApiResponse.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/DateUtils.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/IFileService.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/NaNaXWebHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/chat-label.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/chat-logo.svg`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISOMod001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001Manager.form`
  - 📝 **修改**: `"Release/copyfiles/@iso/default-process/\346\226\207\344\273\266\346\226\260\345\242\236\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"Release/copyfiles/@iso/default-process/\346\226\207\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"Release/copyfiles/@iso/default-process/\346\226\207\347\256\241\346\226\260\345\242\236\346\226\207\344\273\266.bpmn"`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 170. [T100]C01-20240531008 調整表單同步增加Script防呆語法
- **Commit ID**: `d8304c83e7f8cc039b590f4e8237f9347dc147cd`
- **作者**: 林致帆
- **日期**: 2024-06-05 17:45:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopFormTransfer.java`

### 171. [ORGDT]C01-20240517013 調整Web化設計工具在打開後隔一段時間會發生操作錯誤問題
- **Commit ID**: `bf3256bbfca0971951ede28cbee6267edf42c476`
- **作者**: yamiyeh10
- **日期**: 2024-06-05 17:25:04
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java`

### 172. [流程封存]C01-20240527002 修正取系統參數失敗導致流程封存失敗
- **Commit ID**: `c5b8fa42a502b90b2caa2b0e100a81a64d3deb0f`
- **作者**: 周权
- **日期**: 2024-06-05 15:15:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 173. [PRODT]C01-20240603003 修正流程實例中的詳細流程圖中活動關卡與連接線消失問題
- **Commit ID**: `fc66c74db21c3ba3f69b451d2eeaf97cd427e271`
- **作者**: yamiyeh10
- **日期**: 2024-06-04 14:15:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpmn-js/custom-bpmn-navigated-viewer.production.min.js`

### 174. [Oauth]Q00-20240604003 增加程式註解及移除不必要的程式片段
- **Commit ID**: `aafeaf37644034aa0f8b002018173dc7bf9a8652`
- **作者**: 林致帆
- **日期**: 2024-06-04 11:52:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthSettingManagerBean.java`

### 175. [EBG]]Q00-20240604002 增加程式註解及移除不必要的程式片段
- **Commit ID**: `0c77ac3ef45efb6565ec6ac46ae83641b84439c8`
- **作者**: 林致帆
- **日期**: 2024-06-04 11:51:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/ebgModule/EBGHistoricalSigner.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/EBGAccessor.java`

### 176. [Web] C01-202410603001 檢核表單元件資料型態與DB欄位型態是否一致
- **Commit ID**: `8a9143eb32e8a3b107c52443b6af762e36b04d06`
- **作者**: kmin
- **日期**: 2024-06-04 11:16:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 177. [雙因素認證]Q00-20240603001 補上清除信任端點資訊按鈕的多語系
- **Commit ID**: `43415647891d4759e94cee29bef780f6b7a25535`
- **作者**: 林致帆
- **日期**: 2024-06-03 13:35:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 178. [Web]C01-20240531001 修正列印表單報錯 _self.table.bootstrapTable is not a function
- **Commit ID**: `fa1f483fbc27dc23231e6c7bbaaf4e37be2023cb`
- **作者**: 周权
- **日期**: 2024-06-03 09:00:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 179. [BPM APP]C01-20240527003 修正整合企業微信在進行使用者登入返回的錯誤訊息都是undefined問題
- **Commit ID**: `7c41b6040bef94f88388515df61b241ba005cf76`
- **作者**: yamiyeh10
- **日期**: 2024-05-30 17:05:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 180. [CRM]Q00-20240530001 CRM表單轉成RWD響應式表單[補修正]
- **Commit ID**: `423d30a7423b191ad843e7d080f08f65fa4785dd`
- **作者**: 林致帆
- **日期**: 2024-05-30 10:58:31
- **變更檔案數量**: 36
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\344\274\260\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\217\253\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\240\261\345\203\271\350\256\212\346\233\264\345\226\256.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\256\211\350\243\235\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\256\211\350\243\235\351\251\227\346\224\266\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\256\242\346\210\266\344\277\235\351\244\212\347\264\200\351\214\204\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\256\242\346\210\266\345\220\210\347\264\204\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\256\242\346\210\266\346\234\215\345\213\231\347\231\273\351\214\204\350\231\225\347\220\206\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\345\267\245\344\275\234\350\250\230\351\214\204.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\346\250\243\345\223\201\347\224\263\350\253\213\345\226\256.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\347\266\255\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\350\262\273\347\224\250\347\224\263\350\253\213\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\351\200\201\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\351\200\201\345\216\237\345\273\240\347\266\255\344\277\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\351\200\201\345\273\240\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/absolute-form/\351\212\267\345\224\256\346\251\237\346\234\203\347\266\255\350\255\267\344\275\234\346\245\255.form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\344\274\260\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(REPI30).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\217\253\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(REPI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(CTRI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(SALI30).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\240\261\345\203\271\350\256\212\346\233\264\345\226\256(SALI59).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\256\211\350\243\235\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(REPI37).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\256\211\350\243\235\351\251\227\346\224\266\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(REPI38).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\256\242\346\210\266\344\277\235\351\244\212\347\264\200\351\214\204\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(REPI23).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\256\242\346\210\266\345\220\210\347\264\204\345\273\272\347\253\213\344\275\234\346\245\255(CTRI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\256\242\346\210\266\346\234\215\345\213\231\347\231\273\351\214\204\350\231\225\347\220\206\344\275\234\346\245\255(SERI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\345\267\245\344\275\234\350\250\230\351\214\204(SALI21).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\346\250\243\345\223\201\347\224\263\350\253\213\345\226\256(SALI54).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\347\266\255\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(REPI13).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\350\262\273\347\224\250\347\224\263\350\253\213\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PORI51).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\351\200\201\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(REPI17).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\351\200\201\345\216\237\345\273\240\347\266\255\344\277\256\345\273\272\347\253\213\344\275\234\346\245\255(REPI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\351\200\201\345\273\240\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(REPI15).form"`
  - ➕ **新增**: `"Release/copyfiles/@crm/form-default/rwd-form/\351\212\267\345\224\256\346\251\237\346\234\203\347\266\255\350\255\267\344\275\234\346\245\255(SALI17).form"`

### 181. [CRM]Q00-20240530001 CRM表單轉成RWD響應式表單
- **Commit ID**: `d3895e3b3e8a7312df755bd9929dbd6ccaab5374`
- **作者**: 林致帆
- **日期**: 2024-05-30 10:55:44
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\344\274\260\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\217\253\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\240\261\345\203\271\350\256\212\346\233\264\345\226\256.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\256\211\350\243\235\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\256\211\350\243\235\351\251\227\346\224\266\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\256\242\346\210\266\344\277\235\351\244\212\347\264\200\351\214\204\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\256\242\346\210\266\345\220\210\347\264\204\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\256\242\346\210\266\346\234\215\345\213\231\347\231\273\351\214\204\350\231\225\347\220\206\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\345\267\245\344\275\234\350\250\230\351\214\204.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\346\250\243\345\223\201\347\224\263\350\253\213\345\226\256.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\347\266\255\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\350\262\273\347\224\250\347\224\263\350\253\213\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\351\200\201\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\351\200\201\345\216\237\345\273\240\347\266\255\344\277\256\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\351\200\201\345\273\240\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255.form"`
  - ❌ **刪除**: `"Release/copyfiles/@crm/form-default/\351\212\267\345\224\256\346\251\237\346\234\203\347\266\255\350\255\267\344\275\234\346\245\255.form"`

### 182. [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
- **Commit ID**: `152e62211761618ec1c3b3651fcfebc79bc0fdbc`
- **作者**: yamiyeh10
- **日期**: 2024-05-29 16:47:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 183. [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息
- **Commit ID**: `7c3fd7ced4824c628af7bf7e7006b98a06207457`
- **作者**: yamiyeh10
- **日期**: 2024-05-29 16:09:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 184. [E10]Q00-20240529001 移除E10回寫服務多餘的程式內容
- **Commit ID**: `e2a06cf6187d27b01e7b177d3a98e8dc4c64edcd`
- **作者**: 林致帆
- **日期**: 2024-05-29 11:21:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 185. [Web]C01-20240528001 修正Grid无资料时栏位宽度异常的问题
- **Commit ID**: `aacf118a56adab25ab44562a21fab97b450079fa`
- **作者**: 周权
- **日期**: 2024-05-29 09:04:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 186. [WorkFlow]C01-20240520001 修正因ProcessMappingKey的wfRuntimeValueOID未更新造成iReport列印失敗
- **Commit ID**: `be7f98de70b25974cb0c9b01305a6bbd955ca757`
- **作者**: 林致帆
- **日期**: 2024-05-28 16:08:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 187. [Web]C01-20240524002 修正逾時關卡排程新增的工作轉派稽核紀錄表的狀態碼應改成為2逾時未處裡轉派
- **Commit ID**: `7c66f38c524939c27afd8e99d43a426d276f5fe6`
- **作者**: 林致帆
- **日期**: 2024-05-28 11:04:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 188. [雙因素模組] C01-20240523005 修正若開啟全景系統時，不應判斷jndiName，新增防呆
- **Commit ID**: `bc6aa431c24282009af31fd63a9837052261aa4a`
- **作者**: 邱郁晏
- **日期**: 2024-05-27 09:01:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 189. [雙因素模組] C01-20240523005 修正若無開啟全景系統時，不應判斷jndiName，新增防呆
- **Commit ID**: `afeb22e05e368b971b701481b08a5643c71a8e60`
- **作者**: 邱郁晏
- **日期**: 2024-05-27 08:51:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 190. [ISO] C01-20240523004 優化轉檔工具異常Log訊息
- **Commit ID**: `85a67dad4492c3fdcb29550cf8100b2ce80c4ed3`
- **作者**: 邱郁晏
- **日期**: 2024-05-24 17:49:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`

### 191. [Web] C01-20240521003 修正流程表單元件設置invisible時，前端表單會生成元件，導致安全性問題
- **Commit ID**: `5126799f2c6dff7fb5534ffc25f8c5464af73b22`
- **作者**: 邱郁晏
- **日期**: 2024-05-24 16:52:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 192. [EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件[補修正]
- **Commit ID**: `8c74b636184957dfb5a78d9c4b53b20b3e6513c8`
- **作者**: 林致帆
- **日期**: 2024-05-23 16:47:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/EBGModule/EBGCreateForm.js`

### 193. [Web] C01-20240510002 處理人員收到兩封待辦通知信问题修正
- **Commit ID**: `2d5dac09682ead425bbb1313cdad5b5173f2deca`
- **作者**: 刘旭
- **日期**: 2024-05-23 11:18:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 194. [Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正[补]
- **Commit ID**: `abdad31b4c469123ad3573abfbd2ad411e1bf364`
- **作者**: 刘旭
- **日期**: 2024-05-22 18:27:31
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 195. [EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件[補修正]
- **Commit ID**: `757c17eb090f57899a92d6ec2f4d5ecaa2ab8b2b`
- **作者**: 林致帆
- **日期**: 2024-05-22 16:41:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`

### 196. [流程引擎]C01-20240516004 修正TIPTOP拋單，log出現ESS的相關內容[補修正]
- **Commit ID**: `3347d3135c4560d86a065fd24757db86c866e960`
- **作者**: 林致帆
- **日期**: 2024-05-22 16:28:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 197. [流程引擎] C01-20240516007 修正因核決關卡名稱導致流程派送異常問題
- **Commit ID**: `3e3c818372dc14f60a0354e87216a5f29a563128`
- **作者**: 邱郁晏
- **日期**: 2024-05-22 16:09:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 198. [Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正[补]
- **Commit ID**: `af26ff6fd9c4c07279bb45ae5d95d5ccc067a207`
- **作者**: 刘旭
- **日期**: 2024-05-21 18:58:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/NanaDsUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 199. [Web]C01-20240516002 修正流程設定授權人員，外部連結查看表單資訊會顯示此人員無權限訪問
- **Commit ID**: `7b78b5b858d3917d1788a12cececd7cb1b29a397`
- **作者**: 周权
- **日期**: 2024-05-21 17:59:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 200. [WorkFlow]Q00-20240521001 WorkFlow表單轉成RWD響應式表單
- **Commit ID**: `c77d944f7bf92c6c081ad5a58fd1b021dfac0871`
- **作者**: 林致帆
- **日期**: 2024-05-21 15:17:02
- **變更檔案數量**: 178
- **檔案變更詳細**:
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/APS\346\216\241\350\263\274\350\252\277\346\225\264\345\273\272\350\255\260\344\275\234\346\245\255(APSI11).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/APS\350\243\275\344\273\244\350\243\275\347\250\213\350\252\277\346\225\264\345\273\272\350\255\260\344\275\234\346\245\255(APSI13).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/APS\350\243\275\344\273\244\350\252\277\346\225\264\345\273\272\350\255\260\344\275\234\346\245\255(APSI10).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/APS\350\250\202\345\226\256\350\252\277\346\225\264\345\273\272\350\255\260\344\275\234\346\245\255(APSI09).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/BOM \350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI04).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/BOM\347\224\250\351\207\217\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(BOMI02).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/E-BOM\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI12).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/PACKING LIST \345\273\272\347\253\213\344\275\234\346\245\255(EPSI06).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/PACKING LIST\345\273\272\347\253\213\344\275\234\346\245\255(IDLI43).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/SI\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IPSI04).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/WAFER BANK\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IDLI11).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/WAFER \350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IDLI15).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\344\273\230\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACPI03).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\207\272\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(BCSI17).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\223\201\351\201\213\345\233\236\351\200\262\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(BCSI18).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\344\277\235\347\250\205\346\251\237\345\231\250\350\250\255\345\202\231\351\200\262\345\207\272\345\217\243\347\225\260\345\213\225\345\273\272\347\253\213\344\275\234\346\245\255(BCHI14).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(BCHI08).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(BCSI05).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\344\277\235\347\250\205\350\262\250\345\223\201\345\207\272\345\273\240\344\277\256\347\220\206\346\252\242\346\270\254\346\210\226\346\240\270\346\250\243\345\273\272\347\253\213\344\275\234\346\245\255(BCSI15).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\344\277\241\347\224\250\347\213\200\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(EPSI11).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\200\237\345\207\272\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(INVI11)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\200\237\345\207\272\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(INVI12)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\205\266\344\273\226\345\207\272\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI13).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\207\272\345\217\243\350\262\273\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(EPSI10).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BCHI09).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BCSI12).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI05).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI62).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\212\240\345\267\245\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI10).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI19).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI58).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI20).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI59).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\223\201\350\231\237\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(INVI24).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI05).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\244\232\345\270\263\346\234\254\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI62).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\247\224\345\244\226\345\267\245\345\226\256\351\226\213\347\253\213(IDLI33).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI06).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\256\242\346\210\266\350\263\207\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(COPI15).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\345\272\253\345\255\230\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(INVI05)[GPSD260030].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\207\211\344\273\230\346\206\221\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACPI02).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\211\271\346\254\241\346\216\241\350\263\274\350\250\210\345\212\203\347\266\255\350\255\267-\344\276\235\345\223\201\350\231\237(LRPI03).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\211\271\346\254\241\347\224\237\347\224\242\350\250\210\345\212\203\347\266\255\350\255\267-\344\276\235\345\223\201\350\231\237(LRPI01).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\213\206\350\247\243\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI06).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\216\222\347\250\213\350\246\217\345\212\203\344\275\234\346\245\255(APSQ02).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\216\241\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI07)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI08)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\224\266\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACRI03).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\240\261\345\220\215\344\275\234\346\245\255(HRSI34).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\226\260\345\256\242\346\210\266\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(COPI21).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\232\253\345\207\272\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI19).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\232\253\345\207\272\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI20).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI10).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI03).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\346\264\276\350\273\212\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI14).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\347\265\204\345\220\210\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI05).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\347\265\220\345\270\263\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACRI02).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\243\275\351\200\240\345\221\275\344\273\244\345\273\272\347\253\213\344\275\234\346\245\255(MOCI02).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\243\275\351\200\240\345\221\275\344\273\244\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(MOCI12).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI07).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\250\210\345\212\203\344\276\206\346\272\220\350\250\230\351\214\204\347\266\255\350\255\267(LRPI05).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\250\223\347\267\264\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(HRSI23).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\251\242\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI14)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\252\277\346\225\264\346\262\226\351\212\267\345\210\206\351\214\204\345\273\272\347\253\213\344\275\234\346\245\255(FCSI04).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI05)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\253\213\350\263\274\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(PURI16).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\253\213\350\263\274\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(PURI16)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\262\250\351\201\213\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI07).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\345\240\261\345\273\242\345\273\272\347\253\213\344\275\234\346\245\255(ASTI08).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\345\244\226\351\200\201\345\273\272\347\253\213\344\275\234\346\245\255(ASTI13).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\212\230\350\210\212\345\273\272\347\253\213\344\275\234\346\245\255(ASTI11).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\216\241\350\263\274\350\256\212\346\233\264\344\275\234\346\245\255(ASTI24).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\216\241\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI22).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\224\266\345\233\236\345\273\272\347\253\213\344\275\234\346\245\255(ASTI14).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\224\271\350\211\257\345\273\272\347\253\213\344\275\234\346\245\255(ASTI06).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\346\270\233\346\220\215\345\273\272\347\253\213\344\275\234\346\245\255(ASTI25).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\347\247\273\350\275\211\345\273\272\347\253\213\344\275\234\346\245\255(ASTI12).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\350\251\242\345\203\271\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI20).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\350\252\277\346\225\264\345\273\272\347\253\213\344\275\234\346\245\255(ASTI10).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI19).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ASTI23).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\263\207\347\224\242\351\207\215\344\274\260\345\273\272\347\253\213\344\275\234\346\245\255(ASTI07).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\350\275\211\346\222\245\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(INVI08)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\351\200\200\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI11)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI09)[GP25(PR)].form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\351\212\267\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI08).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\351\212\267\351\200\200\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI09).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\351\240\220\347\256\227\346\214\252\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI23).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@workflow/form-default/\351\240\220\347\256\227\350\277\275\345\212\240\345\273\272\347\253\213\344\275\234\346\245\255(ACTI22).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/APS\346\216\241\350\263\274\350\252\277\346\225\264\345\273\272\350\255\260\344\275\234\346\245\255(APSI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/APS\350\243\275\344\273\244\350\243\275\347\250\213\350\252\277\346\225\264\345\273\272\350\255\260\344\275\234\346\245\255(APSI13).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/APS\350\243\275\344\273\244\350\252\277\346\225\264\345\273\272\350\255\260\344\275\234\346\245\255(APSI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/APS\350\250\202\345\226\256\350\252\277\346\225\264\345\273\272\350\255\260\344\275\234\346\245\255(APSI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/BOM \350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI04).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/BOM\347\224\250\351\207\217\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(BOMI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/E-BOM\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/PACKING LIST \345\273\272\347\253\213\344\275\234\346\245\255(EPSI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/PACKING LIST\345\273\272\347\253\213\344\275\234\346\245\255(IDLI43).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/SI\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IPSI04).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/WAFER BANK\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IDLI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/WAFER \350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IDLI15).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\344\273\230\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACPI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\207\272\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(BCSI17).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\344\277\235\347\250\205\345\273\240\345\244\226\345\212\240\345\267\245\345\223\201\351\201\213\345\233\236\351\200\262\345\273\240\345\273\272\347\253\213\344\275\234\346\245\255(BCSI18).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\344\277\235\347\250\205\346\251\237\345\231\250\350\250\255\345\202\231\351\200\262\345\207\272\345\217\243\347\225\260\345\213\225\345\273\272\347\253\213\344\275\234\346\245\255(BCHI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(BCHI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\344\277\235\347\250\205\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(BCSI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\344\277\235\347\250\205\350\262\250\345\223\201\345\207\272\345\273\240\344\277\256\347\220\206\346\252\242\346\270\254\346\210\226\346\240\270\346\250\243\345\273\272\347\253\213\344\275\234\346\245\255(BCSI15).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\344\277\241\347\224\250\347\213\200\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(EPSI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\200\237\345\207\272\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(INVI11)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\200\237\345\207\272\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(INVI12)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\205\266\344\273\226\345\207\272\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI13).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\207\272\345\217\243\350\262\273\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(EPSI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BCHI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\207\272\345\273\240\346\224\276\350\241\214\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BCSI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI62).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\212\240\345\267\245\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI19).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\220\210\347\264\204\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI58).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI20).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\220\210\347\264\204\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI59).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\223\201\350\231\237\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(INVI24).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\244\232\345\270\263\346\234\254\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI62).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\247\224\345\244\226\345\267\245\345\226\256\351\226\213\347\253\213(IDLI33).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\256\242\346\210\266\350\263\207\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(COPI15).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\345\272\253\345\255\230\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(INVI05)[GPSD260030].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\207\211\344\273\230\346\206\221\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACPI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\211\271\346\254\241\346\216\241\350\263\274\350\250\210\345\212\203\347\266\255\350\255\267-\344\276\235\345\223\201\350\231\237(LRPI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\211\271\346\254\241\347\224\237\347\224\242\350\250\210\345\212\203\347\266\255\350\255\267-\344\276\235\345\223\201\350\231\237(LRPI01).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\213\206\350\247\243\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\216\222\347\250\213\350\246\217\345\212\203\344\275\234\346\245\255(APSQ02).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\216\241\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI07)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI08)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\224\266\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACRI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\240\261\345\220\215\344\275\234\346\245\255(HRSI34).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\226\260\345\256\242\346\210\266\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(COPI21).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\232\253\345\207\272\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI19).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\232\253\345\207\272\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(IDL)(IDLI20).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\346\264\276\350\273\212\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\347\265\204\345\220\210\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\347\265\220\345\270\263\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACRI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\243\275\351\200\240\345\221\275\344\273\244\345\273\272\347\253\213\344\275\234\346\245\255(MOCI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\243\275\351\200\240\345\221\275\344\273\244\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(MOCI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\250\210\345\212\203\344\276\206\346\272\220\350\250\230\351\214\204\347\266\255\350\255\267(LRPI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\250\223\347\267\264\347\224\263\350\253\213\345\273\272\347\253\213\344\275\234\346\245\255(HRSI23).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\251\242\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI14)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\252\277\346\225\264\346\262\226\351\212\267\345\210\206\351\214\204\345\273\272\347\253\213\344\275\234\346\245\255(FCSI04).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI05)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\253\213\350\263\274\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(PURI16).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\253\213\350\263\274\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(PURI16)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\262\250\351\201\213\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\345\240\261\345\273\242\345\273\272\347\253\213\344\275\234\346\245\255(ASTI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\345\244\226\351\200\201\345\273\272\347\253\213\344\275\234\346\245\255(ASTI13).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\346\212\230\350\210\212\345\273\272\347\253\213\344\275\234\346\245\255(ASTI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\346\216\241\350\263\274\350\256\212\346\233\264\344\275\234\346\245\255(ASTI24).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\346\216\241\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI22).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\346\224\266\345\233\236\345\273\272\347\253\213\344\275\234\346\245\255(ASTI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\346\224\271\350\211\257\345\273\272\347\253\213\344\275\234\346\245\255(ASTI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\346\270\233\346\220\215\345\273\272\347\253\213\344\275\234\346\245\255(ASTI25).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\347\247\273\350\275\211\345\273\272\347\253\213\344\275\234\346\245\255(ASTI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\350\251\242\345\203\271\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI20).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\350\252\277\346\225\264\345\273\272\347\253\213\344\275\234\346\245\255(ASTI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI19).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ASTI23).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\263\207\347\224\242\351\207\215\344\274\260\345\273\272\347\253\213\344\275\234\346\245\255(ASTI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\350\275\211\346\222\245\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(INVI08)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\351\200\200\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI11)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI09)[GP25(PR)].form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\351\212\267\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\351\212\267\351\200\200\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\351\240\220\347\256\227\346\214\252\347\224\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI23).form"`
  - ➕ **新增**: `"Release/copyfiles/@workflow/form-default/rwd-form/\351\240\220\347\256\227\350\277\275\345\212\240\345\273\272\347\253\213\344\275\234\346\245\255(ACTI22).form"`

### 201. [Web] C01-20240516003 沒使用流程封存，但報流程封存的錯问题修正
- **Commit ID**: `f505ba54e6e7911340188424e62d6bfd334f2362`
- **作者**: 刘旭
- **日期**: 2024-05-21 13:39:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/MainDsManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveDsManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 202. [組織同步] C01-20240517001 HR同步，新增職務定義檢查
- **Commit ID**: `491eb4b98f72d9c87d591784e85e3552cd1ee850`
- **作者**: 邱郁晏
- **日期**: 2024-05-21 11:43:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 203. [Web]C01-20240517012 修正开窗类型为使用者可以勾选"前置组织代号"的问题
- **Commit ID**: `c832ab2f24b0748d3076cb1cc3a30837e7c6367c`
- **作者**: 周权
- **日期**: 2024-05-21 10:50:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 204. [PRODT]C01-20240513002 修正Web流程管理工具的核決層級關卡中設定過參考活動為自定義時殘留的贓資料會導致流程無法簽入的問題
- **Commit ID**: `64d162e6d7140b833d1a2b9e1717856e7cb39e62`
- **作者**: yamiyeh10
- **日期**: 2024-05-20 12:06:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 205. [流程引擎]C01-20240516004 修正TIPTOP拋單，log出現ESS的相關內容
- **Commit ID**: `d5b2a1efdd5ab46b654a016489a95085c40c2380`
- **作者**: 林致帆
- **日期**: 2024-05-20 10:37:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 206. [EBG]Q00-20240517002 調整電子簽章表單卡控+後置流程更新附件
- **Commit ID**: `e0d2c2a233a79b1f535ad6c47342b1e57cf4c5ce`
- **作者**: 林致帆
- **日期**: 2024-05-17 18:05:12
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/EBGModule/EBGCreateForm.js`
  - 📝 **修改**: `Release/copyfiles/@ebg/form-default/EBGForm.form`

### 207. [PRODT]C01-20240509001 修正流程實例中的詳細流程圖中連接線條件名稱會覆蓋問題
- **Commit ID**: `238d3205a27fdda3a0f529eb8587a01059acde8b`
- **作者**: yamiyeh10
- **日期**: 2024-05-17 15:26:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 208. [Athena]Q00-20240517001 應用Token出貨改成固定參數
- **Commit ID**: `6aa5b3431d110da6faf6530b1b3ce34263f66330`
- **作者**: 林致帆
- **日期**: 2024-05-17 12:06:35
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_Oracle.sql`

### 209. [Web]C01-20240516006 修正简易sql被过滤表名导致查询失败的问题
- **Commit ID**: `b3c7443af7c450f737cb6f80aca189bf35baafc6`
- **作者**: 周权
- **日期**: 2024-05-17 10:52:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 210. [流程引擎] C01-20240508002 修正流程在併簽關卡，刪除流程時未釋放連線數問題
- **Commit ID**: `cdcff591a91522adda457fe16c5550805dc0053f`
- **作者**: 邱郁晏
- **日期**: 2024-05-16 17:28:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 211. [内部]Q00-20240516001 修正查询sql包含多组"order by",导致资料选取注册器及SQL注册器查询失败问题
- **Commit ID**: `767326219b63d9d028fa9f92f9b96fbf700ae9f7`
- **作者**: 周权
- **日期**: 2024-05-16 15:45:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 212. [流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法(補)
- **Commit ID**: `61df642dffd6955c675a99060e2ad58b3646dc27`
- **作者**: 邱郁晏
- **日期**: 2024-05-16 13:39:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java`

### 213. [Web]C01-20240515001 修正客制開窗sql语句包含多组order by，sql會查詢失敗的問題
- **Commit ID**: `917ca0a81e5cbbe13d289e0753b100ff583b3065`
- **作者**: 周权
- **日期**: 2024-05-16 08:57:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 214. [Web]C01-20240514002 修正企業流程監控→加總→已處理工作量,選取多人或多部門產生統計圖報錯的問題[補]
- **Commit ID**: `3aa69e3d08717b0f7868691ab354205db77de0f7`
- **作者**: 周权
- **日期**: 2024-05-15 15:06:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`

### 215. [Web]C01-20240514002 修正企業流程監控→加總→已處理工作量,選取多人或多部門產生統計圖報錯的問題
- **Commit ID**: `a7ed7491239a14ac59308ba7da001d1eb2ad4668`
- **作者**: 周权
- **日期**: 2024-05-15 10:00:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`

### 216. [TIPTOP]C01-20240510004 修正流程設定授權人員，外部連結查看該流程會顯示此人員無權限訪問
- **Commit ID**: `d2db3543407a9517948d9131380775bfba073fb3`
- **作者**: 林致帆
- **日期**: 2024-05-14 13:56:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 217. [内部]Q00-20240514001 修改系統設定trace.process.hidden.process.package.ids.prefix的描述
- **Commit ID**: `de32202c6bd73fbcef2357dcfc96f723c181d0f6`
- **作者**: 周权
- **日期**: 2024-05-14 10:21:01
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_Oracle.sql`

### 218. [Web] C01-20240509007 "模擬使用者"可以授權給一般使用者，會讓一般使用者可以模擬最高權限的administrator问题修正
- **Commit ID**: `49a01f7229f304d2854456f7302012ac296b3e87`
- **作者**: 刘旭
- **日期**: 2024-05-11 09:50:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 219. [Web]C01-20240509004 修正grid多栏位格线对不齐的问题
- **Commit ID**: `97067bb4b8b825d621d8a449511538f8ed38dec3`
- **作者**: 周权
- **日期**: 2024-05-10 17:31:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 220. [Web]V00-20240508001 修正流程主旨範本設定<#ActivityId>、 <#ActivityName>显示N.A.
- **Commit ID**: `0590250f3a4ec66ecd12ccb8b6f80f81d58b78be`
- **作者**: 周权
- **日期**: 2024-05-10 14:04:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 221. [Web] C01-20240509002 系统设定中变更密码参数需要重启服务才生效问题修正
- **Commit ID**: `06eefb841d3670bc25a5cc4764bbbddb64bdee0f`
- **作者**: 刘旭
- **日期**: 2024-05-10 11:06:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`

### 222. [流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法(補)
- **Commit ID**: `d76e1d320a9947821ccfbd43045de8ed46d7b498`
- **作者**: 邱郁晏
- **日期**: 2024-05-10 09:17:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/CommonProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java`

### 223. [TIPTOP]C01-20240429008 修正回寫失敗訊息無法顯示在畫面上
- **Commit ID**: `1cc7ea9d248fa9e5cb8e746c79431615853460e3`
- **作者**: 林致帆
- **日期**: 2024-05-09 17:21:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`

### 224. [BPM APP]C01-20240508004 修正結案的流程從企業微信推播進入不會導向追蹤表單畫面問題
- **Commit ID**: `320212d41e8378fdc2c6729473eb0a7b99e89e6a`
- **作者**: yamiyeh10
- **日期**: 2024-05-09 17:07:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 225. [流程引擎] C01-20240508002 修正產品調用JDBC但沒釋放連線數之寫法
- **Commit ID**: `c0b4fa418083df03a81d9f5a96112ddca2612c27`
- **作者**: 邱郁晏
- **日期**: 2024-05-09 15:09:21
- **變更檔案數量**: 29
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/orgAnalyze/OrgAnalyzeManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/CommonProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DoneWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDataSearchListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ParamForProcInvokingListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignTrackListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimpleExpenseAccountItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimplePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SuspendedInvokeActListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileInvokableProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 226. [內部]Q00-20240509001 調整重新向資料庫取SystemVariable內容發生錯誤時的log訊息
- **Commit ID**: `a40dfe573f744355a0a85b8929958cdc26dc26dc`
- **作者**: yamiyeh10
- **日期**: 2024-05-09 11:29:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`

### 227. [流程設計師]C01-20240416006 修正流程走到核決關卡後點擊待辦清單的流程會無法正常打開
- **Commit ID**: `e2fc7a0c1a75ee33dcd8c10443a77fc95400a272`
- **作者**: 林致帆
- **日期**: 2024-05-09 10:35:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java`

### 228. [流程引擎] C01-20240508002 修正加簽關卡後，沒有釋放連線數
- **Commit ID**: `d05789001b2842c6656968d45bf43b47ae5e1a12`
- **作者**: 邱郁晏
- **日期**: 2024-05-08 17:29:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 229. [流程引擎]C01-20240502001 修正在核決權限關卡加簽，預解析会重复出现核決權限表名稱的问题
- **Commit ID**: `b2b82113c282f3669a27ca76b0e73ec814133ae5`
- **作者**: 周权
- **日期**: 2024-05-08 11:43:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 230. [Web]Q00-20240508001 修正系統設定發起人看不到追蹤流程之設定
- **Commit ID**: `ca179a707b3a556e811c3cc3c9a67faedd55bc79`
- **作者**: 周权
- **日期**: 2024-05-08 10:43:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 231. [Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log(補)
- **Commit ID**: `ce076f02a9ce49594284ba8c11713c3c9f4f1f9a`
- **作者**: 邱郁晏
- **日期**: 2024-05-08 10:24:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 232. [Web] Q00-20240507002 調整流程表單符合多個條件式時，錯誤提示不明確問題，新增Log
- **Commit ID**: `76b77e1f3ec50165e9246a4ad4d181e428847c4d`
- **作者**: 邱郁晏
- **日期**: 2024-05-07 18:01:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 233. [流程引擎] Q00-20240507001 新增單筆封存流程還原接口的防呆，若無流程封存模組則不可調用
- **Commit ID**: `73b3ba0ab85a3ae956393aec0c2ec5dbf91ecbbf`
- **作者**: 邱郁晏
- **日期**: 2024-05-07 14:57:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/NaNaXWebHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 234. [MPT]Q00-20240506001 調整ecp_source_connect表ip欄位上限
- **Commit ID**: `ea67c7e769f0add5d2a705ea58473313f3e5c25e`
- **作者**: yamiyeh10
- **日期**: 2024-05-06 16:23:29
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `Release/db/update/MPT_5.8.10.2_DDL_DM8.sql`
  - ➕ **新增**: `Release/db/update/MPT_5.8.10.2_DDL_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/MPT_5.8.10.2_DDL_Oracle.sql`

### 235. [SYSDT]C01-20240502002 修正設計師使用權限管理中若人員的最後工作日設為未來日期時會無法顯示使用者問題
- **Commit ID**: `5d9549e0695b0042f21f15b4f57b9a3db3f6d6ec`
- **作者**: yamiyeh10
- **日期**: 2024-05-03 10:00:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java`

### 236. [PLM]增加 Digiwin PLM 整合，與 Open PLM 共用序號[補]
- **Commit ID**: `e7fff177e8f4eb8f5d6fa86e748057c94fde1509`
- **作者**: lorenchang
- **日期**: 2024-05-03 09:42:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DML_Oracle.sql`

### 237. [PLM]增加 Digiwin PLM 整合，與 Open PLM 共用序號
- **Commit ID**: `9300b65c077f0c8335f4125c1fa17216695f502f`
- **作者**: lorenchang
- **日期**: 2024-05-02 16:49:39
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/UpdateProcessStatusBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIService.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIServiceLocator.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIServicePortType.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIServicePortTypeProxy.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ERPIServiceSoap11BindingStub.java`
  - ➕ **新增**: `Release/db/update/5.8.10.2_DML_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.2_DML_Oracle.sql`

### 238. [SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限
- **Commit ID**: `c4905bbc3f4e780a5bb0bff585f4b2a0c556e878`
- **作者**: yamiyeh10
- **日期**: 2024-05-02 16:34:00
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_Oracle.sql`

### 239. Revert "[SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限"
- **Commit ID**: `102c92992d7f5e4346849c08bdb5929c4b4f842d`
- **作者**: yamiyeh10
- **日期**: 2024-05-02 16:17:51
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_Oracle.sql`

### 240. [SYSDT]C01-20240429006 調整DataAccessDefinition表hostName欄位上限
- **Commit ID**: `4c954d47f73b2c7e186c19c3c27a0403c9543a50`
- **作者**: yamiyeh10
- **日期**: 2024-05-02 16:04:56
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.2_DDL_Oracle.sql`

### 241. [BPM APP]C01-20240430007 調整當郵件內容為空時補上空格讓企業微信可以正常推播
- **Commit ID**: `5be6bb4ae3c134d4b9922be577ee80e4564eb597`
- **作者**: yamiyeh10
- **日期**: 2024-05-02 14:09:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 242. [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制
- **Commit ID**: `dfa40b1075bcffa6b7811cc1d81832da8474d8ae`
- **作者**: yamiyeh10
- **日期**: 2024-04-30 15:47:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 243. [Web] Q00-20240429004 報表設計器儲存報 SqlConditionList is too long问题修正
- **Commit ID**: `29a6dd4c446f3cb886bfcba9866bfeb21bd3e3d1`
- **作者**: 刘旭
- **日期**: 2024-04-29 17:39:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java`

### 244. [T100]Q00-20240429001 新增RWD響應式表單
- **Commit ID**: `82db394ef5fbad1465ff800ef64fd80ed48336ee`
- **作者**: 林致帆
- **日期**: 2024-04-29 14:12:41
- **變更檔案數量**: 427
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/ECR\347\224\263\350\253\213\344\275\234\346\245\255(abmt500).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/Invoice\347\266\255\350\255\267\344\275\234\346\245\255(axmt620).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/MPS\350\250\210\345\212\203\347\266\255\350\255\267\344\275\234\346\245\255(apst310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/RMA\347\266\255\350\255\267\344\275\234\346\245\255(armt100).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\270\200\350\210\254\345\224\256\345\203\271\350\252\277\346\225\264\344\275\234\346\245\255(aprt112).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\270\200\350\210\254\346\216\241\350\263\274\345\220\210\347\264\204\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt480).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\270\200\350\210\254\346\216\241\350\263\274\350\251\242\345\203\271\344\275\234\346\245\255(apmt420).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\270\200\350\210\254\346\240\270\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt440).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\270\200\350\210\254\351\200\262\345\203\271\350\252\277\346\225\264\344\275\234\346\245\255(aprt111).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\270\200\351\232\216\346\256\265\350\252\277\346\222\245\344\275\234\346\245\255(aint330).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\272\244\346\230\223\345\260\215\350\261\241\345\207\206\345\205\245\347\266\255\350\255\267\344\275\234\346\245\255(apmt801).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\272\244\346\230\223\345\260\215\350\261\241\347\224\263\350\253\213\344\275\234\346\245\255(apmt100).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\272\244\346\230\223\345\260\215\350\261\241\350\255\211\347\205\247\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(apmt820).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\276\233\346\207\211\345\225\206\345\207\206\345\205\245\345\217\212\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(apmt800).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\276\233\346\207\211\345\225\206\345\220\210\347\264\204\347\224\263\350\253\213\344\275\234\346\245\255(astt301).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\276\233\346\207\211\345\225\206\347\224\263\350\253\213\344\275\234\346\245\255(apmt200).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\276\233\346\207\211\345\225\206\347\265\220\347\256\227\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt340).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\276\233\346\207\211\345\225\206\347\270\276\346\225\210\350\251\225\346\240\270\345\256\232\346\200\247\345\260\210\346\241\210\350\251\225\345\210\206\344\275\234\346\245\255(apmt811).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\276\233\346\207\211\345\225\206\347\270\276\346\225\210\350\251\225\346\240\270\347\266\234\345\220\210\345\276\227\345\210\206\350\252\277\346\225\264\344\275\234\346\245\255(apmt814).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\276\233\346\207\211\345\225\206\350\262\250\346\254\276\345\260\215\345\270\263\344\275\234\346\245\255(aapt110).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\276\233\346\207\211\345\225\206\350\262\273\347\224\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt320).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\277\203\351\212\267\345\203\271\346\240\274\350\252\277\346\225\264\344\275\234\346\245\255(aprt113).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\277\203\351\212\267\350\253\207\345\210\244\346\242\235\344\273\266\347\224\263\350\253\213(aprt310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\344\277\241\347\224\250\350\266\205\351\231\220\346\224\276\350\241\214\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt140).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\200\237\350\262\250\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt542).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\200\237\350\262\250\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt521).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\200\237\350\262\250\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt501).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\200\237\350\262\250\351\202\204\351\207\217\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt591).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\204\237\351\202\204\346\234\254\346\201\257\347\266\255\350\255\267(afmt170).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\205\247\351\203\250\347\265\220\347\256\227\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt740).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\205\266\344\273\226\346\207\211\344\273\230\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt301).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\205\266\344\273\226\346\207\211\346\224\266\345\270\263\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255(axrt330).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt540).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\207\272\350\262\250\346\207\211\346\224\266\347\266\255\350\255\267\344\275\234\346\245\255(axrt300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\207\272\350\262\250\347\260\275\346\224\266\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt580).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\207\272\350\262\250\347\260\275\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt590).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt520).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\210\206\351\212\267\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt540).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\210\206\351\212\267\345\207\272\350\262\250\347\260\275\346\224\266\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt580).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\210\206\351\212\267\345\207\272\350\262\250\347\260\275\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt590).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\210\206\351\212\267\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt500).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\210\206\351\212\267\350\250\202\345\226\256\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(adbt510).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\210\206\351\212\267\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt600).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\210\270\347\250\256\345\237\272\346\234\254\350\263\207\346\226\231\347\224\263\350\253\213\344\275\234\346\245\255(agct300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\214\205\350\243\235\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt610).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\223\201\350\263\252\346\252\242\351\251\227\350\250\230\351\214\204\347\266\255\350\255\267\344\275\234\346\245\255(aqct300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\223\201\350\263\252\347\225\260\345\270\270\347\224\263\350\253\213\344\275\234\346\245\255(aqct310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\225\206\345\223\201\345\207\206\345\205\245\347\224\263\350\253\213\344\275\234\346\245\255(artt300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\233\272\350\263\207\351\240\220\347\256\227\347\267\250\350\243\275\344\275\234\346\245\255(abgt815).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\234\260\347\243\205\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt640).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\240\261\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255-\345\226\256\347\255\206\345\274\217(asft335).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\240\264\345\234\260\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(amht204).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\244\226\345\214\257\344\272\244\346\230\223\347\266\255\350\255\267\344\275\234\346\245\255(afmt534).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\244\226\345\214\257\345\220\210\347\264\204\347\266\255\350\255\267\344\275\234\346\245\255(afmt533).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\244\226\345\214\257\346\234\237\346\234\253\345\205\254\345\205\201\345\203\271\345\200\274\347\266\255\350\255\267(afmt552).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\244\232\345\273\240\345\225\206\350\253\213\346\254\276\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(\346\265\201\351\200\232)(aapt815).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\216\241\350\263\274\345\200\211\351\200\200\344\275\234\346\245\255(apmt581).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\216\241\350\263\274\345\205\245\345\272\253\344\275\234\346\245\255(apmt571).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\216\241\350\263\274\345\220\210\347\264\204\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt481).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\216\241\350\263\274\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt491).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt501).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255(apmt521).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\216\241\350\263\274\350\251\242\345\203\271\344\275\234\346\245\255(apmt421).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\216\241\350\263\274\351\251\227\351\200\200\344\275\234\346\245\255(apmt561).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\247\224\345\244\226\346\240\270\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt441).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\256\232\345\255\230\347\266\255\350\255\267\344\275\234\346\245\255(afmt531).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\256\242\346\210\266\344\277\241\347\224\250\351\241\215\345\272\246\347\224\263\350\253\213\344\275\234\346\245\255(axmt206).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\256\242\346\210\266\345\207\206\345\205\245\345\217\212\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(axmt800).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\256\242\346\210\266\347\224\263\350\253\213\344\275\234\346\245\255(axmt200).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\256\242\346\210\266\350\262\250\346\254\276\345\260\215\345\270\263\344\275\234\346\245\255(aist310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\256\242\350\250\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt700).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\257\246\345\234\260\347\233\244\351\273\236\350\250\210\347\225\253\347\266\255\350\255\267\344\275\234\346\245\255(aint820).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\260\210\346\253\203\345\220\210\347\264\204\347\225\260\345\213\225\347\224\263\350\253\213\344\275\234\346\245\255(astt401).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\260\210\346\253\203\346\226\260\345\225\206\345\223\201\345\274\225\351\200\262\347\266\255\350\255\267\344\275\234\346\245\255(artt407).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\344\270\200\350\210\254\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft323).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\345\200\222\346\211\243\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft324).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\345\200\222\346\211\243\351\240\230\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft314).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\345\234\250\350\243\275\344\270\213\351\232\216\346\226\231\345\240\261\345\273\242\344\275\234\346\245\255(asft339).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\344\275\234\346\245\255(asft340).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\346\210\220\345\245\227\347\231\274\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft311).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\346\210\220\345\245\227\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft321).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\346\254\240\346\226\231\350\243\234\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft313).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\347\225\266\347\253\231\344\270\213\347\267\232\344\275\234\346\245\255(asft337).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\347\225\266\347\253\231\345\240\261\345\273\242\344\275\234\346\245\255(asft336).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(asft300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\350\243\275\347\250\213\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(asft801).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\350\243\275\347\250\213\351\207\215\345\267\245\350\275\211\345\207\272\344\275\234\346\245\255(asft338).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(asft800).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\350\266\205\351\240\230\347\266\255\350\255\267\344\275\234\346\245\255(asft312).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\350\266\205\351\240\230\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft322).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\270\202\345\240\264\346\216\250\345\273\243\346\264\273\345\213\225\346\240\270\351\212\267\347\266\255\350\255\267\344\275\234\346\245\255(astt605).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\270\202\345\240\264\346\216\250\345\273\243\346\264\273\345\213\225\347\224\263\350\253\213(astt604).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\270\263\351\275\241\345\217\212\345\243\236\345\270\263\346\217\220\345\210\227\347\266\255\350\255\267(axrt940).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\272\224\346\224\266\347\245\250\346\215\256\346\224\266\347\245\250\344\275\234\344\270\232(anmt510).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\272\253\344\275\215\345\217\226\346\266\210\347\225\231\347\275\256\347\266\255\350\255\267\344\275\234\346\245\255(aint161).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\272\253\345\255\230\345\240\261\345\273\242\347\224\263\350\253\213\344\275\234\346\245\255(aint310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\272\253\345\255\230\345\240\261\345\273\242\351\231\244\345\270\263\344\275\234\346\245\255(aint311).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\272\253\345\255\230\346\234\211\346\225\210\346\227\245\346\234\237\350\256\212\346\233\264\344\275\234\346\245\255(aint180).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\272\253\345\255\230\347\225\260\345\270\270\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(aint170).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\275\210\346\200\247\346\216\241\350\263\274\345\203\271\346\240\274\347\224\263\350\253\213\344\275\234\346\245\255(apmt128).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\275\210\346\200\247\351\212\267\345\224\256\345\203\271\346\240\274\347\224\263\350\253\213\344\275\234\346\245\255(axmt128).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\204\217\345\220\221\345\215\224\350\255\260\347\266\255\350\255\267\344\275\234\346\245\255(astt811).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\207\211\344\273\230\345\214\257\346\254\276\347\225\260\345\213\225\344\275\234\346\245\255(anmt480).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\207\211\344\273\230\345\214\257\346\254\276\351\226\213\347\253\213\344\275\234\346\245\255(anmt460).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\207\211\344\273\230\345\270\263\346\254\276\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\207\211\344\273\230\345\276\205\346\212\265\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt340).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\207\211\344\273\230\346\240\270\351\212\267\347\266\255\350\255\267\344\275\234\346\245\255(aapt420).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\207\211\346\224\266\345\276\205\346\212\265\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axrt340).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\212\225\350\263\207\347\220\206\350\262\241\347\266\255\350\255\267\344\275\234\346\245\255(afmt532).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\212\225\350\263\207\347\224\263\350\253\213\345\226\256(afmt510).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\212\225\350\263\207\350\263\274\350\262\267\345\270\263\345\213\231\345\226\256(afmt535).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\345\273\266\346\234\237\350\256\212\346\233\264\347\224\263\350\253\213\344\275\234\346\245\255(astt803).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\347\225\260\345\213\225\347\224\263\350\253\213\344\275\234\346\245\255(astt801).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\347\265\202\346\255\242\347\224\263\350\253\213\344\275\234\346\245\255(astt805).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\350\262\273\347\224\250\345\204\252\346\203\240\347\224\263\350\253\213(astt802).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\350\262\273\347\224\250\346\250\231\346\272\226\350\256\212\346\233\264\344\275\234\346\245\255(astt806).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\351\235\242\347\251\215\350\256\212\346\233\264\347\224\263\350\253\213(astt804).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\345\200\211\351\200\200\347\266\255\350\255\267\344\275\234\346\245\255(apmt580).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\345\203\271\346\240\274\350\241\250\347\224\263\350\253\213\344\275\234\346\245\255(apmt129).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(apmt570).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\345\205\245\345\272\253\347\266\255\350\255\267\344\275\234\346\245\255(apmt880).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt490).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt500).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt840).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt530).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt862).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt520).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt860).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\350\243\234\345\267\256\347\266\255\350\255\267\344\275\234\346\245\255(aprt601).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt510).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt850).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\351\200\200\345\273\240\347\266\255\350\255\267\344\275\234\346\245\255(apmt890).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\351\240\220\344\273\230\346\206\221\350\255\211\347\266\255\350\255\267\344\275\234\346\245\255(aapt310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\351\251\227\351\200\200\347\266\255\350\255\267\344\275\234\346\245\255(apmt560).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\216\241\350\263\274\351\251\227\351\200\200\347\266\255\350\255\267\344\275\234\346\245\255(apmt870).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\224\257\345\207\272\351\241\236\345\220\210\345\220\214\347\225\260\345\213\225\347\224\263\350\253\213\344\275\234\346\245\255(astt820).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\224\266\346\254\276\346\240\270\351\212\267\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axrt400).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\224\276\350\241\214\345\226\256\347\256\241\345\210\266\344\275\234\346\245\255(abxt400).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\226\231\344\273\266\346\211\277\350\252\215\347\224\263\350\253\213\344\275\234\346\245\255(abmt400).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\226\231\344\273\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(aimt300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\226\231\344\273\266\350\243\275\347\250\213\350\263\207\346\226\231\346\226\260\345\242\236\343\200\201\344\277\256\346\224\271\347\224\263\350\253\213\344\275\234\346\245\255(aect801).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\232\253\344\274\260\346\207\211\346\224\266\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axrt320).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\234\203\345\223\241\345\215\241\345\204\262\345\200\274\351\207\221\351\241\215\350\252\277\346\225\264\344\275\234\346\245\255(ammt428).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\234\203\345\223\241\345\215\241\347\250\256\345\237\272\346\234\254\350\263\207\346\226\231\347\224\263\350\253\213\344\275\234\346\245\255(ammt320).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\234\203\345\223\241\345\215\241\347\251\215\351\273\236\350\252\277\346\225\264\347\266\255\350\255\267\344\275\234\346\245\255(ammt421).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\234\237\345\210\245\350\262\273\347\224\250\351\240\220\347\256\227\347\250\275\350\246\210(abgt635).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\234\237\346\234\253\345\205\254\345\205\201\345\203\271\345\200\274\347\266\255\350\255\267(afmt551).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\346\264\276\350\273\212\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt630).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt532).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt863).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt522).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt861).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\204\241\350\250\202\345\226\256\345\207\272\350\262\250\347\266\255\350\255\267\344\275\234\346\245\255(axmt541).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\207\237\351\201\213\346\223\232\351\273\236ECN\347\266\255\350\255\267\344\275\234\346\245\255(abmt310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\207\237\351\201\213\346\223\232\351\273\236\345\244\232\344\270\273\344\273\266ECN\347\266\255\350\255\267\344\275\234\346\245\255(abmt311).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\215\250\347\253\213\351\234\200\346\261\202\347\266\255\350\255\267\344\275\234\346\245\255(apst300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\224\237\351\256\256\345\203\271\346\240\274\350\252\277\346\225\264\344\275\234\346\245\255(aprt121).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\224\242\345\223\201\347\265\204\345\220\210\345\214\205\350\243\235\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aint390).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\231\274\347\245\250\350\253\213\346\254\276\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt415).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\247\237\350\263\203\350\262\273\347\224\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt810).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\266\223\351\212\267\345\225\206\344\273\243\345\242\212\350\262\273\347\224\250\345\240\261\351\212\267\347\266\255\350\255\267\344\275\234\346\245\255(astt606).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\266\223\351\212\267\345\225\206\345\220\210\347\264\204\347\224\263\350\253\213\344\275\234\346\245\255(astt601).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\266\223\351\212\267\345\225\206\347\265\220\347\256\227\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt640).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\347\266\223\351\212\267\345\225\206\350\262\273\347\224\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt620).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\207\252\346\234\211\346\226\260\345\225\206\345\223\201\345\274\225\351\200\262\347\224\263\350\253\213\344\275\234\346\245\255(artt406).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\207\252\347\207\237\345\225\206\345\223\201\345\274\225\351\200\262\347\224\263\350\253\213\344\275\234\346\245\255(artt405).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\236\215\350\263\207\345\220\210\345\220\214\347\266\255\350\255\267(afmt035).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\236\215\350\263\207\347\224\263\350\253\213\345\226\256(afmt015).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\236\215\350\263\207\350\263\207\351\207\221\345\210\260\345\270\263\347\266\255\350\255\267(afmt140).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\246\201\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt830).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\246\201\350\262\250\345\226\256\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(apmt835).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt500).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt510).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\250\202\351\207\221\351\240\220\346\224\266\347\266\255\350\255\267\344\275\234\346\245\255(axrt310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\252\277\346\222\245\345\267\256\347\225\260\350\252\277\346\225\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aint520).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\252\277\346\222\245\347\224\263\350\253\213\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aint320).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt400).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\253\213\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt410).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\262\273\347\224\250\345\240\261\346\224\257\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt330).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\262\273\347\224\250\346\250\231\346\272\226\350\250\255\345\256\232\347\224\263\350\253\213\344\275\234\346\245\255(astt252).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\262\273\347\224\250\351\240\220\346\224\257\347\224\263\350\253\213\344\275\234\346\245\255(aapt331).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\346\272\220\346\255\270\351\202\204\344\275\234\346\245\255(amrt250).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\346\272\220\347\266\255\344\277\256\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(amrt300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\345\207\272\345\224\256\347\266\255\350\255\267\344\275\234\346\245\255(afat504).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\345\240\261\345\273\242\347\266\255\350\255\267\344\275\234\346\245\255(afat507).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\345\244\226\351\200\201\346\224\266\345\233\236\347\266\255\350\255\267\344\275\234\346\245\255(afat450).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\345\244\226\351\200\201\347\266\255\350\255\267\344\275\234\346\245\255(afat440).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\346\222\245\345\205\245\347\266\255\350\255\267\344\275\234\346\245\255(afat491).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\346\222\245\345\207\272\347\266\255\350\255\267\344\275\234\346\245\255(afat490).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\346\224\271\350\211\257\347\266\255\350\255\267\344\275\234\346\245\255(afat508).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\350\263\207\346\234\254\345\214\226\347\266\255\350\255\267\344\275\234\346\245\255(afat400).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\351\203\250\351\226\200\350\275\211\347\247\273\347\266\255\350\255\267\344\275\234\346\245\255(afat421).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\351\207\215\344\274\260\347\266\255\350\255\267\344\275\234\346\245\255(afat503).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\263\207\347\224\242\351\212\267\345\270\263\347\266\255\350\255\267\344\275\234\346\245\255(afat506).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\350\275\211\345\270\263\345\202\263\347\245\250\347\266\255\350\255\267\344\275\234\346\245\255(aglt310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\200\200\350\262\250\347\224\263\350\253\213\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aint530).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\200\345\255\230\346\224\266\346\224\257\347\266\255\350\255\267\344\275\234\346\245\255(anmt310).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\267\345\224\256\344\274\260\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt400).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\267\345\224\256\345\203\271\346\240\274\350\241\250\347\224\263\350\253\213\344\275\234\346\245\255(axmt129).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\267\345\224\256\345\220\210\347\264\204\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt440).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\267\345\224\256\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt450).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\267\345\224\256\345\240\261\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt410).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\267\345\224\256\346\240\270\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt420).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\267\345\224\256\350\243\234\345\267\256\347\266\255\350\255\267\344\275\234\346\245\255(aprt602).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt600).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\213\252\344\275\215\345\200\213\345\210\245\350\262\273\347\224\250\346\250\231\346\272\226\347\224\263\350\253\213\344\275\234\346\245\255(astt253).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\213\252\344\275\215\347\224\263\350\253\213\344\275\234\346\245\255(amht205).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\213\252\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt832).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\226\200\345\272\227\350\252\277\346\222\245\347\266\255\350\255\267\344\275\234\346\245\255(aint511).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\226\200\345\272\227\350\263\207\346\272\220\345\215\224\350\255\260\347\224\263\350\253\213\344\275\234\346\245\255(artt230).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\233\206\345\234\230\347\240\224\347\231\274ECN\347\266\255\350\255\267\344\275\234\346\245\255(abmt300).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\233\206\345\234\230\347\240\224\347\231\274\345\244\232\344\270\273\344\273\266ECN\347\266\255\350\255\267\344\275\234\346\245\255(abmt301).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\233\206\345\234\230\347\240\224\347\231\274\347\224\242\345\223\201\347\265\220\346\247\213\347\224\263\350\253\213\344\275\234\346\245\255(abmt200).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\233\234\351\240\205\345\272\253\345\255\230\346\224\266\346\226\231\344\275\234\346\245\255(aint302).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\233\234\351\240\205\345\272\253\345\255\230\347\231\274\346\226\231\344\275\234\346\245\255(aint301).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\233\234\351\240\205\345\276\205\346\212\265\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axrt341).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\240\220\347\247\237\345\215\224\350\255\260\347\266\255\350\255\267\344\275\234\346\245\255(astt812).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\240\220\347\256\227\346\214\252\347\224\250\347\266\255\350\255\267(abgt060).form"`
  - ➕ **新增**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\351\240\220\347\256\227\350\277\275\345\212\240\347\266\255\350\255\267(abgt050).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/ECR\347\224\263\350\253\213\344\275\234\346\245\255(abmt500).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/Invoice\347\266\255\350\255\267\344\275\234\346\245\255(axmt620).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\270\200\350\210\254\345\224\256\345\203\271\350\252\277\345\203\271\344\275\234\346\245\255(aprt112).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\270\200\350\210\254\351\200\262\345\203\271\350\252\277\345\203\271\344\275\234\346\245\255(aprt111).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\270\200\351\232\216\346\256\265\350\252\277\346\222\245\344\275\234\346\245\255(aint330).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\272\244\346\230\223\345\260\215\350\261\241\345\207\206\345\205\245\347\266\255\350\255\267\344\275\234\346\245\255(apmt801).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\272\244\346\230\223\345\260\215\350\261\241\347\224\263\350\253\213\344\275\234\346\245\255(apmt100).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\272\244\346\230\223\345\260\215\350\261\241\350\255\211\347\205\247\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(apmt820).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\345\207\206\345\205\245\345\226\256(apmt800).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\347\224\263\350\253\213\344\275\234\346\245\255(apmt200).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\347\265\220\347\256\227\345\226\256(astt340).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\347\270\276\346\225\210\350\251\225\346\240\270\345\256\232\346\200\247\350\251\225\345\210\206\345\226\256(apmt811).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\347\270\276\346\225\210\350\251\225\346\240\270\347\266\234\345\220\210\345\276\227\345\210\206\350\252\277\346\225\264\345\226\256(apmt814).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\350\262\250\346\254\276\345\260\215\345\270\263\344\275\234\346\245\255(aapt110).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\350\262\273\347\224\250\345\226\256(astt320).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\277\203\351\212\267\350\252\277\345\203\271\344\275\234\346\245\255(aprt113).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\344\277\203\351\212\267\350\253\207\345\210\244\347\224\263\350\253\213(aprt310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\200\237\350\262\250\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt542).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\200\237\350\262\250\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt521).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\200\237\350\262\250\350\250\202\345\226\256(axmt501).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\200\237\350\262\250\351\202\204\351\207\217\345\226\256(axmt591).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\204\237\351\202\204\346\234\254\346\201\257\347\266\255\350\255\267(afmt170).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\205\247\351\203\250\347\265\220\347\256\227\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt740).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\205\266\344\273\226\346\207\211\344\273\230\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt301).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\205\266\344\273\226\346\207\211\346\224\266\345\270\263\346\254\276\347\266\255\350\255\267\344\275\234\346\245\255(axrt330).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\207\272\350\262\250\345\226\256(axmt540).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\207\272\350\262\250\347\260\275\346\224\266\345\226\256(axmt580).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\207\272\350\262\250\347\260\275\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt590).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256(axmt520).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\210\206\351\212\267\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt540).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\210\206\351\212\267\345\207\272\350\262\250\347\260\275\346\224\266\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt580).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\210\206\351\212\267\345\207\272\350\262\250\347\260\275\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt590).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\210\206\351\212\267\345\220\210\347\264\204\347\224\263\350\253\213\344\275\234\346\245\255(astt601).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\210\206\351\212\267\350\250\202\345\226\256(adbt500).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\210\206\351\212\267\350\250\202\345\226\256\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(adbt510).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\210\206\351\212\267\351\212\267\351\200\200\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(adbt600).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\210\270\347\250\256\345\237\272\346\234\254\350\263\207\346\226\231\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(agct300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\214\205\350\243\235\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt610).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\223\201\350\263\252\346\252\242\351\251\227\345\226\256(aqct300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\223\201\350\263\252\347\225\260\345\270\270\347\224\263\350\253\213\344\275\234\346\245\255(aqct310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\225\206\345\223\201\345\207\206\345\205\245\347\224\263\350\253\213\345\226\256(artt300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\226\256\344\270\200\344\270\273\344\273\266ECN(abmt300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\234\260\347\243\205\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt640).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\240\261\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255-\345\226\256\347\255\206\345\274\217(asft335).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\240\261\345\273\242\347\224\263\350\253\213\345\226\256(aint310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\240\264\345\234\260\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(amht204).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\244\226\345\214\257\344\272\244\346\230\223\347\266\255\350\255\267\344\275\234\346\245\255(afmt534).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\244\226\345\214\257\345\220\210\347\264\204\347\266\255\350\255\267\344\275\234\346\245\255(afmt533).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\244\226\345\214\257\346\234\237\346\234\253\345\205\254\345\205\201\345\203\271\345\200\274\347\266\255\350\255\267(afmt552).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\244\232\344\270\273\344\273\266ECN(abmt301).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\244\232\345\273\240\345\225\206\350\253\213\346\254\276\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(\346\265\201\351\200\232)(aapt815).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\345\200\211\351\200\200\345\226\256(apmt581).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\345\205\245\345\272\253\345\226\256(apmt571).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\345\220\210\347\264\204\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt481).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt491).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255(apmt521).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\347\266\255\350\255\267\344\275\234\346\245\255(apmt501).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\350\251\242\345\203\271\344\275\234\346\245\255(apmt421).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\351\251\227\351\200\200\344\275\234\346\245\255(apmt561).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\240\270\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt441).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\256\232\345\255\230\347\266\255\350\255\267\344\275\234\346\245\255(afmt531).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\256\242\346\210\266\345\207\206\345\205\245\345\217\212\350\256\212\346\233\264\344\275\234\346\245\255(axmt800).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\256\242\346\210\266\347\224\263\350\253\213\344\275\234\346\245\255(axmt200).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\256\242\346\210\266\350\262\250\346\254\276\345\260\215\345\270\263\344\275\234\346\245\255(aist310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\256\242\350\250\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt700).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\257\246\345\234\260\347\233\244\351\273\236\350\250\210\347\225\253\347\266\255\350\255\267\344\275\234\346\245\255(aint820).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\260\210\346\253\203\345\220\210\347\264\204\347\225\260\345\213\225\347\224\263\350\253\213(astt401).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\260\210\346\253\203\346\226\260\345\225\206\345\223\201\345\274\225\351\200\262\347\266\255\350\255\267\344\275\234\346\245\255(artt407).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\344\270\200\350\210\254\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft323).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\345\200\222\346\211\243\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft324).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\345\200\222\346\211\243\351\240\230\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft314).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\345\234\250\350\243\275\344\270\213\351\232\216\346\226\231\345\240\261\345\273\242\344\275\234\346\245\255(asft339).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\344\275\234\346\245\255(asft340).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\346\210\220\345\245\227\347\231\274\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft311).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\346\210\220\345\245\227\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft321).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\346\254\240\346\226\231\350\243\234\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft313).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\347\225\266\347\253\231\345\240\261\345\273\242\344\275\234\346\245\255(asft336).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(asft300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\243\275\347\250\213\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(asft801).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\243\275\347\250\213\351\207\215\345\267\245\350\275\211\345\207\272\344\275\234\346\245\255(asft338).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\256\212\346\233\264(asft800).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\266\205\351\240\230\347\266\255\350\255\267\344\275\234\346\245\255(asft312).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\267\245\345\226\256\350\266\205\351\240\230\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft322).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\270\202\345\240\264\346\216\250\345\273\243\346\264\273\345\213\225\346\240\270\351\212\267\345\226\256(astt605).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\270\202\345\240\264\346\216\250\345\273\243\346\264\273\345\213\225\347\224\263\350\253\213\345\226\256(astt604).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\270\263\351\275\241\345\217\212\345\243\236\345\270\263\346\217\220\345\210\227\347\266\255\350\255\267(axrt940).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\272\224\346\224\266\347\245\250\346\215\256\346\224\266\347\245\250\344\275\234\344\270\232(anmt510).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\272\253\344\275\215\345\217\226\346\266\210\347\225\231\347\275\256\347\266\255\350\255\267\344\275\234\346\245\255(aint161).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\272\253\345\255\230\345\240\261\345\273\242\351\231\244\345\270\263(aint311).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\272\253\345\255\230\346\234\211\346\225\210\346\234\237\350\256\212\346\233\264\344\275\234\346\245\255(aint180).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\272\253\345\255\230\347\225\260\345\270\270\350\256\212\346\233\264\344\275\234\346\245\255(aint170).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\275\210\346\200\247\346\216\241\350\263\274\345\203\271\346\240\274\347\224\263\350\253\213\344\275\234\346\245\255(apmt128).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\345\275\210\346\200\247\351\212\267\345\224\256\345\203\271\346\240\274\347\224\263\350\253\213\344\275\234\346\245\255(axmt128).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\204\217\345\220\221\345\215\224\350\255\260\347\266\255\350\255\267\344\275\234\346\245\255(astt811).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\207\211\344\273\230\345\214\257\346\254\276\347\225\260\345\213\225\344\275\234\346\245\255(anmt480).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\207\211\344\273\230\345\214\257\346\254\276\351\226\213\347\253\213\344\275\234\346\245\255(anmt460).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\207\211\344\273\230\345\270\263\346\254\276\346\206\221\345\226\256(aapt300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\207\211\344\273\230\345\276\205\346\212\265\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt340).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\207\211\344\273\230\346\240\270\351\212\267\345\226\256(aapt420).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\207\211\346\224\266\345\270\263\346\254\276\346\206\221\345\226\256(axrt300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\207\211\346\224\266\345\276\205\346\212\265\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axrt340).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\207\211\346\224\266\346\262\226\351\212\267\346\206\221\350\255\211(axrt400).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\212\225\350\263\207\347\220\206\350\262\241\347\266\255\350\255\267\344\275\234\346\245\255(afmt532).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\212\225\350\263\207\347\224\263\350\253\213\345\226\256(afmt510).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\212\225\350\263\207\350\263\274\350\262\267\345\270\263\345\213\231\345\226\256(afmt535).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\345\273\266\346\234\237\350\256\212\346\233\264\347\224\263\350\253\213\344\275\234\346\245\255(astt803).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\347\225\260\345\213\225\347\224\263\350\253\213\344\275\234\346\245\255(astt801).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\347\265\202\346\255\242\347\224\263\350\253\213\344\275\234\346\245\255(astt805).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\350\262\273\347\224\250\345\204\252\346\203\240\347\224\263\350\253\213(astt802).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\350\262\273\347\224\250\346\250\231\346\272\226\350\256\212\346\233\264\344\275\234\346\245\255(astt806).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\351\235\242\347\251\215\350\256\212\346\233\264\347\224\263\350\253\213(astt804).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\200\211\351\200\200\345\226\256(apmt580).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\200\211\351\200\200\345\226\256(apmt890).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\203\271\346\240\274\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(apmt129).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\205\245\345\272\253\345\226\256(apmt570).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\205\245\345\272\253\345\226\256(apmt880).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\220\210\347\264\204\345\226\256(apmt480).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt490).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\226\256(apmt500).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\226\256(apmt840).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256(apmt862).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt530).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256(apmt520).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256(apmt860).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\350\243\234\345\267\256\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aprt601).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256(apmt510).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt850).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\351\240\220\344\273\230\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\351\251\227\351\200\200\345\226\256(apmt560).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\216\241\350\263\274\351\251\227\351\200\200\345\226\256(apmt870).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\224\257\345\207\272\351\241\236\345\220\210\345\220\214\347\225\260\345\213\225\347\224\263\350\253\213\344\275\234\346\245\255(astt820).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\226\231\344\273\266\346\211\277\350\252\215\347\224\263\350\253\213\344\275\234\346\245\255(abmt400).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\226\231\344\273\266\347\224\263\350\253\213\347\266\255\350\255\267\344\275\234\346\245\255(aimt300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\226\231\344\273\266\350\243\275\347\250\213\350\263\207\346\226\231\346\226\260\345\242\236\343\200\201\344\277\256\346\224\271\347\224\263\350\253\213\344\275\234\346\245\255(aect801).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\232\253\344\274\260\346\207\211\346\224\266\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axrt320).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\234\203\345\223\241\345\215\241\345\204\262\345\200\274\351\207\221\351\241\215\350\252\277\346\225\264\344\275\234\346\245\255(ammt428).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\234\203\345\223\241\345\215\241\347\250\256\347\224\263\350\253\213\345\226\256(ammt320).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\234\203\345\223\241\345\215\241\347\251\215\351\273\236\350\252\277\346\225\264\347\266\255\350\255\267\344\275\234\346\245\255(ammt421).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\234\237\346\234\253\345\205\254\345\205\201\345\203\271\345\200\274\347\266\255\350\255\267(afmt551).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\240\270\345\203\271\345\226\256(apmt440).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\346\264\276\350\273\212\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt630).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\204\241\346\216\241\350\263\274\345\205\245\345\272\253\345\226\256(apmt863).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt532).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256(apmt861).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt522).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\204\241\350\250\202\345\226\256\345\207\272\350\262\250\347\266\255\350\255\267\344\275\234\346\245\255(axmt541).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\207\237\351\201\213\346\223\232\351\273\236ECN\347\266\255\350\255\267\344\275\234\346\245\255(abmt310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\207\237\351\201\213\346\223\232\351\273\236\345\244\232\344\270\273\344\273\266ECN\347\266\255\350\255\267\344\275\234\346\245\255(abmt311).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\215\250\347\253\213\351\234\200\346\261\202\347\266\255\350\255\267\344\275\234\346\245\255(apst300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\224\237\351\256\256\345\203\271\346\240\274\350\252\277\346\225\264\344\275\234\346\245\255(aprt121).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\225\266\347\253\231\344\270\213\347\267\232\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(asft337).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\231\274\347\245\250\350\253\213\346\254\276\345\226\256(aapt415).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\247\237\350\263\203\350\262\273\347\224\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(astt810).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\266\223\351\212\267\345\225\206\344\273\243\345\242\212\350\262\273\347\224\250\345\240\261\351\212\267\347\266\255\350\255\267\344\275\234\346\245\255(astt606).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\266\223\351\212\267\345\225\206\347\265\220\347\256\227\345\226\256(astt640).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\347\266\223\351\212\267\345\225\206\350\262\273\347\224\250\345\226\256(astt620).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\207\252\346\234\211\346\226\260\345\225\206\345\223\201\345\274\225\351\200\262\345\226\256(artt406).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\207\252\347\207\237\345\220\210\347\264\204\347\225\260\345\213\225\347\224\263\350\253\213\345\226\256(astt301).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\207\252\347\207\237\346\226\260\347\224\242\345\223\201\345\274\225\351\200\262(artt405).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\236\215\350\263\207\345\220\210\345\220\214\347\266\255\350\255\267(afmt035).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\236\215\350\263\207\347\224\263\350\253\213\345\226\256(afmt015).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\236\215\350\263\207\350\263\207\351\207\221\345\210\260\345\270\263\347\266\255\350\255\267(afmt140).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\246\201\350\262\250\345\226\256(apmt830).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\246\201\350\262\250\345\226\256\350\256\212\346\233\264\347\266\255\350\255\267\344\275\234\346\245\255(apmt835).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\250\202\345\226\256(axmt500).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\250\202\345\226\256\350\256\212\346\233\264(axmt510).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\250\202\351\207\221\351\240\220\346\224\266\347\266\255\350\255\267\344\275\234\346\245\255(axrt310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\251\242\345\203\271\345\226\256(apmt420).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\252\277\346\222\245\345\267\256\347\225\260\350\252\277\346\225\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aint520).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\252\277\346\222\245\347\224\263\350\253\213\345\226\256(aint320).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\253\213\350\263\274\345\226\256(apmt400).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\253\213\350\263\274\350\256\212\346\233\264\345\226\256(apmt410).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\262\273\347\224\250\345\240\261\346\224\257\345\226\256(aapt330).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\262\273\347\224\250\346\250\231\346\272\226\350\250\255\345\256\232\347\224\263\350\253\213\344\275\234\346\245\255(astt252).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\262\273\347\224\250\351\240\220\346\224\257\347\224\263\350\253\213\345\226\256(aapt331).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\346\272\220\346\255\270\351\202\204\344\275\234\346\245\255(amrt250).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\346\272\220\347\266\255\344\277\256\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(amrt300).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\347\224\242\345\207\272\345\224\256\347\266\255\350\255\267\344\275\234\346\245\255(afat504).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\347\224\242\345\240\261\345\273\242\347\266\255\350\255\267\344\275\234\346\245\255(afat507).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\347\224\242\345\244\226\351\200\201\346\224\266\345\233\236\347\266\255\350\255\267\344\275\234\346\245\255(afat450).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\347\224\242\345\244\226\351\200\201\347\266\255\350\255\267\344\275\234\346\245\255(afat440).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\347\224\242\346\224\271\350\211\257\347\266\255\350\255\267\344\275\234\346\245\255(afat508).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\347\224\242\351\203\250\351\226\200\350\275\211\347\247\273\347\266\255\350\255\267\344\275\234\346\245\255(afat421).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\347\224\242\351\207\215\344\274\260\347\266\255\350\255\267\344\275\234\346\245\255(afat503).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\263\207\347\224\242\351\212\267\345\270\263\347\266\255\350\255\267\344\275\234\346\245\255(afat506).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\350\275\211\345\270\263\345\202\263\347\245\250(aglt310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\200\200\350\262\250\347\224\263\350\253\213\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aint530).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\200\345\255\230\346\224\266\346\224\257\347\266\255\350\255\267\344\275\234\346\245\255(anmt310).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\267\345\224\256\344\274\260\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt400).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\203\271\346\240\274\350\241\250\347\224\263\350\253\213\344\275\234\346\245\255(axmt129).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\220\210\347\264\204\345\226\256(axmt440).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt450).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\240\261\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt410).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\267\345\224\256\346\240\270\345\203\271\345\226\256(axmt420).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\267\345\224\256\350\243\234\345\267\256\347\266\255\350\255\267\344\275\234\346\245\255(aprt602).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\212\267\351\200\200\345\226\256(axmt600).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\213\252\344\275\215\345\200\213\345\210\245\350\262\273\347\224\250\346\250\231\346\272\226\347\224\263\350\253\213\344\275\234\346\245\255(astt253).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\213\252\344\275\215\347\224\263\350\253\213\344\275\234\346\245\255(amht205).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\213\252\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt832).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\226\200\345\272\227\350\252\277\346\222\245\347\266\255\350\255\267\344\275\234\346\245\255(aint511).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\226\200\345\272\227\350\263\207\346\272\220\345\215\224\350\255\260\347\224\263\350\253\213\344\275\234\346\245\255(artt230).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\233\206\345\234\230\347\240\224\347\231\274\347\224\242\345\223\201\347\265\220\346\247\213\347\224\263\350\253\213\344\275\234\346\245\255(abmt200).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\233\234\351\240\205\345\272\253\345\255\230\346\224\266\346\226\231\345\226\256(aint302).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\233\234\351\240\205\345\272\253\345\255\230\347\231\274\346\226\231\345\226\256(aint301).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\233\234\351\240\205\345\276\205\346\212\265\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axrt341).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\240\220\347\247\237\345\215\224\350\255\260\347\266\255\350\255\267\344\275\234\346\245\255(astt812).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\240\220\347\256\227\346\214\252\347\224\250\347\266\255\350\255\267(abgt060).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@t100/form-default/\351\240\220\347\256\227\350\277\275\345\212\240\347\266\255\350\255\267(abgt050).form"`

### 245. [Web] Q00-20240428001 開窗資料條件財產名稱輸入[PL3/雙驅動改造]，資料帶回gird，儲存草稿/儲存表單後gird資料顯示異常问题修正
- **Commit ID**: `be4d7fd56aa320e217b51f60f014b3183ba480ed`
- **作者**: 刘旭
- **日期**: 2024-04-28 14:22:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 246. [Web] Q00-20240426002 修正主管首頁待辦處理量OracleDB異常
- **Commit ID**: `72c7bd8d7f910acb34f068ec01fb240c8e83fb3c`
- **作者**: 邱郁晏
- **日期**: 2024-04-26 11:03:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 247. [Web]Q00-20240426003 修正栏位过多，设置栏位宽度没效果的问题
- **Commit ID**: `33e0c2e3a126aa13c15561d5820e2912344904fc`
- **作者**: 周权
- **日期**: 2024-04-26 10:35:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 248. [PLM]Q00-20240426001 調整PLM歷程接口返回內容
- **Commit ID**: `5fdeff153489429f56fc79f2a2f9f8f3a50dcebe`
- **作者**: 林致帆
- **日期**: 2024-04-26 08:56:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WebServiceUtil.java`

### 249. [Web]Q00-20240425005 修正加簽關卡選取經常對像在吳資料的狀況下會顯示錯誤頁面
- **Commit ID**: `43aeeced1c07119e3057123dba6009a8f9a4e070`
- **作者**: 林致帆
- **日期**: 2024-04-26 08:21:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`

### 250. [Web] Q00-20240425004 修正絕對位置表單，調整表單大小導致ScrollBar異常增加問題
- **Commit ID**: `a40de83958af818ddb88ad8fde8a46b37b2911fa`
- **作者**: 邱郁晏
- **日期**: 2024-04-25 17:48:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`

### 251. [流程引擎]Q00-20240425003 修正核決關卡設置前置關係人造成前置關係人關卡無法繼續簽核
- **Commit ID**: `bc945e289a07fb114a50c70f4f816105f75ccc7a`
- **作者**: 林致帆
- **日期**: 2024-04-25 15:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 252. [Web]Q00-20240425002 修正开启绝对位置表单偶发报错TypeError: Cannot read properties of undefined (reading 'ElementGroup')的问题
- **Commit ID**: `dc3a154974c0ff72cef4ffcb0295f6e68e0133c2`
- **作者**: 周权
- **日期**: 2024-04-25 15:32:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 253. [TIPTOP]Q00-20240425001 調整TIPTOP接口呼叫封存的PORT號改成取流程主機設定
- **Commit ID**: `0dff4ab797dc9e38ff873114823223b05e7d6d1c`
- **作者**: 林致帆
- **日期**: 2024-04-25 09:09:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 254. [内部]Q00-20240423002 登陸頁面新增個資安全宣告，需通過系統設定personal.data.protection.web開啓
- **Commit ID**: `71c59f927fbfd5b69b18c47d428aec9f61c977d5`
- **作者**: 周权
- **日期**: 2024-04-24 15:39:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 255. [PRODT]Q00-20240424002 修正Web流程設計師中發起權限設定屬性的職務資料在編輯狀態後儲存會遺失問題
- **Commit ID**: `026978aef6b8e0c09813692be7827cdb7c1d295a`
- **作者**: yamiyeh10
- **日期**: 2024-04-24 14:08:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 256. [TIPTOP]Q00-20240424001 修正流程封存邏輯影響到TIPTOP流程結案異常
- **Commit ID**: `953944407e12fe3016d1168725c8a614249fc097`
- **作者**: 林致帆
- **日期**: 2024-04-24 11:15:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 257. [Web]Q00-20240423004 在觸發排程Trigger加入睡眠機制，以避免排程執行過快導致重複觸發狀況
- **Commit ID**: `6c0b4f5c331298e95b4932e8e84746858c16269c`
- **作者**: 邱郁晏
- **日期**: 2024-04-23 15:15:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/schedule/SystematicJob.java`

### 258. [Web] Q00-20240423001 客户5521版到5894 將流程從XPDL轉BPMN會失敗问题修正
- **Commit ID**: `f5d58f926a3b4632d2ecf505b69c29d8e3c62867`
- **作者**: 刘旭
- **日期**: 2024-04-23 09:14:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`

### 259. [文件智能家] 调整新增长知识根目录可以问问题
- **Commit ID**: `228f51518bdd6b80f577bcd81d42cb23553976a6`
- **作者**: 周权
- **日期**: 2024-08-27 13:16:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileKnowledgeCategoryDaoImpl.java`

### 260. [資安]Q00-20240827002 修正登入頁面的UserId欄位存在SQL injection的風險
- **Commit ID**: `fec86c3e198f34418c22626ce62442a22971177d`
- **作者**: 張詠威
- **日期**: 2024-08-27 11:43:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 261. [E10、回收謝慧功能]BPM用户设置签名图档时，同步给E10(補)
- **Commit ID**: `dbce0f04785eac971d26440143fbb56ee388ae2b`
- **作者**: lorenchang
- **日期**: 2024-08-27 10:00:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_Oracle.sql`

### 262. [BPM APP、回收謝慧功能]工作转派同步至钉钉待办中
- **Commit ID**: `60d24b7cee416d97a7ea818aeec22e131ec5fc5e`
- **作者**: xiehui
- **日期**: 2024-08-20 17:56:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 263. [E10、回收謝慧功能]BPM用户设置签名图档时，同步给E10
- **Commit ID**: `022c35c64ed35c0da01fae213187226837d56afa`
- **作者**: xiehui
- **日期**: 2024-08-20 17:11:38
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/E10UserImageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/NewE10UserImageSync.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/NewE10UserImageSyncBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/IgnoreFilterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`

### 264. [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息
- **Commit ID**: `9a9e60830112c24cd7d66ce59fc5658bbfc1386e`
- **作者**: kmin
- **日期**: 2024-08-23 14:50:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`

### 265. [流程引擎]C01-20240820003 修正流程解析異常時，未啟用系統郵件通知也會觸發寄送Email給管理員
- **Commit ID**: `2b569a0f1d33179b712c96e612080be0f7f776e7`
- **作者**: lorenchang
- **日期**: 2024-08-22 16:12:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 266. [Web表單設計師]C01-20240819004 修正編輯多語系顯示存入html格式與預設值不符
- **Commit ID**: `91685c94e31c65982ab29e7c306d4a49b18be17c`
- **作者**: kmin
- **日期**: 2024-08-21 15:03:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 267. [行業表單庫]新增範例表單檔案
- **Commit ID**: `78e93f67bc011c71609c7b8741ff26fabf9265a5`
- **作者**: yamiyeh10
- **日期**: 2024-08-21 14:35:27
- **變更檔案數量**: 118
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\270\215\350\211\257\345\223\201\346\227\245\345\240\261\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\270\215\350\211\257\345\223\201\351\200\200\345\272\253\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\272\272\345\223\241\350\252\215\350\255\211\350\246\217\347\257\204\350\250\223\347\267\264\350\252\277\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\343\200\201\345\244\226\345\214\205\345\273\240\346\224\271\345\226\204\346\216\252\346\226\275\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\345\223\201\350\263\252\344\270\215\350\211\257\350\201\257\347\265\241\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\345\223\201\350\263\252\344\277\235\350\255\211\345\210\266\345\272\246\350\251\225\351\221\221\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\345\271\264\345\272\246\347\250\275\346\240\270\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\350\251\225\351\221\221\347\250\275\346\240\270\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\350\251\225\351\221\221\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\276\233\346\207\211\345\225\206\350\263\207\346\226\231\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\200\213\344\272\272\350\252\215\350\255\211\350\250\230\351\214\204.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\215\346\240\241\346\252\242\346\270\254\350\250\255\345\202\231.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\345\223\201\350\263\252\347\250\275\346\240\270\346\237\245\346\240\270\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\345\223\201\350\263\252\347\250\275\346\240\270\347\237\257\346\255\243\346\216\252\346\226\275\347\256\241\345\210\266\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\345\223\201\350\263\252\347\250\275\346\240\270\347\274\272\351\273\236\345\240\261\345\221\212\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\345\223\201\350\263\252\347\250\275\346\240\270\350\250\210\345\212\203.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\205\247\351\203\250\351\200\243\347\265\241\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\206\215\350\252\215\351\200\232\347\237\245\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\210\206\347\231\274\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\215\212\346\210\220\345\223\201\345\244\226\350\247\200\346\252\242\351\251\227\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\215\224\345\212\233\345\273\240\345\225\206\345\237\272\346\234\254\350\263\207\346\226\231\350\252\277\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\215\224\345\212\233\345\273\240\345\225\206\347\222\260\345\242\203\347\256\241\347\220\206\350\246\217\345\212\203\345\225\217\345\215\267\350\252\277\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\216\237\347\211\251\346\226\231\347\222\260\345\242\203\350\200\203\351\207\217\351\235\242\351\221\221\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\216\237\347\211\251\346\226\231\351\200\262\346\226\231\346\252\242\351\251\227\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\220\210\347\264\204(\350\250\202\345\226\256)\350\256\212\346\233\264\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\345\217\257\351\235\240\345\272\246\347\233\256\346\250\231\351\240\220\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\347\225\260\345\270\270\350\201\257\347\265\241\345\226\256(\345\223\201\350\263\252\347\256\241\347\220\206).formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\347\225\260\345\270\270\350\201\257\347\265\241\345\226\256\347\256\241\345\210\266\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\347\225\260\345\270\270\351\200\232\347\237\245\345\226\256(\345\223\201\347\256\241\351\200\262\346\226\231\346\252\242\351\251\227).formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\201\350\263\252\347\233\256\346\250\231\347\256\241\345\210\266\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\241\345\267\245\345\217\227\350\250\223\346\234\215\345\213\231\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\241\345\267\245\346\225\231\350\202\262\350\250\223\347\267\264\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\223\241\345\267\245\350\252\215\350\255\211\347\250\213\345\272\217\345\226\256.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\345\234\213\345\244\226\345\207\272\345\267\256\350\250\274\347\205\247\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\240\261\345\273\242\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\244\226\345\214\205\345\273\240\347\270\276\346\225\210\350\251\225\346\257\224.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\244\226\351\203\250\346\225\231\350\202\262\350\250\223\347\267\264\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\255\230\350\262\250\350\252\277\346\225\264\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\344\276\206\346\250\243\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\350\262\241\347\224\242\347\256\241\345\210\266\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\351\200\201\346\250\243\347\224\263\350\253\213\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\351\221\221\345\210\245\347\264\200\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\345\257\251\346\237\245\345\240\261\345\221\212.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\345\257\251\346\237\245\351\240\205\347\233\256\346\270\205\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\267\241\346\252\242\347\274\272\345\244\261\347\237\257\346\255\243\346\216\252\346\226\275\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\267\245\347\250\213\350\251\225\344\274\260\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\267\245\347\250\213\350\256\212\346\233\264\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\267\245\347\250\213\351\203\250\345\260\210\346\241\210\351\200\262\345\272\246\351\200\261\345\240\261\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\271\264\345\272\246\346\225\231\350\202\262\350\250\223\347\267\264\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\205\247\350\243\275\344\273\244\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\344\277\235\351\244\212\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\345\247\224\345\244\226\344\277\235\351\244\212\347\266\255\344\277\256\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\345\267\245\347\250\213\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\346\224\271\345\226\204\345\260\215\347\255\226\350\251\225\344\274\260\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\347\225\260\345\270\270\346\224\271\345\226\204\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\350\250\255\345\202\231\347\256\241\347\220\206\345\215\241.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\273\240\345\213\231\350\250\255\346\226\275\351\234\200\346\261\202\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\210\220\345\223\201\345\205\245\345\272\253\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\210\220\345\223\201\345\207\272\350\262\250\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\211\277\350\252\215\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\217\220\346\241\210\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\225\231\350\202\262\350\250\223\347\267\264\345\277\203\345\276\227\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\207\344\273\266\344\277\256\345\273\242\347\224\263\350\253\213\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\207\344\273\266\346\224\266\347\231\274\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\260\344\272\272\346\225\231\345\255\270\345\233\236\351\245\213\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\260\347\224\242\345\223\201\350\250\255\350\250\210\351\226\213\347\231\274\347\224\263\350\253\213\350\274\270\345\205\245\345\257\251\346\237\245\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\346\226\260\351\200\262\344\272\272\345\223\241\346\225\231\350\202\262\350\250\223\347\267\264\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\251\237\345\231\250\350\250\255\345\202\231\345\256\232\346\234\237\344\277\235\351\244\212\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\251\237\345\231\250\350\250\255\345\202\231\347\256\241\347\220\206\345\270\263\345\215\241.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\346\270\254\350\250\255\345\202\231\346\240\241\351\251\227\345\240\261\345\221\212\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\346\270\254\350\250\255\345\202\231\346\240\241\351\251\227\345\271\264\345\272\246\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\346\270\254\350\250\255\345\202\231\347\256\241\347\220\206\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\252\242\346\270\254\350\250\255\345\202\231\350\263\274\345\205\245\347\224\263\350\253\213\345\226\256.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\346\270\254\350\251\246\350\250\210\347\225\253\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\272\253\345\272\246\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\272\253\346\277\225\345\272\246\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\211\251\346\226\231\350\231\225\347\275\256\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\211\271\346\216\241\345\257\251\346\237\245\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\222\260\344\277\235\346\263\225\350\246\217\351\221\221\345\256\232\347\231\273\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\222\260\345\242\203\350\200\203\351\207\217\351\235\242\347\231\273\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\222\260\345\242\203\350\200\203\351\207\217\351\235\242\351\221\221\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\222\260\345\242\203\350\210\207\350\201\267\346\245\255\345\256\211\345\205\250\350\241\233\347\224\237\347\233\256\346\250\231\347\256\241\347\220\206\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\224\237\347\224\242\350\250\210\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\233\264\346\216\245\344\272\272\345\223\241\350\252\215\350\255\211\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\240\224\347\231\274\345\260\210\346\241\210\350\250\230\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\347\250\275\346\240\270\347\264\200\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\347\250\275\346\240\270\350\250\210\347\225\253\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\253\213\346\241\210\345\257\251\346\240\270-\347\265\220\346\241\210\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\266\255\344\277\256\350\250\230\351\214\204\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\201\267\346\245\255\345\256\211\345\205\250\350\241\233\347\224\237\346\263\225\350\246\217\351\221\221\345\256\232\347\231\273\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\350\243\275\347\250\213\345\267\241\346\252\242\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\202\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\202\350\263\274\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\223\347\267\264\345\223\241\346\264\245\350\262\274\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\255\350\250\210\345\257\251\346\240\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\255\350\250\210\350\274\270\345\207\272\345\257\251\346\237\245\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\255\350\250\210\351\234\200\346\261\202\350\256\212\346\233\264\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\255\350\250\210\351\251\227\350\255\211\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\252\262\345\276\214\350\241\214\345\213\225\346\226\271\346\241\210\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\252\262\347\250\213\350\250\223\347\267\264\347\260\275\345\210\260\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\344\275\234\346\245\255\347\224\263\350\253\213\345\226\256.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\345\256\211\345\205\250\347\233\256\346\250\231\350\250\255\345\256\232\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\347\263\273\347\265\261\345\217\212\350\250\255\345\202\231\347\266\255\350\255\267\347\264\200\351\214\204\350\241\250.formrepository"`
  - 📝 **修改**: `"Release/copyfiles/@base/form-repository/\350\276\246\345\205\254\347\224\250\345\223\201\345\205\245\345\272\253\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\200\200\350\262\250\345\240\261\345\273\242\345\220\214\346\204\217\346\233\270.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\200\262\346\226\231\346\252\242\351\251\227\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\200\262\350\262\250\351\251\227\346\224\266\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\207\215\345\244\247\347\222\260\345\242\203\350\200\203\351\207\217\351\235\242\350\246\217\345\212\203\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\207\217\347\224\242\347\231\274\344\275\210\351\200\232\347\237\245.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\226\223\346\216\245\350\200\203\351\207\217\351\235\242\347\222\260\345\242\203\350\241\235\346\223\212\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\240\220\351\230\262\346\216\252\346\226\275\350\231\225\347\275\256\345\240\261\345\221\212.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\240\230\346\226\231\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\246\226\344\273\266\345\267\241\350\277\264\346\252\242\351\251\227\350\250\230\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DML_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DML_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 268. [流程引擎]C01-20240815002 修正用戶登入授權數不足時，未啟用系統郵件通知也會觸發寄送Email給管理員，改為依照啟用設定通知及增加Log記錄
- **Commit ID**: `7273a7390261d9d94c8a138800dc6fd37d7fe314`
- **作者**: lorenchang
- **日期**: 2024-08-19 16:04:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 269. [BPM APP]C01-20240816007 修正頁籤元件內的頁籤代號有相似時後端取內容會發生錯亂問題
- **Commit ID**: `0af25afdb4e89ec8d98ca1ff2c95e6cfd916f287`
- **作者**: yamiyeh10
- **日期**: 2024-08-19 15:26:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 270. [Web]C01-20240815003 修正SQL註冊器的查詢條件開始包含跟結束包含的查詢結果相反
- **Commit ID**: `8e46cf0498e44db32506e12f0a015e4455e9aeee`
- **作者**: kmin
- **日期**: 2024-08-19 09:03:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`

### 271. [Web]C01-20240814001 修正增加Grid若綁定的元件是需要key：「元件ID_txt」的判斷防止必填檢查功能失效
- **Commit ID**: `eba8b52811deaa41a84083e8043a28e6bf9acec6`
- **作者**: kmin
- **日期**: 2024-08-16 10:40:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 272. [T100]Q00-20240815001 優化T100拋單若流程第一關非流程發起者用warn訊息警告
- **Commit ID**: `a160fe4237fa73c6959ea9cfd03e1299c90f121a`
- **作者**: kmin
- **日期**: 2024-08-15 16:52:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 273. [ISO]C01-20240813003 調整安裝光碟ISO文件調閱申請流程，因流程ApplyEmp關卡閘道未對稱，導致流程簽出後無法再次簽入
- **Commit ID**: `384e6def7670e26fbb9975b6d35c2ce30f5a416b`
- **作者**: 張詠威
- **日期**: 2024-08-14 14:52:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"Release/copyfiles/@iso/default-process/ISO\346\226\207\344\273\266\350\252\277\351\226\261\347\224\263\350\253\213.bpmn"`

### 274. [T100]Q00-20240814001 修正T100錯誤訊息判讀多語系時異常問題
- **Commit ID**: `178202925138046edb28d5daaf0ca391e0df79f6`
- **作者**: kmin
- **日期**: 2024-08-14 09:47:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/AbstractNewTiptopMethod.java`

### 275. [Web]C01-20240808001 修正formValidation.validateGrid邏輯異常
- **Commit ID**: `edc32f54c79c18c945aff4eddb7a30c8c592e534`
- **作者**: kmin
- **日期**: 2024-08-13 11:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 276. [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題
- **Commit ID**: `4fd33ab3ecd6bd4e9eb617533b8175ff7676ac2d`
- **作者**: kmin
- **日期**: 2024-08-12 11:48:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 277. [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題[补]
- **Commit ID**: `adef6b4c43da164273c040880cc5e5b80cd70f67`
- **作者**: 周权
- **日期**: 2024-08-12 17:25:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 278. [流程引擎]C01-20240704001 修正系統排程AutoRegetWorkAssignmentHandler未能正常取回代理轉派的關卡
- **Commit ID**: `a56ad34954d3b7fa82a40d3fe65e2805ce002c98`
- **作者**: 張詠威
- **日期**: 2024-08-12 14:28:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 279. [流程引擎]A00-20240730001 TT向BPM溝通取得的「可撤銷清單」功能，修正出現多筆重覆資料問題。
- **Commit ID**: `27f8f05f37dce0192d2cc95f19c65e1c8f746db9`
- **作者**: wencheng1208
- **日期**: 2024-08-12 11:18:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 280. [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙因素認證直接登入的異常
- **Commit ID**: `14d43b8171934b2c163477b32b7a411730fc11d3`
- **作者**: lorenchang
- **日期**: 2024-08-09 17:34:25
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 281. [內部]Q00-20240808001 優化TIPTOP(WF)拋單處理附件時異常的錯誤訊息
- **Commit ID**: `c7a9a02a880dfbc37121bc34965d201354d79c6f`
- **作者**: kmin
- **日期**: 2024-08-08 17:25:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 282. [Web]C01-20240807007 Grid 增加支持 Html 標籤 <p>
- **Commit ID**: `3199a5691296ffb9d9b7e2a64e8689b11ece3280`
- **作者**: lorenchang
- **日期**: 2024-08-07 16:00:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 283. [組織同步] C01-20240729002 當產品組織與中介組織的部門關聯整體資料不一致時才可增加更新記錄
- **Commit ID**: `8b1720b6fd99104141097cf6e556d0ece3388282`
- **作者**: wencheng1208
- **日期**: 2024-08-08 14:37:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`

### 284. [BPM APP]Q00-20240807002 修正移動端已轉派列表有資料時會卡在loading畫面問題
- **Commit ID**: `12a5520813f3b14f2fc380975009c3af2ca7673c`
- **作者**: yamiyeh10
- **日期**: 2024-08-07 17:36:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 285. [BPM APP]C01-20240807002 調整行動端的主旨支持換行符號
- **Commit ID**: `33eea8e5277860334c1b96e493d1ed7ea944c29c`
- **作者**: yamiyeh10
- **日期**: 2024-08-07 17:18:07
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileResigendServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 286. [Web]Q00-20240718001 修正部分流程的簽核歷程資訊未能顯示通知關卡的異常
- **Commit ID**: `79ca0ae98873c7c434a231ddbb021c5c961197ec`
- **作者**: 張詠威
- **日期**: 2024-08-07 17:16:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 287. C01-20240806003 修正 XPDL 流程核決層級名稱變成「Decision Lv1.」的異常
- **Commit ID**: `3a507307ebbc950033f4e2cfeef55f8a9bc9a29b`
- **作者**: lorenchang
- **日期**: 2024-08-07 16:00:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 288. [內部]Q00-*********** 調整企業微信推播邏輯並新增錯誤log訊息
- **Commit ID**: `1d32dcce88ca783731c4aeb530e5772771625f19`
- **作者**: yamiyeh10
- **日期**: 2024-08-07 11:35:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 289. [內部]修正 Update SQL 存在非法字元，導致版更工具在特定環境執行 SQL 時出現異常
- **Commit ID**: `562bcc7f69976786f772427d185dcc1a3a53cb08`
- **作者**: lorenchang
- **日期**: 2024-08-06 14:56:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`

### 290. [流程引擎]C01-20240715002 修正 RESTful 轉存表單出現 NullPointerException 的異常
- **Commit ID**: `48dae4e14920aa818b84ffdf40631ff96cc5430c`
- **作者**: lorenchang
- **日期**: 2024-08-06 13:32:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java`

### 291. [內部]Q00-20240806001 優化BpmProcessWorkbox log訊息方便排除日常問題
- **Commit ID**: `c6aa9f5c9bb3fcafac2df0df07a55ddbec7b5fe6`
- **作者**: kmin
- **日期**: 2024-08-06 10:49:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 292. [流程引擎]C01-20240801004 修正监控流程中删除流程逻辑
- **Commit ID**: `3c9cd3fd197514835583a2e734156517846f2bcb`
- **作者**: 周权
- **日期**: 2024-08-05 10:02:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 293. [Web]C01-20240731001 修正同瀏覽器先後登入兩位使用者，都會被登出的問題
- **Commit ID**: `f0f9b45e29b03aae217ab3567ca91390cb62e703`
- **作者**: 周权
- **日期**: 2024-08-02 15:25:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 294. [Web]C01-20240729003 修正切換頁籤Grid寬度異常問題
- **Commit ID**: `1ec3acb08ca18a235e08e06bcecca19b8db81960`
- **作者**: 周权
- **日期**: 2024-08-01 17:53:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/resources/html/RwdSubTabTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 295. [內部]Q00-20240731001 優化若角色為administrator就直接回傳true，不需要再額外做事判斷關卡是否可以列印
- **Commit ID**: `a776e33114ddafcbe230a2ccd4274406d0dc458a`
- **作者**: kmin
- **日期**: 2024-07-31 14:19:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 296. [BPM APP]修正行動表單的RadioButton和CheckBox元件唯讀狀態下不會顯示額外輸入框內容問題
- **Commit ID**: `7cd36dc1abe7bea8d3198b5dd94a912d1c4b0bdd`
- **作者**: yamiyeh10
- **日期**: 2024-07-30 17:12:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 297. [E10]C01-20240722003 修正E10拋單發起人為離職人員不該成功
- **Commit ID**: `f8dd4c28fcc89155c84046ed952c7cdc29f98743`
- **作者**: kmin
- **日期**: 2024-07-29 11:14:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 298. [ESS]A00-20240726001 修正有判斷重復的在其他流程實例，就彈出提示訊息的多語系問題
- **Commit ID**: `e16210a91b7978848a73e53d1b39659f76e0c076`
- **作者**: kmin
- **日期**: 2024-07-29 10:37:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 299. [PRODT]Q00-20240710001 調整Web流程管理工具中儲存流程定義模型前增加檢核參與者機制
- **Commit ID**: `5b86b1f1c046a2ea952063f80410ffd9edfd5677`
- **作者**: yamiyeh10
- **日期**: 2024-07-26 11:59:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 300. [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題[补]
- **Commit ID**: `cd22362864c53f8e8b68d5b84ac22d2d26aab7ea`
- **作者**: 周权
- **日期**: 2024-07-23 17:16:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 301. [Web]C01-20240722004 修正文字有存在空格和英文字串時，呈現無法正常換行的問題
- **Commit ID**: `0092b006926fe1b90947c05a215080325c0713df`
- **作者**: 周权
- **日期**: 2024-07-23 11:04:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 302. [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
- **Commit ID**: `6622f0653eb6447dadab108e97ad8175fa8b00bc`
- **作者**: davidhr
- **日期**: 2024-07-22 11:45:16
- **變更檔案數量**: 68
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/AbsoluteFormBluePrint.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoComplete.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/OnlineReadFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseMutilPrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp`

### 303. [資安]V00-20240123001 修正Vulnerable Component漏洞議題-上次修正漏掉 bootstrap-3.3.5.min.js改為bootstrap-c.c.e.min.js
- **Commit ID**: `79dcd17f1f0f7e93037e956fc96d00b3591ecf86`
- **作者**: davidhr
- **日期**: 2024-07-22 11:37:54
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`

### 304. [內部]Q00-20240719001 優化流程關卡轉派代理人的log機制
- **Commit ID**: `c29fa4daed217bfb8a580b860706bb4f374a35a6`
- **作者**: kmin
- **日期**: 2024-07-19 12:05:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/conf/NaNaLog.properties`

### 305. [SAP]C01-20240712005 修正SAP欄位對應設定若固定值跟說明欄位輸入小於符號呈現異常問題
- **Commit ID**: `5108662982afb2ec71b0611219193f476414d6bc`
- **作者**: kmin
- **日期**: 2024-07-18 17:03:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`

### 306. [SAP]C01-20240712005 優化SAP的log訊息，目前是遇到SapFormMapping.mappingXML欄位內容格式異常
- **Commit ID**: `00b6a99c068b540c8b7278e9d700f4c15115a93b`
- **作者**: kmin
- **日期**: 2024-07-18 15:23:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`

### 307. [SAP]C01-20240712005 優化SAP的log訊息
- **Commit ID**: `381dbbf79bc301d40c98f8dc9a2e387a3a755f81`
- **作者**: kmin
- **日期**: 2024-07-15 14:28:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`

### 308. [Web]C01-20240711002 修正回传栏位含有“_txt”"_lbl"，若勾选双击清空栏位，表单开启报错的问题
- **Commit ID**: `ca46ab1df16c3cbf03f7cd3782e2538a8a5f1a93`
- **作者**: 周权
- **日期**: 2024-07-12 11:45:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 309. [流程引擎]C01-20240711003 修正特定情境導致工作通知內的批次閱讀出現Oops的異常
- **Commit ID**: `7c07428dba482e4e0431016373db7afb2b4acd19`
- **作者**: lorenchang
- **日期**: 2024-07-11 17:51:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 310. [PRODT]C01-20240614001 調整Web流程管理工具中主流程設定屬性的作為活動處理者代理人機制[補]
- **Commit ID**: `3a7b7e7fed7eda3ca7d0bd3e7eab5ffb61b89fe6`
- **作者**: yamiyeh10
- **日期**: 2024-07-10 13:41:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 311. [BPM APP]C01-20240708002 修正使用自定義開窗類型時透過FormUtil.disable方法變更元件編輯狀態會發生找不到onclick事件問題
- **Commit ID**: `cee2516af6d1af626a9094853745c3c15990847e`
- **作者**: yamiyeh10
- **日期**: 2024-07-09 16:03:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 312. [Web]Q00-20240702001 修正移除重覆多餘的批次終止、轉派項目 當活動關卡設定有勾選「允許輸入密碼」就會出現重覆，但實際上點了之後是無法正常運作的
- **Commit ID**: `194b91679c551f7f6334751f2d6d8958ebf8db2f`
- **作者**: kmin
- **日期**: 2024-07-08 17:39:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 313. [流程引擎]C01-20240703009 修正簡易流程預解析核決內加簽出來的關卡取回重辦後流程圖出現null訊息
- **Commit ID**: `bf70287ef0c27470b8f2247296ddf2d68e8a507f`
- **作者**: kmin
- **日期**: 2024-07-08 11:22:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 314. [T100]C01-20240704005 修正工單完工入庫作業 (asft340) 元件異常，導致轉換為 RWD 後更改對齊方式時 Grid 及 Attachment 元件消失的問題
- **Commit ID**: `b21c5a52c51ee82a5c4cd7c54b90eea048a0f935`
- **作者**: lorenchang
- **日期**: 2024-07-05 14:51:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `"Release/copyfiles/@t100/form-default/RWD\350\241\250\345\226\256/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\344\275\234\346\245\255(asft340).form"`
  - 📝 **修改**: `"Release/copyfiles/@t100/form-default/\347\265\225\345\260\215\344\275\215\347\275\256\350\241\250\345\226\256/\345\267\245\345\226\256\345\256\214\345\267\245\345\205\245\345\272\253\344\275\234\346\245\255(asft340).form"`

### 315. [BPM APP]C01-20240703004 修正企業微信值接從菜單進入到工作首頁時畫面異常問題
- **Commit ID**: `d2063666e36872c1ae9d1cf063316af73fdf36e4`
- **作者**: yamiyeh10
- **日期**: 2024-07-04 16:55:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 316. [其他]Q00-20240704001 優化預解析Log訊息加上簡易的Log
- **Commit ID**: `20cc8a2f093a01ff6b115ba158206b62ab53068e`
- **作者**: kmin
- **日期**: 2024-07-04 16:44:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 317. [流程引擎]C01-20240701002 修正並簽流程的其中一關終止流程，並簽流程其他關卡的簽核意見會顯示取回重辦 客戶情境：並簽的關卡中做終止流程的動作，其他並簽關卡的簽核意見出問題，現在改為「已終止(UserId-UserName)」 修正程式完後並測試情境有： 1.其他並簽已簽完 2.退回重辦 3.取回重辦 4.向前加簽
- **Commit ID**: `058d5058a0d201d0826351d1dda0ce0b8388623b`
- **作者**: kmin
- **日期**: 2024-07-04 11:40:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 318. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補多語系)
- **Commit ID**: `ef35f58920ded7efd4c681141882157eed4f93db`
- **作者**: 邱郁晏
- **日期**: 2024-07-04 08:48:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 319. [Web]C01-20240702005 列印模式下Grid顯示千分位異常
- **Commit ID**: `8a70f45438493888c1b0f3b6d9b149e5a1883f35`
- **作者**: 周权
- **日期**: 2024-07-03 17:17:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 320. Merge branch 'develop_v58' of http://************/BPM_Group/BPM into develop_v58
- **Commit ID**: `78f49e142fe93da4b797306b59fd5de5e168dc11`
- **作者**: wencheng1208
- **日期**: 2024-07-03 12:01:29
- **變更檔案數量**: 0

### 321. [ESS]C01-20240701005 將人員姓名中有難字內容「𡶧」替換掉為空字串，讓XML可與ESS溝通正常
- **Commit ID**: `7905c5833f183524b790e18d33b788d266e0f132`
- **作者**: wencheng1208
- **日期**: 2024-07-03 11:55:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 322. [Web]C01-20240702001 修正firefox下載的附件名稱前后都會有_的问题
- **Commit ID**: `f4ffc7824ee5d7dc95d28aca3f9f9e6921e5a8f5`
- **作者**: 周权
- **日期**: 2024-07-03 11:02:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 323. [流程引擎]C01-20240625006 修正WebService退回重辦額外收到追蹤通知信問題 客戶情境：A->B->C，C用webservice退回B，A、B都會收到信，但用Web介面就只有B會收到信，沒有開退回逐級通知。 程式改完並4個情境測試結果： 1.一般關卡退一般關卡=>測試正常 2.核決退一般關卡=>測試正常 3.開逐級通知的一般關卡退一般關卡=>測試正常 4.開逐級通知的核決退一般關卡=>測試正常
- **Commit ID**: `1fcf6ebfa7afb31ec0555626381f83cb4e79000b`
- **作者**: kmin
- **日期**: 2024-07-03 10:56:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 324. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
- **Commit ID**: `0495a5a4bcc890b9ada8f6b638a9bb6e7888c2fd`
- **作者**: 邱郁晏
- **日期**: 2024-07-02 16:08:41
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ISOManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/listreader/ISOListHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/listreader/ISOListHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IISOListHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ISOListHandlerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/standalone/configuration/standalone-full.xml`

### 325. [MPT]C01-20240627004 調整MPT公告申請單中刊登時間未選擇日期前先上傳附件會導致日期欄位出現NaN問題
- **Commit ID**: `6c663ad372bb5608ce41504a8db714517dfa5780`
- **作者**: yamiyeh10
- **日期**: 2024-07-02 15:41:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@mpt/default-form/MptAncApply.form`

### 326. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能(補)
- **Commit ID**: `52daebfeade59028f8e5c026b06757e9c7681a61`
- **作者**: 邱郁晏
- **日期**: 2024-07-02 10:17:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `Release/db/update/5.8.10.3_DDL_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DDL_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.3_DDL_Oracle.sql`

### 327. [BPM APP]C01-20240527003 修正整合企業微信在進行使用者登入返回的錯誤訊息都是undefined問題[補]
- **Commit ID**: `8b303b803cca85bc6fe0c423d1182c53ca210530`
- **作者**: yamiyeh10
- **日期**: 2024-07-01 17:26:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`

### 328. [Web]C01-20240628004 修正“/NaNaWeb/GP/ForwardIndex”會導頁到錯誤頁面
- **Commit ID**: `d367be2c60c656747326f83d0e78c9cee910c0cd`
- **作者**: 周权
- **日期**: 2024-07-01 14:35:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/action/ActionServlet.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ExceptionCatcher.java`

### 329. [Web]C01-20240626002 修正列印模式下Grid無法正常顯示千分位的问题
- **Commit ID**: `b81092ffb8d86619f3afdf45b6265e3e6b9ee09d`
- **作者**: 周权
- **日期**: 2024-06-27 11:35:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 330. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `8c54f6cc76de5e261c12e7acf6f479fe939c3fc4`
- **作者**: 周权
- **日期**: 2024-06-27 08:53:55
- **變更檔案數量**: 0

### 331. [PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題
- **Commit ID**: `b754d4a07201c8c97ae9fea5625c19902d078352`
- **作者**: yamiyeh10
- **日期**: 2024-06-27 08:23:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 332. [ISO] C01-20240625002 新增全文檢索查詢結果上限功能
- **Commit ID**: `6b3d3317385a25d10ed94c57a533d632dc3e2967`
- **作者**: 邱郁晏
- **日期**: 2024-06-26 16:34:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 333. [Web]C01-20240620009 判斷URL是否符合產品的模組URL新增防呆
- **Commit ID**: `032bc510c211a99c8af4dd7320e264ea3308c5a0`
- **作者**: 周权
- **日期**: 2024-06-25 10:56:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

