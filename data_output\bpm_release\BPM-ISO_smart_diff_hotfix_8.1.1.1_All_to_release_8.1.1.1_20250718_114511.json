{"比較資訊": {"專案ID": "BPM-ISO", "倉庫路徑": "D:\\IDEA_workspace\\BPM-ISO", "新分支": {"branch_name": "hotfix_8.1.1.1_All", "date": "2025-04-08 17:38:35", "message": "[B2B]C01-20250407001 修正文件攜出轉檔失敗會以原始檔替代的不合理邏輯，同時增加Log", "author": "lorenchang"}, "舊分支": {"branch_name": "release_8.1.1.1", "date": "2025-03-06 13:45:18", "message": "[ISO]C01-20250305001 ISO文管首页树状作业，全文检索查询报错", "author": "周权"}, "比較時間": "2025-07-18 11:45:11", "新增commit數量": 1, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "1cd926aab277b1d5b52f24918ebff0603361460f", "commit_訊息": "[B2B]C01-20250407001 修正文件攜出轉檔失敗會以原始檔替代的不合理邏輯，同時增加Log", "提交日期": "2025-04-08 17:38:35", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/isoPortability/ISOPortabilityManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}