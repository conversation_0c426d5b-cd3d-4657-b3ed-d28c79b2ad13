{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_*******", "date": "2022-06-26 20:48:05", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.7.2", "date": "2022-06-26 22:04:59", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.7.2", "author": "lorenchang"}, "比較時間": "2025-07-18 11:38:10", "新增commit數量": 238, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "16903d044a5b9d091d246fbfda970f479188ad0f", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******", "提交日期": "2022-06-26 20:48:05", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "4a485905fa02d6af9a71a752f762101de03b5953", "commit_訊息": "[WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務[補修正]", "提交日期": "2022-04-25 15:47:55", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "24e98182eefd2ca6f251881a5eef33973085295c", "commit_訊息": "[內部]Q00-20220414001調整派送表單關聯設定作業SQL指令 [補修正]", "提交日期": "2022-04-22 17:45:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c06df60cef26b21f7214ae2b860eb601cfe8d2ef", "commit_訊息": "[內部]更新******* patch檔", "提交日期": "2022-04-22 17:18:19", "作者": "wayne", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "30dcc37a005bdb7884f2d6887d6ba3a49f925167", "commit_訊息": "[Web]V00-20220422002 修正有取回重辦後，在行動版的待辦清單頁中該流程的主旨會無法正確顯示", "提交日期": "2022-04-22 16:50:44", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23db93abf5bacd818900a0b62fba70274d50b345", "commit_訊息": "[Web]V00-20220422001 修正行動版的待辦清單頁面中，有使用智能示警會無法正確顯示", "提交日期": "2022-04-22 16:33:23", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea7174b86d34cadb9d6145987ab50e9bd41396a5", "commit_訊息": "[流程引擎]V00-20220420001 修正流程定義當核決關卡參考自定義關卡屬性時，流程派送到核決關卡時會無法正常派送", "提交日期": "2022-04-22 14:50:50", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Organization.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "15690cad83a83f4515fa56b1a11211a8e68d533b", "commit_訊息": "[Web]S00-20211230002 調整自定義開窗當不需要模糊查詢欄位與欄位label時，就不顯示搜尋區塊", "提交日期": "2022-04-22 10:57:45", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9dc01090b26b20a29580be8c1ea0ffa33f8faca6", "commit_訊息": "[Web]Q00-20220421003 優化iReport畫面開啟速度", "提交日期": "2022-04-21 18:41:08", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageCustomReportAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "df41b5916fbde8a04b1cf6ce5a38c1aa5e30d9fe", "commit_訊息": "[Web]V00-20220421001 隱藏關注項目沒有分頁機制的筆數欄位", "提交日期": "2022-04-21 15:38:11", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1f5c9e22936de536b3d1139dfab6110f23faf049", "commit_訊息": "[Web]Q00-20220421002 修正Radio&Checkbox在切換不同的Binding欄位內容時，還殘留前一次被勾選樣式的問題", "提交日期": "2022-04-21 11:58:20", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd85b2dc40e85ded329e575dafc36423504a5ad2", "commit_訊息": "[流程設計師]S00-20211105002 服務任務RESTful路徑新增#符號判別要呼叫哪台流程主機", "提交日期": "2022-04-21 11:21:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a2e939d1a04e38aed8a718414ca5381d5d0586c8", "commit_訊息": "[Web]Q00-20220420003 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系但實際的標題未呈現多語系內容", "提交日期": "2022-04-20 18:24:03", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6b0d6431dfbd2c0f9c5c4f81e2412c91e9ea9afb", "commit_訊息": "[E10]Q00-20220420002調整標準出貨E10流程取得附件服務任務RESTful路徑改成/NaNaWeb/api/v1/process/attachinfo/documents/get", "提交日期": "2022-04-20 16:28:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "043f5f5ee9e65e445a9396ccd7fd9982df984acc", "commit_訊息": "[Web]V00-20220415003 修正系統設定「解析HTML tag」時，員工工作轉派流程主旨顯示異常[補]", "提交日期": "2022-04-20 12:05:30", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07111c099967ab6d2fe3395564ed9a88bdc1b720", "commit_訊息": "[Web]V00-20220415008 修正系統設定「解析HTML tag」時，刪除系統流程通知後，結果頁面上流程主旨顯示異常[補]", "提交日期": "2022-04-20 12:03:44", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6f0100f50bdd9f0ddb304118173c12ae60501e17", "commit_訊息": "[Web]V00-20220415007 修正系統設定「解析HTML tag」時，工作取回重辦後，取回重辦結果頁面上流程主旨顯示異常[補]", "提交日期": "2022-04-20 11:51:29", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dfc4b33c3b52c4a7d41c8cf75c0ed9fe8a09e571", "commit_訊息": "[Web]V00-20220415004 修正系統設定「解析HTML tag」時，重啟服務清單主旨顯示異常[補]", "提交日期": "2022-04-20 11:35:45", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7c8a2f345b7aff89c2cfdf1176f9923b3ab2725", "commit_訊息": "[內部] 調整「後置流程撤銷異常處理作業」改為「後置流程撤銷異常處理」", "提交日期": "2022-04-20 11:08:26", "作者": "wayne", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5887386a736900de94429ecc15518a4205ba64da", "commit_訊息": "[Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]", "提交日期": "2022-04-19 16:50:47", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ae653a80f831776d52a001bf267aab30eefb447f", "commit_訊息": "[Web]Q00-20220419002 修正簽核意見內容很少只會顯示一行時的情況，在流程圖中該關卡與下個關卡之間不會有連接線", "提交日期": "2022-04-19 11:01:45", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5e2bd255bef8b19d8a6bfea41b077890794f495a", "commit_訊息": "[Web]Q00-20220419001 修正如果切換讀取較久的頁面視覺上會跑一半又呈現原畫面再跳轉", "提交日期": "2022-04-19 10:45:07", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6362bfa2106fa61e48f679a31636427bfb8324e0", "commit_訊息": "[Web]V00-20220415005 修正首頁的我的關注筆數與我的關注流程清單數量不一致", "提交日期": "2022-04-19 09:20:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "61b0a1f4e22b349efdeeec4788c06649f59d337b", "commit_訊息": "[Web]Q00-20220418002 修正administrator在跳過關卡時可填寫意見", "提交日期": "2022-04-18 17:29:48", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7aa23d659bad65698056e177e49230e8c648e7f3", "commit_訊息": "[Web]V00-20220415008 修正系統設定「解析HTML tag」時，刪除系統流程通知後，結果頁面上流程主旨顯示異常", "提交日期": "2022-04-18 17:17:52", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "678d1b60d49dd0619cfd801c8bd681a49d450433", "commit_訊息": "[Web]V00-20220415007 修正系統設定「解析HTML tag」時，工作取回重辦後，取回重辦結果頁面上流程主旨顯示異常", "提交日期": "2022-04-18 16:52:09", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b295656f8bbd07ebcae38000bf458afcb65bc928", "commit_訊息": "[Web]V00-20220415009 修正片語管理頁面內容新修刪後，片語輸入框不會清空", "提交日期": "2022-04-18 16:47:57", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "98e4ec36c4c5f7ef3627dae603977872121497cb", "commit_訊息": "[內部]V00-20220414004 修正流程負責人-監控流程-點選「建立時間、流程結案時間」欄位排序，會出現請洽系統管理員", "提交日期": "2022-04-18 16:28:58", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "24b4ebb32b14b4971218661540cb174d11a9bf9d", "commit_訊息": "[內部]Q00-20220418001 調整「派送流程關聯」功能需納入序號卡控[補]", "提交日期": "2022-04-18 16:20:17", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "364e78864745cae9fa95d31f83b78f2e29716d4e", "commit_訊息": "[Web]V00-20220415004 修正系統設定「解析HTML tag」時，重啟服務清單主旨顯示異常", "提交日期": "2022-04-18 15:52:22", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SuspendedInvokeActListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e9c2ca23f74fa46fa335ae6c586d3f786931da3d", "commit_訊息": "[Web]V00-20220415006修正 流程主旨帶有「\\」符號，撤銷流程清單無法呈現", "提交日期": "2022-04-18 15:04:06", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d74fe922be205acf5e8e382713a9777edc5125b6", "commit_訊息": "[內部]V00-20220415012 修正使用者帳號中帶有^符號，從一個tool中再開啟其他tool會出現錯誤", "提交日期": "2022-04-18 14:14:02", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "35073186b0855c52b4e0f208f4c2fd3532af9eeb", "commit_訊息": "[Web]V00-20220415003 修正系統設定「解析HTML tag」時，員工工作轉派流程主旨顯示異常", "提交日期": "2022-04-18 13:43:58", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5c5fd7093a281ade93999f128c0593936e9f93ec", "commit_訊息": "[內部]Q00-20220418001 調整「派送流程關聯」功能需納入序號卡控", "提交日期": "2022-04-18 11:46:58", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a877ebf1e38862e9ac7a17a057ff57482914fe58", "commit_訊息": "[Web]S00-20220103001 產品授權註冊頁面增加顯示購買的模組類型[補]", "提交日期": "2022-04-18 11:24:02", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/license/LicenseRegTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d6c2d91e8d1203fce332d3abec9bceee67b873e5", "commit_訊息": "[Web]A00-20220414001修正表單設計器的搜尋表單欄位一直查詢導至InUseCount增長不會釋放", "提交日期": "2022-04-15 17:58:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionSearchReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8053dbd9acfb6770f3d1e85ce8f0f39954cd047e", "commit_訊息": "[Web]V00-20220414001 修正流程有標示智能示警時，在行動版的追蹤(監控)、BPM首頁顯示會出現html文字", "提交日期": "2022-04-15 15:13:56", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "163b47f31093be89338ea830fd4f4bd84574cd02", "commit_訊息": "[Web]Q00-20220414002 修正一般使用者追蹤流程清單匯出Excel報表，簽核時間欄位沒有值", "提交日期": "2022-04-15 13:37:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "13896f5ce56ba83cab3755b0e85d2e48905f35f2", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2022-04-15 11:39:27", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7f6e73340b492ea969553a0b5c6074b8822a37ab", "commit_訊息": "Revert \"[內部]V00-20220415002 修正命名不符合BPM附件檔名的檔案還是能透過URL下載附件\"", "提交日期": "2022-04-15 11:39:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d03ec4255bf50d783a583fba1102b2e356fd200c", "commit_訊息": "[內部]V00-20220414003 修正一般使用者追蹤流程-已轉派的工作點選「全部」後，再點任一欄位上的排序會出現請洽系統管理員", "提交日期": "2022-04-15 11:26:59", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4bf6aaf77f41ccde3d9a9e42318969517aff4008", "commit_訊息": "[內部]V00-20220415002 修正命名不符合BPM附件檔名的檔案還是能透過URL下載附件", "提交日期": "2022-04-15 11:09:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "73b71f6da741f39b5f91e0ae384aa195f21a8816", "commit_訊息": "[內部]Q00-20220414002 修正決關卡中核決條件之描述說明因刪除條件運算式導致清空或復原到某些操作前的值問題", "提交日期": "2022-04-15 10:42:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f75715d1771213511c50a877aaabd59f53a52a47", "commit_訊息": "[流程引擎]Q00-20220415001 修正因多餘附件移除邏輯改成流程結案處理，導至流程無法結案", "提交日期": "2022-04-15 10:15:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c8977b85267897b10ffa7ce31e44d6d9d3daa65f", "commit_訊息": "[內部] 調整瀏覽器閒置過久，請重新登入的多語系", "提交日期": "2022-04-14 09:58:18", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "15de8d6f7b28d4dc01bfef13ca920ae37dcbf849", "commit_訊息": "[內部]Q00-20220414001調整派送表單關聯設定作業SQL指令", "提交日期": "2022-04-14 08:15:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8ba4ef64c4fcef35c577c0b23009581162711b71", "commit_訊息": "S00-20211027003 新增記錄模擬使用者操作[補修正]", "提交日期": "2022-04-13 22:28:13", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "975cfc8e30875624311bcf52535ee86ceb6e64a1", "commit_訊息": "[流程引擎]S00-20211027003 新增記錄模擬使用者操作", "提交日期": "2022-04-13 21:32:11", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkItem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReexecuteActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 15}, {"commit_hash": "082539c3e38c1b661f4201df76e2c18d8a098d7d", "commit_訊息": "[Web]Q00-20220413003 修正DialogInputLabel有設提示文字，版更到5872後進入流程該元件未顯示提示訊息", "提交日期": "2022-04-13 14:39:06", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "889026049211e243aab8f1e499d0a5f50b5e2d07", "commit_訊息": "[Web]Q00-20220413002 修正DialogInput有設提示文字，版更到5872後進入流程該元件未顯示提示訊息", "提交日期": "2022-04-13 14:08:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bed308f7743a1f25bb0d00e47c54636ae63973db", "commit_訊息": "[BPM APP]Q00-20220314002 修正客製開窗為多選且無資料時返回按鈕顯示異常", "提交日期": "2022-04-13 12:20:07", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "700c19ef188c40dae1b4ad28fda7a20d13706e51", "commit_訊息": "[BPM APP]C01-20220308005修正Grid綁定的元件使用FormUtil.show方法時，在新增資料維護介面欄位分割顯示錯誤[補]", "提交日期": "2022-04-12 20:14:52", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76fd37637fea056e466e83c79209ba6718f52449", "commit_訊息": "[內部]Q00-20220317002 增加服務接口「允許非流程處裡者，可以開啟追蹤流程表單畫面」[補]", "提交日期": "2022-04-12 14:44:21", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b264c7959db681158e5a29968a59b3a3f9257f69", "commit_訊息": "[流程引擎]Q00-20220411006 修正派送流程關聯設定中退回及取回重辦「不處理」設定無效問題", "提交日期": "2022-04-11 18:34:44", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "625bdbb20628ea055683d5b77063eefedee32a62", "commit_訊息": "[內部]Q00-20220408003 調整移動端連續簽核參數化開關SQL無法重複執行問題", "提交日期": "2022-04-11 18:28:52", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "06f06d5062ad1ee242756236a98c5ea0d0f85c17", "commit_訊息": "[流程引擎]Q00-20220406001 修正因日期元件缺少特定屬性造成流程無法正常往下派送的問題", "提交日期": "2022-04-11 16:40:27", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "015a00368b424935456ae1db2ceb69404954ba86", "commit_訊息": "[內部]Q00-20220411003 修正核決關卡中核決條件之描述說明會因核決條件於新增或編輯時回到預設的問題", "提交日期": "2022-04-11 13:45:04", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a68d19b2a423f07b0f463f885d7178f93fb5059", "commit_訊息": "[內部]Q00-20220411002 調整行動端查看ERP退件資訊開啟退件表單後點擊返回按鈕後畫面的問題", "提交日期": "2022-04-11 11:37:07", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileReturnRecordView.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "eced9fbb19449b9a2d645ab75c4d7f23baa8d037", "commit_訊息": "[BPM APP]Q00-20211208001 修正企業微信使用者維護作業在MSSQL無法顯示多語系的問題", "提交日期": "2022-04-11 10:51:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/*******_mobile_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "6d602b77c469b4a9f2e39bc3a4709b2bf09af315", "commit_訊息": "[MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]", "提交日期": "2022-04-08 18:51:15", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa8d05e50f53067834130107b920c91558e70cea", "commit_訊息": "[資安]Q00-20220408005 修正jQuery版本過舊漏洞", "提交日期": "2022-04-08 15:57:09", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "34db332af1eaf7384cff7ab94f93e1fc04a7d3d5", "commit_訊息": "[MPT]S00-20211117001 調整公告申請單新增富文本功能", "提交日期": "2022-04-08 15:11:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomCssLib/bpm-wangEditor.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/wangEditor.min.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f6c95d5efd6671f76b31c0e04a1ab187bc40b121", "commit_訊息": "[Web]Q00-20220408004 修正設定發起主旨為必填時，當只有按ENTER來換行也能發起流程", "提交日期": "2022-04-08 14:21:47", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "591c1547997000267ef0fb14db44f81dd48ec260", "commit_訊息": "[內部]Q00-20220408001 優化5.8.2.1及5.8.3.1的update sql指令", "提交日期": "2022-04-08 13:39:45", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.2.1_DML_MSSQL_3.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.3.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.3.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dd7e1f3ee99ea1f26d81426346c544407b20fd72", "commit_訊息": "[內部]更新******* patch檔", "提交日期": "2022-04-08 10:59:30", "作者": "wayne", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9522b4650dc4c82d483f49e53d7c40c121c3dbde", "commit_訊息": "更新越南語系", "提交日期": "2022-04-08 10:35:29", "作者": "wayne", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a6b31d1a455b4a9973f38f306970777a9fef639", "commit_訊息": "[資安]Q00-20220331001修正漏洞：透過下載附件URL替換檔案名稱就能下載任意使用者的附件[補修正]", "提交日期": "2022-04-07 17:05:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "9e74de3cda780abae9313e11f32f621d1472b49b", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-04-07 13:30:59", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7126f6c4fa71c5d4f2aaae0555e444c76b1b03f0", "commit_訊息": "[內部]回收越南多語系", "提交日期": "2022-04-06 18:24:28", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "513eff75a2c1e7aacb385617ad2f759ffd513eaf", "commit_訊息": "[Web]Q00-20220406002 調整派送表單關聯設定作業PK長度過長問題", "提交日期": "2022-04-06 16:31:03", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fd018de8f12d9e1b119f4d01a4a8683fac139773", "commit_訊息": "[登入]S00-*********** 增加使用者登出登入紀錄[補修正]", "提交日期": "2022-04-06 16:29:38", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "17c6440c9aa06830b98b86aeb6d97500a7e5bbb6", "commit_訊息": "[內部]Merge *******_delivery_process分支多語系", "提交日期": "2022-04-06 15:36:53", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9e9816ee40fdec3cd8a6547b89eeb5d97edc973", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-04-06 11:31:39", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5745d7a60cf6d9098f97a950a78bd657e195b1a3", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-04-06 11:23:58", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryBackProcessAbortFailed.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "8265e312a9484a3b14d99f237b4b06af5bc54b5c", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-03-31 16:24:13", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryInstanceManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/deliveryProcess/DeliveryProcessInstanceDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryBackProcessAbortFailed.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "cf8454029d1ab9812be917a122d399ca0cc9b1a8", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-03-25 15:42:36", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "71278a8bc0055bd3c83b71c58cd22f41238bcc36", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-03-23 15:00:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99b54fdfecfb9dec481c9e361ced6f53d5b76255", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-03-22 17:14:28", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "696973d37c8e723a5bcce0e848bc336c1268f804", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-03-22 14:17:04", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryInstanceManagerDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessAbortControllerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortController.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/exception/BackProcessAbortFailException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryBackProcessAbortFailed.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTerminating.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 23}, {"commit_hash": "9b881edbb2a87df24cc84c769dfa6cc36831c976", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-03-03 14:27:17", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "999ff4c8eb9c870b154db3b14ccc5c55d5d9c94e", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-03-02 13:42:31", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d182791c7f8754026e395ddf52fb7593d9a01dfd", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-03-01 15:35:32", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryProcessHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "4b0448e7210ee5f1b29c89bdcf64ee0bc9420cb1", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-02-23 18:00:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7d02102cc5dbe1c4156c17d06547dcc56e7f364f", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-02-23 14:17:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "919449116e8baa53c6483b5beda9a43495a54e01", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2022-02-21 10:48:40", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "0c257303a5a8ae987cf3ebbdc3673f407edecc84", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2021-12-22 15:14:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ee29b167b423852b04aee9d8e40780e164eb95e2", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]", "提交日期": "2021-12-09 19:12:50", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3db191217ff98b694683dbab1cdcee8421be5067", "commit_訊息": "[流程引擎]S00-*********** 新增派送流程關聯設定作業", "提交日期": "2021-12-06 13:51:36", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryProcessHandlerDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandler.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "cf80b83ee0b9c8ccf0bcdf5464340ed4e1ebaa53", "commit_訊息": "[Web]S00-*********** 新增派送流程關聯設定作業 [補修正]", "提交日期": "2021-11-25 17:48:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ca4d2847c779f88010c35c295db4154149822cee", "commit_訊息": "[Web]S00-*********** 新增派送流程關聯設定作業 [補修正]", "提交日期": "2021-11-25 17:11:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "ed783358aff8853a082be90ce38b5adb70aefb15", "commit_訊息": "[Web]S00-*********** 新增派送關聯配置作業", "提交日期": "2021-11-25 14:32:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryManagerDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerLocal.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "e90f6c307a4ad4f485b611ff94106ee275df8adb", "commit_訊息": "Merge *******分支多語系", "提交日期": "2022-04-06 13:54:17", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7f13a0f3992123750b59dceddda9eabfd97c5646", "commit_訊息": "[BPM APP]S00-20211101001 新增移動端連續簽核系統參數化開關", "提交日期": "2022-04-01 13:17:46", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e83ff29fcc3cf4f7c66bc817318848372ac27539", "commit_訊息": "[WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務", "提交日期": "2022-03-31 10:52:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "2a1837783f8f7463f73178187ad322ea1eaa94f7", "commit_訊息": "[在線閱覽]Q00-20220328003 修正如果文件主機與流程主機不同台在線閱覽畫面打開後會看不到檔案內容", "提交日期": "2022-03-28 18:32:04", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3a7b7b513c414319ed1d9d043246f57ef7d584f6", "commit_訊息": "[登入]S00-*********** 增加使用者登出登入紀錄", "提交日期": "2022-03-09 23:39:16", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/license/UserLogInOutRecord.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SessionManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 20}, {"commit_hash": "e907bc5d9881b403f73db2e9082c6d328c6f4c36", "commit_訊息": "[在線閱覽]S00-20210617002 關卡 新增在線閱讀選項", "提交日期": "2022-03-09 15:27:01", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/AllowChangeOnlineReadEditorRenderer.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/AllowChangeOnlineReadType2IntFieldConversion.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/enumTypes/AllowChangeOnlineReadType.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 23}, {"commit_hash": "3920f3810c3a242432132f045c48c8d60005b682", "commit_訊息": "[MPT]調整首頁模組相關上傳下載功能機制的上傳與下載接口有亂碼問題", "提交日期": "2022-03-02 10:35:08", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "37cb816f6ff17417c3bccb5b792cbed8b88bcd10", "commit_訊息": "[Web]S00-20220120003 表單上傳附件支持多選上傳[補]", "提交日期": "2022-02-16 11:10:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a40893d2ddd7243314a2b2244ab3158a1aa8fd52", "commit_訊息": "[Web]S00-20220120003 表單上傳附件支持多選上傳", "提交日期": "2022-02-15 17:44:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jqueryfileupload/jquery.fileupload.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jqueryfileupload/jquery.iframe-transport.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jqueryfileupload/vendor/jquery.ui.widget.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "0862754ba23f25f008db5913621c6d95b4528ef5", "commit_訊息": "[在線閱覽]S00-20211112003 在線閱覽允許轉檔類型系統參數", "提交日期": "2022-02-08 17:23:31", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "6a9be0ce6a48214872c994e621d4a34efd625edc", "commit_訊息": "[Web]S00-20220103001 產品授權註冊頁面增加顯示購買的模組類型", "提交日期": "2022-01-26 11:26:40", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "3dd73c865ce35523080e42aa5569900a04fae83a", "commit_訊息": "[Web]S00-20210910001 移除攻略雲設定，轉檔異常處理作業只在出或移除，建patch時另外處理", "提交日期": "2022-01-25 16:46:20", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "70060da42fc9355d8ed38e5ba95f76bc4a18ce24", "commit_訊息": "[在線閱覽]S00-20211027002 在線閱覽開關", "提交日期": "2022-01-25 15:15:19", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "c35b6c74413829937771d14ef5d6896fd7f5a991", "commit_訊息": "[ESS]S00-20211208003新增ESS外網主機IP設定", "提交日期": "2022-01-25 11:51:39", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "f6db6e13d5d4d2857127d4e9f2c927b4f2c5310c", "commit_訊息": "[MPT]調整首頁模組相關上傳下載功能機制新增的檔案作廢接口", "提交日期": "2022-01-21 15:55:55", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/LocalDocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "2316e9b241211680a318a098901d1dcc1e63ae25", "commit_訊息": "[流程引擎]Q00-*********** 調整Create Index指令，新增Index", "提交日期": "2022-01-18 17:01:11", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ad50bf6ceb0b23495aa50e7504966e44540aa746", "commit_訊息": "[MPT]調整首頁模組相關上傳下載功能機制新增的檔案下載接口", "提交日期": "2021-12-24 15:59:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d0e9c15d773ddbb33e83a252f11c0ddba15abb5a", "commit_訊息": "[流程引擎]S00-20210915002 新增BPM首頁預設撈取的時間區間是依系統變數設定", "提交日期": "2021-12-15 18:06:35", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/Constants.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "65a7cb2f7860f40a0426474119fa893529ebc146", "commit_訊息": "[MPT]調整首頁模組相關上傳下載功能機制新增的檔案上傳接口", "提交日期": "2021-12-10 11:52:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "639f833da29fbea8d8622870caebbe74ee6b07fc", "commit_訊息": "[流程引擎]S00-20210621004 新增待辦事項記錄簽核的Client IP", "提交日期": "2021-12-07 16:47:37", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkItem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReexecuteActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "c7826fea915ea1939a352f9a6535f499586a3566", "commit_訊息": "[流程引擎]S00-20210726001 新增流程草稿列表預設撈取的時間區間是依系統變數設定", "提交日期": "2021-11-29 11:38:42", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/Constants.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "bd0f982bac6e8c4bd6d7f109351894627ea4252f", "commit_訊息": "[Web]S00-20210506012 新增核決關卡中核決條件之描述說明", "提交日期": "2021-11-22 11:53:48", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/decision/ConditionCellEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/decision/DecisionConditionCellEditorRenderer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/decision/DecisionRuleListEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionDefinitionCellPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/DecisionCondition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/definition/DecisionActivityDefForPreviewing.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 20}, {"commit_hash": "53e76428c1e2f8c8ba1b3fa8b595a6be4d8bfe11", "commit_訊息": "[流程引擎]S00-20210910003 退回重辦啟動代理人機制功能", "提交日期": "2021-10-25 18:47:29", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "64a3571c6d4c5c0b28b37bfc823a6230c690b1d1", "commit_訊息": "[資安]Q00-20220401002修正Spring漏洞反射型文件下载漏洞(CVE-2020-5421)", "提交日期": "2022-04-01 17:29:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/lib/Spring/spring-web-4.3.7.RELEASE.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/lib/Spring/spring-web-d.c.c0.RELEASE.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/SpringFramework4/spring-web-4.3.7.RELEASE.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/SpringFramework4/spring-web-d.c.c0.RELEASE.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "e88ea52101b7ddd33846a26f75a24685bb01f693", "commit_訊息": "[資安]Q00-20220331001修正漏洞：透過下載附件URL替換檔案名稱就能下載任意使用者的附件", "提交日期": "2022-04-01 15:52:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "35fdbfbdb7747ebdde6ac29161772f9b86b998e0", "commit_訊息": "[組織設計工具]Q00-20220401001 修正組織設計師刪除職務核決層級定義；當該職務定義有被流程參考時，無法提示被引用的流程資訊", "提交日期": "2022-04-01 14:17:04", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "60f6920c2e506042def1e0c55267b33667568265", "commit_訊息": "[BPM APP]S00-20211101001 新增移動端連續簽核系統參數化開關", "提交日期": "2022-03-31 20:26:01", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "7fd055991faefaeb66750027b3403ccc4742bb28", "commit_訊息": "[流程引擎]S00-*********** SQL註冊器內的設定可調用表單開窗增加查詢功[補]", "提交日期": "2022-03-31 18:16:22", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d8769c9761312e09b46dd954423e39d9ca4bed8a", "commit_訊息": "[流程引擎]S00-*********** SQL註冊器內的設定可調用表單開窗增加查詢功能", "提交日期": "2022-03-31 17:56:47", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "ba33bbd9bf2368b36c10f57f9870b79bd31cc3ff", "commit_訊息": "[BPM APP]C01-20220331006 修正行動端HandWriting元件在script設置disable時無法正常使用問題", "提交日期": "2022-03-31 16:44:03", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e624b1dff5ac9402c5be3e333f6b53b695c60966", "commit_訊息": "[資安]Q00-20220329003 修正jQuery版本過舊漏洞", "提交日期": "2022-03-31 16:41:01", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/IWCIndicatorDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CannotAccessWarnning.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxOrgTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Ajax/AjaxService.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/AttachmentExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/ButtonExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/DateExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/DialogExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileRWDExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/TextboxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/TimeExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Index.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/RESTful/RESTfulIndex.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ErrorPage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/ChildGridChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/EMSI01.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/EMSProgram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DesignerDownload/DesignerDownloadMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/AbsoluteFormBluePrint.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRwdFormScriptEditor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormScriptEditor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CreateSysLanguage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocCategory/ManageDocCategoryMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/PDFUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOChangeFileList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISODocumentsList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOEffectInvalidList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOFileQueryList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOReleaseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/LogoImageUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManageResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ImageUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ShowSignImage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmApp.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContact.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContactV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListResigendV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/IntelligentLearningManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFromReturnRecordView.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileIntegrate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileOAuthRecive.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/NotRegisterApp.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/OnlineReadFileUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineRead/WatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AdjustActivityOrder.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AttachmentHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseMutilPrefechAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteReferProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExpenseAccountItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ViewReassignHistory.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewAutoAgentActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewDecisionActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewParticipantActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/SubProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/InvokeProcessDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/PerformWorkItemDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/ProcessPerformanceMonitor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportTemplate.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainAnalyzeService.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainCombinationService.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainExternalService.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ServiceRegister/ServiceRegister.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ChooseDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/WebViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v1/plugin/jquery-a.h.c.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jquery-a.h.c.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jquery-a.h.c.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/NewTiptop/NewTiptop.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/appform/jquery-a.d.b.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jGrid/gridbox.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jGrid/jqgrid43/js/jquery-a.h.c.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jQueryMobile/jquery-a.h.c.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-a.d.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-a.g.a.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-a.g.a.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-a.h.c.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-a.j.a.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-a.k.a.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-a.k.c.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-c.b.a.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-ui-a.h.i.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-ui-a.j.c.custom.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jqueryUI/jquery-ui-a.k.d.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/zTreeJs/jquery-a.d.d.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@mcloud/openWin-Source/CustomOpenWin/MCloudMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 351}, {"commit_hash": "fedbaa6f4f9ec078a43716b2ea34e98109e9604c", "commit_訊息": "[內部]配合修正Dom4J XXE Injection漏洞進行相關調整", "提交日期": "2022-03-31 16:06:17", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Dom4J/dom4j-1.6.1-changed_serialization.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/domain/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "重新命名", "狀態代碼": "R087"}, {"檔案路徑": "3.Implementation/subproject/domain/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/efgp-pdfViewer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/efgp-pdfViewer/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/efgp-pdfViewer/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/form-builder/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-designer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-designer/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/form-designer/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-importer/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/form-importer/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/org-importer/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/system/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/Dom4J/dom4j-2.1.1.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/Dom4J/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 43}, {"commit_hash": "baa5e04da46e84b4bb510d5ea9431fc74362eef5", "commit_訊息": "[資安]修正Dom4J XXE Injection漏洞", "提交日期": "2022-03-31 16:05:21", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/modules/system/layers/base/org/dom4j/main/dom4j-2.1.3.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/modules/system/layers/base/org/dom4j/main/module.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "168e202cf131d89070626a70710a3b968b294c27", "commit_訊息": "[Web]Q00-20220330002 修正漏洞：一般使用者可登入後在瀏覽器開發者工具輸入goToURL語法進入產品授權註冊青單", "提交日期": "2022-03-31 14:53:21", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "80fc2b7fca3b3d73cf0f1401799f00dc8d7228c4", "commit_訊息": "[Web]A00-20220330001 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系，但實際操作沒有呈現多語系的內容", "提交日期": "2022-03-31 13:51:04", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "253f4f1cf3f039249119cd6ee2bf4c1e2a65419f", "commit_訊息": "[Web]Q00-20220330001修正透過修改參數hdnMethod:loginByQRCode, 不需要密碼便可直接登入BPM", "提交日期": "2022-03-30 18:25:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/QRCodeLoginCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "72385104aa502e1b164413850c71139927837203", "commit_訊息": "[Web]S00-20220329001 登入機制優化，增加記錄密碼功能，Email連結自動跳轉[補]", "提交日期": "2022-03-30 14:33:23", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3dc1d1a3aa6f67d2c44cee3783b8c1ad8da965f5", "commit_訊息": "[Web]Q00-20220329002 調整退回重辦流程清單以結案時間為主排序發起時間為副排序", "提交日期": "2022-03-29 16:48:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0c8fa79b0705ca2b2597f09661f12b6384500d58", "commit_訊息": "[Web]S00-20220329001 登入機制優化，增加記錄密碼功能，Email連結自動跳轉", "提交日期": "2022-03-29 15:24:03", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "758a73fda3f0e4110a3ee613760edb2fca302a48", "commit_訊息": "[流程引擎]Q00-20220329001 修正模擬使用者進到BPM首頁再從下方進入待辦並簽核後，按回到清單頁會卡住", "提交日期": "2022-03-29 14:42:41", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c70bf37cabb405ad32a1e52fe27d91b8d3ae1c19", "commit_訊息": "[流程引擎]提高58版Queue併發承載量", "提交日期": "2022-03-28 16:25:37", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocConvertWithFileHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/MessageHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/QueueHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/BatchNoticeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/DeleteClosedProcessInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/E10SendSignInfoBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/EventDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/FormInstanceTransformerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/McloudPushInvokeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/NoCmDocumentsBackgroundServiceBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/TiptopCleanDocumentBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 20}, {"commit_hash": "5e366b431316899b3a009e46d4209d35d4dfbe6b", "commit_訊息": "[內部]更新bpm-tool-entry設定，避免新版Eclipse編譯異常", "提交日期": "2022-03-28 14:08:51", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/.settings/org.eclipse.jdt.core.prefs", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "18edfe630d1872eda04d6fc7b0216a1720295a89", "commit_訊息": "[Web]Q00-20220328001 修正從首頁的待辦清單點選第二頁以後的任一流程，點擊繼續派送會報錯", "提交日期": "2022-03-28 10:00:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ec826ea9ac32e6208ba1d7cd30f08b2fc738a8e", "commit_訊息": "[系統管理工具]Q00-20220325003 修正使用者密碼帶有^符號，從一個tool中再開啟其他tool會出現錯誤", "提交日期": "2022-03-25 17:45:39", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e4e71609cf6419363e75b08bb9720bcb4a9acdae", "commit_訊息": "[Web]A00-20220325001 修正URL開啟追蹤TraceProcessForSearchForm畫面上的列印按鈕title無法正常顯示多語系", "提交日期": "2022-03-25 16:56:17", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d89780794465578cad8d7b86c8cae6f694ce62a", "commit_訊息": "[流程引擎]Q00-20220325002 修正一般使用者-追蹤流程-已轉派的工作-匯出excel時，當資料庫為Oracle且匯出流程數量大於300時會報錯", "提交日期": "2022-03-25 16:30:15", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "adc7d0aafec9834983565b5b23d7947da35c9533", "commit_訊息": "[Web]Q00-20220325001 修正表單單身欄位對應錯誤", "提交日期": "2022-03-25 14:21:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "44f2b07d980e74e7b4e4b205400319cda37d6f96", "commit_訊息": "[流程引擎]A00-20220323001 修正流程核決層級關卡之後的關卡若有設定自動簽核，流程無法往下派送的問題", "提交日期": "2022-03-24 18:28:08", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1ac4a09e68caf8f75e784df433fc883152625278", "commit_訊息": "[Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出", "提交日期": "2022-03-24 17:25:19", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa0827aec7bd99ebeeae9cc242e595dd79f749a9", "commit_訊息": "[表單設計師]Q00-20220324002 修正使用IE開啟有時間元件的RWD表單會出現錯誤", "提交日期": "2022-03-24 17:04:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c014452b8335599b30e8f04da0cb5e403ede27e9", "commit_訊息": "[表單設計師]Q00-*********** 調整表單設計器在發行表單的視窗中的截止有效日欄位改為非必填", "提交日期": "2022-03-24 15:59:40", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ca5aa04ba018f9d727226c9f73175654ad6399e7", "commit_訊息": "[WebService]Q00-*********** 移除MOfficeIntegrationEFGP WebService服務", "提交日期": "2022-03-23 17:01:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "415c1f91fd8b02dd2f02706c0964ff2b5eb60b26", "commit_訊息": "[流程引擎]A00-20220322002 修正SQL註冊器中語法有使用到order by 會導致報錯", "提交日期": "2022-03-23 16:59:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5924ad66a2ead87fe998ff68d8c03d455f23c5cf", "commit_訊息": "[Web]A00-20220322001 修正流程負責人透過監控流程匯出excel時，當流程狀態為已完成、已撤銷、已中止時，「目前處理者」欄位仍有資料", "提交日期": "2022-03-23 16:46:48", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a7ee539e4e8e3c707b1d5cb76bc7d14ada14f2f3", "commit_訊息": "[WebService]Q00-20220323002\t修正BPM的WebService的SQLInjection漏洞", "提交日期": "2022-03-23 16:30:29", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fd5eaddd778fa28578ad717c4a02078343166952", "commit_訊息": "[Web]Q00-20220322002 修正行動版面下，從追蹤進入絕對表單的流程，沒有橫向scrollbar而無法看到完整表單", "提交日期": "2022-03-22 17:00:14", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "19587aa0960eec9e78ed12d01750b0b4b178ea66", "commit_訊息": "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]", "提交日期": "2022-03-22 15:15:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e1c5820e31be4f7a7ae0db1816d2e6c59dbdfb15", "commit_訊息": "[ESS]Q00-20220321003修正ESS流程經過取回or退回重辦在服務任務關卡前，撈取表單實例序號錯誤導致繼續派送報錯", "提交日期": "2022-03-21 16:51:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed73d34b94b970ac771b54a729312734c133c3f8", "commit_訊息": "[Web]Q00-20220321002 修正一般使用者-追蹤流程-已轉派的工作，使用「表單序號」當條件查詢無效", "提交日期": "2022-03-21 16:38:17", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d46f7a446c1a47a97d96222c57886ef47095e9ac", "commit_訊息": "[Web]A00-20220317001 修正透過URL(TraceProcessForSearchForm)開啟追蹤流程頁面，且系統變數設置「追蹤頁面簽核歷程為top」時，簽核歷程無法展開顯示內容", "提交日期": "2022-03-21 16:28:59", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0a8f9f852d174e1a0ef282f9c56cc772929f1abe", "commit_訊息": "[系統管理工具]S00-20220223003 新增資料來源設定，資料庫類型新增AZURE用的MSSQL", "提交日期": "2022-03-21 14:54:09", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DataBaseType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ColumnsFinder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/DatabasesFinder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/TablesFinder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/resources/DatabaseDriver.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "d25cbb7c60cb0e4163e74d0b62d5b6fc5a80f2e9", "commit_訊息": "[Web]S00-20211013001 調整流程圖中能完整顯示出簽核意見內容[補]", "提交日期": "2022-03-21 11:42:46", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "385d1c396c3ff231708d4d623af65a9650ec7558", "commit_訊息": "[Web]Q00-20220321001 修正絕對位置表單在第一關以外的關卡上傳附件按上傳後，開窗變空白", "提交日期": "2022-03-21 10:51:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0e43541a5cc9e020563cb83f70a9336374a479d5", "commit_訊息": "[Web]Q00-20220317004 修正在IE開啟加簽頁面，確認按鈕無法點選", "提交日期": "2022-03-18 15:26:09", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "61689339591a95c30b3eb3408f04d7b78c030443", "commit_訊息": "[系統管理工具]新增資料來源設定，資料庫類型新增PostgreSQL", "提交日期": "2022-03-18 11:56:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DataBaseType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ColumnsFinder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/DatabasesFinder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/TablesFinder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "32095e8b3663303cd1ffac51a2d9c42dd6fa505b", "commit_訊息": "[流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤[補]", "提交日期": "2022-03-17 16:16:39", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "408d6bd3368d58555b1d2f6fbf497048299109e2", "commit_訊息": "[內部]Q00-20220317002 增加服務接口「允許非流程處裡者，可以開啟追蹤流程表單畫面」", "提交日期": "2022-03-17 14:47:55", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/StringUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "eddc65ae5660cc9692df2cce1fda2c8b9fe23bba", "commit_訊息": "[WorkFlowERP]S00-20220314001 新增WorkFlowERP表單", "提交日期": "2022-03-16 16:35:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@workflow/conf/Process_Mapping.prsmapping", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "86082a138eb0409b9594d3c8817913746cd5f845", "commit_訊息": "Revert \"[WorkFlowERP]S00-20220314001 新增WorkFlowERP表單 PURI16,MOCI12\"", "提交日期": "2022-03-16 16:27:39", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@workflow/conf/Process_Mapping.prsmapping", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "55ca134d8d20ad0d67206bada2118fb81028414e", "commit_訊息": "[表單設計師]Q00-20220316001 修正表單設計師DialogInputLabel有設提示文字，版到5872會導致提示文字出現在預設值", "提交日期": "2022-03-16 14:34:18", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c729deb737026d4015bbfc6f80d09a0e458ae1f7", "commit_訊息": "[Web]S00-20220316002 修正漏洞downloadDocument的下載URL可任意下載其他路徑檔案", "提交日期": "2022-03-16 14:29:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c7169465d7f646890c62b75fb98150dc1449786", "commit_訊息": "[BPM APP]C01-20220316003 修正行動端DialogInput元件有設定提示文字時會顯示_的問題", "提交日期": "2022-03-16 13:37:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23d764e80485b1a163ae9da5e8e066820a852676", "commit_訊息": "[Web]S00-20220316001 新增整合暫存管理供未註冊的BPM使用", "提交日期": "2022-03-16 11:19:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1e902b38c5660a63b00667dc9ee8994565fca510", "commit_訊息": "[WEB]Q00-20220315004 修正離職作業維護-轉派離職人員的工作-勾選「全部轉派第一優先代理人」時，未將預設代理人帶回「接收者」欄位中", "提交日期": "2022-03-15 17:34:33", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "91409ce69ff060e7d9ba7ab068ba492dfebce8f7", "commit_訊息": "[表單設計師]Q00-20220315003 修正表單設計師DialogInput有設提示文字，版更到5872會導致提示文字出現在預設值", "提交日期": "2022-03-15 16:19:59", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "25cf223af9b2344d6e537905b8d59db90d625102", "commit_訊息": "[組織同步]A00-20220314001 修正組織同步完會蓋掉使用者設定的 使用者是否顯示待辦事項小視窗", "提交日期": "2022-03-15 14:52:49", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c69a1e13a75274a4556c61e0f277662bec2901c7", "commit_訊息": "[WEB]A00-20220216001 修正追蹤流程-已轉派的工作，點進表單後再返回清單頁沒有保留原本的查詢條件", "提交日期": "2022-03-15 14:10:49", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ab494c7edb04256b216352979764138fa4e1a030", "commit_訊息": "[WEB]Q00-20220315002 修正舊版表單InputElement若沒有textValue屬性時，儲存表單會發生錯誤", "提交日期": "2022-03-15 14:01:56", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TextElementDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d64e32a5331906530a78e51f22bf342205781d84", "commit_訊息": "[Web]Q00-20220223001表單無法派送，把元件applUserId刪除重拉就可以正常派送", "提交日期": "2022-03-15 13:39:07", "作者": "ocean_yeh", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09259a6676c558d458f0f9c4ba4c958c504c803e", "commit_訊息": "[BPM APP]C01-20220308005 修正Grid綁定的元件使用FormUtil.show方法時，在新增資料維護介面欄位分割顯示錯誤", "提交日期": "2022-03-14 18:56:49", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cd764fa0b6c93ba0c558186ce4ec71f5c85685bd", "commit_訊息": "[BPM APP]C01-20220311003 修正行動端FormUtil的getValue方法異常問題", "提交日期": "2022-03-14 18:00:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d632eaabaa72136a5e8cbe81a85fbbf6c99a1a8c", "commit_訊息": "[流程引擎]Q00-20220314003 修正流程關卡下一關卡為多人關卡處理且設定自動簽核為\"與前一關相同簽核者，則跳過\"，繼續派送會失敗", "提交日期": "2022-03-14 17:46:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "35e22d3b89e3312b96c51c99afc5e9bb6a7f38a1", "commit_訊息": "[WorkFlowERP]S00-20220314001 新增WorkFlowERP表單 PURI16,MOCI12", "提交日期": "2022-03-14 17:32:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@workflow/conf/Process_Mapping.prsmapping", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "122c073c6f8bb8cf9c1dda10f138707e45df5ae0", "commit_訊息": "[BPM APP]C01-20220311002 修正用平板登IMG時session會清空導致發單簽核有空指針異常問題", "提交日期": "2022-03-11 18:02:08", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbec7f6d0cb84e4e45f6095a0e4c11d119056bb9", "commit_訊息": "[流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤", "提交日期": "2022-03-11 16:49:37", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "1e5ceb8593af1fc8bd571a5fd5ee57148f83d2c0", "commit_訊息": "[BPM APP]C01-*********** 移動授權中間層的使用者維護作業增加分頁功能[補]", "提交日期": "2022-03-11 14:42:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9710be65ebbeb2d34a46ea399b6e2764cb5037a4", "commit_訊息": "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]", "提交日期": "2022-03-10 15:45:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "150347685553ecd3e6145367e3651dbf66277772", "commit_訊息": "[BPM APP]C01-20220309001 修正Web端行動模擬發單功能於表單載入時不會呼叫到formCreate方法問題", "提交日期": "2022-03-10 10:28:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9ac4abb021a939fd96fabb0ddd875fe0cbd93e47", "commit_訊息": "[ISO]Q00-20220309001 修正ISO變更單，在ModDocRequester關卡載入附件時，若表單有選擇元件(RadioBox,ComboBox,CheckBox,ListBox)並將元件設定為invisible時，無法載入附件", "提交日期": "2022-03-09 14:50:42", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8fe55178247bd5e4becfd63868ccb285ef414163", "commit_訊息": "[Web]S00-20211013001 調整流程圖中能完整顯示出簽核意見內容", "提交日期": "2022-03-09 11:48:44", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "de69d831ac9a467b0a0a9b9e8558ddd162783228", "commit_訊息": "[流程引擎]A00-20220308001 修正一般使用者進入監控流程並點選『全部』，再到其他頁面後再回到監控會變一片空白", "提交日期": "2022-03-08 15:44:47", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c073465e728e60fcffb16856dab923d71a3dd0cd", "commit_訊息": "[BPM APP]C01-20220224004 修正在取得IMG動態生成表單應用資料與取得綁訂使用者資料時會偶發statement close問題", "提交日期": "2022-03-08 10:08:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "49d8c5a4f94800b98b6bf648b7ec13a29e43bf84", "commit_訊息": "[Web]Q00-20220307001 修正子單身過多造成表單開啟時間變長", "提交日期": "2022-03-07 18:24:49", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/SubGridTransfer.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f3235f7560cc609261ea2af61595cc4f12f449c5", "commit_訊息": "[登入]Q00-20220304002 調整登入加密機制，避免後端session失效時取不到值登入失敗", "提交日期": "2022-03-04 18:24:11", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "123dfb344ec8b57a5ce1078ec1df6abfebcceeac", "commit_訊息": "[Web]Q00-20220304001 修正查詢樣板使用6個$當作『請選擇』的內存值，在渲染後會被換成12個$", "提交日期": "2022-03-04 17:59:14", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "175bbe8619e58af38d081c5cc5680caf43dfc7c9", "commit_訊息": "[流程引擎]Q00-20220215001 修正偶發附件遺失問題[補修正]", "提交日期": "2022-03-03 16:11:02", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8044bd283d43a0d014367d12f776044144ed5f9", "commit_訊息": "[BPM App]C01-20220301003 修正E10表單在移動端操作日期元件時會無法帶入值與必填訊息顯示報錯的問題", "提交日期": "2022-03-02 18:08:26", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/E10Form.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9257448a5d5bc602e6a98f9aa16cb0a5b98dcf41", "commit_訊息": "[組織同步]A00-20220224002 修正組織同步完會蓋掉使用者設定的 簽核完畢後的行為", "提交日期": "2022-03-02 17:41:15", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba91a9a3bd76730b7ccb43bf95a591500adac8ee", "commit_訊息": "[Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢[補修正]", "提交日期": "2022-03-02 09:25:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "64ed6727c962b7dc2ec2767c353ba44116b49bf4", "commit_訊息": "[BPM APP]C01-20220301002 修正在沒有整合行動方案情況下開啟Web表單設計師任一表單時會發生錯誤的問題", "提交日期": "2022-03-01 18:45:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "633b5d3219feb3e4a664862f42511bffbfa0fccf", "commit_訊息": "[流程引擎]A00-20220224001 修正條件式中各條件先做or後再做and，會無法派送到該條件後的關卡", "提交日期": "2022-03-01 17:16:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/ConditionEvaluator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5f6769c5c93831ea5ebaa082b5139d109b740114", "commit_訊息": "[Web]Q00-20220301001 修正SQLCommand沒有設定SQL卻佔用連線的問題", "提交日期": "2022-03-01 16:23:22", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e05217e4cb0e9ec4b736f910f63b1fcb6a1a6271", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2022-03-01 15:16:51", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7f9a6a31bd47e63edda45347a583fd03814d3e6f", "commit_訊息": "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]", "提交日期": "2022-03-01 15:16:38", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2e67353e02cd7b98cc1fffe91f3e2d8f71319436", "commit_訊息": "[BPM APP]S00-20211027004新增移動端支援ERP流程終止或撤銷時，若單據修改重新送審後於移動表單可查看之前審批流程的功能[補]", "提交日期": "2022-03-01 11:16:03", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFromReturnRecordView.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileReturnRecordView.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "aec4f0ff23eb1634482baa2dedd7419e0b4e6ed1", "commit_訊息": "[流程引擎]Q00-20220208004 修正「已轉派的工作」清單，在「全部」頁籤取得的資料筆數與「處理中」、「已處理」兩個頁籤相加的數量不符", "提交日期": "2022-02-25 16:45:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e74ea12207c1115678a815ee3fcabea92138baa8", "commit_訊息": "[Web]Q00-20220224001 修正維護樣板作業從PC版切換到行動版時資料無法顯示", "提交日期": "2022-02-24 16:16:23", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e85114d1dd0e1ceeb83edeeec271fb4fab41c5da", "commit_訊息": "[Web]S00-20211117003 流程資料的頁面排序調整以發起時間大到小排序(DESC)", "提交日期": "2022-02-24 14:47:46", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDataSearchListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba8bcd4cce471874e66def2a5f89766aeb94ecdd", "commit_訊息": "[Web]Q00-20220223001 修正腳本樣板使用QRCode語法，儲存表單時QRCode消失", "提交日期": "2022-02-23 17:37:37", "作者": "ocean_yeh", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-qrcode.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "987652de76c48e4cb99cef539c747054fed718ff", "commit_訊息": "[BPM APP]C01-*********** 移動授權中間層的使用者維護作業增加分頁功能", "提交日期": "2022-02-23 09:07:45", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "d10a2f50b07dd8dbbc60209c7c93307efe8cb890", "commit_訊息": "[Web]Q00-20220221002 修正離職交接人作業維護離職人員開窗增加離職人員可以選擇", "提交日期": "2022-02-21 14:02:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "caaf1fdc19b424175beae6e299fb887108ce7ad1", "commit_訊息": "[流程引擎]Q00-20220221001 修正流程派送到離職人員，離職人員沒有設置離職交接人作業維護就會派送失敗", "提交日期": "2022-02-21 11:46:19", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "42b2b04a397b60affde1b9e848a0a826134fb4a7", "commit_訊息": "[Web]A00-20220216002 修正RWD表單當右下角有出現滑到頂部按鈕時，按鈕也會被列印出來", "提交日期": "2022-02-18 17:28:24", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88e6b188ca9d361cb6a27271e3256c09e4fb051c", "commit_訊息": "[Web]Q00-20220218002 修正維護樣板查詢欄位DropDown使用『請選擇』時，渲染到畫面上時該選項的順序沒出現在第一個", "提交日期": "2022-02-18 11:40:13", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1741f47e9fa7ef6dd378de4f75e081695e989057", "commit_訊息": "[Web]A00-20220214001 修正附件權限設定關卡在追蹤流程看不到的問題", "提交日期": "2022-02-17 22:58:33", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9b1b7f196ff340e06af713998584742bfd306e1c", "commit_訊息": "[Web]Q00-20220217004 TextBox有設定小數位時，使用FormUtil的寫法無法在formOpen時更換背景色", "提交日期": "2022-02-17 15:45:28", "作者": "ocean_yeh", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dfbcde1e4679da81151c59e8b4d07356e0feeaa0", "commit_訊息": "[Web]Q00-20220217002 修正流程負責人在監控流程匯出Excel，處理者欄位內容應與系統管理員的處理者欄位內容一致", "提交日期": "2022-02-17 14:19:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3cbd2a7a794220574874ffc6e512cb9440542559", "commit_訊息": "[Web]Q00-20220217001 修正使用32位元的Chrome進入BPM登入頁面會彈出BPM僅支援的瀏覽器資訊的警告框", "提交日期": "2022-02-17 14:10:05", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d74d8ec4bcb27686ca6c78edfc566850721a2670", "commit_訊息": "[BPM APP]C01-20220211002 修正客製開窗在重組SQL指令的邏輯時，在錯誤的地方插入where條件", "提交日期": "2022-02-17 13:36:24", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileDatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "26f825fb7903c03847467227b8c1e6eb8abef4ae", "commit_訊息": "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]", "提交日期": "2022-02-16 18:22:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a62a3972d0aef39c9e6732404c3762d063657cd1", "commit_訊息": "[Web]Q00-20220215002 調整讓行動版與PC版一致讓Grid只支援(a、br、input、i、button)五種html標籤", "提交日期": "2022-02-15 17:36:01", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c6c47e678d797896a7f70640090b430080bc2e0d", "commit_訊息": "[流程引擎]S00-20220210002 更新產品Base所有Mail.jar到與Wildfly15使用的1.6.2一致", "提交日期": "2022-02-15 16:28:22", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/lib/mail.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/Mail/mail.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Mail/mail.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/lib/Mail/mail.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/Mail/mail.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/lib/JavaMail/mail.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/lib/Mail/mail.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/lib/Mail/mail.jar", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "6014e79609392a3d310ec67d5ac2f0356233368b", "commit_訊息": "[流程引擎]Q00-20220215001 修正偶發附件遺失問題", "提交日期": "2022-02-15 16:11:04", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "57a696470aca1a44490487484d16a455275c235f", "commit_訊息": "[BPM APP]S00-20211027004 新增移動端支援ERP流程終止或撤銷時，若單據修改重新送審後於移動表單可查看之前審批流程的功能", "提交日期": "2022-02-15 11:35:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFromReturnRecordView.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileReturnRecordView.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "4af29dfa4782aa2c846766c5ff101e708f76ca2b", "commit_訊息": "[流程引擎]Q00-20220211001 調整簡易流程圖預先解析，當前核決層級關卡的工作處理者為原處理者的代理人時，以原處理者解析後續關卡", "提交日期": "2022-02-11 18:14:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3a16e4f6893739a149d740eecac5833cdcbb048", "commit_訊息": "[ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態", "提交日期": "2022-02-10 17:51:10", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5dc782652ec5fd69aacc32295a043c61f0fa4e2b", "commit_訊息": "[MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]", "提交日期": "2022-02-10 14:00:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b140e13de353f6369d5f6facf65c76c17adacc2", "commit_訊息": "[流程引擎]Q00-20220209001 調整追蹤流程接口邏輯，當追蹤流程url中未包含表單定義ID時，開放給流程處理者與系統管理員查看流程", "提交日期": "2022-02-09 18:06:33", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8081fac947c04007b0a88a2382921307e2ae0cab", "commit_訊息": "[流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過", "提交日期": "2022-02-08 18:03:20", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9ccb1f4c4b4cc9cc4fe4c25f86d6c647b7b20ec7", "commit_訊息": "[Web]Q00-20220208002 單身加總欄位設定「顯示至小數點後第X位」且在其他欄位的運算規則中，單身加總數值改變後沒有觸發欄位運算", "提交日期": "2022-02-08 17:06:17", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3eea83a580067309357e69bb2571317dd318285c", "commit_訊息": "[流程引擎]A00-20220127001修正流程退回重辦選擇\"按照流程定義依序重新執行\"，關卡會經過\"服務任務\"會導致主旨的退回重辦標籤沒有顯示", "提交日期": "2022-02-08 16:08:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "30efd8539ae87a257289dbd7314571245a4e92dc", "commit_訊息": "[流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程", "提交日期": "2022-02-08 14:49:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5f7a95cf9dbe1fd008f055c10383f956bcb88b0", "commit_訊息": "[BPM APP]C01-20220207001 修正企業微信的待辦推播若已被處理過導向追蹤已處理的表單畫面時缺少的多語系", "提交日期": "2022-02-08 13:37:02", "作者": "郭哲榮", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79b9995548d30cc44b63bd27761a0f70b0783639", "commit_訊息": "[Web]A00-20220121001修正從工作通知從郵件進入，點擊\"回到工作清單\"按紐會應該要回到工作通知清單而不是待辦清單", "提交日期": "2022-01-26 10:41:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f477d505da8d3eca250e46c9c65ef3fbb1420005", "commit_訊息": "[ISO]S00-20210507001 PDF浮水印屬性管理新增「圖片浮水印」功能(BCL8)", "提交日期": "2022-01-24 17:47:23", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8873ac57f3746df086f55ba2839f40cf56636d9", "commit_訊息": "[表單設計師]A00-20220120002 修正欄位樣板的三欄式和四欄式的最右邊欄位切分2欄後，再次編輯會變回切分1欄[補]", "提交日期": "2022-01-24 14:20:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a9ff5749ee2675c1c691510f8fef2475fa6d083", "commit_訊息": "[表單設計師]S00-20210915004 新增Grid元件可設定新增、修改、删除按鈕的顏色屬性", "提交日期": "2022-01-24 10:52:30", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ListElementDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/util.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "bcb82cc73eef1047913ad1de318598cd36d78aef", "commit_訊息": "[內部]Q00-20211115001新增GuardService連線成功的提示訊息", "提交日期": "2022-01-22 16:18:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0c42ed39879332045a3586d09895c85c51d8d896", "commit_訊息": "[MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題", "提交日期": "2022-01-22 11:40:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4c68ba9500c8c2ba3fbffadbdb1af47029ee8bb6", "commit_訊息": "[表單設計師]A00-20220120002 修正欄位樣板的三欄式和四欄式的最右邊欄位切分2欄後，再次編輯會變回切分1欄", "提交日期": "2022-01-21 17:58:43", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4fab4a2654835ced12813bb6ad8e6988830cc03e", "commit_訊息": "[BPM APP]C01-20220118001 修正行動端表單多個Checkbox元件且相同元件名稱時簽核後勾選的選項值不會顯示問題", "提交日期": "2022-01-21 17:42:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa32f20fdc47b5d47e70736a3f57dfb08f0ce7a4", "commit_訊息": "[表單設計師]S00-20211117004 調整RWD表單複合元件開窗類型為自定義開窗時參與者型態增加部門/專案", "提交日期": "2022-01-21 16:48:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/util.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4a509e421889f32ae8b90d8b14ab22db3e8cebe9", "commit_訊息": "[表單設計師]S00-20210811002 調整Web表單設計器日期時間元件比較欄位和TextBox數值欄位運算規則過濾元件本身代號", "提交日期": "2022-01-21 16:25:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "787335e0f03f84c2c315d925ef3ec524adebc108", "commit_訊息": "[Web]A00-20220120001修正IE開起流程用SQLcommand因為用replaceall函式導致報錯", "提交日期": "2022-01-21 11:39:58", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d4b492528f6689d8c6bed291709d5421bd0e280", "commit_訊息": "[Web]Q00-20220120003 流程代理人設定的選擇流程開窗，預設用流程代號做排序", "提交日期": "2022-01-20 18:23:35", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "607f4206c5382d7ad1317925224ff6bff9570dcc", "commit_訊息": "[流程引擎]Q00-20220120001 修正「使用者有多個部門，在選擇發起部門後若發起流程失敗，回到表單頁面後無法再發起流程或儲存表單」問題", "提交日期": "2022-01-20 16:51:55", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c75f2bd6cff7b9722809caddaad982ee620b18e0", "commit_訊息": "[流程引擎]A00-20211221001 修正「第一次發起流程因使用者填寫的表單資料有誤導致發起失敗，在使用者更正表單後仍無法發起流程」的問題", "提交日期": "2022-01-20 16:50:48", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}