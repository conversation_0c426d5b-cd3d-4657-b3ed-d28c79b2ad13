# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.5.1
- **舊版本**: release_5.8.4.3
- **生成時間**: 2025-07-18 11:43:18
- **新增 Commit 數量**: 188

## 變更摘要

### lorenchang (13 commits)

- **2022-06-26 22:27:16**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.1
  - 變更檔案: 25 個
- **2020-12-07 19:22:37**: [Web]Q00-20201207005 修正舊客購入越南語系模組，版更後模組及程式缺少越南語系
  - 變更檔案: 2 個
- **2020-12-01 17:37:36**: [內部]合併5.8.5.1 brach多語系至總表
  - 變更檔案: 2 個
- **2020-11-30 16:05:46**: [Web]C01-20200722004 顯示流程清單頁之流程筆數調整為採用設定檔之設定[補]
  - 變更檔案: 1 個
- **2020-11-04 17:39:07**: Q00-20201104003 修正「攻略雲指標維護作業」點「確定」按鈕後出現錯誤Invalid column name 'indicatorCalculationType'
  - 變更檔案: 1 個
- **2020-11-27 16:43:36**: [流程引擎]C01-***********修正簽核時，系統會有卡住的狀況[補]
  - 變更檔案: 1 個
- **2020-11-25 13:23:38**: [組織設計] 簡稱為空白在某些環境開啟職稱維護作業會出現異常
  - 變更檔案: 1 個
- **2020-11-25 12:03:02**: Revert "[流程引擎] 修正多AP(A/B)時流程在A進核決層級，在另一主機簽核該流程時會出現找不到Entity的問題"
  - 變更檔案: 2 個
- **2020-11-19 16:53:15**: [組織設計師] 調整組織設計師，避免因為Cache資料有問題導致的非預期的異常
  - 變更檔案: 2 個
- **2020-11-19 16:40:50**: [流程引擎] 修正多AP(A/B)時流程在A進核決層級，在另一主機簽核該流程時會出現找不到Entity的問題
  - 變更檔案: 2 個
- **2020-11-19 16:33:48**: [流程引擎] 修正5.8版SessionBeanApplication重覆更新的異常
  - 變更檔案: 1 個
- **2020-11-19 16:02:55**: [流程引擎]Q00-20201111001 修正點選發起流程報錯、我的最愛打不開[補]
  - 變更檔案: 1 個
- **2020-11-05 17:11:32**: [Web]Q00-20201105004 修正表單自適應寬度調整時，Grid沒有重新渲染
  - 變更檔案: 1 個

### pinchi_lin (37 commits)

- **2020-12-18 17:56:09**: [BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]
  - 變更檔案: 1 個
- **2020-12-18 13:40:04**: [BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]
  - 變更檔案: 2 個
- **2020-12-18 11:12:50**: [BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]
  - 變更檔案: 1 個
- **2020-12-17 20:06:56**: [BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得
  - 變更檔案: 12 個
- **2020-12-17 11:28:33**: [內部]修正IMG從推播通知進入簽核後無法提交派送問題
  - 變更檔案: 1 個
- **2020-12-17 11:24:53**: [BPM APP]調整IMG的互聯認證接口支持互聯3.0.1版本
  - 變更檔案: 20 個
- **2020-12-10 20:59:06**: [內部]Q00-20201210005 修正動態渲染表單向前加簽後無法連續簽核問題
  - 變更檔案: 1 個
- **2020-12-08 18:44:53**: [BPM APP]Q00-20200827002 修正LINE的部分推播通知會顯示郵件內文範本tag問題
  - 變更檔案: 2 個
- **2020-12-04 20:21:27**: [BPM APP]Q00-*********** 修正LINE在終止流程的推播通知會顯示簽核歷程問題
  - 變更檔案: 1 個
- **2020-12-04 19:58:05**: [BPM APP]Q00-*********** 修正釘釘與企業微信登入驗證時若BPM用戶未啟用的錯誤訊息顯示不全問題
  - 變更檔案: 3 個
- **2020-12-03 15:44:08**: [BPM APP]Q00-20200926005 修正ESS表單grid欄位在中間層顯示順序異常問題
  - 變更檔案: 4 個
- **2020-12-01 11:02:08**: [BPM APP]Q00-20200926003 修正IMG中間層Grid不會依照流程設定顯示問題
  - 變更檔案: 1 個
- **2020-12-01 10:08:27**: [BPM APP]Q00-20201109001 調整IMG統計元件文字顏色
  - 變更檔案: 1 個
- **2020-11-27 20:08:02**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 3 個
- **2020-11-27 20:06:17**: [BPM APP]新增動態渲染表單轉派功能[補]
  - 變更檔案: 2 個
- **2020-11-27 18:12:03**: [BPM APP]新增動態渲染表單轉派功能[補]
  - 變更檔案: 1 個
- **2020-11-27 18:10:18**: [BPM APP]新增動態渲染表單轉派功能[補]
  - 變更檔案: 5 個
- **2020-11-26 20:07:26**: [BPM APP]新增動態渲染表單轉派功能[補]
  - 變更檔案: 1 個
- **2020-11-26 17:38:19**: [BPM APP]新增動態渲染表單轉派功能[補]
  - 變更檔案: 3 個
- **2020-11-26 16:52:47**: [BPM APP]新增動態渲染表單轉派功能[補]
  - 變更檔案: 7 個
- **2020-11-26 12:03:38**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 2 個
- **2020-11-26 11:17:57**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 4 個
- **2020-11-25 10:28:19**: [BPM APP]IMG動態渲染表單新增加簽功能[補]
  - 變更檔案: 1 個
- **2020-11-24 19:59:45**: [BPM APP]IMG動態渲染表單新增加簽功能[補]
  - 變更檔案: 2 個
- **2020-11-24 18:38:22**: [BPM APP]IMG動態渲染表單新增加簽功能[補]
  - 變更檔案: 2 個
- **2020-11-23 10:48:53**: [BPM APP]IMG動態渲染表單新增加簽功能
  - 變更檔案: 11 個
- **2020-11-20 18:51:39**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 1 個
- **2020-11-19 14:37:42**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 1 個
- **2020-11-12 20:11:03**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 3 個
- **2020-11-12 14:35:37**: [BPM APP]優化IMG列表接口取動態渲染表單應用ID時的速度
  - 變更檔案: 16 個
- **2020-11-12 11:20:06**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 1 個
- **2020-11-12 11:16:30**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 3 個
- **2020-11-05 16:20:39**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 3 個
- **2020-10-29 19:18:24**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
  - 變更檔案: 2 個
- **2020-10-23 10:52:58**: [BPM APP]調整IMG接口與推播功能可支持雲整合方案
  - 變更檔案: 8 個
- **2020-10-22 14:59:10**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 2 個
- **2020-10-22 14:52:48**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合
  - 變更檔案: 6 個

### waynechang (7 commits)

- **2020-12-17 17:39:31**: [Web]S00-20201120001 調整報表註冊器設定SQL註冊器時，若SQL語法使用系統變數時，須加上單引號(此邏輯同資料選取器、SQL註冊器、SQLCommand)
  - 變更檔案: 1 個
- **2020-09-30 14:02:09**: [組織設計師]A00-20200929001 修正無法透過介面刪除組織資料
  - 變更檔案: 4 個
- **2020-11-27 16:06:12**: [ISO]Q00-20201127002 提供ISO文件檔案的URL下載服務
  - 變更檔案: 1 個
- **2020-11-09 11:05:33**: [Web]Q00-20201109002 修正流程設定關卡的表單為唯讀時，formSave後，不會再次執行formOpen
  - 變更檔案: 1 個
- **2020-11-04 13:53:47**: [ISO]A00-20201103002 修正ISO絕對位置變更單，文件開窗若透過簡易查詢時會撈出申請變更中的文件
  - 變更檔案: 1 個
- **2020-11-03 16:27:19**: [ISO]Q00-20201103002 調整BCL8 轉檔錯誤時的log，以便分析錯誤原因
  - 變更檔案: 1 個
- **2020-10-30 11:41:58**: [Web]C01-20201019003 調整首頁模塊嵌入BPM代辦畫面時，表單Grid顯示異常
  - 變更檔案: 1 個

### 詩雅 (25 commits)

- **2020-12-11 12:12:19**: [內部]Q00-20201211002 修正在IE在入口平台整合設定，新增鼎捷移動連線資訊，"是否整合鼎捷雲"的選項元件點擊時發生錯誤
  - 變更檔案: 1 個
- **2020-12-10 18:49:48**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2020-12-10 18:48:42**: [內部]調整入口平台整合設定測試連線，整合鼎捷雲時，排除建議步驟說明
  - 變更檔案: 3 個
- **2020-12-07 19:16:39**: [內部]Q00-*********** 調整使用者批次匯入，增加判斷excel的內容資料格式正確性[補]
  - 變更檔案: 2 個
- **2020-12-04 13:41:07**: [內部]Q00-*********** 調整使用者批次匯入，增加判斷excel的內容資料格式正確性
  - 變更檔案: 5 個
- **2020-12-03 17:20:53**: [內部]Q00-*********** 修正Mobile的SQLcreate語法
  - 變更檔案: 2 個
- **2020-12-03 16:19:41**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2020-12-03 16:16:47**: [內部]Q00-20201202017 修正會議簽到列表字段接口，在IMG應用配置顯示亂碼
  - 變更檔案: 1 個
- **2020-11-20 17:20:51**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 10 個
- **2020-11-19 10:28:33**: [BPM APP]新增動態渲染表單轉派功能[補]
  - 變更檔案: 4 個
- **2020-11-18 20:29:31**: [BPM APP]新增動態渲染表單轉派功能
  - 變更檔案: 4 個
- **2020-11-18 20:25:41**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 11 個
- **2020-11-12 12:02:16**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 1 個
- **2020-11-12 11:59:13**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 2 個
- **2020-11-04 20:36:15**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 4 個
- **2020-10-29 17:08:03**: [BPM APP]修正多語系檔案
  - 變更檔案: 1 個
- **2020-10-29 15:59:41**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 4 個
- **2020-10-13 09:47:40**: [BPM APP]新增行動端會議簽到功能[補]
  - 變更檔案: 1 個
- **2020-10-07 18:12:40**: [BPM APP]新增行動端會議簽到功能[補]
  - 變更檔案: 9 個
- **2020-09-30 12:16:56**: [BPM APP]新增行動端會議簽到功能[補]
  - 變更檔案: 4 個
- **2020-09-25 19:10:59**: [BPM APP]新增行動端會議簽到功能[補]
  - 變更檔案: 1 個
- **2020-09-25 17:03:58**: [BPM APP]新增行動端會議簽到功能[補]
  - 變更檔案: 2 個
- **2020-09-25 16:56:11**: [BPM APP]新增行動端會議簽到功能
  - 變更檔案: 11 個
- **2020-11-16 19:17:06**: [BPM APP]C01-20201111003 修正在自訂義開窗搜尋時會異常的問題
  - 變更檔案: 1 個
- **2020-11-03 11:16:55**: [BPM APP]C01-20201016007 修正Grid操作按鈕(新刪修)可依各個Grid設定顯示/隱藏
  - 變更檔案: 2 個

### yamiyeh10 (34 commits)

- **2020-12-11 10:27:28**: [內部]Ｑ00-20201211001 修正在IE和Safari瀏覽器頁面使用入口平台整合設定可新增多組問題
  - 變更檔案: 1 個
- **2020-12-10 18:47:06**: [BPM APP]Q00-20201210002 調整動態渲染表單的附件支持內網使用
  - 變更檔案: 1 個
- **2020-12-10 18:30:41**: [BPM APP]調整SystemVariable中BPM APP平台整合與服務位址設定的描述說明[補]
  - 變更檔案: 2 個
- **2020-12-08 10:01:16**: [內部]Q00-20201207001 修正入口平台整合設定連線管理資訊出現scroll bar時畫面跑版[補]
  - 變更檔案: 1 個
- **2020-12-07 19:10:09**: [內部]Q00-20201202002 調整入口平台整合企業微信與鼎捷移動時，微信使用者不可操作IMG相關功能[補]
  - 變更檔案: 3 個
- **2020-12-07 17:09:59**: [BPM APP]Q00-20201202007 調整使用者管理新增使用者時的多語系[補]
  - 變更檔案: 1 個
- **2020-12-07 16:39:52**: [內部]Q00-20201207004 修正企業微信使用者管理頁面的啟用狀態顯示為2
  - 變更檔案: 1 個
- **2020-12-07 16:25:16**: [BPM APP]Q00-20201207002 修正在企業微信編輯我的最愛時會將返回不儲存的紀錄一併儲存問題
  - 變更檔案: 1 個
- **2020-12-07 15:22:11**: [內部]Q00-20201207001 修正入口平台整合設定連線管理資訊出現scroll bar時畫面跑版
  - 變更檔案: 1 個
- **2020-12-02 20:11:18**: [BPM APP]Q00-20201202008 修正使用者管理頁面微信使用者匯出樣版手機號碼範例格式錯誤問題
  - 變更檔案: 1 個
- **2020-12-02 20:00:25**: [BPM APP]Q00-20201202007 調整使用者管理新增使用者時的多語系
  - 變更檔案: 1 個
- **2020-12-02 19:44:28**: [內部]Q00-20201202004 調整入口平台整合單一方案時其他工具佈署網址應顯示對應設定資訊問題
  - 變更檔案: 1 個
- **2020-12-02 19:08:40**: [內部]Q00-20201202003 修正入口平台整合企業微信與鼎捷移動時，匯入功能異常問題
  - 變更檔案: 1 個
- **2020-12-02 18:40:52**: [內部]Q00-20201202002 調整入口平台整合企業微信與鼎捷移動時，微信使用者不可操作IMG相關功能
  - 變更檔案: 2 個
- **2020-12-02 17:11:41**: [BPM APP]Q00-20201202010 修正系統設定的BPMAPP鼎捷移動平台整合設定必須重啟才生效問題
  - 變更檔案: 1 個
- **2020-12-02 17:06:30**: [BPM APP]Q00-20201202005 調整系統設定中BPMAPP平台整合設定為0時不應顯示入口平台整合設定
  - 變更檔案: 1 個
- **2020-12-02 15:18:33**: [內部]Q00-20201202001 修正其他工具佈署網址中的統計元件配置點擊儲存時會儲存失敗問題
  - 變更檔案: 1 個
- **2020-12-01 11:17:54**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 5 個
- **2020-11-27 19:17:28**: [BPM APP]調整入口平台整合資訊新增後需重啟才會讀到設定資料
  - 變更檔案: 1 個
- **2020-11-27 19:14:02**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 1 個
- **2020-11-27 18:52:55**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 9 個
- **2020-11-26 15:13:25**: [BPM APP]調整SystemVariable中BPM APP平台整合與服務位址設定的描述說明
  - 變更檔案: 2 個
- **2020-11-26 11:30:16**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 1 個
- **2020-11-25 18:22:52**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 4 個
- **2020-11-25 15:31:30**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 4 個
- **2020-11-25 09:17:59**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 1 個
- **2020-11-24 14:11:39**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 4 個
- **2020-11-19 14:03:43**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 4 個
- **2020-11-17 15:23:11**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 1 個
- **2020-11-16 19:14:31**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 8 個
- **2020-11-16 14:41:49**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 8 個
- **2020-11-04 14:08:15**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 10 個
- **2020-09-29 18:38:34**: [BPM APP]新增行動端會議簽到功能[補]
  - 變更檔案: 2 個
- **2020-10-30 18:53:36**: [BPM APP]Q00-20201030005 在企業微信使用iOS手機若附件是掃描產生的PDF有時會發生模糊問題
  - 變更檔案: 17 個

### cherryliao (30 commits)

- **2020-12-10 19:10:32**: [內部]Q00-20201210004 修正入口平台整合設定沒有任何連線資訊出現紅字訊息的問題
  - 變更檔案: 1 個
- **2020-12-10 18:50:53**: [內部]Q00-20201210003 修正使用者管理維護作業中新增使用者，選擇一使用者後不會自動關閉視窗問題
  - 變更檔案: 1 個
- **2020-12-03 17:12:17**: [內部]Q00-20201202013 調整入口平台無整合資訊時使用者維護管理不該顯示批次維護頁籤的問題
  - 變更檔案: 1 個
- **2020-12-03 16:16:19**: [內部]Q00-20201202012 調整若入口平台無任何連線資訊時其他工具佈署提示資訊的畫面
  - 變更檔案: 2 個
- **2020-12-03 14:45:01**: [內部]Q00-20201202006 調整系統設定中BPMAPP平台整合設定為1或2時若入口平台含已停用的資訊其提示訊息沒出現的問題
  - 變更檔案: 1 個
- **2020-11-27 19:04:20**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 4 個
- **2020-11-26 17:07:11**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 5 個
- **2020-11-25 17:35:38**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 5 個
- **2020-11-24 14:46:03**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 2 個
- **2020-11-23 18:45:06**: [BPM APP]新增動態渲染表單轉派功能[補]
  - 變更檔案: 9 個
- **2020-11-18 14:00:07**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 1 個
- **2020-11-18 13:33:54**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 4 個
- **2020-11-16 14:07:27**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 8 個
- **2020-11-13 18:41:08**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 7 個
- **2020-11-13 09:53:52**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 9 個
- **2020-11-06 18:35:03**: [BPM APP]Q00-20201103004 調整行動端腳本樣版設定元件字體顏色的說明
  - 變更檔案: 2 個
- **2020-11-04 15:13:13**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 2 個
- **2020-11-02 18:28:15**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 3 個
- **2020-11-03 16:54:00**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 3 個
- **2020-10-30 09:35:27**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 3 個
- **2020-10-29 15:42:38**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 1 個
- **2020-10-28 14:26:53**: [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
  - 變更檔案: 16 個
- **2020-10-20 12:01:47**: [BPM APP]S00-20200724001 調整IMG詳情表單中的簽核歷程排序問題
  - 變更檔案: 11 個
- **2020-10-08 17:19:34**: [BPM APP]Q00-20200929001 調整行動端以表單函式庫設定元件標籤無法載入所設定標籤文字的問題
  - 變更檔案: 3 個
- **2020-09-26 10:08:47**: [BPM APP]新增行動端會議簽到功能[補]
  - 變更檔案: 1 個
- **2020-09-21 11:30:08**: [Web]S00-20200713001 調整功能清單中掛載模組的排序方式[補]
  - 變更檔案: 2 個
- **2020-11-10 19:58:33**: [BPM APP]C01-20201110002 修正企業微信在直接進入表單畫面時會因使用者尚未登入而導致畫面異常問題
  - 變更檔案: 5 個
- **2020-11-09 11:22:49**: [BPM APP]Q00-20201106002 修正行動端表單元件同時設定字體顏色和文字對齊樣式時，文字對齊樣式會失效的問題
  - 變更檔案: 1 個
- **2020-11-02 15:50:41**: [Web]S00-20200812003 調整多人簽核關卡含離職人員時仍持續通知管理員的問題
  - 變更檔案: 1 個
- **2020-10-30 11:23:33**: [BPM APP]C01-20201028004 修正若表單有設定文字對齊下拉選單無法點擊展開的問題
  - 變更檔案: 1 個

### 林致帆 (13 commits)

- **2020-12-10 11:51:18**: [流程引擎]Q00-20201210001 增加log：整合接口的Request XMl
  - 變更檔案: 1 個
- **2020-10-29 15:01:40**: [內部]Q00-20201029003 StartNextActInstBean調整log內容
  - 變更檔案: 1 個
- **2020-10-26 16:28:03**: [流程引擎]Q00-20201026001 修正流程派送到下一關，流程下一關會有偶發性的狀況無法啟動
  - 變更檔案: 3 個
- **2020-10-07 15:03:16**: [內部]Q00-20201007001 調整 SystemVariable設定檔，流程主旨標示作廢的的四個設定値的敘述內容
  - 變更檔案: 2 個
- **2020-09-08 18:41:58**: [Web]C01-20200722004 顯示流程清單頁之流程筆數調整為採用設定檔之設定
  - 變更檔案: 6 個
- **2020-11-27 17:20:18**: [Web]Q00-20201127003修正使用者名稱有底線符號，造成流程加簽後，流程實例查看異常
  - 變更檔案: 1 個
- **2020-11-20 15:48:35**: [流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確
  - 變更檔案: 1 個
- **2020-11-18 16:37:05**: [TipTop]Q00-20201118004 修正TIPTOP端簽核表單，開啟表單就報錯
  - 變更檔案: 1 個
- **2020-11-18 16:02:25**: [Web]Q00-20201118003 修正流程點選取回重辦會報錯
  - 變更檔案: 2 個
- **2020-11-18 08:36:33**: [流程引擎]Q00-20201118001 修正流程實例刪除，造成流程定義開啓異常
  - 變更檔案: 1 個
- **2020-11-10 16:26:22**: [Web]Q00-20201110001 修正舊版絕對位置表單轉成RWD會異常
  - 變更檔案: 1 個
- **2020-11-05 14:20:29**: [Web]Q00-20201105002 修正流程中核決關卡沒有處理者，在跳過核決關卡後，查看流程圖會無法開啟
  - 變更檔案: 3 個
- **2020-10-30 14:36:03**: [Web]Q00-20201030002 修正表單點擊Attachment元件，上傳附件時設定權限，套用範圍選擇關卡，無法選到核決權限關卡
  - 變更檔案: 2 個

### 王鵬程 (11 commits)

- **2020-12-09 18:24:24**: [Web]Q00-20201209004 修正RWD的Grid欄位數太多時，用產品提供欄位寬度修改語法，欄寬未改變
  - 變更檔案: 1 個
- **2020-09-18 14:22:37**: [流程引擎]A00-20200812003 修正當要做組織同步的資料量太大時，會導致組織同步出現錯誤[補]
  - 變更檔案: 1 個
- **2020-09-04 09:49:33**: [流程引擎]A00-20200812003 修正當要做組織同步的資料量太大時，會導致組織同步出現錯誤
  - 變更檔案: 2 個
- **2020-11-27 18:01:45**: [Web]Q00-20201127004 修正從外部連結進流程圖無法完整顯示
  - 變更檔案: 1 個
- **2020-11-26 11:54:09**: [Web]C01-20201117001 修正在模組程式維護中修改系統預設模組頁面，將輸入框和按鈕改為禁用，儲存鈕隱藏
  - 變更檔案: 1 個
- **2020-11-24 18:08:17**: [流程設計師]Q00-20201124002 修正流程有設可重定義屬性參考表單元件時，當刪除該元件後將無法點選可重定義屬性
  - 變更檔案: 1 個
- **2020-11-20 15:24:31**: [Web]Q00-20201120002 修正在批次簽核的完成頁面，主旨有html時只能以文字呈現而不能以html呈現
  - 變更檔案: 1 個
- **2020-11-13 12:01:35**: [表單設計師]Q00-20201113002 修正在IE中用FormUtil提供dropdown取選取值和checkbox設定值的方法會報錯
  - 變更檔案: 1 個
- **2020-11-09 18:39:18**: [Web]Q00-20201109004 修正當鎖定工具列時，在簽核意見欄位和流程主旨欄位下方顯示的片語會被擋住
  - 變更檔案: 2 個
- **2020-11-09 17:49:45**: [表單設計師]Q00-20201109003 修正當Date元件勾選顯示時間並有和其他Date元件比較時，時間不會被比較
  - 變更檔案: 3 個
- **2020-11-04 15:49:38**: [Web]A00-20201014001 修正當Grid繫結ListBox且多選加到Grid，點選有多選的Row沒有mark繫結元件選取值
  - 變更檔案: 1 個

### yanann_chen (14 commits)

- **2020-11-26 15:41:06**: [流程引擎]A00-20201118001 修正使用者追蹤流程篩選流程發起人查詢無效
  - 變更檔案: 1 個
- **2020-11-24 14:47:11**: [內部]Q00-20201124001 調整5.5.6.1_MSSQL.sql
  - 變更檔案: 1 個
- **2020-11-23 16:03:58**: [內部]Q00-20201123001 調整5.8.2.1_DML_Oracle_1.sql
  - 變更檔案: 1 個
- **2020-11-19 14:58:50**: [內部]Q00-20201119001 調整SQL指令，補上";--"
  - 變更檔案: 1 個
- **2020-11-16 17:55:35**: [Web]Q00-20201116002 修正Check Box最後一個選項沒有額外輸入框時，Check Box必填判斷功能異常
  - 變更檔案: 1 個
- **2020-11-16 14:34:02**: [Web]Q00-20201116001 修正Radio Button最後一個選項沒有額外輸入框時，Radio Button必填判斷功能異常
  - 變更檔案: 1 個
- **2020-11-11 16:16:16**: [流程引擎]Q00-20201111001 修正點選發起流程報錯、我的最愛打不開
  - 變更檔案: 6 個
- **2020-11-11 13:42:54**: [Web]A00-20201110001 修正追蹤轉會流程異常
  - 變更檔案: 1 個
- **2020-11-10 16:57:00**: [Web]Q00-20201110002 修正Ajax Service沒有帶出員工所屬部門資料
  - 變更檔案: 1 個
- **2020-11-10 11:33:50**: [內部]Q00-20201104002 調整update sql
  - 變更檔案: 74 個
- **2020-11-05 11:49:30**: [流程引擎]Q00-20201105001 修正選擇發起流程的組織單位清單中顯示已失效部門
  - 變更檔案: 1 個
- **2020-11-02 17:17:44**: [內部]Q00-20201102002 調整程式排版
  - 變更檔案: 1 個
- **2020-10-30 17:14:50**: [流程引擎]Q00-20201030004 修正排程AutoRegetWorkAssignmentHandler執行時出現錯誤
  - 變更檔案: 1 個
- **2020-10-30 15:57:40**: [內部]Q00-20201030003 調整程式排版
  - 變更檔案: 1 個

### gaspard.shih (4 commits)

- **2020-11-24 16:00:49**: [流程引擎]C01-***********修正簽核時，系統會有卡住的狀況
  - 變更檔案: 21 個
- **2020-11-11 14:07:29**: [Web]作業程序書的關卡清單改用關卡ID進行排序
  - 變更檔案: 1 個
- **2020-11-11 11:55:00**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2020-11-11 11:54:32**: [Web]BPM首頁於小螢幕時可顯示「可用時間」資訊
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.1
- **Commit ID**: `e7917333eea06db96446ff072ea8f0ea122ad7e9`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:27:16
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]
- **Commit ID**: `08a8fd57c614918597938ff94606924115cab4fb`
- **作者**: pinchi_lin
- **日期**: 2020-12-18 17:56:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 3. [BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]
- **Commit ID**: `1f7ed638573a0a53f41adead5a52e83956de8370`
- **作者**: pinchi_lin
- **日期**: 2020-12-18 13:40:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 4. [BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得[補]
- **Commit ID**: `19ba24efe4df604deb1731756b5cf2fd8667fc5f`
- **作者**: pinchi_lin
- **日期**: 2020-12-18 11:12:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`

### 5. [BPM APP]調整IMG取動態渲染表單應用ID改為依照表單ID與版本來取得
- **Commit ID**: `0c6c754e96b352ebe36dc36a362977873fae14e5`
- **作者**: pinchi_lin
- **日期**: 2020-12-17 20:06:56
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 6. [Web]S00-20201120001 調整報表註冊器設定SQL註冊器時，若SQL語法使用系統變數時，須加上單引號(此邏輯同資料選取器、SQL註冊器、SQLCommand)
- **Commit ID**: `039d550165748dc4473f140d93a7d59366cc0117`
- **作者**: waynechang
- **日期**: 2020-12-17 17:39:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 7. [內部]修正IMG從推播通知進入簽核後無法提交派送問題
- **Commit ID**: `b4480150bf3af22dea00acd80b9e998b83021a3c`
- **作者**: pinchi_lin
- **日期**: 2020-12-17 11:28:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 8. [BPM APP]調整IMG的互聯認證接口支持互聯3.0.1版本
- **Commit ID**: `c5d6ae270723d3d3f9e086a4809fb55f9f96e90a`
- **作者**: pinchi_lin
- **日期**: 2020-12-17 11:24:53
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformDAPService.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleModuleFeatures.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/RestfulWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java`

### 9. [內部]Q00-20201211002 修正在IE在入口平台整合設定，新增鼎捷移動連線資訊，"是否整合鼎捷雲"的選項元件點擊時發生錯誤
- **Commit ID**: `7956673034e75bdfe10f521d42f58fdbac01029c`
- **作者**: 詩雅
- **日期**: 2020-12-11 12:12:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`

### 10. [內部]Ｑ00-20201211001 修正在IE和Safari瀏覽器頁面使用入口平台整合設定可新增多組問題
- **Commit ID**: `a66347255ac780ed0cc120aebb7d5a0b5b5c1585`
- **作者**: yamiyeh10
- **日期**: 2020-12-11 10:27:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 11. [內部]Q00-20201210005 修正動態渲染表單向前加簽後無法連續簽核問題
- **Commit ID**: `75e709bd84742a991dccb0069f30acf5d5dc5e29`
- **作者**: pinchi_lin
- **日期**: 2020-12-10 20:59:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 12. [內部]Q00-20201210004 修正入口平台整合設定沒有任何連線資訊出現紅字訊息的問題
- **Commit ID**: `d61483ff5c80f1973aa93aa09344e50a1c4921dd`
- **作者**: cherryliao
- **日期**: 2020-12-10 19:10:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`

### 13. [內部]Q00-20201210003 修正使用者管理維護作業中新增使用者，選擇一使用者後不會自動關閉視窗問題
- **Commit ID**: `da323615587964e8e087907e40c1af0b6f36b51d`
- **作者**: cherryliao
- **日期**: 2020-12-10 18:50:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js`

### 14. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `f6caf01956775523176b8f1fac80a2668532047c`
- **作者**: 詩雅
- **日期**: 2020-12-10 18:49:48
- **變更檔案數量**: 0

### 15. [內部]調整入口平台整合設定測試連線，整合鼎捷雲時，排除建議步驟說明
- **Commit ID**: `72607db590111ce47fc436a5dd95f8a9f7921a45`
- **作者**: 詩雅
- **日期**: 2020-12-10 18:48:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 16. [BPM APP]Q00-20201210002 調整動態渲染表單的附件支持內網使用
- **Commit ID**: `620633fc3dab0ea1ad8e75661dbad2775d2d4ece`
- **作者**: yamiyeh10
- **日期**: 2020-12-10 18:47:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 17. [BPM APP]調整SystemVariable中BPM APP平台整合與服務位址設定的描述說明[補]
- **Commit ID**: `c8873743a4e75864b6798e708e9011928b333cb5`
- **作者**: yamiyeh10
- **日期**: 2020-12-10 18:30:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 18. [流程引擎]Q00-20201210001 增加log：整合接口的Request XMl
- **Commit ID**: `6a19642a9e5cc1aa722e959779ff2c77c932b36e`
- **作者**: 林致帆
- **日期**: 2020-12-10 11:51:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 19. [Web]Q00-20201209004 修正RWD的Grid欄位數太多時，用產品提供欄位寬度修改語法，欄寬未改變
- **Commit ID**: `7e58771e8addfe4cb63692282e8e207c0bbc9bf2`
- **作者**: 王鵬程
- **日期**: 2020-12-09 18:24:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 20. [BPM APP]Q00-20200827002 修正LINE的部分推播通知會顯示郵件內文範本tag問題
- **Commit ID**: `0877f8a1c3233d8584b568d15ad707fbaabcc579`
- **作者**: pinchi_lin
- **日期**: 2020-12-08 18:44:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 21. [內部]Q00-20201207001 修正入口平台整合設定連線管理資訊出現scroll bar時畫面跑版[補]
- **Commit ID**: `d324c12dcef2330baf982f802e8cc21e1c45b9b0`
- **作者**: yamiyeh10
- **日期**: 2020-12-08 10:01:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 22. [Web]Q00-20201207005 修正舊客購入越南語系模組，版更後模組及程式缺少越南語系
- **Commit ID**: `6a1e7dbb537b8cce5ba5a103fdebddda1466d0ee`
- **作者**: lorenchang
- **日期**: 2020-12-07 19:22:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 23. [內部]Q00-*********** 調整使用者批次匯入，增加判斷excel的內容資料格式正確性[補]
- **Commit ID**: `8d937d23b8a92a6dd38bd5634662081eae15350e`
- **作者**: 詩雅
- **日期**: 2020-12-07 19:16:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 24. [內部]Q00-20201202002 調整入口平台整合企業微信與鼎捷移動時，微信使用者不可操作IMG相關功能[補]
- **Commit ID**: `95f2310da8e0fab45733fe822f3c4452d406d0a6`
- **作者**: yamiyeh10
- **日期**: 2020-12-07 19:10:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 25. [BPM APP]Q00-20201202007 調整使用者管理新增使用者時的多語系[補]
- **Commit ID**: `b697807cfcc10366e9608129bc9095940411d804`
- **作者**: yamiyeh10
- **日期**: 2020-12-07 17:09:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 26. [內部]Q00-20201207004 修正企業微信使用者管理頁面的啟用狀態顯示為2
- **Commit ID**: `88edfb0a38e97c233f4ae799c9c9a612eae93b9e`
- **作者**: yamiyeh10
- **日期**: 2020-12-07 16:39:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js`

### 27. [BPM APP]Q00-20201207002 修正在企業微信編輯我的最愛時會將返回不儲存的紀錄一併儲存問題
- **Commit ID**: `a4f295de643958fe38e16a67b84f87a048d491e9`
- **作者**: yamiyeh10
- **日期**: 2020-12-07 16:25:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`

### 28. [內部]Q00-20201207001 修正入口平台整合設定連線管理資訊出現scroll bar時畫面跑版
- **Commit ID**: `9464faf9e433488de45fd5eab807bba0c7204e44`
- **作者**: yamiyeh10
- **日期**: 2020-12-07 15:22:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 29. [BPM APP]Q00-*********** 修正LINE在終止流程的推播通知會顯示簽核歷程問題
- **Commit ID**: `195dba7acf1fc965f5ff0a966a146c83185427d5`
- **作者**: pinchi_lin
- **日期**: 2020-12-04 20:21:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 30. [BPM APP]Q00-*********** 修正釘釘與企業微信登入驗證時若BPM用戶未啟用的錯誤訊息顯示不全問題
- **Commit ID**: `036e2147106d921e8b857269e66cbc423a184112`
- **作者**: pinchi_lin
- **日期**: 2020-12-04 19:58:05
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`

### 31. [內部]Q00-*********** 調整使用者批次匯入，增加判斷excel的內容資料格式正確性
- **Commit ID**: `4d5344fe7d9fcead725f109c9356a6001c71298e`
- **作者**: 詩雅
- **日期**: 2020-12-04 13:41:07
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 32. [內部]Q00-*********** 修正Mobile的SQLcreate語法
- **Commit ID**: `64aeee3786cba2d172e8f1fae76df51dbf9a8620`
- **作者**: 詩雅
- **日期**: 2020-12-03 17:20:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql`

### 33. [內部]Q00-20201202013 調整入口平台無整合資訊時使用者維護管理不該顯示批次維護頁籤的問題
- **Commit ID**: `37fadf45693ed711654d9d94a24823921c2c6b42`
- **作者**: cherryliao
- **日期**: 2020-12-03 17:12:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`

### 34. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `2f09f44590354f78ad417422cdd3808a467d7751`
- **作者**: 詩雅
- **日期**: 2020-12-03 16:19:41
- **變更檔案數量**: 0

### 35. [內部]Q00-20201202017 修正會議簽到列表字段接口，在IMG應用配置顯示亂碼
- **Commit ID**: `51b4219b67b6a3f17b76e4c49e7fe655bb334b9f`
- **作者**: 詩雅
- **日期**: 2020-12-03 16:16:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 36. [內部]Q00-20201202012 調整若入口平台無任何連線資訊時其他工具佈署提示資訊的畫面
- **Commit ID**: `496d4408841d2356d2b20d31cd38b80931b50687`
- **作者**: cherryliao
- **日期**: 2020-12-03 16:16:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js`

### 37. [BPM APP]Q00-20200926005 修正ESS表單grid欄位在中間層顯示順序異常問題
- **Commit ID**: `7dc9839517e1eb3d5ee27e5d0470df6b6fe8fb5d`
- **作者**: pinchi_lin
- **日期**: 2020-12-03 15:44:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 38. [內部]Q00-20201202006 調整系統設定中BPMAPP平台整合設定為1或2時若入口平台含已停用的資訊其提示訊息沒出現的問題
- **Commit ID**: `9a06a756b765eb3a1c05a6d097061fbc1280a9e1`
- **作者**: cherryliao
- **日期**: 2020-12-03 14:45:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 39. [BPM APP]Q00-20201202008 修正使用者管理頁面微信使用者匯出樣版手機號碼範例格式錯誤問題
- **Commit ID**: `0c80876e722ab2b937b59e5dfba5a558bcc48d19`
- **作者**: yamiyeh10
- **日期**: 2020-12-02 20:11:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`

### 40. [BPM APP]Q00-20201202007 調整使用者管理新增使用者時的多語系
- **Commit ID**: `f18089b05078b7e3d255872e855fe7cfa402bdda`
- **作者**: yamiyeh10
- **日期**: 2020-12-02 20:00:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 41. [內部]Q00-20201202004 調整入口平台整合單一方案時其他工具佈署網址應顯示對應設定資訊問題
- **Commit ID**: `4aa2d8f6927690d8b39d9c525c089dd6f937b8e2`
- **作者**: yamiyeh10
- **日期**: 2020-12-02 19:44:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js`

### 42. [內部]Q00-20201202003 修正入口平台整合企業微信與鼎捷移動時，匯入功能異常問題
- **Commit ID**: `4e7cb1f3c8e46b2b74e0be0217f642a04d767539`
- **作者**: yamiyeh10
- **日期**: 2020-12-02 19:08:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 43. [內部]Q00-20201202002 調整入口平台整合企業微信與鼎捷移動時，微信使用者不可操作IMG相關功能
- **Commit ID**: `c640fca02b09f62eefda6042e4390126b81df842`
- **作者**: yamiyeh10
- **日期**: 2020-12-02 18:40:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 44. [BPM APP]Q00-20201202010 修正系統設定的BPMAPP鼎捷移動平台整合設定必須重啟才生效問題
- **Commit ID**: `a2cebeadf0fd28cedeece0e326677dc09b7373bc`
- **作者**: yamiyeh10
- **日期**: 2020-12-02 17:11:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`

### 45. [BPM APP]Q00-20201202005 調整系統設定中BPMAPP平台整合設定為0時不應顯示入口平台整合設定
- **Commit ID**: `4c913721f527f847d6ee8cfe681186e61cd35672`
- **作者**: yamiyeh10
- **日期**: 2020-12-02 17:06:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 46. [內部]Q00-20201202001 修正其他工具佈署網址中的統計元件配置點擊儲存時會儲存失敗問題
- **Commit ID**: `8a115e30b1899474d8c37f3ddab9ef38a7c96faa`
- **作者**: yamiyeh10
- **日期**: 2020-12-02 15:18:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js`

### 47. [內部]合併5.8.5.1 brach多語系至總表
- **Commit ID**: `03b73c087b7464fbfa23c8b98765dbd4f26a2ff4`
- **作者**: lorenchang
- **日期**: 2020-12-01 17:37:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 48. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `8f8fe0707f24cb6f0b880f4330417a9a1d885693`
- **作者**: yamiyeh10
- **日期**: 2020-12-01 11:17:54
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 49. [BPM APP]Q00-20200926003 修正IMG中間層Grid不會依照流程設定顯示問題
- **Commit ID**: `729fab7adf2bc17cdc8772423c733caea67fa306`
- **作者**: pinchi_lin
- **日期**: 2020-12-01 11:02:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 50. [BPM APP]Q00-20201109001 調整IMG統計元件文字顏色
- **Commit ID**: `3da7e276a36c7a4a9db1b27b616af55ad0e476f0`
- **作者**: pinchi_lin
- **日期**: 2020-12-01 10:08:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 51. [Web]C01-20200722004 顯示流程清單頁之流程筆數調整為採用設定檔之設定[補]
- **Commit ID**: `5b2e4365a01f1cb51fb1fc1e9ecada85c023d8a6`
- **作者**: lorenchang
- **日期**: 2020-11-30 16:05:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 52. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `6e7fd7bca97345625e52b8a016f4211d9c97dfef`
- **作者**: pinchi_lin
- **日期**: 2020-11-27 20:08:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterDesignFormReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 53. [BPM APP]新增動態渲染表單轉派功能[補]
- **Commit ID**: `8012807d8968c3de4bab1bd515c6fc27c3003a4a`
- **作者**: pinchi_lin
- **日期**: 2020-11-27 20:06:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 54. [BPM APP]調整入口平台整合資訊新增後需重啟才會讀到設定資料
- **Commit ID**: `e91734bdada259117bfe73fc3d576971976cceb7`
- **作者**: yamiyeh10
- **日期**: 2020-11-27 19:17:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 55. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `177d987964698d775ae4a1c8d60a1a806b6d93d6`
- **作者**: yamiyeh10
- **日期**: 2020-11-27 19:14:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 56. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `3394191efbf1a83179643a1b8d9addc101a61a70`
- **作者**: cherryliao
- **日期**: 2020-11-27 19:04:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 57. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `6994cc0ee91f1c578f2c2a6caf1cfb8bdaf5a7ac`
- **作者**: yamiyeh10
- **日期**: 2020-11-27 18:52:55
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_Oracle_1.sql`

### 58. [BPM APP]新增動態渲染表單轉派功能[補]
- **Commit ID**: `45681c473e58c88888fd6ed9eb45168390ae1e0d`
- **作者**: pinchi_lin
- **日期**: 2020-11-27 18:12:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`

### 59. [BPM APP]新增動態渲染表單轉派功能[補]
- **Commit ID**: `d7e3c25f4e4d68080e6fb94a918365b6c85c39a2`
- **作者**: pinchi_lin
- **日期**: 2020-11-27 18:10:18
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ReassignData.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SubmitInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 60. [BPM APP]新增動態渲染表單轉派功能[補]
- **Commit ID**: `16f48e9915d66e1bed5702da8787bfc4d137b4b2`
- **作者**: pinchi_lin
- **日期**: 2020-11-26 20:07:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 61. [BPM APP]新增動態渲染表單轉派功能[補]
- **Commit ID**: `f7cbfa9722ff2a0b29cbc35d4f07bd88fbd9f66c`
- **作者**: pinchi_lin
- **日期**: 2020-11-26 17:38:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`

### 62. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `7287d008647ed96f5a57578f1955aaa7cb156054`
- **作者**: cherryliao
- **日期**: 2020-11-26 17:07:11
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 63. [BPM APP]新增動態渲染表單轉派功能[補]
- **Commit ID**: `492479579ae9feba602a449d68781dd676356986`
- **作者**: pinchi_lin
- **日期**: 2020-11-26 16:52:47
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleReassignEmployeeRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReassignEmpRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataReassignEmpRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 64. [BPM APP]調整SystemVariable中BPM APP平台整合與服務位址設定的描述說明
- **Commit ID**: `e19463b550fa7e60ef6099007fea4ac4a037fbf7`
- **作者**: yamiyeh10
- **日期**: 2020-11-26 15:13:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 65. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `dfabb5a8cf4ab717eab9ed539795962c86c8027e`
- **作者**: pinchi_lin
- **日期**: 2020-11-26 12:03:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 66. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `9e95993ade535794c15deeb0c97fcf6d3f1ce59b`
- **作者**: yamiyeh10
- **日期**: 2020-11-26 11:30:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp`

### 67. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `c42e1c927967ed3a256af73764e7aa4173bfdadd`
- **作者**: pinchi_lin
- **日期**: 2020-11-26 11:17:57
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 68. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `024833a274fd840b39229c27590e6487145348c7`
- **作者**: yamiyeh10
- **日期**: 2020-11-25 18:22:52
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp`

### 69. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `e40d8d157e4f6073b5b64cc130e97c84aaa96f83`
- **作者**: cherryliao
- **日期**: 2020-11-25 17:35:38
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 70. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `200f77d744d1648124b252816cbde692117f586b`
- **作者**: yamiyeh10
- **日期**: 2020-11-25 15:31:30
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 71. [BPM APP]IMG動態渲染表單新增加簽功能[補]
- **Commit ID**: `106d811cbd573d55c009ad9a74e176e60d55128c`
- **作者**: pinchi_lin
- **日期**: 2020-11-25 10:28:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 72. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `5dc74ced07abb6fc2a1ad5fa2c715508b77d4b33`
- **作者**: yamiyeh10
- **日期**: 2020-11-25 09:17:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp`

### 73. [BPM APP]IMG動態渲染表單新增加簽功能[補]
- **Commit ID**: `571c1a371f908547b6b168607347fe49d1945460`
- **作者**: pinchi_lin
- **日期**: 2020-11-24 19:59:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 74. [BPM APP]IMG動態渲染表單新增加簽功能[補]
- **Commit ID**: `1e52d04ef62b558e5059fa0da18c9713bc9653a2`
- **作者**: pinchi_lin
- **日期**: 2020-11-24 18:38:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 75. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `22ccbc62fba66fb32f9ed39dfbc03717957a0c00`
- **作者**: cherryliao
- **日期**: 2020-11-24 14:46:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js`

### 76. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `7f3fbeb547e5fa97f94bd76c7f6366e97c03eb3b`
- **作者**: yamiyeh10
- **日期**: 2020-11-24 14:11:39
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 77. [BPM APP]新增動態渲染表單轉派功能[補]
- **Commit ID**: `ca62a0c324d9a9145066184001ca75cd9357da9b`
- **作者**: cherryliao
- **日期**: 2020-11-23 18:45:06
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DataArrayContent.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DataSetList.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDatasetList.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 78. [BPM APP]IMG動態渲染表單新增加簽功能
- **Commit ID**: `16f206a9144517cc4dadbc40502151ec6636c781`
- **作者**: pinchi_lin
- **日期**: 2020-11-23 10:48:53
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/AddActivityData.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/EmployeeInfo.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleAddActEmployeeRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterAddActEmpRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataAddActEmpRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SubmitInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 79. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `25a051bf3f612ab4521f97007936243260328078`
- **作者**: pinchi_lin
- **日期**: 2020-11-20 18:51:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 80. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `13c4e36fa15d97193035b081d7582c68c69d69a8`
- **作者**: 詩雅
- **日期**: 2020-11-20 17:20:51
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 81. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `dc2d6630ac2bed65e2ef5d233fd405fec96f1661`
- **作者**: pinchi_lin
- **日期**: 2020-11-19 14:37:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 82. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `77df18aaa6c94201ec474a0115d9ab14cb773cab`
- **作者**: yamiyeh10
- **日期**: 2020-11-19 14:03:43
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 83. [BPM APP]新增動態渲染表單轉派功能[補]
- **Commit ID**: `9b374b59e269df8318e9a8e151b814f61f599691`
- **作者**: 詩雅
- **日期**: 2020-11-19 10:28:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 84. [BPM APP]新增動態渲染表單轉派功能
- **Commit ID**: `b237d1195f14808a0516364011cfa673599da792`
- **作者**: 詩雅
- **日期**: 2020-11-18 20:29:31
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 85. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `bb676fe75ee9f28496fb1e76a80bbac00e037de1`
- **作者**: 詩雅
- **日期**: 2020-11-18 20:25:41
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 86. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `6448aa3abf543cb11cc51942e0dcf5f9f16c19fe`
- **作者**: cherryliao
- **日期**: 2020-11-18 14:00:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 87. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `b63634a46353feba864632d3a5b87ae9e4185889`
- **作者**: cherryliao
- **日期**: 2020-11-18 13:33:54
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js`

### 88. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `b86d097a11f11edc53500718afef4405d26e9a1f`
- **作者**: yamiyeh10
- **日期**: 2020-11-17 15:23:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 89. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `a8a581077f02d4fb60c6408a5ee7e81fd08453db`
- **作者**: yamiyeh10
- **日期**: 2020-11-16 19:14:31
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 90. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `1a3b3b30d688b2a9bb47a42f70d42d70a2dbd0b8`
- **作者**: yamiyeh10
- **日期**: 2020-11-16 14:41:49
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 91. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `65f0e8546bc9cee6833fd0c93c4575dee35da27c`
- **作者**: cherryliao
- **日期**: 2020-11-16 14:07:27
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DML_InitMobileDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DML_InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_Oracle_1.sql`

### 92. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `cc565cbaaffeaca9b24ae2568b14085c508c73f9`
- **作者**: cherryliao
- **日期**: 2020-11-13 18:41:08
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 93. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `8ea3d45d7fccb689379fa277af0b23ffa40c79ad`
- **作者**: cherryliao
- **日期**: 2020-11-13 09:53:52
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-restful-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageComponentUser.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileManageCommonUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 94. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `f8f41509d2e386b6d26c2a3fe5d2b8fab0a2838b`
- **作者**: pinchi_lin
- **日期**: 2020-11-12 20:11:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 95. [BPM APP]優化IMG列表接口取動態渲染表單應用ID時的速度
- **Commit ID**: `ae58d734eb481ab73568f1389f47d48ddda8fbf0`
- **作者**: pinchi_lin
- **日期**: 2020-11-12 14:35:37
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessInstanceForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ReassignWorkAssignForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java`

### 96. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `5f83dc5e45793cc81b676c4c50e1f732f456a5c3`
- **作者**: 詩雅
- **日期**: 2020-11-12 12:02:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`

### 97. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `4f6a3c2830f6d125435120700a9154d8b9d5304b`
- **作者**: 詩雅
- **日期**: 2020-11-12 11:59:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 98. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `20c87923df00b26ca6f30e3a6648aa2b0def6bf7`
- **作者**: pinchi_lin
- **日期**: 2020-11-12 11:20:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`

### 99. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `3d555c92b41aef260cfe859a97b24a060480762e`
- **作者**: pinchi_lin
- **日期**: 2020-11-12 11:16:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`

### 100. [BPM APP]Q00-20201103004 調整行動端腳本樣版設定元件字體顏色的說明
- **Commit ID**: `fc8022a7fce5e34e751a6cbdc33bf4941f6286ba`
- **作者**: cherryliao
- **日期**: 2020-11-06 18:35:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 101. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `0cb879c99d8219bcdc5f702881b58cc099070e10`
- **作者**: pinchi_lin
- **日期**: 2020-11-05 16:20:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 102. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `55c9b5081104894c690a8e00e3d3802aaa183cb8`
- **作者**: 詩雅
- **日期**: 2020-11-04 20:36:15
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 103. Q00-20201104003 修正「攻略雲指標維護作業」點「確定」按鈕後出現錯誤Invalid column name 'indicatorCalculationType'
- **Commit ID**: `fa3c05a41b1f17db77f48046acf0badecffcd36a`
- **作者**: lorenchang
- **日期**: 2020-11-04 17:39:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DDL_MSSQL_1.sql`

### 104. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `ed648adaeed7411c2f3d6f84b21ee23b32c2c0b7`
- **作者**: cherryliao
- **日期**: 2020-11-04 15:13:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`

### 105. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `e412189d0ee278ecdd10ca00f2cb3acf1e81cb84`
- **作者**: yamiyeh10
- **日期**: 2020-11-04 14:08:15
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 106. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `8c2f050a087ef098a877e8e63daa1e208cf7b29a`
- **作者**: cherryliao
- **日期**: 2020-11-02 18:28:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xlsx`

### 107. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `573ec9d180179efc747694cf739fc3e985fa81a1`
- **作者**: cherryliao
- **日期**: 2020-11-03 16:54:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`

### 108. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `c119a37d78a8937c5f6f183b81cb1d116e6ef8c2`
- **作者**: cherryliao
- **日期**: 2020-10-30 09:35:27
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`

### 109. [BPM APP]調整IMG接口與推播功能可支持雲整合方案[補]
- **Commit ID**: `93821c10480932ed177d0c17ba432718fd88e505`
- **作者**: pinchi_lin
- **日期**: 2020-10-29 19:18:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 110. [BPM APP]修正多語系檔案
- **Commit ID**: `aeb96e311b8bf861960938f90f1e111d5fe4b13c`
- **作者**: 詩雅
- **日期**: 2020-10-29 17:08:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xls`

### 111. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `07067aad28905270313897a6811e086720cf7218`
- **作者**: 詩雅
- **日期**: 2020-10-29 15:59:41
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xls`

### 112. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `c1f2608599eeedc280a389f9cb4e54c1b7338ba5`
- **作者**: cherryliao
- **日期**: 2020-10-29 15:42:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`

### 113. [內部]Q00-20201029003 StartNextActInstBean調整log內容
- **Commit ID**: `763ece41980af787b5468878835d0478158c9730`
- **作者**: 林致帆
- **日期**: 2020-10-29 15:01:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java`

### 114. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `82e43edf80bf8e704532c9126483337098784d92`
- **作者**: cherryliao
- **日期**: 2020-10-28 14:26:53
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xls`

### 115. [流程引擎]Q00-20201026001 修正流程派送到下一關，流程下一關會有偶發性的狀況無法啟動
- **Commit ID**: `91b967f726c7991c06a1f042d5d22b44956699c9`
- **作者**: 林致帆
- **日期**: 2020-10-26 16:28:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`

### 116. [BPM APP]調整IMG接口與推播功能可支持雲整合方案
- **Commit ID**: `253eb5b40c481db1a880d648d168cecb73443575`
- **作者**: pinchi_lin
- **日期**: 2020-10-23 10:52:58
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 117. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合[補]
- **Commit ID**: `9857d9062554b8d2a5f596dd2133d41191f81b5b`
- **作者**: pinchi_lin
- **日期**: 2020-10-22 14:59:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 118. [BPM APP]調整行動簽核管理中心新增支持雲移動平台整合
- **Commit ID**: `8ee21ec23d2e7a27efb0bdedf7b81e5bd909efad`
- **作者**: pinchi_lin
- **日期**: 2020-10-22 14:52:48
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.5.1_mobile_DDL_Oracle_1.sql`

### 119. [BPM APP]S00-20200724001 調整IMG詳情表單中的簽核歷程排序問題
- **Commit ID**: `0e626c49137916d7f08c0901a467632976b660ed`
- **作者**: cherryliao
- **日期**: 2020-10-20 12:01:47
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 120. [BPM APP]新增行動端會議簽到功能[補]
- **Commit ID**: `3aa5770e35ce4ee706c8d5063465f0c2084a32e4`
- **作者**: 詩雅
- **日期**: 2020-10-13 09:47:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`

### 121. [BPM APP]Q00-20200929001 調整行動端以表單函式庫設定元件標籤無法載入所設定標籤文字的問題
- **Commit ID**: `720246db8bca197a38d13af43e1fdb23016dcebb`
- **作者**: cherryliao
- **日期**: 2020-10-08 17:19:34
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 122. [BPM APP]新增行動端會議簽到功能[補]
- **Commit ID**: `c44d730355e9bf3fe6ecf45119501803c9a1cce5`
- **作者**: 詩雅
- **日期**: 2020-10-07 18:12:40
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/dataformat/MobileModuleDataTrasnferService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleModuleFeatures.java`

### 123. [內部]Q00-20201007001 調整 SystemVariable設定檔，流程主旨標示作廢的的四個設定値的敘述內容
- **Commit ID**: `a19dfe11506fc56817ebae28c1c886a54b96ccfe`
- **作者**: 林致帆
- **日期**: 2020-10-07 15:03:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 124. [組織設計師]A00-20200929001 修正無法透過介面刪除組織資料
- **Commit ID**: `8ddf105377428ba3f0cd4bc1f4ac3c2e9a4143d6`
- **作者**: waynechang
- **日期**: 2020-09-30 14:02:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DDL_Oracle_1.sql`

### 125. [BPM APP]新增行動端會議簽到功能[補]
- **Commit ID**: `5d5d37c3cd5fc3f53ca9f68cf0c96f524d5feb5b`
- **作者**: 詩雅
- **日期**: 2020-09-30 12:16:56
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformModuleTool.java`

### 126. [BPM APP]新增行動端會議簽到功能[補]
- **Commit ID**: `8346286a22dc76623d0a3a132e9386aa007dcf9d`
- **作者**: yamiyeh10
- **日期**: 2020-09-29 18:38:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp`

### 127. [BPM APP]新增行動端會議簽到功能[補]
- **Commit ID**: `9b5b6500bba1a7df439e47547eeeae112c0f0b8f`
- **作者**: cherryliao
- **日期**: 2020-09-26 10:08:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp`

### 128. [BPM APP]新增行動端會議簽到功能[補]
- **Commit ID**: `f3f77d5cfbf6fc2fd3855861ca79315b680a07b8`
- **作者**: 詩雅
- **日期**: 2020-09-25 19:10:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5851.xls`

### 129. [BPM APP]新增行動端會議簽到功能[補]
- **Commit ID**: `e93c4ea378c4bf524f8bc30317f2d7c389310b40`
- **作者**: 詩雅
- **日期**: 2020-09-25 17:03:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp`

### 130. [BPM APP]新增行動端會議簽到功能
- **Commit ID**: `54664540b2ccc2fc9ac296a8e8b74320e0c4bb56`
- **作者**: 詩雅
- **日期**: 2020-09-25 16:56:11
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldButton.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListDataset.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 131. [Web]S00-20200713001 調整功能清單中掛載模組的排序方式[補]
- **Commit ID**: `0d9518a27964d5a285674eb5c4d72698f6125d39`
- **作者**: cherryliao
- **日期**: 2020-09-21 11:30:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 132. [Web]C01-20200722004 顯示流程清單頁之流程筆數調整為採用設定檔之設定
- **Commit ID**: `db45a790bf2116c771c9628ac03d06a491183c90`
- **作者**: 林致帆
- **日期**: 2020-09-08 18:41:58
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/SQLHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.5.1_DML_Oracle_1.sql`

### 133. [流程引擎]A00-20200812003 修正當要做組織同步的資料量太大時，會導致組織同步出現錯誤[補]
- **Commit ID**: `69117d7cd80811e24240b62d5f7c322fc9a3d7c4`
- **作者**: 王鵬程
- **日期**: 2020-09-18 14:22:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 134. [流程引擎]A00-20200812003 修正當要做組織同步的資料量太大時，會導致組織同步出現錯誤
- **Commit ID**: `d92d202f7df7c9d6e8e38d34a41f2e5b60c03ed2`
- **作者**: 王鵬程
- **日期**: 2020-09-04 09:49:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/syncorg/SyncTable.properties`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml`

### 135. [Web]Q00-20201127004 修正從外部連結進流程圖無法完整顯示
- **Commit ID**: `ad4a75b781a4859298cc9fbc7241b1d6ea3714a9`
- **作者**: 王鵬程
- **日期**: 2020-11-27 18:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 136. [Web]Q00-20201127003修正使用者名稱有底線符號，造成流程加簽後，流程實例查看異常
- **Commit ID**: `57bcad10e803cdada57df466692d11cddb0cbd50`
- **作者**: 林致帆
- **日期**: 2020-11-27 17:20:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 137. [流程引擎]C01-***********修正簽核時，系統會有卡住的狀況[補]
- **Commit ID**: `5aa2df2fd0e3012d6a2dae7e18b2285905bb2428`
- **作者**: lorenchang
- **日期**: 2020-11-27 16:43:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 138. [ISO]Q00-20201127002 提供ISO文件檔案的URL下載服務
- **Commit ID**: `45db233449e66e28ba823fb9baddc65c261855ad`
- **作者**: waynechang
- **日期**: 2020-11-27 16:06:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 139. [流程引擎]A00-20201118001 修正使用者追蹤流程篩選流程發起人查詢無效
- **Commit ID**: `d36b8eacef30e1ae971478d697128d46b7ddba81`
- **作者**: yanann_chen
- **日期**: 2020-11-26 15:41:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 140. [Web]C01-20201117001 修正在模組程式維護中修改系統預設模組頁面，將輸入框和按鈕改為禁用，儲存鈕隱藏
- **Commit ID**: `91014e3436f4d4949a486f712035f143ec5213ee`
- **作者**: 王鵬程
- **日期**: 2020-11-26 11:54:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`

### 141. [組織設計] 簡稱為空白在某些環境開啟職稱維護作業會出現異常
- **Commit ID**: `ce0add84451975e7faadf6bb7f1d79f3bf66f38f`
- **作者**: lorenchang
- **日期**: 2020-11-25 13:23:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/OrgDesigner.java`

### 142. Revert "[流程引擎] 修正多AP(A/B)時流程在A進核決層級，在另一主機簽核該流程時會出現找不到Entity的問題"
- **Commit ID**: `5486aaf298434ac17fa34efddc128798eb899b73`
- **作者**: lorenchang
- **日期**: 2020-11-25 12:03:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivitySetDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CustomProcessPackage.java`

### 143. [流程設計師]Q00-20201124002 修正流程有設可重定義屬性參考表單元件時，當刪除該元件後將無法點選可重定義屬性
- **Commit ID**: `2c07ecd464d5d7a7e212e6c9a81aa7d299aea53e`
- **作者**: 王鵬程
- **日期**: 2020-11-24 18:08:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java`

### 144. [流程引擎]C01-***********修正簽核時，系統會有卡住的狀況
- **Commit ID**: `d572efc91c542e02aef5495d1e699c678aa541b8`
- **作者**: gaspard.shih
- **日期**: 2020-11-24 16:00:49
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/LocalDocManagerDelegate.java`
  - 📄 **重新命名**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/DocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/CreateDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/background_service/GoodsHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/AttachmentHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/AttachmentViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DocFileUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`

### 145. [內部]Q00-20201124001 調整5.5.6.1_MSSQL.sql
- **Commit ID**: `6da42ea74fa04b79c0586d8df0df47f5efd3272b`
- **作者**: yanann_chen
- **日期**: 2020-11-24 14:47:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.6.1_MSSQL.sql`

### 146. [內部]Q00-20201123001 調整5.8.2.1_DML_Oracle_1.sql
- **Commit ID**: `83a1fae73712d583ff7402bd573423f7cf6e9983`
- **作者**: yanann_chen
- **日期**: 2020-11-23 16:03:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.2.1_DML_Oracle_1.sql`

### 147. [流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確
- **Commit ID**: `9bd026d01552969dc15d81ed42e1b498b4d1c8f2`
- **作者**: 林致帆
- **日期**: 2020-11-20 15:48:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 148. [Web]Q00-20201120002 修正在批次簽核的完成頁面，主旨有html時只能以文字呈現而不能以html呈現
- **Commit ID**: `30724b45d01e2aa177f953fd002494d2da2853d2`
- **作者**: 王鵬程
- **日期**: 2020-11-20 15:24:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`

### 149. [組織設計師] 調整組織設計師，避免因為Cache資料有問題導致的非預期的異常
- **Commit ID**: `70253ef7bafe1b19cfc585ab6dcbb3d614fe48e3`
- **作者**: lorenchang
- **日期**: 2020-11-19 16:53:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`

### 150. [流程引擎] 修正多AP(A/B)時流程在A進核決層級，在另一主機簽核該流程時會出現找不到Entity的問題
- **Commit ID**: `ba84dfca6a03627ba402d2741d2659b3d8ee24e4`
- **作者**: lorenchang
- **日期**: 2020-11-19 16:40:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivitySetDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CustomProcessPackage.java`

### 151. [流程引擎] 修正5.8版SessionBeanApplication重覆更新的異常
- **Commit ID**: `f8ce21518e41d1649285c1f6b63522b6f0187d51`
- **作者**: lorenchang
- **日期**: 2020-11-19 16:33:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/app/SessionBeanApplication.java`

### 152. [流程引擎]Q00-20201111001 修正點選發起流程報錯、我的最愛打不開[補]
- **Commit ID**: `7ed90573d96b4ea620f8bf6ba6a49d408daca107`
- **作者**: lorenchang
- **日期**: 2020-11-19 16:02:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/AbsAuthority.java`

### 153. [內部]Q00-20201119001 調整SQL指令，補上";--"
- **Commit ID**: `f94615e5bbfaecfe7ee15f97005d319acf630471`
- **作者**: yanann_chen
- **日期**: 2020-11-19 14:58:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql`

### 154. [TipTop]Q00-20201118004 修正TIPTOP端簽核表單，開啟表單就報錯
- **Commit ID**: `e7d735d11acf1ade21574154757727a052fc3a45`
- **作者**: 林致帆
- **日期**: 2020-11-18 16:37:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 155. [Web]Q00-20201118003 修正流程點選取回重辦會報錯
- **Commit ID**: `8720a512c72a04b2549f80c1dac498a4c0edaaac`
- **作者**: 林致帆
- **日期**: 2020-11-18 16:02:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 156. [流程引擎]Q00-20201118001 修正流程實例刪除，造成流程定義開啓異常
- **Commit ID**: `960f118d5f940c60b778f19abb984c22724ddbef`
- **作者**: 林致帆
- **日期**: 2020-11-18 08:36:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 157. [BPM APP]C01-20201111003 修正在自訂義開窗搜尋時會異常的問題
- **Commit ID**: `959c9a4ee8e257cba646969edb5f599c9397cab8`
- **作者**: 詩雅
- **日期**: 2020-11-16 19:17:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 158. [Web]Q00-20201116002 修正Check Box最後一個選項沒有額外輸入框時，Check Box必填判斷功能異常
- **Commit ID**: `5982443bb69b35b70315f034a5c59f05120c6045`
- **作者**: yanann_chen
- **日期**: 2020-11-16 17:55:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 159. [Web]Q00-20201116001 修正Radio Button最後一個選項沒有額外輸入框時，Radio Button必填判斷功能異常
- **Commit ID**: `f3ccb640d6fff5914a61e4aa02dae255047a3021`
- **作者**: yanann_chen
- **日期**: 2020-11-16 14:34:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 160. [表單設計師]Q00-20201113002 修正在IE中用FormUtil提供dropdown取選取值和checkbox設定值的方法會報錯
- **Commit ID**: `f79b25e6ffc2aac19585a5f1d19fb648966bb2a5`
- **作者**: 王鵬程
- **日期**: 2020-11-13 12:01:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 161. [流程引擎]Q00-20201111001 修正點選發起流程報錯、我的最愛打不開
- **Commit ID**: `0e403646fa077a9deb7c2f69f239961044063400`
- **作者**: yanann_chen
- **日期**: 2020-11-11 16:16:16
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/model/ProcessPackageModel.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/AbsAuthority.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/AuthorityUnits.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/PackageInvokeAuthority.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/model/ProcessPackageModel.java`
  - 📝 **修改**: `3.Implementation/subproject/sys-authority/src/com/dsc/nana/user_interface/apps/authority/model/AuthorityGroupClientModel.java`

### 162. [Web]作業程序書的關卡清單改用關卡ID進行排序
- **Commit ID**: `de07f82e89a5c6cc0defaa9434c5344d4c00c73f`
- **作者**: gaspard.shih
- **日期**: 2020-11-11 14:07:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java`

### 163. [Web]A00-20201110001 修正追蹤轉會流程異常
- **Commit ID**: `c03c1e850006c67937b38438525b2376048c7589`
- **作者**: yanann_chen
- **日期**: 2020-11-11 13:42:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`

### 164. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `7020e992539a4832a8dd4c675e8093076dd94fba`
- **作者**: gaspard.shih
- **日期**: 2020-11-11 11:55:00
- **變更檔案數量**: 0

### 165. [Web]BPM首頁於小螢幕時可顯示「可用時間」資訊
- **Commit ID**: `c2586590b335370f64fb99813fe6ce50ed7374da`
- **作者**: gaspard.shih
- **日期**: 2020-11-11 11:54:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 166. [BPM APP]C01-20201110002 修正企業微信在直接進入表單畫面時會因使用者尚未登入而導致畫面異常問題
- **Commit ID**: `d0b77d42971b756213b2d89a15a4d1311f0aa836`
- **作者**: cherryliao
- **日期**: 2020-11-10 19:58:33
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 167. [Web]Q00-20201110002 修正Ajax Service沒有帶出員工所屬部門資料
- **Commit ID**: `429d9d62e1e19712ca5e50104bf4197d05fb614e`
- **作者**: yanann_chen
- **日期**: 2020-11-10 16:57:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/UserVo.java`

### 168. [Web]Q00-20201110001 修正舊版絕對位置表單轉成RWD會異常
- **Commit ID**: `1480fd9a3fe694d00711536a4930bb12c701f80c`
- **作者**: 林致帆
- **日期**: 2020-11-10 16:26:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`

### 169. [內部]Q00-20201104002 調整update sql
- **Commit ID**: `cc114644037e11eb47245ce329d7d1dfb08896cd`
- **作者**: yanann_chen
- **日期**: 2020-11-10 11:33:50
- **變更檔案數量**: 74
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.0.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.0.1_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.1.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.1.1_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.2.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.2.1_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.4.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.4.1_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.5.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.5.1_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.6.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.5.6.1_Oracle.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_MSSQL_2.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_MSSQL_3.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle_2.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle_3.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.3_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.3_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_MSSQL_1.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.1_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.2_Oracle.sql`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/db/@base/update/5.6.2.3_\344\277\256\346\224\271\350\241\250\345\226\256\345\205\203\344\273\266\351\241\217\350\211\262\347\225\260\345\270\270_MSSQL.sql"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/db/@base/update/5.6.2.3_\344\277\256\346\224\271\350\241\250\345\226\256\345\205\203\344\273\266\351\241\217\350\211\262\347\225\260\345\270\270_Oracle.sql"`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.3.1_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_useForCritical_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_useForCritical_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.2_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.2_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.3_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.5.3_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.1.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql`

### 170. [Web]Q00-20201109004 修正當鎖定工具列時，在簽核意見欄位和流程主旨欄位下方顯示的片語會被擋住
- **Commit ID**: `dc1110925d35092362b37c7dcd775cf3b4947378`
- **作者**: 王鵬程
- **日期**: 2020-11-09 18:39:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 171. [表單設計師]Q00-20201109003 修正當Date元件勾選顯示時間並有和其他Date元件比較時，時間不會被比較
- **Commit ID**: `839943665f7e99378d5c5d2baca24b2ceaccc34c`
- **作者**: 王鵬程
- **日期**: 2020-11-09 17:49:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/RwdDateTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`

### 172. [BPM APP]Q00-20201106002 修正行動端表單元件同時設定字體顏色和文字對齊樣式時，文字對齊樣式會失效的問題
- **Commit ID**: `015a09e45e4135817ef04b5bbf32184b29b4c8e4`
- **作者**: cherryliao
- **日期**: 2020-11-09 11:22:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 173. [Web]Q00-20201109002 修正流程設定關卡的表單為唯讀時，formSave後，不會再次執行formOpen
- **Commit ID**: `4f12a852a6c9a5d2a075534c601cecb8bdd02bbf`
- **作者**: waynechang
- **日期**: 2020-11-09 11:05:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 174. [Web]Q00-20201105004 修正表單自適應寬度調整時，Grid沒有重新渲染
- **Commit ID**: `98fac441eec5de458819e86965f36ba3e018e0f5`
- **作者**: lorenchang
- **日期**: 2020-11-05 17:11:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 175. [Web]Q00-20201105002 修正流程中核決關卡沒有處理者，在跳過核決關卡後，查看流程圖會無法開啟
- **Commit ID**: `3e074f79a24b7c3c4a6aa5ab4af37a2b7254f7f8`
- **作者**: 林致帆
- **日期**: 2020-11-05 14:20:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/BlockActivityBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 176. [流程引擎]Q00-20201105001 修正選擇發起流程的組織單位清單中顯示已失效部門
- **Commit ID**: `4dd7719eb760dffdb361579907cd092884623cd4`
- **作者**: yanann_chen
- **日期**: 2020-11-05 11:49:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java`

### 177. [Web]A00-20201014001 修正當Grid繫結ListBox且多選加到Grid，點選有多選的Row沒有mark繫結元件選取值
- **Commit ID**: `ba6331c35d0f9bac078057577f76251cfb153ce9`
- **作者**: 王鵬程
- **日期**: 2020-11-04 15:49:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 178. [ISO]A00-20201103002 修正ISO絕對位置變更單，文件開窗若透過簡易查詢時會撈出申請變更中的文件
- **Commit ID**: `b9d45dbc6a65f2a7875789b2f7dc3cdf18bd668f`
- **作者**: waynechang
- **日期**: 2020-11-04 13:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/CreateDocumentAction.java`

### 179. [ISO]Q00-20201103002 調整BCL8 轉檔錯誤時的log，以便分析錯誤原因
- **Commit ID**: `257585532b6a826d3541b83d5ef1820ed30517ea`
- **作者**: waynechang
- **日期**: 2020-11-03 16:27:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 180. [BPM APP]C01-20201016007 修正Grid操作按鈕(新刪修)可依各個Grid設定顯示/隱藏
- **Commit ID**: `4c8ec9e67a8395381d50cd2de9df3aa92751d762`
- **作者**: 詩雅
- **日期**: 2020-11-03 11:16:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 181. [內部]Q00-20201102002 調整程式排版
- **Commit ID**: `fb1e45a42a019e57a7080c723e3554a6df421018`
- **作者**: yanann_chen
- **日期**: 2020-11-02 17:17:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 182. [Web]S00-20200812003 調整多人簽核關卡含離職人員時仍持續通知管理員的問題
- **Commit ID**: `fbc91daa2f58de9681644aca13875f09f6d7a572`
- **作者**: cherryliao
- **日期**: 2020-11-02 15:50:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 183. [BPM APP]Q00-20201030005 在企業微信使用iOS手機若附件是掃描產生的PDF有時會發生模糊問題
- **Commit ID**: `d2d0285eb1986201dbd0571256d0adac236d7378`
- **作者**: yamiyeh10
- **日期**: 2020-10-30 18:53:36
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/WeChatFileCacheMap.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 184. [流程引擎]Q00-20201030004 修正排程AutoRegetWorkAssignmentHandler執行時出現錯誤
- **Commit ID**: `ee8adf5c91cb89a763f67c90264c1be0af02832e`
- **作者**: yanann_chen
- **日期**: 2020-10-30 17:14:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 185. [內部]Q00-20201030003 調整程式排版
- **Commit ID**: `c2e93d4322129dc9c90d8930b05fc380b94ae02b`
- **作者**: yanann_chen
- **日期**: 2020-10-30 15:57:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 186. [Web]Q00-20201030002 修正表單點擊Attachment元件，上傳附件時設定權限，套用範圍選擇關卡，無法選到核決權限關卡
- **Commit ID**: `dfd48f5061e1c49541fb6716a73f361644672a24`
- **作者**: 林致帆
- **日期**: 2020-10-30 14:36:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ActivityDefNormalListReader.java`

### 187. [Web]C01-20201019003 調整首頁模塊嵌入BPM代辦畫面時，表單Grid顯示異常
- **Commit ID**: `e46d0168de252ff1ff01c7d00b5eaafd4f8453ec`
- **作者**: waynechang
- **日期**: 2020-10-30 11:41:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 188. [BPM APP]C01-20201028004 修正若表單有設定文字對齊下拉選單無法點擊展開的問題
- **Commit ID**: `8996e6530e8bc3efbab8eb957628b5c8467d7e42`
- **作者**: cherryliao
- **日期**: 2020-10-30 11:23:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

