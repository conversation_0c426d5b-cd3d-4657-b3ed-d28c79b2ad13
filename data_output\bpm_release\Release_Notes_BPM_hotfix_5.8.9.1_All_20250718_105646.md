# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.1_All
- **舊版本**: release_5.8.9.1
- **生成時間**: 2025-07-18 10:56:46
- **新增 Commit 數量**: 118

## 變更摘要

### kmin (4 commits)

- **2024-06-04 11:16:33**: [Web] C01-202410603001 檢核表單元件資料型態與DB欄位型態是否一致
  - 變更檔案: 1 個
- **2023-04-19 10:26:20**: Revert "[TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗"
  - 變更檔案: 3 個
- **2023-04-19 10:26:07**: Revert "[TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗 [補修正]"
  - 變更檔案: 3 個
- **2023-04-18 14:17:29**: Revert "[TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用"
  - 變更檔案: 5 個

### 林致帆 (52 commits)

- **2023-08-15 17:13:54**: [WorkFlow]Q00-20230815004 新增WorkFlow回寫增加時間訊息
  - 變更檔案: 1 個
- **2023-06-02 11:45:57**: [WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料
  - 變更檔案: 1 個
- **2023-06-02 10:23:40**: [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]
  - 變更檔案: 1 個
- **2023-06-01 12:04:53**: [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息
  - 變更檔案: 1 個
- **2023-05-31 17:47:43**: [WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤
  - 變更檔案: 1 個
- **2024-02-21 10:47:15**: [ESS]Q00-20240221001 調整ESS簽核單據增加防呆避免串單
  - 變更檔案: 3 個
- **2023-06-27 13:39:34**: [T100]Q00-20230627001 修正關卡設置"所有附件皆需開啟過"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送
  - 變更檔案: 1 個
- **2023-06-12 11:27:32**: [在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕
  - 變更檔案: 1 個
- **2023-05-26 16:38:31**: [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
  - 變更檔案: 3 個
- **2023-05-26 10:36:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個
- **2023-04-14 15:44:26**: [Web]Q00-20230414005 調整下載附件不該顯示This URL not have permission to download the file訊息
  - 變更檔案: 1 個
- **2023-05-11 11:02:08**: [Web]Q00-20230511001 調整ErrorPage如果錯誤訊息小於30字，直接顯示在頁面上而不會只出現在"詳細資訊"中
  - 變更檔案: 2 個
- **2023-04-17 17:48:48**: [Web]Q00-20230417003 修正下載原始檔功能在附件元件非"full control"的權限下也能在待辦清單出現
  - 變更檔案: 1 個
- **2023-04-20 14:37:27**: [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊 [補修正]
  - 變更檔案: 1 個
- **2023-04-18 16:37:44**: [流程引擎]Q00-20230418003 增加逾時關卡處理Log [補修正]
  - 變更檔案: 1 個
- **2023-04-18 15:39:32**: [流程引擎]Q00-20230418003 增加逾時關卡處理Log
  - 變更檔案: 1 個
- **2023-04-18 11:51:30**: [Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常 [補修正]
  - 變更檔案: 4 個
- **2023-04-14 16:45:14**: [Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常
  - 變更檔案: 1 個
- **2023-04-14 09:26:59**: [表單設計師]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功 [補修正]
  - 變更檔案: 1 個
- **2023-04-12 14:58:38**: [資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊
  - 變更檔案: 2 個
- **2023-04-07 15:29:33**: [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊
  - 變更檔案: 2 個
- **2023-04-07 14:33:14**: [Web]Q00-20230407002 修正流程重要性在流程第二關後都未顯示
  - 變更檔案: 2 個
- **2023-04-06 16:13:14**: [流程引擎]Q00-20230406003 修正流程終點前若為閘道關卡，流程結案BamProInstData資料表的狀態還是進行中
  - 變更檔案: 1 個
- **2023-03-30 15:29:48**: [TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 2 個
- **2023-03-30 10:57:55**: [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用 [補修正]
  - 變更檔案: 1 個
- **2023-03-29 17:57:34**: [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用 [補修正]
  - 變更檔案: 5 個
- **2023-03-25 14:43:14**: [流程引擎]Q00-20230325001 修正流程退回重瓣到有自動簽核之關卡會觸發自動簽核
  - 變更檔案: 1 個
- **2023-04-18 14:38:14**: [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗 [補修正]
  - 變更檔案: 3 個
- **2023-04-18 13:56:07**: [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗
  - 變更檔案: 4 個
- **2023-04-17 14:44:46**: [TIPTOP]Q00-20230414002 修正流程設計師在TIPTOP流程關卡的"上傳附件時允許修改是否使用在線閱覽"取值為null，會導致無法呈現在線閱覽效果 [補修正]
  - 變更檔案: 5 個
- **2023-04-14 10:57:40**: [TIPTOP]Q00-20230414002 修正流程設計師在TIPTOP流程關卡的"上傳附件時允許修改是否使用在線閱覽"取值為null，會導致無法呈現在線閱覽效果
  - 變更檔案: 4 個
- **2023-03-30 10:46:26**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用 [補修正]
  - 變更檔案: 3 個
- **2023-03-28 18:05:14**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
  - 變更檔案: 5 個
- **2023-03-20 11:49:50**: [派送關聯模組]V00-20230320001 修正派送關聯模組直接發後置流程會無法派送成功跟退回重瓣到服務關卡之前會派送失敗
  - 變更檔案: 8 個
- **2023-03-10 14:26:29**: [派送關聯模組]S00-*********** 派送關聯模組增加可設定發起流程時是否一併拋轉附件功能
  - 變更檔案: 21 個
- **2023-04-18 14:38:14**: [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗 [補修正]
  - 變更檔案: 3 個
- **2023-04-18 13:56:07**: [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗
  - 變更檔案: 3 個
- **2023-03-28 18:05:14**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
  - 變更檔案: 5 個
- **2023-03-21 11:15:38**: [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用
  - 變更檔案: 1 個
- **2023-03-15 10:29:37**: [TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 1 個
- **2023-03-15 10:28:47**: [TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 3 個
- **2023-03-14 16:31:22**: [TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 3 個
- **2023-03-14 14:38:09**: [E10]Q00-20230314003 調整E10流程若為批次簽核，造成回寫審核日期會沒有值
  - 變更檔案: 2 個
- **2023-03-09 09:38:31**: [Web]Q00-20230309001 修正複合元件的樹狀開窗選擇部門用部門名稱查詢會查到不再該部門的人員
  - 變更檔案: 1 個
- **2023-03-06 15:18:27**: [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊
  - 變更檔案: 2 個
- **2023-03-06 11:33:04**: [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功
  - 變更檔案: 3 個
- **2023-03-03 19:22:30**: [E10]Q00-20230303004 修正E10回寫取預設主機因為https不需輸入PORT造成回寫失敗
  - 變更檔案: 1 個
- **2023-03-03 11:25:44**: [Web]Q00-20230303002 修正人員開窗選取帶有特殊字"𤧟"的人員派送後表單會重複多長好幾個"𤧟"字
  - 變更檔案: 1 個
- **2023-02-24 17:08:46**: [雙因素模組]Q00-20230224002 調整個人資訊頁面加上未註冊雙因素模組的防呆[補修正]
  - 變更檔案: 1 個
- **2023-02-24 17:03:58**: [雙因素模組]Q00-20230224002 調整個人資訊頁面加上未註冊雙因素模組的防呆
  - 變更檔案: 1 個
- **2023-02-23 14:45:57**: [TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔
  - 變更檔案: 1 個
- **2023-02-21 10:37:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
  - 變更檔案: 1 個

### 周权 (3 commits)

- **2023-12-08 14:45:43**: [流程引擎]S00-20230602004 Restful转存表单Web Server调整为抓内网地址
  - 變更檔案: 1 個
- **2023-12-29 13:58:54**: [Web]Q00-20231229003 调整"追蹤"“監控”使用表单自适应宽度調整書面寬度無效果的問題
  - 變更檔案: 2 個
- **2023-12-29 13:33:58**: [Web]Q00-20231229002 调整个人资讯-->表单自适应宽度slider预设值为“较宽”
  - 變更檔案: 1 個

### raven.917 (15 commits)

- **2023-07-06 15:22:50**: [組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。
  - 變更檔案: 1 個
- **2023-04-19 11:04:33**: [Web] Q00-20230418004 修正$符號在通知信件樣板異常問題，replaceAll 寫法調整為 replace
  - 變更檔案: 1 個
- **2023-04-18 11:14:02**: [Web] Q00-20230418001 修正 RadioButton & CheckBox 在列印表單時，被強制改成垂直式問題
  - 變更檔案: 1 個
- **2023-04-13 17:02:10**: [Web] Q00-20230413002 修正通知信追蹤連結，流程圖開啟空白問題
  - 變更檔案: 1 個
- **2023-04-11 14:40:23**: [Web] Q00-20230411001 新增資料選取註冊器支援GroupBy語法及Having語法
  - 變更檔案: 1 個
- **2023-04-06 16:40:05**: [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題(補修正)
  - 變更檔案: 1 個
- **2023-04-06 16:28:22**: [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題
  - 變更檔案: 3 個
- **2023-03-15 11:57:02**: [流程引擎] Q00-20230315001 修正XPDL流程匯入，簽入流程，核決關卡不允許NULL問題，調整Table欄位允許NULL
  - 變更檔案: 4 個
- **2023-03-23 15:28:14**: [Web] Q00-20230323002 調整部門主管首頁待辦處理量只會找得到在此部門內的使用者，監控流程圖及在途總處理量一併調整。
  - 變更檔案: 2 個
- **2023-03-13 15:37:27**: [Web] Q00-20230313002 修正SelectElement，Style屬性異常問題
  - 變更檔案: 1 個
- **2023-03-08 12:01:14**: [Web] Q00-20230308002 修正設定小數點後幾位功能，提示文字應加在外顯元件上
  - 變更檔案: 4 個
- **2023-03-08 09:25:27**: [Web] Q00-20230308001 相容Grid,setAction點擊事件，支持點擊Row不帶回繫結欄位
  - 變更檔案: 1 個
- **2023-03-07 15:59:30**: [Web] Q00-20230307001 修正Admin操作員工工作轉派，撈取資料時新增防呆。
  - 變更檔案: 1 個
- **2023-02-23 10:18:01**: [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)
  - 變更檔案: 2 個
- **2023-02-21 15:53:39**: [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數
  - 變更檔案: 1 個

### waynechang (17 commits)

- **2023-08-14 15:59:34**: [在線閱覽]Q00-20230814002 調整在線閱讀浮水印機制，當浮水印內容有特殊字導致無法添加浮水印時，改使用預設內容「userId+閱讀時間」作為浮水印內容，避免使用者無法順利閱讀檔案
  - 變更檔案: 3 個
- **2023-07-25 15:27:07**: [Web]Q00-20230725002 修正發起流程切換預解析流程時，當系統設定有設定該流程只以主部門發起(invoke.process.by.main.unit.process.package.ids)時，若使用者有多個兼職部門時，切換流程圖不應讓使用者選擇參考部門
  - 變更檔案: 1 個
- **2023-05-09 17:39:13**: [Web]Q00-20230509002 修正表單附件上傳後；若重新透過附件開窗上傳新的檔案時，原先上傳的附件無法下載的異常
  - 變更檔案: 1 個
- **2023-05-17 12:00:12**: [流程引擎]Q00-20230512001 修正關卡有啟用自動簽核跳關，當核決關卡參考該關卡時，若該關卡同時有向前或向後加簽時，自動簽核的功能就會失效
  - 變更檔案: 1 個
- **2023-03-24 14:34:36**: [流程引擎]Q00-20230324001 調整退回重辦通知信，當通知信的變數有設定<#allAssigneesOID>、<#allAssigneesID>、<#allAssigneesName>，且流程有設定「退回重辦時逐級通知」時，替換變數的內容由「各個關卡的工作處理者」改為「被退回關卡的工作處理者」
  - 變更檔案: 1 個
- **2023-03-23 15:06:58**: [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析[補]
  - 變更檔案: 3 個
- **2023-03-23 10:51:02**: [Web]Q00-20230323001 調整使用者/流程處理/取回重辦，進入頁面後，選擇時間自訂的時間範圍說明由「流程發起時間」改為「工作完成的時間」
  - 變更檔案: 1 個
- **2023-03-21 11:03:12**: [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析
  - 變更檔案: 2 個
- **2023-03-06 17:36:59**: [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關
  - 變更檔案: 1 個
- **2023-03-02 15:25:23**: [流程引擎]Q00-20230302002 修正流程關係人部門設定為參考表單欄位，且表單欄位為DialogInput部門開窗時，發起流程會報錯
  - 變更檔案: 1 個
- **2023-03-02 15:14:24**: [流程設計師]Q00-20230302001 調整流程設計師-表單定義-可重定義屬性-關係人-選擇部門參考表單欄位，將選擇值帶回畫面後，「表單」欄位應顯示為表單名稱，而非表單代號
  - 變更檔案: 1 個
- **2023-03-01 11:58:51**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
  - 變更檔案: 1 個
- **2023-03-01 11:17:29**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
  - 變更檔案: 1 個
- **2023-03-01 11:17:29**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log
  - 變更檔案: 1 個
- **2023-02-23 14:56:22**: [內部]Q00-20230223003 流程引擎增加派送相關log
  - 變更檔案: 1 個
- **2023-02-22 15:49:01**: [流程引擎]Q00-20230222002 修正核決關卡設定與流程關卡處理者相同時自動簽核，且流程有兩個以上的核決關卡時，只有核決關卡展開的第一關有自動簽核，後續關卡皆未自動簽核
  - 變更檔案: 1 個
- **2023-02-21 15:02:58**: [Web]Q00-20230221001 修正當關卡有設定「必須上傳新附件」，若透過追蹤流程「重新發起新流程」時，卡控是否有上傳附件的功能失效
  - 變更檔案: 1 個

### cherryliao (11 commits)

- **2023-06-20 15:54:17**: [內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息
  - 變更檔案: 1 個
- **2023-04-14 10:47:52**: [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
  - 變更檔案: 1 個
- **2023-05-05 11:13:22**: [Web]Q00-20230504003 修正流程中附件檔名包含逗號時，檔案無法下載的問題
  - 變更檔案: 1 個
- **2023-04-14 10:28:30**: [Web]Q00-20230414001 修正當用戶逾時閒置過久會彈出null訊息框的問題
  - 變更檔案: 2 個
- **2023-04-13 10:27:20**: [Web]Q00-20230413001 修正在表單腳本有使用addAttachment的方法時會無法取得附件描述的問題
  - 變更檔案: 1 個
- **2023-04-07 11:20:01**: [Web]Q00-20230407001 修正表單序號欄位有簡體中文時會出現問號的問題
  - 變更檔案: 1 個
- **2023-03-30 10:35:16**: [Web]Q00-20230330001 修正追蹤清單與匯出Excel筆數不一致的問題
  - 變更檔案: 1 個
- **2023-03-24 17:08:21**: [Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕
  - 變更檔案: 1 個
- **2023-03-21 11:00:48**: [Web]Q00-20230321001 調整TextBox設定數字轉文字對應不會自動觸發更新的問題
  - 變更檔案: 2 個
- **2023-03-20 10:38:43**: [Web]Q00-20230310001 調整倒數計時器功能的機制與提示訊息
  - 變更檔案: 2 個
- **2023-02-21 16:30:41**: [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題
  - 變更檔案: 1 個

### 郭哲榮 (4 commits)

- **2023-04-13 18:31:55**: [MPT]A00-20230410001 修正從郵件連結進入BPM時，右上角沒有顯示首頁按鈕的問題
  - 變更檔案: 1 個
- **2023-03-23 11:42:15**: [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息[補]
  - 變更檔案: 2 個
- **2023-03-09 16:38:22**: [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息
  - 變更檔案: 1 個
- **2023-03-02 12:59:48**: [BPM APP]C01-20230110010 修正逾時流程通知處理者主管的推播無法檢視表單的問題
  - 變更檔案: 10 個

### yamiyeh10 (6 commits)

- **2023-03-27 17:47:54**: [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題[補]
  - 變更檔案: 1 個
- **2023-03-27 11:02:18**: [DT]A00-20230324001 修正系統權限管理員的可存取範圍不會根據編輯後的內容儲存問題
  - 變更檔案: 1 個
- **2023-03-14 11:23:36**: [流程設計師]Q00-20230314002 調整流程設計師在編輯範本內的變數清單中Runtime流程發起部門名稱多了一個姓字問題
  - 變更檔案: 2 個
- **2023-03-14 10:37:39**: [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題
  - 變更檔案: 1 個
- **2023-02-22 16:58:23**: [DT]C01-20230221002 修正在系統權限管理員的可存取範圍權限無法選擇離職人員與失效部門問題
  - 變更檔案: 2 個
- **2023-02-21 15:44:41**: [WEB]Q00-20230221002 修正在行動版的清單頁面上若主旨有<br>時無法正確換行問題
  - 變更檔案: 1 個

### pinchi_lin (3 commits)

- **2023-03-21 10:54:26**: [DT]C01-20230314001 調整判斷是否需要同步cache方法中的log層級
  - 變更檔案: 1 個
- **2023-03-06 10:25:43**: [DT]C01-20230224006 修正在組織管理工具刪除使用者名稱多語系會有髒資料的問題
  - 變更檔案: 1 個
- **2023-02-21 18:56:35**: [DT]C01-20230217001 修正在維護流程關卡中加入新增的核決層級儲存簽入後就無法再開啟或簽出的問題
  - 變更檔案: 1 個

### 謝閔皓 (3 commits)

- **2023-03-03 11:56:46**: [Web]Q00-20230303001 調整 TextBox 元件進階設定中小數點後幾位的保存方式多語系，原本為實際值與四捨五入，將實際值調整為無條件捨去
  - 變更檔案: 3 個
- **2023-03-02 15:06:41**: [Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題
  - 變更檔案: 1 個
- **2023-02-22 17:10:25**: [Web]Q00-20230222003 修正 TextBox 設定浮點數、顯示千分位和小數點後幾位時，與 Grid 繫結會導致 FromScript 取得 Grid 資料以及 FormInstance 的 FieldValues 會有千分位的問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [Web] C01-202410603001 檢核表單元件資料型態與DB欄位型態是否一致
- **Commit ID**: `218ea0510639914cf5465fbb20d8f38a27c0d054`
- **作者**: kmin
- **日期**: 2024-06-04 11:16:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 2. [WorkFlow]Q00-20230815004 新增WorkFlow回寫增加時間訊息
- **Commit ID**: `839699401e8e4a22c22aac1dce1fcd338584371b`
- **作者**: 林致帆
- **日期**: 2023-08-15 17:13:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 3. [WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料
- **Commit ID**: `0437efe512cdbe8f4acd5ea3a3eda8b1a8ec3d61`
- **作者**: 林致帆
- **日期**: 2023-06-02 11:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 4. [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]
- **Commit ID**: `ae7f6367fd3ff41fe3fd24a2d77434d5dce3ca3c`
- **作者**: 林致帆
- **日期**: 2023-06-02 10:23:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 5. [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息
- **Commit ID**: `8587e364a30e53b1762e3f570e251bbf84d74aa5`
- **作者**: 林致帆
- **日期**: 2023-06-01 12:04:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 6. [WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤
- **Commit ID**: `f3a65d4d5c73960c470e42302046741f879ea24d`
- **作者**: 林致帆
- **日期**: 2023-05-31 17:47:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 7. [ESS]Q00-20240221001 調整ESS簽核單據增加防呆避免串單
- **Commit ID**: `6bbdcba66bf5d2f3e2ee24d2e71fdda33943f30f`
- **作者**: 林致帆
- **日期**: 2024-02-21 10:47:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 8. [流程引擎]S00-20230602004 Restful转存表单Web Server调整为抓内网地址
- **Commit ID**: `640413adb81b4d177e729cccef95f2579cedc975`
- **作者**: 周权
- **日期**: 2023-12-08 14:45:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java`

### 9. [Web]Q00-20231229003 调整"追蹤"“監控”使用表单自适应宽度調整書面寬度無效果的問題
- **Commit ID**: `7df7ee8d6d4242022e8acc390a417abaa0f217fc`
- **作者**: 周权
- **日期**: 2023-12-29 13:58:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 10. [Web]Q00-20231229002 调整个人资讯-->表单自适应宽度slider预设值为“较宽”
- **Commit ID**: `1e1bffd447f53613cc1eadc09ee5d2dc2c675009`
- **作者**: 周权
- **日期**: 2023-12-29 13:33:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 11. [組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。
- **Commit ID**: `4f0c6c0e6eb5533f23f9ad591fd2bab0277432bb`
- **作者**: raven.917
- **日期**: 2023-07-06 15:22:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 12. [在線閱覽]Q00-20230814002 調整在線閱讀浮水印機制，當浮水印內容有特殊字導致無法添加浮水印時，改使用預設內容「userId+閱讀時間」作為浮水印內容，避免使用者無法順利閱讀檔案
- **Commit ID**: `2af8f500c7e5438d72e384b748f8433d299d4bd5`
- **作者**: waynechang
- **日期**: 2023-08-14 15:59:34
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 13. [Web]Q00-20230725002 修正發起流程切換預解析流程時，當系統設定有設定該流程只以主部門發起(invoke.process.by.main.unit.process.package.ids)時，若使用者有多個兼職部門時，切換流程圖不應讓使用者選擇參考部門
- **Commit ID**: `e717a0c7b887ff85fccc3f43ced7d458842bcc80`
- **作者**: waynechang
- **日期**: 2023-07-25 15:27:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 14. [T100]Q00-20230627001 修正關卡設置"所有附件皆需開啟過"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送
- **Commit ID**: `bdd852f611bfd7ebdb6a42dc34041fbc113b9b28`
- **作者**: 林致帆
- **日期**: 2023-06-27 13:39:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 15. [在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕
- **Commit ID**: `00bc05bc660c147c72ef6f139d05f101f009c897`
- **作者**: 林致帆
- **日期**: 2023-06-12 11:27:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 16. [內部]Q00-20230620002 增加更新使用者在線資訊發生網路不通時於console印出錯誤訊息
- **Commit ID**: `a7b2e49040d052fc22be6c26595c3512f889fa92`
- **作者**: cherryliao
- **日期**: 2023-06-20 15:54:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 17. [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
- **Commit ID**: `0f62d0480a8638d13100be6a8a3c6e23c0646d9f`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 18. [Web]Q00-20230509002 修正表單附件上傳後；若重新透過附件開窗上傳新的檔案時，原先上傳的附件無法下載的異常
- **Commit ID**: `78902da6b254a0ffa8e7fe86ba4aaf03f06e3d1f`
- **作者**: waynechang
- **日期**: 2023-05-09 17:39:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

### 19. [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
- **Commit ID**: `7eb90d954d209d38744aad4cf5f028ad9fe8025b`
- **作者**: 林致帆
- **日期**: 2023-05-26 16:38:31
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 20. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `904fdaf88b08137a32219b1ff4d8842e4417a3b8`
- **作者**: 林致帆
- **日期**: 2023-05-26 10:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 21. [Web]Q00-20230504003 修正流程中附件檔名包含逗號時，檔案無法下載的問題
- **Commit ID**: `bc30ee610c443e2e83f70eb5823e8ba44c5f33cd`
- **作者**: cherryliao
- **日期**: 2023-05-05 11:13:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 22. [Web]Q00-20230414005 調整下載附件不該顯示This URL not have permission to download the file訊息
- **Commit ID**: `948f8bd5cca27cdaeb7da6a1b188e9db0830f9ee`
- **作者**: 林致帆
- **日期**: 2023-04-14 15:44:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 23. [Web]Q00-20230511001 調整ErrorPage如果錯誤訊息小於30字，直接顯示在頁面上而不會只出現在"詳細資訊"中
- **Commit ID**: `bc811e3c8d2a1c07357ac6120bd40dedd7e2c7de`
- **作者**: 林致帆
- **日期**: 2023-05-11 11:02:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`

### 24. [流程引擎]Q00-20230512001 修正關卡有啟用自動簽核跳關，當核決關卡參考該關卡時，若該關卡同時有向前或向後加簽時，自動簽核的功能就會失效
- **Commit ID**: `96941f7bf90a8dd7ba90c62521efd5dbd2ee0efe`
- **作者**: waynechang
- **日期**: 2023-05-17 12:00:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 25. [Web]Q00-20230417003 修正下載原始檔功能在附件元件非"full control"的權限下也能在待辦清單出現
- **Commit ID**: `743d1d56069d0509d5e1de9a9b4680bed505c22d`
- **作者**: 林致帆
- **日期**: 2023-04-17 17:48:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 26. [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊 [補修正]
- **Commit ID**: `f4d1c3ea6bddff9321da0da433600c67f0e35c63`
- **作者**: 林致帆
- **日期**: 2023-04-20 14:37:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 27. [Web] Q00-20230418004 修正$符號在通知信件樣板異常問題，replaceAll 寫法調整為 replace
- **Commit ID**: `1bc0944c4933805a4f56fcf5880f9b9b1d7281e7`
- **作者**: raven.917
- **日期**: 2023-04-19 11:04:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 28. [流程引擎]Q00-20230418003 增加逾時關卡處理Log [補修正]
- **Commit ID**: `835130f3a1067be96845db1855f05e2e3dc13ab4`
- **作者**: 林致帆
- **日期**: 2023-04-18 16:37:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 29. [流程引擎]Q00-20230418003 增加逾時關卡處理Log
- **Commit ID**: `2ada0c3171fa2741276b3128f7cbef90cfbbb5d8`
- **作者**: 林致帆
- **日期**: 2023-04-18 15:39:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 30. [Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常 [補修正]
- **Commit ID**: `88fbb306417ec47ecf5463bc10c054b6af18768d`
- **作者**: 林致帆
- **日期**: 2023-04-18 11:51:30
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/PhraseManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/taglib/WriteTag.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`

### 31. [Web] Q00-20230418001 修正 RadioButton & CheckBox 在列印表單時，被強制改成垂直式問題
- **Commit ID**: `11a1365eea768f4ee4bf2613e722d757cfb4fb8a`
- **作者**: raven.917
- **日期**: 2023-04-18 11:14:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap-3.3.5-print.css`

### 32. [Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常
- **Commit ID**: `2521a6ee398869faa36771bb616afaaeee10cca6`
- **作者**: 林致帆
- **日期**: 2023-04-14 16:45:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`

### 33. [Web]Q00-20230414001 修正當用戶逾時閒置過久會彈出null訊息框的問題
- **Commit ID**: `7b43cd7f05428dce0b65f7101a4196e4a824f01c`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:28:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 34. [表單設計師]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功 [補修正]
- **Commit ID**: `91bdc8c759a4956257d16e71fcff24ea5f067334`
- **作者**: 林致帆
- **日期**: 2023-04-14 09:26:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 35. [MPT]A00-20230410001 修正從郵件連結進入BPM時，右上角沒有顯示首頁按鈕的問題
- **Commit ID**: `0f403e2607679ac4f1a2dccb72f3a6baa35f2f0c`
- **作者**: 郭哲榮
- **日期**: 2023-04-13 18:31:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 36. [Web] Q00-20230413002 修正通知信追蹤連結，流程圖開啟空白問題
- **Commit ID**: `9fe14ca56390a763689332561f8c8846d886166a`
- **作者**: raven.917
- **日期**: 2023-04-13 17:02:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`

### 37. [Web]Q00-20230413001 修正在表單腳本有使用addAttachment的方法時會無法取得附件描述的問題
- **Commit ID**: `c67cdbf7ad5e6e16f6974675e52c450c103c48f0`
- **作者**: cherryliao
- **日期**: 2023-04-13 10:27:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 38. [資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊
- **Commit ID**: `42c866ab3f0d55c712db57a2cd6c6790eb3f5222`
- **作者**: 林致帆
- **日期**: 2023-04-12 14:58:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 39. [Web] Q00-20230411001 新增資料選取註冊器支援GroupBy語法及Having語法
- **Commit ID**: `5042205013c9376433c8e59bcce7013edeaa6ccc`
- **作者**: raven.917
- **日期**: 2023-04-11 14:40:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 40. [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊
- **Commit ID**: `f9fff34d1934531ce62e8f62e582be8d9ae8bcf2`
- **作者**: 林致帆
- **日期**: 2023-04-07 15:29:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 41. [Web]Q00-20230407002 修正流程重要性在流程第二關後都未顯示
- **Commit ID**: `faa4ca20c0c49257b482ad07fd74d25676f0ca14`
- **作者**: 林致帆
- **日期**: 2023-04-07 14:33:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 42. [Web]Q00-20230407001 修正表單序號欄位有簡體中文時會出現問號的問題
- **Commit ID**: `997bf39814e6af812f3471faef2c24b6c194829b`
- **作者**: cherryliao
- **日期**: 2023-04-07 11:20:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SNGenerator.java`

### 43. [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題(補修正)
- **Commit ID**: `244e0ea636ddfba139792efa828a1725a8e6082c`
- **作者**: raven.917
- **日期**: 2023-04-06 16:40:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 44. [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題
- **Commit ID**: `bfad8f49bc425fef1e8557c3d1fc80ba62acf77b`
- **作者**: raven.917
- **日期**: 2023-04-06 16:28:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 45. [流程引擎]Q00-20230406003 修正流程終點前若為閘道關卡，流程結案BamProInstData資料表的狀態還是進行中
- **Commit ID**: `85cd9428a5e85b1c0ffe9182f7a2138bef63a49a`
- **作者**: 林致帆
- **日期**: 2023-04-06 16:13:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 46. [TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `4f57e5ab943202add6b5cc3f12cfaa7fbb5cad58`
- **作者**: 林致帆
- **日期**: 2023-03-30 15:29:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`

### 47. [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用 [補修正]
- **Commit ID**: `6a61eb02a0b5912f382a1fe6595930cbec339bd7`
- **作者**: 林致帆
- **日期**: 2023-03-30 10:57:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 48. [Web]Q00-20230330001 修正追蹤清單與匯出Excel筆數不一致的問題
- **Commit ID**: `361d6479ef4cd27c1d3e3c73db0212a3306a5529`
- **作者**: cherryliao
- **日期**: 2023-03-30 10:35:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 49. [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用 [補修正]
- **Commit ID**: `53608347415dd9a44139a476f5a766ab5be38aa5`
- **作者**: 林致帆
- **日期**: 2023-03-29 17:57:34
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopUserImageSyncBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/IgnoreFilterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-ignoreFilter-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 50. [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題[補]
- **Commit ID**: `1c6a60bf916944ca115fdd0f77163ce1915d9eaa`
- **作者**: yamiyeh10
- **日期**: 2023-03-27 17:47:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessPackageManager.java`

### 51. [DT]A00-20230324001 修正系統權限管理員的可存取範圍不會根據編輯後的內容儲存問題
- **Commit ID**: `4f3f173acf69963c06ea07e908ead6ce72fce4c1`
- **作者**: yamiyeh10
- **日期**: 2023-03-27 11:02:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`

### 52. [流程引擎]Q00-20230325001 修正流程退回重瓣到有自動簽核之關卡會觸發自動簽核
- **Commit ID**: `334d86846f371a0e8de2ea32a8581565ef8c4d57`
- **作者**: 林致帆
- **日期**: 2023-03-25 14:43:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

### 53. [Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕
- **Commit ID**: `b883e6e59a846bc9b0b4577af5ab7c9849ef1dad`
- **作者**: cherryliao
- **日期**: 2023-03-24 17:08:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 54. [流程引擎] Q00-20230315001 修正XPDL流程匯入，簽入流程，核決關卡不允許NULL問題，調整Table欄位允許NULL
- **Commit ID**: `b0e37cd5319f83c21664175ffdb933d93692346d`
- **作者**: raven.917
- **日期**: 2023-03-15 11:57:02
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`

### 55. [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗 [補修正]
- **Commit ID**: `106185ad427a6da41552aedc71ec6d02cadc0478`
- **作者**: 林致帆
- **日期**: 2023-04-18 14:38:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 56. [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗
- **Commit ID**: `e91ba9b1b678454d7c8098e91b0454aa50d327d2`
- **作者**: 林致帆
- **日期**: 2023-04-18 13:56:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 57. [TIPTOP]Q00-20230414002 修正流程設計師在TIPTOP流程關卡的"上傳附件時允許修改是否使用在線閱覽"取值為null，會導致無法呈現在線閱覽效果 [補修正]
- **Commit ID**: `d0bed2371a8b2ba291729c9131ac7021559dddae`
- **作者**: 林致帆
- **日期**: 2023-04-17 14:44:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 58. [TIPTOP]Q00-20230414002 修正流程設計師在TIPTOP流程關卡的"上傳附件時允許修改是否使用在線閱覽"取值為null，會導致無法呈現在線閱覽效果
- **Commit ID**: `f70d692846314a9d70c85ce699f36be1adcdbd5f`
- **作者**: 林致帆
- **日期**: 2023-04-14 10:57:40
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 59. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用 [補修正]
- **Commit ID**: `d61314720361c58e5c8dc4a06b43f6a55d6d3f7c`
- **作者**: 林致帆
- **日期**: 2023-03-30 10:46:26
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 60. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
- **Commit ID**: `1a0cf853cf3aa4ac7c1d8237e871308ebc47d5f4`
- **作者**: 林致帆
- **日期**: 2023-03-28 18:05:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 61. [派送關聯模組]V00-20230320001 修正派送關聯模組直接發後置流程會無法派送成功跟退回重瓣到服務關卡之前會派送失敗
- **Commit ID**: `632e82f7fa375f4967314d94da5baff74cb4997e`
- **作者**: 林致帆
- **日期**: 2023-03-20 11:49:50
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`

### 62. [派送關聯模組]S00-*********** 派送關聯模組增加可設定發起流程時是否一併拋轉附件功能
- **Commit ID**: `d052bad841d374f67fc7417c3eef3eec7ce8e4af`
- **作者**: 林致帆
- **日期**: 2023-03-10 14:26:29
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/LocalDocManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/deliveryProcess/DeliveryProcessConfigurationDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`

### 63. Revert "[TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗"
- **Commit ID**: `35631d7db39b3a3f53d7e4940dd5decdb3b6af1c`
- **作者**: kmin
- **日期**: 2023-04-19 10:26:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 64. Revert "[TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗 [補修正]"
- **Commit ID**: `c9d7c4b5be7e2a2647027192372c23c56d0b7752`
- **作者**: kmin
- **日期**: 2023-04-19 10:26:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 65. [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗 [補修正]
- **Commit ID**: `078c4c62a4472521c47cfbe524d6645b3add9324`
- **作者**: 林致帆
- **日期**: 2023-04-18 14:38:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 66. [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗
- **Commit ID**: `30b8a39d9bd08c4beb6e7247b894a852f2a30e37`
- **作者**: 林致帆
- **日期**: 2023-04-18 13:56:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 67. Revert "[TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用"
- **Commit ID**: `bb587efe0ca07bdbaaf97d5c45f5afcbbadca6d4`
- **作者**: kmin
- **日期**: 2023-04-18 14:17:29
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - ❌ **刪除**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ❌ **刪除**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 68. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
- **Commit ID**: `b162485f8df41cdbe4ad964091351bd1b302beb8`
- **作者**: 林致帆
- **日期**: 2023-03-28 18:05:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 69. [流程引擎]Q00-20230324001 調整退回重辦通知信，當通知信的變數有設定<#allAssigneesOID>、<#allAssigneesID>、<#allAssigneesName>，且流程有設定「退回重辦時逐級通知」時，替換變數的內容由「各個關卡的工作處理者」改為「被退回關卡的工作處理者」
- **Commit ID**: `e5518a5f64b3667bc7186f0edd7287a4dc244598`
- **作者**: waynechang
- **日期**: 2023-03-24 14:34:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 70. [Web] Q00-20230323002 調整部門主管首頁待辦處理量只會找得到在此部門內的使用者，監控流程圖及在途總處理量一併調整。
- **Commit ID**: `2cd8678ba3ed0acec291b1ce5c12e23cb42f56f9`
- **作者**: raven.917
- **日期**: 2023-03-23 15:28:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 71. [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析[補]
- **Commit ID**: `cc033ac91f484b638dfc20fcc2852f474a4a0a96`
- **作者**: waynechang
- **日期**: 2023-03-23 15:06:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherLocal.java`

### 72. [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息[補]
- **Commit ID**: `b0f4d9add35bd8c0d4934d4bbac4a13e7b91be7d`
- **作者**: 郭哲榮
- **日期**: 2023-03-23 11:42:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 73. [Web]Q00-20230323001 調整使用者/流程處理/取回重辦，進入頁面後，選擇時間自訂的時間範圍說明由「流程發起時間」改為「工作完成的時間」
- **Commit ID**: `a84a29cde6fef0c366eb536cde3e3b0b93ee4cd0`
- **作者**: waynechang
- **日期**: 2023-03-23 10:51:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`

### 74. [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用
- **Commit ID**: `a77aa6a3fc932d8e45c91959a1390f2c2b47da11`
- **作者**: 林致帆
- **日期**: 2023-03-21 11:15:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 75. [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析
- **Commit ID**: `e48aa076eed02412360b9cc8476e866561d0519e`
- **作者**: waynechang
- **日期**: 2023-03-21 11:03:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 76. [Web]Q00-20230321001 調整TextBox設定數字轉文字對應不會自動觸發更新的問題
- **Commit ID**: `25bdf68e7cc158eb4ad0168961e28e7d3eba7b22`
- **作者**: cherryliao
- **日期**: 2023-03-21 11:00:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`

### 77. [DT]C01-20230314001 調整判斷是否需要同步cache方法中的log層級
- **Commit ID**: `217f621bae0a1c34f0eb519fd78fedd151919065`
- **作者**: pinchi_lin
- **日期**: 2023-03-21 10:54:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 78. [Web]Q00-20230310001 調整倒數計時器功能的機制與提示訊息
- **Commit ID**: `2eeee6c348a6e6a2301fe27a5a961c974b1d3a04`
- **作者**: cherryliao
- **日期**: 2023-03-20 10:38:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 79. [TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `3ecec29a54a7bcd01ec145ca023dd9e657dc177c`
- **作者**: 林致帆
- **日期**: 2023-03-15 10:29:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`

### 80. [TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `4be09c7975ed78f58e8a82132c0fa43dd0ece024`
- **作者**: 林致帆
- **日期**: 2023-03-15 10:28:47
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`

### 81. [TIPTOP]Q00-20230104004 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `29ee7c4dbc6a1bdebab7c479ca2a9bc877fc2b20`
- **作者**: 林致帆
- **日期**: 2023-03-14 16:31:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`

### 82. [E10]Q00-20230314003 調整E10流程若為批次簽核，造成回寫審核日期會沒有值
- **Commit ID**: `cb1a8088ff47874f5070768e20a7f69616ca502b`
- **作者**: 林致帆
- **日期**: 2023-03-14 14:38:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10Manager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 83. [流程設計師]Q00-20230314002 調整流程設計師在編輯範本內的變數清單中Runtime流程發起部門名稱多了一個姓字問題
- **Commit ID**: `326aedd6f8465496f7e16ea306ba0187b599cfc2`
- **作者**: yamiyeh10
- **日期**: 2023-03-14 11:23:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_TW.properties`

### 84. [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題
- **Commit ID**: `2ac07bc883f9f23048b18085db20683491f061fb`
- **作者**: yamiyeh10
- **日期**: 2023-03-14 10:37:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessPackageManager.java`

### 85. [Web] Q00-20230313002 修正SelectElement，Style屬性異常問題
- **Commit ID**: `b1303dc323570ddacef90bb2b8739b86a1e5ea48`
- **作者**: raven.917
- **日期**: 2023-03-13 15:37:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 86. [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息
- **Commit ID**: `a42d4e2f68b14657d7b05bbad6198ce95d8af7ab`
- **作者**: 郭哲榮
- **日期**: 2023-03-09 16:38:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 87. [Web]Q00-20230309001 修正複合元件的樹狀開窗選擇部門用部門名稱查詢會查到不再該部門的人員
- **Commit ID**: `2a8ad08f04ea9bea27715cbefee2d377145f4d12`
- **作者**: 林致帆
- **日期**: 2023-03-09 09:38:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 88. [Web] Q00-20230308002 修正設定小數點後幾位功能，提示文字應加在外顯元件上
- **Commit ID**: `1857edb0f4b80f5c833e2ae989834c135cd5b916`
- **作者**: raven.917
- **日期**: 2023-03-08 12:01:14
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 89. [Web] Q00-20230308001 相容Grid,setAction點擊事件，支持點擊Row不帶回繫結欄位
- **Commit ID**: `c8e9d01479bd8c067f986b386d67ee700775d75d`
- **作者**: raven.917
- **日期**: 2023-03-08 09:25:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 90. [Web] Q00-20230307001 修正Admin操作員工工作轉派，撈取資料時新增防呆。
- **Commit ID**: `da79b7b692be1e14ceafe6ef4074f962491acb26`
- **作者**: raven.917
- **日期**: 2023-03-07 15:59:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java`

### 91. [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關
- **Commit ID**: `d363a6418866213df395e3f8a640c047760a75fd`
- **作者**: waynechang
- **日期**: 2023-03-06 17:36:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 92. [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊
- **Commit ID**: `a2a02e6e26bd63c404ba4bd6248a668bdc4f8036`
- **作者**: 林致帆
- **日期**: 2023-03-06 15:18:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 93. [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功
- **Commit ID**: `83825f271b3390b3a468d3e5bef624cd8c2f07c2`
- **作者**: 林致帆
- **日期**: 2023-03-06 11:33:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 94. [DT]C01-20230224006 修正在組織管理工具刪除使用者名稱多語系會有髒資料的問題
- **Commit ID**: `d5426b9c2d4fb6f59086ad8827594b0b00615590`
- **作者**: pinchi_lin
- **日期**: 2023-03-06 10:25:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 95. [E10]Q00-20230303004 修正E10回寫取預設主機因為https不需輸入PORT造成回寫失敗
- **Commit ID**: `20cf0ebd71f4de132ec17d2bf3a65fe3c481ac14`
- **作者**: 林致帆
- **日期**: 2023-03-03 19:22:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 96. [Web]Q00-20230303001 調整 TextBox 元件進階設定中小數點後幾位的保存方式多語系，原本為實際值與四捨五入，將實際值調整為無條件捨去
- **Commit ID**: `4fcb73e547fb2700c93a7afe4f8b3ef34c5c1bd0`
- **作者**: 謝閔皓
- **日期**: 2023-03-03 11:56:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 97. [Web]Q00-20230303002 修正人員開窗選取帶有特殊字"𤧟"的人員派送後表單會重複多長好幾個"𤧟"字
- **Commit ID**: `03b241c34f955e1f09128fee3d9cd6d555a303a5`
- **作者**: 林致帆
- **日期**: 2023-03-03 11:25:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 98. [流程引擎]Q00-20230302002 修正流程關係人部門設定為參考表單欄位，且表單欄位為DialogInput部門開窗時，發起流程會報錯
- **Commit ID**: `2d9668079b6e667a76e3b6524e937ba46b12d483`
- **作者**: waynechang
- **日期**: 2023-03-02 15:25:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 99. [流程設計師]Q00-20230302001 調整流程設計師-表單定義-可重定義屬性-關係人-選擇部門參考表單欄位，將選擇值帶回畫面後，「表單」欄位應顯示為表單名稱，而非表單代號
- **Commit ID**: `8aec50f3a704e0ac6a359924d5f125f106ee27e2`
- **作者**: waynechang
- **日期**: 2023-03-02 15:14:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java`

### 100. [Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題
- **Commit ID**: `fda53577a0b9fb8ffe14059aced40ef6b4df06b5`
- **作者**: 謝閔皓
- **日期**: 2023-03-02 15:06:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 101. [BPM APP]C01-20230110010 修正逾時流程通知處理者主管的推播無法檢視表單的問題
- **Commit ID**: `5cf370d8a5a7f55659aa65e43c7874458b848708`
- **作者**: 郭哲榮
- **日期**: 2023-03-02 12:59:48
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AdapterMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`

### 102. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
- **Commit ID**: `98aa6be0d3988ac41f2c3766a194e47a9b77cf80`
- **作者**: waynechang
- **日期**: 2023-03-01 11:58:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 103. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
- **Commit ID**: `bbb4582fc6932ceded77853ffee6adb18abb768e`
- **作者**: waynechang
- **日期**: 2023-03-01 11:17:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 104. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log
- **Commit ID**: `31cd834f5d81e5ed3ad38378617158c716478cf9`
- **作者**: waynechang
- **日期**: 2023-03-01 11:17:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 105. [雙因素模組]Q00-20230224002 調整個人資訊頁面加上未註冊雙因素模組的防呆[補修正]
- **Commit ID**: `fabc1045314b967caf2d081e19ae71431977b636`
- **作者**: 林致帆
- **日期**: 2023-02-24 17:08:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/license/ModuleKey.java`

### 106. [雙因素模組]Q00-20230224002 調整個人資訊頁面加上未註冊雙因素模組的防呆
- **Commit ID**: `6286859c68c10de94ecacff8b34c5917ccb7c2c8`
- **作者**: 林致帆
- **日期**: 2023-02-24 17:03:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 107. [內部]Q00-20230223003 流程引擎增加派送相關log
- **Commit ID**: `0f90171ac7debaeac6f8d16407cc574436f62cdd`
- **作者**: waynechang
- **日期**: 2023-02-23 14:56:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 108. [TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔
- **Commit ID**: `526ce75c9396553693bff96a46b84032fe35121e`
- **作者**: 林致帆
- **日期**: 2023-02-23 14:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 109. [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)
- **Commit ID**: `****************************************`
- **作者**: raven.917
- **日期**: 2023-02-23 10:18:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 110. [Web]Q00-20230222003 修正 TextBox 設定浮點數、顯示千分位和小數點後幾位時，與 Grid 繫結會導致 FromScript 取得 Grid 資料以及 FormInstance 的 FieldValues 會有千分位的問題
- **Commit ID**: `5320c36babff3f22e28fbd0fa6a500525b553a75`
- **作者**: 謝閔皓
- **日期**: 2023-02-22 17:10:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 111. [DT]C01-20230221002 修正在系統權限管理員的可存取範圍權限無法選擇離職人員與失效部門問題
- **Commit ID**: `ea181aec21926963d21185b40bba880a705f0549`
- **作者**: yamiyeh10
- **日期**: 2023-02-22 16:58:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 112. [流程引擎]Q00-20230222002 修正核決關卡設定與流程關卡處理者相同時自動簽核，且流程有兩個以上的核決關卡時，只有核決關卡展開的第一關有自動簽核，後續關卡皆未自動簽核
- **Commit ID**: `4b00234f3961024e3727d08409092dcf9cb68ad3`
- **作者**: waynechang
- **日期**: 2023-02-22 15:49:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 113. [DT]C01-20230217001 修正在維護流程關卡中加入新增的核決層級儲存簽入後就無法再開啟或簽出的問題
- **Commit ID**: `b10a3402e7fece6cb9e68dec15eaf9581cb23277`
- **作者**: pinchi_lin
- **日期**: 2023-02-21 18:56:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 114. [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題
- **Commit ID**: `fb5efa013b73801f162be5a0d412ceb5e2787fb6`
- **作者**: cherryliao
- **日期**: 2023-02-21 16:30:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 115. [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數
- **Commit ID**: `89e3aa59a5a00a9d000bf8c7c962b3f2950b1d76`
- **作者**: raven.917
- **日期**: 2023-02-21 15:53:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 116. [WEB]Q00-20230221002 修正在行動版的清單頁面上若主旨有<br>時無法正確換行問題
- **Commit ID**: `1a7a914130a56bcd8b790d9540f4db84afb8e14c`
- **作者**: yamiyeh10
- **日期**: 2023-02-21 15:44:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 117. [Web]Q00-20230221001 修正當關卡有設定「必須上傳新附件」，若透過追蹤流程「重新發起新流程」時，卡控是否有上傳附件的功能失效
- **Commit ID**: `a1b2d7da1418e40baeed49c5b5cfeca3b7046031`
- **作者**: waynechang
- **日期**: 2023-02-21 15:02:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 118. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
- **Commit ID**: `d1ce5ee136bca710740cb6272a17d1c488e38cf0`
- **作者**: 林致帆
- **日期**: 2023-02-21 10:37:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

