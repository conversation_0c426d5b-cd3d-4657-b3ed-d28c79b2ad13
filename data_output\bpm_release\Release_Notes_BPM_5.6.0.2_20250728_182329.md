# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.0.2
- **舊版本**: 5.6.0.1
- **生成時間**: 2025-07-28 18:23:29
- **新增 Commit 數量**: 2

## 變更摘要

### Administrator (1 commits)

- **2016-08-09 18:21:50**: Merge branch 'develop' into 'master'

### loren (1 commits)

- **2016-08-09 17:14:37**: 合併BPM_Patch(5.6.0.2)
  - 變更檔案: 223 個

## 詳細變更記錄

### 1. Merge branch 'develop' into 'master'
- **Commit ID**: `869256a0bdd5371ff1e77c7f8e3f83e57e60fed3`
- **作者**: Administrator
- **日期**: 2016-08-09 18:21:50
- **變更檔案數量**: 0

### 2. 合併BPM_Patch(5.6.0.2)
- **Commit ID**: `8924a3ed639a30cd354cacd7a821b06a5f37e2e3`
- **作者**: loren
- **日期**: 2016-08-09 17:14:37
- **變更檔案數量**: 223
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProgramDefManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SapXmlManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/StatefulProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/comparator/CategoryComparator.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISOAccessRight.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocCatergory.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocCmItem.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocTypeLevel.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocument.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISOFile.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/WorkCalendar.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTag.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTagAttachment.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessPackage.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForMobilePerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputLabelTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputMultiTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/ModelConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaEJB.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/syncorg/sync-def.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/conf/syncorg/sync-def_forT100.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocTypeMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/hibernate/ISOPaperRecorDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcher.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/exception/CannotParserParticipantException.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/exception/ServiceException.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerHome.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerLocalHome.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbstractPageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DraftListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FavoritiesProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudNoticeListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudRejectableListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudTraceProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/McloudXmlReaderBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mcloud/McloudMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/AbstractNewTiptopMethod.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopUserImageSyncBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrInvoke.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/util/sap/SapConnection.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/etl/SyncOrgEtl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5602.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/UpdateVersionAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocTypeAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageSecurityLevelAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/sap/SapAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AppFormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ExtOrgAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmInvokeWorkItemVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ISOPaperRecordVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProgramViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DocFileUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ExceptionMessageCreator.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/JSFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/JSPFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageDraft-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracingLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManageLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItemLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileKickStart.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SignImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/JreVersionCheck.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BPMProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmWorkItem.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MCloud/digiwin.mobile.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolWorkItem.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/SaveDraft_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/SaveForm_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/disableDispatch_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/disableInvoke_zh_Hant_TW.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/dispatch_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/invoke_zh_Hant_TW.gif`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/images/index_images/portlet_logo.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/images/add.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/images/invoke.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/jquery.mobile-1.4.5.feng.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/jquery.mobile-1.4.5.feng_tm.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/jquery.mobile.bpm.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/themecolor/main.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/themeroller/my-efgp-theme.feng_tm.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOCancel001.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOMod001.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISONew001.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperApply.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperRecover.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperWriteOff.form`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\344\275\234\345\273\242\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\346\226\260\345\242\236\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\347\264\231\346\234\254\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\347\264\231\346\234\254\346\255\270\351\202\204(\346\262\226\351\212\267)\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOCancel001.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOMod001.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISONew001.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOPaperApply.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOPaperWriteOff.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_SQLServer2005.sql`

