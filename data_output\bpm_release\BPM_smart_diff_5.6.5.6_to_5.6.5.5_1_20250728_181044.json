{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.6.5.6", "date": "tag 5.6.5.6\nTagger: 施翔耀 <<EMAIL>>\n\nlast build  2018/12/122018-12-12 10:19:19", "message": "<V56>A00-20181122001 二次 修正 :多語系匯入功能，因Key大小寫導致無法匯入。", "author": "j<PERSON><PERSON><PERSON><PERSON>"}, "舊分支": {"branch_name": "5.6.5.5_1", "date": "tag 5.6.5.5_1\nTagger: 施翔耀 <jose<PERSON><PERSON><PERSON>@digiwin.biz>\n\nlast build  2018-09-10 14:29:092018-09-10 11:34:05", "message": "A00-20180905001 修權限不一致問題，關卡流程表單頁面沒有設定權限，但是開啟Attachement按鈕卻可以看到附件的問題。", "author": "施廷緯"}, "比較時間": "2025-07-28 18:10:44", "新增commit數量": 111, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "324bdbf056a88239974440645622cadbc980fa29", "commit_訊息": "<V56>A00-20181122001 二次 修正 :多語系匯入功能，因Key大小寫導致無法匯入。", "提交日期": "2018-12-12 10:19:19", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f5e56ed595925a5b420db291d79be3eaeb0b06a", "commit_訊息": "S00-20181017001 系統管理工具", "提交日期": "2018-12-11 16:45:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/main/ADMMainFrame.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/images/adm/EFGP.jpg", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 2}, {"commit_hash": "c0801ce2217282c3922bb75f7140e4d9e40c81f5", "commit_訊息": "C01-20181211006 APP待辦上傳附件時顯示上傳成功，但繼續派送後其實沒有夾帶附件", "提交日期": "2018-12-11 15:53:16", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c630339f395be2898cf2bcd0728f8e2d49f1aa7", "commit_訊息": "Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop", "提交日期": "2018-12-11 15:26:17", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "5518ff73a500630115e662f4b547eae34428fa16", "commit_訊息": "修正鼎捷移動待辦繼續派送後沒有英文語系", "提交日期": "2018-12-11 15:25:44", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f19a36469e9557fc253af6184cd03b24d7c8925d", "commit_訊息": "修正行動版浮動按鈕英文語系會跑版問題", "提交日期": "2018-12-11 15:07:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0beb7cf6252185e8ce9e49b72f5bc7415ad8941b", "commit_訊息": "修正企業微信英文語系片語按鈕跑版問題", "提交日期": "2018-12-11 15:05:50", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ba66da4bf88ade368fa155be6fcb9c48b049339", "commit_訊息": "Q00-20181211001 支持ESS使用https連線", "提交日期": "2018-12-11 14:18:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f023c63eaba8dd1e349f13d87e6f84d0513ffa7e", "commit_訊息": "修正IMG附件資訊只會顯示繁體中文", "提交日期": "2018-12-11 13:54:32", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f125f312d4ec069e674a95e860c6943b84bab29f", "commit_訊息": "隱藏企業微信通知表單的附件進階按鈕", "提交日期": "2018-12-11 12:22:57", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a118bdcc94e385c109d15ad4a0b38e9edd477366", "commit_訊息": "二次修正行動版Grid一鍵展開後無法滑動的問題", "提交日期": "2018-12-11 12:22:27", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a9f1a7ed01bfa0e6e62d99c23d54807000b4b74", "commit_訊息": "修正英文語系造成BPM App顯示跑版問題", "提交日期": "2018-12-11 12:21:11", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5656.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "9d2147d05bbcce99d379a4d4b936cca838367e7a", "commit_訊息": "A00-20181207001 修正企業微信簡體出現繁體中文 1.附件資訊 2.退回重辦的片語只顯示片", "提交日期": "2018-12-11 12:07:27", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5656.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "75026011b5f84694ad725c7dcc603d34939197d1", "commit_訊息": "修正行動版Grid一鍵展開後無法滑動的問題", "提交日期": "2018-12-10 11:36:24", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4fc45716d7750718e4d261f4329f60a845f47574", "commit_訊息": "<V56> S00-20180908001 調整:程式位置", "提交日期": "2018-12-10 10:26:09", "作者": "j<PERSON><PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dbba059e83df5fa25e2f89e19ed091ad23b0e078", "commit_訊息": "修正BPM APP議題", "提交日期": "2018-12-07 19:00:23", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fa14e3ec2bf3cb9289e3564dce6a0a86f0c68879", "commit_訊息": "A00-20181127001 修正多人群組在最後一關終止流程後，流程圖顯示異常", "提交日期": "2018-12-07 16:10:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "411c2157f9a26f4fb208764aeccd14184672d7d9", "commit_訊息": "C01-20181206001 修正在IMG中當待辦已被簽核過時會有\"myFormValue\" is undefined問題", "提交日期": "2018-12-07 12:31:51", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6f8bbf803b31647611f45f5b14b9c35c6d2784e", "commit_訊息": "C01-20181206001 修正BPMAPP終止流程時未填意見且點確定兩次後會變成同意派送問題", "提交日期": "2018-12-07 11:46:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4d513965afc5bad7cb9bcf2a9ae19780bc686e54", "commit_訊息": "將5.7.4.1移動表單相關議題修正到5.6.5.6", "提交日期": "2018-12-06 15:15:50", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "200bc39974b85be5129a8f4496148a6b43643327", "commit_訊息": "將5.7.4.1企業微信相關議題修到5.6.5.6", "提交日期": "2018-12-06 15:13:39", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "4a24d55b73cd72f301ed996f3f2f9af34fca36b1", "commit_訊息": "將5.7.4.1鼎捷移動相關議題下修到5.6.5.6", "提交日期": "2018-12-06 15:11:09", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookData.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "4d6f11f4b318c816ebe69ae2afa02a2cae25789e", "commit_訊息": "<V56>A00-20181122001 修正 :多語系匯入功能，因Key大小寫導致無法匯入。", "提交日期": "2018-11-30 13:52:40", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ddb7ec0a89bfbeb4f23287620cb6e99c5e10506b", "commit_訊息": "A00-20181121001 修正ISO報表一覽表的簽出時間及簽出人員資料未顯示", "提交日期": "2018-11-26 16:26:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocForReportViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "542f3ccbff403533eaffd61be66db658c24a5955", "commit_訊息": "修正SQL錯誤  Table>>COLUMN", "提交日期": "2018-11-23 12:28:38", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3b6323281558b20b2ce545ac4d2cc96d493041f1", "commit_訊息": "A00-20181114001 修正客制開窗改變顯示筆數時 資料會重複出現的問題", "提交日期": "2018-11-16 15:04:10", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e9ede5ff88ee22d78d23c9eefa0a41511b4e21d7", "commit_訊息": "<V56>Q00-20181115002 修正:如果表單定義欄位設定為數值,但實際表單內容填顯文字,在列印表單時會報錯 ,調整照樣顯示文字", "提交日期": "2018-11-15 18:56:47", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "60f111791d139257aa8ffadb5b905ac07fcf5a51", "commit_訊息": "A00-20180717001 修正前次修改後一般退回重辦會出錯", "提交日期": "2018-11-15 18:35:49", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34114f99970fe706db1ec000d8cf3952449650ed", "commit_訊息": "Q00-20181113001 修正A00-20180314001修改錯誤  之前固定小數後兩位是錯誤的 改成以前端設定為準", "提交日期": "2018-11-13 17:35:16", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b47543c9269477ce485be33af53f809e49155729", "commit_訊息": "A00-20180307001 補上<#JumpWorkItem>替換", "提交日期": "2018-11-09 18:12:03", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29906f8a925c3114a485ebe21346a678cbe6bdcf", "commit_訊息": "<V56> Q00-20181108001 修正 :透過portletEntry.jsp 外部連結 來發起流程 ,當發起者無權限發起時,提示訊息異常", "提交日期": "2018-11-08 11:40:03", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5656.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b1f4e2bb102f35aa1c6ea046e741b7966987aedb", "commit_訊息": "C01-20181031001 修正當有多個工作項目建立時間相同時追蹤流程會取錯關卡的問題", "提交日期": "2018-11-06 17:47:10", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a9d3b10ab8499c904babe59720a7814fba4fee98", "commit_訊息": "<V56> A00-20181030001 修正 :當儲存表單 ,表單資料無異動和 需要重新取的後端資料的情境下 ,會造成關卡無派送", "提交日期": "2018-11-05 18:35:10", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4b37bfc3be64da1150873c5a15ce8171b9a8df1c", "commit_訊息": "A00-20180328001 新增如果Mail url有hdnUserId會直接帶到代號欄位", "提交日期": "2018-11-05 17:56:16", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "db1ec9e620ddee6447062272502d7aefb5f9c459", "commit_訊息": "C01-20181029003 工作通知頁面中的進階查詢功能異常修正", "提交日期": "2018-11-01 14:31:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7526edad1698dd0d7b20e9755ea178551f2bd60b", "commit_訊息": "<V56> C01-20180925003 調整 :HRM的兼職部門已經可以支稱直屬主管及核決層級的資料 ,無須再補上資料", "提交日期": "2018-11-01 10:40:36", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2093b8e4878c8c7ce7572ae636d414b01a4b10ad", "commit_訊息": "C01-20180322001 修正表單元件使用SerialNumber並按分類編排時，若無前置字串則無法發起單據", "提交日期": "2018-10-31 14:14:26", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SNGenerator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "afd25ea39cdd19f7145ec96118ce985d2c3a575b", "commit_訊息": "A00-20181025001 ISO文件管理模組-生失效郵件範本管理多語系調整", "提交日期": "2018-10-30 15:14:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageNotificationContent/ModifyNotificationContent.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fcdeeabd6bb96e18bd33e03e12fda26b7a61d404", "commit_訊息": "A00-20181025002 ISO文件管理模組-權限屬性管理多語系調整", "提交日期": "2018-10-30 15:08:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageAccessRight/ModifyAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0d6a804dc4ae19410bd6a52226801679301008d7", "commit_訊息": "A00-20180323001-2 流程代理人將新增、修改和刪除都同時執行儲存 並將儲存鈕移除", "提交日期": "2018-10-29 18:17:32", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7cc7e15eb96f75b708dc167df24491877ac18687", "commit_訊息": "C01-20180926004 增加附件路徑驗證", "提交日期": "2018-10-29 15:51:44", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "575e1b1840164f3b560ffe54af61aa071d9be922", "commit_訊息": "A00-20180323001 將新增、修改和刪除都同時執行儲存 並將儲存鈕移除", "提交日期": "2018-10-26 16:34:46", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "92a5a071fed6aa92db29ef08405bc14127009b5f", "commit_訊息": "C01-20181025001 修正隱藏元件方法在多欄位時會影響到其他元件的議題", "提交日期": "2018-10-26 12:11:33", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileMethodControl.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "178b247e4948d430a81b551abcd05f3cbc4c2955", "commit_訊息": "A00-20181024003 修正行動版隱藏標籤的TextBox元件擺放雙欄式模板會跑版議題", "提交日期": "2018-10-26 10:07:35", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cb210735fca5f813f506ed14b8cac37c48609bb6", "commit_訊息": "<V56> A00-20181018001  產生的組織同步XML會依照type來產生型態   因將屬性type寫死為部門 ,當如果是專案時 , type卻為department,導致同步報錯", "提交日期": "2018-10-25 17:35:27", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NaNaTableUtilV2_0.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NanaTableUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/DeptRelation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "371f1a64ed56f766316cf5fab3809cf202c79b90", "commit_訊息": "A00-20180323001 調整為刪除同時執行儲存動作", "提交日期": "2018-10-25 16:26:51", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "01f2de485e48df0294a41a376295fc3bb30d508f", "commit_訊息": "C01-20181024001 將轉存表單的服務增加withnolock", "提交日期": "2018-10-25 16:18:57", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e6f7f978303559183ee6775f74896b6bb7f13e5", "commit_訊息": "A00-20180314001 上次修改取值方法 值太大會換成科學記號表示 故修改取值方法", "提交日期": "2018-10-24 17:43:46", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "653ca8b42c7f2c0041fb297fe142a570cbd5a290", "commit_訊息": "C01-20180917001 修正多選開窗整批選取資料移除「x」按鈕，無法執行全部刪除", "提交日期": "2018-10-24 15:35:19", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c554225d3d39051c8d7649e01dc7a4830ad85cda", "commit_訊息": "C01-20180628002 修正M-Cloud表單維護效能緩慢", "提交日期": "2018-10-22 17:16:26", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/McloudXmlReaderBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe7334b018909ad6e327628420994c81446e36ac", "commit_訊息": "C01-20181012001 修正行動版已結案流程仍會出現撤銷、取回按鈕", "提交日期": "2018-10-17 17:28:56", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "343248a235de5a00d3dad392fd83e5907a733f82", "commit_訊息": "C01-20180725002 修正DotJIntegration的加簽關卡因關鍵字衝突導致無法加簽", "提交日期": "2018-10-17 17:01:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d1213302deec7314f24a4f4a69e3e6003569258", "commit_訊息": "Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop", "提交日期": "2018-10-17 14:52:51", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "9eef6cff4f2f574e9eff42be8fda4beafa7deefb", "commit_訊息": "<V56>C01-20181015001 修正 :WebService發單近來,SerialNumber缺少attribute id 導致前端解析時報錯", "提交日期": "2018-10-17 14:51:51", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c6ede1d75de81c3a8e922546d94a8af43c592d8e", "commit_訊息": "A00-20180717001 修正核決關卡內按流程定議退回會出現\"請洽系統管理員\"", "提交日期": "2018-10-17 13:55:02", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cf2b04ec5b9f20fb919bada9424d4d991edc66d3", "commit_訊息": "修正企業微信通知列表取下十筆時資料都是撈全部導致筆數與狀態對不上", "提交日期": "2018-10-17 13:47:14", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1ccab566c23c8f6878c894291ce5389b004993c6", "commit_訊息": "A00-20181012002 修正ISO文件的參考文件的版本異常", "提交日期": "2018-10-16 15:11:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/MainFileViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2095448b8c618d3cc0bd1544179eaaaeea23676d", "commit_訊息": "A00-20181012001 修正IMG中間層點不同意時若未填簽核意見會顯示失敗而不是提示使用者必填簽核意見", "提交日期": "2018-10-16 11:37:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7233a3b671199ebb67e5866014252ea7ee3d4d7d", "commit_訊息": "<V56> A00-20180625003 修正 : 填寫ESS流程,切換至一般待辦異常", "提交日期": "2018-10-15 17:09:53", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e9bedc40d54d2a439c82d744a5a1cf3e77604a27", "commit_訊息": "同C01-20171205003 修正資料擷取器開窗問題。", "提交日期": "2018-10-12 10:16:55", "作者": "derrick_shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "806c55b74a75078e89a8d2c6d9ce656eb95a3e7d", "commit_訊息": "C01-20180813001 調整是否代理的判斷邏輯", "提交日期": "2018-10-11 16:10:43", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SubstituteUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd93d44eca21d7bfe15206fd6ada33d0ac6c90b1", "commit_訊息": "<V56> Q00-20181009001 修正 :發起流程,查看簡易流程圖,不會顯示預先解析人員", "提交日期": "2018-10-09 16:44:47", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f6baaaff13caea7763b4f584b5ba0427a02c08c", "commit_訊息": "<V56> S00-20180908001 預覽流程檢視：當解析到離職人員時，於姓名前加上 (X) 註記，可識別是離職人員", "提交日期": "2018-10-09 15:27:01", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e044c72a973692a6dcd9c3649b00b11ee613ce7d", "commit_訊息": "A00-20180720001 修正 :從portal開啟待辦是空白畫面", "提交日期": "2018-10-08 11:22:28", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b86b1bb9a96792349397741e4b25f3f2b1647187", "commit_訊息": "<V56> 二次調整 C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度", "提交日期": "2018-10-08 10:41:33", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad4e9373498cf5e1b1a3e3f17f85954da1eebcc5", "commit_訊息": "C01-20171205003 調整資料擷取器開窗至螢幕中間。", "提交日期": "2018-10-05 16:28:01", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed886456628e1059f651d4d505d58592d2429f5f", "commit_訊息": "C01-20180928002 調整:假如網址有帶入hdnCurrentUserId參數,當因為未登入導入到登入畫面時會將userId帶入到帳號欄位", "提交日期": "2018-10-05 11:42:44", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c0297d331ae0c5e21ff82575f048e5d0c01d773b", "commit_訊息": "C01-20180925009 修正Chrome無法從子視窗使用父視窗confirm()問題(批次轉派修正)", "提交日期": "2018-10-04 19:20:50", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e3f5b8ea2f661abbcf3a3f540da18741c4719e91", "commit_訊息": "A00-20180223001 修正BPM56版,表單設計師ID第一個字必須為字母做卡控。", "提交日期": "2018-10-04 18:32:24", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5656.xls", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/util.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "4fa08b4463647716fed2880665da40ae45dd8ecc", "commit_訊息": "C01-20180925007 修正Chrome無法從子視窗使用父視窗confirm()問題  順便處理空白通過", "提交日期": "2018-10-04 17:52:18", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cb14aef84302eafec0c6f638be6340960de94f05", "commit_訊息": "Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop", "提交日期": "2018-10-04 10:02:28", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "e1a77a67ab5158d4dbb26180a303fd290c80df83", "commit_訊息": "<V56>二次修正 C01-20180528001 調整提示文字為 :僅支持T100及程的流程", "提交日期": "2018-10-04 10:01:01", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "5fff8cc619d424dd843746bc4c801d761c748811", "commit_訊息": "A00-20180717003 流程負責人在管理流程看不到授權的流程", "提交日期": "2018-10-03 17:17:42", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e62d6f150e64b058e13ad41154b9e3f67ee04f02", "commit_訊息": "C01-20180516002 修正Web表單設計師在IE開窗後無法縮放問題", "提交日期": "2018-10-03 17:16:47", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9cd17feeaebcae3ee97fba58f63127ccc5908582", "commit_訊息": "復原被覆蓋的 A00-20171208001 修正:第一關向後加簽，關卡參與者選\"單位主管\",解析不到單位主管", "提交日期": "2018-10-03 14:09:12", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9616c338d8582c9ff4557202cd663e3408da4115", "commit_訊息": "<V56> C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度", "提交日期": "2018-10-03 13:45:00", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0dda1983157a004443986b5d73e2c96e69271de6", "commit_訊息": "<V56> A00-20180124002 將T100發單和寫入發單紀錄的邏輯調整為獨立的交易", "提交日期": "2018-10-03 11:52:05", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b91e91119324c30e910e5b1e66bc8fe976be1029", "commit_訊息": "<V56> C01-20180528001 調整當流程關卡進階勾選直接簽核網址時會提示不支援ESS流程", "提交日期": "2018-10-03 11:49:48", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "d0a33914a89b235416cabf1fd06874c19a4ddba5", "commit_訊息": "A00-20180831001-1 修正BPM56版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。", "提交日期": "2018-10-02 17:05:11", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/RelevantDataHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/relevantdata/FormTypeEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ca58549065416d5cc10af2bd7ad640715e367505", "commit_訊息": "C01-20180614002 修正BPM56版,簽核流程設計師流程定義過期顯示紅叉。", "提交日期": "2018-10-02 14:35:44", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeCellRenderer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f873f2479427058c098488545b728ffca5d743be", "commit_訊息": "C01-20180927001 移除log。", "提交日期": "2018-10-02 13:59:40", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "245c02429fcb3d86c4abab9038fd7a0c73f349d8", "commit_訊息": "A00-20171003002 將log刪除。", "提交日期": "2018-09-28 16:53:18", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorCER.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3bbef320b0f6e3d2d39d923f4d1878136875ca9a", "commit_訊息": "A00-20180831001 修正BPM56版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。", "提交日期": "2018-09-28 16:35:13", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorCER.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "abd3a109bda23f8801af046dd400fd1f19107861", "commit_訊息": "C01-20180919005 修正取回重辦發生非預期錯誤導致關卡未被rollback", "提交日期": "2018-09-28 16:23:08", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "66431109604624710eff44e06f52e053efaddd8e", "commit_訊息": "C01-20180412001 從追蹤流程無法點開資料選取器", "提交日期": "2018-09-28 15:41:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4294dd4c99d9722421bc3d87fb0e13436365a396", "commit_訊息": "C01-20180927001 增加判斷當身分為admin或是模擬使用者時，不卡控權限", "提交日期": "2018-09-28 13:53:23", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d34cd287f899aec3bb317e25ceaf3480da53f57e", "commit_訊息": "C01-20180927001", "提交日期": "2018-09-27 17:00:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2cce5e6e8539078e23ab0bcaae63506adac25bc8", "commit_訊息": "A00-20180914002 修正BPM56版本,將流程設計師中呼叫網路服務設計師的Operations的背景改為白色。", "提交日期": "2018-09-27 15:57:35", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/WSInvocationEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "96bc3603e4d86a2b0bc6cde5fa397e365f99a3c1", "commit_訊息": "A00-20180925005 調整多語系寫法", "提交日期": "2018-09-27 11:20:20", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7d655d22150201e58a0f0099adcde29b42517c59", "commit_訊息": "A00-*********** 修正BPM56版本,表單更新Name後流程所掛的表單也會更新。", "提交日期": "2018-09-26 19:18:43", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/RelevantDataHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/FormDefinitionManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "872931138c3a7dd8f1b4807e753043e225e2d682", "commit_訊息": "A00-*********** 修正IMG在詳情(直連表單)簽核時若人員有多組織則待辦不會往下派送問題", "提交日期": "2018-09-26 18:08:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a7442073c69cc9a0d6aa4eec7d359068fee1e8e1", "commit_訊息": "C01-*********** ISO簡易查詢速度緩慢(增加with nolock)", "提交日期": "2018-09-21 15:56:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "896a854499cd1aeec8a55c76de2ad347fe8b8522", "commit_訊息": "C01-20180720001 表單覆蓋議題", "提交日期": "2018-09-20 17:51:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "48cea1fbb0486549e944818d58aa6e0d8dbcf745", "commit_訊息": "A00-20180919001 修正BPM56版本,退回重辦後會帶回目前處理位置的下一筆資料。", "提交日期": "2018-09-20 17:36:31", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "52d9bde7983676c83b05d9bc42342eb548751d82", "commit_訊息": "C01-20180621002 避免Web表單設計師，偶爾會出現滑鼠黏著元件的狀況發生。", "提交日期": "2018-09-19 15:43:08", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8835cf567f396f6412ff7bb49081d7a1724670c9", "commit_訊息": "A00-20180918001 修正附件開啟除了發起關卡外，都是空白問題。", "提交日期": "2018-09-18 17:03:50", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AttachmentUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc5583300438a6166580265083e8c4c369a3a12a", "commit_訊息": "C01-2018073000 修正BPM56版,從mail進入時不顯示處理下個工作的按鈕。", "提交日期": "2018-09-18 15:48:36", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a88175b8a4d0319cc70455f0ffa18b5cf8f4ba4", "commit_訊息": "Q00-20180911002 修正56版本處理的流程數據模塊篩選功能無效", "提交日期": "2018-09-18 10:54:15", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd012bf5a9acd955f255e4a2daf25dffe5eaae3b", "commit_訊息": "C01-20180906004 表單欄位消失議題", "提交日期": "2018-09-17 18:20:56", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ff2e58a2005875c4f851be138a81efc52ad2f043", "commit_訊息": "A00-20180314001 修正列印表單上面的值與表單上實際的數值不同", "提交日期": "2018-09-17 15:56:12", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "faefefe751e14ecdd847563006597b3539f85ddc", "commit_訊息": "C01-20180719004 修正BPM56版,IE點擊表單浮點數元件會跳動的問題。", "提交日期": "2018-09-17 14:33:23", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "898c55e58dc786452b36e62ca0be8c3885dbb497", "commit_訊息": "修正ESS表單追蹤流程點擊BPM列印按鈕後出現空白，因ESS表單本身已有列印按鈕，故將BPM列印按鈕隱藏。", "提交日期": "2018-09-17 13:51:48", "作者": "施廷緯", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6bf8752b80865533a585a952b1e27f51a5ba02e0", "commit_訊息": "A00-20180913001", "提交日期": "2018-09-14 17:02:26", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ca55d613ae833e779f92fb82e18785aa4e59d2a", "commit_訊息": "A00-20180529003 修正授權流程選擇的流程分類\"無\"流程實例時，會請洽系統管理員", "提交日期": "2018-09-14 16:35:08", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a8418fd1f58861a12c4bb9613358d7e5b53b008", "commit_訊息": "A00-20180913002 修正微信使用者管理頁面在IE環境下無法捲動", "提交日期": "2018-09-14 13:38:45", "作者": "治傑", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3aa901358ccd6adc283a5c02c7f34bce7c4e7f1f", "commit_訊息": "C01-20180331002 修正BPM56版本,ISO製作文件索引失敗的問題。", "提交日期": "2018-09-14 11:21:43", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/NaNa/lib/Lucene/poi-3.12-20150511.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/lib/Lucene/poi-examples-3.12-20150511.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/lib/Lucene/poi-excelant-3.12-20150511.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/lib/Lucene/poi-ooxml-3.12-20150511.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/lib/Lucene/poi-ooxml-schemas-3.12-20150511.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/lib/Lucene/poi-scratchpad-3.12-20150511.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/lib/Lucene/xmlbeans-2.6.0.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "869c27ade4e7980b51d5619bb4c13f526f913128", "commit_訊息": "修正中間層簽核session過期造成派送失敗問題", "提交日期": "2018-09-13 18:10:48", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74af074eacdd8a7a2ce48654e012a49b64064b85", "commit_訊息": "Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop", "提交日期": "2018-09-13 11:20:16", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "26bd409e0c0744a6d7001e1d24248555d347090b", "commit_訊息": "C01-20180907001 修正BPM56版本,信件通知表單有隱藏欄位時,不將隱藏欄位顯示在信上。", "提交日期": "2018-09-12 20:02:20", "作者": "顏伸儒", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd3ca9ffce320976c423f39851febc962275a205", "commit_訊息": "C01-20180723001 處理客制開窗、單選開窗、多選開窗特殊符號在Html轉換問題", "提交日期": "2018-09-12 19:51:51", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/ResultObjectForDataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c2f27230e499075cd147949c64518ea113e66931", "commit_訊息": "修正行動版Grid有單身資料時第一次進入畫面無一鍵展開按鈕", "提交日期": "2018-09-11 10:04:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}