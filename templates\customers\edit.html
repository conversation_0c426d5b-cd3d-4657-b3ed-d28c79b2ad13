{% extends "base.html" %}

{% block title %}編輯客戶連線 - BPM服務部好用工具{% endblock %}

{% block content %}
<div class="container">
    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fas fa-edit me-3"></i>
                編輯客戶連線
            </h1>
            <p class="page-subtitle">修改 {{ customer.company_name }} 的連線資訊</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        客戶連線資訊
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="/customers/edit/{{ customer.oid }}" id="editCustomerForm">
                        <div class="row g-3">
                            <!-- 公司名稱 -->
                            <div class="col-md-6">
                                <label for="company_name" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    公司名稱 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="company_name" 
                                       name="company_name" required placeholder="請輸入公司名稱"
                                       value="{{ customer.company_name }}">
                            </div>

                            <!-- 產品類型 -->
                            <div class="col-md-6">
                                <label for="product_type" class="form-label">
                                    <i class="fas fa-cube me-1"></i>
                                    產品類型 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="product_type" name="product_type" required>
                                    <option value="">請選擇產品類型</option>
                                    {% for product in product_types %}
                                    <option value="{{ product }}" {% if customer.product_type == product %}selected{% endif %}>
                                        {{ product }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- 伺服器IP -->
                            <div class="col-md-6">
                                <label for="server_ip" class="form-label">
                                    <i class="fas fa-server me-1"></i>
                                    伺服器IP <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="server_ip" 
                                       name="server_ip" required placeholder="例：*************"
                                       value="{{ customer.server_ip }}">
                            </div>

                            <!-- 埠號 -->
                            <div class="col-md-6">
                                <label for="port" class="form-label">
                                    <i class="fas fa-plug me-1"></i>
                                    埠號
                                </label>
                                <input type="text" class="form-control" id="port" 
                                       name="port" placeholder="預設：1433"
                                       value="{{ customer.port or '' }}">
                            </div>

                            <!-- 資料庫名稱 -->
                            <div class="col-md-6">
                                <label for="database_name" class="form-label">
                                    <i class="fas fa-database me-1"></i>
                                    資料庫名稱 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="database_name" 
                                       name="database_name" required placeholder="請輸入資料庫名稱"
                                       value="{{ customer.database_name }}">
                            </div>

                            <!-- 使用者名稱 -->
                            <div class="col-md-6">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    使用者名稱 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" 
                                       name="username" required placeholder="請輸入使用者名稱"
                                       value="{{ customer.username }}">
                            </div>

                            <!-- 密碼 -->
                            <div class="col-12">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    密碼 <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" 
                                           name="password" required placeholder="請輸入密碼"
                                           value="{{ customer.password }}">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 說明 -->
                            <div class="col-12">
                                <label for="description" class="form-label">
                                    <i class="fas fa-comment me-1"></i>
                                    說明
                                </label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="3" placeholder="選填：其他相關說明或備註">{{ customer.description or '' }}</textarea>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- 按鈕區域 -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="/customers" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-arrow-left me-2"></i>返回列表
                                </a>
                                <a href="/customers/detail/{{ customer.oid }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye me-2"></i>查看詳情
                                </a>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-warning me-2" id="resetForm">
                                    <i class="fas fa-undo me-2"></i>重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>更新
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 系統資訊 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        系統資訊
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <small class="text-muted">客戶ID (OID)</small>
                            <div class="font-monospace small">{{ customer.oid }}</div>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">建立時間</small>
                            <div class="small">
                                {% if customer.created_at %}
                                    {{ customer.created_at[:19].replace('T', ' ') }}
                                {% else %}
                                    未知
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">最後更新</small>
                            <div class="small">
                                {% if customer.updated_at %}
                                    {{ customer.updated_at[:19].replace('T', ' ') }}
                                {% else %}
                                    未知
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提示資訊 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        編輯說明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>注意事項</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>所有必填欄位都需要填寫</li>
                                <li><i class="fas fa-shield-alt text-info me-2"></i>密碼修改後立即生效</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>更新時間會自動記錄</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>操作提示</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-undo text-primary me-2"></i>重置會恢復到原始值</li>
                                <li><i class="fas fa-save text-success me-2"></i>更新會儲存所有變更</li>
                                <li><i class="fas fa-times text-danger me-2"></i>取消會放棄所有變更</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 儲存原始值用於重置
    const originalValues = {
        company_name: '{{ customer.company_name }}',
        product_type: '{{ customer.product_type }}',
        server_ip: '{{ customer.server_ip }}',
        port: '{{ customer.port or "" }}',
        database_name: '{{ customer.database_name }}',
        username: '{{ customer.username }}',
        password: '{{ customer.password }}',
        description: '{{ customer.description or "" }}'
    };

    // 密碼顯示/隱藏切換
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // 重置表單
    $('#resetForm').click(function() {
        if (confirm('確定要重置表單嗎？所有修改將會還原到原始值。')) {
            Object.keys(originalValues).forEach(function(key) {
                $(`#${key}`).val(originalValues[key]);
            });
            $('#password').attr('type', 'password');
            $('#togglePassword i').removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // 表單驗證
    $('#editCustomerForm').submit(function(e) {
        const serverIp = $('#server_ip').val();
        const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        
        if (serverIp && !ipPattern.test(serverIp)) {
            e.preventDefault();
            alert('請輸入有效的IP位址格式（例：*************）');
            $('#server_ip').focus();
            return false;
        }

        const port = $('#port').val();
        if (port && (isNaN(port) || port < 1 || port > 65535)) {
            e.preventDefault();
            alert('埠號必須是 1-65535 之間的數字');
            $('#port').focus();
            return false;
        }

        // 顯示載入狀態
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>更新中...');
    });

    // IP 位址格式提示
    $('#server_ip').on('blur', function() {
        const value = $(this).val();
        if (value) {
            const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            if (!ipPattern.test(value)) {
                $(this).addClass('is-invalid');
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">請輸入有效的IP位址格式</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        }
    });

    // 埠號格式提示
    $('#port').on('blur', function() {
        const value = $(this).val();
        if (value) {
            if (isNaN(value) || value < 1 || value > 65535) {
                $(this).addClass('is-invalid');
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">埠號必須是 1-65535 之間的數字</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        }
    });

    // 檢測表單變更
    let formChanged = false;
    $('#editCustomerForm input, #editCustomerForm select, #editCustomerForm textarea').on('input change', function() {
        formChanged = true;
    });

    // 離開頁面前提醒
    $(window).on('beforeunload', function() {
        if (formChanged) {
            return '您有未儲存的變更，確定要離開嗎？';
        }
    });

    // 表單提交時不顯示離開提醒
    $('#editCustomerForm').on('submit', function() {
        formChanged = false;
    });
});
</script>
{% endblock %}
