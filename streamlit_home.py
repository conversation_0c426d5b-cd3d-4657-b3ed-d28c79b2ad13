import streamlit as st
from pathlib import Path
import sys

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 設定Streamlit頁面配置
st.set_page_config(
    page_title="BPM服務部好用工具",
    page_icon="🏠",
    layout="wide",
    initial_sidebar_state="collapsed"  # 主頁保持側邊欄關閉
)

# 自定義CSS樣式
st.markdown("""
<style>
    .main-title {
        text-align: center;
        color: #2E86AB;
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .subtitle {
        text-align: center;
        color: #666;
        font-size: 1.2rem;
        margin-bottom: 3rem;
    }
    
    .tool-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 2rem;
        margin: 1rem;
        text-align: center;
        color: white;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
        border: none;
        min-height: 250px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    .tool-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.2);
    }
    
    .tool-card.release-tool {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .tool-card.search-tool {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .tool-card.customer-tool {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }


    
    .tool-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
    }
    
    .tool-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    
    .tool-description {
        font-size: 1rem;
        opacity: 0.9;
        line-height: 1.5;
    }
    
    .footer {
        text-align: center;
        margin-top: 4rem;
        padding: 2rem;
        color: #888;
        border-top: 1px solid #eee;
    }

    /* 隱藏Streamlit默認的英文頁面導航 */
    [data-testid="stSidebarNav"] {
        display: none !important;
    }

    .css-1544g2n, .css-17lntkn, .css-1y4p8pa, .css-1d391kg {
        display: none !important;
    }

    /* 側邊欄美化樣式 */
    .sidebar-nav-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        color: white;
        text-align: center;
        font-weight: bold;
    }

    .nav-section {
        margin: 1rem 0;
        padding: 0.5rem 0;
    }

    .system-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.8rem;
        margin-top: 1rem;
        border-left: 4px solid #667eea;
    }

    .system-info-item {
        font-size: 0.85rem;
        color: #666;
        margin: 0.3rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 側邊欄（默認關閉，但提供中文導航）
with st.sidebar:
    st.markdown("""
    <div class="sidebar-nav-header">
        🧭 導航選單
    </div>
    """, unsafe_allow_html=True)

    # 工具快速導航
    st.markdown("### 🛠️ 快速導航")

    if st.button("📊 產品Release記錄查詢", use_container_width=True, help="查詢各產品的Release記錄和版本資訊"):
        st.switch_page("pages/release_query.py")

    if st.button("🔍 檔案索引路徑查詢", use_container_width=True, help="搜尋檔案在各版本中的路徑位置"):
        st.switch_page("pages/file_search.py")

    if st.button("🏢 客戶連線管理系統", use_container_width=True, help="管理客戶公司的連線資訊"):
        # 設置重置標記，確保進入時回到公司清單頁面
        st.session_state.reset_to_home = True
        st.switch_page("pages/customer_connections.py")



    st.markdown("---")

    # 系統資訊
    st.markdown("""
    <div class="system-info">
        <div style="font-weight: bold; margin-bottom: 0.5rem;">ℹ️ 系統資訊</div>
        <div class="system-info-item">📦 版本：1.0.0</div>
        <div class="system-info-item">📅 更新：2025年7月</div>
        <div class="system-info-item">🏢 部門：BPM服務部</div>
        <div class="system-info-item">💡 提示：點擊上方工具卡片或使用側邊欄導航</div>
    </div>
    """, unsafe_allow_html=True)

# 主標題
st.markdown('<h1 class="main-title">🛠️ BPM服務部好用工具</h1>', unsafe_allow_html=True)
st.markdown('<p class="subtitle">選擇您需要的工具來提升工作效率</p>', unsafe_allow_html=True)

# 建立三欄布局
col1, col2, col3 = st.columns(3, gap="large")

with col1:
    # 產品release記錄查詢工具卡片
    if st.button("📊 產品Release記錄查詢工具", key="release_tool", help="點擊進入產品release記錄查詢工具", use_container_width=True):
        st.switch_page("pages/release_query.py")

    st.markdown("""
    <div class="tool-card release-tool">
        <div class="tool-icon">📊</div>
        <div class="tool-title">產品Release記錄查詢工具</div>
        <div class="tool-description">
            • 查詢BPM、BPM-ISO、NaNaXWeb專案的release記錄<br>
            • 依照branch_name瀏覽commit記錄<br>
            • 關鍵字搜尋commit訊息<br>
            • 檔案名稱搜尋功能<br>
            • 支援排序與分類顯示
        </div>
    </div>
    """, unsafe_allow_html=True)

with col2:
    # 檔案索引路徑查詢工具卡片
    if st.button("🔍 檔案索引路徑查詢工具", key="search_tool", help="點擊進入檔案索引路徑查詢工具", use_container_width=True):
        st.switch_page("pages/file_search.py")

    st.markdown("""
    <div class="tool-card search-tool">
        <div class="tool-icon">🔍</div>
        <div class="tool-title">檔案索引路徑查詢工具</div>
        <div class="tool-description">
            • 查詢class、jsp、js檔案位置<br>
            • 支援關鍵字模糊搜尋<br>
            • 上傳壓縮檔案批量查詢<br>
            • 顯示檔案修改時間<br>
            • 多版本索引支援
        </div>
    </div>
    """, unsafe_allow_html=True)

with col3:
    # 客戶連線管理系統工具卡片
    if st.button("🏢 客戶連線管理系統", key="customer_tool", help="點擊進入客戶連線管理系統", use_container_width=True):
        # 設置重置標記，確保進入時回到公司清單頁面
        st.session_state.reset_to_home = True
        st.switch_page("pages/customer_connections.py")

    st.markdown("""
    <div class="tool-card customer-tool">
        <div class="tool-icon">🏢</div>
        <div class="tool-title">客戶連線管理系統</div>
        <div class="tool-description">
            • 管理客戶公司連線資訊<br>
            • 支援BPM、HRM、Tiptop等多種產品<br>
            • 卡片式介面設計<br>
            • 原始資料參考功能<br>
            • 完整的CRUD操作
        </div>
    </div>
    """, unsafe_allow_html=True)

# 添加說明文字
st.markdown("""
<div style="text-align: center; margin-top: 2rem; color: #666;">
    點擊上方按鈕進入對應工具頁面
</div>
""", unsafe_allow_html=True)

# 頁腳資訊
st.markdown("""
<div class="footer">
    <p>🏢 BPM服務部 | 版本 1.0.0 | 最後更新：2025年7月</p>
    <p>如有問題或建議，請聯繫開發團隊</p>
</div>
""", unsafe_allow_html=True)

# 檢查是否有必要的目錄和檔案
data_dir = project_root / "data_output" / "bpm_path"
release_dir = project_root / "data_output" / "bpm_release"

if not data_dir.exists():
    st.warning("⚠️ 未找到data_output/bpm_path目錄，檔案索引查詢功能可能無法正常使用")

if not release_dir.exists():
    st.warning("⚠️ 未找到data_output/bpm_release目錄，產品release記錄查詢功能可能無法正常使用")
