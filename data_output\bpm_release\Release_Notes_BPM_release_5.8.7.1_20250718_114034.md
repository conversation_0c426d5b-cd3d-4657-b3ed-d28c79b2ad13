# Release Notes - BPM

## 版本資訊
- **新版本**: release_*******
- **舊版本**: release_5.8.6.2
- **生成時間**: 2025-07-18 11:40:34
- **新增 Commit 數量**: 275

## 變更摘要

### lorenchang (11 commits)

- **2022-06-26 21:51:38**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
  - 變更檔案: 25 個
- **2021-10-19 11:15:07**: [內部]新增check-valid-connection-sql及調整background-validation-millis為10秒
  - 變更檔案: 2 個
- **2021-10-05 17:41:18**: [內部]新增首頁模組(MPT)datasource設定
  - 變更檔案: 2 個
- **2021-10-05 14:10:12**: [內部]更改auto-deploy-exploded設定為false
  - 變更檔案: 2 個
- **2021-10-04 09:16:44**: [內部]加入Nginx https反向代理BPM http之設定
  - 變更檔案: 2 個
- **2021-10-01 14:36:34**: [內部]合併*******多語系及移除重覆內容
  - 變更檔案: 2 個
- **2021-10-01 11:18:07**: [內部]******* Oracle的Update SQL每句都補上/
  - 變更檔案: 2 個
- **2021-07-13 15:45:48**: [ESS]Q00-20210713003 補IndexNaNaDB內AppFormActivityRecord缺少的Index
  - 變更檔案: 2 個
- **2021-04-16 16:49:13**: [內部]加入*******專用多語系檔
  - 變更檔案: 1 個
- **2021-08-20 09:06:35**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2021-08-20 09:06:16**: [內部]新增清除所有Server二階快取的相關EJB及RMI接口
  - 變更檔案: 4 個

### 林致帆 (53 commits)

- **2021-10-15 17:21:59**: [Web]Q00-20211015004 修正Grid欄位如果沒有Binding其他欄位，會導致點擊Grid修改鈕的時候，該筆欄位資料會不見
  - 變更檔案: 1 個
- **2021-10-13 15:14:33**: [Web]A00-20211012002 修正使用者名稱有設定多語系，在帳號管理的頁面會有多筆該使用者的重複資料
  - 變更檔案: 1 個
- **2021-10-12 11:53:47**: [內部]調整******* MSSQL的Update SQL 部分alter table相關內容增加exist判斷
  - 變更檔案: 1 個
- **2021-10-06 17:56:11**: [Web]Q00-20211006009 追蹤流程及待辦流程的進階查詢的模糊查詢框增加hint告知只能用流程相關資料查詢
  - 變更檔案: 3 個
- **2021-10-06 17:35:06**: [TIPTOP]Q00-20211006008 調整預設出貨的TIPTOP及CRM的流程主機IP為與標準產品預設出貨的wsf1一致
  - 變更檔案: 4 個
- **2021-10-06 17:23:03**: [Web]Q00-20211006007 修正新增的使用者進入追蹤流程畫面，因為沒有設定"追蹤流程清單預設查看的流程狀態"導致畫面異常
  - 變更檔案: 1 個
- **2021-10-06 16:39:10**: [Web]Q00-20211006005修正離職交接人作業無法修改及刪除資料
  - 變更檔案: 1 個
- **2021-09-27 09:05:19**: [內部]Q00-20210927001 E10單據回傳給E10，回傳簽核權限人員如果採用設定檔，會導致排程回寫簽核歷程時報錯
  - 變更檔案: 1 個
- **2021-09-24 17:09:43**: [E10]S00-20210702001 E10回寫，採用設定檔選擇回寫E10流程指定單據審核的權限人員[補修正]
  - 變更檔案: 2 個
- **2021-09-23 18:33:06**: [E10]S00-20210702001 E10回寫，採用設定檔選擇回寫E10流程指定單據審核的權限人員[補修正]
  - 變更檔案: 2 個
- **2021-09-23 17:36:48**: [E10]S00-20210702001 E10回寫，採用設定檔選擇回寫E10流程指定單據審核的權限人員
  - 變更檔案: 3 個
- **2021-09-10 19:15:08**: [Web]S00-***********新增離職交接人作業
  - 變更檔案: 32 個
- **2021-08-16 15:06:04**: [流程設計師]S00-20210521003 新增流程設計師設定:是否可在監控流程畫面刪除流程[補修正]
  - 變更檔案: 1 個
- **2021-07-26 08:59:19**: S00-20210311003 追蹤流程清單的流程狀態採用個人資訊"追蹤流程清單預設查看的流程狀態"[補修正]
  - 變更檔案: 1 個
- **2021-07-23 13:04:03**: S00-20210311003 追蹤流程清單的流程狀態採用個人資訊"追蹤流程清單預設查看的流程狀態"
  - 變更檔案: 14 個
- **2021-07-19 10:52:06**: [流程設計師]S00-20210521003 新增流程設計師設定:是否可在監控流程畫面刪除流程[補修正]
  - 變更檔案: 1 個
- **2021-07-19 10:47:57**: [流程設計師]S00-20210521003 新增流程設計師設定:是否可在監控流程畫面刪除流程
  - 變更檔案: 12 個
- **2021-07-13 15:22:09**: [Web]S00-20210429008 上傳附件名稱長度調整為可輸入250的字[補修正]
  - 變更檔案: 2 個
- **2021-07-12 10:48:55**: [Web]S00-20210429008 上傳附件名稱長度調整為可輸入250的字
  - 變更檔案: 5 個
- **2021-09-24 16:33:42**: [Web]S00-20210924001 待辦清單接口新增回傳欄位；發起人代號，發起人部門代號，發起人部門名稱
  - 變更檔案: 4 個
- **2021-09-22 14:34:34**: [Web]Q00-20210819001 調整離職人員帳號更新排程為 預設出貨用[補修正]
  - 變更檔案: 1 個
- **2021-09-22 13:38:26**: S00-20210503006 調整出貨光碟T100的axmt510作業 不需押上截止有效日
  - 變更檔案: 1 個
- **2021-09-17 16:41:56**: [易飛]Q00-*********** 調整易飛範例流程命名及服務任務設定
  - 變更檔案: 3 個
- **2021-09-17 13:51:45**: [Web]Q00-20210819001 調整離職人員帳號更新排程為 預設出貨用
  - 變更檔案: 1 個
- **2021-09-16 14:39:55**: [Web]S00-20210318002 優化監控流程匯出Excel功能
  - 變更檔案: 2 個
- **2021-09-16 11:52:32**: [Web]Q00-20210916001調整簽核歷程及簡易流程圖流程狀態"已同意"調整回"已處理" 及 簡易流程圖 增加"已會辦"流程狀態
  - 變更檔案: 2 個
- **2021-09-14 17:36:54**: [Web]A00-20210913001 修正從BPM首頁的待辦清單由第二筆簽核跳到下一筆，都會跳到流程清單的第一筆流程
  - 變更檔案: 1 個
- **2021-09-09 18:03:26**: [Web]A00-20210907001 修正先看ESS流程草稿後在點擊一般流程草稿會導致報錯
  - 變更檔案: 1 個
- **2021-09-08 18:15:18**: [Web]A00-20210908001 修正從待辦事項連結登入BPM後應該要為該流程的簽核頁面，而不是代辦清單頁面
  - 變更檔案: 1 個
- **2021-09-08 11:08:23**: [Web]S00-20210316001 流程筆數為1000時，呈現筆數為1000，不再用999+
  - 變更檔案: 1 個
- **2021-08-25 18:16:33**: [Web]A00-20210825002 修正使用者用IE11登入，線上人數查詢登入裝置資訊為IE7.0
  - 變更檔案: 1 個
- **2021-08-18 18:19:37**: [流程引擎]	Q00-20210818002修正SQLcommand放的SQL指令有簡體字，會造成base64加密報錯
  - 變更檔案: 2 個
- **2021-08-17 16:49:17**: [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
  - 變更檔案: 1 個
- **2021-08-16 16:12:38**: [流程引擎]S00-20210113002調整流程前一關為服務任務，派送到下一關主旨會以下一關處理者的預設語系[補修正]
  - 變更檔案: 1 個
- **2021-08-13 17:05:11**: [內部]Q00-20210813003 修正"取得流程圖資料"接口的欄位performerId的內容不該為OID
  - 變更檔案: 1 個
- **2021-08-12 17:29:10**: [流程引擎]S00-20210113002調整流程前一關為服務任務，派送到下一關主旨會以下一關處理者的預設語系
  - 變更檔案: 1 個
- **2021-08-09 10:49:07**: [TIPTOP]Q00-20210511004 修正TIPTOP拋單回傳給TIPTOP失敗，流程可發起成功[補修正]
  - 變更檔案: 1 個
- **2021-08-06 16:23:26**: [流程設計師]S00-20210318004 修正連接線條件式輸入空格，顯示上會被替換成空字串
  - 變更檔案: 1 個
- **2021-08-04 15:06:56**: [Web]S00-20200528001 優化簽核歷程的流程狀態"已處理"更改成"已同意"
  - 變更檔案: 1 個
- **2021-08-04 15:02:24**: [Web]S00-20210202001 簡易流程圖的流程狀態字眼明確化
  - 變更檔案: 3 個
- **2021-08-03 17:18:10**: [內部]Q00-20210803002優化log訊息：T100拋單時，取得附件的檔案編碼ID以及文檔中心的URL沒有設定
  - 變更檔案: 1 個
- **2021-08-03 11:39:22**: [Web]S00-20210315001 增加轉派意見在待辦流程的主旨上 [補修正]
  - 變更檔案: 1 個
- **2021-08-02 14:26:11**: [Web]S00-20210315001 增加轉派意見在待辦流程的主旨上
  - 變更檔案: 2 個
- **2021-07-29 14:38:49**: [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
  - 變更檔案: 1 個
- **2021-07-26 18:08:39**: [Web]A00-20210726003修正ajax_CommonAccessor的findXmlContent,findResource接口取得內容為中文亂碼
  - 變更檔案: 1 個
- **2021-07-26 15:44:11**: [流程設計師]S00-20210305001 調整服務任務讀取https的WSDL，增加SSL憑證失敗的明確提示窗
  - 變更檔案: 6 個
- **2021-07-26 14:35:15**: [內部]Q00-20210726001 DatabaseAccessor 移除不需要的System.out.print方法
  - 變更檔案: 1 個
- **2021-07-23 18:17:49**: [Web]A00-20210720001 修正絕對位置表單在追蹤流程頁面，表單範圍外的元件顯示出來
  - 變更檔案: 1 個
- **2021-07-20 14:57:22**: [Web]C01-20210706002 修正流程第二關設置radiobutton為invisible狀態，第二關簽核後該元件內容會消失
  - 變更檔案: 1 個
- **2021-07-14 16:59:02**: [Web]S00-*********** 調整人員離職自動將帳號功能停用，復職則帳號功能啟用
  - 變更檔案: 6 個
- **2021-07-13 14:03:42**: [Web]Q00-20210713002修正表單頁籤簽核歷程置放位置選擇"top"且表單設計師設定"顯示流程簽核意見"為"NOT_SHOW"，待辦跟發起畫面的ESS表單上方會顯示"簽核意見"的文字
  - 變更檔案: 1 個
- **2021-07-13 10:47:09**: [Web]Q00-20210702002修正流程最後一關是通知任務，流程設計師在該關卡增加BasicType的流程變數到工具定義表，導致查看流程異常
  - 變更檔案: 1 個
- **2021-07-08 18:56:05**: [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法
  - 變更檔案: 2 個

### walter_wu (21 commits)

- **2021-10-14 15:54:16**: [流程引擎]Q00-20211014003 修正加簽有異常卻未將原始錯誤印出導致出錯無法排查
  - 變更檔案: 1 個
- **2021-10-08 15:56:39**: [Web]S00-20210506008 將列印表單時的"列印流程的表單資料"(頁首文字)改為表單名稱
  - 變更檔案: 4 個
- **2021-06-04 16:42:53**: [ESS]A00-20210521002 修正ESS刪除EFGP缺席紀錄的session bean無作用
  - 變更檔案: 3 個
- **2021-09-30 17:47:37**: [流程引擎]A00-20210913002 增加判斷是否因為代理人導致觸發自動簽核並處理相關邏輯
  - 變更檔案: 1 個
- **2021-09-29 18:06:36**: [Web]Q00-20210928002 優化追蹤流程通知信URL進入速度
  - 變更檔案: 1 個
- **2021-09-29 18:04:48**: Revert "已修正。git請搜尋"[Web]Q00-20210928002 優化追蹤流程通知信URL進入速度"
  - 變更檔案: 1 個
- **2021-09-29 17:58:01**: 已修正。git請搜尋"[Web]Q00-20210928002 優化追蹤流程通知信URL進入速度
  - 變更檔案: 1 個
- **2021-09-24 17:22:33**: [Web]A00-20210906002 修正如果核決層級參考的關卡被代理過預解析會有異常
  - 變更檔案: 2 個
- **2021-09-17 11:37:10**: Q00-20210917001 增加防呆如果自己設定為自己的主管，在用核決層級參考預解析時會導致無窮迴圈
  - 變更檔案: 1 個
- **2021-09-10 17:14:17**: [Web]Q00-20210910003 修正如果再Orderby使用表別名Oracle會報錯
  - 變更檔案: 1 個
- **2021-09-10 11:54:44**: [流程引擎]Q00-20210727002 修正因為關卡設定自動跳關導致代理機制異常[補修正]
  - 變更檔案: 1 個
- **2021-09-09 09:24:32**: [Web]Q00-20210909001 修正加簽後，在流程圖預覽無法看到加簽關卡
  - 變更檔案: 1 個
- **2021-09-08 17:04:07**: [WebService]Q00-20210908003 修正DotJ登入，所記錄的使用者登入資訊沒有加上Locale導致寫入DB時報錯
  - 變更檔案: 1 個
- **2021-09-07 13:53:07**: [流程引擎]Q00-20210907003 修正發起時就算儲存表單，核決層級預解析因為沒有抓到設定的表單欄位而無法解析
  - 變更檔案: 1 個
- **2021-09-01 17:25:55**: [流程引擎]A00-20210901001 修正客戶Grid資料過多導致SQL組過複雜導致DB報錯
  - 變更檔案: 1 個
- **2021-08-16 13:48:13**: [流程引擎]Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回[補修正]
  - 變更檔案: 1 個
- **2021-08-13 19:23:24**: Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回
  - 變更檔案: 1 個
- **2021-08-10 11:05:43**: [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
  - 變更檔案: 1 個
- **2021-07-27 16:50:00**: [Web]A00-20210727001 修正XPDL的流程在監控流程中跳過關卡時，驗證密碼視窗會一片空白
  - 變更檔案: 1 個
- **2021-07-27 15:57:42**: [流程引擎]Q00-20210727002 修正因為關卡設定自動跳關導致代理機制異常
  - 變更檔案: 1 個
- **2021-07-13 10:30:47**: A00-20210630001 修正設定多語系後沒有設定的語系無法吃到預設值
  - 變更檔案: 1 個

### shihya_yu (20 commits)

- **2021-10-14 12:03:55**: [BPM APP]Q00-20211014001 修正入口平台整合設定進入編輯再點其他工具佈署，資訊顯示異常問題
  - 變更檔案: 1 個
- **2021-10-12 17:10:18**: [BPM APP] 新增簽核流設計師中活動表單元件權限，行動端可複製Web端設定功能[補]
  - 變更檔案: 10 個
- **2021-10-12 17:05:51**: [內部]Q00-20211005001 修正簽核流設計師中，行動端複製PC端設定有不同步的狀況
  - 變更檔案: 2 個
- **2021-10-08 18:15:39**: [BPM APP]Q00-20211005004 修正左上方IMG的返回按鈕，會偶發沒顯示問題
  - 變更檔案: 6 個
- **2021-10-08 14:12:53**: [BPM APP]Q00-20210716001 調整行動版表單自定義開窗，單選時checkPointOnClose事件會觸發兩次問題
  - 變更檔案: 1 個
- **2021-10-06 19:36:14**: [BPM APP]Q00-20210831002 修正當Grid區塊沒放置元件，表單畫面會產生多餘的空白區塊問題
  - 變更檔案: 1 個
- **2021-10-06 11:37:31**: [BPM APP]Q00-20210806001 調整行動端腳本樣版setLabelColor設定元件標籤字體顏色說明
  - 變更檔案: 2 個
- **2021-10-06 11:11:02**: [BPM APP]Q00-20210824001 調整Line推播資訊，當未填寫主旨時設定預設資訊，修正會推播失敗問題
  - 變更檔案: 1 個
- **2021-10-05 11:57:21**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2021-10-05 11:57:06**: [BPM APP]Q00-20210831001 修正當流程設定表單欄位為唯讀時，部分元件設定setValue異常問題
  - 變更檔案: 1 個
- **2021-10-05 11:55:18**: [BPM APP]Q00-20210915001 修正Grid綁定設定小數點後幾位的TextBox元件時，會跑版問題
  - 變更檔案: 1 個
- **2021-09-22 14:29:45**: [BPM APP]C01-20210914004 修正行動表單上產品多選開窗(DialogInputMult元件)的資訊沒有回寫到開窗選項上的問題
  - 變更檔案: 1 個
- **2021-09-17 13:38:01**: [BPM APP] 新增簽核流設計師中活動表單元件權限，行動端可複製Web端設定功能
  - 變更檔案: 14 個
- **2021-09-15 16:45:30**: [BPM APP]C01-20210913003 修正當行動版表單有多個SubTab元件，使用formScript無法正常隱藏對應分頁問題
  - 變更檔案: 1 個
- **2021-08-30 10:14:31**: [BPM APP]C01-20210825004 修正移動端加簽功能，選擇人員資訊會撈到離職人員問題
  - 變更檔案: 1 個
- **2021-08-30 09:17:33**: [BPM APP]Q00-20210819003 修正IMG表單畫面，操作到其他頁面再重整表單時，左上方IMG的返回按鈕沒顯示問題[補]
  - 變更檔案: 4 個
- **2021-08-27 10:08:30**: [BPM APP]Q00-20210819003 修正IMG表單畫面，操作到其他頁面再重整表單時，左上方IMG的返回按鈕沒顯示問題
  - 變更檔案: 1 個
- **2021-08-16 18:00:02**: [BPM APP]Q00-20210816002 修正IMG中間層只有在待辦應用才產生上一關卡. 下一關卡資訊
  - 變更檔案: 1 個
- **2021-08-13 14:34:36**: [BPM APP]Q00-20210812002 修正IMG待辦列表可依關卡建立時間排序功能
  - 變更檔案: 2 個
- **2021-08-13 14:33:12**: [BPM APP]Q00-20210813001 調整IMG列表資訊的架構，以符合關鍵字搜尋後，組篩選條件的資訊
  - 變更檔案: 3 個

### pinchi_lin (24 commits)

- **2021-10-13 19:22:52**: [內部]首頁模組的公告申請單引入js移至NaNaWeb中維護
  - 變更檔案: 1 個
- **2021-10-12 11:46:51**: [BPM APP]Q00-20211006003 修正ESS流程在手機端簽核時無法查看附件問題
  - 變更檔案: 2 個
- **2021-10-06 18:13:05**: [BPM APP]Q00-20211006004 修正使用IMG發起ESS流程夾帶附件後在手機簽核時會看不到附件問題
  - 變更檔案: 4 個
- **2021-07-27 14:56:24**: [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
  - 變更檔案: 2 個
- **2021-06-22 18:53:55**: [BPM APP]S00-20210517001 新增IMG呼叫行事曆刪除接口功能[補]
  - 變更檔案: 2 個
- **2021-09-29 11:12:46**: [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題
  - 變更檔案: 6 個
- **2021-09-29 10:49:21**: [BPM APP]Q00-20210929001 修正LINE使用預設port時連結會與login設定不同導致無法登入問題
  - 變更檔案: 1 個
- **2021-09-06 16:32:31**: [BPM APP]Q00-20210906001 調整會議模組的掃碼與推送通知功能沒卡控判斷整合IMG系統變數
  - 變更檔案: 2 個
- **2021-09-03 16:27:59**: [BPM APP]Q00-20210903001 修正顯示流程中簽核人員沒有多語系的問題
  - 變更檔案: 2 個
- **2021-09-02 18:52:40**: [BPM APP]Q00-20210902001 修正BPMAPP所有用到的頁面JSP引入css、js瀏覽器會有緩存問題
  - 變更檔案: 7 個
- **2021-08-20 11:14:44**: [BPM APP]Q00-20210820002 修正企業微信與钉钉的列表頁面jsp引入css、js瀏覽器會有緩存問題
  - 變更檔案: 6 個
- **2021-08-20 10:58:48**: [BPM APP]S00-20210805001 調整企業微信與钉钉的追蹤流程預先顯示已處理流程頁面
  - 變更檔案: 4 個
- **2021-08-16 15:56:50**: [BPM APP]還原行動版用的Grid(單身)顯示摘要功能
  - 變更檔案: 2 個
- **2021-07-28 15:35:58**: [BPM APP]優化移動表單的顯示流程樣式
  - 變更檔案: 2 個
- **2021-07-28 15:12:07**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 4 個
- **2021-07-28 14:58:22**: [內部]移除移動端無用程式並新增一個移動端共用服務工具程式供後續調整
  - 變更檔案: 2 個
- **2021-07-27 17:49:44**: [BPM APP]Q00-20210727003 修正企業微信推送消息中取access_token判斷過期的邏輯異常問題
  - 變更檔案: 1 個
- **2021-07-27 15:01:42**: [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
  - 變更檔案: 1 個
- **2021-07-27 14:36:19**: [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
  - 變更檔案: 8 個
- **2021-07-22 18:26:41**: [BPM APP]Q00-20210722001 修正IMG的jsp引入css、js瀏覽器會有緩存問題
  - 變更檔案: 6 個
- **2021-07-22 17:27:07**: [BPM APP]C01-20210721005 修正整合钉钉在安卓開表單頁面時icon變文字問題
  - 變更檔案: 6 個
- **2021-07-16 18:09:48**: [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法
  - 變更檔案: 3 個
- **2021-07-15 13:59:06**: [內部]調整排版
  - 變更檔案: 1 個
- **2021-07-12 17:12:16**: [內部]調整排版
  - 變更檔案: 1 個

### cherryliao (28 commits)

- **2021-10-13 18:22:47**: [BPM APP]Q00-20210913001 調整行動端上傳附件發生錯誤時前端回應資訊錯誤的問題[補]
  - 變更檔案: 1 個
- **2021-10-13 14:50:53**: [BPM APP]Q00-20211005005 修正行動端詳情表單在操作Grid新增、編輯或取消按鈕時需要點擊兩次才會觸發動作的問題[補]
  - 變更檔案: 1 個
- **2021-10-12 16:24:46**: [BPM APP]Q00-20210913003 調整上傳附件時loading圖示顯示的時機
  - 變更檔案: 3 個
- **2021-10-08 18:37:31**: [BPM APP]Q00-20211005005 修正行動端詳情表單在操作Grid新增、編輯或取消按鈕時需要點擊兩次才會觸發動作的問題
  - 變更檔案: 1 個
- **2021-10-08 14:50:33**: [BPM APP]Q00-20210913001 調整行動端上傳附件發生錯誤時前端回應資訊錯誤的問題
  - 變更檔案: 3 個
- **2021-10-06 15:03:46**: [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能[補]
  - 變更檔案: 2 個
- **2021-10-05 11:05:47**: [表單設計師]S00-20210527001 新增Web表單設計師批量匯出功能
  - 變更檔案: 6 個
- **2021-08-23 14:19:08**: [流程引擎]S00-20210305003 調整流程設計師關卡進階設定的不寄送通知信功能
  - 變更檔案: 16 個
- **2021-07-23 14:53:41**: [Web]S00-20201230001 調整resource.version改取系統啟動時當前系統時間避免版更後js、css緩存問題
  - 變更檔案: 3 個
- **2021-06-09 14:56:52**: [表單設計師]S00-*********** 表單設計師新增腳本資訊和還原上一版FromScript功能[補]
  - 變更檔案: 2 個
- **2021-06-08 18:06:10**: [表單設計師]S00-*********** 表單設計師新增腳本資訊和還原上一版FromScript功能
  - 變更檔案: 18 個
- **2021-09-28 17:22:21**: [T100]S00-*********** 新增T100流程終止或撤銷時，若單據修改且重新送審後於BPM表單可查看之前審批流程的功能
  - 變更檔案: 7 個
- **2021-09-16 13:37:04**: [流程引擎]S00-20210730004 調整Textbox設定浮點數、顯示千分位和小數點幾位時binding到Grid沒有千分位的問題
  - 變更檔案: 2 個
- **2021-09-13 18:14:23**: [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能
  - 變更檔案: 4 個
- **2021-09-13 17:18:08**: [Web]S00-20210902001 優化Web端表單E10子單身呈現樣式
  - 變更檔案: 3 個
- **2021-09-02 13:55:28**: [內部]S00-*********** 調整開發者工具頁面新增清除快取資料功能[補]
  - 變更檔案: 1 個
- **2021-08-30 15:43:46**: [內部]S00-*********** 調整開發者工具頁面新增清除快取資料功能
  - 變更檔案: 6 個
- **2021-08-06 17:08:58**: [Web]A00-20210806001 修正日期和時間元件設定多語系提示文字，但只顯示預設值的問題
  - 變更檔案: 1 個
- **2021-08-06 16:00:02**: [Dot.J]S00-20201124001 調整當設定檔找不到Secure時，設定Secure為false避免ECP呼叫DotJIntegration溝通時發生異常
  - 變更檔案: 1 個
- **2021-08-04 17:29:32**: [系統管理工具]S00-20200616002 調整點選線上使用者的通知，若使用者已登出時會跳出的錯誤訊息
  - 變更檔案: 6 個
- **2021-08-04 14:23:10**: [流程引擎]S00-20201118001 調整當流程關卡中有工作被轉派給代理人處理且流程通知設為結案逐級通知時通知原處理者
  - 變更檔案: 1 個
- **2021-07-29 15:44:18**: [組織設計師]S00-20210506001 調整設定流程代理人時不顯示已失效的流程
  - 變更檔案: 3 個
- **2021-07-27 14:24:25**: [表單設計師]S00-20200716001 新增日期和時間元件預設值配置功能[補]
  - 變更檔案: 5 個
- **2021-07-21 10:52:41**: [表單設計師]S00-20200917002 調整表單設計師Gird勾選最前面欄位設為自動增加流水號時新增一列序號欄位
  - 變更檔案: 3 個
- **2021-07-19 17:54:22**: [表單設計師]S00-20200716003 增加TextBox數字轉文字功能可設定將文字呈現於另一個TextBox欄位中
  - 變更檔案: 17 個
- **2021-07-13 18:41:33**: [Web]S00-20200821001 調整表單TextBox元件於可編輯模式下onblur時檢查資料型態
  - 變更檔案: 2 個
- **2021-07-13 18:22:29**: [表單設計師]S00-20200716001 新增日期和時間元件預設值配置功能
  - 變更檔案: 7 個
- **2021-07-09 11:20:44**: [Web]Q00-20210709001 修正checkbox、radio元件已選擇新樣式的問題
  - 變更檔案: 2 個

### yamiyeh10 (32 commits)

- **2021-10-13 17:22:02**: [BPM APP]Q00-20210831001 修正當流程設定表單欄位為唯讀時，部分元件設定setValue異常問題[補]
  - 變更檔案: 1 個
- **2021-10-13 10:41:18**: [BPM APP]Q00-20211012002 調整行動端詳情頁面的日期與時間元件在iOS 15下會跑版問題
  - 變更檔案: 1 個
- **2021-10-13 10:39:21**: [BPM APP]Q00-20210804003 修正行動端表單當流程設定多人且只需一位處理時不會顯示彈出視窗詢問是否要接收並派送訊息問題[補]
  - 變更檔案: 1 個
- **2021-10-12 14:39:51**: [BPM APP]Q00-20211012001 調整入口平台整合設定中連線管理頁面在整合鼎捷移動時增加鼎捷移動平台管理後台填外網提示
  - 變更檔案: 1 個
- **2021-10-12 14:12:15**: [BPM APP]優化移動表單的顯示流程樣式[補]
  - 變更檔案: 1 個
- **2021-10-07 09:48:21**: [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題[補]
  - 變更檔案: 1 個
- **2021-10-07 08:58:08**: [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題[補]
  - 變更檔案: 1 個
- **2021-10-06 15:55:33**: [BPM APP]Q00-20211005002 將行動端詳情頁面的發送通知中選擇人員畫面上浮動按鈕調整新樣式
  - 變更檔案: 5 個
- **2021-10-06 14:48:18**: [BPM APP]Q00-20210804003 修正行動端表單當流程設定多人且只需一位處理時不會顯示彈出視窗詢問是否要接收並派送訊息問題
  - 變更檔案: 2 個
- **2021-10-06 12:07:46**: [BPM APP]Q00-20210915002 修正行動端E10表單當Grid有多筆單身資料且其中有子單身無資料時會出現錯誤訊息問題
  - 變更檔案: 2 個
- **2021-10-05 16:23:23**: [BPM APP]Q00-20210823001 修正行動端表單當退回重辦必填簽核意見時彈窗沒有遮罩問題
  - 變更檔案: 2 個
- **2021-10-05 14:23:58**: [BPM APP]Q00-20210910002 將行動端詳情頁面的加簽選擇人員畫面上浮動按鈕調整新樣式[補]
  - 變更檔案: 4 個
- **2021-10-05 13:45:07**: [BPM APP]Q00-20210910001 將行動端詳情頁面的轉由他人處理中選擇人員畫面上浮動按鈕調整新樣式
  - 變更檔案: 2 個
- **2021-10-05 11:56:33**: [BPM APP]Q00-20210910002 將行動端詳情頁面的加簽選擇人員畫面上浮動按鈕調整新樣式
  - 變更檔案: 5 個
- **2021-10-05 11:25:49**: [BPM APP]Q00-20210913004 修正在企業微信的追蹤已簽核附件頁面中返回按鈕沒有反應問題
  - 變更檔案: 1 個
- **2021-09-01 16:38:34**: [BPM APP]調整行動端詳情表單的簡易簽核歷程可依系統變數設定是否要顯示全部資料功能[補]
  - 變更檔案: 2 個
- **2021-09-28 14:28:57**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 1 個
- **2021-09-28 14:26:59**: [BPM APP]優化移動表單的顯示流程樣式[補]
  - 變更檔案: 1 個
- **2021-09-08 16:51:33**: [BPM APP]Q00-20210907005 修正Line推播訊息在Textbox有設定顯示千分位時沒顯示問題
  - 變更檔案: 1 個
- **2021-09-08 15:14:14**: [BPM APP]Q00-20210907005 修正Line推播訊息在選項元件有設定額外輸入框且有值時不會顯示問題
  - 變更檔案: 1 個
- **2021-09-01 16:29:16**: [BPM APP]調整行動端詳情表單的簡易簽核歷程可依系統變數設定是否要顯示全部資料功能
  - 變更檔案: 7 個
- **2021-08-23 17:31:08**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 4 個
- **2021-08-19 17:37:43**: [BPM APP]優化移動表單的顯示流程樣式
  - 變更檔案: 26 個
- **2021-08-16 14:04:57**: [BPM APP]Q00-20210816001 修正IMG詳情頁面中的顯示流程標題沒有多語系問題
  - 變更檔案: 1 個
- **2021-08-11 17:25:53**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 5 個
- **2021-08-11 10:47:21**: [BPM APP]Q00-20210811001 修正使用Line官方帳號登入的BPM,直接點擊ESSQ表單類會空白的問題
  - 變更檔案: 1 個
- **2021-08-04 10:45:21**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 2 個
- **2021-07-30 15:08:51**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 1 個
- **2021-07-30 14:30:37**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 15 個
- **2021-07-29 16:18:32**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 10 個
- **2021-07-28 10:33:55**: [BPM APP]優化移動表單的簽核歷程樣式
  - 變更檔案: 7 個
- **2021-07-20 11:18:20**: [BPM APP]C01-20210714002 修正IMG詳情頁面的簽核歷程若前面存在與當前關卡相同關卡id時不會顯示問題
  - 變更檔案: 1 個

### 王鵬程 (37 commits)

- **2021-10-13 17:01:42**: [Web]Q00-20211013003 修正當ListBox從設定選項中只有一個列時並刪除列，再改成從資料庫帶值，儲存表單會出現錯誤
  - 變更檔案: 1 個
- **2021-10-06 15:46:55**: [Web]A00-20211001002 修正絕對位置表單當附件的描述過長時，會導致預覽列印時出現附件資訊和簽核歷程重疊
  - 變更檔案: 1 個
- **2021-09-29 18:37:17**: [Web]S00-*********** SQL註冊器新增查詢功能及維護頁面中SQL語法欄位新增剩餘字數提示
  - 變更檔案: 14 個
- **2021-05-21 15:25:08**: [Web]Q00-20210521001 修正關注類別維護的英文是錯誤的內容
  - 變更檔案: 2 個
- **2021-09-30 17:55:12**: [Web]Q00-20210930005 修正使用SQLcommand時指令帶有中文會導致造成base64加密報錯
  - 變更檔案: 2 個
- **2021-09-16 14:16:21**: [Web]Q00-20210916002 調整退回重辦視窗中confirm按確認後，不再提示成功訊息，避免使用者按下右上角關閉導致表單未刷新頁面
  - 變更檔案: 1 個
- **2021-09-14 18:16:53**: [Web]Q00-20210914003 調整修改密碼的彈出視窗較小，導致按確認後alert的按鈕不能直接點選
  - 變更檔案: 1 個
- **2021-09-14 14:16:32**: [Web]A00-20210906001 修正絕對位置表單日期元件勾選顯示時間並比對另一個日期，條件設==，則欄位上時和分會歸零
  - 變更檔案: 1 個
- **2021-09-13 18:22:14**: [Web]A00-20210909001 修正在PC版表單設計師中更改Grid代號，在切換到行動版表單，畫面上顯示的Grid代號未改變
  - 變更檔案: 1 個
- **2021-09-08 16:10:32**: [Web]Q00-20210908002 修正在一些https環境下會無法下載檔名有中文的檔案
  - 變更檔案: 1 個
- **2021-09-06 18:06:23**: [Web]Q00-20210906003 修正流程主旨允許用html並有輸入tr或tbody的tag，導致在BPM首頁中無法從下方待辦進入流程
  - 變更檔案: 1 個
- **2021-09-03 17:56:00**: [Web]Q00-20210903002 調整模組中的程式開啟方式是設定另開新視窗時，使可以開啟多個不同程式名稱的視窗
  - 變更檔案: 1 個
- **2021-09-01 18:39:24**: [Web]A00-*********** 修正允許流程主旨使用html時，在行動版下主旨無法呈現html的樣式
  - 變更檔案: 1 個
- **2021-09-01 15:25:00**: [Web]A00-20210831001 修正使用英文語系登入，在轉由他人處理頁面中，轉由他人處理內的標題和選項翻譯錯誤
  - 變更檔案: 1 個
- **2021-08-30 11:59:38**: [Web]Q00-*********** 修正在行動版下，選擇退回重辦彈出來的視窗中，無法選擇要退回的關卡
  - 變更檔案: 1 個
- **2021-08-27 11:45:39**: [Web]A00-20210826001 修正退回重辦時簽核意見有換行，導致流程圖中檢視參與者型式的關卡頁面的工作列表無法出現
  - 變更檔案: 2 個
- **2021-08-26 15:19:20**: [Web]A00-20210825001 修正在新增關卡頁面中，在關卡的參與者和工作的處理方式在英文顯示時有誤
  - 變更檔案: 1 個
- **2021-08-19 18:10:49**: [Web]Q00-20210819005 修正使用singleOpenWin開窗，搜尋條件使用簡體字會搜尋不到資料
  - 變更檔案: 1 個
- **2021-08-18 11:57:45**: [Web]Q00-20210818001 調整當瀏覽器封鎖BPM的彈窗時，在使用模擬模式進入監控或是進入待辦提示使用者開啟設定的訊息內容
  - 變更檔案: 2 個
- **2021-08-17 18:35:42**: [Web]Q00-20210817002 修正當瀏覽器封鎖BPM站台的彈窗時，模擬使用者後進入待辦流程中會提示使用者去設定允許彈窗
  - 變更檔案: 1 個
- **2021-08-17 17:51:50**: [Web]Q00-20210817001 修正當瀏覽器封鎖BPM站台的彈窗時，且勾選啟動流程測試模式進入監控流程，會提示使用者去設定允許的彈窗
  - 變更檔案: 1 個
- **2021-08-10 18:02:37**: [Web]Q00-20210810001 修正關卡通知信設定以整張表單時，checkbox元件在信件上呈現應該要為顯示值
  - 變更檔案: 1 個
- **2021-08-05 18:21:30**: [Web]A00-20210804003 修正在行動版畫面的監控流程清單中每列的主旨未對齊
  - 變更檔案: 1 個
- **2021-08-05 17:09:47**: [Web]Q00-20210805002 修正關卡通知信設定以整張表單時，dropdown元件在信件上呈現應該要為顯示值
  - 變更檔案: 1 個
- **2021-08-03 18:16:51**: [Web]Q00-20210803003 修正因之前議題的修正導致移動端功能『入口平台整合設定』頁面出現錯誤
  - 變更檔案: 1 個
- **2021-07-30 10:48:01**: [Web]Q00-20210730001 修正Grid設小螢幕使用名片式並綁定Textarea，在行動版下有輸入換行，Grid呈現應該要是沒換行
  - 變更檔案: 1 個
- **2021-07-28 18:11:54**: [Web]Q00-20210728001 修正透過URL開啟『追蹤流程實例內單一表單資料』，在行動版時未顯示表單名稱
  - 變更檔案: 1 個
- **2021-07-28 15:30:18**: [Web]A00-20210726005 修正表單是絕對表單時，透過URL開啟『追蹤流程實例內單一表單資料』，在行動裝置版面下附件按鈕沒有字樣
  - 變更檔案: 1 個
- **2021-07-27 18:08:51**: [Web]A00-20210727002 修正當Grid有設定將最前面欄位設為流水號時，用流水號排序時應該用數字大小來排序
  - 變更檔案: 1 個
- **2021-07-27 17:11:59**: [Web]A00-20210726002 修正行動版下，從追蹤進去流程且該流程有被同個人簽核多次過，在取回清單頁面中下拉選單會出現奇怪文字
  - 變更檔案: 1 個
- **2021-07-26 18:20:13**: [Web]A00-20210726001 修正關卡有勾選允許批次簽核，在行動版畫面的待辦事項清單中每列的主旨未對齊
  - 變更檔案: 1 個
- **2021-07-21 16:53:57**: [Web]Q00-20210721002 修正Grid綁textarea並輸入換行，畫面縮成mobile再到PC，Grid中資料會變成未換行
  - 變更檔案: 1 個
- **2021-07-20 19:54:00**: [Web]Q00-*********** 修正Grid綁定textarea並輸入換行，點擊排序後再點選Row，帶回textarea會出現<br>
  - 變更檔案: 1 個
- **2021-07-16 16:11:45**: [Web]Q00-20210716002 修正Grid綁定check、radio且設額外產生輸入框，通知信設定以表單元件時通知信的Grid會跑版
  - 變更檔案: 1 個
- **2021-07-14 16:33:22**: [Web]Q00-*********** 修正Grid有綁定Checkbox時，關卡通知信設定以表單元件時，通知信的Grid會跑版
  - 變更檔案: 1 個
- **2021-07-13 12:08:30**: [Web]Q00-20210713001  修正在首頁模組中的追蹤流程區塊，進入ESSF03表單會被截斷一半
  - 變更檔案: 1 個
- **2021-07-08 19:50:15**: [Web]Q00-20210708003 修正將Grid調整為可支援換行標籤<br>
  - 變更檔案: 1 個

### waynechang (9 commits)

- **2021-10-12 17:58:43**: [內部]******* Oracle的Update SQL每句都補上/
  - 變更檔案: 1 個
- **2021-10-06 17:16:33**: [SAP]Q00-20211006006 優化SAP整合服務，回傳Grid資料時，同時支持RWD表單Grid及絕對位置表單Grid
  - 變更檔案: 2 個
- **2021-10-05 18:07:21**: [內部]補上 5871缺少的InitNaNaDB[補]
  - 變更檔案: 1 個
- **2021-10-05 15:09:05**: [內部]補上「在線閱讀模組」浮水印多語系內容
  - 變更檔案: 1 個
- **2021-10-05 10:56:11**: [內部]更新******* patch檔
  - 變更檔案: 1 個
- **2021-10-05 10:48:08**: [內部]補上 5871缺少的InitNaNaDB
  - 變更檔案: 3 個
- **2021-09-07 10:40:16**: [Web]Q00-20210907002 Ajax_FormAccessor.getFormInstanceAttachmentSize() 增加「取得當前表單實例的附件數量」的方法
  - 變更檔案: 2 個
- **2021-08-11 14:51:02**: [在線閱覽]S00-*********** 在線閱覽模組新增自訂浮水印內容及測試轉檔功能
  - 變更檔案: 37 個
- **2021-07-28 16:01:27**: [ISO]S00-20210507001 PDF浮水印屬性管理新增「圖片浮水印」功能(BCL8)
  - 變更檔案: 1 個

### yanann_chen (35 commits)

- **2021-10-08 18:33:55**: [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題[補]
  - 變更檔案: 1 個
- **2021-10-07 12:00:51**: [流程引擎]A00-20211006001 修正「部分流程無法產出作業流程書」的問題
  - 變更檔案: 1 個
- **2021-10-05 17:21:52**: [Web]Q00-20210826001 修正「若有為null的資料時，無法載入資料至Grid」的問題[補]
  - 變更檔案: 1 個
- **2021-10-05 11:14:28**: [Web]A00-20211004001 修正多選自定義開窗在無資料的情況下，勾選表頭的「全選」選項時會帶出一筆空白資料
  - 變更檔案: 1 個
- **2021-08-05 16:01:14**: [報表設計器]Q00-20210805001 調整報表設計器清單資料欄位長度限制
  - 變更檔案: 4 個
- **2021-07-27 18:11:49**: [系統管理工具]Q00-20210727004 系統管理工具無法刪除文件主機資料
  - 變更檔案: 4 個
- **2021-07-27 18:08:50**: Revert "Q00-20210727004 系統管理工具無法刪除文件主機資料"
  - 變更檔案: 4 個
- **2021-07-27 18:04:55**: Q00-20210727004 系統管理工具無法刪除文件主機資料
  - 變更檔案: 4 個
- **2021-04-29 16:50:38**: [內部]Q00-20210429002 修正資料表DocServer_IDocument欄位內容顛倒問題
  - 變更檔案: 3 個
- **2021-09-30 16:04:06**: [流程引擎]Q00-20210930003 修正因有特製流程定義的資料，導致原本的流程定義刪除後無法再匯入相同流程
  - 變更檔案: 1 個
- **2021-09-30 11:30:11**: [流程引擎]A00-20210927003 修正使用系統計時功能後，當使用者被登出BPM時，需要進行兩次登入操作才能進入BPM
  - 變更檔案: 1 個
- **2021-09-28 11:27:56**: [Web]S00-20210730001 調整TraceProcessForSearchForm接口，追蹤其他流程後表單全域變數不會被改變[補]
  - 變更檔案: 2 個
- **2021-09-27 14:42:04**: [流程設計師]Q00-20210927003 表單某多語系顯示內容為空字串時，表單存取管控設定則顯示表單多語系預設值內容
  - 變更檔案: 1 個
- **2021-09-24 18:25:51**: [ESS]Q00-20210924006 在呼叫ESS存檔前增加判斷，防止同單據在ESS與BPM狀態不一致
  - 變更檔案: 3 個
- **2021-09-24 17:15:39**: [Web]S00-20210730001 調整TraceProcessForSearchForm接口，追蹤其他流程後表單全域變數不會被改變
  - 變更檔案: 8 個
- **2021-09-23 14:35:39**: [流程設計師]A00-20210922003 流程設計師多語系更新
  - 變更檔案: 4 個
- **2021-09-17 17:01:00**: [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題
  - 變更檔案: 6 個
- **2021-07-20 09:00:46**: [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能[補]
  - 變更檔案: 1 個
- **2021-07-08 17:23:53**: [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能
  - 變更檔案: 1 個
- **2021-09-03 18:14:46**: [流程引擎]Q00-20210903003 修正「發起流程時儲存表單後，表單的viewMode從『INVOKE』變成『PERFORM』」的問題
  - 變更檔案: 1 個
- **2021-08-27 18:46:59**: [Web]Q00-20210827001 調整DatabaseAccessor重組SQL指令的邏輯，防止在錯誤的地方插入where條件
  - 變更檔案: 1 個
- **2021-08-26 16:44:33**: [Web]Q00-20210826001 修正「若有為null的資料時，無法載入資料至Grid」的問題
  - 變更檔案: 1 個
- **2021-06-02 15:44:58**: [流程設計師]Q00-20210531001 修正「複製有連接線的關卡造成實際流程派送發生異常」的問題
  - 變更檔案: 2 個
- **2021-08-13 14:22:05**: [流程引擎]A00-20210810001 修正簡易流程圖無法查看於核決層級內加簽的關卡的關卡資訊
  - 變更檔案: 1 個
- **2021-08-12 14:35:39**: [流程引擎]Q00-*********** 在加簽過程中加入條件判斷，只有「進行中」的工作才可以進行加簽
  - 變更檔案: 4 個
- **2021-08-04 14:30:03**: [流程引擎]Q00-20210804004 客戶表單定義設計欄位過多，導致表單無法正常簽核
  - 變更檔案: 2 個
- **2021-08-03 17:12:24**: [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題[補]
  - 變更檔案: 1 個
- **2021-06-07 11:43:59**: [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題
  - 變更檔案: 1 個
- **2021-08-03 16:07:47**: [Web]A00-20210729001 SQLCommand資料庫為DBConnection元件，呼叫ajax query時找不到DB連線方式
  - 變更檔案: 1 個
- **2021-07-30 18:23:38**: [流程引擎]Q00-20210730002 修正關卡「只有一人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核[補]
  - 變更檔案: 1 個
- **2021-07-30 17:52:04**: [流程引擎]Q00-20210730002 修正關卡設定「只有一個人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核
  - 變更檔案: 1 個
- **2021-07-27 14:35:37**: [流程引擎]Q00-20210727001 修正因執行加簽關卡導致核決層級預先解析內容不正確
  - 變更檔案: 1 個
- **2021-07-23 17:20:36**: [Web]Q00-20210723001 修正當關卡表單權限設定為「唯讀」時，第一次點擊「儲存表單」後沒有再執行formOpen的問題
  - 變更檔案: 1 個
- **2021-07-21 17:25:33**: [流程引擎]Q00-20210721003 修正取回重辦後，簡易流程圖只顯示流程關卡，未顯示關卡處理者
  - 變更檔案: 1 個
- **2021-07-21 10:24:05**: [流程引擎]Q00-*********** 加快發起流程時表單開啟速度
  - 變更檔案: 7 個

### peng_cheng_wang (1 commits)

- **2021-08-26 15:20:11**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58

### 詩雅 (4 commits)

- **2021-08-02 14:19:40**: [BPM APP]C01-20210730002 修正移動簽核，下一關卡資訊有異常時，流程派送失敗但畫面卻顯示派送成功的問題
  - 變更檔案: 1 個
- **2021-07-27 18:16:47**: [BPM APP] 修正表單上傳/刪除附件後，更新表單時，會跑版問題
  - 變更檔案: 5 個
- **2021-07-26 16:53:15**: [BPM APP]C01-20210722010 調整郵件內容以及Line推播內容，密碼元件值以*號顯示
  - 變更檔案: 1 個
- **2021-07-12 16:01:49**: [ESS]C01-20210629009 修正ESS表單簽核時上傳附件，移動端未顯示附件資訊的問題
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
- **Commit ID**: `8fdf04e7dae2515f9c5397cc19fb7e918d19e623`
- **作者**: lorenchang
- **日期**: 2022-06-26 21:51:38
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [內部]新增check-valid-connection-sql及調整background-validation-millis為10秒
- **Commit ID**: `7b054c0d0729528c80b0aed6a6f15850ce393974`
- **作者**: lorenchang
- **日期**: 2021-10-19 11:15:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 3. [Web]Q00-20211015004 修正Grid欄位如果沒有Binding其他欄位，會導致點擊Grid修改鈕的時候，該筆欄位資料會不見
- **Commit ID**: `593fb85416c82fa20f5829999ece8c5bcec00810`
- **作者**: 林致帆
- **日期**: 2021-10-15 17:21:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 4. [流程引擎]Q00-20211014003 修正加簽有異常卻未將原始錯誤印出導致出錯無法排查
- **Commit ID**: `ff2668f047848af259b01c003c09c796f8edac1d`
- **作者**: walter_wu
- **日期**: 2021-10-14 15:54:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 5. [BPM APP]Q00-20211014001 修正入口平台整合設定進入編輯再點其他工具佈署，資訊顯示異常問題
- **Commit ID**: `10123a269ba13efcba78c1c598aafa29ac016188`
- **作者**: shihya_yu
- **日期**: 2021-10-14 12:03:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 6. [內部]首頁模組的公告申請單引入js移至NaNaWeb中維護
- **Commit ID**: `bb3b7ab5e23ecebf9ffd810ef1d1d5e5ff08dfc1`
- **作者**: pinchi_lin
- **日期**: 2021-10-13 19:22:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomJsLib/wangEditor.min.js`

### 7. [BPM APP]Q00-20210913001 調整行動端上傳附件發生錯誤時前端回應資訊錯誤的問題[補]
- **Commit ID**: `f4d0ff95494ab13061bcb5ea09add42163068bb2`
- **作者**: cherryliao
- **日期**: 2021-10-13 18:22:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 8. [BPM APP]Q00-20210831001 修正當流程設定表單欄位為唯讀時，部分元件設定setValue異常問題[補]
- **Commit ID**: `2b5cf0cea65ec5eb05cd41fac23054eebbaaff11`
- **作者**: yamiyeh10
- **日期**: 2021-10-13 17:22:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 9. [Web]Q00-20211013003 修正當ListBox從設定選項中只有一個列時並刪除列，再改成從資料庫帶值，儲存表單會出現錯誤
- **Commit ID**: `8ddcc161cc322148b63e1c5489f176c22855d1df`
- **作者**: 王鵬程
- **日期**: 2021-10-13 17:01:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 10. [Web]A00-20211012002 修正使用者名稱有設定多語系，在帳號管理的頁面會有多筆該使用者的重複資料
- **Commit ID**: `36b92e51b117262faa7d7a42c1f8dc326a5d3eed`
- **作者**: 林致帆
- **日期**: 2021-10-13 15:14:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserManageListReader.java`

### 11. [BPM APP]Q00-20211005005 修正行動端詳情表單在操作Grid新增、編輯或取消按鈕時需要點擊兩次才會觸發動作的問題[補]
- **Commit ID**: `2a37528d0c328d7456394c24b4da0695ceb0c0d3`
- **作者**: cherryliao
- **日期**: 2021-10-13 14:50:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 12. [BPM APP]Q00-20211012002 調整行動端詳情頁面的日期與時間元件在iOS 15下會跑版問題
- **Commit ID**: `5841f60567285fc63a4e99bef68c41aee4ed54f4`
- **作者**: yamiyeh10
- **日期**: 2021-10-13 10:41:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 13. [BPM APP]Q00-20210804003 修正行動端表單當流程設定多人且只需一位處理時不會顯示彈出視窗詢問是否要接收並派送訊息問題[補]
- **Commit ID**: `573a273a6ccfff8ebb16cd0f5c9794ede8601231`
- **作者**: yamiyeh10
- **日期**: 2021-10-13 10:39:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 14. [內部]******* Oracle的Update SQL每句都補上/
- **Commit ID**: `28b31978f791e485671c806bf37cabd689e6fa1b`
- **作者**: waynechang
- **日期**: 2021-10-12 17:58:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 15. [BPM APP] 新增簽核流設計師中活動表單元件權限，行動端可複製Web端設定功能[補]
- **Commit ID**: `4523e60022a9efb40f35efe105faebc0c841425b`
- **作者**: shihya_yu
- **日期**: 2021-10-12 17:10:18
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_zh_TW.properties`

### 16. [內部]Q00-20211005001 修正簽核流設計師中，行動端複製PC端設定有不同步的狀況
- **Commit ID**: `d47ef04bb2e0339eaf7de9981f2881d3c692ad24`
- **作者**: shihya_yu
- **日期**: 2021-10-12 17:05:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`

### 17. [BPM APP]Q00-20210913003 調整上傳附件時loading圖示顯示的時機
- **Commit ID**: `e2ae598fd25ab238c20949929c03cbe72fc80006`
- **作者**: cherryliao
- **日期**: 2021-10-12 16:24:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 18. [BPM APP]Q00-20211012001 調整入口平台整合設定中連線管理頁面在整合鼎捷移動時增加鼎捷移動平台管理後台填外網提示
- **Commit ID**: `dd411b43a99b592e00c4cb25b9f0ed8f9d478979`
- **作者**: yamiyeh10
- **日期**: 2021-10-12 14:39:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 19. [BPM APP]優化移動表單的顯示流程樣式[補]
- **Commit ID**: `1e1084930ab9fd89d3b90904918affdaf3c30d92`
- **作者**: yamiyeh10
- **日期**: 2021-10-12 14:12:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 20. [內部]調整******* MSSQL的Update SQL 部分alter table相關內容增加exist判斷
- **Commit ID**: `9432f11ef0261ce89b7132048971d2df615fbdaa`
- **作者**: 林致帆
- **日期**: 2021-10-12 11:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`

### 21. [BPM APP]Q00-20211006003 修正ESS流程在手機端簽核時無法查看附件問題
- **Commit ID**: `0ce31f716f00fbdcc612b0bad8388081e9c6ef94`
- **作者**: pinchi_lin
- **日期**: 2021-10-12 11:46:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 22. [BPM APP]Q00-20211005005 修正行動端詳情表單在操作Grid新增、編輯或取消按鈕時需要點擊兩次才會觸發動作的問題
- **Commit ID**: `98d0befe98205c85f69fe63ad08749f23f1893e4`
- **作者**: cherryliao
- **日期**: 2021-10-08 18:37:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 23. [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題[補]
- **Commit ID**: `56383b8c8bc4f43959b8cb978fa16c9e88e6a482`
- **作者**: yanann_chen
- **日期**: 2021-10-08 18:33:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 24. [BPM APP]Q00-20211005004 修正左上方IMG的返回按鈕，會偶發沒顯示問題
- **Commit ID**: `48c3fcfe3ae847222be29fa88547832c0e7ddb1b`
- **作者**: shihya_yu
- **日期**: 2021-10-08 18:15:39
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 25. [Web]S00-20210506008 將列印表單時的"列印流程的表單資料"(頁首文字)改為表單名稱
- **Commit ID**: `e8fedb490e461ee74f27d640e25fbdf448ae4c53`
- **作者**: walter_wu
- **日期**: 2021-10-08 15:56:39
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 26. [BPM APP]Q00-20210913001 調整行動端上傳附件發生錯誤時前端回應資訊錯誤的問題
- **Commit ID**: `a6850a0c618d7f388d23ebd3caf2d84919d15401`
- **作者**: cherryliao
- **日期**: 2021-10-08 14:50:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 27. [BPM APP]Q00-20210716001 調整行動版表單自定義開窗，單選時checkPointOnClose事件會觸發兩次問題
- **Commit ID**: `50c6472b9f7eecf83324d5c320b14610acaa1d68`
- **作者**: shihya_yu
- **日期**: 2021-10-08 14:12:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 28. [流程引擎]A00-20211006001 修正「部分流程無法產出作業流程書」的問題
- **Commit ID**: `6850d64cae1ba78ab069298a88720dd5091fea30`
- **作者**: yanann_chen
- **日期**: 2021-10-07 12:00:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java`

### 29. [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題[補]
- **Commit ID**: `ad4c0b1c73977a9de0ed024f7d3299d65531b86a`
- **作者**: yamiyeh10
- **日期**: 2021-10-07 09:48:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 30. [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題[補]
- **Commit ID**: `9734456c1ae6d534811ee2782211385387b55305`
- **作者**: yamiyeh10
- **日期**: 2021-10-07 08:58:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 31. [BPM APP]Q00-20210831002 修正當Grid區塊沒放置元件，表單畫面會產生多餘的空白區塊問題
- **Commit ID**: `2e0f15711c06ed00b939b22b9d45ed620d777ac4`
- **作者**: shihya_yu
- **日期**: 2021-10-06 19:36:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 32. [BPM APP]Q00-20211006004 修正使用IMG發起ESS流程夾帶附件後在手機簽核時會看不到附件問題
- **Commit ID**: `9f44c74923b009e9e20c432e24279426037d6ed9`
- **作者**: pinchi_lin
- **日期**: 2021-10-06 18:13:05
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFileManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 33. [Web]Q00-20211006009 追蹤流程及待辦流程的進階查詢的模糊查詢框增加hint告知只能用流程相關資料查詢
- **Commit ID**: `dd28979ebbefcc9d30d15c2fd237c66da5f8f541`
- **作者**: 林致帆
- **日期**: 2021-10-06 17:56:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 34. [TIPTOP]Q00-20211006008 調整預設出貨的TIPTOP及CRM的流程主機IP為與標準產品預設出貨的wsf1一致
- **Commit ID**: `7171fe9147b981e319c435d0c7649cca00813ff4`
- **作者**: 林致帆
- **日期**: 2021-10-06 17:35:06
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_Oracle9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_SQLServer2005.sql`

### 35. [Web]Q00-20211006007 修正新增的使用者進入追蹤流程畫面，因為沒有設定"追蹤流程清單預設查看的流程狀態"導致畫面異常
- **Commit ID**: `f7e258bedce39f667b70c2d8b7991f6224026f19`
- **作者**: 林致帆
- **日期**: 2021-10-06 17:23:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 36. [SAP]Q00-20211006006 優化SAP整合服務，回傳Grid資料時，同時支持RWD表單Grid及絕對位置表單Grid
- **Commit ID**: `f2fdb3b10161dbe9fec908876617c04f9b229066`
- **作者**: waynechang
- **日期**: 2021-10-06 17:16:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 37. [Web]Q00-20211006005修正離職交接人作業無法修改及刪除資料
- **Commit ID**: `339c15ff1ce0c1722edefa1d566491a0360afb25`
- **作者**: 林致帆
- **日期**: 2021-10-06 16:39:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Resignation.java`

### 38. [BPM APP]Q00-20211005002 將行動端詳情頁面的發送通知中選擇人員畫面上浮動按鈕調整新樣式
- **Commit ID**: `f33411686eff54e346b5ce9bd4e4f79607bc3d1d`
- **作者**: yamiyeh10
- **日期**: 2021-10-06 15:55:33
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 39. [Web]A00-20211001002 修正絕對位置表單當附件的描述過長時，會導致預覽列印時出現附件資訊和簽核歷程重疊
- **Commit ID**: `c74fbc0fec2790051ac19f2043acbde24483d4b1`
- **作者**: 王鵬程
- **日期**: 2021-10-06 15:46:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 40. [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能[補]
- **Commit ID**: `d7b411bbb46618071b7df12e0f291a2edc361ae4`
- **作者**: cherryliao
- **日期**: 2021-10-06 15:03:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 41. [BPM APP]Q00-20210804003 修正行動端表單當流程設定多人且只需一位處理時不會顯示彈出視窗詢問是否要接收並派送訊息問題
- **Commit ID**: `14468cbbbcc5eb6e247a8a11317b259e3030862c`
- **作者**: yamiyeh10
- **日期**: 2021-10-06 14:48:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js`

### 42. [BPM APP]Q00-20210915002 修正行動端E10表單當Grid有多筆單身資料且其中有子單身無資料時會出現錯誤訊息問題
- **Commit ID**: `01c62d4336663e7d7baa8a364fcdb0ce7cdade76`
- **作者**: yamiyeh10
- **日期**: 2021-10-06 12:07:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 43. [BPM APP]Q00-20210806001 調整行動端腳本樣版setLabelColor設定元件標籤字體顏色說明
- **Commit ID**: `bd3c0cb86e76e47b60d4d7ffa33dfc08d6342ad0`
- **作者**: shihya_yu
- **日期**: 2021-10-06 11:37:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 44. [BPM APP]Q00-20210824001 調整Line推播資訊，當未填寫主旨時設定預設資訊，修正會推播失敗問題
- **Commit ID**: `0c9b3834879a1b73c5328684f2c1a9fc4982194d`
- **作者**: shihya_yu
- **日期**: 2021-10-06 11:11:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`

### 45. [內部]補上 5871缺少的InitNaNaDB[補]
- **Commit ID**: `2f04355ab43c0a0932254fd97e9ca780f239d25c`
- **作者**: waynechang
- **日期**: 2021-10-05 18:07:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 46. [內部]新增首頁模組(MPT)datasource設定
- **Commit ID**: `9a2dc360cf43ccdf2de080237f3bf2be649a004d`
- **作者**: lorenchang
- **日期**: 2021-10-05 17:41:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 47. [Web]Q00-20210826001 修正「若有為null的資料時，無法載入資料至Grid」的問題[補]
- **Commit ID**: `cdd1850e4629ea619f78914bba130d9fe01a2e74`
- **作者**: yanann_chen
- **日期**: 2021-10-05 17:21:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 48. [BPM APP]Q00-20210823001 修正行動端表單當退回重辦必填簽核意見時彈窗沒有遮罩問題
- **Commit ID**: `2ac42820859b8cd93ae53424080c7b8c5dd97650`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 16:23:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 49. [內部]補上「在線閱讀模組」浮水印多語系內容
- **Commit ID**: `9d09299700f9ab5db285c81ad07fb85abb1f0caf`
- **作者**: waynechang
- **日期**: 2021-10-05 15:09:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 50. [BPM APP]Q00-20210910002 將行動端詳情頁面的加簽選擇人員畫面上浮動按鈕調整新樣式[補]
- **Commit ID**: `0d43355081abddd4b79f25171aa55d2f9956e2ba`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 14:23:58
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 51. [內部]更改auto-deploy-exploded設定為false
- **Commit ID**: `fa28e1bb0f716029e7b9d966e3ef3c6fd440809a`
- **作者**: lorenchang
- **日期**: 2021-10-05 14:10:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 52. [BPM APP]Q00-20210910001 將行動端詳情頁面的轉由他人處理中選擇人員畫面上浮動按鈕調整新樣式
- **Commit ID**: `9ccfce86e58096da93b26c7c1010b92581ac0a8a`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 13:45:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 53. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `316d596b43bd1377d899b9bcad1c1630509ca5a6`
- **作者**: shihya_yu
- **日期**: 2021-10-05 11:57:21
- **變更檔案數量**: 0

### 54. [BPM APP]Q00-20210831001 修正當流程設定表單欄位為唯讀時，部分元件設定setValue異常問題
- **Commit ID**: `7be3358175283f859630c9e24a22244e55aa78bf`
- **作者**: shihya_yu
- **日期**: 2021-10-05 11:57:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 55. [BPM APP]Q00-20210910002 將行動端詳情頁面的加簽選擇人員畫面上浮動按鈕調整新樣式
- **Commit ID**: `82a6736820f4c7218648ffee1c8cc6d2018f3c4f`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 11:56:33
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 56. [BPM APP]Q00-20210915001 修正Grid綁定設定小數點後幾位的TextBox元件時，會跑版問題
- **Commit ID**: `79b7671bf60a615ad09710a964c416e1d652f782`
- **作者**: shihya_yu
- **日期**: 2021-10-05 11:55:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 57. [BPM APP]Q00-20210913004 修正在企業微信的追蹤已簽核附件頁面中返回按鈕沒有反應問題
- **Commit ID**: `31d3f6a6044ac4ebe5c78cd4da48341c2bf1a168`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 11:25:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 58. [Web]A00-20211004001 修正多選自定義開窗在無資料的情況下，勾選表頭的「全選」選項時會帶出一筆空白資料
- **Commit ID**: `24d5aa58de87ddbf1536b9180298d08678b7e4b7`
- **作者**: yanann_chen
- **日期**: 2021-10-05 11:14:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 59. [表單設計師]S00-20210527001 新增Web表單設計師批量匯出功能
- **Commit ID**: `50df20aae92f4d5592abb1560c6616bad83dcefa`
- **作者**: cherryliao
- **日期**: 2021-10-05 11:05:47
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 60. [內部]更新******* patch檔
- **Commit ID**: `a100bfe490870d686b56337b7e51b84654cca153`
- **作者**: waynechang
- **日期**: 2021-10-05 10:56:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 61. [內部]補上 5871缺少的InitNaNaDB
- **Commit ID**: `4a15c6db8044b4cc1467adc2af67e1b5d4ff4662`
- **作者**: waynechang
- **日期**: 2021-10-05 10:48:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 62. [內部]加入Nginx https反向代理BPM http之設定
- **Commit ID**: `e5ad13f8d366ec969e84b984e8dc1b134f795d19`
- **作者**: lorenchang
- **日期**: 2021-10-04 09:16:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 63. [內部]合併*******多語系及移除重覆內容
- **Commit ID**: `209212200059e1e0223bc00ccc5ce835bad3ca5c`
- **作者**: lorenchang
- **日期**: 2021-10-01 14:36:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5871.xlsx`

### 64. [內部]******* Oracle的Update SQL每句都補上/
- **Commit ID**: `4cbde2cc09367a8a404a80807661d9b130f838e1`
- **作者**: lorenchang
- **日期**: 2021-10-01 11:18:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 65. [Web]S00-*********** SQL註冊器新增查詢功能及維護頁面中SQL語法欄位新增剩餘字數提示
- **Commit ID**: `30fefe087cfb73d69357af950fc0576afce84fa9`
- **作者**: 王鵬程
- **日期**: 2021-09-29 18:37:17
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IFormDefinitionDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5871.xlsx`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 66. [內部]Q00-20210927001 E10單據回傳給E10，回傳簽核權限人員如果採用設定檔，會導致排程回寫簽核歷程時報錯
- **Commit ID**: `9a7b4566e4aa6c4bd0fef5d251b13f2d4eb8615f`
- **作者**: 林致帆
- **日期**: 2021-09-27 09:05:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 67. [E10]S00-20210702001 E10回寫，採用設定檔選擇回寫E10流程指定單據審核的權限人員[補修正]
- **Commit ID**: `40c7ee82618ce42af3446820b7a43996d049ddf1`
- **作者**: 林致帆
- **日期**: 2021-09-24 17:09:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 68. [E10]S00-20210702001 E10回寫，採用設定檔選擇回寫E10流程指定單據審核的權限人員[補修正]
- **Commit ID**: `de693beb063ab3363a6fe62219be4826901f7043`
- **作者**: 林致帆
- **日期**: 2021-09-23 18:33:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 69. [E10]S00-20210702001 E10回寫，採用設定檔選擇回寫E10流程指定單據審核的權限人員
- **Commit ID**: `4791ee42d4730a7759366ba738f2a9f422dd6940`
- **作者**: 林致帆
- **日期**: 2021-09-23 17:36:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`

### 70. [Web]S00-***********新增離職交接人作業
- **Commit ID**: `35f8550264c760dcdb634fcb74fcf09c8c8c53ab`
- **作者**: 林致帆
- **日期**: 2021-09-10 19:15:08
- **變更檔案數量**: 32
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ResignationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/IProcessVariableNames.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Resignation.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/audit_data/ReassignmentType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/notification/NotificationContentType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/DAOFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IResignationDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/JDBCDAOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBDAOFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBResignationDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignation/ResignationManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignation/ResignationManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignation/ResignationManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ResignationAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ReassignmentInfoForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTypeManager.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5871.xlsx`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 71. [BPM APP]調整行動端詳情表單的簡易簽核歷程可依系統變數設定是否要顯示全部資料功能[補]
- **Commit ID**: `e0d6f5e008f642cf76964a2eb118b3293307205e`
- **作者**: yamiyeh10
- **日期**: 2021-09-01 16:38:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 72. [流程引擎]S00-20210305003 調整流程設計師關卡進階設定的不寄送通知信功能
- **Commit ID**: `636437262883e13c147ba414d04ccbc539a62780`
- **作者**: cherryliao
- **日期**: 2021-08-23 14:19:08
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 73. [流程設計師]S00-20210521003 新增流程設計師設定:是否可在監控流程畫面刪除流程[補修正]
- **Commit ID**: `fb61dc69762a023949aea59442bc76976bac14d3`
- **作者**: 林致帆
- **日期**: 2021-08-16 15:06:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 74. [報表設計器]Q00-20210805001 調整報表設計器清單資料欄位長度限制
- **Commit ID**: `8854d80594c2ff1b84d92ff95ac16b75ee706c12`
- **作者**: yanann_chen
- **日期**: 2021-08-05 16:01:14
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 75. [系統管理工具]Q00-20210727004 系統管理工具無法刪除文件主機資料
- **Commit ID**: `d6e401e5ac6fe34db69522600ae8a47b9e67f808`
- **作者**: yanann_chen
- **日期**: 2021-07-27 18:11:49
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 76. Revert "Q00-20210727004 系統管理工具無法刪除文件主機資料"
- **Commit ID**: `9185608751f853d0a7aa59e218dc00935fcdf15c`
- **作者**: yanann_chen
- **日期**: 2021-07-27 18:08:50
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 77. Q00-20210727004 系統管理工具無法刪除文件主機資料
- **Commit ID**: `1ec0243c4037f46029be3a79fba1c258286985a9`
- **作者**: yanann_chen
- **日期**: 2021-07-27 18:04:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 78. [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
- **Commit ID**: `6ba44368de17dceec2d2daeec010c8250ac72cbb`
- **作者**: pinchi_lin
- **日期**: 2021-07-27 14:56:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 79. S00-20210311003 追蹤流程清單的流程狀態採用個人資訊"追蹤流程清單預設查看的流程狀態"[補修正]
- **Commit ID**: `ed3ffed0ca0da9fe69bb80ddc3c1ef72a726fb51`
- **作者**: 林致帆
- **日期**: 2021-07-26 08:59:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5871.xlsx`

### 80. [Web]S00-20201230001 調整resource.version改取系統啟動時當前系統時間避免版更後js、css緩存問題
- **Commit ID**: `d1d144373b9648a9fbdb29f86fc9270a9ece6223`
- **作者**: cherryliao
- **日期**: 2021-07-23 14:53:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 81. S00-20210311003 追蹤流程清單的流程狀態採用個人資訊"追蹤流程清單預設查看的流程狀態"
- **Commit ID**: `c0a26dc2de227c4509123bd980c865ad89502269`
- **作者**: 林致帆
- **日期**: 2021-07-23 13:04:03
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserProfileForManagerDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/user_profile/UserProfileForManaging.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageUserProfile-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 82. [流程設計師]S00-20210521003 新增流程設計師設定:是否可在監控流程畫面刪除流程[補修正]
- **Commit ID**: `58c367549ad17c234d0277842ae6c7c457b8e5e7`
- **作者**: 林致帆
- **日期**: 2021-07-19 10:52:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 83. [流程設計師]S00-20210521003 新增流程設計師設定:是否可在監控流程畫面刪除流程
- **Commit ID**: `c6a1bc8faaa131c272788c12334c56ca89c0298e`
- **作者**: 林致帆
- **日期**: 2021-07-19 10:47:57
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 84. [ESS]Q00-20210713003 補IndexNaNaDB內AppFormActivityRecord缺少的Index
- **Commit ID**: `5e6bb16b674efb0e8f273e49a52510440344948a`
- **作者**: lorenchang
- **日期**: 2021-07-13 15:45:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql`

### 85. [Web]S00-20210429008 上傳附件名稱長度調整為可輸入250的字[補修正]
- **Commit ID**: `791ebff5b46692ecbbddfd27e8d75de411615158`
- **作者**: 林致帆
- **日期**: 2021-07-13 15:22:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 86. [Web]S00-20210429008 上傳附件名稱長度調整為可輸入250的字
- **Commit ID**: `6df75b3680203d5e8f3ca99755ea5164c1be1d07`
- **作者**: 林致帆
- **日期**: 2021-07-12 10:48:55
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/PDFUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 87. [BPM APP]S00-20210517001 新增IMG呼叫行事曆刪除接口功能[補]
- **Commit ID**: `cfb12029168bcc1ac79fa743c00112c2c830f370`
- **作者**: pinchi_lin
- **日期**: 2021-06-22 18:53:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 88. [表單設計師]S00-*********** 表單設計師新增腳本資訊和還原上一版FromScript功能[補]
- **Commit ID**: `01b9eb9950049cbe8c0902666a7ca86af72f258e`
- **作者**: cherryliao
- **日期**: 2021-06-09 14:56:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5871.xlsx`

### 89. [表單設計師]S00-*********** 表單設計師新增腳本資訊和還原上一版FromScript功能
- **Commit ID**: `80d0d7029dd09bba7ed62acf18cdb7aed9da5d78`
- **作者**: cherryliao
- **日期**: 2021-06-08 18:06:10
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IFormDefinitionDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5871.xlsx`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 90. [ESS]A00-20210521002 修正ESS刪除EFGP缺席紀錄的session bean無作用
- **Commit ID**: `788225b3ea56ea972c83ae9e0a987a8646bcf1b5`
- **作者**: walter_wu
- **日期**: 2021-06-04 16:42:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 91. [Web]Q00-20210521001 修正關注類別維護的英文是錯誤的內容
- **Commit ID**: `da3cd320960b472130cf700cf26459b48a44278d`
- **作者**: 王鵬程
- **日期**: 2021-05-21 15:25:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 92. [內部]Q00-20210429002 修正資料表DocServer_IDocument欄位內容顛倒問題
- **Commit ID**: `ce48215bc866163de09a51444d265627cf4c1629`
- **作者**: yanann_chen
- **日期**: 2021-04-29 16:50:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/document/AbstractDocument.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 93. [內部]加入*******專用多語系檔
- **Commit ID**: `07cffabef530d4fd0ea60289fac85029f5af37bd`
- **作者**: lorenchang
- **日期**: 2021-04-16 16:49:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle_5871.xlsx`

### 94. [Web]Q00-20210930005 修正使用SQLcommand時指令帶有中文會導致造成base64加密報錯
- **Commit ID**: `3f5dfd225dc853ba4c6920e8900b87752a5c66a4`
- **作者**: 王鵬程
- **日期**: 2021-09-30 17:55:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 95. [流程引擎]A00-20210913002 增加判斷是否因為代理人導致觸發自動簽核並處理相關邏輯
- **Commit ID**: `088b195a7ca0622e47eb62a064b54cb54d00ab7f`
- **作者**: walter_wu
- **日期**: 2021-09-30 17:47:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 96. [流程引擎]Q00-20210930003 修正因有特製流程定義的資料，導致原本的流程定義刪除後無法再匯入相同流程
- **Commit ID**: `95295bde262b5f236a4fb0801b8f7b00e9cddac6`
- **作者**: yanann_chen
- **日期**: 2021-09-30 16:04:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 97. [流程引擎]A00-20210927003 修正使用系統計時功能後，當使用者被登出BPM時，需要進行兩次登入操作才能進入BPM
- **Commit ID**: `8d9cae69e4a5d9100e2e06f155a633e2cc8d487d`
- **作者**: yanann_chen
- **日期**: 2021-09-30 11:30:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 98. [Web]Q00-20210928002 優化追蹤流程通知信URL進入速度
- **Commit ID**: `06f2171f8c7fc3eed78b85b56425ab7bdfb8c754`
- **作者**: walter_wu
- **日期**: 2021-09-29 18:06:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 99. Revert "已修正。git請搜尋"[Web]Q00-20210928002 優化追蹤流程通知信URL進入速度"
- **Commit ID**: `695eef737817ab967943cc31a29be7311548696a`
- **作者**: walter_wu
- **日期**: 2021-09-29 18:04:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 100. 已修正。git請搜尋"[Web]Q00-20210928002 優化追蹤流程通知信URL進入速度
- **Commit ID**: `8bdb6cebe44c6360b7d18ac93ea3871517003642`
- **作者**: walter_wu
- **日期**: 2021-09-29 17:58:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 101. [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題
- **Commit ID**: `63c522980ba76edef82c1a30dcc798aea4c0dc42`
- **作者**: pinchi_lin
- **日期**: 2021-09-29 11:12:46
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-undoManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`

### 102. [BPM APP]Q00-20210929001 修正LINE使用預設port時連結會與login設定不同導致無法登入問題
- **Commit ID**: `823d45efb7ef7bace40203b5d6e570825470d030`
- **作者**: pinchi_lin
- **日期**: 2021-09-29 10:49:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 103. [T100]S00-*********** 新增T100流程終止或撤銷時，若單據修改且重新送審後於BPM表單可查看之前審批流程的功能
- **Commit ID**: `326fcdea1a5b6facfbed6fee43184d79c95a90d9`
- **作者**: cherryliao
- **日期**: 2021-09-28 17:22:21
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysNewTiptopToolDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopToolBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TiptopAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 104. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `b687da0f4a11e032565bb8580f314336b9a8d271`
- **作者**: yamiyeh10
- **日期**: 2021-09-28 14:28:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 105. [BPM APP]優化移動表單的顯示流程樣式[補]
- **Commit ID**: `66c731294c27c8385ea18086379f3dce016f32d9`
- **作者**: yamiyeh10
- **日期**: 2021-09-28 14:26:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 106. [Web]S00-20210730001 調整TraceProcessForSearchForm接口，追蹤其他流程後表單全域變數不會被改變[補]
- **Commit ID**: `1e17cb8f13e714ad0edf73a0ece4c7f1d32768a8`
- **作者**: yanann_chen
- **日期**: 2021-09-28 11:27:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

### 107. [流程設計師]Q00-20210927003 表單某多語系顯示內容為空字串時，表單存取管控設定則顯示表單多語系預設值內容
- **Commit ID**: `a0887e637d8cad610e039d69ce6e73a5f922b7b6`
- **作者**: yanann_chen
- **日期**: 2021-09-27 14:42:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`

### 108. [ESS]Q00-20210924006 在呼叫ESS存檔前增加判斷，防止同單據在ESS與BPM狀態不一致
- **Commit ID**: `a14ffaa874c6fbbb2a74dd161b6004d3d6813021`
- **作者**: yanann_chen
- **日期**: 2021-09-24 18:25:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/dao/OJBAppFormActivityRecordDAO.java`

### 109. [Web]A00-20210906002 修正如果核決層級參考的關卡被代理過預解析會有異常
- **Commit ID**: `12d4fd73e98555f8ef4f8966c029e211f14654d8`
- **作者**: walter_wu
- **日期**: 2021-09-24 17:22:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 110. [Web]S00-20210730001 調整TraceProcessForSearchForm接口，追蹤其他流程後表單全域變數不會被改變
- **Commit ID**: `e5d2ffbfd914445ac71b90a7fd5ff15b6b11a236`
- **作者**: yanann_chen
- **日期**: 2021-09-24 17:15:39
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`

### 111. [Web]S00-20210924001 待辦清單接口新增回傳欄位；發起人代號，發起人部門代號，發起人部門名稱
- **Commit ID**: `ae73de179584170b40b3796498e43a502aad4ac6`
- **作者**: 林致帆
- **日期**: 2021-09-24 16:33:42
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 112. [流程設計師]A00-20210922003 流程設計師多語系更新
- **Commit ID**: `faf3c9ff079722cae1eeddd2b5005ca6ceac8db2`
- **作者**: yanann_chen
- **日期**: 2021-09-23 14:35:39
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_zh_TW.properties`

### 113. [Web]Q00-20210819001 調整離職人員帳號更新排程為 預設出貨用[補修正]
- **Commit ID**: `3fd7b4b05d6ac0cf5b88299af67729b3128e571a`
- **作者**: 林致帆
- **日期**: 2021-09-22 14:34:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaJobs.xml`

### 114. [BPM APP]C01-20210914004 修正行動表單上產品多選開窗(DialogInputMult元件)的資訊沒有回寫到開窗選項上的問題
- **Commit ID**: `33c7d71e34c9974e9eb8a419c912ffdd8d4813a1`
- **作者**: shihya_yu
- **日期**: 2021-09-22 14:29:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 115. S00-20210503006 調整出貨光碟T100的axmt510作業 不需押上截止有效日
- **Commit ID**: `7a1e830ab23aa46a5a91124508dfd033c932d68a`
- **作者**: 林致帆
- **日期**: 2021-09-22 13:38:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form-default-t100.zip`

### 116. [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題
- **Commit ID**: `dda295f5e1fa1857b1bd0cd98a8fe68ea2722045`
- **作者**: yanann_chen
- **日期**: 2021-09-17 17:01:00
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 117. [易飛]Q00-*********** 調整易飛範例流程命名及服務任務設定
- **Commit ID**: `3e431b4bcfd68d9bff72f02b2f5c923406186575`
- **作者**: 林致帆
- **日期**: 2021-09-17 16:41:56
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@yife/conf/Process_Mapping.prsmapping`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@yife/process-default/bpmn/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI09).bpmn"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@yife/process-default/bpmn/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(YIFE_PURI09).bpmn"`

### 118. [Web]Q00-20210819001 調整離職人員帳號更新排程為 預設出貨用
- **Commit ID**: `e98f9c2e973177b7debadda7b49185e29ba956d8`
- **作者**: 林致帆
- **日期**: 2021-09-17 13:51:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaJobs.xml`

### 119. [BPM APP] 新增簽核流設計師中活動表單元件權限，行動端可複製Web端設定功能
- **Commit ID**: `57fba776db88602b28fcd28e8f688537a6e831e6`
- **作者**: shihya_yu
- **日期**: 2021-09-17 13:38:01
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileTableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormMobileValidateTableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`

### 120. Q00-20210917001 增加防呆如果自己設定為自己的主管，在用核決層級參考預解析時會導致無窮迴圈
- **Commit ID**: `5dffcd35cffc4f9d954389f4de1bcff403e9a032`
- **作者**: walter_wu
- **日期**: 2021-09-17 11:37:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 121. [Web]S00-20210318002 優化監控流程匯出Excel功能
- **Commit ID**: `0232bf6744758b4c357fcc2abc22f4e2e99ba7f6`
- **作者**: 林致帆
- **日期**: 2021-09-16 14:39:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 122. [Web]Q00-20210916002 調整退回重辦視窗中confirm按確認後，不再提示成功訊息，避免使用者按下右上角關閉導致表單未刷新頁面
- **Commit ID**: `114f912f9343786aefb20357793fee1f07b9446f`
- **作者**: 王鵬程
- **日期**: 2021-09-16 14:16:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 123. [流程引擎]S00-20210730004 調整Textbox設定浮點數、顯示千分位和小數點幾位時binding到Grid沒有千分位的問題
- **Commit ID**: `1e0fd727cd16190a525a4c6875292d7a31142a3d`
- **作者**: cherryliao
- **日期**: 2021-09-16 13:37:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 124. [Web]Q00-20210916001調整簽核歷程及簡易流程圖流程狀態"已同意"調整回"已處理" 及 簡易流程圖 增加"已會辦"流程狀態
- **Commit ID**: `0b15cfab6f966c0d5aa1cae124a1cee73885ea15`
- **作者**: 林致帆
- **日期**: 2021-09-16 11:52:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 125. [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能[補]
- **Commit ID**: `349a880a88fe19e1f6923e33a4e6946251204532`
- **作者**: yanann_chen
- **日期**: 2021-07-20 09:00:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 126. [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能
- **Commit ID**: `2dc7af98124327cf7942412a682ab8069271fcad`
- **作者**: yanann_chen
- **日期**: 2021-07-08 17:23:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 127. [BPM APP]C01-20210913003 修正當行動版表單有多個SubTab元件，使用formScript無法正常隱藏對應分頁問題
- **Commit ID**: `c2ce43c9be990c0d7d372e468e3a6232949ae6a8`
- **作者**: shihya_yu
- **日期**: 2021-09-15 16:45:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileSubTab.js`

### 128. [Web]Q00-20210914003 調整修改密碼的彈出視窗較小，導致按確認後alert的按鈕不能直接點選
- **Commit ID**: `58dda5c4e34551c2470f6358ad98779b04986a6f`
- **作者**: 王鵬程
- **日期**: 2021-09-14 18:16:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 129. [Web]A00-20210913001 修正從BPM首頁的待辦清單由第二筆簽核跳到下一筆，都會跳到流程清單的第一筆流程
- **Commit ID**: `ca8566372b7a207d8ee3f4e9a5f00bdec0ec130a`
- **作者**: 林致帆
- **日期**: 2021-09-14 17:36:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 130. [Web]A00-20210906001 修正絕對位置表單日期元件勾選顯示時間並比對另一個日期，條件設==，則欄位上時和分會歸零
- **Commit ID**: `6946b76de9c097639375caf7f931b705fbb88e7b`
- **作者**: 王鵬程
- **日期**: 2021-09-14 14:16:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DateTemplate.txt`

### 131. [Web]A00-20210909001 修正在PC版表單設計師中更改Grid代號，在切換到行動版表單，畫面上顯示的Grid代號未改變
- **Commit ID**: `77becf567b074af4b8b0dd515c96008ebe346415`
- **作者**: 王鵬程
- **日期**: 2021-09-13 18:22:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 132. [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能
- **Commit ID**: `991a2173c1f448120f48589d68747ad006b78a7b`
- **作者**: cherryliao
- **日期**: 2021-09-13 18:14:23
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 133. [Web]S00-20210902001 優化Web端表單E10子單身呈現樣式
- **Commit ID**: `1c453c1f1a49ac260e73b620aaf22ff2576b48bd`
- **作者**: cherryliao
- **日期**: 2021-09-13 17:18:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/SubGridTransfer.js`

### 134. [Web]Q00-20210910003 修正如果再Orderby使用表別名Oracle會報錯
- **Commit ID**: `73fc1d179249f8cc5e9cb1ec155f01742ce59be6`
- **作者**: walter_wu
- **日期**: 2021-09-10 17:14:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 135. [流程引擎]Q00-20210727002 修正因為關卡設定自動跳關導致代理機制異常[補修正]
- **Commit ID**: `8a123c88235cade1f755da233dc46d83f2077021`
- **作者**: walter_wu
- **日期**: 2021-09-10 11:54:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 136. [Web]A00-20210907001 修正先看ESS流程草稿後在點擊一般流程草稿會導致報錯
- **Commit ID**: `d65cbf046a3b110998468a98c6869bfa65c74253`
- **作者**: 林致帆
- **日期**: 2021-09-09 18:03:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`

### 137. [Web]Q00-20210909001 修正加簽後，在流程圖預覽無法看到加簽關卡
- **Commit ID**: `4e700b24ce97be60675ae8256bb53f7c1cb309ba`
- **作者**: walter_wu
- **日期**: 2021-09-09 09:24:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`

### 138. [Web]A00-20210908001 修正從待辦事項連結登入BPM後應該要為該流程的簽核頁面，而不是代辦清單頁面
- **Commit ID**: `d5ffe403239a49d755fe5c5b756d00b0263ae0e0`
- **作者**: 林致帆
- **日期**: 2021-09-08 18:15:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 139. [WebService]Q00-20210908003 修正DotJ登入，所記錄的使用者登入資訊沒有加上Locale導致寫入DB時報錯
- **Commit ID**: `d9e83fe9364f16cf56a4c44d89c40ceefb088b5d`
- **作者**: walter_wu
- **日期**: 2021-09-08 17:04:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`

### 140. [BPM APP]Q00-20210907005 修正Line推播訊息在Textbox有設定顯示千分位時沒顯示問題
- **Commit ID**: `397c4e31f63aee87a4c78c8ca4ba0e075ee00514`
- **作者**: yamiyeh10
- **日期**: 2021-09-08 16:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 141. [Web]Q00-20210908002 修正在一些https環境下會無法下載檔名有中文的檔案
- **Commit ID**: `c7ed1cfd6ce0ae44aca66159cff2a1c93d5d3ac8`
- **作者**: 王鵬程
- **日期**: 2021-09-08 16:10:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 142. [BPM APP]Q00-20210907005 修正Line推播訊息在選項元件有設定額外輸入框且有值時不會顯示問題
- **Commit ID**: `e9c9dfb110615f9cc5a90408daaf1c77c780830a`
- **作者**: yamiyeh10
- **日期**: 2021-09-08 15:14:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 143. [Web]S00-20210316001 流程筆數為1000時，呈現筆數為1000，不再用999+
- **Commit ID**: `e094f639d805cbd5262dc9e56d4bb15bbc1bc843`
- **作者**: 林致帆
- **日期**: 2021-09-08 11:08:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/CursorRecorder.java`

### 144. [流程引擎]Q00-20210907003 修正發起時就算儲存表單，核決層級預解析因為沒有抓到設定的表單欄位而無法解析
- **Commit ID**: `9fb38df26eb07c5e0a5cd2abcfe1465e17001f5f`
- **作者**: walter_wu
- **日期**: 2021-09-07 13:53:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 145. [Web]Q00-20210907002 Ajax_FormAccessor.getFormInstanceAttachmentSize() 增加「取得當前表單實例的附件數量」的方法
- **Commit ID**: `8b8e95b46a6b203917281313f85fc6db82065c3c`
- **作者**: waynechang
- **日期**: 2021-09-07 10:40:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 146. [Web]Q00-20210906003 修正流程主旨允許用html並有輸入tr或tbody的tag，導致在BPM首頁中無法從下方待辦進入流程
- **Commit ID**: `785ebcf7ba31812c4f0e10737931856214565639`
- **作者**: 王鵬程
- **日期**: 2021-09-06 18:06:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 147. [BPM APP]Q00-20210906001 調整會議模組的掃碼與推送通知功能沒卡控判斷整合IMG系統變數
- **Commit ID**: `7296a42c8be4536ada6668afe0fe6f4413ca7afc`
- **作者**: pinchi_lin
- **日期**: 2021-09-06 16:32:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 148. [流程引擎]Q00-20210903003 修正「發起流程時儲存表單後，表單的viewMode從『INVOKE』變成『PERFORM』」的問題
- **Commit ID**: `f06277e408691689ef0489c9a58dc4ca465f5dc4`
- **作者**: yanann_chen
- **日期**: 2021-09-03 18:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`

### 149. [Web]Q00-20210903002 調整模組中的程式開啟方式是設定另開新視窗時，使可以開啟多個不同程式名稱的視窗
- **Commit ID**: `e8a5fb91f75dd8cbc1b408e0cf3b921b779e1ecf`
- **作者**: 王鵬程
- **日期**: 2021-09-03 17:56:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 150. [BPM APP]Q00-20210903001 修正顯示流程中簽核人員沒有多語系的問題
- **Commit ID**: `87579391ff522503160fab7d3b6e1c94d80ea6d2`
- **作者**: pinchi_lin
- **日期**: 2021-09-03 16:27:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 151. [BPM APP]Q00-20210902001 修正BPMAPP所有用到的頁面JSP引入css、js瀏覽器會有緩存問題
- **Commit ID**: `f9da7570b547f9bb925b8e7b8f4dd06d4af08628`
- **作者**: pinchi_lin
- **日期**: 2021-09-02 18:52:40
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/NotRegisterApp.jsp`

### 152. [內部]S00-*********** 調整開發者工具頁面新增清除快取資料功能[補]
- **Commit ID**: `9568fd3c45999ef895d2b31e7bbca739c9d08497`
- **作者**: cherryliao
- **日期**: 2021-09-02 13:55:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp`

### 153. [Web]A00-*********** 修正允許流程主旨使用html時，在行動版下主旨無法呈現html的樣式
- **Commit ID**: `0a3bafbc1cd72415b454d041f30102d64b6cbbd9`
- **作者**: 王鵬程
- **日期**: 2021-09-01 18:39:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 154. [流程引擎]A00-20210901001 修正客戶Grid資料過多導致SQL組過複雜導致DB報錯
- **Commit ID**: `da8c4de63ae3942796aa9e416e1f633baef16c12`
- **作者**: walter_wu
- **日期**: 2021-09-01 17:25:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 155. [BPM APP]調整行動端詳情表單的簡易簽核歷程可依系統變數設定是否要顯示全部資料功能
- **Commit ID**: `db7189532aa509b8e11236e5e7cfb2aabd1e4603`
- **作者**: yamiyeh10
- **日期**: 2021-09-01 16:29:16
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 156. [Web]A00-20210831001 修正使用英文語系登入，在轉由他人處理頁面中，轉由他人處理內的標題和選項翻譯錯誤
- **Commit ID**: `70a3b2b105411d7917391e0a1997623f69f5c32f`
- **作者**: 王鵬程
- **日期**: 2021-09-01 15:25:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 157. [內部]S00-*********** 調整開發者工具頁面新增清除快取資料功能
- **Commit ID**: `01c91cd84c176a531a99c4a7c4cd83fe73964965`
- **作者**: cherryliao
- **日期**: 2021-08-30 15:43:46
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ServerCacheManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdministratorFunctionAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-administratorFunction-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 158. [Web]Q00-*********** 修正在行動版下，選擇退回重辦彈出來的視窗中，無法選擇要退回的關卡
- **Commit ID**: `bec0c46bb13af6548bd8b29e7d3b45ba6656b38e`
- **作者**: 王鵬程
- **日期**: 2021-08-30 11:59:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 159. [BPM APP]C01-20210825004 修正移動端加簽功能，選擇人員資訊會撈到離職人員問題
- **Commit ID**: `fd5f562447718daa15328296c16755e5766dd684`
- **作者**: shihya_yu
- **日期**: 2021-08-30 10:14:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 160. [BPM APP]Q00-20210819003 修正IMG表單畫面，操作到其他頁面再重整表單時，左上方IMG的返回按鈕沒顯示問題[補]
- **Commit ID**: `755aa0e01f3f87fd3a46fe5e32b0f87ce0281662`
- **作者**: shihya_yu
- **日期**: 2021-08-30 09:17:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 161. [Web]Q00-20210827001 調整DatabaseAccessor重組SQL指令的邏輯，防止在錯誤的地方插入where條件
- **Commit ID**: `76f893ad56c68271526402fad31bf3bc7bc469f9`
- **作者**: yanann_chen
- **日期**: 2021-08-27 18:46:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 162. [Web]A00-20210826001 修正退回重辦時簽核意見有換行，導致流程圖中檢視參與者型式的關卡頁面的工作列表無法出現
- **Commit ID**: `bff2e77c03bbdba7369a28ca91fc2e61b7d13f32`
- **作者**: 王鵬程
- **日期**: 2021-08-27 11:45:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 163. [BPM APP]Q00-20210819003 修正IMG表單畫面，操作到其他頁面再重整表單時，左上方IMG的返回按鈕沒顯示問題
- **Commit ID**: `a5de00e567bb5c36ddc56e6b0afcc343c4dec8f0`
- **作者**: shihya_yu
- **日期**: 2021-08-27 10:08:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 164. [Web]Q00-20210826001 修正「若有為null的資料時，無法載入資料至Grid」的問題
- **Commit ID**: `e31fa96905684a96167426114670dc6437d3bf94`
- **作者**: yanann_chen
- **日期**: 2021-08-26 16:44:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 165. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `35a306e42d019579e9fece74a6af930b7e082b7f`
- **作者**: peng_cheng_wang
- **日期**: 2021-08-26 15:20:11
- **變更檔案數量**: 0

### 166. [Web]A00-20210825001 修正在新增關卡頁面中，在關卡的參與者和工作的處理方式在英文顯示時有誤
- **Commit ID**: `d6864121d05cc38fd8ace5103e3aaaaab5712486`
- **作者**: 王鵬程
- **日期**: 2021-08-26 15:19:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 167. [流程設計師]Q00-20210531001 修正「複製有連接線的關卡造成實際流程派送發生異常」的問題
- **Commit ID**: `f3ce2b5015acc69ac76332a6f161f0ffd3c24ce0`
- **作者**: yanann_chen
- **日期**: 2021-06-02 15:44:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramAction.java`

### 168. [Web]A00-20210825002 修正使用者用IE11登入，線上人數查詢登入裝置資訊為IE7.0
- **Commit ID**: `2900d10c062db4735ce01fbe73e8e6e9fc0851ad`
- **作者**: 林致帆
- **日期**: 2021-08-25 18:16:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 169. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `15a388860bd71aeb7fd72e1eab13e431aaf5e8e5`
- **作者**: yamiyeh10
- **日期**: 2021-08-23 17:31:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/ajax-loader.gif`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 170. [BPM APP]Q00-20210820002 修正企業微信與钉钉的列表頁面jsp引入css、js瀏覽器會有緩存問題
- **Commit ID**: `a66eb6b6d9044d12b59ef407d62bd8a85b952f56`
- **作者**: pinchi_lin
- **日期**: 2021-08-20 11:14:44
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContactV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp`

### 171. [BPM APP]S00-20210805001 調整企業微信與钉钉的追蹤流程預先顯示已處理流程頁面
- **Commit ID**: `1d8f039792f6b3e1e6fedb3cfe5115c336eac073`
- **作者**: pinchi_lin
- **日期**: 2021-08-20 10:58:48
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 172. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `32fd3b9d41af004256601bb2b579de7dfec3ba5a`
- **作者**: lorenchang
- **日期**: 2021-08-20 09:06:35
- **變更檔案數量**: 0

### 173. [內部]新增清除所有Server二階快取的相關EJB及RMI接口
- **Commit ID**: `7c55d7adf91f8570f18547df0b0678f73f0c0f6f`
- **作者**: lorenchang
- **日期**: 2021-08-20 09:06:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IServerCacheManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java`

### 174. [Web]Q00-20210819005 修正使用singleOpenWin開窗，搜尋條件使用簡體字會搜尋不到資料
- **Commit ID**: `f6f13da91f82978c8355848b7ea87751af4417d2`
- **作者**: 王鵬程
- **日期**: 2021-08-19 18:10:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 175. [BPM APP]優化移動表單的顯示流程樣式
- **Commit ID**: `6f0a3dba19b08cffb13ff6b60f07afc9143c8b12`
- **作者**: yamiyeh10
- **日期**: 2021-08-19 17:37:43
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 176. [流程引擎]	Q00-20210818002修正SQLcommand放的SQL指令有簡體字，會造成base64加密報錯
- **Commit ID**: `6c7b826ed87f67e41fd58ab4eaf581e11071b3b6`
- **作者**: 林致帆
- **日期**: 2021-08-18 18:19:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 177. [Web]Q00-20210818001 調整當瀏覽器封鎖BPM的彈窗時，在使用模擬模式進入監控或是進入待辦提示使用者開啟設定的訊息內容
- **Commit ID**: `cedca5d1cd930b55068c39b7de4b5e625b0c7096`
- **作者**: 王鵬程
- **日期**: 2021-08-18 11:57:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 178. [Web]Q00-20210817002 修正當瀏覽器封鎖BPM站台的彈窗時，模擬使用者後進入待辦流程中會提示使用者去設定允許彈窗
- **Commit ID**: `b9901e93f28a12ca3433658b9aca0efa4ecefb09`
- **作者**: 王鵬程
- **日期**: 2021-08-17 18:35:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 179. [Web]Q00-20210817001 修正當瀏覽器封鎖BPM站台的彈窗時，且勾選啟動流程測試模式進入監控流程，會提示使用者去設定允許的彈窗
- **Commit ID**: `d1407e7d23dbbc8b2f4896eca6b696b938701bca`
- **作者**: 王鵬程
- **日期**: 2021-08-17 17:51:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 180. [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
- **Commit ID**: `eb7d6af0de1a8b11c0f2924760803e9cb316583d`
- **作者**: 林致帆
- **日期**: 2021-08-17 16:49:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 181. [BPM APP]Q00-20210816002 修正IMG中間層只有在待辦應用才產生上一關卡. 下一關卡資訊
- **Commit ID**: `855563a5b4aa3f6e8c991a32b65ef065b8db60fa`
- **作者**: shihya_yu
- **日期**: 2021-08-16 18:00:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 182. [流程引擎]S00-20210113002調整流程前一關為服務任務，派送到下一關主旨會以下一關處理者的預設語系[補修正]
- **Commit ID**: `63ebbbec797ef26ef1c3c70460feaaea183fc8f0`
- **作者**: 林致帆
- **日期**: 2021-08-16 16:12:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 183. [BPM APP]還原行動版用的Grid(單身)顯示摘要功能
- **Commit ID**: `7db77740e3e9dc2bdd38ef2571a8424c0341f56f`
- **作者**: pinchi_lin
- **日期**: 2021-08-16 15:56:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 184. [BPM APP]Q00-20210816001 修正IMG詳情頁面中的顯示流程標題沒有多語系問題
- **Commit ID**: `4a59c1d9a612d893a50fb6c309d023379405add7`
- **作者**: yamiyeh10
- **日期**: 2021-08-16 14:04:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 185. [流程引擎]Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回[補修正]
- **Commit ID**: `7a7b789efce509a448ce01d402aafbf483107c28`
- **作者**: walter_wu
- **日期**: 2021-08-16 13:48:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 186. Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回
- **Commit ID**: `3a45d6ef43e7aa7ae27007235e7431086a0dd9df`
- **作者**: walter_wu
- **日期**: 2021-08-13 19:23:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 187. [內部]Q00-20210813003 修正"取得流程圖資料"接口的欄位performerId的內容不該為OID
- **Commit ID**: `c03e7dbc5a52aaf6f1f614aed76b1ed6ebd66d8d`
- **作者**: 林致帆
- **日期**: 2021-08-13 17:05:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 188. [BPM APP]Q00-20210812002 修正IMG待辦列表可依關卡建立時間排序功能
- **Commit ID**: `d389592c8b0a9028e12b21d603b2e27c24cda211`
- **作者**: shihya_yu
- **日期**: 2021-08-13 14:34:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 189. [BPM APP]Q00-20210813001 調整IMG列表資訊的架構，以符合關鍵字搜尋後，組篩選條件的資訊
- **Commit ID**: `1f726d35db3f2655c65e5484f852a75527d8520d`
- **作者**: shihya_yu
- **日期**: 2021-08-13 14:33:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 190. [流程引擎]A00-20210810001 修正簡易流程圖無法查看於核決層級內加簽的關卡的關卡資訊
- **Commit ID**: `4bddafd39f368fbc525876a4c4b53dd69f1b4b0e`
- **作者**: yanann_chen
- **日期**: 2021-08-13 14:22:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`

### 191. [流程引擎]S00-20210113002調整流程前一關為服務任務，派送到下一關主旨會以下一關處理者的預設語系
- **Commit ID**: `f6673f1dfe59b3f23128512a986b0759c091bcbb`
- **作者**: 林致帆
- **日期**: 2021-08-12 17:29:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 192. [流程引擎]Q00-*********** 在加簽過程中加入條件判斷，只有「進行中」的工作才可以進行加簽
- **Commit ID**: `7d3ef5d35fc89fe18bf5fa5ae8d60b3ceb59fcfd`
- **作者**: yanann_chen
- **日期**: 2021-08-12 14:35:39
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`

### 193. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `4135087dc09271e30df3800da139706c11722892`
- **作者**: yamiyeh10
- **日期**: 2021-08-11 17:25:53
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 194. [在線閱覽]S00-*********** 在線閱覽模組新增自訂浮水印內容及測試轉檔功能
- **Commit ID**: `9bc3dca319620aa2a071241b5577b0ed12516de3`
- **作者**: waynechang
- **日期**: 2021-08-11 14:51:02
- **變更檔案數量**: 37
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OnlineReadDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PDFHandleDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/onlineRead/OnlineReadWatermarkPattern.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/onlineRead/WatermarkAllowedForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/onlineRead/OnlineReadManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/onlineRead/OnlineReadManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/onlineRead/OnlineReadManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/onlineRead/OnlineReadManagerMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/OnlineReadAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WaterMarkPattenUtil.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageOnlineRead-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/OnlineReadFileUploader.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/WatermarkPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/spectrum/spectrum.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/spectrum/spectrum.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DDL_InitOnlineReadDB_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DDL_InitOnlineReadDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DDL_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_Oracle_1.sql`

### 195. [BPM APP]Q00-20210811001 修正使用Line官方帳號登入的BPM,直接點擊ESSQ表單類會空白的問題
- **Commit ID**: `93fc4c1ae721090ed3f0989cc8cc2295dfc5097a`
- **作者**: yamiyeh10
- **日期**: 2021-08-11 10:47:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 196. [Web]Q00-20210810001 修正關卡通知信設定以整張表單時，checkbox元件在信件上呈現應該要為顯示值
- **Commit ID**: `495788028dff65228d06b13cb0ea0186df82a7f9`
- **作者**: 王鵬程
- **日期**: 2021-08-10 18:02:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 197. [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
- **Commit ID**: `408bde4ebc7c5e8f64054f5ed18335f3bfe0a4bf`
- **作者**: walter_wu
- **日期**: 2021-08-10 11:05:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 198. [TIPTOP]Q00-20210511004 修正TIPTOP拋單回傳給TIPTOP失敗，流程可發起成功[補修正]
- **Commit ID**: `1d770898645076eb7e209a7e85b507b19e9dc940`
- **作者**: 林致帆
- **日期**: 2021-08-09 10:49:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 199. [Web]A00-20210806001 修正日期和時間元件設定多語系提示文字，但只顯示預設值的問題
- **Commit ID**: `84f4462d18174b7a4f1b1702f2869e51dc4e88d6`
- **作者**: cherryliao
- **日期**: 2021-08-06 17:08:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 200. [流程設計師]S00-20210318004 修正連接線條件式輸入空格，顯示上會被替換成空字串
- **Commit ID**: `ec496bdc0e494a79a617063896bdc9f65bd41bed`
- **作者**: 林致帆
- **日期**: 2021-08-06 16:23:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/util/Content2ElementsConverter.java`

### 201. [Dot.J]S00-20201124001 調整當設定檔找不到Secure時，設定Secure為false避免ECP呼叫DotJIntegration溝通時發生異常
- **Commit ID**: `cd2c3254dcd87f475949714bdf50601894cbe377`
- **作者**: cherryliao
- **日期**: 2021-08-06 16:00:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`

### 202. [Web]A00-20210804003 修正在行動版畫面的監控流程清單中每列的主旨未對齊
- **Commit ID**: `8962674848ee67c331145d5bc4c896aa7bb5c2a4`
- **作者**: 王鵬程
- **日期**: 2021-08-05 18:21:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 203. [Web]Q00-20210805002 修正關卡通知信設定以整張表單時，dropdown元件在信件上呈現應該要為顯示值
- **Commit ID**: `305544f68ba93b82b5279c343d6c2cbda4ea9f00`
- **作者**: 王鵬程
- **日期**: 2021-08-05 17:09:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 204. [系統管理工具]S00-20200616002 調整點選線上使用者的通知，若使用者已登出時會跳出的錯誤訊息
- **Commit ID**: `df3916f2289ec253988eb1c1531fe0f5ed93e08f`
- **作者**: cherryliao
- **日期**: 2021-08-04 17:29:32
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/OnlineUserMgtController.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_zh_TW.properties`

### 205. [Web]S00-20200528001 優化簽核歷程的流程狀態"已處理"更改成"已同意"
- **Commit ID**: `8b5b337cc8be274e945fbfdb2a614bf3eb631b7a`
- **作者**: 林致帆
- **日期**: 2021-08-04 15:06:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 206. [Web]S00-20210202001 簡易流程圖的流程狀態字眼明確化
- **Commit ID**: `7cf617b49289918a8592908bf436f46646b8d7f5`
- **作者**: 林致帆
- **日期**: 2021-08-04 15:02:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 207. [流程引擎]Q00-20210804004 客戶表單定義設計欄位過多，導致表單無法正常簽核
- **Commit ID**: `5dc4c5a5ac9fea7873adce3cded9ae5d01a777e0`
- **作者**: yanann_chen
- **日期**: 2021-08-04 14:30:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 208. [流程引擎]S00-20201118001 調整當流程關卡中有工作被轉派給代理人處理且流程通知設為結案逐級通知時通知原處理者
- **Commit ID**: `82a3e6046b543e9e664a30fc77abafbdd9dd6c75`
- **作者**: cherryliao
- **日期**: 2021-08-04 14:23:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 209. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `21f7a9ddd30840f5fea93f1cbb6d6eb5b12055b8`
- **作者**: yamiyeh10
- **日期**: 2021-08-04 10:45:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 210. [Web]Q00-20210803003 修正因之前議題的修正導致移動端功能『入口平台整合設定』頁面出現錯誤
- **Commit ID**: `7efcfc37924bec16033d1510b611c3daf644f577`
- **作者**: 王鵬程
- **日期**: 2021-08-03 18:16:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 211. [內部]Q00-20210803002優化log訊息：T100拋單時，取得附件的檔案編碼ID以及文檔中心的URL沒有設定
- **Commit ID**: `ef5648ab8ec241fa6431400109f960a60baeb8e5`
- **作者**: 林致帆
- **日期**: 2021-08-03 17:18:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 212. [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題[補]
- **Commit ID**: `274815986fd9a182e95f111684a53fec4e110546`
- **作者**: yanann_chen
- **日期**: 2021-08-03 17:12:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 213. [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題
- **Commit ID**: `2748e864c9b2e0e1e1e0e0ac717267d33f5013ae`
- **作者**: yanann_chen
- **日期**: 2021-06-07 11:43:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 214. [Web]A00-20210729001 SQLCommand資料庫為DBConnection元件，呼叫ajax query時找不到DB連線方式
- **Commit ID**: `f6b461b158d5f64012a2b0a2e10b205be24a2560`
- **作者**: yanann_chen
- **日期**: 2021-08-03 16:07:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 215. [Web]S00-20210315001 增加轉派意見在待辦流程的主旨上 [補修正]
- **Commit ID**: `ca28ab762e4293916342295b30116050571b24d7`
- **作者**: 林致帆
- **日期**: 2021-08-03 11:39:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 216. [Web]S00-20210315001 增加轉派意見在待辦流程的主旨上
- **Commit ID**: `4e24d3464adcf2c6b758feee6fef5a11e0c370d5`
- **作者**: 林致帆
- **日期**: 2021-08-02 14:26:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 217. [BPM APP]C01-20210730002 修正移動簽核，下一關卡資訊有異常時，流程派送失敗但畫面卻顯示派送成功的問題
- **Commit ID**: `4e39ef68db93a48b56900e6da7f1258cfd9512df`
- **作者**: 詩雅
- **日期**: 2021-08-02 14:19:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 218. [流程引擎]Q00-20210730002 修正關卡「只有一人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核[補]
- **Commit ID**: `2459a511f2496fa6995e249657acfea9e608fca8`
- **作者**: yanann_chen
- **日期**: 2021-07-30 18:23:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 219. [流程引擎]Q00-20210730002 修正關卡設定「只有一個人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核
- **Commit ID**: `d57f20bcc6fc17d1087e24ca66a4a05276346024`
- **作者**: yanann_chen
- **日期**: 2021-07-30 17:52:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 220. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `c297e675210f126667fb6f61b3b6b11031958c85`
- **作者**: yamiyeh10
- **日期**: 2021-07-30 15:08:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 221. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `21f4937951bb5a316d30df873fbf85cad198ae00`
- **作者**: yamiyeh10
- **日期**: 2021-07-30 14:30:37
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 222. [Web]Q00-20210730001 修正Grid設小螢幕使用名片式並綁定Textarea，在行動版下有輸入換行，Grid呈現應該要是沒換行
- **Commit ID**: `b33481117363367a6e620c2bf9ad272ac48f4933`
- **作者**: 王鵬程
- **日期**: 2021-07-30 10:48:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 223. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `58dedac3961b46a85607ce874be959f8800a651d`
- **作者**: yamiyeh10
- **日期**: 2021-07-29 16:18:32
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchProcessCommentsBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 224. [組織設計師]S00-20210506001 調整設定流程代理人時不顯示已失效的流程
- **Commit ID**: `a11f529d5560a264a3c8fc4f9cdb3117ee26dcf5`
- **作者**: cherryliao
- **日期**: 2021-07-29 15:44:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessPackageForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/ProcessSelectorTableController.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 225. [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
- **Commit ID**: `6e17b5aa8d446c85e0ff58a0756b0cb02d374b2f`
- **作者**: 林致帆
- **日期**: 2021-07-29 14:38:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 226. [Web]Q00-20210728001 修正透過URL開啟『追蹤流程實例內單一表單資料』，在行動版時未顯示表單名稱
- **Commit ID**: `efb6d9bd7399a3c30cbd6e4e80699f2414c6ca8a`
- **作者**: 王鵬程
- **日期**: 2021-07-28 18:11:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`

### 227. [ISO]S00-20210507001 PDF浮水印屬性管理新增「圖片浮水印」功能(BCL8)
- **Commit ID**: `62659cd4c92e817013a55faa2cc934a0a20277dc`
- **作者**: waynechang
- **日期**: 2021-07-28 16:01:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 228. [BPM APP]優化移動表單的顯示流程樣式
- **Commit ID**: `794a109662a0ab9a2b9acf3b5e6e02ba350dd062`
- **作者**: pinchi_lin
- **日期**: 2021-07-28 15:35:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 229. [Web]A00-20210726005 修正表單是絕對表單時，透過URL開啟『追蹤流程實例內單一表單資料』，在行動裝置版面下附件按鈕沒有字樣
- **Commit ID**: `ca8b869a4fedd5e4080f514705aefd4d261ea37a`
- **作者**: 王鵬程
- **日期**: 2021-07-28 15:30:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 230. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `899820eb9396555ec8b3f0a0c08e083a2009ea6f`
- **作者**: pinchi_lin
- **日期**: 2021-07-28 15:12:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 231. [內部]移除移動端無用程式並新增一個移動端共用服務工具程式供後續調整
- **Commit ID**: `5900c49869bc2227804d9d518681d03cf857a0f5`
- **作者**: pinchi_lin
- **日期**: 2021-07-28 14:58:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileWorkItemServiceTool.java`

### 232. [BPM APP]優化移動表單的簽核歷程樣式
- **Commit ID**: `f919efe5221b18cba2498ba194712d947c12116e`
- **作者**: yamiyeh10
- **日期**: 2021-07-28 10:33:55
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 233. [BPM APP] 修正表單上傳/刪除附件後，更新表單時，會跑版問題
- **Commit ID**: `6b4f5f58bde1c4009cab640348000743172a0f5d`
- **作者**: 詩雅
- **日期**: 2021-07-27 18:16:47
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 234. [Web]A00-20210727002 修正當Grid有設定將最前面欄位設為流水號時，用流水號排序時應該用數字大小來排序
- **Commit ID**: `715528153331c3cc290ab9469eda296740fad3a1`
- **作者**: 王鵬程
- **日期**: 2021-07-27 18:08:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 235. [BPM APP]Q00-20210727003 修正企業微信推送消息中取access_token判斷過期的邏輯異常問題
- **Commit ID**: `2a02a49cb58718651df0e1cf95240a5b373f6702`
- **作者**: pinchi_lin
- **日期**: 2021-07-27 17:49:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 236. [Web]A00-20210726002 修正行動版下，從追蹤進去流程且該流程有被同個人簽核多次過，在取回清單頁面中下拉選單會出現奇怪文字
- **Commit ID**: `f65fac92a05d46c0a8b221914e12010d5e6f8750`
- **作者**: 王鵬程
- **日期**: 2021-07-27 17:11:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 237. [Web]A00-20210727001 修正XPDL的流程在監控流程中跳過關卡時，驗證密碼視窗會一片空白
- **Commit ID**: `ed116113afe5ef7ca3775623e63e1bdcc90ab26e`
- **作者**: walter_wu
- **日期**: 2021-07-27 16:50:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordForByPass.jsp`

### 238. [流程引擎]Q00-20210727002 修正因為關卡設定自動跳關導致代理機制異常
- **Commit ID**: `95ecacded479570a3dbd963f3d199290cf7afcac`
- **作者**: walter_wu
- **日期**: 2021-07-27 15:57:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 239. [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
- **Commit ID**: `b961097182ac27afea4c80c8e833f1d600acc3b3`
- **作者**: pinchi_lin
- **日期**: 2021-07-27 15:01:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 240. [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
- **Commit ID**: `d4e18fdedee90c2a1acaf85e1e5c4f3521dae495`
- **作者**: pinchi_lin
- **日期**: 2021-07-27 14:36:19
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 241. [流程引擎]Q00-20210727001 修正因執行加簽關卡導致核決層級預先解析內容不正確
- **Commit ID**: `9c44b1e5d6def3760102907b3d630a83b60c6d6f`
- **作者**: yanann_chen
- **日期**: 2021-07-27 14:35:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 242. [表單設計師]S00-20200716001 新增日期和時間元件預設值配置功能[補]
- **Commit ID**: `535da737b601fe9d82253575be24291f86782cb2`
- **作者**: cherryliao
- **日期**: 2021-07-27 14:24:25
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 243. [Web]A00-20210726001 修正關卡有勾選允許批次簽核，在行動版畫面的待辦事項清單中每列的主旨未對齊
- **Commit ID**: `56e2d145e50b174011458a906061a661ce6e9bd0`
- **作者**: 王鵬程
- **日期**: 2021-07-26 18:20:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 244. [Web]A00-20210726003修正ajax_CommonAccessor的findXmlContent,findResource接口取得內容為中文亂碼
- **Commit ID**: `3e9b75b92618f79efc8f8a33d6e1dc8366adcf22`
- **作者**: 林致帆
- **日期**: 2021-07-26 18:08:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AbstractAccessor.java`

### 245. [BPM APP]C01-20210722010 調整郵件內容以及Line推播內容，密碼元件值以*號顯示
- **Commit ID**: `5e90cb29799400f577f0748db4c564c24d439d78`
- **作者**: 詩雅
- **日期**: 2021-07-26 16:53:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 246. [流程設計師]S00-20210305001 調整服務任務讀取https的WSDL，增加SSL憑證失敗的明確提示窗
- **Commit ID**: `13c3d08bc0b51a95e88ccec35be74ef4a41b2c56`
- **作者**: 林致帆
- **日期**: 2021-07-26 15:44:11
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/WSInvocationEditorController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_zh_TW.properties`

### 247. [內部]Q00-20210726001 DatabaseAccessor 移除不需要的System.out.print方法
- **Commit ID**: `88ebb8491f18b34fddf7560961f928728703acab`
- **作者**: 林致帆
- **日期**: 2021-07-26 14:35:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 248. [Web]A00-20210720001 修正絕對位置表單在追蹤流程頁面，表單範圍外的元件顯示出來
- **Commit ID**: `59e6839f55dc59f779a061b4be0e80dcc2be6f9d`
- **作者**: 林致帆
- **日期**: 2021-07-23 18:17:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 249. [Web]Q00-20210723001 修正當關卡表單權限設定為「唯讀」時，第一次點擊「儲存表單」後沒有再執行formOpen的問題
- **Commit ID**: `faaaa36733a65b471b248089171dd0d71198498a`
- **作者**: yanann_chen
- **日期**: 2021-07-23 17:20:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 250. [BPM APP]Q00-20210722001 修正IMG的jsp引入css、js瀏覽器會有緩存問題
- **Commit ID**: `b3300901cca5046d16341b36f36597f9a2fbdc29`
- **作者**: pinchi_lin
- **日期**: 2021-07-22 18:26:41
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`

### 251. [BPM APP]C01-20210721005 修正整合钉钉在安卓開表單頁面時icon變文字問題
- **Commit ID**: `98894b0bfd26a49eb8a393420654c490368b1e29`
- **作者**: pinchi_lin
- **日期**: 2021-07-22 17:27:07
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 252. [流程引擎]Q00-20210721003 修正取回重辦後，簡易流程圖只顯示流程關卡，未顯示關卡處理者
- **Commit ID**: `0b2d37cae2ddf2883d030d4dac6f980058fbf839`
- **作者**: yanann_chen
- **日期**: 2021-07-21 17:25:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 253. [Web]Q00-20210721002 修正Grid綁textarea並輸入換行，畫面縮成mobile再到PC，Grid中資料會變成未換行
- **Commit ID**: `cdc36454e88eb84a92b99ef2a3c15486844ec039`
- **作者**: 王鵬程
- **日期**: 2021-07-21 16:53:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 254. [表單設計師]S00-20200917002 調整表單設計師Gird勾選最前面欄位設為自動增加流水號時新增一列序號欄位
- **Commit ID**: `4feadb0ab65cb5dad39eeba76df492478524f5a3`
- **作者**: cherryliao
- **日期**: 2021-07-21 10:52:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 255. [流程引擎]Q00-*********** 加快發起流程時表單開啟速度
- **Commit ID**: `bac62d51dee18b526721373886f35aa81d7c6ff5`
- **作者**: yanann_chen
- **日期**: 2021-07-21 10:24:05
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessPackageManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 256. [Web]Q00-*********** 修正Grid綁定textarea並輸入換行，點擊排序後再點選Row，帶回textarea會出現<br>
- **Commit ID**: `b4571aed33b622e25d4955a5aa5b269b094ab380`
- **作者**: 王鵬程
- **日期**: 2021-07-20 19:54:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 257. [Web]C01-20210706002 修正流程第二關設置radiobutton為invisible狀態，第二關簽核後該元件內容會消失
- **Commit ID**: `e1bb452f04f93819fa90d742fc57864baa722959`
- **作者**: 林致帆
- **日期**: 2021-07-20 14:57:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 258. [BPM APP]C01-20210714002 修正IMG詳情頁面的簽核歷程若前面存在與當前關卡相同關卡id時不會顯示問題
- **Commit ID**: `a1deb80e361f8796ff67bf8075b47f27f6f0c312`
- **作者**: yamiyeh10
- **日期**: 2021-07-20 11:18:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 259. [表單設計師]S00-20200716003 增加TextBox數字轉文字功能可設定將文字呈現於另一個TextBox欄位中
- **Commit ID**: `ffab18408a7fc28ab664c5d990d8c27e71235680`
- **作者**: cherryliao
- **日期**: 2021-07-19 17:54:22
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/InputElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 260. [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法
- **Commit ID**: `f3a9a24ef3615d9ddde2f6ad421a7c5191c72ea9`
- **作者**: pinchi_lin
- **日期**: 2021-07-16 18:09:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 261. [Web]Q00-20210716002 修正Grid綁定check、radio且設額外產生輸入框，通知信設定以表單元件時通知信的Grid會跑版
- **Commit ID**: `87079a7c267c4da9af715d152be75913d06653a8`
- **作者**: 王鵬程
- **日期**: 2021-07-16 16:11:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 262. [內部]調整排版
- **Commit ID**: `4ede974ab1e445cdd10e9607324b5df5ec48c598`
- **作者**: pinchi_lin
- **日期**: 2021-07-15 13:59:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 263. [Web]S00-*********** 調整人員離職自動將帳號功能停用，復職則帳號功能啟用
- **Commit ID**: `3e059360383e0c07128d45d948fa0b48e349888c`
- **作者**: 林致帆
- **日期**: 2021-07-14 16:59:02
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 264. [Web]Q00-*********** 修正Grid有綁定Checkbox時，關卡通知信設定以表單元件時，通知信的Grid會跑版
- **Commit ID**: `f4f98f10a4efc41ca7fc417a049a11c6245eb549`
- **作者**: 王鵬程
- **日期**: 2021-07-14 16:33:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 265. [Web]S00-20200821001 調整表單TextBox元件於可編輯模式下onblur時檢查資料型態
- **Commit ID**: `5edc630805b7c4fa198f259625d0f8e037ea19ea`
- **作者**: cherryliao
- **日期**: 2021-07-13 18:41:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 266. [表單設計師]S00-20200716001 新增日期和時間元件預設值配置功能
- **Commit ID**: `ec5b4da799ede012781b1017ae9dd9cb277b9bd4`
- **作者**: cherryliao
- **日期**: 2021-07-13 18:22:29
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DialogElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 267. [Web]Q00-20210713002修正表單頁籤簽核歷程置放位置選擇"top"且表單設計師設定"顯示流程簽核意見"為"NOT_SHOW"，待辦跟發起畫面的ESS表單上方會顯示"簽核意見"的文字
- **Commit ID**: `82df68b45ea89c4ce088bfeb9bdab95a7f3b55a4`
- **作者**: 林致帆
- **日期**: 2021-07-13 14:03:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`

### 268. [Web]Q00-20210713001  修正在首頁模組中的追蹤流程區塊，進入ESSF03表單會被截斷一半
- **Commit ID**: `70496f4eeca30fb84bad075faf5e00ae2aeb899f`
- **作者**: 王鵬程
- **日期**: 2021-07-13 12:08:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 269. [Web]Q00-20210702002修正流程最後一關是通知任務，流程設計師在該關卡增加BasicType的流程變數到工具定義表，導致查看流程異常
- **Commit ID**: `f5a183c7ad417a8f860847dc25af198e40d45cf4`
- **作者**: 林致帆
- **日期**: 2021-07-13 10:47:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 270. A00-20210630001 修正設定多語系後沒有設定的語系無法吃到預設值
- **Commit ID**: `2541af90185c580746e0af872e87f3a6d59ff1ae`
- **作者**: walter_wu
- **日期**: 2021-07-13 10:30:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/SelectElementDefinition.java`

### 271. [內部]調整排版
- **Commit ID**: `8c45a14f344a0ae9f38a6f00684fe1ce837d2eb7`
- **作者**: pinchi_lin
- **日期**: 2021-07-12 17:12:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 272. [ESS]C01-20210629009 修正ESS表單簽核時上傳附件，移動端未顯示附件資訊的問題
- **Commit ID**: `f66a1a40ab156547c2fd8168f56698d733eddbe9`
- **作者**: 詩雅
- **日期**: 2021-07-12 16:01:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/appform/AppFormXmlTag.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 273. [Web]Q00-20210709001 修正checkbox、radio元件已選擇新樣式的問題
- **Commit ID**: `e4b99f7f10b014a43aa2e68184e84fe2fd3850ac`
- **作者**: cherryliao
- **日期**: 2021-07-09 11:20:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 274. [Web]Q00-20210708003 修正將Grid調整為可支援換行標籤<br>
- **Commit ID**: `461cc8ccab45761e944d90b35c7884d74bfd03ce`
- **作者**: 王鵬程
- **日期**: 2021-07-08 19:50:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 275. [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法
- **Commit ID**: `893e15929a3ae341d6feed5073b402ec37382469`
- **作者**: 林致帆
- **日期**: 2021-07-08 18:56:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

