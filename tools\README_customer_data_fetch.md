# 客戶資料自動化擷取工具

## 🎯 功能概述

這個工具能夠自動掃描 Windows 網路芳鄰中所有客戶的「01客戶基本資料」資料夾，讀取其中的 `.txt` 檔案，**同時保存原始內容與結構化解析結果**，並以公司為單位輸出為 JSON 檔案。

## 🔧 核心功能

### 1. 遞迴掃描與目標識別
- 從根目錄 `\\************\顧問推廣\C1.客戶維護相關` 開始遞迴搜尋
- 自動找到所有符合模式 `*/01客戶基本資料/` 的資料夾
- 收集每個資料夾中的所有 `.txt` 檔案

### 2. 雙重資料提取
對每個 `.txt` 檔案執行：

#### ✅ 原始內容保存 (`raw_content`)
- 完整讀取文字內容
- 支援 `utf-8` / `cp950` / `big5` 編碼自動偵測
- 保留原始格式和所有資訊

#### ✅ 結構化解析 (`structured_data`)
- 自動識別常見鍵值格式：
  - 中文冒號：`主機位址：*************`
  - 英文冒號：`Host: *************`
  - 等號格式：`host=*************`
- 映射為標準欄位：`host`, `port`, `username`, `password`, `database`
- 若格式不規則，標註 `"parsing_error": true`

### 3. 公司識別與資料結構
- 從路徑自動提取公司資訊：
  ```
  路徑：...\01148600-美德向邦\01客戶基本資料\...
  結果：company_id: "01148600", company_name: "美德向邦"
  ```

### 4. JSON 輸出格式
```json
{
  "company_id": "01148600",
  "company_name": "美德向邦",
  "data_source": "01客戶基本資料",
  "folder_path": "\\\\************\\...",
  "files": [
    {
      "filename": "[美德向邦]連線資訊.txt",
      "raw_content": "（完整原始文字）",
      "structured_data": {
        "host": "*************",
        "port": "1433",
        "username": "sa",
        "password": "******",
        "database": "CRM"
      },
      "source_path": "\\\\************\\...",
      "file_size": 1024,
      "encoding_used": "utf-8",
      "processed_at": "2025-08-15T10:30:00"
    }
  ],
  "total_files": 1,
  "processed_at": "2025-08-15T10:30:00"
}
```

## 📋 環境需求

### 必要套件
```bash
pip install smbprotocol python-dotenv chardet
```

### 環境變數設定
在 `config/.env` 檔案中設定：

```env
# SMB 網路芳鄰連線設定
SMB_HOST=************
SMB_USERNAME=<EMAIL>
SMB_PASSWORD=your_password
SMB_DOMAIN=************
SMB_SHARE_NAME=顧問推廣
SMB_BASE_PATH=C1.客戶維護相關

# 客戶資料掃描設定
CUSTOMER_DATA_FOLDER=01客戶基本資料
TARGET_FILE_PATTERNS=*.txt
ENCODING_FALLBACK=cp950,utf-8,big5
```

## 🚀 使用方法

### 1. 執行前測試
```bash
# 測試 SMB 連線和各項功能
python tools/test_customer_data_fetch.py
```

### 2. 執行完整擷取
```bash
# 執行主程式
python tools/fetch_customer_data.py
```

### 3. 檢查輸出結果
- 輸出目錄：`data_output/bpm_customer/`
- 檔案格式：`{company_id}_{company_name}.json`
- 日誌檔案：`logs/customer_data_fetch_YYYYMMDD_HHMMSS.log`

## 📊 執行結果範例

```
🚀 客戶資料自動化擷取工具
==================================================

✅ 執行成功！
📁 找到客戶資料夾：15 個
🏢 成功處理公司：12 家
❌ 處理失敗：3 家
⏱️  執行時間：45.67 秒
📂 輸出目錄：data_output/bpm_customer
```

## 🔍 結構化解析邏輯

### 支援的格式模式
1. **中文冒號格式**：`主機位址：*************`
2. **英文冒號格式**：`Host: *************`
3. **等號格式**：`host=*************`

### 欄位映射表
| 原始欄位 | 標準欄位 | 說明 |
|---------|---------|------|
| 主機位址, 主機, host, server, ip | `host` | 伺服器位址 |
| 連接埠, 埠號, port, 端口 | `port` | 連接埠號 |
| 使用者, 帳號, username, user | `username` | 使用者名稱 |
| 密碼, password, pwd | `password` | 密碼 |
| 資料庫, database, db, dbname | `database` | 資料庫名稱 |
| 公司, company | `company` | 公司名稱 |
| 備註, 說明, note | `note` | 備註資訊 |

### 特殊處理
- **IP 位址自動偵測**：使用正規表達式 `\b(?:\d{1,3}\.){3}\d{1,3}\b`
- **連接埠自動偵測**：尋找 `port|埠|端口` 後的數字
- **編碼自動切換**：優先使用 chardet 偵測，回退到預設編碼列表

## 🛠️ 技術特色

### 1. 穩定性設計
- 完整的錯誤處理和異常捕捉
- 自動重試機制
- 詳細的日誌記錄

### 2. 編碼處理
- 支援多種中文編碼：UTF-8, CP950, Big5
- 自動編碼偵測
- 編碼失敗時的優雅降級

### 3. 模組化架構
- 清晰的類別設計
- 易於擴充支援其他檔案格式
- 可重用的組件

### 4. 安全性考量
- 敏感資訊使用環境變數管理
- 不在程式碼中硬編碼帳號密碼
- 安全的 SMB 連線處理

## 🔧 故障排除

### 常見問題

#### 1. SMB 連線失敗
```
❌ SMB 連線失敗
```
**解決方法：**
- 檢查網路連線
- 確認 SMB 伺服器位址正確
- 驗證帳號密碼
- 確認防火牆設定

#### 2. 套件未安裝
```
警告：smbprotocol 套件未安裝
```
**解決方法：**
```bash
pip install smbprotocol python-dotenv chardet
```

#### 3. 編碼問題
```
檔案編碼解析失敗，使用 utf-8 忽略錯誤模式
```
**解決方法：**
- 檢查原始檔案編碼
- 調整 `ENCODING_FALLBACK` 設定
- 手動指定編碼

#### 4. 權限問題
```
存取目錄失敗：權限不足
```
**解決方法：**
- 確認帳號有讀取權限
- 聯絡系統管理員開放權限
- 檢查網域設定

## 📈 效能優化建議

1. **網路環境**：確保穩定的網路連線
2. **批次處理**：大量資料時可考慮分批執行
3. **日誌管理**：定期清理舊的日誌檔案
4. **輸出管理**：定期整理輸出目錄

## 🔄 未來擴充計畫

- [ ] 支援 `.ini` 檔案格式
- [ ] 支援 `.xlsx` Excel 檔案
- [ ] 增加資料驗證功能
- [ ] 提供 Web 介面
- [ ] 支援排程自動執行
- [ ] 增加資料比對功能
