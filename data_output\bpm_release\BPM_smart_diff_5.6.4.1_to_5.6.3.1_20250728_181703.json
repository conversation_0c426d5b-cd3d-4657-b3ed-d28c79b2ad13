{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.6.4.1", "date": "tag 5.6.4.1\nTagger: 張詠威 <<EMAIL>>\n\n20170721 18:00 last build2017-07-21 17:24:54", "message": "多語系維護作業   描述查詢條件選項  移除 等於和不等於", "author": "jose<PERSON>"}, "舊分支": {"branch_name": "5.6.3.1", "date": "tag 5.6.3.1\nTagger: 張詠威 <<EMAIL>>\n\n20170526 15:40 last build2017-05-26 15:44:55", "message": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "author": "wayne"}, "比較時間": "2025-07-28 18:17:03", "新增commit數量": 199, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "ad91ae34597b8258d44c4e6fa1da9bca3e1353b7", "commit_訊息": "多語系維護作業   描述查詢條件選項  移除 等於和不等於", "提交日期": "2017-07-21 17:24:54", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b819a04df76f5dc90a00b8f3d080445bd31f0f62", "commit_訊息": "修正鼎慧追蹤-已/未完成列表異常", "提交日期": "2017-07-21 13:52:58", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "cb6074c9564a85f0f9edeefbcde7dc043ece0618", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-21 10:01:01", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "e6b1f0880086030df1010ea21ad678b586ed560a", "commit_訊息": "Q00-20170721001 修正ISO 文件總管 ,點選文件中的放大鏡會報錯 \t原因:jspd中要取得的Session是空的", "提交日期": "2017-07-21 10:00:30", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3f81589e42cd1b24fce21f3c5c5556a43438ec3b", "commit_訊息": "[寒舍C01-20170608001]追蹤發起參考流程，流程圖會顯示異常", "提交日期": "2017-07-21 09:48:27", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "baea04bc542b5efc78bdc01d269d1f7fb83c050a", "commit_訊息": "修正鼎慧列表部署工具，追蹤流程已/未完成選項名稱寫反了", "提交日期": "2017-07-20 11:36:05", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "daa786023fd76b399d1765fc037a3d203f6e8713", "commit_訊息": "修正鼎慧使用者匯入匯出錯誤", "提交日期": "2017-07-20 10:13:36", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "511e4f003b29d24647a4925d6b6fc62370a91977", "commit_訊息": "新增BPM APP草稿刪除功能", "提交日期": "2017-07-19 11:39:45", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "ae9df32b4f07428bee3aa4b418253d84400e9f43", "commit_訊息": "修正開啟加簽關卡時，當employeeID與UserID不相同時會報錯", "提交日期": "2017-07-18 19:09:31", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "68daa66c02c9be91f2c5c8dc05f3c32da2387adb", "commit_訊息": "修正 :離職人員維護作業當沒有人時會報錯的問題", "提交日期": "2017-07-18 18:42:36", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d60e81dcfedd4c6ab69e8687da2b29dac4068457", "commit_訊息": "修正絕對位置表單grid元件使用addbinding的元件，畫面上會跑版", "提交日期": "2017-07-18 14:10:05", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileAppGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa7ffc4226713bab9cef4d92ce3c1be7d9e66b3b", "commit_訊息": "調整統計組件維護UI，註解GridElementMobile中測試用的log", "提交日期": "2017-07-18 10:17:01", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "62c27c682ee77f10fe6010818146675181419498", "commit_訊息": "修正在流程設定invisible時也要產生該元件", "提交日期": "2017-07-17 19:04:37", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "44587e925251652cf85075034f0cb3771fdf6a94", "commit_訊息": "修正 : Udpate SQLSERVER 關注事項 CriticalFocusProcess ,CriticalProcessDefinition 的processPackageId 改為nvarchar(255)", "提交日期": "2017-07-17 15:27:57", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3571229238286187b87b0d749f539b86f3070027", "commit_訊息": "修改 :關注事項ORACLE欄位型態  及 將系統多語系維護作業URL更改為查詢維護樣板的URL與JSP的 ACESSRIGHT", "提交日期": "2017-07-17 14:55:11", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "7936141f536da0a82f775cf1166fb19d752f6022", "commit_訊息": "A00-20170717001 修正問題-追蹤流程中，若表單元件權限設為隱藏，元件依舊能看到", "提交日期": "2017-07-17 14:45:18", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74cd7a73a5920391a4c5d70b72995172178664ee", "commit_訊息": "修正鼎慧部分功能異常", "提交日期": "2017-07-17 09:43:06", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "e4da30e7025929576e79c41b3cb6310c130be863", "commit_訊息": "修正:出貨5.6.4.1 Oracle Update_SQL", "提交日期": "2017-07-17 09:11:19", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bdab6bfde6dfc150ad5dfeb0055889bdf49f9bda", "commit_訊息": "修改 5.6.4.1 updateSQL.Oracle", "提交日期": "2017-07-14 18:35:50", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4821eb2600f5e34064311d7335d08739979f9181", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-14 17:04:02", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "865c6dad1bd17a2b817f0057b78a0774c7bc3f3a", "commit_訊息": "補 S00-功能單 加簽的關卡可否增加刪除功能", "提交日期": "2017-07-14 17:03:38", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0a67e08619bc5699d9cd5610336a4f0d825b63fc", "commit_訊息": "修正統計組件多語系錯誤，修正工具版號", "提交日期": "2017-07-14 16:36:36", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "6a2051cf4d6ab8a9cb1caeea6f1fefc54a18f64f", "commit_訊息": "增加sap import json jar", "提交日期": "2017-07-14 15:45:00", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/.classpath", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "693208aabe744b94e8a5d9e9636f9f15c1b603ec", "commit_訊息": "調整關注欄位的css樣式 及調整多語系cache機制", "提交日期": "2017-07-14 14:19:58", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/RsrcBundleCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fd4da86976a95c06ee9f2186ca1bb55610c25f82", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-14 11:25:34", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "96f8969d6a7fff513662350d71cb3ad27b1dc65c", "commit_訊息": "調整多語系excel 及 DB 的patch", "提交日期": "2017-07-14 11:25:17", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4abd99355d2bf3714bb62e1394eb26f8dcf621c5", "commit_訊息": "修正BPMAPP批次簽核畫面跑版", "提交日期": "2017-07-14 10:25:55", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32abb6ada8f0ca64529b736cccf2356323c8e0e8", "commit_訊息": "調整關注欄位jsp", "提交日期": "2017-07-14 10:25:12", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "28d219aac252cd8d844e462e97ef491ab6ef18c9", "commit_訊息": "調整行動簽核中心", "提交日期": "2017-07-14 10:03:29", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b9e3a332301be7519a6fd197ce487ab8c331a74d", "commit_訊息": "更新DB的patch 及 調整關註欄位", "提交日期": "2017-07-14 09:52:03", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "438d2c6d6537883beea9c759d4785b4dc05a60b8", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-14 09:18:34", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "72fd545a1605313fa2fb37e5c8881947549af928", "commit_訊息": "Q00-20170602002 移除log及調整多語系", "提交日期": "2017-07-14 09:18:03", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmMailStraightSignOffPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d185c83c01aaa9c93f26b0986d464f0280dffc8e", "commit_訊息": "修正微信組織綁定表缺少OAuthConfigOID欄位問題(漏簽一支)", "提交日期": "2017-07-13 18:54:01", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "59f228acbde8c754567cff92b1620244424e9014", "commit_訊息": "修正微信組織綁定表缺少OAuthConfigOID欄位問題", "提交日期": "2017-07-13 17:21:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthWeChatOrganization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthClientOrgDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "abbd0d163c7db4a9bf7f914859edcafbf619bab8", "commit_訊息": "新增獨立模組呼叫接口", "提交日期": "2017-07-13 15:07:37", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "406a549e9b0ddefa517fd9dde02646c7cbe3ba43", "commit_訊息": "調整多語系維護作業的code", "提交日期": "2017-07-13 14:23:02", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "00bad35c792788e5e2a1c9192ddfe06944dec46e", "commit_訊息": "調整5641出貨SQL", "提交日期": "2017-07-13 12:15:35", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@criticalModule/5.6.4.1_createSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@criticalModule/5.6.4.1_createSQL_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "c4bb303097fb7e438242c6922d23bbdfc2e2ba85", "commit_訊息": "多語系獨立模組", "提交日期": "2017-07-13 11:15:44", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/CannotAccessWarnning.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/UserInfo.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/BpmTable.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/QueryDesinger.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/bootstrap/bootstrap-3.3.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/AccessRight.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/BpmTable.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/CustomDataChooser.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/ModalDialog.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/OpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/QueryTemplate.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrap-3.3.4.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/jquery-1.11.3.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/jqueryUI/jquery-ui-1.11.4.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/jqueryUI/jquery.ui-contextmenu.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/js/json2.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_en_US.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_zh_CN.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/rescBunble/QueryTemplate_zh_TW.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 35}, {"commit_hash": "2b003ea4a4b57fe786cf3daa17bfe6f686aaaf50", "commit_訊息": "修正sap Object error", "提交日期": "2017-07-13 11:15:11", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "4ea1edd04ead641541fe17ec329059f32a2ca8ea", "commit_訊息": "調整入口平台欄位顯示格式", "提交日期": "2017-07-13 09:22:58", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c945cecb724ab9c3c766efbfd4b12c2076811ea9", "commit_訊息": "調整新增行事曆時增加loading", "提交日期": "2017-07-13 09:19:34", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "151182957315b36304e2270c3dc1c45e5266e5f1", "commit_訊息": "調整統計組件", "提交日期": "2017-07-12 15:48:56", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "213253aec0ef69d15726007e2f6f3229f5871a37", "commit_訊息": "鼎慧部署工具新增統計組件選項", "提交日期": "2017-07-12 14:50:12", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ff4cb8f1ae44b31794580a2535c7c58291d22139", "commit_訊息": "增加關注模組的設定", "提交日期": "2017-07-12 14:36:38", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cbf74661ee8710b31c02105f08e93c3295e0d092", "commit_訊息": "修改 :查詢維護樣板 DropDown元件中請選擇的內顯值'$$$$$$' 改為''", "提交日期": "2017-07-12 12:02:38", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a4b1115b756274a47df65cb5cdb8fed97de81007", "commit_訊息": "新增:離職人員維護作業多語系", "提交日期": "2017-07-12 11:41:54", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5641.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "be1ef6d39bc018984cf0288efb04a4239ebfe67d", "commit_訊息": "修改多語系檔名", "提交日期": "2017-07-12 11:40:44", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 1}, {"commit_hash": "4938ddc3fb7536160277a2cbc8575b5be73bf3f7", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-12 10:55:14", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "d343901d5b3e251ba03466d4f9d503d41738a95a", "commit_訊息": "修正统计组件颜色问题", "提交日期": "2017-07-12 10:52:28", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d19b128b0585042e4cdbe85bbdcc3033ae2f24f7", "commit_訊息": "修正入口平台整合設定微信使用者管理匯入問題 企業號帳號與手機號顛倒問題", "提交日期": "2017-07-12 10:36:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6c9533afa59dceef8436442ee4d48d797ffeca89", "commit_訊息": "偉昌二次開發關注項目維護作業的code", "提交日期": "2017-07-12 10:24:21", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/MultiLanguageSet.jsp", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 8}, {"commit_hash": "a7e7898c5c68b7456cb07e9c24c59f19557318e7", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-12 09:44:18", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "f48f928aefac6b12e80e53ae3eb167b207407b05", "commit_訊息": "S00-20170703001 :修改離職人員作業:將搜尋分為可以只顯示需處理離職人員及所有離職人員", "提交日期": "2017-07-12 09:32:02", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/resignedEmployees/ResignedEmployeesSearchViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-resignedEmployeesMaintain-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "666cc6f023b1e02adefe7356a3bfac8a587eb998", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-12 09:29:07", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "bf07a9cc544fba4c9f1d143bbe7eb096eaa811e1", "commit_訊息": "修正入口平台讀取微信企業號錯誤問題", "提交日期": "2017-07-12 09:28:45", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6b00d3fffd0b8205bb29604dc5b79c679a648f0b", "commit_訊息": "移除不必要的log,及埋入SQL Command log", "提交日期": "2017-07-12 09:28:08", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RscBundleListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ddf36669d571a26fab3aa64551399c9939315fd6", "commit_訊息": "新增 : 多語系依照Key刪除_ajax接口", "提交日期": "2017-07-12 09:25:00", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RsrcBundleDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/ISysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "e5d1adfaf666b7cf63462dbaee0129fb86cc475c", "commit_訊息": "調整統計組件ajax資料封裝格式", "提交日期": "2017-07-11 20:02:32", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e1234f23ee2e53b46672a062e115bd2099477edd", "commit_訊息": "修正BPMAPP表單元件事件消失問題與調整Select元件事件位置 1.TextBox 2.TextArea 3.ListBox 4.DropDown", "提交日期": "2017-07-11 18:08:19", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7dc13966962fea97aa4bf00a971b11a6c861b9ff", "commit_訊息": "修改 :語系支援Win10繁中及簡中", "提交日期": "2017-07-11 18:05:01", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/IntegratePortalURLEntranceAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0eb9f45f4b7db738007df91c838e5e5b767c6f17", "commit_訊息": "新增:事件重要等級定義作業 Create接口", "提交日期": "2017-07-11 18:03:33", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/CriticalFocusProcessDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/critical/CriticalManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalFocusProcessViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "981a027bba6558b73955870dabe859d1e2aa1da4", "commit_訊息": "增加 : 新增及修改多語系時,要寫入Updater及時間", "提交日期": "2017-07-11 17:59:46", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12a52d679e9f29618dd772efef741ab958f006d2", "commit_訊息": "新增 : 多語系維護作業Ajax接口", "提交日期": "2017-07-11 17:58:55", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fb1f312bac4a2e6bb9f9b6fb74771bc11ebc324", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-11 17:57:55", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a12417201a6a256f78ba60c96db3c4e91078c5de", "commit_訊息": "新增: 多語系維護作業ListReader", "提交日期": "2017-07-11 17:57:35", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RscBundleListReader.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "a15f84c3754a5c58381372459e4cdff02e42c414", "commit_訊息": "修正驗證互聯連線測試功能畫面", "提交日期": "2017-07-11 17:54:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "660874ca30c0521e37dc0466273fb3b8a4968d90", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-11 17:32:31", "作者": "ChinRong", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "80662243b3e65d6dd2b49589ba18ac5b25bf9067", "commit_訊息": "修正行動簽核中心部分錯誤", "提交日期": "2017-07-11 17:31:59", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "4be5f3631f161270eb02242fdd385ae69ba2c3a7", "commit_訊息": "修正驗證互聯連線測試功能bug", "提交日期": "2017-07-11 17:25:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8e97cc0cfa626c2e539fad29454d7c43e3661321", "commit_訊息": "將5.6.3.2的sql指令調整為5.6.4.1", "提交日期": "2017-07-11 11:53:52", "作者": "張詠威", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "49d3ec6004390840791764c83fc60c3baa5c5610", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-11 11:51:48", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0afc6f82dbbdcc661d88f7edd1947c89224bdfe4", "commit_訊息": "將5.6.3.2的sql指令調整為5.6.4.1", "提交日期": "2017-07-11 11:51:25", "作者": "張詠威", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_Oracle.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_SQLServer.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 2}, {"commit_hash": "49259625acbdfc1a839fa4d666dba2a11a28be52", "commit_訊息": "修正簽核時更新行事曆紀錄BUG", "提交日期": "2017-07-11 11:17:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e0401b009efc14991196707f8674a7a033bd139c", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-11 11:10:07", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "cd61f81af59ced5d5c291dcde1cebbbae95c5685", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-11 11:09:43", "作者": "張詠威", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c4ff0865b57ffe776d57b0e932d077d943b872cb", "commit_訊息": "增加獨立模組的接口", "提交日期": "2017-07-11 11:09:21", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/RsrcBundleCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LanguageMaintainAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/CustomModuleUserInfoCache.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "0b5e1801d90cb5140612b239eb7639db5fefdb51", "commit_訊息": "調整搜尋行事曆功能", "提交日期": "2017-07-11 11:06:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "49d1b89384f29db9c37a121222a0221dd3d8120f", "commit_訊息": "Q00-20170711001 修正流程內容中如有子流程關卡 , 在點選子流程關卡後跳到子流程流程圖時會出現ClassCastException錯誤", "提交日期": "2017-07-11 10:49:46", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df6da21165f1a54f761cd9a0b7a3e1c13793c391", "commit_訊息": "A00-20170629002 waynechang 修正標準加簽的預覽流程圖異常", "提交日期": "2017-07-11 10:45:27", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9daccb958d0ab5bbc392f07813f16cb107aee249", "commit_訊息": "利用關卡OID取得紀錄行事曆", "提交日期": "2017-07-11 08:21:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "557d15c877775c710cf52784eeb067a7185da08f", "commit_訊息": "修正密榆調整MobileSchedulDTO部分", "提交日期": "2017-07-10 21:07:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3140012e3bc4c680d1ba4f9cac80e06c9613fa6b", "commit_訊息": "調整狀態碼", "提交日期": "2017-07-10 21:05:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed0028516ddf3c4145b269689db924e479af1a93", "commit_訊息": "調整紀錄行事曆新增與修改 合併回MobileScheduleDTO", "提交日期": "2017-07-10 20:56:15", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7189f0df41d5cd1ad33d7c43303dd16e8de56f20", "commit_訊息": "新增功能-簽核時更新行事曆紀錄", "提交日期": "2017-07-10 20:50:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d436618f0118062f66add270d9b0d0168feb2197", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-10 20:49:13", "作者": "pinchi_lin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "fa3078f5b7a279b40ba2633ac0c6d802c2401bb7", "commit_訊息": "合併MobileMPlatformScheduleTool", "提交日期": "2017-07-10 20:48:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "23cf91e80ac790c2b164fb22cacc5f1b8caa6c69", "commit_訊息": "調整MobileScheduleListDTO", "提交日期": "2017-07-10 20:39:20", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fddfa31d5836e99a70cd1d95e6287eac07842b6e", "commit_訊息": "marge", "提交日期": "2017-07-10 20:35:01", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleRecordDTO.java", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 2}, {"commit_hash": "086129ade263112f657c848ce5e27aef80e8dc65", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-10 20:31:19", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "3f0a0469fcf7c781720f7c257a126e6a8bf024f0", "commit_訊息": "marge", "提交日期": "2017-07-10 20:30:44", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "be00a6a94159ef09ac6fede91510c17b120cca26", "commit_訊息": "修正行動簽核中心微信企業號部份問題", "提交日期": "2017-07-10 19:06:04", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c30bda47dcf8bf8d8668af0579aa30ace1aeb73f", "commit_訊息": "修正設定檔中BPMAPP整合平台開關邏輯與預設值", "提交日期": "2017-07-10 18:25:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "68ee4dfea0d7006fee9973f7c15cf86b263144ca", "commit_訊息": "調整行動簽核中心，新增統計元件欄位及互聯Secret", "提交日期": "2017-07-10 18:09:38", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "9b8e22f1adf654d4bbea4f53832b3c4dc582cf72", "commit_訊息": "新增功能-行事曆在新增時同步紀錄對應的workItemOID與時間與行事曆ID到DB內", "提交日期": "2017-07-10 16:54:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformScheduleTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "cddfc76e8d6dbbf77edf7ffa364f37f2238ffb2e", "commit_訊息": "修正行事曆關注資訊與WorkItemOID", "提交日期": "2017-07-10 16:14:36", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3cc77c01697858142edc6adeb10964387596f52d", "commit_訊息": "調整紀錄行事曆 1.domain預計時間與儲存時間為Timestamp 2.日期時間轉換 3.List改為MobileScheduleListDTO 4.MobileScheduleListDTO增加MobileScheduleRecordDTO", "提交日期": "2017-07-10 15:41:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileScheduleRecord.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "e8757c8a8d2c996b0723f2a0aea08211dc13eb6c", "commit_訊息": "調整REST對照Key值,改採物件管理 調整REST統計元件管理 調整REST統計元件設定", "提交日期": "2017-07-10 15:15:52", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/.settings/org.eclipse.core.resources.prefs", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/dataformat/MobileHttpStatusCode.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformDAPService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "e6c79fb7ce8128f6cb083e5ca3c489e5021d7c68", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-10 15:13:44", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "22b801f7690738b17b98250dad4c1b88e9b5d086", "commit_訊息": "修正直連表單JS行事曆BUG", "提交日期": "2017-07-10 14:14:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d9fa7acf4ef64539917c0e3065de5a51e6b311e", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-10 11:27:22", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "16d922d40065e001dd4077bc31f66fc3dd35a0f4", "commit_訊息": "調整紀錄行事曆的DTO&Domain(新增主旨) sessionbean新增利用預約時間搜尋紀錄行事曆", "提交日期": "2017-07-10 10:29:06", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileScheduleRecord.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleRecordDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "6fa5e6b2e7af51d3b28c2315c76f3677ee53fc77", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-10 10:22:54", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "dc2ce4c30fb2bbb0048fe12aad5a35c2d61bf567", "commit_訊息": "調整:5.6.4.1 create&update SQL(紀錄行事曆新增主旨欄位)", "提交日期": "2017-07-10 10:15:04", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "afe91cabb8a42f1d02b1fc54385a1e9aa8f7e0df", "commit_訊息": "5.6.3.2 update SQL製作(移動桌面)", "提交日期": "2017-07-07 16:05:06", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4fbc0f781e2c4673fc9c5621d48fe2b08f1dd3f7", "commit_訊息": "新增紀錄行事曆的DTO與Domain", "提交日期": "2017-07-07 15:29:51", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileScheduleRecord.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleRecordDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileScheduleRecordTool.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "20d6c9f469453534f87928866f24c4a44a44acd8", "commit_訊息": "調整新增行事曆沒有流程主旨時改存關卡名稱與時間 調整新增行事曆增加關注資訊、關卡OID 新增關注資訊與關卡OID欄位", "提交日期": "2017-07-07 15:12:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileScheduleDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "834291561b3c2fc568dd760ce8cdf273dacfaa84", "commit_訊息": "新增紀錄行事曆TABLE", "提交日期": "2017-07-07 15:01:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "59f2661ed1bf8f1e0fb8034664b2a7e5decd760a", "commit_訊息": "企微通訊錄secret與互聯token&URI移至DB維護後，調整企微同步接口與鼎慧推送消息接口使用的參數值初始化", "提交日期": "2017-07-07 14:16:25", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "09d863eb1da03b38a08c658ce93c51acbee2c897", "commit_訊息": "修正:出貨DB InitNaNaDB_ORACLE,關注事項TABLE SQL語法ALTER改為CREATE", "提交日期": "2017-07-06 18:25:20", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f14874cd27dcffee2ef699c29d52270cb89dd258", "commit_訊息": "C01-*********** ESS表單含單身資料會報錯(使用流程維護設定) 調整初始化相關套件", "提交日期": "2017-07-06 17:32:02", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileAppGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "87356dc73960b4ce0aac60f2df7e62c44d3b92d3", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-07-06 11:34:04", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "4691da4631114598b66c1691f94aaabda8c77e79", "commit_訊息": "合并InitMobileDB_Oracle.sql", "提交日期": "2017-07-06 11:18:04", "作者": "jd", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fee2de3bf0222d2265fe405e99778d1afaa02d8a", "commit_訊息": "新增測試互聯平台連線驗證功能(前端與多語系)", "提交日期": "2017-07-05 19:13:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0c13306f2af736793289c2f7ab42bdca91b6b577", "commit_訊息": "新增測試互聯平台連線驗證功能(ajax到service)", "提交日期": "2017-07-05 17:39:55", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "a3d25a571859352d29085593abe6caa68f20c52c", "commit_訊息": "新增統計組件欄位，調整鼎慧取到已結案流程時的提示字", "提交日期": "2017-07-05 17:36:55", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppForm.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "6dba4aaef7e2ab856ce659d82717944941256a3b", "commit_訊息": "調整入口平台管理機制", "提交日期": "2017-07-05 16:46:05", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.4.1_updateSQL_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 13}, {"commit_hash": "433b0c47ba06faa476b6c51a524528a1b539f9a8", "commit_訊息": "新增直連表單session控制功能 新增統計組件功能", "提交日期": "2017-07-05 15:54:17", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0eaacba1052905fe8d52ca83027a61de02ccc0b7", "commit_訊息": "將微信jssdk存到本地端載入", "提交日期": "2017-07-05 09:13:32", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/jweixin-1.2.0.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "2fe0abed0f67f4bc9bb99e617fc8dc82dfdc30e2", "commit_訊息": "新稱鼎慧平台部署工具", "提交日期": "2017-07-05 09:09:43", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAllProcessPkgListReader.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "f9490554f4eb5c8fa4187dc2201b13750a1353dc", "commit_訊息": "Q00-20170704001 修正流程結案時，BPMAPP推送消息會推送兩次問題", "提交日期": "2017-07-04 18:36:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a8854f37420e34db39961161255be62baa08f9ea", "commit_訊息": "A00-*********** 調整Android與iOS顯示落差問題 增加判斷Android手機4.7吋-5.5吋規格的font-size 調整viewport的content增加device-height", "提交日期": "2017-07-04 13:50:28", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "e41614292d7732a88a3cd4f90916f49cb6ba2ddd", "commit_訊息": "修正入口平台整合設定多打符號", "提交日期": "2017-07-04 10:48:07", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8997fb2eb62a5baf0c1f2eee8f223760fc670f55", "commit_訊息": "修正昨日調整之功能BUG(BPMAPP使用微信登入時，若菜單連結未設定語系，則以使用者的電子郵件語系為頁面語系)", "提交日期": "2017-07-04 09:55:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1e8ed7d9d77cb2de7add25f0209a2c4830823d6e", "commit_訊息": "調整入口平台整合設定畫面給予最小寬度", "提交日期": "2017-07-03 17:47:33", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "78ea9b1ba063100ba63e8e64e545aa376741a81e", "commit_訊息": "調整BPMAPP使用微信登入時，若菜單連結未設定語系，則以使用者的電子郵件語系為頁面語系", "提交日期": "2017-07-03 16:59:59", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6acaeefaf04a53a6c57558c73c275daecb979cbf", "commit_訊息": "修正BPMAPP資料庫create&update缺少的的SQL語法(MSSQL)", "提交日期": "2017-06-30 18:25:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b2eeb95c73d13f69957d54418bc1527d38d781b2", "commit_訊息": "修正BPMAPP資料庫create&update缺少的的SQL語法(ORACLESQL)", "提交日期": "2017-06-30 18:23:51", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.2.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a30b9be1f10017d7d6ae0b251e0ff5a3c300626b", "commit_訊息": "修正BPMAPP資料庫create&update缺少的的SQL語法(MSSQL)", "提交日期": "2017-06-30 11:44:41", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.2.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/update/5.6.3.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b6023898649f6c667a28009710953a0c94687476", "commit_訊息": "新增 :關注項目多語系欄位及新增CriticalDefinition方法", "提交日期": "2017-06-30 09:13:55", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/CriticalManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalDefinitionViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalPriorityViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalProcessDefinitionViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "58c0de2084e8eb2ac2133e6f5cbbb55dd461929a", "commit_訊息": "Q00-*********** 修正web表單設計師中行動版腳本問題", "提交日期": "2017-06-29 18:05:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "86a241df758353ddb61210b6fb6ceb3b56014096", "commit_訊息": "修改移動桌面相關table名稱欄位長度(改為255)", "提交日期": "2017-06-29 17:55:56", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f1ebb4f005f623987bfe3e2a3e1ffcd9e6ddd357", "commit_訊息": "修正BPMAPP中，grid維護資料畫面的資料數量提示訊息，更改為grid名稱+資料數量", "提交日期": "2017-06-29 10:23:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileAppGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7a818bb2d22747f50a94164874aa8af5047dc8ed", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-06-28 17:44:30", "作者": "jose<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "881ffa9bd53b909c0e4cf86089a1f2988b981cc0", "commit_訊息": "A00-20170623002 修正:取ESS維護作業權限錯誤,iterator使用錯誤導致", "提交日期": "2017-06-28 17:44:04", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f17b4937c7d7f9ba0cf510c266191cd629221e3e", "commit_訊息": "調整使用者管理，移除編輯格式驗證", "提交日期": "2017-06-28 16:41:26", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e19be849ab53d14f43e1107f60c6b87e60a53830", "commit_訊息": "A00-20170427001 將呼叫Tiptop清理附件交給Queue執行,與開單拆開", "提交日期": "2017-06-28 15:39:42", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/TiptopCleanDocumentBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "32db05d86a49db240833b6b3420d5c66cba7c21f", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-06-28 13:48:56", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "c40eb65dcc09819210ed6c38bcaaf165e0a934bb", "commit_訊息": "修正關注欄位,改為顯示關注提示訊息", "提交日期": "2017-06-28 13:47:25", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3ed59c472da6480a6dd12e848c4dee2abe6794df", "commit_訊息": "SqlInjection議題防範，增加「 OR 」關鍵字判斷", "提交日期": "2017-06-28 11:28:16", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7318ec57b95d16b0911b6c56e8d4afd20471c5ee", "commit_訊息": "C01-20170607002 ESS錯誤代碼機制補充註解說明", "提交日期": "2017-06-28 09:46:12", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99abcb1f3be691f0b740c054322efb0f681e65d7", "commit_訊息": "補上漏掉企業微信回調網址含IP時提示訊息之多語系", "提交日期": "2017-06-28 09:01:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f700ef0006639c3e9c19aa7a51e15cf49f25ecfb", "commit_訊息": "調整BPMAPP顯示流程中的流程圖，將其中的\"檢視詳細流程資訊..\"連結隱藏", "提交日期": "2017-06-27 19:13:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0e7d96c109281a1f08d96dd4a485677a576253dc", "commit_訊息": "移除部分註解", "提交日期": "2017-06-27 14:49:56", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ccda730421e7530c307d68a18e73c144b1d8f5b4", "commit_訊息": "增加判斷企業微信回調網址含IP時顯示提示訊息 BPMAPP聯絡人的手機與信箱功能關閉", "提交日期": "2017-06-27 12:49:23", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "dad41d97ae8e6cf97f3b60d59e85b0c8d6e8cf05", "commit_訊息": "調整使用者管理欄位名稱以及修正部分邏輯", "提交日期": "2017-06-27 11:34:27", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d57d6691f7e1daa6b283b4d5c65f2c519bd749ed", "commit_訊息": "補上使用者管理頁面漏掉的地方", "提交日期": "2017-06-26 18:11:30", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8467da0d09826105711933e8c19b8b05d8329e5a", "commit_訊息": "調整使用者管理頁籤，移除微信開關", "提交日期": "2017-06-26 17:52:01", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ad333a17f3dc89300ac1c73a1c28f7310f2af5e", "commit_訊息": "調整設定檔中BPMAPP整合平台開關", "提交日期": "2017-06-26 17:22:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "33097a04072c1fcee4bf0c703181403eaa6c7a4f", "commit_訊息": "調整行動簽核中心使用者管理介面，新增企業微信部分", "提交日期": "2017-06-26 16:57:37", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "255ae3dceea5f3904c6a5e4d0e8a27c665b11ef8", "commit_訊息": "修正資料選取器與維護樣板SQL異常自動刪除orderby的條件", "提交日期": "2017-06-26 14:55:53", "作者": "<PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a20512a01e36851f01819390136ed845c73cc7ea", "commit_訊息": "行動簽核中心開窗選擇使用者新增聯絡電話", "提交日期": "2017-06-26 13:50:29", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "7439777556682b347c8d0292b17fdd799d5ec476", "commit_訊息": "調整BPMAPP能支援整合企業微信", "提交日期": "2017-06-23 17:29:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/wechat/MobileWeChatService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "e7c07ac1025bd699a83e0ca587f38a99cf639033", "commit_訊息": "Q00-20170623001 修正BPMAPP進入追蹤流程清單時，jboss會報一堆錯誤問題", "提交日期": "2017-06-23 15:16:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "156992adea8c0e2f55446637f850b612ade3a14d", "commit_訊息": "調整使用者管理頁面微信帳號驗證機制", "提交日期": "2017-06-22 17:22:06", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileAppGrid.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f54d63660ff5b0678359a3717722911d9b7ae3be", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-06-22 16:13:44", "作者": "wayne", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "958ed0ca1a48db02bf1ceec910de6370bb5e45e0", "commit_訊息": "調整5632update SQL", "提交日期": "2017-06-22 16:13:13", "作者": "wayne", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "edeb97c3fe6c7b80988cf7fd1a53083f6d912ca7", "commit_訊息": "C01-20170531001 增加卡控ISO文件編碼 編碼規則中的文件階層及文件型態不能為空", "提交日期": "2017-06-22 13:50:52", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/snGenRule.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dee49ee820e865bd6a8eeee05870279b18edc919", "commit_訊息": "ESSTAG增加註寫說明:ESS維護權限、附件參考的Tag", "提交日期": "2017-06-21 18:17:09", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7bd6cc161bd09765df583a421229482bd28f1ca0", "commit_訊息": "C01-20170619002  修正:流程簽核時附件反灰無法點選問題", "提交日期": "2017-06-21 18:11:46", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4bb169c48117ee9e8240c189f3dc76fc2abcdf6b", "commit_訊息": "調整關註欄位維護作業及新增SQL連結及多語系", "提交日期": "2017-06-21 18:05:34", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.2_updateSQL_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 6}, {"commit_hash": "7815e1b0504cf551dbeb6edde7c7293af4949b8d", "commit_訊息": "修正BPMAPP多選客製開窗問題(由V5找出的BUG上修至V6，議題單號:C01-20170601001)", "提交日期": "2017-06-20 14:52:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b31e86c668c29892ed7bea9a4736c92189e0e02", "commit_訊息": "移除行動版Grid調整中測試用的log", "提交日期": "2017-06-20 10:51:00", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "188d981c5089b0aee6509c000b22c79332b9aeb3", "commit_訊息": "調整行動版Grid綁定機制", "提交日期": "2017-06-20 09:43:32", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppGridTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "5905dff301489ea7892963d5d45a87eaecf29997", "commit_訊息": "Q00-20170616001 修正BPMAPP絕對位置與相對位置，若附件有設定權限，表單開不起來問題與附件列表沒有顯示權限設定資訊問題", "提交日期": "2017-06-16 14:55:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "e21a9769113f61f9a311f5d929dc038b41224fde", "commit_訊息": "5.6.3.1_updateSQL_Oracle 修正", "提交日期": "2017-06-16 11:54:16", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.3.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "90fb8784e5f51e64ada2fbf2c225498540fa0d99", "commit_訊息": "新增關注欄位維護作業模組", "提交日期": "2017-06-15 14:35:05", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CannotAccessWarnning.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/UserInfo.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/BpmTable.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/QueryDesinger.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/bootstrap/bootstrap-3.3.5.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/AccessRight.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/BpmTable.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/CustomDataChooser.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/ModalDialog.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/OpenWin.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/QueryTemplate.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrap-3.3.4.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/jquery-1.11.3.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/jqueryUI/jquery-ui-1.11.4.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/jqueryUI/jquery.ui-contextmenu.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/js/json2.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_en_US.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_zh_CN.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/rescBunble/QueryTemplate_zh_TW.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/NewTiptop/NewTiptop.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 42}, {"commit_hash": "1ad311111c3ad170bb7d7499d28db9de6ccd2697", "commit_訊息": "增加json開窗功能", "提交日期": "2017-06-15 14:24:49", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bb4d78b5c52d3169951b72a9ed69209b893cefe5", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-06-15 14:24:24", "作者": "wayne", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "3c7e29c236d1f3e737014f85479a6358368ebdd5", "commit_訊息": "增加json客製開窗功能", "提交日期": "2017-06-15 14:23:32", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/CustomDataChooser.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9536e5c157a401a0ba12e815ff8012fe06c2d155", "commit_訊息": "修正部分LOG內容", "提交日期": "2017-06-14 16:17:10", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cb293b21efe5cc3d04a50ce02c661ee3377e1a20", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-06-14 14:34:29", "作者": "wayne", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "022ce98bc145575d68ea44a23ea2aeedf5c344b1", "commit_訊息": "C01-20170609001 修正同個workitem有多筆轉派紀錄時，批次簽核執行會發生錯誤", "提交日期": "2017-06-14 14:32:39", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "abbaf7344534b2f24bdf2ee48f10748335431630", "commit_訊息": "補上漏修BPMAPP唯讀grid樣式調整 修正鼎慧行事曆指定日期的icon 修正驗證鼎慧平台連線與文字顏色與文字不見問題", "提交日期": "2017-06-14 12:57:43", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6919460dbf2068e09b9ac5636b528a9793abf326", "commit_訊息": "修正相對位置設計器欄位模板、無元件顯示名稱", "提交日期": "2017-06-13 15:56:33", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "67ed8c301ebf2796e06072be25987ebbd1b2c6b7", "commit_訊息": "A00-20170414001 修正動態加簽關卡導致流程圖顯示異常", "提交日期": "2017-06-12 18:11:14", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d65d8bb0fc16497d9e75786cc40e3a957866342d", "commit_訊息": "修正行動版表單RadioButton,CheckBox事件消失問題", "提交日期": "2017-06-09 10:22:49", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32d4c059996c9584846d750e7a61136c4c285ea3", "commit_訊息": "修改LOG內容錯誤", "提交日期": "2017-06-08 18:09:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8693937e6b579b6b673993095e598e2744b3fbd4", "commit_訊息": "C01-20170320001 因表單定義中欄位缺少datatype,導致發起流程及追蹤流程會有錯誤 ,因此判斷datatyep為空時,預設為String型態", "提交日期": "2017-06-08 17:28:34", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TextElementDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4b048f7fd5794159a53e94263201c00e2bb433b6", "commit_訊息": "修正行動簽核中心IE破版問題", "提交日期": "2017-06-08 17:11:00", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "546ac68d7bbedcee7b0f1db9108c4c8fbf52c1ab", "commit_訊息": "BPMAPP的消息推播功能將全部推送改成依流程設計師中有開啟支援行動簽核才進行推送", "提交日期": "2017-06-08 14:30:37", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "575cdfe1ff34ae200dfdfb8ab3c94ef38b5782fe", "commit_訊息": "修正BPMAPP使用連結進入表單畫面後，該筆已結案或終止時會出現工作取回失敗訊息的問題", "提交日期": "2017-06-07 13:45:16", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b59424000b9bad4b6f5d21144e24ec125335c18d", "commit_訊息": "調整BPM App提示訊息", "提交日期": "2017-06-06 13:57:43", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTrace.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "ce85ceb48cc8f249607cf271245ae21439cf9102", "commit_訊息": "調整BPM App外部網址URL", "提交日期": "2017-06-06 13:55:53", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d60927a051775fb0294da579952204ef7649697e", "commit_訊息": "調整BPMAPP表單元件唯讀樣式 調整關注資訊的UI", "提交日期": "2017-06-06 13:41:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b9847120fb787b3a68e52d41bfa5ef043b64fcd1", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-06-05 17:25:01", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "90f5cdf22eb83b0effb7e416b92727a50fda12a1", "commit_訊息": "Q00-20170602002 修正:從Email開啟直接簽核的連結後，按下派送後都失敗", "提交日期": "2017-06-05 17:16:38", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5632.xls", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmCompleteMailStraightSignOff.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmMailStraightSignOffPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "7f4a4e1a20266d58b4480614555870d0e94295d9", "commit_訊息": "Q00-*********** 修正進入微信應用後的被動響應消息失效問題(含使用被動響應消息的查詢發單、查詢人員)", "提交日期": "2017-06-03 16:19:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "4dcd37473f749b06f46c9590a392bfd952f60fa1", "commit_訊息": "調整關注欄位維護作業", "提交日期": "2017-06-03 14:24:36", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/critical/CriticalManagerMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalPriorityDao.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalPriorityDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/critical/CriticalManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "0dd1105d503a9a4f96d4a78f7e8d2f557e0acc0f", "commit_訊息": "A00-20170414001 修正動態加簽關卡流程圖顯示異常", "提交日期": "2017-06-02 15:28:05", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65bbae600ac11b0c7d70771203bb49857901d2f3", "commit_訊息": "調整關注欄位hibernate XML", "提交日期": "2017-06-02 10:08:56", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessDefinition.hbm.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "49a5c81a16445755f525a7c5859e395b585d6d2e", "commit_訊息": "修正工作通知問題", "提交日期": "2017-06-01 16:44:25", "作者": "ChinRong", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "17c42ac95ccb43853baa5da44e4885123c58b880", "commit_訊息": "新增關注欄位維護作業Hibernate", "提交日期": "2017-06-01 14:59:25", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/CriticalManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalConditionDefinition.hbm.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalConditionDefinition.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalDefinition.hbm.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalFocusProcess.hbm.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalFocusProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalPriority.hbm.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalPriority.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessDefinition.hbm.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessHintFields.hbm.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessHintFields.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/hibernate/hibernate.cfg.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/hibernate/spring-bo.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/hibernate/spring-dao.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/BizServiceFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/critical/CriticalManagerMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalDefinitionDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalFocusProcessDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalPriorityDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/CriticalProcessDefinitionDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalDefinitionDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalFocusProcessDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalPriorityDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/critical/hibernate/CriticalProcessDefinitionDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/critical/CriticalManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/critical/CriticalManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/CriticalProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/critical/CriticalProcessDefinitionViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 34}, {"commit_hash": "3e522cffd8851817f168c3c89c5720f76f828bbc", "commit_訊息": "將異常流程單據調整為全部撈取", "提交日期": "2017-06-01 14:24:49", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9798a9f5ac4ecf7e7446509ea9f35a5cb3732e49", "commit_訊息": "A00-20170414001 修正動態加簽關卡流程圖顯示異常", "提交日期": "2017-06-01 14:09:28", "作者": "wayne", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8385a3f7b652d2b3e1c89377dec87461511ba3cf", "commit_訊息": "Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop", "提交日期": "2017-05-31 16:23:59", "作者": "jd", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "bd4558caedfd9d5e6be50a50093eb6f63319cb32", "commit_訊息": "A00-20170525001 修正 :流程關卡中的允許輸入密碼已勾選，但在簽核時並沒有跳出需輸入密碼的訊息", "提交日期": "2017-05-31 16:23:59", "作者": "jose<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d76cbbb5f6f34705423062247fe31719a279f675", "commit_訊息": "修正鼎慧异常问题", "提交日期": "2017-05-31 16:22:52", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b3508aae33ff6311a410b99797d1e6853628f4c4", "commit_訊息": "調整關注資訊顯示內容 調整行事曆前端傳的資料內容", "提交日期": "2017-05-26 16:43:57", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/infoicon.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "49182ab562bd16e2fe5405073a391c4bda006870", "commit_訊息": "修正統計組件樣式問題", "提交日期": "2017-05-26 16:42:14", "作者": "jd", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/MobileRestfulServiceControllerToDo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}