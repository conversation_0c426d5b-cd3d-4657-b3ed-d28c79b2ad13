# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.7.1
- **舊版本**: release_5.8.7.1
- **生成時間**: 2025-07-18 11:21:11
- **新增 Commit 數量**: 5

## 變更摘要

### lorenchang (1 commits)

- **2022-06-26 21:51:38**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.7.1
  - 變更檔案: 25 個

### 林致帆 (3 commits)

- **2021-11-08 17:58:39**: [流程引擎]Q00-20211108002 補上createSQL遺漏監控流程刪除流程設定的參數
  - 變更檔案: 1 個
- **2021-11-02 18:00:22**: [流程引擎]Q00-*********** 修正E10抽單，回傳為流程完成狀態而不是流程撤銷狀態
  - 變更檔案: 2 個
- **2021-11-02 16:58:03**: [E10]S00-20211019003 新增 E10(不驗證表單)的回寫接口
  - 變更檔案: 3 個

### cherryliao (1 commits)

- **2021-10-28 11:00:44**: [E10]S00-*********** 新增E10流程終止或撤銷時，若單據修改且重新送審後於BPM表單可查看之前審批流程的功能
  - 變更檔案: 8 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.7.1
- **Commit ID**: `5960b021d52071a6f06019b9f27e3c39694a2048`
- **作者**: lorenchang
- **日期**: 2022-06-26 21:51:38
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [流程引擎]Q00-20211108002 補上createSQL遺漏監控流程刪除流程設定的參數
- **Commit ID**: `35e85cd695735f923670c1e875dd1d74b0037d20`
- **作者**: 林致帆
- **日期**: 2021-11-08 17:58:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 3. [E10]S00-*********** 新增E10流程終止或撤銷時，若單據修改且重新送審後於BPM表單可查看之前審批流程的功能
- **Commit ID**: `66964bace671b384a660654d6f981a9c3a326f45`
- **作者**: cherryliao
- **日期**: 2021-10-28 11:00:44
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/IPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/OJBPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java`

### 4. [流程引擎]Q00-*********** 修正E10抽單，回傳為流程完成狀態而不是流程撤銷狀態
- **Commit ID**: `9b21cd295187d1638a3ac5395f9461836737535f`
- **作者**: 林致帆
- **日期**: 2021-11-02 18:00:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerLocal.java`

### 5. [E10]S00-20211019003 新增 E10(不驗證表單)的回寫接口
- **Commit ID**: `b5c6ca4a26f04ce0c7fedd9dfdfd93fd93fab2e8`
- **作者**: 林致帆
- **日期**: 2021-11-02 16:58:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10Manager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerLocal.java`

