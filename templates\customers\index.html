{% extends "base.html" %}

{% block title %}{{ page_title }} - BPM服務部好用工具{% endblock %}

{% block extra_css %}
<style>
    .customer-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border: 1px solid #e0e0e0;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        cursor: pointer;
    }
    
    .customer-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .customer-id {
        font-family: 'Courier New', monospace;
        font-size: 0.8rem;
        color: #6c757d;
        background: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        display: inline-block;
    }
    
    .product-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        margin-right: 0.5rem;
    }
    
    .product-badge.bpm {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .product-badge.hrm {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    
    .product-badge.tiptop {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    
    .product-badge.other {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .search-box {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fas fa-building me-3"></i>
                {{ page_title }}
            </h1>
            <p class="page-subtitle">管理客戶公司的連線資訊</p>
        </div>
    </div>

    <!-- 統計資訊 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <h3 class="mb-2">{{ total_customers }}</h3>
                <p class="mb-0">總客戶數</p>
            </div>
        </div>
        <div class="col-md-9">
            <div class="search-box">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="搜尋公司名稱、產品類型或伺服器IP...">
                    </div>
                    <div class="col-md-4">
                        <a href="/customers/add" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>新增客戶
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 客戶列表 -->
    <div class="row" id="customerList">
        {% if customers %}
            {% for customer in customers %}
            <div class="col-lg-6 customer-item" 
                 data-company="{{ customer.company_name|lower }}" 
                 data-product="{{ customer.product_type|lower }}" 
                 data-ip="{{ customer.server_ip }}">
                <div class="customer-card" onclick="viewCustomer('{{ customer.oid }}')">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h5 class="mb-1">{{ customer.company_name }}</h5>
                            <div class="customer-id">{{ customer.oid[:8] }}...</div>
                        </div>
                        <div class="product-badge {{ customer.product_type|lower }}">
                            {{ customer.product_type }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">伺服器IP</small>
                            <div class="fw-bold">{{ customer.server_ip }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">資料庫</small>
                            <div class="fw-bold">{{ customer.database_name }}</div>
                        </div>
                    </div>
                    
                    {% if customer.description %}
                    <div class="mb-3">
                        <small class="text-muted">說明</small>
                        <div>{{ customer.description[:50] }}{% if customer.description|length > 50 %}...{% endif %}</div>
                    </div>
                    {% endif %}
                    
                    <div class="action-buttons" onclick="event.stopPropagation()">
                        <a href="/customers/detail/{{ customer.oid }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>詳情
                        </a>
                        <a href="/customers/edit/{{ customer.oid }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>編輯
                        </a>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteCustomer('{{ customer.oid }}', '{{ customer.company_name }}')">
                            <i class="fas fa-trash me-1"></i>刪除
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">尚無客戶資料</h4>
                    <p class="text-muted">點擊上方的「新增客戶」按鈕開始建立客戶連線資訊</p>
                    <a href="/customers/add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>新增第一個客戶
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- 沒有搜尋結果時顯示 -->
    <div class="row d-none" id="noResults">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">沒有找到符合條件的客戶</h4>
                <p class="text-muted">請嘗試其他搜尋關鍵字</p>
            </div>
        </div>
    </div>
</div>

<!-- 刪除確認模態框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">確認刪除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您確定要刪除客戶「<span id="deleteCustomerName"></span>」嗎？</p>
                <p class="text-danger small">此操作無法復原。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">確認刪除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let deleteOid = null;

    // 搜尋功能
    $('#searchInput').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterCustomers(searchTerm);
    });

    function filterCustomers(searchTerm) {
        const customerItems = $('.customer-item');
        let visibleCount = 0;

        customerItems.each(function() {
            const company = $(this).data('company') || '';
            const product = $(this).data('product') || '';
            const ip = $(this).data('ip') || '';
            
            const isVisible = company.includes(searchTerm) || 
                            product.includes(searchTerm) || 
                            ip.includes(searchTerm);
            
            if (isVisible) {
                $(this).show();
                visibleCount++;
            } else {
                $(this).hide();
            }
        });

        // 顯示或隱藏「沒有結果」訊息
        if (visibleCount === 0 && searchTerm !== '') {
            $('#noResults').removeClass('d-none');
        } else {
            $('#noResults').addClass('d-none');
        }
    }

    // 刪除客戶
    window.deleteCustomer = function(oid, companyName) {
        deleteOid = oid;
        $('#deleteCustomerName').text(companyName);
        $('#deleteModal').modal('show');
    };

    $('#confirmDelete').click(function() {
        if (deleteOid) {
            $.ajax({
                url: `/customers/delete/${deleteOid}`,
                method: 'POST',
                success: function(data) {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('刪除失敗：' + data.error);
                    }
                },
                error: function() {
                    alert('刪除時發生網路錯誤');
                }
            });
        }
        $('#deleteModal').modal('hide');
    });

    // 查看客戶詳情
    window.viewCustomer = function(oid) {
        window.location.href = `/customers/detail/${oid}`;
    };
});
</script>
{% endblock %}
