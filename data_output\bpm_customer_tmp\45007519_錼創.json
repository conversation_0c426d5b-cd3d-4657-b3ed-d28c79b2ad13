{"company_id": "45007519", "company_name": "錼創", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\45007519-錼創\\01客戶基本資料", "files": [{"filename": "錼創.txt", "raw_content": "====20240918客戶提供VPN====\r\n\r\nFortiClient\r\n*************:10443\r\nvpn帳號：DSC_BPM\r\nvpn密碼：S6cMPi5j\r\n\r\n====20240918客戶提供VPN====\r\n\r\n遠端方式:鼎新雲管家\r\n\t\r\n正式機( ******* ):*************\r\nBPM網頁帳密:administrator / Pl@yBpm#2023\r\nhttp://*************:8086/NaNaWeb\r\n\t\r\n正式機DB:*************\r\n名稱/帳號/密碼:\tBPM / sa / sql#DSC\r\n\t\r\n\r\n測試機( ******* ):*************\r\nOS帳號：administrator/密碼：P@ssw0rd\r\nBPM網頁帳密:administrator / 1234\r\nhttp://*************:8086/NaNaWeb\r\n\r\n測試機DB:*************\r\n名稱/帳號/密碼:\tBPMTEST / sa / sql#DSC\r\n", "structured_data": {"vpn帳號": "DSC_BPM", "vpn密碼": "S6cMPi5j", "os帳號": "administrator/密碼：P@ssw0rd", "*************": "10443", "遠端方式": "鼎新雲管家", "正式機( ******* )": "*************", "bpm網頁帳密": "administrator / 1234", "http": "//*************:8086/NaNaWeb", "正式機db": "*************", "名稱/帳號/密碼": "BPMTEST / sa / sql#DSC", "測試機( ******* )": "*************", "測試機db": "*************", "20240918客戶提供vpn": "===", "host": "*************"}, "source_path": "C1.客戶維護相關\\45007519-錼創\\01客戶基本資料\\錼創.txt", "file_size": 543, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:31.047694"}], "total_files": 1, "processed_at": "2025-08-26T10:46:31.047703"}