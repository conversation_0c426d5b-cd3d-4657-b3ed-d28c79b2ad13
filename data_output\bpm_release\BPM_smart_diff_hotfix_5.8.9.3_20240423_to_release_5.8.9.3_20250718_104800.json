{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.3_20240423", "date": "2024-03-05 14:24:48", "message": "[TIPTOP]Q00-20240305001 修正簽核歷程頁該使用者登入後無權限查看時，會造成授權人數被占據", "author": "林致帆"}, "舊分支": {"branch_name": "release_5.8.9.3", "date": "2023-08-23 13:15:30", "message": "[EBG]S00-20230808002 新增EBG電子簽章專案 [補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 10:48:00", "新增commit數量": 209, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "3d531171e290ba0cdf211a545d22805eba23de9b", "commit_訊息": "[TIPTOP]Q00-20240305001 修正簽核歷程頁該使用者登入後無權限查看時，會造成授權人數被占據", "提交日期": "2024-03-05 14:24:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c0f6c6d2abcd8bb2cfb2cfab617755fed7071c81", "commit_訊息": "[Web]Q00-20240412003 修正主旨顯示為編碼後的內容", "提交日期": "2024-04-12 16:15:04", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/StringUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "b6d5210f14770ea9e9f1606e28dba62c1d18fc2b", "commit_訊息": "[Web]Q00-20240412001 修正grid 設定table 模式，有很多個欄位會擠一起的情況", "提交日期": "2024-04-12 10:05:56", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9998b8b2a8755bdafc89a08cd5116b44a7264c7", "commit_訊息": "[Web] Q00-20240410002 從5894版到58101後，Grid 欄位框線不會對齊,將調整寬度的script註解掉，Grid的欄位框線就可正常對齊问题修正", "提交日期": "2024-04-10 18:21:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "56cae3fa65a5320418dc7d6aa32c7398fb6efceb", "commit_訊息": "[Web] V00-20240402002 流程发起时选择流程重要性为：紧急，但是在待办事项列表中没有出现红色标记列问题修正", "提交日期": "2024-04-03 14:01:05", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "59e2ae578e3adcbc0003c7aa63a8b4ec04a5b44e", "commit_訊息": "[Web]Q00-20240402001 修正[報表維護作業]產出的報表畫面欄位异常", "提交日期": "2024-04-02 16:59:55", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75ae779a0d081c09c18a977ce9de48674deabeb3", "commit_訊息": "[Web] Q00-20240319001 修正Grid勾选table模式显示异常问题[补]", "提交日期": "2024-03-25 17:55:20", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "705374e21036bd6d4f0b81aba10c09d50105884d", "commit_訊息": "[Web]Q00-20240313002 修正[ID]Obj.setColumnwidth(\"＜GridcolumnID＞\",100);写法没有效果 [补]", "提交日期": "2024-03-25 14:46:38", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "41853654166abde51485035603e4e3c77175277a", "commit_訊息": "[Web] Q00-20240319001 修正Grid勾选table模式显示异常问题", "提交日期": "2024-03-19 10:09:35", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e62121e1c030f21b307ddeea3097bf6df744990e", "commit_訊息": "[WEB]Q00-20240313002 修正[ID]Obj.setColumnwidth(\"＜GridcolumnID＞\",100);写法没有效果", "提交日期": "2024-03-13 15:22:29", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "26288947bba3ca5aae7e0f1344aaa290f8864b0a", "commit_訊息": "[Web] V00-20240312003 修正页面列表栏位width和出现滚轴问题", "提交日期": "2024-03-13 14:24:14", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9956c47eb7d834d5b78fea72dfbe20d2a99ab59", "commit_訊息": "[Web] Q00-20240201003 修正汇入excel时出现undefined(reading toString)异常信息", "提交日期": "2024-02-01 15:25:55", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c982983b926fe9ff189bb0778ed7839f54a8136", "commit_訊息": "[雙因素模組]Q00-20240409003 信任端點裝置時間修正為24小時制", "提交日期": "2024-04-09 15:32:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4b8426bedc56260cbdabbe080453561114eb3e8f", "commit_訊息": "[雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶 [補修正]", "提交日期": "2024-04-10 08:56:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "59f7279ba592c145adef2e650b5870827ea2b0e2", "commit_訊息": "[雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶", "提交日期": "2024-04-09 17:15:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dab51fdf62df564be9402d6a7cb388454f64cce9", "commit_訊息": "[雙因素模組]Q00-20240202001 修正信任端點資訊有過期資料會造成每次登入都需重複驗政", "提交日期": "2024-02-02 14:36:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc0c8f97b57cf133b8c4ed55d0f5706aeba0d1d7", "commit_訊息": "[雙因素認證]Q00-*********** 修正LDAP登入輸入帳號錯誤不該影響登入畫面進錯誤頁面", "提交日期": "2023-11-01 14:04:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ceff8255f2afde48e5303b049a514b07a12713b2", "commit_訊息": "[Web]Q00-*********** 修正attachment物件OID为空时，会抛错[此URL没有下载文件的权限]的问题", "提交日期": "2024-04-09 16:17:15", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9377c0428bc90d2d2d4e77bbec15d0627abb9fe4", "commit_訊息": "[資安]V00-*********** 修正登入任一使用者可透過URL下載附件", "提交日期": "2023-11-10 17:07:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c3a74653f043af9a68c2cf8ec78812593c1e8e0e", "commit_訊息": "[Web] Q00-20240403002 Grid單身欄位加總異常问题修正", "提交日期": "2024-04-03 16:22:52", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c8986e0dac92b497a69c900e96b73a49a31eb53", "commit_訊息": "[Web]Q00-20240401001 修正XPDL简易流程图显示主旨异常", "提交日期": "2024-04-01 11:41:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7fc681fb930845491280e0b4edcc020de56dfb42", "commit_訊息": "[PRODT]Q00-20240301001 修正Web流程管理工具中關卡處理者有髒資料時無法開啟流程的問題[補]", "提交日期": "2024-03-01 15:42:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0d980879956a7696b7cf67506a0b045b8be5ebfe", "commit_訊息": "[PRODT]Q00-20240301001 修正Web流程管理工具中關卡處理者有髒資料時無法開啟流程的問題", "提交日期": "2024-03-01 13:42:50", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e1ed11db78b67bcd4ecc9a184463965214261c79", "commit_訊息": "[Web]Q00-20240226003 调整改变浏览器视窗大小后，客制JSP排序功能失效的问题", "提交日期": "2024-02-26 15:59:45", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "94b877f8f6c12f5cfb4888e2abfa8b78a03124f9", "commit_訊息": "[Web]Q00-20240221006 修正Grid的新增，修改，删除Button设定背景色或文字颜色，汇入时颜色样式消失的问题", "提交日期": "2024-02-21 14:59:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7d0ef586271c351235a2c9afb8c7a5dd11ae60b0", "commit_訊息": "[PRODT]Q00-20231206001 修正Web流程管理工具中流程樣板的流程簽入後會跑到其他流程分類下的問題", "提交日期": "2023-12-06 17:56:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "125fa8a59d5495c6f48b1cb22a29f81ffb63ae68", "commit_訊息": "[DT]V00-20231115002 修正流程管理工具活動中的通知設定儲存後不顯示問題", "提交日期": "2023-11-17 11:35:16", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6cf10fc82ae32f56ace63a9ae151f62bac81f967", "commit_訊息": "[DT]V00-20231110004 修正Q00-20230926003修正錯誤的問題", "提交日期": "2023-11-14 16:32:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "50475b789a30564730bd2f100e7e9bae9a524394", "commit_訊息": "[DT]Q00-20231107002 修正Web化流程管理工具中活動定義編輯器內表單及附件的模式調整儲存後不會變更的問題", "提交日期": "2023-11-07 13:55:00", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e154edb55de40722127be65eefb8aa8febdf4f29", "commit_訊息": "修正若通知關卡進追蹤畫面時，會提示「查詢不到此流程的資料」也無法顯示表單，以及頁面詳細流程點通知關卡的活動實例也無法顯示任何工作事項", "提交日期": "2024-02-02 14:00:52", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "96d525a36c3e9cfddb42f4eb4646bc1c92e26733", "commit_訊息": "Revert \"[流程引擎]Q00-20240201005 修正5894版本，若通知關卡是透過追蹤流程進入表單畫面時，會提示「查詢不到此流程的資料」也無法顯示表單畫面；以及追蹤頁面詳細流程點通知關卡的活動實例也無法顯示任何工作事項\"", "提交日期": "2024-02-02 11:56:20", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d303c3d87bb56a52a48d13747ebe87bc95faabff", "commit_訊息": "[流程引擎]Q00-20240201005 修正5894版本，若通知關卡是透過追蹤流程進入表單畫面時，會提示「查詢不到此流程的資料」也無法顯示表單畫面；以及追蹤頁面詳細流程點通知關卡的活動實例也無法顯示任何工作事項", "提交日期": "2024-02-01 17:21:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a7967330b14afcc1237fcf03f72cfb22781fba9f", "commit_訊息": "[流程引擎]Q00-20240123002 因流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制[補]", "提交日期": "2024-01-31 15:52:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "053a778ef1d2ba0b8231ea17b7026f2a886db03d", "commit_訊息": "[流程引擎]Q00-20240131002 流程進版或匯入時，增加檢核流程的關卡連接線的From關卡及To關卡是否存在關卡定義中，若From 或 To 對應的關卡不存在時，將其刪除，避免影響流程運作", "提交日期": "2024-01-31 15:05:53", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3453d81fea1f99ce209f32b69e65bad478a6b7b3", "commit_訊息": "[內部]Q00-20240125001 調整清除二階快取的log層級由warn改為debug", "提交日期": "2024-01-25 13:48:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82c74d2b174440557805eb2ccfef3cc8844195e9", "commit_訊息": "[Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题[补]", "提交日期": "2024-01-30 10:58:06", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c221c8e2267fed128f699c79e67f6c78b645409", "commit_訊息": "[Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题", "提交日期": "2024-01-30 09:49:48", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b2f0985abb242e2100280fabb4af1ad0893728f0", "commit_訊息": "[Web]Q00-20240129002 修正客制JSP文件，行点击事件onRowClick报错的问题", "提交日期": "2024-01-29 16:52:18", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "942f79c6f10d595b5e6109c89f8c3a55846716f4", "commit_訊息": "[Web]Q00-20240116003 修正当单身内容长度过大setColumnWidth没有效果的问题", "提交日期": "2024-01-16 13:30:29", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "313018b26134a66772b4e18def03eb8f3b6c5ebf", "commit_訊息": "[Web] Q00-20240103001 修正grid设置冻结栏位后设置样式显示错误", "提交日期": "2024-01-03 13:57:55", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "822120636c314d13f819e74d9063b4c49c0874d1", "commit_訊息": "[Web] Q00-20231207002 excel汇入资料到Grid中，单身加总不计算", "提交日期": "2023-12-07 10:32:40", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b4220073030f24be4972b0c4e0d497ac21adc315", "commit_訊息": "[流程引擎]Q00-20240124003 優化流程在簽核後取活動定義關聯資料的機制", "提交日期": "2024-01-24 18:37:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "548e0f413b8c88284820e05410464e082e5be005", "commit_訊息": "[流程引擎]Q00-20240123003 修正進入待辦或追蹤頁面，且流程進入核決關卡時，若核決關卡定義有髒資料或多組相同代號時可能會導致開啟畫面錯誤，因此增加過濾髒資料的邏輯", "提交日期": "2024-01-23 15:01:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cbcbebfbbcebb20c88bc2748029051ce9f36063e", "commit_訊息": "[流程引擎]Q00-20240123002 流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制[補為了編釋]", "提交日期": "2024-01-24 14:11:44", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a9ce908a69b52bdb70f848420bbd34579af37f79", "commit_訊息": "[流程引擎]Q00-20240123002 流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制[補為了編釋]", "提交日期": "2024-01-24 14:11:14", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa47ebaa194e0a9af7700655777da9da7acf7643", "commit_訊息": "[流程引擎]Q00-20240123002 流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制", "提交日期": "2024-01-23 14:41:22", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "abea894c9178443ce47dd61c994772356ea7f835", "commit_訊息": "[Web] Q00-20240117002 修正通知信进入待办阅读次数加2", "提交日期": "2024-01-17 11:10:22", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b39731a2cf2f7641dea30efa4ebeabeb4c03082e", "commit_訊息": "[BPM APP]Q00-20240118003 修正IMG操作進入開窗頁面後回到表單畫面時沒有顯示左上方的返回按鈕問題", "提交日期": "2024-01-18 14:21:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "141cff8548fd7c58a320ccf128831a85da36d714", "commit_訊息": "[BPM APP]Q00-20230927001 修正行動端的客製開窗不會根據篩選條件搜尋問題", "提交日期": "2023-09-27 14:08:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d29b0b104012808830696b9c3d43b1b940c7c1d", "commit_訊息": "[Web]Q00-20240117001 修正追蹤流程列表中任一流程有顯示關注訊息，手機模式下點擊流程就沒有反應的問題", "提交日期": "2024-01-17 10:38:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eee24ca539c8585e6d09ae7f7ae60d4e0ae9c10b", "commit_訊息": "[Web] Q00-20240108001 調整絕對位置表單進版時，部分表單欄位反黑異常問題，新增防呆", "提交日期": "2024-01-08 11:52:45", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4b9a1ac5d610cd7c9f819b55f824f268483067f6", "commit_訊息": "[SAP]Q00-20240104002 修正SAP整合，当Grid更新或新增固定值栏位时，只会保留本次更新之前栏位固定值会被删掉的问题", "提交日期": "2024-01-04 17:30:37", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "987c37190b44d4a8903f9a740586943f4076324e", "commit_訊息": "[Web] Q00-20240103002 修正checkbox绑定其他checkbox元件，存储表单后无法更改绑定的元件", "提交日期": "2024-01-03 17:41:38", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "27bfa164dee99a760f6d412cfc832d06b62ac97a", "commit_訊息": "[Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正-2)", "提交日期": "2024-01-03 17:06:02", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "54fb1304e108444b7418a82d2ab4a9930ce32f74", "commit_訊息": "[ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題", "提交日期": "2023-12-28 18:09:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c55081324ab64c6b016a2666a89367fc5f727218", "commit_訊息": "Revert \"[ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題\"", "提交日期": "2024-01-03 16:24:38", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "09fe532b4d8ac594f05548c1b49775bf1c647d46", "commit_訊息": "[ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題", "提交日期": "2023-12-28 18:09:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "147fb084991254b73af63c9437e441bd665e4cee", "commit_訊息": "[Web] Q00-20240102006 調整部分系統變數無須重啟即生效", "提交日期": "2024-01-02 17:05:30", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "06c0f3fabdff698df4f279fb2a883054eccc4948", "commit_訊息": "[Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正)", "提交日期": "2024-01-02 15:14:28", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e956ea4436d9476ad8d88799f2b5e81805d7f8fb", "commit_訊息": "[Web]Q00-20231229003 调整\"追蹤\"“監控”使用表单自适应宽度調整書面寬度無效果的問題", "提交日期": "2023-12-29 13:58:54", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e204ad13d4ff42c9e5ba5a308e8dcbdce0091211", "commit_訊息": "[Web]Q00-20231229002 调整个人资讯-->表单自适应宽度slider预设值为“较宽”", "提交日期": "2023-12-29 13:33:58", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "309cca01cf072921ce8b2d6ae18a24a3e47600c0", "commit_訊息": "[Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常", "提交日期": "2023-12-29 10:57:45", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bacaca6950e2a705d9b384ac29b320ed47f3bd06", "commit_訊息": "[web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正[补修正]", "提交日期": "2023-12-06 13:18:15", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4ef440e5b32b3fe6bce9b9039a9e0159b6cbc91", "commit_訊息": "[Web] Q00-20231213002 不同模组下作业名称相同，导航页显示异常", "提交日期": "2023-12-13 13:22:56", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8f278c9bcb6fe64c31140dcaa949cc3ef1f62a42", "commit_訊息": "[Web]Q00-20231215004 建立登入or登出記錄物件資料request为空时新增防呆[补修正]", "提交日期": "2023-12-21 13:28:33", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "82a5c41ee5d64ca323881f1af709ac0d9426255a", "commit_訊息": "[web] Q00-20231220003 當user使用Android手機、並有調整「字型大小」時，登入網頁的綁定畫面-QRCode會跑版問題修正", "提交日期": "2023-12-20 11:26:28", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bcd84d623b7bd36fb05329e3905a107f6d183bbd", "commit_訊息": "[BPM APP]Q00-20231220001 修正LINE綁定LDAP帳號時會出現使用者帳號不存在的問題", "提交日期": "2023-12-20 10:17:30", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "35e299bac3759524b72474206751af62b7c46e67", "commit_訊息": "[Web]Q00-20231219001 調整系統設定LDAP登入時，登入畫面帳號欄位提示訊息的多語系問題", "提交日期": "2023-12-19 10:35:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0a699fd913c0c8bba57fd054e0a87ee1379194f0", "commit_訊息": "[Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題(補)", "提交日期": "2023-12-18 11:30:10", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b89e232e0238f5ef9d14dc4f6de24a59002a4813", "commit_訊息": "[Web]Q00-20231215004 建立登入or登出記錄物件資料request为空时新增防呆", "提交日期": "2023-12-15 17:52:46", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a4c53178fdce80321ba6115224bbcde18764ae1", "commit_訊息": "[Web] Q00-20231215003 由url链接进入待办，清除wms_user_isURL的session", "提交日期": "2023-12-15 14:46:41", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cff4e1ac869538af1b79b5fc61c3a368439d5644", "commit_訊息": "[雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證[補修正]", "提交日期": "2023-12-06 11:13:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7b11b6ea4d58b656e230f37d798e9ccda9f8f14", "commit_訊息": "[Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題(補)", "提交日期": "2023-12-15 10:12:50", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "376865ad374844b78d2a69cfcbdba5e9097e0392", "commit_訊息": "[Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題", "提交日期": "2023-12-15 10:08:52", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a0a6b8c67187ac682ee9ac19c3ede5cd9ce2f41b", "commit_訊息": "[PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題", "提交日期": "2023-12-14 18:37:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79a5417d59f0a0303723514d271c864a7bf4e8c5", "commit_訊息": "[SAP]Q00-20231213003 修正SAP整合服務，當回傳的資料類型為絕對位置表單Grid時，可能會有GridColumnId與GridValue順序錯誤的異常", "提交日期": "2023-12-13 15:44:25", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f85e66194359769e03d0862aaaab8d647de7ed1", "commit_訊息": "[Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題", "提交日期": "2023-12-13 13:59:44", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "49b50db99dc42f270482c85565a45aef5077c610", "commit_訊息": "[PRODT]Q00-20231212002 修正Web流程管理工具中流程徹銷中的sessionBean點編輯呈現空白的問題", "提交日期": "2023-12-12 15:11:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ef716bcec5f10b7c656b2b8e7f71ffc637a3839", "commit_訊息": "[Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項", "提交日期": "2023-12-05 15:26:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de7205492e207112225c537e7fafb1fe72197121", "commit_訊息": "[流程引擎]S00-20230130001 調整信件區分流程轉派給代理人", "提交日期": "2023-09-07 14:14:10", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ad3e6408877610e824f35499fa10871da59bee08", "commit_訊息": "Revert \"[Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項\"", "提交日期": "2023-12-05 17:35:05", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd232702777d67ef7d89e247798a4d0ae9190aaa", "commit_訊息": "[Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項", "提交日期": "2023-12-05 15:26:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f02d54ef6a29a84725e4d1ed15f086c79d4112a", "commit_訊息": "[Web]Q00-20231205004 修正待办事项中选择锁定工具列后缩小视窗表单会被部分遮挡的问题", "提交日期": "2023-12-05 14:47:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a2eaec4c2737f50c2c92c8ee579e2bfcd278e68c", "commit_訊息": "[web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正", "提交日期": "2023-12-05 13:49:48", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1902f7615a9f5a53235ed484d0552b445776821e", "commit_訊息": "[PRODT]Q00-20231204006 修正Web流程管理工具中應用程式管理員的網頁應用程式或session bean新增呼叫參數時自訂id儲存後會變預設值的問題", "提交日期": "2023-12-04 18:52:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/IDGen.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "cd5e88abebf51acb4d4e4d1b794e7256e020f79a", "commit_訊息": "[Web] Q00-20231204005 修正BPM授權數不足時，寄件人並非系統管理中的設定", "提交日期": "2023-12-04 17:13:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22e965b2b97fca3ea9162a8c59ab451d5a7acf1f", "commit_訊息": "[Web]Q00-20231204003 修正流程主旨、列印模式下grid资料有&#加任意數字，被轉成特殊符號的问题", "提交日期": "2023-12-04 15:48:40", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/StringUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7659dec8f6e2170324e1cdcc684a3eb785907a62", "commit_訊息": "[流程引擎] Q00-20231204002 修正退回重辦系統通知變數未被正常置換問題", "提交日期": "2023-12-04 14:10:17", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "411b049f6f55e7700f68d42fd560460f914d9768", "commit_訊息": "[Web]Q00-20231201004 调整ipad Safari浏览器經常選取人員为默认全选", "提交日期": "2023-12-01 10:59:07", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "923eb7bb119d3846e0619bc5255f428db89bde4b", "commit_訊息": "[流程引擎]Q00-20231127001 修正關卡設定多人都要簽核且設定自動簽核2，與前一關相同者，若前一關為核決關卡，且未實際展開核決關卡時，流程無法派送至下一關的異常", "提交日期": "2023-11-27 15:20:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "87c5b575b15ef151dc8e98ed94225e720984e6e6", "commit_訊息": "[Web] Q00-20231129005 修正serialNumber栏位显示问题", "提交日期": "2023-11-29 16:44:03", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a09427353debc146149389ef54cfbe8f64927db0", "commit_訊息": "[Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题[补修正]", "提交日期": "2023-11-29 16:55:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8b28dbdbabc132bd1b4f5e35bfdf790c841a446", "commit_訊息": "[Web] Q00-20231129004 修正從追蹤連結進入已關注的愛心不會亮，該流程原本已被關注，但從追蹤連結進入後不會亮，須等切換到表單葉面後才會亮", "提交日期": "2023-11-29 15:53:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d18e241f8537d55e6109258304c56704cbfc4393", "commit_訊息": "[Web]Q00-20231128006 调整grid標頭固定显示[补修正]", "提交日期": "2023-11-29 14:16:33", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b63d653cc673c57962d3e1eef3df13cd4b64d2a8", "commit_訊息": "[Web]Q00-20231129002 调整個人資訊页多个提示訊息显示不完整的问题", "提交日期": "2023-11-29 12:17:02", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageSimpleUserProfile.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "bccb9ffb3be811609a87fec570310cada1c0726e", "commit_訊息": "[Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题", "提交日期": "2023-11-29 11:43:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8361ef7c5a89019f0510e3244f092b9bb5c0878d", "commit_訊息": "[Web]Q00-20231129001 調整在行動裝置撤銷流程時填寫撤銷意見會被選單擋住的問題", "提交日期": "2023-11-29 09:48:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1b9b2b9706398f6581121c54b50375c531ab1979", "commit_訊息": "[Web]Q00-20231128006 调整grid標頭固定显示", "提交日期": "2023-11-28 16:17:09", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a854c689dcb0e2f3fdbfa13e948b97ef2a872b6", "commit_訊息": "[ESS]Q00-20231128003 調整缺席紀錄方法相容帶有單身資料的ESS單據", "提交日期": "2023-11-28 13:35:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bdced4f5bcd943a27bfc7bef42b9baf5881bbef3", "commit_訊息": "[PRODT]Q00-20231128001 修正在開啟流程管理工具後主畫面的標題一併被更動問題", "提交日期": "2023-11-28 10:02:48", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3bd6c06767301abf8c2beb8e4dad319ca760d84c", "commit_訊息": "[Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊", "提交日期": "2023-11-27 16:31:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4e64e6b096049bac0e4b1af491f9f9c94b648c75", "commit_訊息": "[雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證", "提交日期": "2023-11-24 10:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/TFAModule/TFASetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "12794aeb0935e8ac9bcab6a3cb754da111621a2c", "commit_訊息": "[雙因素模組]Q00-*********** 修正未放入不驗證清單的使用者若未綁定，登入後不會跳出需綁定的提示視窗", "提交日期": "2023-11-10 17:14:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TFAConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fb2fdbc80ec726b09c0f5317a607362981253faf", "commit_訊息": "[SAP]Q00-*********** 優化SAP整合服務，當整合的資料類型為Grid時，同時支持RWD表單Grid及絕對位置表單Grid", "提交日期": "2023-11-24 17:33:54", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "85dab06d05dc84278cac8c80c35eaf4380b731c2", "commit_訊息": "[流程引擎] Q00-*********** 修正批次簽核造成重複寄信問題", "提交日期": "2023-11-24 15:00:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "efce9628ee7aadf3cde9624c0e5e1ccc23275fe0", "commit_訊息": "[Web] Q00-20231124002 修正流程主旨範本設定<#workItemName>显示N.A.", "提交日期": "2023-11-24 10:33:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "31b92e45992c953a6d5e655723416d2a44f0fccf", "commit_訊息": "[Web]Q00-20231124003 修正Grid某一格或某一行设置样式，点击排序后样式消失的问题", "提交日期": "2023-11-24 10:30:47", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba041c73de02792744906128f0fb019c23aec251", "commit_訊息": "[Web] Q00-20231124001 修正附件元件每個檔案容量限制設定成104857600 kb,無法上傳附件", "提交日期": "2023-11-24 10:24:42", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/DisplayLabelUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8e40d6171454165dcf82c803f2572475a00d3a64", "commit_訊息": "[DT]Q00-20231122002 修正系統權限管理員中可存取的範圍設定權限範圍(部門/專案主管)後儲存會報後端接口調用失敗的問題", "提交日期": "2023-11-22 20:01:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2bad621e1d1512b13d7156bde1608bb42d9f212c", "commit_訊息": "[DT]Q00-20231121006 修正在Web化流程管理工具中活動參與者組織相關找不到離職日設定當天的使用者的問題", "提交日期": "2023-11-21 16:14:16", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7b08c25a6b37b750f257968323abdd88b674e20", "commit_訊息": "[SAP]Q00-20231121001 修正SAP整合，當mapping內容有Grid時，可能會有GridColumnId與GridValue順序錯誤的異常", "提交日期": "2023-11-21 11:55:25", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8adc6b272f9b81a2e37b5a3e4ee2b00e70a4cc2e", "commit_訊息": "[流程引擎]Q00-20231120003 修正監控流程的詳細流程圖頁面，當按下「跳過此關卡」功能時，下一關處理者無法收到代辦通知信件", "提交日期": "2023-11-20 17:25:48", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3007eaa7e07831a2b31eac616585ad8fe4ba2c40", "commit_訊息": "[Web] Q00-20231120001 修正BPM注册序号中 BPM流程引擎有过期序号导致其他功能无法正常使用", "提交日期": "2023-11-20 11:01:06", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1350def2f086056f047366345cf1bddc5e493a3c", "commit_訊息": "[Web] Q00-20231117002 修正 首次登录跳转到变更密码页面显示异常", "提交日期": "2023-11-17 13:48:06", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f409a05f6f07b36d1f918db27fc500a620770f3a", "commit_訊息": "[Web] Q00-20231117001 调整簡易流程圖跳過時_輸入密碼後alert的訊息框太小的问题", "提交日期": "2023-11-17 13:42:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "97c085ec8fbfa03c2c2f49118344194783129bba", "commit_訊息": "[Web] Q00-20231114003 修正寄件人重複顯示問題", "提交日期": "2023-11-14 15:54:22", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c8a00f62718a058cc818380cb9515d1ee621585", "commit_訊息": "[WEB] Q00-20231108002 修正待辦事項中表單serialNumber元件欄位資料顯示位置異常的問題", "提交日期": "2023-11-08 13:21:40", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c067f71c228412457a4e493c212803d510f0038", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)", "提交日期": "2023-11-06 17:47:59", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bc3e294a123127a2ddc1d0eefac8cacd73d8a31c", "commit_訊息": "[WEB]Q00-20231106001 调整Select元件在设置背景色时列印却显示唯读背景色的问题", "提交日期": "2023-11-06 11:11:50", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa09f62b64a2b907e650f067b16b87bb162be173", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)", "提交日期": "2023-11-03 11:12:20", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad4bb2ce3a93571fe90e0d3e76e772026cd8523e", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補修正)", "提交日期": "2023-10-25 18:14:01", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4fe066ce39e270b54c4e96b1d24737f6a507e82c", "commit_訊息": "[流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題", "提交日期": "2023-10-25 16:35:30", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d13f8945580ce29c97c02142d0493438a228d7d4", "commit_訊息": "[T100] Q00-20231102002 T100的签核历程沒有权限的显示提示告知登入者", "提交日期": "2023-11-02 13:12:36", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessInfoGet.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8cc9736a639768914dab9892188731823263cc35", "commit_訊息": "[Web] Q00-20231101005 修正表單欄位為invisible且設定顯示千分位，開啟追蹤流程在F12顯示錯誤", "提交日期": "2023-11-01 15:50:29", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "20feced941434a2919c3ba7cf1e24e86f4c8f99c", "commit_訊息": "[DT]Q00-20231101004 修正Web化流程管理工具中服務任務的應用程式加入表單型態參數後會有無法簽入的問題", "提交日期": "2023-11-01 15:38:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bfbfe27f24eae061e549a130bdf2ac8ae907a592", "commit_訊息": "[雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯[補修正]", "提交日期": "2023-11-01 14:10:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d02ea7663b9ef80dafd1fc80241c85c30c59fe5", "commit_訊息": "[雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯", "提交日期": "2023-11-01 14:04:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "cf7baf754637258ac8524e00f2099d83cd72b484", "commit_訊息": "[Web]S00-20220929001 新增BPM外部連結-進入BPM首頁", "提交日期": "2023-10-05 14:23:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/PortletEntry.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d225d3257b4a4748a1e92d6fed3b695881930956", "commit_訊息": "[Web]Q00-20230919001 調整URL從/NaNaWeb/Login.jsp?type=admin登入時會報閒置過久的訊息改成\"請輸入正確的代號或密碼!\"", "提交日期": "2023-09-19 12:02:02", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cdb46378b5dcfd3530572f971f6b374352fa7fa9", "commit_訊息": "[Web] Q00-20231025001 调整隐藏栏位为單身欄位加總的运算操作验证", "提交日期": "2023-10-25 11:01:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63ae946ba9eecd48cf48d49067ff6a8e3ebb660d", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]", "提交日期": "2023-09-28 17:59:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a94486a23d4d1c32d8636c5c0341141a7ae72c9", "commit_訊息": "[Web] Q00-20231031001 修正缩小ESSPlus管理页面时，查询出来的结果在grid显示不全", "提交日期": "2023-10-31 13:39:56", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4cbd31501863505d3bf2ad6dd454da4fab65229b", "commit_訊息": "[Web] Q00-20231030004 调整预览列印会带出表單名稱與表單代號Tab的问题", "提交日期": "2023-10-30 15:54:31", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c94cbb4bb6dc6249ef6a6baeb676dd207f966bd", "commit_訊息": "[Web]Q00-20231023001 修正列印预览grid标题字体颜色为白色", "提交日期": "2023-10-23 09:15:57", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df88b8a0570dd9ba09811456434bd06abb79425d", "commit_訊息": "[流程引擎]Q00-20231030001 修正流程發起者於追蹤流程頁面進入表單畫面點擊撤銷流程後，詳細流程圖的流程詳細資訊應為是「流程發起者」而非「流程負責人」撤銷", "提交日期": "2023-10-30 14:41:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "94bcbf65368ea07dcf42c7be0b6e51786f4a929c", "commit_訊息": "[流程引擎]Q00-20231027003 修正流程結案清除附件的服務，當表單附件OID屬性為空時，會被系統移除附件，此調整為避免移除表單實例的附件的OID屬性為空的附件", "提交日期": "2023-10-27 14:39:03", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d1cba1b0c9639bfecc92261e6b91b0d8b74335f", "commit_訊息": "[資安] Q00-20231017003 修改因paloalto內部防火牆把aes.js當成spyware Malicious JavaScript Files Detection攻擊，將aes.js進行加密編碼。", "提交日期": "2023-10-26 16:16:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/aes.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7f8b7f1acb793176366607b87882dd24f840e47e", "commit_訊息": "[Web]Q00-20231026001 修正转由他人处理 > 经常选取对象 无资料时显示错误页面", "提交日期": "2023-10-26 14:29:58", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fda832304a2ade4c262d5d477479848a405b7c3b", "commit_訊息": "[Web]Q00-20231023002 修正流程第一關有設定必須上傳新附件，若從流程草稿開啟時，系統沒有卡控必須上傳新附件", "提交日期": "2023-10-23 11:10:11", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "88e1cf417dde9e02c3d2795a5d0a99a8cd0a7d1f", "commit_訊息": "[WEB]Q00-20231018001 调整Select元件有textbox輸入格时的点击事件逻辑", "提交日期": "2023-10-18 11:13:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/resources/html/SelectElementTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c6bf4837a6cfd7bad3b6a81c36d796bdec078d92", "commit_訊息": "[流程引擎] Q00-20231017004 修正工作受託者<#allAssigneesIDnName>沒有帶出資料的問題", "提交日期": "2023-10-17 14:56:38", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f5b181b0fb37aa98758b066724a737d7a2d0842d", "commit_訊息": "[表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗", "提交日期": "2023-10-12 15:30:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0e06874d13a0247e42e7812e52620c7f27bdf16c", "commit_訊息": "[T100] Q00-20231004004 修正手寫元件造成拋單失敗，新增判斷略過手寫元件", "提交日期": "2023-10-04 14:56:04", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "266c5f2638566cd340f0073a429382aea76b4bed", "commit_訊息": "[WEB]Q00-20231004003 修正在手機瀏覽器以及RWD窄畫面上沒有附件檔名的URL連結導致無法下載問題", "提交日期": "2023-10-04 14:11:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "873ea01a3965a369c5174a58cf1abbc67680f8d0", "commit_訊息": "[Web] V00-20231011001 修正轉存表單日期格式異常問題，支持常用格式。", "提交日期": "2023-10-11 14:21:33", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4cfae325dcd47ae0219d75a4a203d1cbf4ef777", "commit_訊息": "[Web] V00-20231004001 修正匯出表單，日期格式(yy/M/d)被判斷為異常格式問題。", "提交日期": "2023-10-04 12:04:19", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23fa935ff54e6acaffbae18056b1b85cacbee415", "commit_訊息": "[Web]Q00-20231004001 修正TextBox設定數字轉繁體文字在列印表單時顯示簡體文字的問題", "提交日期": "2023-10-04 10:09:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b0cd56a4c4394613c2121c6b57bbd6e0cd3c0c4f", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]", "提交日期": "2023-09-26 14:32:49", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "499f79d8aea409f6381f69ca47585429f375312a", "commit_訊息": "[Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。", "提交日期": "2023-09-25 17:24:13", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "12f3c8c72f6ae5c9d8f53c90722be953166a8502", "commit_訊息": "[Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题[补修正]", "提交日期": "2023-08-24 13:39:10", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "db2688d4318c7af76af0e9bea23c548a629a5461", "commit_訊息": "[Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题", "提交日期": "2023-08-24 09:32:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c0857b653f8e3532fbc1d57dae9e6a0b8648f30", "commit_訊息": "[SAP]Q00-20231003003 修正SAP整合作業-SAP欄位對應設定，新增整合設定頁面當選擇完表單後，無法載入表單元件", "提交日期": "2023-10-03 17:39:22", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f6ff06040667e7f310884134c1b5bff2046574a4", "commit_訊息": "[DT]Q00-20230928003 修正從Web化流程管理工具入版後的流程圖在Swing中開啟時流程圖會被截斷問題", "提交日期": "2023-09-28 17:56:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd81e5cec621012c141bd295942f76440117bf07", "commit_訊息": "[web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复", "提交日期": "2023-09-27 17:19:26", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84c40844c933817f27b210d50918399c4b8ef837", "commit_訊息": "[DT]Q00-20230926002 修正Web流程管理工具儲存流程因找不到ActivityType導致儲存失敗問題", "提交日期": "2023-09-26 17:58:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2aa04710165d3b86229e2fe26d0e61a2aba081a9", "commit_訊息": "[DT]Q00-20230926003 修正Web流程管理工具的流程圖進版後有條件的連接線顏色變黑色問題", "提交日期": "2023-09-26 17:48:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4791c5e879dcab0752914f3990cef0b6080ee193", "commit_訊息": "[DT]Q00-20230926004 修正Web流程管理工具的流程圖進版後連接線上的名稱消失問題", "提交日期": "2023-09-26 17:44:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29ab55e8f507ab48ed31050044b2889bc9012ffc", "commit_訊息": "[在線閱覽]Q00-20230926001 修正在線閱讀檔案設定不可下載，在待辦表單頁面點擊閱讀檔案的頁面，仍可以下載PDF閱讀檔的異常", "提交日期": "2023-09-26 16:01:14", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "243f0dee28fcd78c01dc355e5ea1ebdeea2cd690", "commit_訊息": "[T100]S00-20220513001 T100取簽核歷程新增是否為代理人標籤", "提交日期": "2023-09-05 10:16:20", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTag.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "89cd1b054764b60e4d9fc1bd4af233cbda0116e4", "commit_訊息": "[Web] Q00-20230922004 在没有添加grid按钮却使用grid方法时，新增防呆，防止focus报错", "提交日期": "2023-09-22 14:57:46", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "849272149d02c8c88a944b12c8c9f07e07f6bfbb", "commit_訊息": "[Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效", "提交日期": "2023-09-22 11:21:43", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e2a961c69ca53882f902a4b2c5780c0b62f12999", "commit_訊息": "[Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加", "提交日期": "2023-09-22 10:42:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7d7e7b251a1cbb609289e4b2a03c760c508123ad", "commit_訊息": "[Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错", "提交日期": "2023-09-22 10:27:58", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16cd4611bfd70d7fc616a2c79cf4cf6239800dd0", "commit_訊息": "[組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號(補修正)", "提交日期": "2023-09-21 17:05:57", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f8404cd5b3f7d7cf2bedd01f370900297e73bd4b", "commit_訊息": "[DT]Q00-20230921001 修正Web流程管理工具中參與者選職務或職稱後儲存再開啟其值會消失的問題", "提交日期": "2023-09-21 16:13:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c505588e3f0b6265145c0da461f5cdfbd0fd9f21", "commit_訊息": "[DT]Q00-20230920002 修正Web系統權限管理員上儲存按鈕後畫面會一直loading的問題", "提交日期": "2023-09-20 16:38:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a4a32ad287ed0d2f080d30b26516b16ab9bd884", "commit_訊息": "[組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號。", "提交日期": "2023-09-20 13:44:52", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b77ad2a05e9dd1f4e1cc36a9f2993fc0242c044e", "commit_訊息": "[DT]A00-20230919001 修正Web流程管理工具中事件處理在流程儲存後會消失問題", "提交日期": "2023-09-20 12:01:52", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed3c3700c858b38d0c7ea39c4a8d3ae08e846af4", "commit_訊息": "[ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為\"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程\"[補修正]", "提交日期": "2023-09-19 13:42:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "11bcd57876342d6dba5b2e9c59d5f2cc20ba1c42", "commit_訊息": "[DT]Q00-20230918002 修正Web流程管理工具中連接線編輯後儲存流程再開啟會變成藍色的問題", "提交日期": "2023-09-18 14:40:32", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d87cb6df57f8577ed09e5ac875957d9a26f508b3", "commit_訊息": "[ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為\"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程\"", "提交日期": "2023-09-18 14:02:51", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "159fcb0154095ae0f17ff8904322b71dbab2cd95", "commit_訊息": "[web]Q00-20230913001 沒有未閱讀的工作通知，但右上角鈴鐺還是一直有紅點點问题修复", "提交日期": "2023-09-18 09:24:41", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a74f2c89c9a72bc332b5ce748f2ca0c6e0e24341", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】", "提交日期": "2023-09-11 16:08:36", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "36ba303dce5da0c92ced190573e26c5ab3d576d2", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】", "提交日期": "2023-09-07 17:54:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d83f57a6e0c67ad479d43287fcbc2e07a80e78d2", "commit_訊息": "[Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面", "提交日期": "2023-08-25 15:40:09", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0b0eee29550a12feff3945b8fa4a5ebee9608db5", "commit_訊息": "[Web] Q00-20230915001 修正 SQL注册器点击资料返回到新增页面，数据错误带回显示", "提交日期": "2023-09-15 11:09:32", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5bbbc2bfd84cf10a5a41fdc952a6fb589756a43", "commit_訊息": "[表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]", "提交日期": "2023-09-15 10:22:25", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8bad3e313c81ff1176821fcf0d2a8e8b7b699bc9", "commit_訊息": "[Web]Q00-20230914001 修正RadioButton，checkbox元件帶入值到grid时报错，新增防呆。", "提交日期": "2023-09-14 16:07:26", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ce886ded44b84dd6da4ed224f1fe92c53f58751", "commit_訊息": "[Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。", "提交日期": "2023-09-12 15:29:48", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b7af9bb3ab4fbec5ab05bb6d4035cf3f357a4e91", "commit_訊息": "[Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)", "提交日期": "2023-09-12 10:23:55", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1c6d4f2abad238d9b424702b482757ba602e6687", "commit_訊息": "[Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法", "提交日期": "2023-09-12 10:06:27", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a249f8c450b6fcbc70a198e10e2a75766225daf", "commit_訊息": "[Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。", "提交日期": "2023-09-11 15:39:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bd50740a44f0ef78967b45dd36f416f141b77b76", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)", "提交日期": "2023-09-12 17:23:00", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ac3b5dec2f5d3ee78d2db6e267b9c9db3f9a5f8", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)", "提交日期": "2023-09-04 10:53:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c574ec137870e563b59669ce40cc6ff52a5f5cc3", "commit_訊息": "[Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題", "提交日期": "2023-08-31 15:41:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ad68936dee6a03a4c9f9582304298e76779aa498", "commit_訊息": "[表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題", "提交日期": "2023-09-08 16:39:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cf50118ad353688beb07bbc27986b8b3ac05330", "commit_訊息": "[Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]", "提交日期": "2023-09-08 10:14:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "20b08849dc32bb291a1c771427bda3f7a555954b", "commit_訊息": "[SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗", "提交日期": "2023-09-08 09:24:46", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ffb260bbb2c54195929376f607925ad7fc6ccd4f", "commit_訊息": "[Web]Q00-20230907001 读取自定义background,Banner,logo图片时，新增图片格式防呆。", "提交日期": "2023-09-07 11:31:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7dea21018ff84f649cce05eed470cd4c4a1beb1e", "commit_訊息": "[流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常", "提交日期": "2023-09-07 10:31:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e699a42126c703329ef58e96c2b657ae20675adc", "commit_訊息": "[Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。", "提交日期": "2023-09-06 13:19:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a3fe8f78be2406373a59e4933ea3d472d5e0eb6", "commit_訊息": "[Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。", "提交日期": "2023-09-06 12:03:56", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29767c102033fb3b27fb7a0909f338d0650aee9f", "commit_訊息": "[Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误", "提交日期": "2023-09-11 11:47:04", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63bc842520f9344ddd0c495ec22b77270b53536b", "commit_訊息": "[Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。", "提交日期": "2023-09-05 18:01:25", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4412bd5a36a8723fdb9d2c7b9843ee27bdfe35fe", "commit_訊息": "[Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白", "提交日期": "2023-09-06 11:11:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a459de8d88f4afe86a63758b5f5fce19e63561bb", "commit_訊息": "[DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題", "提交日期": "2023-09-01 19:47:59", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "721c74a695e0e0a37765d71963ebca50e5fab6b8", "commit_訊息": "打包用", "提交日期": "2023-08-31 14:47:24", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/lib/Json/json.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "7edf85fb3aaae049bc59fd0591ea66848d1cef12", "commit_訊息": "[Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件", "提交日期": "2023-08-31 09:16:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "48fecbee1144a6d2fe423dd3f4ad4ae8e1f605f5", "commit_訊息": "[Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題", "提交日期": "2023-08-30 11:47:29", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b3892d1623b3636b81e6130827e7d2d13eafc60f", "commit_訊息": "[TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷", "提交日期": "2023-08-30 10:43:55", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e1a8c44961c5907be02fe8fbdc1b861b7958e60", "commit_訊息": "[流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常", "提交日期": "2023-08-29 16:50:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b30e78a2b454939864ee2810855877513097fe36", "commit_訊息": "[ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗", "提交日期": "2023-08-29 15:50:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f72c90f6b7c0d2f7c468a8d0dd2c43ba93369fab", "commit_訊息": "[web]Q00-20230829003 列印時附件資訊會超出邊界问题修复", "提交日期": "2023-08-29 14:02:09", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "feae55e055644d8903d73585988148566eb72137", "commit_訊息": "[流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據", "提交日期": "2023-08-29 10:32:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f7d42cd47ebe4f0fa47038313eee19d638b4ee3c", "commit_訊息": "[SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤", "提交日期": "2023-08-28 16:57:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c4e8abf77a2d5851011fb9f7df99123b4b5d5713", "commit_訊息": "[DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題", "提交日期": "2023-08-28 11:15:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e931eef3588ea08db7b75a46fbc9e1083303403", "commit_訊息": "[web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复", "提交日期": "2023-08-25 17:38:15", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ce60fb865619dd15ff23dc0db3378472fda363c", "commit_訊息": "[Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。", "提交日期": "2023-08-24 13:43:54", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "39f901b07448b8ebf85b6a97a2a949267ac915bf", "commit_訊息": "[Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常", "提交日期": "2023-08-23 15:27:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}]}