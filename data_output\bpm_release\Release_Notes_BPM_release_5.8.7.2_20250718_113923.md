# Release Notes - BPM

## 版本資訊
- **新版本**: release_*******
- **舊版本**: release_5.8.7.1
- **生成時間**: 2025-07-18 11:39:23
- **新增 Commit 數量**: 188

## 變更摘要

### lorenchang (2 commits)

- **2022-06-26 22:04:59**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
  - 變更檔案: 25 個
- **2022-01-04 17:09:31**: [內部]新增Commit變更記錄檔
  - 變更檔案: 1 個

### 林致帆 (33 commits)

- **2022-01-19 15:35:52**: [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯
  - 變更檔案: 2 個
- **2022-01-18 11:22:09**: [Tiptop]Q00-20220118002修正Tiptop傳的Grid沒有內容時會產生空陣列在Grid上
  - 變更檔案: 1 個
- **2022-01-17 10:52:45**: [WorkFlow]Q00-20220117001WorkFlow預設流程調整移除通知關卡
  - 變更檔案: 1 個
- **2022-01-14 14:35:28**: [Web]V00-20220111001 修正追蹤"流程清單預設查看的流程狀態"更改設定後點及追蹤流程清單還是設定前的狀態
  - 變更檔案: 1 個
- **2022-01-14 13:50:01**: [Web]V00-20220112001調整取回重辦，退回重辦時，轉派意見字眼不應顯示在待辦流程清單上
  - 變更檔案: 1 個
- **2022-01-13 15:32:28**: [Web]A00-20220110001 修正流程關卡用預設代理人處理，若該關卡沒有預設代理人，不會出現提示訊息畫面[補修正]
  - 變更檔案: 1 個
- **2022-01-12 10:49:50**: [Web]A00-20220110001 修正流程關卡用預設代理人處理，若該關卡沒有預設代理人，不會出現提示訊息畫面
  - 變更檔案: 3 個
- **2022-01-07 10:59:50**: [Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢
  - 變更檔案: 1 個
- **2022-01-07 09:24:23**: [TIPTOP]Q00-20220107001 出貨版本新增TIPTOP RWD響應式表單
  - 變更檔案: 127 個
- **2022-01-03 15:03:34**: [Web]A00-20220103001 修正查看追蹤流程頁面,包含表單及流程的URL，URL中沒有表單定義ID就不開放給一般使用者查看
  - 變更檔案: 1 個
- **2021-12-28 15:03:08**: [內部]Q00-20211228001調整易飛orWFERP整合主機設定兩台多主機時，會無法開單
  - 變更檔案: 1 個
- **2021-12-27 10:54:32**: [Web]A00-20211223001 修正監控流程清單頁連結不開放給系統管理員以外的人員[補修正]
  - 變更檔案: 2 個
- **2021-12-23 14:20:05**: [Web]A00-20211223001 修正監控流程清單頁連結不開放給系統管理員以外的人員
  - 變更檔案: 2 個
- **2021-12-16 11:56:50**: [Web]Q00-20211216001 修正SQLCommand的SQL指令帶有百分比及加號會導致報錯
  - 變更檔案: 2 個
- **2021-12-14 17:41:16**: [Web]Q00-20211214001 修正報表設計器樣板的匯出Excel按鈕多語系錯誤
  - 變更檔案: 5 個
- **2021-12-13 11:18:20**: [Web]A00-20211209001 調整離職維護作業的時間輸入欄位點擊應為時間開窗供使用者選擇
  - 變更檔案: 1 個
- **2021-12-09 17:11:35**: [Web]S00-20211122001 監控流程的查詢條件的流程開窗，調整流程名稱為多語系
  - 變更檔案: 1 個
- **2021-12-08 17:40:19**: [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤[補修正]
  - 變更檔案: 2 個
- **2021-12-08 16:49:13**: [Web]A00-20211202001修正流程關卡設定為"必須上傳新附件"，如果上傳附件後點擊兩次儲存表單，第二次儲存表單會提示必須上傳新附件
  - 變更檔案: 4 個
- **2021-12-02 16:41:02**: [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤
  - 變更檔案: 3 個
- **2021-12-02 09:08:42**: [Web]A00-20211201002 修正追蹤流程點擊匯出Excel會報錯
  - 變更檔案: 1 個
- **2021-12-01 13:41:51**: [ESS]Q00-20211201001表單作者名稱調整為系統管理員
  - 變更檔案: 26 個
- **2021-11-26 17:21:35**: [ESS]Q00-20211112002 修正ESS流程 A員工在 A電腦進行儲存草稿動作，A員工在B電腦打開該草稿時，ESS表單開啟報錯
  - 變更檔案: 1 個
- **2021-11-22 15:22:06**: [WorkFlow]Q00-***********修正WorkFlow在單據進行取消確認時，對該單據抽單，回傳的狀態碼有誤導致WorkFlow作業為待處理
  - 變更檔案: 7 個
- **2021-11-12 09:14:09**: [Web]Q00-20211112001修正，系統管理工具的資料來源設定是用Oracle且修改的欄位是Oracle服務名稱(SID)時，取得該資料來源資料會顯示原來的資訊而不是修改後的
  - 變更檔案: 1 個
- **2021-11-08 17:58:39**: [流程引擎]Q00-*********** 補上createSQL遺漏監控流程刪除流程設定的參數
  - 變更檔案: 1 個
- **2021-11-08 17:11:14**: [組織設計師]A00-20211027001 調整員工administrator點擊檢視員工資料再點編輯跟從修改員工資料的可編輯欄位要一致
  - 變更檔案: 1 個
- **2021-11-02 18:00:22**: [流程引擎]Q00-20211102003 修正E10抽單，回傳為流程完成狀態而不是流程撤銷狀態
  - 變更檔案: 2 個
- **2021-11-02 17:14:23**: [Web]Q00-20211102002 修正元件的label及元件在流程設計師設定invisible時，前端頁面報錯導致系統變數顯示內容異常
  - 變更檔案: 1 個
- **2021-11-02 16:58:03**: [E10]S00-20211019003 新增 E10(不驗證表單)的回寫接口
  - 變更檔案: 3 個
- **2021-10-25 11:48:03**: [Web]A00-20211022001 調整系統管理員在監控流程只有選擇"未結案"，"全部"的流程狀態按鈕，才會在"更多"按鈕顯示撤銷流程
  - 變更檔案: 1 個
- **2021-10-20 11:17:20**: [流程引擎]Q00-20211020002 修正流程關係人設定部門表單欄位，表單內容為[組織ID]部門ID，導致流程發起失敗
  - 變更檔案: 1 個
- **2021-10-19 14:23:08**: [組織設計師]Q00-20211018001修正組織設計師點選群組跟專案右側人員清單不應該顯示分頁鈕
  - 變更檔案: 1 個

### waynechang (20 commits)

- **2022-01-19 14:49:50**: [Web]V00-20220112001調整取回重辦，退回重辦時，轉派意見字眼不應顯示在待辦流程清單上[補]
  - 變更檔案: 1 個
- **2022-01-14 15:02:32**: [ISO]S00-*********** ISO模組增加支持「絕對位置ISO批次作廢單」的批次作廢流程」服務接口「批次申請作廢、批次取消作廢、批次作廢文件」
  - 變更檔案: 1 個
- **2022-01-14 14:38:07**: [流程設計工具]V00-20220105001 調整流程設計工具的BPM版本檢核邏輯，由驗證版本前三碼改為驗證版本四碼都符合才允許開啟
  - 變更檔案: 1 個
- **2022-01-14 14:09:22**: [WEB]V00-20200616004 調整監控流程、追蹤流程頁面，當預設查看的流程狀態為「未結案」時，將Gird欄位「流程結案時間」隱藏
  - 變更檔案: 1 個
- **2022-01-12 15:42:52**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2022-01-12 15:42:11**: Revert "[流程引擎]Q00-20211001001 移除NaNaLog、NaNaWebLog寫log機制，並將log訊息統一寫至server.log"
  - 變更檔案: 1 個
- **2022-01-05 17:40:33**: [流程引擎]Q00-20211001001 移除NaNaLog、NaNaWebLog寫log機制，並將log訊息統一寫至server.log
  - 變更檔案: 1 個
- **2021-12-20 16:08:54**: [WEB]Q00-20211220001 修正表單有選擇元件(RadioBox,ComboBox,CheckBox,ListBox)，且在流程該關卡有將選擇元件設定為invisible時；若該關卡重新上傳附件時，上傳附件畫面為空白，並且無法上傳的異常
  - 變更檔案: 1 個
- **2021-12-15 10:51:15**: [組織設計師]S00-20210429005 調整組織設計師修改人員資料角色設定開窗時增加checkbox多選欄位並載入使用者的角色預設勾選[補]
  - 變更檔案: 2 個
- **2021-12-14 17:19:17**: [組織設計師]S00-20210429005 調整組織設計師修改人員資料角色設定開窗時增加checkbox多選欄位並載入使用者的角色預設勾選
  - 變更檔案: 11 個
- **2021-12-07 15:19:13**: [Web]S00-20210429008 上傳附件名稱長度調整為可輸入250的字[補]
  - 變更檔案: 1 個
- **2021-12-07 15:01:04**: [SAP]A00-20211203001 修正SAP整合，當SAP回傳資料須回寫至Grid時，但SAP回傳Grid資料為空時，Grid原先row的內容需一併清除
  - 變更檔案: 1 個
- **2021-11-22 16:14:49**: [內部]S00-20211112001 組織設計師及組織同步工具增加參數「orgdesigner.unitid.passsymbol」判斷Id是否檢查特殊符號
  - 變更檔案: 7 個
- **2021-11-18 14:13:15**: [流程引擎]S00-*********** 企業流程監控增加流程平均填單時間：將流程中第一關填單的時間加總後取得平均值
  - 變更檔案: 7 個
- **2021-11-10 17:01:06**: [流程引擎]S00-*********** 企業流程監控時間頁籤中的計算增加「TRIMMEAN(排除極端值的百分比)」功能
  - 變更檔案: 7 個
- **2021-11-08 16:41:56**: [流程引擎]Q00-20211108001 調整ExtOrgAccessor.findManagerForUser服務，當傳入的組織OID與人員不相關時，仍需回傳人員的主部門的主管
  - 變更檔案: 1 個
- **2021-10-28 14:14:02**: [WEB]S00-20200616004 表單全域變數增加「mainFunctionLevel」使用者職務核決層級Level值[補]
  - 變更檔案: 2 個
- **2021-10-27 15:45:14**: [WEB]S00-20200616004 表單全域變數增加「mainFunctionLevel」使用者職務核決層級Level值
  - 變更檔案: 2 個
- **2021-10-19 17:26:32**: [SAP]Q00-20211019008 調整SAP系統整合設定的維護作業改以維護樣板方式維護
  - 變更檔案: 4 個
- **2021-10-19 17:14:12**: [SAP]Q00-20211019007 調整SAP整合設定，當更新SAP欄位對應設定作業時，一併重新載入SAP連線
  - 變更檔案: 3 個

### walter_wu (10 commits)

- **2022-01-18 17:41:26**: [Web]A00-20211222001 修正離職作業維護選擇User後修改組織資料資料卻無法正確呈現與修改直屬主管異常
  - 變更檔案: 1 個
- **2022-01-17 17:01:30**: [Web]V00-20220112002 修正檢視參與者型式的關卡中的接收者(讀取次數)，會出現<br>
  - 變更檔案: 1 個
- **2022-01-10 17:19:15**: [流程引擎]Q00-20211220002 修正客戶附件遺失問題
  - 變更檔案: 2 個
- **2021-12-15 16:13:27**: [SQLCommand]Q00-20211215001 修正用Ajax下SQL如果欄位行態是text無法找出資料
  - 變更檔案: 1 個
- **2021-11-11 18:47:13**: [內部]A00-20210820001 在判斷可退回關卡邏輯處加上線的定義詳細Log，方便之後排查
  - 變更檔案: 1 個
- **2021-11-10 16:55:45**: [Web]A00-*********** 預覽流程圖關卡點開彈窗Table新增顯示  接收者  欄位
  - 變更檔案: 1 個
- **2021-11-09 17:01:29**: [流程引擎]Q00-20211109003 修正流程如果設定關係人的部門從表單欄位(選兼職部門)，發起時儲存表單後預解析卻解析主部門
  - 變更檔案: 1 個
- **2021-11-05 15:52:57**: [內部]Q00-20211105002 調整列印龐大Grid位置(絕對位置表單,依設定)
  - 變更檔案: 1 個
- **2021-11-04 17:21:00**: [流程引擎]Q00-20211104003 修正簡易流程圖無法查看於核決層級內加簽的"多人"關卡的關卡資訊
  - 變更檔案: 1 個
- **2021-10-22 16:08:12**: [Web]Q00-20211022001 修正部分情境前端沒有傳入登入方式，會出現設定LDAP驗證卻走系統驗證
  - 變更檔案: 1 個

### 王鵬程 (33 commits)

- **2022-01-18 11:30:45**: [內部]新增E10回寫簽核歷程排程執行取得的Response的log訊息
  - 變更檔案: 1 個
- **2022-01-14 17:22:37**: [Web]S00-20211124001 提供行動版在發起流程頁面可調整流程緊急度
  - 變更檔案: 1 個
- **2022-01-14 11:59:53**: [Web]V00-20220113001 修正系統排程設定的編輯工作觸發程序中選擇每天、每週、每月進去的時間下拉元件太窄導致內容無法完整顯示
  - 變更檔案: 2 個
- **2022-01-10 17:28:40**: [Web]S00-20211012002 在BPM文件/設計工具下載 頁面，調整下載順序並增加step
  - 變更檔案: 1 個
- **2022-01-07 17:57:59**: [Web]Q00-20220107009 調整行動版時左側的選單背景色及字體顏色也要與PC版一致
  - 變更檔案: 1 個
- **2022-01-06 14:52:01**: [流程設計師]Q00-20220106008 修正在流程定義視窗中選擇標頭，勾選是否逾時多次通知後在進來該視窗都會變未勾選
  - 變更檔案: 1 個
- **2022-01-06 11:47:10**: [Web]Q00-20220106001 調整加簽關卡的頁面中，關卡名稱允許單字之間使用空格
  - 變更檔案: 2 個
- **2022-01-04 11:45:34**: [Web]Q00-20220104002 調整簡體語系登入時，在待辦清單頁中Grid標頭的『名稱』調整為『關卡名稱』
  - 變更檔案: 1 個
- **2021-12-27 15:24:57**: [Web]Q00-20211227001 修正使用IE在絕對表單沒有附件時預覽列印，會導致簽核歷程和表單內容重疊
  - 變更檔案: 1 個
- **2021-12-24 17:48:16**: Revert "[ESS]Q00-20211224001 調整當進入為ESS流程時不提供儲存表單按鈕"
  - 變更檔案: 1 個
- **2021-12-24 14:36:36**: [ESS]Q00-20211224001 調整當進入為ESS流程時不提供儲存表單按鈕
  - 變更檔案: 1 個
- **2021-12-22 15:21:26**: [Web]A00-20211220001 修正在行動版時在我的最愛內的常用流程與常用功能維護無法儲存
  - 變更檔案: 2 個
- **2021-12-22 14:26:26**: [Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字[補]
  - 變更檔案: 1 個
- **2021-12-17 14:15:34**: [流程引擎]Q00-20211217001 修正當資料庫為oracle時，SQL註冊器未輸入任何條件查詢會報錯
  - 變更檔案: 1 個
- **2021-12-16 18:24:59**: [Web]Q00-20211216004 修正新增排程的頁面中，排程生效時間下拉元件太窄導致內容無法完整顯示
  - 變更檔案: 1 個
- **2021-12-16 17:48:30**: [Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字
  - 變更檔案: 3 個
- **2021-12-16 17:27:41**: Revert "[Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字"
  - 變更檔案: 3 個
- **2021-12-07 17:03:09**: [Web]Q00-20211207001 修正CSS的樣式缺少右大擴號的錯誤導致寫在後面的CSS無法生效
  - 變更檔案: 1 個
- **2021-12-06 15:01:28**: [Web]A00-20211201001 修正Rwd表單有設定元件的背景色、文字顏色時，在列印時無法印出色彩[補]
  - 變更檔案: 2 個
- **2021-12-03 14:47:30**: [Web]A00-20211201001 修正Rwd表單有設定元件的背景色、文字顏色時，在列印時無法印出色彩
  - 變更檔案: 2 個
- **2021-12-01 15:08:38**: [流程引擎]Q00-20211201002  修正人員名稱有新的特殊字(慈)時，進入ESS流程會報錯
  - 變更檔案: 1 個
- **2021-11-25 17:43:13**: [流程引擎]Q00-20211125001 修正透過HR小助手同步，當人員的兼職部門直屬主管在HR那已設空值，同步後卻未被改成空值[補]
  - 變更檔案: 2 個
- **2021-11-25 15:47:34**: [流程引擎]Q00-20211125001 修正透過HR小助手同步，當人員的兼職部門直屬主管在HR那已設空值，同步後卻未被改成空值
  - 變更檔案: 4 個
- **2021-11-24 18:03:19**: [Web]Q00-20211124001 修正一般使用者在追蹤流程頁面中將『更多』按鈕隱藏
  - 變更檔案: 1 個
- **2021-11-23 17:33:04**: [Web]Q00-20211123002 修正一般使用者在追蹤流程頁面中將『更多』按鈕隱藏
  - 變更檔案: 1 個
- **2021-11-19 17:15:14**: [Web]S00-20210621002 可偵測出非支援瀏覽器時會彈出警告視窗，並一律在登入按鈕下方增加建議的瀏覽器資訊[補]
  - 變更檔案: 1 個
- **2021-11-19 17:05:48**: [Web]S00-20210621002 可偵測出非支援瀏覽器時會彈出警告視窗，並一律在登入按鈕下方增加建議的瀏覽器資訊
  - 變更檔案: 2 個
- **2021-11-16 18:11:35**: [Web]Q00-20211116002修正RWD列印表單開窗中，點下列印表單後，有選到的radio和checobox都會產生類似殘影的樣子
  - 變更檔案: 1 個
- **2021-11-11 15:10:45**: [Web]Q00-20211111004 修正流程有設定列印模式，且有將流程設置在iReport時，點擊上方『列印表單』後流程無法繼續派送
  - 變更檔案: 1 個
- **2021-10-28 14:50:20**: [Web]Q00-20211028003 修正活動關卡中掛載網頁應用程式，在IE中點上面的更多按鈕，出現的選單會被遮蔽而無法點選
  - 變更檔案: 1 個
- **2021-10-27 18:13:43**: [流程引擎]S00-*********** 追蹤流程頁面的進階查詢中增加簽核時間欄位條件
  - 變更檔案: 7 個
- **2021-10-20 15:53:10**: [流程引擎]Q00-20211020004 修正絕對表單Grid內有 左、右中括號及單引號，轉存的Grid資料表的資料未能正確呈現符號
  - 變更檔案: 1 個
- **2021-10-19 16:32:31**: [Web]Q00-20211015001 調整讓Grid支援使用<button>
  - 變更檔案: 1 個

### yamiyeh10 (29 commits)

- **2022-01-18 11:01:14**: [BPM APP]Q00-20210520004 修正Line推播訊息在通知內容為空白時會推送失敗問題
  - 變更檔案: 1 個
- **2022-01-13 14:33:16**: [BPM APP]Q00-20220112003 修正系統設定追蹤清單的順序時，IMG追蹤列表上方篩選列顯示空白問題
  - 變更檔案: 1 個
- **2022-01-12 15:23:39**: [BPM APP]Q00-20211111005 調整行動端追蹤已簽核和已發起表單的取回重辦機制
  - 變更檔案: 9 個
- **2022-01-10 18:07:38**: [BPM APP]Q00-20211122001 修正行動表單自動轉換功能在切分多欄位且未擺入元件時會有部分元件未被生成問題
  - 變更檔案: 1 個
- **2022-01-07 15:03:11**: [BPM APP]Q00-20211111003 修正行動端表單在取回重辦後簡易簽核歷程區塊會多產生一組資料問題
  - 變更檔案: 1 個
- **2022-01-06 15:11:02**: [BPM APP]Q00-20220106010 修正行動版表單jDalert彈窗機制顯示一次後，後續的彈窗不會顯示遮罩問題
  - 變更檔案: 1 個
- **2022-01-05 17:24:50**: [BPM APP]Q00-20211118001 修正IMG在追蹤已簽核畫面執行取回重辦按鈕後原先取消關注按鈕會被更改成關注按鈕的問題
  - 變更檔案: 1 個
- **2022-01-05 11:30:37**: [BPM APP]Q00-20211223002 修正行動模擬簽核按鈕在已轉派流程上出現問題
  - 變更檔案: 1 個
- **2021-12-30 10:52:26**: [BPM APP]Q00-20211012001 調整入口平台整合設定中連線管理頁面在整合鼎捷移動時增加鼎捷移動平台管理後台填外網提示[補]
  - 變更檔案: 1 個
- **2021-12-23 18:16:32**: [BPM APP]S00-20210926001 在行動端的企業微信整合方案新增已轉派列表與表單畫面功能[補]
  - 變更檔案: 5 個
- **2021-12-21 18:24:59**: [BPM APP]C01-*********** 調整行動端儲存草稿一律必填草稿流程主旨[補]
  - 變更檔案: 2 個
- **2021-12-15 11:03:55**: [BPM APP]S00-20210926001 在行動端的企業微信整合方案新增已轉派列表與表單畫面功能[補]
  - 變更檔案: 1 個
- **2021-12-09 16:37:26**: [BPM APP]S00-20210926001 在行動端的企業微信整合方案新增已轉派列表與表單畫面功能
  - 變更檔案: 16 個
- **2021-12-09 16:17:22**: [Web]Q00-20211209001 調整Grid綁定Checkbox與Radio元件時增加判斷元件是否存在機制防止發生找不到元件的問題
  - 變更檔案: 1 個
- **2021-12-08 16:01:51**: [BPM APP]C01-20211202008 修正產品部門開窗在快搜時會顯示失效部門問題
  - 變更檔案: 1 個
- **2021-11-25 14:40:53**: [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]
  - 變更檔案: 16 個
- **2021-11-22 14:53:20**: [BPM APP]C01-*********** 調整行動端儲存草稿一律必填草稿流程主旨
  - 變更檔案: 5 個
- **2021-11-18 15:20:57**: [BPM APP]Q00-*********** 修正在鼎捷移動上開啟草稿流程中的任一條流程會發生取得表單資訊錯誤的問題[補]
  - 變更檔案: 1 個
- **2021-11-17 14:39:01**: [BPM APP]Q00-*********** 修正在鼎捷移動上開啟草稿流程中的任一條流程會發生取得表單資訊錯誤的問題
  - 變更檔案: 1 個
- **2021-11-12 14:55:00**: [BPM APP]S00-20210910007 行動端的追蹤已發起流程表單新增取回重辦功能
  - 變更檔案: 5 個
- **2021-11-11 11:29:47**: [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]
  - 變更檔案: 1 個
- **2021-11-10 17:19:34**: [BPM APP]Q00-20211110002 修正在IMG從草稿流程進入的表單是走發起流程而不是從草稿進入發起流程
  - 變更檔案: 1 個
- **2021-11-10 09:36:34**: [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]
  - 變更檔案: 1 個
- **2021-11-09 18:16:38**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-11-09 18:16:08**: [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題
  - 變更檔案: 4 個
- **2021-11-09 13:40:31**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-11-09 13:38:11**: [BPM APP]C01-*********** 修正行動端從草稿流程進入的表單多呼叫formCreate方法導致畫面異常
  - 變更檔案: 2 個
- **2021-11-05 16:17:27**: [BPM APP]Q00-20211105001 修正移動端FormUtil.setValue異常問題
  - 變更檔案: 1 個
- **2021-11-03 12:06:48**: [BPM APP]Q00-20211103002 修正行動端表單使用客製開窗且回傳欄位填入空字串時會無法正確將選擇的值返回到表單上問題
  - 變更檔案: 1 個

### cherryliao (17 commits)

- **2022-01-17 18:38:44**: [BPM APP]Q00-20220105002 修正當系統啟用動態渲染表單顯示且流程掛載多表單時，IMG各列表出現多筆重複流程的問題
  - 變更檔案: 4 個
- **2021-12-23 16:46:39**: [流程引擎]S00-20210511001 新增追蹤流程列表可依流程結案時間做排序
  - 變更檔案: 2 個
- **2021-12-20 13:54:48**: [Web]S00-20210914003 調整BPM登入頁面可用瀏覽器的儲存密碼功能
  - 變更檔案: 1 個
- **2021-12-13 15:01:10**: [Web]S00-20210810002 新增RWD表單DialogInputLabel元件可調整輸入框顯示比例的設定
  - 變更檔案: 9 個
- **2021-11-24 13:38:28**: [Web]S00-20210810008 新增手機版頁面待辦列表若包含可批簽流程時有全選的功能
  - 變更檔案: 2 個
- **2021-11-23 13:50:04**: [Web]S00-20210810009 調整Web端表單Label元件文字過長時樣式
  - 變更檔案: 1 個
- **2021-11-22 17:33:50**: [Web]S00-*********** 待辦列表新增讀取狀態的圖示[補]
  - 變更檔案: 1 個
- **2021-11-11 11:25:44**: [Web]Q00-20211111001 調整待辦事項行動版畫面資料重複且跑版的問題
  - 變更檔案: 1 個
- **2021-11-11 10:49:06**: [Web]S00-20210429001 調整簡易流程若關卡處理者為代理人則處理者名稱旁有提示文字
  - 變更檔案: 2 個
- **2021-11-10 14:14:40**: [Web]S00-*********** 待辦列表新增讀取狀態的圖示
  - 變更檔案: 1 個
- **2021-11-09 13:38:14**: [Web]S00-20210713001 調整產品授權註冊維護作業雙擊某一筆授權時才會彈出是否刪除的詢問視窗
  - 變更檔案: 1 個
- **2021-11-05 17:35:51**: [Web]S00-20210503004 調整Web端加簽畫面新增經常選取對象
  - 變更檔案: 4 個
- **2021-11-03 14:55:01**: [TipTop]S00-20210729002 新增TIPTOP流程終止或撤銷時，若單據修改且重新送審後於BPM表單可查看之前審批流程的功能
  - 變更檔案: 1 個
- **2021-11-03 13:38:42**: [Web]S00-20210506009 新增RWD表單Grid元件支援凍結欄位功能
  - 變更檔案: 14 個
- **2021-10-28 11:17:11**: [Web]S00-20210709002 調整Web加簽頁面樣式
  - 變更檔案: 3 個
- **2021-10-28 11:00:44**: [E10]S00-*********** 新增E10流程終止或撤銷時，若單據修改且重新送審後於BPM表單可查看之前審批流程的功能
  - 變更檔案: 8 個
- **2021-10-19 13:59:30**: [表單設計師]S00-20210714001 新增表單設計師DialogInput和DialogInputLabel元件的預設值配置功能
  - 變更檔案: 12 個

### yanann_chen (40 commits)

- **2022-01-17 16:49:10**: [Web]V00-20220111001 修正系統設定「解析HTML tag」，監控流程與追蹤流程清單上「執行中的活動」活動名稱顯示為亂碼[補]
  - 變更檔案: 1 個
- **2022-01-17 16:10:26**: Revert "[Web]V00-20220111001 修正系統設定「解析HTML tag」，監控流程與追蹤流程清單上「執行中的活動」活動名稱顯示為亂碼[補]"
  - 變更檔案: 2 個
- **2022-01-17 14:29:49**: [Web]V00-20220111001 修正系統設定「解析HTML tag」，監控流程與追蹤流程清單上「執行中的活動」活動名稱顯示為亂碼[補]
  - 變更檔案: 2 個
- **2022-01-14 15:12:11**: [流程引擎]Q00-*********** 調整系統群組開窗，加入分頁功能
  - 變更檔案: 6 個
- **2022-01-14 14:19:37**: [Web]V00-20220111001 修正系統設定「要解析HTML tag」時，監控流程與追蹤流程清單上「執行中的活動」活動名稱顯示為亂碼
  - 變更檔案: 1 個
- **2022-01-12 16:44:46**: [Web]A00-20220112001 只有在簽核關卡執行加簽才顯示簽核意見欄位；不是簽核意見的關卡，執行加簽時不顯示簽核意見欄位
  - 變更檔案: 1 個
- **2022-01-11 16:58:59**: [流程引擎]Q00-20220111002 修正多人關卡在執行自動簽核時，偶發的沒有押上簽核意見或簽核意見押到正常簽核的工作上的問題
  - 變更檔案: 1 個
- **2022-01-07 15:54:13**: [流程引擎]Q00-20220107008 修正流程主旨的結尾是「\」符號，取回工作重辦清單無法呈現
  - 變更檔案: 1 個
- **2022-01-03 17:35:49**: [流程引擎]Q00-20220103004 修正流程主旨的結尾是「\」符號，系統通知(活動類型)通知清單無法呈現
  - 變更檔案: 2 個
- **2021-12-24 18:00:43**: [流程引擎]Q00-20211224002 調整取回重辦邏輯，只允許使用者從進行中的關卡執行取回重辦
  - 變更檔案: 1 個
- **2021-12-23 13:56:38**: [流程引擎]Q00-20211223001 修正流程主旨的結尾是「\」符號，工作通知清單無法呈現
  - 變更檔案: 1 個
- **2021-12-21 11:07:00**: [流程引擎]Q00-20211221001 修正透過WebService呼叫退回重辦時，被退回的關卡處理者未收到待辦事項通知信
  - 變更檔案: 1 個
- **2021-12-20 16:56:24**: [流程引擎]Q00-20211118002 修正簡易流程圖中使用前置關係人做流程預先解析，導致流程圖與流程實際派送情形不一致[補]
  - 變更檔案: 1 個
- **2021-12-17 16:35:16**: [流程引擎]A00-20211216001 關卡設定EXECUTION時，如果啟動自動簽核，簽核歷程與流程圖中關卡狀態顯示為「已會辦(自動)」
  - 變更檔案: 9 個
- **2021-12-15 14:48:07**: [流程引擎]A00-20211214001 修正若客戶流程進版後第一關的關卡ID與前一版不同，則無法於舊版流程實例使用「重發新流程」功能
  - 變更檔案: 1 個
- **2021-12-10 14:12:15**: [流程引擎]Q00-20211210001 修正流程執行退回重辦後，後續關卡設定為「2.與前一關同簽核者則跳過」的自動簽核失效問題
  - 變更檔案: 1 個
- **2021-12-09 16:21:00**: [Web]A00-20211202001修正流程關卡設定為「必須上傳新附件」，如果上傳附件後點擊兩次儲存表單，第二次儲存表單會提示必須上傳新附件[補]
  - 變更檔案: 1 個
- **2021-12-09 14:57:52**: [流程引擎]Q00-20211207002 調整若在登入頁面閒置一段時間，需要操作登入兩次才能登入BPM
  - 變更檔案: 2 個
- **2021-12-06 17:39:42**: [Web]Q00-20211206001 修正舊版絕對位置表單若有輸入「網頁提示訊息」，版更至5.8.7.1版後，日期元件顯示異常
  - 變更檔案: 1 個
- **2021-11-26 14:28:30**: [流程引擎]Q00-20211126001 條整條件式的判斷方式，進行大、小於判斷時才移除條件式內的逗號
  - 變更檔案: 1 個
- **2021-11-24 16:21:22**: Revert "[Web]Q00-20211123002 修正一般使用者在追蹤流程頁面中將『更多』按鈕隱藏"
  - 變更檔案: 1 個
- **2021-11-22 14:25:48**: [表單設計師]Q00-20211119002 表單選項元件卡控元件名稱不可重複
  - 變更檔案: 2 個
- **2021-11-19 15:44:37**: [流程引擎]Q00-20211119001 修正表單選項元件的代號與名稱不同，導致額外輸入框的內容沒有被帶回到表單上
  - 變更檔案: 1 個
- **2021-11-19 11:35:02**: [流程引擎]A00-20210908002 修正當表單選項元件勾選「額外產生出入框」且元件代號與名稱不同時，執行轉存表單失敗
  - 變更檔案: 1 個
- **2021-11-18 15:07:43**: [流程引擎]Q00-20211118002 修正簡易流程圖中使用前置關係人做流程預先解析，導致流程圖與流程實際派送情形不一致
  - 變更檔案: 1 個
- **2021-11-17 17:12:13**: [流程引擎]A00-*********** 修正表單複合元件的提示文字無法顯示多語系內容
  - 變更檔案: 2 個
- **2021-11-12 15:40:12**: [流程設計師]Q00-20211112003 修正XPDL轉BPMN流程發生閘道元件與流程關卡ID重複的問題，導致BPMN流程中的連接線連接錯誤
  - 變更檔案: 1 個
- **2021-11-11 14:05:22**: [Web]Q00-20211111002 使用FormUtil.setValue賦值給整數或浮點數Textbox欄位時，處理千分位及外顯值邏輯
  - 變更檔案: 1 個
- **2021-11-09 18:16:17**: [Web]A00-20211105001 調整JavaScript浮點數運算誤差造成單身加總計算結果不符預期
  - 變更檔案: 1 個
- **2021-11-03 18:29:48**: [Web]Q00-20211020001 流程表單設定欄位必填時，若儲存表單時必填欄位尚未填寫，就彈出相關提示訊息
  - 變更檔案: 2 個
- **2021-11-03 16:25:30**: [流程引擎]Q00-20211103001 調整自動簽核邏輯，以人員任務(UserTask)的處理者判斷是否執行自動簽核
  - 變更檔案: 1 個
- **2021-11-02 10:54:06**: [流程引擎]Q00-20211101001 使用者從portlet開啟BPM流程發起畫面，若該使用者沒有發起該流程的權限，則顯示錯誤畫面[補]
  - 變更檔案: 1 個
- **2021-11-01 15:01:36**: [流程引擎]Q00-20211101001 使用者從portlet開啟BPM指定流程的發起畫面時，若該使用者沒有發起該流程的權限，則顯示錯誤畫面
  - 變更檔案: 2 個
- **2021-10-28 18:14:16**: [Web]Q00-20211028004 移除追蹤流程與監控流程「已完成」、「已撤銷」、「已終止」清單中的「執行中的活動」欄位
  - 變更檔案: 1 個
- **2021-10-27 14:42:30**: [ESS]Q00-20211026002 調整BPM呼叫ESS存檔前的判斷，防止同單據在ESS與BPM狀態不一致
  - 變更檔案: 2 個
- **2021-10-26 17:01:31**: [ESS]Q00-20211026001 調整BPM發起ESS流程的邏輯，先檢查是否有整合ESS，再往下執行ESS相關的檢查
  - 變更檔案: 1 個
- **2021-10-25 10:57:56**: [Web]A00-20211025001 調整樹狀開窗頁面上的「加入」按鈕多語系呈現
  - 變更檔案: 2 個
- **2021-10-20 14:50:46**: [Web]Q00-20211020003 當響應式表單的下拉式選單元件設定為動態生成選項時，列印表單無法顯示欄位值
  - 變更檔案: 1 個
- **2021-10-20 11:14:27**: [流程引擎]Q00-20211019004 調整DispatchActivityForAutoAgent排程，加入WITH NOLOCK指令
  - 變更檔案: 1 個
- **2021-10-19 18:00:40**: [流程引擎]Q00-20211019009 追蹤流程時，顯示表單欄位的背景顏色
  - 變更檔案: 5 個

### 郭哲榮 (1 commits)

- **2022-01-07 17:13:50**: [BPM APP]Q00-20211105001 修正移動端FormUtil.setValue異常問題[補]
  - 變更檔案: 1 個

### pinchi_lin (1 commits)

- **2021-12-22 20:19:53**: [RESTful]C01-20211221002 修正接口因response的header中key有空白導致會有回應502的問題
  - 變更檔案: 1 個

###  (1 commits)

- **2021-12-16 17:09:16**: [Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字
  - 變更檔案: 3 個

### shihya_yu (1 commits)

- **2021-10-28 11:52:49**: [BPM APP]Q00-20211028001 修正行掛載雙表單的流程關卡狀態為進行中，不會顯示表單資訊問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
- **Commit ID**: `faf54d758f15fbd502f34051e5d2f8e8a1a321f4`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:04:59
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯
- **Commit ID**: `9c9afec793e8699296191e9ec01fb39b21b576d4`
- **作者**: 林致帆
- **日期**: 2022-01-19 15:35:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 3. [Web]V00-20220112001調整取回重辦，退回重辦時，轉派意見字眼不應顯示在待辦流程清單上[補]
- **Commit ID**: `b4b96062f72740b61476dc49d7da23d5ef00f6ce`
- **作者**: waynechang
- **日期**: 2022-01-19 14:49:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 4. [Web]A00-20211222001 修正離職作業維護選擇User後修改組織資料資料卻無法正確呈現與修改直屬主管異常
- **Commit ID**: `4de7ec855d21120e08210173f0e335fb734de7e5`
- **作者**: walter_wu
- **日期**: 2022-01-18 17:41:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`

### 5. [內部]新增E10回寫簽核歷程排程執行取得的Response的log訊息
- **Commit ID**: `6966c039e30b39834ed3a3b7e4784f1d1108d09b`
- **作者**: 王鵬程
- **日期**: 2022-01-18 11:30:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 6. [Tiptop]Q00-20220118002修正Tiptop傳的Grid沒有內容時會產生空陣列在Grid上
- **Commit ID**: `2044da1f9bb0d9cd724d0b48c1720d5e659d2feb`
- **作者**: 林致帆
- **日期**: 2022-01-18 11:22:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 7. [BPM APP]Q00-20210520004 修正Line推播訊息在通知內容為空白時會推送失敗問題
- **Commit ID**: `915e8fe20f5a401657592cabfceea878c77db125`
- **作者**: yamiyeh10
- **日期**: 2022-01-18 11:01:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`

### 8. [BPM APP]Q00-20220105002 修正當系統啟用動態渲染表單顯示且流程掛載多表單時，IMG各列表出現多筆重複流程的問題
- **Commit ID**: `acbbe0b143da21fe9fed347b59691a3c35a00585`
- **作者**: cherryliao
- **日期**: 2022-01-17 18:38:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`

### 9. [Web]V00-20220112002 修正檢視參與者型式的關卡中的接收者(讀取次數)，會出現<br>
- **Commit ID**: `edc1214f4b7e184aa8cb199ce7007792e6af8ab4`
- **作者**: walter_wu
- **日期**: 2022-01-17 17:01:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 10. [Web]V00-20220111001 修正系統設定「解析HTML tag」，監控流程與追蹤流程清單上「執行中的活動」活動名稱顯示為亂碼[補]
- **Commit ID**: `297b2b71f2de4f258ccd7adda95b1b965741acf9`
- **作者**: yanann_chen
- **日期**: 2022-01-17 16:49:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 11. Revert "[Web]V00-20220111001 修正系統設定「解析HTML tag」，監控流程與追蹤流程清單上「執行中的活動」活動名稱顯示為亂碼[補]"
- **Commit ID**: `24ab097ef4df9629bcf20e5d43274ede5228a368`
- **作者**: yanann_chen
- **日期**: 2022-01-17 16:10:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 12. [Web]V00-20220111001 修正系統設定「解析HTML tag」，監控流程與追蹤流程清單上「執行中的活動」活動名稱顯示為亂碼[補]
- **Commit ID**: `e63f3f874902d8d7675dc28a5f500d1f49f784d8`
- **作者**: yanann_chen
- **日期**: 2022-01-17 14:29:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 13. [WorkFlow]Q00-20220117001WorkFlow預設流程調整移除通知關卡
- **Commit ID**: `5bed57b8c8c78b224ed724a33028eeb23f7a834c`
- **作者**: 林致帆
- **日期**: 2022-01-17 10:52:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@workflow/process-default/bpmn/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(WorkFlowERP_PURI05).bpmn"`

### 14. [Web]S00-20211124001 提供行動版在發起流程頁面可調整流程緊急度
- **Commit ID**: `56f7d78074a60ad5a763fd9fb4d594c3daa88d60`
- **作者**: 王鵬程
- **日期**: 2022-01-14 17:22:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 15. [流程引擎]Q00-*********** 調整系統群組開窗，加入分頁功能
- **Commit ID**: `1d1114546d46571d26c03efcd0db55114a37ee10`
- **作者**: yanann_chen
- **日期**: 2022-01-14 15:12:11
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbstractListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/GroupListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`

### 16. [ISO]S00-*********** ISO模組增加支持「絕對位置ISO批次作廢單」的批次作廢流程」服務接口「批次申請作廢、批次取消作廢、批次作廢文件」
- **Commit ID**: `3468511c1ca75bcfb1dd6bbf5e1273a5639cdca2`
- **作者**: waynechang
- **日期**: 2022-01-14 15:02:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`

### 17. [流程設計工具]V00-20220105001 調整流程設計工具的BPM版本檢核邏輯，由驗證版本前三碼改為驗證版本四碼都符合才允許開啟
- **Commit ID**: `a3c2ec5e209f2047845c4697dab14958abfb5d70`
- **作者**: waynechang
- **日期**: 2022-01-14 14:38:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java`

### 18. [Web]V00-20220111001 修正追蹤"流程清單預設查看的流程狀態"更改設定後點及追蹤流程清單還是設定前的狀態
- **Commit ID**: `584a9cf417b545ee3157b898a06684072d284f52`
- **作者**: 林致帆
- **日期**: 2022-01-14 14:35:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 19. [Web]V00-20220111001 修正系統設定「要解析HTML tag」時，監控流程與追蹤流程清單上「執行中的活動」活動名稱顯示為亂碼
- **Commit ID**: `a6f180fb8114e6faf5d9fb664801d6f3ee6a0ab3`
- **作者**: yanann_chen
- **日期**: 2022-01-14 14:19:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 20. [WEB]V00-20200616004 調整監控流程、追蹤流程頁面，當預設查看的流程狀態為「未結案」時，將Gird欄位「流程結案時間」隱藏
- **Commit ID**: `3145f14e5294ce42e60fdf994ef17c676110bde8`
- **作者**: waynechang
- **日期**: 2022-01-14 14:09:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 21. [Web]V00-20220112001調整取回重辦，退回重辦時，轉派意見字眼不應顯示在待辦流程清單上
- **Commit ID**: `80256aa5c9ddac4a849b73f697fa27aff72f23b5`
- **作者**: 林致帆
- **日期**: 2022-01-14 13:50:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 22. [Web]V00-20220113001 修正系統排程設定的編輯工作觸發程序中選擇每天、每週、每月進去的時間下拉元件太窄導致內容無法完整顯示
- **Commit ID**: `c426051d21d3c582e8765eb734d4d83287bae879`
- **作者**: 王鵬程
- **日期**: 2022-01-14 11:59:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`

### 23. [Web]A00-20220110001 修正流程關卡用預設代理人處理，若該關卡沒有預設代理人，不會出現提示訊息畫面[補修正]
- **Commit ID**: `f81465674295393a895348b4067df80dc7f51809`
- **作者**: 林致帆
- **日期**: 2022-01-13 15:32:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ExceptionMessageCreator.java`

### 24. [BPM APP]Q00-20220112003 修正系統設定追蹤清單的順序時，IMG追蹤列表上方篩選列顯示空白問題
- **Commit ID**: `34f722011b6e891cbab2d79062f68ee6a2112d39`
- **作者**: yamiyeh10
- **日期**: 2022-01-13 14:33:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

### 25. [Web]A00-20220112001 只有在簽核關卡執行加簽才顯示簽核意見欄位；不是簽核意見的關卡，執行加簽時不顯示簽核意見欄位
- **Commit ID**: `ce125dadc245677f4c9760a88cad38a5fe6049a6`
- **作者**: yanann_chen
- **日期**: 2022-01-12 16:44:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 26. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `f3ae6de0df2b840c98bd2892b4837b9eb63e076f`
- **作者**: waynechang
- **日期**: 2022-01-12 15:42:52
- **變更檔案數量**: 0

### 27. Revert "[流程引擎]Q00-20211001001 移除NaNaLog、NaNaWebLog寫log機制，並將log訊息統一寫至server.log"
- **Commit ID**: `7b92d01663c3e4b9d98a969b6f1c6814aec15bda`
- **作者**: waynechang
- **日期**: 2022-01-12 15:42:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/logging/NaNaLogFactoryImpl.java`

### 28. [BPM APP]Q00-20211111005 調整行動端追蹤已簽核和已發起表單的取回重辦機制
- **Commit ID**: `bf3f90e31d133781f95e016ebf5594c3a0aec03d`
- **作者**: yamiyeh10
- **日期**: 2022-01-12 15:23:39
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 29. [Web]A00-20220110001 修正流程關卡用預設代理人處理，若該關卡沒有預設代理人，不會出現提示訊息畫面
- **Commit ID**: `819fd973561817d283b04ae5403dc5b35fa4ee0b`
- **作者**: 林致帆
- **日期**: 2022-01-12 10:49:50
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 30. [流程引擎]Q00-20220111002 修正多人關卡在執行自動簽核時，偶發的沒有押上簽核意見或簽核意見押到正常簽核的工作上的問題
- **Commit ID**: `b66c53d4e48be16239ca57a74943a0a13f841e34`
- **作者**: yanann_chen
- **日期**: 2022-01-11 16:58:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 31. [BPM APP]Q00-20211122001 修正行動表單自動轉換功能在切分多欄位且未擺入元件時會有部分元件未被生成問題
- **Commit ID**: `6b6d1e02f614139dc045aad288fed4370db8ebcc`
- **作者**: yamiyeh10
- **日期**: 2022-01-10 18:07:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 32. [Web]S00-20211012002 在BPM文件/設計工具下載 頁面，調整下載順序並增加step
- **Commit ID**: `fd76b24766412c25a2cae6f03c84ee9f51a12e61`
- **作者**: 王鵬程
- **日期**: 2022-01-10 17:28:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DesignerDownload/DesignerDownloadMain.jsp`

### 33. [流程引擎]Q00-20211220002 修正客戶附件遺失問題
- **Commit ID**: `cae21fde5911aa6a7bacf677c2a4bce84d4d96d8`
- **作者**: walter_wu
- **日期**: 2022-01-10 17:19:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 34. [Web]Q00-20220107009 調整行動版時左側的選單背景色及字體顏色也要與PC版一致
- **Commit ID**: `b617d735768aa8068ee2dad5aa5f80450c903615`
- **作者**: 王鵬程
- **日期**: 2022-01-07 17:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 35. [BPM APP]Q00-20211105001 修正移動端FormUtil.setValue異常問題[補]
- **Commit ID**: `27d54bf0de811d50f31b8415639d86f26f2bae77`
- **作者**: 郭哲榮
- **日期**: 2022-01-07 17:13:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 36. [流程引擎]Q00-20220107008 修正流程主旨的結尾是「\」符號，取回工作重辦清單無法呈現
- **Commit ID**: `cd5bb2ada4532a1ed33c51995254a24e172949d4`
- **作者**: yanann_chen
- **日期**: 2022-01-07 15:54:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 37. [BPM APP]Q00-20211111003 修正行動端表單在取回重辦後簡易簽核歷程區塊會多產生一組資料問題
- **Commit ID**: `da5b242d836f9f8227640d6953c9c274058bffae`
- **作者**: yamiyeh10
- **日期**: 2022-01-07 15:03:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 38. [Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢
- **Commit ID**: `5849bbba4797ad3f595b9e93bc2a896b456efe5e`
- **作者**: 林致帆
- **日期**: 2022-01-07 10:59:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 39. [TIPTOP]Q00-20220107001 出貨版本新增TIPTOP RWD響應式表單
- **Commit ID**: `8cfd9f3b420d7f5e6c1caf45f95a40fc744e2868`
- **作者**: 林致帆
- **日期**: 2022-01-07 09:24:23
- **變更檔案數量**: 127
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt110.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt120.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt121.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt150.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt151.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt160.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt210.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt220.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt260.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt330.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aapt331.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/abmi710.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/abmi720.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/abmi901.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/abmt330.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat102.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat103.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat104.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat105.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat106.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat107.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat108.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat109.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat110.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat111.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat300.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/afat305.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aglt110.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aglt130.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aict040.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aict041.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aict042.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aict043.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aict044.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimi150.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt301.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt301_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt302.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt302_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt303.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt303_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt311.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt311_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt312.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt312_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt313.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt313_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt324.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/aimt324_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmi255.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmi265.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmi610.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt110.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt110_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt200.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt200_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt300.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt420.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt540.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt540_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt590.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt590_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt720.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt720_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt721.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt721_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt722.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt722_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt730.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt730_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt731.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt731_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt740.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt741.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt742.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt900.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/apmt910.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi301.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi301_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi500.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi511.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi511_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi512.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi512_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi513.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi513_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi514.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi514_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi526.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi526_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi527.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi527_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi528.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi528_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi529.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asfi529_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asft620.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asft620_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asft803.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asri210.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asri220.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asri230.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/asrt320.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmi250.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt360.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt400.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt410.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt410_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt420.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt610.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt610_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt620.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt620_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt628.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt640.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt640_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt700.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt800.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt800_icd.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt810.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt820.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt821.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt840.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt850.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axrt300.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axrt400.form`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/gglt140.form`

### 40. [BPM APP]Q00-20220106010 修正行動版表單jDalert彈窗機制顯示一次後，後續的彈窗不會顯示遮罩問題
- **Commit ID**: `7a9fbbee20441e5285fefe839a78c60571c8d498`
- **作者**: yamiyeh10
- **日期**: 2022-01-06 15:11:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js`

### 41. [流程設計師]Q00-20220106008 修正在流程定義視窗中選擇標頭，勾選是否逾時多次通知後在進來該視窗都會變未勾選
- **Commit ID**: `567af64714663658c54a20ee395795aa80c68b88`
- **作者**: 王鵬程
- **日期**: 2022-01-06 14:52:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`

### 42. [Web]Q00-20220106001 調整加簽關卡的頁面中，關卡名稱允許單字之間使用空格
- **Commit ID**: `74c2ccfccaddd67a04c7c31de706f545b1274ea6`
- **作者**: 王鵬程
- **日期**: 2022-01-06 11:47:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 43. [流程引擎]Q00-20211001001 移除NaNaLog、NaNaWebLog寫log機制，並將log訊息統一寫至server.log
- **Commit ID**: `76d0258b8167af8940d779baeefb753863570590`
- **作者**: waynechang
- **日期**: 2022-01-05 17:40:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/logging/NaNaLogFactoryImpl.java`

### 44. [BPM APP]Q00-20211118001 修正IMG在追蹤已簽核畫面執行取回重辦按鈕後原先取消關注按鈕會被更改成關注按鈕的問題
- **Commit ID**: `5e3fa8fe1c797894a69fd3bd5f2ed25a757e4c6a`
- **作者**: yamiyeh10
- **日期**: 2022-01-05 17:24:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`

### 45. [BPM APP]Q00-20211223002 修正行動模擬簽核按鈕在已轉派流程上出現問題
- **Commit ID**: `b433487707b0f23915f300996af642a46d5fbba2`
- **作者**: yamiyeh10
- **日期**: 2022-01-05 11:30:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 46. [內部]新增Commit變更記錄檔
- **Commit ID**: `3dbda16f16a5f544a3bc592ace3b01aaaa413b91`
- **作者**: lorenchang
- **日期**: 2022-01-04 17:09:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `"Commit\350\256\212\346\233\264\350\250\230\351\214\204\346\252\224.md"`

### 47. [Web]Q00-20220104002 調整簡體語系登入時，在待辦清單頁中Grid標頭的『名稱』調整為『關卡名稱』
- **Commit ID**: `913efb64dea5e86019dd4535f215e74be9fe1c1b`
- **作者**: 王鵬程
- **日期**: 2022-01-04 11:45:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 48. [流程引擎]Q00-20220103004 修正流程主旨的結尾是「\」符號，系統通知(活動類型)通知清單無法呈現
- **Commit ID**: `930ac7553aef5768dd37686bfbee12418cbce7f2`
- **作者**: yanann_chen
- **日期**: 2022-01-03 17:35:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ActivityNotiListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/WfNotificationListReader.java`

### 49. [Web]A00-20220103001 修正查看追蹤流程頁面,包含表單及流程的URL，URL中沒有表單定義ID就不開放給一般使用者查看
- **Commit ID**: `a1917d6317ff929d9a71c7b5367db3f9047cbd1e`
- **作者**: 林致帆
- **日期**: 2022-01-03 15:03:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 50. [BPM APP]Q00-20211012001 調整入口平台整合設定中連線管理頁面在整合鼎捷移動時增加鼎捷移動平台管理後台填外網提示[補]
- **Commit ID**: `7fc277a609fbdeb51020c6f10b779fdbf5c6fb5f`
- **作者**: yamiyeh10
- **日期**: 2021-12-30 10:52:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 51. [內部]Q00-20211228001調整易飛orWFERP整合主機設定兩台多主機時，會無法開單
- **Commit ID**: `5ae9a45963cf594a493dc3516cbcdd0b95009afe`
- **作者**: 林致帆
- **日期**: 2021-12-28 15:03:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 52. [Web]Q00-20211227001 修正使用IE在絕對表單沒有附件時預覽列印，會導致簽核歷程和表單內容重疊
- **Commit ID**: `b8f192b35438e04d5d0ff617750b9f3124bf7b45`
- **作者**: 王鵬程
- **日期**: 2021-12-27 15:24:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 53. [Web]A00-20211223001 修正監控流程清單頁連結不開放給系統管理員以外的人員[補修正]
- **Commit ID**: `5c5ba887b6a652d713312dd50dae039f8dbc9cf4`
- **作者**: 林致帆
- **日期**: 2021-12-27 10:54:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 54. [流程引擎]Q00-20211224002 調整取回重辦邏輯，只允許使用者從進行中的關卡執行取回重辦
- **Commit ID**: `7387ffbaab60e6a62b5585eb5bb1522cb5e0f38c`
- **作者**: yanann_chen
- **日期**: 2021-12-24 18:00:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 55. Revert "[ESS]Q00-20211224001 調整當進入為ESS流程時不提供儲存表單按鈕"
- **Commit ID**: `cc18403fe6c01427487e2f079719140bb7fe6e8f`
- **作者**: 王鵬程
- **日期**: 2021-12-24 17:48:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 56. [ESS]Q00-20211224001 調整當進入為ESS流程時不提供儲存表單按鈕
- **Commit ID**: `82e4f2001d1b79558f74972ba246b6f503e220bf`
- **作者**: 王鵬程
- **日期**: 2021-12-24 14:36:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 57. [BPM APP]S00-20210926001 在行動端的企業微信整合方案新增已轉派列表與表單畫面功能[補]
- **Commit ID**: `fdc3ce72afe92c1bc473f8c032acd603c7a94a38`
- **作者**: yamiyeh10
- **日期**: 2021-12-23 18:16:32
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js`

### 58. [流程引擎]S00-20210511001 新增追蹤流程列表可依流程結案時間做排序
- **Commit ID**: `369eff7c77ae67d129cb2f4dd10d16ea9822368c`
- **作者**: cherryliao
- **日期**: 2021-12-23 16:46:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 59. [Web]A00-20211223001 修正監控流程清單頁連結不開放給系統管理員以外的人員
- **Commit ID**: `ff90471439a285963d20cad579bbc4cd9c824e97`
- **作者**: 林致帆
- **日期**: 2021-12-23 14:20:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 60. [流程引擎]Q00-20211223001 修正流程主旨的結尾是「\」符號，工作通知清單無法呈現
- **Commit ID**: `b2563a237e26c46fe9fe1cdc91f2556ef465eee6`
- **作者**: yanann_chen
- **日期**: 2021-12-23 13:56:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`

### 61. [RESTful]C01-20211221002 修正接口因response的header中key有空白導致會有回應502的問題
- **Commit ID**: `39a8f91513c80a3768c86d8632def3d5948c31d0`
- **作者**: pinchi_lin
- **日期**: 2021-12-22 20:19:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/EAIHeaderKey.java`

### 62. [Web]A00-20211220001 修正在行動版時在我的最愛內的常用流程與常用功能維護無法儲存
- **Commit ID**: `2b7aa52fa134223c17fb56c221b19a1c07b3b9c7`
- **作者**: 王鵬程
- **日期**: 2021-12-22 15:21:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`

### 63. [Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字[補]
- **Commit ID**: `11f5ec016d68e5e97aa82d916b2d3f352eae3668`
- **作者**: 王鵬程
- **日期**: 2021-12-22 14:26:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/CustomFonts/.gitkeep`

### 64. [BPM APP]C01-*********** 調整行動端儲存草稿一律必填草稿流程主旨[補]
- **Commit ID**: `fb32a42c57cfb7334909f393856f45d5afb6ad65`
- **作者**: yamiyeh10
- **日期**: 2021-12-21 18:24:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`

### 65. [流程引擎]Q00-20211221001 修正透過WebService呼叫退回重辦時，被退回的關卡處理者未收到待辦事項通知信
- **Commit ID**: `60f16b565292156224782ef46a402e33dfd4f4c0`
- **作者**: yanann_chen
- **日期**: 2021-12-21 11:07:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 66. [流程引擎]Q00-20211118002 修正簡易流程圖中使用前置關係人做流程預先解析，導致流程圖與流程實際派送情形不一致[補]
- **Commit ID**: `267e92861084644afde941869ea3bcf438ff0f6d`
- **作者**: yanann_chen
- **日期**: 2021-12-20 16:56:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 67. [WEB]Q00-20211220001 修正表單有選擇元件(RadioBox,ComboBox,CheckBox,ListBox)，且在流程該關卡有將選擇元件設定為invisible時；若該關卡重新上傳附件時，上傳附件畫面為空白，並且無法上傳的異常
- **Commit ID**: `bf3dabf0055040de9518a09e627aa1529a24ff77`
- **作者**: waynechang
- **日期**: 2021-12-20 16:08:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`

### 68. [Web]S00-20210914003 調整BPM登入頁面可用瀏覽器的儲存密碼功能
- **Commit ID**: `8002f7ef0808e13cc7c16d5d4352ad4138cdbd43`
- **作者**: cherryliao
- **日期**: 2021-12-20 13:54:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 69. [流程引擎]A00-20211216001 關卡設定EXECUTION時，如果啟動自動簽核，簽核歷程與流程圖中關卡狀態顯示為「已會辦(自動)」
- **Commit ID**: `654fd27698a477622cf3d13a236ff66ee79766ce`
- **作者**: yanann_chen
- **日期**: 2021-12-17 16:35:16
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkItemStateType.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 70. [流程引擎]Q00-20211217001 修正當資料庫為oracle時，SQL註冊器未輸入任何條件查詢會報錯
- **Commit ID**: `6591be815c90f5b1151d7c380ede390824b92a5d`
- **作者**: 王鵬程
- **日期**: 2021-12-17 14:15:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 71. [Web]Q00-20211216004 修正新增排程的頁面中，排程生效時間下拉元件太窄導致內容無法完整顯示
- **Commit ID**: `72e39b33f93ec3693cae1cc23d6a374b71768d37`
- **作者**: 王鵬程
- **日期**: 2021-12-16 18:24:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`

### 72. [Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字
- **Commit ID**: `7e2a9ab162faf63b7abe36cfa6ea70c21b196d16`
- **作者**: 王鵬程
- **日期**: 2021-12-16 17:48:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 73. Revert "[Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字"
- **Commit ID**: `835ed44fc661518b890d39b0539460fe6c30e6a0`
- **作者**: 王鵬程
- **日期**: 2021-12-16 17:27:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 74. [Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字
- **Commit ID**: `669dd0647e41589c8463b6ba75ab3f8576dc482d`
- **作者**: 
- **日期**: 2021-12-16 17:09:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 75. [Web]Q00-20211216001 修正SQLCommand的SQL指令帶有百分比及加號會導致報錯
- **Commit ID**: `0e6148426054a66664fe75572a271bec56d7edec`
- **作者**: 林致帆
- **日期**: 2021-12-16 11:56:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 76. [SQLCommand]Q00-20211215001 修正用Ajax下SQL如果欄位行態是text無法找出資料
- **Commit ID**: `71e1703ee4dab832fd305a0c3c40639ffe346a84`
- **作者**: walter_wu
- **日期**: 2021-12-15 16:13:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 77. [流程引擎]A00-20211214001 修正若客戶流程進版後第一關的關卡ID與前一版不同，則無法於舊版流程實例使用「重發新流程」功能
- **Commit ID**: `67e1a73d58c4c2cf1ca6806fc958ae1c7495022c`
- **作者**: yanann_chen
- **日期**: 2021-12-15 14:48:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 78. [BPM APP]S00-20210926001 在行動端的企業微信整合方案新增已轉派列表與表單畫面功能[補]
- **Commit ID**: `aa02f052a064e63ce78c614f3ab6960ee890ca4c`
- **作者**: yamiyeh10
- **日期**: 2021-12-15 11:03:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListResigendV2.jsp`

### 79. [組織設計師]S00-20210429005 調整組織設計師修改人員資料角色設定開窗時增加checkbox多選欄位並載入使用者的角色預設勾選[補]
- **Commit ID**: `b8e09b83002ad3755a92ad6921049c82eb3b5439`
- **作者**: waynechang
- **日期**: 2021-12-15 10:51:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OwnerOrgUnitEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/table/DefinitionTableController.java`

### 80. [Web]Q00-20211214001 修正報表設計器樣板的匯出Excel按鈕多語系錯誤
- **Commit ID**: `348c30494cf24005743f0f96b68e54472fd2a9e3`
- **作者**: 林致帆
- **日期**: 2021-12-14 17:41:16
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/ChartQueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_en_US.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_vi_VN.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_zh_CN.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_zh_TW.js`

### 81. [組織設計師]S00-20210429005 調整組織設計師修改人員資料角色設定開窗時增加checkbox多選欄位並載入使用者的角色預設勾選
- **Commit ID**: `2b957184601456ff44027120a411c2925f250ebd`
- **作者**: waynechang
- **日期**: 2021-12-14 17:19:17
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/table/model/EditRoleModeTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OwnerOrgUnitEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/AbstractTableDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainRoleController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainRoleDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/table/DefinitionTableController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MaintainAbstractRoleDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MaintainAbstractRoleDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MaintainAbstractRoleDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MaintainAbstractRoleDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/MaintainAbstractRoleDialog_zh_TW.properties`

### 82. [Web]S00-20210810002 新增RWD表單DialogInputLabel元件可調整輸入框顯示比例的設定
- **Commit ID**: `eae4987b434101413de53c7220ae9f06a39ec30b`
- **作者**: cherryliao
- **日期**: 2021-12-13 15:01:10
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DialogInputLabelElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 83. [Web]A00-20211209001 調整離職維護作業的時間輸入欄位點擊應為時間開窗供使用者選擇
- **Commit ID**: `27f63cebf5f5921c2c1e6e508eeb1bb5d86fa113`
- **作者**: 林致帆
- **日期**: 2021-12-13 11:18:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`

### 84. [流程引擎]Q00-20211210001 修正流程執行退回重辦後，後續關卡設定為「2.與前一關同簽核者則跳過」的自動簽核失效問題
- **Commit ID**: `64b85a34feaf557ceadb4b7e82eedfc7450ade7a`
- **作者**: yanann_chen
- **日期**: 2021-12-10 14:12:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 85. [Web]S00-20211122001 監控流程的查詢條件的流程開窗，調整流程名稱為多語系
- **Commit ID**: `8296176d2ad8a3e3ed02806c1bdd752af881ecbc`
- **作者**: 林致帆
- **日期**: 2021-12-09 17:11:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 86. [BPM APP]S00-20210926001 在行動端的企業微信整合方案新增已轉派列表與表單畫面功能
- **Commit ID**: `8085809673f956612778a8f20e1d92b8ac2255bd`
- **作者**: yamiyeh10
- **日期**: 2021-12-09 16:37:26
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileResigendServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListResigendV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmMobileLibrary.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 87. [Web]A00-20211202001修正流程關卡設定為「必須上傳新附件」，如果上傳附件後點擊兩次儲存表單，第二次儲存表單會提示必須上傳新附件[補]
- **Commit ID**: `59010d11917e72701ad5db15cf88703f546dac95`
- **作者**: yanann_chen
- **日期**: 2021-12-09 16:21:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 88. [Web]Q00-20211209001 調整Grid綁定Checkbox與Radio元件時增加判斷元件是否存在機制防止發生找不到元件的問題
- **Commit ID**: `c4cb09df27cbe7902b6516c6bf3cd4bae4ba1e7e`
- **作者**: yamiyeh10
- **日期**: 2021-12-09 16:17:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 89. [流程引擎]Q00-20211207002 調整若在登入頁面閒置一段時間，需要操作登入兩次才能登入BPM
- **Commit ID**: `a5c9bfc9c85be46085b39751348b590b5877cec6`
- **作者**: yanann_chen
- **日期**: 2021-12-09 14:57:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 90. [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤[補修正]
- **Commit ID**: `ec0252ff7248fc4200cb1def3fb12424afb8ee82`
- **作者**: 林致帆
- **日期**: 2021-12-08 17:40:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`

### 91. [Web]A00-20211202001修正流程關卡設定為"必須上傳新附件"，如果上傳附件後點擊兩次儲存表單，第二次儲存表單會提示必須上傳新附件
- **Commit ID**: `622bdc6999a8a6e0f2d35c64ce7def434822b79d`
- **作者**: 林致帆
- **日期**: 2021-12-08 16:49:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 92. [BPM APP]C01-20211202008 修正產品部門開窗在快搜時會顯示失效部門問題
- **Commit ID**: `7ebf4ff0a78923dee42376024f811625621e2ec8`
- **作者**: yamiyeh10
- **日期**: 2021-12-08 16:01:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OrgUnitCacheSingletonMap.java`

### 93. [Web]Q00-20211207001 修正CSS的樣式缺少右大擴號的錯誤導致寫在後面的CSS無法生效
- **Commit ID**: `fcf4b9ef61c177ce6e4ef69653667418f8a846bd`
- **作者**: 王鵬程
- **日期**: 2021-12-07 17:03:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 94. [Web]S00-20210429008 上傳附件名稱長度調整為可輸入250的字[補]
- **Commit ID**: `d0a35862079fdb8e6aaa5f11124f263436561277`
- **作者**: waynechang
- **日期**: 2021-12-07 15:19:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.7.1_DDL_Oracle_1.sql`

### 95. [SAP]A00-20211203001 修正SAP整合，當SAP回傳資料須回寫至Grid時，但SAP回傳Grid資料為空時，Grid原先row的內容需一併清除
- **Commit ID**: `c5abc89fcac8cd8cc10eb250b8ada5e3cf7d152d`
- **作者**: waynechang
- **日期**: 2021-12-07 15:01:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`

### 96. [Web]Q00-20211206001 修正舊版絕對位置表單若有輸入「網頁提示訊息」，版更至5.8.7.1版後，日期元件顯示異常
- **Commit ID**: `f8d769e420629405f08137087abeaf002d66ce8a`
- **作者**: yanann_chen
- **日期**: 2021-12-06 17:39:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DateTemplate.txt`

### 97. [Web]A00-20211201001 修正Rwd表單有設定元件的背景色、文字顏色時，在列印時無法印出色彩[補]
- **Commit ID**: `7e503521aa6fb8f89b2e69fbcd74495cc3d7d681`
- **作者**: 王鵬程
- **日期**: 2021-12-06 15:01:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-3.3.5-print.css`

### 98. [Web]A00-20211201001 修正Rwd表單有設定元件的背景色、文字顏色時，在列印時無法印出色彩
- **Commit ID**: `74355eaf3310d085f39b6996b75b5929ae889f20`
- **作者**: 王鵬程
- **日期**: 2021-12-03 14:47:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-3.3.5-print.css`

### 99. [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤
- **Commit ID**: `94a2db3a325927235cbc4b1a05613bf0e32c2e5c`
- **作者**: 林致帆
- **日期**: 2021-12-02 16:41:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 100. [Web]A00-20211201002 修正追蹤流程點擊匯出Excel會報錯
- **Commit ID**: `31a086e878f488bbdeb77ba1b6777949aabd7359`
- **作者**: 林致帆
- **日期**: 2021-12-02 09:08:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 101. [流程引擎]Q00-20211201002  修正人員名稱有新的特殊字(慈)時，進入ESS流程會報錯
- **Commit ID**: `681cbf6ff34a31ce8db69ebfcdfe65a8268d117a`
- **作者**: 王鵬程
- **日期**: 2021-12-01 15:08:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 102. [ESS]Q00-20211201001表單作者名稱調整為系統管理員
- **Commit ID**: `8804969d1cea297b797b239d2ff460de50b4fc99`
- **作者**: 林致帆
- **日期**: 2021-12-01 13:41:51
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF01\346\216\222\347\217\255\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF03\350\243\234\345\210\267\345\215\241\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF04\345\212\240\347\217\255\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF06\345\212\240\347\217\255\350\252\277\344\274\221\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF07\350\253\213\345\201\207\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF23\350\252\277\350\201\267\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF25\350\275\211\346\255\243\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF26\347\215\216\346\207\262\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF27\351\233\242\350\201\267\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF29\350\275\211\346\255\243\350\252\277\350\226\252\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF34\351\214\204\347\224\250\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF43\350\277\260\350\201\267\345\240\261\345\221\212.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF44\350\200\203\346\240\270\350\251\225\345\210\206.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF46\350\200\203\346\240\270\347\224\263\350\250\264.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF47\350\200\203\346\240\270\346\224\271\351\200\262.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF52C1\347\217\255\346\254\241\344\272\222\346\217\233.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF61\350\252\262\347\250\213\351\226\213\347\231\274\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF67\345\237\271\350\250\223\345\240\261\345\220\215.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF68\345\217\226\346\266\210\345\237\271\350\250\223\345\240\261\345\220\215.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF69\345\223\241\345\267\245\347\225\260\345\213\225\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF72\345\223\241\345\267\245\345\240\261\345\210\260\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/ESSF76\345\217\254\345\213\237\346\224\271\351\200\262\345\273\272\350\255\260.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF05\345\212\240\347\217\255\350\250\210\345\212\203\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF08\347\251\215\344\274\221\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF52C1\347\217\255\346\254\241\344\272\222\346\217\233.form"`

### 103. [ESS]Q00-20211112002 修正ESS流程 A員工在 A電腦進行儲存草稿動作，A員工在B電腦打開該草稿時，ESS表單開啟報錯
- **Commit ID**: `2527fc85421957b75421f9f93faa5f3cf715995c`
- **作者**: 林致帆
- **日期**: 2021-11-26 17:21:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 104. [流程引擎]Q00-20211126001 條整條件式的判斷方式，進行大、小於判斷時才移除條件式內的逗號
- **Commit ID**: `9878dcb2ccb8a2a86b119de0d083a195bdbd5161`
- **作者**: yanann_chen
- **日期**: 2021-11-26 14:28:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/ConditionEvaluator.java`

### 105. [流程引擎]Q00-20211125001 修正透過HR小助手同步，當人員的兼職部門直屬主管在HR那已設空值，同步後卻未被改成空值[補]
- **Commit ID**: `0e46f30676c4def75f3febafb94d8802f7cf5993`
- **作者**: 王鵬程
- **日期**: 2021-11-25 17:43:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/syncorg/SyncTable_Oracle.properties`

### 106. [流程引擎]Q00-20211125001 修正透過HR小助手同步，當人員的兼職部門直屬主管在HR那已設空值，同步後卻未被改成空值
- **Commit ID**: `76483c5c4a4f9d8ddc1410a664525ea11b29b36e`
- **作者**: 王鵬程
- **日期**: 2021-11-25 15:47:34
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/AppProperties.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/SyncTableConstants.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/syncorg/SyncTable.properties`

### 107. [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]
- **Commit ID**: `68aabee8d8a0da3e27736bfc020a2ca370001542`
- **作者**: yamiyeh10
- **日期**: 2021-11-25 14:40:53
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 108. [Web]Q00-20211124001 修正一般使用者在追蹤流程頁面中將『更多』按鈕隱藏
- **Commit ID**: `52adae2ba8860b6fc493a4e670e9b17fa650385c`
- **作者**: 王鵬程
- **日期**: 2021-11-24 18:03:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 109. Revert "[Web]Q00-20211123002 修正一般使用者在追蹤流程頁面中將『更多』按鈕隱藏"
- **Commit ID**: `af2f765926d1e90a6f8dd65012ce922b511d242f`
- **作者**: yanann_chen
- **日期**: 2021-11-24 16:21:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 110. [Web]S00-20210810008 新增手機版頁面待辦列表若包含可批簽流程時有全選的功能
- **Commit ID**: `69e3db5b36ba1bc45b06943483cc4eee35af4190`
- **作者**: cherryliao
- **日期**: 2021-11-24 13:38:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 111. [Web]Q00-20211123002 修正一般使用者在追蹤流程頁面中將『更多』按鈕隱藏
- **Commit ID**: `dfd834c06be97c566f517a73120f750579ecb50d`
- **作者**: 王鵬程
- **日期**: 2021-11-23 17:33:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 112. [Web]S00-20210810009 調整Web端表單Label元件文字過長時樣式
- **Commit ID**: `28030bee389a699ece809daa834a42166c6a964a`
- **作者**: cherryliao
- **日期**: 2021-11-23 13:50:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 113. [Web]S00-*********** 待辦列表新增讀取狀態的圖示[補]
- **Commit ID**: `b512cc8eff77ec3e8b015b225ea48c3680e05149`
- **作者**: cherryliao
- **日期**: 2021-11-22 17:33:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 114. [內部]S00-20211112001 組織設計師及組織同步工具增加參數「orgdesigner.unitid.passsymbol」判斷Id是否檢查特殊符號
- **Commit ID**: `33e5031978bf2a23bcbf338c20dba5a9f8677bb4`
- **作者**: waynechang
- **日期**: 2021-11-22 16:14:49
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/GroupEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OrgEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OrgUnitEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/UserCreator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`

### 115. [WorkFlow]Q00-***********修正WorkFlow在單據進行取消確認時，對該單據抽單，回傳的狀態碼有誤導致WorkFlow作業為待處理
- **Commit ID**: `132913cb9bd7894b8b96b13531c6bc72d21b329a`
- **作者**: 林致帆
- **日期**: 2021-11-22 15:22:06
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WorkFlowDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IWFRequestRecordDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBWFRequestRecordDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 116. [BPM APP]C01-*********** 調整行動端儲存草稿一律必填草稿流程主旨
- **Commit ID**: `09cbb841392e9fa353d94497a686d67a9ba4b28f`
- **作者**: yamiyeh10
- **日期**: 2021-11-22 14:53:20
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 117. [表單設計師]Q00-20211119002 表單選項元件卡控元件名稱不可重複
- **Commit ID**: `bab08937d2b4c317ca38e2d648e54623c3f420c7`
- **作者**: yanann_chen
- **日期**: 2021-11-22 14:25:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 118. [Web]S00-20210621002 可偵測出非支援瀏覽器時會彈出警告視窗，並一律在登入按鈕下方增加建議的瀏覽器資訊[補]
- **Commit ID**: `5d37f4325e0ac435981dd32db688921e843f0f8b`
- **作者**: 王鵬程
- **日期**: 2021-11-19 17:15:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 119. [Web]S00-20210621002 可偵測出非支援瀏覽器時會彈出警告視窗，並一律在登入按鈕下方增加建議的瀏覽器資訊
- **Commit ID**: `e44518bdadbcb397bc5e0729ab9b267b314cb7aa`
- **作者**: 王鵬程
- **日期**: 2021-11-19 17:05:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/ua-parser.js`

### 120. [流程引擎]Q00-20211119001 修正表單選項元件的代號與名稱不同，導致額外輸入框的內容沒有被帶回到表單上
- **Commit ID**: `0c6e09d27599373ec055aa8a832b2a7b3101a797`
- **作者**: yanann_chen
- **日期**: 2021-11-19 15:44:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 121. [流程引擎]A00-20210908002 修正當表單選項元件勾選「額外產生出入框」且元件代號與名稱不同時，執行轉存表單失敗
- **Commit ID**: `3bc28d1e0e46fa334e39b6ba27e90114b6cbb92c`
- **作者**: yanann_chen
- **日期**: 2021-11-19 11:35:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 122. [BPM APP]Q00-*********** 修正在鼎捷移動上開啟草稿流程中的任一條流程會發生取得表單資訊錯誤的問題[補]
- **Commit ID**: `f4c3701e31ba2aea8ba8b6c7c5ac056606f0b0f5`
- **作者**: yamiyeh10
- **日期**: 2021-11-18 15:20:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 123. [流程引擎]Q00-20211118002 修正簡易流程圖中使用前置關係人做流程預先解析，導致流程圖與流程實際派送情形不一致
- **Commit ID**: `e9163313ffb8b554b98dd5ed7a3175b57ef99e3f`
- **作者**: yanann_chen
- **日期**: 2021-11-18 15:07:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 124. [流程引擎]S00-*********** 企業流程監控增加流程平均填單時間：將流程中第一關填單的時間加總後取得平均值
- **Commit ID**: `957b24d6edcb663b161affb0405198d002bb2fcb`
- **作者**: waynechang
- **日期**: 2021-11-18 14:13:15
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BamManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 125. [流程引擎]A00-*********** 修正表單複合元件的提示文字無法顯示多語系內容
- **Commit ID**: `ee595b245811cfdaf7832c0a6dde51bcee6e1f3f`
- **作者**: yanann_chen
- **日期**: 2021-11-17 17:12:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 126. [BPM APP]Q00-*********** 修正在鼎捷移動上開啟草稿流程中的任一條流程會發生取得表單資訊錯誤的問題
- **Commit ID**: `97beb3d206172a513b2acf3a25caad754d5d64df`
- **作者**: yamiyeh10
- **日期**: 2021-11-17 14:39:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 127. [Web]Q00-20211116002修正RWD列印表單開窗中，點下列印表單後，有選到的radio和checobox都會產生類似殘影的樣子
- **Commit ID**: `e6ea9395d244d84c4beb28b57f0cb85de69e3695`
- **作者**: 王鵬程
- **日期**: 2021-11-16 18:11:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 128. [流程設計師]Q00-20211112003 修正XPDL轉BPMN流程發生閘道元件與流程關卡ID重複的問題，導致BPMN流程中的連接線連接錯誤
- **Commit ID**: `dcfe856b69b6925f673508111bfc65928cf021a5`
- **作者**: yanann_chen
- **日期**: 2021-11-12 15:40:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java`

### 129. [BPM APP]S00-20210910007 行動端的追蹤已發起流程表單新增取回重辦功能
- **Commit ID**: `154defd0f8496652eb0089e9fc50c8b33eafe786`
- **作者**: yamiyeh10
- **日期**: 2021-11-12 14:55:00
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`

### 130. [Web]Q00-20211112001修正，系統管理工具的資料來源設定是用Oracle且修改的欄位是Oracle服務名稱(SID)時，取得該資料來源資料會顯示原來的資訊而不是修改後的
- **Commit ID**: `e1fd5d6a8e50ce50c4ef8345e9bedae296ba1ca2`
- **作者**: 林致帆
- **日期**: 2021-11-12 09:14:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ConnectionFactory.java`

### 131. [內部]A00-20210820001 在判斷可退回關卡邏輯處加上線的定義詳細Log，方便之後排查
- **Commit ID**: `e5abd805bce1eb245e81a49832ef480e50b12739`
- **作者**: walter_wu
- **日期**: 2021-11-11 18:47:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java`

### 132. [Web]Q00-20211111004 修正流程有設定列印模式，且有將流程設置在iReport時，點擊上方『列印表單』後流程無法繼續派送
- **Commit ID**: `8b8ab911e0f949c9161cdc8c2b1f77d8434db339`
- **作者**: 王鵬程
- **日期**: 2021-11-11 15:10:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 133. [Web]Q00-20211111002 使用FormUtil.setValue賦值給整數或浮點數Textbox欄位時，處理千分位及外顯值邏輯
- **Commit ID**: `89d6853e5ce664397aa35b55f072c44ea1466eee`
- **作者**: yanann_chen
- **日期**: 2021-11-11 14:05:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 134. [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]
- **Commit ID**: `328a7343f32d672454f3df0f7f531a6c545076e7`
- **作者**: yamiyeh10
- **日期**: 2021-11-11 11:29:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 135. [Web]Q00-20211111001 調整待辦事項行動版畫面資料重複且跑版的問題
- **Commit ID**: `fa8e1ef513c1005913cc79b82700e0101da8418b`
- **作者**: cherryliao
- **日期**: 2021-11-11 11:25:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 136. [Web]S00-20210429001 調整簡易流程若關卡處理者為代理人則處理者名稱旁有提示文字
- **Commit ID**: `0304bb3be8c647444abda595c3efcccf7eafd280`
- **作者**: cherryliao
- **日期**: 2021-11-11 10:49:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 137. [BPM APP]Q00-20211110002 修正在IMG從草稿流程進入的表單是走發起流程而不是從草稿進入發起流程
- **Commit ID**: `3cf3664259a72070b0aeac566db0a5054e0c0bbe`
- **作者**: yamiyeh10
- **日期**: 2021-11-10 17:19:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`

### 138. [流程引擎]S00-*********** 企業流程監控時間頁籤中的計算增加「TRIMMEAN(排除極端值的百分比)」功能
- **Commit ID**: `58302e31acc8679a464a05578644e5cfa5842792`
- **作者**: waynechang
- **日期**: 2021-11-10 17:01:06
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BamManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 139. [Web]A00-*********** 預覽流程圖關卡點開彈窗Table新增顯示  接收者  欄位
- **Commit ID**: `10eff9028efd90e15424cd9a9f46c1ba19916c9f`
- **作者**: walter_wu
- **日期**: 2021-11-10 16:55:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 140. [Web]S00-*********** 待辦列表新增讀取狀態的圖示
- **Commit ID**: `a913c29236fecc7c8cbf86eee64bc9b7381ba0ad`
- **作者**: cherryliao
- **日期**: 2021-11-10 14:14:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 141. [流程引擎]Q00-*********** 補上createSQL遺漏監控流程刪除流程設定的參數
- **Commit ID**: `8aec960f1a80ab5f87180ac531572779f6f64e9f`
- **作者**: 林致帆
- **日期**: 2021-11-08 17:58:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 142. [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]
- **Commit ID**: `98d265db5ab15226647b66e4b07f772b110273d7`
- **作者**: yamiyeh10
- **日期**: 2021-11-10 09:36:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 143. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `e411a3ea9f18501c6169897de4498ee695dd2040`
- **作者**: yamiyeh10
- **日期**: 2021-11-09 18:16:38
- **變更檔案數量**: 0

### 144. [Web]A00-20211105001 調整JavaScript浮點數運算誤差造成單身加總計算結果不符預期
- **Commit ID**: `6b6688762ff6eaf8828c54f9f59936f87aa59e76`
- **作者**: yanann_chen
- **日期**: 2021-11-09 18:16:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 145. [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題
- **Commit ID**: `bbc5959661a9137567db107de99d154aa7c4f1b2`
- **作者**: yamiyeh10
- **日期**: 2021-11-09 18:16:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 146. [流程引擎]Q00-20211109003 修正流程如果設定關係人的部門從表單欄位(選兼職部門)，發起時儲存表單後預解析卻解析主部門
- **Commit ID**: `9ef4fc09e49a826a85136d2cbebb819a4cbc61b4`
- **作者**: walter_wu
- **日期**: 2021-11-09 17:01:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 147. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `2ce0313c01b183ef08e5b494922bdc1e82aac242`
- **作者**: yamiyeh10
- **日期**: 2021-11-09 13:40:31
- **變更檔案數量**: 0

### 148. [Web]S00-20210713001 調整產品授權註冊維護作業雙擊某一筆授權時才會彈出是否刪除的詢問視窗
- **Commit ID**: `8e9248a71690c61ecf84115f69d4704644ae9eff`
- **作者**: cherryliao
- **日期**: 2021-11-09 13:38:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`

### 149. [BPM APP]C01-*********** 修正行動端從草稿流程進入的表單多呼叫formCreate方法導致畫面異常
- **Commit ID**: `2704163c51c8e60173dc5a6e71ab9f8ed191c4f1`
- **作者**: yamiyeh10
- **日期**: 2021-11-09 13:38:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`

### 150. [組織設計師]A00-20211027001 調整員工administrator點擊檢視員工資料再點編輯跟從修改員工資料的可編輯欄位要一致
- **Commit ID**: `89d6fc855d67193a33f9a88df06dfc0478604f57`
- **作者**: 林致帆
- **日期**: 2021-11-08 17:11:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`

### 151. [流程引擎]Q00-20211108001 調整ExtOrgAccessor.findManagerForUser服務，當傳入的組織OID與人員不相關時，仍需回傳人員的主部門的主管
- **Commit ID**: `043d12a77b1ace5fe0478ea6c66a835eaad1ae65`
- **作者**: waynechang
- **日期**: 2021-11-08 16:41:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 152. [Web]S00-20210503004 調整Web端加簽畫面新增經常選取對象
- **Commit ID**: `1068575d7dc105950247579c300ed08870ff8e94`
- **作者**: cherryliao
- **日期**: 2021-11-05 17:35:51
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseMutilPrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 153. [BPM APP]Q00-20211105001 修正移動端FormUtil.setValue異常問題
- **Commit ID**: `1b9162599dcdf894d7e4da6ed862d44fb5554be9`
- **作者**: yamiyeh10
- **日期**: 2021-11-05 16:17:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 154. [內部]Q00-20211105002 調整列印龐大Grid位置(絕對位置表單,依設定)
- **Commit ID**: `5dc4716810a8ed237f161ac139f87a6df769c8c4`
- **作者**: walter_wu
- **日期**: 2021-11-05 15:52:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 155. [流程引擎]Q00-20211104003 修正簡易流程圖無法查看於核決層級內加簽的"多人"關卡的關卡資訊
- **Commit ID**: `53e60a36412395f002a133a0abdbbcdad74e55ae`
- **作者**: walter_wu
- **日期**: 2021-11-04 17:21:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`

### 156. [Web]Q00-20211020001 流程表單設定欄位必填時，若儲存表單時必填欄位尚未填寫，就彈出相關提示訊息
- **Commit ID**: `bf409d22c3d85256c28d4bb4ebff1a7ee98965f0`
- **作者**: yanann_chen
- **日期**: 2021-11-03 18:29:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 157. [流程引擎]Q00-20211103001 調整自動簽核邏輯，以人員任務(UserTask)的處理者判斷是否執行自動簽核
- **Commit ID**: `bff12f723d6c81cebc26c3b8a1412e8c60ebbc27`
- **作者**: yanann_chen
- **日期**: 2021-11-03 16:25:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 158. [TipTop]S00-20210729002 新增TIPTOP流程終止或撤銷時，若單據修改且重新送審後於BPM表單可查看之前審批流程的功能
- **Commit ID**: `818d3ba31fe135a11a21f87a30e552c5d009fb54`
- **作者**: cherryliao
- **日期**: 2021-11-03 14:55:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 159. [Web]S00-20210506009 新增RWD表單Grid元件支援凍結欄位功能
- **Commit ID**: `0dd93b3ad9ddc04c72d736ea18237bfd62b55a29`
- **作者**: cherryliao
- **日期**: 2021-11-03 13:38:42
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ListElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/RwdGridTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.16.0.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.18.3.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 160. [BPM APP]Q00-20211103002 修正行動端表單使用客製開窗且回傳欄位填入空字串時會無法正確將選擇的值返回到表單上問題
- **Commit ID**: `1cd8bf097272807aafa6e84eff4317b8272a64cd`
- **作者**: yamiyeh10
- **日期**: 2021-11-03 12:06:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 161. [流程引擎]Q00-20211102003 修正E10抽單，回傳為流程完成狀態而不是流程撤銷狀態
- **Commit ID**: `44b57e832006d62530034e780b109dca7191b423`
- **作者**: 林致帆
- **日期**: 2021-11-02 18:00:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerLocal.java`

### 162. [Web]Q00-20211102002 修正元件的label及元件在流程設計師設定invisible時，前端頁面報錯導致系統變數顯示內容異常
- **Commit ID**: `41f6664910ff3a1b2b634531babd7112a0fe2d35`
- **作者**: 林致帆
- **日期**: 2021-11-02 17:14:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 163. [E10]S00-20211019003 新增 E10(不驗證表單)的回寫接口
- **Commit ID**: `6c7e6af61869a9b18e202bfae0640bb903a5ce6b`
- **作者**: 林致帆
- **日期**: 2021-11-02 16:58:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10Manager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerLocal.java`

### 164. [流程引擎]Q00-20211101001 使用者從portlet開啟BPM流程發起畫面，若該使用者沒有發起該流程的權限，則顯示錯誤畫面[補]
- **Commit ID**: `562cdd231aedf3cbc606a244c8c97a2f1b8f4596`
- **作者**: yanann_chen
- **日期**: 2021-11-02 10:54:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 165. [流程引擎]Q00-20211101001 使用者從portlet開啟BPM指定流程的發起畫面時，若該使用者沒有發起該流程的權限，則顯示錯誤畫面
- **Commit ID**: `4f9aae357492163cd5a0ed78c760c12988c03ba2`
- **作者**: yanann_chen
- **日期**: 2021-11-01 15:01:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 166. [Web]Q00-20211028004 移除追蹤流程與監控流程「已完成」、「已撤銷」、「已終止」清單中的「執行中的活動」欄位
- **Commit ID**: `982f9dc45e82a7cbb8f42db37806e60ea22033d0`
- **作者**: yanann_chen
- **日期**: 2021-10-28 18:14:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 167. [Web]Q00-20211028003 修正活動關卡中掛載網頁應用程式，在IE中點上面的更多按鈕，出現的選單會被遮蔽而無法點選
- **Commit ID**: `770ebf28ed34a26bcbee738b4cd2dcb8b4fff5d2`
- **作者**: 王鵬程
- **日期**: 2021-10-28 14:50:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp`

### 168. [WEB]S00-20200616004 表單全域變數增加「mainFunctionLevel」使用者職務核決層級Level值[補]
- **Commit ID**: `b7b91de3759ba2790b82d08a26e531f54bad64cf`
- **作者**: waynechang
- **日期**: 2021-10-28 14:14:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`

### 169. [BPM APP]Q00-20211028001 修正行掛載雙表單的流程關卡狀態為進行中，不會顯示表單資訊問題
- **Commit ID**: `b45a391156ca4770b256ec224c3723064d4326cc`
- **作者**: shihya_yu
- **日期**: 2021-10-28 11:52:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 170. [Web]S00-20210709002 調整Web加簽頁面樣式
- **Commit ID**: `ac4b4834f011b24a6023f4413d3a4258abc99346`
- **作者**: cherryliao
- **日期**: 2021-10-28 11:17:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 171. [E10]S00-*********** 新增E10流程終止或撤銷時，若單據修改且重新送審後於BPM表單可查看之前審批流程的功能
- **Commit ID**: `833a7900187fcc845a19adae11595a9f928d618f`
- **作者**: cherryliao
- **日期**: 2021-10-28 11:00:44
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/IPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/OJBPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java`

### 172. [流程引擎]S00-*********** 追蹤流程頁面的進階查詢中增加簽核時間欄位條件
- **Commit ID**: `e93082d19ae55de7a013678456b0d5c019561333`
- **作者**: 王鵬程
- **日期**: 2021-10-27 18:13:43
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 173. [WEB]S00-20200616004 表單全域變數增加「mainFunctionLevel」使用者職務核決層級Level值
- **Commit ID**: `319220b75282b7c2e94a524e77c3c855250619ec`
- **作者**: waynechang
- **日期**: 2021-10-27 15:45:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`

### 174. [ESS]Q00-20211026002 調整BPM呼叫ESS存檔前的判斷，防止同單據在ESS與BPM狀態不一致
- **Commit ID**: `54ee7caa07a68606ae6bf3a9d77084109f7b8b74`
- **作者**: yanann_chen
- **日期**: 2021-10-27 14:42:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 175. [ESS]Q00-20211026001 調整BPM發起ESS流程的邏輯，先檢查是否有整合ESS，再往下執行ESS相關的檢查
- **Commit ID**: `0234212d6da61b6340d4535f7b6fabb54a83e833`
- **作者**: yanann_chen
- **日期**: 2021-10-26 17:01:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 176. [Web]A00-20211022001 調整系統管理員在監控流程只有選擇"未結案"，"全部"的流程狀態按鈕，才會在"更多"按鈕顯示撤銷流程
- **Commit ID**: `9b741367c987bf46adfdcf8262d5c0cf14069a82`
- **作者**: 林致帆
- **日期**: 2021-10-25 11:48:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 177. [Web]A00-20211025001 調整樹狀開窗頁面上的「加入」按鈕多語系呈現
- **Commit ID**: `e691f17382b37509ab6147487dbe2b682335a26d`
- **作者**: yanann_chen
- **日期**: 2021-10-25 10:57:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 178. [Web]Q00-20211022001 修正部分情境前端沒有傳入登入方式，會出現設定LDAP驗證卻走系統驗證
- **Commit ID**: `c6700548d94bfc6427dedc2fcb96b13b9cf1fa19`
- **作者**: walter_wu
- **日期**: 2021-10-22 16:08:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 179. [流程引擎]Q00-20211020004 修正絕對表單Grid內有 左、右中括號及單引號，轉存的Grid資料表的資料未能正確呈現符號
- **Commit ID**: `bdd99ef57d655f0ce8801cb0e7202f0c9614ba58`
- **作者**: 王鵬程
- **日期**: 2021-10-20 15:53:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 180. [Web]Q00-20211020003 當響應式表單的下拉式選單元件設定為動態生成選項時，列印表單無法顯示欄位值
- **Commit ID**: `a7034ee2b22a6cb2795a9396f84cf67f3586053e`
- **作者**: yanann_chen
- **日期**: 2021-10-20 14:50:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 181. [流程引擎]Q00-20211020002 修正流程關係人設定部門表單欄位，表單內容為[組織ID]部門ID，導致流程發起失敗
- **Commit ID**: `c7e1c4f26738de840bce76b1115d74c478323fb7`
- **作者**: 林致帆
- **日期**: 2021-10-20 11:17:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 182. [流程引擎]Q00-20211019004 調整DispatchActivityForAutoAgent排程，加入WITH NOLOCK指令
- **Commit ID**: `2ed6b032f7f53974409c7e4a0e70229b89125e07`
- **作者**: yanann_chen
- **日期**: 2021-10-20 11:14:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 183. [流程引擎]Q00-20211019009 追蹤流程時，顯示表單欄位的背景顏色
- **Commit ID**: `2bed443e48db387e30acc63b2104baa599fed4c7`
- **作者**: yanann_chen
- **日期**: 2021-10-19 18:00:40
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 184. [SAP]Q00-20211019008 調整SAP系統整合設定的維護作業改以維護樣板方式維護
- **Commit ID**: `d4059a9932dec09a4e290f648a0912c622681423`
- **作者**: waynechang
- **日期**: 2021-10-19 17:26:32
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp`

### 185. [SAP]Q00-20211019007 調整SAP整合設定，當更新SAP欄位對應設定作業時，一併重新載入SAP連線
- **Commit ID**: `868f7b6452b4f50fa69b7de4778841fdbd354112`
- **作者**: waynechang
- **日期**: 2021-10-19 17:14:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/util/rfc/CallRFC.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/util/sap/SapConnection.java`

### 186. [Web]Q00-20211015001 調整讓Grid支援使用<button>
- **Commit ID**: `1750c0054121dee6332515d045ba546209c3b894`
- **作者**: 王鵬程
- **日期**: 2021-10-19 16:32:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 187. [組織設計師]Q00-20211018001修正組織設計師點選群組跟專案右側人員清單不應該顯示分頁鈕
- **Commit ID**: `0b891dcbc73bf9ba08e8eb4dcebbd722c5656769`
- **作者**: 林致帆
- **日期**: 2021-10-19 14:23:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`

### 188. [表單設計師]S00-20210714001 新增表單設計師DialogInput和DialogInputLabel元件的預設值配置功能
- **Commit ID**: `4a4aa9c6235b9050ff70cec6dcc1aacece79d42c`
- **作者**: cherryliao
- **日期**: 2021-10-19 13:59:30
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

