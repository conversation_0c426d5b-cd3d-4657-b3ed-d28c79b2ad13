# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.10.2_20240924
- **舊版本**: release_5.8.10.2
- **生成時間**: 2025-07-18 10:40:20
- **新增 Commit 數量**: 6

## 變更摘要

### lorenchang (6 commits)

- **2024-09-24 10:23:34**: [雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常
  - 變更檔案: 9 個
- **2024-08-19 16:04:11**: [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙(補)
  - 變更檔案: 1 個
- **2024-08-09 17:34:25**: [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙因素認證直接登入的異常
  - 變更檔案: 11 個
- **2024-08-29 09:43:41**: [Web]C01-20240827002 修正當資料庫類型為 MSSQL_AZURE，使用 SQL 註冊器應用於查詢樣板，模糊查詢條件包含特殊中文字符時無法返回結果
  - 變更檔案: 1 個
- **2024-08-19 16:04:11**: [流程引擎]C01-20240815002 修正用戶登入授權數不足時，未啟用系統郵件通知也會觸發寄送Email給管理員，改為依照啟用設定通知及增加Log記錄
  - 變更檔案: 1 個
- **2024-08-07 16:00:53**: C01-*********** 修正 XPDL 流程核決層級名稱變成「Decision Lv1.」的異常
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常
- **Commit ID**: `c03dee7f8d679633886409a3899ea4de529c9a11`
- **作者**: lorenchang
- **日期**: 2024-09-24 10:23:34
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 2. [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙(補)
- **Commit ID**: `df862f7f636bb22f87170bdd238ca53d0f01564b`
- **作者**: lorenchang
- **日期**: 2024-08-19 16:04:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 3. [雙因素認證]C01-*********** 修正啟用登入帳號不需要分大小寫時，除了正確的大小寫外，其餘皆會跳過雙因雙因素認證直接登入的異常
- **Commit ID**: `6763577874bf0ff46505e18ac3dd7ddd14458698`
- **作者**: lorenchang
- **日期**: 2024-08-09 17:34:25
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 4. [Web]C01-20240827002 修正當資料庫類型為 MSSQL_AZURE，使用 SQL 註冊器應用於查詢樣板，模糊查詢條件包含特殊中文字符時無法返回結果
- **Commit ID**: `941128be96ea842f5d3aeed5a17dec83c48c2cd1`
- **作者**: lorenchang
- **日期**: 2024-08-29 09:43:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 5. [流程引擎]C01-20240815002 修正用戶登入授權數不足時，未啟用系統郵件通知也會觸發寄送Email給管理員，改為依照啟用設定通知及增加Log記錄
- **Commit ID**: `c2a15a9403f841bc5e7d7e22212c2647a4467df3`
- **作者**: lorenchang
- **日期**: 2024-08-19 16:04:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 6. C01-*********** 修正 XPDL 流程核決層級名稱變成「Decision Lv1.」的異常
- **Commit ID**: `7472c04434f94d50d61daa06058c49f6782578e1`
- **作者**: lorenchang
- **日期**: 2024-08-07 16:00:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

