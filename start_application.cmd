@echo off
setlocal enabledelayedexpansion
title BPM Easy Tools - Start Application

echo.
echo ==========================================
echo    BPM Easy Tools - Start Application
echo ==========================================
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo Error: Virtual environment not found!
    echo Please run setup_environment.cmd first to setup the environment.
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.8 or higher
    echo Download URL: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Starting BPM Easy Tools...
echo.

REM Get local IP address
set LOCAL_IP=
set FALLBACK_IP=
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set TEMP_IP=%%b
        REM Remove leading spaces
        for /f "tokens=* delims= " %%c in ("!TEMP_IP!") do set TEMP_IP=%%c
        REM Skip loopback address (127.x.x.x)
        echo !TEMP_IP! | findstr /b "127\." >nul
        if errorlevel 1 (
            REM Prefer 10.x.x.x network (corporate network)
            echo !TEMP_IP! | findstr /b "10\." >nul
            if not errorlevel 1 (
                set LOCAL_IP=!TEMP_IP!
                goto :ip_found
            ) else (
                REM Keep as fallback if no 10.x.x.x found
                if not defined FALLBACK_IP set FALLBACK_IP=!TEMP_IP!
            )
        )
    )
)
REM Use fallback if no 10.x.x.x found
if not defined LOCAL_IP set LOCAL_IP=%FALLBACK_IP%
:ip_found

REM Activate virtual environment and start Streamlit
call venv\Scripts\activate.bat

echo Starting Streamlit application...
echo.
echo Access URLs:
echo   Local URL:   http://localhost:8888
if defined LOCAL_IP (
    echo   Network URL: http://!LOCAL_IP!:8888
) else (
    echo   Network URL: http://[YOUR_IP]:8888
)
echo.
echo Note: The application is accessible from other devices on your network
echo Press Ctrl+C to stop the application
echo.

python -m streamlit run streamlit_home.py --server.port 8888

if errorlevel 1 (
    echo.
    echo ==========================================
    echo Error: Failed to start the application
    echo ==========================================
    echo.
    echo Possible solutions:
    echo 1. Check if all dependencies are installed correctly
    echo 2. Try running setup_environment.cmd again
    echo 3. Make sure port 8888 is not being used by another application
    echo 4. Check if Windows Firewall is blocking the application
    echo.
    pause
    exit /b 1
)

echo.
echo ==========================================
echo Application stopped successfully.
echo ==========================================
pause
