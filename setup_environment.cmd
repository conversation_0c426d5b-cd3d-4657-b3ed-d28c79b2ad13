@echo off
setlocal enabledelayedexpansion
title BPM Easy Tools - Setup Environment

echo.
echo ==========================================
echo    BPM Easy Tools - Setup Environment
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.8 or higher
    echo Download URL: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Starting automatic environment setup...
echo.

REM Execute deployment script
python deploy_to_target.py

if errorlevel 1 (
    echo.
    echo Error occurred during setup
    pause
    exit /b 1
)

echo.
echo Environment setup completed!
echo You can now run start_application.cmd to start the application.
echo.

pause
