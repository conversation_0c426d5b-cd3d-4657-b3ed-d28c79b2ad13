{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_8.1.1.1_All", "date": "2025-07-10 11:15:37", "message": "[ESS]C01-20250604003 增加避免串單的卡控機制：1.檢查當前流程與Session內的ProcessSerialNumber是否匹配，2.檢查Identifier有沒有被其它流程用過", "author": "lorenchang"}, "舊分支": {"branch_name": "release_8.1.1.1", "date": "2025-04-01 11:58:26", "message": "[SQL]8.1.1.1更新(-59_ininDB.patch)", "author": "davidhr-2997"}, "比較時間": "2025-07-18 10:39:27", "新增commit數量": 5, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "dfaf64be6336becd23671aece84c053f6cc3ddcc", "commit_訊息": "[ESS]C01-20250604003 增加避免串單的卡控機制：1.檢查當前流程與Session內的ProcessSerialNumber是否匹配，2.檢查Identifier有沒有被其它流程用過", "提交日期": "2025-07-10 11:15:37", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/exception/AppFormProcessMismatchException.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormCacheSingletonCollection.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AppFormAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/appform/EPIBasePageJS.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "11d75a224d6189314ea285bd73bc3e4d89a75832", "commit_訊息": "[ESS]C01-20250514003 加入Log及JDBC連接已關閉之重取機制，避免出現無法發起ESS流程的異常", "提交日期": "2025-05-26 14:27:21", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/persistence/JDBCReadingHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "784f1282e358da10d77cf03227663c0bacaa2189", "commit_訊息": "[流程引擎]C01-20250429006 修正單身中繫結 Checkbox 或 RadioButton 的欄位，若透過非標準方式（如 Excel 匯入）產生表單資料後再編輯第 2 筆以後的內容時，因欄位不存在導致轉存表單失敗的問題(補)", "提交日期": "2025-05-12 15:39:44", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c9dd3650aeee28d76223a3753b2a9ca10ddbdef7", "commit_訊息": "[流程引擎]C01-20250429006 修正單身中繫結 Checkbox 或 RadioButton 的欄位，若透過非標準方式（如 Excel 匯入）產生表單資料後再編輯第 2 筆以後的內容時，因欄位不存在導致轉存表單失敗的問題", "提交日期": "2025-05-05 14:32:09", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29c73c544096152c788f242b516c8d72aa82c2a5", "commit_訊息": "[ORGDT]Q00-20250429001 修正Web組織管理工具無法新增部門問題", "提交日期": "2025-04-29 13:47:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}