# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.5.4_1
- **舊版本**: 5.6.5.3_1
- **生成時間**: 2025-07-28 18:11:33
- **新增 Commit 數量**: 51

## 變更摘要

### yamiyeh10 (9 commits)

- **2018-06-12 10:46:00**: 修正Oracle取得時間語法
  - 變更檔案: 2 個
- **2018-06-04 15:41:05**: 調整SQL語法增加離職日期判斷
  - 變更檔案: 4 個
- **2018-05-18 18:34:04**: 修正加簽進階搜尋功能異常,需先引入material再引pdf的js檔案
  - 變更檔案: 1 個
- **2018-05-18 18:31:57**: C01-20180509002 修正行動任務導航加簽畫面的左上角放大鏡無法搜尋人員
  - 變更檔案: 1 個
- **2018-05-09 10:05:39**: Q00-20180503001 修正流程有自動簽核功能時簽核歷程異常問題
  - 變更檔案: 1 個
- **2018-05-04 16:41:48**: C01-20180426004 修正Grid元件繫結日期元件時Grid頁面顯示異常問題
  - 變更檔案: 1 個
- **2018-05-04 13:07:42**: 修正Grid內設定動態隱藏與顯示元件異常問題
  - 變更檔案: 1 個
- **2018-04-27 13:45:43**: C01-20180408002 修正tiptop表單在同意派送後會有串單問題
  - 變更檔案: 1 個
- **2018-04-25 16:55:50**: C01-20180416001 修正Grid在有特殊符號內容時會有符號轉換問題
  - 變更檔案: 1 個

### Gaspard (3 commits)

- **2018-06-12 10:36:02**: 修改Web表單設計器的格線設定，增加拖曳元件時的間距設定
  - 變更檔案: 3 個
- **2018-06-07 11:03:18**: 優化Web表單設計器，提升表單開啟效能，將表單輔助格線變更為3種模式，分別為5x5 10x10 20x20。
  - 變更檔案: 6 個
- **2018-04-26 11:09:40**: C01-20180330002 修正儲存表單後，表單大小恢復成預設值的異常
  - 變更檔案: 1 個

### ChinRong (12 commits)

- **2018-06-11 15:06:21**: 修正議題
  - 變更檔案: 5 個
- **2018-06-08 11:24:53**: 修正Grid新增時如果有特殊符號時表單中間層顯示錯誤
  - 變更檔案: 1 個
- **2018-06-08 10:51:55**: 修正MCloud修改語系無法生效的問題
  - 變更檔案: 3 個
- **2018-06-08 10:19:12**: 修正議題
  - 變更檔案: 1 個
- **2018-06-08 09:12:43**: 修正議題
  - 變更檔案: 3 個
- **2018-06-07 16:24:00**: 補上轉派資訊多語系
  - 變更檔案: 2 個
- **2018-06-07 15:46:30**: 發單RESTful服務回傳流程序號
  - 變更檔案: 3 個
- **2018-06-07 15:30:09**: 修正議題
  - 變更檔案: 7 個
- **2018-06-07 09:08:20**: 新增鼎捷移動列表轉派資訊欄位
  - 變更檔案: 8 個
- **2018-06-04 14:00:06**: 修正行動表單預覽pdf檔案當附檔名為大寫時無法預覽的問題
  - 變更檔案: 10 個
- **2018-05-03 13:50:00**: 調整鼎捷移動服務取得session的方式
  - 變更檔案: 3 個
- **2018-04-17 17:58:51**: 修正pdfjs議題中錯誤的程式碼
  - 變更檔案: 2 個

### 治傑 (7 commits)

- **2018-06-07 17:33:05**: 修正行動版客製開窗異常及更新多語系
  - 變更檔案: 3 個
- **2018-06-07 15:59:18**: 修正企業微信多語系異常
  - 變更檔案: 1 個
- **2018-05-24 15:15:58**: C01-*********** 修正企業微信日期、時間元件設為disabled後仍可編輯問題
  - 變更檔案: 2 個
- **2018-05-24 13:47:41**: 修正產品開窗在字數過多時會產生重疊問題
  - 變更檔案: 1 個
- **2018-05-17 17:59:43**: A00-20180510004 行動版Grid用FormScript隱藏欄位後，查看頁面多一個逗號
  - 變更檔案: 1 個
- **2018-04-25 16:40:38**: 調整客製多選開窗流水號
  - 變更檔案: 1 個
- **2018-04-18 09:50:01**: 修正退回重辦時有預設模式,仍需點選退回模式才能退簽問題
  - 變更檔案: 2 個

### waynechang (2 commits)

- **2018-06-06 17:41:33**: A00-*********** 修正文件一覽表 文件型態階層條件"會無法查詢到文件
  - 變更檔案: 1 個
- **2018-06-06 17:40:50**: A00-20180402002 修正分區文管 新增單文件類別無法開啟下階類別
  - 變更檔案: 1 個

### joseph (6 commits)

- **2018-06-04 16:50:54**: 修正 : ISO開啟檔案異常
  - 變更檔案: 1 個
- **2018-06-04 16:37:29**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2018-06-04 16:36:39**: A00-20180316001 調整 ESS回傳錯誤代碼判斷格式 ,避免非錯誤代碼文字影響判斷
  - 變更檔案: 1 個
- **2018-06-04 10:25:20**: A00-20180514001 修正 :  資料選取器開窗後，GRID欄位沒有按照設定所顯示
  - 變更檔案: 1 個
- **2018-06-01 14:16:15**: A00-20180514003 修正 Button元件,設定為SQL註冊器開窗 ,在選取資料後,呼叫的方法無作用
  - 變更檔案: 2 個
- **2018-05-30 15:30:50**: C01-20180425002 調整 發信及發通知 不會因為Qeueu報錯而Rollback
  - 變更檔案: 1 個

### 施廷緯 (5 commits)

- **2018-06-04 16:10:18**: 修改組織HRM小助手同步當部門失效時，忽略離職人員設定此單位為主部門的卡控。
  - 變更檔案: 1 個
- **2018-05-23 10:22:45**: 修改OA模組判斷(ProcessID小於3不能繼續派送)
  - 變更檔案: 1 個
- **2018-05-17 17:48:26**: 修改OA模組判斷(ProcessID小於3不能繼續派送)
  - 變更檔案: 1 個
- **2018-04-30 17:54:58**: 在新增表單及新增流程的ID長度卡控，限制字元長度為40。
  - 變更檔案: 2 個
- **2018-04-23 21:12:59**: 修改調閱ISO文件瀏覽開窗權限判斷
  - 變更檔案: 1 個

### jerry1218 (1 commits)

- **2018-05-30 10:05:09**: 修正RemoteObjectProvider.createWorkflowServerManager method中存在session中的WorkflowServerManagerDelegate session key錯誤問題
  - 變更檔案: 1 個

### 顏伸儒 (3 commits)

- **2018-05-16 11:43:04**: [A00-20180129002]修正簽核流程設計師,修改核決權限關卡的ID項目值時未存入資料庫。
  - 變更檔案: 1 個
- **2018-05-16 10:35:38**: [C01-20180208001]修正流程負責人在管理流程時，加入收尋自動簽核狀態的單據。
  - 變更檔案: 1 個
- **2018-04-25 09:45:11**: [C01-20180301001]修正TipTop發單到BPM,附件無寫入NoCmDocument導致無法開啟的問題。
  - 變更檔案: 1 個

### pinchi_lin (2 commits)

- **2018-05-15 16:27:12**: 修正APP因多語系含特殊字元造成無法發單或簽單問題
  - 變更檔案: 16 個
- **2018-05-03 11:42:09**: C01-20180426001 修正鼎捷移動中間層點擊同意或不同意後提交失敗問題
  - 變更檔案: 1 個

### 張詠威 (1 commits)

- **2018-04-27 12:09:43**: 修正客製開窗無法開窗議題
  - 變更檔案: 3 個

## 詳細變更記錄

### 1. 修正Oracle取得時間語法
- **Commit ID**: `ac1ac22943164b0259d7cc081f77962940c62d22`
- **作者**: yamiyeh10
- **日期**: 2018-06-12 10:46:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.3_updateSQL_Oracle.sql`

### 2. 修改Web表單設計器的格線設定，增加拖曳元件時的間距設定
- **Commit ID**: `41c46082a049d8709c4618a64a39e26e2109f6a2`
- **作者**: Gaspard
- **日期**: 2018-06-12 10:36:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5654.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 3. 修正議題
- **Commit ID**: `bfe46d7d246846a929caaa144d5da19cadc036dc`
- **作者**: ChinRong
- **日期**: 2018-06-11 15:06:21
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`

### 4. 修正Grid新增時如果有特殊符號時表單中間層顯示錯誤
- **Commit ID**: `41f87c04d1307099cc2546164708aa3d5cb24181`
- **作者**: ChinRong
- **日期**: 2018-06-08 11:24:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 5. 修正MCloud修改語系無法生效的問題
- **Commit ID**: `4e61b278ad1fb88a8da92cb0f877bf10b8be2d56`
- **作者**: ChinRong
- **日期**: 2018-06-08 10:51:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 6. 修正議題
- **Commit ID**: `50cb37f70473c995839dc7b3eea1340a6d04d50c`
- **作者**: ChinRong
- **日期**: 2018-06-08 10:19:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 7. 修正議題
- **Commit ID**: `ba63c7a0e0122ca575741ef247ab548301e27d23`
- **作者**: ChinRong
- **日期**: 2018-06-08 09:12:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 8. 修正行動版客製開窗異常及更新多語系
- **Commit ID**: `afcad1733b6e5f3104e8e85640ed5696cc27026b`
- **作者**: 治傑
- **日期**: 2018-06-07 17:33:05
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5654.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 9. 補上轉派資訊多語系
- **Commit ID**: `ca22e709f793d9ca83d58c09ed4090ded37f4d83`
- **作者**: ChinRong
- **日期**: 2018-06-07 16:24:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5654.xls`

### 10. 修正企業微信多語系異常
- **Commit ID**: `69a02ae7199d9b7c928faba054baf676822a2e6d`
- **作者**: 治傑
- **日期**: 2018-06-07 15:59:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 11. 發單RESTful服務回傳流程序號
- **Commit ID**: `ebba8849d69b058cddafba9c1df158a57b2bb23f`
- **作者**: ChinRong
- **日期**: 2018-06-07 15:46:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`

### 12. 修正議題
- **Commit ID**: `9714c185bfc2f5e769b80910bcd1b82ce836998a`
- **作者**: ChinRong
- **日期**: 2018-06-07 15:30:09
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_SQLServer.sql`

### 13. 優化Web表單設計器，提升表單開啟效能，將表單輔助格線變更為3種模式，分別為5x5 10x10 20x20。
- **Commit ID**: `06f36135b8562aee367d078805663e8dfc554ef9`
- **作者**: Gaspard
- **日期**: 2018-06-07 11:03:18
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 14. 新增鼎捷移動列表轉派資訊欄位
- **Commit ID**: `7ef77b6ff75a5f88048dbdf56f61c4255105265f`
- **作者**: ChinRong
- **日期**: 2018-06-07 09:08:20
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`

### 15. A00-*********** 修正文件一覽表 文件型態階層條件"會無法查詢到文件
- **Commit ID**: `812bebcac33f22a7cc78e91c5122327d8c648735`
- **作者**: waynechang
- **日期**: 2018-06-06 17:41:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`

### 16. A00-20180402002 修正分區文管 新增單文件類別無法開啟下階類別
- **Commit ID**: `452f2f1188cd8d0100befe7415f580387de9bf60`
- **作者**: waynechang
- **日期**: 2018-06-06 17:40:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`

### 17. 修正 : ISO開啟檔案異常
- **Commit ID**: `ead0ad01b10109c73fc1b2d1929cc203bd0876a0`
- **作者**: joseph
- **日期**: 2018-06-04 16:50:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 18. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `44279559f9215581dcb1cc9b62942ef48e5cec31`
- **作者**: joseph
- **日期**: 2018-06-04 16:37:29
- **變更檔案數量**: 0

### 19. A00-20180316001 調整 ESS回傳錯誤代碼判斷格式 ,避免非錯誤代碼文字影響判斷
- **Commit ID**: `cb563a6181426e537001f95ff10a5f1ed17c7d0a`
- **作者**: joseph
- **日期**: 2018-06-04 16:36:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 20. 修改組織HRM小助手同步當部門失效時，忽略離職人員設定此單位為主部門的卡控。
- **Commit ID**: `0ea272cf0654577399a471aa2d1441a73986dfa0`
- **作者**: 施廷緯
- **日期**: 2018-06-04 16:10:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 21. 調整SQL語法增加離職日期判斷
- **Commit ID**: `f18f82c104a83f9afc5b8529248735226b652658`
- **作者**: yamiyeh10
- **日期**: 2018-06-04 15:41:05
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_SQLServer.sql`

### 22. 修正行動表單預覽pdf檔案當附檔名為大寫時無法預覽的問題
- **Commit ID**: `eb2f313dfa3f9718dd547ef164faafef41971f02`
- **作者**: ChinRong
- **日期**: 2018-06-04 14:00:06
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 23. A00-20180514001 修正 :  資料選取器開窗後，GRID欄位沒有按照設定所顯示
- **Commit ID**: `216ce417199b0a2fd6663c5d6cb9e1031195816b`
- **作者**: joseph
- **日期**: 2018-06-04 10:25:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 24. A00-20180514003 修正 Button元件,設定為SQL註冊器開窗 ,在選取資料後,呼叫的方法無作用
- **Commit ID**: `c20ac1fbae3b3081994807fbcd55e714f6ed0c4f`
- **作者**: joseph
- **日期**: 2018-06-01 14:16:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/CustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 25. C01-20180425002 調整 發信及發通知 不會因為Qeueu報錯而Rollback
- **Commit ID**: `7052337d71d7db76956a13369425c70973022e87`
- **作者**: joseph
- **日期**: 2018-05-30 15:30:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`

### 26. 修正RemoteObjectProvider.createWorkflowServerManager method中存在session中的WorkflowServerManagerDelegate session key錯誤問題
- **Commit ID**: `1c9800c991d3dcc289d6ed68a7730d43fee4e0d0`
- **作者**: jerry1218
- **日期**: 2018-05-30 10:05:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`

### 27. C01-*********** 修正企業微信日期、時間元件設為disabled後仍可編輯問題
- **Commit ID**: `6c6b64c614e8d3bc3ae0200829781d74bce7cf53`
- **作者**: 治傑
- **日期**: 2018-05-24 15:15:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 28. 修正產品開窗在字數過多時會產生重疊問題
- **Commit ID**: `2f49b7f83b404a97fa62752a4d83c9cff29c3b34`
- **作者**: 治傑
- **日期**: 2018-05-24 13:47:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js`

### 29. 修改OA模組判斷(ProcessID小於3不能繼續派送)
- **Commit ID**: `870e8fcd8536a52184a55f061b187eec823f2188`
- **作者**: 施廷緯
- **日期**: 2018-05-23 10:22:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 30. 修正加簽進階搜尋功能異常,需先引入material再引pdf的js檔案
- **Commit ID**: `e87aa9b3a9dd24d713f5a2915bbf7e2d49844073`
- **作者**: yamiyeh10
- **日期**: 2018-05-18 18:34:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`

### 31. C01-20180509002 修正行動任務導航加簽畫面的左上角放大鏡無法搜尋人員
- **Commit ID**: `9bb5ea75bce7187661a8377a188c2cba3ef13bcc`
- **作者**: yamiyeh10
- **日期**: 2018-05-18 18:31:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`

### 32. A00-20180510004 行動版Grid用FormScript隱藏欄位後，查看頁面多一個逗號
- **Commit ID**: `f1dc4100247f27b9dcf3a6e2ae494857a83fd6d3`
- **作者**: 治傑
- **日期**: 2018-05-17 17:59:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 33. 修改OA模組判斷(ProcessID小於3不能繼續派送)
- **Commit ID**: `ed11491651c4082632d6af65984f0b0e5211f467`
- **作者**: 施廷緯
- **日期**: 2018-05-17 17:48:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 34. [A00-20180129002]修正簽核流程設計師,修改核決權限關卡的ID項目值時未存入資料庫。
- **Commit ID**: `cc726f86e29472f8f44c82215e52301871990731`
- **作者**: 顏伸儒
- **日期**: 2018-05-16 11:43:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 35. [C01-20180208001]修正流程負責人在管理流程時，加入收尋自動簽核狀態的單據。
- **Commit ID**: `b69be61a28cdcbb76dcba9a50b9ce4e3403b29a2`
- **作者**: 顏伸儒
- **日期**: 2018-05-16 10:35:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 36. 修正APP因多語系含特殊字元造成無法發單或簽單問題
- **Commit ID**: `85839270c45dce03e19e8d6c27efe594d9ca5bd4`
- **作者**: pinchi_lin
- **日期**: 2018-05-15 16:27:12
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`

### 37. Q00-20180503001 修正流程有自動簽核功能時簽核歷程異常問題
- **Commit ID**: `db1857f5e675f9fb0eb886dd59c5d3246c6dde27`
- **作者**: yamiyeh10
- **日期**: 2018-05-09 10:05:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 38. C01-20180426004 修正Grid元件繫結日期元件時Grid頁面顯示異常問題
- **Commit ID**: `1349b718d6123e7bd609a8d28062fe3e3fcb2b45`
- **作者**: yamiyeh10
- **日期**: 2018-05-04 16:41:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 39. 修正Grid內設定動態隱藏與顯示元件異常問題
- **Commit ID**: `518c7012f48647db68ea58fce566f9d6f4eab608`
- **作者**: yamiyeh10
- **日期**: 2018-05-04 13:07:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 40. 調整鼎捷移動服務取得session的方式
- **Commit ID**: `8d7b397fe567c6443f19d6d5b381aa25354f1c42`
- **作者**: ChinRong
- **日期**: 2018-05-03 13:50:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 41. C01-20180426001 修正鼎捷移動中間層點擊同意或不同意後提交失敗問題
- **Commit ID**: `1d94fa545b07f63546e0024d178e73f8b734417c`
- **作者**: pinchi_lin
- **日期**: 2018-05-03 11:42:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 42. 在新增表單及新增流程的ID長度卡控，限制字元長度為40。
- **Commit ID**: `c099bfb879f736216e7314ea67707bcb8269a47a`
- **作者**: 施廷緯
- **日期**: 2018-04-30 17:54:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/IDValidator.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 43. C01-20180408002 修正tiptop表單在同意派送後會有串單問題
- **Commit ID**: `0015237f7305310ad249acf4d8521b7916c2918f`
- **作者**: yamiyeh10
- **日期**: 2018-04-27 13:45:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 44. 修正客製開窗無法開窗議題
- **Commit ID**: `f4d699abb9433ebdd9ba57acccf33ea95ca304fb`
- **作者**: 張詠威
- **日期**: 2018-04-27 12:09:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 45. C01-20180330002 修正儲存表單後，表單大小恢復成預設值的異常
- **Commit ID**: `48549cedeb5491655eb453aabd0dfe3f1b4f093a`
- **作者**: Gaspard
- **日期**: 2018-04-26 11:09:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`

### 46. C01-20180416001 修正Grid在有特殊符號內容時會有符號轉換問題
- **Commit ID**: `09b3511cfb245469b037da7cb17fb898d9d97871`
- **作者**: yamiyeh10
- **日期**: 2018-04-25 16:55:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 47. 調整客製多選開窗流水號
- **Commit ID**: `23d8779bbb50575f00235b7a41151be83f146365`
- **作者**: 治傑
- **日期**: 2018-04-25 16:40:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 48. [C01-20180301001]修正TipTop發單到BPM,附件無寫入NoCmDocument導致無法開啟的問題。
- **Commit ID**: `c904593ef7cd997d4da7831db04586d2b30f2f02`
- **作者**: 顏伸儒
- **日期**: 2018-04-25 09:45:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 49. 修改調閱ISO文件瀏覽開窗權限判斷
- **Commit ID**: `957efd8709fddaa52586dc3aae572436701f78b4`
- **作者**: 施廷緯
- **日期**: 2018-04-23 21:12:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 50. 修正退回重辦時有預設模式,仍需點選退回模式才能退簽問題
- **Commit ID**: `5f2b4d724af357dae14dc1b916ab1064e5d0f4a2`
- **作者**: 治傑
- **日期**: 2018-04-18 09:50:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 51. 修正pdfjs議題中錯誤的程式碼
- **Commit ID**: `f303b40efbffebee9c7fce07bf0c2c6e734d942b`
- **作者**: ChinRong
- **日期**: 2018-04-17 17:58:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`

