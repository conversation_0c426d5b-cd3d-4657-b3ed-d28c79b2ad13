# Release Notes - BPM

## 版本資訊
- **新版本**: *******
- **舊版本**: *******
- **生成時間**: 2025-07-28 18:06:55
- **新增 Commit 數量**: 220

## 變更摘要

### jerry1218 (33 commits)

- **2018-05-28 15:17:14**: 修正PC版完整menu最後一筆第一層菜單會重覆出現問題
  - 變更檔案: 1 個
- **2018-05-25 11:27:44**: 修正bpm-style.css中的tools name會與其他套件(ex.zTree,shBrushJScript)屬性相衝,故改名spenTools
  - 變更檔案: 17 個
- **2018-05-24 17:00:32**: 修改easyflow整合發起時一律開新視窗
  - 變更檔案: 2 個
- **2018-05-24 15:49:56**: A00-20180522001 修正發起流程清單左邊分類選單無法正常向下捲動的異常
  - 變更檔案: 2 個
- **2018-05-23 16:58:05**: C01-20180522001 簽核畫面會一直轉圈圈，但有成功簽核下去，防呆+錯誤時印error log
  - 變更檔案: 1 個
- **2018-05-23 15:23:07**: 異常修正
  - 變更檔案: 45 個
- **2018-05-23 10:53:45**: Q00-20180523001 修正無法開啟Remote附件問題
  - 變更檔案: 3 個
- **2018-05-22 09:04:52**: 移除struts XML中的多語言檔案設定,避免操作時LOG出現警告訊息
  - 變更檔案: 44 個
- **2018-05-21 17:34:09**: 減少INFO LOG
  - 變更檔案: 1 個
- **2018-05-21 17:15:16**: 將service中的ejbCreate等method中的info層級改為debug, 減少INFO LOG
  - 變更檔案: 40 個
- **2018-05-21 15:58:56**: 移除多餘system.out.println
  - 變更檔案: 55 個
- **2018-05-18 15:45:32**: A00-20180516001 修正外部連結URL searchFormDetail於小螢幕無法顯示附件問題
  - 變更檔案: 1 個
- **2018-05-18 14:30:36**: 修正easyflow整合無法開新視窗問題
  - 變更檔案: 2 個
- **2018-05-17 17:08:33**: 修正追蹤已關閉流程時,顯示流程及顯示表單按鈕重覆出現問題
  - 變更檔案: 1 個
- **2018-05-17 16:46:51**: Q00-20180515001 修正系統管理員無法跳過高置中的關卡問題
  - 變更檔案: 1 個
- **2018-05-17 15:48:34**: C01-20180509003 待辦及追蹤流程清單效能議題
  - 變更檔案: 2 個
- **2018-05-16 17:15:11**: 修正新發起流程頁面與easyflow整合異常問題
  - 變更檔案: 1 個
- **2018-05-16 14:25:38**: 1.系統通知頁面微調  2.排程設定畫面微調
  - 變更檔案: 3 個
- **2018-05-16 11:06:27**: 修正T100簽和樣版中的回寫沒有設定好流程變數問題
  - 變更檔案: 1 個
- **2018-05-15 17:49:26**: A00-20180504002 修正主旨有換行時追蹤流程清單顯示異常
  - 變更檔案: 1 個
- **2018-05-15 17:48:31**: 修正IOS瀏覽器[切換為電腦版網頁]議題修正所造成的後續異常
  - 變更檔案: 1 個
- **2018-05-15 17:45:30**: 修正個人中心sidebar於小螢幕時的顯示異常
  - 變更檔案: 1 個
- **2018-05-15 10:34:57**: A00-20180511001 修正開單時單身空白處理邏輯異常導致表單畫面單身資料無法載入問題
  - 變更檔案: 1 個
- **2018-05-10 16:41:45**: A00-20180510007 修正外部連結URL查看表單無法下載附件問題
  - 變更檔案: 1 個
- **2018-05-09 16:07:03**: 多語系調整
  - 變更檔案: 2 個
- **2018-05-09 16:06:31**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-09 16:06:03**: A00-20180509001 修正外部連結URL - 追蹤流程(圖+表) and 追蹤流程(圖)開啟的畫面顯示問題
  - 變更檔案: 4 個
- **2018-05-08 18:08:32**: 修正小螢幕時簽核無簽核意見欄位問題
  - 變更檔案: 5 個
- **2018-05-07 17:31:36**: 1.修改BPM首頁圖表套件  2.日期元件多語言
  - 變更檔案: 3 個
- **2018-05-03 11:44:31**: Dashboard BPM首頁畫面微調
  - 變更檔案: 1 個
- **2018-05-03 11:04:27**: 新增主頁面右上待辦項目沒有主旨時的預設文字
  - 變更檔案: 5 個
- **2018-05-02 17:47:18**: Q00-20180502001 修正T100主機異常時,上傳簽名圖檔會造成的畫面錯誤
  - 變更檔案: 4 個
- **2018-05-02 16:18:21**: 1.修正發起流程的流程內容沒有[檢視流程詳細資訊]按鈕 2.畫面微調
  - 變更檔案: 7 個

### joseph (16 commits)

- **2018-05-25 18:24:13**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-25 18:23:47**: 調整 ISO報表模組
  - 變更檔案: 5 個
- **2018-05-25 17:54:04**: 修改   下拉式按鈕異常
  - 變更檔案: 1 個
- **2018-05-25 17:36:20**: 新增 E10 Create SQL
  - 變更檔案: 2 個
- **2018-05-25 17:10:28**: 修正  回寫E10狀態異常
  - 變更檔案: 1 個
- **2018-05-25 17:09:49**: 新增 互聯中台產品資訊接口
  - 變更檔案: 1 個
- **2018-05-25 13:47:16**: 修改被覆蓋的程式
  - 變更檔案: 1 個
- **2018-05-25 13:45:09**: 修改被覆蓋的程式
  - 變更檔案: 1 個
- **2018-05-25 13:36:59**: 修復 被覆蓋的程式
  - 變更檔案: 1 個
- **2018-05-25 13:09:55**: 修復 被覆蓋的程式
  - 變更檔案: 1 個
- **2018-05-25 13:04:24**: 復原 正確的版本
  - 變更檔案: 1 個
- **2018-05-25 11:26:07**: 移除不必要log
  - 變更檔案: 1 個
- **2018-05-25 11:22:16**: 補上RWD表單設計器開發優化
  - 變更檔案: 1 個
- **2018-05-24 22:24:41**: E10回寫簽核歷程調整
  - 變更檔案: 1 個
- **2018-05-24 19:53:15**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-24 19:52:32**: E10 整合功能
  - 變更檔案: 97 個

### ChinRong (21 commits)

- **2018-05-25 18:22:18**: 修正console中的訊息多語系錯誤
  - 變更檔案: 1 個
- **2018-05-24 18:29:42**: 修正上傳附件後Grid新增失效的問題
  - 變更檔案: 8 個
- **2018-05-24 15:25:53**: 調整上傳附件重載表單的服務
  - 變更檔案: 2 個
- **2018-05-23 19:07:09**: 修正議題
  - 變更檔案: 3 個
- **2018-05-23 16:52:53**: 修正鼎捷移動行動表單上傳附件後出現JSON異常的問題
  - 變更檔案: 4 個
- **2018-05-23 16:38:07**: 簽核流設計師新增行動版Grid自動生成按鈕的存取限制設定
  - 變更檔案: 1 個
- **2018-05-23 15:06:42**: 調整行動版表單設計器Grid元件自動生成formscript方法觸發時機
  - 變更檔案: 1 個
- **2018-05-23 12:18:41**: 修正議題
  - 變更檔案: 9 個
- **2018-05-18 12:06:36**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-18 12:06:19**: 修正議題: RWD表單轉換成APP表單時的提示訊息
  - 變更檔案: 2 個
- **2018-05-18 11:29:20**: 修正議題
  - 變更檔案: 5 個
- **2018-05-18 10:10:28**: 修正行動版RWD設計器的議題
  - 變更檔案: 5 個
- **2018-05-17 10:57:58**: 修正複合式元件在流程設計器中修改各關卡行動版的驗證屬性時，無法有效保存的議題
  - 變更檔案: 1 個
- **2018-05-16 13:48:46**: 修正議題
  - 變更檔案: 5 個
- **2018-05-15 18:36:36**: 修正議題
  - 變更檔案: 4 個
- **2018-05-10 20:22:00**: 新增RWD行動版表單Grid套件及載入機制
  - 變更檔案: 19 個
- **2018-05-10 13:56:22**: 調整行動簽核中心鼎捷移動使用者欄位，新增最後登入時間欄位
  - 變更檔案: 9 個
- **2018-05-10 09:49:12**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-10 09:48:33**: 新增行動版Grid套件
  - 變更檔案: 4 個
- **2018-05-10 09:10:07**: 修正Grid繫結的元件不會生成的問題
  - 變更檔案: 2 個
- **2018-05-08 16:24:03**: 新增簽核流設計師行動版元件欄位驗證
  - 變更檔案: 12 個

### waynechang (14 commits)

- **2018-05-25 17:05:46**: 調整ISO log 顯示機制
  - 變更檔案: 1 個
- **2018-05-25 17:00:30**: 修正獨立模組開啟檔案機制
  - 變更檔案: 1 個
- **2018-05-25 16:39:43**: 修正覆寫ISOProperty邏輯
  - 變更檔案: 3 個
- **2018-05-25 15:27:45**: 增加調整設定檔服務
  - 變更檔案: 3 個
- **2018-05-25 09:37:49**: 增加ISO獨立模組下載
  - 變更檔案: 7 個
- **2018-05-23 17:27:56**: 將load的圖關閉以免影響附件上傳畫面異常
  - 變更檔案: 1 個
- **2018-05-23 11:07:50**: 修正C01-20180516001 ISO轉檔議題
  - 變更檔案: 3 個
- **2018-05-21 19:03:14**: 修正ISO附件無法上傳議題
  - 變更檔案: 2 個
- **2018-05-16 17:59:56**: 增加ISO模組閱讀檔案機制
  - 變更檔案: 7 個
- **2018-05-15 10:23:17**: 強制轉型RestFul回傳值為UTF-8
  - 變更檔案: 1 個
- **2018-05-15 10:22:26**: 修正ISO文件開窗簡易查尋失敗
  - 變更檔案: 1 個
- **2018-05-11 17:59:38**: 增加json-lib套件引用
  - 變更檔案: 2 個
- **2018-05-11 17:51:37**: 增加取得ISO獨立模組的站台位置
  - 變更檔案: 1 個
- **2018-05-11 17:45:52**: 調整ISO報表取得ISOType
  - 變更檔案: 5 個

### Gaspard (33 commits)

- **2018-05-25 14:32:07**: 修正退回重辦後，後端已成功退回但卻跳出「無可退回關卡」的邏輯錯誤
  - 變更檔案: 1 個
- **2018-05-23 13:43:43**: 修改查詢的SQL使用System.out印在console上
  - 變更檔案: 1 個
- **2018-05-18 17:10:11**: 修改Web表單設計器的scrollbar呈現
  - 變更檔案: 2 個
- **2018-05-18 16:40:32**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-18 16:40:07**: 增加多語系
  - 變更檔案: 2 個
- **2018-05-18 13:53:27**: 修正樹狀開窗中，當組織下無專案或群組時，點擊節點會拋出錯誤提示的異常
  - 變更檔案: 1 個
- **2018-05-18 11:22:59**: 移除多餘註解
  - 變更檔案: 1 個
- **2018-05-18 09:13:34**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-18 09:13:08**: 一致性表單複合式元件中按鈕的寬度。
  - 變更檔案: 1 個
- **2018-05-17 17:43:33**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-17 17:43:15**: 修改在IE高安全性時，菜單與浮動式按鈕無法使用的異常
  - 變更檔案: 3 個
- **2018-05-17 14:56:37**: 優化Web表單設計器左側樹狀區塊Scrollbar的呈現
  - 變更檔案: 2 個
- **2018-05-17 14:35:01**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-17 14:34:45**: 修正RWD表單script編輯器中，於chrome無法將全域變數或元件事件加到script中的異常
  - 變更檔案: 3 個
- **2018-05-17 13:59:30**: 修正嵌入ESS表單時，表單固定高度為1000
  - 變更檔案: 4 個
- **2018-05-15 17:15:17**: 修改多語系顯示邏輯，若查無多語系資料，則使用??xxxx.xxxx??的方式呈現
  - 變更檔案: 3 個
- **2018-05-15 12:09:10**: 修正追蹤流程時，表單元件仍可編輯的異常。
  - 變更檔案: 4 個
- **2018-05-14 14:19:17**: 修正自訂義開窗的Dialog相關元件，若設定visiable時，無法除存表單的異常
  - 變更檔案: 1 個
- **2018-05-11 16:54:50**: 修改在IOS的瀏覽器切換為「電腦版瀏覽」時，會自動切到登錄頁。
  - 變更檔案: 1 個
- **2018-05-11 14:42:41**: 修正複合式元件在流程設計器中修改各關卡的驗證屬性時，無法有效保存的議題
  - 變更檔案: 1 個
- **2018-05-11 14:41:36**: 修改表單中的多選開窗無法保留前一次選取人數的異常
  - 變更檔案: 1 個
- **2018-05-10 15:48:17**: A00-20180510002 修正發單與簽核頁面XPDL的流程圖過長時顯示橫向卷軸
  - 變更檔案: 2 個
- **2018-05-10 10:22:09**: 修正自定義的DialogInputLabel元件，儲存表單後再發起表單，右側欄位的值被左側欄位值覆蓋的異常
  - 變更檔案: 1 個
- **2018-05-07 15:43:57**: 修正若有SQLCommand時，切換到formscript頁面的錯誤
  - 變更檔案: 1 個
- **2018-05-07 15:23:13**: RWD表單設計器開發優化
  - 變更檔案: 33 個
- **2018-05-03 16:42:33**: 統一表單相關畫面不使用較好看的ScrollBar
  - 變更檔案: 1 個
- **2018-05-02 17:27:27**: 修改列印表單時背景色統一為白色
  - 變更檔案: 1 個
- **2018-05-02 17:24:25**: 修改追蹤流程時Title元件無法顯示的議題
  - 變更檔案: 1 個
- **2018-05-02 17:18:19**: 表單相關頁面統一不使用較好看的ScrollBar
  - 變更檔案: 1 個
- **2018-05-02 17:17:00**: 微調Dialog元件的按鈕的間距
  - 變更檔案: 1 個
- **2018-05-02 15:51:01**: 表單相關畫面一律暫不使用樣式較好看的scrollbar
  - 變更檔案: 1 個
- **2018-05-02 13:39:15**: 修正外部連結進入表單畫面時的異常
  - 變更檔案: 3 個
- **2018-04-30 15:55:45**: 修正查詢維護樣版 1.匯出EXCEL的圖片路徑 2.少載入ModalDialog.js 3.移除工具列背景色
  - 變更檔案: 2 個

### 顏伸儒 (5 commits)

- **2018-05-24 20:04:46**: ISO變更單新增浮水印內容資料。
  - 變更檔案: 4 個
- **2018-05-16 11:50:17**: [A00-20180129002]修正BPM57版本,簽核流程設計師,修改核決權限關卡的ID項目值時未存入資料庫。
  - 變更檔案: 1 個
- **2018-05-16 10:51:27**: [C01-20180208001]修正BPM57版本,流程負責人在管理流程時，加入收尋自動簽核狀態的單據。
  - 變更檔案: 1 個
- **2018-05-14 20:54:34**: 修改ISO型態開窗。
  - 變更檔案: 4 個
- **2018-05-10 20:08:11**: 修正表單設計師驗證欄位。
  - 變更檔案: 1 個

### pinchi_lin (27 commits)

- **2018-05-24 19:33:14**: 修正APP中RWD表單grid自動生成按鈕異常問題遺漏部分
  - 變更檔案: 1 個
- **2018-05-24 19:29:24**: 修正APP中grid的value資料格式不對問題
  - 變更檔案: 2 個
- **2018-05-24 17:17:17**: 修正APP中RWD表單grid元件自動生成按鈕異常問題
  - 變更檔案: 2 個
- **2018-05-22 18:42:34**: 修正APP中textbox元件因運算導致顯示異常問題
  - 變更檔案: 2 個
- **2018-05-18 18:28:29**: 加入log
  - 變更檔案: 1 個
- **2018-05-18 12:04:51**: 修正APP中RWD表單位設計APP時中間層不會顯示問題
  - 變更檔案: 1 個
- **2018-05-17 19:32:21**: 修正APP中相對位置表單grid無法使用與gird資料在div上顯示異常問題
  - 變更檔案: 4 個
- **2018-05-17 18:02:09**: 調整APP中RWD表單grid其資料顯示在div異常問題
  - 變更檔案: 1 個
- **2018-05-17 16:08:48**: 修正APP鼎捷移動端使用管理員配置圖表應用異常問題
  - 變更檔案: 1 個
- **2018-05-17 14:22:58**: 修正APP微信端在追蹤流程中下方頁籤顯示錯誤問題
  - 變更檔案: 2 個
- **2018-05-17 12:54:45**: 修正APP中doubleTextbox必填驗證異常問題
  - 變更檔案: 2 個
- **2018-05-16 19:38:56**: 修正APP微信端草稿流程進入表單畫面異常議題
  - 變更檔案: 1 個
- **2018-05-16 17:34:16**: 修正APP微信端發起時因為jsp緩存導致worStep dirty問題
  - 變更檔案: 6 個
- **2018-05-16 17:22:37**: 調整APP中formSave的驗證時機點
  - 變更檔案: 4 個
- **2018-05-16 17:10:00**: 修正APP中千分位顯示問題與小數點顯示問題
  - 變更檔案: 4 個
- **2018-05-10 18:27:48**: 修正APP開窗元件disable問題與樣板中含錢字號問題
  - 變更檔案: 4 個
- **2018-05-09 18:35:20**: 調整APP原先取欄位驗證的地方改成行動用的方法
  - 變更檔案: 13 個
- **2018-05-09 17:26:21**: 修正APP中checkbox與radio元件在onblur時會無法觸發與錯誤訊息滑動問題
  - 變更檔案: 4 個
- **2018-05-08 16:25:16**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-08 16:23:32**: 調整APP驗證方法中使用alert的地方改成jDalert
  - 變更檔案: 2 個
- **2018-05-08 10:42:25**: 修正RWD表單自動轉換時不塞script給APP
  - 變更檔案: 1 個
- **2018-05-08 10:07:44**: 修正APP必填驗證漏掉部分
  - 變更檔案: 10 個
- **2018-05-07 20:01:24**: 新增APP必填欄位驗證功能
  - 變更檔案: 22 個
- **2018-05-03 20:00:10**: 修正BPMAPP多欄位異常問題
  - 變更檔案: 2 個
- **2018-05-03 17:46:23**: 修正開窗樣板多div問題
  - 變更檔案: 1 個
- **2018-05-03 11:53:15**: C01-20180426001 修正鼎捷移動中間層點擊同意或不同意後提交失敗問題
  - 變更檔案: 1 個
- **2018-05-02 19:42:24**: 修正BPMAPP表單多欄位異常問題
  - 變更檔案: 2 個

### yamiyeh10 (24 commits)

- **2018-05-24 18:57:34**: 修正行動表單有小數點時外顯值沒有顯示問題
  - 變更檔案: 1 個
- **2018-05-24 14:53:06**: 調整行動表單頁籤元件樣式 調整Title元件圖片大小
  - 變更檔案: 2 個
- **2018-05-24 11:21:06**: 新增頁籤formscript設置顯示、隱藏、預設功能 調整行動版表單範例說明-新增頁籤範例與調整版本
  - 變更檔案: 18 個
- **2018-05-23 18:41:24**: 修正App千分位屬性增加與運算異常問題
  - 變更檔案: 4 個
- **2018-05-23 17:46:07**: 調整行動版表單範例說明
  - 變更檔案: 1 個
- **2018-05-23 14:25:07**: 註解行動表單自動生成Grid按鈕方法
  - 變更檔案: 2 個
- **2018-05-22 19:17:45**: 修正行動版表單畫面跑版
  - 變更檔案: 1 個
- **2018-05-18 18:25:51**: 修正加簽進階搜尋功能異常,需先引入material再引pdf的js檔案
  - 變更檔案: 2 個
- **2018-05-18 16:34:51**: 修正行動表單畫面跑版
  - 變更檔案: 1 個
- **2018-05-17 19:29:08**: 修正無法刪除附件議題
  - 變更檔案: 1 個
- **2018-05-17 18:27:16**: 修正Grid元件在隱藏標籤時顯示異常
  - 變更檔案: 1 個
- **2018-05-17 16:22:10**: 修正DoubleTextBox在隱藏標籤時顯示異常
  - 變更檔案: 1 個
- **2018-05-17 16:15:06**: 修正App附件列表顯示異常議題
  - 變更檔案: 13 個
- **2018-05-17 13:18:42**: 修正PC有附件時,App未顯示附件清單議題
  - 變更檔案: 1 個
- **2018-05-17 08:46:02**: 修正議題 1.鼎捷移動使用者管理頁面只保留搜尋BPM帳號 2.修正鼎捷移動無法發單議題 3.補上頁籤功能漏將class名稱替換
  - 變更檔案: 3 個
- **2018-05-16 13:43:43**: 修正App頁簽元件顯示異常問題
  - 變更檔案: 2 個
- **2018-05-10 09:44:01**: 修正Grid自動生成漏簽部分
  - 變更檔案: 1 個
- **2018-05-09 19:43:25**: 新增Grid自動生成新增、修改、刪除按鈕
  - 變更檔案: 4 個
- **2018-05-09 16:00:00**: 修正Grid元件繫結日期元件時Grid頁面顯示異常問題
  - 變更檔案: 1 個
- **2018-05-09 10:09:22**: Q00-20180503001 修正流程有自動簽核功能時簽核歷程異常問題
  - 變更檔案: 1 個
- **2018-05-07 18:31:13**: 修正追蹤參數錯誤問題 修正發起漏掉的多語系參數 調整頁籤標題樣式
  - 變更檔案: 5 個
- **2018-05-04 13:09:30**: 修正Grid內設定動態隱藏與顯示元件異常問題
  - 變更檔案: 1 個
- **2018-05-04 09:29:09**: 修正客製開窗無法使用問題,多傳了false參數
  - 變更檔案: 1 個
- **2018-05-04 08:46:13**: 修正頁籤元件內文異常
  - 變更檔案: 5 個

### 治傑 (17 commits)

- **2018-05-24 11:55:01**: 修正行動版FormScript最後不換行，表單會無法發起
  - 變更檔案: 1 個
- **2018-05-23 18:57:08**: 修正企業微信日期元件設定為disabled，仍可編輯
  - 變更檔案: 2 個
- **2018-05-22 19:42:42**: 修正議題 1.鼎捷移動統計元件多語系異常 2.追蹤流程ESS表單顯示異常
  - 變更檔案: 2 個
- **2018-05-21 10:04:14**: 修正鼎捷移動圖表異常
  - 變更檔案: 6 個
- **2018-05-18 15:29:16**: 修正行動版ESS表單顯示異常
  - 變更檔案: 3 個
- **2018-05-17 19:24:12**: 修正鼎捷推播連結異常
  - 變更檔案: 1 個
- **2018-05-17 19:21:57**: 修正流程主旨與派送成功多語系異常
  - 變更檔案: 1 個
- **2018-05-17 17:51:28**: A00-20180510004 行動版Grid用FormScript隱藏欄位後，查看頁面多一個逗號
  - 變更檔案: 1 個
- **2018-05-16 11:34:49**: 調整RESTful說明文件設定
  - 變更檔案: 1 個
- **2018-05-10 19:14:21**: 修正APP小數點進位及位數顯示
  - 變更檔案: 3 個
- **2018-05-10 19:12:12**: 修正行動版DoubleTextBox回寫異常
  - 變更檔案: 1 個
- **2018-05-09 19:20:32**: 修正行動版FormScript點擊儲存表單時，不會儲存FormScript
  - 變更檔案: 1 個
- **2018-05-09 16:05:27**: 調整APP發起連結從流程OID改為流程序號
  - 變更檔案: 5 個
- **2018-05-09 15:51:38**: 修正APP小數點進位及位數顯示漏掉部分
  - 變更檔案: 1 個
- **2018-05-09 14:57:25**: 調整ESS表單退回時鎖定編輯
  - 變更檔案: 2 個
- **2018-05-09 14:49:46**: 調整APP小數點進位及位數顯示功能
  - 變更檔案: 5 個
- **2018-04-30 17:00:23**: 調整客製多選開窗流水號
  - 變更檔案: 1 個

### lorenchang (9 commits)

- **2018-05-23 15:21:47**: 新增用jConsole監控WildFly Datasource的相關檔案及設定
  - 變更檔案: 4 個
- **2018-05-23 14:09:46**: 增加RMI Service設定，解決某些狀況遠端主機無法連線的問題
  - 變更檔案: 4 個
- **2018-05-11 10:11:32**: 更新Patch
  - 變更檔案: 1 個
- **2018-05-11 09:29:51**: 更新update SQL檔名
  - 變更檔案: 2 個
- **2018-05-09 09:19:20**: NaNaEJB增加rmi.hostname設定
  - 變更檔案: 1 個
- **2018-05-09 09:16:50**: 解決RMI在Windows下若開啟防火牆無法連線的問題
  - 變更檔案: 2 個
- **2018-05-02 16:43:40**: 將ECP的Mail實作放入BPM
  - 變更檔案: 3 個
- **2018-04-30 14:02:00**: 修正NaNaWeb.war\WEB-INF\lib內重覆的serializer.jar導致解壓時需要確認是否取代檔案
  - 變更檔案: 1 個
- **2018-04-30 13:43:53**: 版本資訊Created-By、Specification-Version及Built-By加上預設值
  - 變更檔案: 2 個

### 施廷緯 (3 commits)

- **2018-05-23 10:30:52**: 修改OA模組判斷(ProcessID小於3不能繼續派送)
  - 變更檔案: 1 個
- **2018-05-17 17:47:15**: 修改OA模組判斷(ProcessID小於3不能繼續派送)
  - 變更檔案: 1 個
- **2018-04-30 18:01:48**: 在新增表單及新增流程的ID長度卡控，限制字元長度為40。
  - 變更檔案: 2 個

### jd (14 commits)

- **2018-05-09 18:01:56**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 4 個
- **2018-05-09 17:06:44**: 修正微信待辦列表,追蹤流程的工作中頁籤錯誤問題
  - 變更檔案: 3 個
- **2018-05-09 17:05:22**: 修正追蹤流程時,Title元件產生失敗問題
  - 變更檔案: 11 個
- **2018-05-09 14:19:45**: 修正使用Struts轉換JSON字串到頁面上會出現沒有跳脫字元的錯誤
  - 變更檔案: 12 個
- **2018-05-04 15:41:22**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-04 15:38:05**: 修正行動表單水平線問題
- **2018-05-04 15:38:04**: 修正行動表單水平線問題
  - 變更檔案: 7 個
- **2018-05-04 10:03:01**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 2 個
- **2018-05-03 16:55:38**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 1 個
- **2018-05-03 16:55:12**: 修正Title元件生成失敗問題
  - 變更檔案: 16 個
- **2018-05-02 11:00:44**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2018-05-02 10:54:51**: 修正統計圖表需要有歸戶使用者資料才能返回資料的狀況
  - 變更檔案: 1 個
- **2018-04-30 09:31:47**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 1 個
- **2018-04-27 18:22:21**: 修正Title的文字沒顯示問題
  - 變更檔案: 1 個

### 張詠威 (2 commits)

- **2018-05-08 14:33:47**: 調整ISOsession判斷是否為RWD表單邏輯
  - 變更檔案: 3 個
- **2018-05-07 16:10:14**: 修正查詢樣板dropDown預設值的判斷
  - 變更檔案: 1 個

### H05003 (2 commits)

- **2018-05-07 14:23:41**: 修正ISODocServer開窗資料無法帶回議題
  - 變更檔案: 1 個
- **2018-05-04 18:08:59**: 調整ISO獨立模組sessionBean
  - 變更檔案: 11 個

## 詳細變更記錄

### 1. 修正PC版完整menu最後一筆第一層菜單會重覆出現問題
- **Commit ID**: `ae16ba47733e983d9562f1dd88c2ff1a54ba6ef2`
- **作者**: jerry1218
- **日期**: 2018-05-28 15:17:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 2. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `6349aaffabd5b76e0055b6745a361dff1d80fbc9`
- **作者**: joseph
- **日期**: 2018-05-25 18:24:13
- **變更檔案數量**: 0

### 3. 調整 ISO報表模組
- **Commit ID**: `43d61dc899458cd4c2530d879c5948dba1ddd3b3`
- **作者**: joseph
- **日期**: 2018-05-25 18:23:47
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`

### 4. 修正console中的訊息多語系錯誤
- **Commit ID**: `9e6daf7ff80eac9e21ab1cb2b768ffbb716aca2a`
- **作者**: ChinRong
- **日期**: 2018-05-25 18:22:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`

### 5. 修改   下拉式按鈕異常
- **Commit ID**: `5d99005ae9853b4a59485942f5d8d245b78584e1`
- **作者**: joseph
- **日期**: 2018-05-25 17:54:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 6. 新增 E10 Create SQL
- **Commit ID**: `86e44b7a7f8a5d2f5378ace3b4911c7a1be2045a`
- **作者**: joseph
- **日期**: 2018-05-25 17:36:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@E10/create/*******_E10_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@E10/create/*******_E10_DML_Oracle_1.sql`

### 7. 修正  回寫E10狀態異常
- **Commit ID**: `33f2ef9ebbc23c42b8f8b236e340e1d347152cfa`
- **作者**: joseph
- **日期**: 2018-05-25 17:10:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 8. 新增 互聯中台產品資訊接口
- **Commit ID**: `c77d97f93ddbe2c6c0748ddf1310832ca8d7f3fa`
- **作者**: joseph
- **日期**: 2018-05-25 17:09:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`

### 9. 調整ISO log 顯示機制
- **Commit ID**: `c4a0d3c1014f2ee0545b05706bea0005f12ece35`
- **作者**: waynechang
- **日期**: 2018-05-25 17:05:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 10. 修正獨立模組開啟檔案機制
- **Commit ID**: `8e6625a2c0147ff54c96c5bba0f84d54222f9c90`
- **作者**: waynechang
- **日期**: 2018-05-25 17:00:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 11. 修正覆寫ISOProperty邏輯
- **Commit ID**: `101fd8a1fcee1747d498d38d5f48a4c65e172472`
- **作者**: waynechang
- **日期**: 2018-05-25 16:39:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 12. 增加調整設定檔服務
- **Commit ID**: `7be860c3c4ebcec21f3f7c04d9f90ba525908bf2`
- **作者**: waynechang
- **日期**: 2018-05-25 15:27:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 13. 修正退回重辦後，後端已成功退回但卻跳出「無可退回關卡」的邏輯錯誤
- **Commit ID**: `2d5d05461a4fee6955648da8051c064b4d6a1bc1`
- **作者**: Gaspard
- **日期**: 2018-05-25 14:32:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 14. 修改被覆蓋的程式
- **Commit ID**: `8d5c194cc84c65a3b2c6b35e4d2466235c310ab9`
- **作者**: joseph
- **日期**: 2018-05-25 13:47:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`

### 15. 修改被覆蓋的程式
- **Commit ID**: `1dab906af36f80832e5ba55d6624d7ae8404818a`
- **作者**: joseph
- **日期**: 2018-05-25 13:45:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`

### 16. 修復 被覆蓋的程式
- **Commit ID**: `c705a445ab12e3aa297c3d74479057b776666dc3`
- **作者**: joseph
- **日期**: 2018-05-25 13:36:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/.classpath`

### 17. 修復 被覆蓋的程式
- **Commit ID**: `fe98dde20d3c82f7273d86b2637524605c66a33f`
- **作者**: joseph
- **日期**: 2018-05-25 13:09:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-importer/.settings/org.eclipse.jdt.core.prefs`

### 18. 復原 正確的版本
- **Commit ID**: `8a33b9de0e1d1effedb1bb4ebdef2a3039b14a4a`
- **作者**: joseph
- **日期**: 2018-05-25 13:04:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 19. 修正bpm-style.css中的tools name會與其他套件(ex.zTree,shBrushJScript)屬性相衝,故改名spenTools
- **Commit ID**: `4e7c2d78508a574bd2d88e42c96aff8a0b56bead`
- **作者**: jerry1218
- **日期**: 2018-05-25 11:27:44
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 20. 移除不必要log
- **Commit ID**: `eeacc4faa7b1131cb861965d4fc2f5f23fab1be2`
- **作者**: joseph
- **日期**: 2018-05-25 11:26:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`

### 21. 補上RWD表單設計器開發優化
- **Commit ID**: `17e711bfaf1483128537abdea948d17a106ad4c3`
- **作者**: joseph
- **日期**: 2018-05-25 11:22:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`

### 22. 增加ISO獨立模組下載
- **Commit ID**: `a6e862889e020de7bd8e520b9076be440efaa211`
- **作者**: waynechang
- **日期**: 2018-05-25 09:37:49
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/isoModule/struts-manageDocument-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ISOModuleViewer.jsp`

### 23. E10回寫簽核歷程調整
- **Commit ID**: `779b325afa2d7ef614fed89c13cb1495a719151d`
- **作者**: joseph
- **日期**: 2018-05-24 22:24:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 24. ISO變更單新增浮水印內容資料。
- **Commit ID**: `e1aaed32a3afd97111cb2c79a18d9d40bf16b296`
- **作者**: 顏伸儒
- **日期**: 2018-05-24 20:04:46
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/access_control/ISODocCmItem.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/access_control/ISODocCmItem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocCmItemVo.java`

### 25. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `c3f58d932759c6ebea60a9a561fb9e704d3df5ac`
- **作者**: joseph
- **日期**: 2018-05-24 19:53:15
- **變更檔案數量**: 0

### 26. E10 整合功能
- **Commit ID**: `c18f22a1e333463e43b5ca6eed72b51fb0f96f24`
- **作者**: joseph
- **日期**: 2018-05-24 19:52:32
- **變更檔案數量**: 97
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessPackageManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/domain/lib/Json/json.jar`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/RESTfulAPIInfo.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/CheckBoxItem.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormDefinitionExecutionRes.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormDefinitionParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormDefinitionRes.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormDefinitionStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormDefinitionTitle.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormFieldCheckBox.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormFieldDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormFieldGrid.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormFieldRadio.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormFieldSubGrid.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/FormFieldSubTab.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/GridItem.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/RadioItem.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/SubGridItem.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormJSONBean/SubTabPage.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/RESTfulKey.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/org-importer/.settings/org.eclipse.jdt.core.prefs`
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Gson/gson-2.5.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/E10SendSignInfoBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/TestyJSONBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10Manager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormDefinitionJSONTransfer.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormInstanceTransferJson.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/GrantAccessTokenParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/GrantAccessTokenReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/GrantAccessTokenStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeProcessFormDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeProcessParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeProcessReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeProcessStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessAbortParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessAbortReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessAbortStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessInstUrlParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessInstUrlReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessInstUrlStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessListParameterReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessListReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ProcessListStdDataReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GrantAccessTokenParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GrantAccessTokenRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GrantAccessTokenStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/IdentityExecutionRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/InvokeProcessParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/InvokeProcessRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/InvokeProcessStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PrcoessListFormInfoRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessAbortParamterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessAbortRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessAbortStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessExecutionRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstUrlParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstUrlRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessInstUrlStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessListInfoRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessListParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessListRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessListStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/EAIHeaderKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/IntegrationFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/JreVersionCheck.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`

### 27. 修正APP中RWD表單grid自動生成按鈕異常問題遺漏部分
- **Commit ID**: `a5fe60ee96cd23584bb45e5ed8b7875a6c81e0bd`
- **作者**: pinchi_lin
- **日期**: 2018-05-24 19:33:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 28. 修正APP中grid的value資料格式不對問題
- **Commit ID**: `216453c4a9413e8f82df12b3a3b0a5bba78a1d2a`
- **作者**: pinchi_lin
- **日期**: 2018-05-24 19:29:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerButton.java`

### 29. 修正行動表單有小數點時外顯值沒有顯示問題
- **Commit ID**: `098fc1c037e4add34f8fedb8a9a443d7fa15af52`
- **作者**: yamiyeh10
- **日期**: 2018-05-24 18:57:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormManager.js`

### 30. 修正上傳附件後Grid新增失效的問題
- **Commit ID**: `22e5a490068702e1bb3b6e4434a7b0b2da9b70e4`
- **作者**: ChinRong
- **日期**: 2018-05-24 18:29:42
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 31. 修正APP中RWD表單grid元件自動生成按鈕異常問題
- **Commit ID**: `0a8c415680885c11a81af60d46f6dfca29ab1504`
- **作者**: pinchi_lin
- **日期**: 2018-05-24 17:17:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 32. 修改easyflow整合發起時一律開新視窗
- **Commit ID**: `a0d726e1a2a81c5f660cde362af71a7cd099e15e`
- **作者**: jerry1218
- **日期**: 2018-05-24 17:00:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp`

### 33. A00-20180522001 修正發起流程清單左邊分類選單無法正常向下捲動的異常
- **Commit ID**: `191f32e29f472ac48e078038ed46bd0cd3e96ffc`
- **作者**: jerry1218
- **日期**: 2018-05-24 15:49:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 34. 調整上傳附件重載表單的服務
- **Commit ID**: `c7a443545a90bb1c9567315c6c669bb1e8116013`
- **作者**: ChinRong
- **日期**: 2018-05-24 15:25:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`

### 35. 調整行動表單頁籤元件樣式 調整Title元件圖片大小
- **Commit ID**: `277f21ecb4f0245852e68bfb01c16ecaf6079ada`
- **作者**: yamiyeh10
- **日期**: 2018-05-24 14:53:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 36. 修正行動版FormScript最後不換行，表單會無法發起
- **Commit ID**: `a5c52813a40ec2c33175c14af90f87cc5e7c1253`
- **作者**: 治傑
- **日期**: 2018-05-24 11:55:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 37. 新增頁籤formscript設置顯示、隱藏、預設功能 調整行動版表單範例說明-新增頁籤範例與調整版本
- **Commit ID**: `a4f8ec4d0723cb19152800feec261be51d291ceb`
- **作者**: yamiyeh10
- **日期**: 2018-05-24 11:21:06
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElementMobile.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/AppRwdSubTabTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileSubTab.js`

### 38. 修正議題
- **Commit ID**: `e9711fb227fa9ee6265065e7598bc3e09c02bc9f`
- **作者**: ChinRong
- **日期**: 2018-05-23 19:07:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppRwdGridTemplate.txt`

### 39. 修正企業微信日期元件設定為disabled，仍可編輯
- **Commit ID**: `4273d409ac46d242c70e7e6266795372104c47fe`
- **作者**: 治傑
- **日期**: 2018-05-23 18:57:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 40. 修正App千分位屬性增加與運算異常問題
- **Commit ID**: `bf00b51713d5c3a51accca3368abb780b30c36d5`
- **作者**: yamiyeh10
- **日期**: 2018-05-23 18:41:24
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`

### 41. 調整行動版表單範例說明
- **Commit ID**: `d4ea106a64fc667ae3372cb1179884fa99630711`
- **作者**: yamiyeh10
- **日期**: 2018-05-23 17:46:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`

### 42. 將load的圖關閉以免影響附件上傳畫面異常
- **Commit ID**: `2b6c60d78e5d7fce19a686896f25efe58d64587b`
- **作者**: waynechang
- **日期**: 2018-05-23 17:27:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 43. C01-20180522001 簽核畫面會一直轉圈圈，但有成功簽核下去，防呆+錯誤時印error log
- **Commit ID**: `67feb112628bb7d6f8940838dbdc2309eb52936e`
- **作者**: jerry1218
- **日期**: 2018-05-23 16:58:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 44. 修正鼎捷移動行動表單上傳附件後出現JSON異常的問題
- **Commit ID**: `9a2414b25286300c89753a8e936a123a9aaf79b6`
- **作者**: ChinRong
- **日期**: 2018-05-23 16:52:53
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`

### 45. 簽核流設計師新增行動版Grid自動生成按鈕的存取限制設定
- **Commit ID**: `7a65df66e557a044255688df65be688efe862252`
- **作者**: ChinRong
- **日期**: 2018-05-23 16:38:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`

### 46. 異常修正
- **Commit ID**: `****************************************`
- **作者**: jerry1218
- **日期**: 2018-05-23 15:23:07
- **變更檔案數量**: 45
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-abortProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-administratorFunction-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-appFormModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-businessProcessMonitor-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-columnMask-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-createProcessDocument-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-customQuery-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-dealDoneWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDataMainten-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-gatherWfStatistics-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-installCertificate-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-integratePortalURLEntrance-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-languageMaintain-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-licenseModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageCustomReport-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageCuzPattern-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageDraft-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageFavorites-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageOnlineUser-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-managePhrase-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageProcessModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSysIntegration-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageUserProfile-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageWfNotification-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mcloud-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-previewProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-processPerformanceMonitor-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-redoInvoke-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-resignedEmployeesMaintain-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-sap-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-searchFormData-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-searchOrgData-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-serviceRegister-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-sysintegrationSet-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-systemSchedule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceRelationalProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-updateVersion-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-validateProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`

### 47. 新增用jConsole監控WildFly Datasource的相關檔案及設定
- **Commit ID**: `001903f6c11fb94bdcfe073677f21793bbfddc3b`
- **作者**: lorenchang
- **日期**: 2018-05-23 15:21:47
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@java/jdk1.8.0_151/bin/jconsole.exe`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/jconsole.bat`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 48. 調整行動版表單設計器Grid元件自動生成formscript方法觸發時機
- **Commit ID**: `43e9e309c4f76cafb7a5181037138114e15b9b08`
- **作者**: ChinRong
- **日期**: 2018-05-23 15:06:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 49. 註解行動表單自動生成Grid按鈕方法
- **Commit ID**: `eda67173d8dc041d825c268e72aa69be76fcf433`
- **作者**: yamiyeh10
- **日期**: 2018-05-23 14:25:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 50. 增加RMI Service設定，解決某些狀況遠端主機無法連線的問題
- **Commit ID**: `8bf97fe735bf45cab905003f641d7a5758551fec`
- **作者**: lorenchang
- **日期**: 2018-05-23 14:09:46
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/RmiRegistry.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/SMRMISocket.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaEJB.properties`

### 51. 修改查詢的SQL使用System.out印在console上
- **Commit ID**: `5d99b488d92f2ae8cc0de5d493d4a536211bc133`
- **作者**: Gaspard
- **日期**: 2018-05-23 13:43:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 52. 修正議題
- **Commit ID**: `e87a5a3d5f0412d2774054c9bae7240582c1e4c1`
- **作者**: ChinRong
- **日期**: 2018-05-23 12:18:41
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`

### 53. 修正C01-20180516001 ISO轉檔議題
- **Commit ID**: `9a03c220268000e4cfa65bb7dee1873de0bd79e5`
- **作者**: waynechang
- **日期**: 2018-05-23 11:07:50
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 54. Q00-20180523001 修正無法開啟Remote附件問題
- **Commit ID**: `da604febf95273b911d57187e0febd68639fc1c4`
- **作者**: jerry1218
- **日期**: 2018-05-23 10:53:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 55. 修改OA模組判斷(ProcessID小於3不能繼續派送)
- **Commit ID**: `afed0efc9de6fb3a0a98898e7f65c88656dcc42e`
- **作者**: 施廷緯
- **日期**: 2018-05-23 10:30:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 56. 修正議題 1.鼎捷移動統計元件多語系異常 2.追蹤流程ESS表單顯示異常
- **Commit ID**: `f1386947b3f3900281590019bae954b02c3335cf`
- **作者**: 治傑
- **日期**: 2018-05-22 19:42:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 57. 修正行動版表單畫面跑版
- **Commit ID**: `5b534dacd4989edeb0b76b8056cbe2ca8983369e`
- **作者**: yamiyeh10
- **日期**: 2018-05-22 19:17:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 58. 修正APP中textbox元件因運算導致顯示異常問題
- **Commit ID**: `2b43c890987a692cb5ee199c18bf6933c859fa93`
- **作者**: pinchi_lin
- **日期**: 2018-05-22 18:42:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`

### 59. 移除struts XML中的多語言檔案設定,避免操作時LOG出現警告訊息
- **Commit ID**: `5c9e918a82497322f1b41f587b9861ade8a6d1c8`
- **作者**: jerry1218
- **日期**: 2018-05-22 09:04:52
- **變更檔案數量**: 44
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-abortProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-administratorFunction-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-appFormModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-businessProcessMonitor-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-columnMask-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-createProcessDocument-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-customQuery-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-dealDoneWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDataMainten-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-gatherWfStatistics-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-installCertificate-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-integratePortalURLEntrance-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-languageMaintain-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-licenseModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageCustomReport-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageCuzPattern-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageDraft-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageFavorites-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageOnlineUser-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-managePhrase-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageProcessModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSysIntegration-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageUserProfile-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageWfNotification-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mcloud-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-previewProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-processPerformanceMonitor-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-redoInvoke-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-resignedEmployeesMaintain-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-sap-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-searchFormData-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-searchOrgData-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-serviceRegister-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-sysintegrationSet-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-systemSchedule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceRelationalProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-updateVersion-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-validateProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`

### 60. 修正ISO附件無法上傳議題
- **Commit ID**: `a7eb5576b8f8105ceadbd1b22c79aca50b204d24`
- **作者**: waynechang
- **日期**: 2018-05-21 19:03:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 61. 減少INFO LOG
- **Commit ID**: `a08212cdcaef4a4b27bd3028de4885bd1b22a8ad`
- **作者**: jerry1218
- **日期**: 2018-05-21 17:34:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java`

### 62. 將service中的ejbCreate等method中的info層級改為debug, 減少INFO LOG
- **Commit ID**: `2e0e629b44e17c60f76bc9cfcdf1dc68e173c06a`
- **作者**: jerry1218
- **日期**: 2018-05-21 17:15:16
- **變更檔案數量**: 40
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cuzProgram/CuzProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/AttachmentDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessExchangeHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/DraftManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormCategoryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDefBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processModule/ProcessModuleDefMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/schedule/TimerWorkHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/commAM/CommAMManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/CrmManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/easyflow/EasyFlowManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mcloud/McloudManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileScheduleManageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/portal/PortalManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerBean.java`

### 63. 移除多餘system.out.println
- **Commit ID**: `4d47d4426f8fe96e36a48c1dc853757df64b5bde`
- **作者**: jerry1218
- **日期**: 2018-05-21 15:58:56
- **變更檔案數量**: 55
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SystemConfigManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/ProcessTemplateMangerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_side_util/WebBrowser.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/remote_call/RemoteCallFactoryImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/AbstractDesignerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/extend_swing/AbstractMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/util/DesignerExceptionHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DesignTempFile.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/schedule/SystematicScheduler.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/DatabasesFinder.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/SchemaType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForReassignDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/RebuildBamDataBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cuzProgram/CuzProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLocalNoticeWorkItemDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBProcessNotificationDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBTemplateGenMappingDataDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBWFRequestRecordDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/util/WorkFlowUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/ValueListHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ForwardIndexAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ServiceRegisterAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/data/UserForClient.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessPreviewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PortalIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/ViewWorkItem.jsp`

### 64. 修正鼎捷移動圖表異常
- **Commit ID**: `99110da7622f696f9db2a44c720019546a962508`
- **作者**: 治傑
- **日期**: 2018-05-21 10:04:14
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterGaugeChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterLinebarChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterPieChartRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/SeriesConfigForPie.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 65. 加入log
- **Commit ID**: `df2c0a7b7b34558690e5c3095bd0c366b9536b57`
- **作者**: pinchi_lin
- **日期**: 2018-05-18 18:28:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 66. 修正加簽進階搜尋功能異常,需先引入material再引pdf的js檔案
- **Commit ID**: `5b8f5bc792c1d40c369761bd818a58f3d8be4639`
- **作者**: yamiyeh10
- **日期**: 2018-05-18 18:25:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`

### 67. 修改Web表單設計器的scrollbar呈現
- **Commit ID**: `6757a0cadc68199df172dca592e089ca3fea1ee6`
- **作者**: Gaspard
- **日期**: 2018-05-18 17:10:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`

### 68. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `bcff15d3c5f9cef187be6d20d7d3dab9f102dd50`
- **作者**: Gaspard
- **日期**: 2018-05-18 16:40:32
- **變更檔案數量**: 0

### 69. 增加多語系
- **Commit ID**: `0e70058e7a2d68d148583f31f2523677e983fc42`
- **作者**: Gaspard
- **日期**: 2018-05-18 16:40:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`

### 70. 修正行動表單畫面跑版
- **Commit ID**: `929e73c2fe8f0fc5543e89fefe055957c03c4e6e`
- **作者**: yamiyeh10
- **日期**: 2018-05-18 16:34:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 71. A00-20180516001 修正外部連結URL searchFormDetail於小螢幕無法顯示附件問題
- **Commit ID**: `99a838d59f29f3f03b50c877488599c1078db1aa`
- **作者**: jerry1218
- **日期**: 2018-05-18 15:45:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 72. 修正行動版ESS表單顯示異常
- **Commit ID**: `c1a2fed43d7c18b13f2f530416e5c74d55a64d40`
- **作者**: 治傑
- **日期**: 2018-05-18 15:29:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 73. 修正easyflow整合無法開新視窗問題
- **Commit ID**: `f196d33a9cefa051d8eeedfc9e57a31b5c7ac2cf`
- **作者**: jerry1218
- **日期**: 2018-05-18 14:30:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp`

### 74. 修正樹狀開窗中，當組織下無專案或群組時，點擊節點會拋出錯誤提示的異常
- **Commit ID**: `b222ca0ad4ef322531cd7fb81cf108135fed4099`
- **作者**: Gaspard
- **日期**: 2018-05-18 13:53:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TreeViewDataChooserAjax.java`

### 75. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `063c7f3b69dba284e0243931e5cc919e17f86926`
- **作者**: ChinRong
- **日期**: 2018-05-18 12:06:36
- **變更檔案數量**: 0

### 76. 修正議題: RWD表單轉換成APP表單時的提示訊息
- **Commit ID**: `bc26c4086439e0dfcdc8cee0139563a0c730decb`
- **作者**: ChinRong
- **日期**: 2018-05-18 12:06:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 77. 修正APP中RWD表單位設計APP時中間層不會顯示問題
- **Commit ID**: `014d205e3c57551ae6a191efacf7fdaecef0c5c4`
- **作者**: pinchi_lin
- **日期**: 2018-05-18 12:04:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 78. 修正議題
- **Commit ID**: `67b9d9c6d5917e11aaf5af2e4a1e1beef0accca1`
- **作者**: ChinRong
- **日期**: 2018-05-18 11:29:20
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`

### 79. 移除多餘註解
- **Commit ID**: `3db3846bb4a16f7593ef8465d23accfa7a0f80a3`
- **作者**: Gaspard
- **日期**: 2018-05-18 11:22:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 80. 修正行動版RWD設計器的議題
- **Commit ID**: `4bba4bdebbfb36d6de3c8ccdbd2eb9b702cfd7d7`
- **作者**: ChinRong
- **日期**: 2018-05-18 10:10:28
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/FormAppRWDDiagram.css`

### 81. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `8528c636aee6c792c87cc211d74513f0af48e0c8`
- **作者**: Gaspard
- **日期**: 2018-05-18 09:13:34
- **變更檔案數量**: 0

### 82. 一致性表單複合式元件中按鈕的寬度。
- **Commit ID**: `b0942d682c64e8d922634785d73f189515390bd5`
- **作者**: Gaspard
- **日期**: 2018-05-18 09:13:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 83. 修正APP中相對位置表單grid無法使用與gird資料在div上顯示異常問題
- **Commit ID**: `3669ac87aa7d15d1293629789eee3d29d41025f2`
- **作者**: pinchi_lin
- **日期**: 2018-05-17 19:32:21
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 84. 修正無法刪除附件議題
- **Commit ID**: `4c79bc621059d6732df67d446b129ef0ab062e0a`
- **作者**: yamiyeh10
- **日期**: 2018-05-17 19:29:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 85. 修正鼎捷推播連結異常
- **Commit ID**: `3dfe50172e4c94fcff4cf171cdf7717ff9251c9d`
- **作者**: 治傑
- **日期**: 2018-05-17 19:24:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 86. 修正流程主旨與派送成功多語系異常
- **Commit ID**: `d89854bd5e5d149d8c92674ba5e30dbd3e7fe78f`
- **作者**: 治傑
- **日期**: 2018-05-17 19:21:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 87. 修正Grid元件在隱藏標籤時顯示異常
- **Commit ID**: `6411852c96df35799357f045a2d3048b293ace15`
- **作者**: yamiyeh10
- **日期**: 2018-05-17 18:27:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`

### 88. 調整APP中RWD表單grid其資料顯示在div異常問題
- **Commit ID**: `fc8138efe508ab28fb33ade196babeaf02c66eaa`
- **作者**: pinchi_lin
- **日期**: 2018-05-17 18:02:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`

### 89. A00-20180510004 行動版Grid用FormScript隱藏欄位後，查看頁面多一個逗號
- **Commit ID**: `ae9b1b0f022a40a7bf568fe128c01ad6871b3c9b`
- **作者**: 治傑
- **日期**: 2018-05-17 17:51:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 90. 修改OA模組判斷(ProcessID小於3不能繼續派送)
- **Commit ID**: `deaca4144b6dee1bd3ff39422491a1e4c88125d8`
- **作者**: 施廷緯
- **日期**: 2018-05-17 17:47:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 91. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `6bf3f185dd943ef246a392d316c108505045c3c4`
- **作者**: Gaspard
- **日期**: 2018-05-17 17:43:33
- **變更檔案數量**: 0

### 92. 修改在IE高安全性時，菜單與浮動式按鈕無法使用的異常
- **Commit ID**: `b15e1e8041c178080bf49af6f3679ddd7334c6de`
- **作者**: Gaspard
- **日期**: 2018-05-17 17:43:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 93. 修正追蹤已關閉流程時,顯示流程及顯示表單按鈕重覆出現問題
- **Commit ID**: `fd22a4c70c059cbf37180b1f1cfa97dd556eb055`
- **作者**: jerry1218
- **日期**: 2018-05-17 17:08:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 94. Q00-20180515001 修正系統管理員無法跳過高置中的關卡問題
- **Commit ID**: `0082e4ed1f61d2c4afd8ccdd7bdba02737057189`
- **作者**: jerry1218
- **日期**: 2018-05-17 16:46:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 95. 修正DoubleTextBox在隱藏標籤時顯示異常
- **Commit ID**: `aa5b9700470027476ce1c477400cb220b9e55a71`
- **作者**: yamiyeh10
- **日期**: 2018-05-17 16:22:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`

### 96. 修正App附件列表顯示異常議題
- **Commit ID**: `804789bc1ea87d5ba0b6f018ed8e027cfaa6dbb2`
- **作者**: yamiyeh10
- **日期**: 2018-05-17 16:15:06
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 97. 修正APP鼎捷移動端使用管理員配置圖表應用異常問題
- **Commit ID**: `67ac0e37abca545f5c166fd2f8500474fbc3332d`
- **作者**: pinchi_lin
- **日期**: 2018-05-17 16:08:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 98. C01-20180509003 待辦及追蹤流程清單效能議題
- **Commit ID**: `2d0932c7bb30ca69f559852ef74e1916fafbf042`
- **作者**: jerry1218
- **日期**: 2018-05-17 15:48:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`

### 99. 優化Web表單設計器左側樹狀區塊Scrollbar的呈現
- **Commit ID**: `4a2013361ba8410bdad70e84bc0694b16325fd91`
- **作者**: Gaspard
- **日期**: 2018-05-17 14:56:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`

### 100. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `453727f1c6f2311f28e319ca07a0442d5984750e`
- **作者**: Gaspard
- **日期**: 2018-05-17 14:35:01
- **變更檔案數量**: 0

### 101. 修正RWD表單script編輯器中，於chrome無法將全域變數或元件事件加到script中的異常
- **Commit ID**: `67a03c353b16a44076b2a244fca32af2516cb92b`
- **作者**: Gaspard
- **日期**: 2018-05-17 14:34:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormScriptEditor.jsp`

### 102. 修正APP微信端在追蹤流程中下方頁籤顯示錯誤問題
- **Commit ID**: `60e5926962e446f329cee224d26f148f9730517a`
- **作者**: pinchi_lin
- **日期**: 2018-05-17 14:22:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`

### 103. 修正嵌入ESS表單時，表單固定高度為1000
- **Commit ID**: `8ea4de293026f4e0047c88cc29c26708828a3972`
- **作者**: Gaspard
- **日期**: 2018-05-17 13:59:30
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 104. 修正PC有附件時,App未顯示附件清單議題
- **Commit ID**: `0eb9a36dd4a7211c74810df271c98fe57313d2cc`
- **作者**: yamiyeh10
- **日期**: 2018-05-17 13:18:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`

### 105. 修正APP中doubleTextbox必填驗證異常問題
- **Commit ID**: `2d47f5d9e52202bd1f5b9a61eb4c6828d5b2f56a`
- **作者**: pinchi_lin
- **日期**: 2018-05-17 12:54:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/formValidation.js`

### 106. 修正複合式元件在流程設計器中修改各關卡行動版的驗證屬性時，無法有效保存的議題
- **Commit ID**: `da83439736420a2bbf3342791cfca81769db29cb`
- **作者**: ChinRong
- **日期**: 2018-05-17 10:57:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`

### 107. 修正議題 1.鼎捷移動使用者管理頁面只保留搜尋BPM帳號 2.修正鼎捷移動無法發單議題 3.補上頁籤功能漏將class名稱替換
- **Commit ID**: `ef61de21cebd9d3f1b4ddbc2da59093651d9ae56`
- **作者**: yamiyeh10
- **日期**: 2018-05-17 08:46:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`

### 108. 修正APP微信端草稿流程進入表單畫面異常議題
- **Commit ID**: `21573c5d6ddd29a36f5a7caa4d5f8f82459c0d2e`
- **作者**: pinchi_lin
- **日期**: 2018-05-16 19:38:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 109. 增加ISO模組閱讀檔案機制
- **Commit ID**: `6459068e78df3fd0b4f05da5413380bed43fada5`
- **作者**: waynechang
- **日期**: 2018-05-16 17:59:56
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISODocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ISOFileDownloadKeyCache.java`

### 110. 修正APP微信端發起時因為jsp緩存導致worStep dirty問題
- **Commit ID**: `a392b218aac8f57ffb8a6d723faa5ac2910ae256`
- **作者**: pinchi_lin
- **日期**: 2018-05-16 17:34:16
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`

### 111. 調整APP中formSave的驗證時機點
- **Commit ID**: `a0f827549c71cee6dd44617ed0014c1c71fa9e60`
- **作者**: pinchi_lin
- **日期**: 2018-05-16 17:22:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 112. 修正新發起流程頁面與easyflow整合異常問題
- **Commit ID**: `ea19dbd984d7bb3b033c22a1a771013ffed5d873`
- **作者**: jerry1218
- **日期**: 2018-05-16 17:15:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`

### 113. 修正APP中千分位顯示問題與小數點顯示問題
- **Commit ID**: `049d7ac9b3fee7f69aed9315c19ac7a41eeee4b1`
- **作者**: pinchi_lin
- **日期**: 2018-05-16 17:10:00
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/formValidation.js`

### 114. 1.系統通知頁面微調  2.排程設定畫面微調
- **Commit ID**: `c0cc15c5f7338771f4767a7169014070b369d264`
- **作者**: jerry1218
- **日期**: 2018-05-16 14:25:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`

### 115. 修正議題
- **Commit ID**: `bf9acfcb64e76234a396e3ceee001141881f99aa`
- **作者**: ChinRong
- **日期**: 2018-05-16 13:48:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`

### 116. 修正App頁簽元件顯示異常問題
- **Commit ID**: `df4505fe9ffdbbe1784ecc7a2524d61650eeb6fe`
- **作者**: yamiyeh10
- **日期**: 2018-05-16 13:43:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 117. [A00-20180129002]修正BPM57版本,簽核流程設計師,修改核決權限關卡的ID項目值時未存入資料庫。
- **Commit ID**: `1633e51fc1b05c50bebb426524ac1b25fc4ad315`
- **作者**: 顏伸儒
- **日期**: 2018-05-16 11:50:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 118. 調整RESTful說明文件設定
- **Commit ID**: `6e234a2de2b5531c77df88b0c1459f2ee4c94f3b`
- **作者**: 治傑
- **日期**: 2018-05-16 11:34:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`

### 119. 修正T100簽和樣版中的回寫沒有設定好流程變數問題
- **Commit ID**: `60f2ee6ba96b088e816ec7e3155fb548e7e76950`
- **作者**: jerry1218
- **日期**: 2018-05-16 11:06:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/process-default/template/\345\205\251\351\227\234\345\210\266\347\260\275\346\240\270\346\250\243\346\235\277.bpmn"`

### 120. [C01-20180208001]修正BPM57版本,流程負責人在管理流程時，加入收尋自動簽核狀態的單據。
- **Commit ID**: `c3fa170ab6d33bf58ba2e730a2d811628fe433f7`
- **作者**: 顏伸儒
- **日期**: 2018-05-16 10:51:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 121. 修正議題
- **Commit ID**: `4098bb7f079f0cdad2aecc72c7b1a42c9f29d716`
- **作者**: ChinRong
- **日期**: 2018-05-15 18:36:36
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`

### 122. A00-20180504002 修正主旨有換行時追蹤流程清單顯示異常
- **Commit ID**: `683387562b774e5803407b0e5aa54f92b698892d`
- **作者**: jerry1218
- **日期**: 2018-05-15 17:49:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/ProcessInstViewer.java`

### 123. 修正IOS瀏覽器[切換為電腦版網頁]議題修正所造成的後續異常
- **Commit ID**: `5513cc89919df9c9fed0030b4f982d64a9cf1919`
- **作者**: jerry1218
- **日期**: 2018-05-15 17:48:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 124. 修正個人中心sidebar於小螢幕時的顯示異常
- **Commit ID**: `8d8e7769ff5745def855817171f02db016720c60`
- **作者**: jerry1218
- **日期**: 2018-05-15 17:45:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 125. 修改多語系顯示邏輯，若查無多語系資料，則使用??xxxx.xxxx??的方式呈現
- **Commit ID**: `96223fec22d4434a915a686f2973383714f97630`
- **作者**: Gaspard
- **日期**: 2018-05-15 17:15:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/RsrcBundleCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/LoadResourceBundle.java`

### 126. 修正追蹤流程時，表單元件仍可編輯的異常。
- **Commit ID**: `ceb2d2480c4c310ff5c47feb14024c1bea878851`
- **作者**: Gaspard
- **日期**: 2018-05-15 12:09:10
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/FormElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`

### 127. A00-20180511001 修正開單時單身空白處理邏輯異常導致表單畫面單身資料無法載入問題
- **Commit ID**: `3cb369e9d64d6ff30869ed47abe61de003929f7c`
- **作者**: jerry1218
- **日期**: 2018-05-15 10:34:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 128. 強制轉型RestFul回傳值為UTF-8
- **Commit ID**: `f91d9c3bfb2392bf8c32dc8b7ffb2c4718f85d1b`
- **作者**: waynechang
- **日期**: 2018-05-15 10:23:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestFulHelper.java`

### 129. 修正ISO文件開窗簡易查尋失敗
- **Commit ID**: `57b1b12119ef47bf55b4d80346dd50d9acc4baea`
- **作者**: waynechang
- **日期**: 2018-05-15 10:22:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`

### 130. 修改ISO型態開窗。
- **Commit ID**: `2e92dc546366355c6bf6fbf333084d5bbec37de2`
- **作者**: 顏伸儒
- **日期**: 2018-05-14 20:54:34
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocCmItemVo.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ISODocTypeVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 131. 修正自訂義開窗的Dialog相關元件，若設定visiable時，無法除存表單的異常
- **Commit ID**: `5358c72c8788a788a014d09f506736c314ba5d5e`
- **作者**: Gaspard
- **日期**: 2018-05-14 14:19:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 132. 增加json-lib套件引用
- **Commit ID**: `c3309c9db372325770d91bdeb7090d706b0f6f92`
- **作者**: waynechang
- **日期**: 2018-05-11 17:59:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`

### 133. 增加取得ISO獨立模組的站台位置
- **Commit ID**: `c943a350438fd423b7d0c8a2c072b0884191e261`
- **作者**: waynechang
- **日期**: 2018-05-11 17:51:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 134. 調整ISO報表取得ISOType
- **Commit ID**: `9bf8d805787d0d999234f8a6de31079e0956cb86`
- **作者**: waynechang
- **日期**: 2018-05-11 17:45:52
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/access_control/ISODocCmItem.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/access_control/ISODocCmItem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocForReportViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp`

### 135. 修改在IOS的瀏覽器切換為「電腦版瀏覽」時，會自動切到登錄頁。
- **Commit ID**: `2e74cb4e36a4c32b7c1c7b7d63d770325f6bda41`
- **作者**: Gaspard
- **日期**: 2018-05-11 16:54:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 136. 修正複合式元件在流程設計器中修改各關卡的驗證屬性時，無法有效保存的議題
- **Commit ID**: `a8f6a4cd1de2b091d134dc13562556925643cdd3`
- **作者**: Gaspard
- **日期**: 2018-05-11 14:42:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`

### 137. 修改表單中的多選開窗無法保留前一次選取人數的異常
- **Commit ID**: `d0878efe8654eb3faf3c142bbbe2cca80344ea59`
- **作者**: Gaspard
- **日期**: 2018-05-11 14:41:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`

### 138. 更新Patch
- **Commit ID**: `4915977d16215d1308199a234f0d1683ded8db75`
- **作者**: lorenchang
- **日期**: 2018-05-11 10:11:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 139. 更新update SQL檔名
- **Commit ID**: `a801b7a6fe915cf5cf9ea554b34d7e93fe4514b1`
- **作者**: lorenchang
- **日期**: 2018-05-11 09:29:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle.sql`

### 140. 新增RWD行動版表單Grid套件及載入機制
- **Commit ID**: `bc0c70c3d8f2fed4eb3a5403920be55ae626881d`
- **作者**: ChinRong
- **日期**: 2018-05-10 20:22:00
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmAppTable.js`

### 141. 修正表單設計師驗證欄位。
- **Commit ID**: `aed49e26c5f22944d4e3fb620edda9e6d50d1289`
- **作者**: 顏伸儒
- **日期**: 2018-05-10 20:08:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 142. 修正APP小數點進位及位數顯示
- **Commit ID**: `657c3f0ecdd017da0c9692490a857c8f62d6fb87`
- **作者**: 治傑
- **日期**: 2018-05-10 19:14:21
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`

### 143. 修正行動版DoubleTextBox回寫異常
- **Commit ID**: `36daf77d4a763808bac6d8343a6c6c246d8d66b5`
- **作者**: 治傑
- **日期**: 2018-05-10 19:12:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 144. 修正APP開窗元件disable問題與樣板中含錢字號問題
- **Commit ID**: `2f329f87425946ce14eed2ac87faf76366965dd3`
- **作者**: pinchi_lin
- **日期**: 2018-05-10 18:27:48
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/SubElementContainer.java`

### 145. A00-20180510007 修正外部連結URL查看表單無法下載附件問題
- **Commit ID**: `ebef243a9adfd2c6f383b4da3c201682ba3ee4e2`
- **作者**: jerry1218
- **日期**: 2018-05-10 16:41:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 146. A00-20180510002 修正發單與簽核頁面XPDL的流程圖過長時顯示橫向卷軸
- **Commit ID**: `8557be8edb263ab80a3274267fbe8cada05b7dd1`
- **作者**: Gaspard
- **日期**: 2018-05-10 15:48:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`

### 147. 調整行動簽核中心鼎捷移動使用者欄位，新增最後登入時間欄位
- **Commit ID**: `48cd9ba7f2e50fae710d6b5c53c3257548ab659f`
- **作者**: ChinRong
- **日期**: 2018-05-10 13:56:22
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthWeChatUser.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthClientUserDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentWeChateUser.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`

### 148. 修正自定義的DialogInputLabel元件，儲存表單後再發起表單，右側欄位的值被左側欄位值覆蓋的異常
- **Commit ID**: `50de51b6ba36c6f1aa061336846207ba171af30c`
- **作者**: Gaspard
- **日期**: 2018-05-10 10:22:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 149. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `298cad66ebd1e92649cc5b54b995b757f031dca5`
- **作者**: ChinRong
- **日期**: 2018-05-10 09:49:12
- **變更檔案數量**: 0

### 150. 新增行動版Grid套件
- **Commit ID**: `e84edbe4bda2b3114083588e67f05348fda3b436`
- **作者**: ChinRong
- **日期**: 2018-05-10 09:48:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/AppRwdGridTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`

### 151. 修正Grid自動生成漏簽部分
- **Commit ID**: `fcb2bc9ceb21284775bbf111b2160652aaebf39d`
- **作者**: yamiyeh10
- **日期**: 2018-05-10 09:44:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 152. 修正Grid繫結的元件不會生成的問題
- **Commit ID**: `e9adf5c57e361ba8f415e8b1c29f7b9bd99482dd`
- **作者**: ChinRong
- **日期**: 2018-05-10 09:10:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 153. 新增Grid自動生成新增、修改、刪除按鈕
- **Commit ID**: `db3cb9bb8bbe5911331c80224a8ed76de47f024e`
- **作者**: yamiyeh10
- **日期**: 2018-05-09 19:43:25
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`

### 154. 修正行動版FormScript點擊儲存表單時，不會儲存FormScript
- **Commit ID**: `cfcd7571a46e90e154721c47c19d09af0ece324b`
- **作者**: 治傑
- **日期**: 2018-05-09 19:20:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 155. 調整APP原先取欄位驗證的地方改成行動用的方法
- **Commit ID**: `da2bbced19c881376aabd349db8b69181ff0f1ee`
- **作者**: pinchi_lin
- **日期**: 2018-05-09 18:35:20
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 156. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `64c636f8521839c7629bf4fe81e4b8c4a134796c`
- **作者**: jd
- **日期**: 2018-05-09 18:01:56
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 157. 修正APP中checkbox與radio元件在onblur時會無法觸發與錯誤訊息滑動問題
- **Commit ID**: `ec74c09214c756aec53f8de4ac993c798c8f3190`
- **作者**: pinchi_lin
- **日期**: 2018-05-09 17:26:21
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 158. 修正微信待辦列表,追蹤流程的工作中頁籤錯誤問題
- **Commit ID**: `d22280a439b3bf4d5d6c3773f20384cb621ec1a3`
- **作者**: jd
- **日期**: 2018-05-09 17:06:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`

### 159. 修正追蹤流程時,Title元件產生失敗問題
- **Commit ID**: `f1332e2dae8921a416965a7bcd1ddf96e0063661`
- **作者**: jd
- **日期**: 2018-05-09 17:05:22
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 160. 多語系調整
- **Commit ID**: `a76e48e2f3469e0a7f955d8a8985159452847b49`
- **作者**: jerry1218
- **日期**: 2018-05-09 16:07:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`

### 161. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `7080d6cbc74dc5129ccc317f33abc5e5eff55f39`
- **作者**: jerry1218
- **日期**: 2018-05-09 16:06:31
- **變更檔案數量**: 0

### 162. A00-20180509001 修正外部連結URL - 追蹤流程(圖+表) and 追蹤流程(圖)開啟的畫面顯示問題
- **Commit ID**: `90038fe62d83842be480c86eeab57b0f8fd59260`
- **作者**: jerry1218
- **日期**: 2018-05-09 16:06:03
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 163. 調整APP發起連結從流程OID改為流程序號
- **Commit ID**: `52cd24c09492009299471252f7ad7244059f391c`
- **作者**: 治傑
- **日期**: 2018-05-09 16:05:27
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js`

### 164. 修正Grid元件繫結日期元件時Grid頁面顯示異常問題
- **Commit ID**: `f3162541d2dd96211a2c2aebda869f680c74b6bd`
- **作者**: yamiyeh10
- **日期**: 2018-05-09 16:00:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 165. 修正APP小數點進位及位數顯示漏掉部分
- **Commit ID**: `9bc2a99f16c56564e974355e17e8c719e1c1c87f`
- **作者**: 治傑
- **日期**: 2018-05-09 15:51:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 166. 調整ESS表單退回時鎖定編輯
- **Commit ID**: `f4c93d62092efdcce29f66b3b1bfd356836b1548`
- **作者**: 治傑
- **日期**: 2018-05-09 14:57:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 167. 調整APP小數點進位及位數顯示功能
- **Commit ID**: `8b033cb469f23f89cc056ddebd308e5069d1563a`
- **作者**: 治傑
- **日期**: 2018-05-09 14:49:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormManager.js`

### 168. 修正使用Struts轉換JSON字串到頁面上會出現沒有跳脫字元的錯誤
- **Commit ID**: `a3d348f3e4c8d68c3e14d9e2861f393f6539a6b8`
- **作者**: jd
- **日期**: 2018-05-09 14:19:45
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`

### 169. Q00-20180503001 修正流程有自動簽核功能時簽核歷程異常問題
- **Commit ID**: `69faabf6636261fdb4eb4967c9fbce980b95b633`
- **作者**: yamiyeh10
- **日期**: 2018-05-09 10:09:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 170. NaNaEJB增加rmi.hostname設定
- **Commit ID**: `275c2e36ee4e5b3575b5fc6e63265a2de000e5e7`
- **作者**: lorenchang
- **日期**: 2018-05-09 09:19:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaEJB.properties`

### 171. 解決RMI在Windows下若開啟防火牆無法連線的問題
- **Commit ID**: `adf0f8ae6fea82dcd2be780d44dd9bd5db8cc92f`
- **作者**: lorenchang
- **日期**: 2018-05-09 09:16:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/RmiRegistry.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`

### 172. 修正小螢幕時簽核無簽核意見欄位問題
- **Commit ID**: `256ea0d1e7ac8c0d31a214cd8432a5f3a150edde`
- **作者**: jerry1218
- **日期**: 2018-05-08 18:08:32
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle.sql`

### 173. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `c2bf044cbd5005ab2de80664f09bc6c8f94eedde`
- **作者**: pinchi_lin
- **日期**: 2018-05-08 16:25:16
- **變更檔案數量**: 0

### 174. 新增簽核流設計師行動版元件欄位驗證
- **Commit ID**: `a294e15dbce715719303ac7bd1d99fc3fcc90494`
- **作者**: ChinRong
- **日期**: 2018-05-08 16:24:03
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormMobileValidateAccessCellEditorRenderer.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormMobileValidateTableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/FormInstanceForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 175. 調整APP驗證方法中使用alert的地方改成jDalert
- **Commit ID**: `202c4eddb69ef8f168b94960de523e3264ff14f7`
- **作者**: pinchi_lin
- **日期**: 2018-05-08 16:23:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js`

### 176. 調整ISOsession判斷是否為RWD表單邏輯
- **Commit ID**: `230fb01bdaaf594cc6352481b114c87482b2fa59`
- **作者**: 張詠威
- **日期**: 2018-05-08 14:33:47
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 177. 修正RWD表單自動轉換時不塞script給APP
- **Commit ID**: `cad46b375ffba7b3e3fafe19713ebabbf78a8b12`
- **作者**: pinchi_lin
- **日期**: 2018-05-08 10:42:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 178. 修正APP必填驗證漏掉部分
- **Commit ID**: `73af500ed84b908fc1acce7dbc972facbd76deaf`
- **作者**: pinchi_lin
- **日期**: 2018-05-08 10:07:44
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`

### 179. 新增APP必填欄位驗證功能
- **Commit ID**: `99ab587ba4bb49a1b1c19f990b621f7e97960754`
- **作者**: pinchi_lin
- **日期**: 2018-05-07 20:01:24
- **變更檔案數量**: 22
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/FormManager.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 180. 修正追蹤參數錯誤問題 修正發起漏掉的多語系參數 調整頁籤標題樣式
- **Commit ID**: `d707b0856ae209d0ab888375bdf4c087197c33fc`
- **作者**: yamiyeh10
- **日期**: 2018-05-07 18:31:13
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 181. 1.修改BPM首頁圖表套件  2.日期元件多語言
- **Commit ID**: `671fb056f41d473eba30a492d4a38c55c43a33c9`
- **作者**: jerry1218
- **日期**: 2018-05-07 17:31:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/echarts.min.js`

### 182. 修正查詢樣板dropDown預設值的判斷
- **Commit ID**: `b944da99c8eefef5ba7dc08c825895b49d267d57`
- **作者**: 張詠威
- **日期**: 2018-05-07 16:10:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 183. 修正若有SQLCommand時，切換到formscript頁面的錯誤
- **Commit ID**: `e19ef34a2b74352028c3e7aebe946931b8b62c74`
- **作者**: Gaspard
- **日期**: 2018-05-07 15:43:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 184. RWD表單設計器開發優化
- **Commit ID**: `d6cee9bfb322fd4e1de65eeabfeeebee71d2126c`
- **作者**: Gaspard
- **日期**: 2018-05-07 15:23:13
- **變更檔案數量**: 33
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/HorizontalLineElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ScriptEventTrackerMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-importer/src/com/dsc/nana/user_interface/apps/form_importer/TiptopFormImporter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopFormTransfer.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormTransfer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FormDefinitionBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`

### 185. 修正ISODocServer開窗資料無法帶回議題
- **Commit ID**: `ef78f38c2e400c18abebdeddbd32198c9baa181b`
- **作者**: H05003
- **日期**: 2018-05-07 14:23:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp`

### 186. 調整ISO獨立模組sessionBean
- **Commit ID**: `31b439476b916194041c696a526f6543bb293bf2`
- **作者**: H05003
- **日期**: 2018-05-04 18:08:59
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/QueueHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/QueueHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/QueueHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormInstanceTransferJson.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestFulHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 187. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a03ea5e63dca1075045c58c7ebf4760f6ef454b8`
- **作者**: jd
- **日期**: 2018-05-04 15:41:22
- **變更檔案數量**: 0

### 188. 修正行動表單水平線問題
- **Commit ID**: `2ce3957e20620d11bf1c8a8219362fd28a254289`
- **作者**: jd
- **日期**: 2018-05-04 15:38:05
- **變更檔案數量**: 0

### 189. 修正行動表單水平線問題
- **Commit ID**: `8db7a323ee21c601e5c88d1258733a983305aac0`
- **作者**: jd
- **日期**: 2018-05-04 15:38:04
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HorizontalLineElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`

### 190. 修正Grid內設定動態隱藏與顯示元件異常問題
- **Commit ID**: `d0e0d71627043d798485ffdf2b74a0236d9683ae`
- **作者**: yamiyeh10
- **日期**: 2018-05-04 13:09:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 191. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `78097a449481e247d5bf8b6b7f9080ea8ad494c9`
- **作者**: jd
- **日期**: 2018-05-04 10:03:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 192. 修正客製開窗無法使用問題,多傳了false參數
- **Commit ID**: `a47938161242054203176c7e066b611e008dee4d`
- **作者**: yamiyeh10
- **日期**: 2018-05-04 09:29:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 193. 修正頁籤元件內文異常
- **Commit ID**: `6759a0d74268000bd80375cf72ea4a48107ea636`
- **作者**: yamiyeh10
- **日期**: 2018-05-04 08:46:13
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SubTabElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 194. 修正BPMAPP多欄位異常問題
- **Commit ID**: `544f764325773f4b6c9c193bb3241e947f802620`
- **作者**: pinchi_lin
- **日期**: 2018-05-03 20:00:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 195. 修正開窗樣板多div問題
- **Commit ID**: `14e29bf7957d18a7b2cc955046fda7d0f85722a7`
- **作者**: pinchi_lin
- **日期**: 2018-05-03 17:46:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java`

### 196. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `7aa4450762d8a0527d1135ec092a2efe6c9ecc19`
- **作者**: jd
- **日期**: 2018-05-03 16:55:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`

### 197. 修正Title元件生成失敗問題
- **Commit ID**: `78fefc03c376efbae1de2240fe69cb752686b173`
- **作者**: jd
- **日期**: 2018-05-03 16:55:12
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 198. 統一表單相關畫面不使用較好看的ScrollBar
- **Commit ID**: `a798578157ba9b46921e21e2b3ab6162e4dfb1f5`
- **作者**: Gaspard
- **日期**: 2018-05-03 16:42:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`

### 199. C01-20180426001 修正鼎捷移動中間層點擊同意或不同意後提交失敗問題
- **Commit ID**: `b3b00c9565f5f0e3ee7bcde988a64960fa27e46e`
- **作者**: pinchi_lin
- **日期**: 2018-05-03 11:53:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 200. Dashboard BPM首頁畫面微調
- **Commit ID**: `2ffca1a7a30542aae03beba529f77f67cb98782b`
- **作者**: jerry1218
- **日期**: 2018-05-03 11:44:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 201. 新增主頁面右上待辦項目沒有主旨時的預設文字
- **Commit ID**: `12965dc525fd766d9aa0431e35605c30d1042ce4`
- **作者**: jerry1218
- **日期**: 2018-05-03 11:04:27
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AbstractAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ToDoListVo.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`

### 202. 修正BPMAPP表單多欄位異常問題
- **Commit ID**: `00e8ce92ccd567733b891f738b0a8e183af5a8b6`
- **作者**: pinchi_lin
- **日期**: 2018-05-02 19:42:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 203. Q00-20180502001 修正T100主機異常時,上傳簽名圖檔會造成的畫面錯誤
- **Commit ID**: `33f27629fe9da4c083dd47e269a3de397c7028f1`
- **作者**: jerry1218
- **日期**: 2018-05-02 17:47:18
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5721.xls`

### 204. 修改列印表單時背景色統一為白色
- **Commit ID**: `642f5c186eb9d09030e87688d707296b66134b52`
- **作者**: Gaspard
- **日期**: 2018-05-02 17:27:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 205. 修改追蹤流程時Title元件無法顯示的議題
- **Commit ID**: `65c5918c53a9adcd1bcc8506b692110dcbe8e4e0`
- **作者**: Gaspard
- **日期**: 2018-05-02 17:24:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElement.java`

### 206. 表單相關頁面統一不使用較好看的ScrollBar
- **Commit ID**: `412750f0ca1fa19db8c9c146b62288ceab0bd39c`
- **作者**: Gaspard
- **日期**: 2018-05-02 17:18:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 207. 微調Dialog元件的按鈕的間距
- **Commit ID**: `94db08919e24f39480a2a1c51813e44093230f95`
- **作者**: Gaspard
- **日期**: 2018-05-02 17:17:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 208. 將ECP的Mail實作放入BPM
- **Commit ID**: `9667d2dab37ed0f7e74e2353833eccdb4cd7e5f5`
- **作者**: lorenchang
- **日期**: 2018-05-02 16:43:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/JakartaCommons/commons-httpclient.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/ext/ECPMailUtil.java`

### 209. 1.修正發起流程的流程內容沒有[檢視流程詳細資訊]按鈕 2.畫面微調
- **Commit ID**: `603223a936728d99a730ed34966eff4a62254946`
- **作者**: jerry1218
- **日期**: 2018-05-02 16:18:21
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 210. 表單相關畫面一律暫不使用樣式較好看的scrollbar
- **Commit ID**: `4c1a6f1d563d52f5c1cab95ffc3d1facee3a5345`
- **作者**: Gaspard
- **日期**: 2018-05-02 15:51:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 211. 修正外部連結進入表單畫面時的異常
- **Commit ID**: `5af8676876eb1dae48c53563f72f5d4a15998e52`
- **作者**: Gaspard
- **日期**: 2018-05-02 13:39:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`

### 212. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `f768279f66db039b953a5d7edd816e6fcab6364f`
- **作者**: jd
- **日期**: 2018-05-02 11:00:44
- **變更檔案數量**: 0

### 213. 修正統計圖表需要有歸戶使用者資料才能返回資料的狀況
- **Commit ID**: `87633d56a7cfafe92119a3c8e563f57b3d48f54a`
- **作者**: jd
- **日期**: 2018-05-02 10:54:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 214. 在新增表單及新增流程的ID長度卡控，限制字元長度為40。
- **Commit ID**: `5e08acc2877b4730f5780cd92492881bebe0d7de`
- **作者**: 施廷緯
- **日期**: 2018-04-30 18:01:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/IDValidator.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 215. 調整客製多選開窗流水號
- **Commit ID**: `9dbe38fc7e3619d89ad57789a023f5dbc4feda59`
- **作者**: 治傑
- **日期**: 2018-04-30 17:00:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 216. 修正查詢維護樣版 1.匯出EXCEL的圖片路徑 2.少載入ModalDialog.js 3.移除工具列背景色
- **Commit ID**: `8f31497d5e2dd54d3d28a83430bca0001fdd4d6f`
- **作者**: Gaspard
- **日期**: 2018-04-30 15:55:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/QueryDesinger.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 217. 修正NaNaWeb.war\WEB-INF\lib內重覆的serializer.jar導致解壓時需要確認是否取代檔案
- **Commit ID**: `b0f6f8a31d92b9f84eb6054900b237d112ed2acc`
- **作者**: lorenchang
- **日期**: 2018-04-30 14:02:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/lib/Xerces/serializer.jar`

### 218. 版本資訊Created-By、Specification-Version及Built-By加上預設值
- **Commit ID**: `62bdaf1c3008063b828fa9c02bcf0bcaaae9569c`
- **作者**: lorenchang
- **日期**: 2018-04-30 13:43:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/build.xml`

### 219. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `c6c93eb07db07db2b6e73999ed72a2c7a62f7164`
- **作者**: jd
- **日期**: 2018-04-30 09:31:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`

### 220. 修正Title的文字沒顯示問題
- **Commit ID**: `ede3ead8d92589df331f78f8f7747f4a9fa728aa`
- **作者**: jd
- **日期**: 2018-04-27 18:22:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerLabel.java`

