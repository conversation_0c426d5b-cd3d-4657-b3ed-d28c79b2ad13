# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.3_20240104
- **舊版本**: release_5.8.9.3
- **生成時間**: 2025-07-18 10:48:33
- **新增 Commit 數量**: 156

## 變更摘要

### liuyun (21 commits)

- **2024-01-03 17:06:02**: [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正-2)
  - 變更檔案: 1 個
- **2024-01-02 15:14:28**: [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正)
  - 變更檔案: 1 個
- **2023-12-29 10:57:45**: [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常
  - 變更檔案: 1 個
- **2023-12-13 13:22:56**: [Web] Q00-20231213002 不同模组下作业名称相同，导航页显示异常
  - 變更檔案: 2 個
- **2023-12-15 14:46:41**: [Web] Q00-20231215003 由url链接进入待办，清除wms_user_isURL的session
  - 變更檔案: 1 個
- **2023-11-29 16:44:03**: [Web] Q00-20231129005 修正serialNumber栏位显示问题
  - 變更檔案: 1 個
- **2023-11-24 10:33:10**: [Web] Q00-20231124002 修正流程主旨範本設定<#workItemName>显示N.A.
  - 變更檔案: 3 個
- **2023-11-20 11:01:06**: [Web] Q00-20231120001 修正BPM注册序号中 BPM流程引擎有过期序号导致其他功能无法正常使用
  - 變更檔案: 1 個
- **2023-11-17 13:48:06**: [Web] Q00-20231117002 修正 首次登录跳转到变更密码页面显示异常
  - 變更檔案: 1 個
- **2023-11-02 13:12:36**: [T100] Q00-20231102002 T100的签核历程沒有权限的显示提示告知登入者
  - 變更檔案: 3 個
- **2023-10-31 13:39:56**: [Web] Q00-20231031001 修正缩小ESSPlus管理页面时，查询出来的结果在grid显示不全
  - 變更檔案: 1 個
- **2023-10-23 09:15:57**: [Web]Q00-20231023001 修正列印预览grid标题字体颜色为白色
  - 變更檔案: 1 個
- **2023-10-26 14:29:58**: [Web]Q00-20231026001 修正转由他人处理 > 经常选取对象 无资料时显示错误页面
  - 變更檔案: 1 個
- **2023-09-22 11:21:43**: [Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效
  - 變更檔案: 1 個
- **2023-09-22 10:27:58**: [Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错
  - 變更檔案: 1 個
- **2023-09-11 16:08:36**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】
  - 變更檔案: 2 個
- **2023-09-07 17:54:10**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】
  - 變更檔案: 4 個
- **2023-08-25 15:40:09**: [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面
  - 變更檔案: 3 個
- **2023-09-15 11:09:32**: [Web] Q00-20230915001 修正 SQL注册器点击资料返回到新增页面，数据错误带回显示
  - 變更檔案: 1 個
- **2023-09-11 11:47:04**: [Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误
  - 變更檔案: 1 個
- **2023-09-06 11:11:10**: [Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白
  - 變更檔案: 1 個

### pinchi_lin (16 commits)

- **2023-12-28 18:09:52**: [ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題
  - 變更檔案: 1 個
- **2023-12-28 18:09:52**: [ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題
  - 變更檔案: 1 個
- **2023-12-14 18:37:17**: [PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題
  - 變更檔案: 1 個
- **2023-12-12 15:11:46**: [PRODT]Q00-20231212002 修正Web流程管理工具中流程徹銷中的sessionBean點編輯呈現空白的問題
  - 變更檔案: 1 個
- **2023-12-04 18:52:09**: [PRODT]Q00-20231204006 修正Web流程管理工具中應用程式管理員的網頁應用程式或session bean新增呼叫參數時自訂id儲存後會變預設值的問題
  - 變更檔案: 3 個
- **2023-11-22 20:01:03**: [DT]Q00-20231122002 修正系統權限管理員中可存取的範圍設定權限範圍(部門/專案主管)後儲存會報後端接口調用失敗的問題
  - 變更檔案: 1 個
- **2023-11-21 16:14:16**: [DT]Q00-20231121006 修正在Web化流程管理工具中活動參與者組織相關找不到離職日設定當天的使用者的問題
  - 變更檔案: 1 個
- **2023-11-01 15:38:07**: [DT]Q00-20231101004 修正Web化流程管理工具中服務任務的應用程式加入表單型態參數後會有無法簽入的問題
  - 變更檔案: 1 個
- **2023-09-28 17:56:28**: [DT]Q00-20230928003 修正從Web化流程管理工具入版後的流程圖在Swing中開啟時流程圖會被截斷問題
  - 變更檔案: 1 個
- **2023-09-26 17:58:38**: [DT]Q00-20230926002 修正Web流程管理工具儲存流程因找不到ActivityType導致儲存失敗問題
  - 變更檔案: 1 個
- **2023-09-26 17:48:09**: [DT]Q00-20230926003 修正Web流程管理工具的流程圖進版後有條件的連接線顏色變黑色問題
  - 變更檔案: 1 個
- **2023-09-26 17:44:07**: [DT]Q00-20230926004 修正Web流程管理工具的流程圖進版後連接線上的名稱消失問題
  - 變更檔案: 1 個
- **2023-09-21 16:13:03**: [DT]Q00-20230921001 修正Web流程管理工具中參與者選職務或職稱後儲存再開啟其值會消失的問題
  - 變更檔案: 1 個
- **2023-09-18 14:40:32**: [DT]Q00-20230918002 修正Web流程管理工具中連接線編輯後儲存流程再開啟會變成藍色的問題
  - 變更檔案: 1 個
- **2023-09-01 19:47:59**: [DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題
  - 變更檔案: 1 個
- **2023-08-28 11:15:49**: [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
  - 變更檔案: 1 個

### kmin (3 commits)

- **2024-01-03 16:24:38**: Revert "[ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題"
  - 變更檔案: 1 個
- **2023-12-05 17:35:05**: Revert "[Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項"
  - 變更檔案: 1 個
- **2023-08-31 14:47:24**: 打包用
  - 變更檔案: 2 個

### 邱郁晏 (27 commits)

- **2024-01-02 17:05:30**: [Web] Q00-20240102006 調整部分系統變數無須重啟即生效
  - 變更檔案: 11 個
- **2023-12-18 11:30:10**: [Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題(補)
  - 變更檔案: 2 個
- **2023-12-15 10:12:50**: [Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題(補)
  - 變更檔案: 1 個
- **2023-12-15 10:08:52**: [Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題
  - 變更檔案: 1 個
- **2023-12-13 13:59:44**: [Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題
  - 變更檔案: 1 個
- **2023-12-04 17:13:28**: [Web] Q00-20231204005 修正BPM授權數不足時，寄件人並非系統管理中的設定
  - 變更檔案: 1 個
- **2023-12-04 14:10:17**: [流程引擎] Q00-20231204002 修正退回重辦系統通知變數未被正常置換問題
  - 變更檔案: 1 個
- **2023-11-24 15:00:42**: [流程引擎] Q00-*********** 修正批次簽核造成重複寄信問題
  - 變更檔案: 1 個
- **2023-11-14 15:54:22**: [Web] Q00-20231114003 修正寄件人重複顯示問題
  - 變更檔案: 1 個
- **2023-11-06 17:47:59**: [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)
  - 變更檔案: 2 個
- **2023-11-03 11:12:20**: [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)
  - 變更檔案: 1 個
- **2023-10-25 18:14:01**: [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補修正)
  - 變更檔案: 1 個
- **2023-10-25 16:35:30**: [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題
  - 變更檔案: 1 個
- **2023-11-01 15:50:29**: [Web] Q00-20231101005 修正表單欄位為invisible且設定顯示千分位，開啟追蹤流程在F12顯示錯誤
  - 變更檔案: 1 個
- **2023-10-17 14:56:38**: [流程引擎] Q00-20231017004 修正工作受託者<#allAssigneesIDnName>沒有帶出資料的問題
  - 變更檔案: 1 個
- **2023-10-04 14:56:04**: [T100] Q00-20231004004 修正手寫元件造成拋單失敗，新增判斷略過手寫元件
  - 變更檔案: 1 個
- **2023-10-11 14:21:33**: [Web] V00-20231011001 修正轉存表單日期格式異常問題，支持常用格式。
  - 變更檔案: 1 個
- **2023-10-04 12:04:19**: [Web] V00-20231004001 修正匯出表單，日期格式(yy/M/d)被判斷為異常格式問題。
  - 變更檔案: 1 個
- **2023-09-21 17:05:57**: [組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號(補修正)
  - 變更檔案: 1 個
- **2023-09-20 13:44:52**: [組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號。
  - 變更檔案: 1 個
- **2023-09-12 10:23:55**: [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)
  - 變更檔案: 1 個
- **2023-09-12 10:06:27**: [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法
  - 變更檔案: 1 個
- **2023-09-12 17:23:00**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
  - 變更檔案: 1 個
- **2023-09-04 10:53:42**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
  - 變更檔案: 1 個
- **2023-08-31 15:41:41**: [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題
  - 變更檔案: 1 個
- **2023-09-06 12:03:56**: [Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。
  - 變更檔案: 1 個
- **2023-08-24 13:43:54**: [Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。
  - 變更檔案: 1 個

### 周权 (32 commits)

- **2023-12-29 13:58:54**: [Web]Q00-20231229003 调整"追蹤"“監控”使用表单自适应宽度調整書面寬度無效果的問題
  - 變更檔案: 2 個
- **2023-12-29 13:33:58**: [Web]Q00-20231229002 调整个人资讯-->表单自适应宽度slider预设值为“较宽”
  - 變更檔案: 1 個
- **2023-12-21 13:28:33**: [Web]Q00-20231215004 建立登入or登出記錄物件資料request为空时新增防呆[补修正]
  - 變更檔案: 2 個
- **2023-12-15 17:52:46**: [Web]Q00-20231215004 建立登入or登出記錄物件資料request为空时新增防呆
  - 變更檔案: 1 個
- **2023-12-05 14:47:17**: [Web]Q00-20231205004 修正待办事项中选择锁定工具列后缩小视窗表单会被部分遮挡的问题
  - 變更檔案: 1 個
- **2023-12-04 15:48:40**: [Web]Q00-20231204003 修正流程主旨、列印模式下grid资料有&#加任意數字，被轉成特殊符號的问题
  - 變更檔案: 3 個
- **2023-12-01 10:59:07**: [Web]Q00-20231201004 调整ipad Safari浏览器經常選取人員为默认全选
  - 變更檔案: 1 個
- **2023-11-29 16:55:32**: [Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题[补修正]
  - 變更檔案: 1 個
- **2023-11-29 14:16:33**: [Web]Q00-20231128006 调整grid標頭固定显示[补修正]
  - 變更檔案: 1 個
- **2023-11-29 12:17:02**: [Web]Q00-20231129002 调整個人資訊页多个提示訊息显示不完整的问题
  - 變更檔案: 4 個
- **2023-11-29 11:43:19**: [Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题
  - 變更檔案: 1 個
- **2023-11-28 16:17:09**: [Web]Q00-20231128006 调整grid標頭固定显示
  - 變更檔案: 1 個
- **2023-11-24 10:30:47**: [Web]Q00-20231124003 修正Grid某一格或某一行设置样式，点击排序后样式消失的问题
  - 變更檔案: 1 個
- **2023-11-17 13:42:42**: [Web] Q00-20231117001 调整簡易流程圖跳過時_輸入密碼後alert的訊息框太小的问题
  - 變更檔案: 1 個
- **2023-11-08 13:21:40**: [WEB] Q00-20231108002 修正待辦事項中表單serialNumber元件欄位資料顯示位置異常的問題
  - 變更檔案: 1 個
- **2023-11-06 11:11:50**: [WEB]Q00-20231106001 调整Select元件在设置背景色时列印却显示唯读背景色的问题
  - 變更檔案: 1 個
- **2023-10-25 11:01:49**: [Web] Q00-20231025001 调整隐藏栏位为單身欄位加總的运算操作验证
  - 變更檔案: 1 個
- **2023-09-28 17:59:19**: [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]
  - 變更檔案: 1 個
- **2023-10-30 15:54:31**: [Web] Q00-20231030004 调整预览列印会带出表單名稱與表單代號Tab的问题
  - 變更檔案: 1 個
- **2023-10-18 11:13:17**: [WEB]Q00-20231018001 调整Select元件有textbox輸入格时的点击事件逻辑
  - 變更檔案: 1 個
- **2023-09-26 14:32:49**: [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]
  - 變更檔案: 2 個
- **2023-09-25 17:24:13**: [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。
  - 變更檔案: 2 個
- **2023-08-24 13:39:10**: [Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题[补修正]
  - 變更檔案: 1 個
- **2023-08-24 09:32:17**: [Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题
  - 變更檔案: 1 個
- **2023-09-22 14:57:46**: [Web] Q00-20230922004 在没有添加grid按钮却使用grid方法时，新增防呆，防止focus报错
  - 變更檔案: 1 個
- **2023-09-14 16:07:26**: [Web]Q00-20230914001 修正RadioButton，checkbox元件帶入值到grid时报错，新增防呆。
  - 變更檔案: 1 個
- **2023-09-12 15:29:48**: [Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。
  - 變更檔案: 3 個
- **2023-09-11 15:39:19**: [Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。
  - 變更檔案: 1 個
- **2023-09-08 10:14:12**: [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]
  - 變更檔案: 2 個
- **2023-09-07 11:31:32**: [Web]Q00-20230907001 读取自定义background,Banner,logo图片时，新增图片格式防呆。
  - 變更檔案: 1 個
- **2023-09-06 13:19:12**: [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。
  - 變更檔案: 1 個
- **2023-09-05 18:01:25**: [Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。
  - 變更檔案: 1 個

### 刘旭 (9 commits)

- **2023-12-06 13:18:15**: [web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正[补修正]
  - 變更檔案: 1 個
- **2023-12-20 11:26:28**: [web] Q00-20231220003 當user使用Android手機、並有調整「字型大小」時，登入網頁的綁定畫面-QRCode會跑版問題修正
  - 變更檔案: 1 個
- **2023-12-05 13:49:48**: [web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正
  - 變更檔案: 1 個
- **2023-11-29 15:53:31**: [Web] Q00-20231129004 修正從追蹤連結進入已關注的愛心不會亮，該流程原本已被關注，但從追蹤連結進入後不會亮，須等切換到表單葉面後才會亮
  - 變更檔案: 1 個
- **2023-11-24 10:24:42**: [Web] Q00-20231124001 修正附件元件每個檔案容量限制設定成104857600 kb,無法上傳附件
  - 變更檔案: 2 個
- **2023-09-27 17:19:26**: [web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复
  - 變更檔案: 1 個
- **2023-09-18 09:24:41**: [web]Q00-20230913001 沒有未閱讀的工作通知，但右上角鈴鐺還是一直有紅點點问题修复
  - 變更檔案: 2 個
- **2023-08-29 14:02:09**: [web]Q00-20230829003 列印時附件資訊會超出邊界问题修复
  - 變更檔案: 1 個
- **2023-08-25 17:38:15**: [web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复
  - 變更檔案: 1 個

### cherryliao (8 commits)

- **2023-12-20 10:17:30**: [BPM APP]Q00-20231220001 修正LINE綁定LDAP帳號時會出現使用者帳號不存在的問題
  - 變更檔案: 2 個
- **2023-12-19 10:35:08**: [Web]Q00-20231219001 調整系統設定LDAP登入時，登入畫面帳號欄位提示訊息的多語系問題
  - 變更檔案: 2 個
- **2023-11-29 09:48:32**: [Web]Q00-20231129001 調整在行動裝置撤銷流程時填寫撤銷意見會被選單擋住的問題
  - 變更檔案: 3 個
- **2023-11-28 10:02:48**: [PRODT]Q00-20231128001 修正在開啟流程管理工具後主畫面的標題一併被更動問題
  - 變更檔案: 1 個
- **2023-10-04 10:09:25**: [Web]Q00-20231004001 修正TextBox設定數字轉繁體文字在列印表單時顯示簡體文字的問題
  - 變更檔案: 1 個
- **2023-09-15 10:22:25**: [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]
  - 變更檔案: 1 個
- **2023-09-08 16:39:12**: [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題
  - 變更檔案: 1 個
- **2023-08-30 11:47:29**: [Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題
  - 變更檔案: 2 個

### 林致帆 (23 commits)

- **2023-12-06 11:13:41**: [雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證[補修正]
  - 變更檔案: 1 個
- **2023-12-05 15:26:32**: [Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項
  - 變更檔案: 1 個
- **2023-09-07 14:14:10**: [流程引擎]S00-20230130001 調整信件區分流程轉派給代理人
  - 變更檔案: 2 個
- **2023-12-05 15:26:32**: [Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項
  - 變更檔案: 1 個
- **2023-11-28 13:35:41**: [ESS]Q00-20231128003 調整缺席紀錄方法相容帶有單身資料的ESS單據
  - 變更檔案: 1 個
- **2023-11-27 16:31:42**: [Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊
  - 變更檔案: 1 個
- **2023-11-24 10:45:57**: [雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證
  - 變更檔案: 6 個
- **2023-11-10 17:14:59**: [雙因素模組]Q00-*********** 修正未放入不驗證清單的使用者若未綁定，登入後不會跳出需綁定的提示視窗
  - 變更檔案: 2 個
- **2023-11-01 14:10:52**: [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯[補修正]
  - 變更檔案: 1 個
- **2023-11-01 14:04:59**: [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯
  - 變更檔案: 5 個
- **2023-10-05 14:23:44**: [Web]S00-20220929001 新增BPM外部連結-進入BPM首頁
  - 變更檔案: 2 個
- **2023-09-19 12:02:02**: [Web]Q00-20230919001 調整URL從/NaNaWeb/Login.jsp?type=admin登入時會報閒置過久的訊息改成"請輸入正確的代號或密碼!"
  - 變更檔案: 1 個
- **2023-10-26 16:16:43**: [資安] Q00-20231017003 修改因paloalto內部防火牆把aes.js當成spyware Malicious JavaScript Files Detection攻擊，將aes.js進行加密編碼。
  - 變更檔案: 1 個
- **2023-10-12 15:30:00**: [表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗
  - 變更檔案: 1 個
- **2023-09-05 10:16:20**: [T100]S00-20220513001 T100取簽核歷程新增是否為代理人標籤
  - 變更檔案: 2 個
- **2023-09-22 10:42:52**: [Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加
  - 變更檔案: 1 個
- **2023-09-19 13:42:07**: [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"[補修正]
  - 變更檔案: 2 個
- **2023-09-18 14:02:51**: [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"
  - 變更檔案: 2 個
- **2023-09-08 09:24:46**: [SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗
  - 變更檔案: 1 個
- **2023-08-31 09:16:24**: [Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件
  - 變更檔案: 3 個
- **2023-08-30 10:43:55**: [TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷
  - 變更檔案: 1 個
- **2023-08-29 15:50:48**: [ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗
  - 變更檔案: 2 個
- **2023-08-28 16:57:53**: [SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤
  - 變更檔案: 1 個

### waynechang (14 commits)

- **2023-12-13 15:44:25**: [SAP]Q00-20231213003 修正SAP整合服務，當回傳的資料類型為絕對位置表單Grid時，可能會有GridColumnId與GridValue順序錯誤的異常
  - 變更檔案: 1 個
- **2023-11-27 15:20:06**: [流程引擎]Q00-20231127001 修正關卡設定多人都要簽核且設定自動簽核2，與前一關相同者，若前一關為核決關卡，且未實際展開核決關卡時，流程無法派送至下一關的異常
  - 變更檔案: 1 個
- **2023-11-24 17:33:54**: [SAP]Q00-*********** 優化SAP整合服務，當整合的資料類型為Grid時，同時支持RWD表單Grid及絕對位置表單Grid
  - 變更檔案: 1 個
- **2023-11-21 11:55:25**: [SAP]Q00-20231121001 修正SAP整合，當mapping內容有Grid時，可能會有GridColumnId與GridValue順序錯誤的異常
  - 變更檔案: 1 個
- **2023-11-20 17:25:48**: [流程引擎]Q00-20231120003 修正監控流程的詳細流程圖頁面，當按下「跳過此關卡」功能時，下一關處理者無法收到代辦通知信件
  - 變更檔案: 1 個
- **2023-10-30 14:41:37**: [流程引擎]Q00-20231030001 修正流程發起者於追蹤流程頁面進入表單畫面點擊撤銷流程後，詳細流程圖的流程詳細資訊應為是「流程發起者」而非「流程負責人」撤銷
  - 變更檔案: 1 個
- **2023-10-27 14:39:03**: [流程引擎]Q00-20231027003 修正流程結案清除附件的服務，當表單附件OID屬性為空時，會被系統移除附件，此調整為避免移除表單實例的附件的OID屬性為空的附件
  - 變更檔案: 1 個
- **2023-10-23 11:10:11**: [Web]Q00-20231023002 修正流程第一關有設定必須上傳新附件，若從流程草稿開啟時，系統沒有卡控必須上傳新附件
  - 變更檔案: 1 個
- **2023-10-03 17:39:22**: [SAP]Q00-20231003003 修正SAP整合作業-SAP欄位對應設定，新增整合設定頁面當選擇完表單後，無法載入表單元件
  - 變更檔案: 2 個
- **2023-09-26 16:01:14**: [在線閱覽]Q00-20230926001 修正在線閱讀檔案設定不可下載，在待辦表單頁面點擊閱讀檔案的頁面，仍可以下載PDF閱讀檔的異常
  - 變更檔案: 1 個
- **2023-09-07 10:31:49**: [流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常
  - 變更檔案: 1 個
- **2023-08-29 16:50:50**: [流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常
  - 變更檔案: 1 個
- **2023-08-29 10:32:40**: [流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據
  - 變更檔案: 2 個
- **2023-08-23 15:27:37**: [Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常
  - 變更檔案: 3 個

### yamiyeh10 (3 commits)

- **2023-10-04 14:11:21**: [WEB]Q00-20231004003 修正在手機瀏覽器以及RWD窄畫面上沒有附件檔名的URL連結導致無法下載問題
  - 變更檔案: 2 個
- **2023-09-20 16:38:36**: [DT]Q00-20230920002 修正Web系統權限管理員上儲存按鈕後畫面會一直loading的問題
  - 變更檔案: 1 個
- **2023-09-20 12:01:52**: [DT]A00-20230919001 修正Web流程管理工具中事件處理在流程儲存後會消失問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正-2)
- **Commit ID**: `27bfa164dee99a760f6d412cfc832d06b62ac97a`
- **作者**: liuyun
- **日期**: 2024-01-03 17:06:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 2. [ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題
- **Commit ID**: `54fb1304e108444b7418a82d2ab4a9930ce32f74`
- **作者**: pinchi_lin
- **日期**: 2023-12-28 18:09:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 3. Revert "[ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題"
- **Commit ID**: `c55081324ab64c6b016a2666a89367fc5f727218`
- **作者**: kmin
- **日期**: 2024-01-03 16:24:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 4. [ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題
- **Commit ID**: `09fe532b4d8ac594f05548c1b49775bf1c647d46`
- **作者**: pinchi_lin
- **日期**: 2023-12-28 18:09:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 5. [Web] Q00-20240102006 調整部分系統變數無須重啟即生效
- **Commit ID**: `147fb084991254b73af63c9437e441bd665e4cee`
- **作者**: 邱郁晏
- **日期**: 2024-01-02 17:05:30
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 6. [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正)
- **Commit ID**: `06c0f3fabdff698df4f279fb2a883054eccc4948`
- **作者**: liuyun
- **日期**: 2024-01-02 15:14:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 7. [Web]Q00-20231229003 调整"追蹤"“監控”使用表单自适应宽度調整書面寬度無效果的問題
- **Commit ID**: `e956ea4436d9476ad8d88799f2b5e81805d7f8fb`
- **作者**: 周权
- **日期**: 2023-12-29 13:58:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 8. [Web]Q00-20231229002 调整个人资讯-->表单自适应宽度slider预设值为“较宽”
- **Commit ID**: `e204ad13d4ff42c9e5ba5a308e8dcbdce0091211`
- **作者**: 周权
- **日期**: 2023-12-29 13:33:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 9. [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常
- **Commit ID**: `309cca01cf072921ce8b2d6ae18a24a3e47600c0`
- **作者**: liuyun
- **日期**: 2023-12-29 10:57:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 10. [web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正[补修正]
- **Commit ID**: `bacaca6950e2a705d9b384ac29b320ed47f3bd06`
- **作者**: 刘旭
- **日期**: 2023-12-06 13:18:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 11. [Web] Q00-20231213002 不同模组下作业名称相同，导航页显示异常
- **Commit ID**: `a4ef440e5b32b3fe6bce9b9039a9e0159b6cbc91`
- **作者**: liuyun
- **日期**: 2023-12-13 13:22:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 12. [Web]Q00-20231215004 建立登入or登出記錄物件資料request为空时新增防呆[补修正]
- **Commit ID**: `8f278c9bcb6fe64c31140dcaa949cc3ef1f62a42`
- **作者**: 周权
- **日期**: 2023-12-21 13:28:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 13. [web] Q00-20231220003 當user使用Android手機、並有調整「字型大小」時，登入網頁的綁定畫面-QRCode會跑版問題修正
- **Commit ID**: `82a5c41ee5d64ca323881f1af709ac0d9426255a`
- **作者**: 刘旭
- **日期**: 2023-12-20 11:26:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 14. [BPM APP]Q00-20231220001 修正LINE綁定LDAP帳號時會出現使用者帳號不存在的問題
- **Commit ID**: `bcd84d623b7bd36fb05329e3905a107f6d183bbd`
- **作者**: cherryliao
- **日期**: 2023-12-20 10:17:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`

### 15. [Web]Q00-20231219001 調整系統設定LDAP登入時，登入畫面帳號欄位提示訊息的多語系問題
- **Commit ID**: `35e299bac3759524b72474206751af62b7c46e67`
- **作者**: cherryliao
- **日期**: 2023-12-19 10:35:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 16. [Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題(補)
- **Commit ID**: `0a699fd913c0c8bba57fd054e0a87ee1379194f0`
- **作者**: 邱郁晏
- **日期**: 2023-12-18 11:30:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`

### 17. [Web]Q00-20231215004 建立登入or登出記錄物件資料request为空时新增防呆
- **Commit ID**: `b89e232e0238f5ef9d14dc4f6de24a59002a4813`
- **作者**: 周权
- **日期**: 2023-12-15 17:52:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 18. [Web] Q00-20231215003 由url链接进入待办，清除wms_user_isURL的session
- **Commit ID**: `5a4c53178fdce80321ba6115224bbcde18764ae1`
- **作者**: liuyun
- **日期**: 2023-12-15 14:46:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 19. [雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證[補修正]
- **Commit ID**: `cff4e1ac869538af1b79b5fc61c3a368439d5644`
- **作者**: 林致帆
- **日期**: 2023-12-06 11:13:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 20. [Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題(補)
- **Commit ID**: `e7b11b6ea4d58b656e230f37d798e9ccda9f8f14`
- **作者**: 邱郁晏
- **日期**: 2023-12-15 10:12:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 21. [Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題
- **Commit ID**: `376865ad374844b78d2a69cfcbdba5e9097e0392`
- **作者**: 邱郁晏
- **日期**: 2023-12-15 10:08:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`

### 22. [PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題
- **Commit ID**: `a0a6b8c67187ac682ee9ac19c3ede5cd9ce2f41b`
- **作者**: pinchi_lin
- **日期**: 2023-12-14 18:37:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 23. [SAP]Q00-20231213003 修正SAP整合服務，當回傳的資料類型為絕對位置表單Grid時，可能會有GridColumnId與GridValue順序錯誤的異常
- **Commit ID**: `79a5417d59f0a0303723514d271c864a7bf4e8c5`
- **作者**: waynechang
- **日期**: 2023-12-13 15:44:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 24. [Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題
- **Commit ID**: `4f85e66194359769e03d0862aaaab8d647de7ed1`
- **作者**: 邱郁晏
- **日期**: 2023-12-13 13:59:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 25. [PRODT]Q00-20231212002 修正Web流程管理工具中流程徹銷中的sessionBean點編輯呈現空白的問題
- **Commit ID**: `49b50db99dc42f270482c85565a45aef5077c610`
- **作者**: pinchi_lin
- **日期**: 2023-12-12 15:11:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 26. [Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項
- **Commit ID**: `4ef716bcec5f10b7c656b2b8e7f71ffc637a3839`
- **作者**: 林致帆
- **日期**: 2023-12-05 15:26:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 27. [流程引擎]S00-20230130001 調整信件區分流程轉派給代理人
- **Commit ID**: `de7205492e207112225c537e7fafb1fe72197121`
- **作者**: 林致帆
- **日期**: 2023-09-07 14:14:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 28. Revert "[Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項"
- **Commit ID**: `ad3e6408877610e824f35499fa10871da59bee08`
- **作者**: kmin
- **日期**: 2023-12-05 17:35:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 29. [Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項
- **Commit ID**: `bd232702777d67ef7d89e247798a4d0ae9190aaa`
- **作者**: 林致帆
- **日期**: 2023-12-05 15:26:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 30. [Web]Q00-20231205004 修正待办事项中选择锁定工具列后缩小视窗表单会被部分遮挡的问题
- **Commit ID**: `0f02d54ef6a29a84725e4d1ed15f086c79d4112a`
- **作者**: 周权
- **日期**: 2023-12-05 14:47:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 31. [web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正
- **Commit ID**: `a2eaec4c2737f50c2c92c8ee579e2bfcd278e68c`
- **作者**: 刘旭
- **日期**: 2023-12-05 13:49:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 32. [PRODT]Q00-20231204006 修正Web流程管理工具中應用程式管理員的網頁應用程式或session bean新增呼叫參數時自訂id儲存後會變預設值的問題
- **Commit ID**: `1902f7615a9f5a53235ed484d0552b445776821e`
- **作者**: pinchi_lin
- **日期**: 2023-12-04 18:52:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/IDGen.java`

### 33. [Web] Q00-20231204005 修正BPM授權數不足時，寄件人並非系統管理中的設定
- **Commit ID**: `cd5e88abebf51acb4d4e4d1b794e7256e020f79a`
- **作者**: 邱郁晏
- **日期**: 2023-12-04 17:13:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 34. [Web]Q00-20231204003 修正流程主旨、列印模式下grid资料有&#加任意數字，被轉成特殊符號的问题
- **Commit ID**: `22e965b2b97fca3ea9162a8c59ab451d5a7acf1f`
- **作者**: 周权
- **日期**: 2023-12-04 15:48:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/StringUtil.js`

### 35. [流程引擎] Q00-20231204002 修正退回重辦系統通知變數未被正常置換問題
- **Commit ID**: `7659dec8f6e2170324e1cdcc684a3eb785907a62`
- **作者**: 邱郁晏
- **日期**: 2023-12-04 14:10:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 36. [Web]Q00-20231201004 调整ipad Safari浏览器經常選取人員为默认全选
- **Commit ID**: `411b049f6f55e7700f68d42fd560460f914d9768`
- **作者**: 周权
- **日期**: 2023-12-01 10:59:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`

### 37. [流程引擎]Q00-20231127001 修正關卡設定多人都要簽核且設定自動簽核2，與前一關相同者，若前一關為核決關卡，且未實際展開核決關卡時，流程無法派送至下一關的異常
- **Commit ID**: `923eb7bb119d3846e0619bc5255f428db89bde4b`
- **作者**: waynechang
- **日期**: 2023-11-27 15:20:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 38. [Web] Q00-20231129005 修正serialNumber栏位显示问题
- **Commit ID**: `87c5b575b15ef151dc8e98ed94225e720984e6e6`
- **作者**: liuyun
- **日期**: 2023-11-29 16:44:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 39. [Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题[补修正]
- **Commit ID**: `a09427353debc146149389ef54cfbe8f64927db0`
- **作者**: 周权
- **日期**: 2023-11-29 16:55:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 40. [Web] Q00-20231129004 修正從追蹤連結進入已關注的愛心不會亮，該流程原本已被關注，但從追蹤連結進入後不會亮，須等切換到表單葉面後才會亮
- **Commit ID**: `b8b28dbdbabc132bd1b4f5e35bfdf790c841a446`
- **作者**: 刘旭
- **日期**: 2023-11-29 15:53:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 41. [Web]Q00-20231128006 调整grid標頭固定显示[补修正]
- **Commit ID**: `d18e241f8537d55e6109258304c56704cbfc4393`
- **作者**: 周权
- **日期**: 2023-11-29 14:16:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 42. [Web]Q00-20231129002 调整個人資訊页多个提示訊息显示不完整的问题
- **Commit ID**: `b63d653cc673c57962d3e1eef3df13cd4b64d2a8`
- **作者**: 周权
- **日期**: 2023-11-29 12:17:02
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageSimpleUserProfile.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 43. [Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题
- **Commit ID**: `bccb9ffb3be811609a87fec570310cada1c0726e`
- **作者**: 周权
- **日期**: 2023-11-29 11:43:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 44. [Web]Q00-20231129001 調整在行動裝置撤銷流程時填寫撤銷意見會被選單擋住的問題
- **Commit ID**: `8361ef7c5a89019f0510e3244f092b9bb5c0878d`
- **作者**: cherryliao
- **日期**: 2023-11-29 09:48:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 45. [Web]Q00-20231128006 调整grid標頭固定显示
- **Commit ID**: `1b9b2b9706398f6581121c54b50375c531ab1979`
- **作者**: 周权
- **日期**: 2023-11-28 16:17:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 46. [ESS]Q00-20231128003 調整缺席紀錄方法相容帶有單身資料的ESS單據
- **Commit ID**: `9a854c689dcb0e2f3fdbfa13e948b97ef2a872b6`
- **作者**: 林致帆
- **日期**: 2023-11-28 13:35:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`

### 47. [PRODT]Q00-20231128001 修正在開啟流程管理工具後主畫面的標題一併被更動問題
- **Commit ID**: `bdced4f5bcd943a27bfc7bef42b9baf5881bbef3`
- **作者**: cherryliao
- **日期**: 2023-11-28 10:02:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 48. [Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊
- **Commit ID**: `3bd6c06767301abf8c2beb8e4dad319ca760d84c`
- **作者**: 林致帆
- **日期**: 2023-11-27 16:31:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 49. [雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證
- **Commit ID**: `4e64e6b096049bac0e4b1af491f9f9c94b648c75`
- **作者**: 林致帆
- **日期**: 2023-11-24 10:45:57
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/TFAModule/TFASetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 50. [雙因素模組]Q00-*********** 修正未放入不驗證清單的使用者若未綁定，登入後不會跳出需綁定的提示視窗
- **Commit ID**: `12794aeb0935e8ac9bcab6a3cb754da111621a2c`
- **作者**: 林致帆
- **日期**: 2023-11-10 17:14:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/TFAConfigManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 51. [SAP]Q00-*********** 優化SAP整合服務，當整合的資料類型為Grid時，同時支持RWD表單Grid及絕對位置表單Grid
- **Commit ID**: `fb2fdbc80ec726b09c0f5317a607362981253faf`
- **作者**: waynechang
- **日期**: 2023-11-24 17:33:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 52. [流程引擎] Q00-*********** 修正批次簽核造成重複寄信問題
- **Commit ID**: `85dab06d05dc84278cac8c80c35eaf4380b731c2`
- **作者**: 邱郁晏
- **日期**: 2023-11-24 15:00:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 53. [Web] Q00-20231124002 修正流程主旨範本設定<#workItemName>显示N.A.
- **Commit ID**: `efce9628ee7aadf3cde9624c0e5e1ccc23275fe0`
- **作者**: liuyun
- **日期**: 2023-11-24 10:33:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 54. [Web]Q00-20231124003 修正Grid某一格或某一行设置样式，点击排序后样式消失的问题
- **Commit ID**: `31b92e45992c953a6d5e655723416d2a44f0fccf`
- **作者**: 周权
- **日期**: 2023-11-24 10:30:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 55. [Web] Q00-20231124001 修正附件元件每個檔案容量限制設定成104857600 kb,無法上傳附件
- **Commit ID**: `ba041c73de02792744906128f0fb019c23aec251`
- **作者**: 刘旭
- **日期**: 2023-11-24 10:24:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/DisplayLabelUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

### 56. [DT]Q00-20231122002 修正系統權限管理員中可存取的範圍設定權限範圍(部門/專案主管)後儲存會報後端接口調用失敗的問題
- **Commit ID**: `8e40d6171454165dcf82c803f2572475a00d3a64`
- **作者**: pinchi_lin
- **日期**: 2023-11-22 20:01:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`

### 57. [DT]Q00-20231121006 修正在Web化流程管理工具中活動參與者組織相關找不到離職日設定當天的使用者的問題
- **Commit ID**: `2bad621e1d1512b13d7156bde1608bb42d9f212c`
- **作者**: pinchi_lin
- **日期**: 2023-11-21 16:14:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 58. [SAP]Q00-20231121001 修正SAP整合，當mapping內容有Grid時，可能會有GridColumnId與GridValue順序錯誤的異常
- **Commit ID**: `c7b08c25a6b37b750f257968323abdd88b674e20`
- **作者**: waynechang
- **日期**: 2023-11-21 11:55:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 59. [流程引擎]Q00-20231120003 修正監控流程的詳細流程圖頁面，當按下「跳過此關卡」功能時，下一關處理者無法收到代辦通知信件
- **Commit ID**: `8adc6b272f9b81a2e37b5a3e4ee2b00e70a4cc2e`
- **作者**: waynechang
- **日期**: 2023-11-20 17:25:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 60. [Web] Q00-20231120001 修正BPM注册序号中 BPM流程引擎有过期序号导致其他功能无法正常使用
- **Commit ID**: `3007eaa7e07831a2b31eac616585ad8fe4ba2c40`
- **作者**: liuyun
- **日期**: 2023-11-20 11:01:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 61. [Web] Q00-20231117002 修正 首次登录跳转到变更密码页面显示异常
- **Commit ID**: `1350def2f086056f047366345cf1bddc5e493a3c`
- **作者**: liuyun
- **日期**: 2023-11-17 13:48:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`

### 62. [Web] Q00-20231117001 调整簡易流程圖跳過時_輸入密碼後alert的訊息框太小的问题
- **Commit ID**: `f409a05f6f07b36d1f918db27fc500a620770f3a`
- **作者**: 周权
- **日期**: 2023-11-17 13:42:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 63. [Web] Q00-20231114003 修正寄件人重複顯示問題
- **Commit ID**: `97c085ec8fbfa03c2c2f49118344194783129bba`
- **作者**: 邱郁晏
- **日期**: 2023-11-14 15:54:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 64. [WEB] Q00-20231108002 修正待辦事項中表單serialNumber元件欄位資料顯示位置異常的問題
- **Commit ID**: `1c8a00f62718a058cc818380cb9515d1ee621585`
- **作者**: 周权
- **日期**: 2023-11-08 13:21:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 65. [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)
- **Commit ID**: `3c067f71c228412457a4e493c212803d510f0038`
- **作者**: 邱郁晏
- **日期**: 2023-11-06 17:47:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java`

### 66. [WEB]Q00-20231106001 调整Select元件在设置背景色时列印却显示唯读背景色的问题
- **Commit ID**: `bc3e294a123127a2ddc1d0eefac8cacd73d8a31c`
- **作者**: 周权
- **日期**: 2023-11-06 11:11:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 67. [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補)
- **Commit ID**: `aa09f62b64a2b907e650f067b16b87bb162be173`
- **作者**: 邱郁晏
- **日期**: 2023-11-03 11:12:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 68. [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題(補修正)
- **Commit ID**: `ad4bb2ce3a93571fe90e0d3e76e772026cd8523e`
- **作者**: 邱郁晏
- **日期**: 2023-10-25 18:14:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 69. [流程引擎] Q00-20231025004 修正關卡為「多人處理」且低工作執行率時，簽核歷程顯示異常問題
- **Commit ID**: `4fe066ce39e270b54c4e96b1d24737f6a507e82c`
- **作者**: 邱郁晏
- **日期**: 2023-10-25 16:35:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 70. [T100] Q00-20231102002 T100的签核历程沒有权限的显示提示告知登入者
- **Commit ID**: `d13f8945580ce29c97c02142d0493438a228d7d4`
- **作者**: liuyun
- **日期**: 2023-11-02 13:12:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessInfoGet.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`

### 71. [Web] Q00-20231101005 修正表單欄位為invisible且設定顯示千分位，開啟追蹤流程在F12顯示錯誤
- **Commit ID**: `8cc9736a639768914dab9892188731823263cc35`
- **作者**: 邱郁晏
- **日期**: 2023-11-01 15:50:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 72. [DT]Q00-20231101004 修正Web化流程管理工具中服務任務的應用程式加入表單型態參數後會有無法簽入的問題
- **Commit ID**: `20feced941434a2919c3ba7cf1e24e86f4c8f99c`
- **作者**: pinchi_lin
- **日期**: 2023-11-01 15:38:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 73. [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯[補修正]
- **Commit ID**: `bfbfe27f24eae061e549a130bdf2ac8ae907a592`
- **作者**: 林致帆
- **日期**: 2023-11-01 14:10:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 74. [雙因素模組]Q00-20231101002 修正雙因素端點資訊及不驗證清單的處裡邏輯
- **Commit ID**: `6d02ea7663b9ef80dafd1fc80241c85c30c59fe5`
- **作者**: 林致帆
- **日期**: 2023-11-01 14:04:59
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 75. [Web]S00-20220929001 新增BPM外部連結-進入BPM首頁
- **Commit ID**: `cf7baf754637258ac8524e00f2099d83cd72b484`
- **作者**: 林致帆
- **日期**: 2023-10-05 14:23:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PortletEntry.jsp`

### 76. [Web]Q00-20230919001 調整URL從/NaNaWeb/Login.jsp?type=admin登入時會報閒置過久的訊息改成"請輸入正確的代號或密碼!"
- **Commit ID**: `d225d3257b4a4748a1e92d6fed3b695881930956`
- **作者**: 林致帆
- **日期**: 2023-09-19 12:02:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 77. [Web] Q00-20231025001 调整隐藏栏位为單身欄位加總的运算操作验证
- **Commit ID**: `cdb46378b5dcfd3530572f971f6b374352fa7fa9`
- **作者**: 周权
- **日期**: 2023-10-25 11:01:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 78. [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]
- **Commit ID**: `63ae946ba9eecd48cf48d49067ff6a8e3ebb660d`
- **作者**: 周权
- **日期**: 2023-09-28 17:59:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 79. [Web] Q00-20231031001 修正缩小ESSPlus管理页面时，查询出来的结果在grid显示不全
- **Commit ID**: `4a94486a23d4d1c32d8636c5c0341141a7ae72c9`
- **作者**: liuyun
- **日期**: 2023-10-31 13:39:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`

### 80. [Web] Q00-20231030004 调整预览列印会带出表單名稱與表單代號Tab的问题
- **Commit ID**: `4cbd31501863505d3bf2ad6dd454da4fab65229b`
- **作者**: 周权
- **日期**: 2023-10-30 15:54:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 81. [Web]Q00-20231023001 修正列印预览grid标题字体颜色为白色
- **Commit ID**: `7c94cbb4bb6dc6249ef6a6baeb676dd207f966bd`
- **作者**: liuyun
- **日期**: 2023-10-23 09:15:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 82. [流程引擎]Q00-20231030001 修正流程發起者於追蹤流程頁面進入表單畫面點擊撤銷流程後，詳細流程圖的流程詳細資訊應為是「流程發起者」而非「流程負責人」撤銷
- **Commit ID**: `df88b8a0570dd9ba09811456434bd06abb79425d`
- **作者**: waynechang
- **日期**: 2023-10-30 14:41:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 83. [流程引擎]Q00-20231027003 修正流程結案清除附件的服務，當表單附件OID屬性為空時，會被系統移除附件，此調整為避免移除表單實例的附件的OID屬性為空的附件
- **Commit ID**: `94bcbf65368ea07dcf42c7be0b6e51786f4a929c`
- **作者**: waynechang
- **日期**: 2023-10-27 14:39:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 84. [資安] Q00-20231017003 修改因paloalto內部防火牆把aes.js當成spyware Malicious JavaScript Files Detection攻擊，將aes.js進行加密編碼。
- **Commit ID**: `3d1cba1b0c9639bfecc92261e6b91b0d8b74335f`
- **作者**: 林致帆
- **日期**: 2023-10-26 16:16:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/aes.js`

### 85. [Web]Q00-20231026001 修正转由他人处理 > 经常选取对象 无资料时显示错误页面
- **Commit ID**: `7f8b7f1acb793176366607b87882dd24f840e47e`
- **作者**: liuyun
- **日期**: 2023-10-26 14:29:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp`

### 86. [Web]Q00-20231023002 修正流程第一關有設定必須上傳新附件，若從流程草稿開啟時，系統沒有卡控必須上傳新附件
- **Commit ID**: `fda832304a2ade4c262d5d477479848a405b7c3b`
- **作者**: waynechang
- **日期**: 2023-10-23 11:10:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`

### 87. [WEB]Q00-20231018001 调整Select元件有textbox輸入格时的点击事件逻辑
- **Commit ID**: `88e1cf417dde9e02c3d2795a5d0a99a8cd0a7d1f`
- **作者**: 周权
- **日期**: 2023-10-18 11:13:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/resources/html/SelectElementTemplate.txt`

### 88. [流程引擎] Q00-20231017004 修正工作受託者<#allAssigneesIDnName>沒有帶出資料的問題
- **Commit ID**: `c6bf4837a6cfd7bad3b6a81c36d796bdec078d92`
- **作者**: 邱郁晏
- **日期**: 2023-10-17 14:56:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 89. [表單設計師]Q00-20231012004 修正表單有textBox髒資料，匯入轉RWD表單匯入失敗
- **Commit ID**: `f5b181b0fb37aa98758b066724a737d7a2d0842d`
- **作者**: 林致帆
- **日期**: 2023-10-12 15:30:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 90. [T100] Q00-20231004004 修正手寫元件造成拋單失敗，新增判斷略過手寫元件
- **Commit ID**: `0e06874d13a0247e42e7812e52620c7f27bdf16c`
- **作者**: 邱郁晏
- **日期**: 2023-10-04 14:56:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 91. [WEB]Q00-20231004003 修正在手機瀏覽器以及RWD窄畫面上沒有附件檔名的URL連結導致無法下載問題
- **Commit ID**: `266c5f2638566cd340f0073a429382aea76b4bed`
- **作者**: yamiyeh10
- **日期**: 2023-10-04 14:11:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 92. [Web] V00-20231011001 修正轉存表單日期格式異常問題，支持常用格式。
- **Commit ID**: `873ea01a3965a369c5174a58cf1abbc67680f8d0`
- **作者**: 邱郁晏
- **日期**: 2023-10-11 14:21:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 93. [Web] V00-20231004001 修正匯出表單，日期格式(yy/M/d)被判斷為異常格式問題。
- **Commit ID**: `a4cfae325dcd47ae0219d75a4a203d1cbf4ef777`
- **作者**: 邱郁晏
- **日期**: 2023-10-04 12:04:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 94. [Web]Q00-20231004001 修正TextBox設定數字轉繁體文字在列印表單時顯示簡體文字的問題
- **Commit ID**: `23fa935ff54e6acaffbae18056b1b85cacbee415`
- **作者**: cherryliao
- **日期**: 2023-10-04 10:09:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 95. [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。[补修正]
- **Commit ID**: `b0cd56a4c4394613c2121c6b57bbd6e0cd3c0c4f`
- **作者**: 周权
- **日期**: 2023-09-26 14:32:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 96. [Web]Q00-20230925002 调整TextBox元件輸入值為科學計數法時元件值判断邏輯。
- **Commit ID**: `499f79d8aea409f6381f69ca47585429f375312a`
- **作者**: 周权
- **日期**: 2023-09-25 17:24:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 97. [Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题[补修正]
- **Commit ID**: `12f3c8c72f6ae5c9d8f53c90722be953166a8502`
- **作者**: 周权
- **日期**: 2023-08-24 13:39:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 98. [Web]Q00-20230824001 修正textbox类型为浮点数，小数点后几位为完整显示，输入负零点几负号会消失不见的问题
- **Commit ID**: `db2688d4318c7af76af0e9bea23c548a629a5461`
- **作者**: 周权
- **日期**: 2023-08-24 09:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 99. [SAP]Q00-20231003003 修正SAP整合作業-SAP欄位對應設定，新增整合設定頁面當選擇完表單後，無法載入表單元件
- **Commit ID**: `1c0857b653f8e3532fbc1d57dae9e6a0b8648f30`
- **作者**: waynechang
- **日期**: 2023-10-03 17:39:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`

### 100. [DT]Q00-20230928003 修正從Web化流程管理工具入版後的流程圖在Swing中開啟時流程圖會被截斷問題
- **Commit ID**: `f6ff06040667e7f310884134c1b5bff2046574a4`
- **作者**: pinchi_lin
- **日期**: 2023-09-28 17:56:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 101. [web]Q00-20230927003 預覽列印時，頁籤元件顯示文字為白色，但實際列印變成黑色。 问题修复
- **Commit ID**: `fd81e5cec621012c141bd295942f76440117bf07`
- **作者**: 刘旭
- **日期**: 2023-09-27 17:19:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java`

### 102. [DT]Q00-20230926002 修正Web流程管理工具儲存流程因找不到ActivityType導致儲存失敗問題
- **Commit ID**: `84c40844c933817f27b210d50918399c4b8ef837`
- **作者**: pinchi_lin
- **日期**: 2023-09-26 17:58:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 103. [DT]Q00-20230926003 修正Web流程管理工具的流程圖進版後有條件的連接線顏色變黑色問題
- **Commit ID**: `2aa04710165d3b86229e2fe26d0e61a2aba081a9`
- **作者**: pinchi_lin
- **日期**: 2023-09-26 17:48:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 104. [DT]Q00-20230926004 修正Web流程管理工具的流程圖進版後連接線上的名稱消失問題
- **Commit ID**: `4791c5e879dcab0752914f3990cef0b6080ee193`
- **作者**: pinchi_lin
- **日期**: 2023-09-26 17:44:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 105. [在線閱覽]Q00-20230926001 修正在線閱讀檔案設定不可下載，在待辦表單頁面點擊閱讀檔案的頁面，仍可以下載PDF閱讀檔的異常
- **Commit ID**: `29ab55e8f507ab48ed31050044b2889bc9012ffc`
- **作者**: waynechang
- **日期**: 2023-09-26 16:01:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 106. [T100]S00-20220513001 T100取簽核歷程新增是否為代理人標籤
- **Commit ID**: `243f0dee28fcd78c01dc355e5ea1ebdeea2cd690`
- **作者**: 林致帆
- **日期**: 2023-09-05 10:16:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTag.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`

### 107. [Web] Q00-20230922004 在没有添加grid按钮却使用grid方法时，新增防呆，防止focus报错
- **Commit ID**: `89cd1b054764b60e4d9fc1bd4af233cbda0116e4`
- **作者**: 周权
- **日期**: 2023-09-22 14:57:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 108. [Web] Q00-20230922001 修正流程管理>流程派送异常处理页面的全选按钮失效
- **Commit ID**: `849272149d02c8c88a944b12c8c9f07e07f6bfbb`
- **作者**: liuyun
- **日期**: 2023-09-22 11:21:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`

### 109. [Web]Q00-20230922002 修正附件名稱帶有單引號，導致附件點擊次數無法增加
- **Commit ID**: `e2a961c69ca53882f902a4b2c5780c0b62f12999`
- **作者**: 林致帆
- **日期**: 2023-09-22 10:42:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 110. [Web] Q00-20230922003 修正表单设计师>进入响应式表单F12 not found报错
- **Commit ID**: `7d7e7b251a1cbb609289e4b2a03c760c508123ad`
- **作者**: liuyun
- **日期**: 2023-09-22 10:27:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`

### 111. [組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號(補修正)
- **Commit ID**: `16cd4611bfd70d7fc616a2c79cf4cf6239800dd0`
- **作者**: 邱郁晏
- **日期**: 2023-09-21 17:05:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 112. [DT]Q00-20230921001 修正Web流程管理工具中參與者選職務或職稱後儲存再開啟其值會消失的問題
- **Commit ID**: `f8404cd5b3f7d7cf2bedd01f370900297e73bd4b`
- **作者**: pinchi_lin
- **日期**: 2023-09-21 16:13:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 113. [DT]Q00-20230920002 修正Web系統權限管理員上儲存按鈕後畫面會一直loading的問題
- **Commit ID**: `c505588e3f0b6265145c0da461f5cdfbd0fd9f21`
- **作者**: yamiyeh10
- **日期**: 2023-09-20 16:38:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`

### 114. [組織同步] Q00-20230920001 修正HR同步部門核決層級時，沒有判斷組織代號。
- **Commit ID**: `4a4a32ad287ed0d2f080d30b26516b16ab9bd884`
- **作者**: 邱郁晏
- **日期**: 2023-09-20 13:44:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 115. [DT]A00-20230919001 修正Web流程管理工具中事件處理在流程儲存後會消失問題
- **Commit ID**: `b77ad2a05e9dd1f4e1cc36a9f2993fc0242c044e`
- **作者**: yamiyeh10
- **日期**: 2023-09-20 12:01:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 116. [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"[補修正]
- **Commit ID**: `ed3c3700c858b38d0c7ea39c4a8d3ae08e846af4`
- **作者**: 林致帆
- **日期**: 2023-09-19 13:42:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 117. [DT]Q00-20230918002 修正Web流程管理工具中連接線編輯後儲存流程再開啟會變成藍色的問題
- **Commit ID**: `11bcd57876342d6dba5b2e9c59d5f2cc20ba1c42`
- **作者**: pinchi_lin
- **日期**: 2023-09-18 14:40:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 118. [ESS]Q00-20230918001 調整ESS流程發起完成頁面調整訊息為"表單資料尚未處理完成，請至追蹤流程清單頁面查看此流程"
- **Commit ID**: `d87cb6df57f8577ed09e5ac875957d9a26f508b3`
- **作者**: 林致帆
- **日期**: 2023-09-18 14:02:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 119. [web]Q00-20230913001 沒有未閱讀的工作通知，但右上角鈴鐺還是一直有紅點點问题修复
- **Commit ID**: `159fcb0154095ae0f17ff8904322b71dbab2cd95`
- **作者**: 刘旭
- **日期**: 2023-09-18 09:24:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 120. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正目录未展开可以点击 【补修正】
- **Commit ID**: `a74f2c89c9a72bc332b5ce748f2ca0c6e0e24341`
- **作者**: liuyun
- **日期**: 2023-09-11 16:08:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 121. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面，修正设定未修改密码强制退出【补修正】
- **Commit ID**: `36ba303dce5da0c92ced190573e26c5ab3d576d2`
- **作者**: liuyun
- **日期**: 2023-09-07 17:54:10
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 122. [Web] S00-20230619002 将变更密码页面有dialog窗口改为内嵌页面
- **Commit ID**: `d83f57a6e0c67ad479d43287fcbc2e07a80e78d2`
- **作者**: liuyun
- **日期**: 2023-08-25 15:40:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 123. [Web] Q00-20230915001 修正 SQL注册器点击资料返回到新增页面，数据错误带回显示
- **Commit ID**: `0b0eee29550a12feff3945b8fa4a5ebee9608db5`
- **作者**: liuyun
- **日期**: 2023-09-15 11:09:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp`

### 124. [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題[補]
- **Commit ID**: `e5bbbc2bfd84cf10a5a41fdc952a6fb589756a43`
- **作者**: cherryliao
- **日期**: 2023-09-15 10:22:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 125. [Web]Q00-20230914001 修正RadioButton，checkbox元件帶入值到grid时报错，新增防呆。
- **Commit ID**: `8bad3e313c81ff1176821fcf0d2a8e8b7b699bc9`
- **作者**: 周权
- **日期**: 2023-09-14 16:07:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 126. [Web]Q00-20230912003 编辑或新增系统排程时，增加排程生效时间最大日期卡控设定。
- **Commit ID**: `3ce886ded44b84dd6da4ed224f1fe92c53f58751`
- **作者**: 周权
- **日期**: 2023-09-12 15:29:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 127. [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法(補)
- **Commit ID**: `b7af9bb3ab4fbec5ab05bb6d4035cf3f357a4e91`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 10:23:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 128. [Web] Q00-20230912001 新增絕對位置表單列印畫面引入jBPM語法
- **Commit ID**: `1c6d4f2abad238d9b424702b482757ba602e6687`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 10:06:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 129. [Web]Q00-20230911002 修正片语在使用时，特殊符號在Html会轉換的問題。
- **Commit ID**: `2a249f8c450b6fcbc70a198e10e2a75766225daf`
- **作者**: 周权
- **日期**: 2023-09-11 15:39:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`

### 130. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
- **Commit ID**: `bd50740a44f0ef78967b45dd36f416f141b77b76`
- **作者**: 邱郁晏
- **日期**: 2023-09-12 17:23:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 131. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題(補)
- **Commit ID**: `5ac3b5dec2f5d3ee78d2db6e267b9c9db3f9a5f8`
- **作者**: 邱郁晏
- **日期**: 2023-09-04 10:53:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 132. [Web] Q00-20230831004 修正寄件人帶有中文字，導致編碼異常無法寄信問題
- **Commit ID**: `c574ec137870e563b59669ce40cc6ff52a5f5cc3`
- **作者**: 邱郁晏
- **日期**: 2023-08-31 15:41:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 133. [表單設計師]Q00-20230908002 修正資料選取設定參考表單資料的回傳欄位多筆的時候下方的按鈕會被擋住的問題
- **Commit ID**: `ad68936dee6a03a4c9f9582304298e76779aa498`
- **作者**: cherryliao
- **日期**: 2023-09-08 16:39:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 134. [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。[补修正]
- **Commit ID**: `5cf50118ad353688beb07bbc27986b8b3ac05330`
- **作者**: 周权
- **日期**: 2023-09-08 10:14:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`

### 135. [SAP]Q00-20230908001 調整因欄位值取得異常造成呼叫SAP產品失敗
- **Commit ID**: `20b08849dc32bb291a1c771427bda3f7a555954b`
- **作者**: 林致帆
- **日期**: 2023-09-08 09:24:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 136. [Web]Q00-20230907001 读取自定义background,Banner,logo图片时，新增图片格式防呆。
- **Commit ID**: `ffb260bbb2c54195929376f607925ad7fc6ccd4f`
- **作者**: 周权
- **日期**: 2023-09-07 11:31:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 137. [流程引擎]Q00-20230907002 修正核決關卡內的關卡向後加簽關卡後，又再刪除加簽的關卡時，核決關卡繼續派送時會發生異常
- **Commit ID**: `7dea21018ff84f649cce05eed470cd4c4a1beb1e`
- **作者**: waynechang
- **日期**: 2023-09-07 10:31:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 138. [Web]Q00-20230906002 修正转由他人处理二次密码验证弹窗显示过小的问题。
- **Commit ID**: `e699a42126c703329ef58e96c2b657ae20675adc`
- **作者**: 周权
- **日期**: 2023-09-06 13:19:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`

### 139. [Web] Q00-20230906001 修正系統通知為自定義URL時，出現異常錯誤，調整寫法並新增錯誤處理機制。
- **Commit ID**: `4a3fe8f78be2406373a59e4933ea3d472d5e0eb6`
- **作者**: 邱郁晏
- **日期**: 2023-09-06 12:03:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`

### 140. [Web] Q00-20230911001 修正 时间元件提示 限制輸入日期或格式yyyy/MM/dd (HH:mm) 错误
- **Commit ID**: `29767c102033fb3b27fb7a0909f338d0650aee9f`
- **作者**: liuyun
- **日期**: 2023-09-11 11:47:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 141. [Web]Q00-20230905004 修正关卡无處理人員时，签名图档报错问题，添加防呆。
- **Commit ID**: `63bc842520f9344ddd0c495ec22b77270b53536b`
- **作者**: 周权
- **日期**: 2023-09-05 18:01:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 142. [Web] A00-20230904001 修正将HorizontalLine元件设定为invisible隐藏后，上传附件后刷新表单会空白
- **Commit ID**: `4412bd5a36a8723fdb9d2c7b9843ee27bdfe35fe`
- **作者**: liuyun
- **日期**: 2023-09-06 11:11:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 143. [DT]A00-20230901001 修正Web流程管理工具中設定流程負責人跟流程逾時儲存後會消失問題
- **Commit ID**: `a459de8d88f4afe86a63758b5f5fce19e63561bb`
- **作者**: pinchi_lin
- **日期**: 2023-09-01 19:47:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 144. 打包用
- **Commit ID**: `721c74a695e0e0a37765d71963ebca50e5fab6b8`
- **作者**: kmin
- **日期**: 2023-08-31 14:47:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/lib/Json/json.jar`

### 145. [Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件
- **Commit ID**: `7edf85fb3aaae049bc59fd0591ea66848d1cef12`
- **作者**: 林致帆
- **日期**: 2023-08-31 09:16:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 146. [Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題
- **Commit ID**: `48fecbee1144a6d2fe423dd3f4ad4ae8e1f605f5`
- **作者**: cherryliao
- **日期**: 2023-08-30 11:47:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 147. [TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷
- **Commit ID**: `b3892d1623b3636b81e6130827e7d2d13eafc60f`
- **作者**: 林致帆
- **日期**: 2023-08-30 10:43:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 148. [流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常
- **Commit ID**: `9e1a8c44961c5907be02fe8fbdc1b861b7958e60`
- **作者**: waynechang
- **日期**: 2023-08-29 16:50:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 149. [ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗
- **Commit ID**: `b30e78a2b454939864ee2810855877513097fe36`
- **作者**: 林致帆
- **日期**: 2023-08-29 15:50:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 150. [web]Q00-20230829003 列印時附件資訊會超出邊界问题修复
- **Commit ID**: `f72c90f6b7c0d2f7c468a8d0dd2c43ba93369fab`
- **作者**: 刘旭
- **日期**: 2023-08-29 14:02:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 151. [流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據
- **Commit ID**: `feae55e055644d8903d73585988148566eb72137`
- **作者**: waynechang
- **日期**: 2023-08-29 10:32:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 152. [SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤
- **Commit ID**: `f7d42cd47ebe4f0fa47038313eee19d638b4ee3c`
- **作者**: 林致帆
- **日期**: 2023-08-28 16:57:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 153. [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
- **Commit ID**: `c4e8abf77a2d5851011fb9f7df99123b4b5d5713`
- **作者**: pinchi_lin
- **日期**: 2023-08-28 11:15:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 154. [web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复
- **Commit ID**: `9e931eef3588ea08db7b75a46fbc9e1083303403`
- **作者**: 刘旭
- **日期**: 2023-08-25 17:38:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 155. [Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。
- **Commit ID**: `2ce60fb865619dd15ff23dc0db3378472fda363c`
- **作者**: 邱郁晏
- **日期**: 2023-08-24 13:43:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 156. [Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常
- **Commit ID**: `39f901b07448b8ebf85b6a97a2a949267ac915bf`
- **作者**: waynechang
- **日期**: 2023-08-23 15:27:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

