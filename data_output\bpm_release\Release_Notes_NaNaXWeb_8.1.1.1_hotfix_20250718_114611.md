# Release Notes - NaNaXWeb

## 版本資訊
- **新版本**: 8.1.1.1_hotfix
- **舊版本**: release_8.1.1.1
- **生成時間**: 2025-07-18 11:46:11
- **新增 Commit 數量**: 10

## 變更摘要

### yamiyeh10 (7 commits)

- **2025-06-26 17:44:25**: [PRODT]C01-20250626003 修正Web流程管理中設定關卡的表單存取控管設定無法儲存問題
  - 變更檔案: 1 個
- **2025-06-17 11:54:06**: [SYSDT]C01-20250617001 修正Web系統管理中資料來源設定在編輯狀態下調整ID後儲存會發生後端接口調用失敗的問題
  - 變更檔案: 2 個
- **2025-05-05 15:47:58**: [行業表單庫]Q00-20250505001 修正行業表單庫因資料撈取筆數限制導致範例表單顯示不全的問題[補]
  - 變更檔案: 1 個
- **2025-05-05 13:58:40**: [行業表單庫]Q00-20250505001 修正行業表單庫因資料撈取筆數限制導致範例表單顯示不全的問題
  - 變更檔案: 1 個
- **2025-05-02 16:31:09**: [PRODT]C01-20250417005 優化Web流程管理中表單存取控管設定欄位過多時滑鼠滾動會有卡頓問題
  - 變更檔案: 4 個
- **2025-04-18 10:24:37**: [PRODT]Q00-20250416001 修正Web流程管理中設定關卡的表單存取控管設定無法開啟問題
  - 變更檔案: 8 個
- **2025-04-15 15:31:07**: [SYSDT]C01-20250414004 修正Web系統管理中資料來源設定在編輯狀態下儲存會顯示重複ID訊息問題
  - 變更檔案: 1 個

### 周权 (3 commits)

- **2025-06-11 17:58:20**: [文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题[補]
  - 變更檔案: 1 個
- **2025-06-11 17:44:51**: [文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题[補]
  - 變更檔案: 2 個
- **2025-06-11 16:04:40**: [文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [PRODT]C01-20250626003 修正Web流程管理中設定關卡的表單存取控管設定無法儲存問題
- **Commit ID**: `62463c5cb865e9b18f08e942886a38a43c7a5cc7`
- **作者**: yamiyeh10
- **日期**: 2025-06-26 17:44:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts`

### 2. [SYSDT]C01-20250617001 修正Web系統管理中資料來源設定在編輯狀態下調整ID後儲存會發生後端接口調用失敗的問題
- **Commit ID**: `8437a57617e4ff88dee7274b776115c6e0b91ee6`
- **作者**: yamiyeh10
- **日期**: 2025-06-17 11:54:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.ts`

### 3. [文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题[補]
- **Commit ID**: `b776a81ad26a84c03625540ad14e3c33a89eceea`
- **作者**: 周权
- **日期**: 2025-06-11 17:58:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.html`

### 4. [文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题[補]
- **Commit ID**: `773c91d6e2bc7cdd126e0d36ba1adca7d01891fa`
- **作者**: 周权
- **日期**: 2025-06-11 17:44:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.css`
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.ts`

### 5. [文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题
- **Commit ID**: `81141aeaf4e9dc79e92e89b963e7fa126e7c555b`
- **作者**: 周权
- **日期**: 2025-06-11 16:04:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.ts`

### 6. [行業表單庫]Q00-20250505001 修正行業表單庫因資料撈取筆數限制導致範例表單顯示不全的問題[補]
- **Commit ID**: `b60a0c02c7bd318ffd9f01ff023c1cce825dd9db`
- **作者**: yamiyeh10
- **日期**: 2025-05-05 15:47:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/form/form-repository/form-repository.component.ts`

### 7. [行業表單庫]Q00-20250505001 修正行業表單庫因資料撈取筆數限制導致範例表單顯示不全的問題
- **Commit ID**: `7ac8ed865f142e21e399eac14a762f9916e7d120`
- **作者**: yamiyeh10
- **日期**: 2025-05-05 13:58:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/form/form-repository/form-repository.component.ts`

### 8. [PRODT]C01-20250417005 優化Web流程管理中表單存取控管設定欄位過多時滑鼠滾動會有卡頓問題
- **Commit ID**: `2cfa9a1f8496581233a568ef0e9a89b7d2bd86c1`
- **作者**: yamiyeh10
- **日期**: 2025-05-02 16:31:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.css`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts`

### 9. [PRODT]Q00-20250416001 修正Web流程管理中設定關卡的表單存取控管設定無法開啟問題
- **Commit ID**: `4d95b7b355d140409df29eace6081c8f2f756602`
- **作者**: yamiyeh10
- **日期**: 2025-04-18 10:24:37
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-attachment-attributes/form-attachment-attributes.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-attachment-attributes/form-attachment-attributes.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-variable/form-type/form-type.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-variable/form-type/form-type.component.ts`

### 10. [SYSDT]C01-20250414004 修正Web系統管理中資料來源設定在編輯狀態下儲存會顯示重複ID訊息問題
- **Commit ID**: `c9536c55dfa624256d7e6d877efb4aafa5253cdf`
- **作者**: yamiyeh10
- **日期**: 2025-04-15 15:31:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.ts`

