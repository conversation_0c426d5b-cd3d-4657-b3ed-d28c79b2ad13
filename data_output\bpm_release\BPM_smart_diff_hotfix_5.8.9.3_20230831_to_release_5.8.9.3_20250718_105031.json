{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.3_20230831", "date": "2023-08-31 14:47:24", "message": "打包用", "author": "kmin"}, "舊分支": {"branch_name": "release_5.8.9.3", "date": "2023-08-23 13:15:30", "message": "[EBG]S00-*********** 新增EBG電子簽章專案 [補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 10:50:31", "新增commit數量": 13, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "721c74a695e0e0a37765d71963ebca50e5fab6b8", "commit_訊息": "打包用", "提交日期": "2023-08-31 14:47:24", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/lib/Json/json.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "7edf85fb3aaae049bc59fd0591ea66848d1cef12", "commit_訊息": "[Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件", "提交日期": "2023-08-31 09:16:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "48fecbee1144a6d2fe423dd3f4ad4ae8e1f605f5", "commit_訊息": "[Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題", "提交日期": "2023-08-30 11:47:29", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b3892d1623b3636b81e6130827e7d2d13eafc60f", "commit_訊息": "[TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷", "提交日期": "2023-08-30 10:43:55", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e1a8c44961c5907be02fe8fbdc1b861b7958e60", "commit_訊息": "[流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常", "提交日期": "2023-08-29 16:50:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b30e78a2b454939864ee2810855877513097fe36", "commit_訊息": "[ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗", "提交日期": "2023-08-29 15:50:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f72c90f6b7c0d2f7c468a8d0dd2c43ba93369fab", "commit_訊息": "[web]Q00-20230829003 列印時附件資訊會超出邊界问题修复", "提交日期": "2023-08-29 14:02:09", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "feae55e055644d8903d73585988148566eb72137", "commit_訊息": "[流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據", "提交日期": "2023-08-29 10:32:40", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f7d42cd47ebe4f0fa47038313eee19d638b4ee3c", "commit_訊息": "[SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤", "提交日期": "2023-08-28 16:57:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c4e8abf77a2d5851011fb9f7df99123b4b5d5713", "commit_訊息": "[DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題", "提交日期": "2023-08-28 11:15:49", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e931eef3588ea08db7b75a46fbc9e1083303403", "commit_訊息": "[web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复", "提交日期": "2023-08-25 17:38:15", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ce60fb865619dd15ff23dc0db3378472fda363c", "commit_訊息": "[Web] Q00-20230817002 修正TraceProcessForSearchForm待辦URL連結異常問題。", "提交日期": "2023-08-24 13:43:54", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "39f901b07448b8ebf85b6a97a2a949267ac915bf", "commit_訊息": "[Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常", "提交日期": "2023-08-23 15:27:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}]}