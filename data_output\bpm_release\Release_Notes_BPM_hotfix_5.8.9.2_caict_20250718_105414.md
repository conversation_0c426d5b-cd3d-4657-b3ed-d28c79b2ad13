# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.2_caict
- **舊版本**: release_5.8.9.2
- **生成時間**: 2025-07-18 10:54:14
- **新增 Commit 數量**: 13

## 變更摘要

### waynechang (7 commits)

- **2023-09-08 16:33:19**: [Web]Q00-20230908003 追蹤流程進入表單頁面時，增加「返回待辦」頁面的功能按鈕
  - 變更檔案: 1 個
- **2023-09-05 16:41:51**: [流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt=0 [補]
  - 變更檔案: 1 個
- **2023-09-04 16:23:46**: [流程引擎] Q00-20230904001 調整開啟草稿時，先前上傳附件需一併顯示 [補]
  - 變更檔案: 1 個
- **2023-09-04 16:04:01**: [流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt=0 [補]
  - 變更檔案: 1 個
- **2023-09-04 14:56:33**: [WEB] Q00-20230904009 非发起人关卡，把「保存」按鈕取消 →  把 「儲存表單」的按鈕隱藏                       当发起人取回重办时，需要把【退回修改】的按钮隐藏 → 发起人关卡UserTask_3
  - 變更檔案: 1 個
- **2023-09-04 14:36:05**: [流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt<>1
  - 變更檔案: 1 個
- **2023-09-04 14:35:02**: [流程引擎] Q00-20230904001 調整開啟草稿時，先前上傳附件需一併顯示
  - 變更檔案: 2 個

### 林致帆 (3 commits)

- **2023-09-04 14:32:27**: [Web]Q00-20230904008 流程發起、待辦等相關ICON需要能替換，且繼續派送按鈕需依流程、關卡顯示不同內容
  - 變更檔案: 3 個
- **2023-09-04 13:40:44**: [Web]Q00-20230904003 調整退回重瓣按鈕不需跳出退回重瓣視窗，改成直接退回
  - 變更檔案: 1 個
- **2023-09-04 13:37:30**: [Web]Q00-20230904004 新增追蹤流程清單+草稿的Portlet
  - 變更檔案: 3 個

### 刘旭 (2 commits)

- **2023-09-04 11:07:05**: [web]Q00-20230901007 追蹤流程頁面預設顯示發起過的流程
  - 變更檔案: 1 個
- **2023-09-04 11:01:15**: [web]Q00-20230901006 側邊一級菜單只展示客戶需要的內容：管理員維特原本MENU，一般使用者 側邊一級菜單只顯示追蹤流程、流程草稿、取回重辦、待办事项及某些客製作業
  - 變更檔案: 1 個

### liuyun (1 commits)

- **2023-09-04 10:26:14**: [Web] Q00-20230901005 使用Porlet进入发起流程界面，发单成功后添加进入待办按钮和页面内容
  - 變更檔案: 2 個

## 詳細變更記錄

### 1. [Web]Q00-20230908003 追蹤流程進入表單頁面時，增加「返回待辦」頁面的功能按鈕
- **Commit ID**: `399c2e1971a06d89851c6113d567c9b16e327eb0`
- **作者**: waynechang
- **日期**: 2023-09-08 16:33:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 2. [流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt=0 [補]
- **Commit ID**: `f7ada1c139832fbea2aee7aed628a0c3328f341f`
- **作者**: waynechang
- **日期**: 2023-09-05 16:41:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 3. [流程引擎] Q00-20230904001 調整開啟草稿時，先前上傳附件需一併顯示 [補]
- **Commit ID**: `c061169b15f0d09a7af3e401ae99dae5e369088c`
- **作者**: waynechang
- **日期**: 2023-09-04 16:23:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 4. [流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt=0 [補]
- **Commit ID**: `b2fc136f73db51903504f2a0296c6996d36f6d95`
- **作者**: waynechang
- **日期**: 2023-09-04 16:04:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`

### 5. [WEB] Q00-20230904009 非发起人关卡，把「保存」按鈕取消 →  把 「儲存表單」的按鈕隱藏                       当发起人取回重办时，需要把【退回修改】的按钮隐藏 → 发起人关卡UserTask_3
- **Commit ID**: `7fc62ab5815de8d394cfee8a6a29e963a49b01f7`
- **作者**: waynechang
- **日期**: 2023-09-04 14:56:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 6. [流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt<>1
- **Commit ID**: `1032fcea1303aee159e22d4a662445cc04c58729`
- **作者**: waynechang
- **日期**: 2023-09-04 14:36:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`

### 7. [流程引擎] Q00-20230904001 調整開啟草稿時，先前上傳附件需一併顯示
- **Commit ID**: `eac9b58c1d420f36b3b9e949b5a531d2b4f7d899`
- **作者**: waynechang
- **日期**: 2023-09-04 14:35:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 8. [Web]Q00-20230904008 流程發起、待辦等相關ICON需要能替換，且繼續派送按鈕需依流程、關卡顯示不同內容
- **Commit ID**: `8d6fdcb0f22fdb14a5cd16f7367ae18149574758`
- **作者**: 林致帆
- **日期**: 2023-09-04 14:32:27
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 9. [Web]Q00-20230904003 調整退回重瓣按鈕不需跳出退回重瓣視窗，改成直接退回
- **Commit ID**: `f6d511540035b7c9dcb991c68ebffbd07d7a2878`
- **作者**: 林致帆
- **日期**: 2023-09-04 13:40:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 10. [Web]Q00-20230904004 新增追蹤流程清單+草稿的Portlet
- **Commit ID**: `ee2bc7ea2dbeb6d6fb94c02424697be8c99db218`
- **作者**: 林致帆
- **日期**: 2023-09-04 13:37:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PortletEntry.jsp`

### 11. [web]Q00-20230901007 追蹤流程頁面預設顯示發起過的流程
- **Commit ID**: `8394203e2fbd8b03f07a3975d0aeae6367b8acdb`
- **作者**: 刘旭
- **日期**: 2023-09-04 11:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 12. [web]Q00-20230901006 側邊一級菜單只展示客戶需要的內容：管理員維特原本MENU，一般使用者 側邊一級菜單只顯示追蹤流程、流程草稿、取回重辦、待办事项及某些客製作業
- **Commit ID**: `cff65c43833e767d62389829fcc25d5b359984a3`
- **作者**: 刘旭
- **日期**: 2023-09-04 11:01:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 13. [Web] Q00-20230901005 使用Porlet进入发起流程界面，发单成功后添加进入待办按钮和页面内容
- **Commit ID**: `479ba4f38b85deee12d5d71c4089c74855f154df`
- **作者**: liuyun
- **日期**: 2023-09-04 10:26:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

