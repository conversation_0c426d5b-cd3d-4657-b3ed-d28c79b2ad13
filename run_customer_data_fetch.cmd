@echo off
echo.
echo ========================================
echo Customer Data Automation Fetch Tool
echo ========================================
echo.

REM Activate virtual environment
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo Warning: Virtual environment not found, using system Python
)

echo.
echo Checking required packages...
python -c "import smbprotocol, chardet, dotenv; print('All packages installed')" 2>nul
if errorlevel 1 (
    echo Missing required packages, please run: install_customer_data_deps.cmd
    pause
    exit /b 1
)

echo.
echo Starting customer data fetch...
echo.
python tools\fetch_customer_data.py

echo.
echo ========================================
echo Execution completed!
echo ========================================
echo.
echo Please check output directory: data_output\bpm_customer_tmp\
echo Failed companies report: data_output\
echo Log files located at: logs\
echo.
pause
