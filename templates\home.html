{% extends "base.html" %}

{% block title %}{{ app_config.title }}{% endblock %}

{% block content %}
<div class="container">
    <!-- 主標題 -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary mb-3">
            <i class="fas fa-tools me-3"></i>
            {{ app_config.title }}
        </h1>
        <p class="lead text-muted">選擇您需要的工具來提升工作效率</p>
    </div>

    <!-- 警告訊息 -->
    {% if warnings %}
    <div class="row mb-4">
        <div class="col-12">
            {% for warning in warnings %}
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {{ warning }}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- 工具卡片 -->
    <div class="row g-4 mb-5">
        <!-- Release 查詢工具 -->
        <div class="col-lg-4 col-md-6">
            <a href="/release" class="tool-card release-tool text-decoration-none">
                <div class="tool-icon">
                    {{ tools_config.release_query.icon }}
                </div>
                <div class="tool-title">
                    {{ tools_config.release_query.name }}
                </div>
                <div class="tool-description">
                    {% for desc in tools_config.release_query.description %}
                    • {{ desc }}<br>
                    {% endfor %}
                </div>
            </a>
        </div>

        <!-- 檔案搜尋工具 -->
        <div class="col-lg-4 col-md-6">
            <a href="/files" class="tool-card search-tool text-decoration-none">
                <div class="tool-icon">
                    {{ tools_config.file_search.icon }}
                </div>
                <div class="tool-title">
                    {{ tools_config.file_search.name }}
                </div>
                <div class="tool-description">
                    {% for desc in tools_config.file_search.description %}
                    • {{ desc }}<br>
                    {% endfor %}
                </div>
            </a>
        </div>

        <!-- 客戶連線管理工具 -->
        <div class="col-lg-4 col-md-6">
            <a href="/customers" class="tool-card customer-tool text-decoration-none">
                <div class="tool-icon">
                    {{ tools_config.customer_connections.icon }}
                </div>
                <div class="tool-title">
                    {{ tools_config.customer_connections.name }}
                </div>
                <div class="tool-description">
                    {% for desc in tools_config.customer_connections.description %}
                    • {{ desc }}<br>
                    {% endfor %}
                </div>
            </a>
        </div>
    </div>

    <!-- 系統資訊 -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        系統資訊
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <i class="fas fa-tag me-2 text-primary"></i>
                                <strong>版本：</strong>{{ app_config.version }}
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-calendar me-2 text-primary"></i>
                                <strong>更新：</strong>{{ app_config.last_update }}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2">
                                <i class="fas fa-building me-2 text-primary"></i>
                                <strong>部門：</strong>{{ app_config.department }}
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-lightbulb me-2 text-primary"></i>
                                <strong>提示：</strong>點擊上方工具卡片開始使用
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用說明 -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="text-center">
                <h3 class="mb-4">
                    <i class="fas fa-question-circle me-2"></i>
                    如何使用
                </h3>
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-mouse-pointer fa-3x text-primary mb-3"></i>
                                <h5>選擇工具</h5>
                                <p class="text-muted">點擊上方的工具卡片選擇您需要的功能</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-cogs fa-3x text-success mb-3"></i>
                                <h5>設定參數</h5>
                                <p class="text-muted">根據需求設定搜尋條件或輸入相關資訊</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                                <h5>查看結果</h5>
                                <p class="text-muted">獲得詳細的查詢結果和相關資訊</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
