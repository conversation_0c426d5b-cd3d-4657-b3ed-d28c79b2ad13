#!/usr/bin/env python3
"""
檔案部署路徑索引生成器 (File Deployment Path Index Generator)

此工具用於掃描WAR和EAR檔案，建立檔案部署路徑的索引資料庫。
主要功能：
- 掃描指定目錄中的WAR和EAR檔案
- 提取其中的class、JSP、JS檔案資訊
- 記錄檔案在部署結構中的完整路徑
- 生成可供查詢工具使用的索引檔案

使用方式：
1. 設定 .env 檔案中的參數
2. 執行 python src/generate_deployment_index.py
3. 索引檔案會儲存在 data_output/bpm_path/版本號_index.json

作者：BPM服務部
版本：1.2.0
"""

import os
import zipfile
import json
from pathlib import Path
from collections import defaultdict
import io
from dotenv import load_dotenv
from datetime import datetime
import sys

# 確保能找到專案根目錄的.env檔案
project_root = Path(__file__).parent.parent
env_path = project_root / "config" / ".env"
if env_path.exists():
    load_dotenv(env_path)
else:
    # 如果config目錄下沒有.env，嘗試根目錄
    load_dotenv(project_root / ".env")

# 驗證必填環境變數是否存在
REQUIRED_ENV_VARS = ["VERSION", "SCAN_PATHS"]
missing_vars = [var for var in REQUIRED_ENV_VARS if not os.getenv(var)]
if missing_vars:
    print(f"❌ 錯誤：請在 .env 中設定 {', '.join(missing_vars)}")
    exit(1)

# 讀取額外目錄設定
extra_dirs = os.getenv("WAR_EXTRA_DIRS", "WMS,OpenWin,js").split(",")
extra_dirs = [d.strip().rstrip("/") + "/" for d in extra_dirs if d.strip()]


def get_modified_time(zip_obj, entry):
    """從壓縮檔中獲取檔案的最後修改時間
    
    Args:
        zip_obj: 壓縮檔物件 (zipfile.ZipFile實例)
        entry: 壓縮檔中的檔案路徑
        
    Returns:
        str: ISO格式的日期時間字串
    """
    info = zip_obj.getinfo(entry)
    dt = datetime(*info.date_time)
    return dt.isoformat()


def extract_class_entries_from_jar(jar_bytes, jar_name, prefix=""):
    """從JAR檔案中提取所有class檔案的資訊
    
    Args:
        jar_bytes: JAR檔案的二進位內容
        jar_name: JAR檔案名稱
        prefix: 路徑前綴，用於標示JAR檔案在上層壓縮檔中的位置
        
    Returns:
        list: 包含(class名稱, 檔案在壓縮檔中的路徑, 最後修改時間)的元組列表
    """
    result = []
    with zipfile.ZipFile(io.BytesIO(jar_bytes), 'r') as jar:
        for entry in jar.namelist():
            if entry.endswith('.class') and not entry.startswith('META-INF/'):
                class_name = entry.replace('/', '.').removesuffix('.class')
                path_in_archive = f"{prefix}{jar_name}!/{entry}"
                modified_time = get_modified_time(jar, entry)
                result.append((class_name, path_in_archive, modified_time))
    return result


def extract_class_entries_from_war(war_path, allowed_war_jars):
    """從WAR檔案中提取所有class、jsp和js檔案的資訊
    
    Args:
        war_path: WAR檔案的路徑
        allowed_war_jars: 允許處理的JAR檔案列表，如果為空則處理所有JAR
        
    Returns:
        list: 包含(檔案名稱, 檔案在壓縮檔中的路徑, 最後修改時間)的元組列表
    """
    result = []
    with zipfile.ZipFile(war_path, 'r') as war:
        # 第一步：處理WAR檔案中的JAR檔案
        for entry in war.namelist():
            if entry.startswith("WEB-INF/lib/") and entry.endswith('.jar'):
                jar_name = os.path.basename(entry)
                if allowed_war_jars and jar_name not in allowed_war_jars:
                    continue
                with war.open(entry) as jar_file:
                    jar_bytes = jar_file.read()
                    result.extend(extract_class_entries_from_jar(jar_bytes, jar_name, "WEB-INF/lib/"))

        # 第二步：處理WAR檔案中的class、jsp和js檔案
        for entry in war.namelist():
            if entry.startswith("WEB-INF/classes/") and entry.endswith('.class'):
                class_name = entry.replace('/', '.').removesuffix('.class')
                modified_time = get_modified_time(war, entry)
                result.append((class_name, entry, modified_time))
            elif any(entry.startswith(d) and entry.endswith(".jsp") for d in extra_dirs):
                modified_time = get_modified_time(war, entry)
                result.append((entry, entry, modified_time))
            elif any(entry.startswith(d) and entry.endswith(".js") for d in extra_dirs):
                modified_time = get_modified_time(war, entry)
                result.append((entry, entry, modified_time))
            elif entry.endswith(".jsp") and '/' not in entry.strip('/'):
                modified_time = get_modified_time(war, entry)
                result.append((entry, entry, modified_time))
    return result


def extract_class_entries_from_ear(ear_path, allowed_ear_jars):
    """從EAR檔案中提取所有JAR檔案中的class檔案資訊
    
    Args:
        ear_path: EAR檔案的路徑
        allowed_ear_jars: 允許處理的JAR檔案列表，如果為空則處理所有JAR
        
    Returns:
        list: 包含(class名稱, 檔案在壓縮檔中的路徑, 最後修改時間)的元組列表
    """
    result = []
    with zipfile.ZipFile(ear_path, 'r') as ear:
        for entry in ear.namelist():
            if entry.endswith('.jar'):
                jar_name = os.path.basename(entry)
                if allowed_ear_jars and jar_name not in allowed_ear_jars:
                    continue
                with ear.open(entry) as jar_file:
                    jar_bytes = jar_file.read()
                    result.extend(extract_class_entries_from_jar(jar_bytes, jar_name))
    return result


def scan_directories(version: str, paths: list, allowed_wars: list, allowed_ears: list, allowed_war_jars: list, allowed_ear_jars: list):
    """掃描指定目錄，建立檔案索引
    
    Args:
        version: 版本號，用於建立輸出目錄
        paths: 要掃描的目錄路徑列表
        allowed_wars: 允許處理的WAR檔案列表，如果為空則處理所有WAR
        allowed_ears: 允許處理的EAR檔案列表，如果為空則處理所有EAR
        allowed_war_jars: 允許處理的WAR中的JAR檔案列表
        allowed_ear_jars: 允許處理的EAR中的JAR檔案列表
    """
    # 使用defaultdict建立索引
    index = defaultdict(list)
    
    # 遍歷所有指定的目錄
    for base_path in paths:
        for root, dirs, files in os.walk(base_path):
            for file in files:
                full_path = Path(root) / file
                try:
                    # 處理WAR檔案
                    if file.endswith(".war"):
                        if allowed_wars and file not in allowed_wars:
                            continue
                        class_entries = extract_class_entries_from_war(full_path, allowed_war_jars)
                        for class_name, path_in_archive, modified_time in class_entries:
                            index[class_name].append({
                                "file": file,
                                "path_in_archive": path_in_archive,
                                "source_type": "war",
                                "modified_time": modified_time
                            })

                    # 處理EAR檔案
                    elif file.endswith(".ear"):
                        if allowed_ears and file not in allowed_ears:
                            continue
                        class_entries = extract_class_entries_from_ear(full_path, allowed_ear_jars)
                        for class_name, path_in_archive, modified_time in class_entries:
                            index[class_name].append({
                                "file": file,
                                "path_in_archive": path_in_archive,
                                "source_type": "ear",
                                "modified_time": modified_time
                            })
                except Exception as e:
                    print(f"[錯誤] 處理 {full_path} 時發生例外: {e}")

    # 建立輸出目錄並寫入索引檔案
    output_dir = project_root / "data_output" / "bpm_path"
    output_dir.mkdir(parents=True, exist_ok=True)

    # 檔名格式改為 [version]_index.json
    index_filename = f"{version}_index.json"
    index_filepath = output_dir / index_filename

    with open(index_filepath, "w", encoding="utf-8") as f:
        json.dump(index, f, indent=2, ensure_ascii=False)
    print(f"✅ 索引完成：{index_filepath}")


if __name__ == '__main__':
    # 從環境變數讀取設定
    version = os.getenv("VERSION")
    paths = os.getenv("SCAN_PATHS", "").split(";")
    include_wars = os.getenv("INCLUDE_WARS", "").split(";")
    include_ears = os.getenv("INCLUDE_EARS", "").split(";")
    include_war_jars = os.getenv("INCLUDE_WAR_JARS", "").split(";")
    include_ear_jars = os.getenv("INCLUDE_EAR_JARS", "").split(";")
    force_rebuild = os.getenv("FORCE_REBUILD", "false").lower() == "true"

    # 檢查索引檔是否已存在
    index_file = project_root / "data_output" / "bpm_path" / f"{version}_index.json"
    if index_file.exists() and not force_rebuild:
        print(f"⚠️ 版本 {version} 的索引檔已存在，若要重新建立請設定 FORCE_REBUILD=true")
    else:
        scan_directories(
            version,
            paths,
            [w for w in include_wars if w],
            [e for e in include_ears if e],
            [j for j in include_war_jars if j],
            [j for j in include_ear_jars if j]
        )
