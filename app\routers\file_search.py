"""
檔案搜尋路由
"""
from fastapi import APIRouter, Request, Query, Form, File, UploadFile
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from pathlib import Path
import json
import zipfile
import rarfile
import tempfile
import os
from typing import Optional, List, Dict, Any
import sys

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.config import BPM_PATH_DIR

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/", response_class=HTMLResponse)
async def file_search_page(request: Request):
    """檔案搜尋頁面"""
    # 取得可用的版本列表
    versions = get_versions()
    
    context = {
        "request": request,
        "page_title": "檔案索引路徑查詢工具",
        "versions": versions
    }
    
    return templates.TemplateResponse("files/index.html", context)

@router.post("/search", response_class=JSONResponse)
async def search_files(
    version: str = Form(...),
    search_query: str = Form(...),
    search_type: str = Form("fuzzy")  # fuzzy 或 exact
):
    """搜尋檔案"""
    try:
        # 載入指定版本的索引
        index_data = load_index(version)
        
        if not index_data:
            return {"error": f"找不到版本 {version} 的索引資料"}
        
        # 執行搜尋
        results = perform_search(index_data, search_query, search_type)
        
        return {
            "success": True,
            "data": results,
            "total": len(results),
            "version": version,
            "query": search_query
        }
        
    except Exception as e:
        return {"error": f"搜尋時發生錯誤: {str(e)}"}

@router.post("/batch-search", response_class=JSONResponse)
async def batch_search_files(
    version: str = Form(...),
    file: UploadFile = File(...)
):
    """批量搜尋檔案（從上傳的壓縮檔）"""
    try:
        # 從壓縮檔提取檔案名稱
        file_names = extract_file_names_from_archive(file)
        
        if not file_names:
            return {"error": "壓縮檔中沒有找到支援的檔案類型（.class, .jsp, .js）"}
        
        # 載入指定版本的索引
        index_data = load_index(version)
        
        if not index_data:
            return {"error": f"找不到版本 {version} 的索引資料"}
        
        # 批量搜尋
        batch_results = {}
        for file_name in file_names:
            results = perform_search(index_data, file_name, "exact")
            if results:
                batch_results[file_name] = results
        
        return {
            "success": True,
            "data": batch_results,
            "total_files": len(file_names),
            "found_files": len(batch_results),
            "version": version
        }
        
    except Exception as e:
        return {"error": f"批量搜尋時發生錯誤: {str(e)}"}

@router.get("/versions")
async def get_available_versions():
    """取得可用的版本列表"""
    versions = get_versions()
    return {"versions": versions}

def get_versions() -> List[str]:
    """從資料目錄中獲取所有版本索引檔案"""
    if not BPM_PATH_DIR.exists():
        return []
    
    index_files = list(BPM_PATH_DIR.glob("*_index.json"))
    versions = [f.stem.replace("_index", "") for f in index_files]
    return sorted(versions, reverse=True)

def load_index(version: str) -> Dict[str, Any]:
    """載入指定版本的索引檔案"""
    index_file = BPM_PATH_DIR / f"{version}_index.json"
    
    if not index_file.exists():
        return {}
    
    try:
        with open(index_file, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return {}

def perform_search(index_data: Dict, query: str, search_type: str) -> List[Dict]:
    """執行檔案搜尋"""
    results = []
    query_lower = query.lower()
    
    for file_name, file_info_list in index_data.items():
        if search_type == "exact":
            # 精確搜尋（檔案名稱完全匹配）
            if query_lower == file_name.lower():
                for file_info in file_info_list:
                    results.append({
                        "file_name": file_name,
                        "path": file_info.get("path", ""),
                        "modified_time": file_info.get("modified_time", ""),
                        "size": file_info.get("size", 0)
                    })
        else:
            # 模糊搜尋（檔案名稱包含查詢字串）
            if query_lower in file_name.lower():
                for file_info in file_info_list:
                    results.append({
                        "file_name": file_name,
                        "path": file_info.get("path", ""),
                        "modified_time": file_info.get("modified_time", ""),
                        "size": file_info.get("size", 0)
                    })
    
    return results

def extract_file_names_from_archive(uploaded_file: UploadFile) -> List[str]:
    """從壓縮檔中提取檔案名稱"""
    result = set()
    
    with tempfile.TemporaryDirectory() as tmpdir:
        temp_path = Path(tmpdir) / uploaded_file.filename
        
        # 寫入臨時檔案
        with open(temp_path, "wb") as f:
            f.write(uploaded_file.file.read())
        
        try:
            # 處理 ZIP 檔案
            if uploaded_file.filename.endswith(".zip"):
                with zipfile.ZipFile(temp_path, 'r') as zf:
                    for name in zf.namelist():
                        if name.endswith(('.class', '.jsp', '.js')):
                            base = os.path.basename(name)
                            # 移除副檔名
                            base_name = base.replace(".class", "").replace(".jsp", "").replace(".js", "")
                            if base_name:
                                result.add(base_name)
            
            # 處理 RAR 檔案
            elif uploaded_file.filename.endswith(".rar"):
                with rarfile.RarFile(temp_path, 'r') as rf:
                    for info in rf.infolist():
                        if info.filename.endswith(('.class', '.jsp', '.js')):
                            base = os.path.basename(info.filename)
                            # 移除副檔名
                            base_name = base.replace(".class", "").replace(".jsp", "").replace(".js", "")
                            if base_name:
                                result.add(base_name)
        
        except Exception as e:
            print(f"處理壓縮檔時發生錯誤: {e}")
    
    return list(result)
