# Release Notes - NaNaXWeb

## 版本資訊
- **新版本**: 5.8.10.2_hotfix
- **舊版本**: 5.8.10.2_202406261435_autobuild
- **生成時間**: 2025-07-18 15:49:00
- **新增 Commit 數量**: 13

## 變更摘要

### yamiyeh10 (9 commits)

- **2025-02-03 15:20:27**: [PRODT]C01-20250123003 修正Web流程管理工具中同時開啟多個流程時，進行其中一個儲存動作後會導致流程無法正常關閉問題
  - 變更檔案: 1 個
- **2024-11-21 17:03:19**: [PRODT]C01-20241120001 修正Web流程管理工具中匯入流程時代號原本不存在，但修改流程代號後遇到已存在代號卻無法覆蓋的問題
  - 變更檔案: 1 個
- **2024-10-29 15:14:55**: [PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題
  - 變更檔案: 1 個
- **2024-10-09 14:06:59**: [PRODT]C01-20241008003 修正Web流程管理工具中連接線名稱在儲存後會消失的問題
  - 變更檔案: 1 個
- **2024-09-26 08:21:33**: [PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況
  - 變更檔案: 1 個
- **2024-09-20 09:03:16**: [PRODT]Q00-20240626002 修正Web流程管理工具中關卡與連接線存在髒資料卻無提示的問題
  - 變更檔案: 1 個
- **2024-06-25 17:05:06**: [PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題
  - 變更檔案: 2 個
- **2024-06-26 10:21:15**: [內部]A00-20240625001 NG-Zorro套件引入越南語系
  - 變更檔案: 3 個
- **2024-09-03 15:42:36**: [PRODT]Q00-20240828001 調整Web流程管理工具中註解元件無法被拖拉的問題
  - 變更檔案: 1 個

### lorenchang (3 commits)

- **2024-11-07 11:00:59**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)
  - 變更檔案: 2 個
- **2024-10-28 10:58:05**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)
  - 變更檔案: 2 個
- **2024-10-04 17:16:39**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log
  - 變更檔案: 3 個

### 邱郁晏 (1 commits)

- **2024-07-12 10:23:15**: [流程封存] C01-20240506005 調整流程封存維護作業日期儲存計算方式
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [PRODT]C01-20250123003 修正Web流程管理工具中同時開啟多個流程時，進行其中一個儲存動作後會導致流程無法正常關閉問題
- **Commit ID**: `cf100917c719ef17bdacbf4b4125327189b7180d`
- **作者**: yamiyeh10
- **日期**: 2025-02-03 15:20:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 2. [PRODT]C01-20241120001 修正Web流程管理工具中匯入流程時代號原本不存在，但修改流程代號後遇到已存在代號卻無法覆蓋的問題
- **Commit ID**: `a83bedafa74fbf29820a798a9a244a3f09b479e4`
- **作者**: yamiyeh10
- **日期**: 2024-11-21 17:03:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 3. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)
- **Commit ID**: `b74d2797568b77968befe3244f150c45f8a45cdb`
- **作者**: lorenchang
- **日期**: 2024-11-07 11:00:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/util/init/TimeScheduleInitializer.java`

### 4. [PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題
- **Commit ID**: `245019dd8b31b4ba69ee2f655213449e82b7de4d`
- **作者**: yamiyeh10
- **日期**: 2024-10-29 15:14:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.ts`

### 5. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)
- **Commit ID**: `05e661484afcd39a18d0430d42b09f35264cb1d0`
- **作者**: lorenchang
- **日期**: 2024-10-28 10:58:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java`

### 6. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log
- **Commit ID**: `368a57890a882d339ad96b7a904f680682688cef`
- **作者**: lorenchang
- **日期**: 2024-10-04 17:16:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java`

### 7. [流程封存] C01-20240506005 調整流程封存維護作業日期儲存計算方式
- **Commit ID**: `67540bc441eedc75db76b39bc4db025bc0d0b9a2`
- **作者**: 邱郁晏
- **日期**: 2024-07-12 10:23:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java`

### 8. [PRODT]C01-20241008003 修正Web流程管理工具中連接線名稱在儲存後會消失的問題
- **Commit ID**: `f8dfd08cafab9191cfc1a3ccc4ddbddc75c75b69`
- **作者**: yamiyeh10
- **日期**: 2024-10-09 14:06:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 9. [PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況
- **Commit ID**: `7e8221c9cc70810c192b1342198c660b0a61f906`
- **作者**: yamiyeh10
- **日期**: 2024-09-26 08:21:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 10. [PRODT]Q00-20240626002 修正Web流程管理工具中關卡與連接線存在髒資料卻無提示的問題
- **Commit ID**: `393a292592e303efbeee5f81121ca499941c65dd`
- **作者**: yamiyeh10
- **日期**: 2024-09-20 09:03:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 11. [PRODT]C01-20240605009 修正Web流程管理工具當流程模型定義識別碼與關卡ID命名一致時會發生關卡消失問題
- **Commit ID**: `6bd4962bfc06b49fb4c0d214d18b278e95da4aef`
- **作者**: yamiyeh10
- **日期**: 2024-06-25 17:05:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/bpmn-auto-layout/dist/index.cjs`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 12. [內部]A00-20240625001 NG-Zorro套件引入越南語系
- **Commit ID**: `422489779ba9fdd1ecd58ec1cb3da942836d8e50`
- **作者**: yamiyeh10
- **日期**: 2024-06-26 10:21:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/app.module.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/app.module.ts`
  - 📝 **修改**: `AngularProjects/ProcessArchiveModule/src/app/app.module.ts`

### 13. [PRODT]Q00-20240828001 調整Web流程管理工具中註解元件無法被拖拉的問題
- **Commit ID**: `0df1989eb0bdeecdc178d3fe4f18fa2104fdb762`
- **作者**: yamiyeh10
- **日期**: 2024-09-03 15:42:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-custom/customRules.js`

