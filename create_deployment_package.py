#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BPM Easy Tools 部署打包工具
自動建立部署用的壓縮檔案，排除不必要的檔案
"""

import os
import sys
import json
import shutil
import zipfile
from pathlib import Path
from datetime import datetime
import argparse


class DeploymentPackager:
    """部署打包工具類別"""
    
    def __init__(self, project_root=None):
        """
        初始化打包工具
        
        Args:
            project_root (str, optional): 專案根目錄路徑，預設為當前目錄
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent
        self.config_file = self.project_root / "config" / "projects_config.json"
        self.config = self._load_config()
        
    def _load_config(self):
        """載入設定檔案"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ 找不到設定檔案: {self.config_file}")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"❌ 設定檔案格式錯誤: {e}")
            sys.exit(1)
    
    def _should_exclude(self, file_path, exclude_patterns):
        """
        檢查檔案是否應該被排除
        
        Args:
            file_path (Path): 檔案路徑
            exclude_patterns (list): 排除模式清單
            
        Returns:
            bool: True 如果應該排除，False 否則
        """
        file_str = str(file_path).replace('\\', '/')
        relative_path = file_path.relative_to(self.project_root)
        relative_str = str(relative_path).replace('\\', '/')
        
        for pattern in exclude_patterns:
            pattern = pattern.replace('\\', '/')
            
            # 檢查完整路徑
            if pattern in file_str or pattern in relative_str:
                return True
                
            # 檢查檔案名稱
            if pattern.startswith('*.') and file_path.name.endswith(pattern[1:]):
                return True
                
            # 檢查目錄名稱
            if pattern.endswith('/') and (pattern[:-1] in file_str or pattern[:-1] in relative_str):
                return True
                
        return False
    
    def _get_files_to_include(self):
        """
        取得需要包含的檔案清單
        
        Returns:
            list: 檔案路徑清單
        """
        deployment_config = self.config.get('deployment', {})
        exclude_patterns = deployment_config.get('exclude_patterns', [])
        include_files = deployment_config.get('include_files', [])
        include_directories = deployment_config.get('include_directories', [])
        
        files_to_include = []
        
        # 加入指定的檔案
        for file_name in include_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                files_to_include.append(file_path)
                print(f"✅ 包含檔案: {file_name}")
            else:
                print(f"⚠️  檔案不存在: {file_name}")
        
        # 加入指定的目錄
        for dir_name in include_directories:
            dir_path = self.project_root / dir_name.rstrip('/')
            if dir_path.exists() and dir_path.is_dir():
                print(f"📁 掃描目錄: {dir_name}")
                for root, dirs, files in os.walk(dir_path):
                    root_path = Path(root)
                    
                    # 檢查目錄是否應該被排除
                    if self._should_exclude(root_path, exclude_patterns):
                        print(f"❌ 排除目錄: {root_path.relative_to(self.project_root)}")
                        dirs.clear()  # 不遞迴進入此目錄
                        continue
                    
                    for file in files:
                        file_path = root_path / file
                        if not self._should_exclude(file_path, exclude_patterns):
                            files_to_include.append(file_path)
                        else:
                            print(f"❌ 排除檔案: {file_path.relative_to(self.project_root)}")
            else:
                print(f"⚠️  目錄不存在: {dir_name}")
        
        return files_to_include
    
    def create_package(self, output_dir=None, package_name=None):
        """
        建立部署包
        
        Args:
            output_dir (str, optional): 輸出目錄，預設為專案根目錄
            package_name (str, optional): 包名稱，預設從設定檔讀取
            
        Returns:
            str: 建立的壓縮檔路徑
        """
        # 設定輸出目錄和檔案名稱
        if output_dir is None:
            output_dir = self.project_root
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        
        if package_name is None:
            package_name = self.config.get('deployment', {}).get('package_name', 'bpm_easy_tools_deployment')
        
        # 加入時間戳記
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        zip_filename = f"{package_name}_{timestamp}.zip"
        zip_path = output_dir / zip_filename
        
        print(f"🚀 開始建立部署包...")
        print(f"📦 輸出檔案: {zip_path}")
        print("=" * 60)
        
        # 取得要包含的檔案
        files_to_include = self._get_files_to_include()
        
        if not files_to_include:
            print("❌ 沒有找到要包含的檔案")
            return None
        
        # 建立壓縮檔
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 加入一般檔案
            for file_path in files_to_include:
                # 計算相對路徑
                arcname = file_path.relative_to(self.project_root)
                zipf.write(file_path, arcname)
                print(f"✅ 已加入: {arcname}")

            # 動態生成部署說明檔案並加入壓縮檔
            deployment_instructions = self.generate_deployment_instructions_content()
            zipf.writestr("部署說明.txt", deployment_instructions.encode('utf-8'))
            print(f"✅ 已加入: 部署說明.txt (動態生成)")
        
        # 顯示統計資訊
        file_size = zip_path.stat().st_size
        file_size_mb = file_size / (1024 * 1024)
        
        print("=" * 60)
        print(f"🎉 部署包建立完成!")
        print(f"📁 檔案位置: {zip_path}")
        print(f"📊 檔案大小: {file_size_mb:.2f} MB")
        print(f"📋 包含檔案數量: {len(files_to_include) + 1} (含動態生成的部署說明)")
        
        return str(zip_path)
    
    def generate_deployment_instructions_content(self):
        """
        生成部署說明內容（完全內嵌在程式中）
        動態生成檔案結構部分
        """
        # 動態取得設定檔中的檔案清單
        deployment_config = self.config.get('deployment', {})
        include_files = deployment_config.get('include_files', [])
        include_directories = deployment_config.get('include_directories', [])

        # 生成檔案結構說明
        file_structure = []

        # 加入主要檔案
        for file_name in include_files:
            if file_name == "streamlit_home.py":
                file_structure.append("streamlit_home.py              # 主程式")
            elif file_name == "requirements.txt":
                file_structure.append("requirements.txt              # Python 依賴套件")
            elif file_name == "setup_environment.cmd":
                file_structure.append("setup_environment.cmd        # 環境設定")
            elif file_name == "start_application.cmd":
                file_structure.append("start_application.cmd         # 啟動應用")
            elif file_name == "deploy_to_target.py":
                file_structure.append("deploy_to_target.py           # 目標主機部署工具")
            elif file_name == "README.md":
                file_structure.append("README.md                     # 完整使用說明")
            elif file_name == "部署說明.txt":
                # 跳過，因為這個檔案是動態生成的
                continue
            else:
                file_structure.append(f"{file_name}")

        # 加入目錄
        for dir_name in include_directories:
            dir_clean = dir_name.rstrip('/')
            if dir_clean == "pages":
                file_structure.append("pages/                         # 頁面檔案")
                file_structure.append("├── file_search.py            # 檔案搜尋頁面")
                file_structure.append("└── release_query.py          # Release 查詢頁面")
            elif dir_clean == "tools":
                file_structure.append("tools/                        # 工具腳本")
                file_structure.append("├── smart_branch_diff.py      # 分支差異分析")
                file_structure.append("└── generate_deployment_index.py # 部署索引生成")
            elif dir_clean == "config":
                file_structure.append("config/                       # 設定檔案")
                file_structure.append("└── projects_config.json      # 專案設定")
            elif dir_clean == "data_output":
                file_structure.append("data_output/                  # 資料輸出目錄")
                file_structure.append("├── bpm_path/                 # 檔案路徑索引")
                file_structure.append("└── bpm_release/              # Release 記錄")
            elif dir_clean == ".streamlit":
                file_structure.append(".streamlit/                   # Streamlit 設定")
                file_structure.append("└── config.toml               # 應用程式設定檔")
            else:
                file_structure.append(f"{dir_clean}/")

        file_structure_text = "\n".join(file_structure)

        # 完整的部署說明內容（內嵌在程式中）
        instructions_content = f"""BPM Easy Tools 部署說明
========================

部署步驟:
--------
1. 將壓縮檔解壓縮到目標主機的任意目錄

2. 確保目標主機已安裝 Python 3.8 或以上版本
   - 下載網址: https://www.python.org/downloads/

3. 開啟命令提示字元，切換到解壓縮的目錄

4. 執行環境設定:
   setup_environment.cmd

5. 啟動應用:
   start_application.cmd

6. 開啟瀏覽器，訪問顯示的網址 (通常是 http://localhost:8888)

注意事項:
--------
- 如果 config/projects_config.json 中的路徑不正確，請手動修改
- 確保 data/ 和 output/ 目錄有適當的讀寫權限
- 如果遇到套件安裝問題，可手動執行: pip install -r requirements.txt

檔案結構:
--------
{file_structure_text}

疑難排解:
--------
1. 如果無法啟動，檢查 Python 是否正確安裝
2. 如果套件安裝失敗，嘗試升級 pip: python -m pip install --upgrade pip
3. 如果遇到編碼問題，確保系統支援 UTF-8 編碼
4. 詳細說明請參考 README.md 檔案

聯絡資訊:
--------
如有問題請聯繫 BPM 服務部開發團隊
"""
        return instructions_content


def main():
    """主程式"""
    parser = argparse.ArgumentParser(description='BPM Easy Tools 部署打包工具')
    parser.add_argument('--output', '-o', help='輸出目錄路徑')
    parser.add_argument('--name', '-n', help='包名稱')
    parser.add_argument('--project-root', '-p', help='專案根目錄路徑')
    
    args = parser.parse_args()

    try:
        packager = DeploymentPackager(args.project_root)

        # 建立部署包（部署說明會動態生成到壓縮檔中）
        zip_path = packager.create_package(args.output, args.name)

        if zip_path:
            print("\n🎯 部署包建立完成，請參考壓縮檔中的「部署說明.txt」進行部署")
        
    except KeyboardInterrupt:
        print("\n❌ 使用者中斷操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
