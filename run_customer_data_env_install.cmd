@echo off
echo.
echo ========================================
echo Customer Data Fetch Tool - Install Dependencies
echo ========================================
echo.

echo Installing required packages...
echo.

REM Check if we're already in virtual environment
if exist "venv\Scripts\activate.bat" (
    echo Found virtual environment, activating...
    call venv\Scripts\activate.bat
) else (
    echo Warning: Virtual environment not found, using system Python
)

echo.
echo Installing smbprotocol...
pip install smbprotocol>=1.12.0

echo.
echo Installing chardet...
pip install chardet>=5.2.0

echo.
echo Installing python-dotenv...
pip install python-dotenv>=1.0.0

echo.
echo ========================================
echo Installation completed!
echo ========================================
echo.
echo Next steps:
echo 1. Check SMB settings in config/.env file
echo 2. Run fetch: python tools/fetch_customer_data.py
echo 3. Generate report only: python tools/fetch_customer_data.py --report-only
echo.
pause
