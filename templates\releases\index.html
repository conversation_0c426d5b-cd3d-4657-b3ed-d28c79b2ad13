{% extends "base.html" %}

{% block title %}{{ page_title }} - BPM服務部好用工具{% endblock %}

{% block extra_css %}
<style>
    .search-form {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .results-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        min-height: 400px;
    }
    
    .release-item {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    
    .release-item:hover {
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .release-hash {
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.9rem;
    }
    
    .file-list {
        max-height: 200px;
        overflow-y: auto;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .file-item {
        padding: 0.25rem 0;
        border-bottom: 1px solid #e9ecef;
        font-size: 0.9rem;
    }
    
    .file-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fas fa-chart-line me-3"></i>
                {{ page_title }}
            </h1>
            <p class="page-subtitle">查詢各產品的Release記錄和版本資訊</p>
        </div>
    </div>

    <!-- 搜尋表單 -->
    <div class="search-form">
        <form id="searchForm">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="project" class="form-label">專案</label>
                    <select class="form-select" id="project" name="project" required>
                        <option value="">請選擇專案</option>
                        {% for project in projects %}
                        <option value="{{ project }}">{{ project }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="branch_filter" class="form-label">分支過濾</label>
                    <select class="form-select" id="branch_filter" name="branch_filter">
                        <option value="">所有分支</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="keyword_search" class="form-label">關鍵字搜尋</label>
                    <input type="text" class="form-control" id="keyword_search" name="keyword_search" 
                           placeholder="搜尋commit訊息或作者">
                </div>
                
                <div class="col-md-3">
                    <label for="file_search" class="form-label">檔案名稱</label>
                    <input type="text" class="form-control" id="file_search" name="file_search" 
                           placeholder="搜尋檔案名稱">
                </div>
            </div>
            
            <div class="row g-3 mt-3">
                <div class="col-md-3">
                    <label for="sort_by" class="form-label">排序方式</label>
                    <select class="form-select" id="sort_by" name="sort_by">
                        <option value="date">日期</option>
                        <option value="branch">分支</option>
                        <option value="author">作者</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="sort_order" class="form-label">排序順序</label>
                    <select class="form-select" id="sort_order" name="sort_order">
                        <option value="desc">降序</option>
                        <option value="asc">升序</option>
                    </select>
                </div>
                
                <div class="col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>搜尋
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="clearForm">
                        <i class="fas fa-times me-2"></i>清除
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- 載入中 -->
    <div class="loading" id="loading">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">載入中...</span>
        </div>
        <p class="mt-2">正在搜尋資料...</p>
    </div>

    <!-- 搜尋結果 -->
    <div class="results-container" id="resultsContainer">
        <div class="text-center text-muted">
            <i class="fas fa-search fa-3x mb-3"></i>
            <p>請選擇專案並設定搜尋條件，然後點擊搜尋按鈕</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 專案選擇變更時載入分支列表
    $('#project').change(function() {
        const project = $(this).val();
        if (project) {
            loadBranches(project);
        } else {
            $('#branch_filter').html('<option value="">所有分支</option>');
        }
    });

    // 搜尋表單提交
    $('#searchForm').submit(function(e) {
        e.preventDefault();
        performSearch();
    });

    // 清除表單
    $('#clearForm').click(function() {
        $('#searchForm')[0].reset();
        $('#branch_filter').html('<option value="">所有分支</option>');
        showDefaultMessage();
    });

    function loadBranches(project) {
        $.get(`/release/branches/${project}`)
            .done(function(data) {
                if (data.branches) {
                    let options = '<option value="">所有分支</option>';
                    data.branches.forEach(function(branch) {
                        options += `<option value="${branch}">${branch}</option>`;
                    });
                    $('#branch_filter').html(options);
                }
            })
            .fail(function() {
                console.error('載入分支列表失敗');
            });
    }

    function performSearch() {
        const formData = new FormData($('#searchForm')[0]);
        
        $('#loading').show();
        $('#resultsContainer').hide();

        $.ajax({
            url: '/release/search',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false
        })
        .done(function(data) {
            if (data.success) {
                displayResults(data.data, data.total);
            } else {
                showError(data.error);
            }
        })
        .fail(function() {
            showError('搜尋時發生網路錯誤');
        })
        .always(function() {
            $('#loading').hide();
            $('#resultsContainer').show();
        });
    }

    function displayResults(releases, total) {
        if (releases.length === 0) {
            $('#resultsContainer').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <p>沒有找到符合條件的記錄</p>
                </div>
            `);
            return;
        }

        let html = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>搜尋結果 (共 ${total} 筆)</h5>
            </div>
        `;

        releases.forEach(function(release) {
            html += `
                <div class="release-item">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="mb-2">${release.message || '無訊息'}</h6>
                            <p class="text-muted mb-1">
                                <i class="fas fa-user me-2"></i>作者：${release.author || '未知'}
                                <i class="fas fa-calendar ms-3 me-2"></i>日期：${release.date || '未知'}
                            </p>
                            <p class="text-muted mb-1">
                                <i class="fas fa-code-branch me-2"></i>分支：${release.branch || '未知'}
                                <span class="release-hash ms-3">${release.hash || '未知'}</span>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-primary btn-sm" onclick="showFiles('${release.hash}')">
                                <i class="fas fa-file me-2"></i>查看檔案 (${release.files ? release.files.length : 0})
                            </button>
                        </div>
                    </div>
                    ${release.files && release.files.length > 0 ? `
                        <div class="file-list" id="files-${release.hash}" style="display: none;">
                            ${release.files.map(file => `<div class="file-item">${file}</div>`).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
        });

        $('#resultsContainer').html(html);
    }

    function showFiles(hash) {
        $(`#files-${hash}`).toggle();
    }

    function showError(message) {
        $('#resultsContainer').html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `);
    }

    function showDefaultMessage() {
        $('#resultsContainer').html(`
            <div class="text-center text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>請選擇專案並設定搜尋條件，然後點擊搜尋按鈕</p>
            </div>
        `);
    }

    // 全域函數
    window.showFiles = function(hash) {
        $(`#files-${hash}`).toggle();
    };
});
</script>
{% endblock %}
