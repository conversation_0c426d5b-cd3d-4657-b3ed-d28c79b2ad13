# Release Notes - BPM-ISO

## 版本資訊
- **新版本**: release_8.1.1.1
- **舊版本**: release_5.8.10.4
- **生成時間**: 2025-07-18 11:45:14
- **新增 Commit 數量**: 8

## 變更摘要

### 周权 (3 commits)

- **2025-03-06 13:45:18**: [ISO]C01-20250305001 ISO文管首页树状作业，全文检索查询报错
  - 變更檔案: 1 個
- **2025-02-25 13:26:03**: [ISO]C01-20250224007 判斷機密等級新增防呆
  - 變更檔案: 1 個
- **2024-12-20 14:27:05**: [ISO]C01-20241219002 修正浮水印開窗撈不到type為null的資料
  - 變更檔案: 1 個

### lorenchang (4 commits)

- **2025-03-05 10:18:03**: [ISO]C01-20250227003 修正PDF閱讀畫面下載發佈檔會重覆下載2次
  - 變更檔案: 1 個
- **2025-02-21 16:32:17**: [ISO]C01-20250220004 修正文件類別有不存在的群組會導致一般使用者打開文件類別管理出現異常訊息：build tree error
  - 變更檔案: 1 個
- **2025-01-08 09:12:30**: [B2B]S00-20241231001_B2B模組新增獨立的「PDF浮水印屬性管理」功能供文件攜出使用(地)
  - 變更檔案: 12 個
- **2024-12-11 17:39:20**: [內部]增加ISO生失效排程預計作廢的Debug Log
  - 變更檔案: 1 個

### kmin (1 commits)

- **2025-02-21 08:31:04**: [ISO]C01-20250219002 修正評審規則定義觸發Revise流程名稱異常
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [ISO]C01-20250305001 ISO文管首页树状作业，全文检索查询报错
- **Commit ID**: `bb9831095e2b52ae0f91981389d57c0c2fcc484a`
- **作者**: 周权
- **日期**: 2025-03-06 13:45:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp`

### 2. [ISO]C01-20250227003 修正PDF閱讀畫面下載發佈檔會重覆下載2次
- **Commit ID**: `9d6bcfa5f24de0840390eca23a495ddcafdc7f69`
- **作者**: lorenchang
- **日期**: 2025-03-05 10:18:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs`

### 3. [ISO]C01-20250224007 判斷機密等級新增防呆
- **Commit ID**: `cc65e2a51fe4525b24d3e46e569572bb11c5c70c`
- **作者**: 周权
- **日期**: 2025-02-25 13:26:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/AbsAccessRight.java`

### 4. [ISO]C01-20250220004 修正文件類別有不存在的群組會導致一般使用者打開文件類別管理出現異常訊息：build tree error
- **Commit ID**: `152a6743ae59efba6b48e6fb056e253de6adcb01`
- **作者**: lorenchang
- **日期**: 2025-02-21 16:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOAuthorityDaoImpl.java`

### 5. [ISO]C01-20250219002 修正評審規則定義觸發Revise流程名稱異常
- **Commit ID**: `8d009fa10c94ff576003a8168d91763efbb93134`
- **作者**: kmin
- **日期**: 2025-02-21 08:31:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOVettingRuleController.java`

### 6. [B2B]S00-20241231001_B2B模組新增獨立的「PDF浮水印屬性管理」功能供文件攜出使用(地)
- **Commit ID**: `67e4c0008b8726358bd0eb273d2378831548f0e6`
- **作者**: lorenchang
- **日期**: 2025-01-08 09:12:30
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/ModuleForm/UpdateDocumentInfo.jsp`
  - 📝 **修改**: `ISOModule/WebContent/ISOModule/isoPortability/ISOCloudWatermarkPattern.jsp`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreate.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOCreateManager.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOMod.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOPortability.js`
  - 📝 **修改**: `ISOModule/WebContent/RWDFormJs/ISOPortabilityB2B.js`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.hbm.xml`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java`
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java`

### 7. [ISO]C01-20241219002 修正浮水印開窗撈不到type為null的資料
- **Commit ID**: `ae0f540fc46b4a97c2f908eb9fcb590330dac1e8`
- **作者**: 周权
- **日期**: 2024-12-20 14:27:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java`

### 8. [內部]增加ISO生失效排程預計作廢的Debug Log
- **Commit ID**: `5c55ba4d86c2555c0e789f42928cab28a6d8464a`
- **作者**: lorenchang
- **日期**: 2024-12-11 17:39:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java`

