# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.10.4
- **舊版本**: release_5.8.10.3
- **生成時間**: 2025-07-18 11:25:15
- **新增 Commit 數量**: 122

## 變更摘要

### kmin (31 commits)

- **2024-12-18 14:50:52**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]
  - 變更檔案: 1 個
- **2024-12-05 14:08:32**: [Web]C01-20241125013 修正复杂SQL多组“order by”报错，优化包子查询[補]
  - 變更檔案: 1 個
- **2024-12-04 11:15:54**: [Web]C01-20241125017 優化Grid元件排序加入數值判斷
  - 變更檔案: 1 個
- **2024-11-26 11:26:20**: [Web]C01-20241125002 修正當流程定義有設定主旨範本時重發新流程未帶預設值
  - 變更檔案: 1 個
- **2024-11-26 10:57:58**: [Web]C01-20241125011 修正離職作業維護查詢條件日期結束時間改成當天的最後時間
  - 變更檔案: 1 個
- **2024-11-21 14:26:10**: [Web]C01-20241119005 優化T100查看BPM簽核流程卡控未登入狀況的錯誤訊息[補]
  - 變更檔案: 1 個
- **2024-11-21 14:10:17**: [Web]C01-20241119005 優化T100查看BPM簽核流程卡控未登入狀況的錯誤訊息
  - 變更檔案: 1 個
- **2024-11-20 13:51:17**: [ISO]C01-20241118008 修正ISO作廢單缺少記錄version造成ISOformSave儲存異常
  - 變更檔案: 1 個
- **2024-11-15 15:03:13**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-11-15 14:54:21**: [Web]C01-20241114002 修正關卡設定信件有直接簽核網址且必填簽核意見未卡控為空異常問題
  - 變更檔案: 1 個
- **2024-11-14 09:15:28**: [流程引擎]C01-20241112005 優化加簽關卡後派送失敗log訊息不明確
  - 變更檔案: 1 個
- **2024-11-14 09:07:28**: [PRODT]C01-20241111007 卡控核決關卡缺少ActivitySetDefinitions資料
  - 變更檔案: 1 個
- **2024-11-05 16:33:21**: [Web表單設計師]C01-20241104007 修正pairId重覆多組導致元件消失
  - 變更檔案: 1 個
- **2024-11-01 14:45:57**: [Web]C01-20241030003 修正流程草稿因表單新增SerialNumber元件進版後造成無法開啟呈現空白
  - 變更檔案: 1 個
- **2024-10-29 16:12:55**: [內部]Q00-20241029001 優化寄信mail log的記錄判讀
  - 變更檔案: 1 個
- **2024-10-28 10:58:22**: [流程引擎]C01-20241025004 修正改派待辦通知信內容簽核歷程缺失問題
  - 變更檔案: 1 個
- **2024-10-24 17:36:18**: [流程引擎]C01-20241022005 修正呼叫RESTful的發起流程絕對表單單身資料缺失問題
  - 變更檔案: 1 個
- **2024-10-22 10:59:16**: [Web]C01-20241021005 註解關閉表單Grid元件loadGridInfo的console.log訊息
  - 變更檔案: 4 個
- **2024-10-21 11:56:01**: [Web]C01-20240806002 singleOpenWin支援回收AdditionalCriteria參數是否包子查詢加上額外條件作為判斷供客戶使用
  - 變更檔案: 3 個
- **2024-10-17 13:47:47**: [E10]C01-20241012001 修正E10多人簽核關卡簽核歷程重覆問題
  - 變更檔案: 1 個
- **2024-10-16 09:44:11**: [Web]C01-20241014004 修正絕對表單tOtherHtmlObj元件沒有style.top屬性造成列印問題
  - 變更檔案: 1 個
- **2024-10-14 15:55:44**: [Web]C01-20241009001 修正待辦連結用記住我時若密碼已事先變更造成畫面無窮驗證失敗直到帳號鎖定問題
  - 變更檔案: 1 個
- **2024-10-14 08:36:35**: [Web]V00-20240918001 修正當活動關卡設定有勾選「允許輸入密碼」造成批次終止跟轉派異常[補]
  - 變更檔案: 1 個
- **2024-10-09 10:49:16**: [流程引擎]A00-20241008001 修正通知信過濾非HTML標籤主旨值問題
  - 變更檔案: 2 個
- **2024-10-08 14:34:47**: [Web]C01-20241004001 修正同時向前加簽並向後加簽後簡易流程呈現狀態異常問題
  - 變更檔案: 1 個
- **2024-10-08 09:52:30**: [在線閱覽]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)[補]
  - 變更檔案: 6 個
- **2024-10-04 10:46:04**: [Web]A00-20240920001 修正絕對表單Date、Time元件預設值(textValue)造成web表單設計師的顯示文字消失問題並消除代入預設值
  - 變更檔案: 2 個
- **2024-10-04 08:36:02**: [在線閱覽]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)
  - 變更檔案: 414 個
- **2024-10-01 15:46:06**: [Web]A00-20201022001 修正流程主旨中最後是\的符號，會導致無已轉派工作追蹤流程的清單頁會無法開啟
  - 變更檔案: 1 個
- **2024-10-01 14:42:19**: [Web]A00-20240924001 附件名稱包含特殊字元，流程派送後顯示無限增長
  - 變更檔案: 1 個
- **2024-10-01 10:12:03**: [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息[補]
  - 變更檔案: 1 個

### 張詠威 (6 commits)

- **2024-10-17 14:03:53**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
  - 變更檔案: 1 個
- **2024-10-17 14:03:53**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
  - 變更檔案: 1 個
- **2024-10-14 14:54:07**: [Web表單設計師]C01-20241011002 修正表單資訊的欄位比例從表單外及表單內查看值會不一樣
  - 變更檔案: 1 個
- **2024-10-09 15:54:01**: [E10]C01-20240930001 修正E10表單同步無法新增元件
  - 變更檔案: 1 個
- **2024-10-01 10:16:11**: [Web]C01-20240927004 修正58103版本流程的簽核歷程和簡易流程圖畫面沒有顯示進行中的關卡的資訊
  - 變更檔案: 1 個
- **2024-09-30 15:36:44**: [ESS]C01-20240926002 修正ESS流程若關卡符合自動簽核跳過關卡時，且系統參數有開啟非同步簽核時，ESS流程會無法繼續自動往下派送
  - 變更檔案: 1 個

### lorenchang (33 commits)

- **2024-12-16 10:43:19**: Revert "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常"
  - 變更檔案: 1 個
- **2024-12-09 16:18:37**: [內部]修正 SonarQube 顯示錯誤之程式：tIsE10Int.equals("")(補)
  - 變更檔案: 1 個
- **2024-12-05 16:46:56**: [文件總結助手]文件智能家更名為文件總結助手(補)
  - 變更檔案: 9 個
- **2024-11-28 14:37:03**: [文件總結助手]文件智能家更名為文件總結助手(補)
  - 變更檔案: 3 個
- **2024-11-27 17:01:56**: [文件總結助手]文件智能家更名為文件總結助手(補)
  - 變更檔案: 3 個
- **2024-11-27 16:20:36**: [內部]增加DML備註：各 DB SQL 執行時產生當下日期的語法：MSSQL：GETDATE()、Oracle：SYSDATE、DM8：SYSDATE()
  - 變更檔案: 3 個
- **2024-11-27 16:18:15**: [文件總結助手]新增功能：找經驗(補)
  - 變更檔案: 1 個
- **2024-11-27 15:13:53**: [內部]修正 SonarQube 顯示錯誤之程式：tIsE10Int.equals("")
  - 變更檔案: 1 個
- **2024-11-27 14:34:42**: [內部]V00-20241022002 修改系統設定onlineread.allow.convert.filetype的描述，如果pdfconverter.bcl.easypdfversion為digiwin就一併更改設定值(補)
  - 變更檔案: 3 個
- **2024-11-27 11:27:25**: [內部]V00-20241022002 修改系統設定onlineread.allow.convert.filetype的描述，如果pdfconverter.bcl.easypdfversion為digiwin就一併更改設定值
  - 變更檔案: 3 個
- **2024-11-26 17:35:52**: [ESS]C01-20241126006 更新ESSF51
  - 變更檔案: 3 個
- **2024-11-26 11:30:54**: [內部]C01-20241122004 增加服務任務調用RESTful接口的請求回應Log(補)
  - 變更檔案: 1 個
- **2024-11-25 13:53:52**: [TrmModule]合併InitSQL至5.8.10.4
  - 變更檔案: 3 個
- **2024-09-25 11:36:35**: [文件總結助手]新增功能：找經驗
  - 變更檔案: 53 個
- **2024-11-25 11:50:23**: [TrmModule]合併多語系至5.8.10.4
  - 變更檔案: 1 個
- **2024-11-25 11:00:00**: [TrmModule]合併DDL&DML至5.8.10.4
  - 變更檔案: 9 個
- **2024-09-24 16:30:21**: [TrmModule]新增差旅助手模組
  - 變更檔案: 48 個
- **2024-11-25 10:11:00**: [內部]C01-20241122004 增加服務任務調用RESTful接口的請求回應Log
  - 變更檔案: 1 個
- **2024-11-15 17:03:18**: [文件總結助手]文件智能家更名為文件總結助手
  - 變更檔案: 4 個
- **2024-11-15 14:00:54**: [其它]C01-20240916003 修正可能收不到 GuardService 認證失敗 Mail 的異常(補)
  - 變更檔案: 1 個
- **2024-11-15 11:17:08**: [其它]C01-20240916003 修正可能收不到 GuardService 認證失敗 Mail 的異常
  - 變更檔案: 1 個
- **2024-11-12 13:44:34**: [Web]A00-20241111001 修正指定關卡勾選「必須上傳新附件」，刪除附件被視為已上傳允許繼續派送的異常
  - 變更檔案: 1 個
- **2024-11-01 17:17:42**: [Web]C01-20241030004 修正當關卡勾選"需要使用者指定發送的組織單位"時，繼續派送的部門選取畫面確認按鈕使用舊圖示及主部門未預設勾選的問題
  - 變更檔案: 2 個
- **2024-11-01 11:41:57**: [流程引擎]C01-20241030009 修正轉由他人處理之待辦通知信內容簽核歷程缺失問題
  - 變更檔案: 1 個
- **2024-10-23 16:05:04**: [雙因素認證]C01-20241022003 修正使用LdapId登入的記住此裝置沒有作用的異常(信任端點資訊也沒有記錄)
  - 變更檔案: 1 個
- **2024-10-17 16:04:53**: [WebService]C01-20241014003 修正並重新啟用 WebService 接口 addCustomParallelAndSerialActivity
  - 變更檔案: 2 個
- **2024-10-16 14:40:45**: [Web]C01-20241015002 修正待辦列印表單處理者為代理簽核時不會顯示(代)
  - 變更檔案: 1 個
- **2024-10-15 11:30:42**: [流程設計師]C01-20241014007 修正服務任務呼叫 URL 結尾為".asmx?wsdl"的 WebService 出現讀取失敗的異常(TypeName=null 造成 NullPointerException)
  - 變更檔案: 1 個
- **2024-10-14 15:10:53**: [Web]C01-20241014002 修正系統設定 workitem.list.order 的描述，移除無法排序的 ProcessInstance.subject
  - 變更檔案: 3 個
- **2024-10-09 14:15:38**: [Web]C01-20241007002 修正iReport報表定義描述的內容有換行時，刪除報表定義檔的畫面無法正常顯示的異常
  - 變更檔案: 2 個
- **2024-10-01 10:53:47**: [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]
  - 變更檔案: 1 個
- **2024-09-24 10:23:34**: [雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常
  - 變更檔案: 9 個
- **2024-09-25 17:28:45**: [ISO]修正歸檔浮水印新增的字型設定產生的設定值與BCL8不相容造成中文字變方框
  - 變更檔案: 1 個

### 周权 (15 commits)

- **2024-12-09 10:31:49**: [Web]C01-20241205001 修正表單自適應寬度对subTab頁籤无效的问题
  - 變更檔案: 2 個
- **2024-12-02 11:27:21**: [流程引擎]C01-20241129004 優化表單定義被修改后使用重發新流程，restful轉存表單的邏輯
  - 變更檔案: 1 個
- **2024-12-02 11:14:39**: [Web]C01-20241125013 修正复杂SQL多组“order by”报错，优化包子查询
  - 變更檔案: 1 個
- **2024-11-26 14:58:06**: [Web]C01-20241125010 優化“無法順利地與資料庫建立連線”log資訊
  - 變更檔案: 1 個
- **2024-11-21 15:22:42**: [BPM APP] C01-20241114007 修正產品開窗預設值過濾組織id沒效果的問題[补]
  - 變更檔案: 4 個
- **2024-11-21 09:17:42**: [Web]C01-20241115004 E10同步表单元件不存在时新增log，印出错误的元件id
  - 變更檔案: 1 個
- **2024-11-20 15:29:00**: [BPM APP] C01-20241119008 修正企业微信端人員不存在，执行企业微信禁用成員排程会报错的問題[补]
  - 變更檔案: 1 個
- **2024-11-20 15:06:21**: [BPM APP] C01-20241119008 修正企业微信端人員不存在，执行企业微信禁用成員排程会报错的問題
  - 變更檔案: 1 個
- **2024-11-15 13:30:15**: [BPM APP] C01-20241114007 修正產品開窗預設值過濾組織id沒效果的問題
  - 變更檔案: 1 個
- **2024-11-11 10:01:59**: [其他] S00-20241025001 鼎新轉檔工具新增支援.msg檔
  - 變更檔案: 1 個
- **2024-11-05 15:05:35**: [BPM APP] C01-20241030008 优化LINE推播逻辑，避免JSON序列化重复执行导致CPU资源占用过高的问题
  - 變更檔案: 1 個
- **2024-11-04 14:00:15**: [Web] C01-20241104003 调整从pHttpServletRequest获取ip及port
  - 變更檔案: 1 個
- **2024-10-24 09:33:52**: [Web] Q00-20241023001 修正多选自定義開窗的選取清單有值後會跑版的問題[補]
  - 變更檔案: 1 個
- **2024-10-23 17:29:51**: [Web] Q00-20241023001 修正多选自定義開窗的選取清單有值後會跑版的問題
  - 變更檔案: 1 個
- **2024-09-27 12:02:25**: [文件智能家] chatfile设定档资料新增chatfile接口授权令牌[補修正]
  - 變更檔案: 1 個

### pinchi_lin (2 commits)

- **2024-12-06 16:33:39**: [TrmModule]新增差旅助理後端接口使用的dao與service[補]
  - 變更檔案: 5 個
- **2024-11-26 15:47:19**: [TrmModule]調整差旅助理EAI服務接口[補]
  - 變更檔案: 2 個

### davidhr (14 commits)

- **2024-12-06 15:17:48**: 更新58104 patch
  - 變更檔案: 1 個
- **2024-12-06 15:16:11**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2024-11-27 17:34:39**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2024-11-26 14:26:21**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2024-11-26 11:59:56**: C01-20241125005 弱點掃描調整,將ua-parser.js進行壓縮+加密+混淆
  - 變更檔案: 1 個
- **2024-11-21 16:19:11**: [資安]Q00-20241113001 bootstrap-3.3.5.print.css更換bootstrap-c.c.e.print.css
  - 變更檔案: 2 個
- **2024-11-18 16:59:10**: [資安]Q00-20241113001 將bootstrap-3.3.4.min.js、bootstrap-3.3.5.min.js、bootstrap-c.c.d.min.js、bootstrap-c.c.e.min.js壓縮、混淆、加密
  - 變更檔案: 4 個
- **2024-11-18 16:48:52**: [資安]Q00-20241113001 移除版本號
  - 變更檔案: 1 個
- **2024-11-18 16:46:35**: [資安]Q00-20241113001 bootstrap-3.3.5.css、bootstrap-3.3.5.min.css更換,增加bootstrap-c.c.e.css、bootstrap-c.c.e.min.css
  - 變更檔案: 4 個
- **2024-11-18 16:39:51**: [資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css
  - 變更檔案: 90 個
- **2024-11-18 16:26:58**: [資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css
  - 變更檔案: 83 個
- **2024-11-18 16:12:42**: [資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css
  - 變更檔案: 34 個
- **2024-11-18 16:02:12**: [資安]Q00-20241113001 bootstrap-3.3.5.min.css更換bootstrap-c.c.e.min.css
  - 變更檔案: 47 個
- **2024-11-18 15:28:41**: [資安]Q00-20241113001 bootstrap-3.3.5.css、bootstrap-3.3.5.min.css更換,增加bootstrap-c.c.e.css、bootstrap-c.c.e.min.css
  - 變更檔案: 4 個

### liuyun (3 commits)

- **2024-12-02 10:06:37**: [文件总结助手]修正绝对位置表单打开空白,报表管理新增页面少汇入两多语系
  - 變更檔案: 2 個
- **2024-11-19 10:18:49**: [Web]C01-20241118004 修正終止流程formScript返回不允許終止，會跳下一張單據，不會停在原單據
  - 變更檔案: 1 個
- **2024-11-08 10:05:59**: [SSO] 调整Athena SSO登录请求Header中去除appToken
  - 變更檔案: 2 個

### yamiyeh10 (16 commits)

- **2024-11-28 09:55:10**: [PRODT]S00-20231106007 調整Web流程管理工具中在選擇組織或人員畫面上增加搜尋機制[補]
  - 變更檔案: 1 個
- **2024-11-26 13:57:22**: [BPM APP]C01-20241125001 修正當流程設定為允許修改主旨(不可空白)且設置主旨範本時，行動端未顯示預設主旨導致無法發起流程的問題
  - 變更檔案: 2 個
- **2024-11-25 17:00:45**: [BPM APP]C01-20241125003 修正當Line整合授權帳號未與BPM帳號綁定時多語系顯示異常問題
  - 變更檔案: 1 個
- **2024-11-25 11:32:41**: [BPM APP]C01-20241111006 修正iOS的低版本有機率發生setValue設定選中值沒效果問題
  - 變更檔案: 1 個
- **2024-11-20 15:44:23**: [BPM APP]C01-20241119006 修正已轉派流程中有流程主旨為空時會顯示undefined訊息問題
  - 變更檔案: 2 個
- **2024-11-20 11:34:03**: [BPM APP]C01-20241118003 修正多人簽核流程中若使用者沒有簽核記錄時會出現無查看權限問題
  - 變更檔案: 1 個
- **2024-11-18 13:39:20**: [BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題[補]
  - 變更檔案: 1 個
- **2024-11-15 15:00:55**: [BPM APP]C01-20241115001 調整行動版表單必填欄位樣式顯示可以根據驗證設置的卡控規則顯示
  - 變更檔案: 1 個
- **2024-11-14 16:45:50**: [BPM APP]C01-20241112003 修正釘釘查看附件時iOS手機無法上下滑動問題與Android手機查看圖片附件時支持放大縮小功能
  - 變更檔案: 2 個
- **2024-11-11 16:08:16**: [BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題
  - 變更檔案: 11 個
- **2024-11-07 15:30:10**: [BPM APP]C01-20241106002 修正在手機端發起發生異常時顯示的畫面都是undefined 無法與伺服器溝通問題
  - 變更檔案: 1 個
- **2024-11-07 14:10:06**: [BPM APP]C01-20241107001 優化企業微信接收訊息在處理Exception時的邏輯
  - 變更檔案: 1 個
- **2024-11-06 17:37:34**: [BPM APP]C01-20241106004 修正使用自定義開窗當無資料時搜尋按鈕會消失問題
  - 變更檔案: 1 個
- **2024-10-29 15:49:50**: [PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題
  - 變更檔案: 1 個
- **2024-10-22 13:52:27**: [IMG]C01-20241021002 修正直接進入智能待辦應用時會無法顯示清單問題
  - 變更檔案: 1 個
- **2024-09-26 14:37:28**: [MPT]C01-20240916005 調整MPT公告申請單中公告內文是從Word地方複製貼上時會多了空白的問題
  - 變更檔案: 1 個

### davidhr-2997 (1 commits)

- **2024-11-27 17:38:14**: [資安]C01-20241125005 弱點掃描調整,將ua-parser.js進行壓縮+加密+混淆
  - 變更檔案: 1 個

### walter_wu (1 commits)

- **2022-07-14 16:33:03**: [ESS]C01-20240924001 修正58102版本的ESS流程在撤銷、終止流程時有可能會無法撤銷、終止的異常
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]
- **Commit ID**: `a991fa1834b708f1cef7f8d752144d0fd80e583f`
- **作者**: kmin
- **日期**: 2024-12-18 14:50:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 2. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
- **Commit ID**: `86a3aed4e3d7b611763bf39eebf514afb9450e6b`
- **作者**: 張詠威
- **日期**: 2024-10-17 14:03:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 3. Revert "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常"
- **Commit ID**: `16d875c0f7f858ac714d5f740bec61c4e7c37233`
- **作者**: lorenchang
- **日期**: 2024-12-16 10:43:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 4. [內部]修正 SonarQube 顯示錯誤之程式：tIsE10Int.equals("")(補)
- **Commit ID**: `67e767d5e5191b2d39673dcb8afe1cb49dddc185`
- **作者**: lorenchang
- **日期**: 2024-12-09 16:18:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`

### 5. [Web]C01-20241205001 修正表單自適應寬度对subTab頁籤无效的问题
- **Commit ID**: `748b6ccfc2ddcd6da5def8066a13c6b1984969af`
- **作者**: 周权
- **日期**: 2024-12-09 10:31:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`

### 6. [TrmModule]新增差旅助理後端接口使用的dao與service[補]
- **Commit ID**: `7cdcc5f3a99ae905afff0341942ab9cea5f05f5a`
- **作者**: pinchi_lin
- **日期**: 2024-12-06 16:33:39
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmToolDao.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmToolDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/TrmToolService.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/impl/TrmToolServiceImpl.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 7. 更新58104 patch
- **Commit ID**: `b9edcdeb6929a0c12c7a62d26a40e32d562f0cac`
- **作者**: davidhr
- **日期**: 2024-12-06 15:17:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 8. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `2c2f52df1c4af2095c9a1ae3b45326f4d6a8bc02`
- **作者**: davidhr
- **日期**: 2024-12-06 15:16:11
- **變更檔案數量**: 0

### 9. [文件總結助手]文件智能家更名為文件總結助手(補)
- **Commit ID**: `c3d8cb4a5fd624df66b77e8aba6d186030cbffe1`
- **作者**: lorenchang
- **日期**: 2024-12-05 16:46:56
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/service/ChatFileCommonService.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/service/impl/ChatFileCommonServiceImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISOMod001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001.form`
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISONew001Manager.form`

### 10. [Web]C01-20241125013 修正复杂SQL多组“order by”报错，优化包子查询[補]
- **Commit ID**: `2eeb4bc21850b4a97716c0cd1367a87c3b77a3d9`
- **作者**: kmin
- **日期**: 2024-12-05 14:08:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 11. [Web]C01-20241125017 優化Grid元件排序加入數值判斷
- **Commit ID**: `a041222fa3aad047e026b844152b93aa20f69578`
- **作者**: kmin
- **日期**: 2024-12-04 11:15:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 12. [流程引擎]C01-20241129004 優化表單定義被修改后使用重發新流程，restful轉存表單的邏輯
- **Commit ID**: `b4048b3cf37618d755003aaded42411fc32b57d2`
- **作者**: 周权
- **日期**: 2024-12-02 11:27:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 13. [Web]C01-20241125013 修正复杂SQL多组“order by”报错，优化包子查询
- **Commit ID**: `e5b90815bf040a3bd39b51aa887724c2d86cb7bc`
- **作者**: 周权
- **日期**: 2024-12-02 11:14:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 14. [文件总结助手]修正绝对位置表单打开空白,报表管理新增页面少汇入两多语系
- **Commit ID**: `b8da7d06d1ceadff5765512be23576db702182ba`
- **作者**: liuyun
- **日期**: 2024-12-02 10:06:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 15. [文件總結助手]文件智能家更名為文件總結助手(補)
- **Commit ID**: `367d7b68bedbb29262abcf6d465e350701645c94`
- **作者**: lorenchang
- **日期**: 2024-11-28 14:37:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`

### 16. [PRODT]S00-20231106007 調整Web流程管理工具中在選擇組織或人員畫面上增加搜尋機制[補]
- **Commit ID**: `d207510cc0aaa02df5cd21575392196f154804e1`
- **作者**: yamiyeh10
- **日期**: 2024-11-28 09:55:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 17. [資安]C01-20241125005 弱點掃描調整,將ua-parser.js進行壓縮+加密+混淆
- **Commit ID**: `ce69ea792aa9653de3fa678bc30afd247069715f`
- **作者**: davidhr-2997
- **日期**: 2024-11-27 17:38:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ua-parser.js`

### 18. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `7761e625a1f5d83ec258be9c34d9d9686469b751`
- **作者**: davidhr
- **日期**: 2024-11-27 17:34:39
- **變更檔案數量**: 0

### 19. [文件總結助手]文件智能家更名為文件總結助手(補)
- **Commit ID**: `b589ac3d3a26efc8cdcdd58116bb88b88abfa13a`
- **作者**: lorenchang
- **日期**: 2024-11-27 17:01:56
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`

### 20. [內部]增加DML備註：各 DB SQL 執行時產生當下日期的語法：MSSQL：GETDATE()、Oracle：SYSDATE、DM8：SYSDATE()
- **Commit ID**: `3bd94ced248d1b9ca2b59a547de77447479615fb`
- **作者**: lorenchang
- **日期**: 2024-11-27 16:20:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`

### 21. [文件總結助手]新增功能：找經驗(補)
- **Commit ID**: `1444ef718d0bbfed1ea4eee0c4b59b5008e24904`
- **作者**: lorenchang
- **日期**: 2024-11-27 16:18:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`

### 22. [內部]修正 SonarQube 顯示錯誤之程式：tIsE10Int.equals("")
- **Commit ID**: `4a4dab80bd1533a2f4ab6c857aa74e48f54be66d`
- **作者**: lorenchang
- **日期**: 2024-11-27 15:13:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 23. [內部]V00-20241022002 修改系統設定onlineread.allow.convert.filetype的描述，如果pdfconverter.bcl.easypdfversion為digiwin就一併更改設定值(補)
- **Commit ID**: `0a477d736dd2eeba6c3d31d351c208ade3391900`
- **作者**: lorenchang
- **日期**: 2024-11-27 14:34:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`

### 24. [內部]V00-20241022002 修改系統設定onlineread.allow.convert.filetype的描述，如果pdfconverter.bcl.easypdfversion為digiwin就一併更改設定值
- **Commit ID**: `f1831a64083cf907cfa5d671051f606ead3d9474`
- **作者**: lorenchang
- **日期**: 2024-11-27 11:27:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`

### 25. [ESS]C01-20241126006 更新ESSF51
- **Commit ID**: `3e38a4e767936df9d217dd0d520b3b1820c40c03`
- **作者**: lorenchang
- **日期**: 2024-11-26 17:35:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/V5.1\346\227\227\350\211\246/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/V5.2\346\227\227\350\211\246/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).form"`

### 26. [TrmModule]調整差旅助理EAI服務接口[補]
- **Commit ID**: `292bcc151480bc7d667e05a10bf30aa8767d0583`
- **作者**: pinchi_lin
- **日期**: 2024-11-26 15:47:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/impl/TrmBasicInfoServiceImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AthenaMgr.java`

### 27. [Web]C01-20241125002 修正當流程定義有設定主旨範本時重發新流程未帶預設值
- **Commit ID**: `76e5e8bcd7cbce93d056482da374fb7cb54977ac`
- **作者**: kmin
- **日期**: 2024-11-26 11:26:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 28. [內部]C01-20241122004 增加服務任務調用RESTful接口的請求回應Log(補)
- **Commit ID**: `8bfce9eee3502602fe162bd7edf8d22f1bb1d5f1`
- **作者**: lorenchang
- **日期**: 2024-11-26 11:30:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java`

### 29. [Web]C01-20241125010 優化“無法順利地與資料庫建立連線”log資訊
- **Commit ID**: `f87360fb2a2ff462b34f18fa4a9254ffa61885da`
- **作者**: 周权
- **日期**: 2024-11-26 14:58:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 30. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `24409b931f368565f4b47d75484e85cb6a4aee77`
- **作者**: davidhr
- **日期**: 2024-11-26 14:26:21
- **變更檔案數量**: 0

### 31. [BPM APP]C01-20241125001 修正當流程設定為允許修改主旨(不可空白)且設置主旨範本時，行動端未顯示預設主旨導致無法發起流程的問題
- **Commit ID**: `52e31d4f14bd7b38a987d2125e26646d027ba5a9`
- **作者**: yamiyeh10
- **日期**: 2024-11-26 13:57:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`

### 32. C01-20241125005 弱點掃描調整,將ua-parser.js進行壓縮+加密+混淆
- **Commit ID**: `9a3e55c2072a6c6d5d0d290c6495d8a702e97cfb`
- **作者**: davidhr
- **日期**: 2024-11-26 11:59:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ua-parser.js`

### 33. [Web]C01-20241125011 修正離職作業維護查詢條件日期結束時間改成當天的最後時間
- **Commit ID**: `63981690e4e1d2532dbbbf8cb63ab81ffdfd072c`
- **作者**: kmin
- **日期**: 2024-11-26 10:57:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java`

### 34. [BPM APP]C01-20241125003 修正當Line整合授權帳號未與BPM帳號綁定時多語系顯示異常問題
- **Commit ID**: `d04b986c9436d1a3f4acbc2881237a164cc40287`
- **作者**: yamiyeh10
- **日期**: 2024-11-25 17:00:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 35. [TrmModule]合併InitSQL至5.8.10.4
- **Commit ID**: `5ebeed4c3664dc5a7ce9da8fb835745c6e1a2745`
- **作者**: lorenchang
- **日期**: 2024-11-25 13:53:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`

### 36. [文件總結助手]新增功能：找經驗
- **Commit ID**: `df2ab9cf3110923e223f70cd282e6536b09b367d`
- **作者**: lorenchang
- **日期**: 2024-09-25 11:36:35
- **變更檔案數量**: 53
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileExecutionRecordDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileExperienceDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileExperienceRecordsDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileExperienceScheduleDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFilePresetProblemDao.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/ChatFileToolDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExecutionRecordDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceRecordsDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceScheduleDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFilePresetProblemDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileToolDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileExecutionRecord.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileExperience.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileExperienceRecords.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileExperienceSchedule.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFilePresetProblem.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/ChatFileQARecord.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/WordReportMappingDao.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/impl/WordReportMappingDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/domain/WordReportMapping.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/PDFHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IPDFHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/PDFHandlerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 37. [TrmModule]合併多語系至5.8.10.4
- **Commit ID**: `da8629877a249d7ceb90b920a582ec48065378c8`
- **作者**: lorenchang
- **日期**: 2024-11-25 11:50:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 38. [BPM APP]C01-20241111006 修正iOS的低版本有機率發生setValue設定選中值沒效果問題
- **Commit ID**: `e5d9fc9afbd9ae17eaa36d95d7470946f321144c`
- **作者**: yamiyeh10
- **日期**: 2024-11-25 11:32:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 39. [TrmModule]合併DDL&DML至5.8.10.4
- **Commit ID**: `51407dad085a2abfb1969e19d9f587dc55391ff0`
- **作者**: lorenchang
- **日期**: 2024-11-25 11:00:00
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📄 **重新命名**: `Release/db/update/5.8.10.4_DDL_DM8_TRM.sql`
  - 📄 **重新命名**: `Release/db/update/5.8.10.4_DDL_MSSQL_TRM.sql`
  - 📄 **重新命名**: `Release/db/update/5.8.10.4_DDL_Oracle_TRM.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - ❌ **刪除**: `Release/db/update/5.8.10.4_DML_DM8_TRM.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - ❌ **刪除**: `Release/db/update/5.8.10.4_DML_MSSQL_TRM.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`
  - ❌ **刪除**: `Release/db/update/5.8.10.4_DML_Oracle_TRM.sql`

### 40. [TrmModule]新增差旅助手模組
- **Commit ID**: `56d57babe4543d24303c959e64fbc0b8497d6b00`
- **作者**: lorenchang
- **日期**: 2024-09-24 16:30:21
- **變更檔案數量**: 48
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/.keep`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmCompanyMappingDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmConversionFunctionDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmInitiateProcessProfileDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmInitiateRecordDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmPropertiesDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmSourceFormDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/TrmToolDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmCompanyMappingDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmConversionFunctionDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmInitiateProcessProfileDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmInitiateRecordDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmPropertiesDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmSourceFormDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmToolDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/.keep`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmCompanyMapping.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmConversionFunction.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmConversionFunctionData.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmInitiateProcessProfile.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmInitiateRecord.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmProperties.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/domain/TrmSourceForm.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dto/BaseDtoObject.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dto/TrmConversionFunctionDataDto.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dto/TrmConversionFunctionDto.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/.keep`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/TrmBasicInfoService.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/TrmToolService.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/impl/TrmBasicInfoServiceImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/service/impl/TrmToolServiceImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/EaiExecutionRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/EaiPackageRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/EaiPackageStdDataRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Athena.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AthenaMgr.java`
  - ➕ **新增**: `Release/db/update/5.8.10.4_DDL_DM8_TRM.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.4_DDL_MSSQL_TRM.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.4_DDL_Oracle_TRM.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.4_DML_DM8_TRM.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.4_DML_MSSQL_TRM.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.4_DML_Oracle_TRM.sql`

### 41. [內部]C01-20241122004 增加服務任務調用RESTful接口的請求回應Log
- **Commit ID**: `61adc4375bdbcb21593b1b418396e653e0f1de8e`
- **作者**: lorenchang
- **日期**: 2024-11-25 10:11:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java`

### 42. [資安]Q00-20241113001 bootstrap-3.3.5.print.css更換bootstrap-c.c.e.print.css
- **Commit ID**: `a68fcebca81afd53bf0d33cf81865b821cf63501`
- **作者**: davidhr
- **日期**: 2024-11-21 16:19:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bootstrap-c.c.e-print.css`

### 43. [BPM APP] C01-20241114007 修正產品開窗預設值過濾組織id沒效果的問題[补]
- **Commit ID**: `b9b960492ca0eb2e7746d9f08b9679f34c8aeb74`
- **作者**: 周权
- **日期**: 2024-11-21 15:22:42
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 44. [Web]C01-20241119005 優化T100查看BPM簽核流程卡控未登入狀況的錯誤訊息[補]
- **Commit ID**: `2387ab0cc5b9573abd405a2b21dba4afa93469c4`
- **作者**: kmin
- **日期**: 2024-11-21 14:26:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 45. [Web]C01-20241119005 優化T100查看BPM簽核流程卡控未登入狀況的錯誤訊息
- **Commit ID**: `14c3577aa2245d95995943be3d58f51184f992b2`
- **作者**: kmin
- **日期**: 2024-11-21 14:10:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`

### 46. [Web]C01-20241115004 E10同步表单元件不存在时新增log，印出错误的元件id
- **Commit ID**: `f21e4e9cb340dd50526fb906793af23f24e61472`
- **作者**: 周权
- **日期**: 2024-11-21 09:17:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 47. [BPM APP]C01-20241119006 修正已轉派流程中有流程主旨為空時會顯示undefined訊息問題
- **Commit ID**: `f29e3713305568e8a963c5b92d4619c15c04774a`
- **作者**: yamiyeh10
- **日期**: 2024-11-20 15:44:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`

### 48. [BPM APP] C01-20241119008 修正企业微信端人員不存在，执行企业微信禁用成員排程会报错的問題[补]
- **Commit ID**: `a5ef283663ad8108fa4a665a2677932c1a4fa93e`
- **作者**: 周权
- **日期**: 2024-11-20 15:29:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`

### 49. [BPM APP] C01-20241119008 修正企业微信端人員不存在，执行企业微信禁用成員排程会报错的問題
- **Commit ID**: `304b4c539d0c7524f6e2f749f6b384ed7829433c`
- **作者**: 周权
- **日期**: 2024-11-20 15:06:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`

### 50. [ISO]C01-20241118008 修正ISO作廢單缺少記錄version造成ISOformSave儲存異常
- **Commit ID**: `ca90555100ea4168cc61727b6aa0bcae882bad25`
- **作者**: kmin
- **日期**: 2024-11-20 13:51:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISOCancel001.form`

### 51. [BPM APP]C01-20241118003 修正多人簽核流程中若使用者沒有簽核記錄時會出現無查看權限問題
- **Commit ID**: `10f2ae66c4a214444af7a1e16c7469b79cd5d737`
- **作者**: yamiyeh10
- **日期**: 2024-11-20 11:34:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 52. [Web]C01-20241118004 修正終止流程formScript返回不允許終止，會跳下一張單據，不會停在原單據
- **Commit ID**: `0055e3a66b3185e842feca247c3c6aa430dbeef6`
- **作者**: liuyun
- **日期**: 2024-11-19 10:18:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 53. [資安]Q00-20241113001 將bootstrap-3.3.4.min.js、bootstrap-3.3.5.min.js、bootstrap-c.c.d.min.js、bootstrap-c.c.e.min.js壓縮、混淆、加密
- **Commit ID**: `dc625dfc9b2b504d92f103e70dbfc2257afad1c1`
- **作者**: davidhr
- **日期**: 2024-11-18 16:59:10
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-3.3.4.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-3.3.5.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-c.c.d.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-c.c.e.min.js`

### 54. [資安]Q00-20241113001 移除版本號
- **Commit ID**: `b2b826a26d2ffff88113052b59e93c1c3f5175af`
- **作者**: davidhr
- **日期**: 2024-11-18 16:48:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap-3.3.5-print.css`

### 55. [資安]Q00-20241113001 bootstrap-3.3.5.css、bootstrap-3.3.5.min.css更換,增加bootstrap-c.c.e.css、bootstrap-c.c.e.min.css
- **Commit ID**: `00b83d8eac39a1d582b8808d42f9c549b1224924`
- **作者**: davidhr
- **日期**: 2024-11-18 16:46:35
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap/css/bootstrap-3.3.5.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap/css/bootstrap-3.3.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bootstrap/css/bootstrap-c.c.e.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bootstrap/css/bootstrap-c.c.e.min.css`

### 56. [資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css
- **Commit ID**: `8d47b26b0d5bd21e880421f1809a2386897f6da9`
- **作者**: davidhr
- **日期**: 2024-11-18 16:39:51
- **變更檔案數量**: 90
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TimeExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AdjustActivityOrder.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AttachmentHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseMutilPrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteReferProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ViewReassignHistory.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewDecisionActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/SubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ChooseDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/WebViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`

### 57. [資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css
- **Commit ID**: `58f49b9636436a6dab5c5cd1bfd2bbd44c6d4b2d`
- **作者**: davidhr
- **日期**: 2024-11-18 16:26:58
- **變更檔案數量**: 83
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DesignerDownload/DesignerDownloadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocCategory/ManageDocCategoryMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/PDFUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/LogoImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManageResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ShowSignImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoComplete.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/OnlineReadFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`

### 58. [資安]Q00-20241113001 bootstrap-3.3.5.css更換bootstrap-c.c.e.css
- **Commit ID**: `a5fec8e679c9f1e58646e9cce119a2e8d5d39159`
- **作者**: davidhr
- **日期**: 2024-11-18 16:12:42
- **變更檔案數量**: 34
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/AttachmentExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/ButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DialogExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileRWDExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TextboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Index.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/RESTful/RESTfulIndex.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/ChildGridChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ProductManifest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`

### 59. [資安]Q00-20241113001 bootstrap-3.3.5.min.css更換bootstrap-c.c.e.min.css
- **Commit ID**: `2f86a43576f78f5a975d7fa03fa86421b4b3a0c1`
- **作者**: davidhr
- **日期**: 2024-11-18 16:02:12
- **變更檔案數量**: 47
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/IWCIndicatorDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CannotAccessWarnning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/EBGModule/EBGFormManager.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/EBGModule/EBGPropertise.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/EBGModule/EBGSignerTemplate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/SSOCallBack.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/TFAModule/TFASetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/TFAModule/TFAUnauthlist.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/IntelligentLearningManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/WatermarkPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportTemplate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 60. [資安]Q00-20241113001 bootstrap-3.3.5.css、bootstrap-3.3.5.min.css更換,增加bootstrap-c.c.e.css、bootstrap-c.c.e.min.css
- **Commit ID**: `85415fd450fcb95783b7d44342b097050777bddb`
- **作者**: davidhr
- **日期**: 2024-11-18 15:28:41
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-3.3.5.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-3.3.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-c.c.e.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-c.c.e.min.css`

### 61. [BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題[補]
- **Commit ID**: `012d6a38b4994d80530496badf6fe9f3fa311fd5`
- **作者**: yamiyeh10
- **日期**: 2024-11-18 13:39:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 62. [文件總結助手]文件智能家更名為文件總結助手
- **Commit ID**: `a967c937525299a26b6c6ba07116d033eee302b2`
- **作者**: lorenchang
- **日期**: 2024-11-15 17:03:18
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.4_DML_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 63. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `dd3d4d883bf53470dcc8e55cdea759f2402d52b7`
- **作者**: kmin
- **日期**: 2024-11-15 15:03:13
- **變更檔案數量**: 0

### 64. [Web]C01-20241114002 修正關卡設定信件有直接簽核網址且必填簽核意見未卡控為空異常問題
- **Commit ID**: `a0da618b117bd4ea1e8b796c5780cf2dd624b0a5`
- **作者**: kmin
- **日期**: 2024-11-15 14:54:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BpmMailStraightSignOffPhrase.jsp`

### 65. [BPM APP]C01-20241115001 調整行動版表單必填欄位樣式顯示可以根據驗證設置的卡控規則顯示
- **Commit ID**: `d0494d8bda83ca74441ddcbbea4a21395e979153`
- **作者**: yamiyeh10
- **日期**: 2024-11-15 15:00:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js`

### 66. [其它]C01-20240916003 修正可能收不到 GuardService 認證失敗 Mail 的異常(補)
- **Commit ID**: `f712601bc1b0569f1159142f98760859294585d6`
- **作者**: lorenchang
- **日期**: 2024-11-15 14:00:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 67. [BPM APP] C01-20241114007 修正產品開窗預設值過濾組織id沒效果的問題
- **Commit ID**: `2a422c4638b3c6103b7f2c30b3b7f02dce7e5922`
- **作者**: 周权
- **日期**: 2024-11-15 13:30:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 68. [其它]C01-20240916003 修正可能收不到 GuardService 認證失敗 Mail 的異常
- **Commit ID**: `01c124c545c19a349bcea3637aeeb3d756d65b2e`
- **作者**: lorenchang
- **日期**: 2024-11-15 11:17:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 69. [BPM APP]C01-20241112003 修正釘釘查看附件時iOS手機無法上下滑動問題與Android手機查看圖片附件時支持放大縮小功能
- **Commit ID**: `da2d6ebe59b7a92da9d0455a0b4cf11142e37221`
- **作者**: yamiyeh10
- **日期**: 2024-11-14 16:45:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 70. [流程引擎]C01-20241112005 優化加簽關卡後派送失敗log訊息不明確
- **Commit ID**: `03057711c23b80ba4063c8cfae91ad3107225b69`
- **作者**: kmin
- **日期**: 2024-11-14 09:15:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 71. [PRODT]C01-20241111007 卡控核決關卡缺少ActivitySetDefinitions資料
- **Commit ID**: `b9db96010d5538e1cc9d46f3b4f1af264af512d5`
- **作者**: kmin
- **日期**: 2024-11-14 09:07:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 72. [Web]A00-20241111001 修正指定關卡勾選「必須上傳新附件」，刪除附件被視為已上傳允許繼續派送的異常
- **Commit ID**: `049ff31d91f158428c9a14fab08bd6762e0ac6af`
- **作者**: lorenchang
- **日期**: 2024-11-12 13:44:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 73. [BPM APP]C01-20241108004 修正企業微信操作切換企業後直接從推播進入時會導向追蹤清單或者待辦被轉派沒導向追蹤畫面問題
- **Commit ID**: `11afc6d14be172a8d04d55e20dba85ac71746328`
- **作者**: yamiyeh10
- **日期**: 2024-11-11 16:08:16
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 74. [其他] S00-20241025001 鼎新轉檔工具新增支援.msg檔
- **Commit ID**: `fdb7d4f58125c86d40b570eb595e4459f863e463`
- **作者**: 周权
- **日期**: 2024-11-11 10:01:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java`

### 75. [SSO] 调整Athena SSO登录请求Header中去除appToken
- **Commit ID**: `f0d56c41b07b8f5903efe160a2189f15365993ae`
- **作者**: liuyun
- **日期**: 2024-11-08 10:05:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/AthenaSSO.jsp`

### 76. [BPM APP]C01-20241106002 修正在手機端發起發生異常時顯示的畫面都是undefined 無法與伺服器溝通問題
- **Commit ID**: `da9c032e6637c1725bcfcb351cdd850fe4de3758`
- **作者**: yamiyeh10
- **日期**: 2024-11-07 15:30:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 77. [BPM APP]C01-20241107001 優化企業微信接收訊息在處理Exception時的邏輯
- **Commit ID**: `72bf69c072545c5c9477f599eb951fb9cd9a62f3`
- **作者**: yamiyeh10
- **日期**: 2024-11-07 14:10:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 78. [BPM APP]C01-20241106004 修正使用自定義開窗當無資料時搜尋按鈕會消失問題
- **Commit ID**: `f20c46bcfd8f0380beceb9fcd3d56b2b726e99a5`
- **作者**: yamiyeh10
- **日期**: 2024-11-06 17:37:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 79. [Web表單設計師]C01-20241104007 修正pairId重覆多組導致元件消失
- **Commit ID**: `2a903f339992913b9ce763a04ef5b750f13d5197`
- **作者**: kmin
- **日期**: 2024-11-05 16:33:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 80. [BPM APP] C01-20241030008 优化LINE推播逻辑，避免JSON序列化重复执行导致CPU资源占用过高的问题
- **Commit ID**: `89156964814a3628dfe4f18af32ceb1442c205c0`
- **作者**: 周权
- **日期**: 2024-11-05 15:05:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`

### 81. [Web] C01-20241104003 调整从pHttpServletRequest获取ip及port
- **Commit ID**: `0b70c15a8b9faa5c5de9bbabd44b7f8bcdc7c139`
- **作者**: 周权
- **日期**: 2024-11-04 14:00:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 82. [Web]C01-20241030004 修正當關卡勾選"需要使用者指定發送的組織單位"時，繼續派送的部門選取畫面確認按鈕使用舊圖示及主部門未預設勾選的問題
- **Commit ID**: `72743eb6ec97b396a78c6d2052894bd8bbf01e29`
- **作者**: lorenchang
- **日期**: 2024-11-01 17:17:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp`

### 83. [Web]C01-20241030003 修正流程草稿因表單新增SerialNumber元件進版後造成無法開啟呈現空白
- **Commit ID**: `755935c9effcc1005b8112629e9dfa7885e9d57e`
- **作者**: kmin
- **日期**: 2024-11-01 14:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 84. [流程引擎]C01-20241030009 修正轉由他人處理之待辦通知信內容簽核歷程缺失問題
- **Commit ID**: `05bdf9b150ca814e2be2ba11e5fdf6d608a64408`
- **作者**: lorenchang
- **日期**: 2024-11-01 11:41:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 85. [內部]Q00-20241029001 優化寄信mail log的記錄判讀
- **Commit ID**: `0330e8e6ca85803fa7f900c7a45866e8820b65fe`
- **作者**: kmin
- **日期**: 2024-10-29 16:12:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 86. [PRODT]C01-20241024005 修正Web流程管理工具中活動參與者組織相關選擇群組內的使用者時會顯示Error問題
- **Commit ID**: `b7f9f896bb9c55981fd8a40c03a662ac264d9a4d`
- **作者**: yamiyeh10
- **日期**: 2024-10-29 15:49:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 87. [流程引擎]C01-20241025004 修正改派待辦通知信內容簽核歷程缺失問題
- **Commit ID**: `fb24a64ab970fd89b14f9ec55757b4b7a034b5cc`
- **作者**: kmin
- **日期**: 2024-10-28 10:58:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 88. [流程引擎]C01-20241022005 修正呼叫RESTful的發起流程絕對表單單身資料缺失問題
- **Commit ID**: `9e9fda495f672a2d50a242213f484da2138db22d`
- **作者**: kmin
- **日期**: 2024-10-24 17:36:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 89. [Web] Q00-20241023001 修正多选自定義開窗的選取清單有值後會跑版的問題[補]
- **Commit ID**: `4b06462711903c7d00d14445734de2f7761dd9e0`
- **作者**: 周权
- **日期**: 2024-10-24 09:33:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 90. [Web] Q00-20241023001 修正多选自定義開窗的選取清單有值後會跑版的問題
- **Commit ID**: `ce39fbd4c9e6d774fd4d71e9da73cf55fdf2a769`
- **作者**: 周权
- **日期**: 2024-10-23 17:29:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 91. [雙因素認證]C01-20241022003 修正使用LdapId登入的記住此裝置沒有作用的異常(信任端點資訊也沒有記錄)
- **Commit ID**: `c48b2802dc45250fe1ff234739230ac8905e8835`
- **作者**: lorenchang
- **日期**: 2024-10-23 16:05:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 92. [IMG]C01-20241021002 修正直接進入智能待辦應用時會無法顯示清單問題
- **Commit ID**: `12a4b74fd4128a1f71ca51e1ac35b53a963f8f9e`
- **作者**: yamiyeh10
- **日期**: 2024-10-22 13:52:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 93. [Web]C01-20241021005 註解關閉表單Grid元件loadGridInfo的console.log訊息
- **Commit ID**: `f62b76931b9e540eb9285175fbfd072419822378`
- **作者**: kmin
- **日期**: 2024-10-22 10:59:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 94. [Web]C01-20240806002 singleOpenWin支援回收AdditionalCriteria參數是否包子查詢加上額外條件作為判斷供客戶使用
- **Commit ID**: `eaba1dc6706c1d47d6996c8d5af3eb0784ed924f`
- **作者**: kmin
- **日期**: 2024-10-21 11:56:01
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 95. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
- **Commit ID**: `ff58cf107171de5c11287e7d8e290460c3a779c8`
- **作者**: 張詠威
- **日期**: 2024-10-17 14:03:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 96. [WebService]C01-20241014003 修正並重新啟用 WebService 接口 addCustomParallelAndSerialActivity
- **Commit ID**: `61ab127a8b05158a08f2524d0aa18507447c3259`
- **作者**: lorenchang
- **日期**: 2024-10-17 16:04:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java`

### 97. [E10]C01-20241012001 修正E10多人簽核關卡簽核歷程重覆問題
- **Commit ID**: `d04a9210b3111b6203d0dfd5e6b51349a06af147`
- **作者**: kmin
- **日期**: 2024-10-17 13:47:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 98. [Web]C01-20241015002 修正待辦列印表單處理者為代理簽核時不會顯示(代)
- **Commit ID**: `be7229c6546f915925c741e42b69bdad86f1c245`
- **作者**: lorenchang
- **日期**: 2024-10-16 14:40:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 99. [Web]C01-20241014004 修正絕對表單tOtherHtmlObj元件沒有style.top屬性造成列印問題
- **Commit ID**: `1e9607cb06661e13254d5a4c81ca31ca97008a8b`
- **作者**: kmin
- **日期**: 2024-10-16 09:44:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 100. [流程設計師]C01-20241014007 修正服務任務呼叫 URL 結尾為".asmx?wsdl"的 WebService 出現讀取失敗的異常(TypeName=null 造成 NullPointerException)
- **Commit ID**: `2ba12baf57e69525fe10f1bb9bcfab5e3fe33bec`
- **作者**: lorenchang
- **日期**: 2024-10-15 11:30:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 101. [Web]C01-20241009001 修正待辦連結用記住我時若密碼已事先變更造成畫面無窮驗證失敗直到帳號鎖定問題
- **Commit ID**: `c0fc7578f69ee9a588d805080db571c241f4225e`
- **作者**: kmin
- **日期**: 2024-10-14 15:55:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 102. [Web]C01-20241014002 修正系統設定 workitem.list.order 的描述，移除無法排序的 ProcessInstance.subject
- **Commit ID**: `f9930660684922195366eb843a906b77d4302c3a`
- **作者**: lorenchang
- **日期**: 2024-10-14 15:10:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `Release/db/update/5.8.10.4_DML_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.4_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.4_DML_Oracle.sql`

### 103. [Web表單設計師]C01-20241011002 修正表單資訊的欄位比例從表單外及表單內查看值會不一樣
- **Commit ID**: `cafb0c1438ac08964d4a7dee5ef969f8bfa39ff2`
- **作者**: 張詠威
- **日期**: 2024-10-14 14:54:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/FormDefinitionInfoVo.java`

### 104. [Web]V00-20240918001 修正當活動關卡設定有勾選「允許輸入密碼」造成批次終止跟轉派異常[補]
- **Commit ID**: `f727e4d56cbb383482711a761ad5351bb1a93351`
- **作者**: kmin
- **日期**: 2024-10-14 08:36:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 105. [E10]C01-20240930001 修正E10表單同步無法新增元件
- **Commit ID**: `4ece98baf4452a7a8d5e185e843c00d3e06750e3`
- **作者**: 張詠威
- **日期**: 2024-10-09 15:54:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`

### 106. [Web]C01-20241007002 修正iReport報表定義描述的內容有換行時，刪除報表定義檔的畫面無法正常顯示的異常
- **Commit ID**: `4dbf6949f5bcc6eb88160cb6f473e5b878ff1e06`
- **作者**: lorenchang
- **日期**: 2024-10-09 14:15:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/report/ReportConfigViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`

### 107. [流程引擎]A00-20241008001 修正通知信過濾非HTML標籤主旨值問題
- **Commit ID**: `db598b2c42778a80ac784842af201452c9181fa0`
- **作者**: kmin
- **日期**: 2024-10-09 10:49:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/HtmlUtils.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 108. [Web]C01-20241004001 修正同時向前加簽並向後加簽後簡易流程呈現狀態異常問題
- **Commit ID**: `e111519848d9b749a574ba1c264bc9ab6583b332`
- **作者**: kmin
- **日期**: 2024-10-08 14:34:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 109. [在線閱覽]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)[補]
- **Commit ID**: `747af86576796187d607dd441790853c6b657a15`
- **作者**: kmin
- **日期**: 2024-10-08 09:52:30
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.mjs`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.mjs.map`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.sandbox.mjs`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.sandbox.mjs.map`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.mjs`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.mjs.map`

### 110. [Web]A00-20240920001 修正絕對表單Date、Time元件預設值(textValue)造成web表單設計師的顯示文字消失問題並消除代入預設值
- **Commit ID**: `66e03211ded020658ab60bc26fb2684105c68bdc`
- **作者**: kmin
- **日期**: 2024-10-04 10:46:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 111. [在線閱覽]Q00-20241001001 因部分PDF內容無法正常顯示，因此更新PDFJS閱讀器版本為(4.6.82)
- **Commit ID**: `130ae7a19e956922f03ca71ba95a06e63a2a2540`
- **作者**: kmin
- **日期**: 2024-10-04 08:36:02
- **變更檔案數量**: 414
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.mjs`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.mjs.map`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/CNS2-V.bcmap`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/ETenms-B5-H.bcmap`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/cmaps/GB-H.bcmap`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.css`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.mjs`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_add.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_disclaimer.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_done.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_spinner.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/altText_warning.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/annotation-paperclip.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/annotation-pushpin.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/cursor-editorFreeHighlight.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/cursor-editorFreeText.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/cursor-editorInk.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/cursor-editorTextHighlight.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/editor-toolbar-delete.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-next-rtl.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-next.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-next.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-previous-rtl.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-previous.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/findbarButton-previous.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/grab.cur`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/grabbing.cur`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/gv-toolbarButton-download.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/loading-dark.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/loading-small.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/messageBar_closingButton.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/messageBar_warning.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-documentProperties.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-documentProperties.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-firstPage.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-firstPage.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-handTool.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-handTool.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-lastPage.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-lastPage.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCcw.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCcw.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCw.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-rotateCw.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollHorizontal.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollHorizontal.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollPage.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollVertical.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollVertical.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollWrapped.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-scrollWrapped.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-selectTool.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-selectTool.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadEven.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadEven.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadNone.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadNone.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadOdd.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/secondaryToolbarButton-spreadOdd.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/shadow.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/texture.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-bookmark.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-bookmark.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-currentOutlineItem.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-download.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-download.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-editorFreeText.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-editorHighlight.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-editorInk.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-editorStamp.svg`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-menuArrow.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-menuArrows.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-openFile.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-openFile.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageDown-rtl.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageDown.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageDown.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageUp-rtl.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageUp.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-pageUp.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-presentationMode.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-presentationMode.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-print.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-print.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-search.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-search.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle-rtl.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-secondaryToolbarToggle.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-sidebarToggle-rtl.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-sidebarToggle.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-sidebarToggle.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewAttachments.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewAttachments.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewLayers.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewOutline-rtl.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewOutline.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewOutline.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewThumbnail.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-viewThumbnail.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomIn.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomIn.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomOut.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/toolbarButton-zoomOut.svg`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/treeitem-collapsed-rtl.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/treeitem-collapsed.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/treeitem-expanded.png`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/images/<EMAIL>`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/BPMlocale.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ach/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ach/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/af/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/af/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ak/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/an/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/an/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ar/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ar/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/as/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ast/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ast/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/az/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/az/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/be/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/be/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bg/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bg/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn-BD/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn-IN/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bo/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bo/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/br/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/br/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/brx/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/brx/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bs/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bs/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ca/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ca/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cak/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cak/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ckb/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ckb/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/crh/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cs/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cs/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/csb/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cy/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cy/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/da/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/da/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/de/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/de/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/dsb/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/dsb/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/el/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/el/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-CA/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-CA/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-GB/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-GB/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-US/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-US/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-ZA/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eo/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eo/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-AR/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-AR/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-CL/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-CL/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-ES/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-ES/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-MX/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-MX/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/et/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/et/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eu/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eu/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fa/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fa/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ff/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ff/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fi/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fi/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fr/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fr/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fur/viewer.ftl`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fy-NL/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fy-NL/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ga-IE/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ga-IE/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gd/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gd/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gl/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gl/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gn/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gn/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gu-IN/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gu-IN/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/he/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/he/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hi-IN/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hi-IN/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hr/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hr/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hsb/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hsb/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hto/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hu/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hu/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hy-AM/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hy-AM/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hye/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hye/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ia/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ia/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/id/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/id/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/is/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/is/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/it/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/it/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ja/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ja/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ka/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ka/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kab/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kab/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kk/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kk/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/km/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/km/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kn/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kn/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ko/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ko/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kok/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ks/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ku/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lg/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lij/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lij/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lo/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lo/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/locale.json`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/locale.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lt/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lt/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ltg/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ltg/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lv/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lv/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mai/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/meh/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/meh/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mk/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mk/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ml/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mn/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mr/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mr/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ms/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ms/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/my/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/my/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nb-NO/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nb-NO/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ne-NP/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ne-NP/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nl/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nl/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nn-NO/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nn-NO/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nso/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/oc/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/oc/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/or/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pa-IN/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pa-IN/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pl/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pl/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-BR/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-BR/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-PT/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-PT/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rm/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rm/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ro/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ro/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ru/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ru/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rw/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sah/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sat/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sat/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sc/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sc/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/scn/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/scn/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sco/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sco/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/si/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/si/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sk/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sk/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/skr/viewer.ftl`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sl/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sl/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/son/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/son/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sq/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sq/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sr/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sr/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sv-SE/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sv-SE/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sw/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/szl/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/szl/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ta-LK/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ta/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ta/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/te/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/te/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tg/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tg/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/th/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/th/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tl/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tl/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tn/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tr/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tr/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/trs/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/trs/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tsz/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uk/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uk/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ur/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ur/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uz/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uz/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/vi/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/vi/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/wo/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/wo/viewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/xh/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/xh/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zam/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/BPMviewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/BPMviewer.properties`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/viewer.ftl`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zu/viewer.properties`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSans.pfb`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansBold.pfb`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansBoldItalic.pfb`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/standard_fonts/FoxitSansItalic.pfb`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js.map`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.mjs`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.mjs.map`

### 112. [Web]A00-20201022001 修正流程主旨中最後是\的符號，會導致無已轉派工作追蹤流程的清單頁會無法開啟
- **Commit ID**: `c6f4e89f3f495dadce5dc7455b4ba142bf82a5e8`
- **作者**: kmin
- **日期**: 2024-10-01 15:46:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 113. [Web]A00-20240924001 附件名稱包含特殊字元，流程派送後顯示無限增長
- **Commit ID**: `af331b5103fededdd1f2f8f20effbdf0e93b77ea`
- **作者**: kmin
- **日期**: 2024-10-01 14:42:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 114. [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]
- **Commit ID**: `33ec1023da8081f90a215f182cde843341a68f31`
- **作者**: lorenchang
- **日期**: 2024-10-01 10:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 115. [Web]C01-20240927004 修正58103版本流程的簽核歷程和簡易流程圖畫面沒有顯示進行中的關卡的資訊
- **Commit ID**: `bb8159c1a4f395dcf4ba623388efce93ec00052e`
- **作者**: 張詠威
- **日期**: 2024-10-01 10:16:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 116. [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息[補]
- **Commit ID**: `ab0805cf1b372dcb4788b2565424efee6fd87d9a`
- **作者**: kmin
- **日期**: 2024-10-01 10:12:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`

### 117. [ESS]C01-20240926002 修正ESS流程若關卡符合自動簽核跳過關卡時，且系統參數有開啟非同步簽核時，ESS流程會無法繼續自動往下派送
- **Commit ID**: `769d8c7f472ee0fa295b49ee93c9b8bddd63ec08`
- **作者**: 張詠威
- **日期**: 2024-09-30 15:36:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 118. [ESS]C01-20240924001 修正58102版本的ESS流程在撤銷、終止流程時有可能會無法撤銷、終止的異常
- **Commit ID**: `c6b3d02dd6d8940a9daae074dbf687ed493f8fd0`
- **作者**: walter_wu
- **日期**: 2022-07-14 16:33:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 119. [文件智能家] chatfile设定档资料新增chatfile接口授权令牌[補修正]
- **Commit ID**: `d91f3329e507ca76080d7009b9446bd47d26c6b3`
- **作者**: 周权
- **日期**: 2024-09-27 12:02:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.3_DML_Oracle.sql`

### 120. [雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常
- **Commit ID**: `b7144b5a645d89a71ffaee1bd8da26b025ea3198`
- **作者**: lorenchang
- **日期**: 2024-09-24 10:23:34
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 121. [MPT]C01-20240916005 調整MPT公告申請單中公告內文是從Word地方複製貼上時會多了空白的問題
- **Commit ID**: `fd796a968e36872dbb475e6c298b6a23bf409439`
- **作者**: yamiyeh10
- **日期**: 2024-09-26 14:37:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@mpt/default-form/MptAncApply.form`

### 122. [ISO]修正歸檔浮水印新增的字型設定產生的設定值與BCL8不相容造成中文字變方框
- **Commit ID**: `fa159656362931e8cc5f6070044a14a5c9466865`
- **作者**: lorenchang
- **日期**: 2024-09-25 17:28:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

