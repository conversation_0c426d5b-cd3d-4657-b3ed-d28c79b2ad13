{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.10.4_All", "date": "2025-03-05 13:40:19", "message": "[帳號安全管理]C01-20250304009 修正O<PERSON>h登入後顯示的語系是環境語系而不是登入頁選取的語系", "author": "周权"}, "舊分支": {"branch_name": "release_5.8.10.4", "date": "2024-12-18 14:50:52", "message": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]", "author": "kmin"}, "比較時間": "2025-07-18 10:39:31", "新增commit數量": 19, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "22ac0a92dda77e24238e7a8e6b6350394bd08177", "commit_訊息": "[帳號安全管理]C01-20250304009 修正O<PERSON>h登入後顯示的語系是環境語系而不是登入頁選取的語系", "提交日期": "2025-03-05 13:40:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cd74bb1a451eea2df4d88ca52911a5952e9feb7d", "commit_訊息": "[Web]Q00-20250305001 修正系統設定\"nana.performworkItem.html.character.filter\"=true,追蹤、待辦等主旨顯示會異常(補)", "提交日期": "2025-03-05 13:58:54", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "c5943553d1e957e15ce92be2e951c7146876aab8", "commit_訊息": "[Web]Q00-20250305001 修正系統設定\"nana.performworkItem.html.character.filter\"=true,追蹤、待辦等主旨顯示會異常", "提交日期": "2025-03-05 13:40:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "e1aecc428c3af8b74e4ba14cdbb472531f1b8de6", "commit_訊息": "[Web]C01-20250116003 修正SubjectTitle导致主旨显示異常", "提交日期": "2025-01-17 14:25:09", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a82f8ec5d3947dd218d9518961c7400a0a52a013", "commit_訊息": "[帳號安全管理]C01-20250304008 修正驗證碼通知樣版使用的語系是使用者環境語系而不是登入畫面選擇的", "提交日期": "2025-03-06 16:44:33", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "784bb05c9504b5602a0d3b341299b81b61248efe", "commit_訊息": "[帳號安全管理]C01-20250304008 修正缺少使用者語系的通知樣板導致雙因素驗證出現null提示的異常", "提交日期": "2025-03-06 16:44:23", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6495e4bfaae71685a9a17bdd51c98ed95366286f", "commit_訊息": "[文件總結助手]Q00-20250224001 修正找經驗關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)", "提交日期": "2025-02-24 14:53:50", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3695ed504b75d4ecc105a65634332031373d21fd", "commit_訊息": "[資安]Q00-20241227001 多選開窗查詢資安問題修正", "提交日期": "2025-01-07 17:27:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "936e8e74d87ee61442249aefee6ccb40d16c9d9a", "commit_訊息": "[資安]Q00-20241226001 Sql Injection問題，调整多选开窗参数txtConditionValue", "提交日期": "2025-01-05 22:34:45", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0cde96b7a2ebf4794deec704cc26d80453b995dd", "commit_訊息": "[文件總結助手]Q00-20250220001 修正助閱讀關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)", "提交日期": "2025-02-17 16:14:46", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bfa89d43ba1fc46d634871a31c4b0d8882bf8d21", "commit_訊息": "[Web] C01-20250206008 調整助閲讀、找經驗分頁查詢", "提交日期": "2025-02-14 10:26:37", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmCompanyMappingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmInitiateProcessProfileDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "29b17307ef79920fbb65df9cdc2cf196d9d57b17", "commit_訊息": "[雙因素認證]C01-20250217001 修正啟用帳號鎖定次數時雙因素驗出現異常：Argument pSqlString cannot be null or empty string", "提交日期": "2025-02-17 16:14:46", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4d8575854e8290ddafbe37de771e82b88882878", "commit_訊息": "[流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常", "提交日期": "2024-12-03 17:24:14", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9ada8bfcd645ad277e6b269ba063e136c95fa4a1", "commit_訊息": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]", "提交日期": "2024-12-18 14:50:52", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99f505c932f7a7aaad751b5288746d64d8c914cf", "commit_訊息": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常", "提交日期": "2024-10-17 14:03:53", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "41af72642d65968e8b55b08d83b5141104e0622c", "commit_訊息": "[資安]Q00-20241217004 調整個人訊息頁欄位安全性問題", "提交日期": "2024-12-20 14:11:20", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9287c65390def0fc662156d80d63c15409f86a92", "commit_訊息": "[資安]Q00-20241217003 調整查詢欄位可以輸入查詢條件支持查詢的問題，防止SQL注入", "提交日期": "2024-12-19 10:43:41", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b936fce4c7cf078b8d73edb70008982606709af1", "commit_訊息": "[資安]Q00-20241217002 调整登入错误讯息[補]", "提交日期": "2024-12-19 10:23:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "bf1ec218ad7e5cdfa1e1d216b5b9235afc764ce1", "commit_訊息": "[資安]Q00-20241217002 调整登入错误讯息", "提交日期": "2024-12-18 16:10:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}]}