#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BPM Easy Tools 目標主機部署工具
用於在目標主機上自動設定和啟動應用
"""

import os
import sys
import subprocess
import json
from pathlib import Path
import platform


class TargetDeployer:
    """目標主機部署工具類別"""
    
    def __init__(self, project_root=None):
        """
        初始化部署工具
        
        Args:
            project_root (str, optional): 專案根目錄路徑，預設為當前目錄
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        self.requirements_file = self.project_root / "requirements.txt"
        self.config_file = self.project_root / "config" / "projects_config.json"
        
    def check_python(self):
        """檢查 Python 版本"""
        try:
            result = subprocess.run([sys.executable, "--version"], 
                                  capture_output=True, text=True)
            version = result.stdout.strip()
            print(f"✅ {version}")
            
            # 檢查版本是否符合要求 (3.8+)
            version_info = sys.version_info
            if version_info.major < 3 or (version_info.major == 3 and version_info.minor < 8):
                print("⚠️  警告: 建議使用 Python 3.8 或以上版本")
                return False
            return True
        except Exception as e:
            print(f"❌ 無法檢查 Python 版本: {e}")
            return False
    
    def create_virtual_environment(self):
        """建立虛擬環境"""
        if self.venv_path.exists():
            print(f"📁 虛擬環境已存在: {self.venv_path}")
            return True
        
        try:
            print("🔧 建立虛擬環境...")
            subprocess.run([sys.executable, "-m", "venv", str(self.venv_path)], 
                          check=True)
            print("✅ 虛擬環境建立成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 建立虛擬環境失敗: {e}")
            return False
    
    def get_venv_python(self):
        """取得虛擬環境的 Python 執行檔路徑"""
        if platform.system() == "Windows":
            return self.venv_path / "Scripts" / "python.exe"
        else:
            return self.venv_path / "bin" / "python"
    
    def get_venv_pip(self):
        """取得虛擬環境的 pip 執行檔路徑"""
        if platform.system() == "Windows":
            return self.venv_path / "Scripts" / "pip.exe"
        else:
            return self.venv_path / "bin" / "pip"
    
    def install_requirements(self):
        """安裝依賴套件"""
        if not self.requirements_file.exists():
            print(f"❌ 找不到 requirements.txt: {self.requirements_file}")
            return False

        venv_pip = self.get_venv_pip()
        print(f"🔍 使用虛擬環境 pip: {venv_pip}")

        if not venv_pip.exists():
            print(f"❌ 找不到虛擬環境的 pip: {venv_pip}")
            print("💡 請確認虛擬環境已正確建立")
            return False

        try:
            print("📦 安裝依賴套件...")
            print(f"📋 執行命令: {venv_pip} install -r {self.requirements_file}")

            # 使用虛擬環境的 pip 安裝套件
            result = subprocess.run([str(venv_pip), "install", "-r", str(self.requirements_file)],
                                  capture_output=True, text=True, check=True)

            print("✅ 依賴套件安裝成功")
            if result.stdout:
                print("📋 安裝詳情:")
                print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 安裝依賴套件失敗: {e}")
            if e.stdout:
                print("📋 標準輸出:")
                print(e.stdout)
            if e.stderr:
                print("📋 錯誤輸出:")
                print(e.stderr)
            return False
    
    def check_directories(self):
        """檢查必要目錄（從設定檔讀取）"""
        # 從設定檔讀取目錄清單
        required_dirs = self._get_required_directories()
        missing_dirs = []

        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                print(f"✅ 目錄存在: {dir_name}")
            else:
                print(f"❌ 目錄不存在: {dir_name}")
                missing_dirs.append(dir_name)

        return len(missing_dirs) == 0

    def _get_required_directories(self):
        """從設定檔讀取必要目錄清單"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 從部署設定中讀取目錄清單
            deployment_config = config.get('deployment', {})
            include_directories = deployment_config.get('include_directories', [])

            # 清理目錄名稱（移除尾隨的斜線）
            required_dirs = [dir_name.rstrip('/') for dir_name in include_directories]

            # 如果設定檔中沒有目錄清單，使用預設值
            if not required_dirs:
                print("⚠️  設定檔中未找到目錄清單，使用預設值")
                required_dirs = ["pages", "tools", "config", "data_output", ".streamlit"]

            return required_dirs

        except Exception as e:
            print(f"⚠️  讀取設定檔失敗，使用預設目錄清單: {e}")
            return ["pages", "tools", "config", "data_output", ".streamlit"]
    
    def update_config_paths(self):
        """更新設定檔中的路徑（如果需要）"""
        if not self.config_file.exists():
            print("⚠️  設定檔不存在，跳過路徑更新")
            return True
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("📝 檢查設定檔路徑...")
            
            # 檢查專案路徑是否存在
            projects = config.get('projects', {})
            updated = False
            
            for project_name, project_config in projects.items():
                repo_path = project_config.get('repo_path', '')
                if repo_path and not Path(repo_path).exists():
                    print(f"⚠️  專案 {project_name} 的路徑不存在: {repo_path}")
                    print(f"   請手動修改 {self.config_file}")
                    updated = True
                else:
                    print(f"✅ 專案 {project_name} 路徑正確")
            
            if updated:
                print("💡 提示: 請根據目標主機的實際路徑修改 config/projects_config.json")
            
            return True
        except Exception as e:
            print(f"❌ 檢查設定檔失敗: {e}")
            return False
    
    def start_streamlit(self):
        """啟動 Streamlit 應用"""
        venv_python = self.get_venv_python()
        if not venv_python.exists():
            print("❌ 找不到虛擬環境的 Python")
            return False
        
        streamlit_file = self.project_root / "streamlit_home.py"
        if not streamlit_file.exists():
            print(f"❌ 找不到主程式: {streamlit_file}")
            return False
        
        try:
            print("🚀 啟動 Streamlit 應用...")
            print("💡 提示: 按 Ctrl+C 可停止應用")
            print("=" * 50)
            
            # 切換到專案目錄並啟動
            os.chdir(self.project_root)
            subprocess.run([str(venv_python), "-m", "streamlit", "run", "streamlit_home.py", "--server.port", "8888"],
                          check=True)
        except KeyboardInterrupt:
            print("\n👋 應用已停止")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 啟動應用失敗: {e}")
            return False
    
    def deploy(self):
        """執行完整部署流程"""
        print("🚀 開始部署 BPM Easy Tools...")
        print("=" * 50)
        
        # 1. 檢查 Python
        if not self.check_python():
            return False
        
        # 2. 檢查目錄結構
        if not self.check_directories():
            print("❌ 目錄結構不完整，請檢查解壓縮是否正確")
            return False
        
        # 3. 建立虛擬環境
        if not self.create_virtual_environment():
            return False
        
        # 4. 安裝依賴套件
        if not self.install_requirements():
            return False
        
        # 5. 檢查設定檔
        self.update_config_paths()
        
        print("=" * 50)
        print("🎉 部署完成！")
        print("💡 現在可以啟動應用了")
        
        # 詢問是否立即啟動
        try:
            response = input("\n是否立即啟動 Streamlit 應用？(y/N): ").strip().lower()
            if response in ['y', 'yes', '是']:
                self.start_streamlit()
        except KeyboardInterrupt:
            print("\n👋 部署完成，稍後可手動啟動應用")
        
        return True


def main():
    """主程式"""
    try:
        deployer = TargetDeployer()
        success = deployer.deploy()
        
        if success:
            print("\n📋 後續使用說明:")
            print("1. 啟動應用: python -m streamlit run streamlit_home.py --server.port 8888")
            print("2. 或執行: start_application.cmd")
            print("3. 瀏覽器訪問: http://localhost:8888")
        else:
            print("\n❌ 部署失敗，請檢查錯誤訊息")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n❌ 使用者中斷部署")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 部署過程發生錯誤: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
