{"company_id": "61114000", "company_name": "神腦", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\61114000_神腦\\01客戶基本資料", "files": [{"filename": "神腦.txt", "raw_content": "詹宏霖 Alin 0928-634-695 2-2218-3588 Ext.1742\r\n\r\nScott 陳世強 2-2218-3588 Ext.1743\r\n\r\n\r\n------------------------------------------------20241001更新\r\n測試機AP\r\n10.12.0.61\r\n\r\n測試機DB\r\n10.12.0.62\r\nbpmadmin/!qaz2wsx\r\n\r\n------------------------------------------------20220614更新\r\nVPN:\r\n以系統管理員身份執行命令提示字元\r\n輸入 regedit\r\n找到 HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\services\\PolicyAgent 右鍵新增一個 DWORD(32-位元)\r\n加入 AssumeUDPEncapsulationContextOnSendRule 修改將數值改為 2\r\n電腦重新開機\r\n\r\nWindows VPN L2TP \r\nVPN IP:svpn.senao.com.tw\r\n20220905 更新\r\n帳號：digiwin 密碼 mko098&ygv\r\n------------------------------------------------\r\n正式機網址 (5881)(有SSO可能要調)\r\nhttps://efgp.senao.com.tw/\r\n!qaz2wsx\r\n\r\n有三台主機 load balance\r\n[新]客戶2024.05.11自行移機\r\nAP1 **********\r\nAP2 **********\r\nAP3 **********\r\nGuard Server **********\r\n\r\n[舊]\r\n10.0.201.131  登入使用\r\n10.0.201.132  登入使用\r\n10.0.201.133  呼叫API使用\r\n\r\n\r\n遠端桌面 BPM 測試機\r\n************\r\n帳：group01\\018264\r\n密：22180962A]\r\nhttp://************:8086/NaNaWeb/\r\n\r\nBPM 測試機 DB\r\n10.0.201.244 不可遠端\r\n------------------------------------------------20220614更新\r\n\r\n神腦(5.6.5.1_20171201>>>5731)\r\n\r\n--------------BPM正式區(無法連線)---------------\r\n置換檔案請登入下方網址\r\nhttps://bitbucket.org/senaotw/bpm\r\n帳號：<EMAIL>\r\n密碼：digiwin2018\r\n\r\n正式機網頁https://efgp.senao.com.tw/NaNaWeb/\r\n登入密碼：!qaz2wsx\r\n下Fetch 再去執行Terminal(連到不同的branch)\r\n有三台主機\r\n10.0.201.131  登入使用\r\n10.0.201.132  登入使用\r\n10.0.201.133  呼叫API使用\r\n\r\n--------------BPM正式區(無法連線)---------------\r\n\r\n-------跳板主機--------\r\n外*************\r\n帳號如下：bpm01~04\r\n密碼   Easyflow123\r\n-------跳板主機--------\r\n\r\n-----------------------------\r\nBPM測試區DB\r\nIP:************\r\n帳號如下：bpm\r\nOS密碼: !qaz2wsx\r\nUser :bpm02\r\nPW:!qaz2wsx\r\n-----------------------------\r\n\r\n新的Easyflow測試機IP為************\r\nUser :bpm\r\nPW   :!qaz2wsx\r\n(DB244)\r\n\r\n--------------SP7測試區---------------\r\n(SP7)EasyFlow測試區AP、DB : 主機ip ***********\r\n帳號:administrator 密碼:wftest \r\nDB連線帳號sa 密碼eftest\r\n--------------SP7測試區---------------\r\n\r\n周秀珠\r\n數位商務發展部  商業智慧課\r\nTel: +886 2 2218 3588 Ext. 1455\r\n\r\nLeo 分機 1442\r\n蘇芃之  Sophie Su 分機 1731\r\n\r\n\r\n測試機20u\r\n安裝序號:ca1c-20te-1101-0020\r\n安裝密碼:c9d9-7756-5a72-4712-6276-35f5\r\n\r\nRWD \r\nMAC 00-0C-29-8D-21-76      \r\n測式機 安裝序號 7488-77te-1113-0000 \r\n安裝密碼 1579-77a6-a0f2-4712-6274-3235 \r\n\r\n\r\n\r\n--------------OLD------------------\r\n00-0C-29-3F-D8-A5\t\r\naaa7-aate-1101-0020\t\r\nb4d4-d796-4a42-67c2-7270-3cdf\r\n\r\n\t\r\n00-0C-29-3F-D8-A5\t\r\n5004-02te-1113-0000\t\r\n4430-d7c6-dcb2-67c2-7276-3d16\r\n--------------OLD------------------\r\n\r\n\r\n\r\n\r\n\r\n", "structured_data": {"username": "administrator 密碼:wftest", "帳": "group01\\018264", "密": "22180962A]", "password": "digiwin2018", "登入密碼": "!qaz2wsx", "帳號如下": "bpm", "vpn ip": "svpn.senao.com.tw", "https": "//bitbucket.org/senaotw/bpm", "http": "//************:8086/NaNaWeb/", "正式機網頁https": "//efgp.senao.com.tw/NaNaWeb/", "host": "************", "os密碼": "!qaz2wsx", "pw": "!qaz2wsx", "(sp7)easyflow測試區ap、db": "主機ip ***********", "tel": "+886 2 2218 3588 Ext. 1455", "安裝序號": "ca1c-20te-1101-0020", "安裝密碼": "c9d9-7756-5a72-4712-6276-35f5"}, "source_path": "C1.客戶維護相關\\61114000_神腦\\01客戶基本資料\\神腦.txt", "file_size": 2674, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:26.697557"}], "total_files": 1, "processed_at": "2025-08-26T10:46:26.697566"}