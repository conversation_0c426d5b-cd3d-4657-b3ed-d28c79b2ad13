{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.10.3_All", "date": "2025-05-22 15:38:39", "message": "[資安]C01-20250520005 漏洞一:一般使用者可呼叫出管理者功能\"跳過\",透過開發者工具執行前端函式bypassActivity()。漏洞二：密碼驗證後執行的管理者功能也在前端，透過開發者工具執行前端函式completeVerify()跳過驗證", "author": "DESKTOP-R51BOK0\\H-00778"}, "舊分支": {"branch_name": "release_5.8.10.3", "date": "2024-10-01 10:53:47", "message": "[流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]", "author": "lorenchang"}, "比較時間": "2025-07-18 10:39:54", "新增commit數量": 10, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "b3057493f08f354ca5fdd3f745f2f88472aba96b", "commit_訊息": "[資安]C01-20250520005 漏洞一:一般使用者可呼叫出管理者功能\"跳過\",透過開發者工具執行前端函式bypassActivity()。漏洞二：密碼驗證後執行的管理者功能也在前端，透過開發者工具執行前端函式completeVerify()跳過驗證", "提交日期": "2025-05-22 15:38:39", "作者": "DESKTOP-R51BOK0\\H-00778", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/VerifyPasswordForByPass.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "86e62f638d306af784c29a64bcd1024bdaf61284", "commit_訊息": "[流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常", "提交日期": "2024-12-03 17:24:14", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0a125db78239478c807266830c643ab4d3103fd5", "commit_訊息": "[內部]Q00-20241029001 優化寄信mail log的記錄判讀", "提交日期": "2024-10-29 16:12:55", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3b2e93bcc2513d2e5873923448aa753423e115f", "commit_訊息": "[流程引擎]A00-20241008001 修正通知信過濾非HTML標籤主旨值問題", "提交日期": "2024-10-09 10:49:16", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/HtmlUtils.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0032c2d42649d173c8b7e829a7cdf3f86ccc642b", "commit_訊息": "[雙因素認證]C01-20241022003 修正使用LdapId登入的記住此裝置沒有作用的異常(信任端點資訊也沒有記錄)", "提交日期": "2024-10-23 16:05:04", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "70ac10d865d5bf3ffbc2fea1e6942bf1483e657d", "commit_訊息": "[雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常", "提交日期": "2024-09-24 10:23:34", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "c856decf583e5f629964c7f0c7142ffa33648224", "commit_訊息": "[流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]", "提交日期": "2024-10-01 10:53:47", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "19b47ffe3c05c75d7f5cbcff93879e744f1f99fc", "commit_訊息": "[EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息[補]", "提交日期": "2024-10-01 10:12:03", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba15a63ab923367636b4846d6a23bbe0b1158be6", "commit_訊息": "[Web]C01-20240927004 修正58103版本流程的簽核歷程和簡易流程圖畫面沒有顯示進行中的關卡的資訊", "提交日期": "2024-10-01 10:16:11", "作者": "張詠威", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c21865d316770f371cb6d29ebc3ab61ff0ff1745", "commit_訊息": "[ISO]修正歸檔浮水印新增的字型設定產生的設定值與BCL8不相容造成中文字變方框", "提交日期": "2024-09-25 17:28:45", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}