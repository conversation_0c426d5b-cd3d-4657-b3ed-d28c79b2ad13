# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.2.3_1
- **舊版本**: 5.6.2.2
- **生成時間**: 2025-07-28 18:21:01
- **新增 Commit 數量**: 76

## 變更摘要

### joseph (19 commits)

- **2017-04-12 10:28:32**: C01-20170314003 後續查無此狀況，將code復原[匯入的舊版表單，定義中欄位底色變成黑色]
  - 變更檔案: 1 個
- **2017-03-30 14:54:56**: C01-20170329002 舊版流程 invoke跳過關卡異常，輸入密碼後無反應
  - 變更檔案: 1 個
- **2017-03-30 09:15:36**: A00-*********** 修正 離職人員維護作業 查詢出的結果與查詢日期條件有異 ORACLE語法修正
  - 變更檔案: 1 個
- **2017-03-29 14:17:48**: A00-20170329001 修正 :追蹤流程時，只要沒有符合條件的流程，就會錯誤
  - 變更檔案: 1 個
- **2017-03-27 16:09:25**: S00-20170314002 二次修正:增加判斷子部門裡，不能有人設為主部門，才能失效
  - 變更檔案: 1 個
- **2017-03-27 16:04:20**: C01-20170322005 新增:組織設計師，刪除部門時卡控
  - 變更檔案: 6 個
- **2017-03-27 16:00:39**: 新增 : 組織同步，在進行刪除、失效部門時，會判斷是否有人設為主部門(含子部門)，有則不能刪除或失效
  - 變更檔案: 8 個
- **2017-03-27 14:20:13**: C01-20170322008 oracle版本組織設計師撈出資料異常 SQL語法錯誤
  - 變更檔案: 1 個
- **2017-03-24 13:41:35**: 修復 蓋掉的  2e48b178 查詢、維護樣版Code
  - 變更檔案: 1 個
- **2017-03-20 11:11:41**: C01-20170314003 修正匯入的舊版表單，定義中欄位底色變成黑色
  - 變更檔案: 1 個
- **2017-03-20 10:39:56**: C01-20170206002 區分通知、公開轉寄、私人轉寄的判斷邏輯
  - 變更檔案: 1 個
- **2017-03-20 10:37:06**: C01-20170220002 修正cursor殘留，導致超過max，而無法連接資料庫
  - 變更檔案: 1 個
- **2017-03-20 10:23:03**: C01-*********** 二次修正:判斷日期，只看起始日期
  - 變更檔案: 1 個
- **2017-03-17 16:26:10**: S00-20161021001 加簽頁面文字敘述變更需求
  - 變更檔案: 3 個
- **2017-03-15 09:54:05**: A00-20170220001 修正:從通知開啟流程，在重載頁籤時，頁面被截掉
  - 變更檔案: 1 個
- **2017-03-15 09:46:19**: S00-20170314002 新增:當部門失效時，檢核是否有員工設定該單位為主部門時，忽略已離職員工
  - 變更檔案: 1 個
- **2017-03-10 15:41:00**: C01-*********** 新增Ess銷假單有Grid時的接口，並且修改刪除缺席紀錄邏輯
  - 變更檔案: 3 個
- **2017-03-03 17:17:54**: A00-20170217001 修正:簽核流設計師 指定活動後，會異動其他活動的設定
  - 變更檔案: 1 個
- **2017-02-22 16:08:38**: Q00-20170222001 修正:ISO表單在表單多語系維護作業，無法做修改，跳出無法取得EJB服務
  - 變更檔案: 1 個

### yamiyeh10 (1 commits)

- **2017-04-07 18:20:53**: Q00-20170407002-修正Grid新增、修改、刪除按鈕
  - 變更檔案: 1 個

### jerry1218 (11 commits)

- **2017-04-07 15:45:03**: A00-20170309001 BUG調整
  - 變更檔案: 1 個
- **2017-04-07 15:42:42**: A00-20170309001 BUG調整
  - 變更檔案: 1 個
- **2017-03-27 18:13:15**: 修正預測流程掛多表單時分支判斷的BUG
  - 變更檔案: 1 個
- **2017-03-23 14:43:33**: Q00-20170316001 程式調整
  - 變更檔案: 3 個
- **2017-03-17 14:43:24**: 新增及修改T100 Form
  - 變更檔案: 42 個
- **2017-03-17 10:26:14**: Q00-20170316001 修正流程設計師儲存流程時,如因流程名稱長度過長儲存失敗rollback不全問題
  - 變更檔案: 5 個
- **2017-03-09 17:32:37**: A00-20170309001 修正T100整合,抽單時判斷可撤銷人員權限錯誤(應判斷流程發起者,但原本錯誤判斷為T100開單時的登入者)
  - 變更檔案: 1 個
- **2017-03-09 17:22:28**: Q00-20170309001 修正 1.簡易流程圖-點擊已結束關卡如果是核決層級,會出現系統管理員 2.完整流程圖-核決權限關卡沒辦法查看內含子關卡
  - 變更檔案: 6 個
- **2017-03-06 09:54:12**: 流程圖IF ELSE連接線顏色實做 , IF為紅色ELSE為藍色 註:因為BPM流程設計師的流程圖是存放在ProcessDefinition中,故未來版更後需要簽出,將連接線點開後按確認才會變色
  - 變更檔案: 2 個
- **2017-03-01 14:45:42**: S00-*********** 未發起流程預測 , 簡易流程圖及完整流程圖皆支援已儲存表單一併傳至後端解析
  - 變更檔案: 13 個
- **2017-02-22 14:40:10**: 修正流程內容-顯示流程終止人員異常(未顯示)
  - 變更檔案: 1 個

### Gaspard (7 commits)

- **2017-04-05 15:27:02**: 修正查詢樣版 開始包含與結束包含邏輯相反的問題
  - 變更檔案: 1 個
- **2017-03-23 16:27:14**: 修正無法使用資料選取器的問題
  - 變更檔案: 1 個
- **2017-03-21 18:28:59**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-03-21 18:28:31**: 修正日期元件的運算子條件為大於等於開始日期且小於等於結束日期
  - 變更檔案: 1 個
- **2017-03-20 09:16:16**: 查詢、維護樣版
  - 變更檔案: 37 個
- **2017-03-15 15:54:04**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-03-15 15:53:34**: 修改程式排版
  - 變更檔案: 1 個

### pinchi_lin (10 commits)

- **2017-03-31 18:24:41**: 修正BPMAPP中ESS表單附件下載錯誤問題
  - 變更檔案: 2 個
- **2017-03-31 15:14:54**: 修正BPMAPP中若元件勾選必填，在派送時不會彈出訊息問題
  - 變更檔案: 1 個
- **2017-03-30 19:12:00**: 修正BPMAPP若元件有設定計算公式，在APP上會沒效果的問題
  - 變更檔案: 1 個
- **2017-03-30 15:49:54**: 修正BPMAPP發起流程時若有設定驗證則無法發起問題
  - 變更檔案: 1 個
- **2017-03-24 17:28:45**: 修正BPMAPP使用連結進入表單畫面後，點擊下一筆或繼續簽核下一筆時會出現工作取回失敗訊息的問題
  - 變更檔案: 1 個
- **2017-03-24 10:18:15**: 修正BPMAPP，MobileOAuthWeChatUser資料表中的UserOID有空白問題
  - 變更檔案: 1 個
- **2017-03-24 10:11:18**: 修正BPMAPP向前與向後加簽多語系顯示錯誤問題
  - 變更檔案: 1 個
- **2017-03-17 15:42:13**: 修正待辦的附件上傳畫面，關閉不會導到附件清單問題(待確認、先還原)
  - 變更檔案: 1 個
- **2017-03-16 17:30:03**: 修正待辦的附件上傳畫面，關閉不會導到附件清單問題
  - 變更檔案: 1 個
- **2017-03-02 17:27:53**: 修正WEB表單設計師，在切換設計行動版表單時，新增元件的位置會生成在太下面的問題
  - 變更檔案: 1 個

### wayne (15 commits)

- **2017-03-31 14:22:31**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-03-31 14:22:03**: 修正未掛表單時，開啟待辦事項錯誤
  - 變更檔案: 1 個
- **2017-03-30 18:22:23**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-03-30 18:21:22**: 修正流程圖顏色異常
  - 變更檔案: 1 個
- **2017-03-30 11:54:41**: A00-***********  修正流程圖預先解析人員錯誤
  - 變更檔案: 5 個
- **2017-03-27 14:49:52**: S00-*********** waynechang 調整從草稿開啟表單時，需提示"附件需重新上傳
  - 變更檔案: 2 個
- **2017-03-27 14:45:43**: S00-*********** waynechang 調整從草稿開啟表單時，需提示"附件需重新上傳"
  - 變更檔案: 2 個
- **2017-03-24 12:06:44**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-03-24 12:06:00**: 修正[T100&BPM] 組織同步的錯誤訊息
  - 變更檔案: 1 個
- **2017-03-23 10:46:32**: 修正 T100 組織同步的錯誤訊息
  - 變更檔案: 1 個
- **2017-03-09 14:33:47**: Q00-20170124001  修正匯入WorkFlow流程時，當webService的WSDL錯誤時無法開啟畫面
  - 變更檔案: 1 個
- **2017-03-08 10:07:03**: 修正webservice呼叫退回重瓣(reexecuteActivity)，在簽核歷程上沒有呈現退回處理者 C01-20170222003
  - 變更檔案: 1 個
- **2017-03-08 09:29:48**: 修改BPMRsrcBundle5622.xls檔案名稱
  - 變更檔案: 1 個
- **2017-02-24 14:50:32**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-02-24 14:49:05**: A00-20170222001  調整開啟草稿時，將附件資訊移除(附件需要重新上傳，因應多個流程主機無法複製暫存的附件檔案)
  - 變更檔案: 1 個

### WenCheng (4 commits)

- **2017-03-31 10:11:17**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-03-31 10:07:19**: Q00-20170215003 在簽核流程設計師中，於簽核樣版增加「服務任務」(Invoke)關卡設定時，已可正常更新至掛勾的流程。
  - 變更檔案: 1 個
- **2017-03-30 17:18:50**: Q00-20170330001 簽核流程設計師在刪除T100流程定義時，已可正常執行脫勾簽核樣版的動作。
  - 變更檔案: 1 個
- **2017-03-13 17:53:52**: A00-*********** 產品開啟RTX客製功能時，攔截到Exception時，不再往外拋出訊息
  - 變更檔案: 1 個

### LALA (5 commits)

- **2017-03-14 16:00:03**: S00-***********[內部]即時轉檔從指定文件主機取ISOsource檔
  - 變更檔案: 5 個
- **2017-03-01 17:55:19**: A00-20170213001[枝江奥美]修正簡易查詢因程式判斷多語系有異常造成搜尋不到文件的問題。
  - 變更檔案: 1 個
- **2017-03-01 10:10:32**: C01-20170125001[台欣生物]修正預設的格式通知信件出現&nbsp的字串。
  - 變更檔案: 1 個
- **2017-02-24 13:03:33**: A00-20170202001[奥美医疗]修正排程ISODailyJob執行後未將應生效文件的狀態更改成生效
  - 變更檔案: 1 個
- **2017-02-24 11:41:28**: A00-20170111002[上品]修正表單設定成列印模式，必須點選列印按鈕才成功完成工作步驟。
  - 變更檔案: 4 個

### arielshih (4 commits)

- **2017-03-08 09:24:11**: 調整erm資訊內容...
  - 變更檔案: 1 個
- **2017-03-08 09:12:40**: 5622發版時，確認移除項目(產品不維護)
  - 變更檔案: 27 個
- **2017-02-22 19:14:38**: S00-20161227001
- **2017-02-22 19:13:26**: S00-20161227001
  - 變更檔案: 4 個

## 詳細變更記錄

### 1. C01-20170314003 後續查無此狀況，將code復原[匯入的舊版表單，定義中欄位底色變成黑色]
- **Commit ID**: `aeb836bddf1aeec94dda92d235dec7dae97cef5f`
- **作者**: joseph
- **日期**: 2017-04-12 10:28:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 2. Q00-20170407002-修正Grid新增、修改、刪除按鈕
- **Commit ID**: `e9d70a0ff4ed0f73773fe1accfa9258145ab588b`
- **作者**: yamiyeh10
- **日期**: 2017-04-07 18:20:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`

### 3. A00-20170309001 BUG調整
- **Commit ID**: `224522259b402e825db96bb24874a4ca4fea8195`
- **作者**: jerry1218
- **日期**: 2017-04-07 15:45:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`

### 4. A00-20170309001 BUG調整
- **Commit ID**: `092ecf311de6211f5c7c539671075edf2a40a85f`
- **作者**: jerry1218
- **日期**: 2017-04-07 15:42:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`

### 5. 修正查詢樣版 開始包含與結束包含邏輯相反的問題
- **Commit ID**: `57ca8a733f43b3edfa5aa68fb451147645bc7da2`
- **作者**: Gaspard
- **日期**: 2017-04-05 15:27:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 6. 修正BPMAPP中ESS表單附件下載錯誤問題
- **Commit ID**: `33be4385af5d53de767b7a181f333033a1bb264e`
- **作者**: pinchi_lin
- **日期**: 2017-03-31 18:24:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`

### 7. 修正BPMAPP中若元件勾選必填，在派送時不會彈出訊息問題
- **Commit ID**: `9dc97ace0c3542c7181d4ea148af5ccf9efdf744`
- **作者**: pinchi_lin
- **日期**: 2017-03-31 15:14:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`

### 8. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `80ab7ee947a32252e514804694cfac2dd0358af9`
- **作者**: wayne
- **日期**: 2017-03-31 14:22:31
- **變更檔案數量**: 0

### 9. 修正未掛表單時，開啟待辦事項錯誤
- **Commit ID**: `232cbb11ebe3d5d04b44959a8e6c15fac3919b58`
- **作者**: wayne
- **日期**: 2017-03-31 14:22:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 10. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `17d972be2b21f52342f95a5a1e2e670ca6e24206`
- **作者**: WenCheng
- **日期**: 2017-03-31 10:11:17
- **變更檔案數量**: 0

### 11. Q00-20170215003 在簽核流程設計師中，於簽核樣版增加「服務任務」(Invoke)關卡設定時，已可正常更新至掛勾的流程。
- **Commit ID**: `24f3f0e66fd541177a53899e6575249da768bc64`
- **作者**: WenCheng
- **日期**: 2017-03-31 10:07:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 12. 修正BPMAPP若元件有設定計算公式，在APP上會沒效果的問題
- **Commit ID**: `f175d9c62703f551c99f1e3124639bd6cc286a37`
- **作者**: pinchi_lin
- **日期**: 2017-03-30 19:12:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`

### 13. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `304289f288db426ea7891b412d2ffea1b404cd9f`
- **作者**: wayne
- **日期**: 2017-03-30 18:22:23
- **變更檔案數量**: 0

### 14. 修正流程圖顏色異常
- **Commit ID**: `ac2ab8af07190501d43e8d40c4f609ccb27beed3`
- **作者**: wayne
- **日期**: 2017-03-30 18:21:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 15. Q00-20170330001 簽核流程設計師在刪除T100流程定義時，已可正常執行脫勾簽核樣版的動作。
- **Commit ID**: `df250443ee4cbe06ca1d4dd7d3430a3dc2359939`
- **作者**: WenCheng
- **日期**: 2017-03-30 17:18:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/CMManager.java`

### 16. 修正BPMAPP發起流程時若有設定驗證則無法發起問題
- **Commit ID**: `c8e7778782e8753ad667e9c217fec72f41f746fb`
- **作者**: pinchi_lin
- **日期**: 2017-03-30 15:49:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`

### 17. C01-20170329002 舊版流程 invoke跳過關卡異常，輸入密碼後無反應
- **Commit ID**: `e457bafda7daea9f81002b8ccbafe94533448e3a`
- **作者**: joseph
- **日期**: 2017-03-30 14:54:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceAutoAgentActivity.jsp`

### 18. A00-***********  修正流程圖預先解析人員錯誤
- **Commit ID**: `62e7f19488f982f1f7eff8d6a7a2aca3b07dcd1e`
- **作者**: wayne
- **日期**: 2017-03-30 11:54:41
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 19. A00-*********** 修正 離職人員維護作業 查詢出的結果與查詢日期條件有異 ORACLE語法修正
- **Commit ID**: `651230c2adf8e3e30286dbabc0b9eaf7a3b527eb`
- **作者**: joseph
- **日期**: 2017-03-30 09:15:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java`

### 20. A00-20170329001 修正 :追蹤流程時，只要沒有符合條件的流程，就會錯誤
- **Commit ID**: `b11096083bc36c948ecfd1f8331610d5f28b7d5a`
- **作者**: joseph
- **日期**: 2017-03-29 14:17:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 21. 修正預測流程掛多表單時分支判斷的BUG
- **Commit ID**: `b0d38f74ba553193eb756813639516e14324c541`
- **作者**: jerry1218
- **日期**: 2017-03-27 18:13:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 22. S00-20170314002 二次修正:增加判斷子部門裡，不能有人設為主部門，才能失效
- **Commit ID**: `3c2099771df51e2b6cd5dbcfcc1199c3aaf8a6bd`
- **作者**: joseph
- **日期**: 2017-03-27 16:09:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/InvalidOrgUnitAction.java`

### 23. C01-20170322005 新增:組織設計師，刪除部門時卡控
- **Commit ID**: `9fca16f78b37a34019ec2bdda048604fe7440a5b`
- **作者**: joseph
- **日期**: 2017-03-27 16:04:20
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/DeleteOrgUnitAction.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/Actions.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/Actions_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/Actions_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/Actions_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/Actions_zh_TW.properties`

### 24. 新增 : 組織同步，在進行刪除、失效部門時，會判斷是否有人設為主部門(含子部門)，有則不能刪除或失效
- **Commit ID**: `e8a231f9358235444eda4d469d444154624c7257`
- **作者**: joseph
- **日期**: 2017-03-27 16:00:39
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`

### 25. S00-*********** waynechang 調整從草稿開啟表單時，需提示"附件需重新上傳
- **Commit ID**: `2df7e3365f32eaec394b50b34a359819634f8363`
- **作者**: wayne
- **日期**: 2017-03-27 14:49:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5623.xls`

### 26. S00-*********** waynechang 調整從草稿開啟表單時，需提示"附件需重新上傳"
- **Commit ID**: `88a45ce6249099e5684df680288176a8d1fcf6cc`
- **作者**: wayne
- **日期**: 2017-03-27 14:45:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 27. C01-20170322008 oracle版本組織設計師撈出資料異常 SQL語法錯誤
- **Commit ID**: `6582b5d7431b059e6bd6ac4373f460dc7777d8d2`
- **作者**: joseph
- **日期**: 2017-03-27 14:20:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`

### 28. 修正BPMAPP使用連結進入表單畫面後，點擊下一筆或繼續簽核下一筆時會出現工作取回失敗訊息的問題
- **Commit ID**: `e86b3810f83049e28821854ffa20293632e5faaf`
- **作者**: pinchi_lin
- **日期**: 2017-03-24 17:28:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 29. 修復 蓋掉的  2e48b178 查詢、維護樣版Code
- **Commit ID**: `b5d58d27832b0b598c20f97c2e5d91d0ea2541a7`
- **作者**: joseph
- **日期**: 2017-03-24 13:41:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 30. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `2d35c78af30924ab119887063f10bc83cf8b0dd9`
- **作者**: wayne
- **日期**: 2017-03-24 12:06:44
- **變更檔案數量**: 0

### 31. 修正[T100&BPM] 組織同步的錯誤訊息
- **Commit ID**: `b1877df8f5ea46f2c728d39a5e9c100b0bcb11b2`
- **作者**: wayne
- **日期**: 2017-03-24 12:06:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgBean.java`

### 32. 修正BPMAPP，MobileOAuthWeChatUser資料表中的UserOID有空白問題
- **Commit ID**: `c81f116eff9e5e00b35c7eaa9d54a8f08da20a7f`
- **作者**: pinchi_lin
- **日期**: 2017-03-24 10:18:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`

### 33. 修正BPMAPP向前與向後加簽多語系顯示錯誤問題
- **Commit ID**: `068cfe4bcc874dd8bf345f5facc722870a7cce8a`
- **作者**: pinchi_lin
- **日期**: 2017-03-24 10:11:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`

### 34. 修正無法使用資料選取器的問題
- **Commit ID**: `f7f22be56c665eeb061e1fa898bd5bbcf0c9932b`
- **作者**: Gaspard
- **日期**: 2017-03-23 16:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 35. Q00-20170316001 程式調整
- **Commit ID**: `c0b86d290fe8d6bcc8a10644af5b72776787b3fd`
- **作者**: jerry1218
- **日期**: 2017-03-23 14:43:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/persistence/src/com/dsc/nana/persistence/PersistentObjectHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 36. 修正 T100 組織同步的錯誤訊息
- **Commit ID**: `1f5fa08175d5ee8efede57130f8e9bc94b03754f`
- **作者**: wayne
- **日期**: 2017-03-23 10:46:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgBean.java`

### 37. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `eeb8f8e59bc7fe92552ad6b48b26c94103feda2d`
- **作者**: Gaspard
- **日期**: 2017-03-21 18:28:59
- **變更檔案數量**: 0

### 38. 修正日期元件的運算子條件為大於等於開始日期且小於等於結束日期
- **Commit ID**: `b5341a1529d96c1011f6b8506bf83778c6f34377`
- **作者**: Gaspard
- **日期**: 2017-03-21 18:28:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/js/QueryTemplate.js`

### 39. C01-20170314003 修正匯入的舊版表單，定義中欄位底色變成黑色
- **Commit ID**: `1e05b8c49f5bc07416d8d8dc272f3aa18599d599`
- **作者**: joseph
- **日期**: 2017-03-20 11:11:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 40. C01-20170206002 區分通知、公開轉寄、私人轉寄的判斷邏輯
- **Commit ID**: `5809df51d5ec8b1e6d8dbb38f5eac8d3fa9a1873`
- **作者**: joseph
- **日期**: 2017-03-20 10:39:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 41. C01-20170220002 修正cursor殘留，導致超過max，而無法連接資料庫
- **Commit ID**: `4b0739cd06af9fbcfc91a539a0517d26c84a82a4`
- **作者**: joseph
- **日期**: 2017-03-20 10:37:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 42. C01-*********** 二次修正:判斷日期，只看起始日期
- **Commit ID**: `09dda9a9b07c4914ebe73f34df6ee40ac69ad725`
- **作者**: joseph
- **日期**: 2017-03-20 10:23:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 43. 查詢、維護樣版
- **Commit ID**: `2e48b17827771e69489c0e701d25b333308c7b0c`
- **作者**: Gaspard
- **日期**: 2017-03-20 09:16:16
- **變更檔案數量**: 37
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/CannotAccessWarnning.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/UserInfo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/BpmTable.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/QueryDesinger.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/bootstrap/bootstrap-3.3.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_222222_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_2e83ff_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_454545_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_888888_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/images/ui-icons_cd0a0a_256x240.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui-EFGP.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.structure.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/css/jquery-ui-1.11.4.custom/jquery-ui.theme.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/AccessRight.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/BpmTable.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/QueryTemplate.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/bootstrap/bootstrap-3.3.4.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/bootstrap/bootstrapTable/bootstrap-table-1.11.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/bootstrap/bootstrapTable/bootstrap-table-en-US-1.9.1.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/jquery-1.11.3.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/jqueryUI/jquery-ui-1.11.4.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/jqueryUI/jquery.ui-contextmenu.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/js/json2.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/rescBunble/QueryTemplate_en_US.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/rescBunble/QueryTemplate_zh_CN.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomModule/rescBunble/QueryTemplate_zh_TW.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 44. S00-20161021001 加簽頁面文字敘述變更需求
- **Commit ID**: `100d86400ca605a7b8906b7e773bdad4255ca0cb`
- **作者**: joseph
- **日期**: 2017-03-17 16:26:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5623.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 45. 修正待辦的附件上傳畫面，關閉不會導到附件清單問題(待確認、先還原)
- **Commit ID**: `ae091754b731b00ec556fa81290df4a4694a7d74`
- **作者**: pinchi_lin
- **日期**: 2017-03-17 15:42:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 46. 新增及修改T100 Form
- **Commit ID**: `64149045fe5c19da1d84ed78df666e6f20c02690`
- **作者**: jerry1218
- **日期**: 2017-03-17 14:43:24
- **變更檔案數量**: 42
- **檔案變更詳細**:
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\272\244\346\230\223\345\260\215\350\261\241\345\207\206\345\205\245\347\266\255\350\255\267\344\275\234\346\245\255(apmt801).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\272\244\346\230\223\345\260\215\350\261\241\350\255\211\347\205\247\347\225\260\345\213\225\347\266\255\350\255\267\344\275\234\346\245\255(apmt820).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\347\224\263\350\253\213\344\275\234\346\245\255(apmt200).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\200\237\350\262\250\345\207\272\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt542).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\205\266\344\273\226\346\207\211\344\273\230\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt301).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\223\201\350\263\252\346\252\242\351\251\227\345\226\256(aqct300).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\223\201\350\263\252\347\225\260\345\270\270\347\224\263\350\253\213\344\275\234\346\245\255(aqct310).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\226\256\344\270\200\344\270\273\344\273\266ECN(abmt300).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\345\220\210\347\264\204\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt481).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\346\224\266\350\262\250\344\275\234\346\245\255(apmt521).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\350\251\242\345\203\271\344\275\234\346\245\255(apmt421).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\216\241\350\263\274\351\251\227\351\200\200\344\275\234\346\245\255(apmt561).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\247\224\345\244\226\346\240\270\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt441).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\256\242\346\210\266\347\224\263\350\253\213\344\275\234\346\245\255(axmt200).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\207\211\344\273\230\345\276\205\346\212\265\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt340).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\345\273\266\346\234\237\350\256\212\346\233\264\347\224\263\350\253\213\344\275\234\346\245\255(astt803).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\347\225\260\345\213\225\347\224\263\350\253\213\344\275\234\346\245\255(astt801).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\347\265\202\346\255\242\347\224\263\350\253\213\344\275\234\346\245\255(astt805).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\350\262\273\347\224\250\345\204\252\346\203\240\347\224\263\350\253\213(astt802).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\350\262\273\347\224\250\346\250\231\346\272\226\350\256\212\346\233\264\344\275\234\346\245\255(astt806).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\213\233\345\225\206\347\247\237\350\263\203\345\220\210\347\264\204\351\235\242\347\251\215\350\256\212\346\233\264\347\224\263\350\253\213(astt804).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\220\210\347\264\204\345\226\256(apmt480).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt490).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt530).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256(apmt520).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256(apmt510).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\226\231\344\273\266\350\243\275\347\250\213\350\263\207\346\226\231\346\226\260\345\242\236\343\200\201\344\277\256\346\224\271\347\224\263\350\253\213\344\275\234\346\245\255(aect801).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\240\270\345\203\271\345\226\256(apmt440).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\205\245\345\272\253\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt532).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\204\241\346\216\241\350\263\274\346\224\266\350\262\250\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt522).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\204\241\350\250\202\345\226\256\345\207\272\350\262\250\347\266\255\350\255\267\344\275\234\346\245\255(axmt541).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\207\237\351\201\213\346\223\232\351\273\236ECN\347\266\255\350\255\267\344\275\234\346\245\255(abmt310).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\347\207\237\351\201\213\346\223\232\351\273\236\345\244\232\344\270\273\344\273\266ECN\347\266\255\350\255\267\344\275\234\346\245\255(abmt311).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\251\242\345\203\271\345\226\256(apmt420).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\253\213\350\263\274\350\256\212\346\233\264\345\226\256(apmt410).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\263\207\347\224\242\345\244\226\351\200\201\346\224\266\345\233\236\347\266\255\350\255\267\344\275\234\346\245\255(afat450).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\263\207\347\224\242\345\244\226\351\200\201\347\266\255\350\255\267\344\275\234\346\245\255(afat440).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\263\207\347\224\242\346\224\271\350\211\257\347\266\255\350\255\267\344\275\234\346\245\255(afat508).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\344\274\260\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt400).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt450).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\345\240\261\345\203\271\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axmt410).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\212\267\345\224\256\346\240\270\345\203\271\345\226\256(axmt420).form"`

### 47. Q00-20170316001 修正流程設計師儲存流程時,如因流程名稱長度過長儲存失敗rollback不全問題
- **Commit ID**: `54885ab271c7839f47065e99a1551f507bf7b51c`
- **作者**: jerry1218
- **日期**: 2017-03-17 10:26:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/AutoDeployServicesBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 48. 修正待辦的附件上傳畫面，關閉不會導到附件清單問題
- **Commit ID**: `dd2dcf6cb7636f0a7f6a2d556aaff8fae2ff88ce`
- **作者**: pinchi_lin
- **日期**: 2017-03-16 17:30:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 49. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `c19cded372d8f79437528edd32ce805c54d4896f`
- **作者**: Gaspard
- **日期**: 2017-03-15 15:54:04
- **變更檔案數量**: 0

### 50. 修改程式排版
- **Commit ID**: `0c838d54e68113f8a0b2a80ce9c02e023bac1bd8`
- **作者**: Gaspard
- **日期**: 2017-03-15 15:53:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`

### 51. A00-20170220001 修正:從通知開啟流程，在重載頁籤時，頁面被截掉
- **Commit ID**: `d48f6c016961da26a8f90bb42c9151b4b7c88a7c`
- **作者**: joseph
- **日期**: 2017-03-15 09:54:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/BasicTypeHandler.jsp`

### 52. S00-20170314002 新增:當部門失效時，檢核是否有員工設定該單位為主部門時，忽略已離職員工
- **Commit ID**: `f4a79337734293ff29c7f2131d79dbb971026a20`
- **作者**: joseph
- **日期**: 2017-03-15 09:46:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/action/InvalidOrgUnitAction.java`

### 53. S00-***********[內部]即時轉檔從指定文件主機取ISOsource檔
- **Commit ID**: `3a9d3923aa0733c1b8ee73122dfd33372fbea1e1`
- **作者**: LALA
- **日期**: 2017-03-14 16:00:03
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISODocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/server_manager/ServerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 54. A00-*********** 產品開啟RTX客製功能時，攔截到Exception時，不再往外拋出訊息
- **Commit ID**: `0205cc75e8516af76b2bab5071e0d60644215bc9`
- **作者**: WenCheng
- **日期**: 2017-03-13 17:53:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`

### 55. C01-*********** 新增Ess銷假單有Grid時的接口，並且修改刪除缺席紀錄邏輯
- **Commit ID**: `4bf550c2117b510786ea044669de02bcdb332fc9`
- **作者**: joseph
- **日期**: 2017-03-10 15:41:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`

### 56. A00-20170309001 修正T100整合,抽單時判斷可撤銷人員權限錯誤(應判斷流程發起者,但原本錯誤判斷為T100開單時的登入者)
- **Commit ID**: `ec04714deadf08cbf1f4c23fc78862b418ba0e86`
- **作者**: jerry1218
- **日期**: 2017-03-09 17:32:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`

### 57. Q00-20170309001 修正 1.簡易流程圖-點擊已結束關卡如果是核決層級,會出現系統管理員 2.完整流程圖-核決權限關卡沒辦法查看內含子關卡
- **Commit ID**: `743f780a02dc396ca7803088bd8b6f6244b76e15`
- **作者**: jerry1218
- **日期**: 2017-03-09 17:22:28
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`

### 58. Q00-20170124001  修正匯入WorkFlow流程時，當webService的WSDL錯誤時無法開啟畫面
- **Commit ID**: `6c2eeb80dc0503cc933d7eb4848fc12b0c5599ad`
- **作者**: wayne
- **日期**: 2017-03-09 14:33:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/WSInvocationEditorController.java`

### 59. 修正webservice呼叫退回重瓣(reexecuteActivity)，在簽核歷程上沒有呈現退回處理者 C01-20170222003
- **Commit ID**: `1a313e55f44895cb54e71438eb473cfdcb727290`
- **作者**: wayne
- **日期**: 2017-03-08 10:07:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 60. 修改BPMRsrcBundle5622.xls檔案名稱
- **Commit ID**: `c6e44259b1522b8f0a2dd90a8e6552bff94a33b5`
- **作者**: wayne
- **日期**: 2017-03-08 09:29:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle_nextVersion.xls`

### 61. 調整erm資訊內容...
- **Commit ID**: `e3419e67ac547c0110c729d25b6b692c2468d290`
- **作者**: arielshih
- **日期**: 2017-03-08 09:24:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `9.TableSchemaERM/Attachment.erm`

### 62. 5622發版時，確認移除項目(產品不維護)
- **Commit ID**: `e4597a3a4ecad09b9473c61149547684f34295f2`
- **作者**: arielshih
- **日期**: 2017-03-08 09:12:40
- **變更檔案數量**: 27
- **檔案變更詳細**:
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/form-default/\345\212\240\347\217\255\347\224\263\350\253\213\345\226\256.form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/form-default/\346\264\275\345\205\254\347\224\263\350\253\213\345\226\256.form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/form-default/\350\253\213\345\201\207\347\224\263\350\253\213\345\226\256.form"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/form-default/\351\212\267\345\201\207\347\224\263\350\253\213\345\226\256.form"`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomJsLib/attendance.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomJsLib/ds_j.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomMultilanguage/language.en_US.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomMultilanguage/language.zh_CN.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomMultilanguage/language.zh_TW.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomMultilanguage/multilanguageUtil.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomOpenWin/Matt_LeaveRecord.jsp`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomOpenWin/Matt_UnitLeaveRecord.jsp`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/openWin-source/CustomOpenWin/Matt_VisitRecord.jsp`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/process-default/xpdl/\345\212\240\347\217\255\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/process-default/xpdl/\346\264\275\345\205\254\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/process-default/xpdl/\350\253\213\345\201\207\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - 📄 **重新命名**: `"6.Deployment/DeploymentPlan/copyfiles/@matt(hideIt)/process-default/xpdl/\351\212\267\345\201\207\347\224\263\350\253\213\344\275\234\346\245\255.process"`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/form-default/ManageIssue.form`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/form-default/QualityIssue.form`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/openWin-source/CustomJsLib/wards_j.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/openWin-source/CustomMultilanguage/language.en_US.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/openWin-source/CustomMultilanguage/language.zh_CN.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/openWin-source/CustomMultilanguage/language.zh_TW.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/openWin-source/CustomMultilanguage/multilanguageUtil.js`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/openWin-source/CustomOpenWin/WarOpenWinSingle.jsp`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/process-default/xpdl/Manage Issue.process`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/copyfiles/@warroom(hideIt)/process-default/xpdl/Quality Issue.process`

### 63. 流程圖IF ELSE連接線顏色實做 , IF為紅色ELSE為藍色 註:因為BPM流程設計師的流程圖是存放在ProcessDefinition中,故未來版更後需要簽出,將連接線點開後按確認才會變色
- **Commit ID**: `a11362a447210d551675bb7805817986a075fa87`
- **作者**: jerry1218
- **日期**: 2017-03-06 09:54:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 64. A00-20170217001 修正:簽核流設計師 指定活動後，會異動其他活動的設定
- **Commit ID**: `f4efd5cdcc8d74ed42118d38bb6e43045d9cfe56`
- **作者**: joseph
- **日期**: 2017-03-03 17:17:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskChooserController.java`

### 65. 修正WEB表單設計師，在切換設計行動版表單時，新增元件的位置會生成在太下面的問題
- **Commit ID**: `f956083b91b36fae0cd80e2b966197b6c14cd753`
- **作者**: pinchi_lin
- **日期**: 2017-03-02 17:27:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 66. A00-20170213001[枝江奥美]修正簡易查詢因程式判斷多語系有異常造成搜尋不到文件的問題。
- **Commit ID**: `a6e58a0c7f19992796252fcdd299585905ffc7cd`
- **作者**: LALA
- **日期**: 2017-03-01 17:55:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocForQueryViewer.java`

### 67. S00-*********** 未發起流程預測 , 簡易流程圖及完整流程圖皆支援已儲存表單一併傳至後端解析
- **Commit ID**: `60af20c391663e357da5dfef44c510967e33cb6b`
- **作者**: jerry1218
- **日期**: 2017-03-01 14:45:42
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcher.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessPreviewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`

### 68. C01-20170125001[台欣生物]修正預設的格式通知信件出現&nbsp的字串。
- **Commit ID**: `1f621fff9cabc7efa7cd0195938de5a3c9f0c558`
- **作者**: LALA
- **日期**: 2017-03-01 10:10:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 69. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a8b823355ee7aba0ddc829da69055949a8178add`
- **作者**: wayne
- **日期**: 2017-02-24 14:50:32
- **變更檔案數量**: 0

### 70. A00-20170222001  調整開啟草稿時，將附件資訊移除(附件需要重新上傳，因應多個流程主機無法複製暫存的附件檔案)
- **Commit ID**: `b74fd1d509da8c86721510ea02de149fe434241e`
- **作者**: wayne
- **日期**: 2017-02-24 14:49:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 71. A00-20170202001[奥美医疗]修正排程ISODailyJob執行後未將應生效文件的狀態更改成生效
- **Commit ID**: `06615b5d5d68375e881735f972f0a3adf4ff00e5`
- **作者**: LALA
- **日期**: 2017-02-24 13:03:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/BackJobListReaderImpl.java`

### 72. A00-20170111002[上品]修正表單設定成列印模式，必須點選列印按鈕才成功完成工作步驟。
- **Commit ID**: `007743bb87f18b61878244d23d5e044c27806ccb`
- **作者**: LALA
- **日期**: 2017-02-24 11:41:28
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkStepViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 73. S00-20161227001
- **Commit ID**: `2592962b3443432b34b9419a6589dbd61ea77e71`
- **作者**: arielshih
- **日期**: 2017-02-22 19:14:38
- **變更檔案數量**: 0

### 74. S00-20161227001
- **Commit ID**: `5227d9521021eae1d162f0ece7e78326f30ec999`
- **作者**: arielshih
- **日期**: 2017-02-22 19:13:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOCancelBatch001.form`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOModBatch.form`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\346\211\271\346\254\241\344\275\234\345\273\242\347\224\263\350\253\213.bpmn"`
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\346\211\271\346\254\241\350\256\212\346\233\264\347\224\263\350\253\213.bpmn"`

### 75. Q00-20170222001 修正:ISO表單在表單多語系維護作業，無法做修改，跳出無法取得EJB服務
- **Commit ID**: `4a3da45b9b98e09d69bc4e8866b336dc53f92a9e`
- **作者**: joseph
- **日期**: 2017-02-22 16:08:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`

### 76. 修正流程內容-顯示流程終止人員異常(未顯示)
- **Commit ID**: `57f928a4c60a6eed764b3ed9a058a6d8b6f712c4`
- **作者**: jerry1218
- **日期**: 2017-02-22 14:40:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

