{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.7.6.3", "date": "tag 5.7.6.3\nTagger: 張詠威 <<EMAIL>>\n\n20191028 17:30 lastbuild2019-10-28 15:21:22", "message": "增加 mtsmodule00000000000000000000001 之模組icon", "author": "<PERSON>"}, "舊分支": {"branch_name": "5.7.6.2_1", "date": "tag 5.7.6.2_1\nTagger: 張詠威 <<EMAIL>>\n\n20190815 1000 lastbuild2019-08-15 09:42:24", "message": "Q00-20190815001 修正/v1/process/invokeprocess發起流程的接口判斷表單數量異常", "author": "wayne<PERSON>"}, "比較時間": "2025-07-28 17:55:23", "新增commit數量": 180, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "8db9dec6c5051dae89ef9bf2fe181cb73ad22595", "commit_訊息": "增加 mtsmodule00000000000000000000001 之模組icon", "提交日期": "2019-10-28 15:21:22", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/_getModuleIcon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "773e27e08748c906089494474897a6c319d4822f", "commit_訊息": "A00-20190215003 組織設計師的部門核決層級修改，層級數字會亂跳", "提交日期": "2019-10-28 10:26:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "acfac406c13139764aee36ff9c87c6ed5d0a8fea", "commit_訊息": "補修正<第一次> S00-20180611002 從SQL代號排序調整成>>DB代號加SQL代號排序", "提交日期": "2019-10-25 18:15:52", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c204222a61ed0c54f3adde9769596af6da5573db", "commit_訊息": "A00-20190726001 調整響應式表單於mobile時，仍出現主旨欄位讓使用者可填寫", "提交日期": "2019-10-25 18:08:47", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7bfa3dd15bd8a7fc4b5c6e6b172ff46c2271c828", "commit_訊息": "A00-20191023002 簽核流程設計師增加儲存時檢查畫面與後端物件是否一致才能儲存", "提交日期": "2019-10-25 15:54:42", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/CMManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d0ad650db66959c88855ca24eae2a028601ddd9e", "commit_訊息": "V00-20191025001 修正移動端查看ESS表單附件時會卡控表單名稱", "提交日期": "2019-10-25 11:38:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cfd46fdb0eecdd0cb5fc740673997ff9e37b8b15", "commit_訊息": "Q00-20191024001 補修正", "提交日期": "2019-10-24 17:19:07", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9087ef75559eda52c6154e3b4c83b4319e9d6d16", "commit_訊息": "調整專案order順序讓設計師能直接以eclipse開啟", "提交日期": "2019-10-24 16:40:06", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/.classpath", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6ea65ff6239964c0dab4a6bfc3287bb246bd287a", "commit_訊息": "Q00-20191024001 從被代理通知信進入BPM，無法取得流程及表單內容、無法取回工作", "提交日期": "2019-10-24 16:00:52", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e04bb96b6c4d58347895295902bdab12adaa1583", "commit_訊息": "[補修正]Q00-20190826005", "提交日期": "2019-10-24 12:31:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f66e2b9280f4d61846107042a69d254a822894cb", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-10-24 12:23:56", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "e57fd6309bacd11d1dab86dcf3de8b2577383fd2", "commit_訊息": "Q00-20190826005 表單設計師RWD使用title元件，顯示文字為空，再點選字體大小，LOGO會看不到", "提交日期": "2019-10-24 12:23:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TitleElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "259e59d9f4c4ae1f10020b86f621d6c56e8f73cc", "commit_訊息": "補修正<第三次>S00-*********** 增加首次Trigger功能與發起結案紀錄、修正資料庫欄位長度", "提交日期": "2019-10-24 11:12:44", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/monitor/BamProcessRecord.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "be91bf208602c9c4d1353d72c0b8c4db2e721900", "commit_訊息": "補修正<第三次>S00-*********** 修正SQL、修正排程異常、維護作業日期改為可手動輸入", "提交日期": "2019-10-23 18:42:45", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_bpm.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "fb49a83b027e6662eedc54d0f042563d402062c0", "commit_訊息": "補修正<第二次>S00-*********** 調整Log、解決跨交易鎖表、解決時間顯示問題", "提交日期": "2019-10-23 09:41:35", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BamManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Bam.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "de6493b1522f8bacecb6a3076cbfe66291f0c055", "commit_訊息": "V00-20191022001 調整企業微信詳情頁面浮動按鈕畫面", "提交日期": "2019-10-22 17:02:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aba84fed8017c8d779deb6ea5a0e5680959735da", "commit_訊息": "Q00-20190719001 二次調整Web表單設計師的行動版表單預覽註明彈窗裝置螢幕尺寸", "提交日期": "2019-10-22 16:12:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d71381304616bd28e8a0d217a6bee6647cb95cce", "commit_訊息": "V00-20191021003 修正BPM RESTful API問題", "提交日期": "2019-10-22 15:57:30", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessTraceListParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessTraceTotalParameterRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "9469206e91c3490b770f0342448a97efc09e3b9a", "commit_訊息": "Q00-20191008001 修正IMG測試社區在智能快簽應用無法使用同意或不同意按鈕問題", "提交日期": "2019-10-22 11:54:02", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "432e6e7960bb7ccebd5096f193201d26f69a034b", "commit_訊息": "V00-20191021002 修正在IMG中間層上開啟ESS附件會顯示錯誤訊息", "提交日期": "2019-10-22 09:37:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22d6eb7f371529bd187bab7e7f18d8819f093abb", "commit_訊息": "[補修正]A00-20181129001", "提交日期": "2019-10-21 18:41:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "03f9d5d42dfa2134daafdf0e3c5a14585820f7b6", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-10-21 18:20:42", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "f29773dc5336acda8b151e7a35428c2f17bb11af", "commit_訊息": "[補修正]A00-20181129001", "提交日期": "2019-10-21 18:20:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76a86cd63d7ee3366881e0d0e23dfb28c7ef583d", "commit_訊息": "修正英文列印表單失敗", "提交日期": "2019-10-21 18:18:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "2a23315893b7d5b5745b04cb747aa7d6e50d0b5a", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-10-21 18:04:28", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0f5d6ea2ec8f33a426b39f4b6d5578031d3522b5", "commit_訊息": "[補修正]A00-***********", "提交日期": "2019-10-21 18:03:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3cb85c4de240b5d716592452d6fb63c6a36da8b7", "commit_訊息": "補修正<第一次>S00-*********** 測試Code忘記拿掉", "提交日期": "2019-10-21 18:02:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "83d489f058f0a067dc021973836ba4e8b0a87270", "commit_訊息": "S00-*********** 監控流程設定、異動流程清單、整理異動流程清單排程", "提交日期": "2019-10-21 17:49:32", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/BamManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/monitor/BamProcessRecord.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/monitor/BamSetting.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamRecordMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Bam.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BamMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_bpm.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 18}, {"commit_hash": "25548c7712724fcb4816006c8a5b944403a941e5", "commit_訊息": "獨立模組增加透過session傳遞參數的方法", "提交日期": "2019-10-21 11:51:47", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b1cb06a49c81ee56b7465ada83491a7ee79cd5fc", "commit_訊息": "V00-20191018001 修正因單選隱藏導致多選按鈕區塊消失", "提交日期": "2019-10-18 17:53:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "19d5dbbb6ed82634d940683d14e5ae432bd683a8", "commit_訊息": "A00-20181129001 使用Ladp帳號，從Tiptop點選簽核狀況，連結至BPM上登入會出現請洽系統管理員", "提交日期": "2019-10-18 10:38:43", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1197758bf674d16860cddc9952f3dfbb5c4c4420", "commit_訊息": "優化IMG取得中間層資訊效能", "提交日期": "2019-10-18 09:40:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "704679ffa566b299a8010c89a170ae4c3a26bf37", "commit_訊息": "移除NaNaISO.properties", "提交日期": "2019-10-17 16:47:25", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaISO.properties", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 1}, {"commit_hash": "c8f627cb3e4f611645c8436beadf8541bd722626", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-10-17 16:03:44", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0f2aa8f189274ba3c5bfc5b3078f9a47a4b74a64", "commit_訊息": "ISO新版取消ISO.prop設定檔", "提交日期": "2019-10-17 16:03:23", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d96954cef8e2cb4055e4631ed8e35c50f74b966e", "commit_訊息": "C00-*********** 修正透過Sync同步ISO文件後，執行文件變更單時，無法載入附件", "提交日期": "2019-10-17 15:54:41", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/DocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "eebaa74aa44d0ee3fa061ef46181539027f2efb7", "commit_訊息": "A00-20191017001調整英文字", "提交日期": "2019-10-17 15:13:47", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "53f622ac6ea4a02fadc337be947056f5d56e732a", "commit_訊息": "A00-*********** 修正組織設計師在部門人員多的情況點選離職，離職日期視窗等很久才出現", "提交日期": "2019-10-17 10:46:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "02d64919bea0a242c3acd683f9a521754f3ae251", "commit_訊息": "[補修正]A00-20190709001", "提交日期": "2019-10-16 11:03:18", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "28619cf9bfff950c6477dc10f2d2fcd5a530f370", "commit_訊息": "A00-20190709001 修正連接線從否則變條件型別無法更改為黑色", "提交日期": "2019-10-16 10:59:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0687f3729d60d1da849b99fd48be06604db3dd5d", "commit_訊息": "nana.database.type設定(MSSQL/Oracle)改成從standalone-full.xml內NaNaDS的driver取", "提交日期": "2019-10-16 00:37:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d40df264df2b0ac0632ffd54d0203811b3d2df19", "commit_訊息": "補上update sql遺漏的--，統一寫法", "提交日期": "2019-10-15 18:03:24", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9645bd81475803fa4195f0666b35c9b515381c48", "commit_訊息": "移除update sql內--base on 5.6.5.3的備註", "提交日期": "2019-10-15 18:01:04", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.2.2_AppForm_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@appform-essplus/update/5.7.2.2_AppForm_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_MSSQL_2_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.1.1_DML_Oracle_2_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.5.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@plm/update/5.7.2.2_PLM_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@plm/update/5.7.2.2_PLM_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 34}, {"commit_hash": "a818c9c35ecf0e98adcbba031c1c73c132e52e8f", "commit_訊息": "Q00-20191015001 光碟內容移除bcl相關 jar", "提交日期": "2019-10-15 15:07:55", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/module.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93b82d2c2b98f12d1fa246432bd94a1fb5fd44b5", "commit_訊息": "Q00-20191015001 光碟內容移除bcl相關 jar", "提交日期": "2019-10-15 15:07:25", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/easypdf-jacob.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/easypdf.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/jacob.jar", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 3}, {"commit_hash": "b1460c10528d8f99b5f06eba9256efa5499b5ba0", "commit_訊息": "更新Oracle jdbc的版本與設定", "提交日期": "2019-10-15 11:41:18", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/module.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/ojdbc6.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/ojdbc6_11g_releae2.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/ojdbc7_12c_release1.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/jdbc/oracle/main/ojdbc8_12c_release2.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "543aad7471e4bdcc3fed32d0ecb2b5a6fa74d089", "commit_訊息": "調整錯誤資訊", "提交日期": "2019-10-14 17:43:05", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc920c13a57acc8596d7750a41aac09fe485a6a1", "commit_訊息": "新增腳本樣版內容", "提交日期": "2019-10-09 09:53:46", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e1846bf295743749dc2699178e3f30786a9e19d5", "commit_訊息": "C01-20190929001 簽核流程設計師多語系調整", "提交日期": "2019-10-08 15:13:47", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "1fb7c97c058977e785c18f7df5859d68e21cee0e", "commit_訊息": "A00-20191007001 修正IMG的提醒功能標題時間日期錯誤問題", "提交日期": "2019-10-08 13:59:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e4bb7defc019e532c1da93caf0e53e2a55c75b06", "commit_訊息": "C01-20190912003 修正企業微信在iOS手機查看附件時若包含不合法字元會urlencode導致異常", "提交日期": "2019-10-08 11:03:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "30c59e869be4ddb07d0a9737f2a1d4014bfee8a8", "commit_訊息": "C01-20190912003 修正鼎捷移動在iOS手機查看附件時若包含不合法字元會urlencode導致異常", "提交日期": "2019-10-08 10:46:26", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "9a551d0a8ea9b6c95c2bc3993d825ff459d50087", "commit_訊息": "Q00-20190625001 <57>修正手機端在日期元件設定比對今天條件時功能異常問題", "提交日期": "2019-10-07 13:59:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppDateTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "3f0acea75a112d166f691d858918b3abd9c9ee5c", "commit_訊息": "A00-20190531001 修正流程代理人儲存筆數異常", "提交日期": "2019-10-05 16:48:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/table/FlowSubstituteTableController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4a74c81937842e1c7c2296043e63e9ad4a335ee5", "commit_訊息": "A00-20190828001 修正上傳的附件不符會出現上傳成功的訊息", "提交日期": "2019-10-05 11:24:29", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "45d8cb7673b19f11d49f9162700530a41644637d", "commit_訊息": "mail_sso功能-T100相關程式微調整", "提交日期": "2019-10-03 17:48:57", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9d23d895452bd01947ed04798c2ee09e06cacf6d", "commit_訊息": "取消S00-20190925003的相關調整，57版不會有問題，還原回先前版本", "提交日期": "2019-10-03 11:21:48", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bf372fcde7dad14cd9628d54d477fe4cb7d109f7", "commit_訊息": "mail_sso功能-conf微調", "提交日期": "2019-10-03 10:44:59", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa26c83ffb787fb5b5d08a98080b89673858dc65", "commit_訊息": "A00-20190923001 修正重複執行登入request，造成使用者登入後畫面無法正確導入", "提交日期": "2019-10-02 18:01:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2be2bac6c861d59afae86732fc260617e9bdb33c", "commit_訊息": "移除多餘System.out.println", "提交日期": "2019-10-02 17:20:51", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "884e642180f7d7e368f8a9987e3b7c58a26dd33a", "commit_訊息": "mail_SSO功能", "提交日期": "2019-10-02 17:20:02", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "a930d85251f3c668c96dc2c3653e1ad79b46d49b", "commit_訊息": "C01-20190904001 修正第二次mail登入時，關卡設定簽核意見必填異常", "提交日期": "2019-09-26 14:22:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "bf5f96e6a87adc3e5fc634e68f9afa91b9c874f0", "commit_訊息": "Q00-20190926001 修正移動授權中間層依token取使用者的方法異常導致無法登入問題", "提交日期": "2019-09-26 14:09:41", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d1b2d8c7d0709fbe4f77f07135061ef15ff5f54a", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-09-26 14:06:14", "作者": "pinchi_lin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "839a7c50e0be8152cb770ea1323ba979d6b3bfa2", "commit_訊息": "Q00-20190926001 修正移動授權中間層依token取使用者的方法異常導致無法登入問題", "提交日期": "2019-09-26 14:05:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5f81bc34c5f0db3882a4d0027d94febac0275485", "commit_訊息": "Q00-20190926003 修正簽核流設計器-刪除T100簽核樣板跟簽核樣板分類的訊息多語系消失", "提交日期": "2019-09-26 14:05:16", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "36eb13deb74ff3d9ca16193818572b3f71462995", "commit_訊息": "//S00-20190925003 lorenchang T100流程樣版人員及通知任務改成支持掛載T100表單", "提交日期": "2019-09-26 11:34:50", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "86d7b2305fecb8f42c3f3a490923012e0d8bc0c5", "commit_訊息": "A00-20190612002 修正小畫面時模擬流程的下關使用者沒更新左邊區塊上面的使用者資料", "提交日期": "2019-09-25 11:47:09", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de5108f582ac5b9d95c35028f8c4b40d4658a870", "commit_訊息": "Q00-20190821002 調整移動端客製開窗多選開窗下方按鈕跑版調整", "提交日期": "2019-09-25 11:13:37", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "16a6809d84867a1f61b584247fea5502cb4c49dc", "commit_訊息": "新增IMG批次簽核列表接口與批次簽核功能", "提交日期": "2019-09-24 18:13:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleBatchOpReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleBatchOpRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchOpMsgRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchOpReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterBatchOpRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataBatchOpReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataBatchOpRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "e79c8527959949ed93cbf8d225c74cf3c0a24d08", "commit_訊息": "Q00-20190812004 修正IMG從快簽進祥情表單後擱置按鈕不會出現的問題", "提交日期": "2019-09-24 15:43:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a9d6fb6b44164472f90211707315f81b9349e7d", "commit_訊息": "Q00-20190917001 修正行動端預覽功能", "提交日期": "2019-09-23 19:18:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "eaa7c29ea9f0f606f22b08be3eb0fecfb8020c74", "commit_訊息": "補修正<第二次>C01-20190917001 調整變數範圍避免操作兩次轉XML", "提交日期": "2019-09-23 17:36:34", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9954c9c0cdd11623af3547870ca57d182a8f928d", "commit_訊息": "C01-20190917001 補上呼叫WS失敗LOG 原本並無流程資訊無法分辨是哪次req失敗", "提交日期": "2019-09-23 16:55:36", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "47c8dedddef22a3c74b8c89ade1f41da6cfcabe7", "commit_訊息": "A00-20190911001 調整ESS-Invoke 判斷setStatus03邏輯", "提交日期": "2019-09-23 14:12:15", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "35b138eef60f7969adf215d912a98c72f71877fd", "commit_訊息": "Q00-20190821002 調整移動端客製開窗滑動到最下方要查看資料會無法看到完整資訊", "提交日期": "2019-09-23 10:41:33", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "573a8d82a641f8c4d691476609fed1d6227c7519", "commit_訊息": "Q00-20190903001 補上漏修正IMG我的關注篩選條件接口", "提交日期": "2019-09-23 10:12:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7f12be0ef00ea24636f82b8037d06a73ddf1bc5e", "commit_訊息": "C01-20190920001 修正CRM開單時單身空白處理邏輯異常導致表單畫面單身資料無法載入問題", "提交日期": "2019-09-23 09:46:27", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "84f9f628b8a19195044fa32323d5ac12d37ab697", "commit_訊息": "整理程式邏輯", "提交日期": "2019-09-20 16:37:50", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9eac61a33cc72154c85f0400f7296feffdae14e", "commit_訊息": "還原T100FormMerge.java", "提交日期": "2019-09-20 16:07:17", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/T100FormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2beef4396d5b60cbbd52a3f4b600a4d2215dc0d8", "commit_訊息": "Q00-20190903001 修正IMG我的關注統計元件數量與處理的流程列表筆數不一致問題", "提交日期": "2019-09-20 15:38:01", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d9a3a55fa851a78920a826a70b5d9bcd33af352e", "commit_訊息": "Q00-20190909001 修正已轉派流程發起時間顯示異常問題", "提交日期": "2019-09-20 14:41:08", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "addba78682aeb98ad83b9e99526070c068610267", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-09-20 11:12:12", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "2afd67a22e999d93e60d15dc7d7e5d53e7d8a916", "commit_訊息": "Q00-20190920001 修正列印關卡中，絕對位置表單列印失效", "提交日期": "2019-09-20 11:11:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0737a50d18318668e7cafb835d196d05013ec921", "commit_訊息": "A00-20190425002 修正活動的進階只有勾選允許使用者增加前一活動卻可以向後加", "提交日期": "2019-09-20 11:01:44", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e216d3357b33a9015435c05dac9bb63a1eced32", "commit_訊息": "Q00-20190916001 調整移動授權中間層使用者維護匯入結果字體顏色", "提交日期": "2019-09-19 15:14:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "65df2fdc94b537e305122e81125fd44b7d727e85", "commit_訊息": "Q00-20190823001 調整行動簽核管理中心內各模組顯示卡控", "提交日期": "2019-09-19 11:07:42", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a6dafd69713f80f0c2ed1023643acc084547b5ae", "commit_訊息": "A00-20190903002 修正人工任務關卡加簽，預覽流程圖無法顯示", "提交日期": "2019-09-18 18:01:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc4921aec2317b9300ac300244fc83928ffd854d", "commit_訊息": "V00-20190918001 修正T100整合表單加入Image元件後會導致發單失敗", "提交日期": "2019-09-18 17:27:05", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12fd3ef7586b471de2d888ac8d01d14867a767d8", "commit_訊息": "S00-20180611002 SQL註冊器的排序改用SQL代號做排序", "提交日期": "2019-09-18 11:02:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "78592947954a3cb5e229e5071b030a48be9a3dd6", "commit_訊息": "調整BPM信件連結ExtraLogin帶使用者Id參數時，允許修改UserId欄位", "提交日期": "2019-09-17 16:33:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e7b93c33cc0cef7691c9ba89eccead5ec791e2f0", "commit_訊息": "Q00-20190916002 調整IMG快速簽核頁面上一關處理者的顯示機制", "提交日期": "2019-09-17 11:23:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "7ba1a4f74eefeabd2569198b30954ab26b3b0e79", "commit_訊息": "S00-20180515001 程式權限設定 套用範圍新增所有人員選項 另外修正三個BUG", "提交日期": "2019-09-16 19:18:29", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ProgramAccessRightVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2148ca21318784ec2b0eed3cfd9f0b404381a4d1", "commit_訊息": "A00-20190430002修正客製程式-表單選取人員出現無法派送的原因", "提交日期": "2019-09-16 18:50:37", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa2317e223caecfd87b6aeb49d9c5f5cf9580725", "commit_訊息": "V00-20190912004 修正T100整合,如原已轉換為RWD表單,又執行重要欄位同步的話,整個表單會因為轉回絕對位置,排版變很亂", "提交日期": "2019-09-16 16:10:19", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/T100FormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a8b9dbb9605e4f610ce8ac4d466e86f4eff813da", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-09-16 15:52:16", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "3c03a5c96bbf1b43933d478205e8094449233b04", "commit_訊息": "Q00-20190827001 修正元件SubTab在頁籤點擊+時，會產生大量欄位樣版", "提交日期": "2019-09-16 15:51:28", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93edd6298b6e49448e925edd13138aa0ea599f40", "commit_訊息": "修正bpm tool切換成英文語系時，帳號設定的多語系位置顯示不一致", "提交日期": "2019-09-16 15:36:29", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8ff8c6d413d63cf9ae01b0412d6e5548a16dd7be", "commit_訊息": "C00-20190910001 修正離職人員維護作業，若點擊人員下一頁時，系統發生錯誤。", "提交日期": "2019-09-16 14:21:23", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ac0fe0113db2f68f6fb71e3e0170f6d12771a4d", "commit_訊息": "Q00-20190719001 調整Web表單設計師的行動版表單預覽註明彈窗裝置螢幕尺寸", "提交日期": "2019-09-16 11:28:34", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "60884f2adb9965b67f7de817b5ca613d95339339", "commit_訊息": "A00-20190614001 處理手持裝置操作時，點取回重辦無法操作", "提交日期": "2019-09-12 18:46:55", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e9b02b204cd92b7757054b349a46561e15dd423b", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-09-12 16:06:37", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a0c5e293e4e193e9221a4979567e4b1c6c1475e4", "commit_訊息": "ISO新版取消ISO.prop設定檔", "提交日期": "2019-09-12 16:06:19", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f128c45a95513c745bab84bdab9efaf4227a9a28", "commit_訊息": "新增移動授權中間層連線資訊管理後端驗證連線功能", "提交日期": "2019-09-12 16:04:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "673433aa92db4fa7155fe9ab715517c1aa96e107", "commit_訊息": "V00-20190909038 (v58下修至v57)修正關注流程無法匯出Excel的異常", "提交日期": "2019-09-12 11:42:16", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "decdd9da0ad4aa5d1a68599fb138dbb1544b2ac8", "commit_訊息": "將更新T100整設合設定的語法直接放到create Sql內", "提交日期": "2019-09-12 10:09:21", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@t100/create/InitT100DB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@t100/create/InitT100DB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "342a8e595bb9a589d662eb30d1649cc0ab290831", "commit_訊息": "A00-20190730008修正列印RWD表單title元件的Image列印問題", "提交日期": "2019-09-11 19:53:36", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "791a33be20e4763a23ee73d724b040e9de07b638", "commit_訊息": "新增移動授權中間層連線資訊管理前端驗證連線狀態畫面", "提交日期": "2019-09-11 18:32:38", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b8298b4e5bf0b3a8d893908cf1556bbab1cf783e", "commit_訊息": "V00-20190909036 修正撤銷流程功能,關卡有多處理者時顯示筆數不對", "提交日期": "2019-09-11 15:55:29", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7400c86bd7ed2726dbbefea39508b1dce48cf7bb", "commit_訊息": "修正待辦開簽核畫面優化導致的畫面顯示問題", "提交日期": "2019-09-11 15:54:27", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "999b5aaf6b32bb607d0ec017a65eb79f7f770a4c", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-09-11 11:05:12", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "fc19f8d23928fe4a34da5854abbd33c739d9be29", "commit_訊息": "C01-20190321001 修正轉派後發送通知，顯示工作內容失效", "提交日期": "2019-09-11 11:04:44", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba59b5b4d086735db75d9c4feae1cdf36849f233", "commit_訊息": "調整widfly出貨預設上傳附件大小為100MB 補Oracle版", "提交日期": "2019-09-11 10:42:32", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full_Oracle.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "052890df96cac217af7fc75cabb49a9e1d501edb", "commit_訊息": "調整widfly出貨預設上傳附件大小為100MB", "提交日期": "2019-09-11 10:39:00", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6ebc79d0631faab5cafd81a2a46289f0032b9dd5", "commit_訊息": "V00-20190909016 修正某些電腦在[選取使用權限]的Dialog高度不夠導致看不到[是否包含子目錄]", "提交日期": "2019-09-10 22:51:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/accessCtrl/AccessCtrlMgrPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "888673b2ff574764a938b77bbf45f106cdd82eb8", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-09-10 17:46:39", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "2c5fb88a6643a9424699e0acb0a73ad65d5f6033", "commit_訊息": "補修正<第二次>A00-20190826001 開會後決議補上附件相關操作到有機會用到的頁面", "提交日期": "2019-09-10 17:45:16", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a76a7999db5095630aad2a8cea0d8ed9921abd36", "commit_訊息": "調整IMG在快速簽核與詳情的上一關卡機制", "提交日期": "2019-09-10 16:52:14", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "d058fd0108b727ab7530f0e49516c6f3f7fed22d", "commit_訊息": "調整移動授權中間層使用者匯入功能使用者改抓對應的多語系姓名", "提交日期": "2019-09-10 16:14:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b26333728e5e4cbc14e901afb174a5082cc92cd9", "commit_訊息": "Q00-20190910001 修正移動授權中間層使用者刪除後資料不會刷新問題", "提交日期": "2019-09-10 15:51:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6cd56bcd049b75a23d69b928a521987f02372363", "commit_訊息": "新增移動授權中間層使用者匯入功能", "提交日期": "2019-09-10 15:05:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserImport.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "984816b345bdb89359c4f72a5ef4c32c8c270b66", "commit_訊息": "修正支援BPM驗證功能merge錯誤問題", "提交日期": "2019-09-10 10:50:38", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "028fc3c674fab3fe9f10ac6a0d87903ea1316322", "commit_訊息": "A00-20190614001 修正手持裝置操作時，點取回重辦後沒有出現提示訊息的問題。", "提交日期": "2019-09-09 19:18:03", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4154f6b977221c5c7fde952af8de5607079f3dcd", "commit_訊息": "A00-20190715003 修正系統設定為不可更改密碼時，但還能改密碼的問題。", "提交日期": "2019-09-09 18:45:47", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d1e63928282b7fc5816f31b715eb223ea73988af", "commit_訊息": "C01-20190904002 修正IMG處理的流程列表無法顯示問題", "提交日期": "2019-09-09 18:43:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a6675b0d6080ff25461bae88faaada69370966de", "commit_訊息": "C01-20190903002 調整PDFConverter timeOut時間", "提交日期": "2019-09-09 16:30:42", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "33fa2a56d1de120db8ca612fba0ed2f73d58ff8b", "commit_訊息": "A00-20190826001 修正點擊下載TipTop Url類型附件沒有反應的問題(代辦/追蹤)", "提交日期": "2019-09-09 16:06:55", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8ee8062b723fe85f9076c5ce7ab770ac8c36e5e8", "commit_訊息": "Q00-20190816001 修改樣版在使用上下移按鈕，單身資料無法帶入對應之欄位", "提交日期": "2019-09-09 15:22:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "600d77b846ba7971edb63c8bf29150bf2989f336", "commit_訊息": "Q00-20190906001 增加BCL8 的PDFConverter服務", "提交日期": "2019-09-06 14:08:00", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/.classpath", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/lib/PDF/easypdf.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDFConverter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/easypdf.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/lib/bcl/main/module.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.3_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "2737c418e403c25d8b73feb5bf2e60d6b9ddcf2a", "commit_訊息": "A00-20190829001 修正發起流程時，預覽流程圖無法正常顯示", "提交日期": "2019-09-05 18:34:12", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "cf99869f42e619545e3c5ac9feec2e9965429bbe", "commit_訊息": "A00-20190729001 調整條件順序 避免多人每人都要處理 已有一人處理完導致 關卡設定不能取回卻可以取回重辦", "提交日期": "2019-09-05 12:01:11", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1d3894195643c81ca85f121d90b37d4141138218", "commit_訊息": "A00-20190903003 在多語系上新增LDAP帳號設置相同時的資料", "提交日期": "2019-09-04 17:09:44", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "30e4ed988ba613eeb72777f2660aeaf1e42e5377", "commit_訊息": "C01-20190902003 修正移動端ESS表單退回重辦時無簽核意見(詳情)", "提交日期": "2019-09-03 10:58:04", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7b1cd43c9fe867a37a9741a87440dbabd23ce0a", "commit_訊息": "traceProcessMain.script.alert4 英文語系沒寫對", "提交日期": "2019-09-02 16:57:55", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3b8809fdad8b12a4ce72526a98aab71648a4e4e1", "commit_訊息": "C01-20190624003 修正核決層級關卡自動簽核異常", "提交日期": "2019-08-30 17:50:28", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "248ec20752ec3caf4e75a17cc66a00dbd01f2b22", "commit_訊息": "Q00-20190830001 waynechang 調整為取得release的流程OID", "提交日期": "2019-08-30 14:20:32", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a46bbccaf696f71c0a15e4a7e3727df49010935d", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-30 13:49:37", "作者": "gaspard.shih", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "99a3322044ea71883ac8ee3013680acd2683ac44", "commit_訊息": "A00-20190821003 修正T100表單轉換成RWD表單時的異常", "提交日期": "2019-08-30 13:49:16", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "419897a53f56d9ab292b880a8d54956eded1aa0f", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-30 12:21:33", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "415e2e5e585de1df3970fa4a6e797fdb48d58b6e", "commit_訊息": "補上 apmt530", "提交日期": "2019-08-30 12:20:34", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form-default-t100.zip", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "baa6532604afc9b29cd3bacba6e22296698db5ec", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-30 11:02:12", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "e92b55793f444227174411e537510caa107a53ad", "commit_訊息": "A00-20190523001 修正核決權限關卡參考自定義屬性異常", "提交日期": "2019-08-30 11:01:48", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c9cdb03ebd6babf1f99ebd6243d6aebd2b0a2a9", "commit_訊息": "Q00-20190826007 在 BPMRsrcBundle5763 補上 reexecuteActivityMain.script.alert2這個Key", "提交日期": "2019-08-30 10:55:04", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e4595cf05dc5d1cb1aa889030137d4910fc2ddf2", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-29 19:17:57", "作者": "peng_cheng_wang", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "92f2a3e2b9131f024a128ac5fdffe448a34d4c5e", "commit_訊息": "A00-20190718002[補修正]", "提交日期": "2019-08-29 10:42:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1e7cf0ed3857830e42c7484dba401132324ce2c1", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-28 18:08:11", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "66cfea313a0a7c39b070666d1bb13820b6adccbb", "commit_訊息": "A00-20190718002 處理逾時活動時，先判斷是否當前時間逾時活動被處理掉了", "提交日期": "2019-08-28 18:07:00", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52c038679d4ce18fa912e3378f74bdb082bdbfa6", "commit_訊息": "新增移動授權中間層連線資訊與使用者資訊支援BPM功能", "提交日期": "2019-08-28 14:49:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ecd2d2d1364af74ffb81769780d194dd8b5c0a28", "commit_訊息": "A00-20190801002 修正CRM整合流程設定流程變數PlantID時無效果", "提交日期": "2019-08-28 11:01:13", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6867c7b309db6caf990a3b7354381fa9f2341e16", "commit_訊息": "A00-20190823003 修正在IMG中間層無法開啟CRM附件的問題", "提交日期": "2019-08-28 10:08:05", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa25172a209ab3707031fc7d194beebb9c1d62c4", "commit_訊息": "Q00-20190827003 修正相對位置表單grid有設定第一個欄位為流水號時點新增資料會有編輯畫面出不來的問題", "提交日期": "2019-08-27 18:37:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d8c31fd3d29f27faff99c70c7c4bed191aab1922", "commit_訊息": "新增移動授權中間層BPM驗證登入功能", "提交日期": "2019-08-27 10:57:42", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cc9c3a9d37b3d5dc892508566ec23b3da9f93c2e", "commit_訊息": "Q00-20190826007 修正 BPMRsrcBundle 中 缺少reexecuteActivityMain.script.alert2這個Key", "提交日期": "2019-08-26 18:36:15", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "386eb08624a4e8f39523539c63e72e6c873980eb", "commit_訊息": "A00-20190823001 修正ISO透過工具匯入文件時，文件製作索引會失敗(取消從attachment取得資料的邏輯)", "提交日期": "2019-08-26 10:56:25", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fe1668555e93b27425ec9946b228df942fa9f6dd", "commit_訊息": "A00-20190823001] 修正ISO透過工具匯入文件時，文件製作索引會失敗", "提交日期": "2019-08-26 10:32:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7fb65c65fdde82ebf880462945c56594b6a3e2a1", "commit_訊息": "Q00-20190719002 修正Web表單設計師的行動版表單預覽功能", "提交日期": "2019-08-23 18:09:38", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "b065f2d0615a663303461c707c21b123a24abf3b", "commit_訊息": "A00-20190823002 修正語法錯誤", "提交日期": "2019-08-23 10:54:11", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ce8388e484105c772d4c5892976df65af527abd", "commit_訊息": "C01-20190821003 修正移動端多欄位跑版問題", "提交日期": "2019-08-22 17:45:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "54b8fef33f0c14690c7419889b5b9abe549b3b71", "commit_訊息": "新增表單函式庫功能", "提交日期": "2019-08-22 17:06:01", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "058924e137602020885baf19d1932aa61de678bf", "commit_訊息": "新增移動端支持客製Json開窗", "提交日期": "2019-08-22 17:02:00", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "6f8b1547cfdc8a00ba3cc861c44b73bd99dd0dca", "commit_訊息": "新增移動端支持Dialog元件自定義開窗", "提交日期": "2019-08-22 16:58:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d9cc25bc08fafa2f609c7b547a8c74f10218feb1", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-22 16:13:51", "作者": "BPM", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0b905556e5c7ad29ac75927d9772e6228c918404", "commit_訊息": "A00-20190409002[補修正] log調整", "提交日期": "2019-08-22 16:02:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/absFormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8ecd116ef40875e6996b28fa28e6e356da87de8c", "commit_訊息": "A00-20190422002 修正流程設計師中無法設定行動關卡問題", "提交日期": "2019-08-22 16:02:36", "作者": "BPM", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8cbc4a0f1e0e70f28d11b3727622126333e3ac87", "commit_訊息": "A00-20190813001 增加TIPTOP processTerminated的Restful服務", "提交日期": "2019-08-22 15:28:18", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/TIPTOP.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c71a68f6feede71b08368abeb76db88e16943a37", "commit_訊息": "A00-20190409002 修正FormInstance裡的maskFieldValues是空字串導致管理流程報錯", "提交日期": "2019-08-22 14:40:18", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/absFormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c7e2c2a3ab5476d650a94cdeb31f9f119bbef036", "commit_訊息": "大幅提升待辦清單進入表單畫面速度", "提交日期": "2019-08-22 14:33:19", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessContext.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/background_service/PrefetchProcessInstService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "d51925c5b038738212f316dc719a352011e71442", "commit_訊息": "A00-20190717001 修正單身加總異常", "提交日期": "2019-08-22 13:51:03", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9212bf9ae7aef74105aa732d47d36ca16ea299d8", "commit_訊息": "A00-20190704003 修正處理表單時，流程名稱多語系顯示失效問題", "提交日期": "2019-08-22 11:43:29", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "70dd464af3e6743e0e3058d94415d7d46f2e5fbe", "commit_訊息": "Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證(調整log)", "提交日期": "2019-08-22 09:52:26", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "78febba72a69da62be1f135cad74978552bfe8b3", "commit_訊息": "Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證(調整log顯示)", "提交日期": "2019-08-22 09:51:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f7b5171e1a11609f1c4dae9e79294ed9a454b98", "commit_訊息": "A00-20190805001 修正如果隱藏Grid 無法發起或儲存表單(絕對位置/RWD)", "提交日期": "2019-08-21 19:04:06", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3d6b97a11693730755c7f7cb3d8c74c76ec5200e", "commit_訊息": "Q00-20190821001 調整當User沒有ldap帳號時，預設使用SystemId進行驗證", "提交日期": "2019-08-21 18:04:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a596f3bf16bbdb52d729d11a564e4736aed66dd", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-20 18:24:39", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "afd5a9eb329151bfc000b678acb75a1ac21f67ad", "commit_訊息": "Q00-20190820002 修正樹狀開窗多選 縮小視窗沒有checkBox可勾選的問題", "提交日期": "2019-08-20 18:23:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3419c9b862cc148208cc75af4da8e8406f76e14", "commit_訊息": "A00-20190816001 範例程式調整", "提交日期": "2019-08-20 18:22:23", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "432c81f5b05643667cbcd26a20828d3036b88bad", "commit_訊息": "A00-20190816001 範例程式調整", "提交日期": "2019-08-20 16:13:59", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/RadioButtonExample.jsp", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 3}, {"commit_hash": "93bb9c1d1cc5a3f4cc11529fbc18c72b31be515c", "commit_訊息": "Q00-20190820001 修正移動授權中間層登入H5畫面與消息通知的多語系問題", "提交日期": "2019-08-20 15:49:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5763.xls", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "845d781b00b162b3148c01c7c62839da0e7d06c6", "commit_訊息": "授權中間層還原調整前內容", "提交日期": "2019-08-20 10:01:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6dd918b3c32343a7a55a5a52f413536367034ca4", "commit_訊息": "A00-20190806001 修正SUBTAB元件增加完頁籤之後，無法增加模板", "提交日期": "2019-08-15 11:07:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}