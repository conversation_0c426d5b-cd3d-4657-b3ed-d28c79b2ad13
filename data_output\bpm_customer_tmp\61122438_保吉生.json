{"company_id": "61122438", "company_name": "保吉生", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\61122438_保吉生\\01客戶基本資料", "files": [{"filename": "[保吉生] 連線資訊.txt", "raw_content": "61122438\r\n曾俊雅 <EMAIL> 02-2246-7799#816 (常常在子公司支援，比較聽得懂人話)\r\n洪羽筑 <EMAIL> #817 (不愛回信)\r\n<EMAIL> \r\n---------------------------------------------\r\nwindows VPN ************** DSC/DSCDSC\r\n---------------------------------------------\r\n測試機(5883_1021)：********** / *************\r\n正式機(5883_1021)+DB：********** / *************  \r\nRDP:administrator/admin#DSC2022\r\n皆裝NGINX，AP有兩組IP\r\n---------------------------------------------\r\n====企業行動導航====\r\n[企業行動導航後台管理]\r\nhttp://*************:8080  (***********)\r\n行動導航後台對外：**************:8080\r\nadmin / Digiwhale136\r\n \r\n=====互聯應用管理中心=====\r\n[互聯應用管理中心(CC)]\r\nhttp://*************:22610\r\n互聯應用管理中心登入： superadmin/1qaz@WSX\r\n \r\n20080009歸戶: u001/Aa12345678\r\n---------------------------------------------\r\nESS正式區：*************\r\nESS測試區：*************\r\nHR:*************\r\n---------------------------------------------\r\nTIPTOP SOAP:\r\n測試區:http://*************/web/ws/r/aws_efsrv_toptest\r\n---------------------------------------------\r\n工程:林義翔 10287", "structured_data": {"測試機(5883_1021)": "********** / *************", "正式機(5883_1021)+db": "********** / *************", "行動導航後台對外": "**************:8080", "互聯應用管理中心登入": "superadmin/1qaz@WSX", "ess正式區": "*************", "ess測試區": "*************", "rdp": "administrator/admin#DSC2022", "http": "//*************:22610", "行動導航後台對外：**************": "8080", "20080009歸戶": "u001/Aa12345678", "hr": "*************", "測試區": "http://*************/web/ws/r/aws_efsrv_toptest", "工程": "林義翔 10287", "企業行動導航": "===", "互聯應用管理中心": "====", "host": "**************"}, "source_path": "C1.客戶維護相關\\61122438_保吉生\\01客戶基本資料\\[保吉生] 連線資訊.txt", "file_size": 1225, "encoding_used": "utf-8", "processed_at": "2025-08-26T10:46:29.992419"}], "total_files": 1, "processed_at": "2025-08-26T10:46:29.992427"}