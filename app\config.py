"""
應用程式配置設定
"""
from pathlib import Path
import os
from typing import Dict, Any
import json

# 專案根目錄
PROJECT_ROOT = Path(__file__).parent.parent

# 資料目錄
DATA_OUTPUT_DIR = PROJECT_ROOT / "data_output"
BPM_PATH_DIR = DATA_OUTPUT_DIR / "bpm_path"
BPM_RELEASE_DIR = DATA_OUTPUT_DIR / "bpm_release"
BPM_CUSTOMER_DIR = DATA_OUTPUT_DIR / "bpm_customer"

# 設定檔案路徑
CONFIG_DIR = PROJECT_ROOT / "config"
PROJECTS_CONFIG_FILE = CONFIG_DIR / "projects_config.json"

# 模板和靜態檔案目錄
TEMPLATES_DIR = PROJECT_ROOT / "templates"
STATIC_DIR = PROJECT_ROOT / "static"

# 應用程式設定
APP_CONFIG = {
    "title": "BPM服務部好用工具",
    "version": "1.0.0",
    "description": "BPM 服務部開發的綜合性工具集",
    "department": "BPM服務部",
    "last_update": "2025年7月"
}

# 工具設定
TOOLS_CONFIG = {
    "release_query": {
        "name": "產品Release記錄查詢工具",
        "icon": "📊",
        "description": [
            "查詢BPM、BPM-ISO、NaNaXWeb專案的release記錄",
            "依照branch_name瀏覽commit記錄",
            "關鍵字搜尋commit訊息",
            "檔案名稱搜尋功能",
            "支援排序與分類顯示"
        ],
        "gradient": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
    },
    "file_search": {
        "name": "檔案索引路徑查詢工具",
        "icon": "🔍",
        "description": [
            "查詢class、jsp、js檔案位置",
            "支援關鍵字模糊搜尋",
            "上傳壓縮檔案批量查詢",
            "顯示檔案修改時間",
            "多版本索引支援"
        ],
        "gradient": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
    },
    "customer_connections": {
        "name": "客戶連線管理系統",
        "icon": "🏢",
        "description": [
            "管理客戶公司連線資訊",
            "支援BPM、HRM、Tiptop等多種產品",
            "卡片式介面設計",
            "原始資料參考功能",
            "完整的CRUD操作"
        ],
        "gradient": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    }
}

def load_projects_config() -> Dict[str, Any]:
    """載入專案設定檔"""
    try:
        if PROJECTS_CONFIG_FILE.exists():
            with open(PROJECTS_CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"載入專案設定檔失敗: {e}")
    
    # 返回預設設定
    return {
        "projects": {
            "BPM": {
                "name": "BPM 主專案",
                "repo_path": "D:\\IDEA_workspace\\BPM",
                "branch_patterns": {
                    "include_starts_with": ["release_", "hotfix_"],
                    "include_ends_with": [],
                    "exclude_branches": ["develop_v58"]
                }
            }
        }
    }

def ensure_directories():
    """確保必要的目錄存在"""
    directories = [
        DATA_OUTPUT_DIR,
        BPM_PATH_DIR,
        BPM_RELEASE_DIR,
        BPM_CUSTOMER_DIR,
        CONFIG_DIR,
        TEMPLATES_DIR,
        STATIC_DIR
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

# 初始化時確保目錄存在
ensure_directories()
