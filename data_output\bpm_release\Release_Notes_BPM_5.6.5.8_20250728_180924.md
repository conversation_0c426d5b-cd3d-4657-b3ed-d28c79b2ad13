# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.5.8
- **舊版本**: 5.6.5.7
- **生成時間**: 2025-07-28 18:09:24
- **新增 Commit 數量**: 104

## 變更摘要

### 林致帆 (21 commits)

- **2020-01-14 16:48:54**: C01-20200106001 修正客制開窗異常
  - 變更檔案: 1 個
- **2020-01-03 10:33:36**: 取消S00-***********相關修正，此功能目前只需新增至57，還原先前版本
  - 變更檔案: 6 個
- **2019-12-30 21:20:55**: S00-*********** 調整流程開窗增加搜尋條件
  - 變更檔案: 6 個
- **2019-12-25 10:12:56**: A00-*********** 修正流程設計師在新增流程時，不可退回關卡清單顯示的關卡數量異常
  - 變更檔案: 1 個
- **2019-12-20 09:05:53**: [補修正]A00-20190724003
  - 變更檔案: 1 個
- **2019-12-19 12:15:30**: Q00-20191031001 調整ReceiverOID為null，就不存入Mails
  - 變更檔案: 1 個
- **2019-12-17 14:12:15**: C01-20191216001 調整若使用匿名驗證方式，不讓mail.jar判斷為是執行帳號驗證動作
  - 變更檔案: 1 個
- **2019-12-12 08:37:04**: A00-20190724003 修正小於符號造成grid資料顯示異常
  - 變更檔案: 1 個
- **2019-12-10 20:13:48**: [補修正]C01-20191029004
  - 變更檔案: 1 個
- **2019-12-10 14:30:36**: C01-20191029004 修正用戶每天都會收到重複的信件
  - 變更檔案: 2 個
- **2019-11-06 14:20:21**: [補修正]C01-20190925001
  - 變更檔案: 1 個
- **2019-11-05 15:50:44**: A00-20191002002 修正有設定不可退回關卡有核決權限關卡，但在關卡的退回清單還是看到核決權限關卡
  - 變更檔案: 1 個
- **2019-11-01 20:21:57**: C01-20190925001 修正IE匯入Excel會因為url過長導致出現錯誤訊息
  - 變更檔案: 2 個
- **2019-10-28 10:31:46**: A00-20190215003 組織設計師的部門核決層級修改，層級數字會亂跳
  - 變更檔案: 1 個
- **2019-10-21 18:36:32**: [補修正]A00-***********
  - 變更檔案: 1 個
- **2019-10-21 18:34:29**: [補修正]A00-***********
  - 變更檔案: 1 個
- **2019-10-18 10:48:07**: A00-*********** 使用Ladp帳號，從Tiptop點選簽核狀況，連結至BPM上登入會出現請洽系統管理員
  - 變更檔案: 1 個
- **2019-10-17 10:55:36**: A00-*********** 修正組織設計師在部門人員多的情況點選離職，離職日期視窗等很久才出現
  - 變更檔案: 1 個
- **2019-10-16 11:44:47**: A00-20190709001 修正連接線從否則變條件型別無法更改為黑色
  - 變更檔案: 1 個
- **2019-10-05 18:01:40**: A00-20190531001 修正流程代理人儲存筆數異常
  - 變更檔案: 1 個
- **2019-09-16 19:51:18**: A00-20190807001修正客製程式-表單選取人員出現無法派送的問題
  - 變更檔案: 1 個

### yanann_chen (16 commits)

- **2020-01-13 16:48:40**: C01-20190619005 修正"搖旗吶喊小助手登入EFGP"時，ESS相關流程異常
  - 變更檔案: 2 個
- **2019-12-31 10:06:06**: A00-*********** 修正Mcloud工作事項出現已簽核過的單據
  - 變更檔案: 2 個
- **2019-10-08 14:37:01**: C01-20190929001 簽核流程設計師多語系調整
  - 變更檔案: 12 個
- **2019-09-19 12:05:01**: A00-20190903002 修正人工任務關卡加簽，預覽流程圖無法顯示
  - 變更檔案: 1 個
- **2019-09-11 11:29:19**: C01-20190321001 修正轉派後發送通知，顯示工作內容失效
  - 變更檔案: 1 個
- **2019-09-09 11:37:29**: A00-20190829001 修正發起流程時，預覽流程圖無法正常顯示
  - 變更檔案: 2 個
- **2019-09-09 10:27:46**: A00-20190523001 修正核決權限關卡參考自定義屬性異常
  - 變更檔案: 1 個
- **2019-09-05 15:47:58**: C01-20190624003 修正核決層級關卡自動簽核異常
  - 變更檔案: 1 個
- **2019-08-22 14:59:56**: A00-20190409002 修正FormInstance裡的maskFieldValues是空字串導致管理流程報錯
  - 變更檔案: 1 個
- **2019-08-07 09:39:43**: A00-20190403001 HR組織同步增加throws Exception
  - 變更檔案: 2 個
- **2019-07-04 17:24:54**: C01-20190523003 修正"流程中若有核決層級關卡，且退回的關卡設有自動簽核，當處理者重複時會觸發退回的關卡執行自動簽核"的問題
  - 變更檔案: 1 個
- **2019-05-21 16:02:49**: 列印按鈕版面調整
  - 變更檔案: 2 個
- **2019-05-17 18:21:37**: A00-20190411003 第二次補修正
  - 變更檔案: 2 個
- **2019-05-17 15:13:23**: A00-20190411003 補修正:固定隱藏列印按鈕
  - 變更檔案: 2 個
- **2019-05-17 10:44:05**: A00-20190411003修正Chrome列印絕對位置表單功能失效問題
  - 變更檔案: 2 個
- **2019-05-17 09:35:23**: 修正A00-20190509003在oracle資料庫，資料選取器無法使用問題
  - 變更檔案: 1 個

### walter_wu (14 commits)

- **2020-01-13 10:53:36**: C01-20190513001 ESS呼叫更新代理人 改為排隊 避免批簽同User同時更新導致其中一個Invoke卡住
  - 變更檔案: 3 個
- **2020-01-08 17:07:02**: C01-20191220003 修正TIPTOP 拋單超過1000筆單身會出現 StackOverflowError
  - 變更檔案: 2 個
- **2019-11-13 19:08:56**: Q00-20191113001 修正WF整合問題 BPM回寫狀態修正與產生/更新表單功能
  - 變更檔案: 1 個
- **2019-11-01 14:15:45**: A00-20191023002 簽核流程設計師增加儲存時檢查畫面與後端物件是否一致才能儲存
  - 變更檔案: 2 個
- **2019-11-01 11:47:47**: C01-20190726003 修正WebService簽核歷程如果是多人並簽時終止/退回會有少資料
  - 變更檔案: 2 個
- **2019-09-26 14:43:50**: A00-20190327001 A00-20190503001 修正客製開窗下一頁與修改筆數出現的異常
  - 變更檔案: 1 個
- **2019-09-23 17:40:39**: 補修正<第三次>C01-20190917001 調整變數範圍避免操作兩次轉XML
  - 變更檔案: 1 個
- **2019-09-23 16:47:39**: 補修正<第二次>C01-20190917001 移除上次修正Log裡有單號
  - 變更檔案: 1 個
- **2019-09-23 16:45:53**: C01-20190917001 補上呼叫WS失敗LOG並無流程資訊無法分辨是哪次req失敗
  - 變更檔案: 1 個
- **2019-09-05 18:50:03**: A00-20190729001 調整條件順序 避免多人每人都要處理 已有一人處理完導致 關卡設定不能取回卻可以取回重辦
  - 變更檔案: 1 個
- **2019-06-21 18:45:21**: C01-20190618002 調整拿掉再確認是否派送/轉派/終止 因為Chrome新版不支持 而且會有簽核意見開窗填完並確認
  - 變更檔案: 1 個
- **2019-06-20 18:07:30**: A00-20190605002 修正加簽過一次部門主管 先回到表單畫面在點進入加簽第二次時將第一次加簽改為部門
  - 變更檔案: 1 個
- **2019-05-23 10:24:08**: Q00-20190521002 T100環境代號新增兩個區域可以選擇(topstd,toppth)
  - 變更檔案: 1 個
- **2019-04-26 16:43:48**: Q00-20190426001 哲瑋發現在流程進行期間去修改流程緊急度 資料庫連線未關閉
  - 變更檔案: 1 個

### 詩雅 (7 commits)

- **2020-01-09 17:20:17**: Q00-20200109003 調整IMG新增提醒時將outer_schedule_id串流程資訊給IMG
  - 變更檔案: 2 個
- **2020-01-08 10:31:21**: [二次修正] Q00-20200106003 調整IMG各個應用列表共用的日期格式字段[多語系]
  - 變更檔案: 2 個
- **2020-01-06 21:30:42**: Q00-20200106003 調整IMG各個應用列表共用的日期格式字段
  - 變更檔案: 2 個
- **2020-01-06 21:29:19**: Q00-20200106010 修正同意派送RESTFul接口若不給formValue(不更新表單欄位)會造成表單空白問題
  - 變更檔案: 1 個
- **2020-01-06 21:27:41**: Q00-20200103004 調整企業微信詳情頁面浮動按鈕畫面
  - 變更檔案: 1 個
- **2020-01-06 10:02:33**: Q00-20200103006 修正移動端Grid的欄位名稱有雙引號時會發生錯誤問題
  - 變更檔案: 1 個
- **2020-01-03 19:03:26**: Q00-20200103002 修正移動端查看ESS表單附件時會卡控表單名稱
  - 變更檔案: 1 個

### 王鵬程 (4 commits)

- **2020-01-09 17:03:55**: A00-20190805005 修正DialogInputMulti元件產生的隱藏欄位(取的id_hdn)在第二次按傳送後，沒有把前次的值先清掉。
  - 變更檔案: 1 個
- **2019-12-18 18:23:05**: C01-20190715001 在IE下會造成欄位下移偏離原本位置
  - 變更檔案: 1 個
- **2019-12-18 10:39:44**: A00-20191204002 修正日期開窗後和Grid的欄位及欄位間重疊無法選擇日期
  - 變更檔案: 1 個
- **2019-12-06 12:45:06**: A00-20190903003 在多語系上新增LDAP帳號設置相同時的資料
  - 變更檔案: 3 個

### pinchi_lin (5 commits)

- **2020-01-06 21:09:50**: Q00-20200106009 調整IMG所有H5連結將${mobile_token}全部移除
  - 變更檔案: 5 個
- **2019-11-26 14:33:05**: 新增離職人員同步刪除企業微信通訊錄功能
  - 變更檔案: 3 個
- **2019-07-03 15:09:40**: A00-20190626003 修正IMG結案率分析統計圖因資料量大導致回應逾時問題
  - 變更檔案: 2 個
- **2019-05-24 10:19:05**: Q00-20190118009 二次修正:移除IMG發起流程清單回傳網址中串的mobile_token參數
  - 變更檔案: 1 個
- **2019-05-07 16:57:39**: C01-20190505001 修正IMG在詳情表單簽核完成後會跳到首頁問題
  - 變更檔案: 1 個

### yamiyeh10 (22 commits)

- **2020-01-06 20:39:48**: Q00-20200106008 修正附件在若包含不合法字元會urlencode導致異常
  - 變更檔案: 11 個
- **2020-01-06 20:09:17**: Q00-20200106006 修正iphone5手機在選擇發起或派送部門選項跑版問題
  - 變更檔案: 1 個
- **2020-01-06 19:18:44**: Q00-20200106004 修正企業微信編輯我的最愛流程星號顯示機制
  - 變更檔案: 1 個
- **2020-01-06 18:56:16**: Q00-20191204002 修正企業微信使用者綁定批次匯入功能填寫的入口平台並不存在時沒有錯誤訊息
  - 變更檔案: 3 個
- **2020-01-06 17:58:46**: Q00-20200106001 修正整合時因欄位為空字串導致oracle無法新增該入口平台整合資訊問題
  - 變更檔案: 1 個
- **2020-01-03 19:48:18**: Q00-20200103003 修正BPMAPP中Dialog元件若有在過濾條件填全域變數會無效果問題
  - 變更檔案: 3 個
- **2020-01-03 18:28:23**: Q00-20200103001 修正IMG若grid元件有設定中間層顯示會造成中間層空白問題
  - 變更檔案: 1 個
- **2019-11-20 12:17:29**: A00-20191119001 修正移動端通知詳情頁面流程主旨無法正確顯示問題
  - 變更檔案: 2 個
- **2019-11-08 10:26:15**: C01-20191101001 修正IMG取得進階查詢資料來源效能議題
  - 變更檔案: 5 個
- **2019-10-25 17:07:35**: C01-20191018003 修正將點擊同意派送時就喚醒遮罩避免重複點擊造成錯誤
  - 變更檔案: 1 個
- **2019-10-07 18:56:16**: Q00-20190625001 <56>修正手機端在日期元件設定比對今天條件時功能異常問題
  - 變更檔案: 4 個
- **2019-09-03 10:59:58**: C01-20190902003 修正<56>移動端ESS表單退回重辦時無簽核意見(詳情)
  - 變更檔案: 1 個
- **2019-08-29 14:37:54**: C01-20190828001 修正移動端派送異常時未丟出例外而顯示派送成功訊息
  - 變更檔案: 1 個
- **2019-08-23 14:57:46**: C01-20190822002 修正移動端客製開窗的目前頁數未更新目前為第一頁議題
  - 變更檔案: 1 個
- **2019-08-13 18:24:53**: C01-20190807003 修正移動端儲存表單機制
  - 變更檔案: 2 個
- **2019-08-13 13:47:40**: 修正<56>企業微信開啟特定excel檔案時會有亂碼問題
  - 變更檔案: 5 個
- **2019-08-13 11:45:30**: C01-20190808002 修正IMG使用iOS手機在中間層查看excel附件會呈現亂碼問題
  - 變更檔案: 7 個
- **2019-08-06 11:14:12**: C01-20190802004 修正企業微信2.8.10版本使用iOS手機操作待辦與通知畫面時出現異常訊息
  - 變更檔案: 3 個
- **2019-07-04 16:54:18**: A00-20190626004 修正查看附件會保留上一次查看的位置問題
  - 變更檔案: 20 個
- **2019-07-01 10:47:54**: A00-20190621001 修正企業微信開啟附件編碼為簡中時出現亂碼問題
  - 變更檔案: 2 個
- **2019-07-01 10:37:59**: A00-20190626005 修正檢視PDF檔案時會模糊不清晰問題
  - 變更檔案: 1 個
- **2019-05-17 16:37:34**: A00-20190516004 修正移動端推播功能需啟用系統郵件發送才能接收到推播問題
  - 變更檔案: 2 個

### cherryliao (5 commits)

- **2020-01-06 20:33:56**: Q00-20200106007 修正Android在鼎捷移動APP上個人任務應用中間層顯示異常問題
  - 變更檔案: 1 個
- **2020-01-06 20:20:33**: Merge branch 'develop_v56' of http://10.40.41.229/BPM_Group/BPM.git into develop_v56
- **2020-01-06 20:19:05**: Q00-20200106007 修正Android在鼎捷移動APP上個人任務應用中間層顯示異常問題
  - 變更檔案: 1 個
- **2020-01-06 19:39:53**: Q00-20200106005 調整向互聯取回來的使用者資訊log
  - 變更檔案: 1 個
- **2020-01-06 13:35:45**: Q00-20200106002 修正IMG的提醒功能標題時間日期錯誤問題
  - 變更檔案: 1 個

### waynechang (10 commits)

- **2019-09-23 14:14:14**: A00-20190911001 調整ESS-Invoke 判斷setStatus03邏輯
  - 變更檔案: 1 個
- **2019-08-08 14:05:16**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2019-08-08 14:04:51**: 修正T100開單時會持續占用Connection的議題
  - 變更檔案: 1 個
- **2019-07-11 09:45:54**: A00-20190709004 調整排程-關卡往下派送的服務(調整為一個小時前的關卡才允許自動往下派送，避免發生多筆同時處理同一個關卡)
  - 變更檔案: 1 個
- **2019-07-03 16:42:21**: Q00-20190703003 將文件總管的修改文件屬性的保存年限調整為保存日期。
  - 變更檔案: 3 個
- **2019-05-17 14:54:34**: A00-20190516002 調整簽核流設計師的webService服務，選擇port異常
  - 變更檔案: 1 個
- **2019-05-07 14:11:06**: Q00-20190507001 移除「流程設計師-核決權限-選擇樣板」這個按鈕
  - 變更檔案: 2 個
- **2019-05-06 15:22:29**: Q00-20190506001 移除「流程設計師-流程模型-可重定義屬性-語言、國家」這兩個屬性
  - 變更檔案: 6 個
- **2019-05-03 17:25:13**: A00-20190502001 ISO文件作廢單的瀏覽文件資料需要控制文件閱讀權限
  - 變更檔案: 1 個
- **2019-05-02 15:49:41**: A00-20190401001 修正T100通知信內容，會有部分欄位重複議題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. C01-20200106001 修正客制開窗異常
- **Commit ID**: `db0cc95d313a2c7e8d69ff49a32f1c94e459ef89`
- **作者**: 林致帆
- **日期**: 2020-01-14 16:48:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 2. C01-20190619005 修正"搖旗吶喊小助手登入EFGP"時，ESS相關流程異常
- **Commit ID**: `7ae16c477253f9502a99f6e5fb392651ac919561`
- **作者**: yanann_chen
- **日期**: 2020-01-13 16:48:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 3. C01-20190513001 ESS呼叫更新代理人 改為排隊 避免批簽同User同時更新導致其中一個Invoke卡住
- **Commit ID**: `dcce50047964f72e5bdd18569d919cd244735d5d`
- **作者**: walter_wu
- **日期**: 2020-01-13 10:53:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormSynchronized.java`

### 4. Q00-20200109003 調整IMG新增提醒時將outer_schedule_id串流程資訊給IMG
- **Commit ID**: `267574a7afab50f9096c1d530409c3afeba4907c`
- **作者**: 詩雅
- **日期**: 2020-01-09 17:20:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`

### 5. A00-20190805005 修正DialogInputMulti元件產生的隱藏欄位(取的id_hdn)在第二次按傳送後，沒有把前次的值先清掉。
- **Commit ID**: `62aefa9b270c7ea6244308e4728512fa3291d29b`
- **作者**: 王鵬程
- **日期**: 2020-01-09 17:03:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DialogInputMultiTemplate.txt`

### 6. C01-20191220003 修正TIPTOP 拋單超過1000筆單身會出現 StackOverflowError
- **Commit ID**: `6be37b86882bea0516d6491b8335d9da5abcaa8c`
- **作者**: walter_wu
- **日期**: 2020-01-08 17:07:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`

### 7. [二次修正] Q00-20200106003 調整IMG各個應用列表共用的日期格式字段[多語系]
- **Commit ID**: `6be9e985843932753652cfa7e4a59a94ea59a963`
- **作者**: 詩雅
- **日期**: 2020-01-08 10:31:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5658.xls`

### 8. Q00-20200106003 調整IMG各個應用列表共用的日期格式字段
- **Commit ID**: `42756fb1886fe5c79751daad36512fda40f51bcd`
- **作者**: 詩雅
- **日期**: 2020-01-06 21:30:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 9. Q00-20200106010 修正同意派送RESTFul接口若不給formValue(不更新表單欄位)會造成表單空白問題
- **Commit ID**: `b7adb6209ac80b111c725e5ecdfd4e2885e2aa9e`
- **作者**: 詩雅
- **日期**: 2020-01-06 21:29:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 10. Q00-20200103004 調整企業微信詳情頁面浮動按鈕畫面
- **Commit ID**: `a54be345ed0629f4a63223776af1f8f31f49e38b`
- **作者**: 詩雅
- **日期**: 2020-01-06 21:27:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`

### 11. Q00-20200106009 調整IMG所有H5連結將${mobile_token}全部移除
- **Commit ID**: `1f8b372e014ebcf6de7b6cc5544d9ab7f9bf8fa4`
- **作者**: pinchi_lin
- **日期**: 2020-01-06 21:09:50
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java`

### 12. Q00-20200106008 修正附件在若包含不合法字元會urlencode導致異常
- **Commit ID**: `362255b2c706416e2473f9a839910c355d5d20b6`
- **作者**: yamiyeh10
- **日期**: 2020-01-06 20:39:48
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 13. Q00-20200106007 修正Android在鼎捷移動APP上個人任務應用中間層顯示異常問題
- **Commit ID**: `15aa9a0ee5c02df9061d6fa914aeff49f0b918cb`
- **作者**: cherryliao
- **日期**: 2020-01-06 20:33:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java`

### 14. Merge branch 'develop_v56' of http://10.40.41.229/BPM_Group/BPM.git into develop_v56
- **Commit ID**: `2a1d4ceefbfbbebcfa700e32f956e713c0568ed8`
- **作者**: cherryliao
- **日期**: 2020-01-06 20:20:33
- **變更檔案數量**: 0

### 15. Q00-20200106007 修正Android在鼎捷移動APP上個人任務應用中間層顯示異常問題
- **Commit ID**: `7207390b70c1b86ecca1423549c29817624523a5`
- **作者**: cherryliao
- **日期**: 2020-01-06 20:19:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java`

### 16. Q00-20200106006 修正iphone5手機在選擇發起或派送部門選項跑版問題
- **Commit ID**: `dc4d4e24f304a384ad77bce49fd89734a602e9b6`
- **作者**: yamiyeh10
- **日期**: 2020-01-06 20:09:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 17. Q00-20200106005 調整向互聯取回來的使用者資訊log
- **Commit ID**: `8434c7d4e057820a1250439bc210b8eef619c4ee`
- **作者**: cherryliao
- **日期**: 2020-01-06 19:39:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 18. Q00-20200106004 修正企業微信編輯我的最愛流程星號顯示機制
- **Commit ID**: `630c23cfc0332702434783a460f98ea915696d8c`
- **作者**: yamiyeh10
- **日期**: 2020-01-06 19:18:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js`

### 19. Q00-20191204002 修正企業微信使用者綁定批次匯入功能填寫的入口平台並不存在時沒有錯誤訊息
- **Commit ID**: `2e5948e1676400e5c93596d0cc0db912f08f163e`
- **作者**: yamiyeh10
- **日期**: 2020-01-06 18:56:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5658.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 20. Q00-20200106001 修正整合時因欄位為空字串導致oracle無法新增該入口平台整合資訊問題
- **Commit ID**: `d9de3ceedb1a22ad672f2c3dbcd9c53fc5bbb827`
- **作者**: yamiyeh10
- **日期**: 2020-01-06 17:58:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 21. Q00-20200106002 修正IMG的提醒功能標題時間日期錯誤問題
- **Commit ID**: `3b215a24faa3a316de28c1a2653e4ad62e0f441c`
- **作者**: cherryliao
- **日期**: 2020-01-06 13:35:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 22. Q00-20200103006 修正移動端Grid的欄位名稱有雙引號時會發生錯誤問題
- **Commit ID**: `24835cc0a6f0b201b1bf4f1484bfbcf7cba0776e`
- **作者**: 詩雅
- **日期**: 2020-01-06 10:02:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`

### 23. Q00-20200103003 修正BPMAPP中Dialog元件若有在過濾條件填全域變數會無效果問題
- **Commit ID**: `a6d2c9c64574f52b3222eab146ee4cc48f96efa3`
- **作者**: yamiyeh10
- **日期**: 2020-01-03 19:48:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputLabelTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputMultiTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputTemplate.txt`

### 24. Q00-20200103002 修正移動端查看ESS表單附件時會卡控表單名稱
- **Commit ID**: `bdafc6e93eee3ab7e13d8856f9107f7f6a47448c`
- **作者**: 詩雅
- **日期**: 2020-01-03 19:03:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 25. Q00-20200103001 修正IMG若grid元件有設定中間層顯示會造成中間層空白問題
- **Commit ID**: `9be30ed09bcc26f6a45f5bbf9cd845edc98905e4`
- **作者**: yamiyeh10
- **日期**: 2020-01-03 18:28:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 26. 取消S00-***********相關修正，此功能目前只需新增至57，還原先前版本
- **Commit ID**: `aaeef3f5b2588a0b4bd084d96994946699b66f55`
- **作者**: 林致帆
- **日期**: 2020-01-03 10:33:36
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`

### 27. A00-*********** 修正Mcloud工作事項出現已簽核過的單據
- **Commit ID**: `a7aa188bb6c64226d989bbace19e96a25addfc35`
- **作者**: yanann_chen
- **日期**: 2019-12-31 10:06:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/GroupWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudWorkItemListReader.java`

### 28. S00-*********** 調整流程開窗增加搜尋條件
- **Commit ID**: `3e2c9aae2ac47acf91d28b2980d3d57160f717ca`
- **作者**: 林致帆
- **日期**: 2019-12-30 21:20:55
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`

### 29. A00-*********** 修正流程設計師在新增流程時，不可退回關卡清單顯示的關卡數量異常
- **Commit ID**: `90804b99c256b1340e5c86b9e829cc69c3ceb7b8`
- **作者**: 林致帆
- **日期**: 2019-12-25 10:12:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/ProcessHelper.java`

### 30. [補修正]A00-20190724003
- **Commit ID**: `e6ed1c2978799cfcc0d050448e2e3d4680917766`
- **作者**: 林致帆
- **日期**: 2019-12-20 09:05:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 31. Q00-20191031001 調整ReceiverOID為null，就不存入Mails
- **Commit ID**: `2de6160597f4ecbfb0ecd473c2ddf50d1b27daba`
- **作者**: 林致帆
- **日期**: 2019-12-19 12:15:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MailerBean.java`

### 32. C01-20190715001 在IE下會造成欄位下移偏離原本位置
- **Commit ID**: `de231e4103218f4cb5b9e19c306f46705ae85fe4`
- **作者**: 王鵬程
- **日期**: 2019-12-18 18:23:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 33. A00-20191204002 修正日期開窗後和Grid的欄位及欄位間重疊無法選擇日期
- **Commit ID**: `716dce8076b1a2bff038d5a79b9ccc9532189576`
- **作者**: 王鵬程
- **日期**: 2019-12-18 10:39:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/css/calendar/calendar-win2k-cold-1.css`

### 34. C01-20191216001 調整若使用匿名驗證方式，不讓mail.jar判斷為是執行帳號驗證動作
- **Commit ID**: `4e203ecd91d4efe99066e97d17f648f397c8e252`
- **作者**: 林致帆
- **日期**: 2019-12-17 14:12:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`

### 35. A00-20190724003 修正小於符號造成grid資料顯示異常
- **Commit ID**: `85324784b2210976be5e7e2646fc72002dd56aff`
- **作者**: 林致帆
- **日期**: 2019-12-12 08:37:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 36. [補修正]C01-20191029004
- **Commit ID**: `4dea146791cf24a01b8411d4b733002c9cba9b7d`
- **作者**: 林致帆
- **日期**: 2019-12-10 20:13:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 37. C01-20191029004 修正用戶每天都會收到重複的信件
- **Commit ID**: `0887360b42f3e7a836c6e6471cf60ee482cd25b8`
- **作者**: 林致帆
- **日期**: 2019-12-10 14:30:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/notification/MailingFrequencyType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 38. A00-20190903003 在多語系上新增LDAP帳號設置相同時的資料
- **Commit ID**: `bd4d67cd6e9fa8a9cdcf2a36f4275d51b7a1905b`
- **作者**: 王鵬程
- **日期**: 2019-12-06 12:45:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5657.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5658.xls`

### 39. 新增離職人員同步刪除企業微信通訊錄功能
- **Commit ID**: `524e980f9ac7c297b5d096ab606188566856084e`
- **作者**: pinchi_lin
- **日期**: 2019-11-26 14:33:05
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatSchedule.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`

### 40. A00-20191119001 修正移動端通知詳情頁面流程主旨無法正確顯示問題
- **Commit ID**: `8808b069f35045493f86021f35af8f49bd1a74e9`
- **作者**: yamiyeh10
- **日期**: 2019-11-20 12:17:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`

### 41. Q00-20191113001 修正WF整合問題 BPM回寫狀態修正與產生/更新表單功能
- **Commit ID**: `640d5539ae5abb324b9521ec1cf27593f8304d0f`
- **作者**: walter_wu
- **日期**: 2019-11-13 19:08:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 42. C01-20191101001 修正IMG取得進階查詢資料來源效能議題
- **Commit ID**: `5010afa1baf15c9d4a35576c444cfd5930596e6f`
- **作者**: yamiyeh10
- **日期**: 2019-11-08 10:26:15
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 43. [補修正]C01-20190925001
- **Commit ID**: `d635c09148694a4547d02b9574fc1fc4ab8e9633`
- **作者**: 林致帆
- **日期**: 2019-11-06 14:20:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 44. A00-20191002002 修正有設定不可退回關卡有核決權限關卡，但在關卡的退回清單還是看到核決權限關卡
- **Commit ID**: `3538b1e4248f8045421e650552de81bfd0d323eb`
- **作者**: 林致帆
- **日期**: 2019-11-05 15:50:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java`

### 45. C01-20190925001 修正IE匯入Excel會因為url過長導致出現錯誤訊息
- **Commit ID**: `ab851c5d2ee30a58d0d8c45229ee5d333ae6d0d8`
- **作者**: 林致帆
- **日期**: 2019-11-01 20:21:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 46. A00-20191023002 簽核流程設計師增加儲存時檢查畫面與後端物件是否一致才能儲存
- **Commit ID**: `d98db32d85920ebea097259c8fd5543a5916b2f4`
- **作者**: walter_wu
- **日期**: 2019-11-01 14:15:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/CMManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`

### 47. C01-20190726003 修正WebService簽核歷程如果是多人並簽時終止/退回會有少資料
- **Commit ID**: `bd7d5ac548fc51e2c94201aa6aad81bf0fffdd53`
- **作者**: walter_wu
- **日期**: 2019-11-01 11:47:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/PerformDetail.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/PerformInfo.java`

### 48. A00-20190215003 組織設計師的部門核決層級修改，層級數字會亂跳
- **Commit ID**: `44ca2ca10ff38c03ca4084534c2c44adcca9ef93`
- **作者**: 林致帆
- **日期**: 2019-10-28 10:31:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/maintainace/MaintainUnitLevelDialog.java`

### 49. C01-20191018003 修正將點擊同意派送時就喚醒遮罩避免重複點擊造成錯誤
- **Commit ID**: `a778e9aba599a66a8c667b75e70fb9bd86a9fb09`
- **作者**: yamiyeh10
- **日期**: 2019-10-25 17:07:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 50. [補修正]A00-***********
- **Commit ID**: `f6515c9c8fc29d40b72b2d247b828238a817e243`
- **作者**: 林致帆
- **日期**: 2019-10-21 18:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`

### 51. [補修正]A00-***********
- **Commit ID**: `d10ce77ff767cbe7cc7d2e2e2528203c1ea4eaf7`
- **作者**: 林致帆
- **日期**: 2019-10-21 18:34:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 52. A00-*********** 使用Ladp帳號，從Tiptop點選簽核狀況，連結至BPM上登入會出現請洽系統管理員
- **Commit ID**: `853b260e4c2f56341e79dd3d54b020564842afdc`
- **作者**: 林致帆
- **日期**: 2019-10-18 10:48:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 53. A00-*********** 修正組織設計師在部門人員多的情況點選離職，離職日期視窗等很久才出現
- **Commit ID**: `f280a3ee118853bc970c9185a46227dedbc1cb0f`
- **作者**: 林致帆
- **日期**: 2019-10-17 10:55:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`

### 54. A00-20190709001 修正連接線從否則變條件型別無法更改為黑色
- **Commit ID**: `fc655cb6a6c0afcfb310362a298257d825e2dea2`
- **作者**: 林致帆
- **日期**: 2019-10-16 11:44:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java`

### 55. C01-20190929001 簽核流程設計師多語系調整
- **Commit ID**: `5e08025700f991c840f4b1b08e701e4af6fff146`
- **作者**: yanann_chen
- **日期**: 2019-10-08 14:37:01
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_zh_TW.properties`

### 56. Q00-20190625001 <56>修正手機端在日期元件設定比對今天條件時功能異常問題
- **Commit ID**: `3c6026b2ad1b34fc830a6f0bf225582982a5b443`
- **作者**: yamiyeh10
- **日期**: 2019-10-07 18:56:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDateTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5658.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 57. A00-20190531001 修正流程代理人儲存筆數異常
- **Commit ID**: `8d9ac1064a859de1e265d85c98777ac0ccac5eae`
- **作者**: 林致帆
- **日期**: 2019-10-05 18:01:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/table/FlowSubstituteTableController.java`

### 58. A00-20190327001 A00-20190503001 修正客製開窗下一頁與修改筆數出現的異常
- **Commit ID**: `14265637dec56ed7bbc93fa06f69861c8335847c`
- **作者**: walter_wu
- **日期**: 2019-09-26 14:43:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 59. 補修正<第三次>C01-20190917001 調整變數範圍避免操作兩次轉XML
- **Commit ID**: `c1a6543d22981f24efec8677a3889c19f8d48987`
- **作者**: walter_wu
- **日期**: 2019-09-23 17:40:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 60. 補修正<第二次>C01-20190917001 移除上次修正Log裡有單號
- **Commit ID**: `957a62d69c9c5441570a031bff5febba130e7706`
- **作者**: walter_wu
- **日期**: 2019-09-23 16:47:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 61. C01-20190917001 補上呼叫WS失敗LOG並無流程資訊無法分辨是哪次req失敗
- **Commit ID**: `b8dc44302e1fe3aa6042a98ce75589f11a4c0229`
- **作者**: walter_wu
- **日期**: 2019-09-23 16:45:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 62. A00-20190911001 調整ESS-Invoke 判斷setStatus03邏輯
- **Commit ID**: `c790722f3716048d9b0f91f94e337b89b3cf5b07`
- **作者**: waynechang
- **日期**: 2019-09-23 14:14:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 63. A00-20190903002 修正人工任務關卡加簽，預覽流程圖無法顯示
- **Commit ID**: `ca66075e1a6e8cd8b27ab7b4b0b12d4bf937ba46`
- **作者**: yanann_chen
- **日期**: 2019-09-19 12:05:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 64. A00-20190807001修正客製程式-表單選取人員出現無法派送的問題
- **Commit ID**: `69e3f56e8f71f49aa50db9f77922d6c7c3ae3c05`
- **作者**: 林致帆
- **日期**: 2019-09-16 19:51:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`

### 65. C01-20190321001 修正轉派後發送通知，顯示工作內容失效
- **Commit ID**: `a0cdfdedc6dd7004b6f9d2a887d30e64ab32353e`
- **作者**: yanann_chen
- **日期**: 2019-09-11 11:29:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 66. A00-20190829001 修正發起流程時，預覽流程圖無法正常顯示
- **Commit ID**: `4ea5734150fde1a662e5f0bac4d69f0b73ab0f10`
- **作者**: yanann_chen
- **日期**: 2019-09-09 11:37:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 67. A00-20190523001 修正核決權限關卡參考自定義屬性異常
- **Commit ID**: `de6f1760ffae11e4aa4ad88c58e3f71b34dbaf92`
- **作者**: yanann_chen
- **日期**: 2019-09-09 10:27:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 68. A00-20190729001 調整條件順序 避免多人每人都要處理 已有一人處理完導致 關卡設定不能取回卻可以取回重辦
- **Commit ID**: `8f071c25bbee2e27f4634270b7cbf886d26aeff3`
- **作者**: walter_wu
- **日期**: 2019-09-05 18:50:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 69. C01-20190624003 修正核決層級關卡自動簽核異常
- **Commit ID**: `3e52986a8511baad53bd7bd9f3d9a0fc7a3f3a19`
- **作者**: yanann_chen
- **日期**: 2019-09-05 15:47:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 70. C01-20190902003 修正<56>移動端ESS表單退回重辦時無簽核意見(詳情)
- **Commit ID**: `a828ca68294cda8618cd49fbd7cb6bc910e32a0d`
- **作者**: yamiyeh10
- **日期**: 2019-09-03 10:59:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 71. C01-20190828001 修正移動端派送異常時未丟出例外而顯示派送成功訊息
- **Commit ID**: `4aad6f32ea245b2b07644f8d85e0f4f09d5da597`
- **作者**: yamiyeh10
- **日期**: 2019-08-29 14:37:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 72. C01-20190822002 修正移動端客製開窗的目前頁數未更新目前為第一頁議題
- **Commit ID**: `1e66d1ebdbeeb54b59a5bb70f51b25cb66391afb`
- **作者**: yamiyeh10
- **日期**: 2019-08-23 14:57:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`

### 73. A00-20190409002 修正FormInstance裡的maskFieldValues是空字串導致管理流程報錯
- **Commit ID**: `708933b668195beef9eb1d59f33410bbeec7daad`
- **作者**: yanann_chen
- **日期**: 2019-08-22 14:59:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/absFormInstance.java`

### 74. C01-20190807003 修正移動端儲存表單機制
- **Commit ID**: `35fcac641a10f6c699405360f13f6f8f5d5c9e60`
- **作者**: yamiyeh10
- **日期**: 2019-08-13 18:24:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 75. 修正<56>企業微信開啟特定excel檔案時會有亂碼問題
- **Commit ID**: `e99eb32fe3992773fd2e6bcb0560b110c2e0f09c`
- **作者**: yamiyeh10
- **日期**: 2019-08-13 13:47:40
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 76. C01-20190808002 修正IMG使用iOS手機在中間層查看excel附件會呈現亂碼問題
- **Commit ID**: `1d0ead18af28691d590dbbf28a037c35972668bc`
- **作者**: yamiyeh10
- **日期**: 2019-08-13 11:45:30
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`

### 77. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `d343f26ee9883edb2a19a5ada966060ade1f50d1`
- **作者**: waynechang
- **日期**: 2019-08-08 14:05:16
- **變更檔案數量**: 0

### 78. 修正T100開單時會持續占用Connection的議題
- **Commit ID**: `68cb7ce4a97130422dbf33db0b18c7727f6d6130`
- **作者**: waynechang
- **日期**: 2019-08-08 14:04:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 79. A00-20190403001 HR組織同步增加throws Exception
- **Commit ID**: `b0c4e530e36b57467eea4d03da19fdf3d3382971`
- **作者**: yanann_chen
- **日期**: 2019-08-07 09:39:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 80. C01-20190802004 修正企業微信2.8.10版本使用iOS手機操作待辦與通知畫面時出現異常訊息
- **Commit ID**: `168d13c832c24ed1213b0936e5c7fa8952ea6667`
- **作者**: yamiyeh10
- **日期**: 2019-08-06 11:14:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileTool.js`

### 81. A00-20190709004 調整排程-關卡往下派送的服務(調整為一個小時前的關卡才允許自動往下派送，避免發生多筆同時處理同一個關卡)
- **Commit ID**: `503be3b0b3aec6405999c875dbcf768b6ffb4bb5`
- **作者**: waynechang
- **日期**: 2019-07-11 09:45:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 82. C01-20190523003 修正"流程中若有核決層級關卡，且退回的關卡設有自動簽核，當處理者重複時會觸發退回的關卡執行自動簽核"的問題
- **Commit ID**: `1d12baaaa982d8727c8f06bf41e1f1a77be8f1f3`
- **作者**: yanann_chen
- **日期**: 2019-07-04 17:24:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 83. A00-20190626004 修正查看附件會保留上一次查看的位置問題
- **Commit ID**: `ccc5584b7b4dcd4a83faef76f5f5bdcadf43736d`
- **作者**: yamiyeh10
- **日期**: 2019-07-04 16:54:18
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 84. Q00-20190703003 將文件總管的修改文件屬性的保存年限調整為保存日期。
- **Commit ID**: `8998edcc9a38498cc2bbd0b796bcd36daa69ba5d`
- **作者**: waynechang
- **日期**: 2019-07-03 16:42:21
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/isoModule/struts-manageDocument-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/EditDocument.jsp`

### 85. A00-20190626003 修正IMG結案率分析統計圖因資料量大導致回應逾時問題
- **Commit ID**: `5acbed48706433a55fc8f555fe41ca66b86c29dd`
- **作者**: pinchi_lin
- **日期**: 2019-07-03 15:09:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

### 86. A00-20190621001 修正企業微信開啟附件編碼為簡中時出現亂碼問題
- **Commit ID**: `657a33cdee1a3268dd331e562143d3f51702cce8`
- **作者**: yamiyeh10
- **日期**: 2019-07-01 10:47:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/FileManager.java`

### 87. A00-20190626005 修正檢視PDF檔案時會模糊不清晰問題
- **Commit ID**: `84baa5d2ea8234d5c09be46a264af1f44b09f5af`
- **作者**: yamiyeh10
- **日期**: 2019-07-01 10:37:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/pdfJs/pdf-bpm.js`

### 88. C01-20190618002 調整拿掉再確認是否派送/轉派/終止 因為Chrome新版不支持 而且會有簽核意見開窗填完並確認
- **Commit ID**: `8ba62dd5eea13ba1cb96724af786857a5ff345db`
- **作者**: walter_wu
- **日期**: 2019-06-21 18:45:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 89. A00-20190605002 修正加簽過一次部門主管 先回到表單畫面在點進入加簽第二次時將第一次加簽改為部門
- **Commit ID**: `592c153e8865e297f11cc8f035479f9c0e3f3b11`
- **作者**: walter_wu
- **日期**: 2019-06-20 18:07:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`

### 90. Q00-20190118009 二次修正:移除IMG發起流程清單回傳網址中串的mobile_token參數
- **Commit ID**: `2eb13e1215c4320af98d1a670cf853c3a0968348`
- **作者**: pinchi_lin
- **日期**: 2019-05-24 10:19:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 91. Q00-20190521002 T100環境代號新增兩個區域可以選擇(topstd,toppth)
- **Commit ID**: `315465063475404c63b2bd378ba14ababb5058cb`
- **作者**: walter_wu
- **日期**: 2019-05-23 10:24:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`

### 92. 列印按鈕版面調整
- **Commit ID**: `afa78f4245e2f515a7092a1ed54e7a5cc41392a7`
- **作者**: yanann_chen
- **日期**: 2019-05-21 16:02:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 93. A00-20190411003 第二次補修正
- **Commit ID**: `f1f03acf4bda634efdebbfacfab13f10a9d3b521`
- **作者**: yanann_chen
- **日期**: 2019-05-17 18:21:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 94. A00-20190516004 修正移動端推播功能需啟用系統郵件發送才能接收到推播問題
- **Commit ID**: `3ee6710617125c51e03378828d7cbec08f1fcbdc`
- **作者**: yamiyeh10
- **日期**: 2019-05-17 16:37:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`

### 95. A00-20190411003 補修正:固定隱藏列印按鈕
- **Commit ID**: `a14749805a8d53b6f37363dfb613fc0faf1e9d25`
- **作者**: yanann_chen
- **日期**: 2019-05-17 15:13:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 96. A00-20190516002 調整簽核流設計師的webService服務，選擇port異常
- **Commit ID**: `b942edc0115f7d23a76231ff345cf5299df7b491`
- **作者**: waynechang
- **日期**: 2019-05-17 14:54:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/WSInvocationEditorPanel.java`

### 97. A00-20190411003修正Chrome列印絕對位置表單功能失效問題
- **Commit ID**: `e33d34555fdc48ef13067d2cfea390b7c38f15bf`
- **作者**: yanann_chen
- **日期**: 2019-05-17 10:44:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 98. 修正A00-20190509003在oracle資料庫，資料選取器無法使用問題
- **Commit ID**: `83d7e8fef76ac146487dc1df306a0d3eaa3cf368`
- **作者**: yanann_chen
- **日期**: 2019-05-17 09:35:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/CustomDataChooserDefinition.java`

### 99. C01-20190505001 修正IMG在詳情表單簽核完成後會跳到首頁問題
- **Commit ID**: `908428f686642c839c65cfed53cdca523bf89746`
- **作者**: pinchi_lin
- **日期**: 2019-05-07 16:57:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`

### 100. Q00-20190507001 移除「流程設計師-核決權限-選擇樣板」這個按鈕
- **Commit ID**: `c2b2040f04f0f1fde38ee7586384b20e76efac85`
- **作者**: waynechang
- **日期**: 2019-05-07 14:11:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/decision/DecisionRuleListEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/activity/decision/DecisionRuleListEditorPanel.java`

### 101. Q00-20190506001 移除「流程設計師-流程模型-可重定義屬性-語言、國家」這兩個屬性
- **Commit ID**: `f38d5a2e7888547109a2288df40b842b45572bad`
- **作者**: waynechang
- **日期**: 2019-05-06 15:22:29
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/processpackage/ProcessPackageMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/processpackage/ProcessPackageMCERTableModel.java`

### 102. A00-20190502001 ISO文件作廢單的瀏覽文件資料需要控制文件閱讀權限
- **Commit ID**: `7ee25c5fbf2720f6b25f4db4514bc57377b468d5`
- **作者**: waynechang
- **日期**: 2019-05-03 17:25:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 103. A00-20190401001 修正T100通知信內容，會有部分欄位重複議題
- **Commit ID**: `b641a7be7990f7743e36fe247317efedaae5e674`
- **作者**: waynechang
- **日期**: 2019-05-02 15:49:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 104. Q00-20190426001 哲瑋發現在流程進行期間去修改流程緊急度 資料庫連線未關閉
- **Commit ID**: `63ed70d6c37ad65f7e1ad4f8b4308bcdaf4de10c`
- **作者**: walter_wu
- **日期**: 2019-04-26 16:43:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

