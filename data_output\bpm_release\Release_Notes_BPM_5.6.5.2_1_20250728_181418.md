# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.5.2_1
- **舊版本**: 5.6.5.1
- **生成時間**: 2025-07-28 18:14:18
- **新增 Commit 數量**: 329

## 變更摘要

### joseph (27 commits)

- **2018-01-17 14:30:19**: 修正: 有設定下一關為關系人人主管,在解析人員時有誤
  - 變更檔案: 1 個
- **2018-01-17 10:36:21**: 修正 : gridRowClick 範例的內容
  - 變更檔案: 1 個
- **2018-01-15 18:06:40**: A00-20171003001 2次修正 : 當凍結時,原先已經有排序的欄位 ,也要更新凍結欄位
  - 變更檔案: 1 個
- **2018-01-15 18:04:02**: 移除Log
  - 變更檔案: 1 個
- **2018-01-15 14:48:24**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2018-01-15 14:47:59**: 修正 : 有設定流程部門,在解析下一關時，派送的關卡人員有誤
  - 變更檔案: 1 個
- **2018-01-15 13:54:35**: 2次修正:從左邊Menu的取回工作重辦，進行取回重辦會報錯的問題
  - 變更檔案: 1 個
- **2018-01-15 13:51:06**: 調整Ajax DatabaseAccessor 範例內容
  - 變更檔案: 1 個
- **2018-01-12 16:05:11**: 修正:從左邊Menu的取回工作重辦，進行取回重辦會報錯的問題
  - 變更檔案: 1 個
- **2018-01-11 18:23:02**: 修正ESS取ManagerURLPatten問題
  - 變更檔案: 1 個
- **2018-01-11 10:36:53**: Q00-*********** 修正 :掛雙表單的流程 ,A表單掛附件 , B表單沒有掛附件 , 但發起時用 B表單發起 會將附件刪除
  - 變更檔案: 1 個
- **2018-01-08 18:10:51**: 移除log
  - 變更檔案: 1 個
- **2018-01-08 18:07:47**: S00-20170930002 新增：簽核流程設計師流程關係部門的設定
  - 變更檔案: 9 個
- **2018-01-08 17:58:51**: A00-20180102001 修正 :可以取回重辦狀態為暫停的Invoke關卡
  - 變更檔案: 2 個
- **2018-01-04 15:49:37**: S00-20170628001 新增 :ajax查詢更新接口且優化查詢方式
  - 變更檔案: 2 個
- **2018-01-02 17:40:07**: A00-20171003001 修正:簽核流程設計師設定欄位中，凍結欄位後不會排序的問題
  - 變更檔案: 1 個
- **2017-12-27 09:57:59**: A00-20170919001 修正 :組織設計師列印組織圖時,應載入全部人員
  - 變更檔案: 3 個
- **2017-12-08 18:31:02**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-08 18:30:18**: C01-20171115001_2 2次修正:於流程進行中將附件刪除後，待辦清單附件圖示仍然存在
  - 變更檔案: 1 個
- **2017-12-07 17:47:00**: C01-20171115001 修正:於流程進行中將附件刪除後，待辦清單附件圖示仍然存在
  - 變更檔案: 1 個
- **2017-11-27 11:54:00**: Q00-20171127001 修改:連接ESS管理作業URL的Patten 改為設定檔設定
  - 變更檔案: 3 個
- **2017-11-14 18:16:49**: C01-20170831002 修改 簽核完一個待辦後，跳下一筆不簽核返回待辦清單,再次點選待辦會無法進入
  - 變更檔案: 1 個
- **2017-11-03 11:34:29**: A00-20171102001 修正 : ISO文件一覽表，文件類別展開畫面沒有scrollbar
  - 變更檔案: 1 個
- **2017-10-27 16:27:58**: Q00-*********** 修正 :關注事件模組開窗問題
  - 變更檔案: 2 個
- **2017-10-27 16:14:46**: A00-20170830001 修正 :ISO無法開啟參考文件
  - 變更檔案: 1 個
- **2017-10-27 10:28:32**: A00-20170926001 修正 :產品客製開窗查詢功能及查詢樣板 ,輸入簡體文字無法查詢正確資料
  - 變更檔案: 1 個
- **2017-10-25 12:07:42**: 2次修正 關鍵事件開窗點選資料無法帶入欄位
  - 變更檔案: 1 個

### 治傑 (21 commits)

- **2018-01-16 19:07:00**: 修正鼎捷移動直連表單異常
  - 變更檔案: 6 個
- **2018-01-12 17:34:20**: 修正鼎捷平台二期發起流程異常
  - 變更檔案: 1 個
- **2018-01-12 15:19:37**: 修正鼎捷平台二期發起流程異常
  - 變更檔案: 1 個
- **2018-01-11 20:04:02**: 調整行動版表單編輯器可標記附件元件為中間層
  - 變更檔案: 1 個
- **2018-01-11 14:18:21**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2018-01-11 14:16:41**: 修正鼎捷平台無法取得授權
  - 變更檔案: 1 個
- **2018-01-08 09:57:38**: 修正鼎捷待辦列表過濾名稱total_cnt為0的錯誤
  - 變更檔案: 1 個
- **2017-12-07 10:47:32**: 調整中間層取簽核歷程寫法
  - 變更檔案: 3 個
- **2017-12-04 19:07:12**: 新增中間層單身長度與單頭長度判斷有無穩合
  - 變更檔案: 4 個
- **2017-11-30 13:43:36**: 新增設計行動版表單時，預設拖拉前20個元件為中間層
  - 變更檔案: 1 個
- **2017-11-30 13:42:33**: 新增行動版表單無標記中間層時，預設前20個元件為中間層
  - 變更檔案: 3 個
- **2017-11-30 11:50:47**: 修正取簽核歷程時，退回重辦關卡重覆取得錯誤
  - 變更檔案: 1 個
- **2017-11-27 18:06:29**: 修正中間層單身收合、中間層單身可顯示多個
  - 變更檔案: 3 個
- **2017-11-23 14:53:16**: 新增中間單身明細可用字段與中間層單身明細替換成Grid名稱
  - 變更檔案: 3 個
- **2017-11-23 14:50:27**: 修正中間層退回重辦服務錯誤
  - 變更檔案: 2 個
- **2017-11-23 14:47:19**: 修正中間層同意、不同意服務取不到RemoteUser問題
  - 變更檔案: 1 個
- **2017-11-23 14:24:14**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-11-22 18:19:38**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-11-22 18:18:46**: 修正中間層單身
  - 變更檔案: 1 個
- **2017-11-17 14:08:35**: 修正中間層表單單身明細、中間層附件下載連結
  - 變更檔案: 8 個
- **2017-11-15 11:47:12**: 修正中間層簽核歷程、中間層表單單身單頭
  - 變更檔案: 2 個

### MiYu (39 commits)

- **2018-01-16 14:43:30**: 修正微信列表使用的彈出視窗樣式 修正草稿刪除樣式 修正我的最愛多語系
  - 變更檔案: 3 個
- **2018-01-16 11:40:20**: 修正終止流程多語系
  - 變更檔案: 3 個
- **2018-01-16 11:11:27**: 修正多部門iphone手機下拉選項與彈出視窗跑版
  - 變更檔案: 3 個
- **2018-01-15 19:15:12**: 調整多部門顯示問題
  - 變更檔案: 1 個
- **2018-01-15 16:19:32**: 修正微信追蹤、通知畫面遺漏多語系
  - 變更檔案: 4 個
- **2018-01-11 14:59:02**: 修正BPMAPP元件多欄位樣式
  - 變更檔案: 2 個
- **2018-01-02 11:48:32**: 修正BPM文件APP的JS範例增加版號與停用版號 修正BPMAPP多欄位時元件畫面跑版問題
  - 變更檔案: 3 個
- **2017-12-22 17:59:17**: 更新bpm文件增加適用版本
  - 變更檔案: 1 個
- **2017-12-22 11:29:51**: 修正草稿進入畫面的restful無表單資料的問題
  - 變更檔案: 3 個
- **2017-12-21 10:27:07**: 修正發起流程restful在選擇多部門時錯誤問題
  - 變更檔案: 1 個
- **2017-12-20 18:18:24**: 修正通知表單restful簽核歷程沒有顯示問題
  - 變更檔案: 1 個
- **2017-12-20 17:28:39**: 修正待辦表單restful的流程主旨消失問題
  - 變更檔案: 1 個
- **2017-12-18 10:24:36**: 修正表單無法開啟問題
  - 變更檔案: 5 個
- **2017-12-18 09:07:35**: 調整彈出視窗、元件樣式
  - 變更檔案: 8 個
- **2017-12-15 16:37:55**: 新增博眾輕量化通知表單,UI設計
  - 變更檔案: 9 個
- **2017-12-13 19:30:37**: 修正Oracle資料庫在取得聯絡人資訊時大小寫問題
  - 變更檔案: 2 個
- **2017-12-11 11:07:55**: 修正C01-***********衍生問題_附件關閉時找不到formType 修正通知RESTFul讀取後未增加viewTimes的bug
  - 變更檔案: 2 個
- **2017-12-08 18:27:53**: 修正取得表單元件的javabean名稱錯誤
  - 變更檔案: 3 個
- **2017-12-07 14:22:40**: 新增 取得系統中所有的組織資訊 RESTFul服務 修正取processSerialNumber時會是null 修正追蹤取詳細資料時漏寫FormCommentTypeValue資訊
  - 變更檔案: 6 個
- **2017-12-06 14:32:19**: 修正取回重辦restful的OID大小問題
  - 變更檔案: 1 個
- **2017-12-06 14:03:35**: 修正取得流程緊急度javabean的OID大小寫問題
  - 變更檔案: 3 個
- **2017-12-06 12:55:41**: 修正草稿取詳細的javabean少取草稿OID問題
  - 變更檔案: 1 個
- **2017-12-05 15:33:27**: 修正取得組織資料javabean的OID大小寫問題
  - 變更檔案: 3 個
- **2017-12-04 14:35:30**: 調整待辦的javabean參數OID改為oid
  - 變更檔案: 4 個
- **2017-11-30 11:57:59**: 調整草稿列表javabean
  - 變更檔案: 1 個
- **2017-11-29 13:02:44**: 新增編輯我的最愛restful服務
  - 變更檔案: 3 個
- **2017-11-27 13:38:46**: 調整繼續派送RESTFul服務增加多部門功能 調整待辦jsp少冒號問題 調整取得組織人員清單javabean參數名稱
  - 變更檔案: 5 個
- **2017-11-23 11:55:16**: 修正C01-20171121001漏掉'{'符號 調整追蹤詳細資料將systout註解
  - 變更檔案: 2 個
- **2017-11-23 09:09:09**: 新增取回重辦服務
  - 變更檔案: 3 個
- **2017-11-22 17:08:16**: 調整追蹤取回重辦的jsp少了冒號問題 C01-20171121003 修正APP版本在Grid無填寫表單內容時,value會顯示']'問題
  - 變更檔案: 2 個
- **2017-11-20 17:30:36**: 調整取得聯繫人的參數(配合鼎捷移動)
  - 變更檔案: 7 個
- **2017-11-20 11:05:11**: 調整取得待辦流程表單資料RESTFul服務
  - 變更檔案: 3 個
- **2017-11-17 10:56:00**: 調整取得流程分類的RESTFul
  - 變更檔案: 2 個
- **2017-11-15 15:53:23**: 調整撤銷javabean的set、get名稱
  - 變更檔案: 2 個
- **2017-11-13 16:44:56**: 新增取得流程分類的RESTFul服務
  - 變更檔案: 4 個
- **2017-11-10 18:45:28**: 調整追蹤javabean的get跟set名稱
  - 變更檔案: 2 個
- **2017-11-08 19:02:04**: 補上javabean漏寫空的建構式
  - 變更檔案: 2 個
- **2017-11-03 16:28:39**: C01-*********** 修正Android手機新增grid資料被按鈕擋住問題
  - 變更檔案: 3 個
- **2017-11-02 16:13:01**: 調整增加判斷工作通知類別 InputElement純marge
  - 變更檔案: 2 個

### pinchi_lin (52 commits)

- **2018-01-16 14:24:11**: 修正微信發請流程列表編輯我的最愛時無反應問題
  - 變更檔案: 3 個
- **2018-01-16 13:15:33**: 修正鼎捷移動Session already invalidated問題
  - 變更檔案: 3 個
- **2018-01-16 11:05:26**: 修正鼎捷移動發起時有ajax呼叫錯誤問題
  - 變更檔案: 1 個
- **2018-01-16 10:14:46**: 修正鼎捷移動登入操作時會有Session already invalidated問題
  - 變更檔案: 1 個
- **2018-01-15 19:18:24**: 修正多語系錯誤
  - 變更檔案: 3 個
- **2018-01-15 16:36:43**: 修正微信附件上傳後表單值消失問題
  - 變更檔案: 1 個
- **2018-01-15 15:01:26**: 修正多語系錯誤
  - 變更檔案: 2 個
- **2018-01-15 14:29:53**: 修正微信簽核畫面遺漏多語系
  - 變更檔案: 4 個
- **2018-01-12 16:08:21**: 修正鼎捷移動會出現session invaild問題與刪除沒用到的import
  - 變更檔案: 4 個
- **2018-01-12 09:38:49**: 修正打版後測試微信遇到的問題
  - 變更檔案: 3 個
- **2018-01-11 21:08:07**: 修正打版後微信測試到的問題
  - 變更檔案: 3 個
- **2018-01-11 18:15:50**: 修正打版後測試微信的發現的問題
  - 變更檔案: 22 個
- **2018-01-08 19:24:17**: 修正微信被動響應消息改為新UI連結
  - 變更檔案: 4 個
- **2018-01-08 17:49:42**: 修正企業微信新UI表單畫面部分錯誤
  - 變更檔案: 12 個
- **2018-01-08 11:30:14**: 修正微信推播網址連結改連至新UI
  - 變更檔案: 1 個
- **2018-01-08 10:34:00**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 7 個
- **2018-01-08 10:15:18**: 修正微信推播消息網址
  - 變更檔案: 1 個
- **2018-01-03 18:38:11**: A00-20171227001 修正微信推播消息內容不完整問題
  - 變更檔案: 1 個
- **2017-12-27 14:36:21**: A00-20171122003 漏簽入一支程式
  - 變更檔案: 1 個
- **2017-12-27 14:12:57**: A00-20171122003 修正微信推送的流程結案連結，會導到未結案的列表中，應導到已結案的列表
  - 變更檔案: 2 個
- **2017-12-21 18:34:46**: A00-20171122002 修正BPMAPP加簽的搜尋人員，將搜尋條件預設人員姓名，搜尋方式預設包含
  - 變更檔案: 3 個
- **2017-12-20 14:51:54**: 修正相對位置表單必填驗證訊息顯示樣式
  - 變更檔案: 1 個
- **2017-12-19 18:55:58**: 修正相對位置表單若checkbox或radio元件設置必填，驗證會失效問題
  - 變更檔案: 1 個
- **2017-12-19 14:31:02**: 修正多語系總表重覆部分
  - 變更檔案: 2 個
- **2017-12-18 17:33:00**: 修正BPMAPP的GRID編輯畫面，下拉元件帶回應為顯示值問題
  - 變更檔案: 1 個
- **2017-12-11 10:02:39**: 修正鼎捷移動統計組件在編輯時會蓋掉原有顏色問題
  - 變更檔案: 1 個
- **2017-12-08 11:09:16**: 修正加簽restFul服務BUG
  - 變更檔案: 1 個
- **2017-12-07 10:27:09**: 新增加簽restful服務
  - 變更檔案: 4 個
- **2017-12-06 14:25:10**: restful服務的關卡定義javaBean新增同意派送是否必填簽核意見開關
  - 變更檔案: 1 個
- **2017-12-06 13:44:55**: 修正BPMAPP終止流程前端錯誤打錯部分
  - 變更檔案: 1 個
- **2017-12-06 11:01:14**: 修正退回重辦restful服務function名稱
  - 變更檔案: 1 個
- **2017-12-06 10:04:33**: BPMAPP增加簽核時若流程有設定必填簽核意見，在派送時會有提示訊息
  - 變更檔案: 5 個
- **2017-12-06 09:48:52**: 修正formBuilder錯誤
  - 變更檔案: 2 個
- **2017-12-04 16:53:55**: 修正退回重辦javaBean的OID大小寫問題
  - 變更檔案: 3 個
- **2017-12-04 15:31:44**: 修正取退回關卡清單javaBean的OID大小寫問題
  - 變更檔案: 2 個
- **2017-12-01 20:34:50**: 修正鼎捷移動一期列表BUG
  - 變更檔案: 1 個
- **2017-12-01 10:51:30**: 修正查詢待辦工作restFul服務bug
  - 變更檔案: 2 個
- **2017-11-30 19:08:57**: 修正鼎捷移動一期列表流程狀態功能
  - 變更檔案: 1 個
- **2017-11-29 17:18:27**: 修正行動版相對位置textbox若設定資料型態為浮點數且有設定小數點後幾位會無法編輯問題
  - 變更檔案: 1 個
- **2017-11-28 15:26:29**: 修正鼎捷移動平台多語系導致無法簽核問題
  - 變更檔案: 1 個
- **2017-11-28 11:30:18**: 查詢關卡步驟restful服務新增是否必填簽核意見
  - 變更檔案: 1 個
- **2017-11-23 16:37:45**: 新增取退回重辦關卡RESTful服務
  - 變更檔案: 1 個
- **2017-11-21 19:48:32**: 修正BPMAPP取通知資料總數量不對問題
  - 變更檔案: 1 個
- **2017-11-20 12:02:19**: 修正BPMAPP終止流程前端錯誤
  - 變更檔案: 2 個
- **2017-11-16 20:30:46**: 修正ESS流程在BPMAPP中無法終止問題
  - 變更檔案: 4 個
- **2017-11-14 09:21:30**: 調整微信被動響應RESTful服務
  - 變更檔案: 7 個
- **2017-11-07 18:46:17**: Q00-20171107001 修正TT或T100流程在第一關收不到微信推播問題
  - 變更檔案: 7 個
- **2017-11-01 19:24:13**: C01-*********** 修正IOS上微信消息內容被切斷 導致內容異常問題
  - 變更檔案: 1 個
- **2017-10-31 17:48:54**: A00-20171031001 修正ESS流程在通知關卡無法打開行動版相對位置表單問題
  - 變更檔案: 2 個
- **2017-10-26 17:26:13**: 調整簽錯部分
  - 變更檔案: 1 個
- **2017-10-26 17:23:44**: 調整鼎捷移動圖表邏輯
  - 變更檔案: 6 個
- **2017-10-25 14:52:59**: C01-20171020001 修正ESS流程取語系時會有NullPointerException問題
  - 變更檔案: 1 個

### 顏伸儒 (2 commits)

- **2018-01-16 11:07:51**: [S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱2018/01/16
  - 變更檔案: 2 個
- **2018-01-11 18:53:26**: [S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱2018/01/11
  - 變更檔案: 2 個

### 施廷緯 (4 commits)

- **2018-01-16 10:58:02**: C01-20180108002 在ISO文件一覽表的申請狀態，新增全部狀態的選項
  - 變更檔案: 1 個
- **2018-01-16 10:50:09**: A00-20180104002 在ISO文件一覽表中新增失效日期的欄位。
  - 變更檔案: 1 個
- **2018-01-11 18:07:31**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2018-01-11 18:01:47**: S00-20171218001修改多語系及調整administrator在ISO文件總管閱讀文件的權限問題。
  - 變更檔案: 4 個

### ChinRong (33 commits)

- **2018-01-16 10:19:25**: 調整入口平台整合設定
  - 變更檔案: 4 個
- **2018-01-15 20:25:34**: 修正鼎捷移動行事曆提醒議題
  - 變更檔案: 2 個
- **2018-01-12 20:38:22**: 修正鼎捷移動表單多語系異常
  - 變更檔案: 2 個
- **2018-01-11 17:13:07**: 行動簽核加簽的活動關卡名稱帶入預設值
  - 變更檔案: 2 個
- **2018-01-11 14:24:02**: 更換鼎捷移動直連表單網址
  - 變更檔案: 3 個
- **2018-01-11 11:31:05**: 修正相對位置表單設計器問題
  - 變更檔案: 1 個
- **2018-01-08 15:00:45**: 新增鼎捷移動直連表單
  - 變更檔案: 10 個
- **2018-01-08 13:51:58**: 新增鼎捷移動直連發起表單，調整待辦表單提醒UI
  - 變更檔案: 5 個
- **2018-01-08 10:55:27**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2018-01-08 10:54:43**: 新增鼎捷移動直連待辦表單
  - 變更檔案: 3 個
- **2018-01-05 16:53:25**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2018-01-05 16:52:48**: 新增鼎捷移動新樣式Action
  - 變更檔案: 2 個
- **2018-01-05 14:40:31**: 二次修正Lib異常，補上漏掉的action
  - 變更檔案: 3 個
- **2018-01-05 14:36:32**: 修正Lib異常
  - 變更檔案: 1 個
- **2018-01-05 13:55:09**: 更正Action名稱
  - 變更檔案: 1 個
- **2018-01-05 13:54:27**: 新增鼎捷移動新樣式表單
  - 變更檔案: 7 個
- **2017-12-26 18:08:55**: [C01-20171222002]修正行動簽核上傳附件後會清空表單的問題
  - 變更檔案: 1 個
- **2017-12-18 10:37:29**: 修正錯誤的部份
  - 變更檔案: 1 個
- **2017-12-18 10:29:00**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-18 10:25:50**: 補上草稿發起表單,待辦直接派送後回列表
  - 變更檔案: 2 個
- **2017-12-15 19:22:18**: 調整行動表單多語系機制，調整路徑
  - 變更檔案: 18 個
- **2017-12-15 16:56:04**: 調整追蹤流程清單，補上跳轉頁面的箭頭
  - 變更檔案: 5 個
- **2017-12-15 15:29:44**: 新增Grid一鍵展開/收合功能
  - 變更檔案: 6 個
- **2017-12-15 12:09:53**: 新增博眾輕量化表單
  - 變更檔案: 30 個
- **2017-12-04 13:38:55**: 調整入口平台整合設定鼎捷移動平台的詞彙
  - 變更檔案: 3 個
- **2017-12-01 15:52:51**: [C01-***********] APP企业微信发单异常，点击发起后，提示发单失败，实际是有发起成功
  - 變更檔案: 2 個
- **2017-11-29 15:27:22**: 修正行動版Grid欄位順序與表單設計器拉的不同的問題
  - 變更檔案: 2 個
- **2017-11-22 08:52:55**: [C01-20171121001] 修正ESS表單有附件時BPM App開啟表單時的異常
  - 變更檔案: 3 個
- **2017-11-03 17:29:14**: 調整取得所有多語系的RESTful服務
  - 變更檔案: 6 個
- **2017-10-27 08:56:08**: 修正鼎捷移動行事曆列表部份功能異常，增加直連表單的log
  - 變更檔案: 2 個
- **2017-10-26 14:51:16**: 修正鼎捷移動追蹤已完成流程取不到流程資料的問題
  - 變更檔案: 1 個
- **2017-10-26 11:49:24**: 新增取得全部多語系的RESTful服務
  - 變更檔案: 3 個
- **2017-10-25 16:04:34**: 修正取得待辦事項表單資料的RESTful異常
  - 變更檔案: 1 個

### 張詠威 (38 commits)

- **2018-01-11 15:38:32**: C01-*********** 修正離職人員維護作業緩慢
  - 變更檔案: 5 個
- **2018-01-05 16:47:22**: S00-20180103001  派送永久代理人時，若發現代理人離職，發信通知系統管理員
  - 變更檔案: 1 個
- **2018-01-05 13:40:53**: A00-20171122001]調整webService的fetchToDoWorkItem的SQL語法
  - 變更檔案: 1 個
- **2018-01-04 14:35:50**: C01-20180103004 修補表單序號重複議題
  - 變更檔案: 1 個
- **2018-01-03 17:35:45**: C01-20171103005 回復之前版本
  - 變更檔案: 1 個
- **2018-01-02 17:46:31**: A00-20171206001 上傳附件限制類型，當上傳失敗後畫面顯示異常
  - 變更檔案: 1 個
- **2017-12-29 11:35:43**: C01-20171103005 取消TT流程回傳簽核結果失敗時，流程仍會結案議題
  - 變更檔案: 1 個
- **2017-12-28 15:51:44**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-28 15:51:22**: C01-*********** 修正SSO登入當user已離職時，不允許登入
  - 變更檔案: 4 個
- **2017-12-27 15:32:05**: 新增 OJB 的LockType
  - 變更檔案: 1 個
- **2017-12-27 15:30:40**: 調整文管ISOManager判斷方法
  - 變更檔案: 1 個
- **2017-12-27 15:16:04**: C01-20171214001 代理人取回重辦後，關卡解析處理者異常
  - 變更檔案: 1 個
- **2017-12-12 16:16:42**: C01-20171122007 退回重辦出現error，原因為多次點擊(調整退回重瓣按鈕顯示狀態)
  - 變更檔案: 1 個
- **2017-12-11 18:49:28**: C01-20171205004-修正mcloud下載附件需顯示原始檔名
  - 變更檔案: 2 個
- **2017-12-11 18:47:14**: [A00-***********]搖旗吶喊小助手新增userId+LDAP密碼登入
  - 變更檔案: 4 個
- **2017-12-07 16:20:48**: C01-20171205001 workflow結案未清除cache導致發單異常
  - 變更檔案: 1 個
- **2017-12-06 16:49:38**: A00-20171206002 建立index
  - 變更檔案: 2 個
- **2017-12-06 16:15:32**: //20171129 waynechang C01-20171123003 調整 將 DEFAULT_VALUE 調整為空白
  - 變更檔案: 1 個
- **2017-12-06 16:11:33**: C01-20171006002 修正workflow更新表單時，欄位會變黑
  - 變更檔案: 1 個
- **2017-12-05 16:10:33**: S00-20171205001 - ESS流程發起後就結案
  - 變更檔案: 3 個
- **2017-12-01 11:26:39**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-01 11:26:22**: C00-20170927002 修正組織同步更新人員cache失敗時導致組織同步rollback
  - 變更檔案: 1 個
- **2017-12-01 10:54:58**: C01-20170612002 修正追蹤信使用ldap無法登入
  - 變更檔案: 1 個
- **2017-11-29 16:41:53**: C01-20171123003  取消T100表單欄位預設值
  - 變更檔案: 1 個
- **2017-11-24 16:16:04**: C01-20171122001 核決關卡參考通知關卡時，簡易流程圖顯示關卡異常
  - 變更檔案: 1 個
- **2017-11-23 17:31:53**: Q00-*********** BPM開啟後沒有人登入過時，第一次restful呼叫SQL註冊器無法使用
  - 變更檔案: 1 個
- **2017-11-22 16:27:03**: Q00-20171122003  調整SQL註冊器在回傳資料時(select A,B,C from table)，無法依照ABC傳回
  - 變更檔案: 2 個
- **2017-11-07 15:38:10**: C01-20171103004 修正核決關卡結束後不允許取回重瓣
  - 變更檔案: 1 個
- **2017-11-06 16:39:24**: Q00-20171027002
  - 變更檔案: 2 個
- **2017-11-06 16:28:46**: Q00-20171031001
  - 變更檔案: 1 個
- **2017-11-02 17:54:46**: Q00-20171102001
  - 變更檔案: 1 個
- **2017-11-01 17:12:25**: A00-20171101001
  - 變更檔案: 1 個
- **2017-11-01 16:21:44**: A00-20170807002  修正客製開窗變更比數後資料重複議題
  - 變更檔案: 1 個
- **2017-11-01 16:15:37**: A00-20170726004 加上防呆
  - 變更檔案: 1 個
- **2017-11-01 15:33:31**: A00-20170726004 修正通知樣版 代辦URL連結
  - 變更檔案: 1 個
- **2017-10-31 16:55:21**: A00-20170822001 產品開窗使用SQL註冊器，到流程草稿會呈現空白
  - 變更檔案: 1 個
- **2017-10-30 15:00:12**: Q00-20171030001 補上RTX程式註解
  - 變更檔案: 1 個
- **2017-10-30 14:33:45**: C01-20171018001 流程模型更新版本後，使用重發新流程畫面會一直轉 調整寫法
  - 變更檔案: 1 個

### Gaspard (1 commits)

- **2018-01-11 14:12:12**: 將手持裝置使用的資料選取器改用openwin方式操作，不使用iframe方式嵌入
  - 變更檔案: 2 個

### lorenchang (2 commits)

- **2018-01-09 08:47:38**: 調整NaNaWebLog部份INFO項目為WARN
  - 變更檔案: 1 個
- **2018-01-09 08:44:28**: Hibernate SQL Log預設更改為false
  - 變更檔案: 1 個

### shenLu (16 commits)

- **2018-01-08 19:15:44**: [S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱
  - 變更檔案: 2 個
- **2018-01-03 16:49:22**: [A00-20171114001]修改Grid使用時，不執行moveCursorToEnd動作
  - 變更檔案: 2 個
- **2018-01-02 15:26:53**: 修改日期取月份的值。
  - 變更檔案: 1 個
- **2017-12-28 17:40:18**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-28 17:39:11**: [A00-20170918001]修改Object轉為String的型態。
  - 變更檔案: 1 個
- **2017-12-27 16:27:26**: [A00-***********]修改表單的開窗元件「Dialog Input Multi」
- **2017-12-27 16:27:26**: [A00-***********]修改表單的開窗元件「Dialog Input Multi」
  - 變更檔案: 1 個
- **2017-12-22 19:36:20**: [A00-20171026002]總筆數取不到時,從session另外取待辦清單出來
  - 變更檔案: 1 個
- **2017-12-14 18:26:22**: [A00-20171026001] 修改ISO文件查詢,簡易查詢where的SQL指令
  - 變更檔案: 1 個
- **2017-12-07 10:34:09**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-07 10:31:44**: [C01-20171120001]修改上傳ISO表單資料用RequireNew獨立出來，切割跟文件佈署主機的交易
  - 變更檔案: 3 個
- **2017-11-22 10:57:18**: 刪除log
  - 變更檔案: 1 個
- **2017-11-21 17:52:42**: 流程設計師指定時限無法正常儲存值
  - 變更檔案: 1 個
- **2017-11-21 16:53:03**: [S00-20170928002]老版本流程设计师通知和审核都是同一个控件（客户需求就是有部分通知关卡不需要邮件提醒）。
  - 變更檔案: 2 個
- **2017-11-21 16:49:20**: 流程設計師指定時限無法正常儲存值
  - 變更檔案: 1 個
- **2017-11-21 16:37:37**: [A00-20171003002]舊版設計師有流程多次逾時選項，但於簽核流程設計師不見了
  - 變更檔案: 16 個

### jd (56 commits)

- **2018-01-08 14:44:34**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2018-01-08 14:42:57**: 調整RESTful API說明文字
  - 變更檔案: 4 個
- **2018-01-05 17:46:28**: 新增鼎捷移動直連表單新畫面
  - 變更檔案: 7 個
- **2018-01-05 17:08:23**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2018-01-05 17:04:54**: 新增鼎捷移動直連表單新樣式
  - 變更檔案: 3 個
- **2018-01-05 14:08:12**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2018-01-05 13:44:58**: 新增BPM二期服務API接口 新增表單附件操作API接口
  - 變更檔案: 4 個
- **2018-01-05 11:29:40**: 修正Manager大量使用Web Session問題
  - 變更檔案: 17 個
- **2018-01-02 14:15:02**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2018-01-02 14:13:15**: 企業微信簽核效能改進
  - 變更檔案: 434 個
- **2017-12-29 18:55:53**: 調整RESTful說明文件功能
  - 變更檔案: 6 個
- **2017-12-29 18:53:49**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-12-29 14:23:33**: 新增RESTful文件說明套件
  - 變更檔案: 32 個
- **2017-12-26 14:56:15**: 當BPM App尚未註冊時,則鼎捷移動不能正確調用BPM App的相關服務
  - 變更檔案: 5 個
- **2017-12-19 11:14:31**: 新增企業微信效能改進計畫調整
  - 變更檔案: 162 個
- **2017-12-18 10:01:22**: 新增工作首頁畫面
  - 變更檔案: 2 個
- **2017-12-18 09:48:37**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 2 個
- **2017-12-18 09:15:55**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 8 個
- **2017-12-15 19:34:25**: 修正微信认证问题
  - 變更檔案: 22 個
- **2017-12-15 14:47:22**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 3 個
- **2017-12-15 14:40:36**: 新增工作首頁 調整推播機制
  - 變更檔案: 9 個
- **2017-12-14 19:07:37**: 新增輕量化微信簽核、通知、聯絡人、追蹤頁面 調整微信認證機制
  - 變更檔案: 238 個
- **2017-12-14 18:51:46**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-11 16:38:38**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-07 14:22:10**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-07 14:16:37**: 新增微信認證架構
  - 變更檔案: 1 個
- **2017-12-06 18:33:02**: 修正入口整合平台,單一JSP過大會出錯誤問題
  - 變更檔案: 13 個
- **2017-12-06 18:31:28**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-06 15:41:15**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-06 15:28:08**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 2 個
- **2017-12-06 10:59:43**: 修正表单中间层多选元件问题
  - 變更檔案: 2 個
- **2017-12-01 15:36:57**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-01 15:35:49**: 修正表單中間層無法顯示多選元件內容問題
  - 變更檔案: 4 個
- **2017-12-01 11:20:52**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-12-01 11:20:07**: 調整多選元件中間層功能
  - 變更檔案: 2 個
- **2017-12-01 11:11:16**: 修正微信登入問題 修正二期列表問題 修正發起流程二期功能
  - 變更檔案: 9 個
- **2017-11-27 13:39:24**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-11-27 13:37:57**: 新增發起流程篩選功能
  - 變更檔案: 6 個
- **2017-11-27 10:28:39**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 3 個
- **2017-11-27 10:16:26**: 新增鼎捷移動二期發起流程服務
  - 變更檔案: 10 個
- **2017-11-17 15:06:29**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-11-17 15:01:30**: 修正錯誤
  - 變更檔案: 1 個
- **2017-11-17 14:56:17**: 修正ESS表單沒有簽核歷程問題
  - 變更檔案: 4 個
- **2017-11-16 17:35:04**: 修正ESS表單沒有簽核歷程問題
  - 變更檔案: 2 個
- **2017-11-16 10:51:37**: 修正簽核多語系 退簽關卡多語系 撤銷流程功能
  - 變更檔案: 2 個
- **2017-11-16 10:28:56**: 調整簽核和退簽關卡的RESTful URI
  - 變更檔案: 1 個
- **2017-11-15 14:28:57**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2017-11-15 13:46:57**: 修正鼎捷移動token過期無法刷新問題
  - 變更檔案: 3 個
- **2017-11-14 16:52:17**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 2 個
- **2017-11-14 16:47:53**: 新增退回重瓣選擇關卡服務 新增鼎捷移動簽核、終止、退回重瓣服務
  - 變更檔案: 8 個
- **2017-11-14 11:03:01**: 新增表單中間層可用字段
  - 變更檔案: 7 個
- **2017-11-10 14:37:50**: 新增同意派送架構 新增不同意派送架構 新增退簽派送架構
  - 變更檔案: 10 個
- **2017-11-09 18:17:03**: 新增表單中間層單身顯示架構 新增表單中間層附件架構 調整WorkInfo
  - 變更檔案: 7 個
- **2017-10-31 11:49:22**: 修正鼎捷移動表單中間層多語系無法顯示問題 修正鼎捷移動直連表單多語系無法顯示問題 修正列表過濾功能,流程重要性多語系無法顯示問題
  - 變更檔案: 3 個
- **2017-10-30 17:51:14**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 2 個
- **2017-10-30 17:44:53**: 修正鼎捷移動二期表單語系顯示問題
  - 變更檔案: 4 個

### Derrick (2 commits)

- **2018-01-08 11:56:36**: S00-20171218001修改ISO調閱功能，根據權限屬性來決定登入者有無權限看到檔案資料
  - 變更檔案: 1 個
- **2018-01-08 11:49:18**: S00-20171218001修改ISO調閱功能，根據權限屬性來決定登入者有無權限看到檔案資料
  - 變更檔案: 2 個

### Andrew (10 commits)

- **2017-12-29 17:10:34**: A00-20171114002 修正時間元件開啟後離按鈕元件太遠問題
  - 變更檔案: 1 個
- **2017-12-28 10:30:29**: Q00-*********** 修改系統多語系未開放問題
  - 變更檔案: 3 個
- **2017-12-21 15:51:29**: A00-20171206003 修改匯入ESS表單後WEB表單會出現行動版頁簽的問題
  - 變更檔案: 1 個
- **2017-12-18 11:59:18**: A00-20171124001 修改使用者個人資訊的基本資料，選項「簽核完畢後執行」沒有多語系功能
  - 變更檔案: 3 個
- **2017-12-13 17:11:27**: A00-20171129001 修改使用者基本資料的編輯按鈕無法正常使用，及Oracle員工工號問題
  - 變更檔案: 2 個
- **2017-12-07 10:41:49**: A00-20170808002 修改Oracle角色及員工工號不會顯示問題
  - 變更檔案: 1 個
- **2017-11-22 17:40:36**: [A00-20170717003] 修改點選部門出現系統錯誤
  - 變更檔案: 1 個
- **2017-11-22 17:21:35**: [A00-***********] 修正同部門人員可重複新增問題
  - 變更檔案: 11 個
- **2017-11-22 16:30:23**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-11-22 16:24:00**: [A00-20170717003] 修改點選部門出現系統錯誤
  - 變更檔案: 1 個

### 喬倫 (25 commits)

- **2017-12-21 16:21:17**: 修正 鼎捷移動平台部屬工具頁面配置
  - 變更檔案: 8 個
- **2017-12-08 10:06:38**: 修正  依指定序號取得待辦 : 移除測試功能   新增多語系總表
  - 變更檔案: 2 個
- **2017-12-07 14:28:55**: 修正 待辦流程依指定取得 測試用的序號  中間層取得附件 主機URL取得方式
  - 變更檔案: 3 個
- **2017-12-07 08:57:53**: 修正 統計元件佈署工具配置欄位及CSS檔
  - 變更檔案: 1 個
- **2017-12-07 08:54:45**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-06 19:10:27**: 修改 鼎捷平台佈署工具JSP檔配置
  - 變更檔案: 14 個
- **2017-12-06 13:48:26**: 新增 鼎捷移動平台佈署工具的多語系
  - 變更檔案: 1 個
- **2017-12-06 13:46:42**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-06 13:45:25**: 新增 鼎捷移動平台佈署工具頁面的多語系
  - 變更檔案: 1 個
- **2017-12-06 12:45:43**: 新增  鼎捷移動平台佈署工具頁面 : 發起流程、待辦簽核、流程追蹤、工作通知、其他工具
  - 變更檔案: 8 個
- **2017-12-04 14:22:16**: 新增 待辦列表依指定流程、我的最愛、常用流程過濾
  - 變更檔案: 2 個
- **2017-12-04 14:20:19**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-12-04 14:18:21**: 新增 待辦列表依指定流程、我的最愛、常用流程過濾
  - 變更檔案: 2 個
- **2017-11-30 15:54:10**: 新增 多語系:流程狀態 工作來源 通知來源
  - 變更檔案: 1 個
- **2017-11-30 14:56:25**: 新增 待辦列表取得工作來源  追蹤列表取得流程狀態  通知列表取得通知來源
  - 變更檔案: 1 個
- **2017-11-30 14:52:26**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-11-30 14:51:15**: 新增 待辦列表取得工作來源  追蹤列表取得流程狀態  通知列表取得通知來源
  - 變更檔案: 1 個
- **2017-11-22 18:05:21**: 新增 中間層通知列表字段及資料來源 : 通知來源
  - 變更檔案: 2 個
- **2017-11-22 16:12:23**: 新增 中間層追蹤流程列表字段及資料來源 : 流程狀態
  - 變更檔案: 2 個
- **2017-11-22 14:19:39**: 新增 中間層待辦列表字段及資料來源 : 工作來源
  - 變更檔案: 2 個
- **2017-11-21 17:11:12**: 修正 中間層簽核歷程狀態的多語系顯示
  - 變更檔案: 2 個
- **2017-11-21 17:07:34**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2017-11-21 17:05:42**: Revert "修正 中間層簽核歷程狀態的多語系顯示"
  - 變更檔案: 2 個
- **2017-11-21 16:50:16**: 修正 中間層簽核歷程狀態的多語系顯示
  - 變更檔案: 2 個
- **2017-11-14 14:53:23**: 新增利用SQL註冊器取得表格資料、取得中間層簽核意見、中間層表單取得附件以及單身單頭
  - 變更檔案: 23 個

### jerry1218 (1 commits)

- **2017-12-13 10:00:31**: 增加T100整合測試主機連接多語系
  - 變更檔案: 2 個

## 詳細變更記錄

### 1. 修正: 有設定下一關為關系人人主管,在解析人員時有誤
- **Commit ID**: `0605a60853a809597b3bd5b6896a59ca7fc65821`
- **作者**: joseph
- **日期**: 2018-01-17 14:30:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 2. 修正 : gridRowClick 範例的內容
- **Commit ID**: `215e62d32227af8bb811d458ef32c52dbe26ccf0`
- **作者**: joseph
- **日期**: 2018-01-17 10:36:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`

### 3. 修正鼎捷移動直連表單異常
- **Commit ID**: `774615186dd4c7197aa39b14d4758076da3de94c`
- **作者**: 治傑
- **日期**: 2018-01-16 19:07:00
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`

### 4. 修正微信列表使用的彈出視窗樣式 修正草稿刪除樣式 修正我的最愛多語系
- **Commit ID**: `9c57f91abbf7d3fc774bbb74c078a1000f66d725`
- **作者**: MiYu
- **日期**: 2018-01-16 14:43:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 5. 修正微信發請流程列表編輯我的最愛時無反應問題
- **Commit ID**: `6f370f310fd99262b2ae10bf66d0da977e1a3bb9`
- **作者**: pinchi_lin
- **日期**: 2018-01-16 14:24:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js`

### 6. 修正鼎捷移動Session already invalidated問題
- **Commit ID**: `da292bb7d671b30eacd3726f7ec0d99a2f950931`
- **作者**: pinchi_lin
- **日期**: 2018-01-16 13:15:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/AbstractMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/DinWhaleSystemMgr.java`

### 7. 修正終止流程多語系
- **Commit ID**: `d8bfe9b71a01bdf9d4cc2ea78099481a56e955d8`
- **作者**: MiYu
- **日期**: 2018-01-16 11:40:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 8. 修正多部門iphone手機下拉選項與彈出視窗跑版
- **Commit ID**: `b61f997b1f47667aafc3e2dc85344d4c8602909f`
- **作者**: MiYu
- **日期**: 2018-01-16 11:11:27
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 9. [S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱2018/01/16
- **Commit ID**: `7bb5b8e29a6ec33f25349b17c44a88872f6487fd`
- **作者**: 顏伸儒
- **日期**: 2018-01-16 11:07:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`

### 10. 修正鼎捷移動發起時有ajax呼叫錯誤問題
- **Commit ID**: `3da33a32730099efb44bd44d173f2db62aed1b52`
- **作者**: pinchi_lin
- **日期**: 2018-01-16 11:05:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`

### 11. C01-20180108002 在ISO文件一覽表的申請狀態，新增全部狀態的選項
- **Commit ID**: `29d49e1bba9e9c95b1c97c20230f077d5ba391a3`
- **作者**: 施廷緯
- **日期**: 2018-01-16 10:58:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`

### 12. A00-20180104002 在ISO文件一覽表中新增失效日期的欄位。
- **Commit ID**: `dcb1e2fd33dc8c1807ad9f033cd3cdb5bcdff2f2`
- **作者**: 施廷緯
- **日期**: 2018-01-16 10:50:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`

### 13. 調整入口平台整合設定
- **Commit ID**: `b310672f388bd915b2d696bd50cb2bf187befcf3`
- **作者**: ChinRong
- **日期**: 2018-01-16 10:19:25
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 14. 修正鼎捷移動登入操作時會有Session already invalidated問題
- **Commit ID**: `a416d71a21e60a1e93bb263b9f86356591911cf3`
- **作者**: pinchi_lin
- **日期**: 2018-01-16 10:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`

### 15. 修正鼎捷移動行事曆提醒議題
- **Commit ID**: `50389fc3e62141f2cfd2b331fdaaeddbb79bde3d`
- **作者**: ChinRong
- **日期**: 2018-01-15 20:25:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 16. 修正多語系錯誤
- **Commit ID**: `a79802a0dd27a8f3d8d12516b84205826c497436`
- **作者**: pinchi_lin
- **日期**: 2018-01-15 19:18:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 17. 調整多部門顯示問題
- **Commit ID**: `fa55484c92ec7c83ee66d01b871278f5133e1af9`
- **作者**: MiYu
- **日期**: 2018-01-15 19:15:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 18. A00-20171003001 2次修正 : 當凍結時,原先已經有排序的欄位 ,也要更新凍結欄位
- **Commit ID**: `a40a7d9cd37f65ab694ed54a0b4f79330b3eabf0`
- **作者**: joseph
- **日期**: 2018-01-15 18:06:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`

### 19. 移除Log
- **Commit ID**: `029e7fe986e45b1e0632db865c2598e0480971c2`
- **作者**: joseph
- **日期**: 2018-01-15 18:04:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java`

### 20. 修正微信附件上傳後表單值消失問題
- **Commit ID**: `9d8d7a89ab9863c89c8c1b03b61a991d2e3cd0c8`
- **作者**: pinchi_lin
- **日期**: 2018-01-15 16:36:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 21. 修正微信追蹤、通知畫面遺漏多語系
- **Commit ID**: `5e9ef4da72c9767c856d7dc894ed9396eb23db54`
- **作者**: MiYu
- **日期**: 2018-01-15 16:19:32
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`

### 22. 修正多語系錯誤
- **Commit ID**: `5220f5c802b15ec13bcd8ef38d4dea4c1a0e785b`
- **作者**: pinchi_lin
- **日期**: 2018-01-15 15:01:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`

### 23. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `fe3cf1351ba6361068b2fecb21e90cc1dccbc1d9`
- **作者**: joseph
- **日期**: 2018-01-15 14:48:24
- **變更檔案數量**: 0

### 24. 修正 : 有設定流程部門,在解析下一關時，派送的關卡人員有誤
- **Commit ID**: `2153b2eb232291d0d745b0bc7b80b4000732ca74`
- **作者**: joseph
- **日期**: 2018-01-15 14:47:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 25. 修正微信簽核畫面遺漏多語系
- **Commit ID**: `238e34addab68e80eb3a716bd104bcf59b12d318`
- **作者**: pinchi_lin
- **日期**: 2018-01-15 14:29:53
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 26. 2次修正:從左邊Menu的取回工作重辦，進行取回重辦會報錯的問題
- **Commit ID**: `fbf4db922def7c48cc6ba3dfca56b38df867d338`
- **作者**: joseph
- **日期**: 2018-01-15 13:54:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`

### 27. 調整Ajax DatabaseAccessor 範例內容
- **Commit ID**: `52ab9810df37be9a4e81255d7d1eb8daeb277dd0`
- **作者**: joseph
- **日期**: 2018-01-15 13:51:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`

### 28. 修正鼎捷移動表單多語系異常
- **Commit ID**: `d2535afdfb5ffafdc67725879edcb394514d004a`
- **作者**: ChinRong
- **日期**: 2018-01-12 20:38:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js`

### 29. 修正鼎捷平台二期發起流程異常
- **Commit ID**: `9648302ff67553cf047f85ef54282e373c8aaa78`
- **作者**: 治傑
- **日期**: 2018-01-12 17:34:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 30. 修正鼎捷移動會出現session invaild問題與刪除沒用到的import
- **Commit ID**: `5ac462acf75e59319357528279f7c05f4d249fb2`
- **作者**: pinchi_lin
- **日期**: 2018-01-12 16:08:21
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 31. 修正:從左邊Menu的取回工作重辦，進行取回重辦會報錯的問題
- **Commit ID**: `9adcda95fafd19206536b52dccd30ba78d29d65a`
- **作者**: joseph
- **日期**: 2018-01-12 16:05:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`

### 32. 修正鼎捷平台二期發起流程異常
- **Commit ID**: `d7c384445463e4757f9fb60fd8ef057901e957a1`
- **作者**: 治傑
- **日期**: 2018-01-12 15:19:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 33. 修正打版後測試微信遇到的問題
- **Commit ID**: `9a25e9cdaf5f1d5b3bc10ba1b4115ce7e96aa93f`
- **作者**: pinchi_lin
- **日期**: 2018-01-12 09:38:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 34. 修正打版後微信測試到的問題
- **Commit ID**: `10e66ab6717e89653cf328279b317686c0f7d8f5`
- **作者**: pinchi_lin
- **日期**: 2018-01-11 21:08:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js`

### 35. 調整行動版表單編輯器可標記附件元件為中間層
- **Commit ID**: `312cd228b51ab61e03f79a9eb28568e9f26381ef`
- **作者**: 治傑
- **日期**: 2018-01-11 20:04:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 36. [S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱2018/01/11
- **Commit ID**: `02ceacfd69f4de5e3c79b5e70a2ac7a3b0ec66ab`
- **作者**: 顏伸儒
- **日期**: 2018-01-11 18:53:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`

### 37. 修正ESS取ManagerURLPatten問題
- **Commit ID**: `c971a80058a97dc3b5561b5d17398ed45276cb93`
- **作者**: joseph
- **日期**: 2018-01-11 18:23:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`

### 38. 修正打版後測試微信的發現的問題
- **Commit ID**: `2701fe83b148c0e2b90efc154fc0c2f973f045c5`
- **作者**: pinchi_lin
- **日期**: 2018-01-11 18:15:50
- **變更檔案數量**: 22
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileTool.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenu.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenuExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 39. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `753a44f2fe4073ad6c881486e35eeab211e4149f`
- **作者**: 施廷緯
- **日期**: 2018-01-11 18:07:31
- **變更檔案數量**: 0

### 40. S00-20171218001修改多語系及調整administrator在ISO文件總管閱讀文件的權限問題。
- **Commit ID**: `46ed319bb47bc0067884fffd66fd0d71884a996e`
- **作者**: 施廷緯
- **日期**: 2018-01-11 18:01:47
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`

### 41. 行動簽核加簽的活動關卡名稱帶入預設值
- **Commit ID**: `0f9d4f9478267acb79c91a062a84e1746dde4456`
- **作者**: ChinRong
- **日期**: 2018-01-11 17:13:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 42. C01-*********** 修正離職人員維護作業緩慢
- **Commit ID**: `2ed4a3ec768e579ecbf5a1f09f1697781be5d38c`
- **作者**: 張詠威
- **日期**: 2018-01-11 15:38:32
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ResignedEmployeesManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ResignedEmployeesMaintainAction.java`

### 43. 修正BPMAPP元件多欄位樣式
- **Commit ID**: `85f4e6c5803c5a109eed917371fb332d7ec1fa58`
- **作者**: MiYu
- **日期**: 2018-01-11 14:59:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 44. 更換鼎捷移動直連表單網址
- **Commit ID**: `b03dd17e474100e1d63e9496a826e02ef322d435`
- **作者**: ChinRong
- **日期**: 2018-01-11 14:24:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 45. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f32b693365db414c7a2ba0761f16dd36a43cbbf8`
- **作者**: 治傑
- **日期**: 2018-01-11 14:18:21
- **變更檔案數量**: 0

### 46. 修正鼎捷平台無法取得授權
- **Commit ID**: `efe1cc8a34b162be91d85d286a29add3b3d9f0a4`
- **作者**: 治傑
- **日期**: 2018-01-11 14:16:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`

### 47. 將手持裝置使用的資料選取器改用openwin方式操作，不使用iframe方式嵌入
- **Commit ID**: `1fe1a6d61d4a6bc31e66f77858f8c7b42cb00556`
- **作者**: Gaspard
- **日期**: 2018-01-11 14:12:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 48. 修正相對位置表單設計器問題
- **Commit ID**: `47414ed5dd7d60c2c38d92bd0b29238e92394132`
- **作者**: ChinRong
- **日期**: 2018-01-11 11:31:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 49. Q00-*********** 修正 :掛雙表單的流程 ,A表單掛附件 , B表單沒有掛附件 , 但發起時用 B表單發起 會將附件刪除
- **Commit ID**: `c59bfe7d20b62776cfee0c89d8b9530955e70ca9`
- **作者**: joseph
- **日期**: 2018-01-11 10:36:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 50. 調整NaNaWebLog部份INFO項目為WARN
- **Commit ID**: `41584c01e2da4e60bc4f9f9151a866d4fad6199d`
- **作者**: lorenchang
- **日期**: 2018-01-09 08:47:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWebLog.properties`

### 51. Hibernate SQL Log預設更改為false
- **Commit ID**: `f098d5e856d16933a2055a0c6a3e9710959ead56`
- **作者**: lorenchang
- **日期**: 2018-01-09 08:44:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/hibernate/session-factory.xml`

### 52. 修正微信被動響應消息改為新UI連結
- **Commit ID**: `47dded06d9f2a89c143e7a6f3d8ff529c3e63d91`
- **作者**: pinchi_lin
- **日期**: 2018-01-08 19:24:17
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`

### 53. [S00-20170613001]修改簽核流程設計師顯示所有流程分類名稱
- **Commit ID**: `8fec00467f2f56a00bad9170f141233b5d8d5523`
- **作者**: shenLu
- **日期**: 2018-01-08 19:15:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`

### 54. 移除log
- **Commit ID**: `6f5e44b55f968abf11a5d03f355dcad7ee8f4f56`
- **作者**: joseph
- **日期**: 2018-01-08 18:10:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`

### 55. S00-20170930002 新增：簽核流程設計師流程關係部門的設定
- **Commit ID**: `5bb8e0506e1e214dbc261c6cf5eee94bbf26147d`
- **作者**: joseph
- **日期**: 2018-01-08 18:07:47
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/RelationManEditorPanel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 56. A00-20180102001 修正 :可以取回重辦狀態為暫停的Invoke關卡
- **Commit ID**: `0f9a22310b31afb3a7cff537dd2b11747f091967`
- **作者**: joseph
- **日期**: 2018-01-08 17:58:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 57. 修正企業微信新UI表單畫面部分錯誤
- **Commit ID**: `e5c452bbe98d610513aa846b34ad6b0535b01131`
- **作者**: pinchi_lin
- **日期**: 2018-01-08 17:49:42
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenu.css`

### 58. 新增鼎捷移動直連表單
- **Commit ID**: `2fe5ec08eabbaec7857c9978617407ce2c2ee54c`
- **作者**: ChinRong
- **日期**: 2018-01-08 15:00:45
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`

### 59. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `ca8b7f943fd6d7d2cbea86b7001d2c354082b9ce`
- **作者**: jd
- **日期**: 2018-01-08 14:44:34
- **變更檔案數量**: 0

### 60. 調整RESTful API說明文字
- **Commit ID**: `cb1bc95cb6e04f0f3be1f86dc0e7c2e7476ea74d`
- **作者**: jd
- **日期**: 2018-01-08 14:42:57
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Form.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 61. 新增鼎捷移動直連發起表單，調整待辦表單提醒UI
- **Commit ID**: `e27b7312c40530e85f7bdc47a60938e1d6806675`
- **作者**: ChinRong
- **日期**: 2018-01-08 13:51:58
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 62. S00-20171218001修改ISO調閱功能，根據權限屬性來決定登入者有無權限看到檔案資料
- **Commit ID**: `1a44bdb220580061ca4f96fb3a498c0b3140d0df`
- **作者**: Derrick
- **日期**: 2018-01-08 11:56:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 63. S00-20171218001修改ISO調閱功能，根據權限屬性來決定登入者有無權限看到檔案資料
- **Commit ID**: `798b3796564821b7e7e96ae39d6c4469ff0a62da`
- **作者**: Derrick
- **日期**: 2018-01-08 11:49:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 64. 修正微信推播網址連結改連至新UI
- **Commit ID**: `d8bd9fb0becfd2012e9ff219160a0bbdc1c10fa8`
- **作者**: pinchi_lin
- **日期**: 2018-01-08 11:30:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 65. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `fc2590dd8d0f02d744ab58bfac7a4b76649fb50d`
- **作者**: ChinRong
- **日期**: 2018-01-08 10:55:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 66. 新增鼎捷移動直連待辦表單
- **Commit ID**: `26012ea84fa378521c71c8869c65fd445b151d4d`
- **作者**: ChinRong
- **日期**: 2018-01-08 10:54:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 67. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `ac18f7e93127a506b42d94c3a2bfa206b72852b2`
- **作者**: pinchi_lin
- **日期**: 2018-01-08 10:34:00
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 68. 修正微信推播消息網址
- **Commit ID**: `ae414d67269e9422fd7ca4fbde0270a111d2c120`
- **作者**: pinchi_lin
- **日期**: 2018-01-08 10:15:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 69. 修正鼎捷待辦列表過濾名稱total_cnt為0的錯誤
- **Commit ID**: `37a5acb62634aeb0075b939e4d09ac7fa156c0d9`
- **作者**: 治傑
- **日期**: 2018-01-08 09:57:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 70. 新增鼎捷移動直連表單新畫面
- **Commit ID**: `ca9fd6a2e5fda98c91663030833a39712206c20e`
- **作者**: jd
- **日期**: 2018-01-05 17:46:28
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`

### 71. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `1a9e2eedd51f2349aacc0841f99afb59a1e74fa0`
- **作者**: jd
- **日期**: 2018-01-05 17:08:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 72. 新增鼎捷移動直連表單新樣式
- **Commit ID**: `7b3053f7de645dc7423e47606102a94f0a7f1646`
- **作者**: jd
- **日期**: 2018-01-05 17:04:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormNoticeLib.jsp`

### 73. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `74ac60871bec591c6bc1ae7171109c71f601ec07`
- **作者**: ChinRong
- **日期**: 2018-01-05 16:53:25
- **變更檔案數量**: 0

### 74. 新增鼎捷移動新樣式Action
- **Commit ID**: `84d35ba5c8722f497b0543b8c8783080245a957e`
- **作者**: ChinRong
- **日期**: 2018-01-05 16:52:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`

### 75. S00-20180103001  派送永久代理人時，若發現代理人離職，發信通知系統管理員
- **Commit ID**: `7dd078eb709946923bef520954f01c5e610b88bd`
- **作者**: 張詠威
- **日期**: 2018-01-05 16:47:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 76. 二次修正Lib異常，補上漏掉的action
- **Commit ID**: `7b5abd02e849ac4d4e3692cd925fb21835802722`
- **作者**: ChinRong
- **日期**: 2018-01-05 14:40:31
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js`

### 77. 修正Lib異常
- **Commit ID**: `4cc621fe1044085ebadc618d54a6db2d7f588af8`
- **作者**: ChinRong
- **日期**: 2018-01-05 14:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js`

### 78. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a759b6203a922e6cdce9ff727f8c73f05e899e85`
- **作者**: jd
- **日期**: 2018-01-05 14:08:12
- **變更檔案數量**: 0

### 79. 更正Action名稱
- **Commit ID**: `74bf9d553617da97546cd3cd5b3913c6082c5237`
- **作者**: ChinRong
- **日期**: 2018-01-05 13:55:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 80. 新增鼎捷移動新樣式表單
- **Commit ID**: `04b55e244e5ee2b7d09cd6d5fe2386952543d153`
- **作者**: ChinRong
- **日期**: 2018-01-05 13:54:27
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleForm.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/DinWhaleFormTodo.js`

### 81. 新增BPM二期服務API接口 新增表單附件操作API接口
- **Commit ID**: `6ba8c9d1a09e95756fc796aca7d4ecddd4147dd1`
- **作者**: jd
- **日期**: 2018-01-05 13:44:58
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/FormV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 82. A00-20171122001]調整webService的fetchToDoWorkItem的SQL語法
- **Commit ID**: `579fbaa132082628f2617a4d01057c46b52c04da`
- **作者**: 張詠威
- **日期**: 2018-01-05 13:40:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 83. 修正Manager大量使用Web Session問題
- **Commit ID**: `816d6fe0ff24bcc2a7868e2b7fa8bd35f48af35c`
- **作者**: jd
- **日期**: 2018-01-05 11:29:40
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleDataChooseTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java`

### 84. S00-20170628001 新增 :ajax查詢更新接口且優化查詢方式
- **Commit ID**: `c225834de013519e3f3a0f92f1f7118258ebe1ac`
- **作者**: joseph
- **日期**: 2018-01-04 15:49:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`

### 85. C01-20180103004 修補表單序號重複議題
- **Commit ID**: `0f79924767d896cd8f7d7817fffb635d105ba211`
- **作者**: 張詠威
- **日期**: 2018-01-04 14:35:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SNGenerator.java`

### 86. A00-20171227001 修正微信推播消息內容不完整問題
- **Commit ID**: `eb91a9dbb2fe81d5b8d8ef7522e43b1d61195bae`
- **作者**: pinchi_lin
- **日期**: 2018-01-03 18:38:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 87. C01-20171103005 回復之前版本
- **Commit ID**: `2e193fe32cbd5ab2d158fd858f64b47eaccd88cc`
- **作者**: 張詠威
- **日期**: 2018-01-03 17:35:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java`

### 88. [A00-20171114001]修改Grid使用時，不執行moveCursorToEnd動作
- **Commit ID**: `ec42bb222a216300ca1c6bcfda733624a0d8c1be`
- **作者**: shenLu
- **日期**: 2018-01-03 16:49:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 89. A00-20171206001 上傳附件限制類型，當上傳失敗後畫面顯示異常
- **Commit ID**: `57685af28aad522477f04fdf43cde0e21eec4462`
- **作者**: 張詠威
- **日期**: 2018-01-02 17:46:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`

### 90. A00-20171003001 修正:簽核流程設計師設定欄位中，凍結欄位後不會排序的問題
- **Commit ID**: `3c84b8ad83d5116ec0ef8004caae2421815c9c7c`
- **作者**: joseph
- **日期**: 2018-01-02 17:40:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`

### 91. 修改日期取月份的值。
- **Commit ID**: `7a8af01961d4e4020342ddc64736bc8456794062`
- **作者**: shenLu
- **日期**: 2018-01-02 15:26:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/statistics/StatisticianBean.java`

### 92. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `db39dcbed2f405769b2bac829d15f27b2cd46416`
- **作者**: jd
- **日期**: 2018-01-02 14:15:02
- **變更檔案數量**: 0

### 93. 企業微信簽核效能改進
- **Commit ID**: `783f452c7704eeb7677b361db8ee1cf373a6f3b8`
- **作者**: jd
- **日期**: 2018-01-02 14:13:15
- **變更檔案數量**: 434
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BPMProcessTracing.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListContact.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListWorkMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmTaskManage.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkItem.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkItemShell.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmWorkPublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileCustomOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormInvoke.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTool.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/aw.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/cn.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/de.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/es.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/fr.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/it.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/nl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/pt.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lang/ru.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/lib/aw.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/bg1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/bg2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/aqua/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/checkbox1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/checkbox2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/radio1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/radio2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/classic/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/mono/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_aqua-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_vista-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/_xp-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-bg1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-bg2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aqua-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-checkbox1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-checkbox2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-radio1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-radio2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/classic-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-g4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tabs1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tabs2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/vista-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/system/xp-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/g4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tabs1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tabs2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/vista/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ActiveWidgets264/styles/xp/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/AppModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/Dialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/OpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/aw.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ds-grid-aw.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/ds.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/dsMobile.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/form/popup.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/Map.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/MobileAppGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/index.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/ajax-loader.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/action-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/action-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/alert-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/alert-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/arrow-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/audio-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/audio-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/back-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/back-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bars-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bars-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bullets-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/bullets-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/calendar-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/calendar-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/camera-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/camera-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/carat-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/check-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/check-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/clock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/clock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/cloud-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/cloud-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/comment-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/comment-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/delete-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/delete-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/edit-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/edit-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/eye-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/eye-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forbidden-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forbidden-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forward-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/forward-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/gear-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/gear-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/grid-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/grid-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/heart-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/heart-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/home-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/home-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/info-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/info-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/location-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/location-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/lock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/lock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/mail-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/mail-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/minus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/minus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/navigation-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/navigation-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/phone-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/phone-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/plus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/plus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/power-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/power-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/recycle-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/recycle-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/refresh-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/refresh-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/search-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/search-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/shop-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/shop-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/star-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/star-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/tag-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/tag-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/user-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/user-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/video-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-png/video-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/action-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/action-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/alert-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/alert-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/arrow-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/audio-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/audio-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/back-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/back-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bars-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bars-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bullets-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/bullets-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/calendar-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/calendar-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/camera-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/camera-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/carat-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/check-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/check-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/clock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/clock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/cloud-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/cloud-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/comment-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/comment-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/delete-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/delete-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/edit-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/edit-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/eye-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/eye-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forbidden-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forbidden-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forward-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/forward-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/gear-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/gear-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/grid-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/grid-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/heart-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/heart-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/home-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/home-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/info-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/info-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/location-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/location-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/lock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/lock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/mail-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/mail-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/minus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/minus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/navigation-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/navigation-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/phone-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/phone-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/plus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/plus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/power-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/power-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/recycle-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/recycle-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/refresh-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/refresh-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/search-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/search-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/shop-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/shop-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/star-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/star-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/tag-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/tag-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/user-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/user-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/video-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/images/icons-svg/video-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile-1.4.5.min.map`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.structure.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.structure.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.theme.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.custom.theme.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.external-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.external-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.icons-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.icons-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-svg-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.inline-svg-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.structure-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.structure-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.theme-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jQM/jquery.mobile.theme-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jquery-1.8.3.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jweixin-1.0.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/snap.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/BpmMobilePublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileCustomOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileTool.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/knockout-3.2.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/knockout.mapping.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/utab.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/ListToDo.cc`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 94. 修正BPM文件APP的JS範例增加版號與停用版號 修正BPMAPP多欄位時元件畫面跑版問題
- **Commit ID**: `b369a6d897a94408724dea34346771217b312926`
- **作者**: MiYu
- **日期**: 2018-01-02 11:48:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`

### 95. 調整RESTful說明文件功能
- **Commit ID**: `044d5312f1df969ef3c5ccdbb1652a92a28417af`
- **作者**: jd
- **日期**: 2017-12-29 18:55:53
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/Reflection/reflections-0.9.10.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/config/JSONDocInteragtion.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`

### 96. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `57b17c1ee6201fdb41850a9733ab0fa1fb191b05`
- **作者**: jd
- **日期**: 2017-12-29 18:53:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 97. A00-20171114002 修正時間元件開啟後離按鈕元件太遠問題
- **Commit ID**: `e1ec3bf4c8f855b348ad2b2eddffb02206261055`
- **作者**: Andrew
- **日期**: 2017-12-29 17:10:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/popup.js`

### 98. 新增RESTful文件說明套件
- **Commit ID**: `a85d88c912a5694df8908657c9ceec9d5f96827e`
- **作者**: jd
- **日期**: 2017-12-29 14:23:33
- **變更檔案數量**: 32
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/GoogleGuava/guava-20.0.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-core-1.1.16.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-springmvc-1.1.16.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-ui-1.1.16.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JSONDoc/jsondoc-ui-webjar-1.1.16.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/Slf4J/slf4j-api-1.7.25.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-aop-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-aspects-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-beans-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-context-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-context-support-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-core-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-expression-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-instrument-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-instrument-tomcat-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-jdbc-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-jms-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-messaging-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-orm-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-oxm-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-test-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-tx-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-web-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-webmvc-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-webmvc-portlet-4.3.7.RELEASE.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/lib/springframework/spring-websocket-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/config/JSONDocInteragtion.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`

### 99. C01-20171103005 取消TT流程回傳簽核結果失敗時，流程仍會結案議題
- **Commit ID**: `d4ba4c8ed845ef13e88137b9c4a016c852a31d50`
- **作者**: 張詠威
- **日期**: 2017-12-29 11:35:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java`

### 100. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `131e0548f6b6c86fa1ef1fd0939d6c8c050eb27b`
- **作者**: shenLu
- **日期**: 2017-12-28 17:40:18
- **變更檔案數量**: 0

### 101. [A00-20170918001]修改Object轉為String的型態。
- **Commit ID**: `783c1135f6bf53d4b4883e3b8180521a11bb71a9`
- **作者**: shenLu
- **日期**: 2017-12-28 17:39:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/util/DBMessageResources.java`

### 102. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b523bc4fb98271e35616fcbb25c465c640360a23`
- **作者**: 張詠威
- **日期**: 2017-12-28 15:51:44
- **變更檔案數量**: 0

### 103. C01-*********** 修正SSO登入當user已離職時，不允許登入
- **Commit ID**: `8376ec4931cbf936de6997ee25ca042f9eadac77`
- **作者**: 張詠威
- **日期**: 2017-12-28 15:51:22
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 104. Q00-*********** 修改系統多語系未開放問題
- **Commit ID**: `ab2e35b8da20b0dac794caaa797000bd052c206d`
- **作者**: Andrew
- **日期**: 2017-12-28 10:30:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/Next_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/Next_updateSQL_SQLServer.sql`

### 105. [A00-***********]修改表單的開窗元件「Dialog Input Multi」
- **Commit ID**: `9bdb8ed5acb3a08c3753859176542c617bcea273`
- **作者**: shenLu
- **日期**: 2017-12-27 16:27:26
- **變更檔案數量**: 0

### 106. [A00-***********]修改表單的開窗元件「Dialog Input Multi」
- **Commit ID**: `7faafc1d0ff85a36536426c68c9df7befb827819`
- **作者**: shenLu
- **日期**: 2017-12-27 16:27:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 107. 新增 OJB 的LockType
- **Commit ID**: `da93fa2460bf781abb8ff175a445ff659969e85d`
- **作者**: 張詠威
- **日期**: 2017-12-27 15:32:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/persistence/src/com/dsc/nana/persistence/LockType.java`

### 108. 調整文管ISOManager判斷方法
- **Commit ID**: `bb16499c0ee2e0afb219d00f7d807b55a261bcd9`
- **作者**: 張詠威
- **日期**: 2017-12-27 15:30:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 109. C01-20171214001 代理人取回重辦後，關卡解析處理者異常
- **Commit ID**: `4080b71b7c8dd1fe55d11fab74c67dd32683b0f1`
- **作者**: 張詠威
- **日期**: 2017-12-27 15:16:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 110. A00-20171122003 漏簽入一支程式
- **Commit ID**: `d978e463289a277bc4455c638839f74eb94f56fe`
- **作者**: pinchi_lin
- **日期**: 2017-12-27 14:36:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 111. A00-20171122003 修正微信推送的流程結案連結，會導到未結案的列表中，應導到已結案的列表
- **Commit ID**: `4b439571c716b476005d38aad39e0d091d3cf211`
- **作者**: pinchi_lin
- **日期**: 2017-12-27 14:12:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 112. A00-20170919001 修正 :組織設計師列印組織圖時,應載入全部人員
- **Commit ID**: `272598272f28d0c2549b08963030c2c28d920deb`
- **作者**: joseph
- **日期**: 2017-12-27 09:57:59
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgTreeNode.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`

### 113. [C01-20171222002]修正行動簽核上傳附件後會清空表單的問題
- **Commit ID**: `f3ac3deb1f0d68e144c37fbe6da61d2d6f8fd9af`
- **作者**: ChinRong
- **日期**: 2017-12-26 18:08:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 114. 當BPM App尚未註冊時,則鼎捷移動不能正確調用BPM App的相關服務
- **Commit ID**: `a2b453353b112a9cbe12c4bd0e028efc5a4dbd17`
- **作者**: jd
- **日期**: 2017-12-26 14:56:15
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinWhaleServiceAuthenticate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Dinwhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`

### 115. [A00-20171026002]總筆數取不到時,從session另外取待辦清單出來
- **Commit ID**: `8f203679a3d46a8babdec6fdbcbb41fc030469d1`
- **作者**: shenLu
- **日期**: 2017-12-22 19:36:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 116. 更新bpm文件增加適用版本
- **Commit ID**: `1b3b51424ecd19ca1b070353740c2da77667a3d8`
- **作者**: MiYu
- **日期**: 2017-12-22 17:59:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`

### 117. 修正草稿進入畫面的restful無表單資料的問題
- **Commit ID**: `0b9882eabcc1cbcffaad19b76c76149da88c3b27`
- **作者**: MiYu
- **日期**: 2017-12-22 11:29:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/BuildFormBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java`

### 118. A00-20171122002 修正BPMAPP加簽的搜尋人員，將搜尋條件預設人員姓名，搜尋方式預設包含
- **Commit ID**: `7233693ccc0c548421a80d6eea5e8cb701afb38d`
- **作者**: pinchi_lin
- **日期**: 2017-12-21 18:34:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 119. 修正 鼎捷移動平台部屬工具頁面配置
- **Commit ID**: `a9cd79c74af499eba2c340e983bbc71dfa7ebcf1`
- **作者**: 喬倫
- **日期**: 2017-12-21 16:21:17
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 120. A00-20171206003 修改匯入ESS表單後WEB表單會出現行動版頁簽的問題
- **Commit ID**: `cb818ab7c145b4a9456eae83b7852ee5e9d58605`
- **作者**: Andrew
- **日期**: 2017-12-21 15:51:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 121. 修正發起流程restful在選擇多部門時錯誤問題
- **Commit ID**: `ef1ae47a6e558a0b0d8c6a09a66e3b0c9f786a04`
- **作者**: MiYu
- **日期**: 2017-12-21 10:27:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`

### 122. 修正通知表單restful簽核歷程沒有顯示問題
- **Commit ID**: `82d50c088695a028a03599cc2064550d096efe8e`
- **作者**: MiYu
- **日期**: 2017-12-20 18:18:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`

### 123. 修正待辦表單restful的流程主旨消失問題
- **Commit ID**: `755b236e2f3f0422f832d0657a8cec115248a2d5`
- **作者**: MiYu
- **日期**: 2017-12-20 17:28:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 124. 修正相對位置表單必填驗證訊息顯示樣式
- **Commit ID**: `d471c2567a76f486246f330bb1640c9a64cf9bd4`
- **作者**: pinchi_lin
- **日期**: 2017-12-20 14:51:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 125. 修正相對位置表單若checkbox或radio元件設置必填，驗證會失效問題
- **Commit ID**: `7b66d5cc2f5ff48bdb9a9d02b8f23798aa74ba10`
- **作者**: pinchi_lin
- **日期**: 2017-12-19 18:55:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 126. 修正多語系總表重覆部分
- **Commit ID**: `b7c7aa066db563e28e8af731ef768b459d750e27`
- **作者**: pinchi_lin
- **日期**: 2017-12-19 14:31:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`

### 127. 新增企業微信效能改進計畫調整
- **Commit ID**: `699160a843f7159a9676162a724c311e8d603953`
- **作者**: jd
- **日期**: 2017-12-19 11:14:31
- **變更檔案數量**: 162
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/cn.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/de.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/es.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/fr.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/it.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/nl.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/pt.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/ru.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lib/aw.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/bg1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/bg2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/checkbox1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/checkbox2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/radio1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/radio2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-bg1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-bg2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-checkbox1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-checkbox2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-radio1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-radio2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g4.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tabs1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tabs2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g3.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g4.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tabs1.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tabs2.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/aw.css`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/button.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/checkbox.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/combo.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/grid.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/icons.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/radio.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/tabs.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/tree.png`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/AppModalDialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/Dialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ModalDialog.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/OpenWin.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/aw.min.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ds-grid-aw.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ds.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/dsMobile.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/popup.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 128. 修正BPMAPP的GRID編輯畫面，下拉元件帶回應為顯示值問題
- **Commit ID**: `ae0015aa8afe282480af594d61c1cd669908223a`
- **作者**: pinchi_lin
- **日期**: 2017-12-18 17:33:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`

### 129. A00-20171124001 修改使用者個人資訊的基本資料，選項「簽核完畢後執行」沒有多語系功能
- **Commit ID**: `d12ace19e8f2d8eb55b2405c685342473fceaf7c`
- **作者**: Andrew
- **日期**: 2017-12-18 11:59:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 130. 修正錯誤的部份
- **Commit ID**: `d1630510e48e6d3b700bb2226565ab4c1373ce4f`
- **作者**: ChinRong
- **日期**: 2017-12-18 10:37:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js`

### 131. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `71a106750838bdfdfa50b11c538e091b92fdd897`
- **作者**: ChinRong
- **日期**: 2017-12-18 10:29:00
- **變更檔案數量**: 0

### 132. 補上草稿發起表單,待辦直接派送後回列表
- **Commit ID**: `613764bf2716fdfc7cf95376357669ad8dad0e02`
- **作者**: ChinRong
- **日期**: 2017-12-18 10:25:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js`

### 133. 修正表單無法開啟問題
- **Commit ID**: `0bfd9fc94127d5ef9ee3d055aa1435826ab08643`
- **作者**: MiYu
- **日期**: 2017-12-18 10:24:36
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`

### 134. 新增工作首頁畫面
- **Commit ID**: `1648b29404d45aed0e39a0b75ce28f1e1de05a06`
- **作者**: jd
- **日期**: 2017-12-18 10:01:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js`

### 135. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `1ad96d81b2e86e920819e54259be3212e1079bd6`
- **作者**: jd
- **日期**: 2017-12-18 09:48:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`

### 136. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `7bc595638bc7991310a4ab468cabf171383a20b9`
- **作者**: jd
- **日期**: 2017-12-18 09:15:55
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/system/BpmMobileLibrary.js`

### 137. 調整彈出視窗、元件樣式
- **Commit ID**: `8e07077390fa127ba987de0729f5d095170da80f`
- **作者**: MiYu
- **日期**: 2017-12-18 09:07:35
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 138. 修正微信认证问题
- **Commit ID**: `b644a264ff82e8374bc0e1720d5c5dd0fa21bd63`
- **作者**: jd
- **日期**: 2017-12-15 19:34:25
- **變更檔案數量**: 22
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobileLibrary.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobilePublic.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/knockout-3.2.0.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/knockout.mapping.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/utab.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/ListToDo.cc`

### 139. 調整行動表單多語系機制，調整路徑
- **Commit ID**: `d7e09808cc14cb042e7bd15ffd6cb135cf6d7ada`
- **作者**: ChinRong
- **日期**: 2017-12-15 19:22:18
- **變更檔案數量**: 18
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/aw.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobileLibrary.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobilePublic.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileCustomOpenWin.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileGrid.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileLibrary.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileProductOpenWin.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTool.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/knockout-3.2.0.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/knockout.mapping.js`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/Mobile/System/utab.js`

### 140. 調整追蹤流程清單，補上跳轉頁面的箭頭
- **Commit ID**: `3a32300cea73017021b62f695d7d9817b03c3fdc`
- **作者**: ChinRong
- **日期**: 2017-12-15 16:56:04
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTracePerformed.js`

### 141. 新增博眾輕量化通知表單,UI設計
- **Commit ID**: `c3dad5da557bad513cd05f2a5b24cb85129166bd`
- **作者**: MiYu
- **日期**: 2017-12-15 16:37:55
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 142. 新增Grid一鍵展開/收合功能
- **Commit ID**: `792e57c1760cd98a3db1b44c407f3b9b8ffe451c`
- **作者**: ChinRong
- **日期**: 2017-12-15 15:29:44
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js`

### 143. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `dae1fc988279e1cfbafa63d25af59e498a76dde8`
- **作者**: jd
- **日期**: 2017-12-15 14:47:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`

### 144. 新增工作首頁 調整推播機制
- **Commit ID**: `a8ce095a3750d81097dab5e364659e22002faf64`
- **作者**: jd
- **日期**: 2017-12-15 14:40:36
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthClientUserDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListWorkMenu.js`

### 145. 新增博眾輕量化表單
- **Commit ID**: `b58cc3ea50f140d1271d2d0aff66ae4a862928bc`
- **作者**: ChinRong
- **日期**: 2017-12-15 12:09:53
- **變更檔案數量**: 30
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppCustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListTracePerformed.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileCustomOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormInvoke.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTool.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTraceInvoked.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileTracePerform.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/aw.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCss.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 146. 新增輕量化微信簽核、通知、聯絡人、追蹤頁面 調整微信認證機制
- **Commit ID**: `e9846572cc5973ebc783ed7c9a6ec993758d9491`
- **作者**: jd
- **日期**: 2017-12-14 19:07:37
- **變更檔案數量**: 238
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListContact.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/ajax-loader.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/action-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/action-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/alert-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/alert-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/arrow-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/audio-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/audio-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/back-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/back-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bars-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bars-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bullets-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/bullets-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/calendar-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/calendar-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/camera-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/camera-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-d-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-d-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-l-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-l-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-r-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-r-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-u-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/carat-u-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/check-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/check-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/clock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/clock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/cloud-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/cloud-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/comment-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/comment-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/delete-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/delete-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/edit-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/edit-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/eye-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/eye-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forbidden-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forbidden-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forward-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/forward-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/gear-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/gear-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/grid-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/grid-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/heart-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/heart-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/home-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/home-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/info-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/info-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/location-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/location-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/lock-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/lock-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/mail-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/mail-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/minus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/minus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/navigation-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/navigation-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/phone-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/phone-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/plus-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/plus-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/power-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/power-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/recycle-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/recycle-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/refresh-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/refresh-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/search-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/search-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/shop-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/shop-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/star-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/star-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/tag-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/tag-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/user-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/user-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/video-black.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-png/video-white.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/action-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/action-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/alert-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/alert-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/arrow-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/audio-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/audio-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/back-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/back-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bars-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bars-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bullets-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/bullets-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/calendar-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/calendar-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/camera-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/camera-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-d-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-d-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-l-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-l-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-r-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-r-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-u-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/carat-u-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/check-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/check-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/clock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/clock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/cloud-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/cloud-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/comment-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/comment-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/delete-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/delete-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/edit-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/edit-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/eye-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/eye-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forbidden-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forbidden-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forward-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/forward-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/gear-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/gear-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/grid-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/grid-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/heart-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/heart-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/home-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/home-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/info-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/info-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/location-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/location-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/lock-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/lock-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/mail-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/mail-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/minus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/minus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/navigation-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/navigation-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/phone-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/phone-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/plus-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/plus-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/power-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/power-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/recycle-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/recycle-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/refresh-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/refresh-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/search-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/search-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/shop-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/shop-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/star-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/star-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/tag-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/tag-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/user-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/user-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/video-black.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/images/icons-svg/video-white.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile-1.4.5.min.map`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.structure.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.structure.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.theme.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.custom.theme.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.external-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.external-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.icons-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.icons-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-png-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-png-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-svg-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.inline-svg-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.structure-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.structure-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.theme-1.4.5.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jQM/jquery.mobile.theme-1.4.5.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/jquery-1.8.3.min.js`

### 147. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `4236014d2cc3d3fabc0d891aaec032e0891847fb`
- **作者**: jd
- **日期**: 2017-12-14 18:51:46
- **變更檔案數量**: 0

### 148. [A00-20171026001] 修改ISO文件查詢,簡易查詢where的SQL指令
- **Commit ID**: `55fc34f063805f564e67b5d568653e36e77f544c`
- **作者**: shenLu
- **日期**: 2017-12-14 18:26:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java`

### 149. 修正Oracle資料庫在取得聯絡人資訊時大小寫問題
- **Commit ID**: `fadb4b5bbe7005d6910db4ae8dc9b1b4db279e36`
- **作者**: MiYu
- **日期**: 2017-12-13 19:30:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.2_updateSQL_Oracle.sql`

### 150. A00-20171129001 修改使用者基本資料的編輯按鈕無法正常使用，及Oracle員工工號問題
- **Commit ID**: `d2f5f12060d1d6f2ef7286d09b41f1535824e8d4`
- **作者**: Andrew
- **日期**: 2017-12-13 17:11:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`

### 151. 增加T100整合測試主機連接多語系
- **Commit ID**: `ff99d91ea7052cc0942f60d0dc09b1d098d62ddc`
- **作者**: jerry1218
- **日期**: 2017-12-13 10:00:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`

### 152. C01-20171122007 退回重辦出現error，原因為多次點擊(調整退回重瓣按鈕顯示狀態)
- **Commit ID**: `ab85a48d8ba7db8e634558bc8cb5982a0f859915`
- **作者**: 張詠威
- **日期**: 2017-12-12 16:16:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 153. C01-20171205004-修正mcloud下載附件需顯示原始檔名
- **Commit ID**: `d685b14b207d8c6ee569ee8c23df22f30c80a8ae`
- **作者**: 張詠威
- **日期**: 2017-12-11 18:49:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`

### 154. [A00-***********]搖旗吶喊小助手新增userId+LDAP密碼登入
- **Commit ID**: `d64e3ce7f06e89b0f9aae8428e7f38bc9d80e773`
- **作者**: 張詠威
- **日期**: 2017-12-11 18:47:14
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/notifier/NotifierServlet.java`

### 155. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `2c8c166c91aff35a954ec41723c31e729bc16b05`
- **作者**: jd
- **日期**: 2017-12-11 16:38:38
- **變更檔案數量**: 0

### 156. 修正C01-***********衍生問題_附件關閉時找不到formType 修正通知RESTFul讀取後未增加viewTimes的bug
- **Commit ID**: `9c076a42e01d5d942e2dd73634a0035a82083b5c`
- **作者**: MiYu
- **日期**: 2017-12-11 11:07:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 157. 修正鼎捷移動統計組件在編輯時會蓋掉原有顏色問題
- **Commit ID**: `083704fdc7d62c4d93bcbb8924a36967544548e5`
- **作者**: pinchi_lin
- **日期**: 2017-12-11 10:02:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 158. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `1c3c1001b05ad61c7bea6dea4b9845a147eaf518`
- **作者**: joseph
- **日期**: 2017-12-08 18:31:02
- **變更檔案數量**: 0

### 159. C01-20171115001_2 2次修正:於流程進行中將附件刪除後，待辦清單附件圖示仍然存在
- **Commit ID**: `bec5db1f16489147f97652f23cb1964710abb0e7`
- **作者**: joseph
- **日期**: 2017-12-08 18:30:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 160. 修正取得表單元件的javabean名稱錯誤
- **Commit ID**: `9f7558a9dccd879bd9c47fde825428d3ed89a7df`
- **作者**: MiYu
- **日期**: 2017-12-08 18:27:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ElementBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 161. 修正加簽restFul服務BUG
- **Commit ID**: `aa5db5a504b5fbb0cd6f03049e2b92b191f01abd`
- **作者**: pinchi_lin
- **日期**: 2017-12-08 11:09:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 162. 修正  依指定序號取得待辦 : 移除測試功能   新增多語系總表
- **Commit ID**: `0eb4cd92254f982bfb95034c957a1df3634f0bb4`
- **作者**: 喬倫
- **日期**: 2017-12-08 10:06:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 163. C01-20171115001 修正:於流程進行中將附件刪除後，待辦清單附件圖示仍然存在
- **Commit ID**: `1418428685a14daa79c9aea674ccebe6f6cea327`
- **作者**: joseph
- **日期**: 2017-12-07 17:47:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 164. C01-20171205001 workflow結案未清除cache導致發單異常
- **Commit ID**: `1cb2849e58f365ae250d7f9b7f451bf4e3afd614`
- **作者**: 張詠威
- **日期**: 2017-12-07 16:20:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 165. 修正 待辦流程依指定取得 測試用的序號  中間層取得附件 主機URL取得方式
- **Commit ID**: `2c2a35f8fb060917340dfa5ede34efc4d0a7d627`
- **作者**: 喬倫
- **日期**: 2017-12-07 14:28:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 166. 新增 取得系統中所有的組織資訊 RESTFul服務 修正取processSerialNumber時會是null 修正追蹤取詳細資料時漏寫FormCommentTypeValue資訊
- **Commit ID**: `68647a638fcc3f3623fde05b312d3bb431cd54df`
- **作者**: MiYu
- **日期**: 2017-12-07 14:22:40
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`

### 167. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `d1ec8021b9582d393ba8fdd4af181f45710da28a`
- **作者**: jd
- **日期**: 2017-12-07 14:22:10
- **變更檔案數量**: 0

### 168. 新增微信認證架構
- **Commit ID**: `7d88fb5182c80cc0795d2ed8d63c085fb65b1ad3`
- **作者**: jd
- **日期**: 2017-12-07 14:16:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 169. 調整中間層取簽核歷程寫法
- **Commit ID**: `f7483d1ac66264c08e2088b331ff72b3daea93ec`
- **作者**: 治傑
- **日期**: 2017-12-07 10:47:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 170. A00-20170808002 修改Oracle角色及員工工號不會顯示問題
- **Commit ID**: `047e491b20e0f788e815e9a1f5ec59ef08651560`
- **作者**: Andrew
- **日期**: 2017-12-07 10:41:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`

### 171. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `d33e3c7b53c444b04a973fb058f47a9530ed1297`
- **作者**: shenLu
- **日期**: 2017-12-07 10:34:09
- **變更檔案數量**: 0

### 172. [C01-20171120001]修改上傳ISO表單資料用RequireNew獨立出來，切割跟文件佈署主機的交易
- **Commit ID**: `4ad8988b1ef48ef3e20be4672bea99672e7fcbb3`
- **作者**: shenLu
- **日期**: 2017-12-07 10:31:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerLocal.java`

### 173. 新增加簽restful服務
- **Commit ID**: `2d1aead64360848cc28c3f84eea0e8cbc0313262`
- **作者**: pinchi_lin
- **日期**: 2017-12-07 10:27:09
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ActivityDefinitionForClientListBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AddCustomActivityBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 174. 修正 統計元件佈署工具配置欄位及CSS檔
- **Commit ID**: `ffe53406fbb743b223bdd02509350d60c89979eb`
- **作者**: 喬倫
- **日期**: 2017-12-07 08:57:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 175. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `feca9663e915388ae372a6b240b36086df14fc5c`
- **作者**: 喬倫
- **日期**: 2017-12-07 08:54:45
- **變更檔案數量**: 0

### 176. 修改 鼎捷平台佈署工具JSP檔配置
- **Commit ID**: `3d11d702173e818ab7add9670af814db9da9fff1`
- **作者**: 喬倫
- **日期**: 2017-12-06 19:10:27
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeploy.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentMenu.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/clipboard/clipboard.js`

### 177. 修正入口整合平台,單一JSP過大會出錯誤問題
- **Commit ID**: `d350a8aa9be24ea8dce8c57deb3929dfea5c7c69`
- **作者**: jd
- **日期**: 2017-12-06 18:33:02
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeploy.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployInvoke.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployNotice.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTodo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTrace.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUser.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentMenu.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChatDeploy.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentWeChateUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 178. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `21a3e8bd208e71980ac7999439929d13213726a1`
- **作者**: jd
- **日期**: 2017-12-06 18:31:28
- **變更檔案數量**: 0

### 179. A00-20171206002 建立index
- **Commit ID**: `12c07bf8e87919d46aea3d49ab4192491359d808`
- **作者**: 張詠威
- **日期**: 2017-12-06 16:49:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/Next_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/Next_updateSQL_SQLServer.sql`

### 180. //20171129 waynechang C01-20171123003 調整 將 DEFAULT_VALUE 調整為空白
- **Commit ID**: `ca947c0228b3e912e2b3a3b65dcf3fd1ac9b08ff`
- **作者**: 張詠威
- **日期**: 2017-12-06 16:15:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 181. C01-20171006002 修正workflow更新表單時，欄位會變黑
- **Commit ID**: `625ea8b2f4557c9063ae6f1da6b8f3207fdc1314`
- **作者**: 張詠威
- **日期**: 2017-12-06 16:11:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 182. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `4050848849cd76ceb4f27edd0684931fe4f49afe`
- **作者**: jd
- **日期**: 2017-12-06 15:41:15
- **變更檔案數量**: 0

### 183. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `c7706cdd362ed6c84b72ed81d0047dd94ba0976e`
- **作者**: jd
- **日期**: 2017-12-06 15:28:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📄 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`

### 184. 修正取回重辦restful的OID大小問題
- **Commit ID**: `7c6c1c1aada6943a3bbc25028d321a18611cc7f9`
- **作者**: MiYu
- **日期**: 2017-12-06 14:32:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 185. restful服務的關卡定義javaBean新增同意派送是否必填簽核意見開關
- **Commit ID**: `50f49c0cf0123b7357f9e35d6f58c6fdb3e4cbc2`
- **作者**: pinchi_lin
- **日期**: 2017-12-06 14:25:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityDefinitionBeanRes.java`

### 186. 修正取得流程緊急度javabean的OID大小寫問題
- **Commit ID**: `c2651839a5fecad5e8bcad7b6c9031a35dcf8a15`
- **作者**: MiYu
- **日期**: 2017-12-06 14:03:35
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmProcessLevelBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`

### 187. 新增 鼎捷移動平台佈署工具的多語系
- **Commit ID**: `a153267ee9775daf48e6e5165a90a550c6cf270a`
- **作者**: 喬倫
- **日期**: 2017-12-06 13:48:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`

### 188. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `3b2e7c8d7bfc28f9e1151eb6fb3dfac639ebc4f7`
- **作者**: 喬倫
- **日期**: 2017-12-06 13:46:42
- **變更檔案數量**: 0

### 189. 新增 鼎捷移動平台佈署工具頁面的多語系
- **Commit ID**: `cb52e0fb439a83ef6aa643771792429108c03e74`
- **作者**: 喬倫
- **日期**: 2017-12-06 13:45:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`

### 190. 修正BPMAPP終止流程前端錯誤打錯部分
- **Commit ID**: `97890cb0332931d9b9c32654684dbaabfd383478`
- **作者**: pinchi_lin
- **日期**: 2017-12-06 13:44:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 191. 修正草稿取詳細的javabean少取草稿OID問題
- **Commit ID**: `63c678a6b5fba9de3fcbfe2eb6227547c7418d5d`
- **作者**: MiYu
- **日期**: 2017-12-06 12:55:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmWorkItemDataBeanRes.java`

### 192. 新增  鼎捷移動平台佈署工具頁面 : 發起流程、待辦簽核、流程追蹤、工作通知、其他工具
- **Commit ID**: `946d5e347958b3f40ba95e469766a4f64f401cb1`
- **作者**: 喬倫
- **日期**: 2017-12-06 12:45:43
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap.4.0.0.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/clipboard/clipboard.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-3.2.1.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/bootstrap_3_3_7.min.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/bootstrap_4_0_0.min.css`

### 193. 修正退回重辦restful服務function名稱
- **Commit ID**: `68d209312f09814963b3bb1a3f4dd69217d56e5c`
- **作者**: pinchi_lin
- **日期**: 2017-12-06 11:01:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`

### 194. 修正表单中间层多选元件问题
- **Commit ID**: `1641a325aff5220142860a9ff30fbc823f054d28`
- **作者**: jd
- **日期**: 2017-12-06 10:59:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`

### 195. BPMAPP增加簽核時若流程有設定必填簽核意見，在派送時會有提示訊息
- **Commit ID**: `af125353a440b45175bb5fde64040580bf898391`
- **作者**: pinchi_lin
- **日期**: 2017-12-06 10:04:33
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 196. 修正formBuilder錯誤
- **Commit ID**: `1b41958512385a8d3687104543058db5d61b1aac`
- **作者**: pinchi_lin
- **日期**: 2017-12-06 09:48:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`

### 197. S00-20171205001 - ESS流程發起後就結案
- **Commit ID**: `d9ece7618b70cea9815c0ae24f6f3a3323ba2b59`
- **作者**: 張詠威
- **日期**: 2017-12-05 16:10:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/appform/AppFormKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 198. 修正取得組織資料javabean的OID大小寫問題
- **Commit ID**: `cb7bdfc310c4b2e39abe4d03062325a6ba7f298b`
- **作者**: MiYu
- **日期**: 2017-12-05 15:33:27
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/OrgUnitBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 199. 新增中間層單身長度與單頭長度判斷有無穩合
- **Commit ID**: `dfc236ec216769213220fa34e8ef890f34dfaa87`
- **作者**: 治傑
- **日期**: 2017-12-04 19:07:12
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 200. 修正退回重辦javaBean的OID大小寫問題
- **Commit ID**: `cdba097c9c7b9831c4d577c2e80b6a9034727329`
- **作者**: pinchi_lin
- **日期**: 2017-12-04 16:53:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/ReexecuteActivityBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 201. 修正取退回關卡清單javaBean的OID大小寫問題
- **Commit ID**: `106f0266fbfe32559bf175ceda9ffcde3fe2d089`
- **作者**: pinchi_lin
- **日期**: 2017-12-04 15:31:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityInstanceBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 202. 調整待辦的javabean參數OID改為oid
- **Commit ID**: `a0a3bc7a1f167668b323a0fb6c0ab16734053b6d`
- **作者**: MiYu
- **日期**: 2017-12-04 14:35:30
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForPerformBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/BpmWorkItemDataBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`

### 203. 新增 待辦列表依指定流程、我的最愛、常用流程過濾
- **Commit ID**: `0580e44baa99e0210de9d34335497d1ea3ff3420`
- **作者**: 喬倫
- **日期**: 2017-12-04 14:22:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 204. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `044af87fa999789fdc845d429277d8c911db728e`
- **作者**: 喬倫
- **日期**: 2017-12-04 14:20:19
- **變更檔案數量**: 0

### 205. 新增 待辦列表依指定流程、我的最愛、常用流程過濾
- **Commit ID**: `eadd9bbb61fddb76aadfbb30a41abd5c6e5b5abd`
- **作者**: 喬倫
- **日期**: 2017-12-04 14:18:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 206. 調整入口平台整合設定鼎捷移動平台的詞彙
- **Commit ID**: `ceb49d1f5900c074af5c8a8274f78c71511e5f8e`
- **作者**: ChinRong
- **日期**: 2017-12-04 13:38:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5652.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`

### 207. 修正鼎捷移動一期列表BUG
- **Commit ID**: `bb63ca9f1e3da3072d7a336900e2bbedf76552da`
- **作者**: pinchi_lin
- **日期**: 2017-12-01 20:34:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 208. [C01-***********] APP企业微信发单异常，点击发起后，提示发单失败，实际是有发起成功
- **Commit ID**: `50affd3f0d3b987283ad02925a66966d67155258`
- **作者**: ChinRong
- **日期**: 2017-12-01 15:52:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/StatefulProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`

### 209. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `992b65b9c9a11c7d81a9aafefead627d032014b9`
- **作者**: jd
- **日期**: 2017-12-01 15:36:57
- **變更檔案數量**: 0

### 210. 修正表單中間層無法顯示多選元件內容問題
- **Commit ID**: `db3140e8d5d63534ff803db78c4ef1175700fc49`
- **作者**: jd
- **日期**: 2017-12-01 15:35:49
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 211. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `bac46d7ac58d7eb6e3faae7753f66471f809a2a9`
- **作者**: 張詠威
- **日期**: 2017-12-01 11:26:39
- **變更檔案數量**: 0

### 212. C00-20170927002 修正組織同步更新人員cache失敗時導致組織同步rollback
- **Commit ID**: `c6a639cc3f4bd0266a411db1851bbc102a0b72ab`
- **作者**: 張詠威
- **日期**: 2017-12-01 11:26:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 213. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `6e9a557dd01e42d3974b88df3a5a3b0ac038994b`
- **作者**: jd
- **日期**: 2017-12-01 11:20:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`

### 214. 調整多選元件中間層功能
- **Commit ID**: `b73552194c73e1e8dc11976fcacab4bde8424979`
- **作者**: jd
- **日期**: 2017-12-01 11:20:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElementMobile.java`

### 215. 修正微信登入問題 修正二期列表問題 修正發起流程二期功能
- **Commit ID**: `bdbb9563db0966842e44df66f38cf9714ea9b40e`
- **作者**: jd
- **日期**: 2017-12-01 11:11:16
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java`

### 216. C01-20170612002 修正追蹤信使用ldap無法登入
- **Commit ID**: `417eb02db84e3eeb13aa3446f493b9aa1ab25f92`
- **作者**: 張詠威
- **日期**: 2017-12-01 10:54:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 217. 修正查詢待辦工作restFul服務bug
- **Commit ID**: `3d44d5eeae398832f09b131eff602275275484d2`
- **作者**: pinchi_lin
- **日期**: 2017-12-01 10:51:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/PerformTypeBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 218. 修正鼎捷移動一期列表流程狀態功能
- **Commit ID**: `0974ccb2cc7bda5a9a83595c60a8e4177aa87e96`
- **作者**: pinchi_lin
- **日期**: 2017-11-30 19:08:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 219. 新增 多語系:流程狀態 工作來源 通知來源
- **Commit ID**: `0b7665aaf9f306284a3c56b2ac95678cffaeb5a1`
- **作者**: 喬倫
- **日期**: 2017-11-30 15:54:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 220. 新增 待辦列表取得工作來源  追蹤列表取得流程狀態  通知列表取得通知來源
- **Commit ID**: `4ff40002eedca20ed1b9b826550196bb5d138396`
- **作者**: 喬倫
- **日期**: 2017-11-30 14:56:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 221. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `911a5252193db5d68384b3c49a33dc6cd4f10ab2`
- **作者**: 喬倫
- **日期**: 2017-11-30 14:52:26
- **變更檔案數量**: 0

### 222. 新增 待辦列表取得工作來源  追蹤列表取得流程狀態  通知列表取得通知來源
- **Commit ID**: `c95b201129126fc720440c473b55a6c8c4bb0af2`
- **作者**: 喬倫
- **日期**: 2017-11-30 14:51:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 223. 新增設計行動版表單時，預設拖拉前20個元件為中間層
- **Commit ID**: `972f59f867123b1942406a4017a014413c95f306`
- **作者**: 治傑
- **日期**: 2017-11-30 13:43:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`

### 224. 新增行動版表單無標記中間層時，預設前20個元件為中間層
- **Commit ID**: `364b375ea16074966479a93c20a07a58015edf66`
- **作者**: 治傑
- **日期**: 2017-11-30 13:42:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`

### 225. 調整草稿列表javabean
- **Commit ID**: `fe90754a4a8a051561717b47453f230117068a08`
- **作者**: MiYu
- **日期**: 2017-11-30 11:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/DraftHeaderBeanRes.java`

### 226. 修正取簽核歷程時，退回重辦關卡重覆取得錯誤
- **Commit ID**: `0a1fb3743c1c7742ad0c9cd942b0dabc32db3508`
- **作者**: 治傑
- **日期**: 2017-11-30 11:50:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 227. 修正行動版相對位置textbox若設定資料型態為浮點數且有設定小數點後幾位會無法編輯問題
- **Commit ID**: `027288e756fb9610da8e91fcd0ff99049dd51964`
- **作者**: pinchi_lin
- **日期**: 2017-11-29 17:18:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp`

### 228. C01-20171123003  取消T100表單欄位預設值
- **Commit ID**: `39dca528d61714fd73bc0803ebc567b91de14b5b`
- **作者**: 張詠威
- **日期**: 2017-11-29 16:41:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java`

### 229. 修正行動版Grid欄位順序與表單設計器拉的不同的問題
- **Commit ID**: `521958c6a4a50e76c89a70294c9fce55763f2c47`
- **作者**: ChinRong
- **日期**: 2017-11-29 15:27:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`

### 230. 新增編輯我的最愛restful服務
- **Commit ID**: `4a14191caa6664c0fb8a909afd4b6b11cc7e5dc2`
- **作者**: MiYu
- **日期**: 2017-11-29 13:02:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/InvokeFavoriteReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`

### 231. 修正鼎捷移動平台多語系導致無法簽核問題
- **Commit ID**: `d7c333f4669e173b2ababa4f419248f750de1429`
- **作者**: pinchi_lin
- **日期**: 2017-11-28 15:26:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 232. 查詢關卡步驟restful服務新增是否必填簽核意見
- **Commit ID**: `d0c441ed8d152c29a775128896def769a4895cea`
- **作者**: pinchi_lin
- **日期**: 2017-11-28 11:30:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityDefinitionBeanRes.java`

### 233. 修正中間層單身收合、中間層單身可顯示多個
- **Commit ID**: `dc49a2f749466eecaab7fd40ef176b1b3d872b36`
- **作者**: 治傑
- **日期**: 2017-11-27 18:06:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldContent.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 234. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `58a50a5761adc8ae16aa364d7c7496d3029e712e`
- **作者**: jd
- **日期**: 2017-11-27 13:39:24
- **變更檔案數量**: 0

### 235. 調整繼續派送RESTFul服務增加多部門功能 調整待辦jsp少冒號問題 調整取得組織人員清單javabean參數名稱
- **Commit ID**: `9650ac9b3325652a18d0b57e61ccd1f74586dffd`
- **作者**: MiYu
- **日期**: 2017-11-27 13:38:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/CompleteWorkItemForListBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/UserForListBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTodoLib.jsp`

### 236. 新增發起流程篩選功能
- **Commit ID**: `4ec53634c63cc7e6df1aa686e77b7e379e77e05f`
- **作者**: jd
- **日期**: 2017-11-27 13:37:57
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ESSFileManager.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ESSFormHandler.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ESSPerformWorkItem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`

### 237. Q00-20171127001 修改:連接ESS管理作業URL的Patten 改為設定檔設定
- **Commit ID**: `a8e09435174c98d987503b6f55ceb085f2b2b528`
- **作者**: joseph
- **日期**: 2017-11-27 11:54:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`

### 238. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `42581cdc314f729769857da533842bd1df47dae2`
- **作者**: jd
- **日期**: 2017-11-27 10:28:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 239. 新增鼎捷移動二期發起流程服務
- **Commit ID**: `28f80f726dbb57c7253fc25fdcc68ac7bff7289f`
- **作者**: jd
- **日期**: 2017-11-27 10:16:26
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/jdajia.js`

### 240. C01-20171122001 核決關卡參考通知關卡時，簡易流程圖顯示關卡異常
- **Commit ID**: `7ac38181703e8731fa05b81d05abb2665848a428`
- **作者**: 張詠威
- **日期**: 2017-11-24 16:16:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 241. Q00-*********** BPM開啟後沒有人登入過時，第一次restful呼叫SQL註冊器無法使用
- **Commit ID**: `6cba5fde1ac6bb37346b3767e76bea79ff2ec87c`
- **作者**: 張詠威
- **日期**: 2017-11-23 17:31:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`

### 242. 新增取退回重辦關卡RESTful服務
- **Commit ID**: `7c3ce5872b93350dea54bc79b7d7bf01149c8ecc`
- **作者**: pinchi_lin
- **日期**: 2017-11-23 16:37:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`

### 243. 新增中間單身明細可用字段與中間層單身明細替換成Grid名稱
- **Commit ID**: `c097437428d1c663eb0b1c3d5027f981844e1885`
- **作者**: 治傑
- **日期**: 2017-11-23 14:53:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 244. 修正中間層退回重辦服務錯誤
- **Commit ID**: `e0a30db020616843acc3c417dc2c4793bb394eab`
- **作者**: 治傑
- **日期**: 2017-11-23 14:50:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityInstanceBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 245. 修正中間層同意、不同意服務取不到RemoteUser問題
- **Commit ID**: `ffd1d740c92f1e0b88d4d7a8b7a6c0fd0ecac78b`
- **作者**: 治傑
- **日期**: 2017-11-23 14:47:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`

### 246. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `58f428c63990b5f65e10d735428f32aa40ed6092`
- **作者**: 治傑
- **日期**: 2017-11-23 14:24:14
- **變更檔案數量**: 0

### 247. 修正C01-20171121001漏掉'{'符號 調整追蹤詳細資料將systout註解
- **Commit ID**: `4dc4173f5770f8cd687ac29207934d9bd8ac4692`
- **作者**: MiYu
- **日期**: 2017-11-23 11:55:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`

### 248. 新增取回重辦服務
- **Commit ID**: `727c55cd0394cfddbe756b2fe30890b40057fe68`
- **作者**: MiYu
- **日期**: 2017-11-23 09:09:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/RollbackActivityBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 249. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `494ced1b8b5968e7cd84879694eec79e1ad2bf13`
- **作者**: 治傑
- **日期**: 2017-11-22 18:19:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 250. 修正中間層單身
- **Commit ID**: `0da9ee8db0167bddf55e7dd5438f4a72b4b32de4`
- **作者**: 治傑
- **日期**: 2017-11-22 18:18:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 251. 新增 中間層通知列表字段及資料來源 : 通知來源
- **Commit ID**: `4103414307c1babe6a3ee8f6a51cbdd55189d67d`
- **作者**: 喬倫
- **日期**: 2017-11-22 18:05:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 252. [A00-20170717003] 修改點選部門出現系統錯誤
- **Commit ID**: `478f3def9f4709bb979609d95403c00c04c13a9d`
- **作者**: Andrew
- **日期**: 2017-11-22 17:40:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`

### 253. [A00-***********] 修正同部門人員可重複新增問題
- **Commit ID**: `99e2ff28639e1afc62fd97b101891ffb22c85097`
- **作者**: Andrew
- **日期**: 2017-11-22 17:21:35
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/AddUserToUnitDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`

### 254. 調整追蹤取回重辦的jsp少了冒號問題 C01-20171121003 修正APP版本在Grid無填寫表單內容時,value會顯示']'問題
- **Commit ID**: `2eb5f053f40f5bf1dc7dfd756bc958ff4b49cc4e`
- **作者**: MiYu
- **日期**: 2017-11-22 17:08:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppFormTraceLib.jsp`

### 255. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `27b8e1ae075064c771029e0b25e8f05558241515`
- **作者**: Andrew
- **日期**: 2017-11-22 16:30:23
- **變更檔案數量**: 0

### 256. Q00-20171122003  調整SQL註冊器在回傳資料時(select A,B,C from table)，無法依照ABC傳回
- **Commit ID**: `73386f59d84a243979905c578cec6054e7361fd6`
- **作者**: 張詠威
- **日期**: 2017-11-22 16:27:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleDataChooseTool.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/LikedJSONObject.java`

### 257. [A00-20170717003] 修改點選部門出現系統錯誤
- **Commit ID**: `46f7740b2e88c116df2632bc71130e6a38ec76c7`
- **作者**: Andrew
- **日期**: 2017-11-22 16:24:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`

### 258. 新增 中間層追蹤流程列表字段及資料來源 : 流程狀態
- **Commit ID**: `f41773fe6107f40318578cb719fc3be9e03a9770`
- **作者**: 喬倫
- **日期**: 2017-11-22 16:12:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 259. 新增 中間層待辦列表字段及資料來源 : 工作來源
- **Commit ID**: `fd34b9f089652df2d4e866d4d8cba03575c1d8be`
- **作者**: 喬倫
- **日期**: 2017-11-22 14:19:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 260. 刪除log
- **Commit ID**: `849b5357574c031f3d5740a8b5d2c145af2bc73b`
- **作者**: shenLu
- **日期**: 2017-11-22 10:57:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorPanel.java`

### 261. [C01-20171121001] 修正ESS表單有附件時BPM App開啟表單時的異常
- **Commit ID**: `fbc537717ab075c5dd6a7d662fbf5fb8387e2e89`
- **作者**: ChinRong
- **日期**: 2017-11-22 08:52:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFileManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyle.js`

### 262. 修正BPMAPP取通知資料總數量不對問題
- **Commit ID**: `97ce75f8318cfbc2dfd2b86edb813550ade86e69`
- **作者**: pinchi_lin
- **日期**: 2017-11-21 19:48:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`

### 263. 流程設計師指定時限無法正常儲存值
- **Commit ID**: `fd76172d9ef0cbadc28cf201ef160766a5653c6a`
- **作者**: shenLu
- **日期**: 2017-11-21 17:52:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorCER.java`

### 264. 修正 中間層簽核歷程狀態的多語系顯示
- **Commit ID**: `3fdb4062928d61bcb6cb4d1b2c8a8ebce1691b52`
- **作者**: 喬倫
- **日期**: 2017-11-21 17:11:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 265. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `35807bd8d1d48db937f7b8073080a752470acaaf`
- **作者**: 喬倫
- **日期**: 2017-11-21 17:07:34
- **變更檔案數量**: 0

### 266. Revert "修正 中間層簽核歷程狀態的多語系顯示"
- **Commit ID**: `8baa6c4d82a347023425d44cf6156e399fb7393a`
- **作者**: 喬倫
- **日期**: 2017-11-21 17:05:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 267. 修正 中間層簽核歷程狀態的多語系顯示
- **Commit ID**: `c3fb547b1e48d7ad3d115c4c2b695ba1abd4aeac`
- **作者**: 喬倫
- **日期**: 2017-11-21 16:50:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 268. [S00-20170928002]老版本流程设计师通知和审核都是同一个控件（客户需求就是有部分通知关卡不需要邮件提醒）。
- **Commit ID**: `c0858de31a917f7398d4dbeee539f7412078466e`
- **作者**: shenLu
- **日期**: 2017-11-21 16:53:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTableModel.java`

### 269. 流程設計師指定時限無法正常儲存值
- **Commit ID**: `abe9664b6b62e8f0f6ccedaa528e31c84b56e2fb`
- **作者**: shenLu
- **日期**: 2017-11-21 16:49:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorCER.java`

### 270. [A00-20171003002]舊版設計師有流程多次逾時選項，但於簽核流程設計師不見了
- **Commit ID**: `b8a5f09d44be37480b2086662dbaaca3d6fd8e1e`
- **作者**: shenLu
- **日期**: 2017-11-21 16:37:37
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorCER.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateObject.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`

### 271. 調整取得聯繫人的參數(配合鼎捷移動)
- **Commit ID**: `46ad50d94f802350147484d05eacbd8a55b9e0a9`
- **作者**: MiYu
- **日期**: 2017-11-20 17:30:36
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookData.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ContactBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Org.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`

### 272. 修正BPMAPP終止流程前端錯誤
- **Commit ID**: `f178adbd7843e1aa27aae5a29c19bad8873cbd01`
- **作者**: pinchi_lin
- **日期**: 2017-11-20 12:02:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 273. 調整取得待辦流程表單資料RESTFul服務
- **Commit ID**: `13de34ab9dde013a825cf417925ca255697f5386`
- **作者**: MiYu
- **日期**: 2017-11-20 11:05:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemForPerformBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/FormDefinitionBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 274. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `f406dfcab9f6ce445ed9967825f2cc6f51276ef2`
- **作者**: jd
- **日期**: 2017-11-17 15:06:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 275. 修正錯誤
- **Commit ID**: `66bff5cdb606514556a1fdcd0cef3e715ca5ea70`
- **作者**: jd
- **日期**: 2017-11-17 15:01:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 276. 修正ESS表單沒有簽核歷程問題
- **Commit ID**: `90a27688fb919d4bcba48fff1b9a6b55bfc262b3`
- **作者**: jd
- **日期**: 2017-11-17 14:56:17
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 277. 修正中間層表單單身明細、中間層附件下載連結
- **Commit ID**: `188b0399773c46bc8053ae4f02a98db4515f1c03`
- **作者**: 治傑
- **日期**: 2017-11-17 14:08:35
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/RowDataSet.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 278. 調整取得流程分類的RESTFul
- **Commit ID**: `4a0b1d456a468e5accadd4cb4ba1034d7912a4d9`
- **作者**: MiYu
- **日期**: 2017-11-17 10:56:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ListReaderBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ProcessMgr.java`

### 279. 修正ESS流程在BPMAPP中無法終止問題
- **Commit ID**: `c04094f6bf70023108164514909b56b80a25195d`
- **作者**: pinchi_lin
- **日期**: 2017-11-16 20:30:46
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppFormTodo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 280. 修正ESS表單沒有簽核歷程問題
- **Commit ID**: `c6122c4fb56506230e702dd6892e2ec3384a2a47`
- **作者**: jd
- **日期**: 2017-11-16 17:35:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 281. 修正簽核多語系 退簽關卡多語系 撤銷流程功能
- **Commit ID**: `fb6db22a65c42561d3ec3783a2cad909aaa52a36`
- **作者**: jd
- **日期**: 2017-11-16 10:51:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 282. 調整簽核和退簽關卡的RESTful URI
- **Commit ID**: `81dced1a5b43e19f67e4d9825006880228f2b55b`
- **作者**: jd
- **日期**: 2017-11-16 10:28:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`

### 283. 調整撤銷javabean的set、get名稱
- **Commit ID**: `01bd18e3ba7f0939f87460df7a73bfa55c5005d3`
- **作者**: MiYu
- **日期**: 2017-11-15 15:53:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AbortProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 284. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `11010db61ea6e363c8c5a3bb0c3d21b6f692f6a1`
- **作者**: jd
- **日期**: 2017-11-15 14:28:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 285. 修正鼎捷移動token過期無法刷新問題
- **Commit ID**: `855a0c9aeb74dc7552184f08c2deae44f3df21cb`
- **作者**: jd
- **日期**: 2017-11-15 13:46:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 286. 修正中間層簽核歷程、中間層表單單身單頭
- **Commit ID**: `2392da107ffeb48a0e9f54abdb009f7e9328951c`
- **作者**: 治傑
- **日期**: 2017-11-15 11:47:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 287. C01-20170831002 修改 簽核完一個待辦後，跳下一筆不簽核返回待辦清單,再次點選待辦會無法進入
- **Commit ID**: `266a8d7aa0f519251ca5952266c8c3dd41296546`
- **作者**: joseph
- **日期**: 2017-11-14 18:16:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 288. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `ddc4a6a1e596331913c84a4445a2d5b1f094439b`
- **作者**: jd
- **日期**: 2017-11-14 16:52:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`

### 289. 新增退回重瓣選擇關卡服務 新增鼎捷移動簽核、終止、退回重瓣服務
- **Commit ID**: `43ed068790423d24f27aed8219c2cfffc48ef9f8`
- **作者**: jd
- **日期**: 2017-11-14 16:47:53
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchReexecuteActivityBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ActivityInstanceBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/FetchReexecuteActivityBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 290. 新增利用SQL註冊器取得表格資料、取得中間層簽核意見、中間層表單取得附件以及單身單頭
- **Commit ID**: `84eaf358eb15113f49586827d95a116d3fd39905`
- **作者**: 喬倫
- **日期**: 2017-11-14 14:53:23
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ColumnDataSet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentArray.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentColor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentImage.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ContentWidth.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDataset.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldDetail.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/HeaderDataSet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/ListFieldDataset.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/QueryCriteria.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/TableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 291. 新增表單中間層可用字段
- **Commit ID**: `5c703d271a867d4e04b8eeeb71267a6f0b5af34a`
- **作者**: jd
- **日期**: 2017-11-14 11:03:01
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulClientDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/FieldContent.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml`

### 292. 調整微信被動響應RESTful服務
- **Commit ID**: `75c39618fccee3f8594acb4da23f6c603cbdbebf`
- **作者**: pinchi_lin
- **日期**: 2017-11-14 09:21:30
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/WeChatKeyBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/OrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java`

### 293. 新增取得流程分類的RESTFul服務
- **Commit ID**: `02c955f3ec0f7662ec3c444d172b9a44ee04a286`
- **作者**: MiYu
- **日期**: 2017-11-13 16:44:56
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/SearchProcessListBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/mobile/SearchProcessListBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileProcess.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/ProcessMgr.java`

### 294. 調整追蹤javabean的get跟set名稱
- **Commit ID**: `d2794f53f1ce6449c83ae5024d0d340a89478b8e`
- **作者**: MiYu
- **日期**: 2017-11-10 18:45:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/mobile/TraceProcessBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`

### 295. 新增同意派送架構 新增不同意派送架構 新增退簽派送架構
- **Commit ID**: `ae4bd080592804fef9d848fce0b171602239b63d`
- **作者**: jd
- **日期**: 2017-11-10 14:37:50
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataOpReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/RemoteUser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 296. 新增表單中間層單身顯示架構 新增表單中間層附件架構 調整WorkInfo
- **Commit ID**: `249d79112a78ca2dd63a4bd0537dd3bc2263e63c`
- **作者**: jd
- **日期**: 2017-11-09 18:17:03
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/RsrcBundleCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AllRsrcbundleValueBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/MobileSystem.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/WeChatSystemMgr.java`

### 297. 補上javabean漏寫空的建構式
- **Commit ID**: `77316807bcbbeebd7e57024573d91ebc75b19881`
- **作者**: MiYu
- **日期**: 2017-11-08 19:02:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/ProcessCommentTypeBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkItemStateTypeBeanRes.java`

### 298. Q00-20171107001 修正TT或T100流程在第一關收不到微信推播問題
- **Commit ID**: `878f567aa35105e82b288651af3009c98c4e71c7`
- **作者**: pinchi_lin
- **日期**: 2017-11-07 18:46:17
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailDTO.java`

### 299. C01-20171103004 修正核決關卡結束後不允許取回重瓣
- **Commit ID**: `26e5fede83aa6a880966018b1a24bb199c209fb2`
- **作者**: 張詠威
- **日期**: 2017-11-07 15:38:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 300. Q00-20171027002
- **Commit ID**: `2d942439cf78fd8124b06738cbc9e8bdf5c8edca`
- **作者**: 張詠威
- **日期**: 2017-11-06 16:39:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@syncisodoc/InitSyncISO_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@syncisodoc/InitSyncISO_Oracle.sql`

### 301. Q00-20171031001
- **Commit ID**: `4d39a6885942676886de7376d33ccd3d26e3f2c8`
- **作者**: 張詠威
- **日期**: 2017-11-06 16:28:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`

### 302. 調整取得所有多語系的RESTful服務
- **Commit ID**: `e4e5e89dd301b2188fa6cde27094d8f7d17ac4cd`
- **作者**: ChinRong
- **日期**: 2017-11-03 17:29:14
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/GetRsrcbundleBeanReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AllRsrcbundleValueBeanRes.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GetAllRsrcbundleValueBeanRes.java`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GetRsrcbundleBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 303. C01-*********** 修正Android手機新增grid資料被按鈕擋住問題
- **Commit ID**: `ab399b10bae08da5a2bd1afff20cb15dcf25cd07`
- **作者**: MiYu
- **日期**: 2017-11-03 16:28:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 304. A00-20171102001 修正 : ISO文件一覽表，文件類別展開畫面沒有scrollbar
- **Commit ID**: `690fcf5039a0e11623190e1268dffd084dcca07b`
- **作者**: joseph
- **日期**: 2017-11-03 11:34:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`

### 305. Q00-20171102001
- **Commit ID**: `75391bce27e8f853e716cd0bd930808327bdb414`
- **作者**: 張詠威
- **日期**: 2017-11-02 17:54:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 306. 調整增加判斷工作通知類別 InputElement純marge
- **Commit ID**: `89504a68b46ac966b3d362c7f98bf1681b84a9a8`
- **作者**: MiYu
- **日期**: 2017-11-02 16:13:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/NoticeProcessMgr.java`

### 307. C01-*********** 修正IOS上微信消息內容被切斷 導致內容異常問題
- **Commit ID**: `0381c64b7b43037df5b5d00ad04b1f9e37a8a66d`
- **作者**: pinchi_lin
- **日期**: 2017-11-01 19:24:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 308. A00-20171101001
- **Commit ID**: `4f55fd838d4b2e10b8f843323f8c46fa1687aadf`
- **作者**: 張詠威
- **日期**: 2017-11-01 17:12:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`

### 309. A00-20170807002  修正客製開窗變更比數後資料重複議題
- **Commit ID**: `0518635f679667c1d4e88da025ce35ce62be1db0`
- **作者**: 張詠威
- **日期**: 2017-11-01 16:21:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 310. A00-20170726004 加上防呆
- **Commit ID**: `ce4869c1d4a0b34fa71b1389e2d9bfc1ea490902`
- **作者**: 張詠威
- **日期**: 2017-11-01 16:15:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 311. A00-20170726004 修正通知樣版 代辦URL連結
- **Commit ID**: `e456ed75ce94759da59379b58b2903adb823c79c`
- **作者**: 張詠威
- **日期**: 2017-11-01 15:33:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 312. A00-20171031001 修正ESS流程在通知關卡無法打開行動版相對位置表單問題
- **Commit ID**: `bbd46d56e534c9d4c341cc7d97bdd9ed3ab34827`
- **作者**: pinchi_lin
- **日期**: 2017-10-31 17:48:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`

### 313. A00-20170822001 產品開窗使用SQL註冊器，到流程草稿會呈現空白
- **Commit ID**: `657e9d0677b04d8d577e3fcf35a50871be527dd2`
- **作者**: 張詠威
- **日期**: 2017-10-31 16:55:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`

### 314. 修正鼎捷移動表單中間層多語系無法顯示問題 修正鼎捷移動直連表單多語系無法顯示問題 修正列表過濾功能,流程重要性多語系無法顯示問題
- **Commit ID**: `cf82ec16fb44777a32221a22acea3d627267548c`
- **作者**: jd
- **日期**: 2017-10-31 11:49:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 315. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a4d9e76830313b45de7d5c9c507b05b7dfc46291`
- **作者**: jd
- **日期**: 2017-10-30 17:51:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 316. 修正鼎捷移動二期表單語系顯示問題
- **Commit ID**: `adafef365330ef0ae4485b62a96c2f6f05668e15`
- **作者**: jd
- **日期**: 2017-10-30 17:44:53
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 317. Q00-20171030001 補上RTX程式註解
- **Commit ID**: `68542131953721a12049c5cb56d8f2575da52b0c`
- **作者**: 張詠威
- **日期**: 2017-10-30 15:00:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/QueueHelper.java`

### 318. C01-20171018001 流程模型更新版本後，使用重發新流程畫面會一直轉 調整寫法
- **Commit ID**: `b48fcd65ee50addbc23bc73b218d6e52eebb9a9b`
- **作者**: 張詠威
- **日期**: 2017-10-30 14:33:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 319. Q00-*********** 修正 :關注事件模組開窗問題
- **Commit ID**: `b7651b6bba911ffb7a0de990af9e11532bacc829`
- **作者**: joseph
- **日期**: 2017-10-27 16:27:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`

### 320. A00-20170830001 修正 :ISO無法開啟參考文件
- **Commit ID**: `c373201ef905c2b94582ae2b8cfd05292f35fccc`
- **作者**: joseph
- **日期**: 2017-10-27 16:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`

### 321. A00-20170926001 修正 :產品客製開窗查詢功能及查詢樣板 ,輸入簡體文字無法查詢正確資料
- **Commit ID**: `41821cc439bdead6ca37fa1e06df9438e3c61148`
- **作者**: joseph
- **日期**: 2017-10-27 10:28:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 322. 修正鼎捷移動行事曆列表部份功能異常，增加直連表單的log
- **Commit ID**: `dab56a7a48719d7230dc647a9d4933fd5fd0d760`
- **作者**: ChinRong
- **日期**: 2017-10-27 08:56:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 323. 調整簽錯部分
- **Commit ID**: `8983124633e60a2ef55254fb56017cc3d60dcdaa`
- **作者**: pinchi_lin
- **日期**: 2017-10-26 17:26:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`

### 324. 調整鼎捷移動圖表邏輯
- **Commit ID**: `4bb53056055eb3d5cb65963a5234332c0754ddd8`
- **作者**: pinchi_lin
- **日期**: 2017-10-26 17:23:44
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 325. 修正鼎捷移動追蹤已完成流程取不到流程資料的問題
- **Commit ID**: `9b608fc5e835c4e706fbf3bb608dd3500fef58f8`
- **作者**: ChinRong
- **日期**: 2017-10-26 14:51:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 326. 新增取得全部多語系的RESTful服務
- **Commit ID**: `d029981060f75ad14f9cf041f3029d60167ac721`
- **作者**: ChinRong
- **日期**: 2017-10-26 11:49:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/GetAllRsrcbundleValueBeanRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 327. 修正取得待辦事項表單資料的RESTful異常
- **Commit ID**: `77f47789a185e44d6dcb8cc1c6ff6fb06245b402`
- **作者**: ChinRong
- **日期**: 2017-10-25 16:04:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/PerformProcessMgr.java`

### 328. C01-20171020001 修正ESS流程取語系時會有NullPointerException問題
- **Commit ID**: `2a83051aef9dd4e5f126de2ed0a3e45d63d248bd`
- **作者**: pinchi_lin
- **日期**: 2017-10-25 14:52:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileUserAuthTool.java`

### 329. 2次修正 關鍵事件開窗點選資料無法帶入欄位
- **Commit ID**: `e6a4f0ca4549ca1b9d4f3277c6f220ccaadc93f1`
- **作者**: joseph
- **日期**: 2017-10-25 12:07:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`

