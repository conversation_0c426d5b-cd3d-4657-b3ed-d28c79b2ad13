{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_5.8.5.2", "date": "2022-06-26 22:20:59", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2", "author": "lorenchang"}, "舊分支": {"branch_name": "release_5.8.5.1", "date": "2022-06-26 22:27:16", "message": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.1", "author": "lorenchang"}, "比較時間": "2025-07-18 11:42:35", "新增commit數量": 147, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "1254df25fe94e6cd2497db1924b0aa1db497b95b", "commit_訊息": "[內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.5.2", "提交日期": "2022-06-26 22:20:59", "作者": "lorenchang", "檔案變更": [{"檔案路徑": ".giti<PERSON>re", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/crm-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-builder/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/form-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-importer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/persistence/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/lib/WildFly/jboss-client.jar", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/system/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/pom.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "pom.xml", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 25}, {"commit_hash": "4f0b4c8f11376909fe04b1695032abd4e385fdae", "commit_訊息": "[BPM APP]Q00-20210226001 使用IE瀏覽器操作入口平台整合設定中其他工具佈署頁面中產生網址功能顯示異常", "提交日期": "2021-02-26 16:06:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e527ded52d38519a21646194d99e14509c3993ba", "commit_訊息": "[BPM APP]Q00-20210225006 修正在企業微信與IMG操作加簽與發送通知的選擇人員返回按鈕功能異常", "提交日期": "2021-02-25 19:47:09", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "23eebd2782ba0e61ec5750dfa8da4796453f94b1", "commit_訊息": "[流程引擎]Q00-20210225009 修正關注流程設定提示欄位設定中有新版表單新增的欄位時，舊版表單流程無法往下派送", "提交日期": "2021-02-25 16:59:27", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/CriticalProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "69623816c10d022454abf73640e27cc2b53f1a71", "commit_訊息": "[內部]Q00-20210225007 修正IMG取追蹤處理，依動態渲染表單條件的撈取流程列表異常問題", "提交日期": "2021-02-25 16:27:54", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0570c6146f71b053774a7bdcf93eaa7cf12497e8", "commit_訊息": "[內部]Q00-20210225005 修正在企業微信與IMG操作加簽的選擇人員全選功能異常問題", "提交日期": "2021-02-25 16:11:48", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "eef033a2f348a007daea043296cf25a37fd03e4f", "commit_訊息": "[BPM APP]Q00-20210225004 修正在IMG上操作同意或不同意按鈕會失敗問題", "提交日期": "2021-02-25 14:52:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "331a6f299f037a5f951294c923f14793959399d5", "commit_訊息": "[BPM APP]Q00-20210224001調整使用IE瀏覽器操作入口平台整合設定切回連線資訊管理頁面時，會出現其他工具佈署網址頁面資訊的問題", "提交日期": "2021-02-25 14:41:35", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3c0720e427888574ec396173de0e4ce01e3fbb6", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-02-25 14:07:09", "作者": "詩雅", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "17f8269d83dc173285a799cd6ef1956cbe237e96", "commit_訊息": "[BPM APP]Q00-20210224001 使用IE瀏覽器操作入口平台整合設定中其他工具佈署網址頁面中資料過多時無scrollbar", "提交日期": "2021-02-25 14:06:07", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e17abfe4c245a53943ba3e496edf69a341c7bcc", "commit_訊息": "[BPM APP]Q00-20210224002 修正IMG的轉派頁面點擊浮動按鈕後顯示的遮罩未蓋住所有畫面問題", "提交日期": "2021-02-25 14:05:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1945bb5a4f6239047907370515f4a14b9d7b4869", "commit_訊息": "[BPM APP]Q00-20210225003 修正企業微信操作加簽與發送通知功能的選擇人員畫面按鈕跑版問題", "提交日期": "2021-02-25 13:18:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3fadcf50f72d0816b9fdc7b37aeddb96714d077", "commit_訊息": "[Web]Q00-20210225001 修正追蹤流程SQL，符合條件總筆數未達1000確未正確撈出", "提交日期": "2021-02-25 09:06:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "afe91d70b9d6ee953bb4abeb88b42d40d9297fff", "commit_訊息": "[Web]Q00-20210223003 修正監控、待辦頁的進階查詢，用backspace和delete將欄位清空，對應的隱藏欄位資料未確實清除", "提交日期": "2021-02-23 18:10:16", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1c750286f2f29294785b365cd746fe1dd141b9f4", "commit_訊息": "修正集合快取無法移除或更新的異常(同時可讓58支持多AP)", "提交日期": "2021-02-23 15:28:30", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/attachment/AttachmentDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinitionCmItem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/ParticularRecord.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/ParticularRule.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/WorkCalendar.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/AbsImplementationType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/AbstractActivityDefContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ApplicationDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CompositeType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ConditionDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/CriticalProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/DecisionCondition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/DecisionLevel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/DecisionRuleList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/Implementation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessPackage.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessPackageCategory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessPackageCmItem.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/RedefinableHeader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/StrategyAssignDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/decision_sharing/DecisionConditionSharing.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/decision_sharing/DecisionLevelSharing.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/decision_sharing/DecisionRuleListSharing.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 26}, {"commit_hash": "c3854b8b746fea66171181ce500c1e6a922b614b", "commit_訊息": "[Web]Q00-20210107002 修正從待辦連結進入BPM簽核該流程實例，簽核後無法導向下一個簽核流程項目", "提交日期": "2021-02-23 14:46:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9999ed92e91b5deeb4cfe289fb2a6ed34e52ecb0", "commit_訊息": "[BPM APP]調整企業微信的簽核歷程依系統變數設定作排序", "提交日期": "2021-02-23 10:26:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "329652790e4deee39b9ecebdc423f18148ad29e1", "commit_訊息": "[Web]Q00-20210222001 修正barcode的值有某些特殊符號會導致掃描後資料缺少或異常", "提交日期": "2021-02-22 17:32:17", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/BarcodeElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f92ed4b3c74dcf381c1da9ad4292517cfdd91631", "commit_訊息": "[WorkFlow][YIFE]A00-20201211001 取得單別單號記錄時未用公司別區分，導致有同單別單號取錯資料回傳", "提交日期": "2021-02-22 15:32:59", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f1ff12332223983ae809a999dc90348d7f60baf0", "commit_訊息": "[Web]A00-20210203001 修正BPM首頁的待辦清單，除了第一筆流程實例，其他流程實例點擊進入都沒有\"處理下個工作按鈕\"[補修正]", "提交日期": "2021-02-22 15:14:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "03e501fa3ce11e6c5a3a9a4d647413f2ef1166df", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-02-22 14:58:06", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "092bab6d2db6bd76dd16e5bae566afd38c50ea57", "commit_訊息": "[Web]A00-20210203001 修正BPM首頁的待辦清單，除了第一筆流程實例，其他流程實例點擊進入都沒有\"處理下個工作按鈕\"", "提交日期": "2021-02-22 14:57:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b3f2b89f76d747c15c1b0e3551859dd6c9a048be", "commit_訊息": "[BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]", "提交日期": "2021-02-22 13:34:45", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cea39a3a2b4d3ddb7e47845e497cea25c6436a8d", "commit_訊息": "[BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]", "提交日期": "2021-02-22 12:03:17", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmWorkItemDataVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "b7c9af190706c0d2284604a41beced83569c5b93", "commit_訊息": "[BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能[補]", "提交日期": "2021-02-22 11:29:33", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d0cb39b084697e224582bb3cd6ab88f33180fc31", "commit_訊息": "[BPM APP]新增行動端詳情表單簽核頁面支援發送通知按鈕功能", "提交日期": "2021-02-22 10:59:47", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "2ae4915dcfac26725e94c6e6538932a303af8230", "commit_訊息": "[E10]S00-20210105001 新增E10整合表單在行動端上支援E10表單過濾主鍵欄位功能[補]", "提交日期": "2021-02-20 09:43:34", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "380142de726b23e6d2a193b652aa919febedc428", "commit_訊息": "[E10]S00-20210105001 新增E10整合表單在行動端上支援E10表單過濾主鍵欄位功能", "提交日期": "2021-02-19 19:51:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormDefinitionJSONTransfer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "f3574b55544550287458ee027cfeda872f18a927", "commit_訊息": "[流程引擎]A00-20201214001 修正流程處理->系統通知沒有「轉由他人處裡」的通知", "提交日期": "2021-02-19 16:08:22", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c8f18aa56a0bb068dccfabbe9e21122679064c83", "commit_訊息": "[T100]修正 T100出貨表單axmt500單身裡的欄位Id有重覆", "提交日期": "2021-02-17 10:28:14", "作者": "林致帆", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form-default-t100.zip", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ed6df4ad255597ebf7041317083329244a57a9b", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-02-08 15:08:56", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "6d382ec0b191a11c8f98a21ad94fe549fa93aa2f", "commit_訊息": "[流程設計師]A00-20210201002 修正 複製一般關卡，流程在該複製的關卡加簽，加簽完會直接結案", "提交日期": "2021-02-08 15:02:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "93e7f2c7c72beaffb34fc04e0af626b0841af505", "commit_訊息": "[Web]A00-20210204001 修正系統多語系維護作業，當「設定值」內容有大寫字母，進入編輯時多語系Grid無資料", "提交日期": "2021-02-05 17:34:43", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/LanguageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed3a391a6f35f7b0f637cb01ba736d6f555e015a", "commit_訊息": "[BPM APP]S00-20210111002 調整LINE推播訊息依照流程關卡中的允許批次簽核來卡控是否顯示直接簽核按鈕", "提交日期": "2021-02-05 17:18:26", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ab5b6909ea26afa814a5f75eb9dd3e2f1dd2cbf", "commit_訊息": "[BPM APP]Q00-*********** 調整在Line整合方案時行動簽核管理中心的移動消息訂閱管理頁面改撈取所有流程機制", "提交日期": "2021-02-05 16:11:02", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SystemConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAllProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileSubscribeAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "92bf3c90387495c9c2a8d7bfad1747a99dd9cee1", "commit_訊息": "[流程設計工具]Q00-20210205002 修正某些個人環境操作時會出現畫面錯亂的異常", "提交日期": "2021-02-05 15:06:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/DesignerMainApp.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "06a1afd5e96bbb854b0620867d741cd2fea9efb0", "commit_訊息": "[流程引擎]Q00-20210205001 麗智電子客製-核決層級解析邏輯調整", "提交日期": "2021-02-05 13:52:28", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "31f151a996aae3beae633c9f1af66f62745945ad", "commit_訊息": "[DotJ]Q00-20210204004 修正修正如果流程有核決權限表，送簽會出現LazyInitializationException", "提交日期": "2021-02-04 18:06:42", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0fb6c811b3e6e7b79c8495529a16af8d6cdb6f2e", "commit_訊息": "[DotJ]Q00-20210204003 修正取得BPM流程圖出現NullPointerException", "提交日期": "2021-02-04 17:51:12", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceLocator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2abe1002f9f122fc95042fbbc377f65d272473c4", "commit_訊息": "[Web]A00-20210202001 Portal導向BPM，BPM首頁左側功能列語系與Portal傳過來的語系不相同", "提交日期": "2021-02-04 17:34:08", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProgramDefinitionDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "878e03cc562d208a2757d872b1d5df50ebfefbca", "commit_訊息": "[流程引擎]Q00-20210204002 修正已封存的工作通知清單中查不到已閱讀的工作通知", "提交日期": "2021-02-04 16:45:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e2ccd098ba3bdd16f2f81ba34ee4c6abd15aa4f9", "commit_訊息": "[Web]Q00-20210204001 進度條載入優化，減少卡頓的感受改為等速載入", "提交日期": "2021-02-04 15:21:17", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b2a8c5cdedb05e961a36c92f62aee8a2e02b36be", "commit_訊息": "[BPM APP]Q00-20210201004 修正入口平台整合設定中其他工具佈署網址頁面中資料過多時無scrollbar問題", "提交日期": "2021-02-03 17:31:31", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3796c27ce2620530f9aef9e8e55939470018cca2", "commit_訊息": "[組織設計師]A00-*********** 修正人員有任職部門主管並卸任主管職位後，需重啟BPM才能設為離職", "提交日期": "2021-02-03 14:34:37", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "5301c117f19e9519ab14a9f11760e42c90c17c7e", "commit_訊息": "[系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]", "提交日期": "2021-02-03 12:15:39", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/onlinemgt/OnlineUserMgtPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "d5bf6a8db6f6eb655fd18a1e3765823e76384b52", "commit_訊息": "[Web]A00-20210201001 調整表單浮點數欄位運算邏輯", "提交日期": "2021-02-03 11:50:39", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1b105cbffca2745466050688af15dfbb9e0d53ff", "commit_訊息": "[組織同步]Q00-20210129001 修正組織同步StepTtls跟StepRoles的IsRemoveData設為true，同步後不會移除相關資料的異常", "提交日期": "2021-02-03 11:45:09", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b20a86db6eb54f3ff7e048ff4d4e3a49b70fc365", "commit_訊息": "Revert \"[Web]A00-20210201001 修正在Chrome上的浮點數四捨五入計算結果與在IE上的不一致\"", "提交日期": "2021-02-03 10:33:20", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82036b385a5577a5bf10e2deb12e53b4c24d8675", "commit_訊息": "[Web]A00-20210201001 修正在Chrome上的浮點數四捨五入計算結果與在IE上的不一致", "提交日期": "2021-02-02 15:16:19", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormManager.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1038e26b4f94bb7154f36fc1f05c408299bbd2a4", "commit_訊息": "[BPM APP]Q00-20210120002 修正IMG推播消息為動態選染表單時加提醒後於行事曆查看顯示非動態渲染表單畫面的問題", "提交日期": "2021-02-02 14:40:59", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8d3f10ff205ec0492e9d2c1057e344545e56b313", "commit_訊息": "[BPM APP]Q00-20210201005 修正IMG的中間層與動態渲染表單新增明後天提醒後回應的資訊無多語系問題", "提交日期": "2021-02-02 10:30:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a3bcabaa76929b2eb2a63ff93cbb77c99e12b40a", "commit_訊息": "[內部]補上被覆蓋掉的多語系", "提交日期": "2021-02-01 17:21:35", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2583c04c4b0fdbcdac614722d707ea225009f937", "commit_訊息": "[BPM APP]Q00-20210201006 調整入口平台整合設定的其他工具佈署網址中按鈕沒有對應提示訊息問題", "提交日期": "2021-02-01 16:41:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatformDeployTool.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cffc496c1ae57e0ae4ade06c4c914889ed65b4c7", "commit_訊息": "[Web]Q00-20210201002 隱藏IE預設的清除按鈕", "提交日期": "2021-02-01 15:37:12", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bef009cf69e8c6626b754355621a06c272c3976c", "commit_訊息": "[Web]A00-20210128001 修正 外部連結-追蹤流程實例內單一表單資料，點擊列印鍵沒有反應", "提交日期": "2021-02-01 14:17:24", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f510bad79313701322667f1a8ae314fd8c0018a6", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]", "提交日期": "2021-02-01 10:34:38", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0d6d5e9c7066b62a65d93eafa0df90d5071318e3", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]", "提交日期": "2021-01-29 20:17:05", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "196a2fb53e6e7d9c7f98d0912083b2b0d6e10717", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補] 1. 調整待辦列表 2. 調整通知列表 3. 統計元件筆數", "提交日期": "2021-01-29 19:29:40", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "20d8e3a9326bfde29c5583d847ade9761f3d70cf", "commit_訊息": "[內部] 更新越南語系", "提交日期": "2021-01-29 16:28:46", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3353e73a2ebd40f352ee796def11f73617fce80a", "commit_訊息": "[E10]新增E10整合表單同步時自動產生行動端Grid載入語法[補]", "提交日期": "2021-01-29 11:12:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a38a25d0cdc19ae8a4effa23beb614ca91827d0b", "commit_訊息": "[E10]新增E10整合表單同步時自動產生行動端Grid載入語法", "提交日期": "2021-01-29 10:16:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormDefinitionJSONTransfer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "57a49c4fc94f40b03febcb660c597bd387afbf40", "commit_訊息": "[WEB]S00-20201202001 調整流程管理-作業程序書的「使用表單」欄位的樣式為垂直置中", "提交日期": "2021-01-28 15:09:32", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d2dabb63c3a66bfe1ee1c9b30b0ba82901f7a69f", "commit_訊息": "[Web]A00-20201022001 修正流程主旨中最後是\\的符號，會導致待辦、監控、追蹤流程的清單頁會無法開啟", "提交日期": "2021-01-28 13:59:38", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "03c019bd76c95c313ffbdb605bfcec40292b6fd9", "commit_訊息": "[BPM APP]調整行動端詳情簽核派送時可依系統變數設定啟用檢查簽核意見是否為空功能", "提交日期": "2021-01-28 10:10:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "4b16243c9595b430d717fda3357d37a4ce86c5c9", "commit_訊息": "[流程引擎]Q00-20210127002 修正組織同步後WorkCalendar.containerOID被清空", "提交日期": "2021-01-27 18:43:10", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Organization.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a1964ea72903a0185e8f03ae9d59e76723087c56", "commit_訊息": "[T100]回寫T100服務任務關卡回傳錯誤訊息回寫至BPM流程變數並設定流程讓表單回到「人員處理」關卡，將訊息顯示在簽核畫面上", "提交日期": "2021-01-27 17:10:25", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPI.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPIBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPILocal.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "878e0f732379bd2e67075ec1776bbd8ff04c3a95", "commit_訊息": "[流程設計師]A00-20210119002 修正流程的頭兩個關卡將Dialog元件設為invisible，派送到第三關會報錯", "提交日期": "2021-01-27 16:48:58", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f539b942db9dc8b038134046e45c069c2a52745b", "commit_訊息": "[流程引擎]A00-20210126003 修正表單人員開窗設定過濾條件後以\"人員名稱\"為條件查詢無效", "提交日期": "2021-01-27 15:45:03", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "70f4c828118e63a6f0872db985d7a76b61460ddd", "commit_訊息": "[Web]A00-20210114001 修正追蹤流程連結打開Tiptop流程時無法下載URL附件", "提交日期": "2021-01-26 17:39:57", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "75402b56249808072015247a3c2570fc5eae020b", "commit_訊息": "[WEB]A00-20210112002 修正流程處理追蹤流程處理的流程 Grid清單，點擊時間排序會發生異常", "提交日期": "2021-01-26 15:41:54", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a6db0ee8f503e73b69e282d9475687d64d36bb00", "commit_訊息": "[流程設計師]A00-20210122001 修正XPDL流程中有空白子流程時，無法轉換為BPMN流程", "提交日期": "2021-01-26 11:54:10", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12f324129c08c6a8bc247af3f49df5bde41edcf8", "commit_訊息": "[Web]A00-20210114003 修正在帳號管理頁面，將頁面顯示筆數選大於10，會無法切換下一頁和上一頁", "提交日期": "2021-01-25 17:54:30", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/UserManageAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "653ebe8f1910c39b46b73daa871cb0bbe72f28ed", "commit_訊息": "[流程引擎]A00-20210121001 修正追蹤流程>已轉派工作，已處理\"與全部頁籤中模糊查詢功能無效異常", "提交日期": "2021-01-25 17:29:48", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d10e357fc8d8846cf5562fe41566427c1814a2c1", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]", "提交日期": "2021-01-25 14:17:20", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileReassignedWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f2872fe92b22169558a52d44720950f0d3c7abb8", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]", "提交日期": "2021-01-25 11:11:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "907e40ec610cec37c69215645c60c14ae1cf120b", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]", "提交日期": "2021-01-25 10:33:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "4a39493a1ce16d8eaa752c8aa5cbac9986aded92", "commit_訊息": "[Web]Q00-20210122001 將Date元件出現提示訊息的字體顏色調整成紅色", "提交日期": "2021-01-22 18:14:58", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8b491560b769f8fb60f393d0d7d70592d334911c", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]", "提交日期": "2021-01-22 12:51:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "bad7308c7fbdedfa0832493f7291944d3d81c036", "commit_訊息": "[BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定", "提交日期": "2021-01-21 18:58:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "700df16bc0b08271dce78c05d50512ab82e8f4b3", "commit_訊息": "[流程引擎]A00-20210118002 修正關注流程參考表單欄位時，若舊版表單沒有該欄位，則已發起的舊版表單流程無法往下派送", "提交日期": "2021-01-21 10:55:49", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/ConditionEvaluator.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba6c0d9277a30496eb0b8eb17dbdc3d42f4c8031", "commit_訊息": "[Web]A00-20210118001 修正進入一筆待辦並填寫簽核意見後，點選回到工作清單鈕，再進入待辦任一流程簽核意見沒被清除", "提交日期": "2021-01-20 18:10:31", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a49f4b7a881a17b16f45a9c4a2caf33f28387863", "commit_訊息": "[流程引擎]A00-20201224001 修正表單開窗元件設定過濾條件無效異常", "提交日期": "2021-01-20 15:55:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0b2319dc4b543a2f96a222c9e70b25fe26e1a55", "commit_訊息": "[系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]", "提交日期": "2021-01-20 11:59:38", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/onlinemgt/OnlineUserMgtPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "295f295b4efaf8c25275c05a17bb36d57bd9bc85", "commit_訊息": "[系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位[補]", "提交日期": "2021-01-20 11:07:08", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/license/ConnectedUserInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/onlinemgt/OnlineUserMgtPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7f518bc09f4bc3ab87db68270b5a4d75bf632d27", "commit_訊息": "[Web]Q00-20210120001 修正administrator登入行動版，左方選單會出現BPM首頁", "提交日期": "2021-01-20 11:03:26", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1263973a05982a2745eb69b21d8aae3368769048", "commit_訊息": "[BPM APP]調整IMG用的接口支持驗證BPM的access token", "提交日期": "2021-01-20 09:58:12", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleModuleFeatures.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "5f528ab3d7b05ea1696d02d4e954477477a4ea77", "commit_訊息": "[Web]Q00-20210119003 修正絕對位置表單在更換image後，在預覽時顯示圖片不正確", "提交日期": "2021-01-19 17:28:40", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ImageElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9d3da981c75bb7c4633300443389661930cea69a", "commit_訊息": "[流程引擎]S00-20201214001 簡易流程圖的關卡中若有部分處理者已離職，則不顯示已離職的人員", "提交日期": "2021-01-19 17:23:12", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9c79b28b7abfe03172b292573ef7b60f9ab334b1", "commit_訊息": "[系統管理工具] 線上使用者資訊新增登入裝置與入口平台欄位", "提交日期": "2021-01-19 15:49:14", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9770dec5776914c544a914c1330286e4a8b7853e", "commit_訊息": "[流程引擎]Q00-20210115002 修正在組織設計師刪除某部門時，若該部門有被其他物件參考，則提示相關內容", "提交日期": "2021-01-15 14:29:10", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/workflow_definition/IParticipantDefContainerDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "79ca63601a7786458fa8a5fb822a77254cd0c23e", "commit_訊息": "[BPM APP]Q00-20210114003 修正行動簽核管理中心的使用者批次維護頁面選擇非Excel的檔案匯入時顯示錯誤訊息的多語系異常", "提交日期": "2021-01-14 19:07:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6a412ba10dcb6124edc44d2a9863c6663b281660", "commit_訊息": "[BPM APP]Q00-20210112001 修正授權中間層的批次匯入使用者無法使用新版的EXCEL匯入", "提交日期": "2021-01-14 19:01:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserImport.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "9e0af2ad8648e17725c3bbb427136e04cf393252", "commit_訊息": "[BPM APP]Q00-20201231001 修正IMG已結案流程應用中處理的流程其流程名稱篩選條件筆數異常問題", "提交日期": "2021-01-14 16:25:04", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5967dc71515241a1393f93924b6f9dad1d4e2faf", "commit_訊息": "[BPM APP]Q00-20201231001 調整行動簽核管理中心的智能管理中心顯示機制", "提交日期": "2021-01-14 15:09:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aff2393be521e4b95f4ec7114dc127b3e0c2a1b1", "commit_訊息": "[WEB]Q00-20210114002 修正維護樣板QueryTemplate.js，當設定為雙欄顯示，且查詢條件數量為單數時，畫面會排版異常", "提交日期": "2021-01-14 14:56:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f694b56e9e3d3a3b80dbd00695334c1e3080e8d3", "commit_訊息": "[BPM APP]Q00-20201223003 修正IMG的明天提醒與後天提醒英文語系拼錯問題", "提交日期": "2021-01-14 11:17:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de55414d76cfb31f91abcd07ae8912a3238156d5", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2021-01-13 18:42:13", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "6a96790d8e66990c346ce0eba022cae478f7ee4d", "commit_訊息": "[流程引擎]Q00-20210113002 人員名稱有特殊字導致開啟ESS表單報錯", "提交日期": "2021-01-13 18:40:26", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6613f73620a849256239143cc7fc46b0174e0981", "commit_訊息": "[BPM APP]Q00-20201222001 修正鼎捷移動APP上的部份應用未支持多語系的問題", "提交日期": "2021-01-13 18:40:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "85f097219d00051e6c5adb61f2cf8f5a500c0191", "commit_訊息": "[Web]Q00-20210113001 修正編輯系統排程後，系統仍按照編輯前的設定內容執行排程", "提交日期": "2021-01-13 18:25:59", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SystemScheduleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "decf52630011d4cb47db5b9cac2ada41648ee19e", "commit_訊息": "[流程引擎]A00-20210105001 修正經過invoke關卡後，流程主旨參數內容備替換成英文語系的內容", "提交日期": "2021-01-13 15:12:20", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "891ea01bec802cc3d81d72c3df73b97328b8da2d", "commit_訊息": "[BPM APP]Q00-20210104002 調整IMG在動態渲染表單作連續簽核時下一筆流程表單沒同步時會異常問題[補]", "提交日期": "2021-01-12 19:07:55", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ebdc122de46db2223913fa5da4d46ee2fb92e41", "commit_訊息": "[BPM APP]Q00-20210104002 調整IMG在動態渲染表單作連續簽核時下一筆流程表單沒同步時會異常問題", "提交日期": "2021-01-12 18:42:04", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterOprationRes.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ceea5cdeecfb85204708af3776235f26d6f288e1", "commit_訊息": "[Web]Q00-20210112002 日期元件有勾選顯示時間，在開日期窗未選擇日期時按下確定會出現提示訊息", "提交日期": "2021-01-12 16:14:56", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "27ad9af053135f7af75210b033a594f2f0f6d1f8", "commit_訊息": "[BPM APP]Q00-20201231003 修正在IE和Safari瀏覽器頁面整合授權中間層時可選擇隱藏的授權類型問題", "提交日期": "2021-01-12 15:51:27", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e156eaf3ab83a618cf4538bebc23d24e25c286f0", "commit_訊息": "[BPM APP]Q00-20210104001 調整入口平台整合設定中測試連線狀態若出現錯誤訊息時會跑版問題", "提交日期": "2021-01-12 14:44:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5d5995733a4158ec6fdf3573f6244cc79b1c166", "commit_訊息": "[BPM APP]S00-20200514001 調整行動端發起/簽核時的選擇部門樣式", "提交日期": "2021-01-11 19:09:40", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobileSelect.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "ecf00b83116579bb5d43f6f3c9b22b32e016882f", "commit_訊息": "[BPM APP]C01-20201215003 調整追蹤狀態下表單的全域變數viewMode參數值設定為\"TRACE\"", "提交日期": "2021-01-11 13:41:09", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileResigendServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3baac870834566dc24e79ad521df97befbaec2b5", "commit_訊息": "[流程引擎]A00-20210107002 修正WebServie getFormFieldTemplate，複合元件若為自定義開窗時無法填值", "提交日期": "2021-01-08 11:50:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "920dd6240b422acac66ae4c1d638a2102e49351f", "commit_訊息": "[Web]Q00-20210107001 修正流程發起時，若無法解析流程關係人，未呈現相關提示訊息", "提交日期": "2021-01-07 10:50:37", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b62a031c372701d79501202557a2a3355052b5dd", "commit_訊息": "[Web]Q00-20210105001 修正Date元件有勾選顯示時間時，日期選擇窗有換頁過就無法手動輸入時間", "提交日期": "2021-01-05 18:13:52", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmCalendar.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d92aeb62e09ea9dcd60a8afaaff2f2f7bd2731d0", "commit_訊息": "[BPM APP]C01-20201230003修正當瀏覽器為IE時，操作整合頁面Scrollbar無法滾動問題(含授權中間層&入口平台整合設定頁面)", "提交日期": "2021-01-05 16:26:38", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f6864abdd6cdcbe0d6ae2fd380b670922a6eba8e", "commit_訊息": "[Web]A00-20210104001 修正使用HRM Portlet進入BPM追蹤流程，執行撤銷流程時無法填寫撤銷理由", "提交日期": "2021-01-05 10:55:02", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c463270cc6e4a5cd3e20555d8e31414bc50bb223", "commit_訊息": "[Web]A00-20201215002 修正使用formTerminateSave來終止流程時，運作邏輯的順序不正確", "提交日期": "2021-01-04 18:16:46", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "de641fb72c5791627940467bd2e5a0552e4fbe4e", "commit_訊息": "[BPM APP]A01-20210104002 修正整合LINE的部分用戶無法收到系統排程BPM首頁通知推送的問題", "提交日期": "2021-01-04 17:55:53", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "901b3dc6a409d87377142af1c6f53e57cf3eedc5", "commit_訊息": "[流程引擎]Q00-20210104003 修正WebService接口getFormFieldTemplate回傳的值與實際儲存的結果不一致", "提交日期": "2021-01-04 17:32:11", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "681d579ed36e98dd534ebc71d1bc82941d0c64de", "commit_訊息": "[內部]調整排版與移除不必要的部分", "提交日期": "2021-01-04 15:04:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogAspect.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceBpm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/LogRestfulServiceDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/spring-restconfig.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "05a2cffff930ad3508afe70df42532ba6621a4c8", "commit_訊息": "[Web]Q00-20200806002 產品回收NTKO專案[補]", "提交日期": "2020-12-31 17:06:52", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f5e167a254dd4b5b1c46044d87482815455498db", "commit_訊息": "[流程設計師]A00-20201222001 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞", "提交日期": "2020-12-31 16:07:55", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc65402c5b580abe9af64fed7a898e05783f216c", "commit_訊息": "Revert \"[流程設計師]A00-20180129002 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞\"", "提交日期": "2020-12-31 16:04:36", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3526271b56c319197930716dca03a2142b508e52", "commit_訊息": "[流程設計師]A00-20180129002 修正如果雙擊修改未入版的核決關卡，會把所有核決關卡定義弄壞", "提交日期": "2020-12-31 16:01:58", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNDiagram.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc5f4bcd57648baccdcbfb2e7ea6dd1c2ce1927a", "commit_訊息": "[流程引擎]Q00-20201231002 修正Radio/CheckBox元件設定\"最後一個選項額外產生輸入框\"，轉存表單未將輸入值存入資料庫", "提交日期": "2020-12-31 15:34:28", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f75c547a071c68d67d9239dede95b3f54553ff73", "commit_訊息": "[BPM APP]Q00-20201228002 修正詳情表單加提醒時，若有同步動態渲染表單，在行事曆查看流程時可看到動態渲染表單[補]", "提交日期": "2020-12-31 13:17:19", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "311f359f3de85e3b781d22183db35c231ad11e31", "commit_訊息": "[Web]Q00-20201229002 表單有元件使用資料註冊器時，資料註冊器沒有任何一筆資料的狀況下，開啟表單會異常", "提交日期": "2020-12-29 17:49:07", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f23100fa02f69e9568caa7623018bc3f58a5c262", "commit_訊息": "[流程引擎]Q00-20201229001 調整進入程式權限設定清單時清除殘存資料的SQL，以提高系統執行效能", "提交日期": "2020-12-29 14:21:12", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ed174c5d3dd3e3b4f3fa0e1b6590d3feb7141d47", "commit_訊息": "[BPM APP]Q00-20201228002 修正詳情表單加提醒時，若有同步動態渲染表單，在行事曆查看流程時可看到動態渲染表單", "提交日期": "2020-12-28 18:31:03", "作者": "詩雅", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7ecd591a7716e0df328923c336fab13c81043fbf", "commit_訊息": "[ESS]新增ESS整合表單:ESSF50B班次變更(多人多天)", "提交日期": "2020-12-28 17:36:10", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\\346\\227\\227\\350\\211\\246/ESSF50B\\347\\217\\255\\346\\254\\241\\350\\256\\212\\346\\233\\264(\\345\\244\\232\\344\\272\\272\\345\\244\\232\\345\\244\\251).form\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "fb9986bcae2d4e7fe56f3ea711c7721596be830f", "commit_訊息": "[Web]Q00-20201228001 修正系統排程設定頁面中，排程生效時間下拉元件太窄導致內容無法完整顯示", "提交日期": "2020-12-28 12:04:26", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d7d610ea46ca2d21e6b0461b50681a15eddfeff8", "commit_訊息": "[BPM APP]Q00-20201225004 修正入口平台整合設定維護作業存在連線資訊時用手機操作其畫面不會顯示任何資訊問題", "提交日期": "2020-12-25 16:16:35", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8b185876cdb6aa4f8df461b39084d28315456cf2", "commit_訊息": "[Web]A00-20201217001 修正追蹤流程SQL，符合條件總筆數未達1000確未正確撈出", "提交日期": "2020-12-25 15:48:41", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0df38996bf7115d4185a466c1ff49d3d97f7017c", "commit_訊息": "[流程引擎]Q00-20201225003 進入程式權限設定清單時，清除刪除部門後所殘存的程式權限資料，避免維護作業發生異常，無法儲存修改的資料", "提交日期": "2020-12-25 15:39:47", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a9bf3262c73223c5ff718ebadf02249ba14f55a", "commit_訊息": "[流程引擎]Q00-20201225002 修正組織同步修改使用者資料後，使用者帳號狀態變更為\"未啟用\"", "提交日期": "2020-12-25 15:11:51", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0e9638d2336075e161ca558426b5afa4378b5fff", "commit_訊息": "[BPM APP]Q00-20201222003 修正授權中間層管理維護作業存在連線資訊時用手機操作其畫面時不會顯示任何資訊問題", "提交日期": "2020-12-25 14:38:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a15da9abaa9fac0ce3583d0ec275b40eddc96b5a", "commit_訊息": "[BPM APP]Q00-20201225001 修正IMG動態渲染表單點明天後天提醒會失敗問題", "提交日期": "2020-12-25 11:36:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d435133182724c5a0e31d0c299a5a351442730e7", "commit_訊息": "[流程引擎]A00-20201211004 調整DealOvertimeActivity的SQL，提升運行效率", "提交日期": "2020-12-24 14:48:26", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c217c54aa9a98ed2718be3b7a3012b861685729e", "commit_訊息": "Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58", "提交日期": "2020-12-24 10:12:47", "作者": "林致帆", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "86deb73c7cf824af3f9188e2a7d8edea761ff59a", "commit_訊息": "[內部]新增CRM表單-報價變更單", "提交日期": "2020-12-24 10:12:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@crm/form-default/\\345\\240\\261\\345\\203\\271\\350\\256\\212\\346\\233\\264\\345\\226\\256.form\"", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 1}, {"commit_hash": "b43a1eb49f9d5512ba014563388ef320193967e7", "commit_訊息": "[內部]C01-20201208006 修正如果是留職停薪(intermissionDate)有可能導致離職人員作業維護出現異常(NullPointerException)", "提交日期": "2020-12-23 18:24:13", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/resignedEmployees/ResignedEmployeesManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "722ba2912dbcbe0a5fd8d388ed0904b5ed611b9f", "commit_訊息": "[Web]A00-20201203001 修正當有類html tag時導致顯示異常，並將調整只開放a標籤，其餘直接顯示", "提交日期": "2020-12-23 18:15:06", "作者": "王鵬程", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "518bb47128544a93961c12ee9ba61cfa3b78c9d5", "commit_訊息": "[內部]Q00-20201218002 ActionFilter　log訊息調整", "提交日期": "2020-12-23 16:48:21", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1384f6c17a9f7dcb383773616a53a0d854334c59", "commit_訊息": "[流程引擎]Q00-20201120005 修正流程進行轉派任務後再取退回重辦(按流程定義退回)，派送的處理者不正確", "提交日期": "2020-12-23 16:42:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6c298ec562ccb194b21a88aa521d8892e9addfbe", "commit_訊息": "Revert \"[流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確\"", "提交日期": "2020-12-23 16:40:16", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a00f29cf4ac3bf4347de2173b31bcb20b144b8f1", "commit_訊息": "[流程引擎]Q00-20201120004 修正流程進行轉派任務後再取回重辦，派送的處理者不正確", "提交日期": "2020-12-23 16:29:45", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4f69d59f6608b216ce7d90cdb6ecc25517f70488", "commit_訊息": "[Web]A00-20201103001 修正重新發起流程可以往前加簽的問題", "提交日期": "2020-11-30 17:59:13", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7db0349bad0208ed36ab3b03184f01e77af93db5", "commit_訊息": "[Web]C01-20201130004 修正點已經被簽過的Email簽核連結進入確可以再簽一次的錯誤", "提交日期": "2020-12-23 16:00:05", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e88ff378752a4afb8bc7b60b1e0cc9b0e1919d8c", "commit_訊息": "[Web]A00-20201201001 修正不是在代辦清單或追蹤清單頁的其他頁面，點選右上角的單筆代辦直接進入代辦，之後按返回上一頁畫面會卡死", "提交日期": "2020-12-23 15:47:06", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f641ed98d88ca942a79c2e04fabdada6b53d792", "commit_訊息": "Q00-20201223002 修正從E10取得附件接口，在Invoke參數Id設定錯誤的狀態下並未拋錯，導致未取得附件流程就往下走", "提交日期": "2020-12-23 15:37:17", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}