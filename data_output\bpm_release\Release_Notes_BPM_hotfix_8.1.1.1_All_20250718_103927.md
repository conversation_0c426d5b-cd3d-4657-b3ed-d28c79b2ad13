# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_8.1.1.1_All
- **舊版本**: release_8.1.1.1
- **生成時間**: 2025-07-18 10:39:27
- **新增 Commit 數量**: 5

## 變更摘要

### lorenchang (4 commits)

- **2025-07-10 11:15:37**: [ESS]C01-20250604003 增加避免串單的卡控機制：1.檢查當前流程與Session內的ProcessSerialNumber是否匹配，2.檢查Identifier有沒有被其它流程用過
  - 變更檔案: 7 個
- **2025-05-26 14:27:21**: [ESS]C01-20250514003 加入Log及JDBC連接已關閉之重取機制，避免出現無法發起ESS流程的異常
  - 變更檔案: 2 個
- **2025-05-12 15:39:44**: [流程引擎]C01-20250429006 修正單身中繫結 Checkbox 或 RadioButton 的欄位，若透過非標準方式（如 Excel 匯入）產生表單資料後再編輯第 2 筆以後的內容時，因欄位不存在導致轉存表單失敗的問題(補)
  - 變更檔案: 1 個
- **2025-05-05 14:32:09**: [流程引擎]C01-20250429006 修正單身中繫結 Checkbox 或 RadioButton 的欄位，若透過非標準方式（如 Excel 匯入）產生表單資料後再編輯第 2 筆以後的內容時，因欄位不存在導致轉存表單失敗的問題
  - 變更檔案: 1 個

### yamiyeh10 (1 commits)

- **2025-04-29 13:47:08**: [ORGDT]Q00-20250429001 修正Web組織管理工具無法新增部門問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [ESS]C01-20250604003 增加避免串單的卡控機制：1.檢查當前流程與Session內的ProcessSerialNumber是否匹配，2.檢查Identifier有沒有被其它流程用過
- **Commit ID**: `dfaf64be6336becd23671aece84c053f6cc3ddcc`
- **作者**: lorenchang
- **日期**: 2025-07-10 11:15:37
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/exception/AppFormProcessMismatchException.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormCacheSingletonCollection.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AppFormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/appform/EPIBasePageJS.js`

### 2. [ESS]C01-20250514003 加入Log及JDBC連接已關閉之重取機制，避免出現無法發起ESS流程的異常
- **Commit ID**: `11d75a224d6189314ea285bd73bc3e4d89a75832`
- **作者**: lorenchang
- **日期**: 2025-05-26 14:27:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/persistence/JDBCReadingHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 3. [流程引擎]C01-20250429006 修正單身中繫結 Checkbox 或 RadioButton 的欄位，若透過非標準方式（如 Excel 匯入）產生表單資料後再編輯第 2 筆以後的內容時，因欄位不存在導致轉存表單失敗的問題(補)
- **Commit ID**: `784f1282e358da10d77cf03227663c0bacaa2189`
- **作者**: lorenchang
- **日期**: 2025-05-12 15:39:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 4. [流程引擎]C01-20250429006 修正單身中繫結 Checkbox 或 RadioButton 的欄位，若透過非標準方式（如 Excel 匯入）產生表單資料後再編輯第 2 筆以後的內容時，因欄位不存在導致轉存表單失敗的問題
- **Commit ID**: `c9dd3650aeee28d76223a3753b2a9ca10ddbdef7`
- **作者**: lorenchang
- **日期**: 2025-05-05 14:32:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 5. [ORGDT]Q00-20250429001 修正Web組織管理工具無法新增部門問題
- **Commit ID**: `29c73c544096152c788f242b516c8d72aa82c2a5`
- **作者**: yamiyeh10
- **日期**: 2025-04-29 13:47:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`

