# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.9.2
- **舊版本**: release_*******
- **生成時間**: 2025-07-18 11:33:13
- **新增 Commit 數量**: 232

## 變更摘要

### 林致帆 (59 commits)

- **2023-05-26 10:36:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個
- **2023-05-12 17:52:42**: [流程串接套件]Q00-20230512004 調整流程代號在資料庫被更動，派送流程關聯設定作業的查詢作業也不該顯示異常
  - 變更檔案: 3 個
- **2023-05-11 13:46:52**: [流程引擎]S00-20230331002 新增索引
  - 變更檔案: 4 個
- **2023-05-11 11:02:08**: [Web]Q00-20230511001 調整ErrorPage如果錯誤訊息小於30字，直接顯示在頁面上而不會只出現在"詳細資訊"中
  - 變更檔案: 2 個
- **2023-05-05 15:42:43**: [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
  - 變更檔案: 2 個
- **2023-05-04 11:49:50**: [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
  - 變更檔案: 1 個
- **2023-05-04 10:40:48**: Revert "[PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空"
  - 變更檔案: 3 個
- **2023-05-04 10:40:10**: Revert "[PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]"
  - 變更檔案: 1 個
- **2023-05-04 10:37:21**: [E10]Q00-20230504001 修正E10表單同步單身同時有新增，刪除欄位，就無法新增成功
  - 變更檔案: 1 個
- **2023-05-03 17:35:08**: [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
  - 變更檔案: 1 個
- **2023-05-03 17:06:51**: [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空
  - 變更檔案: 3 個
- **2023-04-27 18:24:24**: [組織設計師]Q00-20230427001 修正Log訊息在有人員身兼兩個部門在不同組織，實際上只設定一個組織時，點擊組織樹會報錯
  - 變更檔案: 1 個
- **2023-04-25 10:51:33**: [ESS]Q00-20230425001 修正若使用者設定Proxy，取得的clientIP會有實際IP以及Proxy設定之IP，造成開啟ESS表單失敗
  - 變更檔案: 1 個
- **2023-04-24 15:49:00**: [表單設計師]Q00-20230424001 新增腳本樣版的退回重辦事件增加參數註解
  - 變更檔案: 3 個
- **2023-04-20 14:37:27**: [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊 [補修正]
  - 變更檔案: 1 個
- **2023-04-18 16:37:44**: [流程引擎]Q00-20230418003 增加逾時關卡處理Log [補修正]
  - 變更檔案: 1 個
- **2023-04-18 15:39:32**: [流程引擎]Q00-20230418003 增加逾時關卡處理Log
  - 變更檔案: 1 個
- **2023-04-18 14:38:14**: [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗 [補修正]
  - 變更檔案: 3 個
- **2023-04-18 13:56:07**: [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗
  - 變更檔案: 4 個
- **2023-04-18 11:51:30**: [Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常 [補修正]
  - 變更檔案: 4 個
- **2023-04-17 17:48:48**: [Web]Q00-20230417003 修正下載原始檔功能在附件元件非"full control"的權限下也能在待辦清單出現
  - 變更檔案: 1 個
- **2023-04-17 14:44:46**: [TIPTOP]Q00-20230414002 修正流程設計師在TIPTOP流程關卡的"上傳附件時允許修改是否使用在線閱覽"取值為null，會導致無法呈現在線閱覽效果 [補修正]
  - 變更檔案: 5 個
- **2023-04-14 16:45:14**: [Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常
  - 變更檔案: 1 個
- **2023-04-14 15:44:26**: [Web]Q00-20230414005 調整下載附件不該顯示This URL not have permission to download the file訊息
  - 變更檔案: 1 個
- **2023-04-14 10:57:40**: [TIPTOP]Q00-20230414002 修正流程設計師在TIPTOP流程關卡的"上傳附件時允許修改是否使用在線閱覽"取值為null，會導致無法呈現在線閱覽效果
  - 變更檔案: 4 個
- **2023-04-14 09:26:59**: [表單設計師]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功 [補修正]
  - 變更檔案: 1 個
- **2023-04-12 14:58:38**: [資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊
  - 變更檔案: 2 個
- **2023-04-10 17:22:03**: [E10]Q00-20230314003 調整E10流程若為批次簽核，造成回寫審核日期會沒有值 [補修正]
  - 變更檔案: 2 個
- **2023-04-07 15:29:33**: [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊
  - 變更檔案: 2 個
- **2023-04-07 14:33:14**: [Web]Q00-20230407002 修正流程重要性在流程第二關後都未顯示
  - 變更檔案: 2 個
- **2023-04-06 16:13:14**: [流程引擎]Q00-20230406003 修正流程終點前若為閘道關卡，流程結案BamProInstData資料表的狀態還是進行中
  - 變更檔案: 1 個
- **2023-03-30 15:29:48**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 2 個
- **2023-03-30 10:57:55**: [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用 [補修正]
  - 變更檔案: 1 個
- **2023-03-30 10:47:19**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2023-03-30 10:46:26**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用 [補修正]
  - 變更檔案: 3 個
- **2023-03-29 17:57:34**: [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用 [補修正]
  - 變更檔案: 5 個
- **2023-03-29 13:36:43**: [內部]Oracle Update SQL補上 COMMIT指令
  - 變更檔案: 2 個
- **2023-03-28 18:05:14**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
  - 變更檔案: 5 個
- **2023-03-25 14:43:14**: [流程引擎]Q00-20230325001 修正流程退回重瓣到有自動簽核之關卡會觸發自動簽核
  - 變更檔案: 1 個
- **2023-03-21 11:15:38**: [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用
  - 變更檔案: 1 個
- **2023-03-21 10:18:20**: Revert "[T100]Q00-20230320001 調整T100簽名圖檔同步功能需打開NaNaWeb.propertise註解加上設定白名單IP設定才能使用"
  - 變更檔案: 1 個
- **2023-03-21 10:17:16**: Revert "[T100]Q00-20230320001 調整T100簽名圖檔同步功能需打開NaNaWeb.propertise註解加上設定白名單IP設定才能使用 [補修正]"
  - 變更檔案: 1 個
- **2023-03-20 18:02:35**: [T100]Q00-20230320001 調整T100簽名圖檔同步功能需打開NaNaWeb.propertise註解加上設定白名單IP設定才能使用 [補修正]
  - 變更檔案: 1 個
- **2023-03-20 17:51:44**: [T100]Q00-20230320001 調整T100簽名圖檔同步功能需打開NaNaWeb.propertise註解加上設定白名單IP設定才能使用
  - 變更檔案: 1 個
- **2023-03-20 11:49:50**: [派送關聯模組]V00-20230320001 修正派送關聯模組直接發後置流程會無法派送成功跟退回重瓣到服務關卡之前會派送失敗
  - 變更檔案: 8 個
- **2023-03-15 10:29:37**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 1 個
- **2023-03-15 10:28:47**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 3 個
- **2023-03-14 16:31:22**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 3 個
- **2023-03-14 14:38:09**: [E10]Q00-20230314003 調整E10流程若為批次簽核，造成回寫審核日期會沒有值
  - 變更檔案: 2 個
- **2023-03-10 14:26:29**: [派送關聯模組]S00-*********** 派送關聯模組增加可設定發起流程時是否一併拋轉附件功能
  - 變更檔案: 21 個
- **2023-03-09 09:38:31**: [Web]Q00-20230309001 修正複合元件的樹狀開窗選擇部門用部門名稱查詢會查到不再該部門的人員
  - 變更檔案: 1 個
- **2023-03-06 15:18:27**: [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊
  - 變更檔案: 2 個
- **2023-03-06 11:33:04**: [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功
  - 變更檔案: 3 個
- **2023-03-03 19:22:30**: [E10]Q00-20230303004 修正E10回寫取預設主機因為https不需輸入PORT造成回寫失敗
  - 變更檔案: 1 個
- **2023-03-03 11:25:44**: [Web]Q00-20230303002 修正人員開窗選取帶有特殊字"𤧟"的人員派送後表單會重複多長好幾個"𤧟"字
  - 變更檔案: 1 個
- **2023-02-24 17:08:46**: [雙因素模組]Q00-20230224002 調整個人資訊頁面加上未註冊雙因素模組的防呆[補修正]
  - 變更檔案: 1 個
- **2023-02-24 17:03:58**: [雙因素模組]Q00-20230224002 調整個人資訊頁面加上未註冊雙因素模組的防呆
  - 變更檔案: 1 個
- **2023-02-23 14:45:57**: [TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔
  - 變更檔案: 1 個
- **2023-02-21 10:37:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
  - 變更檔案: 1 個

### raven.917 (57 commits)

- **2023-05-25 19:35:08**: [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
  - 變更檔案: 1 個
- **2023-05-23 16:23:32**: [Web] Q00-20230519001 修正流程結案狀態與實際狀態不一致問題(補)
  - 變更檔案: 1 個
- **2023-05-23 11:18:17**: [Web] Q00-20230519001 修正流程結案狀態與實際狀態不一致問題(補)
  - 變更檔案: 2 個
- **2023-05-22 14:23:44**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
  - 變更檔案: 1 個
- **2023-05-22 11:18:07**: [Web] Q00-20230519001 修正流程結案狀態與實際狀態不一致問題
  - 變更檔案: 3 個
- **2023-05-17 12:09:53**: [Web] Q00-20230424004 修正Grid在縮小畫面時，Grid寬度異常顯示問題(補修正)
  - 變更檔案: 2 個
- **2023-05-17 10:32:19**: [資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true為系統郵件帳號為寄件者，false為操作轉派使用者作為寄件者
  - 變更檔案: 4 個
- **2023-05-16 18:17:00**: [資安] S00-20211220001 是否皆以系統設計師設定的帳號作為寄件者，以防止觸發郵件服務器驗證盜用的警告訊息。
  - 變更檔案: 1 個
- **2023-05-16 16:38:52**: [資安] Q00-20230504002 忽略登入前後SessionID更換，開啟此參數務必確保使用者使用Https連線，避免Session Hijacking攻擊(補修正)
  - 變更檔案: 1 個
- **2023-05-16 16:05:07**: [Web] S00-20211220001 是否皆以系統設計師設定的帳號作為寄件者，以防止觸發郵件服務器驗證盜用的警告訊息。
  - 變更檔案: 5 個
- **2023-05-16 10:37:51**: [Web] Q00-20230516001 修正JSP帶參數之字串為空值時，造成空白畫面異常
  - 變更檔案: 1 個
- **2023-05-15 18:06:25**: [Web] Q00-20230515001 修正DropDown元件，若以DB取值時，列印模式下顯示為第一個(補修正)
  - 變更檔案: 1 個
- **2023-05-15 17:14:12**: [Web] Q00-20230515001 修正DropDown元件，若以DB取值時，列印模式下顯示為第一個(補修正)
  - 變更檔案: 1 個
- **2023-05-15 16:15:33**: [Web] Q00-20230515001 修正DropDown元件，若以DB取值時，列印模式下顯示為第一個。
  - 變更檔案: 1 個
- **2023-05-15 14:14:51**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
  - 變更檔案: 1 個
- **2023-05-15 10:35:16**: [Web] A00-20230511001 調整多語系內容有換行導致頁面異常問題
  - 變更檔案: 1 個
- **2023-05-12 12:02:59**: [Web] Q00-20230502001 相容56版window.print()列印簽核歷程(補修正)
  - 變更檔案: 2 個
- **2023-05-12 10:00:07**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
  - 變更檔案: 1 個
- **2023-05-11 16:52:46**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
  - 變更檔案: 1 個
- **2023-05-10 16:51:55**: [Web] Q00-20230510004 修正關卡說明有換行，導致在流程圖檢視參與者型式關卡頁面工作列表無法顯示
  - 變更檔案: 2 個
- **2023-05-10 16:18:31**: [Web] Q00-20230510003 修正簽核意見有換行符號，檢視參與者關卡頁面沒有換行
  - 變更檔案: 1 個
- **2023-05-09 09:19:48**: [組織同步] S00-20221003002 新增HRM部門核決層級同步功能
  - 變更檔案: 1 個
- **2023-05-08 10:58:29**: [資安] Q00-20230504002 忽略登入前後SessionID更換，開啟此參數務必確保使用者使用Https連線，避免Session Hijacking攻擊(補修正)
  - 變更檔案: 4 個
- **2023-05-04 17:23:48**: [Web] Q00-20230504002 忽略登入前後SessionID更換，開啟此參數務必確保使用者使用Https連線
  - 變更檔案: 5 個
- **2023-05-03 17:26:28**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
  - 變更檔案: 1 個
- **2023-05-03 11:29:49**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
  - 變更檔案: 1 個
- **2023-05-02 16:41:11**: [Web] Q00-20230502001 相容56版window.print()列印簽核歷程
  - 變更檔案: 2 個
- **2023-04-26 17:11:45**: [Web] Q00-20230424004 修正Grid在縮小畫面時，Grid寬度異常顯示問題(補修正)
  - 變更檔案: 3 個
- **2023-04-25 17:48:06**: [Web] Q00-20230425003 修正上傳附件表單，表單異常，新增防呆
  - 變更檔案: 1 個
- **2023-04-25 10:55:57**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
  - 變更檔案: 1 個
- **2023-04-24 18:25:28**: [Web] Q00-20230424004 修正Grid在縮小畫面時，Grid寬度異常顯示問題
  - 變更檔案: 1 個
- **2023-04-21 16:59:09**: [Web] Q00-20230421001 修正頁簽在列印模式下沒有多語系
  - 變更檔案: 2 個
- **2023-04-20 15:39:33**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常
  - 變更檔案: 1 個
- **2023-04-19 11:04:33**: [Web] Q00-20230418004 修正$符號在通知信件樣板異常問題，replaceAll 寫法調整為 replace
  - 變更檔案: 1 個
- **2023-04-19 11:02:05**: Revert "[Web] Q00-20230418004 修正簽核意見中，$符號在通知信件樣板異常問題"
  - 變更檔案: 1 個
- **2023-04-18 16:51:01**: [Web] Q00-20230418004 修正簽核意見中，$符號在通知信件樣板異常問題
  - 變更檔案: 1 個
- **2023-04-18 11:14:02**: [Web] Q00-20230418001 修正 RadioButton & CheckBox 在列印表單時，被強制改成垂直式問題
  - 變更檔案: 1 個
- **2023-04-13 17:02:10**: [Web] Q00-20230413002 修正通知信追蹤連結，流程圖開啟空白問題
  - 變更檔案: 1 個
- **2023-04-11 14:40:23**: [Web] Q00-20230411001 新增資料選取註冊器支援GroupBy語法及Having語法
  - 變更檔案: 1 個
- **2023-04-10 14:12:32**: [組織同步] Q00-20230410001 調整T100組織同步Oracle與MSSQL情境結果一致
  - 變更檔案: 4 個
- **2023-04-06 16:40:05**: [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題(補修正)
  - 變更檔案: 1 個
- **2023-04-06 16:28:22**: [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題
  - 變更檔案: 3 個
- **2023-03-29 11:26:51**: [Service] Q00-20230329001 調整WildFly Service.bat 預設出貨不印bpm-stdout.log 及 bpm-stderr.log
  - 變更檔案: 1 個
- **2023-03-28 09:19:55**: [Web] Q00-20230328001 修正佈景主題更新成功缺少簡體多語系問題
  - 變更檔案: 1 個
- **2023-03-27 12:06:13**: [Web] Q00-20230327001 新增佈景主題，還原預設值的按鈕功能。
  - 變更檔案: 4 個
- **2023-03-23 15:28:14**: [Web] Q00-20230323002 調整部門主管首頁待辦處理量只會找得到在此部門內的使用者，監控流程圖及在途總處理量一併調整。
  - 變更檔案: 2 個
- **2023-03-15 11:57:02**: [流程引擎] Q00-20230315001 修正XPDL流程匯入，簽入流程，核決關卡不允許NULL問題，調整Table欄位允許NULL
  - 變更檔案: 4 個
- **2023-03-14 12:01:27**: [組織同步] Q00-20230313001 調整比對兼職部門的組織邏輯，加入判斷是否有取自定義根節點組織ID，變數調整為一致
  - 變更檔案: 1 個
- **2023-03-13 15:37:27**: [Web] Q00-20230313002 修正SelectElement，Style屬性異常問題
  - 變更檔案: 1 個
- **2023-03-08 12:01:14**: [Web] Q00-20230308002 修正設定小數點後幾位功能，提示文字應加在外顯元件上
  - 變更檔案: 4 個
- **2023-03-08 09:25:27**: [Web] Q00-20230308001 相容Grid,setAction點擊事件，支持點擊Row不帶回繫結欄位
  - 變更檔案: 1 個
- **2023-03-07 15:59:30**: [Web] Q00-20230307001 修正Admin操作員工工作轉派，撈取資料時新增防呆。
  - 變更檔案: 1 個
- **2023-02-24 15:54:51**: [Web] Q00-20230224001 調整多語系「自定義」
  - 變更檔案: 1 個
- **2023-02-23 10:18:01**: [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)
  - 變更檔案: 2 個
- **2023-02-21 16:05:05**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2023-02-21 15:53:39**: [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數
  - 變更檔案: 1 個
- **2023-02-21 15:53:39**: [組織同步] 修正HRM助手更新User資料時，沒有取系統變數
  - 變更檔案: 1 個

### pinchi_lin (13 commits)

- **2023-05-23 14:06:59**: [ESS]調整標準、流通、法尊表單加入標籤內容並顯示與排序欄位
  - 變更檔案: 54 個
- **2023-05-22 11:01:49**: [DT]Q00-20230522001 修正Web組織設計師連結未加入授權卡控判斷的問題
  - 變更檔案: 1 個
- **2023-05-10 16:07:03**: [內部]調整流程圖示下載方法供Web流程管理工具使用並相容舊版[補]
  - 變更檔案: 2 個
- **2023-05-08 18:44:49**: [DT]Q00-20230428002 修正新增或修改組織、部門與專案、群組時id沒卡控特殊符號的問題
  - 變更檔案: 2 個
- **2023-05-08 15:51:54**: [MPT]C01-20230426001 修正從首頁模組進入的待辦列表連點流程資料時會有呼叫兩次的問題
  - 變更檔案: 1 個
- **2023-05-05 17:24:14**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2023-05-05 17:01:29**: [內部]調整流程圖示下載方法供Web流程管理工具使用並相容舊版
  - 變更檔案: 15 個
- **2023-03-25 18:50:27**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 4 個
- **2023-03-21 10:54:26**: [DT]C01-20230314001 調整判斷是否需要同步cache方法中的log層級
  - 變更檔案: 1 個
- **2023-03-15 12:05:09**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2023-03-14 17:57:06**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 3 個
- **2023-03-06 10:25:43**: [DT]C01-20230224006 修正在組織管理工具刪除使用者名稱多語系會有髒資料的問題
  - 變更檔案: 1 個
- **2023-02-21 18:56:35**: [DT]C01-20230217001 修正在維護流程關卡中加入新增的核決層級儲存簽入後就無法再開啟或簽出的問題
  - 變更檔案: 1 個

### cherryliao (18 commits)

- **2023-05-18 14:15:21**: [Web]A00-20230512001 修正使用快搜功能查詢模擬使用者時當用戶姓名含有逗號會讓回傳資料變成null的問題[補]
  - 變更檔案: 1 個
- **2023-05-12 16:35:07**: [Web]A00-20230512001 修正使用快搜功能查詢模擬使用者時當用戶姓名含有逗號會讓回傳資料變成null的問題
  - 變更檔案: 1 個
- **2023-05-08 18:20:09**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 3 個
- **2023-05-08 12:10:33**: [Web]Q00-20230508001 修正絕對表單Grid需雙擊Row才會帶回繫結欄位的問題
  - 變更檔案: 1 個
- **2023-05-05 11:13:22**: [Web]Q00-20230504003 修正流程中附件檔名包含逗號時，檔案無法下載的問題
  - 變更檔案: 1 個
- **2023-05-03 11:47:03**: [Web]Q00-20230310001 調整倒數計時器功能的機制與提示訊息[補]
  - 變更檔案: 2 個
- **2023-04-26 14:11:32**: [Web]Q00-*********** 修正列表搜尋條件輸入的字串包含[ 時會搜尋不到資料的問題[補]
  - 變更檔案: 2 個
- **2023-04-25 18:21:02**: [Web]Q00-*********** 修正列表搜尋條件輸入的字串包含[ 時會搜尋不到資料的問題
  - 變更檔案: 9 個
- **2023-04-14 10:47:52**: [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
  - 變更檔案: 1 個
- **2023-04-14 10:28:30**: [Web]Q00-20230414001 修正當用戶逾時閒置過久會彈出null訊息框的問題
  - 變更檔案: 2 個
- **2023-04-13 10:27:20**: [Web]Q00-20230413001 修正在表單腳本有使用addAttachment的方法時會無法取得附件描述的問題
  - 變更檔案: 1 個
- **2023-04-07 11:20:01**: [Web]Q00-20230407001 修正表單序號欄位有簡體中文時會出現問號的問題
  - 變更檔案: 1 個
- **2023-03-30 10:35:16**: [Web]Q00-20230330001 修正追蹤清單與匯出Excel筆數不一致的問題
  - 變更檔案: 1 個
- **2023-03-24 17:08:21**: [Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕
  - 變更檔案: 1 個
- **2023-03-21 11:00:48**: [Web]Q00-20230321001 調整TextBox設定數字轉文字對應不會自動觸發更新的問題
  - 變更檔案: 2 個
- **2023-03-20 10:38:43**: [Web]Q00-20230310001 調整倒數計時器功能的機制與提示訊息
  - 變更檔案: 2 個
- **2023-03-06 17:25:05**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2023-02-21 16:30:41**: [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題
  - 變更檔案: 1 個

### waynechang (45 commits)

- **2023-05-17 14:18:38**: [內部] 更新5892patch
  - 變更檔案: 3 個
- **2023-05-17 12:00:12**: [流程引擎]Q00-20230512001 修正關卡有啟用自動簽核跳關，當核決關卡參考該關卡時，若該關卡同時有向前或向後加簽時，自動簽核的功能就會失效
  - 變更檔案: 1 個
- **2023-05-16 18:12:18**: 非同步簽核專案[補]
  - 變更檔案: 1 個
- **2023-05-16 16:36:27**: 非同步簽核專案[補]
  - 變更檔案: 1 個
- **2023-05-16 16:22:06**: Merge remote-tracking branch 'origin/AsyncDispatchWorkItem' into develop_v58
  - 變更檔案: 8 個
- **2023-05-16 15:43:54**: Merge branch 'AsyncDispatchWorkItem' of http://10.40.41.229/BPM_Group/BPM into AsyncDispatchWorkItem
- **2023-03-14 17:53:56**: 非同步簽核專案
  - 變更檔案: 21 個
- **2023-05-16 15:11:23**: [WEB]Q00-20230516002 修正系統參數file.security設定為false時(不開啟附件安全性設定)，直接透過附件URL下載檔案仍無法下載檔案
  - 變更檔案: 1 個
- **2023-05-16 14:57:40**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58
- **2023-05-16 14:56:58**: 修正非同步簽核透過Queue將關卡往下派送時，若往下派送拋出錯誤時，Queue會重複執行10次的異常
  - 變更檔案: 4 個
- **2023-05-15 15:42:53**: 因非同步簽核機制，故將「自動簽核異常處理」作業改為「流程派送異常處理」
  - 變更檔案: 3 個
- **2023-05-15 14:58:46**: wildfly增加非同步簽核的Queue
  - 變更檔案: 2 個
- **2023-05-15 14:54:27**: 增加非同步簽核系統參數開關
  - 變更檔案: 8 個
- **2023-05-15 14:20:11**: Merge remote-tracking branch 'origin/AsyncDispatchWorkItem' into develop_v58
- **2023-05-12 16:58:53**: [內部]Q00-20230512003 增加DatabaseAccessor.queryWithRESTful的log內容
  - 變更檔案: 1 個
- **2023-05-12 14:49:33**: [ISO]Q00-20230512002 修正ISO作廢單，當作廢文件有相關單位資料時，出貨流程預設不會走到通知相關單位關卡
  - 變更檔案: 1 個
- **2023-05-09 17:39:13**: [Web]Q00-20230509002 修正表單附件上傳後；若重新透過附件開窗上傳新的檔案時，原先上傳的附件無法下載的異常
  - 變更檔案: 1 個
- **2023-05-09 17:30:59**: [ISO]Q00-20230509001 修正ISO變更單於ModDocRequester關卡載入上一版附件後，點擊下載按鈕沒有反應
  - 變更檔案: 1 個
- **2023-05-09 14:19:01**: [內部]更新patch
  - 變更檔案: 1 個
- **2023-05-04 17:44:03**: [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關[補]
  - 變更檔案: 1 個
- **2023-04-28 14:04:13**: [流程引擎]Q00-20230428001 修正簡易流程圖預解析，當流程裡有多個核決層級時，後面核決關卡解析出來的人員會以正在執行中的核決關卡人員的身分往下做解析導致後面核決關卡預解析的人員錯誤
  - 變更檔案: 1 個
- **2023-04-26 14:40:42**: 非同步批次簽核
  - 變更檔案: 4 個
- **2023-04-24 17:26:57**: [ISO]Q00-20230424003修正ISO文件調閱申請流程，因流程ApplyEmp關卡線的條件判斷有誤，導致流程無法進入ApplyEmp關卡
  - 變更檔案: 1 個
- **2023-04-24 17:23:43**: [內部]Q00-20230330002 調整BPM開窗及模組開窗的頁面，增加每10分鐘向後端更新當前使用者的最後更新時間，避免BPM主畫面沒有時間而被系統判斷逾時登出[補]
  - 變更檔案: 9 個
- **2023-03-30 17:25:09**: [內部]Q00-20230330002 調整BPM開窗及模組開窗的頁面，增加每10分鐘向後端更新當前使用者的最後更新時間，避免BPM主畫面沒有時間而被系統判斷逾時登出[補]
  - 變更檔案: 1 個
- **2023-03-30 17:01:29**: [內部]Q00-20230330002 調整BPM開窗及模組開窗的頁面，增加每10分鐘向後端更新當前使用者的最後更新時間，避免BPM主畫面沒有時間而被系統判斷逾時登出
  - 變更檔案: 3 個
- **2023-03-29 13:53:35**: [Web]Q00-20230329002 修正產品授權註冊-模組類型，有購買「B2B文件管理模組」、「Digiwin PDF Converter」模組時，無法顯示對應的模組名稱
  - 變更檔案: 2 個
- **2022-08-12 15:03:20**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用[補修正]
  - 變更檔案: 1 個
- **2023-03-27 17:07:58**: [Web]Q00-20230327002 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱[補]
  - 變更檔案: 2 個
- **2023-03-27 13:54:51**: [Web]Q00-20230327002 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱
  - 變更檔案: 5 個
- **2023-03-24 14:34:36**: [流程引擎]Q00-20230324001 調整退回重辦通知信，當通知信的變數有設定<#allAssigneesOID>、<#allAssigneesID>、<#allAssigneesName>，且流程有設定「退回重辦時逐級通知」時，替換變數的內容由「各個關卡的工作處理者」改為「被退回關卡的工作處理者」
  - 變更檔案: 1 個
- **2023-03-23 15:06:58**: [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析[補]
  - 變更檔案: 3 個
- **2023-03-23 10:51:02**: [Web]Q00-20230323001 調整使用者/流程處理/取回重辦，進入頁面後，選擇時間自訂的時間範圍說明由「流程發起時間」改為「工作完成的時間」
  - 變更檔案: 1 個
- **2023-03-21 11:03:12**: [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析
  - 變更檔案: 2 個
- **2023-03-20 17:46:15**: [ISO]S00-20230313009 優化ISO新增單、變更單的權限屬性與文件機密等級的連動設計，例如機密等級為高機密，權限屬性只需顯示大於或等於高機密的權限，而非全部列出
  - 變更檔案: 1 個
- **2023-03-14 17:53:56**: 非同步簽核第一階段本地commit
  - 變更檔案: 8 個
- **2023-03-06 17:36:59**: [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關
  - 變更檔案: 1 個
- **2023-03-02 15:25:23**: [流程引擎]Q00-20230302002 修正流程關係人部門設定為參考表單欄位，且表單欄位為DialogInput部門開窗時，發起流程會報錯
  - 變更檔案: 1 個
- **2023-03-02 15:14:24**: [流程設計師]Q00-20230302001 調整流程設計師-表單定義-可重定義屬性-關係人-選擇部門參考表單欄位，將選擇值帶回畫面後，「表單」欄位應顯示為表單名稱，而非表單代號
  - 變更檔案: 1 個
- **2023-03-01 11:58:51**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
  - 變更檔案: 1 個
- **2023-03-01 11:17:29**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
  - 變更檔案: 1 個
- **2023-03-01 11:17:29**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log
  - 變更檔案: 1 個
- **2023-02-23 14:56:22**: [內部]Q00-20230223003 流程引擎增加派送相關log
  - 變更檔案: 1 個
- **2023-02-22 15:49:01**: [流程引擎]Q00-20230222002 修正核決關卡設定與流程關卡處理者相同時自動簽核，且流程有兩個以上的核決關卡時，只有核決關卡展開的第一關有自動簽核，後續關卡皆未自動簽核
  - 變更檔案: 1 個
- **2023-02-21 15:02:58**: [Web]Q00-20230221001 修正當關卡有設定「必須上傳新附件」，若透過追蹤流程「重新發起新流程」時，卡控是否有上傳附件的功能失效
  - 變更檔案: 1 個

### yamiyeh10 (18 commits)

- **2023-05-16 18:03:32**: [DT]A00-20230324001 修正系統權限管理員的可存取範圍不會根據編輯後的內容儲存問題[補]
  - 變更檔案: 1 個
- **2023-05-05 18:20:02**: [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題
  - 變更檔案: 1 個
- **2023-05-02 17:51:06**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2023-04-26 15:22:38**: [表單設計師]Q00-20230426003 修正開啟表單設計師畫面會有超過瀏覽器儲存空間限制的問題
  - 變更檔案: 1 個
- **2023-04-24 14:13:44**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2023-04-14 12:02:53**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2023-03-29 10:52:35**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2023-03-27 17:47:54**: [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題[補]
  - 變更檔案: 1 個
- **2023-03-27 11:02:18**: [DT]A00-20230324001 修正系統權限管理員的可存取範圍不會根據編輯後的內容儲存問題
  - 變更檔案: 1 個
- **2023-03-21 16:38:41**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2023-03-15 16:56:27**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2023-03-15 11:39:08**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2023-03-14 11:23:36**: [流程設計師]Q00-20230314002 調整流程設計師在編輯範本內的變數清單中Runtime流程發起部門名稱多了一個姓字問題
  - 變更檔案: 2 個
- **2023-03-14 10:37:39**: [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題
  - 變更檔案: 1 個
- **2023-03-07 17:06:18**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2023-02-23 10:38:34**: [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2023-02-22 16:58:23**: [DT]C01-20230221002 修正在系統權限管理員的可存取範圍權限無法選擇離職人員與失效部門問題
  - 變更檔案: 2 個
- **2023-02-21 15:44:41**: [WEB]Q00-20230221002 修正在行動版的清單頁面上若主旨有<br>時無法正確換行問題
  - 變更檔案: 1 個

### lorenchang (7 commits)

- **2023-05-15 09:59:24**: [內部]更改Open PLM流程範本路徑
  - 變更檔案: 1 個
- **2023-05-10 08:48:40**: [內部]合併重覆的更新授權說明 SQL，同時調整 Oracle 寫法避免字串過長導致更新 CLOB 欄位出現異常 ORA-01704: string literal too long
  - 變更檔案: 2 個
- **2023-05-08 17:17:35**: [OpenPLM]回收至標準產品並加入授權卡控
  - 變更檔案: 13 個
- **2023-04-14 13:36:54**: [其它]Q00-20230414003 產品一體化(*******)後資料來源設定可能會多出兩組預設不需要的設定：LocalConnection_for_TIPTOP 及LocalConnection_for_CRM，新安裝取消此設定，版更也會將內容未變更過的移除
  - 變更檔案: 9 個
- **2023-03-22 14:17:25**: [其它]C01-20230322002 修正5891安裝 BPM 服務時出現的異常：系統找不到指定的路徑
  - 變更檔案: 3 個
- **2023-03-07 10:29:12**: [內部]新增 pdfconverter.digiwin.tool.path 預設值
  - 變更檔案: 1 個
- **2023-03-01 14:35:18**: [內部]移除 wildfly\README.md，安裝工具改成選取 wildfly 原本存在之README.txt
  - 變更檔案: 1 個

### 郭哲榮 (12 commits)

- **2023-05-04 14:37:55**: [BPM APP]S00-20230109001 調整IMG的所有待辦與智能快簽應用依照系統變數取筆數與移除過濾三個月的限制[補]
  - 變更檔案: 3 個
- **2023-04-27 18:21:25**: [MPT]S00-20230313005 調整啟用訊息首頁模組就關閉原BPM首頁功能[補]
  - 變更檔案: 1 個
- **2023-04-27 11:01:51**: [MPT]S00-20230313005 調整啟用訊息首頁模組就關閉原BPM首頁功能
  - 變更檔案: 1 個
- **2023-04-26 19:33:36**: [BPM APP]S00-20230109001 調整IMG的所有待辦與智能快簽應用依照系統變數取筆數與移除過濾三個月的限制
  - 變更檔案: 5 個
- **2023-04-26 14:05:06**: [MPT]S00-*********** 公告信息維護與申請單的發布範圍調整為複選選項
  - 變更檔案: 2 個
- **2023-04-17 18:24:37**: [BPM APP]C01-20230413005 修正移動端聯絡人應用的搜尋功能在ios上會沒有效果的問題
  - 變更檔案: 1 個
- **2023-04-13 18:31:55**: [MPT]A00-20230410001 修正從郵件連結進入BPM時，右上角沒有顯示首頁按鈕的問題
  - 變更檔案: 1 個
- **2023-03-23 11:42:15**: [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息[補]
  - 變更檔案: 2 個
- **2023-03-15 17:38:05**: [MPT]S00-20221026001 公告維護信息與申請單的發布範圍增加組織與專案選項
  - 變更檔案: 3 個
- **2023-03-14 09:37:09**: [BPM APP]S00-20220916001 新增IMG公司聯絡人詳情顯示員工工號
  - 變更檔案: 1 個
- **2023-03-09 16:38:22**: [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息
  - 變更檔案: 1 個
- **2023-03-02 12:59:48**: [BPM APP]C01-20230110010 修正逾時流程通知處理者主管的推播無法檢視表單的問題
  - 變更檔案: 10 個

### 謝閔皓 (3 commits)

- **2023-03-03 11:56:46**: [Web]Q00-20230303001 調整 TextBox 元件進階設定中小數點後幾位的保存方式多語系，原本為實際值與四捨五入，將實際值調整為無條件捨去
  - 變更檔案: 3 個
- **2023-03-02 15:06:41**: [Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題
  - 變更檔案: 1 個
- **2023-02-22 17:10:25**: [Web]Q00-20230222003 修正 TextBox 設定浮點數、顯示千分位和小數點後幾位時，與 Grid 繫結會導致 FromScript 取得 Grid 資料以及 FormInstance 的 FieldValues 會有千分位的問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `9cde067fb650a8cb719131d3add88b3650b6f136`
- **作者**: 林致帆
- **日期**: 2023-05-26 10:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 2. [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
- **Commit ID**: `450ff066ca68952104cd30fc241bc68d7415a26c`
- **作者**: raven.917
- **日期**: 2023-05-25 19:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 3. [Web] Q00-20230519001 修正流程結案狀態與實際狀態不一致問題(補)
- **Commit ID**: `ab57327ba469ca9f928821ea4a4303c8353cd189`
- **作者**: raven.917
- **日期**: 2023-05-23 16:23:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 4. [ESS]調整標準、流通、法尊表單加入標籤內容並顯示與排序欄位
- **Commit ID**: `4bd2a65958ee96fb9d352eaf45841ba178633e00`
- **作者**: pinchi_lin
- **日期**: 2023-05-23 14:06:59
- **變更檔案數量**: 54
- **檔案變更詳細**:
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF01\346\216\222\347\217\255\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF03\350\243\234\345\210\267\345\215\241\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF04\345\212\240\347\217\255\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF05\345\212\240\347\217\255\350\250\210\345\212\203\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF06\345\212\240\347\217\255\350\252\277\344\274\221\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF07\350\253\213\345\201\207\347\224\263\350\253\213.form"`
  - ❌ **刪除**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF08\347\251\215\344\274\221\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF17\351\212\267\345\201\207\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF20\345\207\272\345\267\256\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF21\345\207\272\345\267\256\347\231\273\350\250\230.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF22\350\252\277\350\201\267\350\252\277\350\226\252\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF23\350\252\277\350\201\267\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF24\350\252\277\350\226\252\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF25\350\275\211\346\255\243\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF26\347\215\216\346\207\262\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF27\351\233\242\350\201\267\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF28\344\272\272\345\212\233\351\234\200\346\261\202\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF29\350\275\211\346\255\243\350\252\277\350\226\252\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF31\346\213\233\350\201\230\350\250\210\347\225\253.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF32\346\207\211\350\201\230\344\272\272\345\223\241\351\235\242\350\251\246.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF33\346\207\211\350\201\230\344\272\272\345\223\241\347\255\206\350\251\246.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF34\351\214\204\347\224\250\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF41\350\200\203\346\240\270\350\250\210\345\212\203.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF42\350\207\252\345\256\232\347\276\251\350\200\203\346\240\270\346\214\207\346\250\231.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF43\350\277\260\350\201\267\345\240\261\345\221\212.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF44\350\200\203\346\240\270\350\251\225\345\210\206.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF46\350\200\203\346\240\270\347\224\263\350\250\264.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF47\350\200\203\346\240\270\346\224\271\351\200\262.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF50\347\217\255\346\254\241\350\256\212\346\233\264\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF51\345\212\240\347\217\255\350\250\210\347\225\253\347\224\263\350\253\213(\345\244\232\346\231\202\346\256\265\345\244\232\344\272\272).form"`
  - ❌ **刪除**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF52C1\347\217\255\346\254\241\344\272\222\346\217\233.form"`
  - ❌ **刪除**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF52C2\347\217\255\346\254\241\350\256\212\346\233\264.form"`
  - ❌ **刪除**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF52\346\212\225\347\217\255\347\224\263\350\253\213.form"`
  - ❌ **刪除**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF53\346\216\222\347\217\255\347\242\272\350\252\215.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF60\350\254\233\345\270\253\350\263\207\346\240\274\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF61\350\252\262\347\250\213\351\226\213\347\231\274\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF62\345\237\271\350\250\223\351\240\220\347\256\227\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF63\345\237\271\350\250\223\351\234\200\346\261\202\346\216\241\351\233\206.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF64\345\237\271\350\250\223\350\250\210\347\225\253\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF66\345\237\271\350\250\223\350\251\225\344\274\260.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF67\345\237\271\350\250\223\345\240\261\345\220\215.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF68\345\217\226\346\266\210\345\237\271\350\250\223\345\240\261\345\220\215.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF69\345\223\241\345\267\245\347\225\260\345\213\225\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF72\345\223\241\345\267\245\345\240\261\345\210\260\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF74\350\263\207\346\272\220\347\224\263\351\240\230.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF75\350\263\207\346\272\220\346\255\270\351\202\204.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSF76\345\217\254\345\213\237\346\224\271\351\200\262\345\273\272\350\255\260.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/ESSQ27\345\270\270\347\224\250\344\270\213\350\274\211.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/\346\263\225\351\201\265/ESSF93\344\270\215\345\212\240\347\217\255\345\216\237\345\233\240\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF08\347\251\215\344\274\221\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF52C1\347\217\255\346\254\241\344\272\222\346\217\233.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF52C2\347\217\255\346\254\241\350\256\212\346\233\264.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF52\346\212\225\347\217\255\347\224\263\350\253\213.form"`
  - 📝 **修改**: `"Release/copyfiles/@appfrom-essplus/form-default/\346\265\201\351\200\232\347\211\210/ESSF53\346\216\222\347\217\255\347\242\272\350\252\215.form"`

### 5. [Web] Q00-20230519001 修正流程結案狀態與實際狀態不一致問題(補)
- **Commit ID**: `22386917eea82c5c82512cd9e4cfe864c7a6b5b1`
- **作者**: raven.917
- **日期**: 2023-05-23 11:18:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 6. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
- **Commit ID**: `ebf7814607cdc3ddd1cbcefd6b1147ad005e9c5b`
- **作者**: raven.917
- **日期**: 2023-05-22 14:23:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 7. [Web] Q00-20230519001 修正流程結案狀態與實際狀態不一致問題
- **Commit ID**: `54bca43a1fd23a79d51739546c63ce6d8cdd4607`
- **作者**: raven.917
- **日期**: 2023-05-22 11:18:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 8. [DT]Q00-20230522001 修正Web組織設計師連結未加入授權卡控判斷的問題
- **Commit ID**: `be4ec6ea46cd8f6e554972d737718de1561e596f`
- **作者**: pinchi_lin
- **日期**: 2023-05-22 11:01:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

### 9. [Web]A00-20230512001 修正使用快搜功能查詢模擬使用者時當用戶姓名含有逗號會讓回傳資料變成null的問題[補]
- **Commit ID**: `418629d77d9c800ecfbc2d06d085b985a7900944`
- **作者**: cherryliao
- **日期**: 2023-05-18 14:15:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`

### 10. [內部] 更新5892patch
- **Commit ID**: `7450ad38f170844928ef3d80e13559cad6e7effc`
- **作者**: waynechang
- **日期**: 2023-05-17 14:18:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 11. [Web] Q00-20230424004 修正Grid在縮小畫面時，Grid寬度異常顯示問題(補修正)
- **Commit ID**: `0c00e1e657c6395f68f21d411453881bde99e91e`
- **作者**: raven.917
- **日期**: 2023-05-17 12:09:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`

### 12. [流程引擎]Q00-20230512001 修正關卡有啟用自動簽核跳關，當核決關卡參考該關卡時，若該關卡同時有向前或向後加簽時，自動簽核的功能就會失效
- **Commit ID**: `02720414895f656808821c05fb858818efaaf2dd`
- **作者**: waynechang
- **日期**: 2023-05-17 12:00:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 13. [資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true為系統郵件帳號為寄件者，false為操作轉派使用者作為寄件者
- **Commit ID**: `06833ab0448acaf5d3b39ef7d2bde96928223f1d`
- **作者**: raven.917
- **日期**: 2023-05-17 10:32:19
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 14. [資安] S00-20211220001 是否皆以系統設計師設定的帳號作為寄件者，以防止觸發郵件服務器驗證盜用的警告訊息。
- **Commit ID**: `ff97e4d1a92406bc5ce624fb9d96276b13a909ea`
- **作者**: raven.917
- **日期**: 2023-05-16 18:17:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 15. 非同步簽核專案[補]
- **Commit ID**: `6ae4aa8a4ad7c2402e0898cfada718dac253b408`
- **作者**: waynechang
- **日期**: 2023-05-16 18:12:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`

### 16. [DT]A00-20230324001 修正系統權限管理員的可存取範圍不會根據編輯後的內容儲存問題[補]
- **Commit ID**: `554230541a5de39978f6d2231ffdfe3f94367197`
- **作者**: yamiyeh10
- **日期**: 2023-05-16 18:03:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/module/OJBAuthorityGroupDAO.java`

### 17. [資安] Q00-20230504002 忽略登入前後SessionID更換，開啟此參數務必確保使用者使用Https連線，避免Session Hijacking攻擊(補修正)
- **Commit ID**: `f0944483599ce8e16b399e302a3f8a0fe0fd1bb9`
- **作者**: raven.917
- **日期**: 2023-05-16 16:38:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 18. 非同步簽核專案[補]
- **Commit ID**: `049ba11f80590731a83c676ffb6dc6f71f5fe31b`
- **作者**: waynechang
- **日期**: 2023-05-16 16:36:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 19. Merge remote-tracking branch 'origin/AsyncDispatchWorkItem' into develop_v58
- **Commit ID**: `41dfca2cd7742bb2bf47f14651c55d2c16163f96`
- **作者**: waynechang
- **日期**: 2023-05-16 16:22:06
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📄 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📄 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📄 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📄 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`
  - 📄 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📄 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 20. [Web] S00-20211220001 是否皆以系統設計師設定的帳號作為寄件者，以防止觸發郵件服務器驗證盜用的警告訊息。
- **Commit ID**: `c325a6d2bcb1bd05dd75ece282931d63d429698b`
- **作者**: raven.917
- **日期**: 2023-05-16 16:05:07
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 21. Merge branch 'AsyncDispatchWorkItem' of http://10.40.41.229/BPM_Group/BPM into AsyncDispatchWorkItem
- **Commit ID**: `4ba960f0ceca705592e4fd2f4ce1a7a3e46589ff`
- **作者**: waynechang
- **日期**: 2023-05-16 15:43:54
- **變更檔案數量**: 0

### 22. 非同步簽核專案
- **Commit ID**: `41fc39ac28ae03e3af68772a1f6bffa30bbc748b`
- **作者**: waynechang
- **日期**: 2023-03-14 17:53:56
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkItem.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkItemStateType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AsyncDispatchWorkItemBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AsyncDispatchWorkItemUpdateMessageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `Release/wildfly/standalone/configuration/standalone-full_Oracle.xml`

### 23. [WEB]Q00-20230516002 修正系統參數file.security設定為false時(不開啟附件安全性設定)，直接透過附件URL下載檔案仍無法下載檔案
- **Commit ID**: `a57b2562813d1baf8ece0e0c371fff007d11832a`
- **作者**: waynechang
- **日期**: 2023-05-16 15:11:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 24. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM into develop_v58
- **Commit ID**: `60f62f88eb1ee1ef462a9cf9356843eaf5b8f193`
- **作者**: waynechang
- **日期**: 2023-05-16 14:57:40
- **變更檔案數量**: 0

### 25. 修正非同步簽核透過Queue將關卡往下派送時，若往下派送拋出錯誤時，Queue會重複執行10次的異常
- **Commit ID**: `af3c63f11312ac7b0958b41087fb75677c2405c8`
- **作者**: waynechang
- **日期**: 2023-05-16 14:56:58
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AsyncDispatchWorkItemBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AsyncDispatchWorkItemUpdateMessageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`

### 26. [Web] Q00-20230516001 修正JSP帶參數之字串為空值時，造成空白畫面異常
- **Commit ID**: `76ea668fae467be5441c0baf3e73bb95b53bb71b`
- **作者**: raven.917
- **日期**: 2023-05-16 10:37:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/taglib/WriteTag.java`

### 27. [Web] Q00-20230515001 修正DropDown元件，若以DB取值時，列印模式下顯示為第一個(補修正)
- **Commit ID**: `1bd2890cd470ea5aad8d1b4e59d1977217acd9d5`
- **作者**: raven.917
- **日期**: 2023-05-15 18:06:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/resources/html/SelectPrintTemplate.txt`

### 28. [Web] Q00-20230515001 修正DropDown元件，若以DB取值時，列印模式下顯示為第一個(補修正)
- **Commit ID**: `dbb9699f50f7060bdc2a27982d58416b74e5be0c`
- **作者**: raven.917
- **日期**: 2023-05-15 17:14:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/resources/html/SelectPrintTemplate.txt`

### 29. [Web] Q00-20230515001 修正DropDown元件，若以DB取值時，列印模式下顯示為第一個。
- **Commit ID**: `9dce211e257c288b648d630781fca86b9a65dc08`
- **作者**: raven.917
- **日期**: 2023-05-15 16:15:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/resources/html/SelectPrintTemplate.txt`

### 30. 因非同步簽核機制，故將「自動簽核異常處理」作業改為「流程派送異常處理」
- **Commit ID**: `6c50472cbddd30d128ee483ac82ce8b6f915605e`
- **作者**: waynechang
- **日期**: 2023-05-15 15:42:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 31. wildfly增加非同步簽核的Queue
- **Commit ID**: `e4a6a5636666ddb5deae7b01ef69254e0ac54d85`
- **作者**: waynechang
- **日期**: 2023-05-15 14:58:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `Release/wildfly/standalone/configuration/standalone-full_Oracle.xml`

### 32. 增加非同步簽核系統參數開關
- **Commit ID**: `41f29f88afc05464dc887cdbe1fbe64c86dacfbb`
- **作者**: waynechang
- **日期**: 2023-05-15 14:54:27
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 33. Merge remote-tracking branch 'origin/AsyncDispatchWorkItem' into develop_v58
- **Commit ID**: `764f6422f4e6d7e012c54090961445a74510d825`
- **作者**: waynechang
- **日期**: 2023-05-15 14:20:11
- **變更檔案數量**: 0

### 34. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
- **Commit ID**: `e5ea7f1cee9271f87d7c7db696ff28fb5d3812ab`
- **作者**: raven.917
- **日期**: 2023-05-15 14:14:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 35. [Web] A00-20230511001 調整多語系內容有換行導致頁面異常問題
- **Commit ID**: `f3d7cecaa273090b9451dd315aa4c6bcef6f7b3d`
- **作者**: raven.917
- **日期**: 2023-05-15 10:35:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 36. [內部]更改Open PLM流程範本路徑
- **Commit ID**: `7dd763e542eaa2d9b7d431c89446dc9d7ff62664`
- **作者**: lorenchang
- **日期**: 2023-05-15 09:59:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **重新命名**: `Release/copyfiles/plm/process-default/Open PLM Process Template.bpmn`

### 37. [流程串接套件]Q00-20230512004 調整流程代號在資料庫被更動，派送流程關聯設定作業的查詢作業也不該顯示異常
- **Commit ID**: `b3a55aea4ce0eb39df5a15a09996fffbe570e1b3`
- **作者**: 林致帆
- **日期**: 2023-05-12 17:52:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`

### 38. [內部]Q00-20230512003 增加DatabaseAccessor.queryWithRESTful的log內容
- **Commit ID**: `5ace9156e12fef6239a6b8753d1712c109965d3f`
- **作者**: waynechang
- **日期**: 2023-05-12 16:58:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 39. [Web]A00-20230512001 修正使用快搜功能查詢模擬使用者時當用戶姓名含有逗號會讓回傳資料變成null的問題
- **Commit ID**: `cc68aa4c942c749d5927f2751b0f8303215c76fe`
- **作者**: cherryliao
- **日期**: 2023-05-12 16:35:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`

### 40. [ISO]Q00-20230512002 修正ISO作廢單，當作廢文件有相關單位資料時，出貨流程預設不會走到通知相關單位關卡
- **Commit ID**: `c1a4a7f934bc92e2bf774eb1d9fd33d7dd6a0d5c`
- **作者**: waynechang
- **日期**: 2023-05-12 14:49:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@iso/default-form/ISOCancel001.form`

### 41. [Web] Q00-20230502001 相容56版window.print()列印簽核歷程(補修正)
- **Commit ID**: `2b25bfb93f6ed92cc83000c2a8441efe304aadb9`
- **作者**: raven.917
- **日期**: 2023-05-12 12:02:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 42. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
- **Commit ID**: `ec9d631f4c5c9f6765b31f23bc97a2f3fe706246`
- **作者**: raven.917
- **日期**: 2023-05-12 10:00:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 43. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
- **Commit ID**: `8c945229557e1595c8ccc34ada9541f873a576bf`
- **作者**: raven.917
- **日期**: 2023-05-11 16:52:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 44. [流程引擎]S00-20230331002 新增索引
- **Commit ID**: `e7c4c1d4a12be6fd3aad19ea7440f5446e6e3d74`
- **作者**: 林致帆
- **日期**: 2023-05-11 13:46:52
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`

### 45. [Web]Q00-20230511001 調整ErrorPage如果錯誤訊息小於30字，直接顯示在頁面上而不會只出現在"詳細資訊"中
- **Commit ID**: `6df23af30ad4ad573f7687befde4bcdcc995eaff`
- **作者**: 林致帆
- **日期**: 2023-05-11 11:02:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`

### 46. [Web] Q00-20230510004 修正關卡說明有換行，導致在流程圖檢視參與者型式關卡頁面工作列表無法顯示
- **Commit ID**: `691f2b045124ac2d55bdd611d3c57bede09a2cd9`
- **作者**: raven.917
- **日期**: 2023-05-10 16:51:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 47. [Web] Q00-20230510003 修正簽核意見有換行符號，檢視參與者關卡頁面沒有換行
- **Commit ID**: `25e351b4ad6e0538d6547b6c3a557016c7509fec`
- **作者**: raven.917
- **日期**: 2023-05-10 16:18:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 48. [內部]調整流程圖示下載方法供Web流程管理工具使用並相容舊版[補]
- **Commit ID**: `2227512ef39e78cc51351cf3a2b268dd033c9528`
- **作者**: pinchi_lin
- **日期**: 2023-05-10 16:07:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 49. [內部]合併重覆的更新授權說明 SQL，同時調整 Oracle 寫法避免字串過長導致更新 CLOB 欄位出現異常 ORA-01704: string literal too long
- **Commit ID**: `87af31d9ef329b99b263179bd036e9b16aa78349`
- **作者**: lorenchang
- **日期**: 2023-05-10 08:48:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 50. [Web]Q00-20230509002 修正表單附件上傳後；若重新透過附件開窗上傳新的檔案時，原先上傳的附件無法下載的異常
- **Commit ID**: `74e7d4faf845e1e56e6946fe3849a7c6f392c9d5`
- **作者**: waynechang
- **日期**: 2023-05-09 17:39:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

### 51. [ISO]Q00-20230509001 修正ISO變更單於ModDocRequester關卡載入上一版附件後，點擊下載按鈕沒有反應
- **Commit ID**: `bfa5ff22f76729c2aec88cdb1ad4f596baca465e`
- **作者**: waynechang
- **日期**: 2023-05-09 17:30:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 52. [內部]更新patch
- **Commit ID**: `31f3371aee28dfa47820d3d0b97b5a8e4eb3f512`
- **作者**: waynechang
- **日期**: 2023-05-09 14:19:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 53. [組織同步] S00-20221003002 新增HRM部門核決層級同步功能
- **Commit ID**: `7d674ea84d95e324106b9d771d80cb38ecea0c32`
- **作者**: raven.917
- **日期**: 2023-05-09 09:19:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 54. [DT]Q00-20230428002 修正新增或修改組織、部門與專案、群組時id沒卡控特殊符號的問題
- **Commit ID**: `3e7ea465bd02265f4ec19ced99e2bb8f2b7096de`
- **作者**: pinchi_lin
- **日期**: 2023-05-08 18:44:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 55. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `4f63b0e46e807596b1e26b3300983b7ecb11e7e1`
- **作者**: cherryliao
- **日期**: 2023-05-08 18:20:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormCategoryManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormCategoryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeLocal.java`

### 56. [OpenPLM]回收至標準產品並加入授權卡控
- **Commit ID**: `e6903766a02bb90a0954bec7b0739a36dd9269b5`
- **作者**: lorenchang
- **日期**: 2023-05-08 17:17:35
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/UpdateProcessStatus.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/UpdateProcessStatusBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/Service.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ServiceLocator.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ServiceSoap.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ServiceSoap12Stub.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/plm/ws/ServiceSoapStub.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java`
  - ➕ **新增**: `Release/copyfiles/plm/process-default/Open PLM Process Template.bpmn`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 57. [MPT]C01-20230426001 修正從首頁模組進入的待辦列表連點流程資料時會有呼叫兩次的問題
- **Commit ID**: `d3a0463a649524d773b245aff2e45b28ff6b1580`
- **作者**: pinchi_lin
- **日期**: 2023-05-08 15:51:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 58. [Web]Q00-20230508001 修正絕對表單Grid需雙擊Row才會帶回繫結欄位的問題
- **Commit ID**: `7788207e7a96b097f176126d63a8e1315db9a640`
- **作者**: cherryliao
- **日期**: 2023-05-08 12:10:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 59. [資安] Q00-20230504002 忽略登入前後SessionID更換，開啟此參數務必確保使用者使用Https連線，避免Session Hijacking攻擊(補修正)
- **Commit ID**: `2d9814f00d03e5c01e7d81f87a055ddc25fc579d`
- **作者**: raven.917
- **日期**: 2023-05-08 10:58:29
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 60. [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題
- **Commit ID**: `ab935ef86561dfafbb659dd92b97de5540552e48`
- **作者**: yamiyeh10
- **日期**: 2023-05-05 18:20:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 61. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `7f12c94ac88e643c47ca9e13817247296a2154bd`
- **作者**: pinchi_lin
- **日期**: 2023-05-05 17:24:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 62. [內部]調整流程圖示下載方法供Web流程管理工具使用並相容舊版
- **Commit ID**: `e090f9bb47b615cf36467de607d482650207af1f`
- **作者**: pinchi_lin
- **日期**: 2023-05-05 17:01:29
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessPackageManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/document/DocType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/MobileProcessPackageVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/definition/ProcessDefForInvoking.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MainMenuManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`

### 63. [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
- **Commit ID**: `65f8bd2220f938e02afbbb3da6b28e1af0e03f52`
- **作者**: 林致帆
- **日期**: 2023-05-05 15:42:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 64. [Web]Q00-20230504003 修正流程中附件檔名包含逗號時，檔案無法下載的問題
- **Commit ID**: `146c3d245d2e9bf5faa1c8a6152d8c00a60b2ff8`
- **作者**: cherryliao
- **日期**: 2023-05-05 11:13:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 65. [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關[補]
- **Commit ID**: `1f58a7062762eac4095a16ad7709573579147dd3`
- **作者**: waynechang
- **日期**: 2023-05-04 17:44:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 66. [Web] Q00-20230504002 忽略登入前後SessionID更換，開啟此參數務必確保使用者使用Https連線
- **Commit ID**: `6cbe3f2a8309adf07bad4c5f14547bd1392503f4`
- **作者**: raven.917
- **日期**: 2023-05-04 17:23:48
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 67. [BPM APP]S00-20230109001 調整IMG的所有待辦與智能快簽應用依照系統變數取筆數與移除過濾三個月的限制[補]
- **Commit ID**: `0036f3ef84179e79bc5785ad11e9656ee21e85d4`
- **作者**: 郭哲榮
- **日期**: 2023-05-04 14:37:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java`

### 68. [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
- **Commit ID**: `bfe9ea52d7f0e78acb81bc14fd4e07d20dec2fc1`
- **作者**: 林致帆
- **日期**: 2023-05-04 11:49:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 69. Revert "[PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空"
- **Commit ID**: `fd847f75d156952087769eafd730f2f159d75bfd`
- **作者**: 林致帆
- **日期**: 2023-05-04 10:40:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceController.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java`

### 70. Revert "[PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]"
- **Commit ID**: `476e4809e9ecfbdd436efbf7a47b3fd1d8de8322`
- **作者**: 林致帆
- **日期**: 2023-05-04 10:40:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormDefinition.java`

### 71. [E10]Q00-20230504001 修正E10表單同步單身同時有新增，刪除欄位，就無法新增成功
- **Commit ID**: `05d8908ce9523007adab455f43023ed23fc9cb8d`
- **作者**: 林致帆
- **日期**: 2023-05-04 10:37:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java`

### 72. [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空 [補修正]
- **Commit ID**: `ca668616ac4bf21ef397e712123745399e6a1d76`
- **作者**: 林致帆
- **日期**: 2023-05-03 17:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormDefinition.java`

### 73. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
- **Commit ID**: `66dddec4c84bbbcf429de01c03e97a73e7d8c461`
- **作者**: raven.917
- **日期**: 2023-05-03 17:26:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 74. [PLM]Q00-20230503001修正PLM拋單，多選開窗按鈕點擊帶出的資料結果為空
- **Commit ID**: `e8af2bc3a469f1a3f3301d49b1c99c6c88084aec`
- **作者**: 林致帆
- **日期**: 2023-05-03 17:06:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/ServiceController.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java`

### 75. [Web]Q00-20230310001 調整倒數計時器功能的機制與提示訊息[補]
- **Commit ID**: `2752e2a6c22c8f6228ad10669a409cdcf613902c`
- **作者**: cherryliao
- **日期**: 2023-05-03 11:47:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 76. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
- **Commit ID**: `192518ad970ecbdd501ce7a473746b07769b6735`
- **作者**: raven.917
- **日期**: 2023-05-03 11:29:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 77. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `03e60c9597b834cf77088c87139925b8c895c9dd`
- **作者**: yamiyeh10
- **日期**: 2023-05-02 17:51:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 78. [Web] Q00-20230502001 相容56版window.print()列印簽核歷程
- **Commit ID**: `def64f6c185112cdf389d3ef3c873057da15543c`
- **作者**: raven.917
- **日期**: 2023-05-02 16:41:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 79. [流程引擎]Q00-20230428001 修正簡易流程圖預解析，當流程裡有多個核決層級時，後面核決關卡解析出來的人員會以正在執行中的核決關卡人員的身分往下做解析導致後面核決關卡預解析的人員錯誤
- **Commit ID**: `17e2e44d6af21c4c7b27333afc1bed6283d569ff`
- **作者**: waynechang
- **日期**: 2023-04-28 14:04:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 80. [組織設計師]Q00-20230427001 修正Log訊息在有人員身兼兩個部門在不同組織，實際上只設定一個組織時，點擊組織樹會報錯
- **Commit ID**: `37761906e9f3a083d5af6a31fbd1f2ca70038993`
- **作者**: 林致帆
- **日期**: 2023-04-27 18:24:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 81. [MPT]S00-20230313005 調整啟用訊息首頁模組就關閉原BPM首頁功能[補]
- **Commit ID**: `9be6bb753ffdf52d55aed35f47cfe2701c968234`
- **作者**: 郭哲榮
- **日期**: 2023-04-27 18:21:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 82. [MPT]S00-20230313005 調整啟用訊息首頁模組就關閉原BPM首頁功能
- **Commit ID**: `98744353ce4568d4d9ca49e6816b8ac8533fb7f1`
- **作者**: 郭哲榮
- **日期**: 2023-04-27 11:01:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 83. [BPM APP]S00-20230109001 調整IMG的所有待辦與智能快簽應用依照系統變數取筆數與移除過濾三個月的限制
- **Commit ID**: `2effdfd031089e1445bb88fc0b3e7c8ed48ef95e`
- **作者**: 郭哲榮
- **日期**: 2023-04-26 19:33:36
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 84. [Web] Q00-20230424004 修正Grid在縮小畫面時，Grid寬度異常顯示問題(補修正)
- **Commit ID**: `7a9d135b7cae66c8f155045e1cd798bfee10a4a5`
- **作者**: raven.917
- **日期**: 2023-04-26 17:11:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 85. [表單設計師]Q00-20230426003 修正開啟表單設計師畫面會有超過瀏覽器儲存空間限制的問題
- **Commit ID**: `010e9362435e1ae480646740c776093bc812a15a`
- **作者**: yamiyeh10
- **日期**: 2023-04-26 15:22:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`

### 86. 非同步批次簽核
- **Commit ID**: `e216d4805abbeb280c359c07c699094f1ee77c60`
- **作者**: waynechang
- **日期**: 2023-04-26 14:40:42
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`

### 87. [Web]Q00-*********** 修正列表搜尋條件輸入的字串包含[ 時會搜尋不到資料的問題[補]
- **Commit ID**: `d29df549a92335291ebdb4fa678f44c4ab578fab`
- **作者**: cherryliao
- **日期**: 2023-04-26 14:11:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DraftListReader.java`

### 88. [MPT]S00-*********** 公告信息維護與申請單的發布範圍調整為複選選項
- **Commit ID**: `49a7f8d2443e3c62984d1964dfd6494fe65e4bf4`
- **作者**: 郭哲榮
- **日期**: 2023-04-26 14:05:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@mpt/default-form/MptAncApply.form`
  - 📝 **修改**: `"Release/copyfiles/@mpt/default-process/\345\205\254\345\221\212\347\224\263\350\257\267\345\215\225.bpmn"`

### 89. [Web]Q00-*********** 修正列表搜尋條件輸入的字串包含[ 時會搜尋不到資料的問題
- **Commit ID**: `8b0f99759a9a4d722d810ffb04394592087a8cb4`
- **作者**: cherryliao
- **日期**: 2023-04-25 18:21:02
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DraftListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/StringHelper.java`

### 90. [Web] Q00-20230425003 修正上傳附件表單，表單異常，新增防呆
- **Commit ID**: `d4c73cb80f8e1f6dfeae63844e046b6cef303708`
- **作者**: raven.917
- **日期**: 2023-04-25 17:48:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 91. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補修正)
- **Commit ID**: `7ef946d907da0dfc4a50f833d740cd54fb40f31f`
- **作者**: raven.917
- **日期**: 2023-04-25 10:55:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 92. [ESS]Q00-20230425001 修正若使用者設定Proxy，取得的clientIP會有實際IP以及Proxy設定之IP，造成開啟ESS表單失敗
- **Commit ID**: `47b997cffea3ba169f17d8fbec558d4c47b87e77`
- **作者**: 林致帆
- **日期**: 2023-04-25 10:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebUtil.java`

### 93. [Web] Q00-20230424004 修正Grid在縮小畫面時，Grid寬度異常顯示問題
- **Commit ID**: `c0968695e26fee2f3c4c0b8d05ae4f8d38d805a0`
- **作者**: raven.917
- **日期**: 2023-04-24 18:25:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 94. [ISO]Q00-20230424003修正ISO文件調閱申請流程，因流程ApplyEmp關卡線的條件判斷有誤，導致流程無法進入ApplyEmp關卡
- **Commit ID**: `3eeb77658680ce0de00f6572e74783f53da47956`
- **作者**: waynechang
- **日期**: 2023-04-24 17:26:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@iso/default-process/ISO\346\226\207\344\273\266\350\252\277\351\226\261\347\224\263\350\253\213.bpmn"`

### 95. [內部]Q00-20230330002 調整BPM開窗及模組開窗的頁面，增加每10分鐘向後端更新當前使用者的最後更新時間，避免BPM主畫面沒有時間而被系統判斷逾時登出[補]
- **Commit ID**: `284911fb173841df51d91aaf1083d670d10d3335`
- **作者**: waynechang
- **日期**: 2023-04-24 17:23:43
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkItem.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/AutomaticSignOffMaintanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AsyncDispatchWorkItemBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AsyncDispatchWorkItemUpdateMessageBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`

### 96. [表單設計師]Q00-20230424001 新增腳本樣版的退回重辦事件增加參數註解
- **Commit ID**: `28035fdfac25b1ce3f7f5979e44aa88b23490683`
- **作者**: 林致帆
- **日期**: 2023-04-24 15:49:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 97. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `ad7141e2c0ba0794f30765e05a092014bbc65df2`
- **作者**: yamiyeh10
- **日期**: 2023-04-24 14:13:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 98. [Web] Q00-20230421001 修正頁簽在列印模式下沒有多語系
- **Commit ID**: `f6be2654b5eda2f6028e2be532f92c4a7718b1eb`
- **作者**: raven.917
- **日期**: 2023-04-21 16:59:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SubTabElement.java`

### 99. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常
- **Commit ID**: `dbaae39c1668e61abfd4e093a6f23e3662e583b9`
- **作者**: raven.917
- **日期**: 2023-04-20 15:39:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 100. [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊 [補修正]
- **Commit ID**: `70398516b33dc5e5b91a1fa9810e21eb4c71fb69`
- **作者**: 林致帆
- **日期**: 2023-04-20 14:37:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 101. [Web] Q00-20230418004 修正$符號在通知信件樣板異常問題，replaceAll 寫法調整為 replace
- **Commit ID**: `87ca96ff3fd6a645da0b2057224ce1f0ec38d2aa`
- **作者**: raven.917
- **日期**: 2023-04-19 11:04:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 102. Revert "[Web] Q00-20230418004 修正簽核意見中，$符號在通知信件樣板異常問題"
- **Commit ID**: `999c7a24ea975162305c09a7610d008d647ce3d5`
- **作者**: raven.917
- **日期**: 2023-04-19 11:02:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 103. [Web] Q00-20230418004 修正簽核意見中，$符號在通知信件樣板異常問題
- **Commit ID**: `76eef76bf5ead7985afa9bfa1913546e9c7e4631`
- **作者**: raven.917
- **日期**: 2023-04-18 16:51:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 104. [流程引擎]Q00-20230418003 增加逾時關卡處理Log [補修正]
- **Commit ID**: `cb12778211f08a66cbb8209c21517da8edaf2258`
- **作者**: 林致帆
- **日期**: 2023-04-18 16:37:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 105. [流程引擎]Q00-20230418003 增加逾時關卡處理Log
- **Commit ID**: `17de94f69ff755d5a2731ae72e60704677c3509d`
- **作者**: 林致帆
- **日期**: 2023-04-18 15:39:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 106. [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗 [補修正]
- **Commit ID**: `11cc44f1d73508a4ebe59b9b826f3edff6d95c63`
- **作者**: 林致帆
- **日期**: 2023-04-18 14:38:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 107. [TPITOP]Q00-20230418002 修正TIPTOP拋單，流程為XPDL時，帶附件會造成拋單失敗
- **Commit ID**: `055a64b22d149a8312023ee25d7e3f0a66c38313`
- **作者**: 林致帆
- **日期**: 2023-04-18 13:56:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 108. [Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常 [補修正]
- **Commit ID**: `2a1a2299d23c47818dd7bf3495af885b6e21cddd`
- **作者**: 林致帆
- **日期**: 2023-04-18 11:51:30
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/PhraseManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/taglib/WriteTag.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`

### 109. [Web] Q00-20230418001 修正 RadioButton & CheckBox 在列印表單時，被強制改成垂直式問題
- **Commit ID**: `53631a7ac1a7ac118a58a8104637658606dc3789`
- **作者**: raven.917
- **日期**: 2023-04-18 11:14:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap-3.3.5-print.css`

### 110. [BPM APP]C01-20230413005 修正移動端聯絡人應用的搜尋功能在ios上會沒有效果的問題
- **Commit ID**: `2a96859a7269a44343875faf9f754ecbb750f886`
- **作者**: 郭哲榮
- **日期**: 2023-04-17 18:24:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListContact.js`

### 111. [Web]Q00-20230417003 修正下載原始檔功能在附件元件非"full control"的權限下也能在待辦清單出現
- **Commit ID**: `fb49ff3567df794432b727ebe5be8ae42143bf60`
- **作者**: 林致帆
- **日期**: 2023-04-17 17:48:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 112. [TIPTOP]Q00-20230414002 修正流程設計師在TIPTOP流程關卡的"上傳附件時允許修改是否使用在線閱覽"取值為null，會導致無法呈現在線閱覽效果 [補修正]
- **Commit ID**: `15f5a25f587fc1cfe805bc16bc1648e3aa0e67b9`
- **作者**: 林致帆
- **日期**: 2023-04-17 14:44:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 113. [Web]Q00-20230414006 修正新增片與內容帶有反斜線會造成片語頁面異常
- **Commit ID**: `db23d48e78def7a23c0f0c297922424abfaa2b5d`
- **作者**: 林致帆
- **日期**: 2023-04-14 16:45:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManagePhraseAction.java`

### 114. [Web]Q00-20230414005 調整下載附件不該顯示This URL not have permission to download the file訊息
- **Commit ID**: `ea364fba9b3c9ba0fe860e9558a4b26598000a9e`
- **作者**: 林致帆
- **日期**: 2023-04-14 15:44:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 115. [其它]Q00-20230414003 產品一體化(*******)後資料來源設定可能會多出兩組預設不需要的設定：LocalConnection_for_TIPTOP 及LocalConnection_for_CRM，新安裝取消此設定，版更也會將內容未變更過的移除
- **Commit ID**: `3e491c79f8f7a133c88273258fd53ebf5b726e59`
- **作者**: lorenchang
- **日期**: 2023-04-14 13:36:54
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db-up-to-*******/@crm/create/InitCrmModel_Oracle9i.sql`
  - 📝 **修改**: `Release/db-up-to-*******/@crm/create/InitCrmModel_SQLServer2005.sql`
  - 📝 **修改**: `Release/db-up-to-*******/@crm/update/crm5.5.0.1Update_Oracle9i.sql`
  - 📝 **修改**: `Release/db-up-to-*******/@crm/update/crm5.5.0.1Update_SQLServer2005.sql`
  - 📝 **修改**: `Release/db-up-to-*******/@tiptop/create/InitTiptopModel_ORACLE9i.sql`
  - 📝 **修改**: `Release/db-up-to-*******/@tiptop/create/InitTiptopModel_SQLServer2005.sql`
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 116. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `4a21621fc9e135967652b46645d449438f355710`
- **作者**: yamiyeh10
- **日期**: 2023-04-14 12:02:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 117. [TIPTOP]Q00-20230414002 修正流程設計師在TIPTOP流程關卡的"上傳附件時允許修改是否使用在線閱覽"取值為null，會導致無法呈現在線閱覽效果
- **Commit ID**: `eeac4779d4df3cb9ee642fd9a767b9939ef03048`
- **作者**: 林致帆
- **日期**: 2023-04-14 10:57:40
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 118. [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
- **Commit ID**: `770dde0ee9977c91de8ff260653b59f4ef74abbf`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 119. [Web]Q00-20230414001 修正當用戶逾時閒置過久會彈出null訊息框的問題
- **Commit ID**: `5b9692db224fe551781c55b57bc1ac10ae5b9f09`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:28:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 120. [表單設計師]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功 [補修正]
- **Commit ID**: `7d8d3e27982c9e36a70b9ed7e029f28e144c81bf`
- **作者**: 林致帆
- **日期**: 2023-04-14 09:26:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 121. [MPT]A00-20230410001 修正從郵件連結進入BPM時，右上角沒有顯示首頁按鈕的問題
- **Commit ID**: `65361495397898b94db9bf8c4b81dccf890de2a5`
- **作者**: 郭哲榮
- **日期**: 2023-04-13 18:31:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 122. [Web] Q00-20230413002 修正通知信追蹤連結，流程圖開啟空白問題
- **Commit ID**: `50e88d0cb1c88167a54318173f580eded5ba11b8`
- **作者**: raven.917
- **日期**: 2023-04-13 17:02:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`

### 123. [Web]Q00-20230413001 修正在表單腳本有使用addAttachment的方法時會無法取得附件描述的問題
- **Commit ID**: `137ed15a4071dde2ad4d10015d9b236fa154a59f`
- **作者**: cherryliao
- **日期**: 2023-04-13 10:27:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 124. [資安]Q00-20230412001 修正在表單formScript調用ajax_DatabaseAccessor.executeQuery方法被檢測到SQL注入攻擊
- **Commit ID**: `24106863893e5c46437606bcbb9aff1b0c4dc7bd`
- **作者**: 林致帆
- **日期**: 2023-04-12 14:58:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 125. [Web] Q00-20230411001 新增資料選取註冊器支援GroupBy語法及Having語法
- **Commit ID**: `1f4c9ad8c9243208fb2c1a184ea905d70b708474`
- **作者**: raven.917
- **日期**: 2023-04-11 14:40:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 126. [E10]Q00-20230314003 調整E10流程若為批次簽核，造成回寫審核日期會沒有值 [補修正]
- **Commit ID**: `5656aed0613c6733239628d7697472cc45824a15`
- **作者**: 林致帆
- **日期**: 2023-04-10 17:22:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `"Release/copyfiles/@e10/process-default/e10\346\265\201\347\250\213(\344\270\215\351\251\227\350\255\211\350\241\250\345\226\256)\346\265\201\347\250\213\347\257\204\346\234\254(E10\346\224\257\346\214\201\347\211\210\346\234\254\357\274\232E10_6003.V5\347\211\210\345\220\253\344\273\245\344\270\212)/e10\346\265\201\347\250\213(\344\270\215\351\251\227\350\255\211\350\241\250\345\226\256)\347\257\204\346\234\254.bpmn"`
  - 📝 **修改**: `"Release/copyfiles/@e10/process-default/e10\346\265\201\347\250\213\347\257\204\346\234\254.bpmn"`

### 127. [組織同步] Q00-20230410001 調整T100組織同步Oracle與MSSQL情境結果一致
- **Commit ID**: `9a857f3a0f8456ba9dc55780844a2ec50d48197c`
- **作者**: raven.917
- **日期**: 2023-04-10 14:12:32
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`

### 128. [Web]Q00-20230407003 Log增加使用者查看監控流程的花費時間資訊
- **Commit ID**: `10b81f1e76a5f7a6ef94e4d0763f9d946b9a1da8`
- **作者**: 林致帆
- **日期**: 2023-04-07 15:29:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 129. [Web]Q00-20230407002 修正流程重要性在流程第二關後都未顯示
- **Commit ID**: `39edb71bbfa9ffb86a550a6c08d9e3bc24c1172e`
- **作者**: 林致帆
- **日期**: 2023-04-07 14:33:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 130. [Web]Q00-20230407001 修正表單序號欄位有簡體中文時會出現問號的問題
- **Commit ID**: `ee22c4d99b1c683bf4a03dc1568afcde512cbe48`
- **作者**: cherryliao
- **日期**: 2023-04-07 11:20:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SNGenerator.java`

### 131. [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題(補修正)
- **Commit ID**: `e99b99417b392b83cc81329bd5b8d442700272e5`
- **作者**: raven.917
- **日期**: 2023-04-06 16:40:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 132. [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題
- **Commit ID**: `3d7866897f1824907c56ebf60cd288e97655a930`
- **作者**: raven.917
- **日期**: 2023-04-06 16:28:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 133. [流程引擎]Q00-20230406003 修正流程終點前若為閘道關卡，流程結案BamProInstData資料表的狀態還是進行中
- **Commit ID**: `fa5dd2115e80d4931fa9129567059c3c6b1831f9`
- **作者**: 林致帆
- **日期**: 2023-04-06 16:13:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 134. [內部]Q00-20230330002 調整BPM開窗及模組開窗的頁面，增加每10分鐘向後端更新當前使用者的最後更新時間，避免BPM主畫面沒有時間而被系統判斷逾時登出[補]
- **Commit ID**: `35ed7c823796088d6fd156562c34f91bc5ce804c`
- **作者**: waynechang
- **日期**: 2023-03-30 17:25:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js`

### 135. [內部]Q00-20230330002 調整BPM開窗及模組開窗的頁面，增加每10分鐘向後端更新當前使用者的最後更新時間，避免BPM主畫面沒有時間而被系統判斷逾時登出
- **Commit ID**: `9e334c87b6b190e7c3aacd76178ee778a27428e1`
- **作者**: waynechang
- **日期**: 2023-03-30 17:01:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html`

### 136. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `9f36e95516635032e8ae3abf46881aa035d8cbe7`
- **作者**: 林致帆
- **日期**: 2023-03-30 15:29:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`

### 137. [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用 [補修正]
- **Commit ID**: `b006a05b67d9b1ce42a49344a698815a0a4d6461`
- **作者**: 林致帆
- **日期**: 2023-03-30 10:57:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 138. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `a192278ec876b52371814ef4cdd75e20667b096d`
- **作者**: 林致帆
- **日期**: 2023-03-30 10:47:19
- **變更檔案數量**: 0

### 139. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用 [補修正]
- **Commit ID**: `4d8e81bf7f234e7e0417cea0a479a6cbf48ae46e`
- **作者**: 林致帆
- **日期**: 2023-03-30 10:46:26
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 140. [Web]Q00-20230330001 修正追蹤清單與匯出Excel筆數不一致的問題
- **Commit ID**: `3f30d2b3553bbb19731a2dbbd6239f2aae06375b`
- **作者**: cherryliao
- **日期**: 2023-03-30 10:35:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 141. [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用 [補修正]
- **Commit ID**: `1ccc4852486e0fa08d829a907853b0629a3b2f95`
- **作者**: 林致帆
- **日期**: 2023-03-29 17:57:34
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopUserImageSyncBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/IgnoreFilterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-ignoreFilter-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 142. [Web]Q00-20230329002 修正產品授權註冊-模組類型，有購買「B2B文件管理模組」、「Digiwin PDF Converter」模組時，無法顯示對應的模組名稱
- **Commit ID**: `966220d0b875342e53c9a5cad3abe2cec5190c8a`
- **作者**: waynechang
- **日期**: 2023-03-29 13:53:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 143. [內部]Oracle Update SQL補上 COMMIT指令
- **Commit ID**: `313acbb8122d7424b23177c039f69f08259b4201`
- **作者**: 林致帆
- **日期**: 2023-03-29 13:36:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 144. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用[補修正]
- **Commit ID**: `362f28c3e211b51cbd7c6b7d032ff5c6ec73a28c`
- **作者**: waynechang
- **日期**: 2022-08-12 15:03:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 145. [Service] Q00-20230329001 調整WildFly Service.bat 預設出貨不印bpm-stdout.log 及 bpm-stderr.log
- **Commit ID**: `3b985558d7f0103158083001124cb6a8ea233145`
- **作者**: raven.917
- **日期**: 2023-03-29 11:26:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/bin/service/service.bat`

### 146. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `af67786c0e2689d0aa45b5c5dd79508f258313fb`
- **作者**: yamiyeh10
- **日期**: 2023-03-29 10:52:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 147. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
- **Commit ID**: `fc3da348ed2b42d38b6f6b85c9efe376ea245848`
- **作者**: 林致帆
- **日期**: 2023-03-28 18:05:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 148. [Web] Q00-20230328001 修正佈景主題更新成功缺少簡體多語系問題
- **Commit ID**: `caba1e32580117a0f93971279af2dee6fc5348b9`
- **作者**: raven.917
- **日期**: 2023-03-28 09:19:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 149. [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題[補]
- **Commit ID**: `6101ecb0c5e20bdf42d452f704a8c045e447cf46`
- **作者**: yamiyeh10
- **日期**: 2023-03-27 17:47:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessPackageManager.java`

### 150. [Web]Q00-20230327002 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱[補]
- **Commit ID**: `8e00474f907ebceb36d22d50c3b7d2713222d86e`
- **作者**: waynechang
- **日期**: 2023-03-27 17:07:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java`

### 151. [Web]Q00-20230327002 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱
- **Commit ID**: `28b13841ea70a9a87bef493eb9eda9e682addec9`
- **作者**: waynechang
- **日期**: 2023-03-27 13:54:51
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/ProgramDefinitionDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`

### 152. [Web] Q00-20230327001 新增佈景主題，還原預設值的按鈕功能。
- **Commit ID**: `8b19005ee23a37845c0b02d35c5d4a5d664814f8`
- **作者**: raven.917
- **日期**: 2023-03-27 12:06:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`

### 153. [DT]A00-20230324001 修正系統權限管理員的可存取範圍不會根據編輯後的內容儲存問題
- **Commit ID**: `559e3e7879b67ed7c951c9297eae1f02500d51b1`
- **作者**: yamiyeh10
- **日期**: 2023-03-27 11:02:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`

### 154. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `33318a44b728465f310b40369c60b722c7c47aaa`
- **作者**: pinchi_lin
- **日期**: 2023-03-25 18:50:27
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 155. [流程引擎]Q00-20230325001 修正流程退回重瓣到有自動簽核之關卡會觸發自動簽核
- **Commit ID**: `4a7a2f840491d10e8ee97076c450325301097411`
- **作者**: 林致帆
- **日期**: 2023-03-25 14:43:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

### 156. [Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕
- **Commit ID**: `5f515fe2956ddb9210e09c6ad0ea0d7029ed06b3`
- **作者**: cherryliao
- **日期**: 2023-03-24 17:08:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 157. [流程引擎]Q00-20230324001 調整退回重辦通知信，當通知信的變數有設定<#allAssigneesOID>、<#allAssigneesID>、<#allAssigneesName>，且流程有設定「退回重辦時逐級通知」時，替換變數的內容由「各個關卡的工作處理者」改為「被退回關卡的工作處理者」
- **Commit ID**: `806f7eb980c77baad985a5a7f02cf32c15765768`
- **作者**: waynechang
- **日期**: 2023-03-24 14:34:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 158. [Web] Q00-20230323002 調整部門主管首頁待辦處理量只會找得到在此部門內的使用者，監控流程圖及在途總處理量一併調整。
- **Commit ID**: `b127a8f1f6f3ed2648b757f029672808055ccfb3`
- **作者**: raven.917
- **日期**: 2023-03-23 15:28:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 159. [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析[補]
- **Commit ID**: `ee246e5d28bca5c9636c1900435c13a3accce368`
- **作者**: waynechang
- **日期**: 2023-03-23 15:06:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherLocal.java`

### 160. [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息[補]
- **Commit ID**: `8528c43135cbc046bd831746fd513f1a34ad6106`
- **作者**: 郭哲榮
- **日期**: 2023-03-23 11:42:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 161. [Web]Q00-20230323001 調整使用者/流程處理/取回重辦，進入頁面後，選擇時間自訂的時間範圍說明由「流程發起時間」改為「工作完成的時間」
- **Commit ID**: `b8745eb1008bba2f72f9009364886ebd4e8a4a7e`
- **作者**: waynechang
- **日期**: 2023-03-23 10:51:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`

### 162. [其它]C01-20230322002 修正5891安裝 BPM 服務時出現的異常：系統找不到指定的路徑
- **Commit ID**: `74b7dbd9f7656f4a687ed973e7f93aaabda9672b`
- **作者**: lorenchang
- **日期**: 2023-03-22 14:17:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `Release/wildfly/bin/service/amd64/wildfly-service.exe`
  - ➕ **新增**: `Release/wildfly/bin/service/wildfly-mgr.exe`
  - ➕ **新增**: `Release/wildfly/bin/service/wildfly-service.exe`

### 163. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `eaf0a57bb25f5e761434f15dba6342bb8924d2af`
- **作者**: yamiyeh10
- **日期**: 2023-03-21 16:38:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 164. [T100]Q00-20230320001 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用
- **Commit ID**: `fbda48fc25b68ce524b649d0bc06386decd8314f`
- **作者**: 林致帆
- **日期**: 2023-03-21 11:15:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 165. [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析
- **Commit ID**: `496d43c6432be5d584c301dbff0bc5191dba5fc1`
- **作者**: waynechang
- **日期**: 2023-03-21 11:03:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 166. [Web]Q00-20230321001 調整TextBox設定數字轉文字對應不會自動觸發更新的問題
- **Commit ID**: `61364bfc803be1c31b80b9345509d8583816f100`
- **作者**: cherryliao
- **日期**: 2023-03-21 11:00:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`

### 167. [DT]C01-20230314001 調整判斷是否需要同步cache方法中的log層級
- **Commit ID**: `57e4f39b0a39c3f8c9ccf168f96e4d832241d5e9`
- **作者**: pinchi_lin
- **日期**: 2023-03-21 10:54:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 168. Revert "[T100]Q00-20230320001 調整T100簽名圖檔同步功能需打開NaNaWeb.propertise註解加上設定白名單IP設定才能使用"
- **Commit ID**: `bbf7c9d72287bdefceac8ce87e028bcb6888e128`
- **作者**: 林致帆
- **日期**: 2023-03-21 10:18:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 169. Revert "[T100]Q00-20230320001 調整T100簽名圖檔同步功能需打開NaNaWeb.propertise註解加上設定白名單IP設定才能使用 [補修正]"
- **Commit ID**: `890d7205bc2ad1e5606edd5cf8d857551309e320`
- **作者**: 林致帆
- **日期**: 2023-03-21 10:17:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/conf/NaNaWeb.properties`

### 170. [T100]Q00-20230320001 調整T100簽名圖檔同步功能需打開NaNaWeb.propertise註解加上設定白名單IP設定才能使用 [補修正]
- **Commit ID**: `76a20348cc9b841ab825f0402e91ff75862b30ae`
- **作者**: 林致帆
- **日期**: 2023-03-20 18:02:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/conf/NaNaWeb.properties`

### 171. [T100]Q00-20230320001 調整T100簽名圖檔同步功能需打開NaNaWeb.propertise註解加上設定白名單IP設定才能使用
- **Commit ID**: `09640ea48742fa6b2f9f63c347d07e2450b099ce`
- **作者**: 林致帆
- **日期**: 2023-03-20 17:51:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 172. [ISO]S00-20230313009 優化ISO新增單、變更單的權限屬性與文件機密等級的連動設計，例如機密等級為高機密，權限屬性只需顯示大於或等於高機密的權限，而非全部列出
- **Commit ID**: `23b353ddc61613722132cd7757d7a8ef0fb04ccc`
- **作者**: waynechang
- **日期**: 2023-03-20 17:46:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 173. [派送關聯模組]V00-20230320001 修正派送關聯模組直接發後置流程會無法派送成功跟退回重瓣到服務關卡之前會派送失敗
- **Commit ID**: `38987ac0ef0783ce8dad692868df11f5f9fb66c0`
- **作者**: 林致帆
- **日期**: 2023-03-20 11:49:50
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`

### 174. [Web]Q00-20230310001 調整倒數計時器功能的機制與提示訊息
- **Commit ID**: `acb1aace27c2f8af2f03e48b6298d7b078e7ccdf`
- **作者**: cherryliao
- **日期**: 2023-03-20 10:38:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 175. [MPT]S00-20221026001 公告維護信息與申請單的發布範圍增加組織與專案選項
- **Commit ID**: `1bb64ee0f2928d055465dbeb273321e703bb1d52`
- **作者**: 郭哲榮
- **日期**: 2023-03-15 17:38:05
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/copyfiles/@mpt/default-form/MptAncApply.form`
  - ➕ **新增**: `Release/db/update/MPT_5.8.9.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/MPT_5.8.9.2_DML_Oracle.sql`

### 176. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `538bf225c84466253f57e99f08b6d914a18cb8db`
- **作者**: yamiyeh10
- **日期**: 2023-03-15 16:56:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 177. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `1377357868671cebf43d74d5563432a7b84ebd0e`
- **作者**: pinchi_lin
- **日期**: 2023-03-15 12:05:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 178. [流程引擎] Q00-20230315001 修正XPDL流程匯入，簽入流程，核決關卡不允許NULL問題，調整Table欄位允許NULL
- **Commit ID**: `5561a2ac31946fbd8501e8c7beb40bfc56a05c54`
- **作者**: raven.917
- **日期**: 2023-03-15 11:57:02
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`

### 179. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `42d6fce90ef0f9a4be4c7a7b91002e496387482c`
- **作者**: yamiyeh10
- **日期**: 2023-03-15 11:39:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 180. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `888a93851b3e1c587d0f142aa469dd50dc4d548a`
- **作者**: 林致帆
- **日期**: 2023-03-15 10:29:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`

### 181. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `aebfe380addb70830412b8edb257da1b016b78e3`
- **作者**: 林致帆
- **日期**: 2023-03-15 10:28:47
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`

### 182. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `484efc686cd8edc8fbc5fb7dbe0cd055535f2dd7`
- **作者**: pinchi_lin
- **日期**: 2023-03-14 17:57:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 183. 非同步簽核第一階段本地commit
- **Commit ID**: `7699546ea240e401b769e4c7d671912760f3f676`
- **作者**: waynechang
- **日期**: 2023-03-14 17:53:56
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkItem.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/WorkItemStateType.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AsyncDispatchWorkItemBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 184. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `****************************************`
- **作者**: 林致帆
- **日期**: 2023-03-14 16:31:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`

### 185. [E10]Q00-20230314003 調整E10流程若為批次簽核，造成回寫審核日期會沒有值
- **Commit ID**: `55fd7427465b5fccd23cb8d6abbd6dbc90401aaf`
- **作者**: 林致帆
- **日期**: 2023-03-14 14:38:09
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10Manager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 186. [組織同步] Q00-20230313001 調整比對兼職部門的組織邏輯，加入判斷是否有取自定義根節點組織ID，變數調整為一致
- **Commit ID**: `2c4fd621cd62881fcc9ac41c8d3a37a06d04fd0e`
- **作者**: raven.917
- **日期**: 2023-03-14 12:01:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

### 187. [流程設計師]Q00-20230314002 調整流程設計師在編輯範本內的變數清單中Runtime流程發起部門名稱多了一個姓字問題
- **Commit ID**: `21745143291a368ad63a4a1b97c7ea7e0e037d00`
- **作者**: yamiyeh10
- **日期**: 2023-03-14 11:23:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_TW.properties`

### 188. [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題
- **Commit ID**: `531ba428ba66cb06fb6e9bb86ddd254475e698b0`
- **作者**: yamiyeh10
- **日期**: 2023-03-14 10:37:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessPackageManager.java`

### 189. [BPM APP]S00-20220916001 新增IMG公司聯絡人詳情顯示員工工號
- **Commit ID**: `37549e8356999eedf3d2798b5dedcc8d917b6362`
- **作者**: 郭哲榮
- **日期**: 2023-03-14 09:37:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 190. [Web] Q00-20230313002 修正SelectElement，Style屬性異常問題
- **Commit ID**: `59ae76442b20f2c1e5e802c12d1e2d186be19638`
- **作者**: raven.917
- **日期**: 2023-03-13 15:37:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 191. [派送關聯模組]S00-*********** 派送關聯模組增加可設定發起流程時是否一併拋轉附件功能
- **Commit ID**: `5f9e237b01b733659eddfc22036a5dae98ef3908`
- **作者**: 林致帆
- **日期**: 2023-03-10 14:26:29
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/LocalDocManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/deliveryProcess/DeliveryProcessConfigurationDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DDL_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DDL_Oracle.sql`

### 192. [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息
- **Commit ID**: `60650826f2ac2912e7d46eaad630c352311478a8`
- **作者**: 郭哲榮
- **日期**: 2023-03-09 16:38:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 193. [Web]Q00-20230309001 修正複合元件的樹狀開窗選擇部門用部門名稱查詢會查到不再該部門的人員
- **Commit ID**: `208fa729f4441d18ee06b82829886e7505372253`
- **作者**: 林致帆
- **日期**: 2023-03-09 09:38:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 194. [Web] Q00-20230308002 修正設定小數點後幾位功能，提示文字應加在外顯元件上
- **Commit ID**: `ca906c66898b3563654c288c8c97fa0b208c1416`
- **作者**: raven.917
- **日期**: 2023-03-08 12:01:14
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 195. [Web] Q00-20230308001 相容Grid,setAction點擊事件，支持點擊Row不帶回繫結欄位
- **Commit ID**: `71368de340d674d509c9a90409c6cea0adf4fe76`
- **作者**: raven.917
- **日期**: 2023-03-08 09:25:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 196. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `fc3a3fa9614968f70339caf28a74d1f8895f07ce`
- **作者**: yamiyeh10
- **日期**: 2023-03-07 17:06:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 197. [Web] Q00-20230307001 修正Admin操作員工工作轉派，撈取資料時新增防呆。
- **Commit ID**: `114d94b6d98b58f28ecfafde31e8bae675b27fac`
- **作者**: raven.917
- **日期**: 2023-03-07 15:59:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java`

### 198. [內部]新增 pdfconverter.digiwin.tool.path 預設值
- **Commit ID**: `dc70a9c19968fcc9ccfca16bcb5ab90a7fbb4e24`
- **作者**: lorenchang
- **日期**: 2023-03-07 10:29:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 199. [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關
- **Commit ID**: `6bc001d853e0c437e08bb7c265f982baa513ecc7`
- **作者**: waynechang
- **日期**: 2023-03-06 17:36:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 200. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `8932638dc627907c7dbb9ca49b8a98d9f286dca8`
- **作者**: cherryliao
- **日期**: 2023-03-06 17:25:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 201. [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊
- **Commit ID**: `36fbbb80469a46b75e713324235e37c017899bd1`
- **作者**: 林致帆
- **日期**: 2023-03-06 15:18:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 202. [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功
- **Commit ID**: `8f932b049a014ebdba876b86d63c0f239a00efaf`
- **作者**: 林致帆
- **日期**: 2023-03-06 11:33:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 203. [DT]C01-20230224006 修正在組織管理工具刪除使用者名稱多語系會有髒資料的問題
- **Commit ID**: `1f0543348e1234a788ea608b4dc609dd667c7dae`
- **作者**: pinchi_lin
- **日期**: 2023-03-06 10:25:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 204. [E10]Q00-20230303004 修正E10回寫取預設主機因為https不需輸入PORT造成回寫失敗
- **Commit ID**: `6d1fb31ae1b7d2b9ef4445a6354168cfb3ad66b0`
- **作者**: 林致帆
- **日期**: 2023-03-03 19:22:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 205. [Web]Q00-20230303001 調整 TextBox 元件進階設定中小數點後幾位的保存方式多語系，原本為實際值與四捨五入，將實際值調整為無條件捨去
- **Commit ID**: `ba96ef886b86415738c4b3183b2d4cffc539774f`
- **作者**: 謝閔皓
- **日期**: 2023-03-03 11:56:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 206. [Web]Q00-20230303002 修正人員開窗選取帶有特殊字"𤧟"的人員派送後表單會重複多長好幾個"𤧟"字
- **Commit ID**: `2f1c3adc818788dc97881f9bd91fc9778b73705c`
- **作者**: 林致帆
- **日期**: 2023-03-03 11:25:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 207. [流程引擎]Q00-20230302002 修正流程關係人部門設定為參考表單欄位，且表單欄位為DialogInput部門開窗時，發起流程會報錯
- **Commit ID**: `7a23a5d130fd6592ea3350da8f9fd682400decb9`
- **作者**: waynechang
- **日期**: 2023-03-02 15:25:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 208. [流程設計師]Q00-20230302001 調整流程設計師-表單定義-可重定義屬性-關係人-選擇部門參考表單欄位，將選擇值帶回畫面後，「表單」欄位應顯示為表單名稱，而非表單代號
- **Commit ID**: `cbd75eb8254a59bceb8cb1a1c071ee51b2535a2a`
- **作者**: waynechang
- **日期**: 2023-03-02 15:14:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/RelationManEditorController.java`

### 209. [Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題
- **Commit ID**: `71d32178b41a22c161da8923933fbd382d1b5dde`
- **作者**: 謝閔皓
- **日期**: 2023-03-02 15:06:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 210. [BPM APP]C01-20230110010 修正逾時流程通知處理者主管的推播無法檢視表單的問題
- **Commit ID**: `b6158e9eb81fef3955f96f7eb0df4866f26e4129`
- **作者**: 郭哲榮
- **日期**: 2023-03-02 12:59:48
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AdapterMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java`

### 211. [內部]移除 wildfly\README.md，安裝工具改成選取 wildfly 原本存在之README.txt
- **Commit ID**: `df027fc129d8053e65cb6ecf8780d55df5f1b1d1`
- **作者**: lorenchang
- **日期**: 2023-03-01 14:35:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `Release/wildfly/README.md`

### 212. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
- **Commit ID**: `9d0e4ced8d4bdd5c396068e020a85ec754b07bd3`
- **作者**: waynechang
- **日期**: 2023-03-01 11:58:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 213. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
- **Commit ID**: `df0c579fbd06afdf7180b2d4cd19deffe583d0f5`
- **作者**: waynechang
- **日期**: 2023-03-01 11:17:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 214. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log
- **Commit ID**: `0a92af543397c7058b28bfce5b1af9cc513bddcb`
- **作者**: waynechang
- **日期**: 2023-03-01 11:17:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 215. [雙因素模組]Q00-20230224002 調整個人資訊頁面加上未註冊雙因素模組的防呆[補修正]
- **Commit ID**: `951d5983c5c79a9f9fa5afccc942abbeb113a5b1`
- **作者**: 林致帆
- **日期**: 2023-02-24 17:08:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/license/ModuleKey.java`

### 216. [雙因素模組]Q00-20230224002 調整個人資訊頁面加上未註冊雙因素模組的防呆
- **Commit ID**: `a5a6ce1e4f9f45495dd658bd7b44aec123638d77`
- **作者**: 林致帆
- **日期**: 2023-02-24 17:03:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 217. [Web] Q00-20230224001 調整多語系「自定義」
- **Commit ID**: `593793c2c8baee09de17e4d2d7f0efbd8becd239`
- **作者**: raven.917
- **日期**: 2023-02-24 15:54:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 218. [內部]Q00-20230223003 流程引擎增加派送相關log
- **Commit ID**: `bce5d1f1b02cf0065f0cd54712ca3f1927ded78b`
- **作者**: waynechang
- **日期**: 2023-02-23 14:56:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 219. [TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔
- **Commit ID**: `d7522a38de7e35e2396a7971aae08ee1042070ba`
- **作者**: 林致帆
- **日期**: 2023-02-23 14:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 220. [內部]新增提供給流程設計師工具Web化使用的SessionBean[補]
- **Commit ID**: `2cb8c3422b5f534d6615dc350afe0ad506a2d6a9`
- **作者**: yamiyeh10
- **日期**: 2023-02-23 10:38:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 221. [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)
- **Commit ID**: `a2e25943eddee648e7a0b23b00228a47a777185f`
- **作者**: raven.917
- **日期**: 2023-02-23 10:18:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 222. [Web]Q00-20230222003 修正 TextBox 設定浮點數、顯示千分位和小數點後幾位時，與 Grid 繫結會導致 FromScript 取得 Grid 資料以及 FormInstance 的 FieldValues 會有千分位的問題
- **Commit ID**: `c7f64005c5d589e60ead9422dda9457dfec83f35`
- **作者**: 謝閔皓
- **日期**: 2023-02-22 17:10:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 223. [DT]C01-20230221002 修正在系統權限管理員的可存取範圍權限無法選擇離職人員與失效部門問題
- **Commit ID**: `41d43d22d53718a8866babd81e1be2c904d1df1e`
- **作者**: yamiyeh10
- **日期**: 2023-02-22 16:58:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 224. [流程引擎]Q00-20230222002 修正核決關卡設定與流程關卡處理者相同時自動簽核，且流程有兩個以上的核決關卡時，只有核決關卡展開的第一關有自動簽核，後續關卡皆未自動簽核
- **Commit ID**: `a4ab731e8977627ebb87f9443b320e0c72127711`
- **作者**: waynechang
- **日期**: 2023-02-22 15:49:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 225. [DT]C01-20230217001 修正在維護流程關卡中加入新增的核決層級儲存簽入後就無法再開啟或簽出的問題
- **Commit ID**: `675bec48a19cd483f6378419d8f8ae03df975b06`
- **作者**: pinchi_lin
- **日期**: 2023-02-21 18:56:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 226. [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題
- **Commit ID**: `3ee69c7252ffe0c721878de2697b23d5a49143cc`
- **作者**: cherryliao
- **日期**: 2023-02-21 16:30:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 227. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `98082cb2f38ba825834e360f2d4a4132b0eb8106`
- **作者**: raven.917
- **日期**: 2023-02-21 16:05:05
- **變更檔案數量**: 0

### 228. [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數
- **Commit ID**: `22b7a58b77eb8e73e3b2be48d2caf624d34dff7a`
- **作者**: raven.917
- **日期**: 2023-02-21 15:53:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 229. [組織同步] 修正HRM助手更新User資料時，沒有取系統變數
- **Commit ID**: `6a8150912ffd54548969e199541fd6d148d531aa`
- **作者**: raven.917
- **日期**: 2023-02-21 15:53:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 230. [WEB]Q00-20230221002 修正在行動版的清單頁面上若主旨有<br>時無法正確換行問題
- **Commit ID**: `65c2d5ca44d98040e5147e0b6e70b0159d9ca4c7`
- **作者**: yamiyeh10
- **日期**: 2023-02-21 15:44:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 231. [Web]Q00-20230221001 修正當關卡有設定「必須上傳新附件」，若透過追蹤流程「重新發起新流程」時，卡控是否有上傳附件的功能失效
- **Commit ID**: `0c58ba25789378cf474d9d4f8f807fc5a16615fd`
- **作者**: waynechang
- **日期**: 2023-02-21 15:02:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 232. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
- **Commit ID**: `e4abfe45ad93005896c8b0af5a71c320e87d3299`
- **作者**: 林致帆
- **日期**: 2023-02-21 10:37:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

