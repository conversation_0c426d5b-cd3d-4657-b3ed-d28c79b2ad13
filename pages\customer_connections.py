import streamlit as st
import json
import os
import uuid
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import sys
import pytz

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設定Streamlit頁面配置
st.set_page_config(
    page_title="客戶連線管理系統",
    page_icon="🏢",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 隱藏默認英文導航並添加中文側邊欄
st.markdown("""
<style>
    [data-testid="stSidebarNav"] { display: none !important; }
    .css-1544g2n, .css-17lntkn, .css-1y4p8pa, .css-1d391kg { display: none !important; }
    
    .sidebar-nav-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px; padding: 1rem; margin-bottom: 1rem;
        color: white; text-align: center; font-weight: bold;
    }
    
    .current-tool {
        background: #e3f2fd; border-radius: 8px; padding: 0.8rem; margin: 0.5rem 0;
        border-left: 4px solid #667eea; font-weight: bold; color: #1976d2;
    }
    
    .company-card {
        background: white; border-radius: 12px; padding: 1.5rem; margin: 1rem 0;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1); border: 1px solid #e0e0e0;
        transition: transform 0.2s ease, box-shadow 0.2s ease; cursor: pointer;
    }
    
    .company-card:hover {
        transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .company-id {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white; padding: 0.3rem 0.8rem; border-radius: 20px;
        font-size: 0.85rem; font-weight: bold;
    }
    
    .company-name { font-size: 1.4rem; font-weight: bold; color: #2c3e50; margin: 0.5rem 0; }
    .connection-count { color: #666; font-size: 0.9rem; }
    
    .connection-card {
        background: #f8f9fa; border-radius: 8px; padding: 1rem; margin: 0.5rem 0;
        border-left: 4px solid #667eea;
    }
    
    .product-type {
        background: #667eea; color: white; padding: 0.2rem 0.6rem;
        border-radius: 15px; font-size: 0.8rem; font-weight: bold;
    }
    
    .environment-badge {
        padding: 0.2rem 0.6rem; border-radius: 15px; font-size: 0.8rem; font-weight: bold;
    }
    
    .env-prod { background: #e8f5e8; color: #2e7d32; }
    .env-test { background: #fff3e0; color: #f57c00; }
    
    .raw-content {
        background: #f5f5f5; border-radius: 8px; padding: 1rem;
        font-family: 'Courier New', monospace; font-size: 0.9rem; line-height: 1.4;
        color: #333; white-space: pre-wrap; max-height: 400px; overflow-y: auto;
        border: 1px solid #ddd;
    }
    
    .search-container {
        background: white; border-radius: 10px; padding: 1rem; margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
</style>
""", unsafe_allow_html=True)

# 側邊欄導航
with st.sidebar:
    st.markdown('<div class="sidebar-nav-header">🧭 導航選單</div>', unsafe_allow_html=True)
    st.markdown('<div class="current-tool">🏢 客戶連線管理系統</div>', unsafe_allow_html=True)
    
    st.markdown("### 🛠️ 其他工具")
    if st.button("🏠 回到首頁", use_container_width=True):
        st.switch_page("streamlit_home.py")
    if st.button("📊 產品Release記錄查詢", use_container_width=True):
        st.switch_page("pages/release_query.py")
    if st.button("🔍 檔案索引路徑查詢", use_container_width=True):
        st.switch_page("pages/file_search.py")

# 資料路徑設定
DATA_INPUT_DIR = project_root / "data_output" / "bpm_customer_tmp"
DATA_OUTPUT_DIR = project_root / "data_output" / "bpm_customer"
DATA_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# 產品類型對應的欄位定義
PRODUCT_FIELDS = {
    "BPM AP": {
        "required": ["environment", "serverIp", "username", "password"],
        "optional": ["bpmAdmin", "bpmPassword", "bpmVersion", "serverUrl", "description"]
    },
    "BPM DB": {
        "required": ["environment", "dbType", "host", "port", "username", "password"],
        "optional": ["database", "serviceName", "sid", "connectionString", "description"]
    },
    "HRM": {
        "required": ["environment", "serverAddress"],
        "optional": ["description"]
    },
    "Tiptop": {
        "required": ["environment", "serverIp", "identifier", "soapUrl", "userCode"],
        "optional": ["description"]
    },
    "T100": {
        "required": ["environment", "host", "companyCode", "port", "environmentCode"],
        "optional": ["apiUrl", "description"]
    },
    "Workflow": {
        "required": ["serverIp", "identifier", "engineUrl", "adminUser"],
        "optional": ["description"]
    },
    "IMG": {
        "required": ["enterpriseNavigationBackend", "enterpriseNavigationExternalIp", 
                    "enterpriseNavigationExternalPort", "enterpriseNavigationUsername",
                    "enterpriseNavigationPassword", "ccManagementCenter", 
                    "ccManagementCenterUsername", "iamServiceUrl", "iamVerificationUrl"],
        "optional": ["description"]
    },
    "Cosmos": {
        "required": ["serverIp", "identifier", "soapUrl", "userCode"],
        "optional": ["description"]
    }
}

def format_taiwan_datetime(iso_string: str) -> str:
    """將 ISO 格式的日期時間轉換為台灣時區的標準顯示格式"""
    if not iso_string:
        return ""

    try:
        # 解析 ISO 格式的日期時間
        dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))

        # 如果沒有時區資訊，假設為 UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=pytz.UTC)

        # 轉換為台灣時區
        taiwan_tz = pytz.timezone('Asia/Taipei')
        taiwan_dt = dt.astimezone(taiwan_tz)

        # 格式化為台灣標準格式：YYYY-MM-DD HH:MM:SS
        return taiwan_dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        # 如果轉換失敗，返回原始字符串
        return iso_string

def get_taiwan_now() -> str:
    """獲取當前台灣時區的 ISO 格式時間"""
    taiwan_tz = pytz.timezone('Asia/Taipei')
    return datetime.now(taiwan_tz).isoformat()

def clear_all_states():
    """清除所有相關的 session state"""
    keys_to_clear = [
        'show_add_company', 'show_add_connection', 'show_connection_detail',
        'show_edit_connection', 'show_delete_confirm', 'selected_connection',
        'selected_connection_idx', 'delete_connection_idx', 'delete_connection_data'
    ]
    for key in keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]

    # 清除所有以特定前綴開頭的 session state
    for key in list(st.session_state.keys()):
        if key.startswith(('add_', 'edit_', 'confirm_delete_')):
            del st.session_state[key]

def reset_to_home():
    """重置到首頁狀態"""
    clear_all_states()
    st.session_state.current_view = 'company_list'
    st.session_state.selected_company = None
    if 'page_initialized' in st.session_state:
        del st.session_state['page_initialized']

# 初始化 session state
if 'current_view' not in st.session_state:
    st.session_state.current_view = 'company_list'
if 'selected_company' not in st.session_state:
    st.session_state.selected_company = None
if 'show_add_company' not in st.session_state:
    st.session_state.show_add_company = False

# 檢查是否需要重置頁面狀態
if st.session_state.get('reset_to_home', False):
    # 從首頁重新進入，重置所有狀態
    reset_to_home()
    st.session_state.reset_to_home = False
elif 'page_initialized' not in st.session_state:
    # 第一次進入頁面，初始化狀態
    reset_to_home()
    st.session_state.page_initialized = True

# 確保當前視圖狀態正確
if st.session_state.current_view not in ['company_list', 'company_detail', 'add_company', 'add_connection']:
    st.session_state.current_view = 'company_list'

def load_customer_data() -> List[Dict]:
    """載入客戶原始資料"""
    customers = []
    if not DATA_INPUT_DIR.exists():
        return customers
    
    for json_file in DATA_INPUT_DIR.glob("*.json"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                customers.append(data)
        except Exception as e:
            st.error(f"載入檔案 {json_file.name} 時發生錯誤: {str(e)}")
    
    return customers

def load_processed_companies() -> Dict[str, Dict]:
    """載入已處理的公司資料"""
    companies = {}
    if not DATA_OUTPUT_DIR.exists():
        return companies
    
    for json_file in DATA_OUTPUT_DIR.glob("*.json"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                companies[data.get('companyId', '')] = data
        except Exception as e:
            st.error(f"載入已處理檔案 {json_file.name} 時發生錯誤: {str(e)}")
    
    return companies

def save_company_data(company_data: Dict) -> bool:
    """儲存公司資料"""
    try:
        company_id = company_data.get('companyId', '')
        company_name = company_data.get('companyName', '')
        filename = f"{company_id}_{company_name}.json"
        
        # 更新時間戳
        now = get_taiwan_now()
        if 'createdAt' not in company_data:
            company_data['createdAt'] = now
        company_data['updatedAt'] = now
        
        filepath = DATA_OUTPUT_DIR / filename
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(company_data, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        st.error(f"儲存資料時發生錯誤: {str(e)}")
        return False

def get_field_label(field: str) -> str:
    """取得欄位的中文標籤"""
    labels = {
        "productType": "產品類別", "environment": "環境類型", "serverIp": "伺服器IP", "username": "使用者帳號", "password": "密碼",
        "bpmVersion": "BPM版本", "bpmAdmin": "BPM管理者帳號", "bpmPassword": "BPM管理者密碼",
        "serverUrl": "伺服器URL", "dbType": "資料庫類型", "host": "主機位址", "port": "連接埠",
        "database": "資料庫名稱", "serviceName": "服務名稱", "sid": "系統識別碼",
        "connectionString": "連線字串", "serverAddress": "伺服器位址", "identifier": "識別碼",
        "soapUrl": "SOAP網址", "userCode": "使用者代號", "companyCode": "企業代號",
        "apiUrl": "API URL", "environmentCode": "環境代號", "engineUrl": "引擎URL",
        "adminUser": "管理者帳號", "enterpriseNavigationBackend": "企業行動導航後台",
        "enterpriseNavigationExternalIp": "對外網路IP", "enterpriseNavigationExternalPort": "對外網路Port",
        "enterpriseNavigationUsername": "導航後台帳號", "enterpriseNavigationPassword": "導航後台密碼",
        "ccManagementCenter": "互聯應用管理中心", "ccManagementCenterUsername": "管理中心帳號",
        "iamServiceUrl": "IAM服務位址", "iamVerificationUrl": "IAM驗證網址", "description": "描述說明"
    }
    return labels.get(field, field)

# 主標題
st.markdown("# 🏢 客戶連線管理系統")
st.markdown("---")

def render_company_list():
    """渲染公司清單頁面"""
    # 載入資料
    raw_customers = load_customer_data()
    processed_companies = load_processed_companies()
    
    # 搜尋功能
    st.markdown('<div class="search-container">', unsafe_allow_html=True)
    col1, col2 = st.columns([3, 1])
    
    with col1:
        search_term = st.text_input("🔍 搜尋公司", placeholder="輸入公司代號或名稱進行搜尋...")
    
    with col2:
        st.markdown("<br>", unsafe_allow_html=True)
        if st.button("➕ 新增公司資訊", type="primary", use_container_width=True):
            clear_all_states()
            st.session_state.current_view = 'add_company'
            st.rerun()
    
    st.markdown('</div>', unsafe_allow_html=True)
    
    # 合併原始資料和已處理資料
    all_companies = {}
    
    # 先加入原始資料
    for customer in raw_customers:
        company_id = customer.get('company_id', '')
        company_name = customer.get('company_name', '')
        if company_id:
            all_companies[company_id] = {
                'companyId': company_id,
                'companyName': company_name,
                'connections': [],
                'raw_content': customer.get('files', [{}])[0].get('raw_content', '') if customer.get('files') else '',
                'is_processed': False
            }
    
    # 覆蓋已處理的資料
    for company_id, company_data in processed_companies.items():
        all_companies[company_id] = company_data
        all_companies[company_id]['is_processed'] = True
    
    # 過濾搜尋結果
    if search_term:
        filtered_companies = {
            k: v for k, v in all_companies.items()
            if search_term.lower() in v.get('companyId', '').lower() or 
               search_term.lower() in v.get('companyName', '').lower()
        }
    else:
        filtered_companies = all_companies
    
    # 顯示公司卡片
    if not filtered_companies:
        st.info("📭 沒有找到符合條件的公司資料")
        return
    
    # 使用網格布局顯示公司卡片
    cols = st.columns(2)
    for idx, (company_id, company) in enumerate(filtered_companies.items()):
        with cols[idx % 2]:
            render_company_card(company)

def render_company_card(company: Dict):
    """渲染單個公司卡片"""
    company_id = company.get('companyId', '')
    company_name = company.get('companyName', '')
    connections = company.get('connections', [])
    connection_count = len(connections)
    is_processed = company.get('is_processed', False)
    
    # 卡片HTML
    card_html = f"""
    <div class="company-card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
            <div class="company-id">{company_id}</div>
            <div style="font-size: 0.8rem; color: {'#4caf50' if is_processed else '#ff9800'};">
                {'✅ 已處理' if is_processed else '⏳ 原始資料'}
            </div>
        </div>
        <div class="company-name">{company_name}</div>
        <div class="connection-count">📊 連線資訊: {connection_count} 筆</div>
    </div>
    """
    
    st.markdown(card_html, unsafe_allow_html=True)
    
    # 點擊按鈕
    if st.button(f"查看 {company_name}", key=f"view_{company_id}", use_container_width=True):
        st.session_state.selected_company = company
        st.session_state.current_view = 'company_detail'
        st.rerun()

def render_company_detail():
    """渲染公司詳細頁面"""
    if not st.session_state.selected_company:
        st.error("未選擇公司")
        return

    company = st.session_state.selected_company

    # 頁面標題和導航
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown(f"## 🏢 {company.get('companyName', '')} ({company.get('companyId', '')})")

    with col2:
        if st.button("⬅️ 返回公司清單", use_container_width=True):
            st.session_state.current_view = 'company_list'
            st.session_state.selected_company = None
            st.rerun()

    # 標籤頁 - 使用 session state 來控制預設選中的分頁
    default_tab = st.session_state.get('active_tab', '🔗 連線管理')  # 預設顯示連線管理

    # 如果設置了 active_tab，清除它以避免影響後續操作
    if 'active_tab' in st.session_state:
        active_tab_index = ['📋 公司資訊', '🔗 連線管理', '📄 原始資料'].index(default_tab)
        del st.session_state['active_tab']
    else:
        active_tab_index = 1  # 預設選中連線管理（索引1）

    # 使用 st.selectbox 來模擬分頁選擇，這樣可以控制預設值
    selected_tab = st.selectbox(
        "選擇頁面",
        options=['📋 公司資訊', '🔗 連線管理', '📄 原始資料'],
        index=active_tab_index,
        key="company_detail_tab"
    )

    st.markdown("---")

    if selected_tab == '📋 公司資訊':
        render_company_info_tab(company)
    elif selected_tab == '🔗 連線管理':
        render_connections_tab(company)
    elif selected_tab == '📄 原始資料':
        render_raw_content_tab(company)

def render_company_info_tab(company: Dict):
    # 在表單外添加原始連線資訊查看功能
    raw_content = company.get('raw_content', '')
    if raw_content:
        with st.expander("📄 查看原始連線資訊（參考用）", expanded=False):
            # 計算原始內容的適當高度
            content_lines = max(5, len(raw_content.split('\n')))  # 最少5行
            st.text_area(
                "原始連線資訊內容",
                value=raw_content,
                height=min(content_lines * 20, 400),  # 每行約20px，最大400px
                help="此為從原始資料匯入的連線資訊，僅供參考"
            )



    # 渲染公司資訊標籤頁
    st.markdown("### 📝 編輯公司資訊")

    with st.form("company_info_form"):
        col1, col2 = st.columns(2)

        with col1:
            company_id = st.text_input("公司代號", value=company.get('companyId', ''), disabled=True)
            company_name = st.text_input("公司名稱", value=company.get('companyName', ''))

        with col2:
            # 顯示時間戳
            created_at = company.get('createdAt', '')
            updated_at = company.get('updatedAt', '')
            if created_at:
                st.text_input("建立時間", value=format_taiwan_datetime(created_at), disabled=True)
            if updated_at:
                st.text_input("更新時間", value=format_taiwan_datetime(updated_at), disabled=True)

        # 計算公司備註的適當高度
        notes_content = company.get('companyNotes', '')
        notes_lines = max(3, len(notes_content.split('\n')) + 1)  # 最少3行，根據內容調整
        company_notes = st.text_area("公司備註", value=notes_content,
                                   height=notes_lines * 25,  # 每行約25px
                                   help="VPN連線方式、跳板機資訊等額外說明")

        if st.form_submit_button("💾 儲存公司資訊", type="primary"):
            # 更新公司資訊
            company['companyName'] = company_name
            company['companyNotes'] = company_notes

            if save_company_data(company):
                st.success("✅ 公司資訊已成功儲存！")
                st.session_state.selected_company = company
                st.rerun()

def render_connections_tab(company: Dict):
    # 渲染連線管理標籤頁
    st.markdown("### 🔗 連線資訊管理")

    connections = company.get('connections', [])

    # 新增連線按鈕
    col1, col2 = st.columns([3, 1])
    with col2:
        if st.button("➕ 新增連線", type="primary", use_container_width=True):
            clear_all_states()
            st.session_state.current_view = 'add_connection'
            st.rerun()

    # 顯示連線清單
    if not connections:
        st.info("📭 尚未建立任何連線資訊")
    else:
        # 按產品類型分組
        connections_by_type = {}
        for idx, connection in enumerate(connections):
            product_type = connection.get('productType', '未知')
            if product_type not in connections_by_type:
                connections_by_type[product_type] = []
            connections_by_type[product_type].append((idx, connection))

        # 為每個產品類型創建一欄
        for product_type, type_connections in connections_by_type.items():
            # st.markdown(f"#### 📋 {product_type}")

            # 每行顯示2個卡片
            for i in range(0, len(type_connections), 2):
                cols = st.columns(2)

                # 處理第一個卡片
                if i < len(type_connections):
                    original_idx, connection = type_connections[i]
                    with cols[0]:
                        render_connection_card(connection, original_idx, company)

                        # 在卡片下方顯示詳細資訊或編輯表單
                        if (st.session_state.get('selected_connection_idx') == original_idx and
                            st.session_state.get('show_connection_detail', False)):
                            render_connection_detail_inline(connection)

                        if (st.session_state.get('selected_connection_idx') == original_idx and
                            st.session_state.get('show_edit_connection', False)):
                            render_edit_connection_inline(connection, original_idx, company)

                # 處理第二個卡片
                if i + 1 < len(type_connections):
                    original_idx, connection = type_connections[i + 1]
                    with cols[1]:
                        render_connection_card(connection, original_idx, company)

                        # 在卡片下方顯示詳細資訊或編輯表單
                        if (st.session_state.get('selected_connection_idx') == original_idx and
                            st.session_state.get('show_connection_detail', False)):
                            render_connection_detail_inline(connection)

                        if (st.session_state.get('selected_connection_idx') == original_idx and
                            st.session_state.get('show_edit_connection', False)):
                            render_edit_connection_inline(connection, original_idx, company)

            st.markdown("---")

def render_connection_card(connection: Dict, idx: int, company: Dict):
    """渲染連線卡片"""
    product_type = connection.get('productType', '')
    environment = connection.get('environment', '')
    server_ip = connection.get('serverIp', connection.get('host', connection.get('serverAddress', '')))
    # 環境標籤樣式
    env_class = "env-prod" if environment == "正式機" else "env-test"
    
    # 新增：根據產品類型設定卡片邊框顏色
    product_color_map = {
        'BPM AP': '#667eea',
        'BPM DB': '#764ba2',
        'HRM': '#4caf50',
        'Tiptop': '#f44336',
        'T100': '#2196f3',
        'Workflow': '#ff9800',
        'IMG': '#9c27b0',
        'Cosmos': '#00bcd4'
    }
    border_color = product_color_map.get(product_type, '#667eea')  # 預設藍色
    
    # 使用兩欄布局：左邊顯示資訊，右邊顯示按鈕（但按鈕現在在下方）
    col_info = st.columns(1)[0]  # 僅使用一列來顯示資訊
    with col_info:
        card_html = f"""
        <div class="connection-card" style="min-height: 120px; display: flex; flex-direction: column; justify-content: space-between; border: 4px solid {border_color};">
            <div>
                <div style="display: flex; justify-content: flex-start; align-items: center; margin-bottom: 0.5rem;">
                    <span class="product-type" style="background: {border_color}; color: white; padding: 0.2rem 0.6rem; border-radius: 15px; font-size: 0.8rem; font-weight: bold; margin-right: 0.5rem;">{product_type}</span>
                    <span class="environment-badge {env_class}">{environment}</span>
                </div>
                <div style="margin-top: 0.5rem;">
                    <strong>伺服器:</strong> {server_ip}<br>
                    <strong>描述:</strong> {connection.get('description', '無')}
                </div>
            </div>
        </div>
        """
        st.markdown(card_html, unsafe_allow_html=True)
    
    # 按鈕區域 - 改為水平排列並放在卡片下方
    button_col = st.columns(7)  # 創建三列用於按鈕
    
    with button_col[0]:
        if st.button("👁️ 查看", key=f"view_conn_{idx}"):
            clear_all_states()
            st.session_state.selected_connection = connection
            st.session_state.selected_connection_idx = idx
            st.session_state.show_connection_detail = True
            st.rerun()
            
    with button_col[1]:
        if st.button("✏️ 編輯", key=f"edit_conn_{idx}"):
            clear_all_states()
            st.session_state.selected_connection = connection
            st.session_state.selected_connection_idx = idx
            st.session_state.show_edit_connection = True
            st.rerun()
            
    with button_col[2]:
        if st.button("🗑️ 刪除", key=f"delete_conn_{idx}"):
            st.session_state.show_delete_confirm = True
            st.session_state.delete_connection_idx = idx
            st.session_state.delete_connection_data = connection
            st.rerun()

def render_connection_detail_inline(connection: Dict):
    """內聯顯示連線詳細資訊，根據產品類別自定義欄位順序並讓 serverUrl 跨欄顯示"""
    # 標題和關閉按鈕在同一行
    col_title, col_close = st.columns([4, 1])
    with col_title:
        st.markdown("#### 👁️ 連線詳細資訊")
    with col_close:
        if st.button("❌ 關閉", key="close_detail_inline", use_container_width=True):
            st.session_state.show_connection_detail = False
            st.rerun()
    
    # 定義不同產品類別的欄位顯示順序
    field_orders = {
        "BPM AP": [
            'productType', 'environment', 'serverIp', 'username', 'password',
            'bpmVersion', 'bpmAdmin', 'bpmPassword', 'serverUrl', 'description'
        ],
        "BPM DB": [
            'productType', 'environment', 'dbType', 'host', 'port', 'username', 'password',
            'database', 'serviceName', 'sid', 'connectionString', 'serverUrl', 'description'
        ],
        "HRM": [
            'productType', 'environment', 'serverAddress', 'serverUrl', 'description'
        ],
        "Tiptop": [
            'productType', 'environment', 'serverIp', 'identifier', 'soapUrl', 'userCode', 'serverUrl', 'description'
        ],
        "T100": [
            'productType', 'environment', 'host', 'companyCode', 'apiUrl', 'port', 'environmentCode', 'serverUrl', 'description'
        ],
        "Workflow": [
            'productType', 'serverIp', 'identifier', 'engineUrl', 'adminUser', 'serverUrl', 'description'
        ],
        "IMG": [
            'productType', 'enterpriseNavigationBackend', 'enterpriseNavigationExternalIp',
            'enterpriseNavigationExternalPort', 'enterpriseNavigationUsername',
            'enterpriseNavigationPassword', 'ccManagementCenter',
            'ccManagementCenterUsername', 'iamServiceUrl', 'iamVerificationUrl', 'serverUrl', 'description'
        ],
        "Cosmos": [
            'productType', 'serverIp', 'identifier', 'soapUrl', 'userCode', 'serverUrl', 'description'
        ]
        # 如果有未定義的產品類別，可以添加一個通用的或讓其自然顯示
    }
    
    product_type = connection.get('productType', '未知')
    
    # 獲取該產品類別的欄位順序，如果未定義則使用所有欄位 (過濾掉 id, createdAt, updatedAt)
    desired_order = field_orders.get(product_type, [k for k in connection.keys() if k not in ['id', 'createdAt', 'updatedAt']])
    
    # 顯示所有定義的欄位，不論是否有值
    filtered_order = desired_order
    
    # 顯示欄位
    col1, col2 = st.columns(2)
    field_count = 0 # 用於計算非跨欄位的欄位數，以正確分配到兩欄
    
    for field in filtered_order:
        label = get_field_label(field)
        value = connection.get(field, '')  # 使用 get 方法，沒有值時返回空字串

        # 特殊處理: 需要跨兩欄顯示的欄位
        if field in ["serverUrl", "description", "connectionString", "apiUrl", "soapUrl"]:
            with st.container():
                st.markdown(f"**{label}：**")
                # 保持一致的佈局，始終使用相同的欄位結構
                col_copy1, col_copy2 = st.columns([11, 1])
                with col_copy1:
                    if field == "description":
                        # 描述說明使用 text_area 顯示
                        help_text = "點擊選擇全部內容，然後 Ctrl+C 複製" if value and str(value).strip() else None
                        st.text_area(label, value=value, key=f"copy_input_{field}",
                                   label_visibility="collapsed",
                                   help=help_text,
                                   height=100, disabled=True)
                    else:
                        help_text = "點擊選擇全部內容，然後 Ctrl+C 複製" if value and str(value).strip() else None
                        st.text_input(label, value=value, key=f"copy_input_{field}",
                                    label_visibility="collapsed",
                                    help=help_text)
                with col_copy2:
                    # 根據是否有值決定是否顯示複製按鈕
                    if value and str(value).strip():
                        if st.button("📋", key=f"copy_{field}", help="複製提示"):
                            st.toast(f"💡 請選擇上方文字框內容並按 Ctrl+C 複製")
                    else:
                        # 沒有值時顯示空白佔位，保持佈局一致
                        st.write("")

            if field_count % 2 == 1:
                 field_count += 1

        else:
            # 一般欄位，按兩欄佈局顯示
            with col1 if field_count % 2 == 0 else col2:
                st.markdown(f"**{label}：**")
                # 保持一致的佈局，始終使用相同的欄位結構
                copy_col1, copy_col2 = st.columns([5, 1])
                with copy_col1:
                    help_text = "點擊選擇內容，Ctrl+C 複製" if value and str(value).strip() else None
                    st.text_input(label, value=value, key=f"copy_input_{field}_{field_count}",
                                label_visibility="collapsed",
                                help=help_text)
                with copy_col2:
                    # 根據是否有值決定是否顯示複製按鈕
                    if value and str(value).strip():
                        if st.button("📋", key=f"copy_{field}_{field_count}", help="複製提示"):
                            st.toast(f"💡 請選擇左側文字框內容並按 Ctrl+C 複製")
                    else:
                        # 沒有值時顯示空白佔位，保持佈局一致
                        st.write("")

            field_count += 1
    
    # 時間戳資訊 (跨欄顯示)
    if connection.get('createdAt') or connection.get('updatedAt'):
        st.markdown("---")
        timestamp_container = st.container() # 放在 container 中確保跨欄
        with timestamp_container:
            if connection.get('createdAt'):
                st.markdown(f"<div style='font-size: 0.8rem; color: #999;'>建立時間: {format_taiwan_datetime(connection.get('createdAt'))}</div>", unsafe_allow_html=True)
            if connection.get('updatedAt'):
                st.markdown(f"<div style='font-size: 0.8rem; color: #999;'>更新時間: {format_taiwan_datetime(connection.get('updatedAt'))}</div>", unsafe_allow_html=True)


def render_edit_connection_inline(connection: Dict, connection_idx: int, company: Dict):
    """內聯顯示編輯連線表單"""
    st.markdown("#### ✏️ 編輯連線資訊")

    product_type = connection.get('productType', '')

    # 顯示原始資料參考
    raw_content = company.get('raw_content', '')
    if raw_content:
        with st.expander("📄 查看原始連線資訊（參考用）", expanded=False):
            st.markdown(f'<div class="raw-content">{raw_content}</div>', unsafe_allow_html=True)

    with st.form(f"edit_connection_form_{connection_idx}"):
        st.markdown(f"**產品類型：** {product_type}")

        # 動態顯示對應欄位
        connection_data = dict(connection)  # 複製現有資料
        fields = PRODUCT_FIELDS.get(product_type, {"required": [], "optional": []})

        # 必要欄位
        st.markdown("**必要欄位：**")
        col1, col2 = st.columns(2)

        for idx, field in enumerate(fields["required"]):
            with col1 if idx % 2 == 0 else col2:
                current_value = connection_data.get(field, '')

                if field == "environment":
                    connection_data[field] = st.selectbox("環境類型*", ["測試機", "正式機"],
                                                        index=0 if current_value == "測試機" else 1, key=f"edit_env_{connection_idx}_{field}")
                elif field == "dbType":
                    connection_data[field] = st.selectbox("資料庫類型*", ["SQL Server", "Oracle"],
                                                        index=0 if current_value == "SQL Server" else 1, key=f"edit_db_{connection_idx}_{field}")
                elif field == "port":
                    connection_data[field] = st.number_input("連接埠*", value=int(current_value) if current_value else 1433,
                                                           min_value=1, max_value=65535, key=f"edit_port_{connection_idx}_{field}")
                elif field == "identifier":
                    if product_type == "Tiptop":
                        options = ["topprd", "toptest"]
                        idx = 0 if current_value in options else 0
                        if current_value in options:
                            idx = options.index(current_value)
                        connection_data[field] = st.selectbox("識別碼*", options, index=idx, key=f"edit_id_{connection_idx}_{field}")
                    elif product_type == "Workflow":
                        connection_data[field] = st.text_input("識別碼*", value=current_value or "WorkFlowServer1", key=f"edit_id_{connection_idx}_{field}")
                    elif product_type == "Cosmos":
                        connection_data[field] = st.text_input("識別碼*", value=current_value or "COSMOS1", key=f"edit_id_{connection_idx}_{field}")
                elif field == "environmentCode":
                    options = ["topprd", "toptest"]
                    idx = 1 if current_value == "toptest" else 0
                    if current_value in options:
                        idx = options.index(current_value)
                    connection_data[field] = st.selectbox("環境代號*", options, index=idx, key=f"edit_ec_{connection_idx}_{field}")
                elif field == "enterpriseNavigationExternalPort":
                    connection_data[field] = st.number_input("對外網路Port*",
                                                           value=int(current_value) if current_value else 8080,
                                                           min_value=1, max_value=65535, key=f"edit_port_{connection_idx}_{field}")
                else:
                    field_label = get_field_label(field)
                    # 移除密碼加密顯示，全部明碼顯示
                    connection_data[field] = st.text_input(f"{field_label}*", value=current_value, key=f"edit_txt_{connection_idx}_{field}")

        # 選擇性欄位
        if fields["optional"]:
            st.markdown("**選擇性欄位：**")

            # 定義跨欄欄位
            span_fields = ["description", "connectionString", "apiUrl", "soapUrl", "serverUrl"]

            # 先處理一般欄位（兩欄布局）- 除了跨欄欄位
            regular_fields = [f for f in fields["optional"] if f not in span_fields]
            if regular_fields:
                col1, col2 = st.columns(2)
                regular_idx = 0
                for field in regular_fields:
                    with col1 if regular_idx % 2 == 0 else col2:
                        current_value = connection_data.get(field, '')
                        field_label = get_field_label(field)

                        if field == "database":
                            # 顯示資料庫名稱欄位，但根據資料庫類型決定是否啟用
                            db_type = connection_data.get("dbType", "")
                            is_sql_server = db_type == "SQL Server"
                            connection_data[field] = st.text_input("資料庫名稱", value=current_value,
                                                                 disabled=not is_sql_server,
                                                                 help="僅適用於 SQL Server" if not is_sql_server else "",
                                                                 key=f"edit_db_{connection_idx}_{field}")
                        elif field == "serviceName":
                            # 顯示服務名稱欄位，但根據資料庫類型決定是否啟用
                            db_type = connection_data.get("dbType", "")
                            is_oracle = db_type == "Oracle"
                            connection_data[field] = st.text_input("服務名稱 (Service Name)", value=current_value,
                                                                 disabled=not is_oracle,
                                                                 help="僅適用於 Oracle" if not is_oracle else "",
                                                                 key=f"edit_svc_{connection_idx}_{field}")
                        elif field == "sid":
                            # 顯示 SID 欄位，但根據資料庫類型決定是否啟用
                            db_type = connection_data.get("dbType", "")
                            is_oracle = db_type == "Oracle"
                            connection_data[field] = st.text_input("系統識別碼 (SID)", value=current_value,
                                                                 disabled=not is_oracle,
                                                                 help="僅適用於 Oracle" if not is_oracle else "",
                                                                 key=f"edit_sid_{connection_idx}_{field}")
                        else:
                            connection_data[field] = st.text_input(field_label, value=current_value, key=f"edit_opt_{connection_idx}_{field}")
                        regular_idx += 1

            # 最後處理跨欄欄位（按照 field_orders 順序）
            for field in fields["optional"]:
                if field in span_fields:
                    current_value = connection_data.get(field, '')
                    field_label = get_field_label(field)

                    if field == "description":
                        connection_data[field] = st.text_area("描述說明", value=current_value,
                                                            placeholder="此連線的用途或備註", key=f"edit_desc_{connection_idx}_{field}")
                    else:
                        connection_data[field] = st.text_input(field_label, value=current_value, key=f"edit_span_{connection_idx}_{field}")

        # 表單按鈕
        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("💾 更新連線", type="primary"):
                # 驗證必要欄位
                missing_fields = []
                for field in fields["required"]:
                    if not connection_data.get(field):
                        missing_fields.append(get_field_label(field))

                if missing_fields:
                    st.error(f"❌ 請填寫必要欄位：{', '.join(missing_fields)}")
                else:
                    # 更新時間戳
                    connection_data["updatedAt"] = get_taiwan_now()

                    # 更新公司連線清單
                    company["connections"][connection_idx] = connection_data

                    if save_company_data(company):
                        st.success("✅ 連線資訊已成功更新！")
                        st.session_state.selected_company = company
                        st.session_state.show_edit_connection = False
                        st.rerun()

        with col2:
            if st.form_submit_button("❌ 取消"):
                st.session_state.show_edit_connection = False
                st.rerun()

def render_raw_content_tab(company: Dict):
    """渲染原始資料標籤頁"""
    st.markdown("### 📄 客戶原始連線資訊")

    raw_content = company.get('raw_content', '')

    if not raw_content:
        st.info("📭 無原始資料內容")
        return

    st.markdown("以下是客戶提供的原始連線資訊：")

    # 計算原始內容的適當高度
    content_lines = max(10, len(raw_content.split('\n')))  # 最少10行
    calculated_height = min(content_lines * 20, 600)  # 每行約20px，最大600px

    st.text_area(
        "原始連線資訊內容",
        value=raw_content,
        height=calculated_height,
        help="此為客戶提供的原始連線資訊，可用於參考和建立正式連線設定"
    )

def render_add_company_page():
    """渲染新增公司頁面"""
    st.markdown("## ➕ 新增公司資訊")

    # 返回按鈕
    if st.button("⬅️ 返回公司清單", use_container_width=False):
        clear_all_states()
        st.session_state.current_view = 'company_list'
        st.rerun()

    st.markdown("---")

    with st.form("add_company_form_page"):
        col1, col2 = st.columns(2)

        with col1:
            new_company_id = st.text_input("公司代號*", placeholder="例如: 02100008")
            new_company_name = st.text_input("公司名稱*", placeholder="例如: 倚強科技")

        with col2:
            new_company_notes = st.text_area("公司備註", placeholder="VPN連線方式、跳板機資訊等", height=100)

        st.markdown("---")
        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("💾 儲存公司", type="primary", use_container_width=True):
                if new_company_id and new_company_name:
                    new_company = {
                        'OID': str(uuid.uuid4()),
                        'companyId': new_company_id,
                        'companyName': new_company_name,
                        'companyNotes': new_company_notes,
                        'connections': [],
                        'raw_content': ''
                    }

                    if save_company_data(new_company):
                        st.success("✅ 公司資訊已成功新增！")
                        st.balloons()
                        clear_all_states()
                        st.session_state.current_view = 'company_list'
                        st.rerun()
                else:
                    st.error("❌ 請填寫必要欄位（公司代號、公司名稱）")

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                clear_all_states()
                st.session_state.current_view = 'company_list'
                st.rerun()

def render_add_connection_page():
    """渲染新增連線頁面"""
    company = st.session_state.selected_company
    if not company:
        st.error("未選擇公司")
        return

    st.markdown(f"## ➕ 新增連線資訊 - {company.get('companyName', '')}")

    # 返回按鈕
    if st.button("⬅️ 返回連線資訊管理", use_container_width=False):
        clear_all_states()
        st.session_state.current_view = 'company_detail'
        st.session_state.active_tab = '🔗 連線管理'  # 設置要顯示的分頁
        st.rerun()

    st.markdown("---")

    # 顯示已維護的連線類型標籤
    existing_connections = company.get('connections', [])
    if existing_connections:
        st.markdown("#### 📋 已維護的連線類型")

        # 收集已存在的產品類型+環境組合
        existing_combinations = set()
        for conn in existing_connections:
            product_type = conn.get('productType', '')
            environment = conn.get('environment', '')
            if product_type and environment:
                existing_combinations.add(f"{product_type} {environment}")

        # 顯示小標籤 - 密集式顯示
        if existing_combinations:
            # 根據產品類型設置不同顏色的標籤
            color_map = {
                'BPM AP': '#667eea',
                'BPM DB': '#764ba2',
                'HRM': '#4caf50',
                'Tiptop': '#f44336',
                'T100': '#2196f3',
                'Workflow': '#ff9800',
                'IMG': '#9c27b0',
                'Cosmos': '#00bcd4'
            }

            # 使用 HTML 來實現密集式顯示
            tags_html = '<div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 1rem;">'

            for combination in sorted(existing_combinations):
                # 提取產品類型來決定顏色
                product_type = combination.split()[0] + ' ' + combination.split()[1] if len(combination.split()) > 2 else combination.split()[0]
                color = color_map.get(product_type, '#667eea')

                # 使用單行 HTML 字符串避免格式問題
                tag_html = f'<span style="display: inline-block; background-color: {color}; color: white; padding: 4px 8px; margin: 2px; border-radius: 12px; font-size: 0.8em; border: 1px solid #ddd; font-weight: 500; white-space: nowrap;">✅ {combination}</span>'
                tags_html += tag_html

            tags_html += '</div>'
            st.markdown(tags_html, unsafe_allow_html=True)
        else:
            st.info("🔍 尚未維護任何連線資訊")
    else:
        st.info("🔍 尚未維護任何連線資訊")

    # 顯示原始資料參考
    raw_content = company.get('raw_content', '')
    if raw_content:
        with st.expander("📄 查看原始連線資訊（參考用）", expanded=False):
            st.markdown(f'<div class="raw-content">{raw_content}</div>', unsafe_allow_html=True)

    # 產品類型選擇
    product_type = st.selectbox("產品類型*",
                              options=list(PRODUCT_FIELDS.keys()),
                              help="選擇要新增的產品連線類型",
                              key="add_product_type_page")

    if product_type:
        st.markdown(f"#### 📝 {product_type} 連線設定")

        with st.form("add_connection_form_page"):
            # 動態顯示對應欄位
            connection_data = {"productType": product_type}
            fields = PRODUCT_FIELDS[product_type]

            # 必要欄位
            st.markdown("**必要欄位：**")
            col1, col2 = st.columns(2)

            for idx, field in enumerate(fields["required"]):
                with col1 if idx % 2 == 0 else col2:
                    if field == "environment":
                        connection_data[field] = st.selectbox("環境類型*", ["測試機", "正式機"], key=f"add_env_page_{field}")
                    elif field == "dbType":
                        connection_data[field] = st.selectbox("資料庫類型*", ["SQL Server", "Oracle"], key=f"add_db_page_{field}")
                    elif field == "port":
                        default_port = 1433 if connection_data.get("dbType") == "SQL Server" else 1521
                        connection_data[field] = st.number_input("連接埠*", value=default_port, min_value=1, max_value=65535, key=f"add_port_page_{field}")
                    elif field == "identifier":
                        if product_type == "Tiptop":
                            connection_data[field] = st.selectbox("識別碼*", ["topprd", "toptest"], index=0, key=f"add_id_page_{field}")
                        elif product_type == "Workflow":
                            connection_data[field] = st.text_input("識別碼*", value="WorkFlowServer1", key=f"add_id_page_{field}")
                        elif product_type == "Cosmos":
                            connection_data[field] = st.text_input("識別碼*", value="COSMOS1", key=f"add_id_page_{field}")
                    elif field == "companyCode":
                        connection_data[field] = st.text_input("企業代號*", value="99", key=f"add_cc_page_{field}")
                    elif field == "environmentCode":
                        connection_data[field] = st.selectbox("環境代號*", ["topprd", "toptest"], index=1, key=f"add_ec_page_{field}")
                    elif field == "enterpriseNavigationExternalPort":
                        connection_data[field] = st.number_input("對外網路Port*", value=8080, min_value=1, max_value=65535, key=f"add_port_page_{field}")
                    else:
                        field_label = get_field_label(field)
                        connection_data[field] = st.text_input(f"{field_label}*", key=f"add_txt_page_{field}")

            # 選擇性欄位
            if fields["optional"]:
                st.markdown("**選擇性欄位：**")

                # 定義跨欄欄位
                span_fields = ["description", "connectionString", "apiUrl", "soapUrl", "serverUrl"]

                # 先處理一般欄位（兩欄布局）- 除了跨欄欄位
                regular_fields = [f for f in fields["optional"] if f not in span_fields]
                if regular_fields:
                    col1, col2 = st.columns(2)
                    regular_idx = 0
                    for field in regular_fields:
                        with col1 if regular_idx % 2 == 0 else col2:
                            field_label = get_field_label(field)
                            if field == "database" and product_type == "BPM DB":
                                # 顯示資料庫名稱欄位，根據資料庫類型決定是否啟用
                                db_type = connection_data.get("dbType", "")
                                is_sql_server = db_type == "SQL Server"
                                connection_data[field] = st.text_input("資料庫名稱",
                                                                     disabled=not is_sql_server,
                                                                     help="僅適用於 SQL Server" if not is_sql_server else "",
                                                                     key=f"add_db_page_{field}")
                            elif field == "serviceName" and product_type == "BPM DB":
                                # 顯示服務名稱欄位，根據資料庫類型決定是否啟用
                                db_type = connection_data.get("dbType", "")
                                is_oracle = db_type == "Oracle"
                                connection_data[field] = st.text_input("服務名稱 (Service Name)",
                                                                     disabled=not is_oracle,
                                                                     help="僅適用於 Oracle" if not is_oracle else "",
                                                                     key=f"add_svc_page_{field}")
                            elif field == "sid" and product_type == "BPM DB":
                                # 顯示 SID 欄位，根據資料庫類型決定是否啟用
                                db_type = connection_data.get("dbType", "")
                                is_oracle = db_type == "Oracle"
                                connection_data[field] = st.text_input("系統識別碼 (SID)",
                                                                     disabled=not is_oracle,
                                                                     help="僅適用於 Oracle" if not is_oracle else "",
                                                                     key=f"add_sid_page_{field}")
                            else:
                                connection_data[field] = st.text_input(field_label, key=f"add_opt_page_{field}")
                            regular_idx += 1

                # 最後處理跨欄欄位（按照 field_orders 順序）
                for field in fields["optional"]:
                    if field in span_fields:
                        field_label = get_field_label(field)
                        if field == "description":
                            connection_data[field] = st.text_area("描述說明", placeholder="此連線的用途或備註", key=f"add_desc_page_{field}")
                        else:
                            connection_data[field] = st.text_input(field_label, key=f"add_span_page_{field}")

            st.markdown("---")
            # 表單按鈕
            col1, col2 = st.columns(2)
            with col1:
                if st.form_submit_button("💾 儲存連線", type="primary", use_container_width=True):
                    # 驗證必要欄位
                    missing_fields = []
                    for field in fields["required"]:
                        if not connection_data.get(field):
                            missing_fields.append(get_field_label(field))

                    if missing_fields:
                        st.error(f"❌ 請填寫必要欄位：{', '.join(missing_fields)}")
                    else:
                        # 新增連線ID和時間戳
                        connection_data["id"] = str(uuid.uuid4())
                        connection_data["createdAt"] = get_taiwan_now()
                        connection_data["updatedAt"] = get_taiwan_now()

                        # 新增到公司連線清單
                        if "connections" not in company:
                            company["connections"] = []
                        company["connections"].append(connection_data)

                        if save_company_data(company):
                            st.success("✅ 連線資訊已成功新增！")
                            st.balloons()
                            st.session_state.selected_company = company
                            clear_all_states()
                            st.session_state.current_view = 'company_detail'
                            st.session_state.active_tab = '🔗 連線管理'  # 設置要顯示的分頁
                            st.rerun()

            with col2:
                if st.form_submit_button("❌ 取消", use_container_width=True):
                    clear_all_states()
                    st.session_state.current_view = 'company_detail'
                    st.session_state.active_tab = '🔗 連線管理'  # 設置要顯示的分頁
                    st.rerun()

# 主要頁面路由
if st.session_state.current_view == 'company_list':
    render_company_list()
elif st.session_state.current_view == 'company_detail':
    render_company_detail()
elif st.session_state.current_view == 'add_company':
    render_add_company_page()
elif st.session_state.current_view == 'add_connection':
    render_add_connection_page()

# 新增公司表單 - 只在公司清單頁面顯示
if st.session_state.get('show_add_company', False) and st.session_state.current_view == 'company_list':
    st.markdown("---")
    st.markdown("### ➕ 新增公司資訊")

    with st.form("add_company_form"):
        col1, col2 = st.columns(2)

        with col1:
            new_company_id = st.text_input("公司代號*", placeholder="例如: 02100008")
            new_company_name = st.text_input("公司名稱*", placeholder="例如: 倚強科技")

        with col2:
            new_company_notes = st.text_area("公司備註", placeholder="VPN連線方式、跳板機資訊等")

        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("💾 儲存公司", type="primary"):
                if new_company_id and new_company_name:
                    new_company = {
                        'OID': str(uuid.uuid4()),
                        'companyId': new_company_id,
                        'companyName': new_company_name,
                        'companyNotes': new_company_notes,
                        'connections': [],
                        'raw_content': ''
                    }

                    if save_company_data(new_company):
                        st.success("✅ 公司資訊已成功新增！")
                        st.session_state.show_add_company = False
                        st.rerun()
                else:
                    st.error("❌ 請填寫必要欄位（公司代號、公司名稱）")

        with col2:
            if st.form_submit_button("❌ 取消"):
                st.session_state.show_add_company = False
                st.rerun()

# 新增連線表單 - 只在公司詳細頁面顯示
if st.session_state.get('show_add_connection', False) and st.session_state.current_view == 'company_detail':
    st.markdown("---")
    st.markdown("### ➕ 新增連線資訊")

    company = st.session_state.selected_company

    # 顯示原始資料參考
    raw_content = company.get('raw_content', '')
    if raw_content:
        with st.expander("📄 查看原始連線資訊（參考用）", expanded=False):
            st.markdown(f'<div class="raw-content">{raw_content}</div>', unsafe_allow_html=True)

    # 產品類型選擇 - 放在表單外面以便即時更新
    product_type = st.selectbox("產品類型*",
                              options=list(PRODUCT_FIELDS.keys()),
                              help="選擇要新增的產品連線類型",
                              key="add_product_type")

    if product_type:
        st.markdown(f"#### 📝 {product_type} 連線設定")

        with st.form("add_connection_form"):
            # 動態顯示對應欄位
            connection_data = {"productType": product_type}
            fields = PRODUCT_FIELDS[product_type]

            # 必要欄位
            st.markdown("**必要欄位：**")
            col1, col2 = st.columns(2)

            for idx, field in enumerate(fields["required"]):
                with col1 if idx % 2 == 0 else col2:
                    if field == "environment":
                        connection_data[field] = st.selectbox("環境類型*", ["測試機", "正式機"], key=f"add_env_{field}")
                    elif field == "dbType":
                        connection_data[field] = st.selectbox("資料庫類型*", ["SQL Server", "Oracle"], key=f"add_db_{field}")
                    elif field == "port":
                        default_port = 1433 if connection_data.get("dbType") == "SQL Server" else 1521
                        connection_data[field] = st.number_input("連接埠*", value=default_port, min_value=1, max_value=65535, key=f"add_port_{field}")
                    elif field == "identifier":
                        if product_type == "Tiptop":
                            connection_data[field] = st.selectbox("識別碼*", ["topprd", "toptest"], index=0, key=f"add_id_{field}")
                        elif product_type == "Workflow":
                            connection_data[field] = st.text_input("識別碼*", value="WorkFlowServer1", key=f"add_id_{field}")
                        elif product_type == "Cosmos":
                            connection_data[field] = st.text_input("識別碼*", value="COSMOS1", key=f"add_id_{field}")
                    elif field == "companyCode":
                        connection_data[field] = st.text_input("企業代號*", value="99", key=f"add_cc_{field}")
                    elif field == "environmentCode":
                        connection_data[field] = st.selectbox("環境代號*", ["topprd", "toptest"], index=1, key=f"add_ec_{field}")
                    elif field == "enterpriseNavigationExternalPort":
                        connection_data[field] = st.number_input("對外網路Port*", value=8080, min_value=1, max_value=65535, key=f"add_port_{field}")
                    else:
                        field_label = get_field_label(field)
                        # 移除密碼加密顯示，全部明碼顯示
                        connection_data[field] = st.text_input(f"{field_label}*", key=f"add_txt_{field}")

            # 選擇性欄位
            if fields["optional"]:
                st.markdown("**選擇性欄位：**")

                # 定義跨欄欄位
                span_fields = ["description", "connectionString", "apiUrl", "soapUrl", "serverUrl"]

                # 先處理一般欄位（兩欄布局）- 除了跨欄欄位
                regular_fields = [f for f in fields["optional"] if f not in span_fields]
                if regular_fields:
                    col1, col2 = st.columns(2)
                    regular_idx = 0
                    for field in regular_fields:
                        with col1 if regular_idx % 2 == 0 else col2:
                            field_label = get_field_label(field)
                            if field == "database" and product_type == "BPM DB":
                                # 顯示資料庫名稱欄位，根據資料庫類型決定是否啟用
                                db_type = connection_data.get("dbType", "")
                                is_sql_server = db_type == "SQL Server"
                                connection_data[field] = st.text_input("資料庫名稱",
                                                                     disabled=not is_sql_server,
                                                                     help="僅適用於 SQL Server" if not is_sql_server else "",
                                                                     key=f"add_db_{field}")
                            elif field == "serviceName" and product_type == "BPM DB":
                                # 顯示服務名稱欄位，根據資料庫類型決定是否啟用
                                db_type = connection_data.get("dbType", "")
                                is_oracle = db_type == "Oracle"
                                connection_data[field] = st.text_input("服務名稱 (Service Name)",
                                                                     disabled=not is_oracle,
                                                                     help="僅適用於 Oracle" if not is_oracle else "",
                                                                     key=f"add_svc_{field}")
                            elif field == "sid" and product_type == "BPM DB":
                                # 顯示 SID 欄位，根據資料庫類型決定是否啟用
                                db_type = connection_data.get("dbType", "")
                                is_oracle = db_type == "Oracle"
                                connection_data[field] = st.text_input("系統識別碼 (SID)",
                                                                     disabled=not is_oracle,
                                                                     help="僅適用於 Oracle" if not is_oracle else "",
                                                                     key=f"add_sid_{field}")
                            else:
                                connection_data[field] = st.text_input(field_label, key=f"add_opt_{field}")
                            regular_idx += 1

                # 最後處理跨欄欄位（按照 field_orders 順序）
                for field in fields["optional"]:
                    if field in span_fields:
                        field_label = get_field_label(field)
                        if field == "description":
                            connection_data[field] = st.text_area("描述說明", placeholder="此連線的用途或備註", key=f"add_desc_{field}")
                        else:
                            connection_data[field] = st.text_input(field_label, key=f"add_span_{field}")

            # 表單按鈕
            col1, col2 = st.columns(2)
            with col1:
                if st.form_submit_button("💾 儲存連線", type="primary"):
                    # 驗證必要欄位
                    missing_fields = []
                    for field in fields["required"]:
                        if not connection_data.get(field):
                            missing_fields.append(get_field_label(field))

                    if missing_fields:
                        st.error(f"❌ 請填寫必要欄位：{', '.join(missing_fields)}")
                    else:
                        # 新增連線ID和時間戳
                        connection_data["id"] = str(uuid.uuid4())
                        connection_data["createdAt"] = get_taiwan_now()
                        connection_data["updatedAt"] = get_taiwan_now()

                        # 新增到公司連線清單
                        if "connections" not in company:
                            company["connections"] = []
                        company["connections"].append(connection_data)

                        if save_company_data(company):
                            st.success("✅ 連線資訊已成功新增！")
                            st.session_state.selected_company = company
                            st.session_state.show_add_connection = False
                            st.session_state.active_tab = '🔗 連線管理'  # 設置要顯示的分頁
                            # 清除相關的 session state
                            for key in list(st.session_state.keys()):
                                if key.startswith('add_'):
                                    del st.session_state[key]
                            st.rerun()

            with col2:
                if st.form_submit_button("❌ 取消"):
                    st.session_state.show_add_connection = False
                    # 清除相關的 session state
                    for key in list(st.session_state.keys()):
                        if key.startswith('add_'):
                            del st.session_state[key]
                    st.rerun()

# 定義刪除確認對話框函數
@st.dialog("確認刪除")
def show_delete_confirmation():
    """顯示刪除確認對話框"""
    delete_idx = st.session_state.get('delete_connection_idx', -1)
    company = st.session_state.selected_company
    connection_data = st.session_state.get('delete_connection_data', {})

    if delete_idx >= 0 and delete_idx < len(company.get('connections', [])):
        product_type = connection_data.get('productType', '')
        environment = connection_data.get('environment', '')
        server_ip = connection_data.get('serverIp', connection_data.get('host', connection_data.get('serverAddress', '')))

        # 警告圖示和訊息
        st.markdown("### ⚠️ 確認刪除")
        st.warning("您確定要刪除以下連線資訊嗎？")

        # 顯示要刪除的連線資訊
        st.markdown("**連線詳細資訊：**")
        col1, col2 = st.columns(2)

        with col1:
            st.markdown(f"**產品類型：** {product_type}")
            st.markdown(f"**環境：** {environment}")

        with col2:
            st.markdown(f"**伺服器：** {server_ip}")

        st.markdown("---")

        # 按鈕區域
        col1, col2 = st.columns(2)

        with col1:
            if st.button("✅ 確認刪除", type="primary", use_container_width=True):
                # 執行刪除
                company['connections'].pop(delete_idx)
                if save_company_data(company):
                    st.success("✅ 連線資訊已刪除！")
                    st.session_state.selected_company = company
                    clear_all_states()
                    st.rerun()

        with col2:
            if st.button("❌ 取消", use_container_width=True):
                clear_all_states()
                st.rerun()

# 檢查是否需要顯示刪除確認對話框
if st.session_state.get('show_delete_confirm', False):
    show_delete_confirmation()


