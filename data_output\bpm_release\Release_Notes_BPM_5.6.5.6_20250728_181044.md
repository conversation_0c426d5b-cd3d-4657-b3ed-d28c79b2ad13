# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.5.6
- **舊版本**: 5.6.5.5_1
- **生成時間**: 2025-07-28 18:10:44
- **新增 Commit 數量**: 111

## 變更摘要

### jose<PERSON><PERSON>h (2 commits)

- **2018-12-12 10:19:19**: <V56>A00-20181122001 二次 修正 :多語系匯入功能，因Key大小寫導致無法匯入。
  - 變更檔案: 1 個
- **2018-12-10 10:26:09**: <V56> S00-20180908001 調整:程式位置
  - 變更檔案: 1 個

### walter_wu (22 commits)

- **2018-12-11 16:45:56**: S00-20181017001 系統管理工具
  - 變更檔案: 2 個
- **2018-11-23 12:28:38**: 修正SQL錯誤  Table>>COLUMN
  - 變更檔案: 1 個
- **2018-11-16 15:04:10**: A00-20181114001 修正客制開窗改變顯示筆數時 資料會重複出現的問題
  - 變更檔案: 1 個
- **2018-11-15 18:35:49**: A00-20180717001 修正前次修改後一般退回重辦會出錯
  - 變更檔案: 1 個
- **2018-11-13 17:35:16**: Q00-20181113001 修正A00-20180314001修改錯誤  之前固定小數後兩位是錯誤的 改成以前端設定為準
  - 變更檔案: 1 個
- **2018-11-09 18:12:03**: A00-20180307001 補上<#JumpWorkItem>替換
  - 變更檔案: 1 個
- **2018-11-05 17:56:16**: A00-20180328001 新增如果Mail url有hdnUserId會直接帶到代號欄位
  - 變更檔案: 2 個
- **2018-11-01 14:31:24**: C01-20181029003 工作通知頁面中的進階查詢功能異常修正
  - 變更檔案: 1 個
- **2018-10-29 18:17:32**: A00-20180323001-2 流程代理人將新增、修改和刪除都同時執行儲存 並將儲存鈕移除
  - 變更檔案: 2 個
- **2018-10-26 16:34:46**: A00-20180323001 將新增、修改和刪除都同時執行儲存 並將儲存鈕移除
  - 變更檔案: 2 個
- **2018-10-25 16:26:51**: A00-20180323001 調整為刪除同時執行儲存動作
  - 變更檔案: 1 個
- **2018-10-24 17:43:46**: A00-20180314001 上次修改取值方法 值太大會換成科學記號表示 故修改取值方法
  - 變更檔案: 1 個
- **2018-10-24 15:35:19**: C01-20180917001 修正多選開窗整批選取資料移除「x」按鈕，無法執行全部刪除
  - 變更檔案: 1 個
- **2018-10-17 13:55:02**: A00-20180717001 修正核決關卡內按流程定議退回會出現"請洽系統管理員"
  - 變更檔案: 1 個
- **2018-10-04 19:20:50**: C01-20180925009 修正Chrome無法從子視窗使用父視窗confirm()問題(批次轉派修正)
  - 變更檔案: 1 個
- **2018-10-04 17:52:18**: C01-20180925007 修正Chrome無法從子視窗使用父視窗confirm()問題  順便處理空白通過
  - 變更檔案: 2 個
- **2018-09-28 15:41:56**: C01-20180412001 從追蹤流程無法點開資料選取器
  - 變更檔案: 1 個
- **2018-09-19 15:43:08**: C01-20180621002 避免Web表單設計師，偶爾會出現滑鼠黏著元件的狀況發生。
  - 變更檔案: 1 個
- **2018-09-17 15:56:12**: A00-20180314001 修正列印表單上面的值與表單上實際的數值不同
  - 變更檔案: 1 個
- **2018-09-14 16:35:08**: A00-20180529003 修正授權流程選擇的流程分類"無"流程實例時，會請洽系統管理員
  - 變更檔案: 1 個
- **2018-09-13 11:20:16**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2018-09-12 19:51:51**: C01-20180723001 處理客制開窗、單選開窗、多選開窗特殊符號在Html轉換問題
  - 變更檔案: 4 個

### ChinRong (16 commits)

- **2018-12-11 15:53:16**: C01-20181211006 APP待辦上傳附件時顯示上傳成功，但繼續派送後其實沒有夾帶附件
  - 變更檔案: 1 個
- **2018-12-11 15:26:17**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2018-12-11 15:25:44**: 修正鼎捷移動待辦繼續派送後沒有英文語系
  - 變更檔案: 2 個
- **2018-12-11 15:05:50**: 修正企業微信英文語系片語按鈕跑版問題
  - 變更檔案: 1 個
- **2018-12-11 12:22:57**: 隱藏企業微信通知表單的附件進階按鈕
  - 變更檔案: 1 個
- **2018-12-11 12:22:27**: 二次修正行動版Grid一鍵展開後無法滑動的問題
  - 變更檔案: 1 個
- **2018-12-11 12:21:11**: 修正英文語系造成BPM App顯示跑版問題
  - 變更檔案: 4 個
- **2018-12-10 11:36:24**: 修正行動版Grid一鍵展開後無法滑動的問題
  - 變更檔案: 1 個
- **2018-12-07 19:00:23**: 修正BPM APP議題
  - 變更檔案: 4 個
- **2018-12-06 15:15:50**: 將5.7.4.1移動表單相關議題修正到5.6.5.6
  - 變更檔案: 7 個
- **2018-12-06 15:13:39**: 將5.7.4.1企業微信相關議題修到5.6.5.6
  - 變更檔案: 4 個
- **2018-12-06 15:11:09**: 將5.7.4.1鼎捷移動相關議題下修到5.6.5.6
  - 變更檔案: 10 個
- **2018-11-06 17:47:10**: C01-20181031001 修正當有多個工作項目建立時間相同時追蹤流程會取錯關卡的問題
  - 變更檔案: 2 個
- **2018-10-26 12:11:33**: C01-20181025001 修正隱藏元件方法在多欄位時會影響到其他元件的議題
  - 變更檔案: 1 個
- **2018-10-17 17:28:56**: C01-20181012001 修正行動版已結案流程仍會出現撤銷、取回按鈕
  - 變更檔案: 4 個
- **2018-09-13 18:10:48**: 修正中間層簽核session過期造成派送失敗問題
  - 變更檔案: 1 個

### yamiyeh10 (8 commits)

- **2018-12-11 15:07:20**: 修正行動版浮動按鈕英文語系會跑版問題
  - 變更檔案: 1 個
- **2018-12-11 13:54:32**: 修正IMG附件資訊只會顯示繁體中文
  - 變更檔案: 1 個
- **2018-12-11 12:07:27**: A00-20181207001 修正企業微信簡體出現繁體中文 1.附件資訊 2.退回重辦的片語只顯示片
  - 變更檔案: 5 個
- **2018-10-26 10:07:35**: A00-20181024003 修正行動版隱藏標籤的TextBox元件擺放雙欄式模板會跑版議題
  - 變更檔案: 1 個
- **2018-10-17 13:47:14**: 修正企業微信通知列表取下十筆時資料都是撈全部導致筆數與狀態對不上
  - 變更檔案: 1 個
- **2018-10-16 11:37:08**: A00-20181012001 修正IMG中間層點不同意時若未填簽核意見會顯示失敗而不是提示使用者必填簽核意見
  - 變更檔案: 1 個
- **2018-09-18 10:54:15**: Q00-20180911002 修正56版本處理的流程數據模塊篩選功能無效
  - 變更檔案: 1 個
- **2018-09-11 10:04:23**: 修正行動版Grid有單身資料時第一次進入畫面無一鍵展開按鈕
  - 變更檔案: 1 個

### waynechang (21 commits)

- **2018-12-11 14:18:46**: Q00-20181211001 支持ESS使用https連線
  - 變更檔案: 1 個
- **2018-12-07 16:10:49**: A00-20181127001 修正多人群組在最後一關終止流程後，流程圖顯示異常
  - 變更檔案: 1 個
- **2018-11-26 16:26:24**: A00-20181121001 修正ISO報表一覽表的簽出時間及簽出人員資料未顯示
  - 變更檔案: 3 個
- **2018-10-31 14:14:26**: C01-20180322001 修正表單元件使用SerialNumber並按分類編排時，若無前置字串則無法發起單據
  - 變更檔案: 1 個
- **2018-10-30 15:14:27**: A00-20181025001 ISO文件管理模組-生失效郵件範本管理多語系調整
  - 變更檔案: 1 個
- **2018-10-30 15:08:24**: A00-20181025002 ISO文件管理模組-權限屬性管理多語系調整
  - 變更檔案: 1 個
- **2018-10-29 15:51:44**: C01-20180926004 增加附件路徑驗證
  - 變更檔案: 1 個
- **2018-10-25 16:18:57**: C01-20181024001 將轉存表單的服務增加withnolock
  - 變更檔案: 1 個
- **2018-10-22 17:16:26**: C01-20180628002 修正M-Cloud表單維護效能緩慢
  - 變更檔案: 1 個
- **2018-10-17 17:01:37**: C01-20180725002 修正DotJIntegration的加簽關卡因關鍵字衝突導致無法加簽
  - 變更檔案: 1 個
- **2018-10-16 15:11:06**: A00-20181012002 修正ISO文件的參考文件的版本異常
  - 變更檔案: 1 個
- **2018-10-11 16:10:43**: C01-20180813001 調整是否代理的判斷邏輯
  - 變更檔案: 1 個
- **2018-10-03 17:17:42**: A00-20180717003 流程負責人在管理流程看不到授權的流程
  - 變更檔案: 1 個
- **2018-09-28 16:23:08**: C01-20180919005 修正取回重辦發生非預期錯誤導致關卡未被rollback
  - 變更檔案: 1 個
- **2018-09-28 13:53:23**: C01-20180927001 增加判斷當身分為admin或是模擬使用者時，不卡控權限
  - 變更檔案: 1 個
- **2018-09-27 17:00:33**: C01-20180927001
  - 變更檔案: 1 個
- **2018-09-27 11:20:20**: A00-20180925005 調整多語系寫法
  - 變更檔案: 1 個
- **2018-09-21 15:56:12**: C01-*********** ISO簡易查詢速度緩慢(增加with nolock)
  - 變更檔案: 1 個
- **2018-09-20 17:51:55**: C01-*********** 表單覆蓋議題
  - 變更檔案: 3 個
- **2018-09-17 18:20:56**: C01-20180906004 表單欄位消失議題
  - 變更檔案: 1 個
- **2018-09-14 17:02:26**: A00-20180913001
  - 變更檔案: 1 個

### pinchi_lin (3 commits)

- **2018-12-07 12:31:51**: C01-20181206001 修正在IMG中當待辦已被簽核過時會有"myFormValue" is undefined問題
  - 變更檔案: 1 個
- **2018-12-07 11:46:07**: C01-20181206001 修正BPMAPP終止流程時未填意見且點確定兩次後會變成同意派送問題
  - 變更檔案: 2 個
- **2018-09-26 18:08:33**: A00-*********** 修正IMG在詳情(直連表單)簽核時若人員有多組織則待辦不會往下派送問題
  - 變更檔案: 1 個

### joseph (20 commits)

- **2018-11-30 13:52:40**: <V56>A00-20181122001 修正 :多語系匯入功能，因Key大小寫導致無法匯入。
  - 變更檔案: 1 個
- **2018-11-15 18:56:47**: <V56>Q00-20181115002 修正:如果表單定義欄位設定為數值,但實際表單內容填顯文字,在列印表單時會報錯 ,調整照樣顯示文字
  - 變更檔案: 1 個
- **2018-11-08 11:40:03**: <V56> Q00-20181108001 修正 :透過portletEntry.jsp 外部連結 來發起流程 ,當發起者無權限發起時,提示訊息異常
  - 變更檔案: 2 個
- **2018-11-05 18:35:10**: <V56> A00-20181030001 修正 :當儲存表單 ,表單資料無異動和 需要重新取的後端資料的情境下 ,會造成關卡無派送
  - 變更檔案: 2 個
- **2018-11-01 10:40:36**: <V56> C01-20180925003 調整 :HRM的兼職部門已經可以支稱直屬主管及核決層級的資料 ,無須再補上資料
  - 變更檔案: 1 個
- **2018-10-25 17:35:27**: <V56> A00-20181018001  產生的組織同步XML會依照type來產生型態   因將屬性type寫死為部門 ,當如果是專案時 , type卻為department,導致同步報錯
  - 變更檔案: 3 個
- **2018-10-17 14:52:51**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2018-10-17 14:51:51**: <V56>C01-20181015001 修正 :WebService發單近來,SerialNumber缺少attribute id 導致前端解析時報錯
  - 變更檔案: 1 個
- **2018-10-15 17:09:53**: <V56> A00-20180625003 修正 : 填寫ESS流程,切換至一般待辦異常
  - 變更檔案: 1 個
- **2018-10-09 16:44:47**: <V56> Q00-20181009001 修正 :發起流程,查看簡易流程圖,不會顯示預先解析人員
  - 變更檔案: 1 個
- **2018-10-09 15:27:01**: <V56> S00-20180908001 預覽流程檢視：當解析到離職人員時，於姓名前加上 (X) 註記，可識別是離職人員
  - 變更檔案: 1 個
- **2018-10-08 11:22:28**: A00-*********** 修正 :從portal開啟待辦是空白畫面
  - 變更檔案: 1 個
- **2018-10-08 10:41:33**: <V56> 二次調整 C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度
  - 變更檔案: 1 個
- **2018-10-05 11:42:44**: C01-20180928002 調整:假如網址有帶入hdnCurrentUserId參數,當因為未登入導入到登入畫面時會將userId帶入到帳號欄位
  - 變更檔案: 2 個
- **2018-10-04 10:02:28**: Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **2018-10-04 10:01:01**: <V56>二次修正 C01-20180528001 調整提示文字為 :僅支持T100及程的流程
  - 變更檔案: 6 個
- **2018-10-03 14:09:12**: 復原被覆蓋的 A00-20171208001 修正:第一關向後加簽，關卡參與者選"單位主管",解析不到單位主管
  - 變更檔案: 1 個
- **2018-10-03 13:45:00**: <V56> C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度
  - 變更檔案: 1 個
- **2018-10-03 11:52:05**: <V56> A00-20180124002 將T100發單和寫入發單紀錄的邏輯調整為獨立的交易
  - 變更檔案: 1 個
- **2018-10-03 11:49:48**: <V56> C01-20180528001 調整當流程關卡進階勾選直接簽核網址時會提示不支援ESS流程
  - 變更檔案: 7 個

### derrick_shih (1 commits)

- **2018-10-12 10:16:55**: 同C01-20171205003 修正資料擷取器開窗問題。
  - 變更檔案: 1 個

### 施廷緯 (4 commits)

- **2018-10-05 16:28:01**: C01-20171205003 調整資料擷取器開窗至螢幕中間。
  - 變更檔案: 1 個
- **2018-10-03 17:16:47**: C01-20180516002 修正Web表單設計師在IE開窗後無法縮放問題
  - 變更檔案: 1 個
- **2018-09-18 17:03:50**: A00-20180918001 修正附件開啟除了發起關卡外，都是空白問題。
  - 變更檔案: 1 個
- **2018-09-17 13:51:48**: 修正ESS表單追蹤流程點擊BPM列印按鈕後出現空白，因ESS表單本身已有列印按鈕，故將BPM列印按鈕隱藏。
  - 變更檔案: 1 個

### 顏伸儒 (13 commits)

- **2018-10-04 18:32:24**: A00-20180223001 修正BPM56版,表單設計師ID第一個字必須為字母做卡控。
  - 變更檔案: 4 個
- **2018-10-02 17:05:11**: A00-20180831001-1 修正BPM56版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。
  - 變更檔案: 2 個
- **2018-10-02 14:35:44**: C01-20180614002 修正BPM56版,簽核流程設計師流程定義過期顯示紅叉。
  - 變更檔案: 1 個
- **2018-10-02 13:59:40**: C01-20180927001 移除log。
  - 變更檔案: 1 個
- **2018-09-28 16:53:18**: A00-20171003002 將log刪除。
  - 變更檔案: 1 個
- **2018-09-28 16:35:13**: A00-20180831001 修正BPM56版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。
  - 變更檔案: 2 個
- **2018-09-27 15:57:35**: A00-20180914002 修正BPM56版本,將流程設計師中呼叫網路服務設計師的Operations的背景改為白色。
  - 變更檔案: 1 個
- **2018-09-26 19:18:43**: A00-*********** 修正BPM56版本,表單更新Name後流程所掛的表單也會更新。
  - 變更檔案: 2 個
- **2018-09-20 17:36:31**: A00-20180919001 修正BPM56版本,退回重辦後會帶回目前處理位置的下一筆資料。
  - 變更檔案: 3 個
- **2018-09-18 15:48:36**: C01-2018073000 修正BPM56版,從mail進入時不顯示處理下個工作的按鈕。
  - 變更檔案: 1 個
- **2018-09-17 14:33:23**: C01-20180719004 修正BPM56版,IE點擊表單浮點數元件會跳動的問題。
  - 變更檔案: 2 個
- **2018-09-14 11:21:43**: C01-20180331002 修正BPM56版本,ISO製作文件索引失敗的問題。
  - 變更檔案: 7 個
- **2018-09-12 20:02:20**: C01-20180907001 修正BPM56版本,信件通知表單有隱藏欄位時,不將隱藏欄位顯示在信上。
  - 變更檔案: 1 個

### 治傑 (1 commits)

- **2018-09-14 13:38:45**: A00-20180913002 修正微信使用者管理頁面在IE環境下無法捲動
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. <V56>A00-20181122001 二次 修正 :多語系匯入功能，因Key大小寫導致無法匯入。
- **Commit ID**: `324bdbf056a88239974440645622cadbc980fa29`
- **作者**: josephshih
- **日期**: 2018-12-12 10:19:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`

### 2. S00-20181017001 系統管理工具
- **Commit ID**: `9f5e56ed595925a5b420db291d79be3eaeb0b06a`
- **作者**: walter_wu
- **日期**: 2018-12-11 16:45:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/main/ADMMainFrame.java`
  - ❌ **刪除**: `3.Implementation/subproject/process-designer/src/images/adm/EFGP.jpg`

### 3. C01-20181211006 APP待辦上傳附件時顯示上傳成功，但繼續派送後其實沒有夾帶附件
- **Commit ID**: `c0801ce2217282c3922bb75f7140e4d9e40c81f5`
- **作者**: ChinRong
- **日期**: 2018-12-11 15:53:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 4. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `8c630339f395be2898cf2bcd0728f8e2d49f1aa7`
- **作者**: ChinRong
- **日期**: 2018-12-11 15:26:17
- **變更檔案數量**: 0

### 5. 修正鼎捷移動待辦繼續派送後沒有英文語系
- **Commit ID**: `5518ff73a500630115e662f4b547eae34428fa16`
- **作者**: ChinRong
- **日期**: 2018-12-11 15:25:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 6. 修正行動版浮動按鈕英文語系會跑版問題
- **Commit ID**: `f19a36469e9557fc253af6184cd03b24d7c8925d`
- **作者**: yamiyeh10
- **日期**: 2018-12-11 15:07:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 7. 修正企業微信英文語系片語按鈕跑版問題
- **Commit ID**: `0beb7cf6252185e8ce9e49b72f5bc7415ad8941b`
- **作者**: ChinRong
- **日期**: 2018-12-11 15:05:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`

### 8. Q00-20181211001 支持ESS使用https連線
- **Commit ID**: `3ba66da4bf88ade368fa155be6fcb9c48b049339`
- **作者**: waynechang
- **日期**: 2018-12-11 14:18:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`

### 9. 修正IMG附件資訊只會顯示繁體中文
- **Commit ID**: `f023c63eaba8dd1e349f13d87e6f84d0513ffa7e`
- **作者**: yamiyeh10
- **日期**: 2018-12-11 13:54:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 10. 隱藏企業微信通知表單的附件進階按鈕
- **Commit ID**: `f125f312d4ec069e674a95e860c6943b84bab29f`
- **作者**: ChinRong
- **日期**: 2018-12-11 12:22:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`

### 11. 二次修正行動版Grid一鍵展開後無法滑動的問題
- **Commit ID**: `a118bdcc94e385c109d15ad4a0b38e9edd477366`
- **作者**: ChinRong
- **日期**: 2018-12-11 12:22:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`

### 12. 修正英文語系造成BPM App顯示跑版問題
- **Commit ID**: `9a9f1a7ed01bfa0e6e62d99c23d54807000b4b74`
- **作者**: ChinRong
- **日期**: 2018-12-11 12:21:11
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5656.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`

### 13. A00-20181207001 修正企業微信簡體出現繁體中文 1.附件資訊 2.退回重辦的片語只顯示片
- **Commit ID**: `9d2147d05bbcce99d379a4d4b936cca838367e7a`
- **作者**: yamiyeh10
- **日期**: 2018-12-11 12:07:27
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5656.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileApplyNewStyleExtruded.js`

### 14. 修正行動版Grid一鍵展開後無法滑動的問題
- **Commit ID**: `75026011b5f84694ad725c7dcc603d34939197d1`
- **作者**: ChinRong
- **日期**: 2018-12-10 11:36:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/MobileFormCommon.js`

### 15. <V56> S00-20180908001 調整:程式位置
- **Commit ID**: `4fc45716d7750718e4d261f4329f60a845f47574`
- **作者**: josephshih
- **日期**: 2018-12-10 10:26:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 16. 修正BPM APP議題
- **Commit ID**: `dbba059e83df5fa25e2f89e19ed091ad23b0e078`
- **作者**: ChinRong
- **日期**: 2018-12-07 19:00:23
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 17. A00-20181127001 修正多人群組在最後一關終止流程後，流程圖顯示異常
- **Commit ID**: `fa14e3ec2bf3cb9289e3564dce6a0a86f0c68879`
- **作者**: waynechang
- **日期**: 2018-12-07 16:10:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 18. C01-20181206001 修正在IMG中當待辦已被簽核過時會有"myFormValue" is undefined問題
- **Commit ID**: `411c2157f9a26f4fb208764aeccd14184672d7d9`
- **作者**: pinchi_lin
- **日期**: 2018-12-07 12:31:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`

### 19. C01-20181206001 修正BPMAPP終止流程時未填意見且點確定兩次後會變成同意派送問題
- **Commit ID**: `f6f8bbf803b31647611f45f5b14b9c35c6d2784e`
- **作者**: pinchi_lin
- **日期**: 2018-12-07 11:46:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`

### 20. 將5.7.4.1移動表單相關議題修正到5.6.5.6
- **Commit ID**: `4d513965afc5bad7cb9bcf2a9ae19780bc686e54`
- **作者**: ChinRong
- **日期**: 2018-12-06 15:15:50
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/system/MobileGrid.js`

### 21. 將5.7.4.1企業微信相關議題修到5.6.5.6
- **Commit ID**: `200bc39974b85be5129a8f4496148a6b43643327`
- **作者**: ChinRong
- **日期**: 2018-12-06 15:13:39
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListTracePerformed.js`

### 22. 將5.7.4.1鼎捷移動相關議題下修到5.6.5.6
- **Commit ID**: `4a24d55b73cd72f301ed996f3f2f9af34fca36b1`
- **作者**: ChinRong
- **日期**: 2018-12-06 15:11:09
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PhonebookData.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentDinWhaleDeployTool.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileWeChatUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-commonExtruded.css`

### 23. <V56>A00-20181122001 修正 :多語系匯入功能，因Key大小寫導致無法匯入。
- **Commit ID**: `4d6f11f4b318c816ebe69ae2afa02a2cae25789e`
- **作者**: joseph
- **日期**: 2018-11-30 13:52:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`

### 24. A00-20181121001 修正ISO報表一覽表的簽出時間及簽出人員資料未顯示
- **Commit ID**: `ddb7ec0a89bfbeb4f23287620cb6e99c5e10506b`
- **作者**: waynechang
- **日期**: 2018-11-26 16:26:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocForReportViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`

### 25. 修正SQL錯誤  Table>>COLUMN
- **Commit ID**: `542f3ccbff403533eaffd61be66db658c24a5955`
- **作者**: walter_wu
- **日期**: 2018-11-23 12:28:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.5.4_updateSQL_SQLServer.sql`

### 26. A00-20181114001 修正客制開窗改變顯示筆數時 資料會重複出現的問題
- **Commit ID**: `3b6323281558b20b2ce545ac4d2cc96d493041f1`
- **作者**: walter_wu
- **日期**: 2018-11-16 15:04:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 27. <V56>Q00-20181115002 修正:如果表單定義欄位設定為數值,但實際表單內容填顯文字,在列印表單時會報錯 ,調整照樣顯示文字
- **Commit ID**: `e9ede5ff88ee22d78d23c9eefa0a41511b4e21d7`
- **作者**: joseph
- **日期**: 2018-11-15 18:56:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 28. A00-20180717001 修正前次修改後一般退回重辦會出錯
- **Commit ID**: `60f111791d139257aa8ffadb5b905ac07fcf5a51`
- **作者**: walter_wu
- **日期**: 2018-11-15 18:35:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 29. Q00-20181113001 修正A00-20180314001修改錯誤  之前固定小數後兩位是錯誤的 改成以前端設定為準
- **Commit ID**: `34114f99970fe706db1ec000d8cf3952449650ed`
- **作者**: walter_wu
- **日期**: 2018-11-13 17:35:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 30. A00-20180307001 補上<#JumpWorkItem>替換
- **Commit ID**: `b47543c9269477ce485be33af53f809e49155729`
- **作者**: walter_wu
- **日期**: 2018-11-09 18:12:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 31. <V56> Q00-20181108001 修正 :透過portletEntry.jsp 外部連結 來發起流程 ,當發起者無權限發起時,提示訊息異常
- **Commit ID**: `29906f8a925c3114a485ebe21346a678cbe6bdcf`
- **作者**: joseph
- **日期**: 2018-11-08 11:40:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5656.xls`

### 32. C01-20181031001 修正當有多個工作項目建立時間相同時追蹤流程會取錯關卡的問題
- **Commit ID**: `b1f4e2bb102f35aa1c6ea046e741b7966987aedb`
- **作者**: ChinRong
- **日期**: 2018-11-06 17:47:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`

### 33. <V56> A00-20181030001 修正 :當儲存表單 ,表單資料無異動和 需要重新取的後端資料的情境下 ,會造成關卡無派送
- **Commit ID**: `a9d3b10ab8499c904babe59720a7814fba4fee98`
- **作者**: joseph
- **日期**: 2018-11-05 18:35:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/Dom4jUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 34. A00-20180328001 新增如果Mail url有hdnUserId會直接帶到代號欄位
- **Commit ID**: `4b37bfc3be64da1150873c5a15ce8171b9a8df1c`
- **作者**: walter_wu
- **日期**: 2018-11-05 17:56:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 35. C01-20181029003 工作通知頁面中的進階查詢功能異常修正
- **Commit ID**: `db1ec9e620ddee6447062272502d7aefb5f9c459`
- **作者**: walter_wu
- **日期**: 2018-11-01 14:31:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`

### 36. <V56> C01-20180925003 調整 :HRM的兼職部門已經可以支稱直屬主管及核決層級的資料 ,無須再補上資料
- **Commit ID**: `7526edad1698dd0d7b20e9755ea178551f2bd60b`
- **作者**: joseph
- **日期**: 2018-11-01 10:40:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`

### 37. C01-20180322001 修正表單元件使用SerialNumber並按分類編排時，若無前置字串則無法發起單據
- **Commit ID**: `2093b8e4878c8c7ce7572ae636d414b01a4b10ad`
- **作者**: waynechang
- **日期**: 2018-10-31 14:14:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SNGenerator.java`

### 38. A00-20181025001 ISO文件管理模組-生失效郵件範本管理多語系調整
- **Commit ID**: `afd25ea39cdd19f7145ec96118ce985d2c3a575b`
- **作者**: waynechang
- **日期**: 2018-10-30 15:14:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageNotificationContent/ModifyNotificationContent.jsp`

### 39. A00-20181025002 ISO文件管理模組-權限屬性管理多語系調整
- **Commit ID**: `fcdeeabd6bb96e18bd33e03e12fda26b7a61d404`
- **作者**: waynechang
- **日期**: 2018-10-30 15:08:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageAccessRight/ModifyAccessRight.jsp`

### 40. A00-20180323001-2 流程代理人將新增、修改和刪除都同時執行儲存 並將儲存鈕移除
- **Commit ID**: `0d6a804dc4ae19410bd6a52226801679301008d7`
- **作者**: walter_wu
- **日期**: 2018-10-29 18:17:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 41. C01-20180926004 增加附件路徑驗證
- **Commit ID**: `7cc7e15eb96f75b708dc167df24491877ac18687`
- **作者**: waynechang
- **日期**: 2018-10-29 15:51:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java`

### 42. A00-20180323001 將新增、修改和刪除都同時執行儲存 並將儲存鈕移除
- **Commit ID**: `575e1b1840164f3b560ffe54af61aa071d9be922`
- **作者**: walter_wu
- **日期**: 2018-10-26 16:34:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`

### 43. C01-20181025001 修正隱藏元件方法在多欄位時會影響到其他元件的議題
- **Commit ID**: `92a5a071fed6aa92db29ef08405bc14127009b5f`
- **作者**: ChinRong
- **日期**: 2018-10-26 12:11:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileMethodControl.js`

### 44. A00-20181024003 修正行動版隱藏標籤的TextBox元件擺放雙欄式模板會跑版議題
- **Commit ID**: `178b247e4948d430a81b551abcd05f3cbc4c2955`
- **作者**: yamiyeh10
- **日期**: 2018-10-26 10:07:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixMaterializeCssExtruded.css`

### 45. <V56> A00-20181018001  產生的組織同步XML會依照type來產生型態   因將屬性type寫死為部門 ,當如果是專案時 , type卻為department,導致同步報錯
- **Commit ID**: `cb210735fca5f813f506ed14b8cac37c48609bb6`
- **作者**: joseph
- **日期**: 2018-10-25 17:35:27
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NaNaTableUtilV2_0.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NanaTableUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/DeptRelation.java`

### 46. A00-20180323001 調整為刪除同時執行儲存動作
- **Commit ID**: `371f1a64ed56f766316cf5fab3809cf202c79b90`
- **作者**: walter_wu
- **日期**: 2018-10-25 16:26:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 47. C01-20181024001 將轉存表單的服務增加withnolock
- **Commit ID**: `01f2de485e48df0294a41a376295fc3bb30d508f`
- **作者**: waynechang
- **日期**: 2018-10-25 16:18:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 48. A00-20180314001 上次修改取值方法 值太大會換成科學記號表示 故修改取值方法
- **Commit ID**: `2e6f7f978303559183ee6775f74896b6bb7f13e5`
- **作者**: walter_wu
- **日期**: 2018-10-24 17:43:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 49. C01-20180917001 修正多選開窗整批選取資料移除「x」按鈕，無法執行全部刪除
- **Commit ID**: `653ca8b42c7f2c0041fb297fe142a570cbd5a290`
- **作者**: walter_wu
- **日期**: 2018-10-24 15:35:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 50. C01-20180628002 修正M-Cloud表單維護效能緩慢
- **Commit ID**: `c554225d3d39051c8d7649e01dc7a4830ad85cda`
- **作者**: waynechang
- **日期**: 2018-10-22 17:16:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/McloudXmlReaderBean.java`

### 51. C01-20181012001 修正行動版已結案流程仍會出現撤銷、取回按鈕
- **Commit ID**: `fe7334b018909ad6e327628420994c81446e36ac`
- **作者**: ChinRong
- **日期**: 2018-10-17 17:28:56
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`

### 52. C01-20180725002 修正DotJIntegration的加簽關卡因關鍵字衝突導致無法加簽
- **Commit ID**: `343248a235de5a00d3dad392fd83e5907a733f82`
- **作者**: waynechang
- **日期**: 2018-10-17 17:01:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`

### 53. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `8d1213302deec7314f24a4f4a69e3e6003569258`
- **作者**: joseph
- **日期**: 2018-10-17 14:52:51
- **變更檔案數量**: 0

### 54. <V56>C01-20181015001 修正 :WebService發單近來,SerialNumber缺少attribute id 導致前端解析時報錯
- **Commit ID**: `9eef6cff4f2f574e9eff42be8fda4beafa7deefb`
- **作者**: joseph
- **日期**: 2018-10-17 14:51:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`

### 55. A00-20180717001 修正核決關卡內按流程定議退回會出現"請洽系統管理員"
- **Commit ID**: `c6ede1d75de81c3a8e922546d94a8af43c592d8e`
- **作者**: walter_wu
- **日期**: 2018-10-17 13:55:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 56. 修正企業微信通知列表取下十筆時資料都是撈全部導致筆數與狀態對不上
- **Commit ID**: `cf2b04ec5b9f20fb919bada9424d4d991edc66d3`
- **作者**: yamiyeh10
- **日期**: 2018-10-17 13:47:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/BpmAppListNotice.js`

### 57. A00-20181012002 修正ISO文件的參考文件的版本異常
- **Commit ID**: `1ccab566c23c8f6878c894291ce5389b004993c6`
- **作者**: waynechang
- **日期**: 2018-10-16 15:11:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/MainFileViewer.jsp`

### 58. A00-20181012001 修正IMG中間層點不同意時若未填簽核意見會顯示失敗而不是提示使用者必填簽核意見
- **Commit ID**: `2095448b8c618d3cc0bd1544179eaaaeea23676d`
- **作者**: yamiyeh10
- **日期**: 2018-10-16 11:37:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 59. <V56> A00-20180625003 修正 : 填寫ESS流程,切換至一般待辦異常
- **Commit ID**: `7233a3b671199ebb67e5866014252ea7ee3d4d7d`
- **作者**: joseph
- **日期**: 2018-10-15 17:09:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 60. 同C01-20171205003 修正資料擷取器開窗問題。
- **Commit ID**: `e9bedc40d54d2a439c82d744a5a1cf3e77604a27`
- **作者**: derrick_shih
- **日期**: 2018-10-12 10:16:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 61. C01-20180813001 調整是否代理的判斷邏輯
- **Commit ID**: `806c55b74a75078e89a8d2c6d9ce656eb95a3e7d`
- **作者**: waynechang
- **日期**: 2018-10-11 16:10:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SubstituteUtil.java`

### 62. <V56> Q00-20181009001 修正 :發起流程,查看簡易流程圖,不會顯示預先解析人員
- **Commit ID**: `dd93d44eca21d7bfe15206fd6ada33d0ac6c90b1`
- **作者**: joseph
- **日期**: 2018-10-09 16:44:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 63. <V56> S00-20180908001 預覽流程檢視：當解析到離職人員時，於姓名前加上 (X) 註記，可識別是離職人員
- **Commit ID**: `1f6baaaff13caea7763b4f584b5ba0427a02c08c`
- **作者**: joseph
- **日期**: 2018-10-09 15:27:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 64. A00-*********** 修正 :從portal開啟待辦是空白畫面
- **Commit ID**: `e044c72a973692a6dcd9c3649b00b11ee613ce7d`
- **作者**: joseph
- **日期**: 2018-10-08 11:22:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 65. <V56> 二次調整 C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度
- **Commit ID**: `b86b1bb9a96792349397741e4b25f3f2b1647187`
- **作者**: joseph
- **日期**: 2018-10-08 10:41:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`

### 66. C01-20171205003 調整資料擷取器開窗至螢幕中間。
- **Commit ID**: `ad4e9373498cf5e1b1a3e3f17f85954da1eebcc5`
- **作者**: 施廷緯
- **日期**: 2018-10-05 16:28:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`

### 67. C01-20180928002 調整:假如網址有帶入hdnCurrentUserId參數,當因為未登入導入到登入畫面時會將userId帶入到帳號欄位
- **Commit ID**: `ed886456628e1059f651d4d505d58592d2429f5f`
- **作者**: joseph
- **日期**: 2018-10-05 11:42:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 68. C01-20180925009 修正Chrome無法從子視窗使用父視窗confirm()問題(批次轉派修正)
- **Commit ID**: `c0297d331ae0c5e21ff82575f048e5d0c01d773b`
- **作者**: walter_wu
- **日期**: 2018-10-04 19:20:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 69. A00-20180223001 修正BPM56版,表單設計師ID第一個字必須為字母做卡控。
- **Commit ID**: `e3f5b8ea2f661abbcf3a3f540da18741c4719e91`
- **作者**: 顏伸儒
- **日期**: 2018-10-04 18:32:24
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5656.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`

### 70. C01-20180925007 修正Chrome無法從子視窗使用父視窗confirm()問題  順便處理空白通過
- **Commit ID**: `4fa08b4463647716fed2880665da40ae45dd8ecc`
- **作者**: walter_wu
- **日期**: 2018-10-04 17:52:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 71. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `cb14aef84302eafec0c6f638be6340960de94f05`
- **作者**: joseph
- **日期**: 2018-10-04 10:02:28
- **變更檔案數量**: 0

### 72. <V56>二次修正 C01-20180528001 調整提示文字為 :僅支持T100及程的流程
- **Commit ID**: `e1a77a67ab5158d4dbb26180a303fd290c80df83`
- **作者**: joseph
- **日期**: 2018-10-04 10:01:01
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_TW.properties`

### 73. A00-20180717003 流程負責人在管理流程看不到授權的流程
- **Commit ID**: `5fff8cc619d424dd843746bc4c801d761c748811`
- **作者**: waynechang
- **日期**: 2018-10-03 17:17:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 74. C01-20180516002 修正Web表單設計師在IE開窗後無法縮放問題
- **Commit ID**: `e62d6f150e64b058e13ad41154b9e3f67ee04f02`
- **作者**: 施廷緯
- **日期**: 2018-10-03 17:16:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`

### 75. 復原被覆蓋的 A00-20171208001 修正:第一關向後加簽，關卡參與者選"單位主管",解析不到單位主管
- **Commit ID**: `9cd17feeaebcae3ee97fba58f63127ccc5908582`
- **作者**: joseph
- **日期**: 2018-10-03 14:09:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 76. <V56> C01-20181002002 調整 簽核流設計師-＞活動定義編輯器-＞進階 的欄位寬度
- **Commit ID**: `9616c338d8582c9ff4557202cd663e3408da4115`
- **作者**: joseph
- **日期**: 2018-10-03 13:45:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`

### 77. <V56> A00-20180124002 將T100發單和寫入發單紀錄的邏輯調整為獨立的交易
- **Commit ID**: `0dda1983157a004443986b5d73e2c96e69271de6`
- **作者**: joseph
- **日期**: 2018-10-03 11:52:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 78. <V56> C01-20180528001 調整當流程關卡進階勾選直接簽核網址時會提示不支援ESS流程
- **Commit ID**: `b91e91119324c30e910e5b1e66bc8fe976be1029`
- **作者**: joseph
- **日期**: 2018-10-03 11:49:48
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

### 79. A00-20180831001-1 修正BPM56版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。
- **Commit ID**: `d0a33914a89b235416cabf1fd06874c19a4ddba5`
- **作者**: 顏伸儒
- **日期**: 2018-10-02 17:05:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/RelevantDataHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/relevantdata/FormTypeEditorPanel.java`

### 80. C01-20180614002 修正BPM56版,簽核流程設計師流程定義過期顯示紅叉。
- **Commit ID**: `ca58549065416d5cc10af2bd7ad640715e367505`
- **作者**: 顏伸儒
- **日期**: 2018-10-02 14:35:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeCellRenderer.java`

### 81. C01-20180927001 移除log。
- **Commit ID**: `f873f2479427058c098488545b728ffca5d743be`
- **作者**: 顏伸儒
- **日期**: 2018-10-02 13:59:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 82. A00-20171003002 將log刪除。
- **Commit ID**: `245c02429fcb3d86c4abab9038fd7a0c73f349d8`
- **作者**: 顏伸儒
- **日期**: 2018-09-28 16:53:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/MultiDueDateEditorCER.java`

### 83. A00-20180831001 修正BPM56版,流程設計師裡流程定義中參考表單欄位無法儲存的問題。
- **Commit ID**: `3bbef320b0f6e3d2d39d923f4d1878136875ca9a`
- **作者**: 顏伸儒
- **日期**: 2018-09-28 16:35:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorCER.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/DueDateEditorPanel.java`

### 84. C01-20180919005 修正取回重辦發生非預期錯誤導致關卡未被rollback
- **Commit ID**: `abd3a109bda23f8801af046dd400fd1f19107861`
- **作者**: waynechang
- **日期**: 2018-09-28 16:23:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 85. C01-20180412001 從追蹤流程無法點開資料選取器
- **Commit ID**: `66431109604624710eff44e06f52e053efaddd8e`
- **作者**: walter_wu
- **日期**: 2018-09-28 15:41:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 86. C01-20180927001 增加判斷當身分為admin或是模擬使用者時，不卡控權限
- **Commit ID**: `4294dd4c99d9722421bc3d87fb0e13436365a396`
- **作者**: waynechang
- **日期**: 2018-09-28 13:53:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 87. C01-20180927001
- **Commit ID**: `d34cd287f899aec3bb317e25ceaf3480da53f57e`
- **作者**: waynechang
- **日期**: 2018-09-27 17:00:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 88. A00-20180914002 修正BPM56版本,將流程設計師中呼叫網路服務設計師的Operations的背景改為白色。
- **Commit ID**: `2cce5e6e8539078e23ab0bcaae63506adac25bc8`
- **作者**: 顏伸儒
- **日期**: 2018-09-27 15:57:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/WSInvocationEditorPanel.java`

### 89. A00-20180925005 調整多語系寫法
- **Commit ID**: `96bc3603e4d86a2b0bc6cde5fa397e365f99a3c1`
- **作者**: waynechang
- **日期**: 2018-09-27 11:20:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`

### 90. A00-*********** 修正BPM56版本,表單更新Name後流程所掛的表單也會更新。
- **Commit ID**: `7d655d22150201e58a0f0099adcde29b42517c59`
- **作者**: 顏伸儒
- **日期**: 2018-09-26 19:18:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/RelevantDataHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/FormDefinitionManagerClientDelegate.java`

### 91. A00-*********** 修正IMG在詳情(直連表單)簽核時若人員有多組織則待辦不會往下派送問題
- **Commit ID**: `872931138c3a7dd8f1b4807e753043e225e2d682`
- **作者**: pinchi_lin
- **日期**: 2018-09-26 18:08:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`

### 92. C01-*********** ISO簡易查詢速度緩慢(增加with nolock)
- **Commit ID**: `a7442073c69cc9a0d6aa4eec7d359068fee1e8e1`
- **作者**: waynechang
- **日期**: 2018-09-21 15:56:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java`

### 93. C01-*********** 表單覆蓋議題
- **Commit ID**: `896a854499cd1aeec8a55c76de2ad347fe8b8522`
- **作者**: waynechang
- **日期**: 2018-09-20 17:51:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 94. A00-20180919001 修正BPM56版本,退回重辦後會帶回目前處理位置的下一筆資料。
- **Commit ID**: `48cea1fbb0486549e944818d58aa6e0d8dbcf745`
- **作者**: 顏伸儒
- **日期**: 2018-09-20 17:36:31
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 95. C01-20180621002 避免Web表單設計師，偶爾會出現滑鼠黏著元件的狀況發生。
- **Commit ID**: `52d9bde7983676c83b05d9bc42342eb548751d82`
- **作者**: walter_wu
- **日期**: 2018-09-19 15:43:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`

### 96. A00-20180918001 修正附件開啟除了發起關卡外，都是空白問題。
- **Commit ID**: `8835cf567f396f6412ff7bb49081d7a1724670c9`
- **作者**: 施廷緯
- **日期**: 2018-09-18 17:03:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AttachmentUtil.java`

### 97. C01-2018073000 修正BPM56版,從mail進入時不顯示處理下個工作的按鈕。
- **Commit ID**: `bc5583300438a6166580265083e8c4c369a3a12a`
- **作者**: 顏伸儒
- **日期**: 2018-09-18 15:48:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 98. Q00-20180911002 修正56版本處理的流程數據模塊篩選功能無效
- **Commit ID**: `4a88175b8a4d0319cc70455f0ffa18b5cf8f4ba4`
- **作者**: yamiyeh10
- **日期**: 2018-09-18 10:54:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 99. C01-20180906004 表單欄位消失議題
- **Commit ID**: `dd012bf5a9acd955f255e4a2daf25dffe5eaae3b`
- **作者**: waynechang
- **日期**: 2018-09-17 18:20:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 100. A00-20180314001 修正列印表單上面的值與表單上實際的數值不同
- **Commit ID**: `ff2e58a2005875c4f851be138a81efc52ad2f043`
- **作者**: walter_wu
- **日期**: 2018-09-17 15:56:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 101. C01-20180719004 修正BPM56版,IE點擊表單浮點數元件會跳動的問題。
- **Commit ID**: `faefefe751e14ecdd847563006597b3539f85ddc`
- **作者**: 顏伸儒
- **日期**: 2018-09-17 14:33:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 102. 修正ESS表單追蹤流程點擊BPM列印按鈕後出現空白，因ESS表單本身已有列印按鈕，故將BPM列印按鈕隱藏。
- **Commit ID**: `898c55e58dc786452b36e62ca0be8c3885dbb497`
- **作者**: 施廷緯
- **日期**: 2018-09-17 13:51:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 103. A00-20180913001
- **Commit ID**: `6bf8752b80865533a585a952b1e27f51a5ba02e0`
- **作者**: waynechang
- **日期**: 2018-09-14 17:02:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 104. A00-20180529003 修正授權流程選擇的流程分類"無"流程實例時，會請洽系統管理員
- **Commit ID**: `3ca55d613ae833e779f92fb82e18785aa4e59d2a`
- **作者**: walter_wu
- **日期**: 2018-09-14 16:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 105. A00-20180913002 修正微信使用者管理頁面在IE環境下無法捲動
- **Commit ID**: `1a8418fd1f58861a12c4bb9613358d7e5b53b008`
- **作者**: 治傑
- **日期**: 2018-09-14 13:38:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 106. C01-20180331002 修正BPM56版本,ISO製作文件索引失敗的問題。
- **Commit ID**: `3aa901358ccd6adc283a5c02c7f34bce7c4e7f1f`
- **作者**: 顏伸儒
- **日期**: 2018-09-14 11:21:43
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/lib/Lucene/poi-3.12-20150511.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/lib/Lucene/poi-examples-3.12-20150511.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/lib/Lucene/poi-excelant-3.12-20150511.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/lib/Lucene/poi-ooxml-3.12-20150511.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/lib/Lucene/poi-ooxml-schemas-3.12-20150511.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/lib/Lucene/poi-scratchpad-3.12-20150511.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/lib/Lucene/xmlbeans-2.6.0.jar`

### 107. 修正中間層簽核session過期造成派送失敗問題
- **Commit ID**: `869c27ade4e7980b51d5619bb4c13f526f913128`
- **作者**: ChinRong
- **日期**: 2018-09-13 18:10:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 108. Merge branch 'develop' of http://10.40.41.229/BPM_Group/BPM.git into develop
- **Commit ID**: `74af074eacdd8a7a2ce48654e012a49b64064b85`
- **作者**: walter_wu
- **日期**: 2018-09-13 11:20:16
- **變更檔案數量**: 0

### 109. C01-20180907001 修正BPM56版本,信件通知表單有隱藏欄位時,不將隱藏欄位顯示在信上。
- **Commit ID**: `26bd409e0c0744a6d7001e1d24248555d347090b`
- **作者**: 顏伸儒
- **日期**: 2018-09-12 20:02:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 110. C01-20180723001 處理客制開窗、單選開窗、多選開窗特殊符號在Html轉換問題
- **Commit ID**: `fd3ca9ffce320976c423f39851febc962275a205`
- **作者**: walter_wu
- **日期**: 2018-09-12 19:51:51
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/ResultObjectForDataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`

### 111. 修正行動版Grid有單身資料時第一次進入畫面無一鍵展開按鈕
- **Commit ID**: `c2f27230e499075cd147949c64518ea113e66931`
- **作者**: yamiyeh10
- **日期**: 2018-09-11 10:04:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/MobileFormCommon.js`

