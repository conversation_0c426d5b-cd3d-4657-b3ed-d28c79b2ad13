{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.4_20240516", "date": "2024-05-16 17:28:25", "message": "[流程引擎] C01-20240508002 修正流程在併簽關卡，刪除流程時未釋放連線數問題", "author": "邱郁晏"}, "舊分支": {"branch_name": "release_5.8.9.4", "date": "2024-11-22 11:40:05", "message": "Revert \"[ORGDT]C01-20240517013 調整Web化設計工具在打開後隔一段時間會發生操作錯誤問題\"", "author": "lorenchang"}, "比較時間": "2025-07-18 10:43:11", "新增commit數量": 160, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "93193489189290afe290d6cfd1559b6b6655eb7d", "commit_訊息": "[流程引擎] C01-20240508002 修正流程在併簽關卡，刪除流程時未釋放連線數問題", "提交日期": "2024-05-16 17:28:25", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2b8093c2e7fda958fa96d4cef45b4f1fe06e2071", "commit_訊息": "[TIPTOP]Q00-20240305001 修正簽核歷程頁該使用者登入後無權限查看時，會造成授權人數被占據", "提交日期": "2024-03-05 14:24:48", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2ec94c32f2b9b923e0244dbd4fe4c65237fa9a19", "commit_訊息": "[Web]C01-20240509004 修正grid多栏位格线对不齐的问题", "提交日期": "2024-05-10 17:31:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b8ea64cae2a9a442c62492197b795acef29ef2a3", "commit_訊息": "[流程設計師]C01-20240416006 修正流程走到核決關卡後點擊待辦清單的流程會無法正常打開", "提交日期": "2024-05-09 10:35:56", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eafd3d27ae9f47a0e3c07ba0f4bceb9902a228cb", "commit_訊息": "[SYSDT]C01-20240502002 修正設計師使用權限管理中若人員的最後工作日設為未來日期時會無法顯示使用者問題", "提交日期": "2024-05-03 10:00:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d14a0deb0a5aa9f6c20338f6f414fb4f6f98a498", "commit_訊息": "[Web] Q00-20240428001 開窗資料條件財產名稱輸入[PL3/雙驅動改造]，資料帶回gird，儲存草稿/儲存表單後gird資料顯示異常问题修正", "提交日期": "2024-04-28 14:22:18", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/ds-grid-aw.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74a70ff9b7e28b07184702ac761df6263f32b31b", "commit_訊息": "[Web]Q00-20240426003 修正栏位过多，设置栏位宽度没效果的问题", "提交日期": "2024-04-26 10:35:22", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a26177cb6e4a1e98956c65bc380f3bf16e4f4833", "commit_訊息": "[Web]Q00-20240425005 修正加簽關卡選取經常對像在吳資料的狀況下會顯示錯誤頁面", "提交日期": "2024-04-26 08:21:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0dcca25fa8c2ca7657573da897cb9b84796817b", "commit_訊息": "[Web] Q00-20240425004 修正絕對位置表單，調整表單大小導致ScrollBar異常增加問題", "提交日期": "2024-04-25 17:48:22", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd93ca7caebfb7c1bbe30f7578ab9cd0e48832cf", "commit_訊息": "[Web]Q00-20240425002 修正开启绝对位置表单偶发报错TypeError: Cannot read properties of undefined (reading 'ElementGroup')的问题", "提交日期": "2024-04-25 15:32:11", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8a1375ce02c2e24fa684bff2b18eadb3e296e6f6", "commit_訊息": "[PRODT]Q00-20240424002 修正Web流程設計師中發起權限設定屬性的職務資料在編輯狀態後儲存會遺失問題", "提交日期": "2024-04-24 14:08:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c03a6578f24306b5ef4598c197f7139eb5ca8c2d", "commit_訊息": "[Web]Q00-20240423004 在觸發排程Trigger加入睡眠機制，以避免排程執行過快導致重複觸發狀況", "提交日期": "2024-04-23 15:15:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/schedule/SystematicJob.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "297df13ea58ecf80c5151dd790d59a7cc9dc8838", "commit_訊息": "[Web] Q00-20240423001 客户5521版到5894 將流程從XPDL轉BPMN會失敗问题修正", "提交日期": "2024-04-23 09:14:36", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "af78f61ad26ed5b302701e44043bfe0e2bbef7bd", "commit_訊息": "[Web]Q00-20240416001 修正含有日期元件運算异常问题[補修正]", "提交日期": "2024-04-19 15:40:55", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cbc3562f290c8610c0d6495e17a1808489a957e9", "commit_訊息": "[Web] Q00-20240419001 修正檢視參與者發送按鈕消失異常", "提交日期": "2024-04-19 11:28:32", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be9f76d537365bbf94f768bacc1fa7875685adbd", "commit_訊息": "[Web]Q00-20240417004 修正radio元件在Disable的狀態下 在「重發新流程」時不會將該元件的選項清空", "提交日期": "2024-04-17 13:56:05", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a12cb5748bd488937d49e1204e10c3e5e545109", "commit_訊息": "[流程引擎]Q00-20240416002 修正流程在併簽關卡進行撤銷or終止，若有呼叫流程事件會造成資料庫Lock", "提交日期": "2024-04-16 13:47:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bea36252a6ed597f3758165a735a5682df1ca35f", "commit_訊息": "[Web]Q00-20240416001 修正含有日期元件運算异常问题", "提交日期": "2024-04-16 10:59:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formValidation.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e4dd5e57466d2e80dc64f511162425d2f3032fb8", "commit_訊息": "[Web]Q00-20240412003 修正主旨顯示為編碼後的內容", "提交日期": "2024-05-13 17:08:05", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/StringUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "8876a858fa5d30c9d8ce1f9ea70bb41d92b1f2e0", "commit_訊息": "[流程設計師]Q00-20240412002 修正流程存在核決關卡時執行XDPL轉BPMN因活動集合定義遺失無法替換id而導致無法轉換問題", "提交日期": "2024-04-12 12:07:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "921eab48336a953cbe507b19356675f99b70d5fe", "commit_訊息": "[Web]Q00-20240412001 修正grid 設定table 模式，有很多個欄位會擠一起的情況", "提交日期": "2024-04-12 10:05:56", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "65ead14a2f3621d933f460d9be51ebac6233bd13", "commit_訊息": "[Web] Q00-20240410002 從5894版到58101後，Grid 欄位框線不會對齊,將調整寬度的script註解掉，Grid的欄位框線就可正常對齊问题修正", "提交日期": "2024-04-10 18:21:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9ed3ba1c5333370110f5fb603d8ee0b584cdc51e", "commit_訊息": "[雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶 [補修正]", "提交日期": "2024-04-10 08:56:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ce367f845876a5440ac5294464048baca12f283d", "commit_訊息": "[雙因素模組]Q00-20240409006 修正未啟用兩步驟認證清單會顯示已綁定的用戶", "提交日期": "2024-04-09 17:15:22", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4bb0e2b0a45a58ddbff9ce37013a780f6efeee27", "commit_訊息": "[Web] Q00-20240409004 修正流程草稿主旨上有反斜線\\導致畫面空白問題", "提交日期": "2024-04-09 16:27:19", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a2d8c489b37d811cd56aadae2f83f6822b3d2f1", "commit_訊息": "[雙因素模組]Q00-20240409003 信任端點裝置時間修正為24小時制", "提交日期": "2024-04-09 15:32:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0390f4d0421f1a151a5ba38b07b58a16024849b5", "commit_訊息": "[Web] V00-20240402002 流程发起时选择流程重要性为：紧急，但是在待办事项列表中没有出现红色标记列问题修正", "提交日期": "2024-04-03 14:01:05", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3489933eef9065c3d6a4e697a8d11cade281ff56", "commit_訊息": "[Web]Q00-20240402001 修正[報表維護作業]產出的報表畫面欄位异常", "提交日期": "2024-04-02 16:59:55", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f36dace889f96d5abd1e15318262043e3e7410f7", "commit_訊息": "[Web]Q00-20240401001 修正XPDL简易流程图显示主旨异常", "提交日期": "2024-04-01 11:41:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bec03bef0d41990b320a2379bbf5e1b9265df42e", "commit_訊息": "[組織同步] Q00-20240329002 修正組織同步User帳號啟用邏輯異常，導致部分使用者同步後帳號變為未啟用", "提交日期": "2024-03-29 15:28:10", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "15c23265b7bb46eb48d41b10e2380b391ef2f979", "commit_訊息": "[ORGDT]Q00-20240322002 修正Web組織管理工具中調離所有部門功能異常問題", "提交日期": "2024-03-27 10:06:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d181752b77750e1143fd5a7643ef5193d045c71", "commit_訊息": "[Web] Q00-20240319001 修正Grid勾选table模式显示异常问题[补]", "提交日期": "2024-03-25 17:55:20", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4bb540b19dd18389802721664fa2e21546060a2f", "commit_訊息": "[Web]Q00-20240313002 修正[ID]Obj.setColumnwidth(\"＜GridcolumnID＞\",100);写法没有效果 [补]", "提交日期": "2024-03-25 14:46:38", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c07ee128b098c1e3004ebec93e6d61a57ee59808", "commit_訊息": "[BPM APP]Q00-20240320001 修正TextBox有設定小數點進位時setFontColor會失效問題", "提交日期": "2024-03-20 15:04:20", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fc9438f00c1d4366482572527ecbf388c919b1c", "commit_訊息": "[Web]Q00-20240319003 修正顯示流程頁點擊轉由他人處理會無法執行", "提交日期": "2024-03-19 12:05:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4afe17f60ee9022b37490c1c3aa4c977b7dcc9c6", "commit_訊息": "[Web] Q00-20240319001 修正Grid勾选table模式显示异常问题", "提交日期": "2024-03-19 10:09:35", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c587765a35d1ca0238ae543ab681063d1570963", "commit_訊息": "[WEB]Q00-20240318002 修正從追踪流程開起，先點選絶對位置表單，再點RWD後，RWD畫面會變成絶對位置畫面寬度", "提交日期": "2024-03-18 17:39:37", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "02e22fdfb41ebcba93308c46c43480924814d6a9", "commit_訊息": "[PRODT]V00-20231208001 修正Web流程管理工具中無法選到放置在最外層表單樹下表單的問題", "提交日期": "2024-03-15 15:15:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormCategoryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07842534e9ef6b3a9494038ac2aabee9d8588ea1", "commit_訊息": "[Web] Q00-20240315001 修正开窗选择后无法关闭问题", "提交日期": "2024-03-15 15:11:41", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3ba1c24db8ba78c2eac953f616c1d23ac7c4c907", "commit_訊息": "[WEB]Q00-20240313002 修正[ID]Obj.setColumnwidth(\"＜GridcolumnID＞\",100);写法没有效果", "提交日期": "2024-03-13 15:22:29", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb66ebc2dec6c27466a801899304994d1202fae8", "commit_訊息": "[Web] V00-20240312003 修正页面列表栏位width和出现滚轴问题", "提交日期": "2024-03-13 14:24:14", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "864b8ea4a3fe8ed0c550f195c3e4ebd9593a6284", "commit_訊息": "[BPM APP]Q00-20240308001 修正行動表單在Label元件設定為invisible時，開啟待辦流程會發生取得表單資訊錯誤的問題", "提交日期": "2024-03-08 09:53:16", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4588891a97e9f37b934e8926234a4c93af782249", "commit_訊息": "[SAP]Q00-20240307001 修正SAP固定值显示异常的问题", "提交日期": "2024-03-07 16:01:36", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3a674511ef8b84e0e2da7174cbe3f6d2064b94e3", "commit_訊息": "[ORGDT]Q00-20240304001 修正Web組織管理工具中將人員離職日設為未來日期時，該使用者無法登入系統的問題", "提交日期": "2024-03-04 14:58:46", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d8322a3b14eca83648c070b4c379c0dfe8a1f3f", "commit_訊息": "[內部]Q00-20240301003 增加Queue啟動下一關是服務任務的相關屬性的log[補]", "提交日期": "2024-03-01 16:37:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52b4fb5836bec4050b6be13c2cfb36b1a0b47f9f", "commit_訊息": "[內部]Q00-20240301003 增加Queue啟動下一關是服務任務的相關屬性的log", "提交日期": "2024-03-01 16:24:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "35727097e39d097fa880d23cb26546242346d2cf", "commit_訊息": "[PRODT]Q00-20240301001 修正Web流程管理工具中關卡處理者有髒資料時無法開啟流程的問題[補]", "提交日期": "2024-03-01 15:42:12", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a16a8afac0ac498e7f560649967e3d0d5f8bfc21", "commit_訊息": "[PRODT]Q00-20240301001 修正Web流程管理工具中關卡處理者有髒資料時無法開啟流程的問題", "提交日期": "2024-03-01 13:42:50", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5f13d517bb9807e14853c792bdbf9cfc3268f106", "commit_訊息": "[Web] Q00-20240229001 調整外部連結信使用者，看不到「發送通知」按鈕問題。", "提交日期": "2024-02-29 17:10:00", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "509a27d2d6a6144e80615fac02da96462dc83198", "commit_訊息": "[Web]Q00-20240226003 调整改变浏览器视窗大小后，客制JSP排序功能失效的问题", "提交日期": "2024-02-26 15:59:45", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "644d0a0df401fae2f89b584365ad0df668b710c5", "commit_訊息": "[Web]Q00-20240222003 新增防呆：因表單有不存在之單身的髒資料造成單據無法開啟", "提交日期": "2024-02-22 15:45:33", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6b1ab37b8368eddc3bc35edc2a21544ed5680298", "commit_訊息": "[Web] Q00-20240222002 修正sql注册器中书写sql的from字段后不带空格用换行导致sql执行错误", "提交日期": "2024-02-22 15:16:29", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a60333d08daf308807c9ceda2afcab493bab6ff8", "commit_訊息": "[SSO]Q00-20240221004 修正使用SSO登入BPM時開啟的介面都是英文版[補修正]", "提交日期": "2024-02-22 11:09:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/PortletEntry.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e62a621fac4b3ce0fcba653c95f833adff2a380c", "commit_訊息": "[Web] Q00-20240222001 修正列印模式FormUtil.getValue获取不到Textbox和HiddenTextbox值", "提交日期": "2024-02-22 10:25:32", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "7c1028999536d2b8d2e83954fe197202ebcf01f6", "commit_訊息": "[Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题(防呆)[补]", "提交日期": "2024-02-21 17:52:16", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d83623daffa83343d6f5fefcfac402acc4c86d98", "commit_訊息": "[Web]Q00-20240221006 修正Grid的新增，修改，删除Button设定背景色或文字颜色，汇入时颜色样式消失的问题", "提交日期": "2024-02-21 14:59:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22b5eb774f46d0f8c62f67b0d3464d0c2410c219", "commit_訊息": "[SSO]Q00-20240221004 修正使用SSO登入BPM時開啟的介面都是英文版", "提交日期": "2024-02-21 14:02:34", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/PortletEntry.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "029b47ec823f2e09575e0b0ebe54cdc0ec43017f", "commit_訊息": "[PRODT]Q00-20240221002 修正Web流程管理工具的核決層級關卡中設定參考活動為自定義時會導致流程無法簽入的問題", "提交日期": "2024-02-21 13:38:45", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "209550d2d4c7305f0b2045a1528e6a0275a75500", "commit_訊息": "[ESS]Q00-20240221001 調整ESS簽核單據增加防呆避免串單", "提交日期": "2024-02-21 10:47:15", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f4b0063d27400bc92f8aa128688010c6a2a62ac6", "commit_訊息": "[流程引擎]Q00-20240220002 修正關卡通知信的內容有設定整張表單，且流程的核決關卡若因退回、取回重辦而需要重新展開核決關卡時，前一關往核決關卡派送時會有NullPointerException的錯誤", "提交日期": "2024-02-20 17:16:54", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "839aac211fdb6bc2468477c66138f672e1a9625a", "commit_訊息": "[Web] Q00-20240220001 修正转派工作人员意见内容会trim", "提交日期": "2024-02-20 14:22:49", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8f8d3ad135803270d1963f5f69fc22e8821cb0c9", "commit_訊息": "[PRODT]Q00-20240217001 修正Web化流程管理工具當流程內有核決關卡時部分流程會發生無法簽出問題", "提交日期": "2024-02-17 15:55:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1e324024c3c5d337d2ddf29ef1003278ed395045", "commit_訊息": "[內部]Q00-20240216004 增加關卡是否完成可以繼續往下派送的log", "提交日期": "2024-02-16 17:00:28", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5cf438cdbfb175524f5ed69eee20fe1dd24cb89b", "commit_訊息": "[Web] Q00-20240216002 修正checkBox若設定為唯讀時，預設選項樣式不一致，且無法取消預設選項問題", "提交日期": "2024-02-16 10:55:25", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7914c35ee52a61f3fde74e90a148215c040a1825", "commit_訊息": "[Web] Q00-20240216001 修正RadioButton若設定為唯讀時，會多預設選項(補)", "提交日期": "2024-02-16 10:49:45", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "feef509fda89204308a0300f35261b851e82a0fd", "commit_訊息": "[Web] Q00-20240216001 修正RadioButton若設定為唯讀時，會多預設選項", "提交日期": "2024-02-16 10:30:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5f9cce8500ba43322d82f218b752416f50b310e0", "commit_訊息": "[Web] Q00-20240205003 修正自定义开窗autocomplete补全列表显示在页面下方", "提交日期": "2024-02-05 16:49:00", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e1b52d4ab5ac741648f43df338b0831ae32bca9e", "commit_訊息": "[<PERSON><PERSON><PERSON>] Q00-20240205001 修正取得資料庫寫法未增加釋放連線", "提交日期": "2024-02-05 11:15:00", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthAuthenticationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthSettingManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "1e9da2047acb2c5bf7371bb2a33433dda403a4df", "commit_訊息": "[Web] Q00-20240202002 修正通过服务任务给表单赋值触发警告问题", "提交日期": "2024-02-02 17:51:56", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "62d791c9699aa3addd21b7f6ce3620064b9dc8a3", "commit_訊息": "[雙因素模組]Q00-20240202001 修正信任端點資訊有過期資料會造成每次登入都需重複驗政", "提交日期": "2024-02-02 14:36:53", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1016035a07086eb1e51588154ff4c65fc9f61f51", "commit_訊息": "[Web] Q00-20240201003 修正汇入excel时出现undefined(reading toString)异常信息", "提交日期": "2024-02-01 15:25:55", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a6aa63f099ef71e6713dece19c40c836eefaba4", "commit_訊息": "[ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊 [補修正]", "提交日期": "2024-02-01 09:06:52", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ac5578afa70ada8fe88a05b7bd0a06e8954cc1f", "commit_訊息": "[TIPTOP]Q00-20240201001 新增TIPTOP呼叫服務時間的Log", "提交日期": "2024-02-01 08:56:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "00a9ae6306d97f3272904690769446d3e3444036", "commit_訊息": "[ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊 [補修正]", "提交日期": "2024-02-01 08:48:35", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d052b14de4ba0394d855ee4e2b2bad2cf1cf8097", "commit_訊息": "[ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊", "提交日期": "2024-01-31 17:22:31", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d55e16ba1a83e12cfd9958695419dca938d6b9ec", "commit_訊息": "[流程引擎]Q00-20240123002 因流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制[補]", "提交日期": "2024-01-31 15:52:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3e1fbbf4369fb8e4b827c8d7d85ff3bd606fb902", "commit_訊息": "[流程引擎]Q00-20240131002 流程進版或匯入時，增加檢核流程的關卡連接線的From關卡及To關卡是否存在關卡定義中，若From 或 To 對應的關卡不存在時，將其刪除，避免影響流程運作", "提交日期": "2024-01-31 15:05:53", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d826a280e1946e8d0b677ab2830afc9f9769ca3", "commit_訊息": "[Web] Q00-20240130001 修正使用者名字有「𣶏」特殊字，流程派送會重複增加問題", "提交日期": "2024-01-30 12:00:41", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6241c3fef0394bdbc4e3f38dc47fc5b0e56b54ce", "commit_訊息": "[Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题[补]", "提交日期": "2024-01-30 10:58:06", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "91a3b10c3a8ab35cbef4a5871c15a10b7a4c697d", "commit_訊息": "[Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题", "提交日期": "2024-01-30 09:49:48", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3d5b907b4e9eb485c4a1716af2ada63aa18f74cb", "commit_訊息": "[Web]Q00-20240129002 修正客制JSP文件，行点击事件onRowClick报错的问题", "提交日期": "2024-01-29 16:52:18", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df888353ff0743241d70035639d98ff4820e7052", "commit_訊息": "[web] Q00-20240126001 选择新增单子点击批次阅读通知会报错问题修正", "提交日期": "2024-01-26 16:52:35", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e80edf6474346edc842771a0c13a69561457ea45", "commit_訊息": "[內部]Q00-20240125001 調整清除二階快取的log層級由warn改為debug", "提交日期": "2024-01-25 13:48:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f75017c573fdc23940ba08d27fa74d106187ab73", "commit_訊息": "[流程引擎]Q00-20240124003 優化流程在簽核後取活動定義關聯資料的機制", "提交日期": "2024-01-24 18:37:00", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "77283bf0141d001144652656604208c269c53e48", "commit_訊息": "[流程引擎]Q00-20240123004 修正關卡設定自動簽核4與流程上相同簽核者(不含發起者)跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，無法派送到下一關", "提交日期": "2024-01-23 15:25:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "52b54818e043a44c471e6da88eb85c207108d42d", "commit_訊息": "[流程引擎]Q00-20240123003 修正進入待辦或追蹤頁面，且流程進入核決關卡時，若核決關卡定義有髒資料或多組相同代號時可能會導致開啟畫面錯誤，因此增加過濾髒資料的邏輯", "提交日期": "2024-01-23 15:01:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7c7934e970fad20ed66a3401be6f80b81db62b83", "commit_訊息": "[流程引擎]Q00-20240123002 流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制", "提交日期": "2024-01-23 14:41:22", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1ada861b205fa6d2341c058f69f8346d850f6aad", "commit_訊息": "[Web] Q00-20240123001 調整信件樣板設置為整張表單時，Grid元件跑版問題", "提交日期": "2024-01-23 11:17:39", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df391c6dd25f60efb06d36ca803d9a0d9db59b93", "commit_訊息": "[Web]Q00-20240116003 修正当单身内容长度过大setColumnWidth没有效果的问题", "提交日期": "2024-01-16 13:30:29", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d9027fe95615480e5d953ec1504e52815e9f33e", "commit_訊息": "[Web] Q00-20240115001 修正LDAP账号登录失败，密码错误次数叠加问题", "提交日期": "2024-01-15 13:57:56", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3a26d9f9908cf1b0ec020e6c750aea0b2c1c1b0", "commit_訊息": "[Web] Q00-20240108002 調整批次離職工作轉派，選取人員為空時拋undefined問題，新增防呆", "提交日期": "2024-01-08 15:34:21", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f71818ec35af7e217a9ec683aee06fe83edac133", "commit_訊息": "[SAP]Q00-20240104002 修正SAP整合，当Grid更新或新增固定值栏位时，只会保留本次更新之前栏位固定值会被删掉的问题", "提交日期": "2024-01-04 17:30:37", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a852c063b8dba163d530f5bd7564bae84498bcd6", "commit_訊息": "[流程引擎]Q00-*********** 修正模擬使用者簽核關卡後，追蹤流程頁面偶發會出現交易異常的錯誤(情境為核決關卡內的下一關處理者為需自動簽核跳關時容易發生)", "提交日期": "2024-01-04 16:31:21", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessTraceControllerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8dcfe71fe89a614a2cb435cbb242c0bd22e9ecdf", "commit_訊息": "[Web] Q00-*********** 修正checkbox绑定其他checkbox元件，存储表单后无法更改绑定的元件", "提交日期": "2024-01-03 17:41:38", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "23ff94fd9234dd055780659c0b5302de61da5fae", "commit_訊息": "[Web] Q00-*********** 修正查询维护样板不输入查询条件排序异常(补修正-2)", "提交日期": "2024-01-03 17:06:02", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ba6639b2aca88455134212cdd4b4574645f23955", "commit_訊息": "[Web] Q00-20240103001 修正grid设置冻结栏位后设置样式显示错误", "提交日期": "2024-01-03 13:57:55", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f4d73174b37bbed0ca833e7a4283891b88ce3907", "commit_訊息": "[Web] Q00-*********** 修正查询维护样板不输入查询条件排序异常(补修正)", "提交日期": "2024-01-02 15:14:28", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "206a40b3a7cf4bf45a19928f3267481951dd557b", "commit_訊息": "[Web]Q00-20231229003 调整\"追蹤\"“監控”使用表单自适应宽度調整書面寬度無效果的問題", "提交日期": "2023-12-29 13:58:54", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "446c66a25d2f4625f66adb4bc4bbf3f2ba0573c3", "commit_訊息": "[Web]Q00-20231229002 调整个人资讯-->表单自适应宽度slider预设值为“较宽”", "提交日期": "2023-12-29 13:33:58", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a4abda0d680895c99a7f6d2194085e1df8ce09b3", "commit_訊息": "[Web] Q00-*********** 修正查询维护样板不输入查询条件排序异常", "提交日期": "2023-12-29 10:57:45", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a513fbbd3f3247ea24a4ccceaf08082d16a22fa5", "commit_訊息": "[ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題", "提交日期": "2023-12-28 18:09:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be4bf1715dfe28d62e5607fb9e5cecb3ed46ef5f", "commit_訊息": "[Web]Q00-20231226002 修正待辦清單頁進入ESS表單時或表單頁面點擊「處理上、下個工作」或簽核後直接跳下一個待辦時，工具列上方的Title欄位未正確顯示流程名稱的異常", "提交日期": "2023-12-26 16:44:52", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "42d72e97d8cb65d109ba1edb119232fb3f63a271", "commit_訊息": "[Web] Q00-20231226001 附件名称包含特殊字符(𡘙)，流程派送后显示无限增长", "提交日期": "2023-12-26 11:32:14", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "81519f1155b3e29ae01942479786760f05694790", "commit_訊息": "[TIPTOP]Q00-20231221004 修正TIPTOP發單若發單失敗未產生流程，解決異常問題後再次發單還是失敗", "提交日期": "2023-12-21 15:36:23", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1cf6fa9a3a00b4ffb2addf17a8868bd3dfa2ef7e", "commit_訊息": "[流程引擎]Q00-20231221003 修正流程引擎啟用「啟動下一關工作(workItem)時要執行的jndi服務」時，若當前結束的關卡為服務任務時，觸發「啟動下一關工作(workItem)時要執行的jndi服務」所傳遞的資料不完整的異常", "提交日期": "2023-12-21 14:22:15", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a2ddd9fcea07eb4ef5bb6c10d7493e6c1d719b91", "commit_訊息": "[Web]Q00-20231215004 建立登入or登出記錄物件資料request为空时新增防呆[补修正]", "提交日期": "2023-12-21 13:28:33", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "99156307abaa7f1f5a72433f51c12cf6307348f7", "commit_訊息": "[雙因素認證]Q00-*********** 修正LDAP登入輸入帳號錯誤不該影響登入畫面進錯誤頁面", "提交日期": "2023-11-01 14:04:59", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ec170b34adb72ce25510226c08599d95fe21302d", "commit_訊息": "[Web] Q00-*********** 修正小螢幕點選流程時，結案狀態未顯示問題(補)", "提交日期": "2023-12-20 16:53:17", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b5cb460b04c89022a6be3040496d2c1bbea5b1b5", "commit_訊息": "[流程引擎]Q00-*********** 修正新版自動簽核邏輯，當核決關卡下一關為核決關卡，且需要判斷自動簽核時，且兩個核決關卡都為相同處理者時應自動跳關，但偶發會無法自動跳關", "提交日期": "2023-12-20 15:10:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "719eda9dc41d2b961fde90fb7069cf1a393d5c28", "commit_訊息": "[Web] Q00-*********** 修正小螢幕點選流程時，結案狀態未顯示問題", "提交日期": "2023-12-20 14:09:25", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9e4a35c16ac19500d77b1849cf2ae542ab013b65", "commit_訊息": "[web] Q00-20231220003 當user使用Android手機、並有調整「字型大小」時，登入網頁的綁定畫面-QRCode會跑版問題修正", "提交日期": "2023-12-20 11:26:28", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05e2849b8be0b142e4353e0547c8b452067df221", "commit_訊息": "[Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題(補)", "提交日期": "2023-12-18 11:30:10", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8c3327a691286ef4f4b6dbcb774cfa6eb8613be7", "commit_訊息": "[Web]Q00-20231215004 建立登入or登出記錄物件資料request为空时新增防呆", "提交日期": "2023-12-15 17:52:46", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "77735074e149c3137b738f8fa3bc2682251410c7", "commit_訊息": "[Web] Q00-20231215003 由url链接进入待办，清除wms_user_isURL的session", "提交日期": "2023-12-15 14:46:41", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c78f4b0e81ac35878cae5aab0ee46219cf2f81fd", "commit_訊息": "[Web]Q00-20231215002 修正RadioButton和CheckBox勾选最后一个选项额外输入框，在刚进入流程时未选择却可以输入的问题", "提交日期": "2023-12-15 11:30:05", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/resources/html/SelectElementTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4db6431e88df71e949000bb19e7cc8d77a1a5f2c", "commit_訊息": "[Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題(補)", "提交日期": "2023-12-15 10:12:50", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea76015331566d76e0ea91122fde0ac481b6b9bb", "commit_訊息": "[Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題", "提交日期": "2023-12-15 10:08:52", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b6c3a12bd6cfa0c92ce3cdd410213d2763d2e1f2", "commit_訊息": "[PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題", "提交日期": "2023-12-14 18:37:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a96f528b5316ac544dae99d4e5e01a14323f4e2", "commit_訊息": "[SAP]Q00-20231213003 修正SAP整合服務，當回傳的資料類型為絕對位置表單Grid時，可能會有GridColumnId與GridValue順序錯誤的異常", "提交日期": "2023-12-13 15:44:25", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "495e89d0a6d102e409a22808b2ef3a9a2e7370f9", "commit_訊息": "[Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題", "提交日期": "2023-12-13 13:59:44", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "43573eb4d9916c14aaa9cfd679621e02f138f89e", "commit_訊息": "[Web] Q00-20231213002 不同模组下作业名称相同，导航页显示异常", "提交日期": "2023-12-13 13:22:56", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d5e7f7e8cb007af87d7cdb2799c4845abe1b782c", "commit_訊息": "[PRODT]Q00-20231212002 修正Web流程管理工具中流程徹銷中的sessionBean點編輯呈現空白的問題", "提交日期": "2023-12-12 15:11:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a083599acb0a3e7d1695d5889bac6f5f4a899f94", "commit_訊息": "[流程引擎]S00-20230602004 Restful转存表单Web Server调整为抓内网地址", "提交日期": "2023-12-08 14:45:43", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c21fb56cec75c5c46ad2d573effd62dad4be8ae0", "commit_訊息": "[Web] Q00-20231207002 excel汇入资料到Grid中，单身加总不计算", "提交日期": "2023-12-07 10:32:40", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f433bc278471dac2c3e447d8ab65c9d98f55d9c", "commit_訊息": "[PRODT]Q00-20231206002 修正Web流程管理工具中流程樣板的流程仍可移動到其他流程分類下的問題", "提交日期": "2023-12-06 18:01:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a3f37e27611144998e6382a7757504810b1cc53d", "commit_訊息": "[PRODT]Q00-20231206001 修正Web流程管理工具中流程樣板的流程簽入後會跑到其他流程分類下的問題", "提交日期": "2023-12-06 17:56:23", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6482ede664c5309dc2a79b715d347370fe894d87", "commit_訊息": "[web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正[补修正]", "提交日期": "2023-12-06 13:18:15", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3bdcb011d9fa9ceb709d653754cac8b6c1df460", "commit_訊息": "[雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證[補修正]", "提交日期": "2023-12-06 11:13:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f66ee96e0fb60a00015beb2ada77cf6aebe55c20", "commit_訊息": "[Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項", "提交日期": "2023-12-05 15:26:32", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "beac6fcb3d3538e42ff422de47131f6765141811", "commit_訊息": "[Web]Q00-20231205004 修正待办事项中选择锁定工具列后缩小视窗表单会被部分遮挡的问题", "提交日期": "2023-12-05 14:47:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "261d889be8c9bd6146f84415f9cf9986e44cdd33", "commit_訊息": "[web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正", "提交日期": "2023-12-05 13:49:48", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c2ac5f6b26cb04d81b0537d6e7fca4e6e830e436", "commit_訊息": "[流程引擎] Q00-20231204004 BPM系統會寄逾時通知信給離職的人問題修正[补修正]", "提交日期": "2023-12-05 10:37:28", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b5fb7d77dd76621b02064d99e5e1520a0104d716", "commit_訊息": "[PRODT]Q00-20231204006 修正Web流程管理工具中應用程式管理員的網頁應用程式或session bean新增呼叫參數時自訂id儲存後會變預設值的問題", "提交日期": "2023-12-04 18:52:09", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/IDGen.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "6c424d20a8d9d8ac935aada8dfd963550bc21fa5", "commit_訊息": "[Web] Q00-20231204005 修正BPM授權數不足時，寄件人並非系統管理中的設定", "提交日期": "2023-12-04 17:13:28", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "808c17f8bb32ce269e5b96c058032142cb4095be", "commit_訊息": "[流程引擎] Q00-20231204004 BPM系統會寄逾時通知信給離職的人問題修正", "提交日期": "2023-12-04 16:31:37", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7dbe4c490545852cd3e4bb22519f292c403a4b34", "commit_訊息": "[Web]Q00-20231204003 修正流程主旨、列印模式下grid资料有&#加任意數字，被轉成特殊符號的问题", "提交日期": "2023-12-04 15:48:40", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/StringUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c9542671f30e72acc8bd7b5650bfaf776660e083", "commit_訊息": "[流程引擎] Q00-20231204002 修正退回重辦系統通知變數未被正常置換問題", "提交日期": "2023-12-04 14:10:17", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "36cb7dd531976696dff2b0ade2b8891f09edcacb", "commit_訊息": "[Web]Q00-20231204001 发起流程时在删除草稿文件新增防呆", "提交日期": "2023-12-04 10:12:52", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "496270ce9b74a043d44615d1e62d0034842f635c", "commit_訊息": "[Web]Q00-20231201004 调整ipad Safari浏览器經常選取人員为默认全选", "提交日期": "2023-12-01 10:59:07", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "59a9f8b513701dd080540a5aef81693ac779054b", "commit_訊息": "[Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题[补修正]", "提交日期": "2023-11-29 16:55:32", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e9b06579311298460597982c56a19aefb369fbde", "commit_訊息": "[Web] Q00-20231129005 修正serialNumber栏位显示问题", "提交日期": "2023-11-29 16:44:03", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8f5edf68ff5da1e143b5933354fa964476c79f0d", "commit_訊息": "[Web] Q00-20231129004 修正從追蹤連結進入已關注的愛心不會亮，該流程原本已被關注，但從追蹤連結進入後不會亮，須等切換到表單葉面後才會亮", "提交日期": "2023-11-29 15:53:31", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "150073d1156688409a2ff10a0044568597a818c2", "commit_訊息": "[Web]Q00-20231128006 调整grid標頭固定显示[补修正]", "提交日期": "2023-11-29 14:16:33", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e2561fc2f8508bf9b14fffbe6c87c1f88de3357", "commit_訊息": "[Web]Q00-20231129002 调整個人資訊页多个提示訊息显示不完整的问题", "提交日期": "2023-11-29 12:17:02", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageSimpleUserProfile.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7597c8548322617eb38743121316293b1351c2a6", "commit_訊息": "[Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题", "提交日期": "2023-11-29 11:43:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3f5a7f41fee1b0533f174e3ff5590d9372db5c8b", "commit_訊息": "[Web]Q00-20231129001 調整在行動裝置撤銷流程時填寫撤銷意見會被選單擋住的問題", "提交日期": "2023-11-29 09:48:32", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f62f1e420f3dc0f8c24d24a282aa12a8a423a408", "commit_訊息": "[Web]Q00-20231128006 调整grid標頭固定显示", "提交日期": "2023-11-28 16:17:09", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/css/BpmTable.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7985c6fcd205359efa7f641b4040f5367834e0dc", "commit_訊息": "[ESS]Q00-20231128003 調整缺席紀錄方法相容帶有單身資料的ESS單據", "提交日期": "2023-11-28 13:35:41", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d10587b61b4fe8d0f9634af62459d5c002b15040", "commit_訊息": "[PRODT]Q00-20231128001 修正在開啟流程管理工具後主畫面的標題一併被更動問題", "提交日期": "2023-11-28 10:02:48", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8ac9b96739088eb1ce1e58f0bdafb2e0a3a4888", "commit_訊息": "[Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊", "提交日期": "2023-11-27 16:31:42", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fa45e971f47db918963d848e361b0b70f830dd3a", "commit_訊息": "[流程引擎]Q00-20231127001 修正關卡設定多人都要簽核且設定自動簽核2，與前一關相同者，若前一關為核決關卡，且未實際展開核決關卡時，流程無法派送至下一關的異常", "提交日期": "2023-11-27 15:20:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5cfe12e2453fa84fa47a2f7e2fc2b75b9670a4c", "commit_訊息": "[SAP]Q00-20231124005 優化SAP整合服務，當整合的資料類型為Grid時，同時支持RWD表單Grid及絕對位置表單Grid", "提交日期": "2023-11-24 17:33:54", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c326fcfc489ce10723d35059941f694c8b0bd409", "commit_訊息": "[流程引擎] Q00-20231124004 修正批次簽核造成重複寄信問題", "提交日期": "2023-11-24 15:00:42", "作者": "邱郁晏", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4d20fd94a82a6a04fd1480ba060f78b0b87da2ca", "commit_訊息": "[雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證", "提交日期": "2023-11-24 10:45:57", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/TFAModule/TFASetting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "f7c713f127e185a5bb19a3cfeff49c424d43bce7", "commit_訊息": "[Web] Q00-20231124002 修正流程主旨範本設定<#workItemName>显示N.A.", "提交日期": "2023-11-24 10:33:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6baed1f8d356d9ce56d65348a4a1e2e325b3531b", "commit_訊息": "[Web]Q00-20231124003 修正Grid某一格或某一行设置样式，点击排序后样式消失的问题", "提交日期": "2023-11-24 10:30:47", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bbc771b5dd78818b211451a9136b4735f254ad82", "commit_訊息": "[Web] Q00-20231124001 修正附件元件每個檔案容量限制設定成104857600 kb,無法上傳附件", "提交日期": "2023-11-24 10:24:42", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/DisplayLabelUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "36e88cf084474f9f6f3cf9e67f0926778edc94c8", "commit_訊息": "[流程引擎]Q00-20240326004 修正未釋放JDBC連線可能導致連線占滿後出現異常：IJ000453: Unable to get managed connection for java:/NaNaDS(補修正)", "提交日期": "2024-03-26 14:31:27", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "44c53147567618ca99cc1a221908660b167d5c2f", "commit_訊息": "[流程引擎]Q00-20240326004 修正未釋放JDBC連線可能導致連線占滿後出現異常：IJ000453: Unable to get managed connection for java:/NaNaDS", "提交日期": "2024-05-13 13:43:04", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "62f94d1b2d1442089b5adf273f6aa36c3bb2e161", "commit_訊息": "[流程封存]修正若特製流程存在條件時，會出現ConditionDefinition PK 重覆導致無法封存的異常", "提交日期": "2023-12-20 15:26:13", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}