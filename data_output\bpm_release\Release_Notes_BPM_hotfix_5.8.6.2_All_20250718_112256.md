# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.6.2_All
- **舊版本**: release_5.8.6.2
- **生成時間**: 2025-07-18 11:22:56
- **新增 Commit 數量**: 403

## 變更摘要

### lorenchang (2 commits)

- **2022-06-26 22:09:39**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.2
  - 變更檔案: 25 個
- **2021-08-20 09:06:16**: [內部]新增清除所有Server二階快取的相關EJB及RMI接口
  - 變更檔案: 4 個

### waynechang (5 commits)

- **2022-07-21 15:36:43**: [流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送
  - 變更檔案: 1 個
- **2021-11-22 16:14:49**: [內部]S00-20211112001 組織設計師及組織同步工具增加參數「orgdesigner.unitid.passsymbol」判斷Id是否檢查特殊符號
  - 變更檔案: 7 個
- **2021-08-11 14:51:02**: [在線閱覽]S00-*********** 在線閱覽模組新增自訂浮水印內容及測試轉檔功能
  - 變更檔案: 37 個
- **2021-11-08 16:41:56**: [流程引擎]Q00-20211108001 調整ExtOrgAccessor.findManagerForUser服務，當傳入的組織OID與人員不相關時，仍需回傳人員的主部門的主管
  - 變更檔案: 1 個
- **2021-09-07 10:40:16**: [Web]Q00-20210907002 Ajax_FormAccessor.getFormInstanceAttachmentSize() 增加「取得當前表單實例的附件數量」的方法
  - 變更檔案: 2 個

### kmin (39 commits)

- **2022-09-30 10:28:41**: C01-20220831001 檢測自動簽核卡住問題
  - 變更檔案: 1 個
- **2022-09-26 16:35:48**: Revert "[流程引擎]Q00-20220921001 調整發起流程頁面；由表單畫面切換至流程圖時，可根據當前表單內容進行流程預解析"
  - 變更檔案: 2 個
- **2022-09-21 17:03:12**: [流程引擎]Q00-20220921001 調整發起流程頁面；由表單畫面切換至流程圖時，可根據當前表單內容進行流程預解析
  - 變更檔案: 2 個
- **2022-08-05 10:04:52**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況 Templete有兩個計時器 (A)10分鐘更新一次User在線 (B)30秒取一次有沒有後端傳給使用者的訊息 如果視窗縮小或切換將兩個都停掉 回到視窗先更新一次User在線 重啟計時器 另外如果發現網路有不通的異常會先將計時器暫停，避免一直呼叫導致流覽器資源耗盡 在此種情況如果成功更新一次User在線會恢復計時器
  - 變更檔案: 3 個
- **2022-08-05 09:50:12**: Revert "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況"
  - 變更檔案: 3 個
- **2022-07-12 15:36:40**: [流程引擎]Q00-20220627001 優化核決層級關卡解析人員緩慢問題
  - 變更檔案: 1 個
- **2022-06-14 18:02:06**: Revert "revert [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題"
  - 變更檔案: 4 個
- **2022-06-14 18:01:20**: revert [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題
  - 變更檔案: 4 個
- **2022-06-14 17:59:25**: Revert "[BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]"
  - 變更檔案: 16 個
- **2022-05-20 09:00:00**: Revert "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]"
  - 變更檔案: 2 個
- **2022-04-28 09:26:20**: [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務[補修正] 內網判斷192.168改成只判斷192
  - 變更檔案: 1 個
- **2022-04-21 15:12:40**: [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務[補]
  - 變更檔案: 1 個
- **2022-04-21 15:05:18**: [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務
  - 變更檔案: 1 個
- **2022-03-23 14:37:55**: Revert "[流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過"
  - 變更檔案: 2 個
- **2022-03-23 11:42:39**: Revert "[流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過"
  - 變更檔案: 2 個
- **2022-03-21 09:44:27**: [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題 for MobileCommonServiceTool
  - 變更檔案: 1 個
- **2022-03-18 17:26:16**: Revert "[內部]Q00-20211005001 修正簽核流設計師中，行動端複製PC端設定有不同步的狀況"
  - 變更檔案: 2 個
- **2022-03-18 17:18:52**: Revert "[表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能[補]"
  - 變更檔案: 2 個
- **2022-03-18 16:39:33**: Revert "[BPM APP]C01-20210722010 調整郵件內容以及Line推播內容，密碼元件值以*號顯示"
  - 變更檔案: 1 個
- **2022-03-18 10:02:35**: 20220317 經博至討論後，暫時先將移除TT附件的機制，以便後續觀察
  - 變更檔案: 1 個
- **2022-03-15 15:46:34**: [組織同步]A00-20220314001 修正組織同步完會蓋掉使用者設定的 使用者是否顯示待辦事項小視窗
  - 變更檔案: 1 個
- **2022-03-15 15:45:29**: Revert "20220314 修正組織同步完會蓋掉使用者設定的 工作事項顯示設定 by kmin."
  - 變更檔案: 1 個
- **2022-03-15 15:26:32**: Revert "[Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單"
  - 變更檔案: 1 個
- **2022-03-15 14:24:22**: Revert "[Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單"
  - 變更檔案: 1 個
- **2022-03-14 16:17:56**: 20220314 修正組織同步完會蓋掉使用者設定的 工作事項顯示設定 by kmin.
  - 變更檔案: 1 個
- **2022-03-14 16:12:09**: 久鼎客戶 增加判斷非LDAP類型的帳號才會去執行「使用者更換密碼」動作 by kmin.
  - 變更檔案: 1 個
- **2022-03-14 16:06:27**: 月眉國際客戶Web表單設計師Grid視窗不會關閉 by kmin.
  - 變更檔案: 1 個
- **2022-03-14 15:51:02**: [組織同步]A00-20220224002 修正組織同步完會蓋掉使用者設定的 簽核完畢後的行為
  - 變更檔案: 1 個
- **2022-03-14 15:30:16**: Revert "[Web]Q00-20220120003 流程代理人設定的選擇流程開窗，預設用流程代號做排序"
  - 變更檔案: 1 個
- **2022-03-14 14:54:53**: Revert "[流程引擎]Q00-20211217001 修正當資料庫為oracle時，SQL註冊器未輸入任何條件查詢會報錯"
  - 變更檔案: 1 個
- **2022-03-11 10:38:35**: [Web]S00-20210429001 調整簡易流程若關卡處理者為代理人則處理者名稱旁有提示文字 Web端與移動端一併調整
  - 變更檔案: 1 個
- **2022-03-11 10:29:55**: 5862用不到這一支 by kmin 3/11
  - 變更檔案: 1 個
- **2022-03-11 10:28:43**: [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題[補] 補修正原因：修正於核決層級關卡的第一關執行向前加簽後，簡易流程圖無法正常呈現的問題。
  - 變更檔案: 1 個
- **2022-03-11 10:14:30**: [BPM APP]Q00-20210903001 修正顯示流程中簽核人員沒有多語系的問題 1. web與mobile端皆有此問題，一併修正
  - 變更檔案: 1 個
- **2022-03-11 08:48:11**: Revert "[流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程 相關議題單號：C01-20211109001。"
  - 變更檔案: 1 個
- **2022-03-10 17:26:25**: Revert "[流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題"
  - 變更檔案: 6 個
- **2022-03-10 14:57:17**: Revert "[流程引擎]Q00-20211019009 追蹤流程時，顯示表單欄位的背景顏色"
  - 變更檔案: 5 個
- **2022-03-09 16:32:45**: [流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程 相關議題單號：C01-20211109001。
  - 變更檔案: 1 個
- **2022-03-09 16:29:08**: Revert "[流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程"
  - 變更檔案: 1 個

### yanann_chen (66 commits)

- **2022-06-20 16:44:21**: [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-11 11:23:52**: [流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料
  - 變更檔案: 1 個
- **2022-05-04 14:02:42**: [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題
  - 變更檔案: 1 個
- **2022-05-25 17:01:11**: [表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出
  - 變更檔案: 3 個
- **2022-05-11 11:10:44**: [流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知
  - 變更檔案: 1 個
- **2022-03-24 18:28:08**: [流程引擎]A00-20220323001 修正流程核決層級關卡之後的關卡若有設定自動簽核，流程無法往下派送的問題
  - 變更檔案: 1 個
- **2022-02-08 18:03:20**: [流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過
  - 變更檔案: 2 個
- **2022-02-08 18:03:20**: [流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過
  - 變更檔案: 2 個
- **2022-01-20 16:51:55**: [流程引擎]Q00-20220120001 修正「使用者有多個部門，在選擇發起部門後若發起流程失敗，回到表單頁面後無法再發起流程或儲存表單」問題
  - 變更檔案: 1 個
- **2022-01-12 16:44:46**: [Web]A00-20220112001 只有在簽核關卡執行加簽才顯示簽核意見欄位；不是簽核意見的關卡，執行加簽時不顯示簽核意見欄位
  - 變更檔案: 1 個
- **2022-01-07 15:54:13**: [流程引擎]Q00-20220107008 修正流程主旨的結尾是「\」符號，取回工作重辦清單無法呈現
  - 變更檔案: 1 個
- **2021-11-12 15:40:12**: [流程設計師]Q00-*********** 修正XPDL轉BPMN流程發生閘道元件與流程關卡ID重複的問題，導致BPMN流程中的連接線連接錯誤
  - 變更檔案: 1 個
- **2021-11-11 14:05:22**: [Web]Q00-20211111002 使用FormUtil.setValue賦值給整數或浮點數Textbox欄位時，處理千分位及外顯值邏輯
  - 變更檔案: 1 個
- **2021-11-03 18:29:48**: [Web]Q00-20211020001 流程表單設定欄位必填時，若儲存表單時必填欄位尚未填寫，就彈出相關提示訊息
  - 變更檔案: 2 個
- **2022-02-08 18:03:20**: [流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過
  - 變更檔案: 2 個
- **2022-01-11 16:58:59**: [流程引擎]Q00-20220111002 修正多人關卡在執行自動簽核時，偶發的沒有押上簽核意見或簽核意見押到正常簽核的工作上的問題
  - 變更檔案: 1 個
- **2021-12-10 14:12:15**: [流程引擎]Q00-20211210001 修正流程執行退回重辦後，後續關卡設定為「2.與前一關同簽核者則跳過」的自動簽核失效問題
  - 變更檔案: 1 個
- **2021-11-03 16:25:30**: [流程引擎]Q00-20211103001 調整自動簽核邏輯，以人員任務(UserTask)的處理者判斷是否執行自動簽核
  - 變更檔案: 1 個
- **2022-02-11 18:14:38**: [流程引擎]Q00-20220211001 調整簡易流程圖預先解析，當前核決層級關卡的工作處理者為原處理者的代理人時，以原處理者解析後續關卡
  - 變更檔案: 1 個
- **2022-02-08 14:49:14**: [流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程
  - 變更檔案: 1 個
- **2021-12-20 16:56:24**: [流程引擎]Q00-20211118002 修正簡易流程圖中使用前置關係人做流程預先解析，導致流程圖與流程實際派送情形不一致[補]
  - 變更檔案: 1 個
- **2021-11-18 15:07:43**: [流程引擎]Q00-20211118002 修正簡易流程圖中使用前置關係人做流程預先解析，導致流程圖與流程實際派送情形不一致
  - 變更檔案: 1 個
- **2021-09-17 17:01:00**: [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題
  - 變更檔案: 6 個
- **2021-09-17 17:01:00**: [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題
  - 變更檔案: 6 個
- **2021-12-24 18:00:43**: [流程引擎]Q00-20211224002 調整取回重辦邏輯，只允許使用者從進行中的關卡執行取回重辦
  - 變更檔案: 1 個
- **2021-12-21 11:07:00**: [流程引擎]Q00-20211221001 修正透過WebService呼叫退回重辦時，被退回的關卡處理者未收到待辦事項通知信
  - 變更檔案: 1 個
- **2022-02-08 17:06:17**: [Web]Q00-20220208002 單身加總欄位設定「顯示至小數點後第X位」且在其他欄位的運算規則中，單身加總數值改變後沒有觸發欄位運算
  - 變更檔案: 2 個
- **2021-11-09 18:16:17**: [Web]A00-20211105001 調整JavaScript浮點數運算誤差造成單身加總計算結果不符預期
  - 變更檔案: 1 個
- **2021-10-05 17:21:52**: [Web]Q00-20210826001 修正「若有為null的資料時，無法載入資料至Grid」的問題[補]
  - 變更檔案: 1 個
- **2021-10-07 12:00:51**: [流程引擎]A00-20211006001 修正「部分流程無法產出作業流程書」的問題
  - 變更檔案: 1 個
- **2021-10-05 11:14:28**: [Web]A00-20211004001 修正多選自定義開窗在無資料的情況下，勾選表頭的「全選」選項時會帶出一筆空白資料
  - 變更檔案: 1 個
- **2021-09-30 16:04:06**: [流程引擎]Q00-20210930003 修正因有特製流程定義的資料，導致原本的流程定義刪除後無法再匯入相同流程
  - 變更檔案: 1 個
- **2021-10-28 18:14:16**: [Web]Q00-20211028004 移除追蹤流程與監控流程「已完成」、「已撤銷」、「已終止」清單中的「執行中的活動」欄位
  - 變更檔案: 1 個
- **2021-11-19 15:44:37**: [流程引擎]Q00-20211119001 修正表單選項元件的代號與名稱不同，導致額外輸入框的內容沒有被帶回到表單上
  - 變更檔案: 1 個
- **2021-10-19 18:00:40**: [流程引擎]Q00-20211019009 追蹤流程時，顯示表單欄位的背景顏色
  - 變更檔案: 5 個
- **2021-11-19 11:35:02**: [流程引擎]A00-20210908002 修正當表單選項元件勾選「額外產生出入框」且元件代號與名稱不同時，執行轉存表單失敗
  - 變更檔案: 1 個
- **2022-01-03 17:35:49**: [流程引擎]Q00-20220103004 修正流程主旨的結尾是「\」符號，系統通知(活動類型)通知清單無法呈現
  - 變更檔案: 2 個
- **2021-12-23 13:56:38**: [流程引擎]Q00-20211223001 修正流程主旨的結尾是「\」符號，工作通知清單無法呈現
  - 變更檔案: 1 個
- **2021-10-20 11:14:27**: [流程引擎]Q00-20211019004 調整DispatchActivityForAutoAgent排程，加入WITH NOLOCK指令
  - 變更檔案: 1 個
- **2022-02-25 16:45:50**: [流程引擎]Q00-20220208004 修正「已轉派的工作」清單，在「全部」頁籤取得的資料筆數與「處理中」、「已處理」兩個頁籤相加的數量不符
  - 變更檔案: 1 個
- **2021-10-20 14:50:46**: [Web]Q00-20211020003 當響應式表單的下拉式選單元件設定為動態生成選項時，列印表單無法顯示欄位值
  - 變更檔案: 1 個
- **2022-02-08 14:49:14**: [流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程
  - 變更檔案: 1 個
- **2022-02-10 17:51:10**: [ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態
  - 變更檔案: 1 個
- **2021-10-27 14:42:30**: [ESS]Q00-20211026002 調整BPM呼叫ESS存檔前的判斷，防止同單據在ESS與BPM狀態不一致
  - 變更檔案: 2 個
- **2021-10-26 17:01:31**: [ESS]Q00-20211026001 調整BPM發起ESS流程的邏輯，先檢查是否有整合ESS，再往下執行ESS相關的檢查
  - 變更檔案: 1 個
- **2021-09-24 18:25:51**: [ESS]Q00-20210924006 在呼叫ESS存檔前增加判斷，防止同單據在ESS與BPM狀態不一致
  - 變更檔案: 3 個
- **2021-12-15 14:48:07**: [流程引擎]A00-20211214001 修正若客戶流程進版後第一關的關卡ID與前一版不同，則無法於舊版流程實例使用「重發新流程」功能
  - 變更檔案: 1 個
- **2021-12-09 14:57:52**: [流程引擎]Q00-20211207002 調整若在登入頁面閒置一段時間，需要操作登入兩次才能登入BPM
  - 變更檔案: 2 個
- **2021-09-30 11:30:11**: [流程引擎]A00-20210927003 修正使用系統計時功能後，當使用者被登出BPM時，需要進行兩次登入操作才能進入BPM
  - 變更檔案: 1 個
- **2021-07-20 09:00:46**: [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能[補]
  - 變更檔案: 1 個
- **2021-07-08 17:23:53**: [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能
  - 變更檔案: 1 個
- **2021-09-03 18:14:46**: [流程引擎]Q00-20210903003 修正「發起流程時儲存表單後，表單的viewMode從『INVOKE』變成『PERFORM』」的問題
  - 變更檔案: 1 個
- **2021-08-27 18:46:59**: [Web]Q00-20210827001 調整DatabaseAccessor重組SQL指令的邏輯，防止在錯誤的地方插入where條件
  - 變更檔案: 1 個
- **2021-08-26 16:44:33**: [Web]Q00-20210826001 修正「若有為null的資料時，無法載入資料至Grid」的問題
  - 變更檔案: 1 個
- **2021-06-02 15:44:58**: [流程設計師]Q00-20210531001 修正「複製有連接線的關卡造成實際流程派送發生異常」的問題
  - 變更檔案: 2 個
- **2021-08-13 14:22:05**: [流程引擎]A00-*********** 修正簡易流程圖無法查看於核決層級內加簽的關卡的關卡資訊
  - 變更檔案: 1 個
- **2021-08-12 14:35:39**: [流程引擎]Q00-*********** 在加簽過程中加入條件判斷，只有「進行中」的工作才可以進行加簽
  - 變更檔案: 4 個
- **2021-08-03 17:12:24**: [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題[補]
  - 變更檔案: 1 個
- **2021-06-07 11:43:59**: [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題
  - 變更檔案: 1 個
- **2021-08-03 16:07:47**: [Web]A00-20210729001 SQLCommand資料庫為DBConnection元件，呼叫ajax query時找不到DB連線方式
  - 變更檔案: 1 個
- **2021-07-30 18:23:38**: [流程引擎]Q00-20210730002 修正關卡「只有一人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核[補]
  - 變更檔案: 1 個
- **2021-07-30 17:52:04**: [流程引擎]Q00-20210730002 修正關卡設定「只有一個人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核
  - 變更檔案: 1 個
- **2021-07-27 14:35:37**: [流程引擎]Q00-20210727001 修正因執行加簽關卡導致核決層級預先解析內容不正確
  - 變更檔案: 1 個
- **2021-07-23 17:20:36**: [Web]Q00-20210723001 修正當關卡表單權限設定為「唯讀」時，第一次點擊「儲存表單」後沒有再執行formOpen的問題
  - 變更檔案: 1 個
- **2021-07-21 17:25:33**: [流程引擎]Q00-20210721003 修正取回重辦後，簡易流程圖只顯示流程關卡，未顯示關卡處理者
  - 變更檔案: 1 個
- **2021-07-21 10:24:05**: [流程引擎]Q00-*********** 加快發起流程時表單開啟速度
  - 變更檔案: 7 個

### 林致帆 (68 commits)

- **2021-10-13 15:14:33**: [Web]A00-20211012002 修正使用者名稱有設定多語系，在帳號管理的頁面會有多筆該使用者的重複資料
  - 變更檔案: 1 個
- **2022-09-05 17:48:01**: [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
  - 變更檔案: 4 個
- **2022-05-05 11:00:24**: [Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢
  - 變更檔案: 1 個
- **2022-04-15 13:37:42**: [Web]Q00-20220414002 修正一般使用者追蹤流程清單匯出Excel報表，簽核時間欄位沒有值
  - 變更檔案: 1 個
- **2022-03-02 09:25:36**: [Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢[補修正]
  - 變更檔案: 1 個
- **2022-01-07 10:59:50**: [Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢
  - 變更檔案: 1 個
- **2022-03-21 16:51:49**: [ESS]Q00-20220321003修正ESS流程經過取回or退回重辦在服務任務關卡前，撈取表單實例序號錯誤導致繼續派送報錯
  - 變更檔案: 1 個
- **2022-03-21 10:51:48**: [Web]Q00-20220321001 修正絕對位置表單在第一關以外的關卡上傳附件按上傳後，開窗變空白
  - 變更檔案: 1 個
- **2022-02-16 18:22:42**: [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
  - 變更檔案: 2 個
- **2022-03-01 15:16:38**: [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
  - 變更檔案: 2 個
- **2022-01-19 15:35:52**: [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯
  - 變更檔案: 2 個
- **2022-04-15 10:15:20**: [流程引擎]Q00-20220415001 修正因多餘附件移除邏輯改成流程結案處理，導至流程無法結案
  - 變更檔案: 1 個
- **2021-08-13 17:05:11**: [內部]Q00-20210813003 修正"取得流程圖資料"接口的欄位performerId的內容不該為OID
  - 變更檔案: 1 個
- **2021-07-26 15:44:11**: [流程設計師]S00-20210305001 調整服務任務讀取https的WSDL，增加SSL憑證失敗的明確提示窗
  - 變更檔案: 6 個
- **2021-07-14 16:59:02**: [Web]S00-*********** 調整人員離職自動將帳號功能停用，復職則帳號功能啟用
  - 變更檔案: 6 個
- **2021-08-04 15:06:56**: [Web]S00-20200528001 優化簽核歷程的流程狀態"已處理"更改成"已同意"
  - 變更檔案: 1 個
- **2021-08-03 11:39:22**: [Web]S00-20210315001 增加轉派意見在待辦流程的主旨上 [補修正]
  - 變更檔案: 1 個
- **2021-08-02 14:26:11**: [Web]S00-20210315001 增加轉派意見在待辦流程的主旨上
  - 變更檔案: 2 個
- **2022-03-16 11:19:36**: [Web]S00-20220316001 新增整合暫存管理供未註冊的BPM使用
  - 變更檔案: 1 個
- **2022-01-26 10:41:27**: [Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單
  - 變更檔案: 1 個
- **2022-01-26 10:41:27**: [Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單
  - 變更檔案: 1 個
- **2022-03-14 17:46:46**: [流程引擎]Q00-20220314003 修正流程關卡下一關卡為多人關卡處理且設定自動簽核為"與前一關相同簽核者，則跳過"，繼續派送會失敗
  - 變更檔案: 1 個
- **2022-01-22 16:18:48**: [內部]Q00-20211115001新增GuardService連線成功的提示訊息
  - 變更檔案: 1 個
- **2021-12-27 10:54:32**: [Web]A00-20211223001 修正監控流程清單頁連結不開放給系統管理員以外的人員[補修正]
  - 變更檔案: 2 個
- **2021-12-23 14:20:05**: [Web]A00-20211223001 修正監控流程清單頁連結不開放給系統管理員以外的人員
  - 變更檔案: 2 個
- **2021-12-02 09:08:42**: [Web]A00-20211201002 修正追蹤流程點擊匯出Excel會報錯
  - 變更檔案: 1 個
- **2021-11-22 15:22:06**: [WorkFlow]Q00-***********修正WorkFlow在單據進行取消確認時，對該單據抽單，回傳的狀態碼有誤導致WorkFlow作業為待處理
  - 變更檔案: 7 個
- **2021-11-12 09:14:09**: [Web]Q00-20211112001修正，系統管理工具的資料來源設定是用Oracle且修改的欄位是Oracle服務名稱(SID)時，取得該資料來源資料會顯示原來的資訊而不是修改後的
  - 變更檔案: 1 個
- **2021-11-02 17:14:23**: [Web]Q00-20211102002 修正元件的label及元件在流程設計師設定invisible時，前端頁面報錯導致系統變數顯示內容異常
  - 變更檔案: 1 個
- **2022-02-08 16:08:27**: [流程引擎]A00-20220127001修正流程退回重辦選擇"按照流程定義依序重新執行"，關卡會經過"服務任務"會導致主旨的退回重辦標籤沒有顯示
  - 變更檔案: 1 個
- **2021-10-20 11:17:20**: [流程引擎]Q00-20211020002 修正流程關係人設定部門表單欄位，表單內容為[組織ID]部門ID，導致流程發起失敗
  - 變更檔案: 1 個
- **2021-12-08 17:40:19**: [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤[補修正]
  - 變更檔案: 2 個
- **2021-12-02 16:41:02**: [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤
  - 變更檔案: 3 個
- **2021-09-16 11:52:32**: [Web]Q00-***********調整簽核歷程及簡易流程圖流程狀態"已同意"調整回"已處理" 及 簡易流程圖 增加"已會辦"流程狀態
  - 變更檔案: 2 個
- **2021-10-19 14:23:08**: [組織設計師]Q00-***********修正組織設計師點選群組跟專案右側人員清單不應該顯示分頁鈕
  - 變更檔案: 1 個
- **2022-01-13 15:32:28**: [Web]A00-20220110001 修正流程關卡用預設代理人處理，若該關卡沒有預設代理人，不會出現提示訊息畫面[補修正]
  - 變更檔案: 1 個
- **2022-01-12 10:49:50**: [Web]A00-20220110001 修正流程關卡用預設代理人處理，若該關卡沒有預設代理人，不會出現提示訊息畫面
  - 變更檔案: 3 個
- **2021-10-15 17:21:59**: [Web]Q00-20211015004 修正Grid欄位如果沒有Binding其他欄位，會導致點擊Grid修改鈕的時候，該筆欄位資料會不見
  - 變更檔案: 1 個
- **2021-11-08 17:11:14**: [組織設計師]A00-20211027001 調整員工administrator點擊檢視員工資料再點編輯跟從修改員工資料的可編輯欄位要一致
  - 變更檔案: 1 個
- **2021-11-26 17:21:35**: [ESS]Q00-20211112002 修正ESS流程 A員工在 A電腦進行儲存草稿動作，A員工在B電腦打開該草稿時，ESS表單開啟報錯
  - 變更檔案: 1 個
- **2022-01-21 11:39:58**: [Web]A00-20220120001修正IE開起流程用SQLcommand因為用replaceall函式導致報錯
  - 變更檔案: 1 個
- **2021-12-16 11:56:50**: [Web]Q00-20211216001 修正SQLCommand的SQL指令帶有百分比及加號會導致報錯
  - 變更檔案: 2 個
- **2021-12-13 11:18:20**: [Web]A00-20211209001 調整離職維護作業的時間輸入欄位點擊應為時間開窗供使用者選擇
  - 變更檔案: 1 個
- **2022-01-18 11:22:09**: [Tiptop]Q00-20220118002修正Tiptop傳的Grid沒有內容時會產生空陣列在Grid上
  - 變更檔案: 1 個
- **2021-12-28 15:03:08**: [內部]Q00-20211228001調整易飛orWFERP整合主機設定兩台多主機時，會無法開單
  - 變更檔案: 1 個
- **2021-08-04 15:02:24**: [Web]S00-20210202001 簡易流程圖的流程狀態字眼明確化
  - 變更檔案: 3 個
- **2021-10-25 11:48:03**: [Web]A00-20211022001 調整系統管理員在監控流程只有選擇"未結案"，"全部"的流程狀態按鈕，才會在"更多"按鈕顯示撤銷流程
  - 變更檔案: 1 個
- **2021-09-16 14:39:55**: [Web]S00-20210318002 優化監控流程匯出Excel功能
  - 變更檔案: 2 個
- **2021-09-14 17:36:54**: [Web]A00-20210913001 修正從BPM首頁的待辦清單由第二筆簽核跳到下一筆，都會跳到流程清單的第一筆流程
  - 變更檔案: 1 個
- **2021-09-09 18:03:26**: [Web]A00-20210907001 修正先看ESS流程草稿後在點擊一般流程草稿會導致報錯
  - 變更檔案: 1 個
- **2021-09-08 18:15:18**: [Web]A00-20210908001 修正從待辦事項連結登入BPM後應該要為該流程的簽核頁面，而不是代辦清單頁面
  - 變更檔案: 1 個
- **2021-09-08 11:08:23**: [Web]S00-20210316001 流程筆數為1000時，呈現筆數為1000，不再用999+
  - 變更檔案: 1 個
- **2021-08-25 18:16:33**: [Web]A00-20210825002 修正使用者用IE11登入，線上人數查詢登入裝置資訊為IE7.0
  - 變更檔案: 1 個
- **2021-08-18 18:19:37**: [流程引擎]	Q00-20210818002修正SQLcommand放的SQL指令有簡體字，會造成base64加密報錯
  - 變更檔案: 2 個
- **2021-08-17 16:49:17**: [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
  - 變更檔案: 1 個
- **2021-08-16 16:12:38**: [流程引擎]S00-20210113002調整流程前一關為服務任務，派送到下一關主旨會以下一關處理者的預設語系[補修正]
  - 變更檔案: 1 個
- **2021-08-12 17:29:10**: [流程引擎]S00-20210113002調整流程前一關為服務任務，派送到下一關主旨會以下一關處理者的預設語系
  - 變更檔案: 1 個
- **2021-08-09 10:49:07**: [TIPTOP]Q00-20210511004 修正TIPTOP拋單回傳給TIPTOP失敗，流程可發起成功[補修正]
  - 變更檔案: 1 個
- **2021-08-06 16:23:26**: [流程設計師]S00-20210318004 修正連接線條件式輸入空格，顯示上會被替換成空字串
  - 變更檔案: 1 個
- **2021-08-03 17:18:10**: [內部]Q00-20210803002優化log訊息：T100拋單時，取得附件的檔案編碼ID以及文檔中心的URL沒有設定
  - 變更檔案: 1 個
- **2021-07-29 14:38:49**: [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
  - 變更檔案: 1 個
- **2021-07-26 18:08:39**: [Web]A00-20210726003修正ajax_CommonAccessor的findXmlContent,findResource接口取得內容為中文亂碼
  - 變更檔案: 1 個
- **2021-07-26 14:35:15**: [內部]Q00-20210726001 DatabaseAccessor 移除不需要的System.out.print方法
  - 變更檔案: 1 個
- **2021-07-23 18:17:49**: [Web]A00-20210720001 修正絕對位置表單在追蹤流程頁面，表單範圍外的元件顯示出來
  - 變更檔案: 1 個
- **2021-07-20 14:57:22**: [Web]C01-20210706002 修正流程第二關設置radiobutton為invisible狀態，第二關簽核後該元件內容會消失
  - 變更檔案: 1 個
- **2021-07-13 14:03:42**: [Web]Q00-20210713002修正表單頁籤簽核歷程置放位置選擇"top"且表單設計師設定"顯示流程簽核意見"為"NOT_SHOW"，待辦跟發起畫面的ESS表單上方會顯示"簽核意見"的文字
  - 變更檔案: 1 個
- **2021-07-13 10:47:09**: [Web]Q00-20210702002修正流程最後一關是通知任務，流程設計師在該關卡增加BasicType的流程變數到工具定義表，導致查看流程異常
  - 變更檔案: 1 個
- **2021-07-08 18:56:05**: [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法
  - 變更檔案: 2 個

### 王鵬程 (66 commits)

- **2022-01-14 17:22:37**: [Web]S00-20211124001 提供行動版在發起流程頁面可調整流程緊急度
  - 變更檔案: 1 個
- **2021-10-27 18:13:43**: [流程引擎]S00-20210701002 追蹤流程頁面的進階查詢中增加簽核時間欄位條件
  - 變更檔案: 7 個
- **2022-07-13 16:33:02**: [Web]Q00-20220713003 修正在行動版面中，在表單內向下滑動時，右下角的浮動按鈕會隱藏而無法後續操作
  - 變更檔案: 1 個
- **2022-06-13 16:14:20**: [Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾
  - 變更檔案: 1 個
- **2022-04-20 18:24:03**: [Web]Q00-20220420003 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系但實際的標題未呈現多語系內容
  - 變更檔案: 1 個
- **2022-04-27 16:50:57**: [Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色
  - 變更檔案: 1 個
- **2022-02-18 17:28:24**: [Web]A00-20220216002 修正RWD表單當右下角有出現滑到頂部按鈕時，按鈕也會被列印出來
  - 變更檔案: 1 個
- **2021-12-06 15:01:28**: [Web]A00-20211201001 修正Rwd表單有設定元件的背景色、文字顏色時，在列印時無法印出色彩[補]
  - 變更檔案: 2 個
- **2021-12-03 14:47:30**: [Web]A00-20211201001 修正Rwd表單有設定元件的背景色、文字顏色時，在列印時無法印出色彩
  - 變更檔案: 2 個
- **2022-03-23 16:59:15**: [流程引擎]A00-20220322002 修正SQL註冊器中語法有使用到order by 會導致報錯
  - 變更檔案: 1 個
- **2022-01-20 18:23:35**: [Web]Q00-20220120003 流程代理人設定的選擇流程開窗，預設用流程代號做排序
  - 變更檔案: 1 個
- **2022-01-14 11:59:53**: [Web]V00-20220113001 修正系統排程設定的編輯工作觸發程序中選擇每天、每週、每月進去的時間下拉元件太窄導致內容無法完整顯示
  - 變更檔案: 2 個
- **2022-01-06 14:52:01**: [流程設計師]Q00-20220106008 修正在流程定義視窗中選擇標頭，勾選是否逾時多次通知後在進來該視窗都會變未勾選
  - 變更檔案: 1 個
- **2021-12-17 14:15:34**: [流程引擎]Q00-20211217001 修正當資料庫為oracle時，SQL註冊器未輸入任何條件查詢會報錯
  - 變更檔案: 1 個
- **2021-12-16 18:24:59**: [Web]Q00-20211216004 修正新增排程的頁面中，排程生效時間下拉元件太窄導致內容無法完整顯示
  - 變更檔案: 1 個
- **2021-12-07 17:03:09**: [Web]Q00-20211207001 修正CSS的樣式缺少右大擴號的錯誤導致寫在後面的CSS無法生效
  - 變更檔案: 1 個
- **2021-12-01 15:08:38**: [流程引擎]Q00-20211201002  修正人員名稱有新的特殊字(慈)時，進入ESS流程會報錯
  - 變更檔案: 1 個
- **2021-11-25 17:43:13**: [流程引擎]Q00-20211125001 修正透過HR小助手同步，當人員的兼職部門直屬主管在HR那已設空值，同步後卻未被改成空值[補]
  - 變更檔案: 2 個
- **2021-11-25 15:47:34**: [流程引擎]Q00-20211125001 修正透過HR小助手同步，當人員的兼職部門直屬主管在HR那已設空值，同步後卻未被改成空值
  - 變更檔案: 4 個
- **2021-11-11 15:10:45**: [Web]Q00-20211111004 修正流程有設定列印模式，且有將流程設置在iReport時，點擊上方『列印表單』後流程無法繼續派送
  - 變更檔案: 1 個
- **2021-10-28 14:50:20**: [Web]Q00-20211028003 修正活動關卡中掛載網頁應用程式，在IE中點上面的更多按鈕，出現的選單會被遮蔽而無法點選
  - 變更檔案: 1 個
- **2021-10-13 17:01:42**: [Web]Q00-20211013003 修正當ListBox從設定選項中只有一個列時並刪除列，再改成從資料庫帶值，儲存表單會出現錯誤
  - 變更檔案: 1 個
- **2022-02-24 16:16:23**: [Web]Q00-20220224001 修正維護樣板作業從PC版切換到行動版時資料無法顯示
  - 變更檔案: 1 個
- **2022-02-15 17:36:01**: [Web]Q00-20220215002 調整讓行動版與PC版一致讓Grid只支援(a、br、input、i、button)五種html標籤
  - 變更檔案: 1 個
- **2021-10-19 16:32:31**: [Web]Q00-20211015001 調整讓Grid支援使用<button>
  - 變更檔案: 1 個
- **2021-11-24 18:03:19**: [Web]Q00-20211124001 修正一般使用者在追蹤流程頁面中將『更多』按鈕隱藏
  - 變更檔案: 1 個
- **2021-12-16 17:48:30**: [Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字
  - 變更檔案: 3 個
- **2021-10-20 15:53:10**: [流程引擎]Q00-20211020004 修正絕對表單Grid內有 左、右中括號及單引號，轉存的Grid資料表的資料未能正確呈現符號
  - 變更檔案: 1 個
- **2021-12-22 15:21:26**: [Web]A00-20211220001 修正在行動版時在我的最愛內的常用流程與常用功能維護無法儲存
  - 變更檔案: 2 個
- **2022-01-07 17:57:59**: [Web]Q00-20220107009 調整行動版時左側的選單背景色及字體顏色也要與PC版一致
  - 變更檔案: 1 個
- **2022-01-06 11:47:10**: [Web]Q00-20220106001 調整加簽關卡的頁面中，關卡名稱允許單字之間使用空格
  - 變更檔案: 2 個
- **2021-09-30 17:55:12**: [Web]Q00-20210930005 修正使用SQLcommand時指令帶有中文會導致造成base64加密報錯
  - 變更檔案: 2 個
- **2021-11-16 18:11:35**: [Web]Q00-20211116002修正RWD列印表單開窗中，點下列印表單後，有選到的radio和checobox都會產生類似殘影的樣子
  - 變更檔案: 1 個
- **2021-12-27 15:24:57**: [Web]Q00-20211227001 修正使用IE在絕對表單沒有附件時預覽列印，會導致簽核歷程和表單內容重疊
  - 變更檔案: 1 個
- **2021-10-06 15:46:55**: [Web]A00-20211001002 修正絕對位置表單當附件的描述過長時，會導致預覽列印時出現附件資訊和簽核歷程重疊
  - 變更檔案: 1 個
- **2022-02-24 14:47:46**: [Web]S00-20211117003 流程資料的頁面排序調整以發起時間大到小排序(DESC)
  - 變更檔案: 1 個
- **2021-09-16 14:16:21**: [Web]Q00-20210916002 調整退回重辦視窗中confirm按確認後，不再提示成功訊息，避免使用者按下右上角關閉導致表單未刷新頁面
  - 變更檔案: 1 個
- **2021-09-14 18:16:53**: [Web]Q00-20210914003 調整修改密碼的彈出視窗較小，導致按確認後alert的按鈕不能直接點選
  - 變更檔案: 1 個
- **2021-09-14 14:16:32**: [Web]A00-20210906001 修正絕對位置表單日期元件勾選顯示時間並比對另一個日期，條件設==，則欄位上時和分會歸零
  - 變更檔案: 1 個
- **2021-09-13 18:22:14**: [Web]A00-20210909001 修正在PC版表單設計師中更改Grid代號，在切換到行動版表單，畫面上顯示的Grid代號未改變
  - 變更檔案: 1 個
- **2021-09-08 16:10:32**: [Web]Q00-20210908002 修正在一些https環境下會無法下載檔名有中文的檔案
  - 變更檔案: 1 個
- **2021-09-06 18:06:23**: [Web]Q00-20210906003 修正流程主旨允許用html並有輸入tr或tbody的tag，導致在BPM首頁中無法從下方待辦進入流程
  - 變更檔案: 1 個
- **2021-09-03 17:56:00**: [Web]Q00-20210903002 調整模組中的程式開啟方式是設定另開新視窗時，使可以開啟多個不同程式名稱的視窗
  - 變更檔案: 1 個
- **2021-09-01 18:39:24**: [Web]A00-20210830001 修正允許流程主旨使用html時，在行動版下主旨無法呈現html的樣式
  - 變更檔案: 1 個
- **2021-08-30 11:59:38**: [Web]Q00-20210830001 修正在行動版下，選擇退回重辦彈出來的視窗中，無法選擇要退回的關卡
  - 變更檔案: 1 個
- **2021-08-27 11:45:39**: [Web]A00-20210826001 修正退回重辦時簽核意見有換行，導致流程圖中檢視參與者型式的關卡頁面的工作列表無法出現
  - 變更檔案: 2 個
- **2021-08-19 18:10:49**: [Web]Q00-20210819005 修正使用singleOpenWin開窗，搜尋條件使用簡體字會搜尋不到資料
  - 變更檔案: 1 個
- **2021-08-18 11:57:45**: [Web]Q00-20210818001 調整當瀏覽器封鎖BPM的彈窗時，在使用模擬模式進入監控或是進入待辦提示使用者開啟設定的訊息內容
  - 變更檔案: 2 個
- **2021-08-17 18:35:42**: [Web]Q00-20210817002 修正當瀏覽器封鎖BPM站台的彈窗時，模擬使用者後進入待辦流程中會提示使用者去設定允許彈窗
  - 變更檔案: 1 個
- **2021-08-17 17:51:50**: [Web]Q00-20210817001 修正當瀏覽器封鎖BPM站台的彈窗時，且勾選啟動流程測試模式進入監控流程，會提示使用者去設定允許的彈窗
  - 變更檔案: 1 個
- **2021-08-10 18:02:37**: [Web]Q00-*********** 修正關卡通知信設定以整張表單時，checkbox元件在信件上呈現應該要為顯示值
  - 變更檔案: 1 個
- **2021-08-05 18:21:30**: [Web]A00-20210804003 修正在行動版畫面的監控流程清單中每列的主旨未對齊
  - 變更檔案: 1 個
- **2021-08-05 17:09:47**: [Web]Q00-20210805002 修正關卡通知信設定以整張表單時，dropdown元件在信件上呈現應該要為顯示值
  - 變更檔案: 1 個
- **2021-08-03 18:16:51**: [Web]Q00-20210803003 修正因之前議題的修正導致移動端功能『入口平台整合設定』頁面出現錯誤
  - 變更檔案: 1 個
- **2021-07-30 10:48:01**: [Web]Q00-20210730001 修正Grid設小螢幕使用名片式並綁定Textarea，在行動版下有輸入換行，Grid呈現應該要是沒換行
  - 變更檔案: 1 個
- **2021-07-28 18:11:54**: [Web]Q00-20210728001 修正透過URL開啟『追蹤流程實例內單一表單資料』，在行動版時未顯示表單名稱
  - 變更檔案: 1 個
- **2021-07-28 15:30:18**: [Web]A00-20210726005 修正表單是絕對表單時，透過URL開啟『追蹤流程實例內單一表單資料』，在行動裝置版面下附件按鈕沒有字樣
  - 變更檔案: 1 個
- **2021-07-27 18:08:51**: [Web]A00-20210727002 修正當Grid有設定將最前面欄位設為流水號時，用流水號排序時應該用數字大小來排序
  - 變更檔案: 1 個
- **2021-07-27 17:11:59**: [Web]A00-20210726002 修正行動版下，從追蹤進去流程且該流程有被同個人簽核多次過，在取回清單頁面中下拉選單會出現奇怪文字
  - 變更檔案: 1 個
- **2021-07-26 18:20:13**: [Web]A00-20210726001 修正關卡有勾選允許批次簽核，在行動版畫面的待辦事項清單中每列的主旨未對齊
  - 變更檔案: 1 個
- **2021-07-21 16:53:57**: [Web]Q00-20210721002 修正Grid綁textarea並輸入換行，畫面縮成mobile再到PC，Grid中資料會變成未換行
  - 變更檔案: 1 個
- **2021-07-20 19:54:00**: [Web]Q00-*********** 修正Grid綁定textarea並輸入換行，點擊排序後再點選Row，帶回textarea會出現<br>
  - 變更檔案: 1 個
- **2021-07-16 16:11:45**: [Web]Q00-20210716002 修正Grid綁定check、radio且設額外產生輸入框，通知信設定以表單元件時通知信的Grid會跑版
  - 變更檔案: 1 個
- **2021-07-14 16:33:22**: [Web]Q00-20210714001 修正Grid有綁定Checkbox時，關卡通知信設定以表單元件時，通知信的Grid會跑版
  - 變更檔案: 1 個
- **2021-07-13 12:08:30**: [Web]Q00-20210713001  修正在首頁模組中的追蹤流程區塊，進入ESSF03表單會被截斷一半
  - 變更檔案: 1 個
- **2021-07-08 19:50:15**: [Web]Q00-20210708003 修正將Grid調整為可支援換行標籤<br>
  - 變更檔案: 1 個

### walter_wu (42 commits)

- **2022-07-29 00:04:37**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
  - 變更檔案: 3 個
- **2022-06-28 18:06:01**: [Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久
  - 變更檔案: 1 個
- **2022-05-30 15:34:30**: [內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方
  - 變更檔案: 1 個
- **2022-05-23 18:19:20**: [內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)
  - 變更檔案: 1 個
- **2022-07-13 18:23:20**: [流程引擎]Q00-20220713005 修正因為取/退回後再次進入核決層級導致核決定義重長，舊(退/取回前)的核決實例找不到定義報錯無法派送
  - 變更檔案: 1 個
- **2022-06-10 18:10:24**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
  - 變更檔案: 1 個
- **2022-06-13 16:07:01**: [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯
  - 變更檔案: 1 個
- **2022-04-19 16:50:47**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
  - 變更檔案: 1 個
- **2022-04-19 10:45:07**: [Web]Q00-20220419001 修正如果切換讀取較久的頁面視覺上會跑一半又呈現原畫面再跳轉
  - 變更檔案: 1 個
- **2022-04-27 15:00:41**: [內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤
  - 變更檔案: 1 個
- **2022-03-24 17:25:19**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出
  - 變更檔案: 1 個
- **2021-09-29 18:06:36**: [Web]Q00-20210928002 優化追蹤流程通知信URL進入速度
  - 變更檔案: 1 個
- **2021-11-11 18:47:13**: [內部]A00-20210820001 在判斷可退回關卡邏輯處加上線的定義詳細Log，方便之後排查
  - 變更檔案: 1 個
- **2021-11-10 16:55:45**: [Web]A00-20210922002 預覽流程圖關卡點開彈窗Table新增顯示  接收者  欄位
  - 變更檔案: 1 個
- **2021-11-04 17:21:00**: [流程引擎]Q00-20211104003 修正簡易流程圖無法查看於核決層級內加簽的"多人"關卡的關卡資訊
  - 變更檔案: 1 個
- **2021-09-30 17:47:37**: [流程引擎]A00-20210913002 增加判斷是否因為代理人導致觸發自動簽核並處理相關邏輯
  - 變更檔案: 1 個
- **2021-10-22 16:08:12**: [Web]Q00-20211022001 修正部分情境前端沒有傳入登入方式，會出現設定LDAP驗證卻走系統驗證
  - 變更檔案: 1 個
- **2021-11-09 17:01:29**: [流程引擎]Q00-20211109003 修正流程如果設定關係人的部門從表單欄位(選兼職部門)，發起時儲存表單後預解析卻解析主部門
  - 變更檔案: 1 個
- **2021-09-24 17:22:33**: [Web]A00-20210906002 修正如果核決層級參考的關卡被代理過預解析會有異常
  - 變更檔案: 2 個
- **2021-10-14 15:54:16**: [流程引擎]Q00-20211014003 修正加簽有異常卻未將原始錯誤印出導致出錯無法排查
  - 變更檔案: 1 個
- **2021-06-04 16:42:53**: [ESS]A00-20210521002 修正ESS刪除EFGP缺席紀錄的session bean無作用
  - 變更檔案: 3 個
- **2022-03-03 16:11:02**: [流程引擎]Q00-20220215001 修正偶發附件遺失問題[補修正]
  - 變更檔案: 1 個
- **2022-02-15 16:11:04**: [流程引擎]Q00-20220215001 修正偶發附件遺失問題
  - 變更檔案: 3 個
- **2022-01-10 17:19:15**: [流程引擎]Q00-20211220002 修正客戶附件遺失問題
  - 變更檔案: 2 個
- **2021-12-15 16:13:27**: [SQLCommand]Q00-20211215001 修正用Ajax下SQL如果欄位行態是text無法找出資料
  - 變更檔案: 1 個
- **2021-11-05 15:52:57**: [內部]Q00-20211105002 調整列印龐大Grid位置(絕對位置表單,依設定)
  - 變更檔案: 1 個
- **2022-02-17 22:58:33**: [Web]A00-20220214001 修正附件權限設定關卡在追蹤流程看不到的問題
  - 變更檔案: 1 個
- **2022-01-18 17:41:26**: [Web]A00-20211222001 修正離職作業維護選擇User後修改組織資料資料卻無法正確呈現與修改直屬主管異常
  - 變更檔案: 1 個
- **2022-03-04 18:24:11**: [登入]Q00-20220304002 調整登入加密機制，避免後端session失效時取不到值登入失敗
  - 變更檔案: 6 個
- **2021-09-17 11:37:10**: Q00-20210917001 增加防呆如果自己設定為自己的主管，在用核決層級參考預解析時會導致無窮迴圈
  - 變更檔案: 1 個
- **2021-09-10 17:14:17**: [Web]Q00-20210910003 修正如果再Orderby使用表別名Oracle會報錯
  - 變更檔案: 1 個
- **2021-09-10 11:54:44**: [流程引擎]Q00-20210727002 修正因為關卡設定自動跳關導致代理機制異常[補修正]
  - 變更檔案: 1 個
- **2021-09-09 09:24:32**: [Web]Q00-20210909001 修正加簽後，在流程圖預覽無法看到加簽關卡
  - 變更檔案: 1 個
- **2021-09-08 17:04:07**: [WebService]Q00-20210908003 修正DotJ登入，所記錄的使用者登入資訊沒有加上Locale導致寫入DB時報錯
  - 變更檔案: 1 個
- **2021-09-07 13:53:07**: [流程引擎]Q00-20210907003 修正發起時就算儲存表單，核決層級預解析因為沒有抓到設定的表單欄位而無法解析
  - 變更檔案: 1 個
- **2021-09-01 17:25:55**: [流程引擎]A00-20210901001 修正客戶Grid資料過多導致SQL組過複雜導致DB報錯
  - 變更檔案: 1 個
- **2021-08-16 13:48:13**: [流程引擎]Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回[補修正]
  - 變更檔案: 1 個
- **2021-08-13 19:23:24**: Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回
  - 變更檔案: 1 個
- **2021-08-10 11:05:43**: [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
  - 變更檔案: 1 個
- **2021-07-27 16:50:00**: [Web]A00-20210727001 修正XPDL的流程在監控流程中跳過關卡時，驗證密碼視窗會一片空白
  - 變更檔案: 1 個
- **2021-07-27 15:57:42**: [流程引擎]Q00-20210727002 修正因為關卡設定自動跳關導致代理機制異常
  - 變更檔案: 1 個
- **2021-07-13 10:30:47**: A00-20210630001 修正設定多語系後沒有設定的語系無法吃到預設值
  - 變更檔案: 1 個

### cherryliao (26 commits)

- **2021-12-23 16:46:39**: [流程引擎]S00-20210511001 新增追蹤流程列表可依流程結案時間做排序
  - 變更檔案: 2 個
- **2021-11-05 17:35:51**: [Web]S00-20210503004 調整Web端加簽畫面新增經常選取對象
  - 變更檔案: 4 個
- **2021-10-13 18:22:47**: [BPM APP]Q00-20210913001 調整行動端上傳附件發生錯誤時前端回應資訊錯誤的問題[補]
  - 變更檔案: 1 個
- **2021-10-13 14:50:53**: [BPM APP]Q00-20211005005 修正行動端詳情表單在操作Grid新增、編輯或取消按鈕時需要點擊兩次才會觸發動作的問題[補]
  - 變更檔案: 1 個
- **2021-10-06 15:03:46**: [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能[補]
  - 變更檔案: 2 個
- **2021-09-13 18:14:23**: [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能
  - 變更檔案: 4 個
- **2021-10-12 16:24:46**: [BPM APP]Q00-20210913003 調整上傳附件時loading圖示顯示的時機
  - 變更檔案: 3 個
- **2021-10-08 18:37:31**: [BPM APP]Q00-20211005005 修正行動端詳情表單在操作Grid新增、編輯或取消按鈕時需要點擊兩次才會觸發動作的問題
  - 變更檔案: 1 個
- **2021-10-08 14:50:33**: [BPM APP]Q00-20210913001 調整行動端上傳附件發生錯誤時前端回應資訊錯誤的問題
  - 變更檔案: 3 個
- **2021-10-06 15:03:46**: [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能[補]
  - 變更檔案: 2 個
- **2021-08-30 15:43:46**: [內部]S00-*********** 調整開發者工具頁面新增清除快取資料功能
  - 變更檔案: 6 個
- **2021-08-04 17:29:32**: [系統管理工具]S00-20200616002 調整點選線上使用者的通知，若使用者已登出時會跳出的錯誤訊息
  - 變更檔案: 6 個
- **2021-08-06 17:08:58**: [Web]A00-20210806001 修正日期和時間元件設定多語系提示文字，但只顯示預設值的問題
  - 變更檔案: 1 個
- **2021-07-21 10:52:41**: [表單設計師]S00-20200917002 調整表單設計師Gird勾選最前面欄位設為自動增加流水號時新增一列序號欄位
  - 變更檔案: 3 個
- **2021-07-27 14:24:25**: [表單設計師]S00-20200716001 新增日期和時間元件預設值配置功能[補]
  - 變更檔案: 5 個
- **2021-07-13 18:22:29**: [表單設計師]S00-20200716001 新增日期和時間元件預設值配置功能
  - 變更檔案: 7 個
- **2021-07-19 17:54:22**: [表單設計師]S00-20200716003 增加TextBox數字轉文字功能可設定將文字呈現於另一個TextBox欄位中
  - 變更檔案: 17 個
- **2021-12-20 13:54:48**: [Web]S00-20210914003 調整BPM登入頁面可用瀏覽器的儲存密碼功能
  - 變更檔案: 1 個
- **2021-10-28 11:17:11**: [Web]S00-20210709002 調整Web加簽頁面樣式
  - 變更檔案: 3 個
- **2021-09-16 13:37:04**: [流程引擎]S00-20210730004 調整Textbox設定浮點數、顯示千分位和小數點幾位時binding到Grid沒有千分位的問題
  - 變更檔案: 2 個
- **2021-09-13 17:18:08**: [Web]S00-20210902001 優化Web端表單E10子單身呈現樣式
  - 變更檔案: 3 個
- **2021-08-06 16:00:02**: [Dot.J]S00-20201124001 調整當設定檔找不到Secure時，設定Secure為false避免ECP呼叫DotJIntegration溝通時發生異常
  - 變更檔案: 1 個
- **2021-08-04 14:23:10**: [流程引擎]S00-20201118001 調整當流程關卡中有工作被轉派給代理人處理且流程通知設為結案逐級通知時通知原處理者
  - 變更檔案: 1 個
- **2021-07-29 15:44:18**: [組織設計師]S00-20210506001 調整設定流程代理人時不顯示已失效的流程
  - 變更檔案: 3 個
- **2021-07-13 18:41:33**: [Web]S00-20200821001 調整表單TextBox元件於可編輯模式下onblur時檢查資料型態
  - 變更檔案: 2 個
- **2021-07-09 11:20:44**: [Web]Q00-20210709001 修正checkbox、radio元件已選擇新樣式的問題
  - 變更檔案: 2 個

### yamiyeh10 (40 commits)

- **2021-11-25 14:40:53**: [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]
  - 變更檔案: 16 個
- **2021-11-18 15:20:57**: [BPM APP]Q00-20211109002 修正在鼎捷移動上開啟草稿流程中的任一條流程會發生取得表單資訊錯誤的問題[補]
  - 變更檔案: 1 個
- **2021-12-21 18:24:59**: [BPM APP]C01-20211108003 調整行動端儲存草稿一律必填草稿流程主旨[補]
  - 變更檔案: 2 個
- **2021-11-22 14:53:20**: [BPM APP]C01-20211108003 調整行動端儲存草稿一律必填草稿流程主旨
  - 變更檔案: 5 個
- **2021-11-17 14:39:01**: [BPM APP]Q00-20211109002 修正在鼎捷移動上開啟草稿流程中的任一條流程會發生取得表單資訊錯誤的問題
  - 變更檔案: 1 個
- **2021-11-09 18:16:08**: [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題
  - 變更檔案: 4 個
- **2021-11-10 17:19:34**: [BPM APP]Q00-20211110002 修正在IMG從草稿流程進入的表單是走發起流程而不是從草稿進入發起流程
  - 變更檔案: 1 個
- **2021-11-09 13:38:11**: [BPM APP]C01-20211108002 修正行動端從草稿流程進入的表單多呼叫formCreate方法導致畫面異常
  - 變更檔案: 2 個
- **2022-05-26 09:50:36**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-16 15:15:57**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
  - 變更檔案: 1 個
- **2022-05-10 18:03:59**: [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題
  - 變更檔案: 1 個
- **2021-09-08 15:14:14**: [BPM APP]Q00-20210907005 修正Line推播訊息在選項元件有設定額外輸入框且有值時不會顯示問題
  - 變更檔案: 1 個
- **2021-10-13 17:22:02**: [BPM APP]Q00-20210831001 修正當流程設定表單欄位為唯讀時，部分元件設定setValue異常問題[補]
  - 變更檔案: 1 個
- **2021-10-13 10:41:18**: [BPM APP]Q00-20211012002 調整行動端詳情頁面的日期與時間元件在iOS 15下會跑版問題
  - 變更檔案: 1 個
- **2021-10-12 14:12:15**: [BPM APP]優化移動表單的顯示流程樣式[補]
  - 變更檔案: 1 個
- **2021-10-06 15:55:33**: [BPM APP]Q00-20211005002 將行動端詳情頁面的發送通知中選擇人員畫面上浮動按鈕調整新樣式
  - 變更檔案: 5 個
- **2021-10-06 14:48:18**: [BPM APP]Q00-20210804003 修正行動端表單當流程設定多人且只需一位處理時不會顯示彈出視窗詢問是否要接收並派送訊息問題
  - 變更檔案: 2 個
- **2021-10-06 12:07:46**: [BPM APP]Q00-20210915002 修正行動端E10表單當Grid有多筆單身資料且其中有子單身無資料時會出現錯誤訊息問題
  - 變更檔案: 2 個
- **2021-10-05 16:23:23**: [BPM APP]Q00-20210823001 修正行動端表單當退回重辦必填簽核意見時彈窗沒有遮罩問題
  - 變更檔案: 2 個
- **2021-10-05 14:23:58**: [BPM APP]Q00-20210910002 將行動端詳情頁面的加簽選擇人員畫面上浮動按鈕調整新樣式[補]
  - 變更檔案: 4 個
- **2021-10-05 13:45:07**: [BPM APP]Q00-20210910001 將行動端詳情頁面的轉由他人處理中選擇人員畫面上浮動按鈕調整新樣式
  - 變更檔案: 2 個
- **2021-10-05 11:56:33**: [BPM APP]Q00-20210910002 將行動端詳情頁面的加簽選擇人員畫面上浮動按鈕調整新樣式
  - 變更檔案: 5 個
- **2021-10-05 11:25:49**: [BPM APP]Q00-20210913004 修正在企業微信的追蹤已簽核附件頁面中返回按鈕沒有反應問題
  - 變更檔案: 1 個
- **2021-09-01 16:29:16**: [BPM APP]調整行動端詳情表單的簡易簽核歷程可依系統變數設定是否要顯示全部資料功能
  - 變更檔案: 7 個
- **2021-08-23 17:31:08**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 4 個
- **2021-08-19 17:37:43**: [BPM APP]優化移動表單的顯示流程樣式
  - 變更檔案: 26 個
- **2021-08-16 14:04:57**: [BPM APP]Q00-20210816001 修正IMG詳情頁面中的顯示流程標題沒有多語系問題
  - 變更檔案: 1 個
- **2021-08-11 17:25:53**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 5 個
- **2021-08-04 10:45:21**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 2 個
- **2021-07-30 15:08:51**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 1 個
- **2021-07-30 14:30:37**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 15 個
- **2021-07-29 16:18:32**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 10 個
- **2021-07-28 10:33:55**: [BPM APP]優化移動表單的簽核歷程樣式
  - 變更檔案: 7 個
- **2021-10-07 09:48:21**: [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題[補]
  - 變更檔案: 1 個
- **2021-10-07 08:58:08**: [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題[補]
  - 變更檔案: 1 個
- **2021-12-09 16:17:22**: [Web]Q00-20211209001 調整Grid綁定Checkbox與Radio元件時增加判斷元件是否存在機制防止發生找不到元件的問題
  - 變更檔案: 1 個
- **2022-03-08 10:08:13**: [BPM APP]C01-20220224004 修正在取得IMG動態生成表單應用資料與取得綁訂使用者資料時會偶發statement close問題
  - 變更檔案: 1 個
- **2021-09-08 16:51:33**: [BPM APP]Q00-20210907005 修正Line推播訊息在Textbox有設定顯示千分位時沒顯示問題
  - 變更檔案: 1 個
- **2021-08-11 10:47:21**: [BPM APP]Q00-*********** 修正使用Line官方帳號登入的BPM,直接點擊ESSQ表單類會空白的問題
  - 變更檔案: 1 個
- **2021-07-20 11:18:20**: [BPM APP]C01-20210714002 修正IMG詳情頁面的簽核歷程若前面存在與當前關卡相同關卡id時不會顯示問題
  - 變更檔案: 1 個

### wayne (6 commits)

- **2022-04-14 09:58:18**: [內部] 調整瀏覽器閒置過久，請重新登入的多語系
  - 變更檔案: 2 個
- **2022-04-22 14:50:50**: [流程引擎]V00-20220420001 修正流程定義當核決關卡參考自定義關卡屬性時，流程派送到核決關卡時會無法正常派送
  - 變更檔案: 1 個
- **2022-05-12 16:55:22**: [Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」
  - 變更檔案: 2 個
- **2022-03-31 13:51:04**: [Web]A00-20220330001 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系，但實際操作沒有呈現多語系的內容
  - 變更檔案: 1 個
- **2022-03-15 14:01:56**: [WEB]Q00-20220315002 修正舊版表單InputElement若沒有textValue屬性時，儲存表單會發生錯誤
  - 變更檔案: 1 個
- **2022-03-15 14:10:49**: [WEB]A00-20220216001 修正追蹤流程-已轉派的工作，點進表單後再返回清單頁沒有保留原本的查詢條件
  - 變更檔案: 2 個

### shihya_yu (16 commits)

- **2021-10-12 17:10:18**: [BPM APP] 新增簽核流設計師中活動表單元件權限，行動端可複製Web端設定功能[補]
  - 變更檔案: 10 個
- **2021-10-12 17:05:51**: [內部]Q00-20211005001 修正簽核流設計師中，行動端複製PC端設定有不同步的狀況
  - 變更檔案: 2 個
- **2021-09-17 13:38:01**: [BPM APP] 新增簽核流設計師中活動表單元件權限，行動端可複製Web端設定功能
  - 變更檔案: 14 個
- **2021-10-12 17:05:51**: [內部]Q00-20211005001 修正簽核流設計師中，行動端複製PC端設定有不同步的狀況
  - 變更檔案: 2 個
- **2021-10-08 18:15:39**: [BPM APP]Q00-20211005004 修正左上方IMG的返回按鈕，會偶發沒顯示問題
  - 變更檔案: 6 個
- **2021-10-08 14:12:53**: [BPM APP]Q00-20210716001 調整行動版表單自定義開窗，單選時checkPointOnClose事件會觸發兩次問題
  - 變更檔案: 1 個
- **2021-10-06 19:36:14**: [BPM APP]Q00-20210831002 修正當Grid區塊沒放置元件，表單畫面會產生多餘的空白區塊問題
  - 變更檔案: 1 個
- **2021-10-06 11:11:02**: [BPM APP]Q00-20210824001 調整Line推播資訊，當未填寫主旨時設定預設資訊，修正會推播失敗問題
  - 變更檔案: 1 個
- **2021-10-05 11:57:06**: [BPM APP]Q00-20210831001 修正當流程設定表單欄位為唯讀時，部分元件設定setValue異常問題
  - 變更檔案: 1 個
- **2021-08-30 09:17:33**: [BPM APP]Q00-*********** 修正IMG表單畫面，操作到其他頁面再重整表單時，左上方IMG的返回按鈕沒顯示問題[補]
  - 變更檔案: 4 個
- **2021-08-27 10:08:30**: [BPM APP]Q00-*********** 修正IMG表單畫面，操作到其他頁面再重整表單時，左上方IMG的返回按鈕沒顯示問題
  - 變更檔案: 1 個
- **2021-08-16 18:00:02**: [BPM APP]Q00-20210816002 修正IMG中間層只有在待辦應用才產生上一關卡. 下一關卡資訊
  - 變更檔案: 1 個
- **2021-08-13 14:34:36**: [BPM APP]Q00-20210812002 修正IMG待辦列表可依關卡建立時間排序功能
  - 變更檔案: 2 個
- **2021-08-13 14:33:12**: [BPM APP]Q00-20210813001 調整IMG列表資訊的架構，以符合關鍵字搜尋後，組篩選條件的資訊
  - 變更檔案: 3 個
- **2021-10-14 12:03:55**: [BPM APP]Q00-20211014001 修正入口平台整合設定進入編輯再點其他工具佈署，資訊顯示異常問題
  - 變更檔案: 1 個
- **2021-08-30 10:14:31**: [BPM APP]C01-20210825004 修正移動端加簽功能，選擇人員資訊會撈到離職人員問題
  - 變更檔案: 1 個

### pinchi_lin (21 commits)

- **2021-10-12 11:46:51**: [BPM APP]Q00-20211006003 修正ESS流程在手機端簽核時無法查看附件問題
  - 變更檔案: 2 個
- **2021-10-06 18:13:05**: [BPM APP]Q00-20211006004 修正使用IMG發起ESS流程夾帶附件後在手機簽核時會看不到附件問題
  - 變更檔案: 4 個
- **2021-08-20 11:14:44**: [BPM APP]Q00-20210820002 修正企業微信與钉钉的列表頁面jsp引入css、js瀏覽器會有緩存問題
  - 變更檔案: 6 個
- **2021-08-20 10:58:48**: [BPM APP]S00-20210805001 調整企業微信與钉钉的追蹤流程預先顯示已處理流程頁面
  - 變更檔案: 4 個
- **2021-08-16 15:56:50**: [BPM APP]還原行動版用的Grid(單身)顯示摘要功能
  - 變更檔案: 2 個
- **2021-07-28 15:35:58**: [BPM APP]優化移動表單的顯示流程樣式
  - 變更檔案: 2 個
- **2021-07-28 15:12:07**: [BPM APP]優化移動表單的簽核歷程樣式[補]
  - 變更檔案: 4 個
- **2021-07-28 14:58:22**: [內部]移除移動端無用程式並新增一個移動端共用服務工具程式供後續調整
  - 變更檔案: 2 個
- **2021-07-27 17:49:44**: [BPM APP]Q00-20210727003 修正企業微信推送消息中取access_token判斷過期的邏輯異常問題
  - 變更檔案: 1 個
- **2021-07-27 15:01:42**: [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
  - 變更檔案: 1 個
- **2021-07-27 14:36:19**: [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
  - 變更檔案: 8 個
- **2021-07-22 18:26:41**: [BPM APP]Q00-20210722001 修正IMG的jsp引入css、js瀏覽器會有緩存問題
  - 變更檔案: 6 個
- **2021-07-22 17:27:07**: [BPM APP]C01-20210721005 修正整合钉钉在安卓開表單頁面時icon變文字問題
  - 變更檔案: 6 個
- **2021-07-16 18:09:48**: [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法
  - 變更檔案: 3 個
- **2021-09-29 11:12:46**: [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題
  - 變更檔案: 6 個
- **2021-12-22 20:19:53**: [RESTful]C01-20211221002 修正接口因response的header中key有空白導致會有回應502的問題
  - 變更檔案: 1 個
- **2021-09-29 10:49:21**: [BPM APP]Q00-20210929001 修正LINE使用預設port時連結會與login設定不同導致無法登入問題
  - 變更檔案: 1 個
- **2022-02-10 14:00:07**: [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]
  - 變更檔案: 1 個
- **2022-01-22 11:40:35**: [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題
  - 變更檔案: 1 個
- **2021-07-15 13:59:06**: [內部]調整排版
  - 變更檔案: 1 個
- **2021-07-12 17:12:16**: [內部]調整排版
  - 變更檔案: 1 個

### 詩雅 (4 commits)

- **2021-07-27 18:16:47**: [BPM APP] 修正表單上傳/刪除附件後，更新表單時，會跑版問題
  - 變更檔案: 5 個
- **2021-07-26 16:53:15**: [BPM APP]C01-20210722010 調整郵件內容以及Line推播內容，密碼元件值以*號顯示
  - 變更檔案: 1 個
- **2021-07-26 16:53:15**: [BPM APP]C01-20210722010 調整郵件內容以及Line推播內容，密碼元件值以*號顯示
  - 變更檔案: 1 個
- **2021-07-12 16:01:49**: [ESS]C01-20210629009 修正ESS表單簽核時上傳附件，移動端未顯示附件資訊的問題
  - 變更檔案: 3 個

### ocean_yeh (2 commits)

- **2022-03-15 13:39:07**: [Web]Q00-20220223001表單無法派送，把元件applUserId刪除重拉就可以正常派送
  - 變更檔案: 1 個
- **2022-02-17 15:45:28**: [Web]Q00-20220217004 TextBox有設定小數位時，使用FormUtil的寫法無法在formOpen時更換背景色
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.2
- **Commit ID**: `208d0d4b13c379cbbe9a4af4feb73a7fdec42f4b`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:09:39
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [流程引擎]Q00-20220721004 修正流程有併簽關卡設計時；若並簽關卡的流程中同時連續包含兩個Router以上的節點時，會導致流程未等待所有併簽關卡結束後，就直接往下進行派送
- **Commit ID**: `d787de8be52d125f1f9e9beb84b0d0eb44b79552`
- **作者**: waynechang
- **日期**: 2022-07-21 15:36:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 3. C01-20220831001 檢測自動簽核卡住問題
- **Commit ID**: `99f61f2267e8d2d50aae03404fe66ec902726e49`
- **作者**: kmin
- **日期**: 2022-09-30 10:28:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 4. Revert "[流程引擎]Q00-20220921001 調整發起流程頁面；由表單畫面切換至流程圖時，可根據當前表單內容進行流程預解析"
- **Commit ID**: `1cdaf3ff66ecba1bdf0f9cfabd9865a9f94dec16`
- **作者**: kmin
- **日期**: 2022-09-26 16:35:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 5. [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題[補]
- **Commit ID**: `c54cec5697967da1cd14d060f5c8a0867462c911`
- **作者**: yanann_chen
- **日期**: 2022-06-20 16:44:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 6. [流程引擎]Q00-20220921001 調整發起流程頁面；由表單畫面切換至流程圖時，可根據當前表單內容進行流程預解析
- **Commit ID**: `c4f36b1686968c87ef69135441a209f9efc0465f`
- **作者**: kmin
- **日期**: 2022-09-21 17:03:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 7. [Web]A00-20211012002 修正使用者名稱有設定多語系，在帳號管理的頁面會有多筆該使用者的重複資料
- **Commit ID**: `d0f4fefa372999849c638cda7e9ec4d53bfb840b`
- **作者**: 林致帆
- **日期**: 2021-10-13 15:14:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserManageListReader.java`

### 8. [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
- **Commit ID**: `e79f91205ec789e123c5674c94d3fb4d2c57a27c`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:48:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 9. [Web]S00-20211124001 提供行動版在發起流程頁面可調整流程緊急度
- **Commit ID**: `deb2958ad014b7c893fb660aeba6df1314d85996`
- **作者**: 王鵬程
- **日期**: 2022-01-14 17:22:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 10. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況 Templete有兩個計時器 (A)10分鐘更新一次User在線 (B)30秒取一次有沒有後端傳給使用者的訊息 如果視窗縮小或切換將兩個都停掉 回到視窗先更新一次User在線 重啟計時器 另外如果發現網路有不通的異常會先將計時器暫停，避免一直呼叫導致流覽器資源耗盡 在此種情況如果成功更新一次User在線會恢復計時器
- **Commit ID**: `b03652c690d404982cf64f387f4552ee64298d7a`
- **作者**: kmin
- **日期**: 2022-08-05 10:04:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 11. Revert "[Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況"
- **Commit ID**: `ab650e3cb3bf8cbe646ad1c49e81e2ce5ddecc1c`
- **作者**: kmin
- **日期**: 2022-08-05 09:50:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 12. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
- **Commit ID**: `624dc86ca6376f19a4c4c8c81b394e34da0fc17b`
- **作者**: walter_wu
- **日期**: 2022-07-29 00:04:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 13. [Web]Q00-20220628002 優化匯出Excel如果將啟始時間填空明明筆數很少卻撈很久
- **Commit ID**: `c1a1cbe3f24a3adbff5cd5fe27c299f885afc8f6`
- **作者**: walter_wu
- **日期**: 2022-06-28 18:06:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 14. [內部]Q00-20220530001 回收二線加上的WITH (NOLOCK)，並補上此程式所有漏加的地方
- **Commit ID**: `b554ac8c21f3703da811e1b3c69bafa0f04dd97b`
- **作者**: walter_wu
- **日期**: 2022-05-30 15:34:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 15. [內部]Q00-20220523002 ChangeProcessStateAudit補上WITH (NOLOCK)
- **Commit ID**: `48b2b0684a222eb26a68e9a837b1d5efd5435b74`
- **作者**: walter_wu
- **日期**: 2022-05-23 18:19:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 16. [流程引擎]Q00-20220504001 修正系統管理員監控流程匯出EXCEL內「執行中的活動」、「目前處理者」只呈現第一筆資料
- **Commit ID**: `e02372e0ddee0af23577b19c311e9aacf681f382`
- **作者**: yanann_chen
- **日期**: 2022-05-11 11:23:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 17. [Web]Q00-20220421001修正一般使用者匯出Excel匯出速度太慢
- **Commit ID**: `dce25fa63e843724a96b9a1c570fb83cd3283405`
- **作者**: 林致帆
- **日期**: 2022-05-05 11:00:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 18. [Web]Q00-20220414002 修正一般使用者追蹤流程清單匯出Excel報表，簽核時間欄位沒有值
- **Commit ID**: `ff635e2c3cc090971d7a53538de8e5fa5e4e8c61`
- **作者**: 林致帆
- **日期**: 2022-04-15 13:37:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 19. [Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢[補修正]
- **Commit ID**: `d121cd61be2ed845b0039b492eb90ace1b3824ba`
- **作者**: 林致帆
- **日期**: 2022-03-02 09:25:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 20. [Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢
- **Commit ID**: `9264975165a0094d43d700dcfd763fb4dd1a6bfe`
- **作者**: 林致帆
- **日期**: 2022-01-07 10:59:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 21. [流程引擎]S00-20210511001 新增追蹤流程列表可依流程結案時間做排序
- **Commit ID**: `2b4f33022a9bf570fd79dd51eda9c27f996e1196`
- **作者**: cherryliao
- **日期**: 2021-12-23 16:46:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 22. [流程引擎]S00-20210701002 追蹤流程頁面的進階查詢中增加簽核時間欄位條件
- **Commit ID**: `77ce2519948607a19db0fc797f78a29dd88518a3`
- **作者**: 王鵬程
- **日期**: 2021-10-27 18:13:43
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 23. [Web]Q00-20220713003 修正在行動版面中，在表單內向下滑動時，右下角的浮動按鈕會隱藏而無法後續操作
- **Commit ID**: `86683f17551e2d9793c0419b8f91f7a528be4232`
- **作者**: 王鵬程
- **日期**: 2022-07-13 16:33:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 24. [流程引擎]Q00-20220713005 修正因為取/退回後再次進入核決層級導致核決定義重長，舊(退/取回前)的核決實例找不到定義報錯無法派送
- **Commit ID**: `4a8b1a9ecd7fb7f886481b4e27ac982fe66ef188`
- **作者**: walter_wu
- **日期**: 2022-07-13 18:23:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 25. [流程引擎]Q00-20220627001 優化核決層級關卡解析人員緩慢問題
- **Commit ID**: `33a670d607802338b25c785f6959786865203d91`
- **作者**: kmin
- **日期**: 2022-07-12 15:36:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/OrganizationUnit.java`

### 26. [ESS]Q00-20220321003修正ESS流程經過取回or退回重辦在服務任務關卡前，撈取表單實例序號錯誤導致繼續派送報錯
- **Commit ID**: `d3a7844bc2a5527b0127524234e145fec27af1a2`
- **作者**: 林致帆
- **日期**: 2022-03-21 16:51:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 27. [內部]S00-20211112001 組織設計師及組織同步工具增加參數「orgdesigner.unitid.passsymbol」判斷Id是否檢查特殊符號
- **Commit ID**: `2cb9975e26881a3edb5d05cb005b9d66974e03f6`
- **作者**: waynechang
- **日期**: 2021-11-22 16:14:49
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/GroupEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OrgEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OrgUnitEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/UserCreator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`

### 28. Revert "revert [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題"
- **Commit ID**: `e65f2827288a8c8b8e5a0e9f0fa8a0f30bd828ff`
- **作者**: kmin
- **日期**: 2022-06-14 18:02:06
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 29. revert [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題
- **Commit ID**: `ca9b5d4db712f48df43dd033f3cc5f6f33c8d6f2`
- **作者**: kmin
- **日期**: 2022-06-14 18:01:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 30. Revert "[BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]"
- **Commit ID**: `d911db57310688e4a588d4f1e6cceaf23fbda03b`
- **作者**: kmin
- **日期**: 2022-06-14 17:59:25
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 31. [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題[補]
- **Commit ID**: `1369ddd477dcc7323049e79bffeaccd1258bab51`
- **作者**: yamiyeh10
- **日期**: 2021-11-25 14:40:53
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 32. [BPM APP]Q00-20211109002 修正在鼎捷移動上開啟草稿流程中的任一條流程會發生取得表單資訊錯誤的問題[補]
- **Commit ID**: `d02cc14874a9529e3d71a6560a98fae35dea8ccd`
- **作者**: yamiyeh10
- **日期**: 2021-11-18 15:20:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 33. [BPM APP]C01-20211108003 調整行動端儲存草稿一律必填草稿流程主旨[補]
- **Commit ID**: `51e6f3012b0edcb17419c973efac543b620cf879`
- **作者**: yamiyeh10
- **日期**: 2021-12-21 18:24:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`

### 34. [BPM APP]C01-20211108003 調整行動端儲存草稿一律必填草稿流程主旨
- **Commit ID**: `4814c781bec5656addaefa8de3e8d49ce8bde768`
- **作者**: yamiyeh10
- **日期**: 2021-11-22 14:53:20
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 35. [BPM APP]Q00-20211109002 修正在鼎捷移動上開啟草稿流程中的任一條流程會發生取得表單資訊錯誤的問題
- **Commit ID**: `0479ee084e11a6664cd264d779e032836f7c1395`
- **作者**: yamiyeh10
- **日期**: 2021-11-17 14:39:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 36. [BPM APP]Q00-20211109001 修正行動端表單使用客製開窗且無資料情況下畫面顯示異常問題
- **Commit ID**: `b43f0bcac2b6584c1d9baa2841d22d38dd61aed0`
- **作者**: yamiyeh10
- **日期**: 2021-11-09 18:16:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 37. [BPM APP]Q00-20211110002 修正在IMG從草稿流程進入的表單是走發起流程而不是從草稿進入發起流程
- **Commit ID**: `eeaaeccf1bb382b16c8db15f343ae3a1e3dd4cb4`
- **作者**: yamiyeh10
- **日期**: 2021-11-10 17:19:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`

### 38. [BPM APP]C01-20211108002 修正行動端從草稿流程進入的表單多呼叫formCreate方法導致畫面異常
- **Commit ID**: `102123757f0b2d3b907a2ff1d8aa69539312195a`
- **作者**: yamiyeh10
- **日期**: 2021-11-09 13:38:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`

### 39. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
- **Commit ID**: `581e43b6f497e76bdb578c620940c642a90c85cb`
- **作者**: walter_wu
- **日期**: 2022-06-10 18:10:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 40. [Web]A00-20220610001 修正程式權限設定為ESS才會有套用權限區塊，如果點到套用權限並非全勾的Row則上方套用權限會全部打勾
- **Commit ID**: `06aec66208dc111d50045f6cce863eb7a600716f`
- **作者**: 王鵬程
- **日期**: 2022-06-13 16:14:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`

### 41. [流程引擎]Q00-20220613001 調整流程設定參考表單欄位如果為部門，同Id部門一個以上的邏輯
- **Commit ID**: `34d7ea4bcfeb54b186dcc839f18f03f55863472b`
- **作者**: walter_wu
- **日期**: 2022-06-13 16:07:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 42. [Web]S00-20210503004 調整Web端加簽畫面新增經常選取對象
- **Commit ID**: `1d5350eacc177388fe847734fc5e7e3985f68711`
- **作者**: cherryliao
- **日期**: 2021-11-05 17:35:51
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseMutilPrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 43. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
- **Commit ID**: `7b5193630c3a81f7e3e4b7881ccf8d43a139b30e`
- **作者**: yamiyeh10
- **日期**: 2022-05-26 09:50:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 44. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題[補]
- **Commit ID**: `8df0056369b93506e2407c1138d5dff709e27f46`
- **作者**: yamiyeh10
- **日期**: 2022-05-16 15:15:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 45. [BPM APP]C01-20220509006 修正流程完成時Line推播訊息內容無法呈現完整表單的問題
- **Commit ID**: `48d5f4bdc9591ffcf241192332e770b8898b0272`
- **作者**: yamiyeh10
- **日期**: 2022-05-10 18:03:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 46. [流程引擎]A00-20220421001 修正流程完成通知信內容無法呈現完整表單的問題
- **Commit ID**: `37b150caf4072770086cebdccf172d422b26d47a`
- **作者**: yanann_chen
- **日期**: 2022-05-04 14:02:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 47. [BPM APP]Q00-20210907005 修正Line推播訊息在選項元件有設定額外輸入框且有值時不會顯示問題
- **Commit ID**: `5c104467f6d6e224921a77ffbbcd1a4545dc24fa`
- **作者**: yamiyeh10
- **日期**: 2021-09-08 15:14:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 48. [Web]Q00-20220420003 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系但實際的標題未呈現多語系內容
- **Commit ID**: `28e02b93f53cbf07b19ec537489c34c19f8c716d`
- **作者**: 王鵬程
- **日期**: 2022-04-20 18:24:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 49. [表單設計師]Q00-20220525005 修正表單設計師有縮小或是切換頁簽後切回來操作一段時間被登出
- **Commit ID**: `f0e35d151116cb47739d1289ff975186ca355dae`
- **作者**: yanann_chen
- **日期**: 2022-05-25 17:01:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 50. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
- **Commit ID**: `170f5c01fdd5abeffec41cd1c09d0d7c51eb9ef5`
- **作者**: walter_wu
- **日期**: 2022-04-19 16:50:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 51. [Web]Q00-20220419001 修正如果切換讀取較久的頁面視覺上會跑一半又呈現原畫面再跳轉
- **Commit ID**: `7763459c9fc0c87796e8ec3c1bf8aa46acb5f30b`
- **作者**: walter_wu
- **日期**: 2022-04-19 10:45:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 52. [內部] 調整瀏覽器閒置過久，請重新登入的多語系
- **Commit ID**: `960a1d01e4e3bf28f31aacb88ff6c7efb1cb4e71`
- **作者**: wayne
- **日期**: 2022-04-14 09:58:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 53. [Web]Q00-20220321001 修正絕對位置表單在第一關以外的關卡上傳附件按上傳後，開窗變空白
- **Commit ID**: `8024cfaf6aa836f5093d690619fe4dfe7a2f448e`
- **作者**: 林致帆
- **日期**: 2022-03-21 10:51:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 54. [流程引擎]V00-20220420001 修正流程定義當核決關卡參考自定義關卡屬性時，流程派送到核決關卡時會無法正常派送
- **Commit ID**: `689a4748b8f6a79b6281209d2465b558df8e0a6d`
- **作者**: wayne
- **日期**: 2022-04-22 14:50:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Organization.java`

### 55. [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
- **Commit ID**: `27870d9e979dcf67decefd142c58b2cb750676ae`
- **作者**: 林致帆
- **日期**: 2022-02-16 18:22:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 56. Revert "[Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]"
- **Commit ID**: `2cb0041fc9f971a321eb9851bd1e3dbc6d8583a4`
- **作者**: kmin
- **日期**: 2022-05-20 09:00:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 57. [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
- **Commit ID**: `ceac8ae77f03f1ec98247b88260eac524993383a`
- **作者**: 林致帆
- **日期**: 2022-03-01 15:16:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 58. [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯
- **Commit ID**: `84f17fb7661f6430856e672a79b21d9d1f33ac06`
- **作者**: 林致帆
- **日期**: 2022-01-19 15:35:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 59. [Web]Q00-20220512004 修正報表設計器修改報表定義後；若該報表為開新視窗方式開啟時，報表畫面上方的Title需顯示為「報表作業名稱」
- **Commit ID**: `3a58cdd443734c6d422adcdddf8a8ee334aa8328`
- **作者**: wayne
- **日期**: 2022-05-12 16:55:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`

### 60. [流程引擎]Q00-20220415001 修正因多餘附件移除邏輯改成流程結案處理，導至流程無法結案
- **Commit ID**: `cd421e67a9a19fd571dfa6227fd430786553ec13`
- **作者**: 林致帆
- **日期**: 2022-04-15 10:15:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java`

### 61. [流程引擎]Q00-20220511002 修正流程設定「結案時逐級通知」，當流程結案時，只有發起人有流程結案的系統通知
- **Commit ID**: `c442826c0bab2617ab03c0a4a429d04c147cfcb8`
- **作者**: yanann_chen
- **日期**: 2022-05-11 11:10:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 62. [Web]Q00-20220427003 修正從佈景主題去設定企業圖像圖片，在左側滑出選單最上方的圖片右側仍會顯示出背景色
- **Commit ID**: `fba9685e8a95aa531c0eb0164ac38b0668743123`
- **作者**: 王鵬程
- **日期**: 2022-04-27 16:50:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 63. [內部]Q00-20220427001 調整DWR設定讓Log不要一直出現轉換找不到轉換Locale方式的錯誤
- **Commit ID**: `3d8987f2410752da86524efc2dcd7a517fc31b7b`
- **作者**: walter_wu
- **日期**: 2022-04-27 15:00:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`

### 64. [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務[補修正] 內網判斷192.168改成只判斷192
- **Commit ID**: `f78a715226ddc622c7a3bd0374a16f8f09fbc744`
- **作者**: kmin
- **日期**: 2022-04-28 09:26:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 65. [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務[補]
- **Commit ID**: `e1125799645c114805ab70f79d5e3e2f99039d65`
- **作者**: kmin
- **日期**: 2022-04-21 15:12:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 66. [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務
- **Commit ID**: `49f3a18ce007ba7578f545d42e03294f3b8f49a2`
- **作者**: kmin
- **日期**: 2022-04-21 15:05:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 67. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出
- **Commit ID**: `add7794a538cfad624121c30e96c83fc2447c109`
- **作者**: walter_wu
- **日期**: 2022-03-24 17:25:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 68. [Web]A00-20220216002 修正RWD表單當右下角有出現滑到頂部按鈕時，按鈕也會被列印出來
- **Commit ID**: `958dc78bc5ab4dd445d6de7932e249683e13fecb`
- **作者**: 王鵬程
- **日期**: 2022-02-18 17:28:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 69. [Web]A00-20211201001 修正Rwd表單有設定元件的背景色、文字顏色時，在列印時無法印出色彩[補]
- **Commit ID**: `344f5d05b30f49c9a106629ec85de00e4ec834f5`
- **作者**: 王鵬程
- **日期**: 2021-12-06 15:01:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-3.3.5-print.css`

### 70. [Web]A00-20211201001 修正Rwd表單有設定元件的背景色、文字顏色時，在列印時無法印出色彩
- **Commit ID**: `613709540ca7d04b4df39ec7c145efde61d9b755`
- **作者**: 王鵬程
- **日期**: 2021-12-03 14:47:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrap-3.3.5-print.css`

### 71. [Web]A00-20220330001 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系，但實際操作沒有呈現多語系的內容
- **Commit ID**: `fff92554781fdd5298393f154fff4d2f9e1b68ad`
- **作者**: wayne
- **日期**: 2022-03-31 13:51:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 72. [流程引擎]A00-20220322002 修正SQL註冊器中語法有使用到order by 會導致報錯
- **Commit ID**: `0e23916634dc3e6391eeb206b3ea25d3017bcb22`
- **作者**: 王鵬程
- **日期**: 2022-03-23 16:59:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 73. [流程引擎]A00-20220323001 修正流程核決層級關卡之後的關卡若有設定自動簽核，流程無法往下派送的問題
- **Commit ID**: `67ec4c1b97a1a2551ada38087ffceb72f8cf64b7`
- **作者**: yanann_chen
- **日期**: 2022-03-24 18:28:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 74. [流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過
- **Commit ID**: `aa9b86e7bea6031497c9f45de3d4c19839962e32`
- **作者**: yanann_chen
- **日期**: 2022-02-08 18:03:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 75. Revert "[流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過"
- **Commit ID**: `83c64f12b81d97292f5c6b08e96869896ff7d36f`
- **作者**: kmin
- **日期**: 2022-03-23 14:37:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 76. [流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過
- **Commit ID**: `aeeacd76f4ac0c91d8410aa7a169a60f1b21792d`
- **作者**: yanann_chen
- **日期**: 2022-02-08 18:03:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 77. Revert "[流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過"
- **Commit ID**: `85139b63c9aa667c26e330143d4e440b567d24ce`
- **作者**: kmin
- **日期**: 2022-03-23 11:42:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 78. [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題 for MobileCommonServiceTool
- **Commit ID**: `0e30e2f49e12ad9e2e2fc2ee46990743ed9a6cfc`
- **作者**: kmin
- **日期**: 2022-03-21 09:44:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 79. [BPM APP]Q00-20210913001 調整行動端上傳附件發生錯誤時前端回應資訊錯誤的問題[補]
- **Commit ID**: `02b097a00bc37ebcf5bfe3859212c9abe43df951`
- **作者**: cherryliao
- **日期**: 2021-10-13 18:22:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 80. [BPM APP]Q00-20210831001 修正當流程設定表單欄位為唯讀時，部分元件設定setValue異常問題[補]
- **Commit ID**: `1fe2df07da286a69b9ee48fdce6891fc97b33037`
- **作者**: yamiyeh10
- **日期**: 2021-10-13 17:22:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 81. [BPM APP]Q00-20211005005 修正行動端詳情表單在操作Grid新增、編輯或取消按鈕時需要點擊兩次才會觸發動作的問題[補]
- **Commit ID**: `91e8634dbcab29407a1325a4a1b2cfd827087909`
- **作者**: cherryliao
- **日期**: 2021-10-13 14:50:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 82. [BPM APP]Q00-20211012002 調整行動端詳情頁面的日期與時間元件在iOS 15下會跑版問題
- **Commit ID**: `abfc8cd02fb982d393a4dc23f35338b68b08faea`
- **作者**: yamiyeh10
- **日期**: 2021-10-13 10:41:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 83. [BPM APP] 新增簽核流設計師中活動表單元件權限，行動端可複製Web端設定功能[補]
- **Commit ID**: `1e3c72955dc16445f264376329544ddb8a2a4eb8`
- **作者**: shihya_yu
- **日期**: 2021-10-12 17:10:18
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_zh_TW.properties`

### 84. [內部]Q00-20211005001 修正簽核流設計師中，行動端複製PC端設定有不同步的狀況
- **Commit ID**: `58ff2e1f6a4f42be045d3e1c46441a00b220101b`
- **作者**: shihya_yu
- **日期**: 2021-10-12 17:05:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`

### 85. [BPM APP] 新增簽核流設計師中活動表單元件權限，行動端可複製Web端設定功能
- **Commit ID**: `568fc970475215f62e313f75e1c80f413d245459`
- **作者**: shihya_yu
- **日期**: 2021-09-17 13:38:01
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileTableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormMobileValidateTableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`

### 86. [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能[補]
- **Commit ID**: `e839858dd13b791b24e755c57baf53279aefa447`
- **作者**: cherryliao
- **日期**: 2021-10-06 15:03:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 87. [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能
- **Commit ID**: `ba60ca0dc9b33b2466d68184e6b24417c4d91bd6`
- **作者**: cherryliao
- **日期**: 2021-09-13 18:14:23
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmCalendar.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 88. Revert "[內部]Q00-20211005001 修正簽核流設計師中，行動端複製PC端設定有不同步的狀況"
- **Commit ID**: `327fd0eaaacd85aff8b72b7ad456a848c1b30ea6`
- **作者**: kmin
- **日期**: 2022-03-18 17:26:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`

### 89. [內部]Q00-20211005001 修正簽核流設計師中，行動端複製PC端設定有不同步的狀況
- **Commit ID**: `c2aaaf012da161324600fdec97c19bfef4d84e8f`
- **作者**: shihya_yu
- **日期**: 2021-10-12 17:05:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`

### 90. [BPM APP]Q00-20210913003 調整上傳附件時loading圖示顯示的時機
- **Commit ID**: `8d37ed10e6afc60da0e74a7e82fe041dcd021ec9`
- **作者**: cherryliao
- **日期**: 2021-10-12 16:24:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 91. [BPM APP]優化移動表單的顯示流程樣式[補]
- **Commit ID**: `d74c3118c4858ab747eb5d4c2e417c77b5f1bd6e`
- **作者**: yamiyeh10
- **日期**: 2021-10-12 14:12:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 92. [BPM APP]Q00-20211006003 修正ESS流程在手機端簽核時無法查看附件問題
- **Commit ID**: `99042a5d691cd83b13b8ed59a2f9ea35ece19b96`
- **作者**: pinchi_lin
- **日期**: 2021-10-12 11:46:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 93. [BPM APP]Q00-20211005005 修正行動端詳情表單在操作Grid新增、編輯或取消按鈕時需要點擊兩次才會觸發動作的問題
- **Commit ID**: `4282ebfc70d8cfaaa6984308ebe9abcbe57dc3ea`
- **作者**: cherryliao
- **日期**: 2021-10-08 18:37:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 94. [BPM APP]Q00-20211005004 修正左上方IMG的返回按鈕，會偶發沒顯示問題
- **Commit ID**: `f97fa819c6ebd876eb5ec0c358efc8f37da26fa8`
- **作者**: shihya_yu
- **日期**: 2021-10-08 18:15:39
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 95. [BPM APP]Q00-20210913001 調整行動端上傳附件發生錯誤時前端回應資訊錯誤的問題
- **Commit ID**: `5a40cdd90034e7c8ebf232b90efe0963877acbd5`
- **作者**: cherryliao
- **日期**: 2021-10-08 14:50:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 96. [BPM APP]Q00-20210716001 調整行動版表單自定義開窗，單選時checkPointOnClose事件會觸發兩次問題
- **Commit ID**: `421437e51b01d214a48ae4f8c97c4e202fd953ba`
- **作者**: shihya_yu
- **日期**: 2021-10-08 14:12:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 97. [BPM APP]Q00-20210831002 修正當Grid區塊沒放置元件，表單畫面會產生多餘的空白區塊問題
- **Commit ID**: `986dabca791cd1f14884c0d77d0db01bc5f9444b`
- **作者**: shihya_yu
- **日期**: 2021-10-06 19:36:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`

### 98. [BPM APP]Q00-20211006004 修正使用IMG發起ESS流程夾帶附件後在手機簽核時會看不到附件問題
- **Commit ID**: `c34ce2781c2cb7edb3d9e7ec29b7dda2d12de409`
- **作者**: pinchi_lin
- **日期**: 2021-10-06 18:13:05
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFileManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 99. [BPM APP]Q00-20211005002 將行動端詳情頁面的發送通知中選擇人員畫面上浮動按鈕調整新樣式
- **Commit ID**: `19103c7250c48aaae7592cb26ae108df516a15ea`
- **作者**: yamiyeh10
- **日期**: 2021-10-06 15:55:33
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 100. Revert "[表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能[補]"
- **Commit ID**: `1e3c3a24b51580b02e59941025397c57576d9dee`
- **作者**: kmin
- **日期**: 2022-03-18 17:18:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 101. [表單設計師]S00-20210727002 調整表單設計師日期彈窗樣式並優化日期與時間預設值設定功能[補]
- **Commit ID**: `2e882dc206472e3d43164baccd55b25fbcf228ab`
- **作者**: cherryliao
- **日期**: 2021-10-06 15:03:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 102. [BPM APP]Q00-20210804003 修正行動端表單當流程設定多人且只需一位處理時不會顯示彈出視窗詢問是否要接收並派送訊息問題
- **Commit ID**: `a7082fb4b766fec86eabf9ddfb243412e1656aec`
- **作者**: yamiyeh10
- **日期**: 2021-10-06 14:48:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js`

### 103. [BPM APP]Q00-20210915002 修正行動端E10表單當Grid有多筆單身資料且其中有子單身無資料時會出現錯誤訊息問題
- **Commit ID**: `75d270f49916f42176b0894493cd2d449adf2dab`
- **作者**: yamiyeh10
- **日期**: 2021-10-06 12:07:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 104. [BPM APP]Q00-20210824001 調整Line推播資訊，當未填寫主旨時設定預設資訊，修正會推播失敗問題
- **Commit ID**: `9764ba59bc7be8c526fd85f113db2405f593ed42`
- **作者**: shihya_yu
- **日期**: 2021-10-06 11:11:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`

### 105. [BPM APP]Q00-20210823001 修正行動端表單當退回重辦必填簽核意見時彈窗沒有遮罩問題
- **Commit ID**: `c1f04ad5198b971bcaea47685605b9f9bd6397ab`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 16:23:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 106. [BPM APP]Q00-20210910002 將行動端詳情頁面的加簽選擇人員畫面上浮動按鈕調整新樣式[補]
- **Commit ID**: `3e91d024baf0951356b6d61e86689e708c9d914f`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 14:23:58
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 107. [BPM APP]Q00-20210910001 將行動端詳情頁面的轉由他人處理中選擇人員畫面上浮動按鈕調整新樣式
- **Commit ID**: `595dd7cab44626ef9a35840d5350524929e620b6`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 13:45:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 108. [BPM APP]Q00-20210831001 修正當流程設定表單欄位為唯讀時，部分元件設定setValue異常問題
- **Commit ID**: `504418b61bacdb87ca2928a469a2d6ae6703616c`
- **作者**: shihya_yu
- **日期**: 2021-10-05 11:57:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 109. [BPM APP]Q00-20210910002 將行動端詳情頁面的加簽選擇人員畫面上浮動按鈕調整新樣式
- **Commit ID**: `ea752cc9ab1f3bd9d32d3c14be538656d646b30b`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 11:56:33
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 110. [BPM APP]Q00-20210913004 修正在企業微信的追蹤已簽核附件頁面中返回按鈕沒有反應問題
- **Commit ID**: `638d84cac0ad623fa88e2c54a6e961dc1fc860d8`
- **作者**: yamiyeh10
- **日期**: 2021-10-05 11:25:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 111. [BPM APP]調整行動端詳情表單的簡易簽核歷程可依系統變數設定是否要顯示全部資料功能
- **Commit ID**: `4fa6db3d83e8ef92397f1cea19cd5876480beb55`
- **作者**: yamiyeh10
- **日期**: 2021-09-01 16:29:16
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 112. [內部]S00-*********** 調整開發者工具頁面新增清除快取資料功能
- **Commit ID**: `2e67716fef75b467fb916f9f817d2a8bc0592621`
- **作者**: cherryliao
- **日期**: 2021-08-30 15:43:46
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ServerCacheManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdministratorFunctionAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-administratorFunction-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 113. [BPM APP]Q00-*********** 修正IMG表單畫面，操作到其他頁面再重整表單時，左上方IMG的返回按鈕沒顯示問題[補]
- **Commit ID**: `7ab0b88fec14f3dc47ead8754bb0faefe26091af`
- **作者**: shihya_yu
- **日期**: 2021-08-30 09:17:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`

### 114. [BPM APP]Q00-*********** 修正IMG表單畫面，操作到其他頁面再重整表單時，左上方IMG的返回按鈕沒顯示問題
- **Commit ID**: `72e3ed6095be4524ba1bd3c41a9852cee013373f`
- **作者**: shihya_yu
- **日期**: 2021-08-27 10:08:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 115. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `d5b3ee11b302c6d51184c4e623db536b263165b3`
- **作者**: yamiyeh10
- **日期**: 2021-08-23 17:31:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFormServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/ajax-loader.gif`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 116. [BPM APP]Q00-20210820002 修正企業微信與钉钉的列表頁面jsp引入css、js瀏覽器會有緩存問題
- **Commit ID**: `67d4be0a93d6a42b722bf4bfff2f10f9f4099737`
- **作者**: pinchi_lin
- **日期**: 2021-08-20 11:14:44
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContactV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp`

### 117. [BPM APP]S00-20210805001 調整企業微信與钉钉的追蹤流程預先顯示已處理流程頁面
- **Commit ID**: `045ab6b34cefe1e946dd887546a0b85a3796a633`
- **作者**: pinchi_lin
- **日期**: 2021-08-20 10:58:48
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 118. [BPM APP]優化移動表單的顯示流程樣式
- **Commit ID**: `375a3255ed4cc78e58f3a9af0178184eb647a68e`
- **作者**: yamiyeh10
- **日期**: 2021-08-19 17:37:43
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 119. [BPM APP]Q00-20210816002 修正IMG中間層只有在待辦應用才產生上一關卡. 下一關卡資訊
- **Commit ID**: `ea5949e9899b39a2a3e87899361b731244259ea1`
- **作者**: shihya_yu
- **日期**: 2021-08-16 18:00:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 120. [BPM APP]還原行動版用的Grid(單身)顯示摘要功能
- **Commit ID**: `4a4e9471d7b286e44c417a6d78d39e2d75eb92ac`
- **作者**: pinchi_lin
- **日期**: 2021-08-16 15:56:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 121. [BPM APP]Q00-20210816001 修正IMG詳情頁面中的顯示流程標題沒有多語系問題
- **Commit ID**: `01c098286609a34d6544f65e2f43a41082348e49`
- **作者**: yamiyeh10
- **日期**: 2021-08-16 14:04:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 122. [內部]Q00-20210813003 修正"取得流程圖資料"接口的欄位performerId的內容不該為OID
- **Commit ID**: `1bf98eb45f12e30ba1837b350902b0197cd06481`
- **作者**: 林致帆
- **日期**: 2021-08-13 17:05:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 123. [BPM APP]Q00-20210812002 修正IMG待辦列表可依關卡建立時間排序功能
- **Commit ID**: `6a1f6cf5ecbd7c7206f49d54f129307e73493efc`
- **作者**: shihya_yu
- **日期**: 2021-08-13 14:34:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 124. [BPM APP]Q00-20210813001 調整IMG列表資訊的架構，以符合關鍵字搜尋後，組篩選條件的資訊
- **Commit ID**: `085b13b912b273d520ebd8c9476b85c8436b96c5`
- **作者**: shihya_yu
- **日期**: 2021-08-13 14:33:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`

### 125. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `3a88db50966377521dcc391215b54f50a62aafde`
- **作者**: yamiyeh10
- **日期**: 2021-08-11 17:25:53
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 126. [系統管理工具]S00-20200616002 調整點選線上使用者的通知，若使用者已登出時會跳出的錯誤訊息
- **Commit ID**: `63b895b0c5a4b20210493ccda2e952be48c38dd9`
- **作者**: cherryliao
- **日期**: 2021-08-04 17:29:32
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/OnlineUserMgtController.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_zh_TW.properties`

### 127. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `99f9488aa933f87a3c52128beb3d393868ebe13d`
- **作者**: yamiyeh10
- **日期**: 2021-08-04 10:45:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 128. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `126f8344f3f3d0a6baef952e6fe2fea2ee3b3d74`
- **作者**: yamiyeh10
- **日期**: 2021-07-30 15:08:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 129. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `131d947dc3f4192f989ddc241b41589b8844d728`
- **作者**: yamiyeh10
- **日期**: 2021-07-30 14:30:37
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 130. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `fd3d2c7e28c50929374c2535d0e54f1fe9722eab`
- **作者**: yamiyeh10
- **日期**: 2021-07-29 16:18:32
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/FetchProcessCommentsBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 131. [BPM APP]優化移動表單的顯示流程樣式
- **Commit ID**: `c5cb3b27c77e078d959397c4683254ed04202f33`
- **作者**: pinchi_lin
- **日期**: 2021-07-28 15:35:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 132. [BPM APP]優化移動表單的簽核歷程樣式[補]
- **Commit ID**: `99b96cad0df187bae1f5ef78996311741f1fd950`
- **作者**: pinchi_lin
- **日期**: 2021-07-28 15:12:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 133. [內部]移除移動端無用程式並新增一個移動端共用服務工具程式供後續調整
- **Commit ID**: `9ef27203e57d6cd871cb19a1e406aa99fc3b0d53`
- **作者**: pinchi_lin
- **日期**: 2021-07-28 14:58:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileWorkItemServiceTool.java`

### 134. [BPM APP]優化移動表單的簽核歷程樣式
- **Commit ID**: `477176c7fc7890403c9b6ed39f031ee9bf323255`
- **作者**: yamiyeh10
- **日期**: 2021-07-28 10:33:55
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 135. [BPM APP] 修正表單上傳/刪除附件後，更新表單時，會跑版問題
- **Commit ID**: `e1b6979c875c17c9ae4cac00cd970cdae6d585c7`
- **作者**: 詩雅
- **日期**: 2021-07-27 18:16:47
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 136. [BPM APP]Q00-20210727003 修正企業微信推送消息中取access_token判斷過期的邏輯異常問題
- **Commit ID**: `700bf000088232fd344aca81c3ead9d6b3452e61`
- **作者**: pinchi_lin
- **日期**: 2021-07-27 17:49:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 137. [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
- **Commit ID**: `0c13651156c388c5378095212dc3a4a1d0cc7557`
- **作者**: pinchi_lin
- **日期**: 2021-07-27 15:01:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 138. [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法[補]
- **Commit ID**: `361d32aa4790231187e6829f531e445a12d023a3`
- **作者**: pinchi_lin
- **日期**: 2021-07-27 14:36:19
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 139. Revert "[BPM APP]C01-20210722010 調整郵件內容以及Line推播內容，密碼元件值以*號顯示"
- **Commit ID**: `c5b0431b0edb4b431f0851edae5fc7c038953e9e`
- **作者**: kmin
- **日期**: 2022-03-18 16:39:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 140. [BPM APP]C01-20210722010 調整郵件內容以及Line推播內容，密碼元件值以*號顯示
- **Commit ID**: `f6f5090a1463649b0165264e38e40b0cc228d92e`
- **作者**: 詩雅
- **日期**: 2021-07-26 16:53:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 141. [流程設計師]S00-20210305001 調整服務任務讀取https的WSDL，增加SSL憑證失敗的明確提示窗
- **Commit ID**: `376a69b8ca75bf6b7446fd638c2181e3b89e6e15`
- **作者**: 林致帆
- **日期**: 2021-07-26 15:44:11
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/WSInvocationEditorController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_zh_TW.properties`

### 142. [BPM APP]Q00-20210722001 修正IMG的jsp引入css、js瀏覽器會有緩存問題
- **Commit ID**: `bdcee939c85d5129a6768011945e3ad936419918`
- **作者**: pinchi_lin
- **日期**: 2021-07-22 18:26:41
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`

### 143. [BPM APP]C01-20210721005 修正整合钉钉在安卓開表單頁面時icon變文字問題
- **Commit ID**: `5f037fe098125635ca460d266dbd454235a867d7`
- **作者**: pinchi_lin
- **日期**: 2021-07-22 17:27:07
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 144. [BPM APP]新增提供給模組在整合移動端(IMG、企業微信、钉钉等)可以開啟模組頁面的方法
- **Commit ID**: `3ca9a1b49da88e32e0341ee03562f18b04ebc5cb`
- **作者**: pinchi_lin
- **日期**: 2021-07-16 18:09:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 145. [Web]S00-*********** 調整人員離職自動將帳號功能停用，復職則帳號功能啟用
- **Commit ID**: `ef4e900bc25dea2d1e57cdc36708e707fd06976f`
- **作者**: 林致帆
- **日期**: 2021-07-14 16:59:02
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 146. [在線閱覽]S00-*********** 在線閱覽模組新增自訂浮水印內容及測試轉檔功能
- **Commit ID**: `99ecb53a3184ce412114cde94d0834212d0ae86d`
- **作者**: waynechang
- **日期**: 2021-08-11 14:51:02
- **變更檔案數量**: 37
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OnlineReadDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PDFHandleDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/onlineRead/OnlineReadWatermarkPattern.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/onlineRead/WatermarkAllowedForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/onlineRead/OnlineReadManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/onlineRead/OnlineReadManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/onlineRead/OnlineReadManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/onlineRead/OnlineReadManagerMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/OnlineReadAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WaterMarkPattenUtil.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageOnlineRead-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/OnlineReadFileUploader.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/WatermarkPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/spectrum/spectrum.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/spectrum/spectrum.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DDL_InitOnlineReadDB_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DDL_InitOnlineReadDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/5.8.7.1_onlineRead_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/5.8.7.1_onlineRead_DDL_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/5.8.7.1_onlineRead_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/5.8.7.1_onlineRead_DML_Oracle_1.sql`

### 147. [Web]A00-20210806001 修正日期和時間元件設定多語系提示文字，但只顯示預設值的問題
- **Commit ID**: `79456f79dabf381b22231ca7ba999294e04afa4a`
- **作者**: cherryliao
- **日期**: 2021-08-06 17:08:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 148. [Web]S00-20200528001 優化簽核歷程的流程狀態"已處理"更改成"已同意"
- **Commit ID**: `4552f080d01d365a0043f1ebfc9ab65fac1c50ec`
- **作者**: 林致帆
- **日期**: 2021-08-04 15:06:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 149. [Web]S00-20210315001 增加轉派意見在待辦流程的主旨上 [補修正]
- **Commit ID**: `1cb38541834b10705308d1b61a95695e7b196188`
- **作者**: 林致帆
- **日期**: 2021-08-03 11:39:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 150. [Web]S00-20210315001 增加轉派意見在待辦流程的主旨上
- **Commit ID**: `20c76972e6e1315f75a0e2ceb9a4b64794f58066`
- **作者**: 林致帆
- **日期**: 2021-08-02 14:26:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 151. [表單設計師]S00-20200917002 調整表單設計師Gird勾選最前面欄位設為自動增加流水號時新增一列序號欄位
- **Commit ID**: `22196fb50e17ac42f3d43bad69a43fa6a33b149e`
- **作者**: cherryliao
- **日期**: 2021-07-21 10:52:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 152. [表單設計師]S00-20200716001 新增日期和時間元件預設值配置功能[補]
- **Commit ID**: `29571a9686f550dd20959ddbb583cc707f58a78a`
- **作者**: cherryliao
- **日期**: 2021-07-27 14:24:25
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 153. [表單設計師]S00-20200716001 新增日期和時間元件預設值配置功能
- **Commit ID**: `acb68522d7c99728cba79c44cc0e54c012154f8a`
- **作者**: cherryliao
- **日期**: 2021-07-13 18:22:29
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DialogElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 154. [表單設計師]S00-20200716003 增加TextBox數字轉文字功能可設定將文字呈現於另一個TextBox欄位中
- **Commit ID**: `9a2d00a0afe018fdd7c7d4a3c0bdd251d6517b9d`
- **作者**: cherryliao
- **日期**: 2021-07-19 17:54:22
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/InputElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 155. 20220317 經博至討論後，暫時先將移除TT附件的機制，以便後續觀察
- **Commit ID**: `1a09f929b521fd16983fa5537e85c5c17b79ed75`
- **作者**: kmin
- **日期**: 2022-03-18 10:02:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 156. [Web]S00-20220316001 新增整合暫存管理供未註冊的BPM使用
- **Commit ID**: `eb451cf5414d0283c19e27cb7eebfacedf439934`
- **作者**: 林致帆
- **日期**: 2022-03-16 11:19:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 157. [WEB]Q00-20220315002 修正舊版表單InputElement若沒有textValue屬性時，儲存表單會發生錯誤
- **Commit ID**: `fa8b121889ac8e3187986159795eba789a3d7965`
- **作者**: wayne
- **日期**: 2022-03-15 14:01:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TextElementDefinition.java`

### 158. [Web]Q00-20220223001表單無法派送，把元件applUserId刪除重拉就可以正常派送
- **Commit ID**: `f2da0deba5d3f488af9c5682bca0522a63d7a8a6`
- **作者**: ocean_yeh
- **日期**: 2022-03-15 13:39:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 159. [組織同步]A00-20220314001 修正組織同步完會蓋掉使用者設定的 使用者是否顯示待辦事項小視窗
- **Commit ID**: `aa6d9e5bba9498988230b7ab6cd7f0db1b835c1a`
- **作者**: kmin
- **日期**: 2022-03-15 15:46:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 160. Revert "20220314 修正組織同步完會蓋掉使用者設定的 工作事項顯示設定 by kmin."
- **Commit ID**: `cc8cdb0ebe3c2a8f840a37288a6316ca9e6afcb1`
- **作者**: kmin
- **日期**: 2022-03-15 15:45:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 161. [WEB]A00-20220216001 修正追蹤流程-已轉派的工作，點進表單後再返回清單頁沒有保留原本的查詢條件
- **Commit ID**: `be40502f046f73b1906d20819c3ce65bc3eb8c32`
- **作者**: wayne
- **日期**: 2022-03-15 14:10:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 162. Revert "[Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單"
- **Commit ID**: `534dde4f20a37524d0ece1869d02eda8439c5a66`
- **作者**: kmin
- **日期**: 2022-03-15 15:26:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 163. [Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單
- **Commit ID**: `20fc1b3c49efb8d1c261badc42a7adef540adb3e`
- **作者**: 林致帆
- **日期**: 2022-01-26 10:41:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 164. Revert "[Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單"
- **Commit ID**: `eb2cb801f9ef11d787cad86483971241d8d5fb03`
- **作者**: kmin
- **日期**: 2022-03-15 14:24:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 165. [Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單
- **Commit ID**: `2f0107feb88d17104f3ee05cf5d3f625edd3db58`
- **作者**: 林致帆
- **日期**: 2022-01-26 10:41:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 166. [流程引擎]Q00-20220314003 修正流程關卡下一關卡為多人關卡處理且設定自動簽核為"與前一關相同簽核者，則跳過"，繼續派送會失敗
- **Commit ID**: `a75bdd640d3825f02db5de5266f462574456d752`
- **作者**: 林致帆
- **日期**: 2022-03-14 17:46:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 167. 20220314 修正組織同步完會蓋掉使用者設定的 工作事項顯示設定 by kmin.
- **Commit ID**: `94758d1ce30897a992cfbf2597ad19d6167e6746`
- **作者**: kmin
- **日期**: 2022-03-14 16:17:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 168. 久鼎客戶 增加判斷非LDAP類型的帳號才會去執行「使用者更換密碼」動作 by kmin.
- **Commit ID**: `8a388e59c605c5d380bbff9f49ab6699d3159b75`
- **作者**: kmin
- **日期**: 2022-03-14 16:12:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 169. 月眉國際客戶Web表單設計師Grid視窗不會關閉 by kmin.
- **Commit ID**: `67b4384e2262f2bcb80d3abf8d2cf8bcd8c4b05a`
- **作者**: kmin
- **日期**: 2022-03-14 16:06:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`

### 170. [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題[補]
- **Commit ID**: `afe4352dd9f8af4252efa83e03fa65f39019f879`
- **作者**: yamiyeh10
- **日期**: 2021-10-07 09:48:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 171. [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題[補]
- **Commit ID**: `82edbafcbd3aa62300abf2b434bc44acfc9b50dd`
- **作者**: yamiyeh10
- **日期**: 2021-10-07 08:58:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 172. [表單設計師]C01-20210824006 調整移動端方法呼叫卡控防止未註冊時影響PC端操作異常問題
- **Commit ID**: `1f25281ff4c255bd4f64556b22e90624b5130c86`
- **作者**: pinchi_lin
- **日期**: 2021-09-29 11:12:46
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-undoManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`

### 173. [組織同步]A00-20220224002 修正組織同步完會蓋掉使用者設定的 簽核完畢後的行為
- **Commit ID**: `b3609994c894675a4ed7304d05bc19b83fd6594d`
- **作者**: kmin
- **日期**: 2022-03-14 15:51:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 174. [Web]S00-20210914003 調整BPM登入頁面可用瀏覽器的儲存密碼功能
- **Commit ID**: `e19d2f731c3055ed5a2a604aa2b5af60cc452c5d`
- **作者**: cherryliao
- **日期**: 2021-12-20 13:54:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 175. [內部]Q00-20211115001新增GuardService連線成功的提示訊息
- **Commit ID**: `71e9ff3aee3bd7946d378daf644e53e92c8fdf28`
- **作者**: 林致帆
- **日期**: 2022-01-22 16:18:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java`

### 176. Revert "[Web]Q00-20220120003 流程代理人設定的選擇流程開窗，預設用流程代號做排序"
- **Commit ID**: `f01795ed4837e046af13a3c65267bd2cb927995b`
- **作者**: kmin
- **日期**: 2022-03-14 15:30:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 177. [Web]Q00-20220120003 流程代理人設定的選擇流程開窗，預設用流程代號做排序
- **Commit ID**: `1980bdab043328c7d24a0c855252777d674aa88b`
- **作者**: 王鵬程
- **日期**: 2022-01-20 18:23:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 178. [流程引擎]Q00-20220120001 修正「使用者有多個部門，在選擇發起部門後若發起流程失敗，回到表單頁面後無法再發起流程或儲存表單」問題
- **Commit ID**: `be79cb5d7810a0e54bfb63b5935c11b83392b24c`
- **作者**: yanann_chen
- **日期**: 2022-01-20 16:51:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 179. [Web]A00-20220112001 只有在簽核關卡執行加簽才顯示簽核意見欄位；不是簽核意見的關卡，執行加簽時不顯示簽核意見欄位
- **Commit ID**: `13d374859fcd11d4b35b1666bab7cafc176473e8`
- **作者**: yanann_chen
- **日期**: 2022-01-12 16:44:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 180. [Web]S00-20210709002 調整Web加簽頁面樣式
- **Commit ID**: `3b65ff382a83169d41e2fffb501e69259c6e494e`
- **作者**: cherryliao
- **日期**: 2021-10-28 11:17:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 181. [流程引擎]Q00-20220107008 修正流程主旨的結尾是「\」符號，取回工作重辦清單無法呈現
- **Commit ID**: `5ec687c3b8111afe68486c176f6bc7057403ecef`
- **作者**: yanann_chen
- **日期**: 2022-01-07 15:54:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 182. [Web]A00-20211223001 修正監控流程清單頁連結不開放給系統管理員以外的人員[補修正]
- **Commit ID**: `b913ee59f2d82c08e062070f9ccba963a574f15d`
- **作者**: 林致帆
- **日期**: 2021-12-27 10:54:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 183. [Web]A00-20211223001 修正監控流程清單頁連結不開放給系統管理員以外的人員
- **Commit ID**: `ccb08143ae07da3dda65d9f0d061fc6b7699c8bb`
- **作者**: 林致帆
- **日期**: 2021-12-23 14:20:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 184. [Web]Q00-20210928002 優化追蹤流程通知信URL進入速度
- **Commit ID**: `26c32ed8ce5c0925f912c05926ee51bc4cb2e1c3`
- **作者**: walter_wu
- **日期**: 2021-09-29 18:06:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 185. [Web]V00-20220113001 修正系統排程設定的編輯工作觸發程序中選擇每天、每週、每月進去的時間下拉元件太窄導致內容無法完整顯示
- **Commit ID**: `fe6413b048e914aa82a2b2a3e31de2bfcfa5e900`
- **作者**: 王鵬程
- **日期**: 2022-01-14 11:59:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`

### 186. [流程設計師]Q00-20220106008 修正在流程定義視窗中選擇標頭，勾選是否逾時多次通知後在進來該視窗都會變未勾選
- **Commit ID**: `9bdbd7fb6e7f85aae4d13da29a7cb036a2bb6469`
- **作者**: 王鵬程
- **日期**: 2022-01-06 14:52:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`

### 187. [RESTful]C01-20211221002 修正接口因response的header中key有空白導致會有回應502的問題
- **Commit ID**: `a62dfc875a424eaa1470114170230a4a6a905ef3`
- **作者**: pinchi_lin
- **日期**: 2021-12-22 20:19:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/EAIHeaderKey.java`

### 188. Revert "[流程引擎]Q00-20211217001 修正當資料庫為oracle時，SQL註冊器未輸入任何條件查詢會報錯"
- **Commit ID**: `9c08833fb4e95ebf07574cdebacba542d01ce609`
- **作者**: kmin
- **日期**: 2022-03-14 14:54:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 189. [流程引擎]Q00-20211217001 修正當資料庫為oracle時，SQL註冊器未輸入任何條件查詢會報錯
- **Commit ID**: `65ef113e210dd7dff825a8e9e9f05fd7982172cd`
- **作者**: 王鵬程
- **日期**: 2021-12-17 14:15:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 190. [Web]Q00-20211216004 修正新增排程的頁面中，排程生效時間下拉元件太窄導致內容無法完整顯示
- **Commit ID**: `d0190a19dfff7675cae51ccfc1f61ae217837c47`
- **作者**: 王鵬程
- **日期**: 2021-12-16 18:24:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`

### 191. [Web]Q00-20211207001 修正CSS的樣式缺少右大擴號的錯誤導致寫在後面的CSS無法生效
- **Commit ID**: `d6e832470e561284638f52ad028f4d0236d788ca`
- **作者**: 王鵬程
- **日期**: 2021-12-07 17:03:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 192. [Web]A00-20211201002 修正追蹤流程點擊匯出Excel會報錯
- **Commit ID**: `24f6f5189ed019773de403993ebb64b94706c23f`
- **作者**: 林致帆
- **日期**: 2021-12-02 09:08:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 193. [流程引擎]Q00-20211201002  修正人員名稱有新的特殊字(慈)時，進入ESS流程會報錯
- **Commit ID**: `cf92714b0a40132c9634e71842ad0952bbdc3cf6`
- **作者**: 王鵬程
- **日期**: 2021-12-01 15:08:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 194. [流程引擎]Q00-20211125001 修正透過HR小助手同步，當人員的兼職部門直屬主管在HR那已設空值，同步後卻未被改成空值[補]
- **Commit ID**: `20e9b92d173dfb99f5739bd316c4fc0205f09cbd`
- **作者**: 王鵬程
- **日期**: 2021-11-25 17:43:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/syncorg/SyncTable_Oracle.properties`

### 195. [流程引擎]Q00-20211125001 修正透過HR小助手同步，當人員的兼職部門直屬主管在HR那已設空值，同步後卻未被改成空值
- **Commit ID**: `1273a779bee06e944efedf2909264e1473eae300`
- **作者**: 王鵬程
- **日期**: 2021-11-25 15:47:34
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/AppProperties.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/SyncTableConstants.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/syncorg/SyncTable.properties`

### 196. [WorkFlow]Q00-***********修正WorkFlow在單據進行取消確認時，對該單據抽單，回傳的狀態碼有誤導致WorkFlow作業為待處理
- **Commit ID**: `112ea3464a8c35deb3696da64b6fd395f3f0b0c2`
- **作者**: 林致帆
- **日期**: 2021-11-22 15:22:06
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WorkFlowDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IWFRequestRecordDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBWFRequestRecordDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 197. [流程設計師]Q00-*********** 修正XPDL轉BPMN流程發生閘道元件與流程關卡ID重複的問題，導致BPMN流程中的連接線連接錯誤
- **Commit ID**: `3a18bcc0b4326c42d2cc8a785666fa16a715d5d4`
- **作者**: yanann_chen
- **日期**: 2021-11-12 15:40:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java`

### 198. [Web]Q00-20211112001修正，系統管理工具的資料來源設定是用Oracle且修改的欄位是Oracle服務名稱(SID)時，取得該資料來源資料會顯示原來的資訊而不是修改後的
- **Commit ID**: `d7b53dce46e6680182b89d53ba28fe4db4ee4379`
- **作者**: 林致帆
- **日期**: 2021-11-12 09:14:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ConnectionFactory.java`

### 199. [內部]A00-20210820001 在判斷可退回關卡邏輯處加上線的定義詳細Log，方便之後排查
- **Commit ID**: `427079eee5cc2e871e4c63ba67479da06f330516`
- **作者**: walter_wu
- **日期**: 2021-11-11 18:47:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java`

### 200. [Web]Q00-20211111004 修正流程有設定列印模式，且有將流程設置在iReport時，點擊上方『列印表單』後流程無法繼續派送
- **Commit ID**: `371e7148d0178797f1df2fe52f411570cf15dfcf`
- **作者**: 王鵬程
- **日期**: 2021-11-11 15:10:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 201. [Web]Q00-20220217004 TextBox有設定小數位時，使用FormUtil的寫法無法在formOpen時更換背景色
- **Commit ID**: `13d0afe4afc18da1c3252d39f53e1c55d681b7fa`
- **作者**: ocean_yeh
- **日期**: 2022-02-17 15:45:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 202. [Web]Q00-20211111002 使用FormUtil.setValue賦值給整數或浮點數Textbox欄位時，處理千分位及外顯值邏輯
- **Commit ID**: `8ece79839f82e0f02dff09038b1815a544df46b4`
- **作者**: yanann_chen
- **日期**: 2021-11-11 14:05:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 203. [Web]A00-20210922002 預覽流程圖關卡點開彈窗Table新增顯示  接收者  欄位
- **Commit ID**: `e9b72386ccc57fd8e693ea043c03a0dd4b2130dc`
- **作者**: walter_wu
- **日期**: 2021-11-10 16:55:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 204. [流程引擎]Q00-20211104003 修正簡易流程圖無法查看於核決層級內加簽的"多人"關卡的關卡資訊
- **Commit ID**: `cb003e71c6c226543e9daf2ba38ff2cc4c014a64`
- **作者**: walter_wu
- **日期**: 2021-11-04 17:21:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`

### 205. [Web]Q00-20211020001 流程表單設定欄位必填時，若儲存表單時必填欄位尚未填寫，就彈出相關提示訊息
- **Commit ID**: `5b568cf7f888988915fe7396cf6eca6a5d4d366c`
- **作者**: yanann_chen
- **日期**: 2021-11-03 18:29:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 206. [Web]Q00-20211102002 修正元件的label及元件在流程設計師設定invisible時，前端頁面報錯導致系統變數顯示內容異常
- **Commit ID**: `f480b5ba42302b124551065e383188c5df20de0d`
- **作者**: 林致帆
- **日期**: 2021-11-02 17:14:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 207. [Web]Q00-20211028003 修正活動關卡中掛載網頁應用程式，在IE中點上面的更多按鈕，出現的選單會被遮蔽而無法點選
- **Commit ID**: `b8b2dd4bf3b24be7b19c2c622655dbc614767144`
- **作者**: 王鵬程
- **日期**: 2021-10-28 14:50:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp`

### 208. [流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過
- **Commit ID**: `3fb0bd419d8be92002417a88d3296b23d019c03b`
- **作者**: yanann_chen
- **日期**: 2022-02-08 18:03:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 209. [流程引擎]A00-20220127001修正流程退回重辦選擇"按照流程定義依序重新執行"，關卡會經過"服務任務"會導致主旨的退回重辦標籤沒有顯示
- **Commit ID**: `d285ee127714e016ea24336463e3bf5a81b02d2a`
- **作者**: 林致帆
- **日期**: 2022-02-08 16:08:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 210. [流程引擎]Q00-20220111002 修正多人關卡在執行自動簽核時，偶發的沒有押上簽核意見或簽核意見押到正常簽核的工作上的問題
- **Commit ID**: `70e03aacd7e74d14c34bc72db65695007465970c`
- **作者**: yanann_chen
- **日期**: 2022-01-11 16:58:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 211. [流程引擎]Q00-20211210001 修正流程執行退回重辦後，後續關卡設定為「2.與前一關同簽核者則跳過」的自動簽核失效問題
- **Commit ID**: `9dd9fce0b3779c098711fb43ca1d402e7644a401`
- **作者**: yanann_chen
- **日期**: 2021-12-10 14:12:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 212. [流程引擎]Q00-20211103001 調整自動簽核邏輯，以人員任務(UserTask)的處理者判斷是否執行自動簽核
- **Commit ID**: `0cd71bfd4e73327a58e4efe0cd928444853d7128`
- **作者**: yanann_chen
- **日期**: 2021-11-03 16:25:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 213. [流程引擎]A00-20210913002 增加判斷是否因為代理人導致觸發自動簽核並處理相關邏輯
- **Commit ID**: `131a78f96b4c323e6e2edfd974d45d045dce241f`
- **作者**: walter_wu
- **日期**: 2021-09-30 17:47:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 214. [Web]Q00-20211022001 修正部分情境前端沒有傳入登入方式，會出現設定LDAP驗證卻走系統驗證
- **Commit ID**: `425c32161e5d779e84152a9f233678a2da048b47`
- **作者**: walter_wu
- **日期**: 2021-10-22 16:08:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 215. [流程引擎]Q00-20211109003 修正流程如果設定關係人的部門從表單欄位(選兼職部門)，發起時儲存表單後預解析卻解析主部門
- **Commit ID**: `0312e9b360913d27a59e099375d4da527a6d8615`
- **作者**: walter_wu
- **日期**: 2021-11-09 17:01:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 216. [流程引擎]Q00-20211020002 修正流程關係人設定部門表單欄位，表單內容為[組織ID]部門ID，導致流程發起失敗
- **Commit ID**: `578a1ed0a43a64a24ba197981f4ee6205ad73d36`
- **作者**: 林致帆
- **日期**: 2021-10-20 11:17:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 217. [流程引擎]Q00-20220211001 調整簡易流程圖預先解析，當前核決層級關卡的工作處理者為原處理者的代理人時，以原處理者解析後續關卡
- **Commit ID**: `43b294e42f57c4099c3d7d6504e5dbe1c0fefa1d`
- **作者**: yanann_chen
- **日期**: 2022-02-11 18:14:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 218. [流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程
- **Commit ID**: `086fed096e22188e1d1f5df5ec86b1ff94bc6a03`
- **作者**: yanann_chen
- **日期**: 2022-02-08 14:49:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 219. [流程引擎]Q00-20211118002 修正簡易流程圖中使用前置關係人做流程預先解析，導致流程圖與流程實際派送情形不一致[補]
- **Commit ID**: `5b013929b82ea2b389c1d400f6f52f1cdec94d6d`
- **作者**: yanann_chen
- **日期**: 2021-12-20 16:56:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 220. [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤[補修正]
- **Commit ID**: `048e9a3dfe8525626980e82faf9ee8bc017bdd0d`
- **作者**: 林致帆
- **日期**: 2021-12-08 17:40:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`

### 221. [Web]Q00-20211202001修正簡易流程圖跟工作歷程顯示關卡資訊有順序錯誤
- **Commit ID**: `ed249f6dda0131f4ad928a7fac902977d7640200`
- **作者**: 林致帆
- **日期**: 2021-12-02 16:41:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 222. [流程引擎]Q00-20211118002 修正簡易流程圖中使用前置關係人做流程預先解析，導致流程圖與流程實際派送情形不一致
- **Commit ID**: `1de08147e229a643cffe7bde3e82f5e65fedb48f`
- **作者**: yanann_chen
- **日期**: 2021-11-18 15:07:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 223. [Web]S00-20210429001 調整簡易流程若關卡處理者為代理人則處理者名稱旁有提示文字 Web端與移動端一併調整
- **Commit ID**: `925c0c4e6776e23890911fbeafbeb379facd2270`
- **作者**: kmin
- **日期**: 2022-03-11 10:38:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 224. 5862用不到這一支 by kmin 3/11
- **Commit ID**: `f47990f7b29b87ba56f0782a0bb0c9e9771b89a9`
- **作者**: kmin
- **日期**: 2022-03-11 10:29:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 225. [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題[補] 補修正原因：修正於核決層級關卡的第一關執行向前加簽後，簡易流程圖無法正常呈現的問題。
- **Commit ID**: `2d988c512705f4fe488bde12a54f56341e452c16`
- **作者**: kmin
- **日期**: 2022-03-11 10:28:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 226. [Web]A00-20210906002 修正如果核決層級參考的關卡被代理過預解析會有異常
- **Commit ID**: `ed7f942c1e9928ba7bffa3911eaf1d8fcb9aea7f`
- **作者**: walter_wu
- **日期**: 2021-09-24 17:22:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 227. [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題
- **Commit ID**: `a86d43fde6b298775896494bad2bd69eb23e5062`
- **作者**: yanann_chen
- **日期**: 2021-09-17 17:01:00
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 228. [Web]Q00-***********調整簽核歷程及簡易流程圖流程狀態"已同意"調整回"已處理" 及 簡易流程圖 增加"已會辦"流程狀態
- **Commit ID**: `c5bb3f57d456f982fbacd48381428e8cdf7f398d`
- **作者**: 林致帆
- **日期**: 2021-09-16 11:52:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 229. [BPM APP]Q00-20210903001 修正顯示流程中簽核人員沒有多語系的問題 1. web與mobile端皆有此問題，一併修正
- **Commit ID**: `764b287d8406ca82594a64d0309ec799f7447e80`
- **作者**: kmin
- **日期**: 2022-03-11 10:14:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 230. Revert "[流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程 相關議題單號：C01-20211109001。"
- **Commit ID**: `6510dbfc99c5b07837514c9de4909a16330efc1c`
- **作者**: kmin
- **日期**: 2022-03-11 08:48:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 231. Revert "[流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題"
- **Commit ID**: `e21a803f6d29e150cb7000332b57867e8d62201a`
- **作者**: kmin
- **日期**: 2022-03-10 17:26:25
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 232. [流程引擎]Q00-*********** 修正「流程退回重辦後，簡易流程圖預先解析內容與流程實際派送情形不一致」的問題
- **Commit ID**: `a040d4f064fc0bbab72cdfdb717740fb1171f739`
- **作者**: yanann_chen
- **日期**: 2021-09-17 17:01:00
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`

### 233. [組織設計師]Q00-***********修正組織設計師點選群組跟專案右側人員清單不應該顯示分頁鈕
- **Commit ID**: `400fdeb13f9a6b7cf683502fbe70301e333ff9df`
- **作者**: 林致帆
- **日期**: 2021-10-19 14:23:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`

### 234. [Web]A00-20220110001 修正流程關卡用預設代理人處理，若該關卡沒有預設代理人，不會出現提示訊息畫面[補修正]
- **Commit ID**: `d300c4159ac1410eaa0b1358225276983cf554f4`
- **作者**: 林致帆
- **日期**: 2022-01-13 15:32:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ExceptionMessageCreator.java`

### 235. [Web]A00-20220110001 修正流程關卡用預設代理人處理，若該關卡沒有預設代理人，不會出現提示訊息畫面
- **Commit ID**: `840aefa0a9105171ae58a57368cbe1ae8d979023`
- **作者**: 林致帆
- **日期**: 2022-01-12 10:49:50
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 236. [流程引擎]Q00-20211224002 調整取回重辦邏輯，只允許使用者從進行中的關卡執行取回重辦
- **Commit ID**: `9591f497155172893086e7c038f1df35a455cc84`
- **作者**: yanann_chen
- **日期**: 2021-12-24 18:00:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 237. [流程引擎]Q00-20211221001 修正透過WebService呼叫退回重辦時，被退回的關卡處理者未收到待辦事項通知信
- **Commit ID**: `b8e70cd92a1845c340e141b8b3d90dc16382000a`
- **作者**: yanann_chen
- **日期**: 2021-12-21 11:07:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 238. [流程引擎]Q00-20211014003 修正加簽有異常卻未將原始錯誤印出導致出錯無法排查
- **Commit ID**: `dab4911c1d58da0aafb7f2fc31b67b1e466e94fa`
- **作者**: walter_wu
- **日期**: 2021-10-14 15:54:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 239. [Web]Q00-20211013003 修正當ListBox從設定選項中只有一個列時並刪除列，再改成從資料庫帶值，儲存表單會出現錯誤
- **Commit ID**: `a754093276163d16a0059c7fe8c50c9e3d615ef4`
- **作者**: 王鵬程
- **日期**: 2021-10-13 17:01:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 240. [Web]Q00-20220224001 修正維護樣板作業從PC版切換到行動版時資料無法顯示
- **Commit ID**: `57486ebdb6f8aba59c24a3c9c9bd1292aaa817b1`
- **作者**: 王鵬程
- **日期**: 2022-02-24 16:16:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 241. [Web]Q00-20220215002 調整讓行動版與PC版一致讓Grid只支援(a、br、input、i、button)五種html標籤
- **Commit ID**: `e522ff432cf3a2c300b49db841d377e8f1827f97`
- **作者**: 王鵬程
- **日期**: 2022-02-15 17:36:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 242. [Web]Q00-20220208002 單身加總欄位設定「顯示至小數點後第X位」且在其他欄位的運算規則中，單身加總數值改變後沒有觸發欄位運算
- **Commit ID**: `7f53128114360bb055381b2d85141ba500382c5b`
- **作者**: yanann_chen
- **日期**: 2022-02-08 17:06:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 243. [Web]Q00-20211209001 調整Grid綁定Checkbox與Radio元件時增加判斷元件是否存在機制防止發生找不到元件的問題
- **Commit ID**: `5abca46e83f81077827780f71604e24522ada85a`
- **作者**: yamiyeh10
- **日期**: 2021-12-09 16:17:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 244. [Web]A00-20211105001 調整JavaScript浮點數運算誤差造成單身加總計算結果不符預期
- **Commit ID**: `6175cfe78014a0c16b3359ca391c540b7633a93b`
- **作者**: yanann_chen
- **日期**: 2021-11-09 18:16:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 245. [Web]Q00-20211015001 調整讓Grid支援使用<button>
- **Commit ID**: `773762de5aa449add3b8943c7102c6540bb4c73d`
- **作者**: 王鵬程
- **日期**: 2021-10-19 16:32:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 246. [Web]Q00-20211015004 修正Grid欄位如果沒有Binding其他欄位，會導致點擊Grid修改鈕的時候，該筆欄位資料會不見
- **Commit ID**: `0ceaa4597d2eb6ad62a77a4b175e445ee86f3fdb`
- **作者**: 林致帆
- **日期**: 2021-10-15 17:21:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 247. [BPM APP]Q00-20211014001 修正入口平台整合設定進入編輯再點其他工具佈署，資訊顯示異常問題
- **Commit ID**: `968c44b27657097dc5db5c3b5263099376336ddf`
- **作者**: shihya_yu
- **日期**: 2021-10-14 12:03:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 248. [Web]Q00-20210826001 修正「若有為null的資料時，無法載入資料至Grid」的問題[補]
- **Commit ID**: `8e966b4db442cf6bad6d7d1f733a123da3c6238e`
- **作者**: yanann_chen
- **日期**: 2021-10-05 17:21:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 249. [流程引擎]S00-20210730004 調整Textbox設定浮點數、顯示千分位和小數點幾位時binding到Grid沒有千分位的問題
- **Commit ID**: `12009fc669e7b7553659698485e726fd4c085fd1`
- **作者**: cherryliao
- **日期**: 2021-09-16 13:37:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 250. [流程引擎]A00-20211006001 修正「部分流程無法產出作業流程書」的問題
- **Commit ID**: `00c3d6ca9ea6da84e02aafb8fbb56229f188ac41`
- **作者**: yanann_chen
- **日期**: 2021-10-07 12:00:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java`

### 251. [Web]A00-20211004001 修正多選自定義開窗在無資料的情況下，勾選表頭的「全選」選項時會帶出一筆空白資料
- **Commit ID**: `4df1edd048340389d81c37434fa8fdb403ce5d3f`
- **作者**: yanann_chen
- **日期**: 2021-10-05 11:14:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 252. [ESS]A00-20210521002 修正ESS刪除EFGP缺席紀錄的session bean無作用
- **Commit ID**: `4373ed461e0b8c9eca6ab8f69793359ec59ba7ab`
- **作者**: walter_wu
- **日期**: 2021-06-04 16:42:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 253. [流程引擎]Q00-20210930003 修正因有特製流程定義的資料，導致原本的流程定義刪除後無法再匯入相同流程
- **Commit ID**: `42019dfcad9c7a084b3f1daf5a0669d50ee8380c`
- **作者**: yanann_chen
- **日期**: 2021-09-30 16:04:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 254. [組織設計師]A00-20211027001 調整員工administrator點擊檢視員工資料再點編輯跟從修改員工資料的可編輯欄位要一致
- **Commit ID**: `a5222c11cdcf0990f51e82fef953aa9c1d2b28c9`
- **作者**: 林致帆
- **日期**: 2021-11-08 17:11:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`

### 255. [Web]Q00-20211124001 修正一般使用者在追蹤流程頁面中將『更多』按鈕隱藏
- **Commit ID**: `d6196fc969fd31731fe3c0d6a3d04d37e3ba0863`
- **作者**: 王鵬程
- **日期**: 2021-11-24 18:03:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 256. [Web]Q00-20211028004 移除追蹤流程與監控流程「已完成」、「已撤銷」、「已終止」清單中的「執行中的活動」欄位
- **Commit ID**: `4cd5ce9ea198709c0f0b43c846ae3cf2dd9ac7e8`
- **作者**: yanann_chen
- **日期**: 2021-10-28 18:14:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 257. [Web]Q00-20211216002 修正當有造字時，Chrome上會無法顯示造字的字
- **Commit ID**: `f43d4760e3d67cc90b03297a83e3440fcf9f75b9`
- **作者**: 王鵬程
- **日期**: 2021-12-16 17:48:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 258. [流程引擎]Q00-20211119001 修正表單選項元件的代號與名稱不同，導致額外輸入框的內容沒有被帶回到表單上
- **Commit ID**: `4f64c810d026b189412314669e34aeeaafea4275`
- **作者**: yanann_chen
- **日期**: 2021-11-19 15:44:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 259. Revert "[流程引擎]Q00-20211019009 追蹤流程時，顯示表單欄位的背景顏色"
- **Commit ID**: `f8c15a9f2cf8ddb15484a4cbff1802a2cc172880`
- **作者**: kmin
- **日期**: 2022-03-10 14:57:17
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 260. [流程引擎]Q00-20211019009 追蹤流程時，顯示表單欄位的背景顏色
- **Commit ID**: `448f40df24e08581401bce30e641f0a2ce054d07`
- **作者**: yanann_chen
- **日期**: 2021-10-19 18:00:40
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 261. [流程引擎]A00-20210908002 修正當表單選項元件勾選「額外產生出入框」且元件代號與名稱不同時，執行轉存表單失敗
- **Commit ID**: `9b855962b7f5f280dd06b58613b248a757f42285`
- **作者**: yanann_chen
- **日期**: 2021-11-19 11:35:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 262. [流程引擎]Q00-20211020004 修正絕對表單Grid內有 左、右中括號及單引號，轉存的Grid資料表的資料未能正確呈現符號
- **Commit ID**: `e7dd3ba7aae1eb000e3313ae5ca56dc3ad0dde18`
- **作者**: 王鵬程
- **日期**: 2021-10-20 15:53:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 263. [Web]A00-20211220001 修正在行動版時在我的最愛內的常用流程與常用功能維護無法儲存
- **Commit ID**: `6a3cc04202dfde08bad6d50051d71dd8bb9de0c0`
- **作者**: 王鵬程
- **日期**: 2021-12-22 15:21:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`

### 264. [流程引擎]Q00-20220103004 修正流程主旨的結尾是「\」符號，系統通知(活動類型)通知清單無法呈現
- **Commit ID**: `391a1cca55288ba2a6351eecba4a0b4ae87a6338`
- **作者**: yanann_chen
- **日期**: 2022-01-03 17:35:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ActivityNotiListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/WfNotificationListReader.java`

### 265. [流程引擎]Q00-20211223001 修正流程主旨的結尾是「\」符號，工作通知清單無法呈現
- **Commit ID**: `1e512423835308c54a4411483cb0f569ef727be9`
- **作者**: yanann_chen
- **日期**: 2021-12-23 13:56:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`

### 266. [Web]Q00-20220107009 調整行動版時左側的選單背景色及字體顏色也要與PC版一致
- **Commit ID**: `80cab1d8e1ced881609a944a7472b55fac71a1da`
- **作者**: 王鵬程
- **日期**: 2022-01-07 17:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 267. [流程引擎]Q00-20220215001 修正偶發附件遺失問題[補修正]
- **Commit ID**: `d4e1c28c44f0ea9737f14072eb6be0b384cc73b5`
- **作者**: walter_wu
- **日期**: 2022-03-03 16:11:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 268. [流程引擎]Q00-20220215001 修正偶發附件遺失問題
- **Commit ID**: `5403bbf1187e3cb28659e3e861d58b0789e734d0`
- **作者**: walter_wu
- **日期**: 2022-02-15 16:11:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 269. [流程引擎]Q00-20211220002 修正客戶附件遺失問題
- **Commit ID**: `bf0f913d96e4ac626c36df0ed75c0cac71eaff90`
- **作者**: walter_wu
- **日期**: 2022-01-10 17:19:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 270. [ESS]Q00-20211112002 修正ESS流程 A員工在 A電腦進行儲存草稿動作，A員工在B電腦打開該草稿時，ESS表單開啟報錯
- **Commit ID**: `36edb8d44341f062128d28b6c5c1773614fc263b`
- **作者**: 林致帆
- **日期**: 2021-11-26 17:21:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 271. [流程引擎]Q00-20211019004 調整DispatchActivityForAutoAgent排程，加入WITH NOLOCK指令
- **Commit ID**: `83cacb92c11a7fa93812604487347ca7e259a900`
- **作者**: yanann_chen
- **日期**: 2021-10-20 11:14:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 272. [流程引擎]Q00-20220208004 修正「已轉派的工作」清單，在「全部」頁籤取得的資料筆數與「處理中」、「已處理」兩個頁籤相加的數量不符
- **Commit ID**: `c5531e44b7af9499cacff1aebae72ed7b2bb557b`
- **作者**: yanann_chen
- **日期**: 2022-02-25 16:45:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 273. [Web]A00-20220120001修正IE開起流程用SQLcommand因為用replaceall函式導致報錯
- **Commit ID**: `c44c7b28ee4e5b45f9ebdde570a16c46e42aa3a3`
- **作者**: 林致帆
- **日期**: 2022-01-21 11:39:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 274. [Web]Q00-20220106001 調整加簽關卡的頁面中，關卡名稱允許單字之間使用空格
- **Commit ID**: `c0778f5938b570a0f00464d7876ae46a2f3b3ef7`
- **作者**: 王鵬程
- **日期**: 2022-01-06 11:47:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 275. [Web]Q00-20211216001 修正SQLCommand的SQL指令帶有百分比及加號會導致報錯
- **Commit ID**: `ea794b11e5dfbd651e1e8c587c669c76b113fa49`
- **作者**: 林致帆
- **日期**: 2021-12-16 11:56:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 276. [SQLCommand]Q00-20211215001 修正用Ajax下SQL如果欄位行態是text無法找出資料
- **Commit ID**: `d618e494b8f146705dc7c1b2439228a9ac120285`
- **作者**: walter_wu
- **日期**: 2021-12-15 16:13:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 277. [Web]Q00-20210930005 修正使用SQLcommand時指令帶有中文會導致造成base64加密報錯
- **Commit ID**: `b9338dd7ead11fdf51528ec1722c81a6d458bfea`
- **作者**: 王鵬程
- **日期**: 2021-09-30 17:55:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 278. [Web]Q00-20211116002修正RWD列印表單開窗中，點下列印表單後，有選到的radio和checobox都會產生類似殘影的樣子
- **Commit ID**: `e0cba6a4753d60e1b5d34d15ded40481d3bf8db8`
- **作者**: 王鵬程
- **日期**: 2021-11-16 18:11:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 279. [Web]Q00-20211020003 當響應式表單的下拉式選單元件設定為動態生成選項時，列印表單無法顯示欄位值
- **Commit ID**: `02a45be9deca921be8412d8a74a8d295f6a94b44`
- **作者**: yanann_chen
- **日期**: 2021-10-20 14:50:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 280. [Web]Q00-20211227001 修正使用IE在絕對表單沒有附件時預覽列印，會導致簽核歷程和表單內容重疊
- **Commit ID**: `0f4759c88ba9d4c1664bb1c343a839842be0016a`
- **作者**: 王鵬程
- **日期**: 2021-12-27 15:24:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 281. [內部]Q00-20211105002 調整列印龐大Grid位置(絕對位置表單,依設定)
- **Commit ID**: `761211f52a3982ed37433bcc8baf5f248526c758`
- **作者**: walter_wu
- **日期**: 2021-11-05 15:52:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 282. [Web]A00-20211001002 修正絕對位置表單當附件的描述過長時，會導致預覽列印時出現附件資訊和簽核歷程重疊
- **Commit ID**: `71da1cd9ccb489658e77f189e9d54fd3c5fb670c`
- **作者**: 王鵬程
- **日期**: 2021-10-06 15:46:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 283. [流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程 相關議題單號：C01-20211109001。
- **Commit ID**: `4605a80aaf0ada3514fbe49c3f134b3bb792ac5c`
- **作者**: kmin
- **日期**: 2022-03-09 16:32:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 284. Revert "[流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程"
- **Commit ID**: `208360f96b7c9287f00abc5a4959a4d603879ed8`
- **作者**: kmin
- **日期**: 2022-03-09 16:29:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 285. [流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程
- **Commit ID**: `2d99a46d54f5133cb4f39b0bf4b0ae548ee9c0a3`
- **作者**: yanann_chen
- **日期**: 2022-02-08 14:49:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 286. [Web]A00-20220214001 修正附件權限設定關卡在追蹤流程看不到的問題
- **Commit ID**: `e2ffbd836da2dcac31141658a32079f334498034`
- **作者**: walter_wu
- **日期**: 2022-02-17 22:58:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 287. [Web]A00-20211222001 修正離職作業維護選擇User後修改組織資料資料卻無法正確呈現與修改直屬主管異常
- **Commit ID**: `615074e3adc1c812d96d1937e6af15351d9e95ae`
- **作者**: walter_wu
- **日期**: 2022-01-18 17:41:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`

### 288. [Web]A00-20211209001 調整離職維護作業的時間輸入欄位點擊應為時間開窗供使用者選擇
- **Commit ID**: `cf67c729b83dac372513b9de61bf7f66f4fd63fe`
- **作者**: 林致帆
- **日期**: 2021-12-13 11:18:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`

### 289. [ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態
- **Commit ID**: `bd1f87feeb2039533f2bddd32fde36d9f87acc83`
- **作者**: yanann_chen
- **日期**: 2022-02-10 17:51:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 290. [ESS]Q00-20211026002 調整BPM呼叫ESS存檔前的判斷，防止同單據在ESS與BPM狀態不一致
- **Commit ID**: `021abce0d8b5f732bd1dfee93bbd98fc6afb1bd7`
- **作者**: yanann_chen
- **日期**: 2021-10-27 14:42:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 291. [ESS]Q00-20211026001 調整BPM發起ESS流程的邏輯，先檢查是否有整合ESS，再往下執行ESS相關的檢查
- **Commit ID**: `7efd6caf9bf9de1fdbfd3c93a6b8c916e358f955`
- **作者**: yanann_chen
- **日期**: 2021-10-26 17:01:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 292. [ESS]Q00-20210924006 在呼叫ESS存檔前增加判斷，防止同單據在ESS與BPM狀態不一致
- **Commit ID**: `45cabe69dd43665444f9ac51446eb0b52727eb6c`
- **作者**: yanann_chen
- **日期**: 2021-09-24 18:25:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/dao/OJBAppFormActivityRecordDAO.java`

### 293. [登入]Q00-20220304002 調整登入加密機制，避免後端session失效時取不到值登入失敗
- **Commit ID**: `77b3e6a30538a2da4bc82cc516b17b41eaae1d95`
- **作者**: walter_wu
- **日期**: 2022-03-04 18:24:11
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`

### 294. [BPM APP]Q00-20210929001 修正LINE使用預設port時連結會與login設定不同導致無法登入問題
- **Commit ID**: `8e84c360af017baf65d31f33bdef5782b4b9a1aa`
- **作者**: pinchi_lin
- **日期**: 2021-09-29 10:49:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 295. [流程引擎]Q00-20211108001 調整ExtOrgAccessor.findManagerForUser服務，當傳入的組織OID與人員不相關時，仍需回傳人員的主部門的主管
- **Commit ID**: `68d3f518f21c49c641c7b953d01c5f72f0fe9e23`
- **作者**: waynechang
- **日期**: 2021-11-08 16:41:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 296. [Web]S00-20211117003 流程資料的頁面排序調整以發起時間大到小排序(DESC)
- **Commit ID**: `dc5477fffe14cc4ff0ef91d26cf5a92f3726eba2`
- **作者**: 王鵬程
- **日期**: 2022-02-24 14:47:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDataSearchListReader.java`

### 297. [BPM APP]C01-20220224004 修正在取得IMG動態生成表單應用資料與取得綁訂使用者資料時會偶發statement close問題
- **Commit ID**: `d8db2eb7e64905db03e026196b23f006278d51b5`
- **作者**: yamiyeh10
- **日期**: 2022-03-08 10:08:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`

### 298. [流程引擎]A00-20211214001 修正若客戶流程進版後第一關的關卡ID與前一版不同，則無法於舊版流程實例使用「重發新流程」功能
- **Commit ID**: `10503bb34f1dac57319adada11b7d00d1c3c737b`
- **作者**: yanann_chen
- **日期**: 2021-12-15 14:48:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 299. [Tiptop]Q00-20220118002修正Tiptop傳的Grid沒有內容時會產生空陣列在Grid上
- **Commit ID**: `b75dc83330c4224df0ec341ab52b58cce849e6d1`
- **作者**: 林致帆
- **日期**: 2022-01-18 11:22:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 300. [內部]Q00-20211228001調整易飛orWFERP整合主機設定兩台多主機時，會無法開單
- **Commit ID**: `249d9b072c42d12b151ab36b71d18bdf62532371`
- **作者**: 林致帆
- **日期**: 2021-12-28 15:03:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 301. [Web]S00-20210202001 簡易流程圖的流程狀態字眼明確化
- **Commit ID**: `13115a53f322f30699336cdbf475a93881f9b606`
- **作者**: 林致帆
- **日期**: 2021-08-04 15:02:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmViewProcessImgActVo.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 302. [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]
- **Commit ID**: `****************************************`
- **作者**: pinchi_lin
- **日期**: 2022-02-10 14:00:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 303. [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題
- **Commit ID**: `5fdeb7838a32a088d9e4e85add08c3fee637b4fd`
- **作者**: pinchi_lin
- **日期**: 2022-01-22 11:40:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 304. [流程引擎]Q00-20211207002 調整若在登入頁面閒置一段時間，需要操作登入兩次才能登入BPM
- **Commit ID**: `f18e55feded8d85bdc53b1c9e6c4fb3220d8fef3`
- **作者**: yanann_chen
- **日期**: 2021-12-09 14:57:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 305. [流程引擎]A00-20210927003 修正使用系統計時功能後，當使用者被登出BPM時，需要進行兩次登入操作才能進入BPM
- **Commit ID**: `4a655bd69e002aa7ab182f8c3a73bbb29757ff59`
- **作者**: yanann_chen
- **日期**: 2021-09-30 11:30:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 306. [Web]A00-20211022001 調整系統管理員在監控流程只有選擇"未結案"，"全部"的流程狀態按鈕，才會在"更多"按鈕顯示撤銷流程
- **Commit ID**: `c88cf9066406122cdb265ed313583635bbeab8c9`
- **作者**: 林致帆
- **日期**: 2021-10-25 11:48:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 307. Q00-20210917001 增加防呆如果自己設定為自己的主管，在用核決層級參考預解析時會導致無窮迴圈
- **Commit ID**: `07faa69c17fab59f4d70d3bf6000a9d148be41f4`
- **作者**: walter_wu
- **日期**: 2021-09-17 11:37:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 308. [Web]S00-20210318002 優化監控流程匯出Excel功能
- **Commit ID**: `d23f452b488d60ae624c798a344618770600ce6e`
- **作者**: 林致帆
- **日期**: 2021-09-16 14:39:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 309. [Web]Q00-20210916002 調整退回重辦視窗中confirm按確認後，不再提示成功訊息，避免使用者按下右上角關閉導致表單未刷新頁面
- **Commit ID**: `503ec9f11035771c3de8bbc9f61e92c69fc68982`
- **作者**: 王鵬程
- **日期**: 2021-09-16 14:16:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 310. [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能[補]
- **Commit ID**: `23c3d93ebf4c450a4df1a7b17710b388d961c157`
- **作者**: yanann_chen
- **日期**: 2021-07-20 09:00:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 311. [流程引擎]S00-20210519001 「每個人都要處理」的活動關卡增加自動簽核功能
- **Commit ID**: `6ce8800aff90a92cd9eaf0a7eb83694c6018c282`
- **作者**: yanann_chen
- **日期**: 2021-07-08 17:23:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 312. [Web]Q00-20210914003 調整修改密碼的彈出視窗較小，導致按確認後alert的按鈕不能直接點選
- **Commit ID**: `0029b8368d8e054b122325add21a91cd2d780a6f`
- **作者**: 王鵬程
- **日期**: 2021-09-14 18:16:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 313. [Web]A00-20210913001 修正從BPM首頁的待辦清單由第二筆簽核跳到下一筆，都會跳到流程清單的第一筆流程
- **Commit ID**: `5f3ee7b03e2e003e7753b6346f7d07f647fc6c8a`
- **作者**: 林致帆
- **日期**: 2021-09-14 17:36:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 314. [Web]A00-20210906001 修正絕對位置表單日期元件勾選顯示時間並比對另一個日期，條件設==，則欄位上時和分會歸零
- **Commit ID**: `f48d49fe49d535c95c30dbf7feb36789aa23c4f5`
- **作者**: 王鵬程
- **日期**: 2021-09-14 14:16:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DateTemplate.txt`

### 315. [Web]A00-20210909001 修正在PC版表單設計師中更改Grid代號，在切換到行動版表單，畫面上顯示的Grid代號未改變
- **Commit ID**: `5f273ecc9f2fc74c52a911012cb0a9176357b673`
- **作者**: 王鵬程
- **日期**: 2021-09-13 18:22:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 316. [Web]S00-20210902001 優化Web端表單E10子單身呈現樣式
- **Commit ID**: `5531731db3d748fff3ef503675f67d415fcce833`
- **作者**: cherryliao
- **日期**: 2021-09-13 17:18:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/SubGridTransfer.js`

### 317. [Web]Q00-20210910003 修正如果再Orderby使用表別名Oracle會報錯
- **Commit ID**: `38f2a3b145bff507a17d731c0c44b462795ebdef`
- **作者**: walter_wu
- **日期**: 2021-09-10 17:14:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 318. [流程引擎]Q00-20210727002 修正因為關卡設定自動跳關導致代理機制異常[補修正]
- **Commit ID**: `4cc78082d82e72e3c75b26bdfe1234a32f286e43`
- **作者**: walter_wu
- **日期**: 2021-09-10 11:54:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 319. [Web]A00-20210907001 修正先看ESS流程草稿後在點擊一般流程草稿會導致報錯
- **Commit ID**: `cfd298526fb20bd02d9233ccb15989b649cc7835`
- **作者**: 林致帆
- **日期**: 2021-09-09 18:03:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`

### 320. [Web]Q00-20210909001 修正加簽後，在流程圖預覽無法看到加簽關卡
- **Commit ID**: `b05bb46611c9320d0bf815c58407f595dae1380e`
- **作者**: walter_wu
- **日期**: 2021-09-09 09:24:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`

### 321. [Web]A00-20210908001 修正從待辦事項連結登入BPM後應該要為該流程的簽核頁面，而不是代辦清單頁面
- **Commit ID**: `125decf1c0eb49884afba6a11d0936da9c78a179`
- **作者**: 林致帆
- **日期**: 2021-09-08 18:15:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 322. [WebService]Q00-20210908003 修正DotJ登入，所記錄的使用者登入資訊沒有加上Locale導致寫入DB時報錯
- **Commit ID**: `f3ef8c015bec0040eccf795a2be75c7cee7b47fb`
- **作者**: walter_wu
- **日期**: 2021-09-08 17:04:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`

### 323. [BPM APP]Q00-20210907005 修正Line推播訊息在Textbox有設定顯示千分位時沒顯示問題
- **Commit ID**: `e9b0cbdecde759fbac35bf91c9d99a93b6cfabf8`
- **作者**: yamiyeh10
- **日期**: 2021-09-08 16:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 324. [Web]Q00-20210908002 修正在一些https環境下會無法下載檔名有中文的檔案
- **Commit ID**: `5b8dd301c0e1e7eef17d4f14d2c1a86bc8dc8bea`
- **作者**: 王鵬程
- **日期**: 2021-09-08 16:10:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 325. [Web]S00-20210316001 流程筆數為1000時，呈現筆數為1000，不再用999+
- **Commit ID**: `6916ea566722763325dda0a56ecae8bcbde60764`
- **作者**: 林致帆
- **日期**: 2021-09-08 11:08:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/CursorRecorder.java`

### 326. [流程引擎]Q00-20210907003 修正發起時就算儲存表單，核決層級預解析因為沒有抓到設定的表單欄位而無法解析
- **Commit ID**: `a8c0a5f80bc2ce1c3cfc54d58c5e362480e7ee3a`
- **作者**: walter_wu
- **日期**: 2021-09-07 13:53:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 327. [Web]Q00-20210907002 Ajax_FormAccessor.getFormInstanceAttachmentSize() 增加「取得當前表單實例的附件數量」的方法
- **Commit ID**: `01e3140262a5e441139259227a67687111ffbc9e`
- **作者**: waynechang
- **日期**: 2021-09-07 10:40:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 328. [Web]Q00-20210906003 修正流程主旨允許用html並有輸入tr或tbody的tag，導致在BPM首頁中無法從下方待辦進入流程
- **Commit ID**: `2acebd87c69db79658a2ec5a377a29d3856fa9e3`
- **作者**: 王鵬程
- **日期**: 2021-09-06 18:06:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 329. [流程引擎]Q00-20210903003 修正「發起流程時儲存表單後，表單的viewMode從『INVOKE』變成『PERFORM』」的問題
- **Commit ID**: `b11447cee9d665b0a684b63bb128dd885aa1c806`
- **作者**: yanann_chen
- **日期**: 2021-09-03 18:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`

### 330. [Web]Q00-20210903002 調整模組中的程式開啟方式是設定另開新視窗時，使可以開啟多個不同程式名稱的視窗
- **Commit ID**: `1e31389e124ccdaee4d671c8f3d0acd275014840`
- **作者**: 王鵬程
- **日期**: 2021-09-03 17:56:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 331. [Web]A00-20210830001 修正允許流程主旨使用html時，在行動版下主旨無法呈現html的樣式
- **Commit ID**: `013889b3cab57fd7784dab5c70ff56dd7f1f1603`
- **作者**: 王鵬程
- **日期**: 2021-09-01 18:39:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 332. [流程引擎]A00-20210901001 修正客戶Grid資料過多導致SQL組過複雜導致DB報錯
- **Commit ID**: `92c6a7833959f3c4fed844bf6148ca12344da6c2`
- **作者**: walter_wu
- **日期**: 2021-09-01 17:25:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 333. [Web]Q00-20210830001 修正在行動版下，選擇退回重辦彈出來的視窗中，無法選擇要退回的關卡
- **Commit ID**: `5619d924486a224b18909254551c396532855324`
- **作者**: 王鵬程
- **日期**: 2021-08-30 11:59:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 334. [BPM APP]C01-20210825004 修正移動端加簽功能，選擇人員資訊會撈到離職人員問題
- **Commit ID**: `4e06fa32f95a3b70fc48790e60a8ffbef100e289`
- **作者**: shihya_yu
- **日期**: 2021-08-30 10:14:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 335. [Web]Q00-20210827001 調整DatabaseAccessor重組SQL指令的邏輯，防止在錯誤的地方插入where條件
- **Commit ID**: `34073e6fe25678b6fd70c8b848ffbf07f58752d3`
- **作者**: yanann_chen
- **日期**: 2021-08-27 18:46:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 336. [Web]A00-20210826001 修正退回重辦時簽核意見有換行，導致流程圖中檢視參與者型式的關卡頁面的工作列表無法出現
- **Commit ID**: `16bf6cb1f011349dec21f65bbb774a756e907471`
- **作者**: 王鵬程
- **日期**: 2021-08-27 11:45:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 337. [Web]Q00-20210826001 修正「若有為null的資料時，無法載入資料至Grid」的問題
- **Commit ID**: `de525c46083abc22ef67d80c76dcf8868e9b3e3b`
- **作者**: yanann_chen
- **日期**: 2021-08-26 16:44:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 338. [流程設計師]Q00-20210531001 修正「複製有連接線的關卡造成實際流程派送發生異常」的問題
- **Commit ID**: `38ef116254be6b627b3f713692333822b4101d13`
- **作者**: yanann_chen
- **日期**: 2021-06-02 15:44:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramAction.java`

### 339. [Web]A00-20210825002 修正使用者用IE11登入，線上人數查詢登入裝置資訊為IE7.0
- **Commit ID**: `af282adf9454df542c4fc25169e876f5185e9cc4`
- **作者**: 林致帆
- **日期**: 2021-08-25 18:16:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 340. [內部]新增清除所有Server二階快取的相關EJB及RMI接口
- **Commit ID**: `2cc3981f72d98e3687946ae66f6cce64b5a63867`
- **作者**: lorenchang
- **日期**: 2021-08-20 09:06:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IServerCacheManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerLocal.java`

### 341. [Web]Q00-20210819005 修正使用singleOpenWin開窗，搜尋條件使用簡體字會搜尋不到資料
- **Commit ID**: `3e9739fec72008efbfc511cdcbbae4247d4c0ed3`
- **作者**: 王鵬程
- **日期**: 2021-08-19 18:10:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 342. [流程引擎]	Q00-20210818002修正SQLcommand放的SQL指令有簡體字，會造成base64加密報錯
- **Commit ID**: `959d6065cb59404e3b391718369a3881a9c8c4fd`
- **作者**: 林致帆
- **日期**: 2021-08-18 18:19:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 343. [Web]Q00-20210818001 調整當瀏覽器封鎖BPM的彈窗時，在使用模擬模式進入監控或是進入待辦提示使用者開啟設定的訊息內容
- **Commit ID**: `04ff68a341eab6a6114760e14d5508df45126950`
- **作者**: 王鵬程
- **日期**: 2021-08-18 11:57:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 344. [Web]Q00-20210817002 修正當瀏覽器封鎖BPM站台的彈窗時，模擬使用者後進入待辦流程中會提示使用者去設定允許彈窗
- **Commit ID**: `29fc4d825b572780dcbb3b19de0e6fa6be75a7f4`
- **作者**: 王鵬程
- **日期**: 2021-08-17 18:35:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 345. [Web]Q00-20210817001 修正當瀏覽器封鎖BPM站台的彈窗時，且勾選啟動流程測試模式進入監控流程，會提示使用者去設定允許的彈窗
- **Commit ID**: `12aa6c0a660b2bd526efc52d3d6d149e71e7fd71`
- **作者**: 王鵬程
- **日期**: 2021-08-17 17:51:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 346. [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
- **Commit ID**: `d418b5d02f3ee8ae5e516bc043bf95d1602b1bb2`
- **作者**: 林致帆
- **日期**: 2021-08-17 16:49:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 347. [流程引擎]S00-20210113002調整流程前一關為服務任務，派送到下一關主旨會以下一關處理者的預設語系[補修正]
- **Commit ID**: `83b519423556e8cffcc827aa04b5e12efddd2822`
- **作者**: 林致帆
- **日期**: 2021-08-16 16:12:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 348. [流程引擎]Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回[補修正]
- **Commit ID**: `7eadb3042424d79b09ea4aa1569d5fc7612e3321`
- **作者**: walter_wu
- **日期**: 2021-08-16 13:48:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 349. Q00-20210813004 修正重複取回錯誤，並調整邏輯讓迴圈型也可取回
- **Commit ID**: `c757fa1f49da6dda33e15fc605cc08329c861c5b`
- **作者**: walter_wu
- **日期**: 2021-08-13 19:23:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 350. [流程引擎]A00-*********** 修正簡易流程圖無法查看於核決層級內加簽的關卡的關卡資訊
- **Commit ID**: `a42c1088dec7e9b739d4f475d497501fb661c45e`
- **作者**: yanann_chen
- **日期**: 2021-08-13 14:22:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`

### 351. [流程引擎]S00-20210113002調整流程前一關為服務任務，派送到下一關主旨會以下一關處理者的預設語系
- **Commit ID**: `a6dc365243ed680168b2ce336da7b78b8a2fadd8`
- **作者**: 林致帆
- **日期**: 2021-08-12 17:29:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 352. [流程引擎]Q00-*********** 在加簽過程中加入條件判斷，只有「進行中」的工作才可以進行加簽
- **Commit ID**: `5e22b12f4826c8b40cb7e4874498129fa7a5913e`
- **作者**: yanann_chen
- **日期**: 2021-08-12 14:35:39
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java`

### 353. [BPM APP]Q00-*********** 修正使用Line官方帳號登入的BPM,直接點擊ESSQ表單類會空白的問題
- **Commit ID**: `2f405dea2616fa7be4aacd0d19e55e0ba441fa99`
- **作者**: yamiyeh10
- **日期**: 2021-08-11 10:47:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 354. [Web]Q00-*********** 修正關卡通知信設定以整張表單時，checkbox元件在信件上呈現應該要為顯示值
- **Commit ID**: `30430a87dea0b008e45b87603ebe98a4c908cb98`
- **作者**: 王鵬程
- **日期**: 2021-08-10 18:02:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 355. [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
- **Commit ID**: `898b82cbd23895c8c77896c3708b5a0835baccc4`
- **作者**: walter_wu
- **日期**: 2021-08-10 11:05:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 356. [TIPTOP]Q00-20210511004 修正TIPTOP拋單回傳給TIPTOP失敗，流程可發起成功[補修正]
- **Commit ID**: `a04936f45512064245de2c72a00613ff8aacd293`
- **作者**: 林致帆
- **日期**: 2021-08-09 10:49:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 357. [流程設計師]S00-20210318004 修正連接線條件式輸入空格，顯示上會被替換成空字串
- **Commit ID**: `319e63233bda9b7e9e6fb94c82d15cc1b5d46804`
- **作者**: 林致帆
- **日期**: 2021-08-06 16:23:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/util/Content2ElementsConverter.java`

### 358. [Dot.J]S00-20201124001 調整當設定檔找不到Secure時，設定Secure為false避免ECP呼叫DotJIntegration溝通時發生異常
- **Commit ID**: `1b4f457e396bc21ee8b781d386e0a2b7897d1bb3`
- **作者**: cherryliao
- **日期**: 2021-08-06 16:00:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`

### 359. [Web]A00-20210804003 修正在行動版畫面的監控流程清單中每列的主旨未對齊
- **Commit ID**: `49ebd91c2f521fdbeaec20e6d6209ce60b410886`
- **作者**: 王鵬程
- **日期**: 2021-08-05 18:21:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 360. [Web]Q00-20210805002 修正關卡通知信設定以整張表單時，dropdown元件在信件上呈現應該要為顯示值
- **Commit ID**: `b566b79dc51b288780687fae968bec87b1ca5cb0`
- **作者**: 王鵬程
- **日期**: 2021-08-05 17:09:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 361. [流程引擎]S00-20201118001 調整當流程關卡中有工作被轉派給代理人處理且流程通知設為結案逐級通知時通知原處理者
- **Commit ID**: `d22b2c5735a82ee36f51f5e0de00c942b424691c`
- **作者**: cherryliao
- **日期**: 2021-08-04 14:23:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 362. [Web]Q00-20210803003 修正因之前議題的修正導致移動端功能『入口平台整合設定』頁面出現錯誤
- **Commit ID**: `1acbdd4b0a94e7e9ef4c74094618203527f500bf`
- **作者**: 王鵬程
- **日期**: 2021-08-03 18:16:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 363. [內部]Q00-20210803002優化log訊息：T100拋單時，取得附件的檔案編碼ID以及文檔中心的URL沒有設定
- **Commit ID**: `71f6266019b03029d9ef49e385a53b7c563a45dd`
- **作者**: 林致帆
- **日期**: 2021-08-03 17:18:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 364. [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題[補]
- **Commit ID**: `4cd87359548e7cf3ac7773cf0e09ce5601e698e5`
- **作者**: yanann_chen
- **日期**: 2021-08-03 17:12:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 365. [流程引擎]Q00-20210607003 修正多AP主機的狀況下，首頁模組報錯「當前登錄人不合法」問題
- **Commit ID**: `eeba26de1cc5ce506a25fb9d8795d7311c946679`
- **作者**: yanann_chen
- **日期**: 2021-06-07 11:43:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 366. [Web]A00-20210729001 SQLCommand資料庫為DBConnection元件，呼叫ajax query時找不到DB連線方式
- **Commit ID**: `7f8593846d00b4276c0ef35dbf2e0958e2863b28`
- **作者**: yanann_chen
- **日期**: 2021-08-03 16:07:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 367. [流程引擎]Q00-20210730002 修正關卡「只有一人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核[補]
- **Commit ID**: `4c27886ea5ca0e405e2e36d310619caf2805f641`
- **作者**: yanann_chen
- **日期**: 2021-07-30 18:23:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 368. [流程引擎]Q00-20210730002 修正關卡設定「只有一個人處理」、「與前一關同簽核者，則跳過」，當前一關處理者為多人時，未執行自動簽核
- **Commit ID**: `c1281c2635fa8c23b3d3fa461a2043b11d233efd`
- **作者**: yanann_chen
- **日期**: 2021-07-30 17:52:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 369. [Web]Q00-20210730001 修正Grid設小螢幕使用名片式並綁定Textarea，在行動版下有輸入換行，Grid呈現應該要是沒換行
- **Commit ID**: `4dcdc0f01f1f5cd6393e18da1e299a875ca9c127`
- **作者**: 王鵬程
- **日期**: 2021-07-30 10:48:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 370. [組織設計師]S00-20210506001 調整設定流程代理人時不顯示已失效的流程
- **Commit ID**: `c6aac2122cd95ff1bbde72576eae5aa3dd6217b3`
- **作者**: cherryliao
- **日期**: 2021-07-29 15:44:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/ProcessPackageForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/ProcessSelectorTableController.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 371. [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法 [補修正]
- **Commit ID**: `178edcd7775667c45d38e100a3aa912bd852a1fc`
- **作者**: 林致帆
- **日期**: 2021-07-29 14:38:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 372. [Web]Q00-20210728001 修正透過URL開啟『追蹤流程實例內單一表單資料』，在行動版時未顯示表單名稱
- **Commit ID**: `9760dd558b55156b0e4fa014b05136df4df438fa`
- **作者**: 王鵬程
- **日期**: 2021-07-28 18:11:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`

### 373. [Web]A00-20210726005 修正表單是絕對表單時，透過URL開啟『追蹤流程實例內單一表單資料』，在行動裝置版面下附件按鈕沒有字樣
- **Commit ID**: `29f2a399d29d8902b3bd10bdaa2104262eedf09a`
- **作者**: 王鵬程
- **日期**: 2021-07-28 15:30:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 374. [Web]A00-20210727002 修正當Grid有設定將最前面欄位設為流水號時，用流水號排序時應該用數字大小來排序
- **Commit ID**: `dd8c960ee8caa3f9db9cb7a1e0d3a0db4eb5e628`
- **作者**: 王鵬程
- **日期**: 2021-07-27 18:08:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 375. [Web]A00-20210726002 修正行動版下，從追蹤進去流程且該流程有被同個人簽核多次過，在取回清單頁面中下拉選單會出現奇怪文字
- **Commit ID**: `ddf8db555e7a567904bd8b3d96684afed15059d3`
- **作者**: 王鵬程
- **日期**: 2021-07-27 17:11:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 376. [Web]A00-20210727001 修正XPDL的流程在監控流程中跳過關卡時，驗證密碼視窗會一片空白
- **Commit ID**: `759440031a746090f2fce390f5621daaba880291`
- **作者**: walter_wu
- **日期**: 2021-07-27 16:50:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordForByPass.jsp`

### 377. [流程引擎]Q00-20210727002 修正因為關卡設定自動跳關導致代理機制異常
- **Commit ID**: `351b041bd999770eca4cdf2eaa4f4cfc2839488b`
- **作者**: walter_wu
- **日期**: 2021-07-27 15:57:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 378. [流程引擎]Q00-20210727001 修正因執行加簽關卡導致核決層級預先解析內容不正確
- **Commit ID**: `1b1039e6eb02a04e5bd32dc392d61bc4fb582f4c`
- **作者**: yanann_chen
- **日期**: 2021-07-27 14:35:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 379. [Web]A00-20210726001 修正關卡有勾選允許批次簽核，在行動版畫面的待辦事項清單中每列的主旨未對齊
- **Commit ID**: `07f0e9198e17fb13d5e768954d20d26d47f9f264`
- **作者**: 王鵬程
- **日期**: 2021-07-26 18:20:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`

### 380. [Web]A00-20210726003修正ajax_CommonAccessor的findXmlContent,findResource接口取得內容為中文亂碼
- **Commit ID**: `cce3554cd3d5e3466687eda9bf9548f2434acbc7`
- **作者**: 林致帆
- **日期**: 2021-07-26 18:08:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AbstractAccessor.java`

### 381. [BPM APP]C01-20210722010 調整郵件內容以及Line推播內容，密碼元件值以*號顯示
- **Commit ID**: `da2b2c1a1c8ef5fadb73e4bc1a056e0a504e4429`
- **作者**: 詩雅
- **日期**: 2021-07-26 16:53:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 382. [內部]Q00-20210726001 DatabaseAccessor 移除不需要的System.out.print方法
- **Commit ID**: `f583bcf397991c7e95c0b095656b74319d8c5fee`
- **作者**: 林致帆
- **日期**: 2021-07-26 14:35:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 383. [Web]A00-20210720001 修正絕對位置表單在追蹤流程頁面，表單範圍外的元件顯示出來
- **Commit ID**: `dd7a2e23cd6e9b1c56473fe29c36911e5b7c703b`
- **作者**: 林致帆
- **日期**: 2021-07-23 18:17:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 384. [Web]Q00-20210723001 修正當關卡表單權限設定為「唯讀」時，第一次點擊「儲存表單」後沒有再執行formOpen的問題
- **Commit ID**: `550fb243b33292d4ac48882586930760eb89350b`
- **作者**: yanann_chen
- **日期**: 2021-07-23 17:20:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 385. [流程引擎]Q00-20210721003 修正取回重辦後，簡易流程圖只顯示流程關卡，未顯示關卡處理者
- **Commit ID**: `bcd89649328bebab25556076351af54d65d5f6ca`
- **作者**: yanann_chen
- **日期**: 2021-07-21 17:25:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 386. [Web]Q00-20210721002 修正Grid綁textarea並輸入換行，畫面縮成mobile再到PC，Grid中資料會變成未換行
- **Commit ID**: `a8fa12c59060990b94d3f8eea021db5a2dcb9b5d`
- **作者**: 王鵬程
- **日期**: 2021-07-21 16:53:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 387. [流程引擎]Q00-*********** 加快發起流程時表單開啟速度
- **Commit ID**: `d65fc758fae85f8422a4f205bd66df4281d867e0`
- **作者**: yanann_chen
- **日期**: 2021-07-21 10:24:05
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessPackageManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 388. [Web]Q00-*********** 修正Grid綁定textarea並輸入換行，點擊排序後再點選Row，帶回textarea會出現<br>
- **Commit ID**: `158214b068df5197e3b5cc8dc4e8f8d9267d0d88`
- **作者**: 王鵬程
- **日期**: 2021-07-20 19:54:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 389. [Web]C01-20210706002 修正流程第二關設置radiobutton為invisible狀態，第二關簽核後該元件內容會消失
- **Commit ID**: `e9636fb5ce24c06c238d4a379144c8ab08fd5715`
- **作者**: 林致帆
- **日期**: 2021-07-20 14:57:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 390. [BPM APP]C01-20210714002 修正IMG詳情頁面的簽核歷程若前面存在與當前關卡相同關卡id時不會顯示問題
- **Commit ID**: `efd83e543fc77013dbce000e1b097e57b4de76a0`
- **作者**: yamiyeh10
- **日期**: 2021-07-20 11:18:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 391. [Web]Q00-20210716002 修正Grid綁定check、radio且設額外產生輸入框，通知信設定以表單元件時通知信的Grid會跑版
- **Commit ID**: `2fe9395013d269014bf87b43bfc62717a2252d36`
- **作者**: 王鵬程
- **日期**: 2021-07-16 16:11:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 392. [內部]調整排版
- **Commit ID**: `2dc54aa45c1ba75d2bfc1aabf68e74be2ce7b4fb`
- **作者**: pinchi_lin
- **日期**: 2021-07-15 13:59:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 393. [Web]Q00-20210714001 修正Grid有綁定Checkbox時，關卡通知信設定以表單元件時，通知信的Grid會跑版
- **Commit ID**: `32c1f1796a23da887e265bfb19c6d7dd5d61964f`
- **作者**: 王鵬程
- **日期**: 2021-07-14 16:33:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 394. [Web]S00-20200821001 調整表單TextBox元件於可編輯模式下onblur時檢查資料型態
- **Commit ID**: `551f3dc8ec0c4f215a23b1f8940d796c8d6506dd`
- **作者**: cherryliao
- **日期**: 2021-07-13 18:41:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 395. [Web]Q00-20210713002修正表單頁籤簽核歷程置放位置選擇"top"且表單設計師設定"顯示流程簽核意見"為"NOT_SHOW"，待辦跟發起畫面的ESS表單上方會顯示"簽核意見"的文字
- **Commit ID**: `80bbd1c8bd3f6f7e87350e1e31bce11dd60afdaf`
- **作者**: 林致帆
- **日期**: 2021-07-13 14:03:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`

### 396. [Web]Q00-20210713001  修正在首頁模組中的追蹤流程區塊，進入ESSF03表單會被截斷一半
- **Commit ID**: `dc990ff95eb011a6dc4162341693143035e187b1`
- **作者**: 王鵬程
- **日期**: 2021-07-13 12:08:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 397. [Web]Q00-20210702002修正流程最後一關是通知任務，流程設計師在該關卡增加BasicType的流程變數到工具定義表，導致查看流程異常
- **Commit ID**: `c7624c15c9db3ae4faf9eaee3b83be2c09a2a469`
- **作者**: 林致帆
- **日期**: 2021-07-13 10:47:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 398. A00-20210630001 修正設定多語系後沒有設定的語系無法吃到預設值
- **Commit ID**: `3a6dec14cbd8e464d65df97b413b98291eded51a`
- **作者**: walter_wu
- **日期**: 2021-07-13 10:30:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/SelectElementDefinition.java`

### 399. [內部]調整排版
- **Commit ID**: `6663a0fe3f54febfa4e0e74b3ce1422574fbca5c`
- **作者**: pinchi_lin
- **日期**: 2021-07-12 17:12:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 400. [ESS]C01-20210629009 修正ESS表單簽核時上傳附件，移動端未顯示附件資訊的問題
- **Commit ID**: `8dfe8108f41d5924cf52f2ab0255871f784def49`
- **作者**: 詩雅
- **日期**: 2021-07-12 16:01:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/appform/AppFormXmlTag.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 401. [Web]Q00-20210709001 修正checkbox、radio元件已選擇新樣式的問題
- **Commit ID**: `be6433e6549576a82a881abc0bb6b67628753603`
- **作者**: cherryliao
- **日期**: 2021-07-09 11:20:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 402. [Web]Q00-20210708003 修正將Grid調整為可支援換行標籤<br>
- **Commit ID**: `4cc603dfe8b5ce94c349448a14b650ca84ee66b4`
- **作者**: 王鵬程
- **日期**: 2021-07-08 19:50:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 403. [Web]S00-20210122001 DataSource.query語法自動改呼叫使用ajax的query方法
- **Commit ID**: `e267941fab79d5904557d7d419a017e04f970040`
- **作者**: 林致帆
- **日期**: 2021-07-08 18:56:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

