{"比較資訊": {"專案ID": "NaNaXWeb", "倉庫路徑": "D:\\IDEA_workspace\\NaNaXWeb", "新分支": {"branch_name": "8.1.1.1_hotfix", "date": "2025-06-26 17:44:25", "message": "[PRODT]C01-20250626003 修正Web流程管理中設定關卡的表單存取控管設定無法儲存問題", "author": "yamiyeh10"}, "舊分支": {"branch_name": "release_8.1.1.1", "date": "2025-04-01 11:47:50", "message": "Revert \"[文件總結助手]調整若問答出現“抱歉，您问的问题尚未出现在文档中”就不顯示參考文件\"", "author": "周权"}, "比較時間": "2025-07-18 11:46:11", "新增commit數量": 10, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "62463c5cb865e9b18f08e942886a38a43c7a5cc7", "commit_訊息": "[PRODT]C01-20250626003 修正Web流程管理中設定關卡的表單存取控管設定無法儲存問題", "提交日期": "2025-06-26 17:44:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8437a57617e4ff88dee7274b776115c6e0b91ee6", "commit_訊息": "[SYSDT]C01-20250617001 修正Web系統管理中資料來源設定在編輯狀態下調整ID後儲存會發生後端接口調用失敗的問題", "提交日期": "2025-06-17 11:54:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b776a81ad26a84c03625540ad14e3c33a89eceea", "commit_訊息": "[文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题[補]", "提交日期": "2025-06-11 17:58:20", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.html", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "773c91d6e2bc7cdd126e0d36ba1adca7d01891fa", "commit_訊息": "[文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题[補]", "提交日期": "2025-06-11 17:44:51", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "81141aeaf4e9dc79e92e89b963e7fa126e7c555b", "commit_訊息": "[文件總結助手] 調整文件總結助手参数设定画面没有scrollBar的问题", "提交日期": "2025-06-11 16:04:40", "作者": "周权", "檔案變更": [{"檔案路徑": "AngularProjects/ChatFileModule/src/app/chatfile/integration-parameters-setting/integration-parameters/integration-parameters.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b60a0c02c7bd318ffd9f01ff023c1cce825dd9db", "commit_訊息": "[行業表單庫]Q00-20250505001 修正行業表單庫因資料撈取筆數限制導致範例表單顯示不全的問題[補]", "提交日期": "2025-05-05 15:47:58", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/CommonProgramModule/src/app/form/form-repository/form-repository.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ac8ed865f142e21e399eac14a762f9916e7d120", "commit_訊息": "[行業表單庫]Q00-20250505001 修正行業表單庫因資料撈取筆數限制導致範例表單顯示不全的問題", "提交日期": "2025-05-05 13:58:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/CommonProgramModule/src/app/form/form-repository/form-repository.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2cfa9a1f8496581233a568ef0e9a89b7d2bd86c1", "commit_訊息": "[PRODT]C01-20250417005 優化Web流程管理中表單存取控管設定欄位過多時滑鼠滾動會有卡頓問題", "提交日期": "2025-05-02 16:31:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "4d95b7b355d140409df29eace6081c8f2f756602", "commit_訊息": "[PRODT]Q00-20250416001 修正Web流程管理中設定關卡的表單存取控管設定無法開啟問題", "提交日期": "2025-04-18 10:24:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control-edit/form-access-control-edit.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-access-control/form-access-control.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-attachment-attributes/form-attachment-attributes.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/form-attachment-attributes/form-attachment-attributes.component.ts", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-variable/form-type/form-type.component.html", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-variable/form-type/form-type.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "c9536c55dfa624256d7e6d877efb4aafa5253cdf", "commit_訊息": "[SYSDT]C01-20250414004 修正Web系統管理中資料來源設定在編輯狀態下儲存會顯示重複ID訊息問題", "提交日期": "2025-04-15 15:31:07", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-access/sys-conf-drawer-access.component.ts", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}