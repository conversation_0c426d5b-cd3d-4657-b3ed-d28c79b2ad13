{% extends "base.html" %}

{% block title %}{{ page_title }} - BPM服務部好用工具{% endblock %}

{% block extra_css %}
<style>
    .detail-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .info-item {
        padding: 1rem 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .info-value {
        font-size: 1.1rem;
        color: #212529;
    }
    
    .password-field {
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 4px;
        border: 1px solid #dee2e6;
        display: inline-block;
        min-width: 200px;
    }
    
    .product-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 1rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }
    
    .product-badge.bpm {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .product-badge.hrm {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    
    .product-badge.tiptop {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    
    .product-badge.other {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    
    .oid-display {
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 4px;
        border: 1px solid #dee2e6;
        font-size: 0.9rem;
        word-break: break-all;
    }
    
    .connection-string {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        word-break: break-all;
        margin-top: 0.5rem;
    }
    
    .timestamp {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fas fa-building me-3"></i>
                {{ customer.company_name }}
            </h1>
            <p class="page-subtitle">客戶連線詳細資訊</p>
        </div>
    </div>

    <div class="row">
        <!-- 基本資訊 -->
        <div class="col-lg-8">
            <div class="detail-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>
                        <i class="fas fa-info-circle me-2"></i>
                        基本資訊
                    </h4>
                    <div class="product-badge {{ customer.product_type|lower }}">
                        {{ customer.product_type }}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-building me-2"></i>公司名稱
                            </div>
                            <div class="info-value">{{ customer.company_name }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-server me-2"></i>伺服器IP
                            </div>
                            <div class="info-value">{{ customer.server_ip }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-database me-2"></i>資料庫名稱
                            </div>
                            <div class="info-value">{{ customer.database_name }}</div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-plug me-2"></i>埠號
                            </div>
                            <div class="info-value">{{ customer.port or '1433' }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-user me-2"></i>使用者名稱
                            </div>
                            <div class="info-value">{{ customer.username }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-lock me-2"></i>密碼
                            </div>
                            <div class="info-value">
                                <div class="password-field" id="passwordField">
                                    ************************************
                                </div>
                                <button class="btn btn-outline-secondary btn-sm ms-2" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm ms-1" id="copyPassword">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {% if customer.description %}
                <div class="info-item">
                    <div class="info-label">
                        <i class="fas fa-comment me-2"></i>說明
                    </div>
                    <div class="info-value">{{ customer.description }}</div>
                </div>
                {% endif %}
            </div>

            <!-- 連線資訊 -->
            <div class="detail-card">
                <h4 class="mb-4">
                    <i class="fas fa-link me-2"></i>
                    連線資訊
                </h4>

                <div class="info-item">
                    <div class="info-label">
                        <i class="fas fa-code me-2"></i>連線字串
                    </div>
                    <div class="connection-string" id="connectionString">
                        Server={{ customer.server_ip }},{{ customer.port or '1433' }};Database={{ customer.database_name }};User Id={{ customer.username }};Password=************************************;
                    </div>
                    <button class="btn btn-outline-primary btn-sm mt-2" id="copyConnectionString">
                        <i class="fas fa-copy me-2"></i>複製連線字串
                    </button>
                </div>
            </div>
        </div>

        <!-- 側邊欄 -->
        <div class="col-lg-4">
            <!-- 操作按鈕 -->
            <div class="detail-card">
                <h5 class="mb-3">
                    <i class="fas fa-cogs me-2"></i>
                    操作
                </h5>
                <div class="d-grid gap-2">
                    <a href="/customers/edit/{{ customer.oid }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>編輯資訊
                    </a>
                    <button class="btn btn-danger" onclick="deleteCustomer('{{ customer.oid }}', '{{ customer.company_name }}')">
                        <i class="fas fa-trash me-2"></i>刪除客戶
                    </button>
                    <hr>
                    <a href="/customers" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回列表
                    </a>
                </div>
            </div>

            <!-- 系統資訊 -->
            <div class="detail-card">
                <h5 class="mb-3">
                    <i class="fas fa-info me-2"></i>
                    系統資訊
                </h5>
                
                <div class="info-item">
                    <div class="info-label">客戶ID (OID)</div>
                    <div class="oid-display">{{ customer.oid }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">建立時間</div>
                    <div class="timestamp">
                        {% if customer.created_at %}
                            {{ customer.created_at[:19].replace('T', ' ') }}
                        {% else %}
                            未知
                        {% endif %}
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-label">最後更新</div>
                    <div class="timestamp">
                        {% if customer.updated_at %}
                            {{ customer.updated_at[:19].replace('T', ' ') }}
                        {% else %}
                            未知
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 刪除確認模態框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">確認刪除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您確定要刪除客戶「<span id="deleteCustomerName"></span>」嗎？</p>
                <p class="text-danger small">此操作無法復原。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">確認刪除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const actualPassword = '{{ customer.password }}';
    let passwordVisible = false;
    let deleteOid = null;

    // 密碼顯示/隱藏切換
    $('#togglePassword').click(function() {
        const passwordField = $('#passwordField');
        const icon = $(this).find('i');
        
        if (passwordVisible) {
            passwordField.text('************************************');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
            passwordVisible = false;
        } else {
            passwordField.text(actualPassword);
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
            passwordVisible = true;
        }
    });

    // 複製密碼
    $('#copyPassword').click(function() {
        navigator.clipboard.writeText(actualPassword).then(function() {
            const btn = $('#copyPassword');
            const originalHtml = btn.html();
            btn.html('<i class="fas fa-check"></i>');
            btn.addClass('btn-success').removeClass('btn-outline-primary');
            
            setTimeout(function() {
                btn.html(originalHtml);
                btn.removeClass('btn-success').addClass('btn-outline-primary');
            }, 2000);
        });
    });

    // 複製連線字串
    $('#copyConnectionString').click(function() {
        const connectionString = `Server={{ customer.server_ip }},{{ customer.port or '1433' }};Database={{ customer.database_name }};User Id={{ customer.username }};Password=${actualPassword};`;
        
        navigator.clipboard.writeText(connectionString).then(function() {
            const btn = $('#copyConnectionString');
            const originalHtml = btn.html();
            btn.html('<i class="fas fa-check me-2"></i>已複製');
            btn.addClass('btn-success').removeClass('btn-outline-primary');
            
            setTimeout(function() {
                btn.html(originalHtml);
                btn.removeClass('btn-success').addClass('btn-outline-primary');
            }, 2000);
        });
    });

    // 刪除客戶
    window.deleteCustomer = function(oid, companyName) {
        deleteOid = oid;
        $('#deleteCustomerName').text(companyName);
        $('#deleteModal').modal('show');
    };

    $('#confirmDelete').click(function() {
        if (deleteOid) {
            $.ajax({
                url: `/customers/delete/${deleteOid}`,
                method: 'POST',
                success: function(data) {
                    if (data.success) {
                        window.location.href = '/customers';
                    } else {
                        alert('刪除失敗：' + data.error);
                    }
                },
                error: function() {
                    alert('刪除時發生網路錯誤');
                }
            });
        }
        $('#deleteModal').modal('hide');
    });
});
</script>
{% endblock %}
