# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.10.1
- **舊版本**: release_5.8.9.4
- **生成時間**: 2025-07-18 11:29:12
- **新增 Commit 數量**: 375

## 變更摘要

### <PERSON><PERSON><PERSON> (34 commits)

- **2024-03-26 16:55:42**: [內部]更新58101patch[補]
  - 變更檔案: 1 個
- **2024-03-22 16:55:56**: [內部]更新58101patch[補]
  - 變更檔案: 1 個
- **2024-03-21 15:58:51**: [內部]更新58101patch[補]
  - 變更檔案: 1 個
- **2024-03-21 15:35:43**: [內部]調整新版自動簽核邏輯[補]
  - 變更檔案: 1 個
- **2024-03-21 11:50:58**: [內部]調整新版自動簽核邏輯
  - 變更檔案: 1 個
- **2024-03-15 13:44:13**: [內部]更新58101patch
  - 變更檔案: 1 個
- **2024-03-12 17:11:32**: [流程引擎]A00-20240307001 修正自動簽核2.當關卡設定為與前一關相同者則跳過時，若該關卡被退回重辦在重新執行時，自動簽核會失效的異常
  - 變更檔案: 1 個
- **2024-03-06 17:01:49**: [ISO]S00-20230703001 ISO閱讀文件支持同時開啟多份閱讀文件
  - 變更檔案: 1 個
- **2024-03-05 18:00:32**: [WebService]Q00-20221117006 停用addCustomParallelAndSerialActivity服務，建議以 addCustomParallelActivity 服務代替
  - 變更檔案: 1 個
- **2024-03-01 16:37:17**: [內部]Q00-20240301003 增加Queue啟動下一關是服務任務的相關屬性的log[補]
  - 變更檔案: 1 個
- **2024-03-01 16:24:38**: [內部]Q00-20240301003 增加Queue啟動下一關是服務任務的相關屬性的log
  - 變更檔案: 2 個
- **2024-03-01 13:39:39**: [Web]Q00-20231218001 修正系統參數(processaccessor.activity.name.format.unit-duty=true)時，簽核歷程的關卡若為進行中尚未簽核時，關卡名稱應顯示為[部門-職稱]，而非[關卡定義名稱][補修正]
  - 變更檔案: 1 個
- **2024-03-01 10:46:40**: [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口(補修正)
  - 變更檔案: 1 個
- **2024-02-27 14:54:46**: [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口(補修正)
  - 變更檔案: 1 個
- **2024-02-20 17:16:54**: [流程引擎]Q00-20240220002 修正關卡通知信的內容有設定整張表單，且流程的核決關卡若因退回、取回重辦而需要重新展開核決關卡時，前一關往核決關卡派送時會有NullPointerException的錯誤
  - 變更檔案: 1 個
- **2024-02-16 17:00:28**: [內部]Q00-20240216004 增加關卡是否完成可以繼續往下派送的log
  - 變更檔案: 1 個
- **2024-02-16 14:09:44**: [流程引擎]Q00-20240216003 流程引擎-簡易流程圖預解析邏輯優化
  - 變更檔案: 6 個
- **2024-02-01 17:21:34**: [流程引擎]Q00-20240201005 修正5894版本，若通知關卡是透過追蹤流程進入表單畫面時，會提示「查詢不到此流程的資料」也無法顯示表單畫面；以及追蹤頁面詳細流程點通知關卡的活動實例也無法顯示任何工作事項
  - 變更檔案: 4 個
- **2024-01-31 15:52:17**: [流程引擎]Q00-20240123002 因流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制[補]
  - 變更檔案: 1 個
- **2024-01-31 15:05:53**: [流程引擎]Q00-20240131002 流程進版或匯入時，增加檢核流程的關卡連接線的From關卡及To關卡是否存在關卡定義中，若From 或 To 對應的關卡不存在時，將其刪除，避免影響流程運作
  - 變更檔案: 1 個
- **2024-01-26 17:23:07**: [SAP]Q00-20240126002 調整SAP整合回寫內容到表單Grid時，增加判斷Grid定義是否有該欄位(Column)，當Column存在時，則插入或更新Column資料；若定義不存在則忽略該筆資料
  - 變更檔案: 1 個
- **2024-01-25 13:48:06**: [內部]Q00-20240125001 調整清除二階快取的log層級由warn改為debug
  - 變更檔案: 1 個
- **2024-01-23 15:25:12**: [流程引擎]Q00-20240123004 修正關卡設定自動簽核4與流程上相同簽核者(不含發起者)跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，無法派送到下一關
  - 變更檔案: 1 個
- **2024-01-23 15:01:34**: [流程引擎]Q00-20240123003 修正進入待辦或追蹤頁面，且流程進入核決關卡時，若核決關卡定義有髒資料或多組相同代號時可能會導致開啟畫面錯誤，因此增加過濾髒資料的邏輯
  - 變更檔案: 1 個
- **2024-01-23 14:41:22**: [流程引擎]Q00-20240123002 流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制
  - 變更檔案: 1 個
- **2024-01-04 16:31:21**: [流程引擎]Q00-*********** 修正模擬使用者簽核關卡後，追蹤流程頁面偶發會出現交易異常的錯誤(情境為核決關卡內的下一關處理者為需自動簽核跳關時容易發生)
  - 變更檔案: 1 個
- **2023-12-27 13:55:31**: [流程引擎]S00-20230710001 優化流程引擎對於離職轉派的支持度
  - 變更檔案: 2 個
- **2023-12-26 16:44:52**: [Web]Q00-20231226002 修正待辦清單頁進入ESS表單時或表單頁面點擊「處理上、下個工作」或簽核後直接跳下一個待辦時，工具列上方的Title欄位未正確顯示流程名稱的異常
  - 變更檔案: 1 個
- **2023-12-21 14:22:15**: [流程引擎]Q00-20231221003 修正流程引擎啟用「啟動下一關工作(workItem)時要執行的jndi服務」時，若當前結束的關卡為服務任務時，觸發「啟動下一關工作(workItem)時要執行的jndi服務」所傳遞的資料不完整的異常
  - 變更檔案: 1 個
- **2023-12-20 15:10:55**: [流程引擎]Q00-*********** 修正新版自動簽核邏輯，當核決關卡下一關為核決關卡，且需要判斷自動簽核時，且兩個核決關卡都為相同處理者時應自動跳關，但偶發會無法自動跳關
  - 變更檔案: 1 個
- **2023-12-18 15:31:40**: [Web]修Q00-20231218001 修正系統參數(processaccessor.activity.name.format.unit-duty=true)時，簽核歷程的關卡若為進行中尚未簽核時，關卡名稱應顯示為[部門-職稱]，而非[關卡定義名稱]
  - 變更檔案: 1 個
- **2023-12-13 15:44:25**: [SAP]Q00-20231213003 修正SAP整合服務，當回傳的資料類型為絕對位置表單Grid時，可能會有GridColumnId與GridValue順序錯誤的異常
  - 變更檔案: 1 個
- **2023-11-27 15:20:06**: [流程引擎]Q00-20231127001 修正關卡設定多人都要簽核且設定自動簽核2，與前一關相同者，若前一關為核決關卡，且未實際展開核決關卡時，流程無法派送至下一關的異常
  - 變更檔案: 1 個
- **2023-11-24 17:33:54**: [SAP]Q00-20231124005 優化SAP整合服務，當整合的資料類型為Grid時，同時支持RWD表單Grid及絕對位置表單Grid
  - 變更檔案: 1 個

### 林致帆 (41 commits)

- **2024-03-26 16:21:41**: [Cosmos]更新COSMOS WebServicesApplication內容
  - 變更檔案: 3 個
- **2024-03-25 15:25:26**: [Cosmos]更新出貨表單95張
  - 變更檔案: 96 個
- **2024-03-22 16:30:13**: [Cosmos]更新預設出貨表單流程 [補修正]
  - 變更檔案: 1 個
- **2024-03-22 11:01:16**: [Cosmos]更新預設出貨表單流程
  - 變更檔案: 4 個
- **2024-03-19 12:05:32**: [Web]Q00-20240319003 修正顯示流程頁點擊轉由他人處理會無法執行
  - 變更檔案: 1 個
- **2024-03-12 10:19:35**: [Web]Q00-20240312001 停用Web服務的addCustomParallelAndSerialActivity服務，建議以 addCustomParallelActivity 服務代替
  - 變更檔案: 1 個
- **2024-03-05 14:24:48**: [TIPTOP]Q00-20240305001 修正簽核歷程頁該使用者登入後無權限查看時，會造成授權人數被占據
  - 變更檔案: 1 個
- **2024-03-04 17:42:20**: [流程引擎]Q00-20240304002 修正Ajax核決加簽方法因為ContainerOID無法加入null內容造成異常
  - 變更檔案: 6 個
- **2024-03-01 09:13:35**: [Cosmos]修正表單同步會新產生WorkFlowERP表單分類
  - 變更檔案: 3 個
- **2024-02-27 08:46:23**: [SSO]Q00-20240227001 調整Athena三方待辦取得之使用者改透過接口取得租戶代號綁定的BPM使用者
  - 變更檔案: 1 個
- **2024-02-23 14:43:11**: [SSO]Q00-20240223001 調整Athena SSO驗證判斷不需增加Athena平台設置的應用程式代號是否命名為BPM
  - 變更檔案: 1 個
- **2024-02-22 15:45:33**: [Web]Q00-20240222003 新增防呆：因表單有不存在之單身的髒資料造成單據無法開啟
  - 變更檔案: 1 個
- **2024-02-22 11:09:57**: [SSO]Q00-20240221004 修正使用SSO登入BPM時開啟的介面都是英文版[補修正]
  - 變更檔案: 1 個
- **2024-02-21 14:02:34**: [SSO]Q00-20240221004 修正使用SSO登入BPM時開啟的介面都是英文版
  - 變更檔案: 1 個
- **2024-02-21 10:47:15**: [ESS]Q00-20240221001 調整ESS簽核單據增加防呆避免串單
  - 變更檔案: 3 個
- **2024-02-15 14:24:16**: [資安]V00-20240123001 修正Vulnerable Component漏洞議題
  - 變更檔案: 150 個
- **2024-02-02 14:36:53**: [雙因素模組]Q00-20240202001 修正信任端點資訊有過期資料會造成每次登入都需重複驗政
  - 變更檔案: 1 個
- **2022-12-09 17:09:53**: [Cosmos]更新Cosmos發單邏輯
  - 變更檔案: 3 個
- **2024-02-01 09:06:52**: [ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊 [補修正]
  - 變更檔案: 1 個
- **2024-02-01 08:56:27**: [TIPTOP]Q00-20240201001 新增TIPTOP呼叫服務時間的Log
  - 變更檔案: 1 個
- **2024-02-01 08:48:35**: [ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊 [補修正]
  - 變更檔案: 1 個
- **2024-01-31 17:22:31**: [ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊
  - 變更檔案: 1 個
- **2024-01-26 16:52:40**: [COSMOS]更新COSMOS的wsdl檔案
  - 變更檔案: 2 個
- **2024-01-18 18:02:11**: [表單設計師]S00-*********** Dialog系列元件開窗類型選擇部門新增選項"顯示失效部門"
  - 變更檔案: 10 個
- **2024-01-18 11:05:51**: [Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache
  - 變更檔案: 5 個
- **2024-01-18 08:15:14**: Revert "[Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache"
  - 變更檔案: 5 個
- **2024-01-17 13:48:14**: [Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache
  - 變更檔案: 5 個
- **2024-01-08 18:10:45**: [Web]Q00-20240102002 修正系統參數邏輯改從SystemVariableCache取得設定
  - 變更檔案: 11 個
- **2024-01-08 15:38:41**: [ISO]S00-*********** "ISO部門資訊批次更新"表單調整原部門可選取到失效部門
  - 變更檔案: 6 個
- **2024-01-08 15:38:03**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-01-08 15:32:26**: Revert "[ISO]S00-*********** "ISO部門資訊批次更新"表單調整原部門可選取到失效部門"
  - 變更檔案: 6 個
- **2024-01-08 15:25:43**: [ISO]S00-*********** "ISO部門資訊批次更新"表單調整原部門可選取到失效部門
  - 變更檔案: 6 個
- **2023-12-21 15:36:23**: [TIPTOP]Q00-20231221004 修正TIPTOP發單若發單失敗未產生流程，解決異常問題後再次發單還是失敗
  - 變更檔案: 1 個
- **2023-11-01 14:04:59**: [雙因素認證]Q00-*********** 修正LDAP登入輸入帳號錯誤不該影響登入畫面進錯誤頁面
  - 變更檔案: 1 個
- **2023-12-19 11:03:18**: [BPM APP]C01-20221208002 移除移動端WorkFlowERP查看過去審批流程功能[補修正]
  - 變更檔案: 1 個
- **2023-12-19 11:01:29**: [WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能 [補修正]
  - 變更檔案: 1 個
- **2023-12-06 11:13:41**: [雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證[補修正]
  - 變更檔案: 1 個
- **2023-12-05 15:26:32**: [Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項
  - 變更檔案: 1 個
- **2023-11-28 13:35:41**: [ESS]Q00-20231128003 調整缺席紀錄方法相容帶有單身資料的ESS單據
  - 變更檔案: 1 個
- **2023-11-27 16:31:42**: [Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊
  - 變更檔案: 1 個
- **2023-11-24 10:45:57**: [雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證
  - 變更檔案: 6 個

### lorenchang (36 commits)

- **2024-03-26 14:31:27**: [流程引擎]Q00-20240326004 修正未釋放JDBC連線可能導致連線占滿後出現異常：IJ000453: Unable to get managed connection for java:/NaNaDS(補修正)
  - 變更檔案: 1 個
- **2024-03-26 11:34:01**: [流程引擎]Q00-20240326004 修正未釋放JDBC連線可能導致連線占滿後出現異常：IJ000453: Unable to get managed connection for java:/NaNaDS
  - 變更檔案: 1 個
- **2024-03-21 15:42:29**: [內部]修正新安裝在Linux環境無法正常啟動的異常
  - 變更檔案: 3 個
- **2024-03-18 18:03:22**: [內部]修正 5.8.9.4 Oracle 更新 License 資訊的 Update SQL 語法
  - 變更檔案: 1 個
- **2024-03-07 15:23:32**: Merge branch 'develop_v58' into develop_formrepository
- **2024-03-05 17:24:53**: [行業表單庫]Domain 及 DAO 移至 nana-app.ear 之相應調整2
  - 變更檔案: 4 個
- **2024-03-05 17:17:34**: Merge branch 'develop_v58' into develop_formrepository
- **2024-03-05 17:15:10**: [內部]平台共用程式優化，Paging 增加方法提供後端調用快速產生使用 OID、ID 取得指定 Doamin 的條件(補修正)
  - 變更檔案: 1 個
- **2024-03-05 17:03:56**: [內部]平台共用程式優化，Paging 增加方法提供後端調用快速產生使用 OID、ID 取得指定 Doamin 的條件
  - 變更檔案: 1 個
- **2024-03-05 15:32:18**: [Secudocx]更新程式路徑
  - 變更檔案: 2 個
- **2024-03-05 15:23:46**: [行業表單庫]Domain 及 DAO 移至 nana-app.ear 之相應調整
  - 變更檔案: 10 個
- **2024-03-05 10:30:22**: Merge branch 'develop_v58' into develop_formrepository
- **2024-03-05 10:29:53**: [內部]平台共用程式優化，更新 BaseDomainObject 欄位命名(補修正2)
  - 變更檔案: 3 個
- **2024-03-05 10:06:27**: Merge branch 'develop_v58' into develop_formrepository
- **2024-03-05 09:28:32**: [內部]平台共用程式優化，更新 BaseDomainObject 欄位命名
  - 變更檔案: 3 個
- **2024-03-04 16:37:34**: Merge branch 'develop_v58' into develop_formrepository
- **2024-03-04 16:21:05**: [內部]平台共用程式優化(補修正2)
  - 變更檔案: 4 個
- **2024-03-04 11:02:34**: [行業表單庫]後端程式初版
  - 變更檔案: 7 個
- **2024-03-01 15:46:00**: [內部]新增 Demo Postman 設定
  - 變更檔案: 1 個
- **2024-03-01 15:44:28**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-03-01 15:40:12**: [內部]平台共用程式優化(補修正)
  - 變更檔案: 1 個
- **2024-03-01 15:22:58**: [內部]平台共用程式優化，請更新其它模組 git 及 nana-service-client.jar
  - 變更檔案: 39 個
- **2024-02-21 16:05:35**: Revert "[內部]調整平台統一工具程式路徑"
  - 變更檔案: 8 個
- **2024-02-21 15:57:11**: [內部]調整平台統一工具程式路徑
  - 變更檔案: 8 個
- **2024-02-02 14:39:47**: [內部]新增 HTML 工具類(HtmlUtils)
  - 變更檔案: 1 個
- **2024-02-02 14:38:24**: Revert "[內部]新增 HTML 工具類(HtmlUtils)"
  - 變更檔案: 4 個
- **2024-02-02 14:38:15**: Revert "[web] Q00-20240201002 流程主旨中的某个字段加上颜色标注，在BPM中的流程列表里主旨显示正常，但是在邮件提醒里主旨里显示异常问题修正"
  - 變更檔案: 1 個
- **2024-02-02 11:22:08**: [內部]新增 HTML 工具類(HtmlUtils)
  - 變更檔案: 4 個
- **2024-01-29 16:46:03**: [Cosmos]更新整合所需內容
  - 變更檔案: 10 個
- **2024-01-26 15:36:43**: [微服務註冊器]初始化開發環境
  - 變更檔案: 2 個
- **2024-01-25 16:00:54**: [Cosmos]新增範本檔
  - 變更檔案: 4 個
- **2024-01-25 15:00:55**: [內部]優化 Linux 安裝檔
  - 變更檔案: 3 個
- **2024-01-08 16:34:20**: [內部]更新平台統一工具庫 StringUtils
  - 變更檔案: 2 個
- **2023-12-20 15:26:13**: [流程封存]修正若特製流程存在條件時，會出現ConditionDefinition PK 重覆導致無法封存的異常
  - 變更檔案: 1 個
- **2023-12-18 16:35:01**: [內部]新增平台統一工具庫
  - 變更檔案: 9 個
- **2023-12-14 13:20:41**: [內部]配合 NaNaXWeb 修正 Http Message Converter 轉換物件到 JSON 時 OID 會變成 oid 的問題，透過 @JsonProperty("OID") 強制轉換為 OID
  - 變更檔案: 8 個

### 邱郁晏 (82 commits)

- **2024-03-26 14:28:54**: [Secudocx] V00-*********** 新增以柔轉檔卡控副檔名
  - 變更檔案: 7 個
- **2024-03-26 13:54:49**: [Secudocx] V00-*********** 修正以柔加密，ISO攜出段無下載加密問題
  - 變更檔案: 1 個
- **2024-03-26 10:40:31**: [Secudocx] V00-20240326001 修正以柔在線閱讀下載按鈕沒有進行加密動作
  - 變更檔案: 1 個
- **2024-03-22 15:47:11**: [Secudocx] V00-20240322003 修正ISO攜出段，若設定為ZIP時，資料夾會經過加密作業
  - 變更檔案: 1 個
- **2024-03-21 15:50:46**: [SecuDocx] 以柔調整CreateSQL
  - 變更檔案: 3 個
- **2024-03-21 11:30:44**: [Web] Q00-20240321001 新增LDAP 設定SSL參數定義憑證
  - 變更檔案: 1 個
- **2024-03-21 11:20:25**: [Web] Q00-20240321002 standalone-full.xml預設出貨預設不顯示版本資訊
  - 變更檔案: 1 個
- **2024-03-20 10:37:27**: [SecuDocx] 以柔整合調整多語系
  - 變更檔案: 1 個
- **2024-03-19 18:26:44**: [SecuDocx] 以柔整合調整寫法(補修正)
  - 變更檔案: 2 個
- **2024-03-19 18:01:07**: [SecuDocx] 以柔整合調整寫法
  - 變更檔案: 15 個
- **2024-03-13 10:33:41**: [Web] V00-20240312005 修正產品授權註冊模組類型內容為空問題(補)
  - 變更檔案: 1 個
- **2024-03-13 10:01:07**: [Web] V00-20240312005 修正產品授權註冊模組類型內容為空問題
  - 變更檔案: 3 個
- **2024-03-12 10:07:13**: [SecuDocx] 調整模組授權SQL(補)
  - 變更檔案: 2 個
- **2024-03-12 10:05:22**: [SecuDocx] 調整模組授權SQL
  - 變更檔案: 1 個
- **2024-03-11 15:37:11**: [Web] Q00-20240308002 調整Web終止流程Confirm視窗變為ShowDialog的方式呈現(補)
  - 變更檔案: 1 個
- **2024-03-11 14:44:11**: [Web] Q00-20240308002 調整Web終止流程Confirm視窗變為ShowDialog的方式呈現(補)
  - 變更檔案: 1 個
- **2024-03-08 16:09:09**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-03-08 16:08:45**: [Web] Q00-20240308002 調整Web終止流程Confirm視窗變為ShowDialog的方式呈現
  - 變更檔案: 1 個
- **2024-03-05 17:16:52**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-03-05 17:16:19**: [Web] V00-20240304001 修正searchFormDetail連結沒有檢核流程參與者是否有權限查看
  - 變更檔案: 1 個
- **2024-03-05 15:19:32**: [Secudocx] 新增Secudocx專案多語系
  - 變更檔案: 1 個
- **2024-03-05 14:40:26**: Merge branch 'develop_secudocx' into develop_v58
  - 變更檔案: 12 個
- **2024-03-05 14:32:53**: [Secudocx] 新增SQL指令
  - 變更檔案: 9 個
- **2024-03-05 13:41:13**: [Secudocx] 新增模組維護作業
  - 變更檔案: 1 個
- **2024-03-05 11:36:34**: [Secudocx] 新增模組維護作業
  - 變更檔案: 2 個
- **2024-02-29 17:10:00**: [Web] Q00-20240229001 調整外部連結信使用者，看不到「發送通知」按鈕問題。
  - 變更檔案: 2 個
- **2024-02-29 14:27:45**: [Secudocx] 新增BPM模組授權卡控、前端按鈕觸發按鈕
  - 變更檔案: 6 個
- **2024-02-22 10:00:36**: Revert "[Web] Q00-20240221003 修正追蹤流程匯出Excel功能SQL優化"
  - 變更檔案: 1 個
- **2024-02-21 17:19:08**: [Web] Q00-20240221003 修正追蹤流程匯出Excel功能SQL優化
  - 變更檔案: 1 個
- **2024-02-21 17:16:45**: Revert "[Web] Q00-20240221004 修正追蹤流程匯出Excel功能SQL優化"
  - 變更檔案: 1 個
- **2024-02-21 14:03:21**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-02-21 14:02:58**: [Web] Q00-20240221004 修正追蹤流程匯出Excel功能SQL優化
  - 變更檔案: 1 個
- **2024-02-20 17:30:12**: [SecuDox] 新增ISO段下載發布檔
  - 變更檔案: 4 個
- **2024-02-16 10:55:25**: [Web] Q00-20240216002 修正checkBox若設定為唯讀時，預設選項樣式不一致，且無法取消預設選項問題
  - 變更檔案: 1 個
- **2024-02-16 10:49:45**: [Web] Q00-20240216001 修正RadioButton若設定為唯讀時，會多預設選項(補)
  - 變更檔案: 1 個
- **2024-02-16 10:30:28**: [Web] Q00-20240216001 修正RadioButton若設定為唯讀時，會多預設選項
  - 變更檔案: 1 個
- **2024-02-15 17:19:30**: [SecuDox] 修正郵件落地功能異常問題。
  - 變更檔案: 1 個
- **2024-02-05 11:15:00**: [Oauth] Q00-20240205001 修正取得資料庫寫法未增加釋放連線
  - 變更檔案: 2 個
- **2024-02-01 11:00:55**: [SecuDox] 新增Mail攜出加密功能
  - 變更檔案: 8 個
- **2024-01-30 12:00:41**: [Web] Q00-20240130001 修正使用者名字有「𣶏」特殊字，流程派送會重複增加問題
  - 變更檔案: 1 個
- **2024-01-29 18:12:13**: [SecuDocx] 修改以柔整合段，上傳下載調整參數。
  - 變更檔案: 9 個
- **2024-01-26 15:16:43**: [SecuDocx] Base段，上傳下載以柔加解密
  - 變更檔案: 7 個
- **2024-01-23 11:17:39**: [Web] Q00-20240123001 調整信件樣板設置為整張表單時，Grid元件跑版問題
  - 變更檔案: 1 個
- **2024-01-18 13:59:51**: [Web] Q00-*********** 調整有不同組織相同部門Id時，部門主管首頁顯示在塗總處理量異常問題(補修正)
  - 變更檔案: 7 個
- **2024-01-17 16:55:49**: [Web] Q00-*********** 調整有不同組織相同部門Id時，部門主管首頁顯示在塗總處理量異常問題
  - 變更檔案: 5 個
- **2024-01-17 11:09:12**: [組織同步] Q00-20240116002 調整組織同步部門ID時，自動刪除空白字符(補修正)
  - 變更檔案: 3 個
- **2024-01-16 11:37:34**: [組織同步] Q00-20240116002 調整組織同步部門ID時，自動刪除空白字符
  - 變更檔案: 1 個
- **2024-01-15 16:35:14**: [附件擴充] 調整系統變數file.encryption.management.jndiname不須重啟即生效
  - 變更檔案: 1 個
- **2024-01-15 16:35:14**: [附件擴充] 調整系統變數file.encryption.management.jndiname不須重啟即生效
  - 變更檔案: 1 個
- **2024-01-15 09:12:42**: [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口(補修正)
  - 變更檔案: 1 個
- **2024-01-04 15:54:45**: [Web] Q00-*********** 調整顯示首頁按鈕權限，從Session方式改為存入UserProfile資訊內(補修正)
  - 變更檔案: 1 個
- **2024-01-08 17:07:32**: [Web] Q00-20240102004 修正系統參數:忽略HTML TAG，修改後無須重啟即生效
  - 變更檔案: 50 個
- **2024-01-08 15:34:21**: [Web] Q00-*********** 調整批次離職工作轉派，選取人員為空時拋undefined問題，新增防呆
  - 變更檔案: 1 個
- **2024-01-08 11:52:45**: [Web] Q00-*********** 調整絕對位置表單進版時，部分表單欄位反黑異常問題，新增防呆
  - 變更檔案: 1 個
- **2024-01-05 16:15:46**: [Web] Q00-*********** 調整顯示首頁按鈕權限，從Session方式改為存入UserProfile資訊內(補修正)
  - 變更檔案: 1 個
- **2024-01-05 10:27:05**: [組織同步] S00-20210120001 調整組織同步部門失效時，需過濾離職人員
  - 變更檔案: 1 個
- **2024-01-04 15:54:45**: [Web] Q00-*********** 調整顯示首頁按鈕權限，從Session方式改為存入UserProfile資訊內
  - 變更檔案: 4 個
- **2024-01-04 10:53:36**: [組織同步] Q00-20240103003 修正同步部門生失效時，若子部門已生效時，無法正常同步
  - 變更檔案: 1 個
- **2024-01-02 17:05:30**: [Web] Q00-20240102006 調整部分系統變數無須重啟即生效
  - 變更檔案: 11 個
- **2023-12-29 15:59:41**: [組織同步] S00-20230201001 調整HR組織同步，郵件設置改取系統管理中的郵件設定(補修正)
  - 變更檔案: 1 個
- **2023-12-29 14:53:02**: [組織同步] Q00-20231229004 調整Users表的mailAddress欄位上限至100，以避免中介表mailAddress大小超過50導致同步失敗
  - 變更檔案: 6 個
- **2023-12-25 10:36:28**: [Web] Q00-20231225001 調整URL為traceProcessFromExternalWeb支持「記住我」直接登入功能
  - 變更檔案: 1 個
- **2023-12-20 16:53:17**: [Web] Q00-*********** 修正小螢幕點選流程時，結案狀態未顯示問題(補)
  - 變更檔案: 1 個
- **2023-12-20 14:09:25**: [Web] Q00-*********** 修正小螢幕點選流程時，結案狀態未顯示問題
  - 變更檔案: 2 個
- **2023-12-20 10:38:22**: [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口(補修正)
  - 變更檔案: 1 個
- **2023-12-18 18:09:13**: [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口
  - 變更檔案: 25 個
- **2023-12-18 11:30:10**: [Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題(補)
  - 變更檔案: 2 個
- **2023-12-15 10:12:50**: [Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題(補)
  - 變更檔案: 1 個
- **2023-12-15 10:08:52**: [Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題
  - 變更檔案: 1 個
- **2023-12-13 13:59:44**: [Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題
  - 變更檔案: 1 個
- **2023-12-12 11:58:36**: [組織同步] Q00-20231212001 調整HR同步職務、職稱、角色項目時，自動刪除多餘空白(補修正)
  - 變更檔案: 4 個
- **2023-12-12 11:46:58**: [組織同步] Q00-20231212001 調整HR同步職務、職稱、角色項目時，自動刪除多餘空白
  - 變更檔案: 6 個
- **2023-12-11 14:24:40**: [附件擴充] 在線閱覽新增橋接機制(補)
  - 變更檔案: 1 個
- **2023-12-08 17:25:14**: [附件擴充] 在線閱覽新增橋接機制
  - 變更檔案: 1 個
- **2023-12-08 15:36:48**: [ISO] S00-20231026002 新增鼎新轉檔工具上傳PNG功能
  - 變更檔案: 1 個
- **2023-12-05 17:46:00**: [附件擴充] 修正參數未開啟時判斷邏輯異常問題
  - 變更檔案: 1 個
- **2023-12-05 15:01:32**: [附件擴充] 修正參數未開啟時判斷邏輯異常問題
  - 變更檔案: 8 個
- **2023-12-04 18:08:08**: [Web] V00-20231204001 修正工作轉派使用者，原處理者無法查閱已轉派的工作清單問題
  - 變更檔案: 1 個
- **2023-12-04 17:13:28**: [Web] Q00-20231204005 修正BPM授權數不足時，寄件人並非系統管理中的設定
  - 變更檔案: 1 個
- **2023-12-04 14:10:17**: [流程引擎] Q00-20231204002 修正退回重辦系統通知變數未被正常置換問題
  - 變更檔案: 1 個
- **2023-11-28 17:29:23**: [附件擴充] 在線閱讀新增發布檔下載接口
  - 變更檔案: 4 個
- **2023-11-24 15:00:42**: [流程引擎] Q00-20231124004 修正批次簽核造成重複寄信問題
  - 變更檔案: 1 個

### liuyun (45 commits)

- **2024-03-25 17:55:20**: [Web] Q00-20240319001 修正Grid勾选table模式显示异常问题[补]
  - 變更檔案: 1 個
- **2024-03-19 10:09:35**: [Web] Q00-20240319001 修正Grid勾选table模式显示异常问题
  - 變更檔案: 1 個
- **2024-03-15 15:11:41**: [Web] Q00-20240315001 修正开窗选择后无法关闭问题
  - 變更檔案: 1 個
- **2024-03-14 10:30:48**: [其他] Q00-20230828003 修改 ReportDesignerDefinition 资料表 sqlConditionLists 栏位字段类型(修改格式)[补]
  - 變更檔案: 2 個
- **2024-03-13 14:24:14**: [Web] V00-20240312003 修正页面列表栏位width和出现滚轴问题
  - 變更檔案: 1 個
- **2024-03-12 10:56:24**: [Web] V00-20240312002 修正管理員-其它設定-版本資訊頁面異常
  - 變更檔案: 1 個
- **2024-03-11 16:39:36**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
  - 變更檔案: 1 個
- **2024-03-11 16:39:02**: [其他] Q00-20230828003 修改 ReportDesignerDefinition 资料表 sqlConditionLists 栏位字段类型[补]
  - 變更檔案: 1 個
- **2024-02-27 14:21:47**: [TIPTOP] S00-20231222001 调用TIPTOP接口增加流程封存还原预防
  - 變更檔案: 4 個
- **2024-02-22 15:16:29**: [Web] Q00-20240222002 修正sql注册器中书写sql的from字段后不带空格用换行导致sql执行错误
  - 變更檔案: 1 個
- **2024-02-22 10:25:32**: [Web] Q00-20240222001 修正列印模式FormUtil.getValue获取不到Textbox和HiddenTextbox值
  - 變更檔案: 2 個
- **2024-02-21 17:52:16**: [Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题(防呆)[补]
  - 變更檔案: 2 個
- **2024-02-20 14:22:49**: [Web] Q00-20240220001 修正转派工作人员意见内容会trim
  - 變更檔案: 1 個
- **2024-02-05 16:49:00**: [Web] Q00-20240205003 修正自定义开窗autocomplete补全列表显示在页面下方
  - 變更檔案: 1 個
- **2024-02-02 17:51:56**: [Web] Q00-20240202002 修正通过服务任务给表单赋值触发警告问题
  - 變更檔案: 1 個
- **2024-02-01 15:25:55**: [Web] Q00-20240201003 修正汇入excel时出现undefined(reading toString)异常信息
  - 變更檔案: 1 個
- **2024-01-30 10:58:06**: [Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题[补]
  - 變更檔案: 1 個
- **2024-01-30 09:49:48**: [Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题
  - 變更檔案: 3 個
- **2024-01-17 11:10:49**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-01-17 11:10:22**: [Web] Q00-20240117002 修正通知信进入待办阅读次数加2
  - 變更檔案: 2 個
- **2024-01-15 13:57:56**: [Web] Q00-20240115001 修正LDAP账号登录失败，密码错误次数叠加问题
  - 變更檔案: 1 個
- **2024-01-10 14:11:17**: [Web] S00-20231221001 提供维护样板可调用单笔流程封存还原接口
  - 變更檔案: 3 個
- **2024-01-10 13:17:14**: [Web] Q00-20240110002 修改跳过关卡预设意见中去掉<br>
  - 變更檔案: 3 個
- **2024-01-09 17:47:20**: [Web] Q00-20240102003 修正系統參數邏輯改從SystemVariableCache取得設定
  - 變更檔案: 15 個
- **2024-01-03 17:41:38**: [Web] Q00-20240103002 修正checkbox绑定其他checkbox元件，存储表单后无法更改绑定的元件
  - 變更檔案: 1 個
- **2024-01-03 17:06:02**: [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正-2)
  - 變更檔案: 1 個
- **2024-01-03 13:57:55**: [Web] Q00-20240103001 修正grid设置冻结栏位后设置样式显示错误
  - 變更檔案: 1 個
- **2024-01-02 15:14:28**: [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正)
  - 變更檔案: 1 個
- **2023-12-29 10:57:45**: [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常
  - 變更檔案: 1 個
- **2023-12-26 11:32:14**: [Web] Q00-20231226001 附件名称包含特殊字符(𡘙)，流程派送后显示无限增长
  - 變更檔案: 1 個
- **2023-12-25 16:12:29**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2023-12-25 15:58:46**: [TIPTOP] S00-20231221001 新增TIPTOP回寫關卡支持錯誤訊息顯示在畫面上
  - 變更檔案: 3 個
- **2023-12-25 15:38:07**: Revert "[Web] S00-20231221001 TIPTOP回寫關卡支持錯誤訊息顯示在畫面上"
  - 變更檔案: 3 個
- **2023-12-25 15:07:27**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2023-12-25 15:01:55**: [TIPTOP] S00-20231221001 TIPTOP回寫關卡支持錯誤訊息顯示在畫面上
  - 變更檔案: 3 個
- **2023-12-25 15:01:55**: [Web] S00-20231221001 TIPTOP回寫關卡支持錯誤訊息顯示在畫面上
  - 變更檔案: 3 個
- **2023-12-21 17:22:27**: [其他] Q00-20230828003 修改 ReportDesignerDefinition 资料表 sqlConditionLists 栏位字段类型
  - 變更檔案: 7 個
- **2023-12-15 14:46:41**: [Web] Q00-20231215003 由url链接进入待办，清除wms_user_isURL的session
  - 變更檔案: 1 個
- **2023-12-13 16:29:37**: [Web] S00-20230810002 优化核决层级关卡名称显示
  - 變更檔案: 2 個
- **2023-12-13 13:22:56**: [Web] Q00-20231213002 不同模组下作业名称相同，导航页显示异常
  - 變更檔案: 2 個
- **2023-12-07 10:32:40**: [Web] Q00-20231207002 excel汇入资料到Grid中，单身加总不计算
  - 變更檔案: 1 個
- **2023-11-30 13:51:01**: [流程引擎] Q00-20230727001 在后端卡控保存密码是否符合规则，密码只能为数字、字母和特殊符号(!@#$%^&*.()/=-+) [补]
  - 變更檔案: 2 個
- **2023-11-29 16:44:03**: [Web] Q00-20231129005 修正serialNumber栏位显示问题
  - 變更檔案: 1 個
- **2023-11-29 14:39:15**: [流程引擎] S00-20230601003 增加系统参数设定流程解析遇到离职人员是否产生待办(默认不产生)
  - 變更檔案: 6 個
- **2023-11-24 10:33:10**: [Web] Q00-20231124002 修正流程主旨範本設定<#workItemName>显示N.A.
  - 變更檔案: 3 個

### 周权 (37 commits)

- **2024-03-25 14:46:38**: [Web]Q00-20240313002 修正[ID]Obj.setColumnwidth("＜GridcolumnID＞",100);写法没有效果 [补]
  - 變更檔案: 1 個
- **2024-03-21 16:25:44**: [WEB]Q00-20240321003 修正绝对位置表单通过流程追踪URL打开后没有横向滚动条
  - 變更檔案: 2 個
- **2024-03-20 11:42:32**: [WEB]V00-20240318001 修正钉钉待办同步 调整浏览器分辨率，表头和内容格线对不齐
  - 變更檔案: 1 個
- **2024-03-18 17:39:37**: [WEB]Q00-20240318002 修正從追踪流程開起，先點選絶對位置表單，再點RWD後，RWD畫面會變成絶對位置畫面寬度
  - 變更檔案: 1 個
- **2024-03-18 14:13:57**: [BPM APP]Q00-20240318001 钉钉待办同步与钉钉连线新增重试机制
  - 變更檔案: 2 個
- **2024-03-13 15:22:29**: [WEB]Q00-20240313002 修正[ID]Obj.setColumnwidth("＜GridcolumnID＞",100);写法没有效果
  - 變更檔案: 1 個
- **2024-03-12 17:22:48**: [BPM APP]Q00-20240312002 修正創建釘釘待辦失敗后，仍會跑更新待辦的問題
  - 變更檔案: 1 個
- **2024-03-07 16:01:36**: [SAP]Q00-20240307001 修正SAP固定值显示异常的问题
  - 變更檔案: 1 個
- **2024-02-28 16:18:38**: [BPM APP]Q00-20240228002 调整使用Oracel资料库 钉钉待办同步页面查询不到资料的问题
  - 變更檔案: 1 個
- **2024-02-28 16:13:51**: [BPM APP]Q00-20240228001 调整开启钉钉待办同步收不到推播的问题
  - 變更檔案: 3 個
- **2024-02-26 15:59:45**: [Web]Q00-20240226003 调整改变浏览器视窗大小后，客制JSP排序功能失效的问题
  - 變更檔案: 1 個
- **2024-02-21 14:59:44**: [Web]Q00-20240221006 修正Grid的新增，修改，删除Button设定背景色或文字颜色，汇入时颜色样式消失的问题
  - 變更檔案: 1 個
- **2024-02-05 17:17:12**: [Web]Q00-20240205004 修正监控、追踪流程点击"此分类全部"后再通过"未结案","以关闭","全部"条件筛选后流程条目显示不全的问题
  - 變更檔案: 2 個
- **2024-01-29 16:52:18**: [Web]Q00-20240129002 修正客制JSP文件，行点击事件onRowClick报错的问题
  - 變更檔案: 1 個
- **2024-01-25 15:30:15**: [Web]Q00-20240125002 发起流程时切换页签新增提示弹框，并调整弹框内容
  - 變更檔案: 3 個
- **2024-01-18 10:11:21**: [Web]Q00-20240118002 修正重启BPM后会出现多次'頁面閒置過久或伺服器端服務資訊異常，請重新登入'弹窗的问题
  - 變更檔案: 1 個
- **2024-01-16 13:30:29**: [Web]Q00-20240116003 修正当单身内容长度过大setColumnWidth没有效果的问题
  - 變更檔案: 1 個
- **2024-01-15 09:18:10**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-01-15 09:17:51**: [Web]S00-20231123001 BPM右上角工具列新增登出按钮
  - 變更檔案: 3 個
- **2024-01-09 14:56:37**: [Web]Q00-20240102005 修正系統參數:修改後無需重啓即生效，并新增防呆
  - 變更檔案: 13 個
- **2024-01-04 17:30:37**: [SAP]Q00-20240104002 修正SAP整合，当Grid更新或新增固定值栏位时，只会保留本次更新之前栏位固定值会被删掉的问题
  - 變更檔案: 1 個
- **2023-12-29 13:58:54**: [Web]Q00-20231229003 调整"追蹤"“監控”使用表单自适应宽度調整書面寬度無效果的問題
  - 變更檔案: 2 個
- **2023-12-29 13:33:58**: [Web]Q00-20231229002 调整个人资讯-->表单自适应宽度slider预设值为“较宽”
  - 變更檔案: 1 個
- **2023-12-21 13:28:33**: [Web]Q00-*********** 建立登入or登出記錄物件資料request为空时新增防呆[补修正]
  - 變更檔案: 2 個
- **2023-12-15 17:52:46**: [Web]Q00-*********** 建立登入or登出記錄物件資料request为空时新增防呆
  - 變更檔案: 1 個
- **2023-12-15 11:30:05**: [Web]Q00-20231215002 修正RadioButton和CheckBox勾选最后一个选项额外输入框，在刚进入流程时未选择却可以输入的问题
  - 變更檔案: 1 個
- **2023-12-08 14:45:43**: [流程引擎]S00-20230602004 Restful转存表单Web Server调整为抓内网地址
  - 變更檔案: 1 個
- **2023-12-05 14:47:17**: [Web]Q00-20231205004 修正待办事项中选择锁定工具列后缩小视窗表单会被部分遮挡的问题
  - 變更檔案: 1 個
- **2023-12-04 15:48:40**: [Web]Q00-20231204003 修正流程主旨、列印模式下grid资料有&#加任意數字，被轉成特殊符號的问题
  - 變更檔案: 3 個
- **2023-12-04 10:12:52**: [Web]Q00-20231204001 发起流程时在删除草稿文件新增防呆
  - 變更檔案: 1 個
- **2023-12-01 10:59:07**: [Web]Q00-20231201004 调整ipad Safari浏览器經常選取人員为默认全选
  - 變更檔案: 1 個
- **2023-11-29 16:55:32**: [Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题[补修正]
  - 變更檔案: 1 個
- **2023-11-29 14:16:33**: [Web]Q00-20231128006 调整grid標頭固定显示[补修正]
  - 變更檔案: 1 個
- **2023-11-29 12:17:02**: [Web]Q00-20231129002 调整個人資訊页多个提示訊息显示不完整的问题
  - 變更檔案: 4 個
- **2023-11-29 11:43:19**: [Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题
  - 變更檔案: 1 個
- **2023-11-28 16:17:09**: [Web]Q00-20231128006 调整grid標頭固定显示
  - 變更檔案: 1 個
- **2023-11-24 10:30:47**: [Web]Q00-20231124003 修正Grid某一格或某一行设置样式，点击排序后样式消失的问题
  - 變更檔案: 1 個

### pinchi_lin (20 commits)

- **2024-03-22 10:48:54**: [BPM APP]V00-20240322001 修正IOS手機無法下拉重整流程列表的問題
  - 變更檔案: 5 個
- **2024-03-22 10:43:44**: [BPM APP]Q00-20240322001 調整BPMAPP中流程列表的載入更多改成列表底部按鈕觸發
  - 變更檔案: 15 個
- **2024-03-20 15:04:20**: [BPM APP]Q00-20240320001 修正TextBox有設定小數點進位時setFontColor會失效問題
  - 變更檔案: 1 個
- **2024-03-14 14:23:06**: [BPM APP]V00-20240313001 修正BPM APP催辦功能的推播消息異常問題
  - 變更檔案: 3 個
- **2024-02-20 16:10:12**: [MPT]S00-20220808002 首頁模組的待辦模塊與列表新增流程名稱過濾功能
  - 變更檔案: 5 個
- **2024-01-29 11:37:32**: [BPM APP]S00-*********** BPM APP的流程追蹤已發起流程表單頁面加入催辦功能
  - 變更檔案: 20 個
- **2024-01-24 15:20:34**: [PRODT]Q00-20240124001 修正Web流程管理工具中活動參與者為職務時其值顯示不正確的問題
  - 變更檔案: 2 個
- **2024-01-16 16:10:51**: [PRODT]調整流程實例中的詳細流程圖更換成Web化流程管理工具使用的bpmnjs套件
  - 變更檔案: 14 個
- **2024-01-02 16:37:43**: [PRODT]新增匯出流程PNG功能[補]
  - 變更檔案: 1 個
- **2023-12-28 18:09:52**: [ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題
  - 變更檔案: 1 個
- **2023-12-26 19:35:59**: [PRODT]新增匯出流程PNG功能
  - 變更檔案: 1 個
- **2023-12-26 19:12:44**: [PRODT]新增流程圖自動排版功能[補]
  - 變更檔案: 1 個
- **2023-12-22 14:32:13**: [PRODT]新增流程圖自動排版功能
  - 變更檔案: 1 個
- **2023-12-19 14:00:14**: [BPM APP]S00-20231212001 調整钉钉移動端附件依系統變數的啟用支持預覽操作
  - 變更檔案: 7 個
- **2023-12-14 18:37:17**: [PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題
  - 變更檔案: 1 個
- **2023-12-12 15:11:46**: [PRODT]Q00-20231212002 修正Web流程管理工具中流程徹銷中的sessionBean點編輯呈現空白的問題
  - 變更檔案: 1 個
- **2023-12-06 18:01:26**: [PRODT]Q00-20231206002 修正Web流程管理工具中流程樣板的流程仍可移動到其他流程分類下的問題
  - 變更檔案: 1 個
- **2023-12-06 17:56:23**: [PRODT]Q00-20231206001 修正Web流程管理工具中流程樣板的流程簽入後會跑到其他流程分類下的問題
  - 變更檔案: 1 個
- **2023-12-05 18:52:58**: [PRODT]Q00-20231205006 修正Web流程管理工具中流程圖有缺少連接線時仍可儲存的問題
  - 變更檔案: 1 個
- **2023-12-04 18:52:09**: [PRODT]Q00-20231204006 修正Web流程管理工具中應用程式管理員的網頁應用程式或session bean新增呼叫參數時自訂id儲存後會變預設值的問題
  - 變更檔案: 3 個

### yamiyeh10 (38 commits)

- **2024-03-19 19:13:34**: [行業表單庫]V00-20240319002 修正新增範例表單時執行覆蓋已存在表單代號發生無法新增問題
  - 變更檔案: 2 個
- **2024-03-15 15:15:45**: [PRODT]V00-20231208001 修正Web流程管理工具中無法選到放置在最外層表單樹下表單的問題
  - 變更檔案: 1 個
- **2024-03-14 18:13:32**: [BPM APP]Q00-20240314003 調整釘釘同步待辦維護作業在重新執行創建待辦前增加檢查關卡是否已簽核機制
  - 變更檔案: 1 個
- **2024-03-14 16:03:54**: [BPM APP]Q00-20240314001 修正釘釘待辦整合當流程不支持行動簽核時卻創建待辦的問題
  - 變更檔案: 1 個
- **2024-03-14 14:02:07**: [BPM APP]S00-*********** BPMAPP移動列表增加模糊查詢功能[補]
  - 變更檔案: 4 個
- **2024-03-13 18:36:02**: [行業表單庫]V00-20240311003 調整程式權限設定作業可以設定行業表單庫權限
  - 變更檔案: 3 個
- **2024-03-13 17:39:56**: [行業表單庫]V00-20240311002 修正描述異常問題
  - 變更檔案: 4 個
- **2024-03-13 17:29:33**: [行業表單庫]增加SQL與多語系
  - 變更檔案: 4 個
- **2024-03-13 09:46:12**: [BPM APP]Q00-20240313001 修正釘釘待辦整合當流程存在核決關卡時會重複創建待辦問題
  - 變更檔案: 1 個
- **2024-03-07 15:15:06**: [行業表單庫]新增多語系
  - 變更檔案: 1 個
- **2024-03-07 15:07:21**: [行業表單庫]新增SQL指令與範例表單檔案
  - 變更檔案: 26 個
- **2024-03-07 10:27:30**: [行業表單庫]調整匯入範例表單檔案邏輯
  - 變更檔案: 1 個
- **2024-03-01 16:51:10**: [BPM APP]Q00-20240301002 修正釘釘整合方案多人同時間登入時會發生串帳號問題
  - 變更檔案: 1 個
- **2024-02-29 16:53:51**: [BPM APP]Q00-20240229002 修正使用電腦版釘釘操作行動表單時因scrollbar原生樣式導致意見區塊不好填寫問題
  - 變更檔案: 2 個
- **2024-02-17 17:49:11**: [行業表單庫]加入模組授權卡控判斷[補]
  - 變更檔案: 3 個
- **2024-02-17 15:55:53**: [PRODT]Q00-20240217001 修正Web化流程管理工具當流程內有核決關卡時部分流程會發生無法簽出問題
  - 變更檔案: 1 個
- **2024-02-01 16:02:03**: [BPM APP]Q00-20240201004 修正使用Android手機操作列表時會發生無法載入更多筆資料問題
  - 變更檔案: 6 個
- **2024-02-01 09:44:43**: [MPT]Q00-*********** 調整系統管理員不受到首頁基本信息權限設置的卡控[補]
  - 變更檔案: 3 個
- **2024-01-31 18:26:10**: [MPT]Q00-*********** 調整系統管理員不受到首頁基本信息權限設置的卡控
  - 變更檔案: 3 個
- **2024-01-29 17:45:53**: [行業表單庫]加入模組授權卡控判斷[補]
  - 變更檔案: 3 個
- **2024-01-26 09:59:09**: [行業表單庫]加入模組授權卡控判斷
  - 變更檔案: 3 個
- **2024-01-24 14:50:26**: [行業表單庫]增加創建行業表單範本到Web表單設計師的接口
  - 變更檔案: 2 個
- **2024-01-18 14:21:21**: [BPM APP]Q00-20240118003 修正IMG操作進入開窗頁面後回到表單畫面時沒有顯示左上方的返回按鈕問題
  - 變更檔案: 2 個
- **2024-01-16 17:47:39**: [BPM APP]Q00-20240116005 修正整合釘釘情況下使用Android手機上傳多筆附件時檔案重複問題
  - 變更檔案: 5 個
- **2024-01-10 16:44:35**: [BPM APP]Q00-20240110003 修正移動端列表在跳轉至表單畫面前沒有顯示遮罩導致用戶誤連續點擊而發生錯誤問題
  - 變更檔案: 6 個
- **2024-01-10 09:42:22**: [MPT]S00-20231024001 首頁模組的待辦列表主旨增加待辦來源文字[補]
  - 變更檔案: 3 個
- **2024-01-05 09:01:05**: [BPM APP]Q00-20240104003 將行動端表單下方的按鈕位置調整置中樣式
  - 變更檔案: 1 個
- **2024-01-04 11:38:46**: [BPM APP]S00-*********** BPMAPP移動列表增加模糊查詢功能
  - 變更檔案: 11 個
- **2024-01-03 15:50:43**: [BPM APP]S00-20221109003 BPMAPP在整合企業微信時新增排程可以禁用離職人員並刪除歸戶資料
  - 變更檔案: 7 個
- **2024-01-02 10:35:19**: [MPT]S00-20231024001 首頁模組的待辦列表主旨增加待辦來源文字
  - 變更檔案: 3 個
- **2023-12-27 11:03:27**: [BPM APP]Q00-20231207001 調整釘釘待辦整合在沒有啟用釘釘多法人情況下將創建者改為流程發起人使得釘釘可以發送待辦消息給簽核者
  - 變更檔案: 6 個
- **2023-12-21 16:27:10**: [BPM APP]Q00-20231221005 修正釘釘待辦整合沒有啟用系統郵件時會發生JSONException問題
  - 變更檔案: 1 個
- **2023-12-21 16:07:33**: [BPM APP]Q00-20231221001 修正行動端追蹤流程取得簽核歷程或顯示流程時會偶發沒有顯示資料問題
  - 變更檔案: 4 個
- **2023-12-19 18:01:44**: [BPM APP]Q00-20231219002 修正授權中間層管理頁面的使用者維護管理作業上查詢特定人員時發生錯誤問題
  - 變更檔案: 1 個
- **2023-12-14 09:32:07**: [BPM APP]Q00-20231214001 修正使用電腦版釘釘操作行動表單時因scrollbar原生樣式導致意見區塊不好填寫問題
  - 變更檔案: 1 個
- **2023-12-07 12:07:35**: [BPM APP]Q00-20231207003 調整同步E10表單時生成行動端FormScript增加引入E10Form檔案與呼叫initForm方法防止日期未顯示
  - 變更檔案: 1 個
- **2023-11-28 14:13:26**: [BPM APP]S00-20230626002 行動端新增在退回重辦時可以將簽核意見帶入到退回意見上顯示
  - 變更檔案: 2 個
- **2023-11-27 16:37:37**: [BPM APP]Q00-20231123002 修正設計顯示流程簽核意見為SHOW_NOT_EMPTY且無人填寫意見時開啟行動版表單畫面會發生undefined問題
  - 變更檔案: 1 個

### 刘旭 (25 commits)

- **2024-03-14 16:37:25**: [web] Q00-20240314002 作業程序書列印問題,列印的表單欄位显示不完整问题修正
  - 變更檔案: 1 個
- **2024-03-06 15:01:36**: [web] Q00-20240306001 作業程序書預覽未點選列印時，最後的表單未顯示完全问题修正
  - 變更檔案: 1 個
- **2024-03-06 10:12:22**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2024-03-01 16:27:50**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
  - 變更檔案: 1 個
- **2024-02-02 15:59:31**: [web] Q00-20240201002 流程主旨中的某个字段加上颜色标注，在BPM中的流程列表里主旨显示正常，但是在邮件提醒里主旨里显示异常问题修正
  - 變更檔案: 1 個
- **2024-02-02 13:47:41**: [web] Q00-20240201002 流程主旨中的某个字段加上颜色标注，在BPM中的流程列表里主旨显示正常，但是在邮件提醒里主旨里显示异常问题修正
  - 變更檔案: 1 個
- **2024-01-30 16:57:07**: [web] S00-20231017004 响应式表单，前后栏位在前面栏位没标签(比如button按钮)与前面不对齐问题修正
  - 變更檔案: 2 個
- **2024-01-26 16:52:35**: [web] Q00-20240126001 选择新增单子点击批次阅读通知会报错问题修正
  - 變更檔案: 1 個
- **2024-01-11 10:28:33**: [web] Q00-20240102001 調整系統變數寫法:前端JSP頁面，改使用新寫法，直接取到SystemVariableCache表的值。[补修正]
  - 變更檔案: 1 個
- **2024-01-11 09:40:40**: [web] Q00-20240102001 調整系統變數寫法:前端JSP頁面，改使用新寫法，直接取到SystemVariableCache表的值。
  - 變更檔案: 4 個
- **2024-01-10 11:22:03**: [web] V00-20231225001 只增加了序號,但在地化功能未納入控管问题修正[补修正]
  - 變更檔案: 1 個
- **2024-01-09 15:52:29**: [web] Q00-20240108003 数值栏位属性中配置的是无条件舍去时结果错误问题修正[补修正]
  - 變更檔案: 1 個
- **2024-01-08 16:58:59**: [web] V00-20231225001 只增加了序號,但在地化功能未納入控管问题修正[补修正]
  - 變更檔案: 1 個
- **2024-01-08 16:57:22**: [web] Q00-20240108003 数值栏位属性中配置的是无条件舍去时结果错误问题修正
  - 變更檔案: 1 個
- **2024-01-04 11:19:13**: [web] V00-20231225001 只增加了序號,但在地化功能未納入控管问题修正
  - 變更檔案: 4 個
- **2023-12-25 13:44:43**: [web] S00-20230921001 模擬使用者小視窗高度调整
  - 變更檔案: 1 個
- **2023-12-20 11:26:28**: [web] Q00-20231220003 當user使用Android手機、並有調整「字型大小」時，登入網頁的綁定畫面-QRCode會跑版問題修正
  - 變更檔案: 1 個
- **2023-12-06 13:18:15**: [web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正[补修正]
  - 變更檔案: 1 個
- **2023-12-05 13:49:48**: [web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正
  - 變更檔案: 1 個
- **2023-12-05 10:37:28**: [流程引擎] Q00-20231204004 BPM系統會寄逾時通知信給離職的人問題修正[补修正]
  - 變更檔案: 1 個
- **2023-12-04 16:31:37**: [流程引擎] Q00-20231204004 BPM系統會寄逾時通知信給離職的人問題修正
  - 變更檔案: 1 個
- **2023-11-30 11:47:50**: [Web] S00-20230824001 在地化-納入模組[补修正]
  - 變更檔案: 3 個
- **2023-11-30 10:09:10**: [Web] S00-20230824001 在地化-納入模組
  - 變更檔案: 4 個
- **2023-11-29 15:53:31**: [Web] Q00-20231129004 修正從追蹤連結進入已關注的愛心不會亮，該流程原本已被關注，但從追蹤連結進入後不會亮，須等切換到表單葉面後才會亮
  - 變更檔案: 1 個
- **2023-11-24 10:24:42**: [Web] Q00-20231124001 修正附件元件每個檔案容量限制設定成104857600 kb,無法上傳附件
  - 變更檔案: 2 個

### cherryliao (17 commits)

- **2024-03-08 09:53:16**: [BPM APP]Q00-20240308001 修正行動表單在Label元件設定為invisible時，開啟待辦流程會發生取得表單資訊錯誤的問題
  - 變更檔案: 1 個
- **2024-03-04 14:58:46**: [ORGDT]Q00-20240304001 修正Web組織管理工具中將人員離職日設為未來日期時，該使用者無法登入系統的問題
  - 變更檔案: 1 個
- **2024-03-01 15:42:12**: [PRODT]Q00-20240301001 修正Web流程管理工具中關卡處理者有髒資料時無法開啟流程的問題[補]
  - 變更檔案: 1 個
- **2024-03-01 13:42:50**: [PRODT]Q00-20240301001 修正Web流程管理工具中關卡處理者有髒資料時無法開啟流程的問題
  - 變更檔案: 1 個
- **2024-02-21 13:38:45**: [PRODT]Q00-20240221002 修正Web流程管理工具的核決層級關卡中設定參考活動為自定義時會導致流程無法簽入的問題
  - 變更檔案: 1 個
- **2024-01-26 10:12:33**: [MPT]S00-*********** 調整公告信息維護附件與常用工具上傳檔案最大限制參考系統設定[補]
  - 變更檔案: 3 個
- **2024-01-25 14:19:49**: [MPT]S00-*********** 調整公告信息維護附件與常用工具上傳檔案最大限制參考系統設定
  - 變更檔案: 7 個
- **2024-01-24 18:37:00**: [流程引擎]Q00-20240124003 優化流程在簽核後取活動定義關聯資料的機制
  - 變更檔案: 1 個
- **2024-01-17 10:38:40**: [Web]Q00-20240117001 修正追蹤流程列表中任一流程有顯示關注訊息，手機模式下點擊流程就沒有反應的問題
  - 變更檔案: 1 個
- **2023-12-25 16:07:05**: [BPM APP]Q00-20231225002 調整釘釘集成多對一推播時有誤判的問題
  - 變更檔案: 1 個
- **2023-12-21 10:37:42**: [BPM APP]Q00-20231221002 調整行動表單Textarea元件在唯讀狀態下沒有換行效果的問題
  - 變更檔案: 1 個
- **2023-12-20 10:17:30**: [BPM APP]Q00-20231220001 修正LINE綁定LDAP帳號時會出現使用者帳號不存在的問題
  - 變更檔案: 2 個
- **2023-12-19 10:35:08**: [Web]Q00-20231219001 調整系統設定LDAP登入時，登入畫面帳號欄位提示訊息的多語系問題
  - 變更檔案: 2 個
- **2023-12-13 10:18:28**: [BPM APP]S00-20231023001 優化行動端Grid元件在編輯狀態下增加新增按鈕，點擊可快速新增新的一筆單身
  - 變更檔案: 2 個
- **2023-11-29 09:48:32**: [Web]Q00-20231129001 調整在行動裝置撤銷流程時填寫撤銷意見會被選單擋住的問題
  - 變更檔案: 3 個
- **2023-11-28 10:02:48**: [PRODT]Q00-20231128001 修正在開啟流程管理工具後主畫面的標題一併被更動問題
  - 變更檔案: 1 個
- **2023-11-27 10:09:26**: [BPM APP]S00-20231017001 企業微信與釘釘的待辦列表新增顯示轉派資訊
  - 變更檔案: 2 個

## 詳細變更記錄

### 1. [內部]更新58101patch[補]
- **Commit ID**: `90b8efa36ce31e7671d7b26a120c58338ba05021`
- **作者**: waynechang
- **日期**: 2024-03-26 16:55:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 2. [Cosmos]更新COSMOS WebServicesApplication內容
- **Commit ID**: `8e9956c76a902557e6ff3ae09e5db66f4c7e8491`
- **作者**: 林致帆
- **日期**: 2024-03-26 16:21:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 3. [流程引擎]Q00-20240326004 修正未釋放JDBC連線可能導致連線占滿後出現異常：IJ000453: Unable to get managed connection for java:/NaNaDS(補修正)
- **Commit ID**: `bd628ad64df059a2d50b8ccadd5539370bf9e3c7`
- **作者**: lorenchang
- **日期**: 2024-03-26 14:31:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 4. [Secudocx] V00-*********** 新增以柔轉檔卡控副檔名
- **Commit ID**: `ebc4067abc92b1e299e038d15e6dea012fcf2901`
- **作者**: 邱郁晏
- **日期**: 2024-03-26 14:28:54
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 5. [Secudocx] V00-*********** 修正以柔加密，ISO攜出段無下載加密問題
- **Commit ID**: `341aedc6d884af0484e243ed85dac3fbd54a448b`
- **作者**: 邱郁晏
- **日期**: 2024-03-26 13:54:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 6. [流程引擎]Q00-20240326004 修正未釋放JDBC連線可能導致連線占滿後出現異常：IJ000453: Unable to get managed connection for java:/NaNaDS
- **Commit ID**: `86246c418f7d1f19ab96bf8f9aa8803bdf0c2ffa`
- **作者**: lorenchang
- **日期**: 2024-03-26 11:34:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 7. [Secudocx] V00-20240326001 修正以柔在線閱讀下載按鈕沒有進行加密動作
- **Commit ID**: `7876b1dfc8b538d9d83c0c657e2119b3769d9789`
- **作者**: 邱郁晏
- **日期**: 2024-03-26 10:40:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`

### 8. [Web] Q00-20240319001 修正Grid勾选table模式显示异常问题[补]
- **Commit ID**: `c57a1762f4f3741ddadfaf10da14c0b3701d93ff`
- **作者**: liuyun
- **日期**: 2024-03-25 17:55:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 9. [Cosmos]更新出貨表單95張
- **Commit ID**: `0e83e1013a49c98c935ae8c06d8381cc4e2e150e`
- **作者**: 林致帆
- **日期**: 2024-03-25 15:25:26
- **變更檔案數量**: 96
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/BOM\347\224\250\351\207\217\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(BOMI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/BOM\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI04).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/PACKING LIST \345\273\272\347\253\213\344\275\234\346\245\255(EPSI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\344\270\200\350\210\254\350\262\273\347\224\250\350\253\213\346\254\276\345\273\272\347\253\213\344\275\234\346\245\255(PCMI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\344\272\272\345\223\241\351\240\220\346\216\222\350\241\214\347\250\213\345\273\272\347\253\213\344\275\234\346\245\255(RMAI25).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\344\273\230\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACPI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\344\275\243\351\207\221\345\223\201\350\231\237\350\250\255\345\256\232\344\275\234\346\245\255(EPSI13).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\344\277\241\347\224\250\347\213\200\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(EPSI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\344\277\241\347\224\250\347\213\200\350\263\207\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(EPSI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\200\237\351\202\204\346\254\276\345\273\272\347\253\213\344\275\234\346\245\255(NOTI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\207\272\345\217\243\350\262\273\347\224\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\207\272\345\267\256\346\227\205\350\262\273\350\253\213\346\254\276\345\273\272\347\253\213\344\275\234\346\245\255(PCMI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\207\272\350\262\250\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\210\206\346\211\271\346\216\241\350\263\274\345\273\272\347\253\213\344\275\234\346\245\255(PURI15).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\212\240\345\267\245\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\212\240\347\217\255\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PALI15).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\214\257\345\205\214\346\220\215\347\233\212\350\252\277\346\225\264\344\275\234\346\245\255(NOTI16).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\217\253\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(RMAI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\223\201\350\231\237\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(INVI24).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\233\236\347\261\240\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PXMI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\240\261\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\240\261\345\267\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(SFCI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\240\261\351\227\234\350\264\226\345\226\256\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IPSI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\256\242\346\210\266\344\277\235\351\244\212\350\250\210\345\212\203\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(RMAI24).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\256\242\346\210\266\344\277\235\351\244\212\350\250\230\351\214\204\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(RMAI23).form"`
  - 📝 **修改**: `"Release/copyfiles/@cosmos/form-default/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\256\242\346\210\266\350\263\207\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(COPI15).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\272\253\345\255\230\347\225\260\345\213\225\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(INVI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\273\240\345\225\206\345\225\206\345\223\201\345\205\201\346\224\266\346\234\237\345\273\272\347\253\213\344\275\234\346\245\255(PURI17).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\273\240\345\225\206\350\263\207\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(PURI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\207\211\344\273\230\346\206\221\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACPI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\210\220\346\234\254\351\226\213\345\270\263\350\252\277\346\225\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(INVI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\213\206\350\247\243\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\216\241\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\216\241\350\263\274\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\224\266\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACRI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\232\253\345\207\272\345\205\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(INVI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\232\253\345\207\272\345\205\245\346\255\270\351\202\204\345\273\272\347\253\213\344\275\234\346\245\255(INVI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\234\203\350\250\210\345\202\263\347\245\250\345\273\272\347\253\213\344\275\234\346\245\255(ACTI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\240\270\345\203\271\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\252\224\346\234\237\345\220\210\347\264\204\345\273\272\347\253\213\344\275\234\346\245\255(COMI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\252\224\346\234\237\345\220\210\347\264\204\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(COMI10)\343\200\220administrator\343\200\221.form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\346\264\276\350\273\212\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\224\237\347\224\242\345\205\245\345\272\253\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\224\237\347\224\242\345\205\245\345\272\253\346\252\242\351\251\227\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(QMSI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\224\242\345\223\201\345\272\217\350\231\237\346\217\233\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(RMAI19).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\247\273\350\275\211\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(SFCI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\247\273\350\275\211\346\252\242\351\251\227\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(QMSI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\265\204\345\220\210\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(BOMI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\265\220\345\270\263\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ACRI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\266\255\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(RMAI13).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\243\275\344\273\244\344\276\206\346\272\220\350\250\202\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(MOCI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\243\275\351\200\240\345\221\275\344\273\244\345\273\272\347\253\213\344\275\234\346\245\255(MOCI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\243\275\351\200\240\345\221\275\344\273\244\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(MOCI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\250\202\345\226\256\350\256\212\346\233\264\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\250\227\345\244\226\351\200\200\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\250\227\345\244\226\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\250\227\345\244\226\351\200\262\350\262\250\345\226\256\351\251\227\346\224\266\344\275\234\346\245\255(MOCI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\250\227\345\244\226\351\200\262\350\262\250\346\252\242\351\251\227\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(QMSI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\252\277\346\225\264\346\262\226\351\212\267\345\210\206\351\214\204\345\273\272\347\253\213\344\275\234\346\245\255(FCSI04).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\253\213\345\201\207\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PALI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\262\250\351\201\213\351\200\232\347\237\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(EPSI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\262\273\347\224\250\344\273\230\346\254\276\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PCMI20).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\345\207\272\345\224\256\345\273\272\347\253\213\344\275\234\346\245\255(ASTI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\345\240\261\345\273\242\345\273\272\347\253\213\344\275\234\346\245\255(ASTI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\345\244\226\351\200\201\345\273\272\347\253\213\344\275\234\346\245\255(ASTI13).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\346\212\230\350\210\212\345\273\272\347\253\213\344\275\234\346\245\255(ASTI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\346\216\241\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI22).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\346\224\266\345\233\236\345\273\272\347\253\213\344\275\234\346\245\255(ASTI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\346\224\271\350\211\257\345\273\272\347\253\213\344\275\234\346\245\255(ASTI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\347\247\273\350\275\211\345\273\272\347\253\213\344\275\234\346\245\255(ASTI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\350\252\277\346\225\264\345\273\272\347\253\213\344\275\234\346\245\255(ASTI10).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\350\253\213\350\263\274\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(ASTI19).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(ASTI23).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\263\207\347\224\242\351\207\215\344\274\260\345\273\272\347\253\213\344\275\234\346\245\255(ASTI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\275\211\346\222\245\345\226\256\346\223\232\345\273\272\347\253\213\344\275\234\346\245\255(INVI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\200\346\226\231\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI04).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\200\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI11).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\201\345\273\240\346\255\270\351\202\204\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(RMAI15).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\201\345\273\240\347\266\255\344\277\256\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(RMAI14).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\232\350\267\257\345\271\264\345\272\246\345\220\210\347\264\204\345\273\272\347\253\213\344\275\234\346\245\255(COMI12).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\232\350\267\257\345\271\264\345\272\246\345\220\210\347\264\204\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(COMI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\262\345\217\243\350\262\273\347\224\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(IPSI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\262\350\262\250\345\226\256\351\251\227\346\224\266\344\275\234\346\245\255(PURI13).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\200\262\350\262\250\346\252\242\351\251\227\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(QMSI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\201\236\345\273\266\346\224\266\345\205\245\347\266\255\350\255\267\344\275\234\346\245\255(COPI20).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\212\267\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI08).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\212\267\351\200\200\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\233\266\347\224\250\351\207\221\345\200\237\346\224\257\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PCMI07).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\233\266\347\224\250\351\207\221\345\240\261\351\212\267\345\273\272\347\253\213\344\275\234\346\245\255(PCMI09).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\233\266\347\224\250\351\207\221\350\275\211\346\222\245\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PCMI06).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\240\220\344\273\230\350\263\274\346\226\231\350\256\212\346\233\264\345\273\272\347\253\213\344\275\234\346\245\255(IPSI03).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\240\220\344\273\230\350\263\274\346\226\231\350\263\207\346\226\231\345\273\272\347\253\213\344\275\234\346\245\255(IPSI02).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\351\240\230\346\226\231\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(MOCI03).form"`

### 10. [Web]Q00-20240313002 修正[ID]Obj.setColumnwidth("＜GridcolumnID＞",100);写法没有效果 [补]
- **Commit ID**: `eaafcca0ced910b8a076d765d4acce7b408e51e7`
- **作者**: 周权
- **日期**: 2024-03-25 14:46:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 11. [內部]更新58101patch[補]
- **Commit ID**: `06ff3dd1cfd57737e5c300af2aa111318e634a86`
- **作者**: waynechang
- **日期**: 2024-03-22 16:55:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 12. [Cosmos]更新預設出貨表單流程 [補修正]
- **Commit ID**: `f3d66982a605d679af7b8a7539de31beb445036f`
- **作者**: 林致帆
- **日期**: 2024-03-22 16:30:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"Release/copyfiles/@cosmos/process-default/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(Cosmos_COPI06).bpmn"`

### 13. [Secudocx] V00-20240322003 修正ISO攜出段，若設定為ZIP時，資料夾會經過加密作業
- **Commit ID**: `5545c470da6694ab90d2845ebf4717248fed125d`
- **作者**: 邱郁晏
- **日期**: 2024-03-22 15:47:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 14. [Cosmos]更新預設出貨表單流程
- **Commit ID**: `57334d449cb128484168ec8c883fcdad17bf215e`
- **作者**: 林致帆
- **日期**: 2024-03-22 11:01:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(COPI06).form"`
  - ❌ **刪除**: `"Release/copyfiles/@cosmos/form-default/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI05).form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/process-default/\345\256\242\346\210\266\350\250\202\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(Cosmos_COPI06).bpmn"`
  - ❌ **刪除**: `"Release/copyfiles/@cosmos/process-default/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(Cosmos_PURI05).bpmn"`

### 15. [BPM APP]V00-20240322001 修正IOS手機無法下拉重整流程列表的問題
- **Commit ID**: `bed1fbb15d491a69c204e2471a354af676f23df5`
- **作者**: pinchi_lin
- **日期**: 2024-03-22 10:48:54
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`

### 16. [BPM APP]Q00-20240322001 調整BPMAPP中流程列表的載入更多改成列表底部按鈕觸發
- **Commit ID**: `7ab99ee34e17ab93f0e33eee769a87ac19f892ee`
- **作者**: pinchi_lin
- **日期**: 2024-03-22 10:43:44
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListResigendV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmAppWorkMenu.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`

### 17. [WEB]Q00-20240321003 修正绝对位置表单通过流程追踪URL打开后没有横向滚动条
- **Commit ID**: `7cc48b852e7ed99beeda8bc834e70d43e3ff8ef5`
- **作者**: 周权
- **日期**: 2024-03-21 16:25:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`

### 18. [內部]更新58101patch[補]
- **Commit ID**: `409e26c2c7e55b6ce52581cdef8a21967bba7867`
- **作者**: waynechang
- **日期**: 2024-03-21 15:58:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 19. [SecuDocx] 以柔調整CreateSQL
- **Commit ID**: `0b91e934860c563d8a5e07bdc71c8e3fbdc4874c`
- **作者**: 邱郁晏
- **日期**: 2024-03-21 15:50:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`

### 20. [內部]修正新安裝在Linux環境無法正常啟動的異常
- **Commit ID**: `dc32da30db5ae743bdbbd84ea962c995d6fd6f53`
- **作者**: lorenchang
- **日期**: 2024-03-21 15:42:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/bin/standalone.sh`
  - 📝 **修改**: `Release/wildfly/bin/systemd/bpm.service`
  - ➕ **新增**: `Release/wildfly/standalone/configuration/logging.properties`

### 21. [內部]調整新版自動簽核邏輯[補]
- **Commit ID**: `62b58af8eba562a088ed2acf95cdfb051ee7f6bd`
- **作者**: waynechang
- **日期**: 2024-03-21 15:35:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 22. [內部]調整新版自動簽核邏輯
- **Commit ID**: `d1e70ac21f4dfe63c07ee37ab863980c854636e0`
- **作者**: waynechang
- **日期**: 2024-03-21 11:50:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 23. [Web] Q00-20240321001 新增LDAP 設定SSL參數定義憑證
- **Commit ID**: `cdfd324418fb64eb6ebe13393771c1f728fd1d5f`
- **作者**: 邱郁晏
- **日期**: 2024-03-21 11:30:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 24. [Web] Q00-20240321002 standalone-full.xml預設出貨預設不顯示版本資訊
- **Commit ID**: `8895fb6828b81f1866ce6ad71833d491489f1224`
- **作者**: 邱郁晏
- **日期**: 2024-03-21 11:20:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/standalone/configuration/standalone-full.xml`

### 25. [BPM APP]Q00-20240320001 修正TextBox有設定小數點進位時setFontColor會失效問題
- **Commit ID**: `b275c710b876143e46d7aff4cabf8b7b494b1af3`
- **作者**: pinchi_lin
- **日期**: 2024-03-20 15:04:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 26. [WEB]V00-20240318001 修正钉钉待办同步 调整浏览器分辨率，表头和内容格线对不齐
- **Commit ID**: `4da50f6b3c943537f760da76307cb34e69378bca`
- **作者**: 周权
- **日期**: 2024-03-20 11:42:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp`

### 27. [SecuDocx] 以柔整合調整多語系
- **Commit ID**: `8070122149908f599f8ac2aca2f11e1b5e6b9312`
- **作者**: 邱郁晏
- **日期**: 2024-03-20 10:37:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 28. [行業表單庫]V00-20240319002 修正新增範例表單時執行覆蓋已存在表單代號發生無法新增問題
- **Commit ID**: `008fd803ef3dee24dda2be8641d6de350b5df0a8`
- **作者**: yamiyeh10
- **日期**: 2024-03-19 19:13:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/service/FormRepositoryManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/service/impl/FormRepositoryManagerBean.java`

### 29. [SecuDocx] 以柔整合調整寫法(補修正)
- **Commit ID**: `a373e435ab263641ff17fe2e39b0b7aaf6b8954f`
- **作者**: 邱郁晏
- **日期**: 2024-03-19 18:26:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Secudocx/service/impl/SecudocxSettingManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/secudocxModule/SecudocxSetting.java`

### 30. [SecuDocx] 以柔整合調整寫法
- **Commit ID**: `992c6e3acd4735269b7b280149f5de908a6ba5f6`
- **作者**: 邱郁晏
- **日期**: 2024-03-19 18:01:07
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 31. [Web]Q00-20240319003 修正顯示流程頁點擊轉由他人處理會無法執行
- **Commit ID**: `bd38d11e576bb7e85957a1786a094e8bb0fb8f96`
- **作者**: 林致帆
- **日期**: 2024-03-19 12:05:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 32. [Web] Q00-20240319001 修正Grid勾选table模式显示异常问题
- **Commit ID**: `e16235c3e7e62af1df0a629ab8ef69b926c8c1b1`
- **作者**: liuyun
- **日期**: 2024-03-19 10:09:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 33. [內部]修正 5.8.9.4 Oracle 更新 License 資訊的 Update SQL 語法
- **Commit ID**: `47a00b6bacf7cbe7b77e9245282ba67080fd112a`
- **作者**: lorenchang
- **日期**: 2024-03-18 18:03:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.4_DML_Oracle.sql`

### 34. [WEB]Q00-20240318002 修正從追踪流程開起，先點選絶對位置表單，再點RWD後，RWD畫面會變成絶對位置畫面寬度
- **Commit ID**: `b034940d5f15ed9e4da0be855aafd8f73d659b41`
- **作者**: 周权
- **日期**: 2024-03-18 17:39:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 35. [BPM APP]Q00-20240318001 钉钉待办同步与钉钉连线新增重试机制
- **Commit ID**: `f6a3100a1f3c990a9841f467466a2a6e48e2c568`
- **作者**: 周权
- **日期**: 2024-03-18 14:13:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java`

### 36. [PRODT]V00-20231208001 修正Web流程管理工具中無法選到放置在最外層表單樹下表單的問題
- **Commit ID**: `ffb2858ae7999625aa03fb3763292e2cd2314346`
- **作者**: yamiyeh10
- **日期**: 2024-03-15 15:15:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormCategoryManagerBean.java`

### 37. [Web] Q00-20240315001 修正开窗选择后无法关闭问题
- **Commit ID**: `676c234f8a828148053ccf2937cbb4fd3f420eed`
- **作者**: liuyun
- **日期**: 2024-03-15 15:11:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 38. [內部]更新58101patch
- **Commit ID**: `f05f5a06c31b138f846cb0004ceb3b44cc5bd9ee`
- **作者**: waynechang
- **日期**: 2024-03-15 13:44:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`

### 39. [BPM APP]Q00-20240314003 調整釘釘同步待辦維護作業在重新執行創建待辦前增加檢查關卡是否已簽核機制
- **Commit ID**: `d2f4f8ea0d24a2e4e95843c4a33999612d87465e`
- **作者**: yamiyeh10
- **日期**: 2024-03-14 18:13:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`

### 40. [web] Q00-20240314002 作業程序書列印問題,列印的表單欄位显示不完整问题修正
- **Commit ID**: `a132ff2c67a23aaa50a75b1a6e12b3f7d2a49475`
- **作者**: 刘旭
- **日期**: 2024-03-14 16:37:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`

### 41. [BPM APP]Q00-20240314001 修正釘釘待辦整合當流程不支持行動簽核時卻創建待辦的問題
- **Commit ID**: `9aaa7609c7309e79e097e21dbe4bcea32f1f0501`
- **作者**: yamiyeh10
- **日期**: 2024-03-14 16:03:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`

### 42. [BPM APP]V00-20240313001 修正BPM APP催辦功能的推播消息異常問題
- **Commit ID**: `9ce643be3d8ddb83d68b7a45a75dd67cd9c6a155`
- **作者**: pinchi_lin
- **日期**: 2024-03-14 14:23:06
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 43. [BPM APP]S00-*********** BPMAPP移動列表增加模糊查詢功能[補]
- **Commit ID**: `3463dc4018dd35d27e7e343fb93e92bbb9f70e0a`
- **作者**: yamiyeh10
- **日期**: 2024-03-14 14:02:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`

### 44. [其他] Q00-20230828003 修改 ReportDesignerDefinition 资料表 sqlConditionLists 栏位字段类型(修改格式)[补]
- **Commit ID**: `0f696d1b63fbb1b9d87ee4c4e07d615847b5fda2`
- **作者**: liuyun
- **日期**: 2024-03-14 10:30:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`

### 45. [行業表單庫]V00-20240311003 調整程式權限設定作業可以設定行業表單庫權限
- **Commit ID**: `1261ce5077329375de92c45e4a8ca10e9a1a9df4`
- **作者**: yamiyeh10
- **日期**: 2024-03-13 18:36:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 46. [行業表單庫]V00-20240311002 修正描述異常問題
- **Commit ID**: `6cdf63d8fa394c4ed583c2fac4fb5e6f8cdffec9`
- **作者**: yamiyeh10
- **日期**: 2024-03-13 17:39:56
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 47. [行業表單庫]增加SQL與多語系
- **Commit ID**: `3e0fcdfeeff2b840bcce38f71eac368890083a6b`
- **作者**: yamiyeh10
- **日期**: 2024-03-13 17:29:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 48. [WEB]Q00-20240313002 修正[ID]Obj.setColumnwidth("＜GridcolumnID＞",100);写法没有效果
- **Commit ID**: `b5a9d8e4d7bfa2217d8ce20bd1643d3a263c0387`
- **作者**: 周权
- **日期**: 2024-03-13 15:22:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 49. [Web] V00-20240312003 修正页面列表栏位width和出现滚轴问题
- **Commit ID**: `d8221bc10c253f75f8ed0299e8625e17e0b8a567`
- **作者**: liuyun
- **日期**: 2024-03-13 14:24:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 50. [Web] V00-20240312005 修正產品授權註冊模組類型內容為空問題(補)
- **Commit ID**: `cfba026bc75cbb122802cf02401a9a3af9621333`
- **作者**: 邱郁晏
- **日期**: 2024-03-13 10:33:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 51. [Web] V00-20240312005 修正產品授權註冊模組類型內容為空問題
- **Commit ID**: `c3965a5c04e4d70d3b7f6b2dc765b7caf32862cf`
- **作者**: 邱郁晏
- **日期**: 2024-03-13 10:01:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 52. [BPM APP]Q00-20240313001 修正釘釘待辦整合當流程存在核決關卡時會重複創建待辦問題
- **Commit ID**: `1b1ba84b12024e93e3db6374b227292402474c12`
- **作者**: yamiyeh10
- **日期**: 2024-03-13 09:46:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 53. [BPM APP]Q00-20240312002 修正創建釘釘待辦失敗后，仍會跑更新待辦的問題
- **Commit ID**: `c821ec26d16fbb102f564382d5b2cdd3a77c9787`
- **作者**: 周权
- **日期**: 2024-03-12 17:22:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`

### 54. [流程引擎]A00-20240307001 修正自動簽核2.當關卡設定為與前一關相同者則跳過時，若該關卡被退回重辦在重新執行時，自動簽核會失效的異常
- **Commit ID**: `c87e7eb8ef51b2f386fc2a1e355619d28977fdad`
- **作者**: waynechang
- **日期**: 2024-03-12 17:11:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 55. [Web] V00-20240312002 修正管理員-其它設定-版本資訊頁面異常
- **Commit ID**: `bc0a6b5701ed702a1188bb2fe474035c73ae891c`
- **作者**: liuyun
- **日期**: 2024-03-12 10:56:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 56. [Web]Q00-20240312001 停用Web服務的addCustomParallelAndSerialActivity服務，建議以 addCustomParallelActivity 服務代替
- **Commit ID**: `e22f9632026676f242e7859ab83b4e379a18974c`
- **作者**: 林致帆
- **日期**: 2024-03-12 10:19:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java`

### 57. [SecuDocx] 調整模組授權SQL(補)
- **Commit ID**: `7acf945bf8e15c5ac6f6aea229fd9e3c3f6d2d07`
- **作者**: 邱郁晏
- **日期**: 2024-03-12 10:07:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 58. [SecuDocx] 調整模組授權SQL
- **Commit ID**: `a37d09eed495a98c6ed0297ce808de91ce55e58c`
- **作者**: 邱郁晏
- **日期**: 2024-03-12 10:05:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`

### 59. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `bbd53f9d6cb925272c9b2c04cbde574279679048`
- **作者**: liuyun
- **日期**: 2024-03-11 16:39:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`

### 60. [其他] Q00-20230828003 修改 ReportDesignerDefinition 资料表 sqlConditionLists 栏位字段类型[补]
- **Commit ID**: `eaf2022b17abdbcf48645fc26193067c22b4fb92`
- **作者**: liuyun
- **日期**: 2024-03-11 16:39:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`

### 61. [Web] Q00-20240308002 調整Web終止流程Confirm視窗變為ShowDialog的方式呈現(補)
- **Commit ID**: `417b6f0b6d49c14b042e00622b5f88db734d1f9d`
- **作者**: 邱郁晏
- **日期**: 2024-03-11 15:37:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 62. [Web] Q00-20240308002 調整Web終止流程Confirm視窗變為ShowDialog的方式呈現(補)
- **Commit ID**: `264aa67a18b18a2a12610af6e0f94a23b2646f8c`
- **作者**: 邱郁晏
- **日期**: 2024-03-11 14:44:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 63. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `c3be53887d939dcfbd88f390211795bc72489663`
- **作者**: 邱郁晏
- **日期**: 2024-03-08 16:09:09
- **變更檔案數量**: 0

### 64. [Web] Q00-20240308002 調整Web終止流程Confirm視窗變為ShowDialog的方式呈現
- **Commit ID**: `1ef0eab255b322f4add5d5479875940549b60489`
- **作者**: 邱郁晏
- **日期**: 2024-03-08 16:08:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 65. [BPM APP]Q00-20240308001 修正行動表單在Label元件設定為invisible時，開啟待辦流程會發生取得表單資訊錯誤的問題
- **Commit ID**: `d4d362afeab01a0b0296c2f6c616fb0da79575b6`
- **作者**: cherryliao
- **日期**: 2024-03-08 09:53:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 66. [SAP]Q00-20240307001 修正SAP固定值显示异常的问题
- **Commit ID**: `8b6278fc12324bdf614cb3b628d30a67db275f88`
- **作者**: 周权
- **日期**: 2024-03-07 16:01:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`

### 67. Merge branch 'develop_v58' into develop_formrepository
- **Commit ID**: `720516072ffdedb71017ef2b19d7232a7b9e3010`
- **作者**: lorenchang
- **日期**: 2024-03-07 15:23:32
- **變更檔案數量**: 0

### 68. [行業表單庫]新增多語系
- **Commit ID**: `9b4617ecd0b527d408099256c647d5434673922c`
- **作者**: yamiyeh10
- **日期**: 2024-03-07 15:15:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 69. [行業表單庫]新增SQL指令與範例表單檔案
- **Commit ID**: `d17bdb49d5e0423f646fec0f4dddb81c30523ba8`
- **作者**: yamiyeh10
- **日期**: 2024-03-07 15:07:21
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\344\277\256\347\271\225\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\215\260\351\221\221\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\220\215\347\211\207\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\225\206\346\251\237\351\200\232\345\240\261\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\234\213\345\244\226\345\207\272\345\267\256\350\250\274\347\205\247\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\346\210\266\351\221\221\345\210\245\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\345\256\242\350\250\264\350\231\225\347\220\206\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\223\254\350\276\246\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\226\260\351\200\262\344\272\272\345\223\241\346\225\231\350\202\262\350\250\223\347\267\264\347\264\200\351\214\204\350\241\250.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\346\245\255\345\213\231\350\201\257\347\271\253\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\231\274\346\226\207\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\237\257\346\255\243\350\210\207\351\240\220\351\230\262\346\216\252\346\226\275\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\347\260\275\345\221\210.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\241\214\351\212\267\346\224\257\346\217\264\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\250\223\347\267\264\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\263\207\350\250\212\346\234\215\345\213\231\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\273\212\350\274\233\351\240\220\345\256\232\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\276\246\345\205\254\347\224\250\345\223\201\345\205\245\345\272\253\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\350\276\246\345\205\254\347\224\250\345\223\201\347\224\263\350\253\213\345\226\256.formrepository"`
  - ➕ **新增**: `"Release/copyfiles/@base/form-repository/\351\251\227\346\224\266\345\226\256.formrepository"`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 70. [行業表單庫]調整匯入範例表單檔案邏輯
- **Commit ID**: `c2d323baf88566d675394668f3b3cbf15ed8eb5d`
- **作者**: yamiyeh10
- **日期**: 2024-03-07 10:27:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/service/impl/FormRepositoryManagerBean.java`

### 71. [ISO]S00-20230703001 ISO閱讀文件支持同時開啟多份閱讀文件
- **Commit ID**: `a0eb9259b61c31363816fd260903bb33d5e6ce3e`
- **作者**: waynechang
- **日期**: 2024-03-06 17:01:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ModalDialog.js`

### 72. [web] Q00-20240306001 作業程序書預覽未點選列印時，最後的表單未顯示完全问题修正
- **Commit ID**: `488897f4ee94258b2d85b7cc4983ea0403b4f06c`
- **作者**: 刘旭
- **日期**: 2024-03-06 15:01:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`

### 73. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `a25674bf8b9a56789e2d585142875d64edd3085c`
- **作者**: 刘旭
- **日期**: 2024-03-06 10:12:22
- **變更檔案數量**: 0

### 74. [WebService]Q00-20221117006 停用addCustomParallelAndSerialActivity服務，建議以 addCustomParallelActivity 服務代替
- **Commit ID**: `78487812450ac024e0de97ce03c1a408bb2a2f5d`
- **作者**: waynechang
- **日期**: 2024-03-05 18:00:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WorkflowService.java`

### 75. [行業表單庫]Domain 及 DAO 移至 nana-app.ear 之相應調整2
- **Commit ID**: `b263418f7dfac123a60a9248522340b60d09a296`
- **作者**: lorenchang
- **日期**: 2024-03-05 17:24:53
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormRepositoryManager.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormRepositoryManagerBean.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormRepository.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormRepositoryTool.java`

### 76. Merge branch 'develop_v58' into develop_formrepository
- **Commit ID**: `2f407cacd94768e558c712f88ce33b5d3d77e6b4`
- **作者**: lorenchang
- **日期**: 2024-03-05 17:17:34
- **變更檔案數量**: 0

### 77. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `12f4863e613491d9f0a0152c3f68a7524d78da76`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 17:16:52
- **變更檔案數量**: 0

### 78. [Web] V00-20240304001 修正searchFormDetail連結沒有檢核流程參與者是否有權限查看
- **Commit ID**: `6d7cc2ce673011c7c7854a8a20dd67f7c1456c00`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 17:16:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 79. [內部]平台共用程式優化，Paging 增加方法提供後端調用快速產生使用 OID、ID 取得指定 Doamin 的條件(補修正)
- **Commit ID**: `9bfc1822615b8a1f2e97d4ebde5c61c09b89a5d1`
- **作者**: lorenchang
- **日期**: 2024-03-05 17:15:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/Paging.java`

### 80. [內部]平台共用程式優化，Paging 增加方法提供後端調用快速產生使用 OID、ID 取得指定 Doamin 的條件
- **Commit ID**: `31540437e2a2cc621e003e32924d6123ebe5c05b`
- **作者**: lorenchang
- **日期**: 2024-03-05 17:03:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/Paging.java`

### 81. [Secudocx]更新程式路徑
- **Commit ID**: `4b0ec01626276453d263e7e219b37ae915b2f6b2`
- **作者**: lorenchang
- **日期**: 2024-03-05 15:32:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/services/secudocxModule/SecudocxSettingManager.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/services/secudocxModule/SecudocxSettingManagerBean.java`

### 82. [行業表單庫]Domain 及 DAO 移至 nana-app.ear 之相應調整
- **Commit ID**: `9aa8b1c302eca32692279bbc10faebe163b45a4d`
- **作者**: lorenchang
- **日期**: 2024-03-05 15:23:46
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/dao/FormRepositoryDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/dao/FormTypeCategoryDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/dao/IndustryCategoryDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/dao/impl/FormRepositoryDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/dao/impl/FormTypeCategoryDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/dao/impl/IndustryCategoryDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/domain/FormRepository.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/domain/FormTypeCategory.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/FormRepository/domain/IndustryCategory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java`

### 83. [Secudocx] 新增Secudocx專案多語系
- **Commit ID**: `33654150f8837d8ad22834fba54e45838ea1bb27`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 15:19:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 84. Merge branch 'develop_secudocx' into develop_v58
- **Commit ID**: `74d30b5403ca6dd9e2d96cbd5b9ced7468956d8d`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 14:40:26
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📄 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📄 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📄 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📄 **修改**: `Release/db/update/5.8.10.1_DDL_DM8.sql`
  - 📄 **修改**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`
  - 📄 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`
  - 📄 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📄 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📄 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 85. [Secudocx] 新增SQL指令
- **Commit ID**: `6211e6bc9f1fed3a59ff945ed55b5b55e61a7844`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 14:32:53
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 86. [TIPTOP]Q00-20240305001 修正簽核歷程頁該使用者登入後無權限查看時，會造成授權人數被占據
- **Commit ID**: `3cf7510c40997ea2500380579805002f56f0a084`
- **作者**: 林致帆
- **日期**: 2024-03-05 14:24:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 87. [Secudocx] 新增模組維護作業
- **Commit ID**: `0c31e08aa9f000b594da214ba2225df044867ded`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 13:41:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 88. [Secudocx] 新增模組維護作業
- **Commit ID**: `6594877ca0812df1dcc2ac6a8540835d06eaa027`
- **作者**: 邱郁晏
- **日期**: 2024-03-05 11:36:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/secudocxModule/SecudocxSettingManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/secudocxModule/SecudocxSettingManagerBean.java`

### 89. Merge branch 'develop_v58' into develop_formrepository
- **Commit ID**: `241620916223377190f7e150bce83db61eac575c`
- **作者**: lorenchang
- **日期**: 2024-03-05 10:30:22
- **變更檔案數量**: 0

### 90. [內部]平台共用程式優化，更新 BaseDomainObject 欄位命名(補修正2)
- **Commit ID**: `45f462453c9b5d692d4a2c4b52f4d7b2d7c90a7e`
- **作者**: lorenchang
- **日期**: 2024-03-05 10:29:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/file/Demo.postman_collection.json`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/file/InitDemoDB_MSSQL.sql`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/domain/BaseDomainObject.java`

### 91. Merge branch 'develop_v58' into develop_formrepository
- **Commit ID**: `77b1b0788f9013cf2f09158026874f1ea132fa57`
- **作者**: lorenchang
- **日期**: 2024-03-05 10:06:27
- **變更檔案數量**: 0

### 92. [內部]平台共用程式優化，更新 BaseDomainObject 欄位命名
- **Commit ID**: `7cfa66ea829ba8503be43cd9ab757af3384fa050`
- **作者**: lorenchang
- **日期**: 2024-03-05 09:28:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/file/Demo.postman_collection.json`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/file/InitDemoDB_MSSQL.sql`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/domain/BaseDomainObject.java`

### 93. [流程引擎]Q00-20240304002 修正Ajax核決加簽方法因為ContainerOID無法加入null內容造成異常
- **Commit ID**: `5538ef6675a2268da323cf3453d503e53eff9b26`
- **作者**: 林致帆
- **日期**: 2024-03-04 17:42:20
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`

### 94. Merge branch 'develop_v58' into develop_formrepository
- **Commit ID**: `117f30e31ef5b11cbf1b4d3b9e541583fb8c2aae`
- **作者**: lorenchang
- **日期**: 2024-03-04 16:37:34
- **變更檔案數量**: 0

### 95. [內部]平台共用程式優化(補修正2)
- **Commit ID**: `e8ea9f4b3ddc3f97151b1ba13101db20cafb0bf1`
- **作者**: lorenchang
- **日期**: 2024-03-04 16:21:05
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/dao/DemoUserDao.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/dao/impl/DemoUserDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/file/Demo.postman_collection.json`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java`

### 96. [ORGDT]Q00-20240304001 修正Web組織管理工具中將人員離職日設為未來日期時，該使用者無法登入系統的問題
- **Commit ID**: `34e865b2c80b268e19fbfc5a4b18ce6a1cfe28fd`
- **作者**: cherryliao
- **日期**: 2024-03-04 14:58:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 97. [行業表單庫]後端程式初版
- **Commit ID**: `cb2fed432637968d84df14825c24af840b9b98d1`
- **作者**: lorenchang
- **日期**: 2024-03-04 11:02:34
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormRepository.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormRepositoryManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormRepositoryManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormRepositoryTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`

### 98. [BPM APP]Q00-20240301002 修正釘釘整合方案多人同時間登入時會發生串帳號問題
- **Commit ID**: `28bf878921331eaf392a6874cacef626ca8995b9`
- **作者**: yamiyeh10
- **日期**: 2024-03-01 16:51:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`

### 99. [內部]Q00-20240301003 增加Queue啟動下一關是服務任務的相關屬性的log[補]
- **Commit ID**: `b5c153f560ac207f8a88c43cfdbdc92cffe2776a`
- **作者**: waynechang
- **日期**: 2024-03-01 16:37:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 100. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `67a97d96666c3a52b9ac2cfb34a2cbb1aa667ac1`
- **作者**: 刘旭
- **日期**: 2024-03-01 16:27:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 101. [內部]Q00-20240301003 增加Queue啟動下一關是服務任務的相關屬性的log
- **Commit ID**: `3f2eebf4c67bbb79828153501adc9d99d4fc5a93`
- **作者**: waynechang
- **日期**: 2024-03-01 16:24:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java`

### 102. [內部]新增 Demo Postman 設定
- **Commit ID**: `547827e42f72155d18e05b2412a2753aeb2a071d`
- **作者**: lorenchang
- **日期**: 2024-03-01 15:46:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/file/Demo.postman_collection.json`

### 103. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `07ded7508ba15bbaaba56a9a48ccae15c45d20e3`
- **作者**: lorenchang
- **日期**: 2024-03-01 15:44:28
- **變更檔案數量**: 0

### 104. [PRODT]Q00-20240301001 修正Web流程管理工具中關卡處理者有髒資料時無法開啟流程的問題[補]
- **Commit ID**: `9ac8623c6f8bb1c5acabebeae2800f0b29f1dc78`
- **作者**: cherryliao
- **日期**: 2024-03-01 15:42:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 105. [內部]平台共用程式優化(補修正)
- **Commit ID**: `0cb2f5e65c88247be750ea711d1aee244344fdfe`
- **作者**: lorenchang
- **日期**: 2024-03-01 15:40:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/LoadResourceBundle.java`

### 106. [內部]平台共用程式優化，請更新其它模組 git 及 nana-service-client.jar
- **Commit ID**: `e22e2b1885e942d01347af655264e9ce3c7dcdc5`
- **作者**: lorenchang
- **日期**: 2024-03-01 15:22:58
- **變更檔案數量**: 39
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RsrcBundleDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Jackson/jackson-core-2.13.5.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Jackson/jackson-databind-2.13.5.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Lombok/lombok-1.18.30.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-process-archive/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/dao/DemoUserDao.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/dao/impl/DemoUserDaoImpl.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/domain/DemoUser.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Demo/file/InitDemoDB_MSSQL.sql`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dao/BaseDomainCrud.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/domain/BaseDomainObject.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/Paging.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/PagingList.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryCondition.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/QueryOperator.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/SortCondition.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/request/PlatformApiParameterRequest.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/request/PlatformApiRequest.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/request/PlatformApiStdDataRequest.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/response/PlatformAPiStdDataResponse.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/response/PlatformApiExecutionResponse.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/response/PlatformApiParameterResponse.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/dto/response/PlatformApiResponse.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/exception/OIDIsEmptyException.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/exception/OIDIsNotEmptyException.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/HtmlUtils.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/JacksonUtils.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/README.md`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/README.md`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/StopWatch.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/StringUtils.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/html/HtmlUtils.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/RmiRegistry.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/RsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/SessionBeanHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/LoadResourceBundle.java`

### 107. [PRODT]Q00-20240301001 修正Web流程管理工具中關卡處理者有髒資料時無法開啟流程的問題
- **Commit ID**: `a06d0586026dc7258023281899f78e1ddd6e2d86`
- **作者**: cherryliao
- **日期**: 2024-03-01 13:42:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 108. [Web]Q00-20231218001 修正系統參數(processaccessor.activity.name.format.unit-duty=true)時，簽核歷程的關卡若為進行中尚未簽核時，關卡名稱應顯示為[部門-職稱]，而非[關卡定義名稱][補修正]
- **Commit ID**: `afc6fe9b8475f8cd061f48ec275a96dda18f74cd`
- **作者**: waynechang
- **日期**: 2024-03-01 13:39:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 109. [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口(補修正)
- **Commit ID**: `48ef79cd9f6f06c27d9e94222c973a4ab9548d11`
- **作者**: waynechang
- **日期**: 2024-03-01 10:46:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 110. [Cosmos]修正表單同步會新產生WorkFlowERP表單分類
- **Commit ID**: `913ea2ffab83ae84f9eb41283b89b4dc450e5706`
- **作者**: 林致帆
- **日期**: 2024-03-01 09:13:35
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormCategory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/integration/model/SysIntegrationXmlTag.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 111. [Web] Q00-20240229001 調整外部連結信使用者，看不到「發送通知」按鈕問題。
- **Commit ID**: `5439ae60a46f5a22ef746d2a32835e5f1300b476`
- **作者**: 邱郁晏
- **日期**: 2024-02-29 17:10:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 112. [BPM APP]Q00-20240229002 修正使用電腦版釘釘操作行動表單時因scrollbar原生樣式導致意見區塊不好填寫問題
- **Commit ID**: `cd7f0a747d8e95ed18f1fb7837b8ef054b9b36d1`
- **作者**: yamiyeh10
- **日期**: 2024-02-29 16:53:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 113. [Secudocx] 新增BPM模組授權卡控、前端按鈕觸發按鈕
- **Commit ID**: `7223889b6857b58390c5e353f93ad4362e0e3546`
- **作者**: 邱郁晏
- **日期**: 2024-02-29 14:27:45
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/secudocxModule/SecudocxSetting.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/secudocxModule/SecudocxSettingManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/secudocxModule/SecudocxSettingManagerBean.java`

### 114. [BPM APP]Q00-20240228002 调整使用Oracel资料库 钉钉待办同步页面查询不到资料的问题
- **Commit ID**: `23d50b742c68d686f25ef5d9e5995c32404f4b85`
- **作者**: 周权
- **日期**: 2024-02-28 16:18:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java`

### 115. [BPM APP]Q00-20240228001 调整开启钉钉待办同步收不到推播的问题
- **Commit ID**: `9d4cccf434d3c6e13711fe2e5ce4f2086878e6d6`
- **作者**: 周权
- **日期**: 2024-02-28 16:13:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/GenerateNextWorkItemEventBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`

### 116. [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口(補修正)
- **Commit ID**: `9e32e383a6d1171f5c0e3d1e45a3371678a0deab`
- **作者**: waynechang
- **日期**: 2024-02-27 14:54:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 117. [TIPTOP] S00-20231222001 调用TIPTOP接口增加流程封存还原预防
- **Commit ID**: `ea04558bc5369cbf580248fa2efb58ea3bc1440c`
- **作者**: liuyun
- **日期**: 2024-02-27 14:21:47
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/NaNaXWebHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/NaNaXWebHelper.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/TipTopIntegration.java`

### 118. [SSO]Q00-20240227001 調整Athena三方待辦取得之使用者改透過接口取得租戶代號綁定的BPM使用者
- **Commit ID**: `40ab1ada581447f978296bc67a329135e218c00a`
- **作者**: 林致帆
- **日期**: 2024-02-27 08:46:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java`

### 119. [Web]Q00-20240226003 调整改变浏览器视窗大小后，客制JSP排序功能失效的问题
- **Commit ID**: `6d72e78134e45c770f11651ec59151f2ffeac6d7`
- **作者**: 周权
- **日期**: 2024-02-26 15:59:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 120. [SSO]Q00-20240223001 調整Athena SSO驗證判斷不需增加Athena平台設置的應用程式代號是否命名為BPM
- **Commit ID**: `b431ae055ab1978d2c655e78de75d1d7d3d2fd7a`
- **作者**: 林致帆
- **日期**: 2024-02-23 14:43:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 121. [Web]Q00-20240222003 新增防呆：因表單有不存在之單身的髒資料造成單據無法開啟
- **Commit ID**: `97e03fb08ad813c63a5d36b04ce358c6401b3aa1`
- **作者**: 林致帆
- **日期**: 2024-02-22 15:45:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 122. [Web] Q00-20240222002 修正sql注册器中书写sql的from字段后不带空格用换行导致sql执行错误
- **Commit ID**: `a3d0cc1831dc00b930f6a100680b918371401a19`
- **作者**: liuyun
- **日期**: 2024-02-22 15:16:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 123. [SSO]Q00-20240221004 修正使用SSO登入BPM時開啟的介面都是英文版[補修正]
- **Commit ID**: `ed8a7b05d07cf48d3cd1aa44b101de95ff49b027`
- **作者**: 林致帆
- **日期**: 2024-02-22 11:09:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PortletEntry.jsp`

### 124. [Web] Q00-20240222001 修正列印模式FormUtil.getValue获取不到Textbox和HiddenTextbox值
- **Commit ID**: `ac4104214a357c5fa773e29264414e575e01d5a3`
- **作者**: liuyun
- **日期**: 2024-02-22 10:25:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 125. Revert "[Web] Q00-20240221003 修正追蹤流程匯出Excel功能SQL優化"
- **Commit ID**: `b8eccb12f47f992633f53eb44394b3ebdb079f13`
- **作者**: 邱郁晏
- **日期**: 2024-02-22 10:00:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 126. [Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题(防呆)[补]
- **Commit ID**: `4dd4a40966a88d032ae6fc43a68db64805b202a0`
- **作者**: liuyun
- **日期**: 2024-02-21 17:52:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`

### 127. [Web] Q00-20240221003 修正追蹤流程匯出Excel功能SQL優化
- **Commit ID**: `57d05af21d10d6f17ae4b675e237e36ce1b9f817`
- **作者**: 邱郁晏
- **日期**: 2024-02-21 17:19:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 128. Revert "[Web] Q00-20240221004 修正追蹤流程匯出Excel功能SQL優化"
- **Commit ID**: `0887ebeb8a6231cd0e27a37fbcf352b4c2d628b1`
- **作者**: 邱郁晏
- **日期**: 2024-02-21 17:16:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 129. Revert "[內部]調整平台統一工具程式路徑"
- **Commit ID**: `bda8aad0d38bfaaf000ebe4f15105242c1453d46`
- **作者**: lorenchang
- **日期**: 2024-02-21 16:05:35
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/base/StopWatch.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/README.md`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/StopWatch.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/base/StringUtils.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/html/HtmlUtils.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 130. [內部]調整平台統一工具程式路徑
- **Commit ID**: `d569e43205dbb9602cda27c00285caaf3819d566`
- **作者**: lorenchang
- **日期**: 2024-02-21 15:57:11
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/README.md`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/base/StopWatch.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/StringUtils.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/html/HtmlUtils.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/StopWatch.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 131. [Web]Q00-20240221006 修正Grid的新增，修改，删除Button设定背景色或文字颜色，汇入时颜色样式消失的问题
- **Commit ID**: `737e89f09e214f3f70957021a472af69724396f1`
- **作者**: 周权
- **日期**: 2024-02-21 14:59:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`

### 132. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `444b9512560fff8153a70da8b0ebc117d68308ea`
- **作者**: 邱郁晏
- **日期**: 2024-02-21 14:03:21
- **變更檔案數量**: 0

### 133. [Web] Q00-20240221004 修正追蹤流程匯出Excel功能SQL優化
- **Commit ID**: `c341a50d8b4b56b45c58230ca58cfce19be78c2f`
- **作者**: 邱郁晏
- **日期**: 2024-02-21 14:02:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 134. [SSO]Q00-20240221004 修正使用SSO登入BPM時開啟的介面都是英文版
- **Commit ID**: `a4c767b0f760439a45ee570fd9e395fdea0476db`
- **作者**: 林致帆
- **日期**: 2024-02-21 14:02:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PortletEntry.jsp`

### 135. [PRODT]Q00-20240221002 修正Web流程管理工具的核決層級關卡中設定參考活動為自定義時會導致流程無法簽入的問題
- **Commit ID**: `caa902ec6ed6dfc05109da4d75045266d4c30228`
- **作者**: cherryliao
- **日期**: 2024-02-21 13:38:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 136. [ESS]Q00-20240221001 調整ESS簽核單據增加防呆避免串單
- **Commit ID**: `2224a538c486f13d75aa392e2a1acda424f4b72b`
- **作者**: 林致帆
- **日期**: 2024-02-21 10:47:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 137. [SecuDox] 新增ISO段下載發布檔
- **Commit ID**: `728fe60989ed7989b5789d74389a3242ab652bb9`
- **作者**: 邱郁晏
- **日期**: 2024-02-20 17:30:12
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`

### 138. [流程引擎]Q00-20240220002 修正關卡通知信的內容有設定整張表單，且流程的核決關卡若因退回、取回重辦而需要重新展開核決關卡時，前一關往核決關卡派送時會有NullPointerException的錯誤
- **Commit ID**: `6c97b1f9858655640797f4cff35cd333e3682ff5`
- **作者**: waynechang
- **日期**: 2024-02-20 17:16:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 139. [MPT]S00-20220808002 首頁模組的待辦模塊與列表新增流程名稱過濾功能
- **Commit ID**: `29556cd13da1f2adef6f2089103d2f1c06b41c96`
- **作者**: pinchi_lin
- **日期**: 2024-02-20 16:10:12
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_Oracle.sql`

### 140. [Web] Q00-20240220001 修正转派工作人员意见内容会trim
- **Commit ID**: `b887ea856446b0e3f0c36e05a15505c771e9f0eb`
- **作者**: liuyun
- **日期**: 2024-02-20 14:22:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`

### 141. [行業表單庫]加入模組授權卡控判斷[補]
- **Commit ID**: `5b31aaf06f619e183f096d5d56a51d14e1670036`
- **作者**: yamiyeh10
- **日期**: 2024-02-17 17:49:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

### 142. [PRODT]Q00-20240217001 修正Web化流程管理工具當流程內有核決關卡時部分流程會發生無法簽出問題
- **Commit ID**: `d5549aa209cebd9993023983eaaa7dd5c68f5e0f`
- **作者**: yamiyeh10
- **日期**: 2024-02-17 15:55:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 143. [內部]Q00-20240216004 增加關卡是否完成可以繼續往下派送的log
- **Commit ID**: `11b5142d3a731222b7b8387f71d63c56147415ae`
- **作者**: waynechang
- **日期**: 2024-02-16 17:00:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 144. [流程引擎]Q00-20240216003 流程引擎-簡易流程圖預解析邏輯優化
- **Commit ID**: `0724d8b28de582d0863ea34befe48ddc758dab25`
- **作者**: waynechang
- **日期**: 2024-02-16 14:09:44
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/AbstractActivityDefContainer.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 145. [Web] Q00-20240216002 修正checkBox若設定為唯讀時，預設選項樣式不一致，且無法取消預設選項問題
- **Commit ID**: `3558194680987facef4de35c22fb3b5fd7d28599`
- **作者**: 邱郁晏
- **日期**: 2024-02-16 10:55:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 146. [Web] Q00-20240216001 修正RadioButton若設定為唯讀時，會多預設選項(補)
- **Commit ID**: `d546a6741478402f671a3a6a66ea7670b43b3835`
- **作者**: 邱郁晏
- **日期**: 2024-02-16 10:49:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 147. [Web] Q00-20240216001 修正RadioButton若設定為唯讀時，會多預設選項
- **Commit ID**: `bfb90dba7dae1a2a918a9dc474319cdc88cdf858`
- **作者**: 邱郁晏
- **日期**: 2024-02-16 10:30:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 148. [SecuDox] 修正郵件落地功能異常問題。
- **Commit ID**: `a66ab2f3bff840fd7c5897c0cfba0e163f688373`
- **作者**: 邱郁晏
- **日期**: 2024-02-15 17:19:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 149. [資安]V00-20240123001 修正Vulnerable Component漏洞議題
- **Commit ID**: `8491f3b0beef39e7db74becd15450a3158b6e5ec`
- **作者**: 林致帆
- **日期**: 2024-02-15 14:24:16
- **變更檔案數量**: 150
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/AutomaticSignOffMaintance.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/IWCIndicatorDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/EBGModule/EBGFormManager.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/EBGModule/EBGPropertise.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/EBGModule/EBGSignerTemplate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/ChildGridChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/SSOCallBack.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/TFAModule/TFASetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/TFAModule/TFAUnauthlist.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DesignerDownload/DesignerDownloadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocCategory/ManageDocCategoryMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/PDFUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/LogoImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManageResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ShowSignImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterDingtalkTodoTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/IntelligentLearningManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/WatermarkPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AdjustActivityOrder.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AttachmentHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewDecisionActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/SubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportTemplate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-c.c.d.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrap-c.c.e.min.js`

### 150. [Web]Q00-20240205004 修正监控、追踪流程点击"此分类全部"后再通过"未结案","以关闭","全部"条件筛选后流程条目显示不全的问题
- **Commit ID**: `bc6a848f877ed9edccad4334712d1d7b0cf3e1f3`
- **作者**: 周权
- **日期**: 2024-02-05 17:17:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 151. [Web] Q00-20240205003 修正自定义开窗autocomplete补全列表显示在页面下方
- **Commit ID**: `78b4024d424e8db9b4b5672c4252a1dc9baa6a17`
- **作者**: liuyun
- **日期**: 2024-02-05 16:49:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`

### 152. [Oauth] Q00-20240205001 修正取得資料庫寫法未增加釋放連線
- **Commit ID**: `894aa9df23004f3bd654c1d5a2bd803b646721aa`
- **作者**: 邱郁晏
- **日期**: 2024-02-05 11:15:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthAuthenticationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/oauthModule/OauthSettingManagerBean.java`

### 153. [Web] Q00-20240202002 修正通过服务任务给表单赋值触发警告问题
- **Commit ID**: `ada374fe01978f7c4612f71e69c7a4d24bf0c741`
- **作者**: liuyun
- **日期**: 2024-02-02 17:51:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 154. [web] Q00-20240201002 流程主旨中的某个字段加上颜色标注，在BPM中的流程列表里主旨显示正常，但是在邮件提醒里主旨里显示异常问题修正
- **Commit ID**: `5b26f73b2dcb840bde3d0010a92b28910626bbaf`
- **作者**: 刘旭
- **日期**: 2024-02-02 15:59:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 155. [內部]新增 HTML 工具類(HtmlUtils)
- **Commit ID**: `1faeafd1c4e58145cae1e13ab00c1afb5ba458b4`
- **作者**: lorenchang
- **日期**: 2024-02-02 14:39:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/html/HtmlUtils.java`

### 156. Revert "[內部]新增 HTML 工具類(HtmlUtils)"
- **Commit ID**: `9ded5576b69889a5db341df77d92a8724ed2ce04`
- **作者**: lorenchang
- **日期**: 2024-02-02 14:38:24
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/Jsoup/jsoup-1.17.2.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/pom.xml`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/html/HtmlUtils.java`

### 157. Revert "[web] Q00-20240201002 流程主旨中的某个字段加上颜色标注，在BPM中的流程列表里主旨显示正常，但是在邮件提醒里主旨里显示异常问题修正"
- **Commit ID**: `364fa05669ee493853732a9d32d5ee013f7682da`
- **作者**: lorenchang
- **日期**: 2024-02-02 14:38:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 158. [雙因素模組]Q00-20240202001 修正信任端點資訊有過期資料會造成每次登入都需重複驗政
- **Commit ID**: `2a29d5dbbf68a72bb02593ebda3986d13009f2be`
- **作者**: 林致帆
- **日期**: 2024-02-02 14:36:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 159. [web] Q00-20240201002 流程主旨中的某个字段加上颜色标注，在BPM中的流程列表里主旨显示正常，但是在邮件提醒里主旨里显示异常问题修正
- **Commit ID**: `3d1c9bab8e6259e922edd868920eb19d75048127`
- **作者**: 刘旭
- **日期**: 2024-02-02 13:47:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 160. [內部]新增 HTML 工具類(HtmlUtils)
- **Commit ID**: `d69a4a5aee7eaddefe16a37f55ba249401a208f8`
- **作者**: lorenchang
- **日期**: 2024-02-02 11:22:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Jsoup/jsoup-1.17.2.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/html/HtmlUtils.java`

### 161. [Cosmos]更新Cosmos發單邏輯
- **Commit ID**: `de15432205ee66ddd4420473d06926a9a148f23b`
- **作者**: 林致帆
- **日期**: 2022-12-09 17:09:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/sysintegration/integration/model/SysIntegrationXmlTag.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 162. [流程引擎]Q00-20240201005 修正5894版本，若通知關卡是透過追蹤流程進入表單畫面時，會提示「查詢不到此流程的資料」也無法顯示表單畫面；以及追蹤頁面詳細流程點通知關卡的活動實例也無法顯示任何工作事項
- **Commit ID**: `000d0163ea7deca1a3fb45e2f77779c111dc64fd`
- **作者**: waynechang
- **日期**: 2024-02-01 17:21:34
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 163. [BPM APP]Q00-20240201004 修正使用Android手機操作列表時會發生無法載入更多筆資料問題
- **Commit ID**: `b46e694ac30b14343ae88aa0287a88db71ddf6b1`
- **作者**: yamiyeh10
- **日期**: 2024-02-01 16:02:03
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`

### 164. [Web] Q00-20240201003 修正汇入excel时出现undefined(reading toString)异常信息
- **Commit ID**: `9a2d63001435c47e350d3dd5b60cadedf4d7d522`
- **作者**: liuyun
- **日期**: 2024-02-01 15:25:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 165. [SecuDox] 新增Mail攜出加密功能
- **Commit ID**: `53a5777963e4cda806deff57c96ef8240bbd8bff`
- **作者**: 邱郁晏
- **日期**: 2024-02-01 11:00:55
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 166. [MPT]Q00-*********** 調整系統管理員不受到首頁基本信息權限設置的卡控[補]
- **Commit ID**: `72762aedbf5c149f7f1b35cc6e829adf0f4065dc`
- **作者**: yamiyeh10
- **日期**: 2024-02-01 09:44:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_Oracle.sql`

### 167. [ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊 [補修正]
- **Commit ID**: `72234d9ef6d012f36b87d92eb96c25c8240ccab8`
- **作者**: 林致帆
- **日期**: 2024-02-01 09:06:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 168. [TIPTOP]Q00-20240201001 新增TIPTOP呼叫服務時間的Log
- **Commit ID**: `9c8d5ad19fbfd57769dbbd57e98a0ef02d2c1cf2`
- **作者**: 林致帆
- **日期**: 2024-02-01 08:56:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java`

### 169. [ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊 [補修正]
- **Commit ID**: `429bbd22f099198cf9d1e477e286e60028333511`
- **作者**: 林致帆
- **日期**: 2024-02-01 08:48:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 170. [MPT]Q00-*********** 調整系統管理員不受到首頁基本信息權限設置的卡控
- **Commit ID**: `47562ff0f932c709b4cde8396fecae0d01b4a691`
- **作者**: yamiyeh10
- **日期**: 2024-01-31 18:26:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_Oracle.sql`

### 171. [ESS]Q00-20240131004 新增ESS呼叫服務耗費時間的Log資訊
- **Commit ID**: `df6fb4440394342e9a1b57f05c583cfb6073b913`
- **作者**: 林致帆
- **日期**: 2024-01-31 17:22:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 172. [流程引擎]Q00-20240123002 因流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制[補]
- **Commit ID**: `ba34d1dc59083323604154a994c585505b4f4632`
- **作者**: waynechang
- **日期**: 2024-01-31 15:52:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 173. [流程引擎]Q00-20240131002 流程進版或匯入時，增加檢核流程的關卡連接線的From關卡及To關卡是否存在關卡定義中，若From 或 To 對應的關卡不存在時，將其刪除，避免影響流程運作
- **Commit ID**: `2fdcd4fc3984c08c932fc8a6b3bfb558ae2ce7b6`
- **作者**: waynechang
- **日期**: 2024-01-31 15:05:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 174. [web] S00-20231017004 响应式表单，前后栏位在前面栏位没标签(比如button按钮)与前面不对齐问题修正
- **Commit ID**: `f3da73cc61de88a4e67fe5b5e10de852d609e2d8`
- **作者**: 刘旭
- **日期**: 2024-01-30 16:57:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 175. [Web] Q00-20240130001 修正使用者名字有「𣶏」特殊字，流程派送會重複增加問題
- **Commit ID**: `c6518ccfb81ba502e5b706fc112c5dd63ca8a7c2`
- **作者**: 邱郁晏
- **日期**: 2024-01-30 12:00:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 176. [Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题[补]
- **Commit ID**: `06ae08dfebd430332eeb19655ae6c9ffd436dcfe`
- **作者**: liuyun
- **日期**: 2024-01-30 10:58:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 177. [Web] Q00-20240130002 修正自定义开窗-参考表单资料 返回栏位值显示问题
- **Commit ID**: `e60cdf77bc429373f525b345139556583834e668`
- **作者**: liuyun
- **日期**: 2024-01-30 09:49:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`

### 178. [SecuDocx] 修改以柔整合段，上傳下載調整參數。
- **Commit ID**: `7f3e27f7223b2c7543cc854cc014a77f0d951163`
- **作者**: 邱郁晏
- **日期**: 2024-01-29 18:12:13
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 179. [行業表單庫]加入模組授權卡控判斷[補]
- **Commit ID**: `c6b18b7e369efdee239e1a5ace4951baa114e82d`
- **作者**: yamiyeh10
- **日期**: 2024-01-29 17:45:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

### 180. [Web]Q00-20240129002 修正客制JSP文件，行点击事件onRowClick报错的问题
- **Commit ID**: `3b0fe1ec823ee25c3f68579c0601884ca8b41fdc`
- **作者**: 周权
- **日期**: 2024-01-29 16:52:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 181. [Cosmos]更新整合所需內容
- **Commit ID**: `53c2ef66fe4a58eb24e3f615534cc7a3c4d17e02`
- **作者**: lorenchang
- **日期**: 2024-01-29 16:46:03
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd`
  - 📄 **重新命名**: `"Release/copyfiles/@cosmos/conf/\347\231\274\347\211\210\345\211\215\351\234\200\346\233\264\346\226\260\347\202\272\346\255\243\345\274\217\347\211\210\346\234\254.prsmapping"`
  - ❌ **刪除**: `"Release/copyfiles/@cosmos/form-default/\347\231\274\347\211\210\345\211\215\351\234\200\346\233\264\346\226\260\347\202\272\346\255\243\345\274\217\347\211\210\346\234\254.form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\350\253\213\350\263\274\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(PURI05).form"`
  - 📄 **重新命名**: `"Release/copyfiles/@cosmos/process-default/\347\231\274\347\211\210\345\211\215\351\234\200\346\233\264\346\226\260\347\202\272\346\255\243\345\274\217\347\211\210\346\234\254.bpmn"`
  - 📝 **修改**: `Release/db/create/-59_InitDB.patch`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 182. [BPM APP]S00-*********** BPM APP的流程追蹤已發起流程表單頁面加入催辦功能
- **Commit ID**: `81f6e6717349802d68ab37e2e87efb850e2b3343`
- **作者**: pinchi_lin
- **日期**: 2024-01-29 11:37:32
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileAbstractPlatform.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmProcessInstForTracingVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 183. [SAP]Q00-20240126002 調整SAP整合回寫內容到表單Grid時，增加判斷Grid定義是否有該欄位(Column)，當Column存在時，則插入或更新Column資料；若定義不存在則忽略該筆資料
- **Commit ID**: `dd43f5678fffbee62c2f35bb9394739c99144800`
- **作者**: waynechang
- **日期**: 2024-01-26 17:23:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormInstance.java`

### 184. [COSMOS]更新COSMOS的wsdl檔案
- **Commit ID**: `a8a120f857747f1101f27d52f0b021a729463cb8`
- **作者**: 林致帆
- **日期**: 2024-01-26 16:52:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `Release/copyfiles/@cosmos/conf/CMEFGPService.wsdl`
  - ❌ **刪除**: `"Release/copyfiles/@cosmos/conf/\347\231\274\347\211\210\345\211\215\351\234\200\346\233\264\346\226\260\347\202\272\346\255\243\345\274\217\347\211\210\346\234\254.wsdl"`

### 185. [web] Q00-20240126001 选择新增单子点击批次阅读通知会报错问题修正
- **Commit ID**: `cc01ca3adcb1e0dae8e0a72f0372db521fa43a89`
- **作者**: 刘旭
- **日期**: 2024-01-26 16:52:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 186. [微服務註冊器]初始化開發環境
- **Commit ID**: `7edce1e77496aa8d12c61640fb8819548e29cd43`
- **作者**: lorenchang
- **日期**: 2024-01-26 15:36:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/NaNaXWeb/MircoServiceModule/.gitkeep`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/NaNaXWeb/MircoServiceModule/.gitkeep`

### 187. [SecuDocx] Base段，上傳下載以柔加解密
- **Commit ID**: `2c977565a1a821809bffe6d946dbbbe74ff45bc3`
- **作者**: 邱郁晏
- **日期**: 2024-01-26 15:16:43
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 188. [MPT]S00-*********** 調整公告信息維護附件與常用工具上傳檔案最大限制參考系統設定[補]
- **Commit ID**: `a8e808fb588c45dc0ae309ad6b20f8838690c1e1`
- **作者**: cherryliao
- **日期**: 2024-01-26 10:12:33
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 189. [行業表單庫]加入模組授權卡控判斷
- **Commit ID**: `0ebe5bd00611c5a648b8dee1a693978d30296c7d`
- **作者**: yamiyeh10
- **日期**: 2024-01-26 09:59:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

### 190. [Cosmos]新增範本檔
- **Commit ID**: `dc158c17ab597515ef8ec590354ba0289225fe69`
- **作者**: lorenchang
- **日期**: 2024-01-25 16:00:54
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - ➕ **新增**: `"Release/copyfiles/@cosmos/conf/\347\231\274\347\211\210\345\211\215\351\234\200\346\233\264\346\226\260\347\202\272\346\255\243\345\274\217\347\211\210\346\234\254.prsmapping"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/conf/\347\231\274\347\211\210\345\211\215\351\234\200\346\233\264\346\226\260\347\202\272\346\255\243\345\274\217\347\211\210\346\234\254.wsdl"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/form-default/\347\231\274\347\211\210\345\211\215\351\234\200\346\233\264\346\226\260\347\202\272\346\255\243\345\274\217\347\211\210\346\234\254.form"`
  - ➕ **新增**: `"Release/copyfiles/@cosmos/process-default/\347\231\274\347\211\210\345\211\215\351\234\200\346\233\264\346\226\260\347\202\272\346\255\243\345\274\217\347\211\210\346\234\254.bpmn"`

### 191. [Web]Q00-20240125002 发起流程时切换页签新增提示弹框，并调整弹框内容
- **Commit ID**: `b5c21d26e0047129e0732f9cc755b06410d9c065`
- **作者**: 周权
- **日期**: 2024-01-25 15:30:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 192. [內部]優化 Linux 安裝檔
- **Commit ID**: `a2a009ebcbbd837fcaa1a3b5cde7fb6c6bd90dfd`
- **作者**: lorenchang
- **日期**: 2024-01-25 15:00:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/bin/standalone.conf`
  - 📝 **修改**: `Release/wildfly/bin/standalone.sh`
  - 📝 **修改**: `Release/wildfly/bin/systemd/launch.sh`

### 193. [MPT]S00-*********** 調整公告信息維護附件與常用工具上傳檔案最大限制參考系統設定
- **Commit ID**: `80b70dc89eab06b3e639803ee058dcd511e975e4`
- **作者**: cherryliao
- **日期**: 2024-01-25 14:19:49
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_Oracle.sql`

### 194. [內部]Q00-20240125001 調整清除二階快取的log層級由warn改為debug
- **Commit ID**: `48cc358bab81c11c472d1212dae6febf5ee4890f`
- **作者**: waynechang
- **日期**: 2024-01-25 13:48:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`

### 195. [流程引擎]Q00-20240124003 優化流程在簽核後取活動定義關聯資料的機制
- **Commit ID**: `09ffbe4e4d18364afdc0f4d557ef424e07c3f0b5`
- **作者**: cherryliao
- **日期**: 2024-01-24 18:37:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java`

### 196. [PRODT]Q00-20240124001 修正Web流程管理工具中活動參與者為職務時其值顯示不正確的問題
- **Commit ID**: `8a102e21ab487b052f0fd59da8fc001b8ef8f81c`
- **作者**: pinchi_lin
- **日期**: 2024-01-24 15:20:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 197. [行業表單庫]增加創建行業表單範本到Web表單設計師的接口
- **Commit ID**: `36a00c51c517ac4359d8b64e27fa05c136b7c878`
- **作者**: yamiyeh10
- **日期**: 2024-01-24 14:50:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`

### 198. [流程引擎]Q00-20240123004 修正關卡設定自動簽核4與流程上相同簽核者(不含發起者)跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，無法派送到下一關
- **Commit ID**: `a911617a3a6d729ae805a970bff58146b34c0c55`
- **作者**: waynechang
- **日期**: 2024-01-23 15:25:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 199. [流程引擎]Q00-20240123003 修正進入待辦或追蹤頁面，且流程進入核決關卡時，若核決關卡定義有髒資料或多組相同代號時可能會導致開啟畫面錯誤，因此增加過濾髒資料的邏輯
- **Commit ID**: `b5e1cb321bf04475170ac5598b1945da52242f69`
- **作者**: waynechang
- **日期**: 2024-01-23 15:01:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`

### 200. [流程引擎]Q00-20240123002 流程進版或匯入時，若DB上一版定義或XML存有不存在的或是多組相同Id的ActivitySetDefinition時，會導致流程運作異常，因此在流程進版或匯入時增加過濾髒資料的機制
- **Commit ID**: `38b182c7920c644b90467fd7ad1c53786c15b4b3`
- **作者**: waynechang
- **日期**: 2024-01-23 14:41:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 201. [Web] Q00-20240123001 調整信件樣板設置為整張表單時，Grid元件跑版問題
- **Commit ID**: `cf76a2a0950ca5daae9c4c36211a1e32ac74df9e`
- **作者**: 邱郁晏
- **日期**: 2024-01-23 11:17:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 202. [表單設計師]S00-*********** Dialog系列元件開窗類型選擇部門新增選項"顯示失效部門"
- **Commit ID**: `8147901b12eda054a12ba997e76b36bc7646d08d`
- **作者**: 林致帆
- **日期**: 2024-01-18 18:02:11
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/DialogInputElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/DialogInputLabelElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/DialogInputMultiElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 203. [BPM APP]Q00-20240118003 修正IMG操作進入開窗頁面後回到表單畫面時沒有顯示左上方的返回按鈕問題
- **Commit ID**: `aa7450aa579a7864d8cf273ce039a5698f567ba8`
- **作者**: yamiyeh10
- **日期**: 2024-01-18 14:21:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 204. [Web] Q00-*********** 調整有不同組織相同部門Id時，部門主管首頁顯示在塗總處理量異常問題(補修正)
- **Commit ID**: `dab1ed9f058ea15a98b696665fc93cb118df8b28`
- **作者**: 邱郁晏
- **日期**: 2024-01-18 13:59:51
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 205. [Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache
- **Commit ID**: `0513b95df432a4e52b85f4f9f73041dc8b4dd7bd`
- **作者**: 林致帆
- **日期**: 2024-01-18 11:05:51
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 206. [Web]Q00-20240118002 修正重启BPM后会出现多次'頁面閒置過久或伺服器端服務資訊異常，請重新登入'弹窗的问题
- **Commit ID**: `a78557c624090ee7f20f8c73b14fd8decab398d8`
- **作者**: 周权
- **日期**: 2024-01-18 10:11:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 207. Revert "[Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache"
- **Commit ID**: `5331b62d9595b91dae22532d6c340c06a02efcf4`
- **作者**: 林致帆
- **日期**: 2024-01-18 08:15:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 208. [Web] Q00-*********** 調整有不同組織相同部門Id時，部門主管首頁顯示在塗總處理量異常問題
- **Commit ID**: `ca58c9708fad6c972d008c43eb1bd0b13d35d308`
- **作者**: 邱郁晏
- **日期**: 2024-01-17 16:55:49
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 209. [Web]Q00-*********** 調整變更系統參數邏輯改成更新其它流程主機時移除Cache裡的該筆設定，讓BPM調用該設定時重取並更新回Cache
- **Commit ID**: `cdbc86a342922f346a60649ab718932423f34ce8`
- **作者**: 林致帆
- **日期**: 2024-01-17 13:48:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 210. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `6737afbe15f782e35e52db0d2de7e9d8233cc5df`
- **作者**: liuyun
- **日期**: 2024-01-17 11:10:49
- **變更檔案數量**: 0

### 211. [Web] Q00-20240117002 修正通知信进入待办阅读次数加2
- **Commit ID**: `a30a9c693082388bf32c692a2cb752eccbdbb6d9`
- **作者**: liuyun
- **日期**: 2024-01-17 11:10:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 212. [組織同步] Q00-20240116002 調整組織同步部門ID時，自動刪除空白字符(補修正)
- **Commit ID**: `42436881d4ad4a5075f3852cd45eaeef9c7da212`
- **作者**: 邱郁晏
- **日期**: 2024-01-17 11:09:12
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NaNaTableUtilV2_4.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/SyncTableUtilV1_4.java`

### 213. [Web]Q00-20240117001 修正追蹤流程列表中任一流程有顯示關注訊息，手機模式下點擊流程就沒有反應的問題
- **Commit ID**: `aa49908d648dff5f4b08b8b9a01410a8d659cc19`
- **作者**: cherryliao
- **日期**: 2024-01-17 10:38:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 214. [BPM APP]Q00-20240116005 修正整合釘釘情況下使用Android手機上傳多筆附件時檔案重複問題
- **Commit ID**: `81ae5d136fd82dd54f04aa199bae7d6e8bd89645`
- **作者**: yamiyeh10
- **日期**: 2024-01-16 17:47:39
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 215. [PRODT]調整流程實例中的詳細流程圖更換成Web化流程管理工具使用的bpmnjs套件
- **Commit ID**: `2889c6d78d26fd05e22d74843f5c75a1ccfb3bda`
- **作者**: pinchi_lin
- **日期**: 2024-01-16 16:10:51
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessPackageManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessPreviewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-previewProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessPicture.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/css/bpmn-js/bpmn-js.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/bpmn-js/custom-bpmn-navigated-viewer.production.min.js`

### 216. [Web]Q00-20240116003 修正当单身内容长度过大setColumnWidth没有效果的问题
- **Commit ID**: `0e0166b562377d176a84e0c2c28eeef1b4bb5bf0`
- **作者**: 周权
- **日期**: 2024-01-16 13:30:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 217. [組織同步] Q00-20240116002 調整組織同步部門ID時，自動刪除空白字符
- **Commit ID**: `00276a4ad68ac51446c0893320d4ea435655a477`
- **作者**: 邱郁晏
- **日期**: 2024-01-16 11:37:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 218. [附件擴充] 調整系統變數file.encryption.management.jndiname不須重啟即生效
- **Commit ID**: `b9f35e9a03f107a0baba369c5ecf68d14c79fe06`
- **作者**: 邱郁晏
- **日期**: 2024-01-15 16:35:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 219. [附件擴充] 調整系統變數file.encryption.management.jndiname不須重啟即生效
- **Commit ID**: `3d0708a41b2f52da21c1136dcb176f24e3fa1795`
- **作者**: 邱郁晏
- **日期**: 2024-01-15 16:35:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 220. [Web] Q00-20240115001 修正LDAP账号登录失败，密码错误次数叠加问题
- **Commit ID**: `dc3d7f11d271e21629b528fb0d88d6988c761302`
- **作者**: liuyun
- **日期**: 2024-01-15 13:57:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 221. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `bd2833d2a5743cb4e8c037dbf1e9a565505d614e`
- **作者**: 周权
- **日期**: 2024-01-15 09:18:10
- **變更檔案數量**: 0

### 222. [Web]S00-20231123001 BPM右上角工具列新增登出按钮
- **Commit ID**: `8acb807e42532a92b1d76584b3143c255a78cba4`
- **作者**: 周权
- **日期**: 2024-01-15 09:17:51
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-icon-logout-hover.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/main-icon-logout.png`

### 223. [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口(補修正)
- **Commit ID**: `403d06f0421d64c2ac21327462b6613dc8702f24`
- **作者**: 邱郁晏
- **日期**: 2024-01-15 09:12:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 224. [web] Q00-20240102001 調整系統變數寫法:前端JSP頁面，改使用新寫法，直接取到SystemVariableCache表的值。[补修正]
- **Commit ID**: `355e4a804779c43f124413cd0b938ffd55472322`
- **作者**: 刘旭
- **日期**: 2024-01-11 10:28:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 225. [web] Q00-20240102001 調整系統變數寫法:前端JSP頁面，改使用新寫法，直接取到SystemVariableCache表的值。
- **Commit ID**: `742e06cf1029e3471a271c264a1cec8e3e252c53`
- **作者**: 刘旭
- **日期**: 2024-01-11 09:40:40
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 226. [BPM APP]Q00-20240110003 修正移動端列表在跳轉至表單畫面前沒有顯示遮罩導致用戶誤連續點擊而發生錯誤問題
- **Commit ID**: `eb7edf8eca8212a2708f4f88fec55d2f2cc90233`
- **作者**: yamiyeh10
- **日期**: 2024-01-10 16:44:35
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListResigendV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`

### 227. [Web] Q00-*********** 調整顯示首頁按鈕權限，從Session方式改為存入UserProfile資訊內(補修正)
- **Commit ID**: `4d4c580d37c8e8bfdd58c4c047e285cba6e1bc0b`
- **作者**: 邱郁晏
- **日期**: 2024-01-04 15:54:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 228. [Web] S00-20231221001 提供维护样板可调用单笔流程封存还原接口
- **Commit ID**: `03a54cb6519f75c86e0ab653da115b7df66624c5`
- **作者**: liuyun
- **日期**: 2024-01-10 14:11:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/data/HttpUtil.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/NaNaXWebHelper.java`

### 229. [Web] Q00-20240110002 修改跳过关卡预设意见中去掉<br>
- **Commit ID**: `bdb6297e9d346890ceb241678970ca680684c5bb`
- **作者**: liuyun
- **日期**: 2024-01-10 13:17:14
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 230. [web] V00-20231225001 只增加了序號,但在地化功能未納入控管问题修正[补修正]
- **Commit ID**: `e6fb3caa44fc552289d7649f70378fb50661255d`
- **作者**: 刘旭
- **日期**: 2024-01-10 11:22:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 231. [MPT]S00-20231024001 首頁模組的待辦列表主旨增加待辦來源文字[補]
- **Commit ID**: `51dba41555b65fd2842e8bdcbee1b00d34070800`
- **作者**: yamiyeh10
- **日期**: 2024-01-10 09:42:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/MPT_5.8.10.1_DML_Oracle.sql`

### 232. [Web] Q00-20240102003 修正系統參數邏輯改從SystemVariableCache取得設定
- **Commit ID**: `3eacc2f0a591f675140b0fe7c0feeaea8d52d107`
- **作者**: liuyun
- **日期**: 2024-01-09 17:47:20
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessDemoAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessPreviewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp`

### 233. [web] Q00-20240108003 数值栏位属性中配置的是无条件舍去时结果错误问题修正[补修正]
- **Commit ID**: `3404852af06f4d2d78d61dc3518f2b749be7d2e4`
- **作者**: 刘旭
- **日期**: 2024-01-09 15:52:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 234. [Web]Q00-20240102005 修正系統參數:修改後無需重啓即生效，并新增防呆
- **Commit ID**: `9a69ba8b7a6d9935ec807628369210c50b69d2b1`
- **作者**: 周权
- **日期**: 2024-01-09 14:56:37
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/InvokeProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceRelationalProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileFileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalRelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileInvokeServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`

### 235. [Web]Q00-20240102002 修正系統參數邏輯改從SystemVariableCache取得設定
- **Commit ID**: `63a9c0584b480a397c1889082c7262b7eff5bc2f`
- **作者**: 林致帆
- **日期**: 2024-01-08 18:10:45
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAuthorizedPrsInsListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 236. [Web] Q00-20240102004 修正系統參數:忽略HTML TAG，修改後無須重啟即生效
- **Commit ID**: `cf950946e391466c13f338ce4df8872167868091`
- **作者**: 邱郁晏
- **日期**: 2024-01-08 17:07:32
- **變更檔案數量**: 50
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExpenseAccountItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormInstanceViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/RelationalProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/TraceAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/TraceParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/TraceRelationalProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/ViewAllClosedWorkItems.jsp`

### 237. [web] V00-20231225001 只增加了序號,但在地化功能未納入控管问题修正[补修正]
- **Commit ID**: `b113d692a03996f14ad80927bc65929c7831aced`
- **作者**: 刘旭
- **日期**: 2024-01-08 16:58:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 238. [web] Q00-20240108003 数值栏位属性中配置的是无条件舍去时结果错误问题修正
- **Commit ID**: `632eeca57c541dcecf260437d3355dcc14725f3f`
- **作者**: 刘旭
- **日期**: 2024-01-08 16:57:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 239. [內部]更新平台統一工具庫 StringUtils
- **Commit ID**: `999e90d206b04179b6174836101ba2fac5f20918`
- **作者**: lorenchang
- **日期**: 2024-01-08 16:34:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/StringUtils.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/Test.java`

### 240. [ISO]S00-*********** "ISO部門資訊批次更新"表單調整原部門可選取到失效部門
- **Commit ID**: `363d69c5aa19b869970c56c46460c8921067a530`
- **作者**: 林致帆
- **日期**: 2024-01-08 15:38:41
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllOrgUnitListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`

### 241. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `610560538401da7b659d662006aadb1637c856fd`
- **作者**: 林致帆
- **日期**: 2024-01-08 15:38:03
- **變更檔案數量**: 0

### 242. [Web] Q00-*********** 調整批次離職工作轉派，選取人員為空時拋undefined問題，新增防呆
- **Commit ID**: `b981a0beb77a9aa165f82914187d2049e47c71fd`
- **作者**: 邱郁晏
- **日期**: 2024-01-08 15:34:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 243. Revert "[ISO]S00-*********** "ISO部門資訊批次更新"表單調整原部門可選取到失效部門"
- **Commit ID**: `be26c2bf4c70df8664d4132ddd228cbe7bb50bf4`
- **作者**: 林致帆
- **日期**: 2024-01-08 15:32:26
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllOrgUnitListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`

### 244. [ISO]S00-*********** "ISO部門資訊批次更新"表單調整原部門可選取到失效部門
- **Commit ID**: `ef76d04d5367076621137dfec12426305e2a0976`
- **作者**: 林致帆
- **日期**: 2024-01-08 15:25:43
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllOrgUnitListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`

### 245. [Web] Q00-*********** 調整絕對位置表單進版時，部分表單欄位反黑異常問題，新增防呆
- **Commit ID**: `de6c5d773164cd4860551a2063da22342d5e3f0c`
- **作者**: 邱郁晏
- **日期**: 2024-01-08 11:52:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java`

### 246. [Web] Q00-*********** 調整顯示首頁按鈕權限，從Session方式改為存入UserProfile資訊內(補修正)
- **Commit ID**: `3cb05b2bd1da1ee1f6b510a16f7505960427cfdc`
- **作者**: 邱郁晏
- **日期**: 2024-01-05 16:15:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 247. [組織同步] S00-20210120001 調整組織同步部門失效時，需過濾離職人員
- **Commit ID**: `1bda6ba41fc84ada2725fdd9b297e9fa62861070`
- **作者**: 邱郁晏
- **日期**: 2024-01-05 10:27:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 248. [BPM APP]Q00-20240104003 將行動端表單下方的按鈕位置調整置中樣式
- **Commit ID**: `797ccb7a77c09295c828d71df90f818c7090f0a7`
- **作者**: yamiyeh10
- **日期**: 2024-01-05 09:01:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 249. [SAP]Q00-20240104002 修正SAP整合，当Grid更新或新增固定值栏位时，只会保留本次更新之前栏位固定值会被删掉的问题
- **Commit ID**: `4d91b5e49b4ac1c7f622fb1ef990b1e563d6d65c`
- **作者**: 周权
- **日期**: 2024-01-04 17:30:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`

### 250. [流程引擎]Q00-*********** 修正模擬使用者簽核關卡後，追蹤流程頁面偶發會出現交易異常的錯誤(情境為核決關卡內的下一關處理者為需自動簽核跳關時容易發生)
- **Commit ID**: `cd81436521f350da0869caf9ab87b197bf7e3a1d`
- **作者**: waynechang
- **日期**: 2024-01-04 16:31:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessTraceControllerDelegate.java`

### 251. [Web] Q00-*********** 調整顯示首頁按鈕權限，從Session方式改為存入UserProfile資訊內
- **Commit ID**: `2df64eab8a8301c2e1bdbe1b0d24e13c46a6926b`
- **作者**: 邱郁晏
- **日期**: 2024-01-04 15:54:45
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 252. [BPM APP]S00-*********** BPMAPP移動列表增加模糊查詢功能
- **Commit ID**: `281c035695f3c24f1dfddef1d3833a32dbda5d00`
- **作者**: yamiyeh10
- **日期**: 2024-01-04 11:38:46
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListResigendV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 253. [web] V00-20231225001 只增加了序號,但在地化功能未納入控管问题修正
- **Commit ID**: `3fada7292bb0a886fbdd5a943ef2e8787c80b5d4`
- **作者**: 刘旭
- **日期**: 2024-01-04 11:19:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MainMenuManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 254. [組織同步] Q00-20240103003 修正同步部門生失效時，若子部門已生效時，無法正常同步
- **Commit ID**: `de418a862a64e1666645a3213bb81195e6b7695e`
- **作者**: 邱郁晏
- **日期**: 2024-01-04 10:53:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 255. [Web] Q00-20240103002 修正checkbox绑定其他checkbox元件，存储表单后无法更改绑定的元件
- **Commit ID**: `ce26d60f6b1e66f1ddc6d5ef3b1e1026d82ac003`
- **作者**: liuyun
- **日期**: 2024-01-03 17:41:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 256. [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正-2)
- **Commit ID**: `55fbb65c6f7bb659294929f26986efe512c3d1f4`
- **作者**: liuyun
- **日期**: 2024-01-03 17:06:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 257. [BPM APP]S00-20221109003 BPMAPP在整合企業微信時新增排程可以禁用離職人員並刪除歸戶資料
- **Commit ID**: `30a1c0c83a8a047c86d1f9371ab05efd8a3ac389`
- **作者**: yamiyeh10
- **日期**: 2024-01-03 15:50:43
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatSchedule.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/wechat/MobileWeChatService.java`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 258. [Web] Q00-20240103001 修正grid设置冻结栏位后设置样式显示错误
- **Commit ID**: `42f1b6ee8f1376220d225901ad98cd2a3c1303cd`
- **作者**: liuyun
- **日期**: 2024-01-03 13:57:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 259. [Web] Q00-20240102006 調整部分系統變數無須重啟即生效
- **Commit ID**: `acd0f6229b592dcb9155bcd65a8a438691fa8285`
- **作者**: 邱郁晏
- **日期**: 2024-01-02 17:05:30
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 260. [PRODT]新增匯出流程PNG功能[補]
- **Commit ID**: `dc42015d6cb2e894c3849fdc73cd813827705725`
- **作者**: pinchi_lin
- **日期**: 2024-01-02 16:37:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 261. [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常(补修正)
- **Commit ID**: `4abfe59588e2cc2501b02f9ce8f8659d96ca7b38`
- **作者**: liuyun
- **日期**: 2024-01-02 15:14:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 262. [MPT]S00-20231024001 首頁模組的待辦列表主旨增加待辦來源文字
- **Commit ID**: `29bed56f663150bda82eb30b0c1a6601f7f97049`
- **作者**: yamiyeh10
- **日期**: 2024-01-02 10:35:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `Release/db/update/MPT_5.8.10.1_DML_DM8.sql`
  - ➕ **新增**: `Release/db/update/MPT_5.8.10.1_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/MPT_5.8.10.1_DML_Oracle.sql`

### 263. [組織同步] S00-20230201001 調整HR組織同步，郵件設置改取系統管理中的郵件設定(補修正)
- **Commit ID**: `0828c977b99eb625f3d39612f2d8f777ecb5fe99`
- **作者**: 邱郁晏
- **日期**: 2023-12-29 15:59:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/SendMailUtil.java`

### 264. [組織同步] Q00-20231229004 調整Users表的mailAddress欄位上限至100，以避免中介表mailAddress大小超過50導致同步失敗
- **Commit ID**: `735605c32b3a16a22bb8cb07e192a1141bc8110a`
- **作者**: 邱郁晏
- **日期**: 2023-12-29 14:53:02
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`

### 265. [Web]Q00-20231229003 调整"追蹤"“監控”使用表单自适应宽度調整書面寬度無效果的問題
- **Commit ID**: `6e73569339c2b2fdedfdf457a4f796976e89c962`
- **作者**: 周权
- **日期**: 2023-12-29 13:58:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 266. [Web]Q00-20231229002 调整个人资讯-->表单自适应宽度slider预设值为“较宽”
- **Commit ID**: `6019793a61958c1768fdf32f4f74985e30788af9`
- **作者**: 周权
- **日期**: 2023-12-29 13:33:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 267. [Web] Q00-20231229001 修正查询维护样板不输入查询条件排序异常
- **Commit ID**: `8f4cb05f7ba35c0abb499878d132b19f805fa3f8`
- **作者**: liuyun
- **日期**: 2023-12-29 10:57:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 268. [ORGDT]Q00-20231228002 修正Web化組織管理工具中編輯工作行事曆時操作刪除後再新增資料後會有異常的問題
- **Commit ID**: `927d665dbf9997f9273b3918ac47986b28436d26`
- **作者**: pinchi_lin
- **日期**: 2023-12-28 18:09:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 269. [流程引擎]S00-20230710001 優化流程引擎對於離職轉派的支持度
- **Commit ID**: `66cd2dbd635552d2e6358f92dd89191a408de9c1`
- **作者**: waynechang
- **日期**: 2023-12-27 13:55:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 270. [BPM APP]Q00-20231207001 調整釘釘待辦整合在沒有啟用釘釘多法人情況下將創建者改為流程發起人使得釘釘可以發送待辦消息給簽核者
- **Commit ID**: `d43243e82af7e5b8f02a16444c8a19e4012f49b6`
- **作者**: yamiyeh10
- **日期**: 2023-12-27 11:03:27
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java`

### 271. [PRODT]新增匯出流程PNG功能
- **Commit ID**: `262daf10e38e16ecb99f7d42b1ae647dddfac3fd`
- **作者**: pinchi_lin
- **日期**: 2023-12-26 19:35:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 272. [PRODT]新增流程圖自動排版功能[補]
- **Commit ID**: `5a0029d7041a48f8f3be5a6480a3a45d2227f907`
- **作者**: pinchi_lin
- **日期**: 2023-12-26 19:12:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 273. [Web]Q00-20231226002 修正待辦清單頁進入ESS表單時或表單頁面點擊「處理上、下個工作」或簽核後直接跳下一個待辦時，工具列上方的Title欄位未正確顯示流程名稱的異常
- **Commit ID**: `3072ede257b6bf33497d567cd386ac7aa4c3e7ac`
- **作者**: waynechang
- **日期**: 2023-12-26 16:44:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`

### 274. [Web] Q00-20231226001 附件名称包含特殊字符(𡘙)，流程派送后显示无限增长
- **Commit ID**: `8fe1d7cfd3e73740690cadbfca07b9e462805121`
- **作者**: liuyun
- **日期**: 2023-12-26 11:32:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 275. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `074220caddf9019d4c3389911a88a4a4b1f4e368`
- **作者**: liuyun
- **日期**: 2023-12-25 16:12:29
- **變更檔案數量**: 0

### 276. [BPM APP]Q00-20231225002 調整釘釘集成多對一推播時有誤判的問題
- **Commit ID**: `ab65da99d8a75e8bb46781f86d1558df32921cf7`
- **作者**: cherryliao
- **日期**: 2023-12-25 16:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`

### 277. [TIPTOP] S00-20231221001 新增TIPTOP回寫關卡支持錯誤訊息顯示在畫面上
- **Commit ID**: `50d9ceef6b687fd8dd7d731dab4271e354adbf9f`
- **作者**: liuyun
- **日期**: 2023-12-25 15:58:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerLocal.java`

### 278. Revert "[Web] S00-20231221001 TIPTOP回寫關卡支持錯誤訊息顯示在畫面上"
- **Commit ID**: `b154677bde6f2f31f1bb8096052bbe6646aea2b1`
- **作者**: liuyun
- **日期**: 2023-12-25 15:38:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerLocal.java`

### 279. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `4cd4e662756e35d9e46fd60a8f8e17656faf13e4`
- **作者**: liuyun
- **日期**: 2023-12-25 15:07:27
- **變更檔案數量**: 0

### 280. [TIPTOP] S00-20231221001 TIPTOP回寫關卡支持錯誤訊息顯示在畫面上
- **Commit ID**: `d4891e01155c98bf5f5002cd384ebd22e05c7dd3`
- **作者**: liuyun
- **日期**: 2023-12-25 15:01:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerLocal.java`

### 281. [Web] S00-20231221001 TIPTOP回寫關卡支持錯誤訊息顯示在畫面上
- **Commit ID**: `0443c366f3e689f974449e72a926fb705aa113d0`
- **作者**: liuyun
- **日期**: 2023-12-25 15:01:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerLocal.java`

### 282. [web] S00-20230921001 模擬使用者小視窗高度调整
- **Commit ID**: `b43e4abdaa8e9ce65a285479e9c16b8f01ea0a65`
- **作者**: 刘旭
- **日期**: 2023-12-25 13:44:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ValidationTest.js`

### 283. [Web] Q00-20231225001 調整URL為traceProcessFromExternalWeb支持「記住我」直接登入功能
- **Commit ID**: `85bd31651d08e1b6448df171167b80faa28d749b`
- **作者**: 邱郁晏
- **日期**: 2023-12-25 10:36:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 284. [PRODT]新增流程圖自動排版功能
- **Commit ID**: `aef02c6d170964c38a4090b7a77f573047c88b3a`
- **作者**: pinchi_lin
- **日期**: 2023-12-22 14:32:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 285. [其他] Q00-20230828003 修改 ReportDesignerDefinition 资料表 sqlConditionLists 栏位字段类型
- **Commit ID**: `81a296cc84e44e3d9c47ec0115d18cbca34d7639`
- **作者**: liuyun
- **日期**: 2023-12-21 17:22:27
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ReportModuleAccessor.java`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_DM8.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DDL_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DDL_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DDL_Oracle.sql`

### 286. [BPM APP]Q00-20231221005 修正釘釘待辦整合沒有啟用系統郵件時會發生JSONException問題
- **Commit ID**: `cab1e1cd0c18ffbeb5126bcea77f8cce18674dbb`
- **作者**: yamiyeh10
- **日期**: 2023-12-21 16:27:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`

### 287. [BPM APP]Q00-20231221001 修正行動端追蹤流程取得簽核歷程或顯示流程時會偶發沒有顯示資料問題
- **Commit ID**: `00ebd554b2586e0b1ef49d04da826dbecfd38c98`
- **作者**: yamiyeh10
- **日期**: 2023-12-21 16:07:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 288. [TIPTOP]Q00-20231221004 修正TIPTOP發單若發單失敗未產生流程，解決異常問題後再次發單還是失敗
- **Commit ID**: `97f613ceeec97cf2757b056f4bcc7105103e30fa`
- **作者**: 林致帆
- **日期**: 2023-12-21 15:36:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 289. [流程引擎]Q00-20231221003 修正流程引擎啟用「啟動下一關工作(workItem)時要執行的jndi服務」時，若當前結束的關卡為服務任務時，觸發「啟動下一關工作(workItem)時要執行的jndi服務」所傳遞的資料不完整的異常
- **Commit ID**: `d30c181caa2f177f254c3a5abc4bd791cc8c3185`
- **作者**: waynechang
- **日期**: 2023-12-21 14:22:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 290. [Web]Q00-*********** 建立登入or登出記錄物件資料request为空时新增防呆[补修正]
- **Commit ID**: `4bc38749488cecc007f261e53a0bdf72d65d9f5b`
- **作者**: 周权
- **日期**: 2023-12-21 13:28:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 291. [BPM APP]Q00-20231221002 調整行動表單Textarea元件在唯讀狀態下沒有換行效果的問題
- **Commit ID**: `6102d326c6d62ac2c0b8ed7b9a84e4bec741bbca`
- **作者**: cherryliao
- **日期**: 2023-12-21 10:37:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 292. [雙因素認證]Q00-*********** 修正LDAP登入輸入帳號錯誤不該影響登入畫面進錯誤頁面
- **Commit ID**: `522fd91b4d5561954f7aaa9657cc183df10b75b4`
- **作者**: 林致帆
- **日期**: 2023-11-01 14:04:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`

### 293. [Web] Q00-*********** 修正小螢幕點選流程時，結案狀態未顯示問題(補)
- **Commit ID**: `fb53c9e89fddb783403851a2637e32d2b3bd8828`
- **作者**: 邱郁晏
- **日期**: 2023-12-20 16:53:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 294. [流程封存]修正若特製流程存在條件時，會出現ConditionDefinition PK 重覆導致無法封存的異常
- **Commit ID**: `fb9d9b3eaa4ecc8284490a26b8fa3f378891edbb`
- **作者**: lorenchang
- **日期**: 2023-12-20 15:26:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/processarchive/ProcessArchiveCommonImpl.java`

### 295. [流程引擎]Q00-*********** 修正新版自動簽核邏輯，當核決關卡下一關為核決關卡，且需要判斷自動簽核時，且兩個核決關卡都為相同處理者時應自動跳關，但偶發會無法自動跳關
- **Commit ID**: `315cdad1758777029242c33eaa3a0e1e3d64f701`
- **作者**: waynechang
- **日期**: 2023-12-20 15:10:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 296. [Web] Q00-*********** 修正小螢幕點選流程時，結案狀態未顯示問題
- **Commit ID**: `0660acc4c777921cd0702c510fbf84bcb42105f0`
- **作者**: 邱郁晏
- **日期**: 2023-12-20 14:09:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 297. [web] Q00-20231220003 當user使用Android手機、並有調整「字型大小」時，登入網頁的綁定畫面-QRCode會跑版問題修正
- **Commit ID**: `f81f1523dc243a8baa9f0075d0b22a359c3e2c77`
- **作者**: 刘旭
- **日期**: 2023-12-20 11:26:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 298. [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口(補修正)
- **Commit ID**: `808a6e8919fd21810117299b5d559f08ed882e4f`
- **作者**: 邱郁晏
- **日期**: 2023-12-20 10:38:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 299. [BPM APP]Q00-20231220001 修正LINE綁定LDAP帳號時會出現使用者帳號不存在的問題
- **Commit ID**: `30fc1dd948f1c6937067929937241c09b0dbb8cd`
- **作者**: cherryliao
- **日期**: 2023-12-20 10:17:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`

### 300. [BPM APP]Q00-20231219002 修正授權中間層管理頁面的使用者維護管理作業上查詢特定人員時發生錯誤問題
- **Commit ID**: `dc5d1483006a64a6c52210a5a73ad805e79efdd0`
- **作者**: yamiyeh10
- **日期**: 2023-12-19 18:01:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java`

### 301. [BPM APP]S00-20231212001 調整钉钉移動端附件依系統變數的啟用支持預覽操作
- **Commit ID**: `c5973c466dab679d4c9e0c3caccf1dffff6e1c14`
- **作者**: pinchi_lin
- **日期**: 2023-12-19 14:00:14
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 302. [BPM APP]C01-20221208002 移除移動端WorkFlowERP查看過去審批流程功能[補修正]
- **Commit ID**: `c972ba98f77b51630aaf36425ed4ddbd590a2326`
- **作者**: 林致帆
- **日期**: 2023-12-19 11:03:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 303. [WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能 [補修正]
- **Commit ID**: `f38c313a5efa26bb43a830797aecab6652ec29cc`
- **作者**: 林致帆
- **日期**: 2023-12-19 11:01:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 304. [Web]Q00-20231219001 調整系統設定LDAP登入時，登入畫面帳號欄位提示訊息的多語系問題
- **Commit ID**: `0952ddb8d4be57a5865227641a8fcc94d1c3e757`
- **作者**: cherryliao
- **日期**: 2023-12-19 10:35:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 305. [流程引擎] Q00-20231218003 調整簽核歷程排序共用接口
- **Commit ID**: `c0b52e900c6e453a62e15ea2f6a979f78a84fe7c`
- **作者**: 邱郁晏
- **日期**: 2023-12-18 18:09:13
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ActInstanceInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActivityInstanceComparator.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/TraceProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TiptopAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmRelevantDataTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalRelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/WebServiceUtil.java`

### 306. [內部]新增平台統一工具庫
- **Commit ID**: `84cb17a45aee4ac0e875954325d8575a38f88268`
- **作者**: lorenchang
- **日期**: 2023-12-18 16:35:01
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/lib/JakartaCommons/commons-lang3-3.14.0.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-process-archive/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/README.md`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/StopWatch.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/StringUtils.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/platform/util/base/Test.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JakartaCommons/commons-lang3-3.14.0.jar`

### 307. [Web]修Q00-20231218001 修正系統參數(processaccessor.activity.name.format.unit-duty=true)時，簽核歷程的關卡若為進行中尚未簽核時，關卡名稱應顯示為[部門-職稱]，而非[關卡定義名稱]
- **Commit ID**: `a5f4bda8b3075aefe3e583876207aa941520d52f`
- **作者**: waynechang
- **日期**: 2023-12-18 15:31:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 308. [Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題(補)
- **Commit ID**: `756b1e09afc42ac285f09adb145a533ca5de7a8f`
- **作者**: 邱郁晏
- **日期**: 2023-12-18 11:30:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`

### 309. [Web]Q00-*********** 建立登入or登出記錄物件資料request为空时新增防呆
- **Commit ID**: `1940695cd57592168b6cbae567528574875f753a`
- **作者**: 周权
- **日期**: 2023-12-15 17:52:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 310. [Web] Q00-20231215003 由url链接进入待办，清除wms_user_isURL的session
- **Commit ID**: `698c7e5b7ae00d68f4f84d6a9bddd10d3afe6098`
- **作者**: liuyun
- **日期**: 2023-12-15 14:46:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 311. [Web]Q00-20231215002 修正RadioButton和CheckBox勾选最后一个选项额外输入框，在刚进入流程时未选择却可以输入的问题
- **Commit ID**: `04ac87300450e4f6c84807c13e90339bc9d89b99`
- **作者**: 周权
- **日期**: 2023-12-15 11:30:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/resources/html/SelectElementTemplate.txt`

### 312. [Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題(補)
- **Commit ID**: `18ff35d20b2f2d8c3696c9b1e12167145c8bac9a`
- **作者**: 邱郁晏
- **日期**: 2023-12-15 10:12:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 313. [Web] Q00-20231215001 修正使用者登入登出紀錄多筆紀錄時，出現兩個滾軸問題
- **Commit ID**: `7167a664760b37f229eefccb3f6be3867e47f0f6`
- **作者**: 邱郁晏
- **日期**: 2023-12-15 10:08:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap/bootstrapTable/bootstrap-table-1.8.1.css`

### 314. [PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題
- **Commit ID**: `6966af50c4e261ffd9eecb6f375bd97d306bc44f`
- **作者**: pinchi_lin
- **日期**: 2023-12-14 18:37:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 315. [內部]配合 NaNaXWeb 修正 Http Message Converter 轉換物件到 JSON 時 OID 會變成 oid 的問題，透過 @JsonProperty("OID") 強制轉換為 OID
- **Commit ID**: `8883ad61705a67b6e053c90d22163bf2b8a586f3`
- **作者**: lorenchang
- **日期**: 2023-12-14 13:20:41
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Jackson/jackson-annotations-2.13.5.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-app/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/nana-process-archive/jboss-deployment-structure.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/pom.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/ProcessInstanceForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/persistence/AbstractPersistentObject.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/Jackson/jackson-annotations-2.13.5.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/pom.xml`

### 316. [BPM APP]Q00-20231214001 修正使用電腦版釘釘操作行動表單時因scrollbar原生樣式導致意見區塊不好填寫問題
- **Commit ID**: `8686147d5dcafd3e58d0d672e61d5f89d8552708`
- **作者**: yamiyeh10
- **日期**: 2023-12-14 09:32:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 317. [Web] S00-20230810002 优化核决层级关卡名称显示
- **Commit ID**: `dd8464460b2e8dd4ab03a31166124ecb78a23dc5`
- **作者**: liuyun
- **日期**: 2023-12-13 16:29:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 318. [SAP]Q00-20231213003 修正SAP整合服務，當回傳的資料類型為絕對位置表單Grid時，可能會有GridColumnId與GridValue順序錯誤的異常
- **Commit ID**: `81fbf232d78255b4b2fedc19d7688c667d440d05`
- **作者**: waynechang
- **日期**: 2023-12-13 15:44:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 319. [Web] Q00-20231213001 修正簽核意見有中括弧符號被濾除問題
- **Commit ID**: `8badf8e20a6d32c455f64bfef376d38f31fcdc33`
- **作者**: 邱郁晏
- **日期**: 2023-12-13 13:59:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 320. [Web] Q00-20231213002 不同模组下作业名称相同，导航页显示异常
- **Commit ID**: `194545f4c3ef02929a914c214552aabc6cfb88a9`
- **作者**: liuyun
- **日期**: 2023-12-13 13:22:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 321. [BPM APP]S00-20231023001 優化行動端Grid元件在編輯狀態下增加新增按鈕，點擊可快速新增新的一筆單身
- **Commit ID**: `3bec1106e2498799e39387e8991c2e207da00a0b`
- **作者**: cherryliao
- **日期**: 2023-12-13 10:18:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 322. [PRODT]Q00-20231212002 修正Web流程管理工具中流程徹銷中的sessionBean點編輯呈現空白的問題
- **Commit ID**: `16e9c330c348c3e343cff0f146854e789eb05b40`
- **作者**: pinchi_lin
- **日期**: 2023-12-12 15:11:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 323. [組織同步] Q00-20231212001 調整HR同步職務、職稱、角色項目時，自動刪除多餘空白(補修正)
- **Commit ID**: `682b516a15e55b9efc5ad9183409d465dc26db1b`
- **作者**: 邱郁晏
- **日期**: 2023-12-12 11:58:36
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/FunctionDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/RoleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/TitleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 324. [組織同步] Q00-20231212001 調整HR同步職務、職稱、角色項目時，自動刪除多餘空白
- **Commit ID**: `9b54c0381868b6a5c6adcf8f20e4b33f32aba5e9`
- **作者**: 邱郁晏
- **日期**: 2023-12-12 11:46:58
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/FunctionDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/RoleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/TitleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/NaNaTableUtilV2_0.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/db/SyncTableUtilV1_2.java`

### 325. [附件擴充] 在線閱覽新增橋接機制(補)
- **Commit ID**: `b318579a61de801775545c55c111cd78034adfb1`
- **作者**: 邱郁晏
- **日期**: 2023-12-11 14:24:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 326. [附件擴充] 在線閱覽新增橋接機制
- **Commit ID**: `92a4c24a7bf9698c63eb16538288ff9b5ba4f036`
- **作者**: 邱郁晏
- **日期**: 2023-12-08 17:25:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 327. [ISO] S00-20231026002 新增鼎新轉檔工具上傳PNG功能
- **Commit ID**: `b60cdf458436cc83ad12f91f3681382902a68556`
- **作者**: 邱郁晏
- **日期**: 2023-12-08 15:36:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 328. [流程引擎]S00-20230602004 Restful转存表单Web Server调整为抓内网地址
- **Commit ID**: `bf67c5154f0307c1948fde4f00d9302ca30c28ed`
- **作者**: 周权
- **日期**: 2023-12-08 14:45:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java`

### 329. [BPM APP]Q00-20231207003 調整同步E10表單時生成行動端FormScript增加引入E10Form檔案與呼叫initForm方法防止日期未顯示
- **Commit ID**: `cf90d6102279a5a6d81a5d4c67a0c4da82b0a17b`
- **作者**: yamiyeh10
- **日期**: 2023-12-07 12:07:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/FormDefinitionJSONTransfer.java`

### 330. [Web] Q00-20231207002 excel汇入资料到Grid中，单身加总不计算
- **Commit ID**: `5d4dbcff5a59001a737dd1190838a5c0b2a45ecb`
- **作者**: liuyun
- **日期**: 2023-12-07 10:32:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 331. [PRODT]Q00-20231206002 修正Web流程管理工具中流程樣板的流程仍可移動到其他流程分類下的問題
- **Commit ID**: `959ebca428d0593bf9c06833a2d7387624f58a1f`
- **作者**: pinchi_lin
- **日期**: 2023-12-06 18:01:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java`

### 332. [PRODT]Q00-20231206001 修正Web流程管理工具中流程樣板的流程簽入後會跑到其他流程分類下的問題
- **Commit ID**: `5c7593694eacb3a884168e63e96a5fa3b43389ef`
- **作者**: pinchi_lin
- **日期**: 2023-12-06 17:56:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 333. [web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正[补修正]
- **Commit ID**: `7d518b6ae01d854a6ce205bd41d440048aa030ce`
- **作者**: 刘旭
- **日期**: 2023-12-06 13:18:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 334. [雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證[補修正]
- **Commit ID**: `1a59bffbae825b41613135d1f11d74281de07066`
- **作者**: 林致帆
- **日期**: 2023-12-06 11:13:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 335. [PRODT]Q00-20231205006 修正Web流程管理工具中流程圖有缺少連接線時仍可儲存的問題
- **Commit ID**: `5562bd56cbc561028c518a0ae68dfd5e1600fd44`
- **作者**: pinchi_lin
- **日期**: 2023-12-05 18:52:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 336. [附件擴充] 修正參數未開啟時判斷邏輯異常問題
- **Commit ID**: `16d5fa847e3d6e505c2156e94f7c6812ed04cefe`
- **作者**: 邱郁晏
- **日期**: 2023-12-05 17:46:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 337. [Web]Q00-20231205005 修正退回重瓣信件主旨不應該是通知事項而是待辦事項
- **Commit ID**: `a3e5e24c3987806184084bc8aa0871fc3faf28c5`
- **作者**: 林致帆
- **日期**: 2023-12-05 15:26:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 338. [附件擴充] 修正參數未開啟時判斷邏輯異常問題
- **Commit ID**: `ad1129adbdd25c2094c364c8245e10539ad11a52`
- **作者**: 邱郁晏
- **日期**: 2023-12-05 15:01:32
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/FormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`

### 339. [Web]Q00-20231205004 修正待办事项中选择锁定工具列后缩小视窗表单会被部分遮挡的问题
- **Commit ID**: `9fb8c6d783cad2824a7f881e28a831c806e96df7`
- **作者**: 周权
- **日期**: 2023-12-05 14:47:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 340. [web] Q00-20231205003 使用者自定義客製開窗，連線DB是INFORMIX，下查詢條件出現對資料庫查詢SQL指令失敗問題修正
- **Commit ID**: `f264de2a333280e72745444ed799d6c53508cde3`
- **作者**: 刘旭
- **日期**: 2023-12-05 13:49:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 341. [流程引擎] Q00-20231204004 BPM系統會寄逾時通知信給離職的人問題修正[补修正]
- **Commit ID**: `13d2491bf4435513324b9d069fc21857ac60f9da`
- **作者**: 刘旭
- **日期**: 2023-12-05 10:37:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 342. [PRODT]Q00-20231204006 修正Web流程管理工具中應用程式管理員的網頁應用程式或session bean新增呼叫參數時自訂id儲存後會變預設值的問題
- **Commit ID**: `83cfc5eafbd2847fc54c9867d879b990805e8086`
- **作者**: pinchi_lin
- **日期**: 2023-12-04 18:52:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageCategoryManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/IDGen.java`

### 343. [Web] V00-20231204001 修正工作轉派使用者，原處理者無法查閱已轉派的工作清單問題
- **Commit ID**: `9e29a5b36fe80d9dbf8adcf385fc05281d83cefb`
- **作者**: 邱郁晏
- **日期**: 2023-12-04 18:08:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 344. [Web] Q00-20231204005 修正BPM授權數不足時，寄件人並非系統管理中的設定
- **Commit ID**: `e0f829e83d0d65fb4b578e18eab72e497fe28e77`
- **作者**: 邱郁晏
- **日期**: 2023-12-04 17:13:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 345. [流程引擎] Q00-20231204004 BPM系統會寄逾時通知信給離職的人問題修正
- **Commit ID**: `5feb844f5aef3b83968e38e4a7980657aa20c1e0`
- **作者**: 刘旭
- **日期**: 2023-12-04 16:31:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 346. [Web]Q00-20231204003 修正流程主旨、列印模式下grid资料有&#加任意數字，被轉成特殊符號的问题
- **Commit ID**: `a2da6441b01eecd0d357d545800caf37de2247fd`
- **作者**: 周权
- **日期**: 2023-12-04 15:48:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/StringUtil.js`

### 347. [流程引擎] Q00-20231204002 修正退回重辦系統通知變數未被正常置換問題
- **Commit ID**: `eb6d2cb9f448643adab825de3ad590019bcf2d79`
- **作者**: 邱郁晏
- **日期**: 2023-12-04 14:10:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 348. [Web]Q00-20231204001 发起流程时在删除草稿文件新增防呆
- **Commit ID**: `4eb6013a88f28e3d2301d6d9e218fca7467096c1`
- **作者**: 周权
- **日期**: 2023-12-04 10:12:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 349. [Web]Q00-20231201004 调整ipad Safari浏览器經常選取人員为默认全选
- **Commit ID**: `d13db570a921c47c770eb45777dde716619f2149`
- **作者**: 周权
- **日期**: 2023-12-01 10:59:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`

### 350. [流程引擎] Q00-20230727001 在后端卡控保存密码是否符合规则，密码只能为数字、字母和特殊符号(!@#$%^&*.()/=-+) [补]
- **Commit ID**: `a8c5b0f6ce4ceb2eb2b5533f4a0a3941be2f7d86`
- **作者**: liuyun
- **日期**: 2023-11-30 13:51:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.3_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.3_DML_Oracle.sql`

### 351. [Web] S00-20230824001 在地化-納入模組[补修正]
- **Commit ID**: `b4bb7d7cf2756308d12a515269bb877452f6de48`
- **作者**: 刘旭
- **日期**: 2023-11-30 11:47:50
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `Release/db/update/5.8.9.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.4_DML_Oracle.sql`

### 352. [Web] S00-20230824001 在地化-納入模組
- **Commit ID**: `dcf0b2d97da89206bfbf03ad769337f5d0331d98`
- **作者**: 刘旭
- **日期**: 2023-11-30 10:09:10
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `Release/db/update/5.8.9.4_DML_DM8.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.4_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.4_DML_Oracle.sql`

### 353. [Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题[补修正]
- **Commit ID**: `c4ae46a7e97e7ec5c767060d3f608355fb813b71`
- **作者**: 周权
- **日期**: 2023-11-29 16:55:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 354. [Web] Q00-20231129005 修正serialNumber栏位显示问题
- **Commit ID**: `49e37b11ba7ed748720db1c0cafb810de4c74fbd`
- **作者**: liuyun
- **日期**: 2023-11-29 16:44:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 355. [Web] Q00-20231129004 修正從追蹤連結進入已關注的愛心不會亮，該流程原本已被關注，但從追蹤連結進入後不會亮，須等切換到表單葉面後才會亮
- **Commit ID**: `7d4430368c91996608c3053fb3f34797e3372f06`
- **作者**: 刘旭
- **日期**: 2023-11-29 15:53:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 356. [流程引擎] S00-20230601003 增加系统参数设定流程解析遇到离职人员是否产生待办(默认不产生)
- **Commit ID**: `6d2f6437be8958164b1e8fca2c738584fc2f0e4c`
- **作者**: liuyun
- **日期**: 2023-11-29 14:39:15
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DML_DM8.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.10.1_DML_Oracle.sql`

### 357. [Web]Q00-20231128006 调整grid標頭固定显示[补修正]
- **Commit ID**: `7ec2823ac24ae4f3951c848d7fb119db172187cd`
- **作者**: 周权
- **日期**: 2023-11-29 14:16:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 358. [Web]Q00-20231129002 调整個人資訊页多个提示訊息显示不完整的问题
- **Commit ID**: `a148b9b9e20f2ff5313e2824d72d34c6498cd834`
- **作者**: 周权
- **日期**: 2023-11-29 12:17:02
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageSimpleUserProfile.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 359. [Web]Q00-20231129003 修正“使用者登入登出紀錄”使用清單顯示密度設定无效的问题
- **Commit ID**: `c017c91cb6747b88f321258f84c5cc32c8b8677c`
- **作者**: 周权
- **日期**: 2023-11-29 11:43:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 360. [Web]Q00-20231129001 調整在行動裝置撤銷流程時填寫撤銷意見會被選單擋住的問題
- **Commit ID**: `9add2c5310425a02d5689dfd0729fa6d5b6cb04c`
- **作者**: cherryliao
- **日期**: 2023-11-29 09:48:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 361. [附件擴充] 在線閱讀新增發布檔下載接口
- **Commit ID**: `99feb094e796276ec49961c1d08078ee8eca33ab`
- **作者**: 邱郁晏
- **日期**: 2023-11-28 17:29:23
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/BPMviewer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html`

### 362. [Web]Q00-20231128006 调整grid標頭固定显示
- **Commit ID**: `0f2847e9d7072fe07e730c6e11450b6c0ba8e70b`
- **作者**: 周权
- **日期**: 2023-11-28 16:17:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 363. [BPM APP]S00-20230626002 行動端新增在退回重辦時可以將簽核意見帶入到退回意見上顯示
- **Commit ID**: `424c86fd2119d412ae850a4b8ec9961cd7620625`
- **作者**: yamiyeh10
- **日期**: 2023-11-28 14:13:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 364. [ESS]Q00-20231128003 調整缺席紀錄方法相容帶有單身資料的ESS單據
- **Commit ID**: `be840377e1983125a2f0f3874331340332ad19b1`
- **作者**: 林致帆
- **日期**: 2023-11-28 13:35:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`

### 365. [PRODT]Q00-20231128001 修正在開啟流程管理工具後主畫面的標題一併被更動問題
- **Commit ID**: `ab8af78b695871bba2cefc3d7ea7f2998c99369b`
- **作者**: cherryliao
- **日期**: 2023-11-28 10:02:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 366. [BPM APP]Q00-20231123002 修正設計顯示流程簽核意見為SHOW_NOT_EMPTY且無人填寫意見時開啟行動版表單畫面會發生undefined問題
- **Commit ID**: `2b009a113789b68d3960ae372f3146eff55a3739`
- **作者**: yamiyeh10
- **日期**: 2023-11-27 16:37:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 367. [Web]Q00-20231127002 修正簡易流程圖無法顯示取回重瓣資訊
- **Commit ID**: `8977755fcc37b6b811c9aedac1cdcc1bdd102a63`
- **作者**: 林致帆
- **日期**: 2023-11-27 16:31:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 368. [流程引擎]Q00-20231127001 修正關卡設定多人都要簽核且設定自動簽核2，與前一關相同者，若前一關為核決關卡，且未實際展開核決關卡時，流程無法派送至下一關的異常
- **Commit ID**: `7bbaa05755249bbf95d42e11791f2c56dbc85d0a`
- **作者**: waynechang
- **日期**: 2023-11-27 15:20:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 369. [BPM APP]S00-20231017001 企業微信與釘釘的待辦列表新增顯示轉派資訊
- **Commit ID**: `89fb194221608c64c750219c3e20406e69db6799`
- **作者**: cherryliao
- **日期**: 2023-11-27 10:09:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp`

### 370. [SAP]Q00-20231124005 優化SAP整合服務，當整合的資料類型為Grid時，同時支持RWD表單Grid及絕對位置表單Grid
- **Commit ID**: `2c26c8ef6cf6e35dd129356ec8fb71984a314d07`
- **作者**: waynechang
- **日期**: 2023-11-24 17:33:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`

### 371. [流程引擎] Q00-20231124004 修正批次簽核造成重複寄信問題
- **Commit ID**: `12cbf17cda6125403a2926c2931d161ca30e4fa4`
- **作者**: 邱郁晏
- **日期**: 2023-11-24 15:00:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 372. [雙因素模組]Q00-20231101003 新增administrator帳號加入雙因素認證
- **Commit ID**: `8f05060b3fea7dee996e07dd10ce748b302a1971`
- **作者**: 林致帆
- **日期**: 2023-11-24 10:45:57
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/TFAModule/TFASetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 373. [Web] Q00-20231124002 修正流程主旨範本設定<#workItemName>显示N.A.
- **Commit ID**: `85323da95360497dfb7018264f29cf20c2b78b47`
- **作者**: liuyun
- **日期**: 2023-11-24 10:33:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 374. [Web]Q00-20231124003 修正Grid某一格或某一行设置样式，点击排序后样式消失的问题
- **Commit ID**: `f4308f3a39eda41f3d93ecc77bd7be7331eace0e`
- **作者**: 周权
- **日期**: 2023-11-24 10:30:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 375. [Web] Q00-20231124001 修正附件元件每個檔案容量限制設定成104857600 kb,無法上傳附件
- **Commit ID**: `5fe24fd5eb495c84c3659d6bf2ba5913325a895a`
- **作者**: 刘旭
- **日期**: 2023-11-24 10:24:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/DisplayLabelUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

