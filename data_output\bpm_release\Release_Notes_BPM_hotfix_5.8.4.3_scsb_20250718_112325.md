# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.4.3_scsb
- **舊版本**: release_5.8.4.3
- **生成時間**: 2025-07-18 11:23:25
- **新增 Commit 數量**: 3

## 變更摘要

### lorenchang (3 commits)

- **2022-06-26 22:44:16**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.3
  - 變更檔案: 25 個
- **2020-11-16 17:26:45**: 修正5.8版(Oracle)在流程有設定發起權限時會出現異常
  - 變更檔案: 4 個
- **2020-11-16 17:18:48**: 修正SessionBeanApplication objectVersion異常增加的問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.4.3
- **Commit ID**: `0c277aa141a7adfea9ab98c34852c2ef5e8d92d8`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:44:16
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. 修正5.8版(Oracle)在流程有設定發起權限時會出現異常
- **Commit ID**: `8261af7e403a7f53d63b962428eb8eeb70756315`
- **作者**: lorenchang
- **日期**: 2020-11-16 17:26:45
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/model/ProcessPackageModel.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/AbsAuthority.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/PackageInvokeAuthority.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/model/ProcessPackageModel.java`

### 3. 修正SessionBeanApplication objectVersion異常增加的問題
- **Commit ID**: `a2ad168a2cab8ffc080092ed8b3028534530cabb`
- **作者**: lorenchang
- **日期**: 2020-11-16 17:18:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/app/SessionBeanApplication.java`

