# Release Notes - BPM

## 版本資訊
- **新版本**: release_5.8.6.1
- **舊版本**: release_5.8.5.2
- **生成時間**: 2025-07-18 11:42:06
- **新增 Commit 數量**: 135

## 變更摘要

### lorenchang (4 commits)

- **2022-06-26 22:15:41**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.1
  - 變更檔案: 25 個
- **2021-04-12 14:26:35**: [內部]更新越南多語系
  - 變更檔案: 3 個
- **2021-04-08 11:00:28**: 將5.8.6.1多語系Merge至develop_v58
  - 變更檔案: 2 個
- **2021-04-07 12:54:58**: [DotJ]Q00-20210407001 修正流程派送異常
  - 變更檔案: 2 個

### 王鵬程 (6 commits)

- **2021-04-16 15:52:04**: [Web]Q00-20210416002 修正點選常用流程後畫面會先閃一下回原畫面後才進入流程
  - 變更檔案: 1 個
- **2021-03-24 18:17:18**: [流程引擎]A00-20210315002 修正從HR同步時，設定檔內的部門/專案 有設不同步部門層級時，依然會同步該欄位而導致報錯
  - 變更檔案: 3 個
- **2021-03-15 17:52:36**: [流程引擎]Q00-20210315004 修正TextBox設日期/時間型態，年月日之間以dash作為分隔符在轉存表單時會錯
  - 變更檔案: 1 個
- **2021-03-10 18:10:03**: [Web]Q00-20210310001 修正表單欄位設顯示小數後幾位和跨欄位運算，且流程關卡中欄位驗證是onblur或both，使欄位顯示異常
  - 變更檔案: 1 個
- **2021-03-08 16:44:22**: [Web]Q00-20210308005 修正監控流程中進入XPDL流程，流程圖太大沒出現scrollbar，以致無法查看流程圖
  - 變更檔案: 1 個
- **2021-03-03 17:54:53**: [Web]A00-20210225001 修正工作通知頁面用流程發起人來搜尋，會搜尋不到資料
  - 變更檔案: 1 個

### walter_wu (14 commits)

- **2021-04-16 11:15:25**: [簽核流程設計師]Q00-20210416001 修正在簽出ProcessPackage時會誤取到CustomProcessPackage
  - 變更檔案: 1 個
- **2021-04-13 17:54:51**: [內部]Q00-20210413001 用UserId與LDAPId取得User接口Log優化
  - 變更檔案: 1 個
- **2021-03-31 15:12:56**: [補修正]S00-20210323001 整理Log
  - 變更檔案: 2 個
- **2021-03-30 17:54:08**: S00-20210323001 帳號鎖定功能
  - 變更檔案: 17 個
- **2021-03-25 11:39:37**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-03-25 11:38:46**: [內部]Q00-20210325001 修正每個User進入首頁console就會印出四行SQL造成系統負擔
  - 變更檔案: 1 個
- **2021-03-24 11:31:03**: [Web]Q00-20210324001 修正Chrome,Edge列印絕對位置表單時，因為表格標頭跨頁重複顯示導至與其他元件重疊
  - 變更檔案: 1 個
- **2021-03-23 11:59:12**: [Web]A00-20210101001 修正列印會出現右邊邊界留白過大的狀況(圖一)，該情況在不同瀏覽器均如此
  - 變更檔案: 1 個
- **2021-03-17 17:30:45**: [AD登入]C01-20210315009 修正使用者同時有兩個以上AD，密碼又不一樣會因為直接拋密碼錯誤，導至密碼正確的AD驗證不到
  - 變更檔案: 1 個
- **2021-03-16 16:48:28**: [Web]C01-20210312001 修正在"非"發起人關卡上傳附件(勾選在線閱讀)，點選上傳的附件會出現閱讀檔未準備好
  - 變更檔案: 1 個
- **2021-03-16 16:20:16**: [Web]Q00-20210316003 修正附件元件設定FullControl 上傳檔案之後點擊下載按鈕出現請洽系統管理員
  - 變更檔案: 1 個
- **2021-03-16 16:14:06**: [Web]Q00-20210316002 修正在線閱覽的檔案點擊沒有反應
  - 變更檔案: 1 個
- **2021-03-16 16:01:43**: [Web]Q00-20210316001 修正關卡設定必須上傳附件，沒有上傳卻能通過
  - 變更檔案: 2 個
- **2021-03-10 15:50:33**: [流程引擎]C01-20210305004 修正5621以下OracleDB客戶版更後，流程派送報錯
  - 變更檔案: 2 個

### waynechang (5 commits)

- **2021-04-15 14:59:05**: [內部]更新5.8.6.1 patch檔
  - 變更檔案: 1 個
- **2021-04-01 15:01:55**: [Web]S00-20210326002 表單删除附件時，增加提示「確認是否刪除附件」
  - 變更檔案: 2 個
- **2021-03-26 16:53:15**: [Web]Q00-20210326002 修正BPM 5.8.5.2版本在ISO新增單、變更單調整「檔案安全性」時，無法顯示html按鈕元件
  - 變更檔案: 1 個
- **2021-03-15 15:54:51**: [Web]Q00-20210315001 維護查詢樣版js新增「重載頁面」功能
  - 變更檔案: 1 個
- **2021-03-11 16:36:46**: [Web]S00-*********** 新增簡易流程圖支援核決關卡預先解析處理人員
  - 變更檔案: 4 個

### yamiyeh10 (11 commits)

- **2021-04-15 11:13:12**: [內部]Q00-20210414001 行動端於小米手機操作詳情表單發送通知功能在選擇人員的頁面往上滑動功能異常
  - 變更檔案: 2 個
- **2021-04-14 16:04:01**: [內部]Q00-20210414002 整合鼎捷雲且沒BPMAPP序號時動態表單同步失敗
  - 變更檔案: 1 個
- **2021-04-09 14:13:44**: [內部]Q00-20210409003 修正更新雲商品授權日期排程在Oracle的SQL資料多了--導致版更工具異常問題
  - 變更檔案: 1 個
- **2021-03-10 18:29:37**: [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
  - 變更檔案: 7 個
- **2021-01-28 14:02:53**: [BPM APP]調整行動端詳情簽核派送時可依系統變數設定啟用檢查簽核意見是否為空功能[補]
  - 變更檔案: 2 個
- **2021-03-25 19:27:28**: [BPM APP]優化行動端發送通知、加簽、轉派、退回等功能與畫面
  - 變更檔案: 7 個
- **2021-03-19 10:10:49**: [BPM APP]Q00-20210318002 修正行動端詳情表單發送通知功能在選擇發送通知人員時有重複人員的問題
  - 變更檔案: 2 個
- **2021-03-15 17:41:17**: [E10]調整E10整合表單在行動端支援多個子單身功能與樣式
  - 變更檔案: 3 個
- **2021-03-10 17:10:40**: [ESS]新增ESS整合表單:ESSF01B員工排班(多人多天)
  - 變更檔案: 1 個
- **2021-03-04 13:36:11**: [BPM APP]新增Web表單設計師的檢視表單發行歷程添加行動端動態渲染表單同步狀態功能
  - 變更檔案: 9 個
- **2021-03-04 11:56:16**: [BPM APP]調整行動端相關功能在各平台的授權卡控機制
  - 變更檔案: 5 個

### cherryliao (12 commits)

- **2021-04-15 10:07:24**: [BPM APP] Q00-20210401003調整行動端表單textbox元件設定浮點數時的樣式
  - 變更檔案: 3 個
- **2021-04-08 12:05:42**: [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
  - 變更檔案: 3 個
- **2021-04-08 11:58:18**: [Web]線上人數查詢維護作業新增使用者登入資訊
  - 變更檔案: 13 個
- **2021-04-08 11:13:25**: [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
  - 變更檔案: 1 個
- **2021-03-23 10:50:52**: [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
  - 變更檔案: 1 個
- **2021-03-11 11:13:30**: [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
  - 變更檔案: 1 個
- **2021-03-10 16:54:57**: [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊
  - 變更檔案: 15 個
- **2021-01-07 12:01:48**: [BPM APP]C01-20210104004 調整授權中間層使用者維護作業使用者人名若為簡體會呈現問號的問題
  - 變更檔案: 2 個
- **2021-03-17 11:25:58**: [BPM APP]調整行動端詳情表單加簽功能的工作分派的方式
  - 變更檔案: 2 個
- **2021-03-16 16:01:06**: [BPM APP]C01-20210315006 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題[補]
  - 變更檔案: 1 個
- **2021-03-16 15:52:43**: [BPM APP]C01-20210315006 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題
  - 變更檔案: 1 個
- **2021-03-05 13:34:03**: [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
  - 變更檔案: 14 個

### 林致帆 (20 commits)

- **2021-04-15 08:53:56**: [流程引擎]Q00-20210415001修正Collections.sort在1.7版在資料異常的狀況下會報IllegalArgumentException
  - 變更檔案: 2 個
- **2021-04-12 10:11:45**: [TIPTOP]Q00-20210412001 調整TIPTOP預設出貨流程為SessionBean
  - 變更檔案: 7 個
- **2021-04-09 17:50:08**: [TIPTOP]Q00-20210409012修正BPM為多主機時，TIPTOP回寫結案與拋單的主機不同的狀況下，cache無法清除導致TIPTOP重新拋單會失敗
  - 變更檔案: 7 個
- **2021-03-25 14:48:53**: [ESS]Q00-20210325002 修正開啟ESS查詢作業模組，ESS頁面顯示'未將對象引用設置到對象的實例'
  - 變更檔案: 1 個
- **2021-03-22 17:47:42**: [SSC]新增SSC整合程式[補]
  - 變更檔案: 1 個
- **2021-03-12 17:40:02**: [SSC]新增SSC整合程式
  - 變更檔案: 16 個
- **2021-01-27 16:36:36**: [E10]S00-20210105002 E10發單，採用設定檔選擇是否由主部門發起
  - 變更檔案: 5 個
- **2021-04-07 18:09:19**: [流程引擎]Q00-20210407003 修正發起關卡設定"允許通知信夾帶附件"下一關為核決關卡時，發起關卡上傳附件派送到下一關會報錯
  - 變更檔案: 1 個
- **2021-04-06 11:35:53**: [TIPTOP]Q00-20210406001 修正TIPTOP拋單緊急度設定緊急，流程實例的緊急度為一般
  - 變更檔案: 1 個
- **2021-03-26 14:22:35**: [內部]Q00-20210326001 增加E10回寫任務的log訊息
  - 變更檔案: 1 個
- **2021-03-17 16:05:31**: [T100]結案回寫給T100，T100回饋錯誤訊息，通知系統管理員的Mail會顯示T100回饋的訊息
  - 變更檔案: 2 個
- **2021-03-12 14:18:32**: [Web]A00-20210311001 修正 Safrai在追蹤，待辦，監控流程及發起流程下載附件須把下載視窗關閉，才能再下載附件
  - 變更檔案: 3 個
- **2021-03-11 16:58:32**: [E10]Q00-20210311005 修正E10取得簽核歷程排程，在執行第一筆E10單據報錯後，後面的E10單據就無法往下執行
  - 變更檔案: 4 個
- **2021-03-11 14:35:19**: [Web]Q00-*********** 修正Chrome升級到89.0.4389.82版，在追蹤，待辦，監控流程及發起流程，無法下載附件
  - 變更檔案: 3 個
- **2021-03-09 11:30:16**: [Web]Q00-20210309001 修正若資料庫為Oracle DB，追蹤流程點擊授權的流程使用「流程名稱」進行排序會報錯[補修正]
  - 變更檔案: 1 個
- **2021-03-09 11:25:09**: [Web]Q00-20210309001 修正若資料庫為Oracle DB，追蹤流程點擊授權的流程使用「流程名稱」進行排序會報錯
  - 變更檔案: 1 個
- **2021-03-08 11:23:23**: [E10]Q00-20210308001 修正E10流程畫面子單身按鈕變成html字串
  - 變更檔案: 2 個
- **2021-03-04 11:22:41**: [E10]A00-20210304001 修正E10單據在沒有附件的狀況，到取得附件關卡，無法派送到下個關卡
  - 變更檔案: 1 個
- **2021-03-02 11:55:04**: [Web]Q00-20210302001 修正流程有迴圈設計，在迴圈的指定關卡為服務任務，造成簡易流程圖檢視異常
  - 變更檔案: 1 個
- **2021-02-26 18:25:53**: [Web]修正 Chrome在追蹤，待辦，監控流程及發起流程下載附件須把下載視窗關閉，才能再下載附件
  - 變更檔案: 3 個

### pinchi_lin (23 commits)

- **2021-04-14 17:34:06**: [BPM APP]Q00-20210414003 修正整合IMG在推播時判斷授權的邏輯異常導致無法推播問題
  - 變更檔案: 2 個
- **2021-04-12 18:41:32**: [BPM APP]Q00-20210409008 修正整合地端IMG時動態渲染表單中的圖片元件顯示不出來問題
  - 變更檔案: 2 個
- **2021-04-12 18:10:26**: [BPM APP]Q00-20210409013 修正安卓手機在產品開窗滾動選擇人員時無法載入下一頁人員
  - 變更檔案: 1 個
- **2021-04-12 18:06:27**: [BPM APP]Q00-20210409001 修正安卓手機在發送通知、加簽等功能滾動選擇人員時無法載入下一頁人員
  - 變更檔案: 2 個
- **2021-04-09 21:06:53**: [BPM APP]Q00-20210409002 修正未進入菜單前先點企業微信推播進入表單畫面會空白問題
  - 變更檔案: 1 個
- **2021-04-09 20:13:26**: [內部]Q00-20210409009 修正移動各平台推播邏輯異常問題
  - 變更檔案: 7 個
- **2021-04-09 18:28:50**: [內部]Q00-20210409006 修正在整合雲端IMG時動態渲染表單會同步失敗問題
  - 變更檔案: 2 個
- **2021-04-09 17:12:51**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-04-09 17:11:29**: [內部]Q00-20210408007 加入新增的cac與img網址的系統變數缺少的Oracle SQL
  - 變更檔案: 1 個
- **2021-04-08 18:37:58**: [內部]Q00-20210408002 加入動態渲染表單上附件與圖片走外網機制處理的系統變數缺少匯入SQL資料
  - 變更檔案: 2 個
- **2021-03-22 16:04:20**: [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
  - 變更檔案: 2 個
- **2021-04-07 18:17:56**: [BPM APP]Q00-20210308003 修正手寫元件唯讀時在移動端會無法顯示內容與點擊會觸發遮罩問題
  - 變更檔案: 2 個
- **2021-04-07 11:48:57**: [BPM APP]Q00-20210330001 修正IMG智能快簽列表在啟用動態渲染表單時有報錯問題
  - 變更檔案: 1 個
- **2021-03-31 14:07:29**: [內部]調整因開APP整合但沒匯動態渲染表單資料表會導致發單或簽核失敗問題
  - 變更檔案: 6 個
- **2021-03-30 18:27:18**: [流程設計工具]調整選擇流程在移動裝置使用方式時加入表單支持的提示
  - 變更檔案: 6 個
- **2021-03-26 16:16:33**: [BPM APP]調整動態渲染表單上附件資料在雲地與內外網的不同處理方式的邏輯[補]
  - 變更檔案: 2 個
- **2021-03-26 15:47:33**: [BPM APP]S00-20210318001 調整IMG追蹤流程列表的預設時間區間改系統變數控制
  - 變更檔案: 2 個
- **2021-03-25 16:36:28**: [BPM APP]調整動態渲染表單上附件資料在雲地與內外網的不同處理方式的邏輯
  - 變更檔案: 13 個
- **2021-03-25 15:20:46**: [BPM APP]調整動態渲染表單上圖片資料在雲地與內外網的不同處理方式的邏輯[補]
  - 變更檔案: 1 個
- **2021-03-25 15:13:15**: [BPM APP]調整動態渲染表單上圖片資料在雲地與內外網的不同處理方式的邏輯
  - 變更檔案: 2 個
- **2021-03-22 16:29:29**: [BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數[補]
  - 變更檔案: 1 個
- **2021-03-22 16:26:54**: [BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數[補]
  - 變更檔案: 3 個
- **2021-03-22 16:19:17**: [BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數
  - 變更檔案: 2 個

### 詩雅 (16 commits)

- **2021-04-13 10:32:39**: [BPM APP]Q00-20210409011 手寫板橫式時跑版
  - 變更檔案: 1 個
- **2021-04-12 15:13:18**: [內部]Q00-20210409010 修正線上人數查詢維護作業若只有IMG使用者在線使用，會顯示無資料
  - 變更檔案: 1 個
- **2021-04-12 13:53:40**: [BPM APP]Q00-20210409005 修正IMG轉由他人選擇人員頁面搜尋框跑版問題
  - 變更檔案: 1 個
- **2021-04-12 10:16:49**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-04-12 10:15:30**: [內部]Q00-20210409004 調整腳本樣版說明與移除行動端不支持的方法，缺少匯入Oracle SQL資料
  - 變更檔案: 1 個
- **2021-04-08 12:18:42**: [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
  - 變更檔案: 5 個
- **2021-04-07 16:28:43**: [BPM APP]調整腳本樣版說明與移除行動端不支持的方法
  - 變更檔案: 1 個
- **2021-03-31 14:25:01**: [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
  - 變更檔案: 5 個
- **2021-03-26 15:36:02**: [內部]新增鼎捷雲平台URL和CAC URL的系統變數[補]
  - 變更檔案: 1 個
- **2021-03-26 09:32:52**: [內部]新增鼎捷雲平台URL和CAC URL的系統變數
  - 變更檔案: 3 個
- **2021-03-23 20:41:39**: [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
  - 變更檔案: 1 個
- **2021-04-07 16:19:25**: [E10]調整E10整合表單在行動端支援多個子單身功能與樣式[補]
  - 變更檔案: 3 個
- **2021-03-17 13:37:55**: [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
  - 變更檔案: 2 個
- **2021-03-16 14:34:54**: [BPM APP]C01-20210310003 修正企業微信在iOS手機上，滾動查看到完整附件內容，操作不易的問題[補]
  - 變更檔案: 1 個
- **2021-03-16 11:41:14**: [BPM APP]C01-20210310003 修正企業微信在iOS手機上，滾動查看到完整附件內容，操作不易的問題
  - 變更檔案: 11 個
- **2021-03-11 11:10:21**: [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
  - 變更檔案: 13 個

### yanann_chen (22 commits)

- **2021-04-09 17:11:30**: [流程引擎]A00-20210408001 修正若使用者的密碼中包含特殊字元，該使用者無法從搖旗吶喊小助手開啟BPM畫面
  - 變更檔案: 1 個
- **2021-04-07 13:46:13**: [流程引擎]Q00-20210407002 追蹤流程可呈現多張名稱相同的表單
  - 變更檔案: 1 個
- **2021-04-01 15:43:51**: [表單設計師]Q00-20210401001 絕對位置表單轉換RWD表單時移除表單內多餘的元件，避免轉換時發生異常導致無法開啟表單
  - 變更檔案: 1 個
- **2021-03-29 17:23:11**: [Web]Q00-20210329001 解決列印關卡無法列印表單Title元件Logo圖片的問題
  - 變更檔案: 2 個
- **2021-03-24 15:39:56**: [流程引擎]Q00-20210324002 調整流程關卡自動簽核「2.與前一關同簽核者，則跳過」邏輯
  - 變更檔案: 1 個
- **2021-03-23 16:55:07**: [流程引擎]Q00-20210323001 使用者取回工作重辦清單過濾通知關卡，讓使用者無法取回通知
  - 變更檔案: 1 個
- **2021-03-19 17:01:14**: [流程引擎]Q00-20210319004 加簽時將表單設定為唯讀，原本權限為完全控制的附件元件也會在加簽關卡中被調整成唯讀
  - 變更檔案: 1 個
- **2021-03-19 14:44:24**: [Web]Q00-20210319003 退回重辦頁面加上簽核意見的片語按鈕
  - 變更檔案: 1 個
- **2021-03-19 11:05:53**: [流程引擎]Q00-20210319001 工作歷程依流程關卡設定限制發送工作通知
  - 變更檔案: 3 個
- **2021-03-18 17:43:26**: [Web]Q00-20210318003 調整BPM登入頁面，提高系統安全性
  - 變更檔案: 4 個
- **2021-03-17 16:18:42**: [Web]A00-20210317002 修正進入多個處理者僅需一人處理的待辦事項，若使用者未接收工作就返回待辦清單，畫面卡住問題
  - 變更檔案: 1 個
- **2021-03-15 16:04:36**: [流程引擎]Q00-20210315002 修正搖旗吶喊小助手顯示的待辦事項數量與BPM待辦事項清單上的數量不符
  - 變更檔案: 1 個
- **2021-03-12 18:29:19**: [流程引擎]Q00-*********** 單身繫結元件為記錄Radio元件實際值的隱藏欄位，且單身欄位代號與元件代號相同，儲存表單失敗[補]
  - 變更檔案: 1 個
- **2021-03-12 17:32:50**: [流程引擎]A00-20210311002 修正若進行中的關卡有多個處理者，撤銷流程清單上資料的實際數量與下方顯示的資料總筆數不符
  - 變更檔案: 1 個
- **2021-03-11 15:07:43**: [流程引擎]Q00-*********** 表單單身繫結元件為記錄Radio元件實際值的隱藏欄位，且單身欄位代號與元件代號相同，儲存表單發生異常
  - 變更檔案: 1 個
- **2021-03-10 11:17:42**: [流程引擎]A00-20210305001 修正若資料庫為Oracle DB，在授權的流程清單使用建立時間排序發生異常[補]
  - 變更檔案: 1 個
- **2021-03-08 16:26:34**: [內部]Q00-20210308004 調整字串(String)轉多語系(Locale)方法，加入NULL判斷避免資料轉換時發生異常
  - 變更檔案: 1 個
- **2021-03-05 18:15:43**: [流程引擎]A00-20210305001 修正若資料庫為Oracle DB，在"授權的流程"清單使用"建立時間"排序發生異常
  - 變更檔案: 1 個
- **2021-03-04 17:25:45**: [Web]Q00-20210304001 調整JavaScript浮點數運算誤差造成欄位計算結果不符預期
  - 變更檔案: 1 個
- **2021-03-04 14:53:01**: [流程引擎]A00-20210303001 修正建立workItem轉派記錄時發生NullPointerException問題
  - 變更檔案: 1 個
- **2021-03-03 11:29:42**: [Web]Q00-20210303002 修正「開發教學索引->表單元件介紹及範例->CheckBox/RadioButton」範例內容錯誤
  - 變更檔案: 1 個
- **2021-03-03 10:47:21**: [流程引擎]Q00-20210303001 修正時間已達到活動逾時通知設定的間隔時間，但活動逾時排程未發送通知信
  - 變更檔案: 1 個

### peng_cheng_wang (2 commits)

- **2021-03-16 12:03:33**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2021-03-16 11:24:16**: Revert "[流程引擎]Q00-20210315004 修正TextBox設日期/時間型態，年月日之間以dash作為分隔符在轉存表單時會錯"
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為5.8.6.1
- **Commit ID**: `47fc3b9c1fad0beea90e01c8688f4ce5a07651cb`
- **作者**: lorenchang
- **日期**: 2022-06-26 22:15:41
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [Web]Q00-20210416002 修正點選常用流程後畫面會先閃一下回原畫面後才進入流程
- **Commit ID**: `836c7c4f0a4ebe85d5dfc4b9d5bd70b1efecc3c5`
- **作者**: 王鵬程
- **日期**: 2021-04-16 15:52:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 3. [簽核流程設計師]Q00-20210416001 修正在簽出ProcessPackage時會誤取到CustomProcessPackage
- **Commit ID**: `84f50946fc5b9682f8351d53c241192588ff6e16`
- **作者**: walter_wu
- **日期**: 2021-04-16 11:15:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 4. [內部]更新5.8.6.1 patch檔
- **Commit ID**: `097ccdcbdfd3683ecfb76a2fe7667080361f1822`
- **作者**: waynechang
- **日期**: 2021-04-15 14:59:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 5. [內部]Q00-20210414001 行動端於小米手機操作詳情表單發送通知功能在選擇人員的頁面往上滑動功能異常
- **Commit ID**: `09b6570dce8e4439389021847a1536f7be66e8cf`
- **作者**: yamiyeh10
- **日期**: 2021-04-15 11:13:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 6. [BPM APP] Q00-20210401003調整行動端表單textbox元件設定浮點數時的樣式
- **Commit ID**: `17d8fc28d7e580fc9066ff40938522a35af2e8df`
- **作者**: cherryliao
- **日期**: 2021-04-15 10:07:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 7. [流程引擎]Q00-20210415001修正Collections.sort在1.7版在資料異常的狀況下會報IllegalArgumentException
- **Commit ID**: `f831f7cd3c526366626f056aebcc6e17edf85dac`
- **作者**: 林致帆
- **日期**: 2021-04-15 08:53:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/bin/standalone.conf`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/bin/standalone.conf.bat`

### 8. [BPM APP]Q00-20210414003 修正整合IMG在推播時判斷授權的邏輯異常導致無法推播問題
- **Commit ID**: `31b484c9c62f6d3c4633b26fa6daf23d0e97b1f0`
- **作者**: pinchi_lin
- **日期**: 2021-04-14 17:34:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 9. [內部]Q00-20210414002 整合鼎捷雲且沒BPMAPP序號時動態表單同步失敗
- **Commit ID**: `94280b7e692beaae03c35d9f796d04052fd65c94`
- **作者**: yamiyeh10
- **日期**: 2021-04-14 16:04:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 10. [內部]Q00-20210413001 用UserId與LDAPId取得User接口Log優化
- **Commit ID**: `e6b6eb443f4e5d8d7946d565b0928897906bf21b`
- **作者**: walter_wu
- **日期**: 2021-04-13 17:54:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 11. [BPM APP]Q00-20210409011 手寫板橫式時跑版
- **Commit ID**: `b5df8cbc26b8b66aa46491160cf70725e5d5cd70`
- **作者**: 詩雅
- **日期**: 2021-04-13 10:32:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js`

### 12. [BPM APP]Q00-20210409008 修正整合地端IMG時動態渲染表單中的圖片元件顯示不出來問題
- **Commit ID**: `82df8d03f44d70e31e6904ba461d00c87a11af0f`
- **作者**: pinchi_lin
- **日期**: 2021-04-12 18:41:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 13. [BPM APP]Q00-20210409013 修正安卓手機在產品開窗滾動選擇人員時無法載入下一頁人員
- **Commit ID**: `8d42022f939eedfaefcd2cf05f49a437390139fb`
- **作者**: pinchi_lin
- **日期**: 2021-04-12 18:10:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 14. [BPM APP]Q00-20210409001 修正安卓手機在發送通知、加簽等功能滾動選擇人員時無法載入下一頁人員
- **Commit ID**: `f3e0421fdc76db7b13c26d2a7d6a64053865098b`
- **作者**: pinchi_lin
- **日期**: 2021-04-12 18:06:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 15. [內部]Q00-20210409010 修正線上人數查詢維護作業若只有IMG使用者在線使用，會顯示無資料
- **Commit ID**: `bbd73d0afc6058b5b816a9e9c4634c772d6cd532`
- **作者**: 詩雅
- **日期**: 2021-04-12 15:13:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ConnectedUserInfoListReader.java`

### 16. [內部]更新越南多語系
- **Commit ID**: `e7f103c655fb763117661ccb9b584f425f510694`
- **作者**: lorenchang
- **日期**: 2021-04-12 14:26:35
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 17. [BPM APP]Q00-20210409005 修正IMG轉由他人選擇人員頁面搜尋框跑版問題
- **Commit ID**: `3fc8367eeba05c9a2f0aa83a495f591a73dac8c2`
- **作者**: 詩雅
- **日期**: 2021-04-12 13:53:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 18. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `84bb87f8caeb00a3f6de4e389d549df6cd7767f6`
- **作者**: 詩雅
- **日期**: 2021-04-12 10:16:49
- **變更檔案數量**: 0

### 19. [內部]Q00-20210409004 調整腳本樣版說明與移除行動端不支持的方法，缺少匯入Oracle SQL資料
- **Commit ID**: `4f53e1e72280fe5703186353c2d380c9e7fb791c`
- **作者**: 詩雅
- **日期**: 2021-04-12 10:15:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 20. [TIPTOP]Q00-20210412001 調整TIPTOP預設出貨流程為SessionBean
- **Commit ID**: `e42c36d5a221d6e2f0c623305033942f0a2916c7`
- **作者**: 林致帆
- **日期**: 2021-04-12 10:11:45
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@tiptop/process-default/bpmn/5.25/\350\253\213\350\263\274\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255.bpmn"`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_1_Check.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_MSSQL_2.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_1_Check.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.7.5.1_TIPTOP_DML_Oracle_2.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.8.2.2_TIPTOP_DML_MSSQL_1_Check.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@tiptop/update/5.8.2.2_TIPTOP_DML_Oracle_1_Check.sql`

### 21. [BPM APP]Q00-20210409002 修正未進入菜單前先點企業微信推播進入表單畫面會空白問題
- **Commit ID**: `c8fec81395eb5948845cf479ef809f6435791167`
- **作者**: pinchi_lin
- **日期**: 2021-04-09 21:06:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 22. [內部]Q00-20210409009 修正移動各平台推播邏輯異常問題
- **Commit ID**: `d934708c1a96080bc1b1b6a8d1f3f9da959cd47a`
- **作者**: pinchi_lin
- **日期**: 2021-04-09 20:13:26
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`

### 23. [內部]Q00-20210409006 修正在整合雲端IMG時動態渲染表單會同步失敗問題
- **Commit ID**: `356a1a3033104c7d31d4a649952643389dd00a7c`
- **作者**: pinchi_lin
- **日期**: 2021-04-09 18:28:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`

### 24. [TIPTOP]Q00-20210409012修正BPM為多主機時，TIPTOP回寫結案與拋單的主機不同的狀況下，cache無法清除導致TIPTOP重新拋單會失敗
- **Commit ID**: `81842b82db3ed4ca5cb327190b9d6587585025d2`
- **作者**: 林致帆
- **日期**: 2021-04-09 17:50:08
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IServerCacheManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ServerCacheManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TipTopCreatedFormInstances.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 25. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `b6fea63e969bafeffa7183954a6d35d84995b327`
- **作者**: pinchi_lin
- **日期**: 2021-04-09 17:12:51
- **變更檔案數量**: 0

### 26. [流程引擎]A00-20210408001 修正若使用者的密碼中包含特殊字元，該使用者無法從搖旗吶喊小助手開啟BPM畫面
- **Commit ID**: `55432a64761ba24d5d24a44a52facb5fb0d96e96`
- **作者**: yanann_chen
- **日期**: 2021-04-09 17:11:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ForwardIndexAction.java`

### 27. [內部]Q00-20210408007 加入新增的cac與img網址的系統變數缺少的Oracle SQL
- **Commit ID**: `8625db5ebba2c3829b12a21031c97cf8cee38283`
- **作者**: pinchi_lin
- **日期**: 2021-04-09 17:11:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 28. [內部]Q00-20210409003 修正更新雲商品授權日期排程在Oracle的SQL資料多了--導致版更工具異常問題
- **Commit ID**: `da6376e12f1fd13b2da86e1aa64fe985c1102b96`
- **作者**: yamiyeh10
- **日期**: 2021-04-09 14:13:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 29. [內部]Q00-20210408002 加入動態渲染表單上附件與圖片走外網機制處理的系統變數缺少匯入SQL資料
- **Commit ID**: `0c600b884a92a4f3d65048bf386a246f578962c1`
- **作者**: pinchi_lin
- **日期**: 2021-04-08 18:37:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 30. [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
- **Commit ID**: `1b4b9876f34395d023db0d68a1e31ffd2f12fba2`
- **作者**: 詩雅
- **日期**: 2021-04-08 12:18:42
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MobileAuthorizeUtil.java`

### 31. [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
- **Commit ID**: `12b5d35ca4bdc68ee14b6c45a40ec0a89f2727fc`
- **作者**: cherryliao
- **日期**: 2021-04-08 12:05:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformRESTTransferTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MobileAuthorizeUtil.java`

### 32. [Web]線上人數查詢維護作業新增使用者登入資訊
- **Commit ID**: `b3ea7b6fcef06fbfd8adeb7f3868e5da731713bc`
- **作者**: cherryliao
- **日期**: 2021-04-08 11:58:18
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/license/ConnectedUserInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/onlinemgt/OnlineUserMgtPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ConnectedUserInfoListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/OnlineUserAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/user_profile/ConnectedUserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DDL_Oracle_1.sql`

### 33. [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
- **Commit ID**: `16ad2cc35e7ed1de65b29ef1d80e2e1248fd6f3b`
- **作者**: cherryliao
- **日期**: 2021-04-08 11:13:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java`

### 34. 將5.8.6.1多語系Merge至develop_v58
- **Commit ID**: `39cca690f6bee2e19be6902de19d4647c8207600`
- **作者**: lorenchang
- **日期**: 2021-04-08 11:00:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5861.xlsx`

### 35. [BPM APP]調整腳本樣版說明與移除行動端不支持的方法
- **Commit ID**: `a18513599b6cd320e5aacfcf3fcf62f4c0f89bfa`
- **作者**: 詩雅
- **日期**: 2021-04-07 16:28:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`

### 36. [補修正]S00-20210323001 整理Log
- **Commit ID**: `1373cf4013960473e62ddbafc540787bb998fba5`
- **作者**: walter_wu
- **日期**: 2021-03-31 15:12:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 37. [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
- **Commit ID**: `b077cc1b854117b3ac219739ba7329701eb04574`
- **作者**: 詩雅
- **日期**: 2021-03-31 14:25:01
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5861.xlsx`

### 38. S00-20210323001 帳號鎖定功能
- **Commit ID**: `a8bb63b2a88184dd21e637a43d00c7ba787fe2e1`
- **作者**: walter_wu
- **日期**: 2021-03-30 17:54:08
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/exception/UserLockOutException.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 39. [內部]新增鼎捷雲平台URL和CAC URL的系統變數[補]
- **Commit ID**: `a90609613d436aa23f8d873f452ed81c7270d80d`
- **作者**: 詩雅
- **日期**: 2021-03-26 15:36:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`

### 40. [內部]新增鼎捷雲平台URL和CAC URL的系統變數
- **Commit ID**: `a12f7c24727dda75812e387e78541d4ec534c867`
- **作者**: 詩雅
- **日期**: 2021-03-26 09:32:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`

### 41. [ESS]Q00-20210325002 修正開啟ESS查詢作業模組，ESS頁面顯示'未將對象引用設置到對象的實例'
- **Commit ID**: `7fda6f21eb797758d5d70373f8080853c6362155`
- **作者**: 林致帆
- **日期**: 2021-03-25 14:48:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 42. [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
- **Commit ID**: `35fbbd70ca795656759848593f3e0aab38845617`
- **作者**: 詩雅
- **日期**: 2021-03-23 20:41:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 43. [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
- **Commit ID**: `1de9dd386fdb2e4dafc92fe0850f5715e3e36383`
- **作者**: cherryliao
- **日期**: 2021-03-23 10:50:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 44. [SSC]新增SSC整合程式[補]
- **Commit ID**: `b266ff2bb492ecb674cfa01b754a1324271843c4`
- **作者**: 林致帆
- **日期**: 2021-03-22 17:47:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 45. [BPM APP]調整IMG取各列表接口在判斷是否一併撈動態渲染表單的條件改抓系統變數中的值來給定[補]
- **Commit ID**: `f9583397501cbf68fe6477344ad01b6b95b4d96a`
- **作者**: pinchi_lin
- **日期**: 2021-03-22 16:04:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 46. [SSC]新增SSC整合程式
- **Commit ID**: `5e6f26301e0427ce2c77765fa9d3be2e19e90649`
- **作者**: 林致帆
- **日期**: 2021-03-12 17:40:02
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageConditionsReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/PackageParameterReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/WorkListParameterRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/ProcessV2.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PortletEntry.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 47. [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
- **Commit ID**: `080265e5c689795977eac521ae8ddba6e1c8559f`
- **作者**: cherryliao
- **日期**: 2021-03-11 11:13:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`

### 48. [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊[補]
- **Commit ID**: `d1f3179fe8d01ea33382fc95d5fe35a4aa820a3f`
- **作者**: yamiyeh10
- **日期**: 2021-03-10 18:29:37
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 49. [BPM APP]調整行動簽核管理中心整合雲方案時新增顯示鼎捷雲應用資訊
- **Commit ID**: `5c5baacc98379480129fe200e690be6d149b8070`
- **作者**: cherryliao
- **日期**: 2021-03-10 16:54:57
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MobileManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/MobileOAuthConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthConfigDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformRESTTransferTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileDataSourceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileUerManageComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.6.1_mobile_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.6.1_mobile_DDL_Oracle_1.sql`

### 50. [BPM APP]調整行動端詳情簽核派送時可依系統變數設定啟用檢查簽核意見是否為空功能[補]
- **Commit ID**: `eff48fc4644ddef17ecc7c33dcc8a818e908b157`
- **作者**: yamiyeh10
- **日期**: 2021-01-28 14:02:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 51. [E10]S00-20210105002 E10發單，採用設定檔選擇是否由主部門發起
- **Commit ID**: `10f7c63cfc0b16f78c9079b352ae110ccab3823e`
- **作者**: 林致帆
- **日期**: 2021-01-27 16:36:36
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.6.1_DML_Oracle_1.sql`

### 52. [BPM APP]C01-20210104004 調整授權中間層使用者維護作業使用者人名若為簡體會呈現問號的問題
- **Commit ID**: `d0b55b5a8a041bc4ab820a5b0bfd1a7fafc40e46`
- **作者**: cherryliao
- **日期**: 2021-01-07 12:01:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.6.1_mobile_DDL_MSSQL_1.sql`

### 53. [BPM APP]Q00-20210308003 修正手寫元件唯讀時在移動端會無法顯示內容與點擊會觸發遮罩問題
- **Commit ID**: `777a6972522e4488e28d3d89bf1f195f9a1f760e`
- **作者**: pinchi_lin
- **日期**: 2021-04-07 18:17:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 54. [流程引擎]Q00-20210407003 修正發起關卡設定"允許通知信夾帶附件"下一關為核決關卡時，發起關卡上傳附件派送到下一關會報錯
- **Commit ID**: `92e9e513e11de7d52242216ee26165d09bc19f41`
- **作者**: 林致帆
- **日期**: 2021-04-07 18:09:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 55. [E10]調整E10整合表單在行動端支援多個子單身功能與樣式[補]
- **Commit ID**: `041a0a062b7ab71b7f3a0374bf98d7c51de1a850`
- **作者**: 詩雅
- **日期**: 2021-04-07 16:19:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`

### 56. [流程引擎]Q00-20210407002 追蹤流程可呈現多張名稱相同的表單
- **Commit ID**: `ac4ae1aee9e8a5ae99699fa97acbcbdb4dba1205`
- **作者**: yanann_chen
- **日期**: 2021-04-07 13:46:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 57. [DotJ]Q00-20210407001 修正流程派送異常
- **Commit ID**: `11cba4c3fd3e031e9927fa5d08b23b52568bcc73`
- **作者**: lorenchang
- **日期**: 2021-04-07 12:54:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/DotJIntegration.java`

### 58. [BPM APP]Q00-20210330001 修正IMG智能快簽列表在啟用動態渲染表單時有報錯問題
- **Commit ID**: `f9b65b95f65a8e1bf39d6fe2df88a247e4119847`
- **作者**: pinchi_lin
- **日期**: 2021-04-07 11:48:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 59. [TIPTOP]Q00-20210406001 修正TIPTOP拋單緊急度設定緊急，流程實例的緊急度為一般
- **Commit ID**: `340318fd31a9354d8dcbebe184fc50f9e9cfb4a0`
- **作者**: 林致帆
- **日期**: 2021-04-06 11:35:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 60. [表單設計師]Q00-20210401001 絕對位置表單轉換RWD表單時移除表單內多餘的元件，避免轉換時發生異常導致無法開啟表單
- **Commit ID**: `f1a96d50ad735624944f54d8c784ca63163b1bd5`
- **作者**: yanann_chen
- **日期**: 2021-04-01 15:43:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 61. [Web]S00-20210326002 表單删除附件時，增加提示「確認是否刪除附件」
- **Commit ID**: `d9bdda92d8d628bb734c14bc5207459f12791ede`
- **作者**: waynechang
- **日期**: 2021-04-01 15:01:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 62. [內部]調整因開APP整合但沒匯動態渲染表單資料表會導致發單或簽核失敗問題
- **Commit ID**: `a9a676f8ea577324b4d13629abf1a99219a5a32b`
- **作者**: pinchi_lin
- **日期**: 2021-03-31 14:07:29
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`

### 63. [流程設計工具]調整選擇流程在移動裝置使用方式時加入表單支持的提示
- **Commit ID**: `c897082e0047fa888e05c19e868ab3ce5c25c119`
- **作者**: pinchi_lin
- **日期**: 2021-03-30 18:27:18
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/MobilityOperationCellEditorRenderer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_zh_TW.properties`

### 64. [Web]Q00-20210329001 解決列印關卡無法列印表單Title元件Logo圖片的問題
- **Commit ID**: `494aec8442e926f0212a17f3efdfa20ebf3e36d4`
- **作者**: yanann_chen
- **日期**: 2021-03-29 17:23:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 65. [Web]Q00-20210326002 修正BPM 5.8.5.2版本在ISO新增單、變更單調整「檔案安全性」時，無法顯示html按鈕元件
- **Commit ID**: `a91feb15de2a2927324e0c152013e360115ed0c0`
- **作者**: waynechang
- **日期**: 2021-03-26 16:53:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 66. [BPM APP]調整動態渲染表單上附件資料在雲地與內外網的不同處理方式的邏輯[補]
- **Commit ID**: `b8738286b689efe84802765e2e80b555fe24bcdb`
- **作者**: pinchi_lin
- **日期**: 2021-03-26 16:16:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 67. [BPM APP]S00-20210318001 調整IMG追蹤流程列表的預設時間區間改系統變數控制
- **Commit ID**: `f9069f620aed5f6c97665b7c5cd261b28068b582`
- **作者**: pinchi_lin
- **日期**: 2021-03-26 15:47:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java`

### 68. [內部]Q00-20210326001 增加E10回寫任務的log訊息
- **Commit ID**: `7ec49f9b7c8b2cd3b14e1968e7edd54400354f98`
- **作者**: 林致帆
- **日期**: 2021-03-26 14:22:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 69. [BPM APP]優化行動端發送通知、加簽、轉派、退回等功能與畫面
- **Commit ID**: `ecbae91148e8d89ef51714c753677a50d33085c7`
- **作者**: yamiyeh10
- **日期**: 2021-03-25 19:27:28
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 70. [BPM APP]調整動態渲染表單上附件資料在雲地與內外網的不同處理方式的邏輯
- **Commit ID**: `d5dd6c9dcfcb25861bcab2cc6ed63a0ce7c7f50b`
- **作者**: pinchi_lin
- **日期**: 2021-03-25 16:36:28
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/DataListContent.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageDinwhaleTransAttachmentRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageParameterTransAttachmentRes.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/PackageStdDataTransAttachmentRes.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV3.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 71. [BPM APP]調整動態渲染表單上圖片資料在雲地與內外網的不同處理方式的邏輯[補]
- **Commit ID**: `a877b2b2e38033ea298cc70c8c01d6a3dfd549cb`
- **作者**: pinchi_lin
- **日期**: 2021-03-25 15:20:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 72. [BPM APP]調整動態渲染表單上圖片資料在雲地與內外網的不同處理方式的邏輯
- **Commit ID**: `9782161074997038185e7dd3a51266787eb82245`
- **作者**: pinchi_lin
- **日期**: 2021-03-25 15:13:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleDetailForm.java`

### 73. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `c90dc8ee85b25d1182251d8b0f5a90b983115185`
- **作者**: walter_wu
- **日期**: 2021-03-25 11:39:37
- **變更檔案數量**: 0

### 74. [內部]Q00-20210325001 修正每個User進入首頁console就會印出四行SQL造成系統負擔
- **Commit ID**: `de21c8691b094928e996a2ac2bfb03833b695928`
- **作者**: walter_wu
- **日期**: 2021-03-25 11:38:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`

### 75. [流程引擎]A00-20210315002 修正從HR同步時，設定檔內的部門/專案 有設不同步部門層級時，依然會同步該欄位而導致報錯
- **Commit ID**: `b71c24f6f8b24043d6b7d5047bc074615aae650a`
- **作者**: 王鵬程
- **日期**: 2021-03-24 18:17:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-importer/src/com/dsc/nana/user_interface/apps/org_importer/util/OrgXMLGenerator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/OrgXMLGenerator.java`

### 76. [流程引擎]Q00-20210324002 調整流程關卡自動簽核「2.與前一關同簽核者，則跳過」邏輯
- **Commit ID**: `6389b635266dd82cac6bed57a2ecc49c0e7d85fe`
- **作者**: yanann_chen
- **日期**: 2021-03-24 15:39:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 77. [Web]Q00-20210324001 修正Chrome,Edge列印絕對位置表單時，因為表格標頭跨頁重複顯示導至與其他元件重疊
- **Commit ID**: `890726931563e1b48422a2b76edccbf60809dcdb`
- **作者**: walter_wu
- **日期**: 2021-03-24 11:31:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 78. [流程引擎]Q00-20210323001 使用者取回工作重辦清單過濾通知關卡，讓使用者無法取回通知
- **Commit ID**: `d34eb28e1d6f41fc620be992b18ca4641428a3ba`
- **作者**: yanann_chen
- **日期**: 2021-03-23 16:55:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 79. [Web]A00-20210101001 修正列印會出現右邊邊界留白過大的狀況(圖一)，該情況在不同瀏覽器均如此
- **Commit ID**: `d0d739886e54ffe5c3d6e4ce67c3e8c3d71b8c33`
- **作者**: walter_wu
- **日期**: 2021-03-23 11:59:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 80. [BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數[補]
- **Commit ID**: `44289f62bb4877daf80ee9e90116a7a2bd67647c`
- **作者**: pinchi_lin
- **日期**: 2021-03-22 16:29:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 81. [BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數[補]
- **Commit ID**: `63685821a848b57b294e43effa40976ca2daa58d`
- **作者**: pinchi_lin
- **日期**: 2021-03-22 16:26:54
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 82. [BPM APP]新增動態渲染表單上附件與圖片走外網機制處理的系統變數
- **Commit ID**: `f012e6528b8cef1e7d9dcdeb87983d0d952a5a0d`
- **作者**: pinchi_lin
- **日期**: 2021-03-22 16:19:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`

### 83. [流程引擎]Q00-20210319004 加簽時將表單設定為唯讀，原本權限為完全控制的附件元件也會在加簽關卡中被調整成唯讀
- **Commit ID**: `07406ffddfa496d041767b8cc18766da0e4303c0`
- **作者**: yanann_chen
- **日期**: 2021-03-19 17:01:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`

### 84. [Web]Q00-20210319003 退回重辦頁面加上簽核意見的片語按鈕
- **Commit ID**: `5055444ad1f5fda4cd8efb785fd7f1d458a92e7e`
- **作者**: yanann_chen
- **日期**: 2021-03-19 14:44:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`

### 85. [流程引擎]Q00-20210319001 工作歷程依流程關卡設定限制發送工作通知
- **Commit ID**: `21899d680a2339555b608a7c654841cfc4dcb217`
- **作者**: yanann_chen
- **日期**: 2021-03-19 11:05:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 86. [BPM APP]Q00-20210318002 修正行動端詳情表單發送通知功能在選擇發送通知人員時有重複人員的問題
- **Commit ID**: `aa4e7b522ba61b50cd395061eea5ad4d1437089f`
- **作者**: yamiyeh10
- **日期**: 2021-03-19 10:10:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 87. [Web]Q00-20210318003 調整BPM登入頁面，提高系統安全性
- **Commit ID**: `73b3bbdb8d1122677d9c0cc94b2b077ee4d31610`
- **作者**: yanann_chen
- **日期**: 2021-03-18 17:43:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ForwardIndexAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`

### 88. [AD登入]C01-20210315009 修正使用者同時有兩個以上AD，密碼又不一樣會因為直接拋密碼錯誤，導至密碼正確的AD驗證不到
- **Commit ID**: `22b2ea601c290d162afd7d8f3305944a84eb4ee8`
- **作者**: walter_wu
- **日期**: 2021-03-17 17:30:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 89. [Web]A00-20210317002 修正進入多個處理者僅需一人處理的待辦事項，若使用者未接收工作就返回待辦清單，畫面卡住問題
- **Commit ID**: `5d8663452f627350d8ec4cae1f86cd2a8caa5a2b`
- **作者**: yanann_chen
- **日期**: 2021-03-17 16:18:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 90. [T100]結案回寫給T100，T100回饋錯誤訊息，通知系統管理員的Mail會顯示T100回饋的訊息
- **Commit ID**: `ce0dc23aa9869c332c0b6e66ebd0d24cb2f6fef3`
- **作者**: 林致帆
- **日期**: 2021-03-17 16:05:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/BpmServiceAPIBean.java`

### 91. [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
- **Commit ID**: `3dd396427b9357cac39585d4951270c6c3397c46`
- **作者**: 詩雅
- **日期**: 2021-03-17 13:37:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 92. [BPM APP]調整行動端詳情表單加簽功能的工作分派的方式
- **Commit ID**: `590cf36b92ee64e7abe26e7957075a8662c846f6`
- **作者**: cherryliao
- **日期**: 2021-03-17 11:25:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 93. [Web]C01-20210312001 修正在"非"發起人關卡上傳附件(勾選在線閱讀)，點選上傳的附件會出現閱讀檔未準備好
- **Commit ID**: `05791c73dbeaed834ad8eb1afccb53a41239c8de`
- **作者**: walter_wu
- **日期**: 2021-03-16 16:48:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 94. [Web]Q00-20210316003 修正附件元件設定FullControl 上傳檔案之後點擊下載按鈕出現請洽系統管理員
- **Commit ID**: `e48bb878b839ae356c97ba70407d5b1550ae9d28`
- **作者**: walter_wu
- **日期**: 2021-03-16 16:20:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 95. [Web]Q00-20210316002 修正在線閱覽的檔案點擊沒有反應
- **Commit ID**: `166f04df5d4745c98295747a7e4e5e027e322561`
- **作者**: walter_wu
- **日期**: 2021-03-16 16:14:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 96. [Web]Q00-20210316001 修正關卡設定必須上傳附件，沒有上傳卻能通過
- **Commit ID**: `e2236eeb19450162c4a29c7037fcf9656fb26bc4`
- **作者**: walter_wu
- **日期**: 2021-03-16 16:01:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 97. [BPM APP]C01-20210315006 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題[補]
- **Commit ID**: `b33bb86821264ea77d52e6679278299af7e849a1`
- **作者**: cherryliao
- **日期**: 2021-03-16 16:01:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`

### 98. [BPM APP]C01-20210315006 修正移動表單含有千分位的欄位在簽核後該數字會顯示異常的問題
- **Commit ID**: `f1fa59f249064f0d5f402aceaae9ea83b893675e`
- **作者**: cherryliao
- **日期**: 2021-03-16 15:52:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormManager.js`

### 99. [BPM APP]C01-20210310003 修正企業微信在iOS手機上，滾動查看到完整附件內容，操作不易的問題[補]
- **Commit ID**: `a9a78cb30f3baeaec08caf3ad77341e41f0e9fa8`
- **作者**: 詩雅
- **日期**: 2021-03-16 14:34:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`

### 100. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `d37971926797f616c46b0db396319db3caec1bb6`
- **作者**: peng_cheng_wang
- **日期**: 2021-03-16 12:03:33
- **變更檔案數量**: 0

### 101. [BPM APP]C01-20210310003 修正企業微信在iOS手機上，滾動查看到完整附件內容，操作不易的問題
- **Commit ID**: `9b93a46a262897fa32fd0f2c383776d3a8caa7ae`
- **作者**: 詩雅
- **日期**: 2021-03-16 11:41:14
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`

### 102. Revert "[流程引擎]Q00-20210315004 修正TextBox設日期/時間型態，年月日之間以dash作為分隔符在轉存表單時會錯"
- **Commit ID**: `cf9467594047f5408afb55cb95477327a7de9134`
- **作者**: peng_cheng_wang
- **日期**: 2021-03-16 11:24:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 103. [流程引擎]Q00-20210315004 修正TextBox設日期/時間型態，年月日之間以dash作為分隔符在轉存表單時會錯
- **Commit ID**: `b16c4a844adcc7c9495edc9f75b2d45d3cd9b59d`
- **作者**: 王鵬程
- **日期**: 2021-03-15 17:52:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 104. [E10]調整E10整合表單在行動端支援多個子單身功能與樣式
- **Commit ID**: `2881b0490227a134c284277b0e68926056e75a68`
- **作者**: yamiyeh10
- **日期**: 2021-03-15 17:41:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 105. [流程引擎]Q00-20210315002 修正搖旗吶喊小助手顯示的待辦事項數量與BPM待辦事項清單上的數量不符
- **Commit ID**: `faf227a54895e265a8ab4be54b0b828f4193d946`
- **作者**: yanann_chen
- **日期**: 2021-03-15 16:04:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NotifierListReader.java`

### 106. [Web]Q00-20210315001 維護查詢樣版js新增「重載頁面」功能
- **Commit ID**: `2b223a65def66aaffee351bf288f2864e231b405`
- **作者**: waynechang
- **日期**: 2021-03-15 15:54:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 107. [流程引擎]Q00-*********** 單身繫結元件為記錄Radio元件實際值的隱藏欄位，且單身欄位代號與元件代號相同，儲存表單失敗[補]
- **Commit ID**: `d7d2704c387f04c14ad21e47fe898fda53273e03`
- **作者**: yanann_chen
- **日期**: 2021-03-12 18:29:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 108. [流程引擎]A00-20210311002 修正若進行中的關卡有多個處理者，撤銷流程清單上資料的實際數量與下方顯示的資料總筆數不符
- **Commit ID**: `18087235d7dd3c0aeb6f23525b87b730d730e3cc`
- **作者**: yanann_chen
- **日期**: 2021-03-12 17:32:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AbortProcessAction.java`

### 109. [Web]A00-20210311001 修正 Safrai在追蹤，待辦，監控流程及發起流程下載附件須把下載視窗關閉，才能再下載附件
- **Commit ID**: `1179312b5b780929a9b0b4585dfba5f895e318be`
- **作者**: 林致帆
- **日期**: 2021-03-12 14:18:32
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 110. [E10]Q00-20210311005 修正E10取得簽核歷程排程，在執行第一筆E10單據報錯後，後面的E10單據就無法往下執行
- **Commit ID**: `9f4716b01b3988737865b761c8501ca3ac233058`
- **作者**: 林致帆
- **日期**: 2021-03-11 16:58:32
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`

### 111. [Web]S00-*********** 新增簡易流程圖支援核決關卡預先解析處理人員
- **Commit ID**: `be5771b5ed940eb7bb071377aafd1bd2ac08d052`
- **作者**: waynechang
- **日期**: 2021-03-11 16:36:46
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 112. [流程引擎]Q00-*********** 表單單身繫結元件為記錄Radio元件實際值的隱藏欄位，且單身欄位代號與元件代號相同，儲存表單發生異常
- **Commit ID**: `986759923c01af88abe3c672ccb91d44d74983b4`
- **作者**: yanann_chen
- **日期**: 2021-03-11 15:07:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 113. [Web]Q00-*********** 修正Chrome升級到89.0.4389.82版，在追蹤，待辦，監控流程及發起流程，無法下載附件
- **Commit ID**: `fe64f15e5a09136dc81abe1ea32d9c22867f22dc`
- **作者**: 林致帆
- **日期**: 2021-03-11 14:35:19
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 114. [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
- **Commit ID**: `0803b61e1648d027287c00ca6e1546230742a0c0`
- **作者**: 詩雅
- **日期**: 2021-03-11 11:10:21
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterLineTool.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileCommonManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MobileAuthorizeUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/aspect/AuthenticateRestfulService.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java`

### 115. [Web]Q00-20210310001 修正表單欄位設顯示小數後幾位和跨欄位運算，且流程關卡中欄位驗證是onblur或both，使欄位顯示異常
- **Commit ID**: `2159a4a404c221c76b35fa72de7c1749d606c80c`
- **作者**: 王鵬程
- **日期**: 2021-03-10 18:10:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 116. [ESS]新增ESS整合表單:ESSF01B員工排班(多人多天)
- **Commit ID**: `5720aea520e65b08f808f12ea1b7989bce9433da`
- **作者**: yamiyeh10
- **日期**: 2021-03-10 17:10:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@appfrom-essplus/form-default/V5.2\346\227\227\350\211\246/ESSF01B\345\223\241\345\267\245\346\216\222\347\217\255(\345\244\232\344\272\272\345\244\232\345\244\251).form"`

### 117. [流程引擎]C01-20210305004 修正5621以下OracleDB客戶版更後，流程派送報錯
- **Commit ID**: `1a3a9ae1bf0dc7e99d13125b3f334ccb30b68534`
- **作者**: walter_wu
- **日期**: 2021-03-10 15:50:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_for_5.5.6.2_Oracle_3.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.2.2_Oracle.sql`

### 118. [流程引擎]A00-20210305001 修正若資料庫為Oracle DB，在授權的流程清單使用建立時間排序發生異常[補]
- **Commit ID**: `c413f549032c902068ecd13ff92371083734f06a`
- **作者**: yanann_chen
- **日期**: 2021-03-10 11:17:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 119. [Web]Q00-20210309001 修正若資料庫為Oracle DB，追蹤流程點擊授權的流程使用「流程名稱」進行排序會報錯[補修正]
- **Commit ID**: `9681d96ca64f4d7b835637e6940d0373ad90729b`
- **作者**: 林致帆
- **日期**: 2021-03-09 11:30:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 120. [Web]Q00-20210309001 修正若資料庫為Oracle DB，追蹤流程點擊授權的流程使用「流程名稱」進行排序會報錯
- **Commit ID**: `353c37b82e26c50a0381960b68cec27f650159fd`
- **作者**: 林致帆
- **日期**: 2021-03-09 11:25:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 121. [Web]Q00-20210308005 修正監控流程中進入XPDL流程，流程圖太大沒出現scrollbar，以致無法查看流程圖
- **Commit ID**: `07eff3d20e311e22cfbdbb4c2fecb71d7f17fba5`
- **作者**: 王鵬程
- **日期**: 2021-03-08 16:44:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`

### 122. [內部]Q00-20210308004 調整字串(String)轉多語系(Locale)方法，加入NULL判斷避免資料轉換時發生異常
- **Commit ID**: `97cf0ee71f18e31a50fa3dd5473a466ad51b7300`
- **作者**: yanann_chen
- **日期**: 2021-03-08 16:26:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/Locale2StringFieldConversion.java`

### 123. [E10]Q00-20210308001 修正E10流程畫面子單身按鈕變成html字串
- **Commit ID**: `f81562e8e60c13624c36de7ad03bcfa646f1c8a8`
- **作者**: 林致帆
- **日期**: 2021-03-08 11:23:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 124. [流程引擎]A00-20210305001 修正若資料庫為Oracle DB，在"授權的流程"清單使用"建立時間"排序發生異常
- **Commit ID**: `4bfc8cfca326ad90afe50ae77893735e1d8e733b`
- **作者**: yanann_chen
- **日期**: 2021-03-05 18:15:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java`

### 125. [BPM APP]調整行動端相關功能在各平台的授權卡控機制[補]
- **Commit ID**: `3fdb3d849cd7e27aff78079542674f3327cd8dc8`
- **作者**: cherryliao
- **日期**: 2021-03-05 13:34:03
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SystemConfigManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/SystemConfigManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 126. [Web]Q00-20210304001 調整JavaScript浮點數運算誤差造成欄位計算結果不符預期
- **Commit ID**: `0975fce8923635aa7f546497eefb8817e79c2f3e`
- **作者**: yanann_chen
- **日期**: 2021-03-04 17:25:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 127. [流程引擎]A00-20210303001 修正建立workItem轉派記錄時發生NullPointerException問題
- **Commit ID**: `591c95d1344ade6de7f2f93e724986f140229583`
- **作者**: yanann_chen
- **日期**: 2021-03-04 14:53:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 128. [BPM APP]新增Web表單設計師的檢視表單發行歷程添加行動端動態渲染表單同步狀態功能
- **Commit ID**: `5df4bd373cb9faa71976e6270ad0755d367c4ed4`
- **作者**: yamiyeh10
- **日期**: 2021-03-04 13:36:11
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/FormDefinitionHistoryForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionHistoryListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/JSONConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 129. [BPM APP]調整行動端相關功能在各平台的授權卡控機制
- **Commit ID**: `47742d46616a24c58561f48173f6663192410538`
- **作者**: yamiyeh10
- **日期**: 2021-03-04 11:56:16
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MobileAuthorizeUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/PatternViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 130. [E10]A00-20210304001 修正E10單據在沒有附件的狀況，到取得附件關卡，無法派送到下個關卡
- **Commit ID**: `ca741e9eacb6647f42110d9edc61bf6cd4cb6ec2`
- **作者**: 林致帆
- **日期**: 2021-03-04 11:22:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 131. [Web]A00-20210225001 修正工作通知頁面用流程發起人來搜尋，會搜尋不到資料
- **Commit ID**: `56e494672bcb35876805e982ca8a33a473a82372`
- **作者**: 王鵬程
- **日期**: 2021-03-03 17:54:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`

### 132. [Web]Q00-20210303002 修正「開發教學索引->表單元件介紹及範例->CheckBox/RadioButton」範例內容錯誤
- **Commit ID**: `4dc25e3ceb7d54b0f763611a789b86d071cfd868`
- **作者**: yanann_chen
- **日期**: 2021-03-03 11:29:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`

### 133. [流程引擎]Q00-20210303001 修正時間已達到活動逾時通知設定的間隔時間，但活動逾時排程未發送通知信
- **Commit ID**: `07031f8c0a89d888a954be9e6bca270d0af63abb`
- **作者**: yanann_chen
- **日期**: 2021-03-03 10:47:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 134. [Web]Q00-20210302001 修正流程有迴圈設計，在迴圈的指定關卡為服務任務，造成簡易流程圖檢視異常
- **Commit ID**: `9a90fdc53ba1f013415668e7c7ee28ed4ae6b560`
- **作者**: 林致帆
- **日期**: 2021-03-02 11:55:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 135. [Web]修正 Chrome在追蹤，待辦，監控流程及發起流程下載附件須把下載視窗關閉，才能再下載附件
- **Commit ID**: `b6e9a0b5f52c5d3500acc760567be1b336bff0b3`
- **作者**: 林致帆
- **日期**: 2021-02-26 18:25:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

