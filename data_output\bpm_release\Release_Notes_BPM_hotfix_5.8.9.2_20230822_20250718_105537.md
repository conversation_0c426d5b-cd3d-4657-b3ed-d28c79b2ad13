# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.2_20230822
- **舊版本**: release_5.8.9.2
- **生成時間**: 2025-07-18 10:55:37
- **新增 Commit 數量**: 106

## 變更摘要

### 邱郁晏 (8 commits)

- **2023-08-02 11:31:06**: [資安] S00-20211220001 S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信(補)
  - 變更檔案: 4 個
- **2023-08-01 16:27:12**: [資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信
  - 變更檔案: 6 個
- **2023-07-20 11:50:08**: [資安] Q00-20230718001 修正發送通知者寄送Mail，不應副本給通知者的問題。
  - 變更檔案: 4 個
- **2023-08-15 15:10:34**: [Web] V00-20230815001 修正無上傳簽名圖檔時，後端拋錯異常，新增防呆。
  - 變更檔案: 2 個
- **2023-07-20 13:44:37**: [Web] Q00-20230713005 新增登入登出LOG印出SessionId，修正模擬為同一IP時取瀏覽器資訊異常問題
  - 變更檔案: 2 個
- **2023-07-17 17:55:45**: [流程引擎] Q00-20230717003 修正終止流程時，偶發ProcessInstance與BamProInstData狀態不一致問題
  - 變更檔案: 5 個
- **2023-07-14 14:48:48**: [流程引擎] Q00-20230714001 修正服務任務關卡執行後，不會重新組成全文檢索欄位問題
  - 變更檔案: 1 個
- **2023-07-13 11:44:10**: [Web] Q00-20230713003 優化使用者登入時異常時的LOG
  - 變更檔案: 1 個

### liuyun (4 commits)

- **2023-08-18 10:41:47**: [Web] Q00-20230817004 修改手机版追踪流程，取回重办、撤销流程显示两个时间
  - 變更檔案: 3 個
- **2023-08-16 12:08:59**: [Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常 [补修正]
  - 變更檔案: 1 個
- **2023-08-15 13:57:44**: [Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常
  - 變更檔案: 1 個
- **2023-08-11 15:36:12**: [Web] Q00-20230811001 修正BPM首页手机端流程有两个时间显示
  - 變更檔案: 1 個

### 周权 (8 commits)

- **2023-08-17 11:05:11**: [Web]Q00-20230817001 修正有關卡設定為列印模式，從待辦事項點選列印時，格式會跑掉
  - 變更檔案: 1 個
- **2023-08-14 16:08:20**: [Web]V00-20230814001 元件在表單存取控管設為Invisible 绝对位置表单列印空白
  - 變更檔案: 4 個
- **2023-08-10 13:22:36**: [Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白[补修正]
  - 變更檔案: 1 個
- **2023-07-17 09:25:32**: [Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]
  - 變更檔案: 4 個
- **2023-07-18 12:40:40**: [流程引擎]Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序[补修正]
  - 變更檔案: 1 個
- **2023-07-14 17:36:50**: [Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。
  - 變更檔案: 2 個
- **2023-07-20 10:33:53**: [流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException[补修正]
  - 變更檔案: 1 個
- **2023-07-18 16:45:19**: [流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException
  - 變更檔案: 1 個

### raven.917 (28 commits)

- **2023-06-12 13:57:42**: [Web] Q00-20230612004 修正絕對定位表單，上傳圖片異常問題。
  - 變更檔案: 1 個
- **2023-05-30 16:03:49**: [流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序(補修正)
  - 變更檔案: 2 個
- **2023-05-25 14:03:05**: [流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序
  - 變更檔案: 1 個
- **2023-06-20 17:03:38**: [在地化] Q00-20230620003 增加驗證SSOkey時，時間間隔超過5分鐘，印出LOG訊息
  - 變更檔案: 1 個
- **2023-07-11 10:24:08**: [流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題(補)
  - 變更檔案: 3 個
- **2023-07-07 14:39:22**: [流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題。
  - 變更檔案: 3 個
- **2023-07-06 15:22:50**: [組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。
  - 變更檔案: 1 個
- **2023-07-04 17:34:15**: [Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級(補)
  - 變更檔案: 1 個
- **2023-07-04 16:43:46**: [Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級
  - 變更檔案: 1 個
- **2023-07-03 16:31:00**: [Web] Q00-20230703004 修正表單列印畫面元件跑版問題，邊界調整為0
  - 變更檔案: 2 個
- **2023-06-27 14:41:06**: [TipTop] Q00-20230627002 調整TIPTOP的附件選擇txt時，上傳文件且未填說明欄位，轉檔異常問題。
  - 變更檔案: 1 個
- **2023-06-26 16:28:03**: [Tiptop] Q00-20230626002 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。
  - 變更檔案: 1 個
- **2023-06-21 11:44:24**: [Web] Q00-20230621003 修正Rwd-Grid 設置必填時，Alert訊息異常問題
  - 變更檔案: 1 個
- **2023-06-20 14:56:48**: [Web] Q00-20230620001 調整絕對定位表單追蹤流程下列印表單畫面。
  - 變更檔案: 3 個
- **2023-06-16 16:45:08**: [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)
  - 變更檔案: 2 個
- **2023-06-15 11:52:52**: [Web] Q00-20230615001 修正客制開窗order by轉小寫導致模糊查詢異常問題。
  - 變更檔案: 1 個
- **2023-06-05 11:41:57**: [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補)
  - 變更檔案: 1 個
- **2023-06-13 14:18:28**: [Web] Q00-20230613003 調整參與者型式的關卡頁面的「檢視轉派歷程」按鈕圖示。
  - 變更檔案: 1 個
- **2023-06-13 11:36:01**: [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)
  - 變更檔案: 1 個
- **2023-06-12 14:57:27**: [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。
  - 變更檔案: 1 個
- **2023-06-12 10:45:38**: [Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息(補修正)
  - 變更檔案: 3 個
- **2023-06-09 17:16:16**: [Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息
  - 變更檔案: 1 個
- **2023-06-09 15:13:05**: [Web] Q00-20230609004 修正匯入Excel資料內有,會被替換成空白問題
  - 變更檔案: 1 個
- **2023-06-05 17:14:49**: [流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求
  - 變更檔案: 1 個
- **2023-06-02 10:54:49**: [Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題
  - 變更檔案: 3 個
- **2023-05-26 14:59:32**: [Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題
  - 變更檔案: 1 個
- **2023-05-25 10:16:09**: [Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題
  - 變更檔案: 1 個
- **2023-05-25 19:35:08**: [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
  - 變更檔案: 1 個

### 林致帆 (26 commits)

- **2023-08-15 17:13:54**: [WorkFlow]Q00-20230815004 新增WorkFlow回寫增加時間訊息
  - 變更檔案: 1 個
- **2023-08-10 12:00:14**: [Web]V00-20230810005 修正ORACLE使用者開窗在有離職人員的狀況下打開會異常
  - 變更檔案: 1 個
- **2023-08-02 18:54:36**: [流程引擎]Q00-20230802004 修正流程未設定"被處理者終止時逐級通知"應該只要發起人收到信件通知
  - 變更檔案: 1 個
- **2023-07-26 10:15:28**: [WEB]Q00-20230725004 修正主旨樣板選擇"允許修改主旨"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨[補修正]
  - 變更檔案: 1 個
- **2023-07-25 18:16:32**: [WEB]Q00-20230725004 修正主旨樣板選擇"允許修改主旨"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨
  - 變更檔案: 1 個
- **2023-07-10 10:49:59**: [ESS]Q00-20230710001 調整log錯誤訊息的顯示
  - 變更檔案: 1 個
- **2023-07-20 11:16:58**: [Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員 [補修正]
  - 變更檔案: 1 個
- **2023-07-19 14:28:32**: [Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員
  - 變更檔案: 1 個
- **2023-07-13 10:23:47**: [Web]Q00-20230713002 修正使用者為部門主管從Portal進入BPM點選首頁顯示內容不為主管首頁
  - 變更檔案: 1 個
- **2023-06-27 13:39:34**: [T100]Q00-20230627001 修正關卡設置"所有附件皆需開啟過"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送
  - 變更檔案: 1 個
- **2023-06-15 17:18:15**: [Web]Q00-20230615002 修正離職維護作業無法開啟
  - 變更檔案: 2 個
- **2023-06-12 12:02:20**: [Web]Q00-20230612003 修正Script撰寫Grid的setColumnWith語法會跳出alert
  - 變更檔案: 1 個
- **2023-06-12 11:27:32**: [在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕
  - 變更檔案: 1 個
- **2023-05-26 10:45:38**: [Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間[補修正]
  - 變更檔案: 1 個
- **2023-05-25 15:33:05**: [Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間
  - 變更檔案: 1 個
- **2023-06-07 15:58:33**: [Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系
  - 變更檔案: 1 個
- **2023-06-06 14:44:56**: [ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持"通知"選項
  - 變更檔案: 1 個
- **2023-06-02 11:45:57**: [WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料
  - 變更檔案: 1 個
- **2023-06-02 10:23:40**: [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]
  - 變更檔案: 1 個
- **2023-06-01 10:56:27**: [Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常
  - 變更檔案: 1 個
- **2023-06-01 12:04:53**: [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息
  - 變更檔案: 1 個
- **2023-05-31 17:47:43**: [WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤
  - 變更檔案: 1 個
- **2023-05-26 16:38:31**: [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
  - 變更檔案: 3 個
- **2023-05-24 17:36:47**: [流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常
  - 變更檔案: 1 個
- **2023-05-24 17:01:45**: [Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加
  - 變更檔案: 1 個
- **2023-05-26 10:36:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個

### waynechang (10 commits)

- **2023-08-14 15:59:34**: [在線閱覽]Q00-20230814002 調整在線閱讀浮水印機制，當浮水印內容有特殊字導致無法添加浮水印時，改使用預設內容「userId+閱讀時間」作為浮水印內容，避免使用者無法順利閱讀檔案
  - 變更檔案: 3 個
- **2023-08-02 16:07:19**: [流程引擎]V00-20230802001 修正流程引擎在核決關卡展開第一關時向前加簽或核決關卡展開的最後一關向後加簽時，若PerformWorkItemHandlerBean的log層級設定info以上時，系統會出現錯誤訊息，實際上加簽關卡已成功的異常
  - 變更檔案: 1 個
- **2023-08-02 13:48:33**: [在線閱覽]Q00-20230802001 修正在線閱覽管理-轉檔異常處理作業，當關閉「上傳PDF」的開窗畫面後，系統會彈跳服務錯誤的訊息
  - 變更檔案: 1 個
- **2023-07-25 15:27:07**: [Web]Q00-20230725002 修正發起流程切換預解析流程時，當系統設定有設定該流程只以主部門發起(invoke.process.by.main.unit.process.package.ids)時，若使用者有多個兼職部門時，切換流程圖不應讓使用者選擇參考部門
  - 變更檔案: 1 個
- **2023-07-10 11:38:17**: [其他]Q00-20230704001 調整BCL8單個檔案轉檔逾時時間由預設2分鐘改為預設10分鐘
  - 變更檔案: 1 個
- **2023-07-04 17:40:33**: [Web]Q00-20230704003 調整formScript撰寫ajax加簽關卡後，需要更新session裡面的ProcessInst的相關屬性(Processpackage,ProcessDef等屬性)，避免預覽流程仍以加簽前的定義做解析
  - 變更檔案: 1 個
- **2023-07-04 17:02:01**: [Web]Q00-20230704001 調整CommonAccessor.updateConnectedUserInfo()更新使用者時間的方法，當更新異常時，由前端畫面提示「更新線上時間失敗」改為後端serverlog記錄就好
  - 變更檔案: 2 個
- **2023-06-28 18:02:27**: [SAP]Q00-20230628003 修正SAP整合回寫呼叫SAP的invoke服務，當SAP回傳的訊息需存放在Grid時，若Grid內容為空時，可能會導致formInstance.fieldValues產生多組相同Grid代號的內容
  - 變更檔案: 1 個
- **2023-06-27 15:09:38**: [SAP]Q00-20230627003 SAP整合的invoke服務任務增加表單相關資訊log
  - 變更檔案: 1 個
- **2023-06-01 11:24:59**: [其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容
  - 變更檔案: 2 個

### cherryliao (7 commits)

- **2023-07-31 15:59:38**: [表單設計師]Q00-20230731001 調整表單設計師縮小或切換頁簽後切回來操作沒立即更新使用者資訊的問題
  - 變更檔案: 2 個
- **2023-07-25 11:17:31**: [表單設計師]Q00-20230725001 調整選項元件進階設定選項開窗顯示值多語系有含單引號時確定按鈕異常的問題
  - 變更檔案: 2 個
- **2023-06-09 12:02:28**: [Web]Q00-20230609001 調整待辦流程開啟列印表單時Grid數據沒有加載的問題
  - 變更檔案: 1 個
- **2023-06-19 10:12:14**: [Web]Q00-20230619001 修正Grid元件在多欄位時欄位寬度異常顯示問題
  - 變更檔案: 2 個
- **2023-06-16 13:56:03**: [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題[補]
  - 變更檔案: 1 個
- **2023-06-06 13:57:44**: [Web]A00-20230605001 修正在待辦情況下將HandWriting元件透過Script設置disable時沒有作用問題
  - 變更檔案: 1 個
- **2023-06-07 14:51:50**: [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題
  - 變更檔案: 1 個

### yamiyeh10 (3 commits)

- **2023-07-26 16:31:24**: [BPM APP]Q00-20230726002 修正移動端絕對位置表單中Grid元件單身數據有空值時組成格式不正確問題
  - 變更檔案: 1 個
- **2023-06-01 15:10:16**: [BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題
  - 變更檔案: 1 個
- **2023-05-26 10:10:30**: [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]
  - 變更檔案: 1 個

### pinchi_lin (1 commits)

- **2023-07-20 12:13:56**: [DT]C01-20230719005 修正設計師使用權限管理中的組織設計師清單其id與名稱顯示異常問題
  - 變更檔案: 3 個

### 刘旭 (2 commits)

- **2023-07-17 14:37:58**: [web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复
  - 變更檔案: 1 個
- **2023-07-12 15:59:31**: [web]Q00-20230712003 修正在转存表单时栏位原數值為小數點後兩位，轉存表單後僅剩小數點後一位
  - 變更檔案: 1 個

### xiaobai (2 commits)

- **2023-07-11 14:12:59**: [Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白。
  - 變更檔案: 1 個
- **2023-07-05 14:23:40**: [Web]Q00-20230705002 修正表單在预览时，更換image后，显示图片不正确的问题
  - 變更檔案: 2 個

### liuxua (2 commits)

- **2023-07-10 16:56:51**: [web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题
  - 變更檔案: 2 個
- **2023-07-10 16:51:34**: [web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题
  - 變更檔案: 1 個

### develop_20274 (5 commits)

- **2023-06-05 10:37:25**: [Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示
  - 變更檔案: 1 個
- **2023-05-30 10:27:14**: [Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態
  - 變更檔案: 1 個
- **2023-05-29 17:06:25**: [Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)
  - 變更檔案: 1 個
- **2023-05-29 16:54:46**: [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正
  - 變更檔案: 2 個
- **2023-05-26 15:02:51**: [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [資安] S00-20211220001 S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信(補)
- **Commit ID**: `05d72e6d520fada627b175c8151fa780b7268171`
- **作者**: 邱郁晏
- **日期**: 2023-08-02 11:31:06
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 2. [資安] S00-20211220001 是否以系統郵件帳號作為寄件者，true啟用系統郵件帳號為寄件者，false以系統郵件帳號代理寄信
- **Commit ID**: `4ea7c2636820889857a89eb1b190c576cf5aadb8`
- **作者**: 邱郁晏
- **日期**: 2023-08-01 16:27:12
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.3_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.3_DML_Oracle.sql`

### 3. [資安] Q00-20230718001 修正發送通知者寄送Mail，不應副本給通知者的問題。
- **Commit ID**: `6298acb838c74bb082f68d4e3a8c3079018e247c`
- **作者**: 邱郁晏
- **日期**: 2023-07-20 11:50:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - 📝 **修改**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 4. [Web] Q00-20230817004 修改手机版追踪流程，取回重办、撤销流程显示两个时间
- **Commit ID**: `62e51be08467200ab0fa21aa45f18dface5c56ae`
- **作者**: liuyun
- **日期**: 2023-08-18 10:41:47
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 5. [Web]Q00-20230817001 修正有關卡設定為列印模式，從待辦事項點選列印時，格式會跑掉
- **Commit ID**: `bed691d4bf30566611d01ff417578e18f51205be`
- **作者**: 周权
- **日期**: 2023-08-17 11:05:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 6. [Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常 [补修正]
- **Commit ID**: `55b7791cd3c98498cc968b169e24178b7a81b0ef`
- **作者**: liuyun
- **日期**: 2023-08-16 12:08:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 7. [Web] Q00-20230815002 修正表单设计器进阶资料选取新增列序号异常
- **Commit ID**: `32677eef161658c2cab1dbcd3aa3647f24e66462`
- **作者**: liuyun
- **日期**: 2023-08-15 13:57:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 8. [Web] Q00-20230612004 修正絕對定位表單，上傳圖片異常問題。
- **Commit ID**: `76f3b43150049bd8afe4dbbe7135103cc8e02a4c`
- **作者**: raven.917
- **日期**: 2023-06-12 13:57:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 9. [Web] V00-20230815001 修正無上傳簽名圖檔時，後端拋錯異常，新增防呆。
- **Commit ID**: `c1fb067e6f7467ed3108f7dfd916a900917c35cb`
- **作者**: 邱郁晏
- **日期**: 2023-08-15 15:10:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 10. [WorkFlow]Q00-20230815004 新增WorkFlow回寫增加時間訊息
- **Commit ID**: `6e3b5f42bf955a2029a00794d37acceb997f29ca`
- **作者**: 林致帆
- **日期**: 2023-08-15 17:13:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 11. [Web]V00-20230810005 修正ORACLE使用者開窗在有離職人員的狀況下打開會異常
- **Commit ID**: `c11a0fbaa41a99a89b255f760611d3e459590213`
- **作者**: 林致帆
- **日期**: 2023-08-10 12:00:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 12. [Web]V00-20230814001 元件在表單存取控管設為Invisible 绝对位置表单列印空白
- **Commit ID**: `15a7b148bbb5dd47cf5b6230f8f72a73d82c00c1`
- **作者**: 周权
- **日期**: 2023-08-14 16:08:20
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 13. [Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白[补修正]
- **Commit ID**: `136c32b278339d60263a625a444bb6643ecefeab`
- **作者**: 周权
- **日期**: 2023-08-10 13:22:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 14. [在線閱覽]Q00-20230814002 調整在線閱讀浮水印機制，當浮水印內容有特殊字導致無法添加浮水印時，改使用預設內容「userId+閱讀時間」作為浮水印內容，避免使用者無法順利閱讀檔案
- **Commit ID**: `a192059f13d5ca6f6d51a507434a7163f0fcb5aa`
- **作者**: waynechang
- **日期**: 2023-08-14 15:59:34
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/PDFBoxConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 15. [Web] Q00-20230811001 修正BPM首页手机端流程有两个时间显示
- **Commit ID**: `f34347c441444e1744b0cea51be4d68e30236b57`
- **作者**: liuyun
- **日期**: 2023-08-11 15:36:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 16. [流程引擎]Q00-20230802004 修正流程未設定"被處理者終止時逐級通知"應該只要發起人收到信件通知
- **Commit ID**: `0d07714fdcece81f2ea82497346ce29308e48e29`
- **作者**: 林致帆
- **日期**: 2023-08-02 18:54:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 17. [表單設計師]Q00-20230731001 調整表單設計師縮小或切換頁簽後切回來操作沒立即更新使用者資訊的問題
- **Commit ID**: `c4cfed03c122d56573a1c0b4790825fe30b511d3`
- **作者**: cherryliao
- **日期**: 2023-07-31 15:59:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 18. [流程引擎]V00-20230802001 修正流程引擎在核決關卡展開第一關時向前加簽或核決關卡展開的最後一關向後加簽時，若PerformWorkItemHandlerBean的log層級設定info以上時，系統會出現錯誤訊息，實際上加簽關卡已成功的異常
- **Commit ID**: `0e379c71b448b2e2104b6f3ed5bbb0e890cccc71`
- **作者**: waynechang
- **日期**: 2023-08-02 16:07:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 19. [在線閱覽]Q00-20230802001 修正在線閱覽管理-轉檔異常處理作業，當關閉「上傳PDF」的開窗畫面後，系統會彈跳服務錯誤的訊息
- **Commit ID**: `65f50bed63c4be1493df5b803a20fbd251fdc330`
- **作者**: waynechang
- **日期**: 2023-08-02 13:48:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp`

### 20. [BPM APP]Q00-20230726002 修正移動端絕對位置表單中Grid元件單身數據有空值時組成格式不正確問題
- **Commit ID**: `cd12b56b261c5b37331e93b0df691b56915bb528`
- **作者**: yamiyeh10
- **日期**: 2023-07-26 16:31:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 21. [WEB]Q00-20230725004 修正主旨樣板選擇"允許修改主旨"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨[補修正]
- **Commit ID**: `653a28397caf5df5545fc44e54f844efff5f0b25`
- **作者**: 林致帆
- **日期**: 2023-07-26 10:15:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 22. [WEB]Q00-20230725004 修正主旨樣板選擇"允許修改主旨"輸入換行，會導致流程發起時更新主旨都無法更新成使用者輸入的主旨
- **Commit ID**: `264e17cfac1401d2de40019376306f3a64617bc6`
- **作者**: 林致帆
- **日期**: 2023-07-25 18:16:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 23. [Web]Q00-20230725002 修正發起流程切換預解析流程時，當系統設定有設定該流程只以主部門發起(invoke.process.by.main.unit.process.package.ids)時，若使用者有多個兼職部門時，切換流程圖不應讓使用者選擇參考部門
- **Commit ID**: `b6d7a2e0fb05489f89f35540a545a63918d942f8`
- **作者**: waynechang
- **日期**: 2023-07-25 15:27:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 24. [表單設計師]Q00-20230725001 調整選項元件進階設定選項開窗顯示值多語系有含單引號時確定按鈕異常的問題
- **Commit ID**: `d9977a78ea7353126c8d315d324efca8985281b4`
- **作者**: cherryliao
- **日期**: 2023-07-25 11:17:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 25. [Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。[补修正]
- **Commit ID**: `8cce2261fc5f227f5106e398481c42cca377cea4`
- **作者**: 周权
- **日期**: 2023-07-17 09:25:32
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmPrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 26. [流程引擎]Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序[补修正]
- **Commit ID**: `ac8ef6489b530b4906d10748851897418222fd1b`
- **作者**: 周权
- **日期**: 2023-07-18 12:40:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 27. [Web]Q00-20230714003 修正：签名图档为空时，删除预设白色图片。
- **Commit ID**: `386562baff8e1cfd0c6e853df74435fe0cd596d9`
- **作者**: 周权
- **日期**: 2023-07-14 17:36:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 28. [流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序(補修正)
- **Commit ID**: `ec2aaa8a764e59ab64ccd11bf93b78bfb91fb6e6`
- **作者**: raven.917
- **日期**: 2023-05-30 16:03:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActivityInstanceComparator.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 29. [流程引擎] Q00-20230525003 調整參與者活動實例若關卡建立時間相同時，排序異常，改使用OID作為排序
- **Commit ID**: `13cc2014085068c6433b880199df9daf4519b499`
- **作者**: raven.917
- **日期**: 2023-05-25 14:03:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 30. [ESS]Q00-20230710001 調整log錯誤訊息的顯示
- **Commit ID**: `79c9a925292248de488b5944292104a6ade61401`
- **作者**: 林致帆
- **日期**: 2023-07-10 10:49:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 31. [Web] Q00-20230713005 新增登入登出LOG印出SessionId，修正模擬為同一IP時取瀏覽器資訊異常問題
- **Commit ID**: `2c6a995677ed1a4d523e715c2685b88a7f62d59b`
- **作者**: 邱郁晏
- **日期**: 2023-07-20 13:44:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 32. [在地化] Q00-20230620003 增加驗證SSOkey時，時間間隔超過5分鐘，印出LOG訊息
- **Commit ID**: `e0e066dfdb1fa81d7dde0a12847582c19a056424`
- **作者**: raven.917
- **日期**: 2023-06-20 17:03:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 33. [DT]C01-20230719005 修正設計師使用權限管理中的組織設計師清單其id與名稱顯示異常問題
- **Commit ID**: `3c2b454bdefe63b2353f5ef81b66dec59cea18c3`
- **作者**: pinchi_lin
- **日期**: 2023-07-20 12:13:56
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/controller/ParticipantInfoAcquirer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/AccessCrtlMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/DesignerAuthorityMgr.java`

### 34. [Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員 [補修正]
- **Commit ID**: `aafe83f9744a0522fa4b16467f8fc1f6b559336f`
- **作者**: 林致帆
- **日期**: 2023-07-20 11:16:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 35. [流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException[补修正]
- **Commit ID**: `edabb9e0cb8722c57fd8ddd88d5d79c8bec2ed7a`
- **作者**: 周权
- **日期**: 2023-07-20 10:33:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 36. [流程引擎]Q00-20230718002 流程卡在轉存表單，报NullPointerException
- **Commit ID**: `fc582f6da64668f93d0c6ffaa029deed2235e987`
- **作者**: 周权
- **日期**: 2023-07-18 16:45:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 37. [Web]Q00-20230719001 修正人員設定最後工作日為當天，人員開窗會選不到該人員
- **Commit ID**: `0f788d638ad67ac07d9b8d81d2a0e3d0f024c7a0`
- **作者**: 林致帆
- **日期**: 2023-07-19 14:28:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 38. [流程引擎] Q00-20230717003 修正終止流程時，偶發ProcessInstance與BamProInstData狀態不一致問題
- **Commit ID**: `c00c08cb53e93fa28864627f6e82c57ee87f3bb8`
- **作者**: 邱郁晏
- **日期**: 2023-07-17 17:55:45
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`

### 39. [web]Q00-20230717002 客製開發 JSP，引用產品 Grid 元件，發現 Grid 的格線，有時會出現無法對齊的情況问题修复
- **Commit ID**: `2f528ac2e10384e8bc2909b3d893c6614bf50cdb`
- **作者**: 刘旭
- **日期**: 2023-07-17 14:37:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/BpmTable.css`

### 40. [流程引擎] Q00-20230714001 修正服務任務關卡執行後，不會重新組成全文檢索欄位問題
- **Commit ID**: `8836d2fef061b8277612950109ca6c9d06d1915c`
- **作者**: 邱郁晏
- **日期**: 2023-07-14 14:48:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 41. [Web] Q00-20230713003 優化使用者登入時異常時的LOG
- **Commit ID**: `62d8599eafb1afd676fc0759806242adb502d48a`
- **作者**: 邱郁晏
- **日期**: 2023-07-13 11:44:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/persistence/PersistentObjectHelper.java`

### 42. [Web]Q00-20230713002 修正使用者為部門主管從Portal進入BPM點選首頁顯示內容不為主管首頁
- **Commit ID**: `8c3136f94185f1a6a7ae4cd4caed76991059cb0a`
- **作者**: 林致帆
- **日期**: 2023-07-13 10:23:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 43. [web]Q00-20230712003 修正在转存表单时栏位原數值為小數點後兩位，轉存表單後僅剩小數點後一位
- **Commit ID**: `8460f5b46051afe0a1bf0d9ea9d70e36b25a4b41`
- **作者**: 刘旭
- **日期**: 2023-07-12 15:59:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 44. [Web]Q00-20230711001 元件在表單存取控管設為Invisible 在表單畫面中顯示會出現空白。
- **Commit ID**: `1340937f84f9f2c661d156f10b560fdf7070e44d`
- **作者**: xiaobai
- **日期**: 2023-07-11 14:12:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`

### 45. [流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題(補)
- **Commit ID**: `4148e0147df5360584669f5a36c4c6d37b30af64`
- **作者**: raven.917
- **日期**: 2023-07-11 10:24:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`

### 46. [流程引擎] Q00-20230707003 修正系統通知待辦URL顯示N.A及重複寄送多餘系統通知問題。
- **Commit ID**: `c2a731f0ba096ff654dd7ff1958831b92a28550b`
- **作者**: raven.917
- **日期**: 2023-07-07 14:39:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageWfNotificationAction.java`

### 47. [web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题
- **Commit ID**: `5ae761b8897e3299c43924e6b126a748d53654ae`
- **作者**: liuxua
- **日期**: 2023-07-10 16:56:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`

### 48. [web]Q00-20230710006 修正系统在SAP主机设定更新时主键重复问题
- **Commit ID**: `7c967dc6e64de3e36dcfc2a6743fe64736beed0c`
- **作者**: liuxua
- **日期**: 2023-07-10 16:51:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`

### 49. [其他]Q00-20230704001 調整BCL8單個檔案轉檔逾時時間由預設2分鐘改為預設10分鐘
- **Commit ID**: `7c4a1138898a33d8aaf6fac7f8040c41c183d3d6`
- **作者**: waynechang
- **日期**: 2023-07-10 11:38:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 50. [組織同步] Q00-20230706002 修正組織同步帳號是否啟用邏輯，導致異常錯誤問題。
- **Commit ID**: `d3145c639005ba13adaeb4e0ee35d7716266bada`
- **作者**: raven.917
- **日期**: 2023-07-06 15:22:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 51. [Web]Q00-20230705002 修正表單在预览时，更換image后，显示图片不正确的问题
- **Commit ID**: `f9286e2062de27ff6f0ec71845f20857cb357f8d`
- **作者**: xiaobai
- **日期**: 2023-07-05 14:23:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ImageElement.java`

### 52. [Web]Q00-20230704003 調整formScript撰寫ajax加簽關卡後，需要更新session裡面的ProcessInst的相關屬性(Processpackage,ProcessDef等屬性)，避免預覽流程仍以加簽前的定義做解析
- **Commit ID**: `b8bf4fe66412e250d96f526e456fdf27cb66c531`
- **作者**: waynechang
- **日期**: 2023-07-04 17:40:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 53. [Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級(補)
- **Commit ID**: `e61a884d84b124afd3f9fe7ce225455f5494d141`
- **作者**: raven.917
- **日期**: 2023-07-04 17:34:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 54. [Web] Q00-20230704002 新增LOG並調整驗證授權人數及排程剔除閒置人員的LOG層級
- **Commit ID**: `2f41906d8ea89b45960d13034fecd607eece5268`
- **作者**: raven.917
- **日期**: 2023-07-04 16:43:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 55. [Web]Q00-20230704001 調整CommonAccessor.updateConnectedUserInfo()更新使用者時間的方法，當更新異常時，由前端畫面提示「更新線上時間失敗」改為後端serverlog記錄就好
- **Commit ID**: `b83a5ca0fc7355b63c5a0b7c0ae1b1ae2903d4c4`
- **作者**: waynechang
- **日期**: 2023-07-04 17:02:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmUpdateConnUser.js`

### 56. [Web] Q00-20230703004 修正表單列印畫面元件跑版問題，邊界調整為0
- **Commit ID**: `80c003dc533af2a497d5589f2639c1220d34f0ea`
- **作者**: raven.917
- **日期**: 2023-07-03 16:31:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 57. [Web]Q00-20230609001 調整待辦流程開啟列印表單時Grid數據沒有加載的問題
- **Commit ID**: `725e5ca7380785ed5c5b1c2eadfcf137301f99d1`
- **作者**: cherryliao
- **日期**: 2023-06-09 12:02:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`

### 58. [SAP]Q00-20230628003 修正SAP整合回寫呼叫SAP的invoke服務，當SAP回傳的訊息需存放在Grid時，若Grid內容為空時，可能會導致formInstance.fieldValues產生多組相同Grid代號的內容
- **Commit ID**: `496c3900bcfcfa0ee21a76f9f83e3c67d2d93990`
- **作者**: waynechang
- **日期**: 2023-06-28 18:02:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormInstance.java`

### 59. [SAP]Q00-20230627003 SAP整合的invoke服務任務增加表單相關資訊log
- **Commit ID**: `3d4c925a54cd3b1935c5cef028033a6f1b9458e2`
- **作者**: waynechang
- **日期**: 2023-06-27 15:09:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrInvoke.java`

### 60. [TipTop] Q00-20230627002 調整TIPTOP的附件選擇txt時，上傳文件且未填說明欄位，轉檔異常問題。
- **Commit ID**: `70284b28ae495eb228734f2f6683c402f9486448`
- **作者**: raven.917
- **日期**: 2023-06-27 14:41:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/PDFHandler.java`

### 61. [T100]Q00-20230627001 修正關卡設置"所有附件皆需開啟過"在T100單據未帶附件只有附件的內容說明，生成的txt附件點擊下載還是無法繼續派送
- **Commit ID**: `66d963f1cf56945d586c84cc56cd7851d36d243c`
- **作者**: 林致帆
- **日期**: 2023-06-27 13:39:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 62. [Tiptop] Q00-20230626002 修正TT拋單，欄位值若有換行符號，導致絕對位置表單Grid異常問題。
- **Commit ID**: `dc65303df2e29fd80e723ebd68da1b825cb3d0c8`
- **作者**: raven.917
- **日期**: 2023-06-26 16:28:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 63. [Web] Q00-20230621003 修正Rwd-Grid 設置必填時，Alert訊息異常問題
- **Commit ID**: `a98fbcd500a2f55192f5e8516bcb87d48cdd9ede`
- **作者**: raven.917
- **日期**: 2023-06-21 11:44:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 64. [Web] Q00-20230620001 調整絕對定位表單追蹤流程下列印表單畫面。
- **Commit ID**: `f4e8cc1b1e5872edd97568885677d5f1389baa0f`
- **作者**: raven.917
- **日期**: 2023-06-20 14:56:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 65. [Web]Q00-20230619001 修正Grid元件在多欄位時欄位寬度異常顯示問題
- **Commit ID**: `70df8e2b201aebca721e28c12cb9807900007148`
- **作者**: cherryliao
- **日期**: 2023-06-19 10:12:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`

### 66. [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)
- **Commit ID**: `18e7381a2579f976079d29a9e616128cf3869280`
- **作者**: raven.917
- **日期**: 2023-06-16 16:45:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 67. [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題[補]
- **Commit ID**: `abeabeed156b7026be0dcfbc69fb6f59cc9bae48`
- **作者**: cherryliao
- **日期**: 2023-06-16 13:56:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`

### 68. [Web]Q00-20230615002 修正離職維護作業無法開啟
- **Commit ID**: `b81ce47c61e8107c4ed254b8219768b4f077d4c1`
- **作者**: 林致帆
- **日期**: 2023-06-15 17:18:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java`

### 69. [Web] Q00-20230615001 修正客制開窗order by轉小寫導致模糊查詢異常問題。
- **Commit ID**: `2bbe00d25531c762f9618270e49a9ad1e63d67fc`
- **作者**: raven.917
- **日期**: 2023-06-15 11:52:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 70. [Web] Q00-20230420001 修正客製開窗子查詢Group By異常(補)
- **Commit ID**: `c068bc54562590d6548eaa7e358fd9753bb0e5c0`
- **作者**: raven.917
- **日期**: 2023-06-05 11:41:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 71. [Web] Q00-20230613003 調整參與者型式的關卡頁面的「檢視轉派歷程」按鈕圖示。
- **Commit ID**: `6afb05dadf8a24d7c3a20b56d27ff507dececbd8`
- **作者**: raven.917
- **日期**: 2023-06-13 14:18:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 72. [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。(補修正)
- **Commit ID**: `02c0450d001b5267e58f30b51eca64ae478a4711`
- **作者**: raven.917
- **日期**: 2023-06-13 11:36:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 73. [Web] Q00-20230612005 修正使用者簽名圖檔找不到的異常，調整邏輯並新增防呆。
- **Commit ID**: `bd71e74c850bdab523818fed0bd80ecbf8d8702e`
- **作者**: raven.917
- **日期**: 2023-06-12 14:57:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 74. [Web]Q00-20230612003 修正Script撰寫Grid的setColumnWith語法會跳出alert
- **Commit ID**: `fe114df67129a5ea377789fdf8453eaeb686eb62`
- **作者**: 林致帆
- **日期**: 2023-06-12 12:02:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 75. [在線閱覽]Q00-20230612002 修正附件元件權限狀態為full-controll且有在線閱覽權限，才會長出原始檔下載按鈕
- **Commit ID**: `a8b4e25bc25a0d29efc9bf12c6d11dd0cee18818`
- **作者**: 林致帆
- **日期**: 2023-06-12 11:27:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 76. [Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間[補修正]
- **Commit ID**: `cd837fc6847cbc1581f23a344abdac9a2540f0f4`
- **作者**: 林致帆
- **日期**: 2023-05-26 10:45:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 77. [Web]Q00-20230525005 調整表單上傳附件的上傳時間會隨著時區變動的時間
- **Commit ID**: `ee073a7c97ffa26a05ca184b5f52f2d34395482c`
- **作者**: 林致帆
- **日期**: 2023-05-25 15:33:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 78. [Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息(補修正)
- **Commit ID**: `bc98a848c088df53b51b0910d727ccc537891167`
- **作者**: raven.917
- **日期**: 2023-06-12 10:45:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 79. [Web] Q00-20230609006 修正匯入Excel表單時內容為空時，顯示Alert異常訊息
- **Commit ID**: `152815aa6f63074961368e6c2682abce9b796aff`
- **作者**: raven.917
- **日期**: 2023-06-09 17:16:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 80. [Web] Q00-20230609004 修正匯入Excel資料內有,會被替換成空白問題
- **Commit ID**: `87231d6677524bca1103b0d5ea5d82c77947d78f`
- **作者**: raven.917
- **日期**: 2023-06-09 15:13:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 81. [Web]A00-20230605001 修正在待辦情況下將HandWriting元件透過Script設置disable時沒有作用問題
- **Commit ID**: `54ade605839d6befe3b6720e28974a214591de53`
- **作者**: cherryliao
- **日期**: 2023-06-06 13:57:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`

### 82. [Portal]Q00-20230607002 修正從Portal開到BPM畫面都為英文語系
- **Commit ID**: `8c565b7e43091d7c353185f535ac2d264dcb5760`
- **作者**: 林致帆
- **日期**: 2023-06-07 15:58:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SysLanguageHandler.java`

### 83. [Web]A00-20230602001 修正HandWriting元件在沒寫入資料時使用getData語法仍會判斷成有內容的問題
- **Commit ID**: `8812df4c09c48db0f2d506641f0d4239796cdba6`
- **作者**: cherryliao
- **日期**: 2023-06-07 14:51:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-handWriting.js`

### 84. [ESS]Q00-20230606001 調整ESS流程第一關若使用加簽只支持"通知"選項
- **Commit ID**: `05a01ebc45a6add25c0f63a8346dd8b052a31834`
- **作者**: 林致帆
- **日期**: 2023-06-06 14:44:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 85. [流程引擎] Q00-20230605003 修正WebApplication未依照呼叫方法發送請求
- **Commit ID**: `ecc88a61dbc4ea0d5ae16956dfac7e9c842d04e7`
- **作者**: raven.917
- **日期**: 2023-06-05 17:14:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/WebApplicationAgent.java`

### 86. [Web] C01-20230530001 調整DialogInputMulti樹狀開窗高度顯示
- **Commit ID**: `49a235dc520fd84652e53353c19fa8885e68c652`
- **作者**: develop_20274
- **日期**: 2023-06-05 10:37:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`

### 87. [Web] Q00-20230602001 修正在列印模式下，選項元件FormUtil取值異常問題
- **Commit ID**: `8088ac8fef882d3642e763766e45acdc61474dd9`
- **作者**: raven.917
- **日期**: 2023-06-02 10:54:49
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 88. [WorkFlow]Q00-20230602003 修正取簽核歷程為多筆數時會無法取得資料
- **Commit ID**: `f48fac77aa1f751759f325043641f43783dbc0e1`
- **作者**: 林致帆
- **日期**: 2023-06-02 11:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 89. [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息 [補修正]
- **Commit ID**: `acdfd7b7d457a9d312a383bdfdcf3d542c86ff76`
- **作者**: 林致帆
- **日期**: 2023-06-02 10:23:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 90. [BPM APP]Q00-20230601006 調整郵件內容以及Line推播內容中DialogInputLabel元件的內容顯示不完全的問題
- **Commit ID**: `01f34b18f3cb2674099a28bbc63dd503bc57ceea`
- **作者**: yamiyeh10
- **日期**: 2023-06-01 15:10:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 91. [其他]Q00-20230601003 調整digiwin轉檔工具，需相容舊版的服務接口，避免檔案可以轉檔，但無法顯示浮水印內容
- **Commit ID**: `8186af7b5bb50188f75c03ec114653c012364ad8`
- **作者**: waynechang
- **日期**: 2023-06-01 11:24:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/DigiwinPDFConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`

### 92. [Web]Q00-20230601002 修正表單用ajax撈資料開窗用中文字查詢資料異常
- **Commit ID**: `dee1ff9a9f49a64f2036216c91549582832b47c5`
- **作者**: 林致帆
- **日期**: 2023-06-01 10:56:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Dwr/dwr.jar`

### 93. [WorkFlow]Q00-20230601004 調整WorkFlow單據為取消確認，在流程終止後回傳的狀態碼為3，並優化log訊息
- **Commit ID**: `bbba77b6293efa1c468c03bbea5681d634c91537`
- **作者**: 林致帆
- **日期**: 2023-06-01 12:04:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 94. [WorkFlow]Q00-20230531002 新增流程撤銷,終止增加取得WFRequestRecordModel資料的log以判別回傳的內容是否有誤
- **Commit ID**: `7465c9e3fae294f55777e734f7262a6de032f0fd`
- **作者**: 林致帆
- **日期**: 2023-05-31 17:47:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 95. [Web] Q00-20230530001 調整radioButton&ListBox&DropDown元件自定義值內有「英打逗號,」列印時無法正常顯示選取狀態
- **Commit ID**: `f1654eb781f177acdabd651a99072bebbfb3464f`
- **作者**: develop_20274
- **日期**: 2023-05-30 10:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 96. [Web] Q00-20230526003 調整radioButton元件自定義值內有「英打逗號,」儲存時無法被Selected問題(單選)
- **Commit ID**: `256583ba35be24e43684df0f48927bbe9ab01313`
- **作者**: develop_20274
- **日期**: 2023-05-29 17:06:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 97. [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題_補修正
- **Commit ID**: `f879995c811268ee03a7fa0f5bb44fb8b838acc1`
- **作者**: develop_20274
- **日期**: 2023-05-29 16:54:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 98. [WorkFlow]Q00-20230526004 調整ERP的流程建立完成前先處理附件，避免附件異常流程也能繼續發起
- **Commit ID**: `eb4fcda9bed15bec18820491e2c67e769a24a91d`
- **作者**: 林致帆
- **日期**: 2023-05-26 16:38:31
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 99. [Web] Q00-20230525006 調整dropdown元件自定義值內有「英打逗號,」儲存時的無法被Selected問題
- **Commit ID**: `d72accd32ca2ac64d731380966e8b8f53529744d`
- **作者**: develop_20274
- **日期**: 2023-05-26 15:02:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 100. [Web] Q00-20230526001 修正關卡通知信設定以整張表單時，<>符號在通知信上顯示異常問題
- **Commit ID**: `4ae609cd95bff0a2b8728345da221270ec06eafb`
- **作者**: raven.917
- **日期**: 2023-05-26 14:59:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 101. [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題[補]
- **Commit ID**: `0eabc66d8706bfec8d0f937d7f5ad593bfbc736b`
- **作者**: yamiyeh10
- **日期**: 2023-05-26 10:10:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 102. [Web] Q00-20230525001 修正單身繫結元件Radio元件實際值隱藏欄位，實際值丟失問題
- **Commit ID**: `64fa32e7bc15301766f00da2e16ea1dbe87fdc20`
- **作者**: raven.917
- **日期**: 2023-05-25 10:16:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 103. [流程引擎]Q00-20230524005 調整程式log層級，避免讓客戶誤解產品異常
- **Commit ID**: `ab7a426ee61294822f61d9d19fb01ace9ac934c4`
- **作者**: 林致帆
- **日期**: 2023-05-24 17:36:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java`

### 104. [Web]Q00-20230524004 修正使用者名字有特殊字，上傳附件後派送流程後，附件的上傳者內容的特殊字會一直重複增加
- **Commit ID**: `cb8973a1085888406960b5ae2c4373b226bd9b14`
- **作者**: 林致帆
- **日期**: 2023-05-24 17:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 105. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `ed1a7b3d587b5d6d7a041912c9f502e537d2eff5`
- **作者**: 林致帆
- **日期**: 2023-05-26 10:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 106. [組織同步] Q00-20230525008 修正HRM同步設置orgId異常值導致報錯問題
- **Commit ID**: `798a76611aaaefd9e7ca40e01e571b75e5b99e7a`
- **作者**: raven.917
- **日期**: 2023-05-25 19:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/HrmSyncOrgMgr.java`

