{"company_id": "06100101", "company_name": "泰藝", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06100101_泰藝\\01客戶基本資料", "files": [{"filename": "[泰藝]連線資訊.txt", "raw_content": "06100101[泰藝]ISO、<PERSON>\r\n李奕璁 (02)26861287#326 <EMAIL> 0934-045-168\r\n373施經理 \r\n------------------------------------------------\r\n以下是Dell SonicWALL vpn連線資訊 \r\n(要連要提前講，他們內部要申請，不能馬上開)\r\nsslvpn.taitien.com.tw\r\nEFGP\r\nf8g$KCm#\r\nDomin選擇：LocalDomain\r\n------------------------------------------------\r\nEFGP正式機+DB (5892_0621)\r\n***********\r\nRDP:administrator / dsc@12345\r\nhttp://***********:8080/NaNaWeb\r\nhttp://efgp.taitien.com.tw:8080/NaNaWeb\r\nEFGP網頁：administrator / efgp!@#taitien\r\n\r\n新測試機+BCL8\r\n***********:8080 (5892_0621)\r\nOS密碼 : administrator / 1234\r\n\r\n\r\n5892版的正式區是8080port,DB是NaNa\r\n5892版的測試區是8080port,DB是NaNaTEST2\r\n5883版的測試區是8089port,DB是NaNaTEST\r\n-----------------------------\r\n工程-林義翔 20230311:\r\n\r\n當初在起案時候，我跟客戶討論時候是這樣\r\n沒有額外再添購Windows Server / SQL Server 授權\r\nPortal 與 EFGP 同一台AP/DB，但Portal 並沒有要版更 ( 客製相當多 )\r\n \r\n所以最後是將EFGP測試機 ( 實體 /2003 )，至少轉到虛擬化 2008R2\r\n-----------------------------\r\n", "structured_data": {"domin選擇": "LocalDomain", "efgp網頁": "administrator / efgp!@#taitien", "rdp": "administrator / dsc@12345", "http": "//efgp.taitien.com.tw:8080/NaNaWeb", "***********": "8080 (5892_0621)", "os密碼": "administrator / 1234", "host": "***********"}, "source_path": "C1.客戶維護相關\\06100101_泰藝\\01客戶基本資料\\[泰藝]連線資訊.txt", "file_size": 1033, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:29.161765"}], "total_files": 1, "processed_at": "2025-08-26T10:46:29.161774"}