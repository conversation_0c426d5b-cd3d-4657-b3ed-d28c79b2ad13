# Release Notes - BPM

## 版本資訊
- **新版本**: 5.7.6.2_1
- **舊版本**: 5.7.6.1
- **生成時間**: 2025-07-28 17:56:24
- **新增 Commit 數量**: 120

## 變更摘要

### waynechang (18 commits)

- **2019-08-15 09:42:24**: Q00-20190815001 修正/v1/process/invokeprocess發起流程的接口判斷表單數量異常
  - 變更檔案: 1 個
- **2019-08-14 11:58:55**: V00-20190807034 將右上角的其他設定、版本資訊設定為只有admin能使用
  - 變更檔案: 1 個
- **2019-08-08 14:08:05**: 修正T100發單有附件時，會造成Connection持續被占住
  - 變更檔案: 1 個
- **2019-08-08 10:30:51**: V00-20190807020 調整維護樣板，當回傳的status不等於200時，提示錯誤訊息
  - 變更檔案: 4 個
- **2019-08-06 16:07:15**: A00-20190730006 取消舊版SyncISO文件同步方法
  - 變更檔案: 1 個
- **2019-08-06 16:02:26**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-06 16:01:55**: Q00-20190806004 調整FormInstance的configureFieldValue,判斷是否要一併調整txt屬性的值
  - 變更檔案: 1 個
- **2019-07-25 09:32:17**: 補上hibernate.jar的classpath
  - 變更檔案: 1 個
- **2019-07-19 17:15:01**: A00-20190718004 修正多人處理關卡，每個人都要簽核，若其中有轉派記錄時，則須判斷是否為同一個處理者處理，避免造成轉派記錄錯誤
  - 變更檔案: 1 個
- **2019-07-18 14:38:56**: Q00-*********** 增加 ISO舊表單能在V58轉呼叫V57站台的服務
  - 變更檔案: 12 個
- **2019-07-17 17:06:46**: Q00-20190717002 排除透過RMI呼叫時無法取得hibetnate的class
  - 變更檔案: 3 個
- **2019-07-17 16:47:20**: Q00-20190717001 修正ISO自動編碼規則判斷邏輯
  - 變更檔案: 3 個
- **2019-07-12 10:56:45**: A00-20190711002 調整獨立模組產生連結的url
  - 變更檔案: 1 個
- **2019-07-11 09:43:27**: A00-20190709004 調整排程-關卡往下派送的服務(調整為一個小時前的關卡才允許自動往下派送，避免發生多筆同時處理同一個關卡)
  - 變更檔案: 1 個
- **2019-07-10 15:06:29**: Q00-20190710001 調整verifyaccesstokenForMDO
  - 變更檔案: 2 個
- **2019-07-10 15:43:50**: Q00-20190704004 調整外部模組透過url登入BPM的畫面
  - 變更檔案: 2 個
- **2019-07-03 16:43:38**: Q00-20190703003 將文件總管的修改文件屬性的保存年限調整為保存日期。
  - 變更檔案: 3 個
- **2019-06-27 13:53:49**: A00-20190513001 修正ISO排程dailyJob當制定單位設定成群組時會報錯
  - 變更檔案: 1 個

### walter_wu (19 commits)

- **2019-08-14 19:49:30**: 補修正<第二次>S00-20190528002 修正原本取得流程種類(XPDL/BPMN)的寫法在當前是核決關卡有異常
  - 變更檔案: 1 個
- **2019-08-14 14:04:38**: C01-20190627001 調整增加BPMN流程的詳細資訊顯示流程狀態
  - 變更檔案: 1 個
- **2019-08-14 11:40:38**: A00-20190813002 修正設定檔key=TodoWorkItemOrderBy的內容導致，點右上角代辦出現異常
  - 變更檔案: 1 個
- **2019-08-08 16:30:41**: 更新 e10流程範本 新增取得附件的Invoke關卡
  - 變更檔案: 1 個
- **2019-08-08 15:51:31**: A00-20190719001 修正樹狀開窗如果部門名稱不是英文或數字 在IE會查不出部門內人員
  - 變更檔案: 1 個
- **2019-08-08 11:08:57**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-08 11:08:12**: 補修正<第二次>Q00-20190802002 修正前一筆修改的邏輯錯誤
  - 變更檔案: 1 個
- **2019-08-07 16:18:18**: 增加debug印出呼叫回寫T100時 T100所回傳的資訊
  - 變更檔案: 1 個
- **2019-08-05 18:35:31**: Q00-20190802002 修正Invoke呼叫restful時使用過期的token驗證  導致無法呼叫失敗
  - 變更檔案: 1 個
- **2019-08-05 17:57:05**: S00-*********** 修正formInstance 裡fieldValue的attachment Tag加入位置錯誤
  - 變更檔案: 1 個
- **2019-08-05 17:28:40**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-05 17:28:09**: 調整/v1/process/abort接口 撤銷流程失敗的回傳訊息 加上實際錯誤內容
  - 變更檔案: 1 個
- **2019-08-01 09:17:04**: Q00-20190801001 公司私有雲增加支持Nutanix AHV 平台
  - 變更檔案: 1 個
- **2019-07-31 19:08:26**: 增加DML註解
  - 變更檔案: 2 個
- **2019-07-31 19:06:36**: S00-*********** E10發單新增附件
  - 變更檔案: 17 個
- **2019-07-30 18:22:47**: C01-20190730007 處理將簽合歷程轉為JSON時，因為通知關卡處理者為空導致解析錯誤
  - 變更檔案: 1 個
- **2019-07-26 11:33:59**: S00-20190528002 改善BPMN流程效能 加快代辦點單筆畫面載入速度 如果是BPMN流程忽略JGO畫圖
  - 變更檔案: 2 個
- **2019-07-26 11:06:52**: C01-*********** 發現gDialog_overlay會影響IE會導致絕對位置列印的排版跑掉
  - 變更檔案: 1 個
- **2019-07-04 19:07:36**: C01-20190520001 修正查找問題時發現的標準差計算不正確
  - 變更檔案: 1 個

### pinchi_lin (17 commits)

- **2019-08-14 16:23:29**: 調整多餘空白與換行
  - 變更檔案: 1 個
- **2019-08-12 17:33:47**: 修正移動授權中間層管理畫面
  - 變更檔案: 3 個
- **2019-08-09 17:12:19**: 修正移動的待辦與追蹤用listReader會有Connection沒釋放的問題
  - 變更檔案: 2 個
- **2019-08-08 21:03:43**: 調整移動授權中間層連線資訊維護頁面
  - 變更檔案: 7 個
- **2019-08-07 17:44:13**: Q00-20190807006 修正行動版相對位置表單在grid或subTab元件新增後會無法拖曳欄位樣板進去問題
  - 變更檔案: 2 個
- **2019-08-07 16:54:13**: A00-20190806001 修正響應式表單中新增頁籤後點選確定但頁籤設定視窗不會關閉的問題
  - 變更檔案: 1 個
- **2019-07-31 14:58:52**: Q00-20190718002 修正腳本樣板內容錯誤
  - 變更檔案: 3 個
- **2019-07-25 14:02:03**: Q00-20190725001 修正IMG在關卡為多人處理且有設定工作執行率時上一關卡資訊會顯示異常問題
  - 變更檔案: 1 個
- **2019-07-25 10:56:24**: Q00-20190722002 調整撥打給上一關處理人的多語系
  - 變更檔案: 2 個
- **2019-07-24 17:24:27**: Q00-20190718005 修正會辦流程在IMG快簽中仍會顯示終止流程選項的問題
  - 變更檔案: 1 個
- **2019-07-23 18:34:03**: Q00-20190701002 修正智能待辦排程會因使用者無工作行事曆導致排成執行失敗的問題
  - 變更檔案: 3 個
- **2019-07-23 13:55:19**: Q00-20190718004 修正關注按鈕在已結案流程不會顯示問題
  - 變更檔案: 2 個
- **2019-07-18 18:27:47**: Q00-*********** 多新增delegate方法來接只取數量的方法
  - 變更檔案: 5 個
- **2019-07-18 18:12:22**: Q00-*********** 還原被覆蓋的地方
  - 變更檔案: 1 個
- **2019-07-16 15:35:37**: S00-20190307001 新增在IMG中在連續簽核時保留列表所選篩選條件並且會取下筆而非最新流程
  - 變更檔案: 13 個
- **2019-07-15 20:35:35**: Q00-20190704001 補上漏改部分
  - 變更檔案: 1 個
- **2019-07-15 20:31:05**: Q00-20190704001 修正IMG用詳情作連續簽核時，進入下一筆的表單畫面會一片空白問題
  - 變更檔案: 3 個

### yamiyeh10 (27 commits)

- **2019-08-12 17:00:08**: Q00-20190812003 修正移動端儲存表單後選擇繼續留在表單畫面時未顯示遮罩導致可以再次被點擊
  - 變更檔案: 2 個
- **2019-08-12 16:54:08**: Q00-20190812002 修正IMG在儲存表單時原本為disable的元件會被打開可填寫
  - 變更檔案: 1 個
- **2019-08-09 16:10:09**: 修正移動端在取得通知筆數會有Connection沒釋放問題
  - 變更檔案: 1 個
- **2019-08-08 16:34:41**: C01-20190808005 二次修正企業微信2.8.10版本移動端的Grid顯示異常
  - 變更檔案: 1 個
- **2019-08-08 11:22:05**: V00-20190807035 修正移動端客製開窗與產品開窗先勾選後查詢時已勾選人員的勾選未打勾
  - 變更檔案: 2 個
- **2019-08-08 10:09:44**: V00-20190807007 修正入口平台整合設定的統計元件欄位中智能快簽多語系異常
  - 變更檔案: 2 個
- **2019-08-07 19:25:40**: Q00-20190807002 修正移動端Grid元件資料展開畫面跑版
  - 變更檔案: 1 個
- **2019-08-07 19:17:13**: Q00-20190807003 修正客製開窗在資料為一行時圖式跑版
  - 變更檔案: 2 個
- **2019-08-07 18:51:16**: 調整移動授權中間層使用者維護頁面
  - 變更檔案: 4 個
- **2019-08-07 16:44:35**: Q00-20190807004 調整智慧待辦的排程log層級
  - 變更檔案: 1 個
- **2019-08-07 16:36:40**: Q00-20190807005 修正移動授權中間層使用者維護頁面
  - 變更檔案: 3 個
- **2019-08-06 11:15:20**: C01-20190802003 修正企業微信2.8.10版本使用iOS手機操作待辦與通知畫面時出現異常訊息
  - 變更檔案: 3 個
- **2019-07-25 19:02:30**: 修正移動端查看ESS表單附件時會卡控表單名稱
  - 變更檔案: 1 個
- **2019-07-25 18:11:30**: Q00-20190725008 修正移動授權中間層的連線資訊管理會有別名相同問題
  - 變更檔案: 1 個
- **2019-07-25 11:27:12**: Q00-20190722004 調整預測關卡計算的時間機制
  - 變更檔案: 2 個
- **2019-07-25 10:49:21**: Q00-20190702002 修正RWD設計器轉換出的行動表單發生派送後無Grild資料問題
  - 變更檔案: 1 個
- **2019-07-24 10:53:12**: Q00-20190705001 修正移動端Grid內容不會清除
  - 變更檔案: 2 個
- **2019-07-23 16:31:23**: Q00-20190718006 調整智能學習管理中心頁面偏移值
  - 變更檔案: 3 個
- **2019-07-23 16:25:08**: Q00-20190703002 修正使用Android點選產品開窗時畫面會滑動
  - 變更檔案: 1 個
- **2019-07-22 17:12:24**: Q00-20190704003 修正企業微信查看附件會保留上一次查看的位置
  - 變更檔案: 10 個
- **2019-07-22 17:06:19**: Q00-20190627003 修正企業微信從菜單進入待辦列表上方的篩選字樣顯示undefined
  - 變更檔案: 1 個
- **2019-07-22 15:35:41**: Q00-20190627002 企業微信編輯我的最愛流程星號顯示機制
  - 變更檔案: 1 個
- **2019-07-18 16:56:13**: C01-20190705001 移動端預解析關卡功能僅支援簽核流程設計師的流程
  - 變更檔案: 1 個
- **2019-07-17 11:25:38**: 調整移動端支持電腦版企業微信與瀏覽器異常功能與畫面
  - 變更檔案: 11 個
- **2019-07-16 18:34:45**: 修正移動端預覽表單畫面功能
  - 變更檔案: 8 個
- **2019-07-15 10:00:45**: 調整移動端支持電腦版企業微信與瀏覽器異常功能與畫面
  - 變更檔案: 25 個
- **2019-07-03 14:20:32**: C01-20190702004 修正移動端客製開窗使用模糊查詢時會顯示對資料庫執行SQL查詢指令失敗問題
  - 變更檔案: 1 個

### Catherine (11 commits)

- **2019-08-08 12:16:40**: A00-20190805002 Checkbox語法調整
  - 變更檔案: 1 個
- **2019-08-08 11:31:53**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-08-08 11:31:06**: A00-20190805002 依據交付提供語法調整
  - 變更檔案: 1 個
- **2019-08-07 19:15:05**: A00-20190807002 移除文件
  - 變更檔案: 1 個
- **2019-08-02 13:47:59**: A00-20190724002 調整
  - 變更檔案: 3 個
- **2019-07-24 11:10:00**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-07-24 11:01:46**: A00-20190723003 指令錯誤更正
  - 變更檔案: 1 個
- **2019-07-18 18:09:46**: C01-*********** 在地化server2修正智能示警漏了tIconDesc
  - 變更檔案: 1 個
- **2019-06-26 19:19:16**: A00-20190626001 多語系縮減調整
  - 變更檔案: 2 個
- **2019-06-26 14:55:43**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-06-26 14:55:11**: A00-20190626001 多語系縮減調整
  - 變更檔案: 2 個

### yanann_chen (12 commits)

- **2019-08-08 09:43:17**: Q00-20190807007 修正英文列印表單失效
  - 變更檔案: 1 個
- **2019-08-07 13:46:06**: Q00-20190807001 修正英文待辦事項標題未正常顯示
  - 變更檔案: 2 個
- **2019-08-07 09:37:06**: A00-20190403001補修正
  - 變更檔案: 1 個
- **2019-08-07 09:31:51**: A00-20190403001 HR組織同步增加throws Exception
  - 變更檔案: 2 個
- **2019-07-29 13:40:57**: A00-20190708001 修正bpm-bootstrap-util.js出現錯誤時，表單script無作用
  - 變更檔案: 1 個
- **2019-07-29 11:09:34**: A00-20190709002 A00-20190710004 修正FormUtil失效問題
  - 變更檔案: 1 個
- **2019-07-24 17:05:35**: C01-20190712001 修正FormUtil設定Button元件樣式無效
  - 變更檔案: 1 個
- **2019-07-11 16:09:21**: C01-20190311004 修正人員任務排版錯亂
  - 變更檔案: 1 個
- **2019-07-11 11:06:50**: A00-20190710003 修正人員任務進階設定的多語系
  - 變更檔案: 3 個
- **2019-07-05 10:29:06**: A00-20190703001 bpm-tools.exe英文拼字錯誤
  - 變更檔案: 1 個
- **2019-07-04 16:05:18**: C01-20190523003 修正"流程中若有核決層級關卡，且退回的關卡設有自動簽核，當處理者重複時會觸發退回的關卡執行自動簽核"的問題
  - 變更檔案: 1 個
- **2019-06-26 14:44:50**: C01-20190617004 修正使用者表單設計師查詢失效
  - 變更檔案: 1 個

### gaspard.shih (2 commits)

- **2019-08-01 11:17:57**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
  - 變更檔案: 1 個
- **2019-08-01 11:17:08**: 在地化-於BPM首頁強制顯示兩台主機的待辦項目ICON、標題列增加當前主機描述、移除判斷用戶設定僅顯示哪幾台主機ICON的功能
  - 變更檔案: 2 個

### jerry1218 (9 commits)

- **2019-07-25 11:16:22**: V00-20190717001 修改server cache同步邏輯-WorkflowServer AppServerAddress沒有值的就不同步
  - 變更檔案: 1 個
- **2019-07-18 16:46:50**: InUse Conn未釋放問題處理
  - 變更檔案: 10 個
- **2019-07-18 09:47:19**: 刪除多餘的system.out.println
  - 變更檔案: 1 個
- **2019-07-18 09:37:08**: Q00-20190718001 修正智能計算排程-計算流程模型權重會導致conn不會釋放
  - 變更檔案: 1 個
- **2019-07-09 10:03:23**: V00-20190624012 修正IE瀏覽器-個人化首頁連結至待辦清單按鈕,按下後無法導頁問題
  - 變更檔案: 2 個
- **2019-07-05 13:42:38**: V00-20190703003 修正系統管理工具測試寄信失敗問題
  - 變更檔案: 1 個
- **2019-07-01 11:11:54**: V00-20190626004 (第二次修正)修正組織設計師-修改員工資料-設定所屬單位-部門 , 完全沒選擇主部門也可儲存問題
  - 變更檔案: 1 個
- **2019-07-01 10:51:58**: V00-20190624004-修正TT整合設定無論成功失敗皆會顯示successful
  - 變更檔案: 1 個
- **2019-06-27 15:36:20**: V00-20190626004 修正組織設計師-修改員工資料-設定所屬單位-部門 , 完全沒選擇主部門也可儲存問題
  - 變更檔案: 1 個

### BPM (5 commits)

- **2019-07-18 16:06:31**: Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **2019-07-18 15:47:37**: Q00***********-在56發現待辦未結案率的統計圖會因資料量大而沒有回應，已調整成只取數量回來做計算。
  - 變更檔案: 2 個
- **2019-07-18 15:20:22**: Q0020190703004 企業微信開啟附件編碼為簡中時出現亂碼問題
  - 變更檔案: 2 個
- **2019-07-18 14:51:57**: Q0020190703005檢視PDF檔案時會模糊不清晰問題(企業微信)
  - 變更檔案: 1 個
- **2019-07-18 14:34:18**: Q0020190718002 修正腳本樣版更新與法
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. Q00-20190815001 修正/v1/process/invokeprocess發起流程的接口判斷表單數量異常
- **Commit ID**: `df2db71eeafa5c9ed76fbf10b3958fd6637d0d26`
- **作者**: waynechang
- **日期**: 2019-08-15 09:42:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 2. 補修正<第二次>S00-20190528002 修正原本取得流程種類(XPDL/BPMN)的寫法在當前是核決關卡有異常
- **Commit ID**: `a0e8b560788119396d5b87704b3d00841468586a`
- **作者**: walter_wu
- **日期**: 2019-08-14 19:49:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 3. 調整多餘空白與換行
- **Commit ID**: `30f5b7e8dbae02517ec251f4e236fc741d71d9bc`
- **作者**: pinchi_lin
- **日期**: 2019-08-14 16:23:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 4. C01-20190627001 調整增加BPMN流程的詳細資訊顯示流程狀態
- **Commit ID**: `c9a14d1dbebfa468f68584c127ac4133956f2a00`
- **作者**: walter_wu
- **日期**: 2019-08-14 14:04:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`

### 5. V00-20190807034 將右上角的其他設定、版本資訊設定為只有admin能使用
- **Commit ID**: `32e303a9191c3b7c763c27efe54bdc9b890f3ef0`
- **作者**: waynechang
- **日期**: 2019-08-14 11:58:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp`

### 6. A00-20190813002 修正設定檔key=TodoWorkItemOrderBy的內容導致，點右上角代辦出現異常
- **Commit ID**: `b25a50b0aa22da46b52b1932525ee20727aceb47`
- **作者**: walter_wu
- **日期**: 2019-08-14 11:40:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 7. 修正移動授權中間層管理畫面
- **Commit ID**: `8e3f714d33748810b87a0589fed6c67218153638`
- **作者**: pinchi_lin
- **日期**: 2019-08-12 17:33:47
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js`

### 8. Q00-20190812003 修正移動端儲存表單後選擇繼續留在表單畫面時未顯示遮罩導致可以再次被點擊
- **Commit ID**: `6d828a6e7b8db9769d68122e4ee18a9586ba779e`
- **作者**: yamiyeh10
- **日期**: 2019-08-12 17:00:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 9. Q00-20190812002 修正IMG在儲存表單時原本為disable的元件會被打開可填寫
- **Commit ID**: `c675e5b729b575b471cf829750c8fb2d958d2f11`
- **作者**: yamiyeh10
- **日期**: 2019-08-12 16:54:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 10. 修正移動的待辦與追蹤用listReader會有Connection沒釋放的問題
- **Commit ID**: `d567c1ddde2b2d0443ad53b019a9c397abad4b6b`
- **作者**: pinchi_lin
- **日期**: 2019-08-09 17:12:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`

### 11. 修正移動端在取得通知筆數會有Connection沒釋放問題
- **Commit ID**: `9fa4726e6e8182f1d5764afc8f8120c31a6ae2ca`
- **作者**: yamiyeh10
- **日期**: 2019-08-09 16:10:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java`

### 12. 調整移動授權中間層連線資訊維護頁面
- **Commit ID**: `606a2e10164256733ce4e068bb6537b51947b8e2`
- **作者**: pinchi_lin
- **日期**: 2019-08-08 21:03:43
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/Common.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 13. C01-20190808005 二次修正企業微信2.8.10版本移動端的Grid顯示異常
- **Commit ID**: `710b053d29058ab915373e66760e4eb09487026c`
- **作者**: yamiyeh10
- **日期**: 2019-08-08 16:34:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 14. 更新 e10流程範本 新增取得附件的Invoke關卡
- **Commit ID**: `13445c73732ec7469647f93179c9084983a41be7`
- **作者**: walter_wu
- **日期**: 2019-08-08 16:30:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\346\265\201\347\250\213\347\257\204\346\234\254.bpmn"`

### 15. A00-20190719001 修正樹狀開窗如果部門名稱不是英文或數字 在IE會查不出部門內人員
- **Commit ID**: `be90b79fc00be5246a6bb6aed5d0f12251f29eaa`
- **作者**: walter_wu
- **日期**: 2019-08-08 15:51:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`

### 16. 修正T100發單有附件時，會造成Connection持續被占住
- **Commit ID**: `b9330e4e65e85b4cd804efde77353b1d06cda3b6`
- **作者**: waynechang
- **日期**: 2019-08-08 14:08:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 17. A00-20190805002 Checkbox語法調整
- **Commit ID**: `3fe456da800b240de976d348846fdf2cde051797`
- **作者**: Catherine
- **日期**: 2019-08-08 12:16:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`

### 18. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `9a976fe3fe9efb513961418d50114cf408da1b94`
- **作者**: Catherine
- **日期**: 2019-08-08 11:31:53
- **變更檔案數量**: 0

### 19. A00-20190805002 依據交付提供語法調整
- **Commit ID**: `e11ed8b64906388a50edf542a1370e72ab64241d`
- **作者**: Catherine
- **日期**: 2019-08-08 11:31:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`

### 20. V00-20190807035 修正移動端客製開窗與產品開窗先勾選後查詢時已勾選人員的勾選未打勾
- **Commit ID**: `ddfba3ada0874e2d9a3111b47abac8a1daae4580`
- **作者**: yamiyeh10
- **日期**: 2019-08-08 11:22:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 21. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `5091e868f263604fdebd36f8b77baf05db3c630e`
- **作者**: walter_wu
- **日期**: 2019-08-08 11:08:57
- **變更檔案數量**: 0

### 22. 補修正<第二次>Q00-20190802002 修正前一筆修改的邏輯錯誤
- **Commit ID**: `9146c1563a967379e15c71a6476fe1db5d9f244b`
- **作者**: walter_wu
- **日期**: 2019-08-08 11:08:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java`

### 23. V00-20190807020 調整維護樣板，當回傳的status不等於200時，提示錯誤訊息
- **Commit ID**: `c56a779cfe49754e8018e8172172882bdb5829fa`
- **作者**: waynechang
- **日期**: 2019-08-08 10:30:51
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_en_US.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_zh_CN.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_zh_TW.js`

### 24. V00-20190807007 修正入口平台整合設定的統計元件欄位中智能快簽多語系異常
- **Commit ID**: `f67195db32c8eb56391f562bed283a5a160123e1`
- **作者**: yamiyeh10
- **日期**: 2019-08-08 10:09:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 25. Q00-20190807007 修正英文列印表單失效
- **Commit ID**: `0e5040459076c812ad194e2161ca9daf54bcc92f`
- **作者**: yanann_chen
- **日期**: 2019-08-08 09:43:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 26. Q00-20190807002 修正移動端Grid元件資料展開畫面跑版
- **Commit ID**: `ea9081434cd530c723c3815242e74e2e0111b83a`
- **作者**: yamiyeh10
- **日期**: 2019-08-07 19:25:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 27. Q00-20190807003 修正客製開窗在資料為一行時圖式跑版
- **Commit ID**: `cebaf16d3ee36e05af8db80d880487528bc5cf99`
- **作者**: yamiyeh10
- **日期**: 2019-08-07 19:17:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`

### 28. A00-20190807002 移除文件
- **Commit ID**: `d3e42926dc3f30c70994eb9579a815dcd5828102`
- **作者**: Catherine
- **日期**: 2019-08-07 19:15:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form_\346\226\274\345\205\247\351\203\250\344\270\273\346\251\237\344\270\213\350\274\211\346\234\200\346\226\260\347\211\210.txt"`

### 29. 調整移動授權中間層使用者維護頁面
- **Commit ID**: `bf90d771d63e91f8f0c556c8c81cca9289795e0d`
- **作者**: yamiyeh10
- **日期**: 2019-08-07 18:51:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 30. Q00-20190807006 修正行動版相對位置表單在grid或subTab元件新增後會無法拖曳欄位樣板進去問題
- **Commit ID**: `50de7191cb9525566dfcc968d9b69ad350456f7f`
- **作者**: pinchi_lin
- **日期**: 2019-08-07 17:44:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 31. A00-20190806001 修正響應式表單中新增頁籤後點選確定但頁籤設定視窗不會關閉的問題
- **Commit ID**: `61443cdc250d57bc1a565c8661c32acd12d99ebf`
- **作者**: pinchi_lin
- **日期**: 2019-08-07 16:54:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`

### 32. Q00-20190807004 調整智慧待辦的排程log層級
- **Commit ID**: `d528056547d93dc650ed4ea3cf22972e683d6110`
- **作者**: yamiyeh10
- **日期**: 2019-08-07 16:44:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java`

### 33. Q00-20190807005 修正移動授權中間層使用者維護頁面
- **Commit ID**: `05197706a56d8b6f57624535ac8dfcd29a74b84d`
- **作者**: yamiyeh10
- **日期**: 2019-08-07 16:36:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 34. 增加debug印出呼叫回寫T100時 T100所回傳的資訊
- **Commit ID**: `2d810496fea9d5e8003ffe2e184dbd8a11754614`
- **作者**: walter_wu
- **日期**: 2019-08-07 16:18:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`

### 35. Q00-20190807001 修正英文待辦事項標題未正常顯示
- **Commit ID**: `524ac66eb2cd955a8ab2c3435acc5af46474ab95`
- **作者**: yanann_chen
- **日期**: 2019-08-07 13:46:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 36. A00-20190403001補修正
- **Commit ID**: `d55acdcbe79597dc851f8e592eccfceae52c2b22`
- **作者**: yanann_chen
- **日期**: 2019-08-07 09:37:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 37. A00-20190403001 HR組織同步增加throws Exception
- **Commit ID**: `2d68411b95494640d87f46baa9af10f40f404bc7`
- **作者**: yanann_chen
- **日期**: 2019-08-07 09:31:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java`

### 38. A00-20190730006 取消舊版SyncISO文件同步方法
- **Commit ID**: `0b65f81f71748a9bc1581db332c745bf49554048`
- **作者**: waynechang
- **日期**: 2019-08-06 16:07:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerBean.java`

### 39. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `301c95077f7f99b5faad96e23a62270ed77b0be6`
- **作者**: waynechang
- **日期**: 2019-08-06 16:02:26
- **變更檔案數量**: 0

### 40. Q00-20190806004 調整FormInstance的configureFieldValue,判斷是否要一併調整txt屬性的值
- **Commit ID**: `a66a15f59793a7c74404c2cf0cc90625a874d804`
- **作者**: waynechang
- **日期**: 2019-08-06 16:01:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`

### 41. C01-20190802003 修正企業微信2.8.10版本使用iOS手機操作待辦與通知畫面時出現異常訊息
- **Commit ID**: `420f651654303ac3d620d91514dfdb41b772ba2f`
- **作者**: yamiyeh10
- **日期**: 2019-08-06 11:15:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileTool.js`

### 42. Q00-20190802002 修正Invoke呼叫restful時使用過期的token驗證  導致無法呼叫失敗
- **Commit ID**: `75fffcab2eb2d8ed6f52b70b41fe517f71914b1d`
- **作者**: walter_wu
- **日期**: 2019-08-05 18:35:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java`

### 43. S00-*********** 修正formInstance 裡fieldValue的attachment Tag加入位置錯誤
- **Commit ID**: `6e9cc9664bf2520e31373273f8a425758a9b5d24`
- **作者**: walter_wu
- **日期**: 2019-08-05 17:57:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 44. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a2b8872651410dc82ef98ab1010326f1b6a9b978`
- **作者**: walter_wu
- **日期**: 2019-08-05 17:28:40
- **變更檔案數量**: 0

### 45. 調整/v1/process/abort接口 撤銷流程失敗的回傳訊息 加上實際錯誤內容
- **Commit ID**: `fb004cda6807ee846baaee3252489053eb60cc0e`
- **作者**: walter_wu
- **日期**: 2019-08-05 17:28:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`

### 46. A00-20190724002 調整
- **Commit ID**: `20ad8b45c975ef6464ceefc9eed7c010d3cabc05`
- **作者**: Catherine
- **日期**: 2019-08-02 13:47:59
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 47. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `a9061e96dd9b918ba2a4a8d071d9b8afa1e21486`
- **作者**: gaspard.shih
- **日期**: 2019-08-01 11:17:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 48. 在地化-於BPM首頁強制顯示兩台主機的待辦項目ICON、標題列增加當前主機描述、移除判斷用戶設定僅顯示哪幾台主機ICON的功能
- **Commit ID**: `f020dc3ff6a9b40a31d17590a1d16e2f6be938a3`
- **作者**: gaspard.shih
- **日期**: 2019-08-01 11:17:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 49. Q00-20190801001 公司私有雲增加支持Nutanix AHV 平台
- **Commit ID**: `c74cde7e83a9856254daeca6d31a45fe66a810fb`
- **作者**: walter_wu
- **日期**: 2019-08-01 09:17:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/GuardServiceUtil.java`

### 50. 增加DML註解
- **Commit ID**: `194546433902ade3575570a6bd976b5d3d54d995`
- **作者**: walter_wu
- **日期**: 2019-07-31 19:08:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_Oracle_1.sql`

### 51. S00-*********** E10發單新增附件
- **Commit ID**: `cb1067127c526f8c3417e2ae0e470ff369cefbc0`
- **作者**: walter_wu
- **日期**: 2019-07-31 19:06:36
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/DocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/ProcessMappingKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AttachmentsReq.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/DocumentBeanReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeProcessFormDataReq.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DDL_Oracle_1.sql`

### 52. Q00-20190718002 修正腳本樣板內容錯誤
- **Commit ID**: `648543587a43b2287ab28bd9355e00b717852af3`
- **作者**: pinchi_lin
- **日期**: 2019-07-31 14:58:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_Oracle_1.sql`

### 53. C01-20190730007 處理將簽合歷程轉為JSON時，因為通知關卡處理者為空導致解析錯誤
- **Commit ID**: `68bb3136dca54386c438c1320131eb0a070e5774`
- **作者**: walter_wu
- **日期**: 2019-07-30 18:22:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 54. A00-20190708001 修正bpm-bootstrap-util.js出現錯誤時，表單script無作用
- **Commit ID**: `f9074fa8e64f7ccb85687528a566147c3d35e98e`
- **作者**: yanann_chen
- **日期**: 2019-07-29 13:40:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 55. A00-20190709002 A00-20190710004 修正FormUtil失效問題
- **Commit ID**: `6d0240382828e8357719bed2891955a6931aefc5`
- **作者**: yanann_chen
- **日期**: 2019-07-29 11:09:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 56. S00-20190528002 改善BPMN流程效能 加快代辦點單筆畫面載入速度 如果是BPMN流程忽略JGO畫圖
- **Commit ID**: `fc8a156bbd89f4a19b5f79045258c891fd971a62`
- **作者**: walter_wu
- **日期**: 2019-07-26 11:33:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 57. C01-*********** 發現gDialog_overlay會影響IE會導致絕對位置列印的排版跑掉
- **Commit ID**: `3ffd96850d2e92956aad18f98055f69870483101`
- **作者**: walter_wu
- **日期**: 2019-07-26 11:06:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 58. 修正移動端查看ESS表單附件時會卡控表單名稱
- **Commit ID**: `f9e645f174755ae72fe740b783bea77c31189928`
- **作者**: yamiyeh10
- **日期**: 2019-07-25 19:02:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`

### 59. Q00-20190725008 修正移動授權中間層的連線資訊管理會有別名相同問題
- **Commit ID**: `db7b5c1a3509301432219f5562ff6bb5c5b56ca0`
- **作者**: yamiyeh10
- **日期**: 2019-07-25 18:11:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js`

### 60. Q00-20190725001 修正IMG在關卡為多人處理且有設定工作執行率時上一關卡資訊會顯示異常問題
- **Commit ID**: `67d462ad67d5575edb6cafdd3b63a343365d4736`
- **作者**: pinchi_lin
- **日期**: 2019-07-25 14:02:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`

### 61. Q00-20190722004 調整預測關卡計算的時間機制
- **Commit ID**: `2a6affa3dc8b3ebdff3a0c0857595800e9614b6d`
- **作者**: yamiyeh10
- **日期**: 2019-07-25 11:27:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 62. V00-20190717001 修改server cache同步邏輯-WorkflowServer AppServerAddress沒有值的就不同步
- **Commit ID**: `50c84dc28ee253445af4562ffe62c6434c0c0f55`
- **作者**: jerry1218
- **日期**: 2019-07-25 11:16:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 63. Q00-20190722002 調整撥打給上一關處理人的多語系
- **Commit ID**: `9a7a1d0a488a82ceb7286ae70ceb5bba6bbdd006`
- **作者**: pinchi_lin
- **日期**: 2019-07-25 10:56:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 64. Q00-20190702002 修正RWD設計器轉換出的行動表單發生派送後無Grild資料問題
- **Commit ID**: `83df969a6ac05f01c0302385845c7d623392d182`
- **作者**: yamiyeh10
- **日期**: 2019-07-25 10:49:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 65. 補上hibernate.jar的classpath
- **Commit ID**: `9fb16294cf936befadd7fdb183a27a2cf1a59387`
- **作者**: waynechang
- **日期**: 2019-07-25 09:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/.classpath`

### 66. Q00-20190718005 修正會辦流程在IMG快簽中仍會顯示終止流程選項的問題
- **Commit ID**: `c386fe0f70b6d741bec56fe92e30a0a992a15180`
- **作者**: pinchi_lin
- **日期**: 2019-07-24 17:24:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`

### 67. C01-20190712001 修正FormUtil設定Button元件樣式無效
- **Commit ID**: `eac1301c7b8a8a8cd8b02eb0128a507cfcb4dd26`
- **作者**: yanann_chen
- **日期**: 2019-07-24 17:05:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 68. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `1f0afd80de42332c183e683c0d6c5a8515ccbf94`
- **作者**: Catherine
- **日期**: 2019-07-24 11:10:00
- **變更檔案數量**: 0

### 69. A00-20190723003 指令錯誤更正
- **Commit ID**: `533f01ecbeac03c57232e86eac9b5eefb7b2ee6a`
- **作者**: Catherine
- **日期**: 2019-07-24 11:01:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql`

### 70. Q00-20190705001 修正移動端Grid內容不會清除
- **Commit ID**: `c1050cfb5b111d24e6a607c8e1e9ae4affada739`
- **作者**: yamiyeh10
- **日期**: 2019-07-24 10:53:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 71. Q00-20190701002 修正智能待辦排程會因使用者無工作行事曆導致排成執行失敗的問題
- **Commit ID**: `954e0737321938026d8bcf5a34fd0febe3168cb7`
- **作者**: pinchi_lin
- **日期**: 2019-07-23 18:34:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java`

### 72. Q00-20190718006 調整智能學習管理中心頁面偏移值
- **Commit ID**: `0e6c7ff577e693081e3afd0ec4359fddd3c51811`
- **作者**: yamiyeh10
- **日期**: 2019-07-23 16:31:23
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 73. Q00-20190703002 修正使用Android點選產品開窗時畫面會滑動
- **Commit ID**: `26177f30e9482e74c181991896659ab3deb160e4`
- **作者**: yamiyeh10
- **日期**: 2019-07-23 16:25:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js`

### 74. Q00-20190718004 修正關注按鈕在已結案流程不會顯示問題
- **Commit ID**: `d82919a48984ade36ae665d78a40070f53f902ad`
- **作者**: pinchi_lin
- **日期**: 2019-07-23 13:55:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`

### 75. Q00-20190704003 修正企業微信查看附件會保留上一次查看的位置
- **Commit ID**: `fb0caa1e237d3bf935987104da2a02f6f4030b89`
- **作者**: yamiyeh10
- **日期**: 2019-07-22 17:12:24
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 76. Q00-20190627003 修正企業微信從菜單進入待辦列表上方的篩選字樣顯示undefined
- **Commit ID**: `ee2abfc94b8fd53e0d2535a36e5242302454450e`
- **作者**: yamiyeh10
- **日期**: 2019-07-22 17:06:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 77. Q00-20190627002 企業微信編輯我的最愛流程星號顯示機制
- **Commit ID**: `2c1ecd5614eceaa7ac76395585930bec323692bf`
- **作者**: yamiyeh10
- **日期**: 2019-07-22 15:35:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js`

### 78. A00-20190718004 修正多人處理關卡，每個人都要簽核，若其中有轉派記錄時，則須判斷是否為同一個處理者處理，避免造成轉派記錄錯誤
- **Commit ID**: `6e17255b694b7e4ae4050bd272b85f0af95bf868`
- **作者**: waynechang
- **日期**: 2019-07-19 17:15:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 79. Q00-*********** 多新增delegate方法來接只取數量的方法
- **Commit ID**: `ca58329c32ede6139dcd47719be9b8dcf5f5c7f1`
- **作者**: pinchi_lin
- **日期**: 2019-07-18 18:27:47
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java`

### 80. Q00-*********** 還原被覆蓋的地方
- **Commit ID**: `dcccdf1ab75b9f27739ec5312549850e561eb874`
- **作者**: pinchi_lin
- **日期**: 2019-07-18 18:12:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties`

### 81. C01-*********** 在地化server2修正智能示警漏了tIconDesc
- **Commit ID**: `4afd74e53a517dd1e71ea2d5f7496cb5bb6227cf`
- **作者**: Catherine
- **日期**: 2019-07-18 18:09:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 82. C01-20190705001 移動端預解析關卡功能僅支援簽核流程設計師的流程
- **Commit ID**: `e672e9a646110f1e1caba2cd41c966a912a0e606`
- **作者**: yamiyeh10
- **日期**: 2019-07-18 16:56:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`

### 83. InUse Conn未釋放問題處理
- **Commit ID**: `deec00d9041d68a12024b0a56e57548fbc11e124`
- **作者**: jerry1218
- **日期**: 2019-07-18 16:46:50
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/orgAnalyze/OrgAnalyzeManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/serviceRegister/ServiceRegisterBean.java`

### 84. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `35eca86bc8d78f3b228c1774cb25d9a1ef43ad5b`
- **作者**: BPM
- **日期**: 2019-07-18 16:06:31
- **變更檔案數量**: 0

### 85. Q00***********-在56發現待辦未結案率的統計圖會因資料量大而沒有回應，已調整成只取數量回來做計算。
- **Commit ID**: `7af7a5f1e9333c2e298f64767b10fc2c750a0808`
- **作者**: BPM
- **日期**: 2019-07-18 15:47:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`

### 86. Q0020190703004 企業微信開啟附件編碼為簡中時出現亂碼問題
- **Commit ID**: `c5a15d1ea3d35c3827d5ffd6d9cdc8e09c21c0ea`
- **作者**: BPM
- **日期**: 2019-07-18 15:20:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/FileManager.java`

### 87. Q0020190703005檢視PDF檔案時會模糊不清晰問題(企業微信)
- **Commit ID**: `031648b37392807db303188cbb0b69ddf0ac8162`
- **作者**: BPM
- **日期**: 2019-07-18 14:51:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/pdfJs/pdf-bpm.js`

### 88. Q00-*********** 增加 ISO舊表單能在V58轉呼叫V57站台的服務
- **Commit ID**: `df7606d6838156cdb7e62a75da6df9cccd32abf5`
- **作者**: waynechang
- **日期**: 2019-07-18 14:38:56
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISORemoteDocManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISORemoteListHandlerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISORemotePageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IISOPageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ISODocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ISOPageListReaderImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`

### 89. Q0020190718002 修正腳本樣版更新與法
- **Commit ID**: `6deb3c79bf49607b89d87ec6e7914d77fef5ffee`
- **作者**: BPM
- **日期**: 2019-07-18 14:34:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql`

### 90. 刪除多餘的system.out.println
- **Commit ID**: `e5f206202ebc1ca4ca9dfe4056c67c602092df79`
- **作者**: jerry1218
- **日期**: 2019-07-18 09:47:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`

### 91. Q00-20190718001 修正智能計算排程-計算流程模型權重會導致conn不會釋放
- **Commit ID**: `1abc8eb4dba7d83245076aef980f71ec24e90305`
- **作者**: jerry1218
- **日期**: 2019-07-18 09:37:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java`

### 92. Q00-20190717002 排除透過RMI呼叫時無法取得hibetnate的class
- **Commit ID**: `468bfb7dfb5ed62d48c1c23d45ca06d837e49302`
- **作者**: waynechang
- **日期**: 2019-07-17 17:06:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/lib/Hibernate/hibernate.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/Hibernate/hibernate3.jar`

### 93. Q00-20190717001 修正ISO自動編碼規則判斷邏輯
- **Commit ID**: `b2325e5cb5451729d788f72bc9a5be0efc45b09d`
- **作者**: waynechang
- **日期**: 2019-07-17 16:47:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocSnGenerator.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/hibernate/SnRuleDaoImpl.java`

### 94. 調整移動端支持電腦版企業微信與瀏覽器異常功能與畫面
- **Commit ID**: `95df42a15b4d86c8981db27fc6a8ae2dc9564fa8`
- **作者**: yamiyeh10
- **日期**: 2019-07-17 11:25:38
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 95. 修正移動端預覽表單畫面功能
- **Commit ID**: `0fb127ccd1e8476e997a522d6b31a1f9ea45ffc4`
- **作者**: yamiyeh10
- **日期**: 2019-07-16 18:34:45
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 96. S00-20190307001 新增在IMG中在連續簽核時保留列表所選篩選條件並且會取下筆而非最新流程
- **Commit ID**: `93810034333d2c7c1f91efd58669f7e69b3cc27c`
- **作者**: pinchi_lin
- **日期**: 2019-07-16 15:35:37
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 97. Q00-20190704001 補上漏改部分
- **Commit ID**: `cc69bf2fd12e2c777d4196e44ec950e323849ae8`
- **作者**: pinchi_lin
- **日期**: 2019-07-15 20:35:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`

### 98. Q00-20190704001 修正IMG用詳情作連續簽核時，進入下一筆的表單畫面會一片空白問題
- **Commit ID**: `fb335ec28c08fbba903101817288fe4e243e8786`
- **作者**: pinchi_lin
- **日期**: 2019-07-15 20:31:05
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`

### 99. 調整移動端支持電腦版企業微信與瀏覽器異常功能與畫面
- **Commit ID**: `ae9c52156e83d8920450fad4aa117a37fec657cf`
- **作者**: yamiyeh10
- **日期**: 2019-07-15 10:00:45
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css`

### 100. A00-20190711002 調整獨立模組產生連結的url
- **Commit ID**: `101f12404b069f310aad68b095035a96fe96cbb1`
- **作者**: waynechang
- **日期**: 2019-07-12 10:56:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java`

### 101. C01-20190311004 修正人員任務排版錯亂
- **Commit ID**: `82b6adb419fd7026b3d31fdc019022dd36f0c1a4`
- **作者**: yanann_chen
- **日期**: 2019-07-11 16:09:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`

### 102. A00-20190710003 修正人員任務進階設定的多語系
- **Commit ID**: `157ea298e6e115235fe36884810e380a37ef67ef`
- **作者**: yanann_chen
- **日期**: 2019-07-11 11:06:50
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_TW.properties`

### 103. A00-20190709004 調整排程-關卡往下派送的服務(調整為一個小時前的關卡才允許自動往下派送，避免發生多筆同時處理同一個關卡)
- **Commit ID**: `7e9e15cf1b07901cfd9cc669902a22efc182f5ac`
- **作者**: waynechang
- **日期**: 2019-07-11 09:43:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 104. Q00-20190710001 調整verifyaccesstokenForMDO
- **Commit ID**: `1213869390cdca82848d7c2f0f9aaee8c11e9a5d`
- **作者**: waynechang
- **日期**: 2019-07-10 15:06:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java`

### 105. V00-20190624012 修正IE瀏覽器-個人化首頁連結至待辦清單按鈕,按下後無法導頁問題
- **Commit ID**: `0474a9d0e6814034e3d671201e431f94761adcac`
- **作者**: jerry1218
- **日期**: 2019-07-09 10:03:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 106. Q00-20190704004 調整外部模組透過url登入BPM的畫面
- **Commit ID**: `a49b921db1c0ce51fc7f96240acd689ada13d04a`
- **作者**: waynechang
- **日期**: 2019-07-10 15:43:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`

### 107. V00-20190703003 修正系統管理工具測試寄信失敗問題
- **Commit ID**: `264765f21bd05376a8b012bf5c3098f43a4ae850`
- **作者**: jerry1218
- **日期**: 2019-07-05 13:42:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`

### 108. A00-20190703001 bpm-tools.exe英文拼字錯誤
- **Commit ID**: `3daa2892b3bee06b743d57eb119377af225a8bc6`
- **作者**: yanann_chen
- **日期**: 2019-07-05 10:29:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java`

### 109. C01-20190520001 修正查找問題時發現的標準差計算不正確
- **Commit ID**: `e1f96f7b1e447499d82529ba42406503c91fc63a`
- **作者**: walter_wu
- **日期**: 2019-07-04 19:07:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`

### 110. C01-20190523003 修正"流程中若有核決層級關卡，且退回的關卡設有自動簽核，當處理者重複時會觸發退回的關卡執行自動簽核"的問題
- **Commit ID**: `a01fe48128f3d0d70d2450fc31734eaefc169a6d`
- **作者**: yanann_chen
- **日期**: 2019-07-04 16:05:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 111. Q00-20190703003 將文件總管的修改文件屬性的保存年限調整為保存日期。
- **Commit ID**: `22143437a27eb1653d3bd5281ef2e34eca5201b5`
- **作者**: waynechang
- **日期**: 2019-07-03 16:43:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/isoModule/struts-manageDocument-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/EditDocument.jsp`

### 112. C01-20190702004 修正移動端客製開窗使用模糊查詢時會顯示對資料庫執行SQL查詢指令失敗問題
- **Commit ID**: `78132277a5b1ae780bbd58115af93cd164ac225b`
- **作者**: yamiyeh10
- **日期**: 2019-07-03 14:20:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 113. V00-20190626004 (第二次修正)修正組織設計師-修改員工資料-設定所屬單位-部門 , 完全沒選擇主部門也可儲存問題
- **Commit ID**: `ef63e1172cb587961def4a96e2f5bab377c69e0e`
- **作者**: jerry1218
- **日期**: 2019-07-01 11:11:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`

### 114. V00-20190624004-修正TT整合設定無論成功失敗皆會顯示successful
- **Commit ID**: `791fdd5e43f911aab4eea852843139e1f4b03927`
- **作者**: jerry1218
- **日期**: 2019-07-01 10:51:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/src/com/dsc/nana/user_interface/apps/syscfg/view/tiptop/TiptopToolBar.java`

### 115. V00-20190626004 修正組織設計師-修改員工資料-設定所屬單位-部門 , 完全沒選擇主部門也可儲存問題
- **Commit ID**: `482649be8b416b3111d470fc3e42e9d4fcd11135`
- **作者**: jerry1218
- **日期**: 2019-06-27 15:36:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`

### 116. A00-20190513001 修正ISO排程dailyJob當制定單位設定成群組時會報錯
- **Commit ID**: `77ab3c05642cc9da835719ffc6af2272222e0ff6`
- **作者**: waynechang
- **日期**: 2019-06-27 13:53:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/bakjob/ISOAutoJobDaoWorker.java`

### 117. A00-20190626001 多語系縮減調整
- **Commit ID**: `9689d3935441f77229b47fff779d834a7c2ac2c8`
- **作者**: Catherine
- **日期**: 2019-06-26 19:19:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 118. Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57
- **Commit ID**: `e367214a7b65abdead02b94fffb6670c5fccf30f`
- **作者**: Catherine
- **日期**: 2019-06-26 14:55:43
- **變更檔案數量**: 0

### 119. A00-20190626001 多語系縮減調整
- **Commit ID**: `fb27b38538950e9b1d2303a6a099485108399908`
- **作者**: Catherine
- **日期**: 2019-06-26 14:55:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls`

### 120. C01-20190617004 修正使用者表單設計師查詢失效
- **Commit ID**: `99a593324c426f517b3f959caa41503d5692e350`
- **作者**: yanann_chen
- **日期**: 2019-06-26 14:44:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionSearchReader.java`

