{"company_id": "06100053", "company_name": "弘凱", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06100053_弘凱\\01客戶基本資料", "files": [{"filename": "[弘凱] 相關資訊.txt", "raw_content": "06100053弘凱 TIPTOP、ESS、portal鼎新連線精靈\r\n沒有工程移機、要假日版更可以(內部提撥)\r\n-----------------------------------------\r\n正式機(58101_0513 )\r\n*************\r\ndb 同台 NaNa \r\nbkefgp / 95#1C319a17C\r\n00-50-56-B8-00-05\r\n\r\n測試機：http://************:8080/NaNaWeb/\r\n測試機DB 同台 EFGP_Test efgptest / brightek\r\n\r\n新測試機AP+DB+ESS+nginx (58101_0513)\r\n(客戶自己生的主機、自己移機)\r\n192.168.32.43\r\nWEB:1234\r\n00-50-56-B3-4A-63\r\n\r\n-----------------------------------------\r\n戴禾濬ㄐㄩㄣˋ <EMAIL>\r\n03-3267845分機508238(在大陸，電話撥起來會像沒通一樣嘟嘟嘟 是正常的，直接再撥分機即可)\r\n0913-627-232\r\n\r\n\r\n-----------------------------------------\r\n主機MAC位置  安裝序號  安裝密碼  授權人數  OID  是否逾期  \r\n11a8-10te-1101-0060  bad2-9776-3d73-6dc8-a28c-39df  60  179a4bd8d393100481e4f94bc07c8496   \r\n866e-ecte-1102-0000 f4b9-9776-3644-61c8-a2d9-314f\r\n8999-82te-1123-0000 24d9-9776-3585-61c8-a204-3347\r\nf775-66te-1113-0000 0817-97f6-3b45-61c8-a2e8-3726\r\n\r\nALTER TABLE ISOWatermarkPattern ADD checkType NVARCHAR (20) not null ;\r\nALTER TABLE ISOWatermarkPattern ADD checkValue NVARCHAR (50) not null ;\r\nALTER TABLE ISOWatermarkPattern ADD watermarkAttribute NVARCHAR (300) null ;\r\nALTER TABLE DocCategory ADD nameStack nvarchar(4000) null ;\r\n-----------------------------------------\r\n\r\n [目前環境]\r\n表單版本1:DBConnection=DB0 (所有表單DBConnection都叫DB0)\r\n表單版本2:刪除DBConnection，剩下SQLCommand=SQL1 (對到資料來源設定叫HRM)\r\n\r\n[確認到的系統邏輯]\r\n1.SQLCommand會認最新表單定義的設定\r\n2.主要是看Script這句new DataSource(\"表單id\", \"SQLXXX\") 的SQLXXX\r\n\r\n\r\n因為Script都沒改，所以代表new DataSource語法也還是寫SQLXXX，\r\n那這樣最新版有SQLCommand叫SQLXXX就好，\r\n這樣不管表單版本1、2都可以繼續派送，版更後不用再改\r\n\r\n需確認此邏輯是否正確+確認是否有其他錯誤，\r\n可以在clone一次DB到新主機上，采蓉模擬一次版更，\r\n用版更來的資料再作驗證\r\n\r\n", "structured_data": {"測試機": "http://************:8080/NaNaWeb/", "測試機：http": "//************:8080/NaNaWeb/", "web": "1234", "表單版本1": "DBConnection=DB0 (所有表單DBConnection都叫DB0)", "表單版本2": "刪除DBConnection，剩下SQLCommand=SQL1 (對到資料來源設定叫HRM)", "表單版本1:dbconnection": "DB0 (所有表單DBConnection都叫DB0)", "表單版本2:刪除dbconnection，剩下sqlcommand": "SQL1 (對到資料來源設定叫HRM)", "host": "*************"}, "source_path": "C1.客戶維護相關\\06100053_弘凱\\01客戶基本資料\\[弘凱] 相關資訊.txt", "file_size": 2120, "encoding_used": "utf-8", "processed_at": "2025-08-26T10:46:29.001105"}], "total_files": 1, "processed_at": "2025-08-26T10:46:29.001114"}