{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.7.6.1", "date": "tag 5.7.6.1\nTagger: 張詠威 <<EMAIL>>\n\nlast build 108/06/26  10:302019-06-26 10:18:09", "message": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "author": "<PERSON>"}, "舊分支": {"branch_name": "*******", "date": "tag *******\nTagger: 施翔耀 <jose<PERSON><PERSON><PERSON>@digiwin.biz>\n\nlast build 108/03/27  10:41:452019-03-27 10:38:43", "message": "調回Linux啟動conf檔", "author": "jerry1218"}, "比較時間": "2025-07-28 17:58:42", "新增commit數量": 270, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "e7c1f5899e2ae4bea081e2de54abc143b2c98f0c", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-26 10:18:09", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "d9a03a2c493ddba2ef85482d151244911913e603", "commit_訊息": "A00-20190626002 調整英文語系打開關注類別維護+關注指標重要性維護的功能無法操作", "提交日期": "2019-06-26 10:16:23", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "4dd6b5a7541f31485841edb485b788bcd90422ef", "commit_訊息": "製作5761Patch", "提交日期": "2019-06-26 10:00:44", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c86f41169ae27cd9f361ad43bbc70dd53c77ece9", "commit_訊息": "新增維護樣板匯出excel的套件及增加模組呼叫匯出的方法", "提交日期": "2019-06-25 15:53:08", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/SheetJS-0.14.3/FileSaver.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/SheetJS-0.14.3/xlsx.full.min.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8d9323a432e4fdfa1dd6a15a53ca486e358c24d3", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-25 14:56:55", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "799c4c8e88e7f5aa29e1385a251e76dbb21eb5ea", "commit_訊息": "A00-20190522007 修正系統管理師時，最底部的相關資料未顯示", "提交日期": "2019-06-25 14:56:34", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/main/ADMMainFrame.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b7218059cee5cb17df05a3407b83bf10a5a47173", "commit_訊息": "A00-20190625001 拼字修正", "提交日期": "2019-06-25 14:47:03", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "5813a447366886077fa3bd2842f2e96343803bba", "commit_訊息": "A00-20190625001 Favorities -> Favorites", "提交日期": "2019-06-25 14:12:53", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "31ecaf0b458172cb312257f5582d88ecc264a8b2", "commit_訊息": "Q00-20190624001 修正文件一覽表匯出excel失效", "提交日期": "2019-06-24 16:31:36", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ab9ed59b9d70a95a130505fc61e91ebb9a43156d", "commit_訊息": "修正iphone5手機在選擇發起或派送部門選項跑版問題", "提交日期": "2019-06-21 18:33:51", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6229a38a0dbb2c789525f6ec7203d039117dea47", "commit_訊息": "修改log層級", "提交日期": "2019-06-21 18:21:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a698559a2e86e7c50ee706c33914da7d7ddfb8d4", "commit_訊息": "C01-20190618002 調整拿掉再確認是否派送/轉派/終止 因為Chrome新版不支持 而且會有簽核意見開窗填完並確認", "提交日期": "2019-06-21 17:14:41", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "363d7d806988943d655aa6d5eee066857e173a74", "commit_訊息": "調整 ISO， BPMAPP 的選單icon", "提交日期": "2019-06-21 14:58:01", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/_getModuleIcon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1d8482c699fe519b8a899041c9f2ae2b9dc3afc4", "commit_訊息": "拿掉測試用的字串", "提交日期": "2019-06-21 13:55:16", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "00326457afcc7c9e6dcf9d1b41cfdc8f13cae5bd", "commit_訊息": "S00-20190416001 待辦事項放大鏡顯示當前關卡名稱", "提交日期": "2019-06-21 13:54:13", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "693227d5f82c5b8ce5f17d81e651793b55dd3803", "commit_訊息": "SystemVariable相關修正", "提交日期": "2019-06-20 18:47:04", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/LoadSystemVariable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebPropsLoader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "7c5ccedf26641842080c47ae569aaf4fdab582f3", "commit_訊息": "Q00-20190620001 修正點選遮罩時更多選項上方的按鈕未顯示問題", "提交日期": "2019-06-20 17:50:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "adb8b15e3daad2955153b2c7675f96b0fdab49c4", "commit_訊息": "A00-20190603002 簽核流設計師服務任務無法加入WebServices(補jar)", "提交日期": "2019-06-20 16:38:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/Xalan/xalan.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "08e1ccd1779200684006c0fff863389663721a08", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-20 14:23:06", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "f7c2fd72b3068f13df7c818d3ac999c04f493b94", "commit_訊息": "A00-20190605002 修正加簽過一次部門主管 先回到表單畫面在點進入加簽第二次時將第一次加簽改為部門", "提交日期": "2019-06-20 13:42:40", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "391b52ddcadde72be2abbe355205f5569d4f32c0", "commit_訊息": "取值字串改使用SystemVariableCache的key", "提交日期": "2019-06-20 12:02:52", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/SecurityManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/FormAttachmentsEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/ActualParameterChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/main/DesignerMainFrame.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/packagetree/ProcessTreeNode.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "480f0e645bfbd2439d320415d97ccbdfaaf4e6a8", "commit_訊息": "修改NaNaEJB.properties設定", "提交日期": "2019-06-20 11:50:02", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaEJB.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08af106605ae774eb88d72b5441cce2be1dafe25", "commit_訊息": "C01-20190617001 修正Android手機(6.39英寸)畫面跑版", "提交日期": "2019-06-20 11:30:56", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d557d8f9b551f392f3a137a2d3882b1b552380da", "commit_訊息": "A00-20190617001 修正流程變數解析出的處理者進行簽核時，出現系統異常畫面", "提交日期": "2019-06-20 10:22:00", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/monitor/ActivityNodePreviewState.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/monitor/ActivityNodePreviewState_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/monitor/ActivityNodePreviewState_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/monitor/ActivityNodePreviewState_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "52966a358909b6990fcf11ea23b43c7cb8069e7b", "commit_訊息": "調整向互聯取回來的使用者資訊log(漏簽部分)", "提交日期": "2019-06-19 20:14:14", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "700dd9f4c2b7b128f0fd90838756ed1f180ab102", "commit_訊息": "調整向互聯取回來的使用者資訊log", "提交日期": "2019-06-19 20:11:11", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f74375c749e5de3094c6f373c5d11ca10333740b", "commit_訊息": "Q00-20190619004 修正IMG中已結案流程的處理的流程會抓全部資料問題", "提交日期": "2019-06-19 20:09:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "425173feb3b279042c5fa1d9fdf46c8cfb518270", "commit_訊息": "Q00-20190619003 修正IMG表單詳情中連續簽核會報工作已被處理的錯誤", "提交日期": "2019-06-19 18:12:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "501b7c1cf6afbda4a80fa2138e39b49db102aaa3", "commit_訊息": "Q00-20190619002 追蹤流程資料無法正常顯示", "提交日期": "2019-06-19 14:45:14", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3431d527770b1c35c7d44a77beedc91f7ee21b2b", "commit_訊息": "Q00-20190619001 Tool加上版本資訊", "提交日期": "2019-06-19 10:31:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8fb248700e6c8c297eb19d30f02ec30da182e6a8", "commit_訊息": "Q00-20190618003 修正Oracle的SQL語句過長問題", "提交日期": "2019-06-18 20:04:40", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "ba4613764f9bbf5e0fa5e27ccf17d0801cb1b387", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-18 16:50:58", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c4e8e123d1313e29a420ddea7f0732e551667131", "commit_訊息": "補上缺少的多語系", "提交日期": "2019-06-18 16:50:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "166566597da9946892af51ff8a9cb3c8292721be", "commit_訊息": "Q00-20190618002 修正關卡為列印模式時，流程無法往下派送", "提交日期": "2019-06-18 16:48:00", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b0ea8118aa82db65ab865abb1ce0c9f5cf3c7a87", "commit_訊息": "Server2NoticeWorkItem補上isView，讓物件與資料庫一致", "提交日期": "2019-06-18 16:36:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/iconListView/Server2NoticeWorkItem.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9da1941819d954e1dba20fdbc22810dfb363dbb9", "commit_訊息": "調整log層級&內容", "提交日期": "2019-06-18 11:13:10", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c67ee006eb21b92013a71456e45098e1c7846558", "commit_訊息": "Q00-20190618001", "提交日期": "2019-06-18 09:34:16", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "420be0e423689abcaef835f3eea05df637e28abc", "commit_訊息": "Q00-20190617002 授權中間層設定維護畫面欄位名稱調整", "提交日期": "2019-06-17 19:45:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "541138986f58748c8357b1729ad33229a5e9df00", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-17 17:11:08", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "72e31c1c93ab8152a0d67dcbc316d38cd448cee1", "commit_訊息": "Q00-20190614004 設定檔整理", "提交日期": "2019-06-17 17:10:17", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaEJB.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3db3d7b133ecfa4954f2146a7842e19fdc00e0fa", "commit_訊息": "Q00-20190614004 設定檔整理", "提交日期": "2019-06-17 17:09:56", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaDesigner.properties", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaEJB.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaIntSys.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "f57ce1d2bc6506cafdd2c32cfff672326b7f4e6f", "commit_訊息": "Q00-20190617005 5751簡易流程圖點選核決關卡查看詳細資料的畫面  是v56的樣子 調整為v57", "提交日期": "2019-06-17 17:00:58", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d6b750569e289f84a5e12ff80756cf6af4b7061", "commit_訊息": "C01-20190528003[updateSQL] 修正XPDL轉BPMN簡易流程圖點選以簽核的核決關卡查看詳細資訊時 頁面無法顯示", "提交日期": "2019-06-17 15:12:27", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_3_Check.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_3_Check.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "b0430854bac7a546763c7b5d849134775a7077c9", "commit_訊息": "Q00-20190617003 修正企業微信登入BPMAPP後操握異常", "提交日期": "2019-06-17 11:10:34", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1bdce0c0bb4b9aa59b51edc4c93d45d48fe64452", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-17 10:56:20", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "55adf054e59372822977ee91b41a2263852d08e8", "commit_訊息": "NaNaDesigner.properties的設定改讀取SystemVariable資料表", "提交日期": "2019-06-17 10:55:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "4400d23e28b7e1e43360e940aeaada7cda4a72a0", "commit_訊息": "Q00-20190617002 授權中間層設定維護畫面欄位名稱調整", "提交日期": "2019-06-17 10:36:17", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f83238f8d9fc1b3dc5f9f23743baaaafb6d73f11", "commit_訊息": "Q00-20190617001 通知信樣板如果有使用FormBody時，會導致流程發起失敗", "提交日期": "2019-06-17 10:01:28", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4ab83487aa0343c72a8edfd5327a04f29df26ecb", "commit_訊息": "Q00-20190614002 隱藏授權中間層H5下方footer返回按鈕", "提交日期": "2019-06-14 18:23:10", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContactV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListContact.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "0411eb6c409281b83e0b8daba1258d8aa3c864fc", "commit_訊息": "修改log內容", "提交日期": "2019-06-14 18:11:39", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ce32f32d8f03beda52e97598dc7bfd3c9671c3bd", "commit_訊息": "Q00-20190614003 修正智能快簽判斷用欄位字眼與其引發錯誤", "提交日期": "2019-06-14 15:00:28", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkAssignment.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "12cb35583a4dbfa631514e2ae1a51e7e17e660df", "commit_訊息": "Q00-20190614001 補修正", "提交日期": "2019-06-14 10:54:13", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "22584344f94e8b1d521fa829999706cdeccc0009", "commit_訊息": "調整SQL指令大小寫", "提交日期": "2019-06-14 10:28:12", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "736178a4ecb69f0a14b07982aedca087411fd0dc", "commit_訊息": "Q00-20190614001 調整個資宣告相關設定", "提交日期": "2019-06-14 10:26:55", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_2_Check.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "031c9915ddc85f1ab74d91f388916b999de877be", "commit_訊息": "Q00-20190613001 當processStatus為空值，系統報錯問題", "提交日期": "2019-06-14 09:22:30", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkAssignment.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08a4977109c213f4c5c0607b492b059bce18107b", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-13 15:55:57", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "86cc13cae5c230751709604bafdac33e73d96532", "commit_訊息": "A00-20190522002 修正ISO文件變更明細表的製作日期異常", "提交日期": "2019-06-13 15:55:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/dialect/ISODocListReaderImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89f5511e026cee593bb71aa850bcd44b60aaf322", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-13 11:00:33", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "eb9b2d200d620402c751b8aeb1014de2074396db", "commit_訊息": "C01-20190605001 調整BPM掛載服務設定", "提交日期": "2019-06-13 10:59:56", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/jboss-cli.bat", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-11.0.0.Final/bin/jconsole.bat", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "28f37afbce057f4282fcb9e41cca0ba1980b07d2", "commit_訊息": "更新 5.7.6.1的SQL及patch", "提交日期": "2019-06-13 10:54:21", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c98ac41fa9a2044cdc87817cb2992bc7c575435e", "commit_訊息": "新增智能學習排程預設資料", "提交日期": "2019-06-13 10:35:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "971bf527fa6d221eef6f4b84135bfc4728522457", "commit_訊息": "調整為 5.7.6.1 patch", "提交日期": "2019-06-13 10:22:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "965fec42d9e96bdfad4db5a8394b7fdeb5a758cc", "commit_訊息": "C01-20190528003 修正XPDL轉BPMN簡易流程圖點選以簽核的核決關卡查看詳細資訊時 頁面無法顯示", "提交日期": "2019-06-13 10:10:41", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ecae0a8d9efcc64d67abeac5b004d436d1225a49", "commit_訊息": "C01-20190531004 調整設計師的越南語系", "提交日期": "2019-06-13 09:28:26", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/AdvancedFDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/AdvancedLDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/GridAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromXPDLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenApplicationManagerAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenDecisionPattern_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenFromXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenProcessViewAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenSoftscoreXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/RedoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/RemoveProcessDefAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/RemoveProcessDefinitionAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveAsImageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/ShowHistoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SimpleFDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SimpleLDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoCheckoutPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoCheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/common/common_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/DataAcsDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/DesignerIFrameController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/SecurityManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/swingext/AbstractMCERDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/AskActivityReexecuteTypeEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BlockActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BlockActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/BpmUserTaskEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlineDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlineMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DeadlinesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DecisionActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/DecisionActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormAttachmentsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormSelectDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/InvokeActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/InvokeActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/InvokeActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/InvokeActivityMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ReferredActivityCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/RouteActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/SubflowActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionConditionConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/decision/DecisionRuleListEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/subflow/SubflowActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ApplicationManagerDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ApplicationManagerPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ApplicationsToolsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/FormalParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/HttpCodesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeSessionBeanToolMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeSesstionBeanToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeWebApplicationToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/InvokeWebServicesToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/MailApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ScriptCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ScriptEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/ScriptingApplicationDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationWizardPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WSMsgMappingEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WebApplicationDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WebApplicationMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/application/WebApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/batchio/BatchIO_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ActivityChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ActualParameterChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ApplicationChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ApprovalLevelChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/BpmUserTaskChooserPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/EntireFormChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/OrganizationBpmUserTaskChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/ProcessRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/chooser/StrategyAssignmentPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/ActualParameterCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/ApplicationTypeCellRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/CalendarEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/PattternEditorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/XMLInstanceGeneratorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/common/property_editor/PropertyEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/composite/CompositeEditorController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/composite/CompositeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionActivitySimplePropertiesShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionConfigShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/DecisionRuleListEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/ConditionExpressionPatternBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/ConditionPatternCellBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/DecisionConditionBatchConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/batchChange/DecisionPatternsMappingBatchDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/decisionShareCfg/transition/ConditionExpressionEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/AbolishDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/CMPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/CheckInDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerIFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DesignerMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/DiagramEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/main/SaveAllDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationInfoPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/popmenu/ObjectPopupMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/popmenu/ProcessViewPopupMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/SysintegrationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/formalparameter/FormalParameterMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/BasicTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/BasicTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/process/relevantdata/RelevantDatasMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageCategoryMultiZh_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/transition/TransitionDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/tree/orgtree/OrganizationTreeModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/tree/packagetree/ProcessTreeNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/AbstractDesignerDialog_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/AbstractListDialog_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/DateChooserDialog_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/DefaultTranslator_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/DesignerLoginDialog_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/DesignerStatusPanel_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/Actions_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/AddUserDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/DefaultSubstitudeEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/EmployeeCreator_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/FunctionViewer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/GroupEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MainMenuBar_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/MaintainAbstractRoleDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/ManagerSelectorDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgChooserDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgGraphDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OrgUnitEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerGroupPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerOrgPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerOrgUnitEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/OwnerOrgUnitPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/RelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SearchProcessDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/SubstitutePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/TransferUserDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/UserCompanyEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/WorkCalendarEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/plugin/ISOPlugInDecoder_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/DepartmentNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/GroupNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/OrgNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/OrgRootNode_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/OrgTree_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/tree/OrganizationTreeModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/view/OrgSelectorTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/view/UnitTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/resource/view/UserTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/AbstractStep_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/CfgTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Config_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ImportOrgXML_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ImporterMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/ModelMainTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/NewOrgDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/StepTab_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/Term_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/TreeMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/org-importer/src/resource/status_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/AdvancedFDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/AdvancedLDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/CreateCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/EditCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/GridAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/NewProcessPackageFromXPDLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenDecisionPattern_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenObjectAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/OpenSoftscoreXMLAction_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveAllProcessPackagesAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveAsImageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveAsXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SaveAsXPDLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/ShowHistoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SimpleFDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/SimpleLDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoCheckoutPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/action/UndoCheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/ADMIDValidator_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/OnlineUserMgtController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/OrgWizardAuthorityScopeController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/SystemConfigController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/controller/ToolAuthController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/accessCtrl/AccessCtrlMainPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/accessCtrl/AccessCtrlMgrPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/main/ADMMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/onlinemgt/OnlineUserMgtPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/DataAcsDefTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/DocServerDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/DocServerTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/LdapDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/LdapTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/adm/view/sysconf/SystemConfigPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/common/common_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/DataAcsDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/controller/SecurityManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/DueDateEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ActivityTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/AskActivityReexecuteTypeEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/AutoDeliveryEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BlockActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BlockActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/BpmUserTaskEditorPanel_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DeadlineConditionCellEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DeadlineMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DeadlinesEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DecisionActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DecisionActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/DecisionActivityTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/FormAttachmentsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/FormSelectDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/InvokeActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/InvokeActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/InvokeActivityMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ParticipantEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/ReferredActivityCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/SubflowActivityDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/SubflowActivityDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/SubflowActivityMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionActivitySimplePropertiesDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionConditionConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionConfigDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/decision/DecisionRuleListEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/activity/subflow/SubflowActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ActualParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ApplicationManagerDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ApplicationManagerPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/ApplicationsToolsEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/FormalParametersEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeRestfulToolDialogController_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeRestfulToolEditorPanel_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeRestfulToolMCERTableModel_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeSessionBeanToolMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeSesstionBeanToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeWebApplicationToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/InvokeWebServicesToolEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/MailApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/TiptopSessionBeanDialogChooser_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WSInvocationEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WebApplicationDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WebApplicationMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/application/WebApplicationToolDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/batchio/BatchIO_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ActivityChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ActualParameterChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ApplicationChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ApprovalLevelChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ApprovalLevelComboBoxRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/BpmUserTaskChooserPanel_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/BpmUserTaskDefinitionChooser_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/BpmUserTaskInfoPanel_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/EntireFormChooser_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/FormElementChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/OrganizationBpmUserTaskChooser_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/OrganizationParticipantChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ParticipantChooserPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ParticipantDefinitionChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/ProcessRelationshipPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/StrategyAssignmentPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/chooser/TimeSetupPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/ActualParameterCellPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/ApplicationTypeCellRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/CalendarEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/PatternEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/common/property_editor/PropertyEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/composite/CompositeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/DecisionActivitySimplePropertiesShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/DecisionConfigShareDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/batchChange/ConditionExpressionPatternBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/batchChange/ConditionPatternCellBatchEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/batchChange/DecisionPatternsMappingBatchDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/decisionShareCfg/transition/ConditionExpressionEditorSharePanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessTableHeader_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/AbolishDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/CMPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/CheckInDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerIFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DesignerMainFrame_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/DiagramEditor_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/ReleaseDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/main/SaveAllDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/notification/NotificationInfoPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/notification/NotificationManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/popmenu/ObjectPopupMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/popmenu/ProcessViewPopupMenu_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/AccessLevelCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/DurationUnitCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/ProcessDefinitionMCERTable_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/SysintegrationEditorPanel_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/AttachmentTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/BasicTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/BasicTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/formalparameter/FormalParameterMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/AttachmentTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/BasicTypeDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/BasicTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/BasicTypeMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/FormTypeEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/process/relevantdata/RelevantDatasMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ConformanceClassCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/processpackage/VariableNamesList_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/sysintegration/T100TemplateGenMappingList_vi_VN.properties", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/ConditionExpressionEditorPanel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/ConditionTypeCellEditorRenderer_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/transition/TransitionDefinitionMCERTableModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/resource/view/tree/orgtree/OrganizationTreeModel_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/src/resource/SysAuthorityRsrc_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/Language_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/MainRsrcProperties_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/rsrcbundle/Rsrc_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-configure/src/resource/tiptopcfg_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 355}, {"commit_hash": "d98703a1888cc8155895668afb10cf07c09b2c0e", "commit_訊息": "修正mobile的SQL中欄位索引太長問題", "提交日期": "2019-06-12 17:19:21", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "1638095b7ddd0274918790dfb53e76112640b4b3", "commit_訊息": "修正mobile的SQL欄位名稱錯誤", "提交日期": "2019-06-12 16:20:48", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "d2e388211fd5cdee637a2a63b0b355f656fad3d4", "commit_訊息": "A00-20190531002 調整組織設計師選擇部門底下人員的主管邏輯", "提交日期": "2019-06-12 15:59:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cf4e8f9fb7655cdff58c4d7195efb0abead622a1", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-12 15:32:52", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0ca0771332940ad0481613ee42498aaea40aa4e0", "commit_訊息": "修正未使用共通方法讀取設定的程式", "提交日期": "2019-06-12 15:32:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ResignedEmployeesListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/PersonalDataProtectionUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmTraceProcessTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "8ac14d0adefcd05262bb15c82aa6d2eef1acb930", "commit_訊息": "C01-20190611001 修正BPMAPP中Dialog元件若有在過濾條件填全域變數會無效果問題", "提交日期": "2019-06-12 15:11:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputLabelTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputMultiTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9a37cb3a67c65d3bfbc602817c4dfe6c09f4c58f", "commit_訊息": "C01-20190611001 修正BPMAPP中Dialog元件若有在過濾條件填全域變數會無效果問題", "提交日期": "2019-06-12 15:09:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputLabelTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputMultiTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputTemplate.txt", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4ef180cdab0948f8aea068d334a273b4844083a5", "commit_訊息": "A00-20190507002 補修正", "提交日期": "2019-06-12 14:29:51", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08814f914011e0759373a6f361fd80d801d75d45", "commit_訊息": "修正log內容", "提交日期": "2019-06-11 18:15:51", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76eb44ad19a9e225130bf26be36522cc3fe12995", "commit_訊息": "設定檔取值修正: 若取出設定結果為null,就拋出錯誤", "提交日期": "2019-06-11 18:10:23", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "78968e652eae03f8f462fe074aa8275fd60aed53", "commit_訊息": "Q00-20190611002 修正統計元件維護畫面缺少智能快簽選項且多語系核對不起來問題", "提交日期": "2019-06-11 17:52:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/BPMMobile/MobileUserManage/MobileUerManageComponentDinWhaleDeploy.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "36dd9de293c39b47e6a8d72861a3d7e5ee81f81f", "commit_訊息": "Q00-20190611001 修正IMG若grid元件有設定中間層顯示會造成中間層空白問題", "提交日期": "2019-06-11 16:19:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f378021abc72382ed56ee357a7d681eeda60a3c", "commit_訊息": "調整多語系與mobile的updateSQL版號改成5761", "提交日期": "2019-06-11 11:17:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.1.1_updateSQL_Oracle.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.1.1_updateSQL_SQLServer.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 3}, {"commit_hash": "0d5d90963af21a008f63ec58bbac587692df51c3", "commit_訊息": "調整智能快簽判斷用欄位的名稱", "提交日期": "2019-06-11 11:09:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkAssignment.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "409bd79e28b29b38d5cf42ec68ba47eda00b5342", "commit_訊息": "C01-20190603001 修正響應式表單行動版設計中grid的自動生成按鈕script功能會重複加入問題", "提交日期": "2019-06-10 14:19:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "13a8403989dacf4c13f6d227c150302f341de345", "commit_訊息": "先移除ISO報表V58調整第一版", "提交日期": "2019-06-10 13:46:42", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "88a4dad232cd0abedc8caff2eb999a2b07792cc6", "commit_訊息": "A00-20190424001 調整IMG所有H5連結將${mobile_token}全部移除", "提交日期": "2019-06-10 11:20:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileScheduleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "794cbafb1db6040c91eacd9f27c3c2cc222fa292", "commit_訊息": "S00-20190528001 調整設計師之登入介面使用checkbox 取代輸入administrator", "提交日期": "2019-06-10 11:13:06", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7ebad15f5b1f8d6aaecac32351fb341a45a84bbe", "commit_訊息": "A00-20190514001 修正BPMAPP開啟附件時檔名會有亂碼問題", "提交日期": "2019-06-10 10:09:20", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "4e1353d7ee3512b305c1d4027f77366343f2d7d0", "commit_訊息": "C01-20190531004 調整設計師的越南語系", "提交日期": "2019-06-10 09:41:56", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/AdvancedFDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/AdvancedLDALayoutAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/ConversionXPDLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/CopyAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/DeleteCMProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/EditCMCategoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/GridAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/MoveProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromToolBarAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/NewProcessPackageFromXPDLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenDecisionPattern_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenFromXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenNotificationManagerAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenObjectAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/OpenSoftscoreXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/PrintAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/RedoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/RemoveProcessDefAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveAllPackagesAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveAllProcessPackagesAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveAsImageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveAsXMLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SaveAsXPDLAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/SavePackageTreeAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/ShowHistoryAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoCheckoutPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/UndoCheckoutProcessPackageAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/action/ZoomToFitAction_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/common/common_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/controller/DesignerIFrameController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/monitor/ActivityNodePreviewState_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/swingext/AbstractMCERDialogController_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/DueDateEditor_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/AuthorityTermTranslator_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/OrgParticipantChooser_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/resource/common/ReadOnlyOrgTree_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/sys-authority/src/resource/SysAuthorityRsrc_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 42}, {"commit_hash": "bf0c5fece473f107f096b3630328afc029658b8a", "commit_訊息": "ISO報表V58調整第一版", "提交日期": "2019-06-10 09:32:11", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocCmItemVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/DocumentVo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/isoModule/DocForReportViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/RestfulHelper.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "2554effc10a6997e2bc323b5d4195d803483077c", "commit_訊息": "A00-20190529004 & A00-20190605006 單身異常補修正", "提交日期": "2019-06-06 19:25:42", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a475e081d535e6677eb9f13f8b0153e6a6889a62", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-06 18:10:35", "作者": "劉建德", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "6a48e1913a5528b7eb754db53c51304caaede2a6", "commit_訊息": "Q0020190411005快速簽核頁面與詳情頁面在點擊擱置工作後的彈出視窗訊息需在做調整", "提交日期": "2019-06-06 17:58:31", "作者": "劉建德", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "decb02c166dc6d817409b9f459b99a328406ed4c", "commit_訊息": "智能學習管理中心功能修正", "提交日期": "2019-06-06 17:50:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/IntelligentLearningManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0ca7a085b1d8e926b41b92896642c3146810c17a", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-06 17:39:07", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "3159e7ee02c0c74988b6bac98ebe41c1ffdfa9da", "commit_訊息": "A00-20190529004 & A00-20190605006 修正單身加總異常", "提交日期": "2019-06-06 17:12:05", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e3fa6d1b5be763239222e78d23ebeaa4e3ec3c1", "commit_訊息": "Q0020190411006詳情頁面需過濾擱置浮動按鈕的顯示機制", "提交日期": "2019-06-06 17:06:21", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5044229d69ada2d84fc5ed5a661e235a82f11d67", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-06 16:08:44", "作者": "yamiyeh10", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "ed24fab625b3bf87940199e2400b61375588f771", "commit_訊息": "修正移動授權中間層使用者維護介面", "提交日期": "2019-06-06 16:08:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "acad1cdba589becd772bee8bd214a04d7301b30c", "commit_訊息": "移動授權中間層測試修正", "提交日期": "2019-06-06 16:00:39", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterCommonTool.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterToolFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.1.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.1.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 18}, {"commit_hash": "e56b659f9fe3ce9faa5cbc173dc356c9c55ecf71", "commit_訊息": "Q0020190524001 向BPM取得的accessToken不會過期", "提交日期": "2019-06-06 15:18:04", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1abba06f071c78811f6b82fec06f4f40b94d37d5", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-06 12:00:31", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "91074e5bbe7a325fffdfc850f0af619d0bc88e8d", "commit_訊息": "Q0020190523003 1.腳本樣版篩選時,不要把中文說明自動加入到註解中 2.複製腳本樣版時,目前的畫面顯示機制需要再調整", "提交日期": "2019-06-06 11:59:13", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dd5b21c40fa527cbf692754414bb8ce2674192c2", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-06 11:31:15", "作者": "jerry1218", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "edaa9c047ab44bfd44f758c7551493fed71abed1", "commit_訊息": "新增LocalNoticeWorkItem & LocalToDoWorkItem Index", "提交日期": "2019-06-06 11:30:25", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "7bbd4e041c70d2679b62fb28c37ecad93018d668", "commit_訊息": "調整移動授權中間層維護介面功能與畫面", "提交日期": "2019-06-06 11:15:48", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "851a3531d02b3e5b8791c2c8b542ee029ee1d658", "commit_訊息": "Q00-20190410001 修正Android在鼎捷移動APP上個人任務應用中間層顯示異常問題", "提交日期": "2019-06-06 10:36:47", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "59dd18ab84a67880a9c6e5ccf83ff0b51b8d0276", "commit_訊息": "A00-20190507002 修正DialogInputMulti顯示異常", "提交日期": "2019-06-06 09:49:32", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d5ffb5bde706117d0b5b4e0895aa718811ef9005", "commit_訊息": "修改變數與固定字串位置，以防止NullException", "提交日期": "2019-06-06 09:47:43", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a82cf64edc207da491013f783197b7fdc08c64ff", "commit_訊息": "rename sql file 5.8.1.1 -> 5.7.6.1", "提交日期": "2019-06-05 19:52:02", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DDL_MSSQL_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DDL_Oracle_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql", "修改狀態": "重新命名", "狀態代碼": "R095"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_2.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_1.sql", "修改狀態": "重新命名", "狀態代碼": "R095"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_2_Check.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 6}, {"commit_hash": "d338d899aa4b6571cea9d61fac00e97368f1c5d9", "commit_訊息": "調整移動授權中間層維護介面功能與畫面", "提交日期": "2019-06-05 18:12:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.1.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.1.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "830530295a7ed852716f21fb44dce3e7703e3c68", "commit_訊息": "A00-20190524002 修正BPMAPP預測關卡功能的邏輯錯誤導致近表單畫面時會提示錯誤訊息", "提交日期": "2019-06-05 14:30:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "254351e1748d048f000b00741fb0ac516db0e171", "commit_訊息": "A00-20190605001 修正如果有DoubleText元件並在表單勾選必填 流程表單設定要驗證  整張表單會無法顯示", "提交日期": "2019-06-05 14:20:54", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cfe19fece7377e06c2e64e3aa4cc76b6186e359d", "commit_訊息": "調整sql指令", "提交日期": "2019-06-05 11:28:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_2.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_2.sql", "修改狀態": "重新命名", "狀態代碼": "R095"}], "變更檔案數量": 2}, {"commit_hash": "aa93dba51683e0b3a08e4025f5bd1181460ed244", "commit_訊息": "調整SystemVariableCache", "提交日期": "2019-06-05 11:18:35", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "11ef43e6912e37e4684429884b659ed2eef87b4a", "commit_訊息": "C01-20190318002 將registerRmi的對象宣告為static避免被JAVA回收後呼叫找不到", "提交日期": "2019-06-04 15:11:03", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/RmiRegistry.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "13df147b096d683cf4e78682eeb72773bc422380", "commit_訊息": "Q00-20190604001 修正判斷是鼎捷移動或企業微信的設定檔時邏輯有誤導致不會發送推播", "提交日期": "2019-06-04 11:46:54", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/AnnouncementManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "622bf8b399ff018f3b5e123f577af2bb6d2ad692", "commit_訊息": "Q00-20190325003 調整IMG列表標籤與快速簽核頁面的智能示警相關畫面", "提交日期": "2019-06-03 17:35:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2bdbeabe3e5507afee3453ab901196e3dc9a36c7", "commit_訊息": "更改設定", "提交日期": "2019-06-03 13:35:20", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "26fbf2032eb6663742989ec4bf918fbbf4bd47df", "commit_訊息": "設定檔整理", "提交日期": "2019-06-03 11:57:40", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaDesigner.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaEJB.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/NaNaIntSys.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0f83393c5906637460a100e332ff97c5c463d714", "commit_訊息": "修正getDBType方法", "提交日期": "2019-06-03 11:27:40", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c130c29a9a339c85c45fdab2c5707c1adfd18f0a", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-03 11:03:36", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "4964d1d040164ab58e189ee1198f327d39265236", "commit_訊息": "調整ListReader部分程式，改用共同方法讀取設定", "提交日期": "2019-06-03 11:01:54", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileAuthorizedPrsInsListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileInvokableProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileTraceableProcessInstListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 17}, {"commit_hash": "360078e0fddf010e51303f1c5c0ecae243e610e5", "commit_訊息": "S00-20190528001 設計師之登入介面使用checkbox 取代輸入administrator", "提交日期": "2019-06-03 11:00:58", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_vi_VN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/designer-common/src/com/dsc/nana/user_interface/apps/common/model/ToolEntryLoginTemp.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "b528f16308418814f730c8a935e3944f9c49fca8", "commit_訊息": "Q00-20190530001 調整移動端彈出視窗與智能示警樣式跑版", "提交日期": "2019-06-03 10:54:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "fb5dd0a7a1c81ec4dd4f102b64c1efb4f9e90a4d", "commit_訊息": "Q00-20190517001 調整IMG詳情頁面浮動按鈕畫面", "提交日期": "2019-06-03 10:44:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "2f959b9d1b55a4cecb13b95d74b405d207a50047", "commit_訊息": "Q00-20190411002 調整移動端取消訂閱畫面呈現", "提交日期": "2019-06-03 10:32:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileLibrary.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "d2e6181966cf3b07c4dba7933cc467c25f66c742", "commit_訊息": "Q00-20190419001 調整移動端代理通知推播送出的連結", "提交日期": "2019-06-03 10:17:11", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "572e1ff8af16e4628250a4aee4a1c05f5514a7fc", "commit_訊息": "Q00-20190325003 調整IMG列表標籤與快速簽核頁面的智能示警相關畫面", "提交日期": "2019-06-03 10:10:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "547847bb30c7aca7c2b9e929ac4f323b7346865b", "commit_訊息": "Q00-20190325004 調整IMG詳情頁面內的智能示警相關畫面", "提交日期": "2019-06-03 10:04:00", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/infoicon.png", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "16d7b989a7f85b67fa5276a843aa246fb0f6fe43", "commit_訊息": "新增與調整SystemVariable相關的SQL指令", "提交日期": "2019-05-31 16:55:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_2.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_2.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "1db408aa5e8cee87e8ea93c101f0a01bc311dd94", "commit_訊息": "調整Server2NoticeWorkItem 結構 與LocalNoticeWorkItem 一致", "提交日期": "2019-05-31 10:49:12", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "13fa977b88cdf5571132d9a821316ea87db7626d", "commit_訊息": "A00-20190311001 修正客製程式，當menu展開無發縮回的問題", "提交日期": "2019-05-30 18:38:57", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5a9616d57234a68ec7a5093bc584edf1fafff447", "commit_訊息": "調整新增表單時，預設為響應式 formDesigner.msg.rwdForm", "提交日期": "2019-05-29 17:22:13", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1ef1a51440fd48ec519299bfcfcedd327158996c", "commit_訊息": "A00-20190416003 補修正", "提交日期": "2019-05-29 17:15:12", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceRelationalProcess-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceRelationalProcess/ViewWorkItem.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "105856c333c3bdbbc07607a1a2dd3ea21dd52ee1", "commit_訊息": "Q00-*********** [補修正]原本update ProcessMappingKey的方式錯誤 重新調整", "提交日期": "2019-05-27 15:02:39", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f22460d3c11dcfcc20f9ec86f58e0709a41adecd", "commit_訊息": "調整移動授權中間層維護介面", "提交日期": "2019-05-24 19:05:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.1.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.8.1.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "68daee60b5832fee12cd0cbe5febbc18ef2f3969", "commit_訊息": "新增授權中間層發送通知消息功能", "提交日期": "2019-05-24 18:35:04", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterCommonTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "142c7e7432cddb1b3a2dbaa78705ca4913cc12db", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-24 18:30:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "1cf2a382258355cee5fd0274fbeede807aee4c01", "commit_訊息": "新增授權中間層發送通知消息功能", "提交日期": "2019-05-24 18:29:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterCommonTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "d56d50779122e3e494fe3f42151d6fd28aeb2c58", "commit_訊息": "調整移動授權中間層DTO", "提交日期": "2019-05-24 18:20:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9dad5b2041081537e6f4ca13839aca0ae5755c96", "commit_訊息": "修正 User的PerformForwardType如果是Null會出現Exception", "提交日期": "2019-05-24 18:00:26", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/PerformForwardType2IntFieldConversion.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "57bd950d43c0330b0289caaa51e49b655ea0ba5c", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-24 17:50:14", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a9f85bb9ec716e15892e43a66b8be485245b4d45", "commit_訊息": "修正流程掛載多表單時，mobile型態之切換表單頁籤功能失效的問題", "提交日期": "2019-05-24 17:43:33", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "12a9db785f07fbd2d8d6d1a3a5edb077890892f4", "commit_訊息": "A00-20190520001 修正字串切割 將&換成#BPM&避免E10表單值有&造成解析錯誤", "提交日期": "2019-05-24 17:39:19", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e886c65ae79742045ae82f74e9526fea067fcaf1", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-24 14:53:00", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c2091b18707b3ccb572c9810366201857a8df601", "commit_訊息": "A00-20190524001 更新指令", "提交日期": "2019-05-24 14:34:53", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@appform-essplus/create/Init_AppForm_Data_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "856176449f23e1a853accc5cf7e33a0c57bdaeb9", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-24 14:31:27", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "81f0f4147ac6a20d6dd23c3df078a59f9c1e0c77", "commit_訊息": "1.新增授權中間層消息推播雞制 2.調整縣有消息推播雞制,把原有放在MobileMailerBean的邏輯搬回MobileManageBean", "提交日期": "2019-05-24 14:29:45", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServiceLocator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "c233521e158f3fae642b3be52d93bcb3840270b8", "commit_訊息": "增加方法 - 載入ISO-RWD文件變更單上一版的文件ISOFile資料", "提交日期": "2019-05-24 11:25:28", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9f8f3e6b3bc4439c529e0cc07a0bd1e5c165a867", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-24 09:37:52", "作者": "劉建德", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "782a1f5d69a78dcb09517008cdcd00faed4b7943", "commit_訊息": "修正授權中間層當請求的服務填空字串會跑到錯誤畫面的問題", "提交日期": "2019-05-23 19:06:44", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterCommonTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9cfe74add3fb951988c2afd5405214f9e5615e24", "commit_訊息": "A00-20190517001 修正移動端通知詳情頁面流程主旨無法正確顯示問題", "提交日期": "2019-05-23 19:00:43", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileNoticeServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d8c9327fed5d5a5e77fa12df7d87c08120a07303", "commit_訊息": "A00-20190416003 修正於\"待辦事項\"列印表單時，只能取到表單載入時的值", "提交日期": "2019-05-23 18:30:37", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0618bfdefbf6a954d53b9a1648341a863de70098", "commit_訊息": "Q00-20190523002 表單開發腳本樣版太混亂,許多語系不正確,且沒有依照腳本內容正確分類", "提交日期": "2019-05-23 17:34:43", "作者": "劉建德", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "391f20828ca97d74d4752c96937fbd41c327a810", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-23 16:25:30", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "8435b44d96cd3a4e13c2a55dace4af3696b2499b", "commit_訊息": "A00-20190516001 修正系統管理員撤銷流程，流程主旨出現亂碼", "提交日期": "2019-05-23 16:24:56", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1cdd4a0fe650d6eda83c45370db137bdeb72f02b", "commit_訊息": "調整授權中間層取應用網址與登入驗證與共用參數化功能", "提交日期": "2019-05-23 15:30:40", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterCommonTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterToolFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "92d9e73a19184fae7a0cd006396a4470b9b2c4fc", "commit_訊息": "修改變數與固定字串位置，以防止NullException", "提交日期": "2019-05-23 10:15:51", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1ba1befb010b0c466d925c247dd9a282fb207984", "commit_訊息": "insert語法調整", "提交日期": "2019-05-22 18:50:02", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "554c00739aeec4384f647dd3761316e9d1723b88", "commit_訊息": "A00-20190522006 Oralce語法錯誤", "提交日期": "2019-05-22 18:36:25", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.2.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.3.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.4.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "fa3e6f9adcfd190750b6037b9dc0e4020b160430", "commit_訊息": "A00-20190415001 原本寫法是為了iPad上 無法使用100%，但此語法會造成某耶IE異常", "提交日期": "2019-05-22 16:34:38", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c3d5be1bbe646d12a8a7da2ea88335e19e51fef", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-22 16:07:39", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "2e97a1d87e2ff07ab5fbe3f522b471b586d5a828", "commit_訊息": "Q0020190409002 預計關卡功能在最後一關為服務關卡或關卡為多處理者時中間層與詳情頁面會彈出未預期的錯誤，請洽系統管理員", "提交日期": "2019-05-22 16:06:24", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1ba85e3cc0426cfc828f916716e3cdb790d754ed", "commit_訊息": "A00-20190522005 Oralce語法調整", "提交日期": "2019-05-22 14:42:54", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.6.5.3_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0bdd9fc2e5ec262310aaa9e7e0adb32d72035093", "commit_訊息": "Q00-20190521002 T100環境代號新增兩個區域可以選擇(topstd,toppth)", "提交日期": "2019-05-21 17:30:10", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5449dd3d79f3e571d4bf0ee336cb011ee451ac34", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-21 17:19:31", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "cbc065dfa8b4380b8aa1a55252a9877fb6361bde", "commit_訊息": "Q0020190411004 智能快簽應用的列表若為擱置流程時應有標籤內容提示用戶", "提交日期": "2019-05-21 17:18:17", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "87701085506a68b48eeae5956a89859b65e0c738", "commit_訊息": "Q0020190411004 智能快簽應用的列表若為擱置流程時應有標籤內容提示用戶", "提交日期": "2019-05-21 17:18:06", "作者": "劉建德", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "6ae1990a2f1b5cbf3437796586d90b3f21df64e4", "commit_訊息": "Q0020190411004 智能快簽應用的列表若為擱置流程時應有標籤內容提示用戶", "提交日期": "2019-05-21 17:17:39", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForListDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmPerformWorkItemTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "04caf55a675fcce34e5c96506e0d349f749f943b", "commit_訊息": "A00-20190520002 修正系統管理員-ldap無法設定", "提交日期": "2019-05-21 16:35:15", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "396601004b93c4fdb4f95776f115517c3c38c202", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-21 15:55:24", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "522a8c4ed6a3946be8a58c09e7577244d0412625", "commit_訊息": "列印按鈕版面調整", "提交日期": "2019-05-21 15:54:54", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "54a1be6f5f2c5fb0b1def8bf09056d76e894f1e5", "commit_訊息": "Q00-20190521001 調整出貨E10流程範本 中止流程SessionBean參數未指定", "提交日期": "2019-05-21 15:40:07", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4c67187ae84a17b1970f4e0b9c4c767079e6c243", "commit_訊息": "C01-20190507001 修正移動端開啟特定excel檔案時會有亂碼問題", "提交日期": "2019-05-20 17:35:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "68e5558c382c5474c4468abbb2dc0131bd1767d1", "commit_訊息": "設定檔讀取SystemVariable資料表修正", "提交日期": "2019-05-20 12:21:45", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/StartUpLoadSystemVariable.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "6622aa8df8b7f2e12dbadcbfb68470cbd81d203c", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-17 17:43:51", "作者": "yanann_chen", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "d8d27d377cc2d72d430525763d2d86a328f85b1a", "commit_訊息": "A00-20190411003 第二次補修正", "提交日期": "2019-05-17 17:42:53", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a56e59c85f82dc5340d812e4956dfcf1a48b0a4c", "commit_訊息": "Q00-20190502001 修正移動端推播功能", "提交日期": "2019-05-17 16:31:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "842bf772f30e4ec4f5bd433033958e558b9964a2", "commit_訊息": "A00-20190516002 調整簽核流設計師的webService服務，選擇port異常", "提交日期": "2019-05-17 14:52:32", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/WSInvocationEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e825d1a5c07188a8f0d7f4fc771426e90bf3d21f", "commit_訊息": "A00-20190411003 補修正:固定隱藏列印按鈕", "提交日期": "2019-05-17 14:50:39", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4625d35028c8acd157a8e90ca9812c49271affe5", "commit_訊息": "A00-20190411003修正Chrome列印絕對位置表單功能失效問題", "提交日期": "2019-05-17 10:10:27", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c5a6cc577b7e2082f6db7ebf32b5586766007541", "commit_訊息": "修正A00-20190509003在oracle資料庫，資料選取器無法使用問題", "提交日期": "2019-05-17 09:12:41", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/CustomDataChooserDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "db621ff780a808a88f81048c2ca7fd747092cef1", "commit_訊息": "A00-20190516002 修正簽核流設計師服務任務無法加入WebServices,讀取WSDL URL失敗", "提交日期": "2019-05-16 14:27:56", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/lib/JakartaCommons/commons-httpclient.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "8d4d36d95ff69d4e19caeaa5db86055ae8dbb534", "commit_訊息": "修正授權中間層維護介面的動態參數區塊名稱錯誤問題", "提交日期": "2019-05-16 09:12:26", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1761c7923877a8faddd83dc814b9b56c53dd063c", "commit_訊息": "調整移動授權中間層維護管理介面", "提交日期": "2019-05-15 19:48:06", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "2b9f7792c7028875d301ccf49320810783c4d195", "commit_訊息": "5.8.1.1 設定改讀取SystemVariable資料表", "提交日期": "2019-05-15 18:30:52", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SystemConfigManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/NaNaSysIntUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SimpleExpenseAccountItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/util/CrmUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/TiptopUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/MailSenderUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/PersonalDataProtectionUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AddCustomActivityAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/mail_timer/MailTimerTaskManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebPropsLoader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/CrossIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 35}, {"commit_hash": "d5e87be02b1700f9ea8b54864fff34fcdb5639ab", "commit_訊息": "調整授權中間層編輯完後上方divToolBar按鈕未顯示問題", "提交日期": "2019-05-15 16:16:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d6288eb2c49ce21e794ab5a235e53cb47eaac364", "commit_訊息": "Q00-20190515006 修正移動授權中間層維護頁面的動態參數區塊", "提交日期": "2019-05-15 16:01:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d411cc0cf165a1ea33c091ce658d11fbd63a85f", "commit_訊息": "修正移動授權中間層維護介面功能", "提交日期": "2019-05-15 14:11:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "af4133a889be69c8534053896ba0371d7432f580", "commit_訊息": "S00-20190514003 補上create指令更新", "提交日期": "2019-05-15 11:56:00", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5810e2b3d5edb6da91851607bba85d9a23def2a4", "commit_訊息": "調整移動授權中間層維護介面支援OAuth", "提交日期": "2019-05-15 09:30:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/Common.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "d8808b534166c138a7419924f3e69fa5f50adf14", "commit_訊息": "S00-20190514003 BPM需要根據T100所傳xml中資訊，利用文檔中心API取得附件檔案", "提交日期": "2019-05-14 19:34:24", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/SysintegrationServer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTag.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SysintegrationSetAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-sysintegrationSet-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_bpm.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.8.1.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "0a9ce0310ea2e17b29695d05ce0cb11568eb470a", "commit_訊息": "新增處理的流程-未結案篩選服務", "提交日期": "2019-05-14 18:58:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "18943a48f19b670cf03a797d264e30ebab660c7b", "commit_訊息": "新增智能示警、智能快簽、重要流程三種筆樹統計服務", "提交日期": "2019-05-14 15:29:52", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "27d852b395ef1cd3cdeeffe7623dfa6a8053f1eb", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-05-14 09:59:26", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 4}, {"commit_hash": "28d5dda85c306c3ed66a98535f8de71e2fe197a4", "commit_訊息": "新增使用者認證身分暫存功能", "提交日期": "2019-05-14 09:38:49", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterUser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterUserDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "c88753528a490f21a17fdfa533e40be1a6bf7a1d", "commit_訊息": "調整授權中間層共用參數化方法", "提交日期": "2019-05-13 16:27:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterCommonTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "f580700ca1a56e895880faa7ce15e49339f5c4d4", "commit_訊息": "C01-20190416003 調整進入單筆代辦需要花較多時間的項目log，原本單位秒調整成毫秒", "提交日期": "2019-05-13 11:07:08", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7abf9d728922dc00c9e642f36a411142b58a8401", "commit_訊息": "S00-20190510001 調整登入右上角之人形圖示，下拉項目調整", "提交日期": "2019-05-10 17:59:20", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5811.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "ee3245886ccca9d96a8503d59ccea25646d7cdf7", "commit_訊息": "Q00-20190510002 整合使用URL開表單設計師未登入，避免重導太慢執行到下面程式導致報錯", "提交日期": "2019-05-10 17:36:55", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aaeb51421664e1ea889b62674a1e4d3f005f1965", "commit_訊息": "20190510 yanann_chen A00-20190215001 修正工作通知表單資料顯示異常問題", "提交日期": "2019-05-10 16:00:33", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9bff523ba4a3b52028521ce1bffda1c2e0d41ba", "commit_訊息": "調整移動授權中間層取得應用服務", "提交日期": "2019-05-09 12:06:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9e59609a52ebe297cd7fa7151a7afc86af0b260e", "commit_訊息": "將之前新增的多語系與SQL的版號改為5.8.1.1", "提交日期": "2019-05-09 11:19:46", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 7}, {"commit_hash": "18c19a7c6f8b37db2156d250ad58c2c3a4afb82d", "commit_訊息": "調整授權中間層資料庫欄位", "提交日期": "2019-05-09 11:12:06", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterApp.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterAppDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterOAuthConfigDTO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "4d81dcc980628a937b496d6696fbf12f10baf112", "commit_訊息": "新增移動授權中間層OAuth端功能 1. 跳轉認證網址 2. 獲取OAuth帳號 3. 驗證OAuth歸戶與登入", "提交日期": "2019-05-09 11:08:49", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterCommonTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterDintalkTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterOAuthTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterToolFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileRESTTransferTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "05e662022119d18135e5f2b27dac2452cc7ef826", "commit_訊息": "Q00-20190507001 移除「流程設計師-核決權限-選擇樣板」這個按鈕", "提交日期": "2019-05-07 14:03:45", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/decision/DecisionRuleListEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/activity/decision/DecisionRuleListEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "770e16ca0188706aef38994ab0aec5b2d0a1559f", "commit_訊息": "C01-20190107001 補修正 TextArea在chrome未正常撐開", "提交日期": "2019-05-07 11:02:10", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "89013662e9128d5b0cf1b0ca3f54d82ceb911d2e", "commit_訊息": "Q00-20190506001 移除「流程設計師-流程模型-可重定義屬性-語言、國家」這兩個屬性", "提交日期": "2019-05-06 15:15:02", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/processpackage/ProcessPackageMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/processpackage/ProcessPackageMCERTableModel.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "314efa3913ad61cd0121cd4a2bd09dec3ad967ec", "commit_訊息": "A00-20190502001 ISO文件作廢單的瀏覽文件資料需要控制文件閱讀權限", "提交日期": "2019-05-03 17:28:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "99e6353d8bb5c02cd2857addc8a3f2af429c40c2", "commit_訊息": "A00-20190401001 修正T100通知信內容，會有部分欄位重複", "提交日期": "2019-05-02 16:12:12", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1395e1a82a0634f3dd55cd21a0e97519640bc6bb", "commit_訊息": "調整IMG待辦與追蹤的表單詳情畫面中流程關注功能", "提交日期": "2019-05-02 14:59:34", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "0efe2c58cf6027a0a40ec6a7b9571540eb269ffe", "commit_訊息": "Q00-20190409001 修正行動版表單設計雙欄位模板時，Button元件欄位高度異常", "提交日期": "2019-05-02 11:57:28", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b0365139b58cad9c5afd7d4325f34d768ecdb09", "commit_訊息": "新增IMG待辦與追蹤的表單詳情畫面中流程關注功能", "提交日期": "2019-04-30 17:15:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "b0c5ce54ecf95584bac6099f94aa4c34eaff7d2c", "commit_訊息": "Q00-20190429003 調整授權中間層使用者維護管理介面", "提交日期": "2019-04-30 15:06:33", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "dabc880f5f29f21a211ccaa49476b93b98be859a", "commit_訊息": "新增移動端支持客製RESTFul開窗", "提交日期": "2019-04-30 13:56:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 23}, {"commit_hash": "52a8e0de8e890e477c39193ec024cda2c9226ea9", "commit_訊息": "調整加入重要流程功能相關的函式名稱", "提交日期": "2019-04-30 12:07:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileUserAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "c5373b732816b8eed3c89e1217aa8db526403ae2", "commit_訊息": "C01-20190430001 修正原XPDL流程連接線name為null(畫面看到都是空白)會導致導致套件底層出錯", "提交日期": "2019-04-30 11:54:11", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/util/ConversionXPDLProcess.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/.gitignore", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9d6792c7bfbf9936ab7c24638b5dadb2d414a225", "commit_訊息": "A00-20190423002 修正移動端在value內含引號時會被截斷問題", "提交日期": "2019-04-29 16:50:48", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerDialog.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerInput.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerText.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "a0f1338e6cf6c1b21af11c1fee10f26352944465", "commit_訊息": "A00-20190426002 <BPM57only>調整搜尋後，動態的計算資料區域高度因為搜尋視窗沒有先關閉導致計算錯誤", "提交日期": "2019-04-29 16:34:04", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "394a0a1c7c9bc932c0772eb663da7de25ae1f8d4", "commit_訊息": "Q00-20190429001 授權中間層測試與修正", "提交日期": "2019-04-29 14:05:57", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "6dbc8314da5637c67137c66795542d0eeb072483", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-04-29 09:42:55", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 2}, {"commit_hash": "1ee243427de2461835d53759776670b4c5bbdf43", "commit_訊息": "修正授權中間層設定檔維護作業關連問題", "提交日期": "2019-04-29 09:41:05", "作者": "劉建德", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c72ea0e34a5019fdb7bf613216ee4989a1c93dcd", "commit_訊息": "合併衝突,修正設定檔維護作業功能", "提交日期": "2019-04-29 09:37:26", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "修改", "狀態代碼": "MA"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "MM"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "MA"}], "變更檔案數量": 6}, {"commit_hash": "609c56a6d64bbdb3e3e20facd7be828903fd7476", "commit_訊息": "修正移動授權中間層使用者歸戶頁面在取得清單有錯誤時隱藏遮罩使其可正常操作", "提交日期": "2019-04-26 17:43:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d3efb71c6e4e717d53e3626648088664f74924c3", "commit_訊息": "Q00-20190426001 哲瑋發現在流程進行期間去修改流程緊急度 資料庫連線未關閉", "提交日期": "2019-04-26 16:38:15", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d74cace0aa1bed11a8a34374ffb0bd810f1931e", "commit_訊息": "調整移動授權中間層使用者管理頁面", "提交日期": "2019-04-25 18:50:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "17e6ad21c4455183759fc22ad69c8c8bf69d932a", "commit_訊息": "C01-20190416003 增加進入單筆代辦需要花較多時間的項目log", "提交日期": "2019-04-25 17:31:45", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc885a88725cefb718539c4ac64579ba548a31c2", "commit_訊息": "Q00-*********** 調整整合發單先記錄發起訊息再發起流程", "提交日期": "2019-04-25 17:06:36", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3603bc49f43eaacf79bb74afab341b201a291deb", "commit_訊息": "新增移動授權中間層取得應用服務", "提交日期": "2019-04-25 15:06:29", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AdapterExtraServiceParameterReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AdapterExtraServiceReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AdapterExtraServiceStdDataReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AdapterExtraServiceExecutionRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AdapterExtraServiceParameterRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AdapterExtraServiceRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/response/AdapterExtraServiceStdDataRes.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Adapter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 9}, {"commit_hash": "b0ff43e4665efc0be718c9e178a150b03d4245a3", "commit_訊息": "新增移動授權中間層企業微信端功能", "提交日期": "2019-04-25 14:43:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterAbstractTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterToolFactory.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterWeChatTool.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/web.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "f8aafb2f0aa5f160eafa89deb63ba7af5ec0b4f0", "commit_訊息": "調整移動授權中間層使用者維護管理服務ajax", "提交日期": "2019-04-25 13:47:26", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7f1bfac228b40891c71d21f30d7335750accd777", "commit_訊息": "Q00-20190425001 單純卡控發起表單、簽核時按下backspace，不再詢問使用者是否回上一頁", "提交日期": "2019-04-25 09:54:38", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "17b483673b7b06a6eae11c9b542a3d118977c98a", "commit_訊息": "新增中間層設定檔維護作業", "提交日期": "2019-04-24 18:12:00", "作者": "劉建德", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "c6897dc301d4f36bebabb4c3c2508652379fa5ce", "commit_訊息": "新增中間層設定檔維護作業", "提交日期": "2019-04-24 18:11:44", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/adapter/AdapterUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/Common.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "eb7d3fe3579dcd9905c3f4e97c6ef5e11ceb738a", "commit_訊息": "新增移動授權中間層使用者維護管理頁面與服務", "提交日期": "2019-04-24 16:53:54", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterApp.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/AdapterVo.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 15}, {"commit_hash": "bd92c7825e4c28fe271d0622612e2983c7dd4ac5", "commit_訊息": "調整腳本樣板搜尋功能SQL漏寫部分", "提交日期": "2019-04-23 13:40:20", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b87d480329d519f23b11456c5e95650fc692c352", "commit_訊息": "A00-20190411001 調整CRM的換行符號", "提交日期": "2019-04-22 17:05:41", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3f44f60ad3ec0e25087b1952482a383c06cb46e3", "commit_訊息": "新增腳本樣板搜尋功能", "提交日期": "2019-04-22 15:36:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormScriptTemplate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRwdFormScriptEditor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormScriptEditor.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "ec4e0a34ff73609e3b6147260137247e27fae53f", "commit_訊息": "新增移動授權中間層使用者資訊資料庫管理程式與結構", "提交日期": "2019-04-19 15:01:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterUser.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterUserDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "b56ee69a5c01774bcd50ce6366e08f3a2e9d9ca5", "commit_訊息": "A00-20190418001 修正移動端Grid中的欄位資料無法顯示出資料全貌議題", "提交日期": "2019-04-19 11:15:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cbda7e1f2ad71eaf6e76a305b1170b0f5c8c52f4", "commit_訊息": "C01-20190411002、C01-20190412003 修正遺漏部分", "提交日期": "2019-04-18 18:49:07", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d6a01631e0bb606c51ae2af2913abc2b9409c74f", "commit_訊息": "修改/v1/identity/accesstoken/grant,加入Adapter類型判斷", "提交日期": "2019-04-17 16:49:46", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/GrantAccessTokenParameterReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AdapterMgr.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 4}, {"commit_hash": "5e8d7fa3245f12aee30fe8afa38f1abf0dfe9f5f", "commit_訊息": "新增移動授權中間層連線資訊資料庫管理程式與結構", "提交日期": "2019-04-17 15:46:15", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterApp.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/AdapterConfig.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterAppDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/AdapterOAuthConfigDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerLocal.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 14}, {"commit_hash": "38fd2a1e48781140690e0125d047d9b42c706c48", "commit_訊息": "A00-20190415001 20190417 Catherine 異常處理", "提交日期": "2019-04-17 11:02:45", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5843618e2a147821a68cc41cb0b66929d682446b", "commit_訊息": "C01-20190411002、C01-20190412003 修正行動版相對位置表單grid內容顯示異常問題", "提交日期": "2019-04-17 10:25:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "96680aa970530f092077ca629fd6c5b33004c9c9", "commit_訊息": "C01-20190311005 修正客戶的FormScript onkeydown function失效問題", "提交日期": "2019-04-16 14:08:00", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a51cb16b0635902ed9a0bc473235e17e042a7340", "commit_訊息": "Q00-20190415004 修正<57>若已有設計行動版絕對位置表單,在沒啟用App情況下還是會顯示", "提交日期": "2019-04-16 11:41:42", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f0c4ac08bff16984b5f034add4ba6be889400497", "commit_訊息": "修正 :E10表單同步 ,因為有隱藏欄位,在Merge時把隱藏欄位放在 RWDlayout導致開啟表單設計師異常", "提交日期": "2019-04-15 18:11:27", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b712323754621840ccec24fa4ff03ed270f40cfd", "commit_訊息": "A00-20190328001 修正Android手機在附件列表畫面會有重疊問題", "提交日期": "2019-04-15 13:29:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d7c0894881c8bac74d9d2086bf34bc115df7e03", "commit_訊息": "A00-20190411001 調整CRM增加判斷eol的換行符號", "提交日期": "2019-04-15 10:16:41", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/crm/MethodCreateForm.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f45b6dd78c9ce7415c024a0ce2812029b15fc20e", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-04-15 09:02:56", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "609073f74eb050566b3b263a9e613ae3beb5a313", "commit_訊息": "A00-20190219001 修正RWD表單列印看不到附件的問題", "提交日期": "2019-04-12 18:25:20", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3f8aad521d49cbf12fe6a44176636906cf4f3c52", "commit_訊息": "Q00-20190412002 智能快簽與智能學習紀錄排程修正", "提交日期": "2019-04-12 12:44:50", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkAssignment.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "02418d9a26e39b86c521e49b37553f74ae9e67c0", "commit_訊息": "<V57> S00-20181001001 新增:通知信樣版增加表單內容變數,在產生通知信時會將變數替換成表單內容, 僅支援關卡通知信、RWD表單、*特例絕對訂為表單為T100流程也會生程表單內容", "提交日期": "2019-04-12 11:50:52", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/common/PatternEditorPanel.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/processpackage/VariableNamesList.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/IProcessVariableNames.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "7e0cafff656b1aca1ba82f957a308dffe6387aa7", "commit_訊息": "C01-20180418003 修正模擬使用者時就算有授權使用者設計師也不應該看到", "提交日期": "2019-04-11 19:28:32", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d0a32280389edaff4a9db8549a6fcd79771c3aa8", "commit_訊息": "新增:*******的patch檔", "提交日期": "2019-04-11 17:01:13", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "afca160c18b9d3a9f71227ee4476653792102c7b", "commit_訊息": "調整 :Cross同步後，E10的IP卻沒更改的異常", "提交日期": "2019-04-11 17:00:56", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df1ab594b961ebfad95ef00445f3de5270bf8fd8", "commit_訊息": "調整 :印log的方式", "提交日期": "2019-04-11 16:58:51", "作者": "施翔耀", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08b32e658c4c31afbfe65037503fec7eca85660f", "commit_訊息": "A00-20190329002 修正轉換信件內容時加入防呆機制", "提交日期": "2019-04-10 14:08:59", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "995d5c17153190562d74ebd71bfc531911f63666", "commit_訊息": "Q00-20190410003 修正restful的postData的編碼問題", "提交日期": "2019-04-10 13:55:37", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0b7553ab3cb84ce6ff7d21d411ab333e8e3508fc", "commit_訊息": "A00-20190315003 修正上傳附件後表單全域變數的locale會跑掉的錯誤", "提交日期": "2019-04-09 19:16:12", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9c2de9776f9c72eb8a4a134e1d03aecba9348eb5", "commit_訊息": "新增智能快簽中的擱置功能", "提交日期": "2019-04-09 16:41:26", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/IntelligentLearningDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearning.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 18}, {"commit_hash": "8aabb276f333b1f0ac2caa620fc080d5ddad78c2", "commit_訊息": "新增智能學習相關功能", "提交日期": "2019-04-09 16:21:24", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileIntelligentWorkItemComparator.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "8e736edf012d768d11eb392440ba08ff39761e64", "commit_訊息": "新增智能學習相關功能", "提交日期": "2019-04-09 15:59:33", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/IntelligentLearningDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/IntelligentLearningRecord.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/mobile/external/IntelligentLearningSchedule.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkAssignment.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/IntelligentLearningScheduleDTO.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearning.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningLocal.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IntelligentLearningAccessor.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/IntelligentLearningManage.jsp", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5761.xls", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/create/InitMobileDB_SQLServer.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@mobile/db/update/5.7.6.1_updateSQL_SQLServer.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 28}, {"commit_hash": "357bdaff07824bb88e149d41ff8909a6ceca21a2", "commit_訊息": "調整智能快簽收件夾服務,新增自動計算功能", "提交日期": "2019-04-09 14:30:58", "作者": "劉建德", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/mplatform/MobileMPlatformRESTFulService.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DinwhaleV2.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/AbstractMobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MgrFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleButton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleParameter.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 9}, {"commit_hash": "e2724945a671ab44669e2a9267fb90e7246a4219", "commit_訊息": "A00-20181023001 修正通知關卡的進階設定不允許列印 但從通知信連結進入 畫面上有列印紐", "提交日期": "2019-04-08 15:45:27", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4aedefabf72457718008b2bbe075b93e668f5a7a", "commit_訊息": "A00-20190318001 修正流程並簽且關卡都為自動簽核時，有可能會導致其中一個關卡無法往下派送。", "提交日期": "2019-04-01 15:59:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "21fcd351cb1268b812b7acf88b0946cea4d24b36", "commit_訊息": "C01-20190401001 修正Web表單設計師中行動版相對位置的表單元件上圖案變文字問題", "提交日期": "2019-04-01 11:08:53", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "54affdc01267243e028886d46e3c499914bb4b20", "commit_訊息": "A00-20190315004 修正RWD表單設計師SUBTAB元件無法新增樣板、開窗元件複製後屬性重複", "提交日期": "2019-03-29 16:15:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/util.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "122f730b1b12a6a12bb2695200886dfdc7aa7b2d", "commit_訊息": "Q00-20190329001 修正用一般使用者查詢ISO文件一覽表,查詢條件為已生效的文件,但卻會查出失效的文件", "提交日期": "2019-03-29 10:50:31", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/listreader/ISODocListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f43d5c586465a8a7b9079d09b354f9baa7b35d00", "commit_訊息": "更換修正方式A00-20190227001 修正Grid 自動產生的按鈕 權限呈現不正確", "提交日期": "2019-03-28 20:06:37", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "fe9fbb7676555debfc4b6ffabb988ec0db03dece", "commit_訊息": "補修正A00-20190227001 修正如果課戶FormDispatch false Grid自動產生的按鈕disable設定會解開", "提交日期": "2019-03-27 16:23:25", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "3d97f8c0cb67a01e0a0d3e0d6488b632339444da", "commit_訊息": "補修正A00-20190227001 修正如果課戶FormDispatch false Grid自動產生的按鈕disable設定會解開", "提交日期": "2019-03-27 16:23:25", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e24a1863eb8e6a5e609730a250c7849e0e5c5761", "commit_訊息": "A00-20190227001 修正如果客戶FormDispatch return false Grid自動產生的按鈕disable設定會解開", "提交日期": "2019-03-27 14:30:18", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}