# Release Notes - BPM

## 版本資訊
- **新版本**: release_*******
- **舊版本**: release_5.8.8.3
- **生成時間**: 2025-07-18 11:35:59
- **新增 Commit 數量**: 270

## 變更摘要

### cherry<PERSON>o (25 commits)

- **2022-11-16 14:27:02**: [Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題
  - 變更檔案: 1 個
- **2022-10-18 09:18:27**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-10-11 11:29:38**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-10-06 17:14:18**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-10-06 11:29:53**: [流程設計師]Q00-20221006001 調整在流程設計點擊編輯表單欄位權限時，若表單發行狀態已過期或UNDER_REVISION時會彈提示訊息
  - 變更檔案: 5 個
- **2022-10-05 15:52:05**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-10-04 10:28:05**: [表單設計師]Q00-20221004001 修正DialogInputLabel元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容
  - 變更檔案: 1 個
- **2022-09-30 14:23:17**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-09-23 16:53:05**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-09-23 11:31:10**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-09-16 13:48:45**: [Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題
  - 變更檔案: 1 個
- **2022-09-08 16:46:53**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-09-08 14:06:21**: [Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息
  - 變更檔案: 2 個
- **2022-09-01 18:37:03**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-29 11:03:27**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-25 18:39:57**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-25 15:41:20**: [Web]Q00-20220825003 修正程式權限設定套用範圍為部門但不包含子部門，在修改編輯時卻勾選包含子部門的問題
  - 變更檔案: 1 個
- **2022-08-17 14:56:49**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-10 17:55:30**: [內部]新增組織設計工具使用的服務[補]
  - 變更檔案: 2 個
- **2022-08-10 11:01:13**: [Web]A00-20220808001 調整報表查詢產出的日期與匯出Excel的日期不一致問題
  - 變更檔案: 1 個
- **2022-08-05 17:28:13**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-05 11:44:44**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-02 10:59:59**: [內部]調整系統設計工具Web化組織樹接口
  - 變更檔案: 1 個
- **2022-07-28 14:39:59**: [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]
  - 變更檔案: 1 個
- **2022-07-27 17:51:33**: [Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題
  - 變更檔案: 1 個

### raven.917 (30 commits)

- **2022-11-01 12:00:20**: [WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定
  - 變更檔案: 1 個
- **2022-10-19 17:09:54**: [WEB]Q00-20221014001為相容56版上來的客戶，沒有唯讀背景顏色設定，補防呆
  - 變更檔案: 5 個
- **2022-10-17 10:37:56**: [WEB]Q00-20221014003修正變更經常選取對象 & 變更您的關係人，更新資料後沒有即時刷新頁面問題。
  - 變更檔案: 3 個
- **2022-10-17 08:52:43**: [WEB]A00-20221014001修正簽核意見斷行顯示。
  - 變更檔案: 1 個
- **2022-10-14 12:52:12**: [WEB]Q00-20221014001為相容56版上來的客戶，沒有唯讀背景顏色設定。
  - 變更檔案: 9 個
- **2022-10-14 09:17:30**: [內部]V00-20221012008修正流程管理/監控流程 選擇「已關閉」流程，匯出Excel發現多了簽核時間的欄。
  - 變更檔案: 1 個
- **2022-10-13 15:36:41**: [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。(改)
  - 變更檔案: 1 個
- **2022-10-13 14:03:06**: [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。
  - 變更檔案: 1 個
- **2022-10-13 11:28:13**: [WEB]Q00-20221013002:修正表單欄位有設定 "唯讀"時的欄位顏色，顯示卻都為背景顏色。
  - 變更檔案: 5 個
- **2022-10-13 11:06:45**: [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，找不到欄位ID時的Alert訊息
  - 變更檔案: 1 個
- **2022-10-13 08:37:30**: [WEB]Q00-20221013001修正簽核意見沒有換行符號。
  - 變更檔案: 1 個
- **2022-10-12 11:33:33**: [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，ID為null時的Alert訊息。
  - 變更檔案: 1 個
- **2022-10-11 09:20:14**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2022-10-11 09:20:02**: [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(修)
  - 變更檔案: 1 個
- **2022-10-07 11:05:35**: [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。(補修正多語系)
  - 變更檔案: 1 個
- **2022-10-06 14:57:39**: [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。(補修正多語系)
  - 變更檔案: 1 個
- **2022-10-06 14:35:32**: [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。
  - 變更檔案: 2 個
- **2022-10-06 08:57:33**: [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用(補修正，增加可讀性)
  - 變更檔案: 1 個
- **2022-10-04 15:26:58**: [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用
  - 變更檔案: 1 個
- **2022-09-28 10:55:38**: [WEB]S00-***********新增線上使用者最大閒置時間系統變數給系統管理員可控制
  - 變更檔案: 5 個
- **2022-09-28 10:55:03**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2022-09-27 10:51:44**: [web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。(補修正)
  - 變更檔案: 3 個
- **2022-09-27 09:59:18**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2022-09-26 15:02:43**: [web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。
  - 變更檔案: 4 個
- **2022-09-21 16:42:30**: [WEB] C01-20220919007 Admin 需要能開啟設計師時，直接針對該表單做復原簽出的操作行為。
  - 變更檔案: 2 個
- **2022-09-21 10:40:08**: [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)
  - 變更檔案: 1 個
- **2022-09-20 18:09:03**: [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)
  - 變更檔案: 1 個
- **2022-09-20 16:57:50**: [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。
  - 變更檔案: 2 個
- **2022-09-19 14:33:11**: [表單設計師]S00-***********系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷，四則運算及單身加總運算。(補修正)
  - 變更檔案: 1 個
- **2022-09-16 14:46:47**: [表單設計師]S00-***********系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷。
  - 變更檔案: 1 個

### 林致帆 (70 commits)

- **2022-10-26 16:02:29**: [雙因素模組]Q00-20221026005 在未授權時，BPM首頁左側功能列會顯示雙因素模組功能
  - 變更檔案: 1 個
- **2022-10-24 10:16:39**: [WorkFlow]Q00-20221024001 WorkFlow移除標準表單ASTI02
  - 變更檔案: 1 個
- **2022-10-21 14:39:33**: [雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]
  - 變更檔案: 1 個
- **2022-10-21 14:30:35**: [雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]
  - 變更檔案: 1 個
- **2022-10-21 11:38:44**: [雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]
  - 變更檔案: 1 個
- **2022-10-21 11:06:23**: [雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤
  - 變更檔案: 1 個
- **2022-10-17 14:30:14**: [組織同步]Q00-20221017001 調整5883 updateSQL判斷中介表有建立就不需要在移除後重新再建立
  - 變更檔案: 2 個
- **2022-10-14 17:38:20**: [WorkFlow]]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能
  - 變更檔案: 5 個
- **2022-10-14 11:22:59**: [雙因素模組]V00-20221012001 修正郵件認證在登入輸入錯誤密碼還是會寄驗證信[補修正]
  - 變更檔案: 1 個
- **2022-10-13 11:46:34**: [雙因素模組]V00-20221012002 移除未認證清單的搜尋按鈕
  - 變更檔案: 1 個
- **2022-10-13 11:42:35**: [雙因素模組]V00-20221012001 修正郵件認證在登入輸入錯誤密碼還是會寄驗證信
  - 變更檔案: 1 個
- **2022-10-12 15:45:47**: [WorkFlow]Q00-20221012003 調整WF傳狀態Action為5時，回寫關卡須回傳狀態8回去
  - 變更檔案: 1 個
- **2022-10-11 19:17:51**: [Web]Q00-20221011001 修正ESS表單開啟沒有ESS畫面
  - 變更檔案: 1 個
- **2022-10-06 13:59:05**: [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
  - 變更檔案: 1 個
- **2022-10-06 13:56:04**: [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
  - 變更檔案: 1 個
- **2022-10-04 17:59:50**: [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
  - 變更檔案: 3 個
- **2022-10-04 16:45:09**: [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
  - 變更檔案: 1 個
- **2022-10-04 14:51:39**: [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
  - 變更檔案: 6 個
- **2022-09-30 18:45:25**: [內部]MFA雙因素模組名稱更改成TFA雙因素模組
  - 變更檔案: 23 個
- **2022-09-30 09:04:30**: [WorkFlowERP]S00-*********** 調整WorkFLow取簽核歷程及取簽核頁面URL邏輯
  - 變更檔案: 7 個
- **2022-09-27 11:50:22**: [ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核
  - 變更檔案: 1 個
- **2022-09-27 11:47:05**: Revert "[ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核"
  - 變更檔案: 1 個
- **2022-09-27 11:34:54**: [ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核
  - 變更檔案: 1 個
- **2022-09-27 08:36:26**: [T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容
  - 變更檔案: 1 個
- **2022-09-26 16:46:17**: [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
  - 變更檔案: 2 個
- **2022-09-21 14:56:53**: [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
  - 變更檔案: 1 個
- **2022-09-21 14:54:38**: [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
  - 變更檔案: 3 個
- **2022-09-21 13:49:55**: [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
  - 變更檔案: 3 個
- **2022-09-21 13:42:52**: Revert "[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]"
  - 變更檔案: 3 個
- **2022-09-21 13:40:30**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2022-09-21 13:39:25**: [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
  - 變更檔案: 3 個
- **2022-09-20 17:25:45**: [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證
  - 變更檔案: 21 個
- **2022-09-20 14:39:54**: [內部]Q00-20220920002 T100傳附件用http方式且未帶drivetoken的tag內容，增加log訊息提示修正T100
  - 變更檔案: 1 個
- **2022-09-08 14:26:10**: [表單設計師]Q00-20220908001 修正subTab元件無法編輯
  - 變更檔案: 1 個
- **2022-09-07 17:39:10**: [組織同步]Q00-20220907004 修正TT組織同步失敗
  - 變更檔案: 2 個
- **2022-09-07 17:15:14**: [Web]Q00-20220907003 修正TIPTOP附件無法下載
  - 變更檔案: 1 個
- **2022-09-07 08:38:05**: [Web]Q00-20220906004 修正系統管理員登入BPM不應該發雙因素模組的驗證信
  - 變更檔案: 1 個
- **2022-09-05 17:48:01**: [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
  - 變更檔案: 4 個
- **2022-09-05 17:39:22**: Revert "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]"
  - 變更檔案: 4 個
- **2022-09-05 17:33:28**: [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
  - 變更檔案: 4 個
- **2022-09-05 17:28:50**: Revert "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]"
  - 變更檔案: 4 個
- **2022-09-05 17:26:35**: [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
  - 變更檔案: 4 個
- **2022-09-05 17:19:03**: Revert "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確"
  - 變更檔案: 4 個
- **2022-09-05 17:17:31**: [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確
  - 變更檔案: 4 個
- **2022-08-30 08:34:29**: [WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能
  - 變更檔案: 1 個
- **2022-08-22 16:19:00**: [Web]Q00-20220822002 修正登入頁輸入錯誤帳號，會跳出非標準的錯誤訊息
  - 變更檔案: 1 個
- **2022-08-22 14:11:47**: [Web]Q00-20220822001 修正BPM使用IE瀏覽器上傳附件時會失敗
  - 變更檔案: 1 個
- **2022-08-19 17:57:37**: [TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久
  - 變更檔案: 1 個
- **2022-08-18 17:50:32**: [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]
  - 變更檔案: 1 個
- **2022-08-18 17:47:19**: [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]
  - 變更檔案: 2 個
- **2022-08-18 17:41:56**: [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到
  - 變更檔案: 2 個
- **2022-08-18 14:18:06**: [WorkFlow]Q00-20220818004 優化易飛，WorkFlow回傳的Debug訊息
  - 變更檔案: 1 個
- **2022-08-18 11:54:05**: [TIPTOP]A00-20220816001 調整整合產品的createSQL的新增workflow主機指令[補修正]
  - 變更檔案: 4 個
- **2022-08-18 11:27:17**: [Web]Q00-20220818001 儲存系統設定T100整合時，會跳出密碼政策的錯誤訊息
  - 變更檔案: 1 個
- **2022-08-17 14:05:42**: [TIPTOP]A00-20220816001 調整整合產品的createSQL的新增workflow主機指令
  - 變更檔案: 4 個
- **2022-08-17 11:58:58**: [Portal]]Q00-20220817001調整有整合Portal，用查看流程圖的外部portlet，導入的畫面不是BPM而是Portal的登入頁面
  - 變更檔案: 1 個
- **2022-08-10 15:47:45**: [Web]Q00-20220810001 修正設定模擬使用者給一般人員，用模擬使用者模擬一般人員，會出現兩筆模擬使用者的作業
  - 變更檔案: 1 個
- **2022-08-09 15:40:54**: [T100]Q00-20220809005 修正T100拋單，附件為從文檔中心取得的，檔案大小與實際大小不符合
  - 變更檔案: 1 個
- **2022-08-09 10:47:45**: [易飛]Q00-20220809001 調整易飛出貨流程的回寫事件
  - 變更檔案: 1 個
- **2022-08-04 16:21:09**: [內部]Q00-20220804002 優化ContextManager的log訊息
  - 變更檔案: 1 個
- **2022-08-02 11:27:26**: [Web]A00-20220802001 修正無法開啟SAP維護作業
  - 變更檔案: 1 個
- **2022-07-29 17:50:27**: [易飛]A00-20220729001 修正PURI09表單的Script單身欄位代號錯誤
  - 變更檔案: 1 個
- **2022-07-29 14:34:14**: [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]
  - 變更檔案: 1 個
- **2022-07-29 14:02:17**: [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]
  - 變更檔案: 1 個
- **2022-07-28 15:20:57**: [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
  - 變更檔案: 3 個
- **2022-07-27 16:27:04**: [Web]S00-20220718006 新增模組-雙因子認證模組[補修正]
  - 變更檔案: 1 個
- **2022-07-27 10:41:09**: [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法
  - 變更檔案: 1 個
- **2022-07-26 09:25:27**: [Web]S00-20220718006 新增模組-雙因子認證模組[補修正]
  - 變更檔案: 1 個
- **2022-07-25 17:19:49**: [內部]Q00-20220725003 Index指令回收
  - 變更檔案: 4 個
- **2022-07-25 16:39:57**: [TIPTOP]Q00-20220519002 axmt700_icd表單入版
  - 變更檔案: 2 個

### waynechang (26 commits)

- **2022-10-24 15:02:42**: [內部]更新******* patch檔
  - 變更檔案: 1 個
- **2022-10-19 17:07:47**: [內部]新增B2B文件雲
  - 變更檔案: 1 個
- **2022-10-18 17:38:26**: Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **2022-10-18 17:38:01**: [內部]更新******* patch檔
  - 變更檔案: 1 個
- **2022-10-14 14:45:17**: [內部]V00-20221012007 調整在線閱覽管理/轉檔異常處理作業 的「執行」欄位寬度
  - 變更檔案: 1 個
- **2022-10-14 14:29:50**: [流程設計師]A00-20221012001 修正流程設計師當子流程有變更代號時，流程簽入新版時，資料庫的子流程代號未更新
  - 變更檔案: 1 個
- **2022-10-12 11:38:23**: [內部]更新******* patch檔
  - 變更檔案: 1 個
- **2022-10-12 11:21:47**: [內部]修正IndexNaNaDB_Oracle建立索引名稱過長問題
  - 變更檔案: 1 個
- **2022-10-04 16:10:18**: [內部]更新******* patch檔
  - 變更檔案: 1 個
- **2022-10-03 16:00:26**: [內部]Q00-20221003004 增加Table相關Index
  - 變更檔案: 4 個
- **2022-10-03 13:59:51**: [流程引擎]Q00-20221003002 流程預先解析支持流程設計關卡型態為「活動簽核人」的活動
  - 變更檔案: 1 個
- **2022-10-03 10:27:09**: [流程設計師]Q00-20221003001 調整簽核流設計師，將流程設計師原有的「活動定義/選擇參與者/活動簽核人」重新加回簽核流設計師中
  - 變更檔案: 5 個
- **2022-09-21 14:14:38**: [流程引擎]Q00-20220921001 調整發起流程頁面；由表單畫面切換至流程圖時，可根據當前表單內容進行流程預解析
  - 變更檔案: 2 個
- **2022-09-20 14:55:50**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **2022-09-20 14:55:38**: [TIPTOP]A00-20220919001 新增TIPTOP整合設定，當夾帶附件型態為http,根據TIPTOP附件主機的port號取得附件
  - 變更檔案: 5 個
- **2022-09-20 11:19:53**: [Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊
  - 變更檔案: 1 個
- **2022-09-15 17:24:04**: [流程引擎]Q00-20220915001 修正簡易流程圖若流程有設計迴圈型且線的條件剛好為兩個Gateway互為下一關時，加入防呆避免系統Crash
  - 變更檔案: 1 個
- **2022-08-25 14:52:12**: [流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡
  - 變更檔案: 1 個
- **2022-08-18 11:43:00**: [流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤
  - 變更檔案: 1 個
- **2022-08-15 17:45:31**: [在線閱覽]Q00-*********** 更新PDFjs閱讀器版本(2.3.200)，原因為修正部分PDF因字形而顯示異常的錯誤
  - 變更檔案: 86 個
- **2022-08-12 15:03:20**: [流程引擎]S00-20220613004 優化流程預解析功能-1.核決關卡加簽後，預覽流程圖即可顯示，2.核決關卡有前置關係人時，預覽流程圖即可顯示，3.流程發起關卡的預覽流程圖，若使用者有多個發起部門，可動態依使用者選擇的部門做解析
  - 變更檔案: 10 個
- **2022-08-08 16:01:52**: [流程引擎]Q00-20220803002 調整流程主機呼叫其他流程主機清除系統快取的服務，若其他主機無法連線時，逾時時間由20秒改為1秒[補]
  - 變更檔案: 1 個
- **2022-08-08 14:51:00**: [流程引擎]Q00-20220803002 調整流程主機呼叫其他流程主機清除系統快取的服務，若其他主機無法連線時，逾時時間由20秒改為1秒
  - 變更檔案: 1 個
- **2022-08-08 14:36:18**: [內部]更新5.8.8.3 patch檔
  - 變更檔案: 1 個
- **2022-08-05 17:26:46**: [Web]Q00-20220805002 調整log訊息，當流程向後派送，後面關卡解析的使用者找不到或是沒有主部門時，增加log訊息
  - 變更檔案: 1 個
- **2022-08-05 14:06:24**: [Web]Q00-20220805001 修正作業程序書沒有顯示核決層級關卡的作業名稱
  - 變更檔案: 2 個

### pinchi_lin (30 commits)

- **2022-10-24 13:29:55**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-10-21 20:36:58**: [內部]新增提供給組織設計工具Web化使用的多語系
  - 變更檔案: 1 個
- **2022-10-19 20:01:21**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-10-14 14:01:38**: [內部]V00-20221013001 修正修改使用者資料中復職操作的問題
  - 變更檔案: 1 個
- **2022-10-13 19:13:56**: [內部]V00-20221013004、20221013005 修正維護核准層級異常問題
  - 變更檔案: 1 個
- **2022-10-13 14:56:21**: [內部]V00-20221012004 修正組織節點無排序問題
  - 變更檔案: 1 個
- **2022-10-13 11:52:44**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-10-07 20:02:23**: [內部]補上遺漏註解
  - 變更檔案: 1 個
- **2022-10-07 19:43:57**: [內部]新增Web化組織管理工具程式定義與其SQL[補]
  - 變更檔案: 2 個
- **2022-10-07 19:38:37**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 4 個
- **2022-09-30 19:40:17**: [內部]新增Web化組織管理工具程式定義與其SQL
  - 變更檔案: 4 個
- **2022-09-30 15:34:21**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-09-28 09:39:32**: [BPM APP]C01-20220921006 修正在移動端多人處理關卡向前加簽後簽核歷程處理者顯示錯誤問題
  - 變更檔案: 1 個
- **2022-09-23 12:03:44**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-09-23 09:44:31**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-09-21 19:30:51**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-09-21 16:04:10**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-09-19 14:28:24**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-09-17 11:38:21**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 3 個
- **2022-09-02 16:59:28**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-26 11:30:43**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-26 10:39:38**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 3 個
- **2022-08-24 18:52:30**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-19 18:28:48**: [BPM APP]S00-20220525001 支持同時整合企業微信與LINE
  - 變更檔案: 19 個
- **2022-08-18 11:41:39**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-08-17 18:57:42**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-03 19:56:35**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-02 20:24:41**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-07-26 20:33:38**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-07-22 17:38:10**: [內部]新增提供給組織設計工具Web化使用的SessionBean
  - 變更檔案: 2 個

### yamiyeh10 (14 commits)

- **2022-10-19 11:45:03**: [內部]調整bpm-tools點組織設計工具時提示已Web化訊息[補]
  - 變更檔案: 1 個
- **2022-10-18 17:12:22**: [內部]調整bpm-tools點組織設計工具時提示已Web化訊息[補]
  - 變更檔案: 12 個
- **2022-10-18 13:02:37**: [內部]調整bpm-tools點組織設計工具時提示已Web化訊息
  - 變更檔案: 1 個
- **2022-10-13 13:44:58**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 1 個
- **2022-10-11 10:47:52**: [內部]調整Web化系統工具的系統權限管理頁面重新載入快取資料按鈕的多語系
  - 變更檔案: 1 個
- **2022-09-27 16:42:30**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-09-26 09:27:20**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-09-22 18:04:54**: [流程引擎]Q00-20220922001 調整流程撈取工作通知內容機制
  - 變更檔案: 2 個
- **2022-09-15 10:23:28**: [內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題
  - 變更檔案: 4 個
- **2022-09-07 13:48:36**: [Web]Q00-20220825002 調整模組程式維護作業加入系統語系供使用者設定
  - 變更檔案: 5 個
- **2022-08-16 14:49:49**: [Web]A00-20220811001 修正表單若TextBox元件設定浮點數且顯示實際值時會有偏移值問題
  - 變更檔案: 1 個
- **2022-08-10 17:10:05**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-08-09 13:51:45**: [組織同步]Q00-20220809002 修正組織同步log出現Error時改寄送失敗通知信
  - 變更檔案: 1 個
- **2022-07-25 10:22:50**: [Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題
  - 變更檔案: 1 個

### 謝閔皓 (35 commits)

- **2022-10-18 16:35:00**: [SAP]S00-20220506006 調整 SAP 欄位對應設定，在新增時將呼叫型態固定為 Ajax，並且在畫面中隱藏
  - 變更檔案: 1 個
- **2022-10-18 10:33:07**: [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
  - 變更檔案: 1 個
- **2022-10-15 11:47:52**: [TIPTOP]Q00-20221014007修正客戶從TIPTOP端udm_tree操作原稿匣撤銷流程時，選擇特定流程後，BPM仍會回傳所有可撤銷流程的清單
  - 變更檔案: 1 個
- **2022-10-12 13:49:55**: [Web]Q00-20221012002修正員工工作轉派的轉派意見若只有輸入單一個反斜線，則使用者的待辦事項無法呈現的問題
  - 變更檔案: 1 個
- **2022-10-07 18:11:16**: [Web]S00-20220714004刪除元件時，判斷此元件是否與Grid繫結[補修正]
  - 變更檔案: 1 個
- **2022-10-06 13:39:46**: [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
  - 變更檔案: 1 個
- **2022-10-03 13:43:45**: [Web]Q00-20221003003修正設計流程時，關卡名稱有空格，但在待辦事項的檢核意見表中的關卡名稱，空格未顯示的問題
  - 變更檔案: 2 個
- **2022-09-30 18:35:18**: [Web]Q00-20220930003修正使用URL進入表單發起畫面，再使用另一個URL進入表單發起畫面，會有頁面殘留的問題
  - 變更檔案: 1 個
- **2022-09-30 16:36:17**: [TIPTOP]S00-20220613005 從 TIPTOP 拋到 BPM 的單據，若回寫到 TIPTOP 失敗時，則發送 Mail 給系統管理員[補]
  - 變更檔案: 1 個
- **2022-09-30 15:47:30**: [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]
  - 變更檔案: 1 個
- **2022-09-30 12:24:25**: [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員
  - 變更檔案: 1 個
- **2022-09-26 17:09:11**: [TIPTOP]S00-20220613005 從 TIPTOP 拋到 BPM 的單據，若回寫到 TIPTOP 失敗時，則發送 Mail 給系統管理員
  - 變更檔案: 1 個
- **2022-09-15 22:01:27**: [Web]S00-20220810001簽核意見是否顯示管理員[補修正]
  - 變更檔案: 4 個
- **2022-09-13 14:27:14**: [Web]S00-20220714004刪除元件時，判斷此元件是否與Grid繫結
  - 變更檔案: 2 個
- **2022-09-07 17:08:34**: [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題[補修正]
  - 變更檔案: 1 個
- **2022-09-07 14:59:37**: [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題
  - 變更檔案: 1 個
- **2022-09-04 00:13:13**: [Web]Q00-20220902001修正企業流程監控的圈型圖時間單位問題[補修正]
  - 變更檔案: 1 個
- **2022-09-02 17:15:46**: [BPMAPP]Q00-20220902002 修正企業微信回調接收消息時出錯，調整commons-codec套件版本為1.9版
  - 變更檔案: 3 個
- **2022-09-02 14:36:17**: [Web]Q00-20220902001修正企業流程監控的圈型圖時間單位問題
  - 變更檔案: 1 個
- **2022-08-29 10:42:15**: [Web]Q00-20220826002修正模組定義若底下無程式定義，不會顯示在模組程式維護頁面的問題[補]
  - 變更檔案: 1 個
- **2022-08-26 18:47:21**: [Web]Q00-20220826002修正模組定義若底下無程式定義，不會顯示在模組程式維護頁面的問題
  - 變更檔案: 1 個
- **2022-08-23 15:27:14**: [流程引擎]S00-20220722001新增批次通知信件主旨內容
  - 變更檔案: 1 個
- **2022-08-19 10:49:46**: [Web]S00-20220810001簽核意見是否顯示管理員
  - 變更檔案: 5 個
- **2022-08-15 15:23:05**: [Web]Q00-20220815003修正絕對位置表單中多選開窗小畫面沒有顯示選取清單的問題
  - 變更檔案: 1 個
- **2022-08-15 08:31:26**: [Web]Q00-20220815001修正絕對位置表單的image無法依照寬高呈現的問題
  - 變更檔案: 1 個
- **2022-08-11 12:56:57**: [Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題
  - 變更檔案: 1 個
- **2022-08-10 18:34:57**: [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
  - 變更檔案: 1 個
- **2022-08-10 18:08:04**: Revert "[Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題"
  - 變更檔案: 1 個
- **2022-08-10 18:03:03**: [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
  - 變更檔案: 1 個
- **2022-08-08 17:43:54**: [Web]Q00-20220808003修正使用產品表單中的Date元件，並搭配TextBox元件的進階功能，資料型態整數中的時間區間運算，當遇到元件ID有使用下底線時，會導致TextBox元件無法正常運算
  - 變更檔案: 1 個
- **2022-08-08 14:57:44**: [Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題
  - 變更檔案: 1 個
- **2022-08-04 22:21:33**: [Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容
  - 變更檔案: 2 個
- **2022-08-03 11:33:17**: [Web]Q00-20220803001修正在手機模式下，檢視附件檔案下載時跳出空白頁的問題
  - 變更檔案: 1 個
- **2022-08-01 16:26:26**: [Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料
  - 變更檔案: 1 個
- **2022-08-01 11:28:16**: [Web]Q00-20220801001新增複製流程序號的多語系內容
  - 變更檔案: 2 個

### 郭哲榮 (7 commits)

- **2022-10-17 19:04:07**: [BPM APP]C01-20221013006 調整企業微信在同步使用者時的同步狀態欄位若有簡體字會呈現問號的問題
  - 變更檔案: 2 個
- **2022-10-14 17:02:04**: [BPM APP]C01-20220826004 調整企業微信同步時獲取部門成員與子部門列表的接口為新接口
  - 變更檔案: 4 個
- **2022-09-30 11:24:55**: [BPM APP]S00-20220721001 新增移動端Grid元件點擊取消按鈕後可設定欲執行方法
  - 變更檔案: 4 個
- **2022-08-26 15:52:29**: [BPM APP]C01-20220825002 修正移動端在發起詳情畫面點擊Grid元件查看更多時無法顯示的問題
  - 變更檔案: 4 個
- **2022-07-27 09:54:27**: [BPM APP]C01-20220713002 調整移動端上傳附件後取消呼叫formSave與formClose事件
  - 變更檔案: 4 個
- **2022-07-26 19:37:46**: [BPM APP]C01-20220722001 修正移動端Grid元件的itemOrder有0時會造成欄位順序錯亂問題
  - 變更檔案: 1 個
- **2022-07-25 18:57:30**: [BPM APP]C01-20220627005 修正IMG中間層Grid元件的itemOrder有0時會造成欄位順序錯亂問題[補]
  - 變更檔案: 1 個

### wencheng1208 (20 commits)

- **2022-09-30 10:36:45**: [Web]Q00-*********** 執行iReport套件當發生Exception錯誤時，增加列印異常的堆疊資訊
  - 變更檔案: 1 個
- **2022-09-21 17:49:58**: [Web]Q00-20220921002 調整「必須上傳新附件」邏輯，只要存在一筆以上的附件並且符合在該「關卡名稱」上傳的附件，即可通過該驗證
  - 變更檔案: 1 個
- **2022-09-16 15:28:43**: [流程引擎]A00-*********** 此單號先前調整邏輯廢除，改為調整判斷附件權限為共用方法，可讓前後端共同呼叫使用
  - 變更檔案: 6 個
- **2022-09-14 17:52:35**: [流程引擎]A00-*********** 調整寄信時依照「附件權限設定的套用範圍為人員」時，判斷是否需要夾帶附件，在其餘範圍情境下則不主動夾帶附件
  - 變更檔案: 1 個
- **2022-09-14 10:34:55**: [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
  - 變更檔案: 1 個
- **2022-09-12 17:59:01**: [流程引擎]Q00-20220912004 修正findProcessPackageById方法內容為取得流程包裹最新一版，以避免後續同仁遇到此坑
  - 變更檔案: 1 個
- **2022-09-12 17:39:32**: [組織同步]QQ00-20220912003 組織同步功能執行人員資料修改時，可保留人員姓名多語系關聯
  - 變更檔案: 1 個
- **2022-09-08 15:12:59**: [Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本
  - 變更檔案: 1 個
- **2022-09-07 11:55:46**: [Web]Q00-*********** 增加判斷表單主要隱藏欄位hdnFormDefOID資料未完全載入時，彈出訊息提示以及不給予執行開窗動作
  - 變更檔案: 1 個
- **2022-09-06 14:52:00**: [流程引擎]Q00-20220823003 讓亂數產生的ID增加動態加簽CustomDecisionRule的開頭關鍵字，前面流程圖解析邏輯段也要新增
  - 變更檔案: 2 個
- **2022-09-01 15:53:47**: [Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式
  - 變更檔案: 1 個
- **2022-08-24 11:48:03**: [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
  - 變更檔案: 1 個
- **2022-08-23 14:44:52**: [流程引擎]Q00-20220823001 修正使用客製的方式執行動態加簽後，無法呈現詳細流程圖畫面的問題
  - 變更檔案: 1 個
- **2022-08-19 15:38:23**: [流程設計工具]Q00-20220819002 服務任務關卡中的呼叫應用程式，移除Mail Delivery下拉項目
  - 變更檔案: 1 個
- **2022-08-05 12:00:16**: [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]
  - 變更檔案: 1 個
- **2022-08-03 16:49:17**: Merge branch 'develop_v58' of http://************/BPM_Group/BPM into develop_v58
- **2022-08-03 16:48:09**: [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。
  - 變更檔案: 1 個
- **2022-07-29 16:30:05**: [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。
  - 變更檔案: 2 個
- **2022-07-27 12:07:05**: [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。
  - 變更檔案: 1 個
- **2022-07-25 10:55:23**: [Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法
  - 變更檔案: 1 個

### lorenchang (4 commits)

- **2022-09-22 13:54:12**: [E10]修正組織同步轉換中介資料時，只有兼職沒有主部門的話會因為insert empId(null)到SYN_Employee時出現異常
  - 變更檔案: 1 個
- **2022-09-21 14:00:16**: [E10]因為E10根節點組織ID固定為org001無法修改，改由BPM SyncTable.properties的synRootId設定，預設為Root
  - 變更檔案: 1 個
- **2022-09-19 15:49:15**: [E10]修正組織同步E10有可能傳送人員離職日期為V的內容(代表失效)，調整離職日期對應方式：V視為離職，9998/12/31視為在職
  - 變更檔案: 1 個
- **2022-09-06 16:13:30**: [內部]新增調用指定SessionBean的方法
  - 變更檔案: 1 個

### walter_wu (4 commits)

- **2022-07-29 17:03:53**: [Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失
  - 變更檔案: 1 個
- **2022-07-29 14:20:21**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]
  - 變更檔案: 2 個
- **2022-07-29 00:04:37**: [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
  - 變更檔案: 3 個
- **2022-07-26 18:17:44**: [內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來
  - 變更檔案: 1 個

### 王鵬程 (5 commits)

- **2022-07-29 16:49:53**: [Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示
  - 變更檔案: 1 個
- **2022-07-28 17:32:17**: [Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行
  - 變更檔案: 1 個
- **2022-07-27 18:14:17**: [Web]Q00-20220727004 調整使用JS做singleOpenWin開窗且寬度為720，開窗後顯示的是名片式改為表格式
  - 變更檔案: 1 個
- **2022-07-26 18:16:29**: [Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入
  - 變更檔案: 1 個
- **2022-07-25 15:44:06**: [Web]S00-20220129003 調整當表單元件的Label內容過長時完整顯示出Label內容
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題
- **Commit ID**: `02af0f371b5d77fba63b1bf065e95774cffc6818`
- **作者**: cherryliao
- **日期**: 2022-11-16 14:27:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 2. [WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定
- **Commit ID**: `913748b5cd406437318ec3a77f71a92cdbe99a89`
- **作者**: raven.917
- **日期**: 2022-11-01 12:00:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 3. [雙因素模組]Q00-20221026005 在未授權時，BPM首頁左側功能列會顯示雙因素模組功能
- **Commit ID**: `46c0cee9ace45ed54323753f3e30e159e8fdb114`
- **作者**: 林致帆
- **日期**: 2022-10-26 16:02:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

### 4. [內部]更新******* patch檔
- **Commit ID**: `dd35d2fef8d5d0389f58faa428bfeb3dec5253fc`
- **作者**: waynechang
- **日期**: 2022-10-24 15:02:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 5. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `28d2db4fb8f62dc5c830b975e49a1ab837d87d06`
- **作者**: pinchi_lin
- **日期**: 2022-10-24 13:29:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 6. [WorkFlow]Q00-20221024001 WorkFlow移除標準表單ASTI02
- **Commit ID**: `5a6e29a083c666c9fafcb9b76ce5abd8603f8daf`
- **作者**: 林致帆
- **日期**: 2022-10-24 10:16:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip`

### 7. [內部]新增提供給組織設計工具Web化使用的多語系
- **Commit ID**: `d0d642e422be143046f52a4314fba00d8044d8c8`
- **作者**: pinchi_lin
- **日期**: 2022-10-21 20:36:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 8. [雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]
- **Commit ID**: `fe80ea497dcd8ffcd98d9408957e9090bee94b76`
- **作者**: 林致帆
- **日期**: 2022-10-21 14:39:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 9. [雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]
- **Commit ID**: `c733e6c8e3d6f0a18f32b32c82dd55444d7a9507`
- **作者**: 林致帆
- **日期**: 2022-10-21 14:30:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 10. [雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤[補修正]
- **Commit ID**: `9fc5859e0086ec055fbf5fafbbe225fb0eeef4d3`
- **作者**: 林致帆
- **日期**: 2022-10-21 11:38:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 11. [雙因素模組]Q00-20221021001 調整第三方驗證內容回傳json內容錯誤
- **Commit ID**: `5c1200aa6972c360b28c49fd0c0b809c61735b53`
- **作者**: 林致帆
- **日期**: 2022-10-21 11:06:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 12. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `ae5cb7bc68f2d8f6bedeb727a44873be2a80f0e1`
- **作者**: pinchi_lin
- **日期**: 2022-10-19 20:01:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 13. [WEB]Q00-20221014001為相容56版上來的客戶，沒有唯讀背景顏色設定，補防呆
- **Commit ID**: `ad14a826a182920534efd4684fb27d411ed0a27c`
- **作者**: raven.917
- **日期**: 2022-10-19 17:09:54
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 14. [內部]新增B2B文件雲
- **Commit ID**: `a3738f6c09dac28ec3882257103b9a3654fbcd41`
- **作者**: waynechang
- **日期**: 2022-10-19 17:07:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

### 15. [內部]調整bpm-tools點組織設計工具時提示已Web化訊息[補]
- **Commit ID**: `2912cc10a873edba19767ec03b38ae4a9c949c4d`
- **作者**: yamiyeh10
- **日期**: 2022-10-19 11:45:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@designer/NaNaTools.properties`

### 16. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `4382fdbabf03c21323803a10db8360c6f5b64730`
- **作者**: waynechang
- **日期**: 2022-10-18 17:38:26
- **變更檔案數量**: 0

### 17. [內部]更新******* patch檔
- **Commit ID**: `82343fe2642cfe5ee391da910eb1dbdb6ee1d6ba`
- **作者**: waynechang
- **日期**: 2022-10-18 17:38:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 18. [內部]調整bpm-tools點組織設計工具時提示已Web化訊息[補]
- **Commit ID**: `e6ce9b366997458ab1881fe84a408b29b7211b02`
- **作者**: yamiyeh10
- **日期**: 2022-10-18 17:12:22
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/DesignerChooseDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/DesignerChooseDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/resource/view/dialog/ToolEntryLoginDialog_zh_TW.properties`

### 19. [SAP]S00-20220506006 調整 SAP 欄位對應設定，在新增時將呼叫型態固定為 Ajax，並且在畫面中隱藏
- **Commit ID**: `66b3391d9e0aade714a46a38e31d481207731025`
- **作者**: 謝閔皓
- **日期**: 2022-10-18 16:35:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`

### 20. [內部]調整bpm-tools點組織設計工具時提示已Web化訊息
- **Commit ID**: `46de7cf3aecb34ba517c4c17af28f2a7a8a9789c`
- **作者**: yamiyeh10
- **日期**: 2022-10-18 13:02:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java`

### 21. [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
- **Commit ID**: `eb49aa2603a80cf6163b824944da797ae70aa2d3`
- **作者**: 謝閔皓
- **日期**: 2022-10-18 10:33:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`

### 22. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `4101280b6eb520e94055ad87181f496d4beddb40`
- **作者**: cherryliao
- **日期**: 2022-10-18 09:18:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 23. [BPM APP]C01-20221013006 調整企業微信在同步使用者時的同步狀態欄位若有簡體字會呈現問號的問題
- **Commit ID**: `6fed0239eea34808782d84c09e42d8f3fb621654`
- **作者**: 郭哲榮
- **日期**: 2022-10-17 19:04:07
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/*******_mobile_DDL_MSSQL_1.sql`

### 24. [組織同步]Q00-20221017001 調整5883 updateSQL判斷中介表有建立就不需要在移除後重新再建立
- **Commit ID**: `f9b7e7baee72b028aeb92b48a4e9f14da9918e8f`
- **作者**: 林致帆
- **日期**: 2022-10-17 14:30:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DDL_Oracle_1.sql`

### 25. [WEB]Q00-20221014003修正變更經常選取對象 & 變更您的關係人，更新資料後沒有即時刷新頁面問題。
- **Commit ID**: `986d9bf2b9eb3b9fada2556fa95c4d60814b6a80`
- **作者**: raven.917
- **日期**: 2022-10-17 10:37:56
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 26. [WEB]A00-20221014001修正簽核意見斷行顯示。
- **Commit ID**: `f7c0f0769bf8fd124ba430acf8d0b50c1090a706`
- **作者**: raven.917
- **日期**: 2022-10-17 08:52:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 27. [TIPTOP]Q00-20221014007修正客戶從TIPTOP端udm_tree操作原稿匣撤銷流程時，選擇特定流程後，BPM仍會回傳所有可撤銷流程的清單
- **Commit ID**: `31c98b9a321eec7f84e84f72e7d37debbf48841a`
- **作者**: 謝閔皓
- **日期**: 2022-10-15 11:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 28. [WorkFlow]]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能
- **Commit ID**: `654ddac6596904f4c4c8605db05940301c1f56dd`
- **作者**: 林致帆
- **日期**: 2022-10-14 17:38:20
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 29. [BPM APP]C01-20220826004 調整企業微信同步時獲取部門成員與子部門列表的接口為新接口
- **Commit ID**: `e9667d0ee40c11be6f9d8c9e05eda63a1c378523`
- **作者**: 郭哲榮
- **日期**: 2022-10-14 17:02:04
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/mobile/wechat/MobileWeChatService.java`

### 30. [內部]V00-20221012007 調整在線閱覽管理/轉檔異常處理作業 的「執行」欄位寬度
- **Commit ID**: `a7e9c480af2581fd6014e069ffc6f25afdea85af`
- **作者**: waynechang
- **日期**: 2022-10-14 14:45:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp`

### 31. [流程設計師]A00-20221012001 修正流程設計師當子流程有變更代號時，流程簽入新版時，資料庫的子流程代號未更新
- **Commit ID**: `236a7abf515f81f6401a299362d852959f4cec23`
- **作者**: waynechang
- **日期**: 2022-10-14 14:29:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/SubflowActivityMCERTableModel.java`

### 32. [內部]V00-20221013001 修正修改使用者資料中復職操作的問題
- **Commit ID**: `caaafadbcc58ea51fad17361a5bc3c21de8453c9`
- **作者**: pinchi_lin
- **日期**: 2022-10-14 14:01:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 33. [WEB]Q00-20221014001為相容56版上來的客戶，沒有唯讀背景顏色設定。
- **Commit ID**: `e8bf645092bc65160151958536e9afe0aeec880d`
- **作者**: raven.917
- **日期**: 2022-10-14 12:52:12
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 34. [雙因素模組]V00-20221012001 修正郵件認證在登入輸入錯誤密碼還是會寄驗證信[補修正]
- **Commit ID**: `3937ecff27e9f65f8692b6acb9f8f710cf812937`
- **作者**: 林致帆
- **日期**: 2022-10-14 11:22:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 35. [內部]V00-20221012008修正流程管理/監控流程 選擇「已關閉」流程，匯出Excel發現多了簽核時間的欄。
- **Commit ID**: `f40e4cd5ffe835f270fca348d1dbdd0be7be2ba0`
- **作者**: raven.917
- **日期**: 2022-10-14 09:17:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 36. [內部]V00-20221013004、20221013005 修正維護核准層級異常問題
- **Commit ID**: `09011944be1092879b95ab7aeb21bee480678b08`
- **作者**: pinchi_lin
- **日期**: 2022-10-13 19:13:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 37. [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。(改)
- **Commit ID**: `d2c8c6e8f6bc6ffad31b0f6b554a5744616a2f9a`
- **作者**: raven.917
- **日期**: 2022-10-13 15:36:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 38. [內部]V00-20221012004 修正組織節點無排序問題
- **Commit ID**: `91ff01a7d66a0d4f24d1e8d891bec46283324b87`
- **作者**: pinchi_lin
- **日期**: 2022-10-13 14:56:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java`

### 39. [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，傳參數為number即報明顯錯誤，新增多語系。
- **Commit ID**: `8cbf837214c7f4d7d2d3d4a0838428f21aa63f0f`
- **作者**: raven.917
- **日期**: 2022-10-13 14:03:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 40. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `4f2d0527cffb19c911ab791d6fcc55c99beed06c`
- **作者**: yamiyeh10
- **日期**: 2022-10-13 13:44:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 41. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `c0306f1c819e2ce4a9b0c0e7dc96876f4bad643f`
- **作者**: pinchi_lin
- **日期**: 2022-10-13 11:52:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 42. [雙因素模組]V00-20221012002 移除未認證清單的搜尋按鈕
- **Commit ID**: `e999acb7c0e5d23cf6f34688bc84c962bae4aa51`
- **作者**: 林致帆
- **日期**: 2022-10-13 11:46:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/TFAModule/TFAUnauthlist.jsp`

### 43. [雙因素模組]V00-20221012001 修正郵件認證在登入輸入錯誤密碼還是會寄驗證信
- **Commit ID**: `a6b13f5f391c21ade9e54d43484fc026df2f6c6f`
- **作者**: 林致帆
- **日期**: 2022-10-13 11:42:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 44. [WEB]Q00-20221013002:修正表單欄位有設定 "唯讀"時的欄位顏色，顯示卻都為背景顏色。
- **Commit ID**: `e05497244efb8a34ae58631b35eb58a75d8eee42`
- **作者**: raven.917
- **日期**: 2022-10-13 11:28:13
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 45. [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，找不到欄位ID時的Alert訊息
- **Commit ID**: `b34d2c9f51516617ea17209416600449bbcf6cab`
- **作者**: raven.917
- **日期**: 2022-10-13 11:06:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 46. [WEB]Q00-20221013001修正簽核意見沒有換行符號。
- **Commit ID**: `2813b668bf14466b46542acdf0f0cf391ee3fd9a`
- **作者**: raven.917
- **日期**: 2022-10-13 08:37:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/StringUtil.js`

### 47. [WorkFlow]Q00-20221012003 調整WF傳狀態Action為5時，回寫關卡須回傳狀態8回去
- **Commit ID**: `3cd993fcec17497f9fe32266c26ddb4d58434ef7`
- **作者**: 林致帆
- **日期**: 2022-10-12 15:45:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 48. [Web]Q00-20221012002修正員工工作轉派的轉派意見若只有輸入單一個反斜線，則使用者的待辦事項無法呈現的問題
- **Commit ID**: `272fe4fc4bc384d21e68bf7109369f31151e098b`
- **作者**: 謝閔皓
- **日期**: 2022-10-12 13:49:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 49. [內部]更新******* patch檔
- **Commit ID**: `cdc2ce7f6068b0d08803f7b34d5994598fbfe8ff`
- **作者**: waynechang
- **日期**: 2022-10-12 11:38:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 50. [WEB]Q00-20221012001優化載入Grid元件設定欄位寬度時，ID為null時的Alert訊息。
- **Commit ID**: `cff38f8b92be7e538a34c37187143ba7c3e73293`
- **作者**: raven.917
- **日期**: 2022-10-12 11:33:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 51. [內部]修正IndexNaNaDB_Oracle建立索引名稱過長問題
- **Commit ID**: `22594e63a1a27a0cafd20698b0dc8c9067fb466e`
- **作者**: waynechang
- **日期**: 2022-10-12 11:21:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql`

### 52. [Web]Q00-20221011001 修正ESS表單開啟沒有ESS畫面
- **Commit ID**: `c1be618eafc3f3854be76432c1f810f219d01dd7`
- **作者**: 林致帆
- **日期**: 2022-10-11 19:17:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 53. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `2e382aa2033d0ad14d82a3549fbce89b3b521085`
- **作者**: cherryliao
- **日期**: 2022-10-11 11:29:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 54. [內部]調整Web化系統工具的系統權限管理頁面重新載入快取資料按鈕的多語系
- **Commit ID**: `1998c755092d1fe922b0fb29cc01347b46467036`
- **作者**: yamiyeh10
- **日期**: 2022-10-11 10:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 55. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `0ebb29f2cb3bc9e5a652d1b6210f8de7f69c5785`
- **作者**: raven.917
- **日期**: 2022-10-11 09:20:14
- **變更檔案數量**: 0

### 56. [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(修)
- **Commit ID**: `1b30613541ce3b2d014e6c22a0b1ca0a512b22de`
- **作者**: raven.917
- **日期**: 2022-10-11 09:20:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 57. [內部]補上遺漏註解
- **Commit ID**: `bc26a1f887fcd2377061980ea1febfff34b1c539`
- **作者**: pinchi_lin
- **日期**: 2022-10-07 20:02:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java`

### 58. [內部]新增Web化組織管理工具程式定義與其SQL[補]
- **Commit ID**: `1f88ae1cb0a4434e9fc8ed076c90e02506529806`
- **作者**: pinchi_lin
- **日期**: 2022-10-07 19:43:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 59. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `247fdf8922f583fd8677d0cb79ff19507c8b22fa`
- **作者**: pinchi_lin
- **日期**: 2022-10-07 19:38:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java`

### 60. [Web]S00-20220714004刪除元件時，判斷此元件是否與Grid繫結[補修正]
- **Commit ID**: `aa34c8bec0a9b3615cfb929565de8dd1f3f9ea55`
- **作者**: 謝閔皓
- **日期**: 2022-10-07 18:11:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 61. [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。(補修正多語系)
- **Commit ID**: `adc808510debd5da671a8218e9c514c150d2a182`
- **作者**: raven.917
- **日期**: 2022-10-07 11:05:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 62. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `5cc4254f1b9bf184ca7551b23b6e4f05242c510c`
- **作者**: cherryliao
- **日期**: 2022-10-06 17:14:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 63. [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。(補修正多語系)
- **Commit ID**: `7592a1d19a36acc8186de41f715a9305d3751786`
- **作者**: raven.917
- **日期**: 2022-10-06 14:57:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 64. [WEB]A00-20221004002 修正上傳表單附件容量過大時，超出Server Request限制，報錯會有不友善的提示。
- **Commit ID**: `4e383a1fce65bca6f5b1fb2d1cb9d8e039c7b945`
- **作者**: raven.917
- **日期**: 2022-10-06 14:35:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 65. [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
- **Commit ID**: `f61bd9808f2bbe977e2e56d5ccf02c3d57f39347`
- **作者**: 林致帆
- **日期**: 2022-10-06 13:59:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`

### 66. [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
- **Commit ID**: `d4536ac6ea2efb6982d228c1a27996227b36ff10`
- **作者**: 林致帆
- **日期**: 2022-10-06 13:56:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 67. [ESS]Q00-20221006003修正BPM開啟ESS模組時，下方有多餘的灰色區塊阻擋頁面檢視
- **Commit ID**: `851679ed80b6d5bbd5a16428a20605e13384d162`
- **作者**: 謝閔皓
- **日期**: 2022-10-06 13:39:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`

### 68. [流程設計師]Q00-20221006001 調整在流程設計點擊編輯表單欄位權限時，若表單發行狀態已過期或UNDER_REVISION時會彈提示訊息
- **Commit ID**: `631348a61d167d9928d3a47cf142b2a3cfc2c7ac`
- **作者**: cherryliao
- **日期**: 2022-10-06 11:29:53
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`

### 69. [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用(補修正，增加可讀性)
- **Commit ID**: `b8e8f1daca8a74424f2a35822fdc03d4cb4cffcd`
- **作者**: raven.917
- **日期**: 2022-10-06 08:57:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 70. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `f6ca1fa7596b0a327bde93ca9717eab9af44757a`
- **作者**: cherryliao
- **日期**: 2022-10-05 15:52:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 71. [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
- **Commit ID**: `d46d0d7a00420d2d3378f2170d22fff30d378d6d`
- **作者**: 林致帆
- **日期**: 2022-10-04 17:59:50
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 72. [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
- **Commit ID**: `9bd1ac160c39ee69407b945ff85d4abeec75a3fa`
- **作者**: 林致帆
- **日期**: 2022-10-04 16:45:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`

### 73. [內部]更新******* patch檔
- **Commit ID**: `c718d609272c2c7882154bbac232e30f72b66e41`
- **作者**: waynechang
- **日期**: 2022-10-04 16:10:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 74. [WEB]A00-20221004001 修正表單中上傳附件是否讓使用者可自行設定權限"沒有作用
- **Commit ID**: `182599ac1c20c1330a52460065f540553f24e9d5`
- **作者**: raven.917
- **日期**: 2022-10-04 15:26:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 75. [內部]MFA雙因素模組名稱更改成TFA雙因素模組[補修正]
- **Commit ID**: `6a8109be6a095b80f7ff5dd1a0f5ca33cd8be5bf`
- **作者**: 林致帆
- **日期**: 2022-10-04 14:51:39
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 76. [表單設計師]Q00-20221004001 修正DialogInputLabel元件設定預設值為「填表人主部門」，再次打開表單定義時，原本的預設值變成提示文字內容
- **Commit ID**: `60f7568aa90e3a1bb6f9f09b6e71b64df1b1d858`
- **作者**: cherryliao
- **日期**: 2022-10-04 10:28:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 77. [內部]Q00-20221003004 增加Table相關Index
- **Commit ID**: `09912e9dfd71f8756e03901f7c02e7a7839f421f`
- **作者**: waynechang
- **日期**: 2022-10-03 16:00:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 78. [流程引擎]Q00-20221003002 流程預先解析支持流程設計關卡型態為「活動簽核人」的活動
- **Commit ID**: `3dca5cc064183b3ff4d5d60909e4f4b99bba4ac8`
- **作者**: waynechang
- **日期**: 2022-10-03 13:59:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 79. [Web]Q00-20221003003修正設計流程時，關卡名稱有空格，但在待辦事項的檢核意見表中的關卡名稱，空格未顯示的問題
- **Commit ID**: `726c5f566fba05632b244c3be73b0bb3f531fe27`
- **作者**: 謝閔皓
- **日期**: 2022-10-03 13:43:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 80. [流程設計師]Q00-20221003001 調整簽核流設計師，將流程設計師原有的「活動定義/選擇參與者/活動簽核人」重新加回簽核流設計師中
- **Commit ID**: `af5eadd772c686b6fc67fd66db3d8a509801e84a`
- **作者**: waynechang
- **日期**: 2022-10-03 10:27:09
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/BpmUserTaskInfoAcquirer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/BpmUserTaskEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskChooserController.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/BpmUserTaskInfoPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/ProcessRelationshipPanel.java`

### 81. [內部]新增Web化組織管理工具程式定義與其SQL
- **Commit ID**: `b7a1df87439b8f93df4be2e19c1ef4d4e36ae94e`
- **作者**: pinchi_lin
- **日期**: 2022-09-30 19:40:17
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 82. [內部]MFA雙因素模組名稱更改成TFA雙因素模組
- **Commit ID**: `99e256684036302aa00f7b16989d14a6715677af`
- **作者**: 林致帆
- **日期**: 2022-09-30 18:45:25
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - 📄 **重新命名**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MFAConfigManagerDelegate.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/MFANotVerifylist.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/MFASetting.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/MFATrustDevice.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/MFAuthentication.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/services/MFAConfigManager.java`
  - 📄 **重新命名**: `3.Implementation/subproject/service/src/com/dsc/nana/services/MFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/GoogleAuthenticator.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/MFAModule/MFASetting.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/MFAModule/MFAUnauthlist.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 83. [Web]Q00-20220930003修正使用URL進入表單發起畫面，再使用另一個URL進入表單發起畫面，會有頁面殘留的問題
- **Commit ID**: `d99ee3a389a1ca90e4ded8a0bee3207e059f2d99`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 18:35:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 84. [TIPTOP]S00-20220613005 從 TIPTOP 拋到 BPM 的單據，若回寫到 TIPTOP 失敗時，則發送 Mail 給系統管理員[補]
- **Commit ID**: `cdfc94162159a5128882f445a634ab426807a29a`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 16:36:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java`

### 85. [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員[補]
- **Commit ID**: `bd6c87b38bb9390bef6d07ee6e9aba4c613deb37`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 15:47:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 86. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `c11e7966633132dd427cbd7255485d2ed2f6d5f8`
- **作者**: pinchi_lin
- **日期**: 2022-09-30 15:34:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 87. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `acc90b9d139af31ffeda96e0abac5bc1aac229ca`
- **作者**: cherryliao
- **日期**: 2022-09-30 14:23:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 88. [Web]Q00-20220930002修正模擬簽核後，工作歷程及列印是否顯示管理員
- **Commit ID**: `e21f2626202345ec8159f8c0c48a4d3e3616ed63`
- **作者**: 謝閔皓
- **日期**: 2022-09-30 12:24:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java`

### 89. [BPM APP]S00-20220721001 新增移動端Grid元件點擊取消按鈕後可設定欲執行方法
- **Commit ID**: `9d5d554e60adcb9b94606aad878a0248ed1939bd`
- **作者**: 郭哲榮
- **日期**: 2022-09-30 11:24:55
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 90. [Web]Q00-*********** 執行iReport套件當發生Exception錯誤時，增加列印異常的堆疊資訊
- **Commit ID**: `edb50c81bf34eac2969a5dc51d32cdb0900da325`
- **作者**: wencheng1208
- **日期**: 2022-09-30 10:36:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`

### 91. [WorkFlowERP]S00-*********** 調整WorkFLow取簽核歷程及取簽核頁面URL邏輯
- **Commit ID**: `13183e1f7e019fc0e7a47ef159a6aebfabc1d07f`
- **作者**: 林致帆
- **日期**: 2022-09-30 09:04:30
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WorkFlowDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IWFRequestRecordDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBWFRequestRecordDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/workflow/WorkflowManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 92. [WEB]S00-***********新增線上使用者最大閒置時間系統變數給系統管理員可控制
- **Commit ID**: `30903b098927f0192b946c1cb80b8b014ce61c3d`
- **作者**: raven.917
- **日期**: 2022-09-28 10:55:38
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 93. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `39d8725d8ea0dd086d913d4e9b22d66b2a160676`
- **作者**: raven.917
- **日期**: 2022-09-28 10:55:03
- **變更檔案數量**: 0

### 94. [BPM APP]C01-20220921006 修正在移動端多人處理關卡向前加簽後簽核歷程處理者顯示錯誤問題
- **Commit ID**: `f3fe0269c386953687637678c7d24f7d25b6d78f`
- **作者**: pinchi_lin
- **日期**: 2022-09-28 09:39:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`

### 95. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `9d1c3018f77b16de17d0d686f403331cf5191008`
- **作者**: yamiyeh10
- **日期**: 2022-09-27 16:42:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 96. [ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核
- **Commit ID**: `87e01542cbe2c5f5b3d32adb0af2630996f577e6`
- **作者**: 林致帆
- **日期**: 2022-09-27 11:50:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 97. Revert "[ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核"
- **Commit ID**: `9e494dd6c8fd32b42b32cabc67b38f7e6bd7458b`
- **作者**: 林致帆
- **日期**: 2022-09-27 11:47:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 98. [ESS]Q00-20220927002 調整移除AppFormAttachment資料移除失敗時，不該拋Exception導致無法往下簽核
- **Commit ID**: `0a3772f88abf0e86795be1be02835bc5017815ef`
- **作者**: 林致帆
- **日期**: 2022-09-27 11:34:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 99. [web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。(補修正)
- **Commit ID**: `4dffee4a5f09a067e396b7df77d711765e108f61`
- **作者**: raven.917
- **日期**: 2022-09-27 10:51:44
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 100. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `f7410f6fb834359ee0166e3081881c560914758b`
- **作者**: raven.917
- **日期**: 2022-09-27 09:59:18
- **變更檔案數量**: 0

### 101. [T100]Q00-20220927001 修正T100表單轉RWD會產生多餘的Script內容
- **Commit ID**: `667c3f269b11aab7fa83d5050051659af7453825`
- **作者**: 林致帆
- **日期**: 2022-09-27 08:36:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`

### 102. [TIPTOP]S00-20220613005 從 TIPTOP 拋到 BPM 的單據，若回寫到 TIPTOP 失敗時，則發送 Mail 給系統管理員
- **Commit ID**: `bc2ca12db611244468a6d072e0ab9402ddf75f53`
- **作者**: 謝閔皓
- **日期**: 2022-09-26 17:09:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodSetStatus.java`

### 103. [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
- **Commit ID**: `de37debe8929de5a90078d14550011d176bac103`
- **作者**: 林致帆
- **日期**: 2022-09-26 16:46:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 104. [web]S00-20220613001 LDAP登入驗證不可變更密碼且不彈窗，系統帳號驗證登入維持原設定。
- **Commit ID**: `cc3b3b55956278581eda647009790012d24d938c`
- **作者**: raven.917
- **日期**: 2022-09-26 15:02:43
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 105. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `d1cce86437d5821a4ab074b7732f5c7f09d27cae`
- **作者**: yamiyeh10
- **日期**: 2022-09-26 09:27:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 106. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `b52492f027e68a55745be2a65b1c78049d145a45`
- **作者**: cherryliao
- **日期**: 2022-09-23 16:53:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 107. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `5d56681b6497ac4976e1808ae8727916192fb47c`
- **作者**: pinchi_lin
- **日期**: 2022-09-23 12:03:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 108. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `e68455e3f63a1ba96977c847826cc7908603abb3`
- **作者**: cherryliao
- **日期**: 2022-09-23 11:31:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 109. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `7394a9457a61b4e2ce19ffbb0eaba8636670ff21`
- **作者**: pinchi_lin
- **日期**: 2022-09-23 09:44:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 110. [流程引擎]Q00-20220922001 調整流程撈取工作通知內容機制
- **Commit ID**: `34c44facb5528eb9195b40775d623194a5f185a8`
- **作者**: yamiyeh10
- **日期**: 2022-09-22 18:04:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/ProcessDefinition.java`

### 111. [E10]修正組織同步轉換中介資料時，只有兼職沒有主部門的話會因為insert empId(null)到SYN_Employee時出現異常
- **Commit ID**: `b89768e28da9338c321ec379aa8af53699914320`
- **作者**: lorenchang
- **日期**: 2022-09-22 13:54:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java`

### 112. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `a0560a91e44fbf92bcfe6a933721ef4daec36f65`
- **作者**: pinchi_lin
- **日期**: 2022-09-21 19:30:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 113. [Web]Q00-20220921002 調整「必須上傳新附件」邏輯，只要存在一筆以上的附件並且符合在該「關卡名稱」上傳的附件，即可通過該驗證
- **Commit ID**: `c209fe77f86dae12c73be7aba0ef18c60712f8d8`
- **作者**: wencheng1208
- **日期**: 2022-09-21 17:49:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`

### 114. [WEB] C01-20220919007 Admin 需要能開啟設計師時，直接針對該表單做復原簽出的操作行為。
- **Commit ID**: `0588a14cbba671fb49ad29340d6379f95da440b1`
- **作者**: raven.917
- **日期**: 2022-09-21 16:42:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefNodeState.java`

### 115. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `7c2c65c36c8a893f2ab8364bc2cbb7380a9530ae`
- **作者**: pinchi_lin
- **日期**: 2022-09-21 16:04:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 116. [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
- **Commit ID**: `06c2b7d1b62058c8a004a366dec9b80797fb7a5d`
- **作者**: 林致帆
- **日期**: 2022-09-21 14:56:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 117. [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
- **Commit ID**: `1318eb79d1665f211a194582b2f55a3c1645cfb7`
- **作者**: 林致帆
- **日期**: 2022-09-21 14:54:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mfa/IdExpert.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mfa/IdExpertBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java`

### 118. [流程引擎]Q00-20220921001 調整發起流程頁面；由表單畫面切換至流程圖時，可根據當前表單內容進行流程預解析
- **Commit ID**: `8891adaece68c54a5f4e832ed7f79227e1c9d29e`
- **作者**: waynechang
- **日期**: 2022-09-21 14:14:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 119. [E10]因為E10根節點組織ID固定為org001無法修改，改由BPM SyncTable.properties的synRootId設定，預設為Root
- **Commit ID**: `23796bebd4449eef49b5b486e9d74b96b19f9539`
- **作者**: lorenchang
- **日期**: 2022-09-21 14:00:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java`

### 120. [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
- **Commit ID**: `88058f3ceba61d85c315e28aa41c52796c7fe6f8`
- **作者**: 林致帆
- **日期**: 2022-09-21 13:49:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 121. Revert "[Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]"
- **Commit ID**: `32db33e291e16f6335026ecfa739cdc766bc7caf`
- **作者**: 林致帆
- **日期**: 2022-09-21 13:42:52
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 122. Merge remote-tracking branch 'origin/develop_v58' into develop_v58
- **Commit ID**: `d96b047167488e56dd5e5c185b22b74ecb0a9160`
- **作者**: 林致帆
- **日期**: 2022-09-21 13:40:30
- **變更檔案數量**: 0

### 123. [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證[補修正]
- **Commit ID**: `55d030f80c49d7607af0d529cc508b9999b87263`
- **作者**: 林致帆
- **日期**: 2022-09-21 13:39:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 124. [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)
- **Commit ID**: `86a9185a042f5969e03eae5741173a4f7e93181a`
- **作者**: raven.917
- **日期**: 2022-09-21 10:40:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 125. [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。(補)
- **Commit ID**: `71233bf48a11a63ce6c06c0c72233b9a3ae7a61f`
- **作者**: raven.917
- **日期**: 2022-09-20 18:09:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 126. [Web]S00-20220920002 新增雙因素模組支援全景系統及第三方驗證系統進行登入驗證
- **Commit ID**: `423b4567baf00fae5c4c200fb819cd3ca043052b`
- **作者**: 林致帆
- **日期**: 2022-09-20 17:25:45
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/MFASetting.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/MFAuthentication.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/MFAConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/MFAModule/MFASetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/MFAModule/MFAUnauthlist.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 127. [表單設計師]C01-20220920002 TextBox的DateTime欄位格式支持"-"符號為合法輸入，並且新增提示，後端修改格式存進資料庫。
- **Commit ID**: `66b6857bae99e32b302f7a9e8d38805cf7f5acaa`
- **作者**: raven.917
- **日期**: 2022-09-20 16:57:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 128. Merge branch 'develop_v58' of http://************/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `bc3f0b0994e1b9f2c0a2813efd0c09fbc077ca4f`
- **作者**: waynechang
- **日期**: 2022-09-20 14:55:50
- **變更檔案數量**: 0

### 129. [TIPTOP]A00-20220919001 新增TIPTOP整合設定，當夾帶附件型態為http,根據TIPTOP附件主機的port號取得附件
- **Commit ID**: `5c2c3e8f584ca2ebc5f87c0189c7aafeb9dd02b2`
- **作者**: waynechang
- **日期**: 2022-09-20 14:55:38
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 130. [內部]Q00-20220920002 T100傳附件用http方式且未帶drivetoken的tag內容，增加log訊息提示修正T100
- **Commit ID**: `8d77be0f61fd66714439b0b35f81c131fbcf72a4`
- **作者**: 林致帆
- **日期**: 2022-09-20 14:39:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 131. [Web]A00-20220919002 調整表單附件上傳畫面，取消「已上傳附件」的顯示區塊
- **Commit ID**: `0a8ae6418733cdf442e2f7340420e71435431d82`
- **作者**: waynechang
- **日期**: 2022-09-20 11:19:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 132. [E10]修正組織同步E10有可能傳送人員離職日期為V的內容(代表失效)，調整離職日期對應方式：V視為離職，9998/12/31視為在職
- **Commit ID**: `b5d739881475ae35e51d427c697d2a8044689a90`
- **作者**: lorenchang
- **日期**: 2022-09-19 15:49:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/ExtSyncOrgMgr.java`

### 133. [表單設計師]S00-***********系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷，四則運算及單身加總運算。(補修正)
- **Commit ID**: `b3d5ebeb5903e44af9c71ecefe5e08aa209c78fb`
- **作者**: raven.917
- **日期**: 2022-09-19 14:33:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 134. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `05231e30673fa198f1b5348a27d265b28d94ed22`
- **作者**: pinchi_lin
- **日期**: 2022-09-19 14:28:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 135. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `86d9a99f44f0dba4e452d5e80fba2b96efe6460e`
- **作者**: pinchi_lin
- **日期**: 2022-09-17 11:38:21
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/WorkCalendarManagerBean.java`

### 136. [流程引擎]A00-*********** 此單號先前調整邏輯廢除，改為調整判斷附件權限為共用方法，可讓前後端共同呼叫使用
- **Commit ID**: `ac2642215cde8327d68a58dd08bcf3947263bcea`
- **作者**: wencheng1208
- **日期**: 2022-09-16 15:28:43
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDTOFactoryDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDTOFactoryLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 137. [表單設計師]S00-***********系統相容用戶自行輸入千分位之判斷，另新增浮點數欄位非法字元判斷。
- **Commit ID**: `eb23d26047057086ab97dbd5efb38cf1ddaa66e2`
- **作者**: raven.917
- **日期**: 2022-09-16 14:46:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 138. [Web]Q00-20220916001 修正在透過SQLCommand取得的值為null時與原先回傳值不同的問題
- **Commit ID**: `c703c4c9e15cb846bf87d1611215fe041a07db73`
- **作者**: cherryliao
- **日期**: 2022-09-16 13:48:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 139. [Web]S00-20220810001簽核意見是否顯示管理員[補修正]
- **Commit ID**: `e62ea54d0fcde2a8e458da8308658d86f3d4b1ec`
- **作者**: 謝閔皓
- **日期**: 2022-09-15 22:01:27
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 140. [流程引擎]Q00-20220915001 修正簡易流程圖若流程有設計迴圈型且線的條件剛好為兩個Gateway互為下一關時，加入防呆避免系統Crash
- **Commit ID**: `e8cd2c847e89957cbf8a7c0120bccbbd307c83f2`
- **作者**: waynechang
- **日期**: 2022-09-15 17:24:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 141. [內部]Q00-20220715002 優化Web化系統工具的系統權限管理頁面開啟緩慢問題
- **Commit ID**: `e569439c69f0f4c51cb111111509737520e8984a`
- **作者**: yamiyeh10
- **日期**: 2022-09-15 10:23:28
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthoritySingletonCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/design_tool_web/SystemManageTool.java`

### 142. [流程引擎]A00-*********** 調整寄信時依照「附件權限設定的套用範圍為人員」時，判斷是否需要夾帶附件，在其餘範圍情境下則不主動夾帶附件
- **Commit ID**: `8e8caeb029dec494813baeef6735751bec40ac23`
- **作者**: wencheng1208
- **日期**: 2022-09-14 17:52:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`

### 143. [流程引擎]Q00-20220914001 原撰寫方式的亂數產生「動態加簽ID」名稱會太長，已調整為解析「往前的參考關卡ID」及排除「-ADD-」關鍵字，避免後續流程圖解析出錯
- **Commit ID**: `be78c34d912f2c9ebf5e0f95ad1815db2bfcd74a`
- **作者**: wencheng1208
- **日期**: 2022-09-14 10:34:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 144. [Web]S00-20220714004刪除元件時，判斷此元件是否與Grid繫結
- **Commit ID**: `52421c326cf0f1613899d44f3e1e644f4cd4d3e2`
- **作者**: 謝閔皓
- **日期**: 2022-09-13 14:27:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 145. [流程引擎]Q00-20220912004 修正findProcessPackageById方法內容為取得流程包裹最新一版，以避免後續同仁遇到此坑
- **Commit ID**: `e3332c4bcc0a27d42750b11fa93d2ee22adf3f4d`
- **作者**: wencheng1208
- **日期**: 2022-09-12 17:59:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`

### 146. [組織同步]QQ00-20220912003 組織同步功能執行人員資料修改時，可保留人員姓名多語系關聯
- **Commit ID**: `c3a3ad228ffcb72133193b54ca3e4d16c65bc4a7`
- **作者**: wencheng1208
- **日期**: 2022-09-12 17:39:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 147. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `2fc1b92bad6100be65d9fcfabd6227de42916bef`
- **作者**: cherryliao
- **日期**: 2022-09-08 16:46:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 148. [Web]Q00-20220908002 關注欄位維護作業設定條件其驗證動作，調整取得的流程包裹是最新而且是發行狀態的版本
- **Commit ID**: `3aa0350a5a8ae1f050d4c764896ff91340c641f3`
- **作者**: wencheng1208
- **日期**: 2022-09-08 15:12:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CriticalAccessor.java`

### 149. [表單設計師]Q00-20220908001 修正subTab元件無法編輯
- **Commit ID**: `88a520d69306bacf96b0422463a5c53b6a3b6b84`
- **作者**: 林致帆
- **日期**: 2022-09-08 14:26:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 150. [Web]Q00-20220906002 調整當更新使用者在線資訊時發生網路不通等異常情況下的彈出訊息
- **Commit ID**: `5d22f84188e66922c460580b3c6a9fc78e49fa60`
- **作者**: cherryliao
- **日期**: 2022-09-08 14:06:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 151. [組織同步]Q00-20220907004 修正TT組織同步失敗
- **Commit ID**: `421c91c1053586c7dfd2ecf3b1a7d893ea6ae9ba`
- **作者**: 林致帆
- **日期**: 2022-09-07 17:39:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/NaNaPropertiesTable.java`

### 152. [Web]Q00-20220907003 修正TIPTOP附件無法下載
- **Commit ID**: `3abeb5057b7c232208e6e7c9a68860bce8ad4cd1`
- **作者**: 林致帆
- **日期**: 2022-09-07 17:15:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 153. [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題[補修正]
- **Commit ID**: `cde89594f891494cc88a488db9f6d2a0269b41ba`
- **作者**: 謝閔皓
- **日期**: 2022-09-07 17:08:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 154. [Web]Q00-20220907002修正流程代理人設定，操作新增、修改及刪除時，scrollbar 消失的問題
- **Commit ID**: `99978cb0a4b50c1197b600b67bafc34473c3c5be`
- **作者**: 謝閔皓
- **日期**: 2022-09-07 14:59:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`

### 155. [Web]Q00-20220825002 調整模組程式維護作業加入系統語系供使用者設定
- **Commit ID**: `e0369e191125ce4b0f33ca4a3f1f817fdef62e7b`
- **作者**: yamiyeh10
- **日期**: 2022-09-07 13:48:36
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProgramViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`

### 156. [Web]Q00-*********** 增加判斷表單主要隱藏欄位hdnFormDefOID資料未完全載入時，彈出訊息提示以及不給予執行開窗動作
- **Commit ID**: `476230c37c5ca5bfff80afd0dfac71cd3855975c`
- **作者**: wencheng1208
- **日期**: 2022-09-07 11:55:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 157. [Web]Q00-20220906004 修正系統管理員登入BPM不應該發雙因素模組的驗證信
- **Commit ID**: `84d79452acecfdc44f331ce61136ddd932facc2e`
- **作者**: 林致帆
- **日期**: 2022-09-07 08:38:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 158. [內部]新增調用指定SessionBean的方法
- **Commit ID**: `b8943bf839f60481c6a069ebb2a9cd20c340dbe8`
- **作者**: lorenchang
- **日期**: 2022-09-06 16:13:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/SessionBeanHelper.java`

### 159. [流程引擎]Q00-20220823003 讓亂數產生的ID增加動態加簽CustomDecisionRule的開頭關鍵字，前面流程圖解析邏輯段也要新增
- **Commit ID**: `6146e3d20631ab9a7babf1a634ca03115c3c4341`
- **作者**: wencheng1208
- **日期**: 2022-09-06 14:52:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 160. [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
- **Commit ID**: `328105d4fbb42b188bf8aa5a9fe98ddc0f499e76`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:48:01
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 161. Revert "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]"
- **Commit ID**: `7c0edb9acf298dd9266cb6946ec766d4ea3ccb32`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:39:22
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 162. [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
- **Commit ID**: `6282ad27e6f1fd7bbe8dc6a16a12b023636c08c2`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:33:28
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 163. Revert "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]"
- **Commit ID**: `3838207f5c5ca4957d62ccf66348ae71ff315f1c`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:28:50
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 164. [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確[補修正]
- **Commit ID**: `d808f9cef1995ed9ed1bbc1f2cbe7b2d8cc567b0`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:26:35
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 165. Revert "[TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確"
- **Commit ID**: `8f0e428e833aa33faf011405ea368673309a0467`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:19:03
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 166. [TIPTOP]Q00-20220905001 修正Tiptop取得清單服務內容不正確
- **Commit ID**: `a7736a90b46c3b3eed28f40219287dcc25b38630`
- **作者**: 林致帆
- **日期**: 2022-09-05 17:17:31
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/RollbackableWorkListReader.java`

### 167. [Web]Q00-20220902001修正企業流程監控的圈型圖時間單位問題[補修正]
- **Commit ID**: `c94504c9c7cee6f1e33463a1ae19d32adde6869f`
- **作者**: 謝閔皓
- **日期**: 2022-09-04 00:13:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java`

### 168. [BPMAPP]Q00-20220902002 修正企業微信回調接收消息時出錯，調整commons-codec套件版本為1.9版
- **Commit ID**: `f80f20cc66dbc76b0cb7ad9eb5c4f88fb2aa21de`
- **作者**: 謝閔皓
- **日期**: 2022-09-02 17:15:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/lib/JakartaCommons/commons-codec-1.13.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/JakartaCommons/commons-codec-1.9.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/pom.xml`

### 169. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `03d7a8da1cc1e6720c73d16af8629865b193279d`
- **作者**: pinchi_lin
- **日期**: 2022-09-02 16:59:28
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 170. [Web]Q00-20220902001修正企業流程監控的圈型圖時間單位問題
- **Commit ID**: `8df129b902dd95465fdd801cc10cd1e8cbb02ab6`
- **作者**: 謝閔皓
- **日期**: 2022-09-02 14:36:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BAMAccessor.java`

### 171. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `76645157e93db3fed715ea028dc5e65c9f229afe`
- **作者**: cherryliao
- **日期**: 2022-09-01 18:37:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 172. [Web]Q00-20220901001 增加可區別簡易與複雜SQL查詢判斷，若為簡易SQL則執行原邏輯、複雜SQL則使用類子查詢方式
- **Commit ID**: `0d139e11ee78194cc82dd92cec3baec3fca2dc90`
- **作者**: wencheng1208
- **日期**: 2022-09-01 15:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 173. [WorkFlowERP]Q00-20220829001 移除WorkFlowERP查看過去審批流程功能
- **Commit ID**: `fcb3ba1957187bcd948ab26dbafdd3390393d03c`
- **作者**: 林致帆
- **日期**: 2022-08-30 08:34:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 174. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `707e419312c4362bb137282f1c3f05f709ceb326`
- **作者**: cherryliao
- **日期**: 2022-08-29 11:03:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 175. [Web]Q00-20220826002修正模組定義若底下無程式定義，不會顯示在模組程式維護頁面的問題[補]
- **Commit ID**: `c1d4baea88e29b660c3cc5d5fb2e5cdede3aeba3`
- **作者**: 謝閔皓
- **日期**: 2022-08-29 10:42:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java`

### 176. [Web]Q00-20220826002修正模組定義若底下無程式定義，不會顯示在模組程式維護頁面的問題
- **Commit ID**: `28d568b8960f8156ab0cd41a63ff153109538264`
- **作者**: 謝閔皓
- **日期**: 2022-08-26 18:47:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java`

### 177. [BPM APP]C01-20220825002 修正移動端在發起詳情畫面點擊Grid元件查看更多時無法顯示的問題
- **Commit ID**: `175c188e496be29275074711bd49b7ae2d718b56`
- **作者**: 郭哲榮
- **日期**: 2022-08-26 15:52:29
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`

### 178. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `06c4c3c2fc0d4a30de7309003025f5417d8b0ea0`
- **作者**: pinchi_lin
- **日期**: 2022-08-26 11:30:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 179. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `3ada000d791b3a599ca99b6ad98895ab4d36e524`
- **作者**: pinchi_lin
- **日期**: 2022-08-26 10:39:38
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 180. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `1456beff8538d0b0f6d63b0fc89183c6a02c64b1`
- **作者**: cherryliao
- **日期**: 2022-08-25 18:39:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 181. [Web]Q00-20220825003 修正程式權限設定套用範圍為部門但不包含子部門，在修改編輯時卻勾選包含子部門的問題
- **Commit ID**: `f4d5f7c4096191a4c0e1f9fb317fb0a703878f67`
- **作者**: cherryliao
- **日期**: 2022-08-25 15:41:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`

### 182. [流程引擎]Q00-20220825001 修正5883版本，當流程有執行通知關卡時，有機率會無法繼續派送至下一個關卡
- **Commit ID**: `2503deaec03851d03a3f64332bbd4cd63e720ab8`
- **作者**: waynechang
- **日期**: 2022-08-25 14:52:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 183. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `70d8e3ad327fcde84965d7fade62ad10ac55c778`
- **作者**: pinchi_lin
- **日期**: 2022-08-24 18:52:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 184. [流程引擎]Q00-20220823002 讓動態加簽出來的核決層級關卡，可在詳細流程圖上呈現關卡名稱內容
- **Commit ID**: `78bd5429a1d9375469be190c0b472761dfddae68`
- **作者**: wencheng1208
- **日期**: 2022-08-24 11:48:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`

### 185. [流程引擎]S00-20220722001新增批次通知信件主旨內容
- **Commit ID**: `26180e44b9f2c2346904834109c6a45dd61a9078`
- **作者**: 謝閔皓
- **日期**: 2022-08-23 15:27:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 186. [流程引擎]Q00-20220823001 修正使用客製的方式執行動態加簽後，無法呈現詳細流程圖畫面的問題
- **Commit ID**: `d44f99afaa18345a580db009c9483b9266cc2f91`
- **作者**: wencheng1208
- **日期**: 2022-08-23 14:44:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/DiagramUtil.java`

### 187. [Web]Q00-20220822002 修正登入頁輸入錯誤帳號，會跳出非標準的錯誤訊息
- **Commit ID**: `0fddabae15278c3e8c020a713bee981e3816648d`
- **作者**: 林致帆
- **日期**: 2022-08-22 16:19:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 188. [Web]Q00-20220822001 修正BPM使用IE瀏覽器上傳附件時會失敗
- **Commit ID**: `e979a57a244b1a6d5129208f97b79b2e3b19d3e5`
- **作者**: 林致帆
- **日期**: 2022-08-22 14:11:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

### 189. [BPM APP]S00-20220525001 支持同時整合企業微信與LINE
- **Commit ID**: `a7c604c3b4e14ecd70a85b58cbbb21a6eb28007a`
- **作者**: pinchi_lin
- **日期**: 2022-08-19 18:28:48
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileCommonManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MobileAuthorizeUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Line.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdapterAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobilePortletsAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/MobileLicenseUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 190. [TIPTOP]Q00-20220819003 修正Q00-20220525003造成TIPTOP拋單太久
- **Commit ID**: `31a3f56f09a5d15401cc617c88b9a18d5a3b2b73`
- **作者**: 林致帆
- **日期**: 2022-08-19 17:57:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 191. [流程設計工具]Q00-20220819002 服務任務關卡中的呼叫應用程式，移除Mail Delivery下拉項目
- **Commit ID**: `27734706db2d15e6559a9a1e441022d185a55bca`
- **作者**: wencheng1208
- **日期**: 2022-08-19 15:38:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/ApplicationsToolsEditorPanel.java`

### 192. [Web]S00-20220810001簽核意見是否顯示管理員
- **Commit ID**: `d827631348fbaddbfd0524c83f0471772ad79e98`
- **作者**: 謝閔皓
- **日期**: 2022-08-19 10:49:46
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 193. [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]
- **Commit ID**: `5e48602f19f19e4529c67355766c48a9ede41485`
- **作者**: 林致帆
- **日期**: 2022-08-18 17:50:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 194. [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到[補修正]
- **Commit ID**: `d74732c2253f7fd660f6a0196143c8c073cb35ac`
- **作者**: 林致帆
- **日期**: 2022-08-18 17:47:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

### 195. [流程引擎]Q00-20220818006 修正TIPTOP拋單，自動簽核有時候不會被觸發到
- **Commit ID**: `ba64aded10eeaab2380186dee73f476213adbbbd`
- **作者**: 林致帆
- **日期**: 2022-08-18 17:41:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

### 196. [WorkFlow]Q00-20220818004 優化易飛，WorkFlow回傳的Debug訊息
- **Commit ID**: `9bab88985ed1e259568ac9ea150e67ce030c9b68`
- **作者**: 林致帆
- **日期**: 2022-08-18 14:18:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 197. [TIPTOP]A00-20220816001 調整整合產品的createSQL的新增workflow主機指令[補修正]
- **Commit ID**: `e399bced9ab4046562849d0854d1b03cd6612348`
- **作者**: 林致帆
- **日期**: 2022-08-18 11:54:05
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_Oracle9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_SQLServer2005.sql`

### 198. [流程引擎]Q00-20220818003 修正5883版本當核決關卡解析的處理者有多個組織部門時，流程引擎有機率會以非發起參考部門的層級做解析導致核決關卡走向有誤
- **Commit ID**: `b12e376cbca07aa957fad4cead03b764187d840b`
- **作者**: waynechang
- **日期**: 2022-08-18 11:43:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java`

### 199. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `c2d79e3fee129e27a8f956d35ddb417b20e077a6`
- **作者**: pinchi_lin
- **日期**: 2022-08-18 11:41:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 200. [Web]Q00-20220818001 儲存系統設定T100整合時，會跳出密碼政策的錯誤訊息
- **Commit ID**: `8c58859c893a28b5fa51b622662d79dd7f2617fb`
- **作者**: 林致帆
- **日期**: 2022-08-18 11:27:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`

### 201. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `47e56fcef310f15c9068c279beb246b31078fc06`
- **作者**: pinchi_lin
- **日期**: 2022-08-17 18:57:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 202. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `5f7a7cadc37f265aca9c28f1418debdfb0851a92`
- **作者**: cherryliao
- **日期**: 2022-08-17 14:56:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 203. [TIPTOP]A00-20220816001 調整整合產品的createSQL的新增workflow主機指令
- **Commit ID**: `84e11d650f4f2499b3c85a0f77739e498d6d8509`
- **作者**: 林致帆
- **日期**: 2022-08-17 14:05:42
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_Oracle9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@crm/create/InitCrmModel_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@tiptop/create/InitTiptopModel_SQLServer2005.sql`

### 204. [Portal]]Q00-20220817001調整有整合Portal，用查看流程圖的外部portlet，導入的畫面不是BPM而是Portal的登入頁面
- **Commit ID**: `1edd545e3b041986bc4bbea1edd0184ec06cd6e4`
- **作者**: 林致帆
- **日期**: 2022-08-17 11:58:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`

### 205. [Web]A00-20220811001 修正表單若TextBox元件設定浮點數且顯示實際值時會有偏移值問題
- **Commit ID**: `b3161060ba229d1be016e874e01ea6fd57249870`
- **作者**: yamiyeh10
- **日期**: 2022-08-16 14:49:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 206. [在線閱覽]Q00-*********** 更新PDFjs閱讀器版本(2.3.200)，原因為修正部分PDF因字形而顯示異常的錯誤
- **Commit ID**: `4d024d9e9c76bf9a69e147cf1b6f51c2286fc674`
- **作者**: waynechang
- **日期**: 2022-08-15 17:45:31
- **變更檔案數量**: 86
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/MFAConfigManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/build/pdf.worker.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/debugger.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ar/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ast/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/az/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/be/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn-BD/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/bn-IN/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/br/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/brx/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ca/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cak/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cs/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/cy/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/da/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/de/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/el/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-CA/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-GB/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/en-US/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eo/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-AR/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-CL/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-ES/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/es-MX/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/et/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/eu/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fa/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ff/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fi/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fr/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/fy-NL/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gd/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gl/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gn/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/gu-IN/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/he/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hi-IN/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hr/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hsb/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/hu/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ia/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/id/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/is/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/it/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ja/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ka/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kab/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/kk/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ko/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lij/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/locale.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/lt/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/meh/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/mr/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/my/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nb-NO/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nl/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/nn-NO/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pa-IN/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pl/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-BR/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/pt-PT/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/rm/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ro/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ru/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/si/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sk/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sl/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sq/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/sv-SE/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/te/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/th/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tl/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/tr/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/uk/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/ur/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/vi/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-CN/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/locale/zh-TW/viewer.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.js.map`

### 207. [Web]Q00-20220815003修正絕對位置表單中多選開窗小畫面沒有顯示選取清單的問題
- **Commit ID**: `b7588a8d2b96e92a90c120ddd13a6e553678cf5c`
- **作者**: 謝閔皓
- **日期**: 2022-08-15 15:23:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 208. [Web]Q00-20220815001修正絕對位置表單的image無法依照寬高呈現的問題
- **Commit ID**: `d929c6cade7392008027ae81db23cf300991516b`
- **作者**: 謝閔皓
- **日期**: 2022-08-15 08:31:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 209. [流程引擎]S00-20220613004 優化流程預解析功能-1.核決關卡加簽後，預覽流程圖即可顯示，2.核決關卡有前置關係人時，預覽流程圖即可顯示，3.流程發起關卡的預覽流程圖，若使用者有多個發起部門，可動態依使用者選擇的部門做解析
- **Commit ID**: `c429847f3a795fd5e2a8d8622549b83fc52b345b`
- **作者**: waynechang
- **日期**: 2022-08-12 15:03:20
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ParticipantDefParserDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParser.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileCommonServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 210. [Web]Q00-20220811001修正表單中checkbox的label在信件顯示的問題
- **Commit ID**: `fa486cdf385dfa0d3a076bd48a294a37170acff5`
- **作者**: 謝閔皓
- **日期**: 2022-08-11 12:56:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 211. [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
- **Commit ID**: `494f7fc048dd1e5114eb83101765128b077da016`
- **作者**: 謝閔皓
- **日期**: 2022-08-10 18:34:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 212. Revert "[Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題"
- **Commit ID**: `ab97a8cb70dea0fcd603c695afa8e0959d05417a`
- **作者**: 謝閔皓
- **日期**: 2022-08-10 18:08:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 213. [Web]Q00-20220810003修正若表單中有設定RadioButton與checkbox的額外輸入框，但信件沒有顯示的問題
- **Commit ID**: `5834e5cfb8eabe14d293953b84ebb012029c1eed`
- **作者**: 謝閔皓
- **日期**: 2022-08-10 18:03:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 214. [內部]新增組織設計工具使用的服務[補]
- **Commit ID**: `94109e97f9a6eae975420f5af471f35adf594f88`
- **作者**: cherryliao
- **日期**: 2022-08-10 17:55:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 215. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `dc8a4d1c87347a77d7e4fd15d783d71239760c3b`
- **作者**: yamiyeh10
- **日期**: 2022-08-10 17:10:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 216. [Web]Q00-20220810001 修正設定模擬使用者給一般人員，用模擬使用者模擬一般人員，會出現兩筆模擬使用者的作業
- **Commit ID**: `2edb5cc6afeb7f4ba6ee57a921abe6bb1cb445a0`
- **作者**: 林致帆
- **日期**: 2022-08-10 15:47:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 217. [Web]A00-20220808001 調整報表查詢產出的日期與匯出Excel的日期不一致問題
- **Commit ID**: `d4c0b24f1a92ed9ca9e907398a46ac7721585913`
- **作者**: cherryliao
- **日期**: 2022-08-10 11:01:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/ChartQueryTemplate.js`

### 218. [T100]Q00-20220809005 修正T100拋單，附件為從文檔中心取得的，檔案大小與實際大小不符合
- **Commit ID**: `5a7394f09c8a97359c34fc98c5bebc4be95b78b8`
- **作者**: 林致帆
- **日期**: 2022-08-09 15:40:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`

### 219. [組織同步]Q00-20220809002 修正組織同步log出現Error時改寄送失敗通知信
- **Commit ID**: `5b173058239738874d60289d9cc240df369d1c90`
- **作者**: yamiyeh10
- **日期**: 2022-08-09 13:51:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`

### 220. [易飛]Q00-20220809001 調整易飛出貨流程的回寫事件
- **Commit ID**: `525e63905e003ec93ed6803213744554c344717d`
- **作者**: 林致帆
- **日期**: 2022-08-09 10:47:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@yife/process-default/bpmn/\351\200\262\350\262\250\345\226\256\345\273\272\347\253\213\344\275\234\346\245\255(YIFE_PURI09).bpmn"`

### 221. [Web]Q00-20220808003修正使用產品表單中的Date元件，並搭配TextBox元件的進階功能，資料型態整數中的時間區間運算，當遇到元件ID有使用下底線時，會導致TextBox元件無法正常運算
- **Commit ID**: `8c3c85a9ac2d08d555346ece855df33dbcb5606a`
- **作者**: 謝閔皓
- **日期**: 2022-08-08 17:43:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 222. [流程引擎]Q00-20220803002 調整流程主機呼叫其他流程主機清除系統快取的服務，若其他主機無法連線時，逾時時間由20秒改為1秒[補]
- **Commit ID**: `d42291a46fb14305d9bf4255bf3c5a70fdb7db69`
- **作者**: waynechang
- **日期**: 2022-08-08 16:01:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 223. [Web]Q00-20220808001修正從我的最愛點擊流程，第二次點擊時，等待時間的問題
- **Commit ID**: `5162185deae7b6755397b43c95fb48504b7bdb9a`
- **作者**: 謝閔皓
- **日期**: 2022-08-08 14:57:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 224. [流程引擎]Q00-20220803002 調整流程主機呼叫其他流程主機清除系統快取的服務，若其他主機無法連線時，逾時時間由20秒改為1秒
- **Commit ID**: `0b3442d2079dfa86d14a4f0284ec22640629cd10`
- **作者**: waynechang
- **日期**: 2022-08-08 14:51:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 225. [內部]更新5.8.8.3 patch檔
- **Commit ID**: `12fe44fa0a800db5b36a136f07123782b1084df5`
- **作者**: waynechang
- **日期**: 2022-08-08 14:36:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 226. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `9de4a70ea0e18ace787e38e1ea7a44196371fc3f`
- **作者**: cherryliao
- **日期**: 2022-08-05 17:28:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 227. [Web]Q00-20220805002 調整log訊息，當流程向後派送，後面關卡解析的使用者找不到或是沒有主部門時，增加log訊息
- **Commit ID**: `772eb0da6f644dfb42ea94a04979931320065fe3`
- **作者**: waynechang
- **日期**: 2022-08-05 17:26:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 228. [Web]Q00-20220805001 修正作業程序書沒有顯示核決層級關卡的作業名稱
- **Commit ID**: `f79f700e479fd86f728a71a0307682e52d117b21`
- **作者**: waynechang
- **日期**: 2022-08-05 14:06:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/CreateProcessDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`

### 229. [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。[補]
- **Commit ID**: `006bf89284d91dd8180f7232ef48a07b3d483e03`
- **作者**: wencheng1208
- **日期**: 2022-08-05 12:00:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 230. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `f2a408d1f7059099a37f8dc94b8c3115849da8e1`
- **作者**: cherryliao
- **日期**: 2022-08-05 11:44:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 231. [Web]Q00-20220804003修正流程進版後，使用者若未重新登入，從分類進入該流程，畫面就會空白，並新增提示訊息的多語系內容
- **Commit ID**: `253195da1f08ebb4d107d5e8c6f5fc91cbbf9376`
- **作者**: 謝閔皓
- **日期**: 2022-08-04 22:21:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 232. [內部]Q00-20220804002 優化ContextManager的log訊息
- **Commit ID**: `7a3f94e2fbeac1855ffc32195fa2dbcba3b04dc1`
- **作者**: 林致帆
- **日期**: 2022-08-04 16:21:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 233. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `fcb3952006df70a801f529bb0d48d728f0e666bb`
- **作者**: pinchi_lin
- **日期**: 2022-08-03 19:56:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 234. Merge branch 'develop_v58' of http://************/BPM_Group/BPM into develop_v58
- **Commit ID**: `dc3b8f90e6fb22b8ed798df440fa66fa04845588`
- **作者**: wencheng1208
- **日期**: 2022-08-03 16:49:17
- **變更檔案數量**: 0

### 235. [Web]A00-20220801003 調整判斷是否自動附加where條件的預設值為true，以避免客戶撰寫語法沒有where內容出現異常。
- **Commit ID**: `a0731c694c9a4436d8ad3a60765c810b2b49277f`
- **作者**: wencheng1208
- **日期**: 2022-08-03 16:48:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 236. [Web]Q00-20220803001修正在手機模式下，檢視附件檔案下載時跳出空白頁的問題
- **Commit ID**: `57c74512f535c7cf11f0fd6ca63276d48bf82686`
- **作者**: 謝閔皓
- **日期**: 2022-08-03 11:33:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 237. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `d9548a050c658c82aae9d8d8a84fbe08c0973944`
- **作者**: pinchi_lin
- **日期**: 2022-08-02 20:24:41
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 238. [Web]A00-20220802001 修正無法開啟SAP維護作業
- **Commit ID**: `82679f63433b30a999d8bb4662b85a1a47b50254`
- **作者**: 林致帆
- **日期**: 2022-08-02 11:27:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 239. [內部]調整系統設計工具Web化組織樹接口
- **Commit ID**: `7505e95810ff35063e87ad5dced53f70c31c725f`
- **作者**: cherryliao
- **日期**: 2022-08-02 10:59:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SharedServicesMgr.java`

### 240. [Web]Q00-20220801002修正在流程圖的核決關卡內容打開單身需要縮才會顯示資料
- **Commit ID**: `58e9caed6e35d72f99b57a40f0edb1c60f6043ad`
- **作者**: 謝閔皓
- **日期**: 2022-08-01 16:26:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 241. [Web]Q00-20220801001新增複製流程序號的多語系內容
- **Commit ID**: `1f7ba05161b070de6e5fa6f77cd93d71eda2de81`
- **作者**: 謝閔皓
- **日期**: 2022-08-01 11:28:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CommonJs/CopySerialNumber.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 242. [易飛]A00-20220729001 修正PURI09表單的Script單身欄位代號錯誤
- **Commit ID**: `001f7411548ff8233e135cb38c5cd0a209eff787`
- **作者**: 林致帆
- **日期**: 2022-07-29 17:50:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@yife/form-default/PURI09.form`

### 243. [Web]Q00-20220729004 修正如果絕對位置表單Grid連續空的兩關第二關儲存表單時會連FieldValue的Grid根節點都消失
- **Commit ID**: `74ced252b7de769c8b4573af9908ffd9fa8edb44`
- **作者**: walter_wu
- **日期**: 2022-07-29 17:03:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`

### 244. [Web]Q00-20220729003 修正關卡通知信設定以整張表單時，在表單上有設定顯示千分位，但通知信沒顯示
- **Commit ID**: `2e18724d4d31214518136ae608f4eb167ae8300d`
- **作者**: 王鵬程
- **日期**: 2022-07-29 16:49:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 245. [流程引擎]Q00-20220729001修正執行活動逾時排程動作，配合活動設定為「JUMP_TO_NEXT」選項時，後續實際發生逾時動作已可正常寄送「活動跳過」通知信。
- **Commit ID**: `f069f728ab46b060962de221112d5dd13e7a81f7`
- **作者**: wencheng1208
- **日期**: 2022-07-29 16:30:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 246. [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法[補修正]
- **Commit ID**: `d09de7907277acf369a9d48b0d2c0a604992f812`
- **作者**: 林致帆
- **日期**: 2022-07-29 14:34:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 247. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況[補修正]
- **Commit ID**: `8856c3f79bed996ea372d6399d8f43db71330e23`
- **作者**: walter_wu
- **日期**: 2022-07-29 14:20:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 248. [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock[補修正]
- **Commit ID**: `8574d28d3536dbbd509666abd3eeb8f538fba807`
- **作者**: 林致帆
- **日期**: 2022-07-29 14:02:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 249. [Web]Q00-20220728004 調整使用者是否閒置檢查機制另外處理網路順斷情況
- **Commit ID**: `f0517ebb311abe716b5169e5937ef5febdfa41e1`
- **作者**: walter_wu
- **日期**: 2022-07-29 00:04:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 250. [Web]Q00-20220728003 修正關卡通知信設定以整張表單時，TextArea元件在web上有換行時，但通知信沒有換行
- **Commit ID**: `1991f2f836709f1ce13bdebfcaced0528495a9d8`
- **作者**: 王鵬程
- **日期**: 2022-07-28 17:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 251. [WorkFlowERP]Q00-20220728002 修正關卡維多人處理且未有人接收，撤銷單據會造成DB Lock
- **Commit ID**: `cdeaa96601380b79bd66c768d69837704d0087bc`
- **作者**: 林致帆
- **日期**: 2022-07-28 15:20:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 252. [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作[補]
- **Commit ID**: `7c9f17af5dd50a32818283a1b31bdc7bd319ce83`
- **作者**: cherryliao
- **日期**: 2022-07-28 14:39:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 253. [Web]Q00-20220727004 調整使用JS做singleOpenWin開窗且寬度為720，開窗後顯示的是名片式改為表格式
- **Commit ID**: `a3422654da0b0dc00a5b8315aebc81c553851e05`
- **作者**: 王鵬程
- **日期**: 2022-07-27 18:14:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`

### 254. [Web]Q00-20220727003 修正Gird元件在關卡設置隱藏時開啟表單會彈出null訊息的問題
- **Commit ID**: `ebd0d8c8a95377c8054202c7cd86d88e8268f64f`
- **作者**: cherryliao
- **日期**: 2022-07-27 17:51:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`

### 255. [Web]S00-20220718006 新增模組-雙因子認證模組[補修正]
- **Commit ID**: `e140afdea0bdd5281a844f6574e1eaa1fc5f3dd3`
- **作者**: 林致帆
- **日期**: 2022-07-27 16:27:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.8.3_DDL_MSSQL_1.sql`

### 256. [Web]Q00-20220727002 增加載入列印畫面之後，取得所有Grid顯示按鈕元件，直接執行一次顯示Grid清單內容動作。
- **Commit ID**: `eb2c4eb459d042146f7e90e4d320e8e246780079`
- **作者**: wencheng1208
- **日期**: 2022-07-27 12:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 257. [WebService]Q00-20220727001 調整WebService白名單取得用戶端位置的寫法
- **Commit ID**: `6b0f6b8a32ae93c0617f18b1d2ddba4e2f4f0f92`
- **作者**: 林致帆
- **日期**: 2022-07-27 10:41:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 258. [BPM APP]C01-20220713002 調整移動端上傳附件後取消呼叫formSave與formClose事件
- **Commit ID**: `2bb6ed34ec223d13790d890c159dcad0116c9833`
- **作者**: 郭哲榮
- **日期**: 2022-07-27 09:54:27
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 259. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `3dbf5ddaaaa7870f5a5b6c38d8afbbde85da12ba`
- **作者**: pinchi_lin
- **日期**: 2022-07-26 20:33:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 260. [BPM APP]C01-20220722001 修正移動端Grid元件的itemOrder有0時會造成欄位順序錯亂問題
- **Commit ID**: `2f10c73e660863a3e76a72156d92654aa83b3143`
- **作者**: 郭哲榮
- **日期**: 2022-07-26 19:37:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilderMobile.java`

### 261. [內部]Q00-20220726001 調整DB取法避免用Id找ProcessPackage撈出一大堆全部取回來
- **Commit ID**: `8a6e7947b88e51ed41e2e083179ecaa7998339e3`
- **作者**: walter_wu
- **日期**: 2022-07-26 18:17:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/persistence/JpaService.java`

### 262. [Web]Q00-20220726002 修正匯入Excel檔案且內容有單引號時會出現錯誤而無法匯入
- **Commit ID**: `b0b30bc124c7a365591c067c80f3fefa794608b3`
- **作者**: 王鵬程
- **日期**: 2022-07-26 18:16:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`

### 263. [Web]S00-20220718006 新增模組-雙因子認證模組[補修正]
- **Commit ID**: `0b4138b68bc7d88d7644e1453bc56cabb8d0f56d`
- **作者**: 林致帆
- **日期**: 2022-07-26 09:25:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/MFAModule/MFASetting.jsp`

### 264. [BPM APP]C01-20220627005 修正IMG中間層Grid元件的itemOrder有0時會造成欄位順序錯亂問題[補]
- **Commit ID**: `d11672ff497727146e9aa7ea5dcb98c9e0e36b48`
- **作者**: 郭哲榮
- **日期**: 2022-07-25 18:57:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 265. [內部]Q00-20220725003 Index指令回收
- **Commit ID**: `9b62c1984fd559d10052c597c68cc095e4378c2b`
- **作者**: 林致帆
- **日期**: 2022-07-25 17:19:49
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 266. [TIPTOP]Q00-20220519002 axmt700_icd表單入版
- **Commit ID**: `1ac86343489e60928d743dbd560ded3994b2cfb2`
- **作者**: 林致帆
- **日期**: 2022-07-25 16:39:57
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/conf/5.25/Process_Mapping.prsmapping`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@tiptop/form-default/5.25(RWD)/axmt700_icd.form`

### 267. [Web]S00-20220129003 調整當表單元件的Label內容過長時完整顯示出Label內容
- **Commit ID**: `ddfe649e9e166c3ed1da9238ba899139d6bea643`
- **作者**: 王鵬程
- **日期**: 2022-07-25 15:44:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 268. [Web]A00-20220720001 舊版本客製開窗語法在使用模糊查詢時恢復可支援GroupBy語法
- **Commit ID**: `7c0ff3d034164d789260e004aae76c88893c4822`
- **作者**: wencheng1208
- **日期**: 2022-07-25 10:55:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 269. [Web]Q00-20220725001 調整流程逾時通知在自定義選擇待辦事項URL時會顯示N.A問題
- **Commit ID**: `ef025d5f4224bcda3a4f531c426a61fc03dcacf8`
- **作者**: yamiyeh10
- **日期**: 2022-07-25 10:22:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`

### 270. [內部]新增提供給組織設計工具Web化使用的SessionBean
- **Commit ID**: `733949d9b237d94324dcab9328036815c66dfd6f`
- **作者**: pinchi_lin
- **日期**: 2022-07-22 17:38:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

