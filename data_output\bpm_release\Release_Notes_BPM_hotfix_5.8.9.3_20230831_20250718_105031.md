# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.9.3_20230831
- **舊版本**: release_5.8.9.3
- **生成時間**: 2025-07-18 10:50:31
- **新增 Commit 數量**: 13

## 變更摘要

### kmin (1 commits)

- **2023-08-31 14:47:24**: 打包用
  - 變更檔案: 2 個

### 林致帆 (4 commits)

- **2023-08-31 09:16:24**: [Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件
  - 變更檔案: 3 個
- **2023-08-30 10:43:55**: [TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷
  - 變更檔案: 1 個
- **2023-08-29 15:50:48**: [ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗
  - 變更檔案: 2 個
- **2023-08-28 16:57:53**: [SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤
  - 變更檔案: 1 個

### cherryliao (1 commits)

- **2023-08-30 11:47:29**: [Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題
  - 變更檔案: 2 個

### waynechang (3 commits)

- **2023-08-29 16:50:50**: [流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常
  - 變更檔案: 1 個
- **2023-08-29 10:32:40**: [流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據
  - 變更檔案: 2 個
- **2023-08-23 15:27:37**: [Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常
  - 變更檔案: 3 個

### 刘旭 (2 commits)

- **2023-08-29 14:02:09**: [web]Q00-20230829003 列印時附件資訊會超出邊界问题修复
  - 變更檔案: 1 個
- **2023-08-25 17:38:15**: [web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复
  - 變更檔案: 1 個

### pinchi_lin (1 commits)

- **2023-08-28 11:15:49**: [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
  - 變更檔案: 1 個

### 邱郁晏 (1 commits)

- **2023-08-24 13:43:54**: [Web] Q00-*********** 修正TraceProcessForSearchForm待辦URL連結異常問題。
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. 打包用
- **Commit ID**: `721c74a695e0e0a37765d71963ebca50e5fab6b8`
- **作者**: kmin
- **日期**: 2023-08-31 14:47:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/lib/Json/json.jar`

### 2. [Web]Q00-*********** 調整若附件為在線閱覽狀態，在線閱覽開關，也要能下載附件
- **Commit ID**: `7edf85fb3aaae049bc59fd0591ea66848d1cef12`
- **作者**: 林致帆
- **日期**: 2023-08-31 09:16:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 3. [Web]Q00-*********** 調整上傳附件畫面樣式與附件資訊無法呈現的問題
- **Commit ID**: `48fecbee1144a6d2fe423dd3f4ad4ae8e1f605f5`
- **作者**: cherryliao
- **日期**: 2023-08-30 11:47:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 4. [TIPTOP]Q00-20230830001 修正拋單附件為非URL類型，增加在線閱覽判斷
- **Commit ID**: `b3892d1623b3636b81e6130827e7d2d13eafc60f`
- **作者**: 林致帆
- **日期**: 2023-08-30 10:43:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 5. [流程引擎]Q00-20230829005 修正關卡設定自動簽核2.與前一關相同則跳過時。當核決關卡的最後一關與下一關為相同處理者且下一關關卡有設定自動簽核2，下一關未自動跳過的異常
- **Commit ID**: `9e1a8c44961c5907be02fe8fbdc1b861b7958e60`
- **作者**: waynechang
- **日期**: 2023-08-29 16:50:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 6. [ESS]Q00-20230829004 修正回寫IDENTIFIER有重複值，造成ESS回寫失敗
- **Commit ID**: `b30e78a2b454939864ee2810855877513097fe36`
- **作者**: 林致帆
- **日期**: 2023-08-29 15:50:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormUtil.java`

### 7. [web]Q00-20230829003 列印時附件資訊會超出邊界问题修复
- **Commit ID**: `f72c90f6b7c0d2f7c468a8d0dd2c43ba93369fab`
- **作者**: 刘旭
- **日期**: 2023-08-29 14:02:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 8. [流程引擎]Q00-20230829001 調整自動簽核判斷(與前一關相同處理者跳過)，當前一關的關卡處理者為多人且每個人都要處理時，若關卡設定工作執行率50%時，前一關只會有一半的人簽核，故自動簽核判斷需以實際完成簽核的人員作為自動跳關的依據
- **Commit ID**: `feae55e055644d8903d73585988148566eb72137`
- **作者**: waynechang
- **日期**: 2023-08-29 10:32:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ParticipantActivityInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 9. [SAP]Q00-20230828004 修正SAP欄位對應設定作業傳入Structure都會產生錯誤
- **Commit ID**: `f7d42cd47ebe4f0fa47038313eee19d638b4ee3c`
- **作者**: 林致帆
- **日期**: 2023-08-28 16:57:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 10. [DT]Q00-20230828001 修正不顯示失效部門時列印組織圖仍會顯示失效部門的問題
- **Commit ID**: `c4e8abf77a2d5851011fb9f7df99123b4b5d5713`
- **作者**: pinchi_lin
- **日期**: 2023-08-28 11:15:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 11. [web]Q00-20230825001 响应式表单执行打印表单功能时签核历程会超出边界问题修复
- **Commit ID**: `9e931eef3588ea08db7b75a46fbc9e1083303403`
- **作者**: 刘旭
- **日期**: 2023-08-25 17:38:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 12. [Web] Q00-*********** 修正TraceProcessForSearchForm待辦URL連結異常問題。
- **Commit ID**: `2ce60fb865619dd15ff23dc0db3378472fda363c`
- **作者**: 邱郁晏
- **日期**: 2023-08-24 13:43:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 13. [Web]Q00-20230823001 修正待辦、追蹤流程的行動版表單檢視附件，當未購買在線閱讀模組但仍出現{onlineRead}的異常
- **Commit ID**: `39f901b07448b8ebf85b6a97a2a949267ac915bf`
- **作者**: waynechang
- **日期**: 2023-08-23 15:27:37
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

