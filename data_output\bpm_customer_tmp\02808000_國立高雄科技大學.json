{"company_id": "02808000", "company_name": "國立高雄科技大學", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\02808000-國立高雄科技大學\\01客戶基本資料", "files": [{"filename": "高科大(高應大).txt", "raw_content": "高應大\r\n\r\n客戶端直接設定服務的外網IP，遠端桌面直接登入即可\r\n--------------------(2020.11.19更新)-----------------------------\r\n\r\n正式機( ******* ):\t140.127.113.101\r\nBPM網頁帳密:\tadministrator / !!EFbpm21@\r\nwindows帳密:DSC/ @EFGP!2023\r\n外網:\thttps://efgp.nkust.edu.tw/NaNaWeb/\r\n\r\n正式機( ******* ):\t140.127.113.97\r\nwindows帳密:\tadministrator / !Dotwork2021\r\n\t\r\n正式機DB:\t*************** 先連到正式機後再遠端\r\n名稱/帳號/密碼:\tNaNa / sa / @efgp2023db\r\nwindows帳密:DSC/ !EFGP@db2023\r\n\t\r\n測試機( ******* ):\t**************\r\nBPM網頁帳密:\tadministrator / @@EFtest!21 \r\nwindows帳密:DSC/ @EFGP!2023\r\nwindows帳密:\tadministrator / @eftest2021!\r\n外網:\thttps://efgptest.nkust.edu.tw/NaNaWeb/\r\n\t\r\n測試機DB:\t**************\r\n名稱/帳號/密碼:\tBPMTEST / sa / !!kuas@EFGP  \r\nwindows帳密:\teasyflow / !eftest2021@\r\n\r\n\r\n-------------------------------------------------------------\r\n\r\n\r\n測試機: *************  遠端登入帳密:DSC/EFGP\r\n                          遠端登入帳密:administrator/!!kuas@@3167\r\n                           EFGP:administrator密碼:nkusttest\r\n\r\n\r\n   正式機: 140.127.113.101 遠端登入帳密:DSC/EFGP!!efgp\r\n                           遠端登入帳密:administrator/kuas@@3167\r\n                                                 DSC/EFGP@@89111688\r\n                           登入EFGP administrator登入密碼nkust@2020\r\nhttps://efgp.nkust.edu.tw/NaNaWeb\r\n\r\n與EFGP測試機與正式機DB連線\r\n測試機:*************  遠端登入帳密:DSC/EFGP\r\n\r\n正式機: ***************   遠端登入帳密:DSC/EFGP!!efgp\r\n\t\t\t  遠端登入帳密:administrator/!!kuas@@3167\r\n\r\n\r\n", "structured_data": {"正式機( ******* )": "140.127.113.97", "bpm網頁帳密": "administrator / @@EFtest!21", "windows帳密": "easyflow / !eftest2021@", "外網": "https://efgptest.nkust.edu.tw/NaNaWeb/", "正式機db": "*************** 先連到正式機後再遠端", "名稱/帳號/密碼": "BPMTEST / sa / !!kuas@EFGP", "測試機( ******* )": "**************", "測試機db": "**************", "測試機": "*************  遠端登入帳密:DSC/EFGP", "遠端登入帳密": "administrator/!!kuas@@3167", "efgp": "administrator密碼:nkusttest", "正式機": "***************   遠端登入帳密:DSC/EFGP!!efgp", "https": "//efgp.nkust.edu.tw/NaNaWeb", "host": "*******"}, "source_path": "C1.客戶維護相關\\02808000-國立高雄科技大學\\01客戶基本資料\\高科大(高應大).txt", "file_size": 1518, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:31.584577"}], "total_files": 1, "processed_at": "2025-08-26T10:46:31.584587"}