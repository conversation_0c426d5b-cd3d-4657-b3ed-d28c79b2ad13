# Release Notes - NaNaXWeb

## 版本資訊
- **新版本**: 5.8.9.4_hotfix
- **舊版本**: 5.8.9.4
- **生成時間**: 2025-07-18 15:49:24
- **新增 Commit 數量**: 46

## 變更摘要

### 周权 (3 commits)

- **2024-11-26 14:32:47**: [ORGDT]C01-20241121003 调整新增、修改组织ID对前后有空格的处理
  - 變更檔案: 1 個
- **2024-11-26 10:07:24**: Revert "[PRODT] C01-20241121003 修正系统管理LDAP编辑画面组织id中有空格会trim掉的问题"
  - 變更檔案: 1 個
- **2024-11-22 14:46:39**: [PRODT] C01-20241121003 修正系统管理LDAP编辑画面组织id中有空格会trim掉的问题
  - 變更檔案: 1 個

### yamiyeh10 (17 commits)

- **2024-07-12 10:45:37**: [PRODT]V00-20240328001 修正Web流程管理工具當登入者沒有組織設計師權限時會無法選擇使用者或組織問題
  - 變更檔案: 15 個
- **2024-09-26 08:21:33**: [PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況
  - 變更檔案: 1 個
- **2024-07-10 17:32:40**: [PRODT]C01-20240710001 修正Web流程管理工具在活動定義編輯器中將允許系統改派他人取消勾選後還是存在問題
  - 變更檔案: 1 個
- **2024-06-26 10:21:15**: [內部]A00-20240625001 NG-Zorro套件引入越南語系
  - 變更檔案: 3 個
- **2024-06-12 11:57:45**: [PRODT] V00-20240612001 修正Web流程管理工具加入核決層級關卡後無法儲存會跳出未設定條件等提示訊息問題
  - 變更檔案: 1 個
- **2024-06-11 17:06:11**: [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
  - 變更檔案: 1 個
- **2024-05-31 17:38:16**: [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
  - 變更檔案: 1 個
- **2024-05-31 16:31:30**: [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制[補]
  - 變更檔案: 1 個
- **2024-05-29 17:30:08**: [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息
  - 變更檔案: 3 個
- **2024-05-21 15:31:08**: [PRODT]C01-20240517015 修正Web流程管理工具中活動定義編輯器上編輯網路服務的應用程式時會發生找不到portName問題
  - 變更檔案: 2 個
- **2024-04-30 14:55:28**: [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制
  - 變更檔案: 3 個
- **2024-04-16 08:56:26**: [PRODT]Q00-20240415003 調整流程屬於簽核樣版分類時不驗證流程ID命名規格必須是[營運中心_單據_單別]的機制
  - 變更檔案: 1 個
- **2024-04-24 13:53:44**: [PRODT]V00-20240407001 修正Web流程管理工具中發起權限設定屬性刪除所有資料後原先資料還是存在的問題
  - 變更檔案: 1 個
- **2024-04-17 09:33:23**: [PRODT]Q00-20240417001 修正Web流程管理工具中流程定義進版後不會更新作者資訊問題
  - 變更檔案: 3 個
- **2024-03-26 18:45:07**: [ORGDT]Q00-20240326005 修正Web組織管理工具中列印組織圖異常問題
  - 變更檔案: 3 個
- **2024-03-15 18:29:58**: [內部]Q00-20240315002 調整Log訊息層級
  - 變更檔案: 14 個
- **2024-02-19 11:33:55**: [DT]Q00-20240203001 調整組織設計工具最上層的Enterprise Organizations名稱改用系統多語系維護
  - 變更檔案: 2 個

### lorenchang (4 commits)

- **2024-11-07 11:00:59**: [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)
  - 變更檔案: 2 個
- **2024-10-28 11:19:16**: [流程封存]修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)
  - 變更檔案: 2 個
- **2024-10-04 17:16:39**: [流程封存]修正更新排程時間的程式只在封存主機執行並增加更詳細的Log
  - 變更檔案: 3 個
- **2024-09-20 13:34:04**: [內部]在儲存排程及觸發排程時增加Log
  - 變更檔案: 2 個

### 邱郁晏 (1 commits)

- **2024-07-12 10:23:15**: [流程封存] C01-20240506005 調整流程封存維護作業日期儲存計算方式
  - 變更檔案: 1 個

### cherryliao (3 commits)

- **2024-02-22 10:37:29**: [流程封存]S00-20240123001 調整封存流程規則設定的結案時間超過屬性新增以月為單位
  - 變更檔案: 1 個
- **2024-02-26 14:09:09**: [PRODT]Q00-20240226001 修正Web流程管理工具中核決層級關卡的參考活動顯示異常的問題
  - 變更檔案: 1 個
- **2024-02-21 15:21:41**: [PRODT]Q00-20240221005 修正Web流程管理工具中核決層級關卡的參考活動為自定義時要再次編輯資料時發生丟失的問題
  - 變更檔案: 1 個

### 刘旭 (1 commits)

- **2024-05-15 17:57:59**: [流程封存] C01-20240510006 已封存流程在監控流程仍查看的到问题修复
  - 變更檔案: 1 個

### pinchi_lin (17 commits)

- **2024-04-03 14:51:22**: [PRODT]Q00-20240403001 修正Web流程管理工具中建立核決活動時更換預設id會在發起流程後報錯的問題
  - 變更檔案: 1 個
- **2024-03-27 10:50:11**: [PRODT]Q00-20240327001 修正Web流程管理工具中匯入流程無法覆蓋流程進版的問題
  - 變更檔案: 3 個
- **2024-01-31 12:15:50**: [PRODT]Q00-20240131001 修正Web流程管理工具中元件放到連接線上會自動生成或合併線的問題
  - 變更檔案: 1 個
- **2024-01-25 15:51:11**: [PRODT]Q00-20240125003 修正Web流程管理工具中流程模型屬性設定無法點確定的問題
  - 變更檔案: 1 個
- **2024-01-24 15:33:34**: [PRODT]Q00-20240124001 修正Web流程管理工具中活動參與者為職務時其值顯示不正確的問題
  - 變更檔案: 1 個
- **2024-01-22 15:53:10**: [PRODT]Q00-20240122001 修正Web流程管理工具中從流程主旨進入編輯範本後其變數頁籤未顯示的問題
  - 變更檔案: 3 個
- **2024-01-10 11:10:10**: [PRODT]Q00-20240110001 修正Web流程管理工具中儲存進版後的建立日期非當前日期的問題
  - 變更檔案: 4 個
- **2024-01-05 15:51:55**: [ORGDT]Q00-20240105002 修正Web組織管理工具中工作行事曆新增適用日期到第二頁會有欄位存在空值的錯誤提示問題
  - 變更檔案: 1 個
- **2023-12-20 18:37:47**: [PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題
  - 變更檔案: 1 個
- **2023-12-20 10:18:40**: [PRODT]Q00-20231220002 修正Web流程管理工具中核決活動的核決規則其層級選擇異常的問題
  - 變更檔案: 1 個
- **2023-12-14 18:40:50**: [PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題
  - 變更檔案: 6 個
- **2023-12-05 18:54:37**: [PRODT]Q00-20231205006 修正Web流程管理工具中流程圖有缺少連接線時仍可儲存的問題
  - 變更檔案: 2 個
- **2023-12-01 17:06:36**: [ORGDT]Q00-20231201005 修正組織管理工具從首頁使用者清單調離並轉調其他單位時不會帶入原單位任職資料的問題
  - 變更檔案: 3 個
- **2023-12-01 10:51:32**: [ORGDT]Q00-20231201003 修正組織管理工具從搜尋修改使用者資料後返回首頁使用者清單未更新的問題
  - 變更檔案: 1 個
- **2023-12-01 10:25:39**: [ORGDT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題
  - 變更檔案: 1 個
- **2023-12-01 10:25:05**: [PRODT]Q00-20231201001 修正流程管理工具進階按鈕禁用選項點擊後仍會觸發問題
  - 變更檔案: 1 個
- **2023-11-28 11:32:17**: [PRODT]Q00-20231128002 修正流程樹節點禁用選項點擊後仍會觸發問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [ORGDT]C01-20241121003 调整新增、修改组织ID对前后有空格的处理
- **Commit ID**: `e2ae8df10696e67f6c228b17b00e11197e1d9587`
- **作者**: 周权
- **日期**: 2024-11-26 14:32:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/OrganizationDesignMgr.java`

### 2. Revert "[PRODT] C01-20241121003 修正系统管理LDAP编辑画面组织id中有空格会trim掉的问题"
- **Commit ID**: `0233899715b60006e1019b02d0636f4154125d98`
- **作者**: 周权
- **日期**: 2024-11-26 10:07:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-authentication/sys-conf-drawer-authentication.component.ts`

### 3. [PRODT] C01-20241121003 修正系统管理LDAP编辑画面组织id中有空格会trim掉的问题
- **Commit ID**: `5d2a344e43a4f88e77fee4a05ec1be38fccab708`
- **作者**: 周权
- **日期**: 2024-11-22 14:46:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/system-manage-tool/system-configuration/sys-conf-drawer-authentication/sys-conf-drawer-authentication.component.ts`

### 4. [PRODT]V00-20240328001 修正Web流程管理工具當登入者沒有組織設計師權限時會無法選擇使用者或組織問題
- **Commit ID**: `eb6bc0de2450b2ab835ef133c404ce8b4e692e68`
- **作者**: yamiyeh10
- **日期**: 2024-07-12 10:45:37
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/add-invoke-aurhority-openwin/add-invoke-aurhority-openwin.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/organization-relationship/organization-relationship.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/participants/participant-chooser/participant-chooser.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/process-definition-redefinable-header/process-definition-redefinable-header.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/shared/components/organization-tree/organization-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/shared/components/select-organization-tree/select-organization-tree.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/shared/components/select-organization-tree/select-organization-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/shared/components/select-user-tree/select-user-tree.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/shared/components/select-user-tree/select-user-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/shared/services/organization-tree.service.ts`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/restful/SharedServicesController.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/SharedServicesMgr.java`
  - 📝 **修改**: `src/main/resources/application.properties`

### 5. [流程封存]C01-20241021006 修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補2)
- **Commit ID**: `0d361cf6a66e0b3379c43341bfafe63bf6afaeb7`
- **作者**: lorenchang
- **日期**: 2024-11-07 11:00:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/util/init/TimeScheduleInitializer.java`

### 6. [流程封存]修正更新排程時間的程式只在封存主機執行並增加更詳細的Log(補)
- **Commit ID**: `6ce9e420ff1c8260013adeac3914c7f998ea19bb`
- **作者**: lorenchang
- **日期**: 2024-10-28 11:19:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java`

### 7. [流程封存]修正更新排程時間的程式只在封存主機執行並增加更詳細的Log
- **Commit ID**: `c13076e0e2466c3507764f53b95b541fc866c2b0`
- **作者**: lorenchang
- **日期**: 2024-10-04 17:16:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/QuartzManager.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java`

### 8. [內部]在儲存排程及觸發排程時增加Log
- **Commit ID**: `aa7025135e30c62a1bcb00fa5dc7cb15d775766c`
- **作者**: lorenchang
- **日期**: 2024-09-20 13:34:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java`

### 9. [PRODT]C01-20420912001 調整Web流程管理工具在儲存流程前重新設定連接線顏色避免發生顏色未更動情況
- **Commit ID**: `05e4812548828909b66226e76ec005b2aca0c9d3`
- **作者**: yamiyeh10
- **日期**: 2024-09-26 08:21:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 10. [流程封存] C01-20240506005 調整流程封存維護作業日期儲存計算方式
- **Commit ID**: `779280687a594acea210c09f78e0b25a087a9243`
- **作者**: 邱郁晏
- **日期**: 2024-07-12 10:23:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/service/impl/ArchiveTimeScheduleServiceImpl.java`

### 11. [PRODT]C01-20240710001 修正Web流程管理工具在活動定義編輯器中將允許系統改派他人取消勾選後還是存在問題
- **Commit ID**: `b5715cf5c7d4bab0474fa3518fe0f52d1caf1b1c`
- **作者**: yamiyeh10
- **日期**: 2024-07-10 17:32:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-advanced-attributes/activity-advanced-attributes.component.ts`

### 12. [內部]A00-20240625001 NG-Zorro套件引入越南語系
- **Commit ID**: `1e838ab8c4baf377c08edc6f68bf66f973d5c30c`
- **作者**: yamiyeh10
- **日期**: 2024-06-26 10:21:15
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/CommonProgramModule/src/app/app.module.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/app.module.ts`
  - 📝 **修改**: `AngularProjects/ProcessArchiveModule/src/app/app.module.ts`

### 13. [流程封存]S00-20240123001 調整封存流程規則設定的結案時間超過屬性新增以月為單位
- **Commit ID**: `93c2730f100f7fb5da7446ca81028727c4cc172f`
- **作者**: cherryliao
- **日期**: 2024-02-22 10:37:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/ProcessArchiveModule/src/app/process-archive/archive-setting-manage/add-process-archive-rule/add-process-archive-rule.component.html`

### 14. [PRODT] V00-20240612001 修正Web流程管理工具加入核決層級關卡後無法儲存會跳出未設定條件等提示訊息問題
- **Commit ID**: `b00a92147ec930aa7ea3bf1842fcf464aa1d9457`
- **作者**: yamiyeh10
- **日期**: 2024-06-12 11:57:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 15. [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
- **Commit ID**: `43bf9c38e99eb0bce7ed1314ec03f40ce71cab62`
- **作者**: yamiyeh10
- **日期**: 2024-06-11 17:06:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-rule-list-editor/decision-rule-list-editor.component.ts`

### 16. [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息[補]
- **Commit ID**: `7ee7492562e2779d3b4c8fde1dea8c243aa56ead`
- **作者**: yamiyeh10
- **日期**: 2024-05-31 17:38:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 17. [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制[補]
- **Commit ID**: `f534eb3c62097f04741b5c16ea9e79b884d9a848`
- **作者**: yamiyeh10
- **日期**: 2024-05-31 16:31:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 18. [PRODT]C01-20240527001 調整Web流程管理工具在匯入流程中當核決關卡的條件與層級存在異常資料時在畫面上顯示提示訊息
- **Commit ID**: `94dc39bce73a1b5c7949e70a23a9ab2ce98712d1`
- **作者**: yamiyeh10
- **日期**: 2024-05-29 17:30:08
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-rule-list-editor/decision-rule-list-editor.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/models/save-process-package-response-body.interface.ts`

### 19. [PRODT]C01-20240517015 修正Web流程管理工具中活動定義編輯器上編輯網路服務的應用程式時會發生找不到portName問題
- **Commit ID**: `bbe312101d956253e66974cfbe4c29438e74847b`
- **作者**: yamiyeh10
- **日期**: 2024-05-21 15:31:08
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/web-services-openwin/web-services-openwin.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/openwin/web-services-openwin/web-services-openwin.component.ts`

### 20. [流程封存] C01-20240510006 已封存流程在監控流程仍查看的到问题修复
- **Commit ID**: `2a246589b93dc7a412bc2c182bc6cae3c8088764`
- **作者**: 刘旭
- **日期**: 2024-05-15 17:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/ProcessArchiveJob.java`

### 21. [PRODT]Q00-20240429003 修正Web流程管理工具中匯入與新建流程時識別碼卡控不可填寫中文機制
- **Commit ID**: `10ac01e16637f39d7fdbaad2a117b485862efebb`
- **作者**: yamiyeh10
- **日期**: 2024-04-30 14:55:28
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 22. [PRODT]Q00-20240415003 調整流程屬於簽核樣版分類時不驗證流程ID命名規格必須是[營運中心_單據_單別]的機制
- **Commit ID**: `783d8fd27e3086ed3d59da60680b47537dc36c9c`
- **作者**: yamiyeh10
- **日期**: 2024-04-16 08:56:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 23. [PRODT]V00-20240407001 修正Web流程管理工具中發起權限設定屬性刪除所有資料後原先資料還是存在的問題
- **Commit ID**: `47bd65fef0feb067ecd8ef7240535be9b10f69a2`
- **作者**: yamiyeh10
- **日期**: 2024-04-24 13:53:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`

### 24. [PRODT]Q00-20240417001 修正Web流程管理工具中流程定義進版後不會更新作者資訊問題
- **Commit ID**: `a3b4d8255e4ca3c3608fbdca6dad058d473661c7`
- **作者**: yamiyeh10
- **日期**: 2024-04-17 09:33:23
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 25. [PRODT]Q00-20240403001 修正Web流程管理工具中建立核決活動時更換預設id會在發起流程後報錯的問題
- **Commit ID**: `375c498c1b856d05209b361d8fa66248c4bc5e8f`
- **作者**: pinchi_lin
- **日期**: 2024-04-03 14:51:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/decision-activity-manager-drawer/decision-activity-manager-drawer.component.ts`

### 26. [PRODT]Q00-20240327001 修正Web流程管理工具中匯入流程無法覆蓋流程進版的問題
- **Commit ID**: `75fa4fc08e93bc503d46f690d5304e75cfbba261`
- **作者**: pinchi_lin
- **日期**: 2024-03-27 10:50:11
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/services/process-package-manage.service.ts`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/ProcessDesignMgr.java`

### 27. [ORGDT]Q00-20240326005 修正Web組織管理工具中列印組織圖異常問題
- **Commit ID**: `fd36112f4e6d529d3d794c8b0e1ef1843ae09c18`
- **作者**: yamiyeh10
- **日期**: 2024-03-26 18:45:07
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/print-openwin/print-openwin.component.css`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/print-openwin/print-openwin.component.ts`

### 28. [內部]Q00-20240315002 調整Log訊息層級
- **Commit ID**: `0125f743b60a603441717fd3d1a3f69056d88034`
- **作者**: yamiyeh10
- **日期**: 2024-03-15 18:29:58
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/restful/DesignerAuthorityController.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/restful/OrganizationDesignController.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/restful/ProcessDesignController.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/restful/SharedServicesController.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/restful/TiptopSystemIntegrationController.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/DesignerAuthorityMgr.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/SharedServicesMgr.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/SystemAuthManagerMgr.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/SystemConfigMgr.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/TiptopSystemIntegrationMgr.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/PlatformModule/restful/LoginVerifyController.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/PlatformModule/service/LoginVerifyMgr.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/schedule/CleanEventRecordTask.java`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/ProcessArchiveModule/util/init/TimeScheduleInitializer.java`

### 29. [PRODT]Q00-20240226001 修正Web流程管理工具中核決層級關卡的參考活動顯示異常的問題
- **Commit ID**: `6cda2aa56af38ce2ecf9d6956d79a5400b54a4bd`
- **作者**: cherryliao
- **日期**: 2024-02-26 14:09:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-general-attribute.component.ts`

### 30. [PRODT]Q00-20240221005 修正Web流程管理工具中核決層級關卡的參考活動為自定義時要再次編輯資料時發生丟失的問題
- **Commit ID**: `b4d58ca923b22b1ab495f2737341889f77e5112b`
- **作者**: cherryliao
- **日期**: 2024-02-21 15:21:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/activity-chooser/activity-chooser.component.ts`

### 31. [DT]Q00-20240203001 調整組織設計工具最上層的Enterprise Organizations名稱改用系統多語系維護
- **Commit ID**: `3bdc85b70b16a68098a7dfd925618fd8e0ca5a78`
- **作者**: yamiyeh10
- **日期**: 2024-02-19 11:33:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/shared/components/organization-tree/organization-tree.component.ts`

### 32. [PRODT]Q00-20240131001 修正Web流程管理工具中元件放到連接線上會自動生成或合併線的問題
- **Commit ID**: `8d822a24ddc7a8382401ccea21a5dcc7c27aceb3`
- **作者**: pinchi_lin
- **日期**: 2024-01-31 12:15:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/bpmn-js/lib/features/modeling/behavior/DropOnFlowBehavior.js`

### 33. [PRODT]Q00-20240125003 修正Web流程管理工具中流程模型屬性設定無法點確定的問題
- **Commit ID**: `95cdcfabcd86d0067f440eccff1e4b0a51fa730f`
- **作者**: pinchi_lin
- **日期**: 2024-01-25 15:51:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`

### 34. [PRODT]Q00-20240124001 修正Web流程管理工具中活動參與者為職務時其值顯示不正確的問題
- **Commit ID**: `c36ce3b20f96affbb56d052681fae02416365f5f`
- **作者**: pinchi_lin
- **日期**: 2024-01-24 15:33:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/ProcessDesignMgr.java`

### 35. [PRODT]Q00-20240122001 修正Web流程管理工具中從流程主旨進入編輯範本後其變數頁籤未顯示的問題
- **Commit ID**: `f4088f4cae03690625fb8c596053a6042198a5a3`
- **作者**: pinchi_lin
- **日期**: 2024-01-22 15:53:10
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/process-inspector-drawer/process-inspector-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/pattern-editor/pattern-editor.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/pattern-editor/pattern-editor.component.ts`

### 36. [PRODT]Q00-20240110001 修正Web流程管理工具中儲存進版後的建立日期非當前日期的問題
- **Commit ID**: `ba3fcaf5d7aceede6555fa4a8f453fe8cb0cb763`
- **作者**: pinchi_lin
- **日期**: 2024-01-10 11:10:10
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/save-process-package-drawer/save-process-package-drawer.component.html`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/drawers/save-process-package-drawer/save-process-package-drawer.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 37. [ORGDT]Q00-20240105002 修正Web組織管理工具中工作行事曆新增適用日期到第二頁會有欄位存在空值的錯誤提示問題
- **Commit ID**: `faef0baa72adc03118b8383a44d0148754034fe3`
- **作者**: pinchi_lin
- **日期**: 2024-01-05 15:51:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/manage-work-calendar-create-openwin/manage-work-calendar-create-openwin.component.ts`

### 38. [PRODT]Q00-20231220009 修正Web流程管理工具中任務或閘道元件一併刪除連接線異常導致無法刪除的問題
- **Commit ID**: `88a6f8d7b4941bffc52be6134e3e482d24fbc2ef`
- **作者**: pinchi_lin
- **日期**: 2023-12-20 18:37:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`

### 39. [PRODT]Q00-20231220002 修正Web流程管理工具中核決活動的核決規則其層級選擇異常的問題
- **Commit ID**: `2d4fb06d54da798bc4bd8ac4f8707a9e156aa688`
- **作者**: pinchi_lin
- **日期**: 2023-12-20 10:18:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/components/decision-general-attribute/decision-rule-list-editor/decision-rule-list-editor.component.ts`

### 40. [PRODT]Q00-20231214002 修正Web流程管理工具中流程樹會顯示流程草稿的問題
- **Commit ID**: `0cba75c19e079616078a0951e09207a77e35e39f`
- **作者**: pinchi_lin
- **日期**: 2023-12-14 18:40:50
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/services/home-button-event-emitter-collection.service.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/shared/services/process-package-manage.service.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/tabs/tabs.component.ts`
  - 📝 **修改**: `src/main/java/com/digiwin/bpm/DTModule/services/ProcessDesignMgr.java`

### 41. [PRODT]Q00-20231205006 修正Web流程管理工具中流程圖有缺少連接線時仍可儲存的問題
- **Commit ID**: `ab9d029ee7c591a6974b24a99216c9449c13f299`
- **作者**: pinchi_lin
- **日期**: 2023-12-05 18:54:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/bpmn-diagram/bpmn-diagram.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

### 42. [ORGDT]Q00-20231201005 修正組織管理工具從首頁使用者清單調離並轉調其他單位時不會帶入原單位任職資料的問題
- **Commit ID**: `fecb9f2d58f5b5b510bb20d42dbbeba6e431b28a`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 17:06:36
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/hosting-department-openwin/hosting-department-openwin.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/openwin/hosting-project-openwin/hosting-project-openwin.component.ts`
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/tables/user-table/user-table.component.ts`

### 43. [ORGDT]Q00-20231201003 修正組織管理工具從搜尋修改使用者資料後返回首頁使用者清單未更新的問題
- **Commit ID**: `7a6226c80d42031b783f7984d3168858a9c1f829`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 10:51:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>

### 44. [ORGDT]Q00-20231201002 修正組織管理工具進階按鈕禁用選項點擊後仍會觸發問題
- **Commit ID**: `5803e810931ac74072dbed2ccddd6ae9c0cc112d`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 10:25:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/organization-design-tool/home/<USER>

### 45. [PRODT]Q00-20231201001 修正流程管理工具進階按鈕禁用選項點擊後仍會觸發問題
- **Commit ID**: `307e592876e5df7a4e6da1d3eea844a8e1d14e8f`
- **作者**: pinchi_lin
- **日期**: 2023-12-01 10:25:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/home/<USER>

### 46. [PRODT]Q00-20231128002 修正流程樹節點禁用選項點擊後仍會觸發問題
- **Commit ID**: `0061b7777fcf6dc8be8bd4a630020d8bff05198a`
- **作者**: pinchi_lin
- **日期**: 2023-11-28 11:32:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `AngularProjects/DTModule/src/app/bpm-design-tool/process-design-tool/process-design-tree/process-design-tree.component.ts`

