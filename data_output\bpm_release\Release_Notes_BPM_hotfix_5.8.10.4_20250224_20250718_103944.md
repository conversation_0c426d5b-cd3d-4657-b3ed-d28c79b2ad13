# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.10.4_20250224
- **舊版本**: release_5.8.10.4
- **生成時間**: 2025-07-18 10:39:44
- **新增 Commit 數量**: 13

## 變更摘要

### lorenchang (4 commits)

- **2025-02-24 14:53:50**: [文件總結助手]Q00-20250224001 修正找經驗關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)
  - 變更檔案: 1 個
- **2025-02-17 16:14:46**: [文件總結助手]Q00-20250220001 修正助閱讀關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)
  - 變更檔案: 1 個
- **2025-02-17 16:14:46**: [雙因素認證]C01-20250217001 修正啟用帳號鎖定次數時雙因素驗出現異常：Argument pSqlString cannot be null or empty string
  - 變更檔案: 1 個
- **2024-12-03 17:24:14**: [流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常
  - 變更檔案: 1 個

### 周权 (7 commits)

- **2025-01-07 17:27:19**: [資安]Q00-20241227001 多選開窗查詢資安問題修正
  - 變更檔案: 4 個
- **2025-01-05 22:34:45**: [資安]Q00-20241226001 Sql Injection問題，调整多选开窗参数txtConditionValue
  - 變更檔案: 1 個
- **2025-02-14 10:26:37**: [Web] C01-20250206008 調整助閲讀、找經驗分頁查詢
  - 變更檔案: 4 個
- **2024-12-20 14:11:20**: [資安]Q00-20241217004 調整個人訊息頁欄位安全性問題
  - 變更檔案: 2 個
- **2024-12-19 10:43:41**: [資安]Q00-20241217003 調整查詢欄位可以輸入查詢條件支持查詢的問題，防止SQL注入
  - 變更檔案: 1 個
- **2024-12-19 10:23:42**: [資安]Q00-20241217002 调整登入错误讯息[補]
  - 變更檔案: 3 個
- **2024-12-18 16:10:44**: [資安]Q00-20241217002 调整登入错误讯息
  - 變更檔案: 4 個

### kmin (1 commits)

- **2024-12-18 14:50:52**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]
  - 變更檔案: 1 個

### 張詠威 (1 commits)

- **2024-10-17 14:03:53**: [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [文件總結助手]Q00-20250224001 修正找經驗關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)
- **Commit ID**: `6495e4bfaae71685a9a17bdd51c98ed95366286f`
- **作者**: lorenchang
- **日期**: 2025-02-24 14:53:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java`

### 2. [資安]Q00-20241227001 多選開窗查詢資安問題修正
- **Commit ID**: `3695ed504b75d4ecc105a65634332031373d21fd`
- **作者**: 周权
- **日期**: 2025-01-07 17:27:19
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml`

### 3. [資安]Q00-20241226001 Sql Injection問題，调整多选开窗参数txtConditionValue
- **Commit ID**: `936e8e74d87ee61442249aefee6ccb40d16c9d9a`
- **作者**: 周权
- **日期**: 2025-01-05 22:34:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`

### 4. [文件總結助手]Q00-20250220001 修正助閱讀關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)
- **Commit ID**: `0cde96b7a2ebf4794deec704cc26d80453b995dd`
- **作者**: lorenchang
- **日期**: 2025-02-17 16:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java`

### 5. [Web] C01-20250206008 調整助閲讀、找經驗分頁查詢
- **Commit ID**: `bfa89d43ba1fc46d634871a31c4b0d8882bf8d21`
- **作者**: 周权
- **日期**: 2025-02-14 10:26:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmCompanyMappingDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmInitiateProcessProfileDaoImpl.java`

### 6. [雙因素認證]C01-20250217001 修正啟用帳號鎖定次數時雙因素驗出現異常：Argument pSqlString cannot be null or empty string
- **Commit ID**: `29b17307ef79920fbb65df9cdc2cf196d9d57b17`
- **作者**: lorenchang
- **日期**: 2025-02-17 16:14:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 7. [流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常
- **Commit ID**: `d4d8575854e8290ddafbe37de771e82b88882878`
- **作者**: lorenchang
- **日期**: 2024-12-03 17:24:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 8. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]
- **Commit ID**: `9ada8bfcd645ad277e6b269ba063e136c95fa4a1`
- **作者**: kmin
- **日期**: 2024-12-18 14:50:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 9. [流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常
- **Commit ID**: `99f505c932f7a7aaad751b5288746d64d8c914cf`
- **作者**: 張詠威
- **日期**: 2024-10-17 14:03:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 10. [資安]Q00-20241217004 調整個人訊息頁欄位安全性問題
- **Commit ID**: `41af72642d65968e8b55b08d83b5141104e0622c`
- **作者**: 周权
- **日期**: 2024-12-20 14:11:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`

### 11. [資安]Q00-20241217003 調整查詢欄位可以輸入查詢條件支持查詢的問題，防止SQL注入
- **Commit ID**: `9287c65390def0fc662156d80d63c15409f86a92`
- **作者**: 周权
- **日期**: 2024-12-19 10:43:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 12. [資安]Q00-20241217002 调整登入错误讯息[補]
- **Commit ID**: `b936fce4c7cf078b8d73edb70008982606709af1`
- **作者**: 周权
- **日期**: 2024-12-19 10:23:42
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 13. [資安]Q00-20241217002 调整登入错误讯息
- **Commit ID**: `bf1ec218ad7e5cdfa1e1d216b5b9235afc764ce1`
- **作者**: 周权
- **日期**: 2024-12-18 16:10:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

