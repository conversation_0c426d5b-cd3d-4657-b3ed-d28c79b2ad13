import streamlit as st
import json
import os
from pathlib import Path
from datetime import datetime
import re
import sys

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設定Streamlit頁面配置
st.set_page_config(
    page_title="產品Release記錄查詢工具",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"  # 工具頁面展開側邊欄方便導航
)

# 隱藏默認英文導航並添加中文側邊欄
st.markdown("""
<style>
    /* 隱藏Streamlit默認的英文頁面導航 */
    [data-testid="stSidebarNav"] {
        display: none !important;
    }

    .css-1544g2n, .css-17lntkn, .css-1y4p8pa, .css-1d391kg {
        display: none !important;
    }

    /* 側邊欄美化樣式 */
    .sidebar-nav-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        color: white;
        text-align: center;
        font-weight: bold;
    }

    .current-tool {
        background: #fce4ec;
        border-radius: 8px;
        padding: 0.8rem;
        margin: 1rem 0;
        border-left: 4px solid #e91e63;
    }

    .system-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.8rem;
        margin-top: 1rem;
        border-left: 4px solid #f093fb;
    }

    .system-info-item {
        font-size: 0.85rem;
        color: #666;
        margin: 0.3rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 側邊欄導航
with st.sidebar:
    st.markdown("""
    <div class="sidebar-nav-header">
        🧭 導航選單
    </div>
    """, unsafe_allow_html=True)

    # 返回主頁按鈕
    if st.button("🏠 返回主頁", use_container_width=True, help="返回工具選擇主頁"):
        st.switch_page("streamlit_home.py")

    st.markdown("---")

    # 當前工具提示
    st.markdown("""
    <div class="current-tool">
        <div style="font-weight: bold; color: #c2185b;">📊 當前工具</div>
        <div style="font-size: 0.9rem; color: #666;">產品Release記錄查詢</div>
    </div>
    """, unsafe_allow_html=True)

    # 工具切換
    st.markdown("### 🛠️ 切換工具")

    if st.button("🔍 檔案索引路徑查詢", use_container_width=True, help="切換到檔案索引路徑查詢工具"):
        st.switch_page("pages/file_search.py")

    st.markdown("---")

    # 系統資訊
    st.markdown("""
    <div class="system-info">
        <div style="font-weight: bold; margin-bottom: 0.5rem;">ℹ️ 系統資訊</div>
        <div class="system-info-item">📦 版本：1.0.0</div>
        <div class="system-info-item">📅 更新：2025年7月</div>
        <div class="system-info-item">🏢 部門：BPM服務部</div>
        <div class="system-info-item">📊 功能：Release記錄查詢分析</div>
    </div>
    """, unsafe_allow_html=True)

st.title("📊 產品Release記錄查詢工具")

# GitHub風格CSS樣式
st.markdown("""
<style>
    /* GitHub風格的commit卡片 */
    .github-commit {
        border: 1px solid #d1d9e0;
        border-radius: 8px;
        margin: 12px 0;
        background: white;
        transition: all 0.2s ease;
        overflow: hidden;
    }
    .github-commit:hover {
        border-color: #0969da;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* GitHub風格的commit頭部 */
    .github-header {
        padding: 16px;
        border-bottom: 1px solid #d1d9e0;
        display: flex;
        align-items: center;
        gap: 12px;
        background: #f6f8fa;
    }

    /* GitHub風格的頭像 */
    .github-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(45deg, #0969da, #218bff);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 14px;
        flex-shrink: 0;
    }

    /* GitHub風格的commit資訊 */
    .github-info {
        flex: 1;
        min-width: 0;
    }
    .github-title {
        font-weight: 600;
        color: #1f2328;
        margin: 0 0 4px 0;
        font-size: 16px;
        line-height: 1.3;
        word-wrap: break-word;
    }
    .github-meta {
        color: #656d76;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    /* GitHub風格的commit hash */
    .github-hash {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        background: #f6f8fa;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        border: 1px solid #d1d9e0;
        color: #1f2328;
    }

    /* GitHub風格的統計資訊 */
    .github-stats {
        display: flex;
        gap: 16px;
        padding: 12px 16px;
        background: #f6f8fa;
        font-size: 14px;
        color: #656d76;
        border-top: 1px solid #d1d9e0;
    }

    /* GitHub風格的檔案變更 */
    .github-files {
        padding: 16px;
        background: white;
    }
    .github-file-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 0;
        font-size: 14px;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }
    .github-file-status {
        width: 16px;
        height: 16px;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        color: white;
        flex-shrink: 0;
    }
    .status-modified { background: #fb8500; }
    .status-added { background: #2da44e; }
    .status-deleted { background: #cf222e; }
    .status-renamed { background: #8250df; }

    /* GitHub風格的分支標題 */
    .github-branch-header {
        background: linear-gradient(135deg, #f6f8fa 0%, #e1e4e8 100%);
        border: 1px solid #d1d9e0;
        border-radius: 8px;
        padding: 16px;
        margin: 20px 0 16px 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }
    .github-branch-icon {
        width: 24px;
        height: 24px;
        background: #0969da;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
    }
    .github-branch-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2328;
        margin: 0;
    }
    .github-branch-count {
        background: #0969da;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        margin-left: auto;
    }

    /* GitHub風格的比較資訊 */
    .github-compare-info {
        background: #f6f8fa;
        border: 1px solid #d1d9e0;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
    }
    .github-compare-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2328;
        margin: 0 0 12px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .github-compare-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
    }
    .github-compare-item {
        background: white;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #d1d9e0;
    }
    .github-compare-label {
        font-size: 12px;
        color: #656d76;
        margin-bottom: 4px;
        font-weight: 500;
    }
    .github-compare-value {
        font-size: 14px;
        color: #1f2328;
        font-weight: 600;
        font-family: 'SFMono-Regular', Consolas, monospace;
    }

    /* 響應式設計 */
    @media (max-width: 768px) {
        .github-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }
        .github-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
        }
        .github-stats {
            flex-direction: column;
            gap: 8px;
        }
        .github-compare-grid {
            grid-template-columns: 1fr;
        }
    }

    .status-modified { border-left: 3px solid #ffc107; }
    .status-added { border-left: 3px solid #28a745; }
    .status-deleted { border-left: 3px solid #dc3545; }
    .status-renamed { border-left: 3px solid #17a2b8; }
    
    .branch-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
        text-align: center;
        font-weight: bold;
    }
    
    .stats-container {
        background: #f1f3f4;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 定義release_output目錄路徑
RELEASE_DIR = project_root / "data_output" / "bpm_release"

def get_release_files():
    """獲取所有release JSON檔案"""
    if not RELEASE_DIR.exists():
        return []
    return [f for f in RELEASE_DIR.glob("*.json")]

def parse_filename(filename):
    """解析檔案名稱，提取專案名稱、分支等資訊"""
    name = filename.stem
    parts = name.split('_')

    project = parts[0] if parts else "Unknown"

    # 提取版本資訊
    version_pattern = r'(\d+\.\d+\.\d+\.\d+)'
    versions = re.findall(version_pattern, name)

    # 判斷分支類型
    branch_type = "unknown"
    if "hotfix" in name.lower():
        branch_type = "hotfix"
    elif "release" in name.lower():
        branch_type = "release"

    # 提取主要版本號用於排序
    main_version = versions[0] if versions else "0.0.0.0"

    # 生成友善的顯示名稱
    display_name = generate_display_name(name, branch_type, versions)

    return {
        'project': project,
        'filename': filename.name,
        'versions': versions,
        'main_version': main_version,
        'branch_type': branch_type,
        'display_name': display_name,
        'full_name': name
    }

def generate_display_name(filename, branch_type, versions):
    """生成友善的顯示名稱"""
    if not versions:
        return filename

    main_version = versions[0]

    if branch_type == "hotfix":
        # 檢查是否有特殊標識
        if "_All_" in filename:
            return f"hotfix_{main_version}_All"

        # 檢查是否有特殊名稱標識（如 scsb, haers, cnce 等）
        special_patterns = [
            r'hotfix_[\d\.]+_([a-zA-Z]+)_to_',
            r'_hotfix_[\d\.]+_([a-zA-Z]+)_'
        ]

        for pattern in special_patterns:
            matches = re.findall(pattern, filename)
            if matches:
                special_name = matches[0]
                if special_name not in ['to', 'release'] and len(special_name) > 2:
                    return f"hotfix_{main_version}_{special_name}"

        # 提取日期部分 (8位數字)
        date_pattern = r'_(\d{8})_'
        dates = re.findall(date_pattern, filename)
        if dates:
            date_str = dates[0]
            # 保持原始日期格式，不進行轉換
            return f"hotfix_{main_version}_{date_str}"

        # 默認情況
        return f"hotfix_{main_version}"

    elif branch_type == "release":
        return f"release_{main_version}"

    return filename

def extract_date_from_display_name(display_name):
    """從顯示名稱中提取日期用於排序"""
    # 檢查是否為All版本（優先級最高）
    if display_name.endswith('_All'):
        return "99999999"  # 最大值，確保All版本排在最前面

    # 提取8位數字的日期
    date_pattern = r'_(\d{8})$'
    match = re.search(date_pattern, display_name)
    if match:
        return match.group(1)
    return "00000000"  # 默認值，確保沒有日期的項目排在最後

def create_sort_key(file_info):
    """創建排序鍵，優先按版本號，再按日期"""
    version_key = version_sort_key(file_info['main_version'])

    # 如果是hotfix且有日期，按日期排序
    if file_info['branch_type'] == 'hotfix':
        date_str = extract_date_from_display_name(file_info['display_name'])
        # 日期按降序排列（新的在前）
        date_key = [-int(date_str)]
    else:
        date_key = [0]

    return version_key + date_key

def version_sort_key(version_str):
    """版本號排序鍵，用於由大到小排序"""
    try:
        parts = [int(x) for x in version_str.split('.')]
        # 補齊到4位數，確保比較一致性
        while len(parts) < 4:
            parts.append(0)
        # 返回負數以實現降序排序
        return [-x for x in parts]
    except:
        return [0, 0, 0, 0]

@st.cache_data
def load_release_data(file_path):
    """載入release JSON資料"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        st.error(f"載入檔案 {file_path} 時發生錯誤: {e}")
        return None

def format_datetime_display(dt_str):
    """格式化日期時間顯示"""
    try:
        dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
        return dt.strftime("%Y/%m/%d %H:%M:%S")
    except:
        return dt_str

def get_status_class(status_code):
    """根據狀態代碼返回CSS類別"""
    status_map = {
        'M': 'status-modified',
        'A': 'status-added', 
        'D': 'status-deleted',
        'R': 'status-renamed'
    }
    return status_map.get(status_code, 'status-modified')

def get_status_text(status_code):
    """根據狀態代碼返回中文說明"""
    status_map = {
        'M': '修改',
        'A': '新增',
        'D': '刪除', 
        'R': '重新命名'
    }
    return status_map.get(status_code, '修改')

# 主要功能區域
st.header("🔍 選擇查詢條件")

# 獲取所有release檔案
release_files = get_release_files()

if not release_files:
    st.error("❌ 未找到data_output/bpm_release目錄或目錄中沒有JSON檔案")
    st.info("請確認data_output/bpm_release目錄中有JSON檔案，或執行相關腳本生成檔案")
    st.stop()

# 解析所有檔案資訊
file_info = [parse_filename(f) for f in release_files]

# 專案選擇
projects = sorted(list(set([info['project'] for info in file_info])))
if not projects:
    st.error("❌ 無法從檔案名稱中解析出專案資訊")
    st.stop()

selected_project = st.selectbox("選擇專案", projects, index=0)

# 根據選擇的專案過濾檔案
project_files = [info for info in file_info if info['project'] == selected_project]

# 查詢方式選擇
query_mode = st.radio(
    "選擇查詢方式",
    ["依照Branch名稱瀏覽", "關鍵字搜尋Commit訊息", "檔案名稱搜尋"],
    horizontal=True
)

if query_mode == "依照Branch名稱瀏覽":
    st.subheader("📋 依照Branch名稱瀏覽")

    # 分析分支類型
    branch_types = set()
    for info in project_files:
        if info['branch_type'] != "unknown":
            branch_types.add(info['branch_type'])

    branch_types = sorted(list(branch_types))

    if not branch_types:
        st.warning("⚠️ 該專案沒有可用的分支類型")
    else:
        # 選擇分支類型（預設為Release分支）
        default_index = 0
        if "release" in branch_types:
            default_index = branch_types.index("release")

        selected_branch_type = st.selectbox(
            "選擇分支類型",
            branch_types,
            index=default_index,
            format_func=lambda x: f"🔧 Hotfix分支" if x == "hotfix" else f"🚀 Release分支"
        )

        # 根據選擇的分支類型過濾檔案
        filtered_files = [info for info in project_files if info['branch_type'] == selected_branch_type]

        if not filtered_files:
            st.warning(f"⚠️ 該專案沒有{selected_branch_type}類型的檔案")
        else:
            # 按版本號和日期排序（由大到小）
            filtered_files.sort(key=create_sort_key)

            # 選擇檔案
            file_options = [(info['display_name'], info['filename']) for info in filtered_files]

            selected_display_name = st.selectbox(
                "選擇Release檔案",
                [option[0] for option in file_options]
            )

            # 找到對應的檔案名稱
            selected_file_name = None
            for display_name, filename in file_options:
                if display_name == selected_display_name:
                    selected_file_name = filename
                    break

            if selected_file_name:
                selected_file_path = RELEASE_DIR / selected_file_name

                # 載入資料
                data = load_release_data(selected_file_path)
                if data:
                    # 顯示比較資訊 - GitHub風格
                    compare_info = data.get("比較資訊", {})
                    st.markdown(f"""
                    <div class="github-compare-info">
                        <div class="github-compare-title">
                            📊 比較資訊
                        </div>
                        <div class="github-compare-grid">
                            <div class="github-compare-item">
                                <div class="github-compare-label">專案ID</div>
                                <div class="github-compare-value">{compare_info.get('專案ID', 'N/A')}</div>
                            </div>
                            <div class="github-compare-item">
                                <div class="github-compare-label">新分支</div>
                                <div class="github-compare-value">{compare_info.get('新分支', {}).get('branch_name', 'N/A')}</div>
                            </div>
                            <div class="github-compare-item">
                                <div class="github-compare-label">舊分支</div>
                                <div class="github-compare-value">{compare_info.get('舊分支', {}).get('branch_name', 'N/A')}</div>
                            </div>
                            <div class="github-compare-item">
                                <div class="github-compare-label">新增Commit數量</div>
                                <div class="github-compare-value">{compare_info.get('新增commit數量', 0)}</div>
                            </div>
                            <div class="github-compare-item">
                                <div class="github-compare-label">新分支建立時間</div>
                                <div class="github-compare-value">{format_datetime_display(compare_info.get('新分支', {}).get('date', 'N/A'))}</div>
                            </div>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

                    # 顯示commit記錄 - GitHub風格
                    commits = data.get("新增commit記錄", [])
                    if commits:
                        st.markdown(f"""
                        <div class="github-branch-header">
                            <div class="github-branch-icon">📝</div>
                            <div class="github-branch-title">Commit記錄</div>
                            <div class="github-branch-count">{len(commits)} 筆</div>
                        </div>
                        """, unsafe_allow_html=True)

                        # 如果commit記錄較多，提示使用者可以使用瀏覽器搜尋功能
                        if len(commits) > 5:
                            st.info("💡 **小提示：** Commit記錄較多時，您可以使用瀏覽器的搜尋功能（**Ctrl+F**）來快速定位特定的Commit訊息、作者名稱或檔案路徑。")

                        # 按日期排序（由新到舊）
                        commits_sorted = sorted(commits, key=lambda x: x.get('提交日期', ''), reverse=True)

                        for commit in commits_sorted:
                            commit_msg = commit.get('commit_訊息', 'N/A')
                            author = commit.get('作者', 'N/A')
                            date = format_datetime_display(commit.get('提交日期', 'N/A'))
                            file_count = commit.get('變更檔案數量', 0)
                            commit_hash = commit.get('commit_hash', 'N/A')[:8]

                            author_initial = author[0].upper() if author and author != 'N/A' else '?'

                            st.markdown(f"""
                            <div class="github-commit">
                                <div class="github-header">
                                    <div class="github-avatar">{author_initial}</div>
                                    <div class="github-info">
                                        <div class="github-title">{commit_msg}</div>
                                        <div class="github-meta">
                                            <span>👤 {author}</span>
                                            <span>📅 {date}</span>
                                            <span class="github-hash">{commit_hash}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="github-stats">
                                    <span>📁 {file_count} files changed</span>
                                </div>
                            </div>
                            """, unsafe_allow_html=True)

                            # 顯示檔案變更 - GitHub風格
                            file_changes = commit.get('檔案變更', [])
                            if file_changes:
                                with st.expander(f"📁 查看檔案變更 ({len(file_changes)} 個)", expanded=False):
                                    for change in file_changes:
                                        status_code = change.get('狀態代碼', 'M')
                                        status_text = get_status_text(status_code)
                                        file_path = change.get('檔案路徑', 'N/A')

                                        # 根據狀態代碼設定樣式
                                        if status_code == 'M':
                                            status_class = 'status-modified'
                                            status_symbol = 'M'
                                        elif status_code == 'A':
                                            status_class = 'status-added'
                                            status_symbol = '+'
                                        elif status_code == 'D':
                                            status_class = 'status-deleted'
                                            status_symbol = '-'
                                        elif status_code == 'R':
                                            status_class = 'status-renamed'
                                            status_symbol = 'R'
                                        else:
                                            status_class = 'status-modified'
                                            status_symbol = 'M'

                                        st.markdown(f"""
                                        <div class="github-file-item">
                                            <div class="github-file-status {status_class}">{status_symbol}</div>
                                            <span>{file_path}</span>
                                        </div>
                                        """, unsafe_allow_html=True)

elif query_mode == "關鍵字搜尋Commit訊息":
    st.subheader("🔎 關鍵字搜尋Commit訊息")

    search_keyword = st.text_input("請輸入搜尋關鍵字", placeholder="例如：修正、新增、優化...")

    if search_keyword:
        st.info(f"🔍 搜尋關鍵字: {search_keyword}")

        # 按branch_name分類的結果
        results_by_branch = {}

        for info in project_files:
            file_path = RELEASE_DIR / info['filename']
            data = load_release_data(file_path)

            if data:
                branch_name = data.get("比較資訊", {}).get("新分支", {}).get("branch_name", "Unknown")
                commits = data.get("新增commit記錄", [])

                for commit in commits:
                    commit_msg = commit.get('commit_訊息', '')
                    if search_keyword.lower() in commit_msg.lower():
                        if branch_name not in results_by_branch:
                            results_by_branch[branch_name] = []

                        results_by_branch[branch_name].append({
                            'commit': commit,
                            'source_file': info['filename'],
                            'branch_info': data.get("比較資訊", {})
                        })

        if results_by_branch:
            st.success(f"✅ 在 {len(results_by_branch)} 個分支中找到相關Commit記錄")

            # 提示使用者可以使用瀏覽器搜尋功能
            st.info("💡 **小提示：** 查詢結果較多時，您可以使用瀏覽器的搜尋功能（**Ctrl+F**）來快速定位特定的Commit訊息、作者名稱或檔案路徑。")

            # 按分支名稱排序 - GitHub風格
            for branch_name in sorted(results_by_branch.keys(), reverse=True):
                results = results_by_branch[branch_name]

                st.markdown(f"""
                <div class="github-branch-header">
                    <div class="github-branch-icon">🌿</div>
                    <div class="github-branch-title">分支: {branch_name}</div>
                    <div class="github-branch-count">{len(results)} 筆記錄</div>
                </div>
                """, unsafe_allow_html=True)

                # 按日期排序
                results.sort(key=lambda x: x['commit'].get('提交日期', ''), reverse=True)

                for result in results:
                    commit = result['commit']
                    commit_msg = commit.get('commit_訊息', 'N/A')
                    author = commit.get('作者', 'N/A')
                    date = format_datetime_display(commit.get('提交日期', 'N/A'))
                    file_count = commit.get('變更檔案數量', 0)
                    commit_hash = commit.get('commit_hash', 'N/A')[:8]
                    source_file = result.get('source_file', 'N/A')

                    author_initial = author[0].upper() if author and author != 'N/A' else '?'

                    st.markdown(f"""
                    <div class="github-commit">
                        <div class="github-header">
                            <div class="github-avatar">{author_initial}</div>
                            <div class="github-info">
                                <div class="github-title">{commit_msg}</div>
                                <div class="github-meta">
                                    <span>👤 {author}</span>
                                    <span>📅 {date}</span>
                                    <span class="github-hash">{commit_hash}</span>
                                    <span>📄 {source_file}</span>
                                </div>
                            </div>
                        </div>
                        <div class="github-stats">
                            <span>📁 {file_count} files changed</span>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

                    # 顯示檔案變更 - GitHub風格
                    file_changes = commit.get('檔案變更', [])
                    if file_changes:
                        with st.expander(f"📁 查看檔案變更 ({len(file_changes)} 個)", expanded=False):
                            for change in file_changes:
                                status_code = change.get('狀態代碼', 'M')
                                file_path = change.get('檔案路徑', 'N/A')

                                # 根據狀態代碼設定樣式
                                if status_code == 'M':
                                    status_class = 'status-modified'
                                    status_symbol = 'M'
                                elif status_code == 'A':
                                    status_class = 'status-added'
                                    status_symbol = '+'
                                elif status_code == 'D':
                                    status_class = 'status-deleted'
                                    status_symbol = '-'
                                elif status_code == 'R':
                                    status_class = 'status-renamed'
                                    status_symbol = 'R'
                                else:
                                    status_class = 'status-modified'
                                    status_symbol = 'M'

                                st.markdown(f"""
                                <div class="github-file-item">
                                    <div class="github-file-status {status_class}">{status_symbol}</div>
                                    <span>{file_path}</span>
                                </div>
                                """, unsafe_allow_html=True)
        else:
            st.warning("⚠️ 未找到包含該關鍵字的Commit記錄")

elif query_mode == "檔案名稱搜尋":
    st.subheader("📁 檔案名稱搜尋")
    
    file_search_keyword = st.text_input("請輸入檔案名稱關鍵字", placeholder="例如：Action.java、Main.jsp...")
    
    if file_search_keyword:
        st.info(f"🔍 搜尋檔案名稱: {file_search_keyword}")
        
        # 按branch_name或TAG分類的結果
        results_by_branch = {}
        
        for info in project_files:
            file_path = RELEASE_DIR / info['filename']
            data = load_release_data(file_path)
            
            if data:
                branch_name = data.get("比較資訊", {}).get("新分支", {}).get("branch_name", "Unknown")
                commits = data.get("新增commit記錄", [])
                
                for commit in commits:
                    file_changes = commit.get('檔案變更', [])
                    for change in file_changes:
                        file_path_str = change.get('檔案路徑', '')
                        if file_search_keyword.lower() in file_path_str.lower():
                            if branch_name not in results_by_branch:
                                results_by_branch[branch_name] = []
                            
                            results_by_branch[branch_name].append({
                                'commit': commit,
                                'file_change': change,
                                'source_file': info['filename']
                            })
        
        if results_by_branch:
            st.success(f"✅ 在 {len(results_by_branch)} 個分支中找到相關檔案")

            # 提示使用者可以使用瀏覽器搜尋功能
            st.info("💡 **小提示：** 查詢結果較多時，您可以使用瀏覽器的搜尋功能（**Ctrl+F**）來快速定位特定的Commit訊息、作者名稱或檔案路徑。")
            
            # 按分支名稱排序 - GitHub風格
            for branch_name in sorted(results_by_branch.keys(), reverse=True):
                results = results_by_branch[branch_name]

                st.markdown(f"""
                <div class="github-branch-header">
                    <div class="github-branch-icon">🌿</div>
                    <div class="github-branch-title">分支: {branch_name}</div>
                    <div class="github-branch-count">{len(results)} 筆記錄</div>
                </div>
                """, unsafe_allow_html=True)

                # 按日期排序
                results.sort(key=lambda x: x['commit'].get('提交日期', ''), reverse=True)

                for result in results:
                    commit = result['commit']
                    file_change = result['file_change']
                    commit_msg = commit.get('commit_訊息', 'N/A')
                    author = commit.get('作者', 'N/A')
                    date = format_datetime_display(commit.get('提交日期', 'N/A'))
                    commit_hash = commit.get('commit_hash', 'N/A')[:8]
                    source_file = result.get('source_file', 'N/A')
                    matched_file = file_change.get('檔案路徑', 'N/A').split('/')[-1]

                    author_initial = author[0].upper() if author and author != 'N/A' else '?'

                    st.markdown(f"""
                    <div class="github-commit">
                        <div class="github-header">
                            <div class="github-avatar">{author_initial}</div>
                            <div class="github-info">
                                <div class="github-title">{commit_msg}</div>
                                <div class="github-meta">
                                    <span>👤 {author}</span>
                                    <span>📅 {date}</span>
                                    <span class="github-hash">{commit_hash}</span>
                                    <span>📄 {source_file}</span>
                                    <span>🎯 {matched_file}</span>
                                </div>
                            </div>
                        </div>
                        <div class="github-stats">
                            <span>🎯 匹配檔案: {matched_file}</span>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

                    # 顯示匹配的檔案變更 - GitHub風格
                    status_code = file_change.get('狀態代碼', 'M')
                    file_path = file_change.get('檔案路徑', 'N/A')

                    # 根據狀態代碼設定樣式
                    if status_code == 'M':
                        status_class = 'status-modified'
                        status_symbol = 'M'
                    elif status_code == 'A':
                        status_class = 'status-added'
                        status_symbol = '+'
                    elif status_code == 'D':
                        status_class = 'status-deleted'
                        status_symbol = '-'
                    elif status_code == 'R':
                        status_class = 'status-renamed'
                        status_symbol = 'R'
                    else:
                        status_class = 'status-modified'
                        status_symbol = 'M'

                    with st.expander(f"🎯 查看匹配檔案詳情", expanded=False):
                        st.markdown(f"""
                        <div class="github-files">
                            <div class="github-file-item">
                                <div class="github-file-status {status_class}">{status_symbol}</div>
                                <span><strong>🎯 匹配檔案:</strong> {file_path}</span>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)
        else:
            st.warning("⚠️ 未找到包含該檔案名稱的記錄")

# 顯示統計資訊
st.sidebar.header("📈 統計資訊")
st.sidebar.metric("總Release檔案數", len(release_files))
st.sidebar.metric("當前專案檔案數", len(project_files))

if project_files:
    # 計算總commit數
    total_commits = 0
    for info in project_files:
        file_path = RELEASE_DIR / info['filename']
        data = load_release_data(file_path)
        if data:
            total_commits += len(data.get("新增commit記錄", []))

    st.sidebar.metric("當前專案總Commit數", total_commits)

    # 分支類型統計
    branch_counts = {}
    for info in project_files:
        branch_type = info['branch_type']
        if branch_type not in branch_counts:
            branch_counts[branch_type] = 0
        branch_counts[branch_type] += 1

    st.sidebar.subheader("分支類型統計")
    for branch_type, count in branch_counts.items():
        icon = "🔧" if branch_type == "hotfix" else "🚀" if branch_type == "release" else "📄"
        st.sidebar.metric(f"{icon} {branch_type.capitalize()}", count)

    # 版本統計
    st.sidebar.subheader("版本統計")
    versions = {}
    for info in project_files:
        if info['main_version'] not in versions:
            versions[info['main_version']] = 0
        versions[info['main_version']] += 1

    # 按版本號排序（由大到小）
    sorted_versions = sorted(versions.keys(), key=version_sort_key)

    # 顯示前5個版本
    for version in sorted_versions[:5]:
        st.sidebar.metric(f"版本 {version}", versions[version])
