# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.10.3_All
- **舊版本**: release_5.8.10.3
- **生成時間**: 2025-07-18 10:39:54
- **新增 Commit 數量**: 10

## 變更摘要

### DESKTOP-R51BOK0\H-00778 (1 commits)

- **2025-05-22 15:38:39**: [資安]C01-20250520005 漏洞一:一般使用者可呼叫出管理者功能"跳過",透過開發者工具執行前端函式bypassActivity()。漏洞二：密碼驗證後執行的管理者功能也在前端，透過開發者工具執行前端函式completeVerify()跳過驗證
  - 變更檔案: 3 個

### lorenchang (5 commits)

- **2024-12-03 17:24:14**: [流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常
  - 變更檔案: 1 個
- **2024-10-23 16:05:04**: [雙因素認證]C01-20241022003 修正使用LdapId登入的記住此裝置沒有作用的異常(信任端點資訊也沒有記錄)
  - 變更檔案: 1 個
- **2024-09-24 10:23:34**: [雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常
  - 變更檔案: 9 個
- **2024-10-01 10:53:47**: [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]
  - 變更檔案: 1 個
- **2024-09-25 17:28:45**: [ISO]修正歸檔浮水印新增的字型設定產生的設定值與BCL8不相容造成中文字變方框
  - 變更檔案: 1 個

### kmin (3 commits)

- **2024-10-29 16:12:55**: [內部]Q00-20241029001 優化寄信mail log的記錄判讀
  - 變更檔案: 1 個
- **2024-10-09 10:49:16**: [流程引擎]A00-20241008001 修正通知信過濾非HTML標籤主旨值問題
  - 變更檔案: 2 個
- **2024-10-01 10:12:03**: [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息[補]
  - 變更檔案: 1 個

### 張詠威 (1 commits)

- **2024-10-01 10:16:11**: [Web]C01-20240927004 修正58103版本流程的簽核歷程和簡易流程圖畫面沒有顯示進行中的關卡的資訊
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [資安]C01-20250520005 漏洞一:一般使用者可呼叫出管理者功能"跳過",透過開發者工具執行前端函式bypassActivity()。漏洞二：密碼驗證後執行的管理者功能也在前端，透過開發者工具執行前端函式completeVerify()跳過驗證
- **Commit ID**: `b3057493f08f354ca5fdd3f745f2f88472aba96b`
- **作者**: DESKTOP-R51BOK0\H-00778
- **日期**: 2025-05-22 15:38:39
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordForByPass.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 2. [流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常
- **Commit ID**: `86e62f638d306af784c29a64bcd1024bdaf61284`
- **作者**: lorenchang
- **日期**: 2024-12-03 17:24:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 3. [內部]Q00-20241029001 優化寄信mail log的記錄判讀
- **Commit ID**: `0a125db78239478c807266830c643ab4d3103fd5`
- **作者**: kmin
- **日期**: 2024-10-29 16:12:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 4. [流程引擎]A00-20241008001 修正通知信過濾非HTML標籤主旨值問題
- **Commit ID**: `d3b2e93bcc2513d2e5873923448aa753423e115f`
- **作者**: kmin
- **日期**: 2024-10-09 10:49:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/digiwin/bpm/PlatformModule/util/HtmlUtils.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 5. [雙因素認證]C01-20241022003 修正使用LdapId登入的記住此裝置沒有作用的異常(信任端點資訊也沒有記錄)
- **Commit ID**: `0032c2d42649d173c8b7e829a7cdf3f86ccc642b`
- **作者**: lorenchang
- **日期**: 2024-10-23 16:05:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 6. [雙因素認證]C01-*********** 修正使用LdapId登入不會進入雙因素認證的異常
- **Commit ID**: `70ac10d865d5bf3ffbc2fea1e6942bf1483e657d`
- **作者**: lorenchang
- **日期**: 2024-09-24 10:23:34
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPILocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 7. [流程引擎]C01-20240806006 修正溝通郵件主失敗Mails未存入問題[補]
- **Commit ID**: `c856decf583e5f629964c7f0c7142ffa33648224`
- **作者**: lorenchang
- **日期**: 2024-10-01 10:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 8. [EBG]Q00-20240823002 優化EBG專案使用-作廢簽署文件log訊息[補]
- **Commit ID**: `19b47ffe3c05c75d7f5cbcff93879e744f1f99fc`
- **作者**: kmin
- **日期**: 2024-10-01 10:12:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ebgModule/EBGManagerBean.java`

### 9. [Web]C01-20240927004 修正58103版本流程的簽核歷程和簡易流程圖畫面沒有顯示進行中的關卡的資訊
- **Commit ID**: `ba15a63ab923367636b4846d6a23bbe0b1158be6`
- **作者**: 張詠威
- **日期**: 2024-10-01 10:16:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`

### 10. [ISO]修正歸檔浮水印新增的字型設定產生的設定值與BCL8不相容造成中文字變方框
- **Commit ID**: `c21865d316770f371cb6d29ebc3ab61ff0ff1745`
- **作者**: lorenchang
- **日期**: 2024-09-25 17:28:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`

