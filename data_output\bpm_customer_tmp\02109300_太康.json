{"company_id": "02109300", "company_name": "太康", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\02109300_太康\\01客戶基本資料", "files": [{"filename": "[太康] 連線資訊.txt", "raw_content": "VPN: FortiClient (需連外網)\r\n************* 14443\r\ndsc01 / Digiwin@01\r\n\r\n測試機AP+DB：***********  (00-50-56-83-A1-A1)\r\nhttp://bpmtest.t-conn.com:8086/NaNaWeb/\r\n遠端：administrator / TCTPKing@6893\r\nsa/Sql#dsc2020\r\nBPM測試機anydesk：*********\r\nBPM測試機密碼：BTK2wsx6688@tc\r\n\r\n正式機AP+DB：***********  (00-50-56-83-84-A5)\r\nhttps://bpm.t-conn.com/NaNaWeb/\r\nadministrator / BPMK1qaz3023tconn\r\n遠端：administrator / TCTPKing@6893\r\nsa/Sql#dsc2020\r\n\r\n\r\n轉檔主機：***********\r\n遠端：administrator / TCTPKing@6893\r\n\r\nhttp://gofile.me/6CDkr/fDvy7tCRF", "structured_data": {"測試機ap+db": "***********  (00-50-56-83-A1-A1)", "遠端": "administrator / TCTPKing@6893", "bpm測試機anydesk": "*********", "bpm測試機密碼": "BTK2wsx6688@tc", "正式機ap+db": "***********  (00-50-56-83-84-A5)", "轉檔主機": "***********", "vpn": "FortiClient (需連外網)", "http": "//gofile.me/6CDkr/fDvy7tCRF", "https": "//bpm.t-conn.com/NaNaWeb/", "host": "*************"}, "source_path": "C1.客戶維護相關\\02109300_太康\\01客戶基本資料\\[太康] 連線資訊.txt", "file_size": 541, "encoding_used": "Big5", "processed_at": "2025-08-26T10:46:22.065725"}], "total_files": 1, "processed_at": "2025-08-26T10:46:22.065751"}