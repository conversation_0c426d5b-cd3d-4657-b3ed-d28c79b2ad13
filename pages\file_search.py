import streamlit as st  # 匯入Streamlit庫，用於建立Web應用界面
import zipfile  # 匯入zipfile庫，用於處理ZIP壓縮檔
import rarfile  # 匯入rarfile庫，用於處理RAR壓縮檔
import os  # 匯入os庫，用於操作檔案系統
import json  # 匯入json庫，用於處理JSON格式資料
import tempfile  # 匯入tempfile庫，用於建立臨時檔案和目錄
from pathlib import Path  # 匯入Path類，用於處理檔案路徑
from collections import defaultdict  # 匯入defaultdict類，用於建立預設值的字典
from datetime import datetime  # 匯入datetime類，用於處理日期和時間
import st_file_uploader as stf  # 匯入自定義的檔案上傳元件
import sys

# 添加專案根目錄到Python路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設定Streamlit頁面配置
st.set_page_config(
    page_title="檔案索引路徑查詢工具",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"  # 工具頁面展開側邊欄方便導航
)

# 隱藏默認英文導航並添加中文側邊欄
st.markdown("""
<style>
    /* 隱藏Streamlit默認的英文頁面導航 */
    [data-testid="stSidebarNav"] {
        display: none !important;
    }

    .css-1544g2n, .css-17lntkn, .css-1y4p8pa, .css-1d391kg {
        display: none !important;
    }

    /* 側邊欄美化樣式 */
    .sidebar-nav-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        color: white;
        text-align: center;
        font-weight: bold;
    }

    .current-tool {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 0.8rem;
        margin: 1rem 0;
        border-left: 4px solid #2196f3;
    }

    .system-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.8rem;
        margin-top: 1rem;
        border-left: 4px solid #4facfe;
    }

    .system-info-item {
        font-size: 0.85rem;
        color: #666;
        margin: 0.3rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 側邊欄導航
with st.sidebar:
    st.markdown("""
    <div class="sidebar-nav-header">
        🧭 導航選單
    </div>
    """, unsafe_allow_html=True)

    # 返回主頁按鈕
    if st.button("🏠 返回主頁", use_container_width=True, help="返回工具選擇主頁"):
        st.switch_page("streamlit_home.py")

    st.markdown("---")

    # 當前工具提示
    st.markdown("""
    <div class="current-tool">
        <div style="font-weight: bold; color: #1976d2;">🔍 當前工具</div>
        <div style="font-size: 0.9rem; color: #666;">檔案索引路徑查詢</div>
    </div>
    """, unsafe_allow_html=True)

    # 工具切換
    st.markdown("### 🛠️ 切換工具")

    if st.button("📊 產品Release記錄查詢", use_container_width=True, help="切換到Release記錄查詢工具"):
        st.switch_page("pages/release_query.py")

    st.markdown("---")

    # 系統資訊
    st.markdown("""
    <div class="system-info">
        <div style="font-weight: bold; margin-bottom: 0.5rem;">ℹ️ 系統資訊</div>
        <div class="system-info-item">📦 版本：1.0.0</div>
        <div class="system-info-item">📅 更新：2025年7月</div>
        <div class="system-info-item">🏢 部門：BPM服務部</div>
        <div class="system-info-item">🔧 功能：檔案路徑索引查詢</div>
    </div>
    """, unsafe_allow_html=True)

st.title("🔍 檔案索引路徑查詢工具")  # 設定頁面主標題

# 定義資料目錄路徑
DATA_DIR = project_root / "data_output" / "bpm_path"  # 設定索引資料的存放目錄

# 取得所有版本清單
def get_versions():
    """從資料目錄中獲取所有版本索引檔案，並按照版本號降序排序

    Returns:
        list: 按照降序排序的版本號列表
    """
    if not DATA_DIR.exists():
        return []
    # 獲取所有 *_index.json 檔案，提取版本號
    index_files = list(DATA_DIR.glob("*_index.json"))
    versions = [f.stem.replace("_index", "") for f in index_files]
    return sorted(versions, reverse=True)

# 讀取特定版本的 index.json
@st.cache_data  # 使用Streamlit的快取裝飾器，避免重複載入相同的資料
def load_index(version):
    """載入指定版本的索引檔案

    Args:
        version: 版本號

    Returns:
        dict: 索引資料，格式為 {檔案名稱: [檔案資訊列表]}
    """
    index_file = DATA_DIR / f"{version}_index.json"
    with open(index_file, "r", encoding="utf-8") as f:  # 開啟索引檔案
        return json.load(f)  # 載入JSON資料並返回

# 處理壓縮檔案，回傳 class/jsp/js 檔名清單
def extract_relevant_files(uploaded_file):
    """從上傳的壓縮檔案中提取所有class、jsp和js檔案的基本名稱
    
    Args:
        uploaded_file: 上傳的壓縮檔案物件
        
    Returns:
        list: 不重複的檔案基本名稱列表（不含副檔名）
    """
    result = set()  # 使用集合避免重複檔名
    with tempfile.TemporaryDirectory() as tmpdir:  # 建立臨時目錄
        temp_path = Path(tmpdir) / uploaded_file.name  # 建立臨時檔案路徑
        temp_path.write_bytes(uploaded_file.read())  # 將上傳的檔案寫入臨時檔案

        # 處理ZIP檔案
        if uploaded_file.name.endswith(".zip"):
            with zipfile.ZipFile(temp_path, 'r') as zf:  # 開啟ZIP檔案
                for name in zf.namelist():  # 遍歷ZIP檔案中的所有檔案
                    if name.endswith('.class') or name.endswith('.jsp') or name.endswith('.js'):  # 只處理class、jsp和js檔案
                        # 獲取檔案基本名稱（不含路徑和副檔名）
                        base = os.path.basename(name).replace(".class", "").replace(".jsp", "").replace(".js", "")
                        result.add(base)  # 添加到結果集合

        # 處理RAR檔案
        elif uploaded_file.name.endswith(".rar"):
            with rarfile.RarFile(temp_path, 'r') as rf:  # 開啟RAR檔案
                for info in rf.infolist():  # 遍歷RAR檔案中的所有檔案
                    if info.filename.endswith('.class') or info.filename.endswith('.jsp') or info.filename.endswith('.js'):  # 只處理class、jsp和js檔案
                        # 獲取檔案基本名稱（不含路徑和副檔名）
                        base = os.path.basename(info.filename).replace(".class", "").replace(".jsp", "").replace(".js", "")
                        result.add(base)  # 添加到結果集合

    return list(result)  # 將集合轉換為列表並返回

# 查詢索引
def query_index(index, keyword, exact_match=False):
    """在索引中搜尋包含關鍵字的項目

    Args:
        index: 索引資料字典
        keyword: 搜尋關鍵字
        exact_match: 是否使用精確匹配，預設為False（模糊匹配）

    Returns:
        dict: 符合條件的索引項目，格式為 {檔案名稱: [檔案資訊列表]}
    """
    if exact_match:
        # 精確匹配：檔案名稱的最後一部分必須完全相同（不區分大小寫）
        # 例如：keyword="WebUtil" 可以匹配 "WEB-INF.classes.com.dsc.nana.user_interface.web.util.WebUtil"
        matches = {}
        for k, v in index.items():
            # 取得檔案名稱的最後一部分（去除路徑）
            last_part = k.split('.')[-1]
            if keyword.lower() == last_part.lower():
                matches[k] = v
    else:
        # 模糊匹配：檔案名稱包含關鍵字（不區分大小寫）
        matches = {k: v for k, v in index.items() if keyword.lower() in k.lower()}
    return matches

def analyze_index_statistics(index_data):
    """分析索引資料的統計資訊

    Args:
        index_data: 索引資料字典

    Returns:
        dict: 包含各種統計資訊的字典
    """
    stats = {
        'total_files': len(index_data),
        'file_types': defaultdict(int),
        'source_types': defaultdict(int),
        'war_files': set(),
        'ear_files': set(),
        'jar_files': set(),
        'total_instances': 0
    }

    for file_name, instances in index_data.items():
        stats['total_instances'] += len(instances)

        # 分析檔案類型
        if file_name.endswith('.class') or '.' not in file_name.split('/')[-1]:
            stats['file_types']['Class檔案'] += 1
        elif '.jsp' in file_name.lower():
            stats['file_types']['JSP檔案'] += 1
        elif '.js' in file_name.lower():
            stats['file_types']['JS檔案'] += 1
        else:
            stats['file_types']['其他檔案'] += 1

        # 分析來源類型和檔案
        for instance in instances:
            source_type = instance.get('source_type', 'unknown')
            stats['source_types'][source_type] += 1

            file_name_instance = instance.get('file', '')
            if file_name_instance.endswith('.war'):
                stats['war_files'].add(file_name_instance)
            elif file_name_instance.endswith('.ear'):
                stats['ear_files'].add(file_name_instance)
            elif file_name_instance.endswith('.jar'):
                stats['jar_files'].add(file_name_instance)

    return stats

def format_datetime(dt_str):
    """將ISO格式的日期時間字串轉換為更易讀的格式

    Args:
        dt_str: ISO格式的日期時間字串
        
    Returns:
        str: 格式化後的日期時間字串
    """
    try:
        dt = datetime.strptime(dt_str, "%Y-%m-%dT%H:%M:%S")  # 解析ISO格式的日期時間字串
        return dt.strftime(f"%Y/%m/%d %p %I:%M:%S")  # 轉換為更易讀的格式
    except Exception:  # 如果解析失敗，則返回原始字串
        return dt_str

# 主畫面UI元件設置
st.header("選擇版本與查詢方式")  # 添加次標題
versions = get_versions()  # 獲取所有版本

if not versions:
    st.error("❌ 未找到任何版本資料，請確認data_output/bpm_path目錄中有索引檔案")
    st.stop()

selected_version = st.selectbox("選擇版本", versions)  # 建立版本選擇下拉選單

try:
    index_data = load_index(selected_version)  # 載入選定版本的索引資料
    # 分析統計資訊
    stats = analyze_index_statistics(index_data)
except FileNotFoundError:
    st.error(f"❌ 找不到版本 {selected_version} 的索引檔案")
    st.stop()
except Exception as e:
    st.error(f"❌ 載入索引檔案時發生錯誤: {e}")
    st.stop()

# 建立查詢方式選擇控制項
query_mode = st.segmented_control("查詢方式", ["輸入檔案名稱", "上傳壓縮檔案"], default="輸入檔案名稱")  # 建立分段控制項，提供兩種查詢方式

# 處理「輸入檔案名稱」查詢模式
if query_mode == "輸入檔案名稱":
    # 建立文字輸入框
    keyword = st.text_input("請輸入要查詢的 class、jsp 或 js 名稱 (可模糊搜尋)", "")
    if keyword:  # 如果使用者輸入了關鍵字
        result = query_index(index_data, keyword)  # 執行查詢
        st.subheader(f"🔎 查詢結果 - 關鍵字：{keyword}")  # 顯示查詢結果標題
        if result:  # 如果有查詢結果
            # 遍歷並顯示每個查詢結果
            for key, items in result.items():
                st.markdown(f"### 📄 **`{key}`**")  # 顯示檔案名稱
                for item in items:  # 顯示每個檔案的詳細資訊
                    st.markdown(
                        f"&nbsp;&nbsp;📦 **war** ：`{item['file']}`<br/>"
                        f"&nbsp;&nbsp;&nbsp;&nbsp;└ 🗂️ `{item['path_in_archive']}`<br/>"
                        f"&nbsp;&nbsp;&nbsp;&nbsp;└ ⏰ 最後修改： `{format_datetime(item['modified_time'])}`",
                        unsafe_allow_html=True
                    )
        else:  # 如果沒有查詢結果
            st.info("找不到符合條件的資料。")  # 顯示提示訊息

# 處理「上傳壓縮檔案」查詢模式
elif query_mode == "上傳壓縮檔案":
    # 建立自定義檔案上傳元件
    custom = stf.create_custom_uploader(
        uploader_msg="可將文件拖放到此處",  # 上傳區域提示文字
        limit_msg="文件限制大小為 200MB",  # 檔案大小限制提示
        button_msg="選取檔案",  # 按鈕文字
        icon="MdCloudUpload"  # 上傳圖示
    )
    # 建立檔案上傳元件
    file_custom = custom.file_uploader(
        "請上傳 zip / rar 檔案：",  # 上傳元件標題
        type=["zip", "rar"],  # 允許的檔案類型
        accept_multiple_files=False,  # 不允許多檔案上傳
    )

    if file_custom:  # 如果使用者上傳了檔案
        st.info("⏳ 正在分析檔案...")  # 顯示處理中提示
        names = extract_relevant_files(file_custom)  # 提取檔案中的相關檔名
        st.success(f"✅ 找到 {len(names)} 個相關檔名。")  # 顯示提取結果
        
        # 查詢每個檔名在索引中的匹配項（使用精確匹配）
        result = {}
        matched_files = []
        unmatched_files = []

        for name in names:  # 遍歷所有提取的檔名
            match = query_index(index_data, name, exact_match=True)  # 執行精確匹配查詢
            if match:  # 如果有匹配結果
                result.update(match)  # 更新結果字典
                matched_files.append(name)
            else:
                unmatched_files.append(name)

        # 顯示匹配統計
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("📁 壓縮檔案中的檔案數", len(names))
        with col2:
            st.metric("✅ 成功匹配", len(matched_files))
        with col3:
            st.metric("❌ 未匹配", len(unmatched_files))

        # 如果有未匹配的檔案，顯示詳細資訊
        if unmatched_files:
            with st.expander(f"📋 未匹配的檔案清單 ({len(unmatched_files)} 個)", expanded=False):
                for file in unmatched_files:
                    st.text(f"• {file}")
                st.info("💡 這些檔案在當前版本的索引中找不到對應項目，可能是新檔案或檔案名稱不同。")

        # 顯示查詢結果
        st.subheader(f"🔎 查詢結果 - 上傳檔案內容對應版本 {selected_version}")  # 顯示查詢結果標題
        if result:  # 如果有查詢結果
            # 遍歷並顯示每個查詢結果
            for key, items in result.items():
                st.markdown(f"### 📄 **`{key}`**")  # 顯示檔案名稱
                for item in items:  # 顯示每個檔案的詳細資訊
                    st.markdown(
                        f"&nbsp;&nbsp;📦 **war** ：`{item['file']}`<br/>"
                        f"&nbsp;&nbsp;&nbsp;&nbsp;└ 🗂️ `{item['path_in_archive']}`<br/>"
                        f"&nbsp;&nbsp;&nbsp;&nbsp;└ ⏰ 最後修改： `{format_datetime(item['modified_time'])}`",
                        unsafe_allow_html=True
                    )
        else:  # 如果沒有查詢結果
            st.warning("⚠️ 無符合檔案與版本索引資料。")  # 顯示警告訊息

# 顯示側邊欄統計資訊
st.sidebar.header("📈 統計資訊")

# 版本資訊
st.sidebar.subheader("🏷️ 版本資訊")
st.sidebar.metric("當前版本", selected_version)
st.sidebar.metric("可用版本數", len(versions))

# 檔案統計
st.sidebar.subheader("📁 檔案統計")
st.sidebar.metric("索引檔案總數", stats['total_files'])
st.sidebar.metric("檔案實例總數", stats['total_instances'])

# 檔案類型分布
st.sidebar.subheader("📄 檔案類型分布")
for file_type, count in sorted(stats['file_types'].items(), key=lambda x: x[1], reverse=True):
    icon = "☕" if file_type == "Class檔案" else "🌐" if file_type == "JSP檔案" else "⚡" if file_type == "JS檔案" else "📄"
    st.sidebar.metric(f"{icon} {file_type}", count)

# 來源類型統計
st.sidebar.subheader("📦 來源類型統計")
for source_type, count in sorted(stats['source_types'].items(), key=lambda x: x[1], reverse=True):
    icon = "🗃️" if source_type == "war" else "📚" if source_type == "ear" else "📄"
    st.sidebar.metric(f"{icon} {source_type.upper()}檔案", count)

# 容器檔案統計
st.sidebar.subheader("🗂️ 容器檔案統計")
st.sidebar.metric("🗃️ WAR檔案數", len(stats['war_files']))
st.sidebar.metric("📚 EAR檔案數", len(stats['ear_files']))
st.sidebar.metric("☕ JAR檔案數", len(stats['jar_files']))

# 顯示主要容器檔案
if stats['war_files']:
    st.sidebar.subheader("🗃️ 主要WAR檔案")
    war_list = sorted(list(stats['war_files']))
    for war_file in war_list[:5]:  # 只顯示前5個
        st.sidebar.text(f"• {war_file}")
    if len(war_list) > 5:
        st.sidebar.text(f"... 還有 {len(war_list) - 5} 個檔案")

if stats['ear_files']:
    st.sidebar.subheader("📚 主要EAR檔案")
    ear_list = sorted(list(stats['ear_files']))
    for ear_file in ear_list[:3]:  # 只顯示前3個
        st.sidebar.text(f"• {ear_file}")
    if len(ear_list) > 3:
        st.sidebar.text(f"... 還有 {len(ear_list) - 3} 個檔案")
