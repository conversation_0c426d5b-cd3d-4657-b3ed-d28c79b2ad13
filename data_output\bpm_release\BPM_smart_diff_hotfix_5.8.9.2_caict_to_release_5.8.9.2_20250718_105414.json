{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "hotfix_5.8.9.2_caict", "date": "2023-09-08 16:33:19", "message": "[Web]Q00-20230908003 追蹤流程進入表單頁面時，增加「返回待辦」頁面的功能按鈕", "author": "wayne<PERSON>"}, "舊分支": {"branch_name": "release_5.8.9.2", "date": "2023-05-26 10:36:32", "message": "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為\"上傳附件時允許修改是否使用在線閱讀\"，就呈現在線閱讀功能[補修正]", "author": "林致帆"}, "比較時間": "2025-07-18 10:54:14", "新增commit數量": 13, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "399c2e1971a06d89851c6113d567c9b16e327eb0", "commit_訊息": "[Web]Q00-20230908003 追蹤流程進入表單頁面時，增加「返回待辦」頁面的功能按鈕", "提交日期": "2023-09-08 16:33:19", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f7ada1c139832fbea2aee7aed628a0c3328f341f", "commit_訊息": "[流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt=0 [補]", "提交日期": "2023-09-05 16:41:51", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c061169b15f0d09a7af3e401ae99dae5e369088c", "commit_訊息": "[流程引擎] Q00-20230904001 調整開啟草稿時，先前上傳附件需一併顯示 [補]", "提交日期": "2023-09-04 16:23:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b2fc136f73db51903504f2a0296c6996d36f6d95", "commit_訊息": "[流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt=0 [補]", "提交日期": "2023-09-04 16:04:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7fc62ab5815de8d394cfee8a6a29e963a49b01f7", "commit_訊息": "[WEB] Q00-20230904009 非发起人关卡，把「保存」按鈕取消 →  把 「儲存表單」的按鈕隱藏                       当发起人取回重办时，需要把【退回修改】的按钮隐藏 → 发起人关卡UserTask_3", "提交日期": "2023-09-04 14:56:33", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1032fcea1303aee159e22d4a662445cc04c58729", "commit_訊息": "[流程引擎] Q00-20230904002 取回重辦時判斷流程ID是XMSB就一併執行以下語法 update XMSB set lszt=NULL where processSerialNumber='流程序号' and lszt<>1", "提交日期": "2023-09-04 14:36:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eac9b58c1d420f36b3b9e949b5a531d2b4f7d899", "commit_訊息": "[流程引擎] Q00-20230904001 調整開啟草稿時，先前上傳附件需一併顯示", "提交日期": "2023-09-04 14:35:02", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "8d6fdcb0f22fdb14a5cd16f7367ae18149574758", "commit_訊息": "[Web]Q00-20230904008 流程發起、待辦等相關ICON需要能替換，且繼續派送按鈕需依流程、關卡顯示不同內容", "提交日期": "2023-09-04 14:32:27", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f6d511540035b7c9dcb991c68ebffbd07d7a2878", "commit_訊息": "[Web]Q00-20230904003 調整退回重瓣按鈕不需跳出退回重瓣視窗，改成直接退回", "提交日期": "2023-09-04 13:40:44", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ee2bc7ea2dbeb6d6fb94c02424697be8c99db218", "commit_訊息": "[Web]Q00-20230904004 新增追蹤流程清單+草稿的Portlet", "提交日期": "2023-09-04 13:37:30", "作者": "林致帆", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/PortletEntry.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "8394203e2fbd8b03f07a3975d0aeae6367b8acdb", "commit_訊息": "[web]Q00-20230901007 追蹤流程頁面預設顯示發起過的流程", "提交日期": "2023-09-04 11:07:05", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cff65c43833e767d62389829fcc25d5b359984a3", "commit_訊息": "[web]Q00-20230901006 側邊一級菜單只展示客戶需要的內容：管理員維特原本MENU，一般使用者 側邊一級菜單只顯示追蹤流程、流程草稿、取回重辦、待办事项及某些客製作業", "提交日期": "2023-09-04 11:01:15", "作者": "刘旭", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "479ba4f38b85deee12d5d71c4089c74855f154df", "commit_訊息": "[Web] Q00-20230901005 使用Porlet进入发起流程界面，发单成功后添加进入待办按钮和页面内容", "提交日期": "2023-09-04 10:26:14", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}]}