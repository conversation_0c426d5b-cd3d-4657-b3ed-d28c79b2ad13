{"company_id": "06100394", "company_name": "安碁", "data_source": "01客戶基本資料", "folder_path": "C1.客戶維護相關\\06100394-安碁\\01客戶基本資料", "files": [{"filename": "安碁_連線資訊.txt", "raw_content": "wֳsu\r\n\r\nιssuF\r\n\r\nw\r\nG192.168.5.40\r\nef.aker.com.tw:8086/NaNaWeb\r\nhttp://ef.aker.com.tw:8086/NaNaWeb\r\nbKGadministrator/0425335978#133\r\n\r\nվG192.168.5.44\r\n\r\nδվƮw192.168.5.40\r\n\r\nTIPTOPSOAP}\r\n: http://************/web/ws/r/aws_efsrv?WSDL\r\nհ: http://************/web/ws/r/aws_efsrv_toptest?WSDL\r\n\r\n", "structured_data": {"ef.aker.com.tw": "8086/NaNaWeb", "http": "//************/web/ws/r/aws_efsrv?WSDL", "հ": "http://************/web/ws/r/aws_efsrv_toptest?WSDL", "host": "************"}, "source_path": "C1.客戶維護相關\\06100394-安碁\\01客戶基本資料\\安碁_連線資訊.txt", "file_size": 363, "encoding_used": "utf-8-ignore", "processed_at": "2025-08-26T10:46:23.401137"}], "total_files": 1, "processed_at": "2025-08-26T10:46:23.401147"}