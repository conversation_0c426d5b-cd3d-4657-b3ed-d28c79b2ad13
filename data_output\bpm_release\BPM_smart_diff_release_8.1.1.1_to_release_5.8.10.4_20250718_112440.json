{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "release_8.1.1.1", "date": "2025-04-01 11:58:26", "message": "[SQL]8.1.1.1更新(-59_ininDB.patch)", "author": "davidhr-2997"}, "舊分支": {"branch_name": "release_5.8.10.4", "date": "2024-12-18 14:50:52", "message": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]", "author": "kmin"}, "比較時間": "2025-07-18 11:24:40", "新增commit數量": 172, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "36bfbfd6c336dc72ce03a4a848ed9d84035fc13d", "commit_訊息": "[SQL]8.1.1.1更新(-59_ininDB.patch)", "提交日期": "2025-04-01 11:58:26", "作者": "davidhr-2997", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82f7456d23789ba6ad5aa3d1b3e299c4ffa82da5", "commit_訊息": "[文件總結助手]調整word報表中Barcode元件轉json失敗導致找經驗無法上傳的問題[補]", "提交日期": "2025-03-28 10:21:30", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/impl/WordReportMappingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c0e68fff472b9cff840b6670dd57bba0236bd8d2", "commit_訊息": "[文件總結助手]調整word報表中Barcode元件轉json失敗導致找經驗無法上傳的問題", "提交日期": "2025-03-28 10:16:12", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "adc23c63ec94fa58cb8b8a9a6b8a46846a70eea4", "commit_訊息": "[Web]C01-20250303005 修正表單在预览时，更換Title元件中image后，显示图片不正确的问题[补]", "提交日期": "2025-03-27 17:40:20", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b679454ce5ba754e771a608cc0bb7aa4eb1e51e1", "commit_訊息": "[Web]S00-20250226002 数智员工模组增加介绍页面[补]", "提交日期": "2025-03-27 13:40:04", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "34dee7dcb6efc60281d324e7b3baf2486f2fcd53", "commit_訊息": "[Web]S00-20250224007 控制登入頁外顯AI產品名稱圖示及登入頁圖片(補)", "提交日期": "2025-03-26 13:46:33", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "43921a2d3e9ac4304da27d2774dbc10fdae474d3", "commit_訊息": "[文件智能家]更新ChatFile授權令牌", "提交日期": "2025-03-26 13:43:58", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b39e26a61e1a39faceae1a5b837b50e96959506c", "commit_訊息": "[文件智能家]优化找经验上传逻辑，优化执行记录", "提交日期": "2025-03-25 19:07:27", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/impl/WordReportMappingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "42025505a70b1fe70a36d4c431c41ae00fb9ee26", "commit_訊息": "[文件總結助手]修正關聯作業進入時會出現查詢失敗", "提交日期": "2025-03-25 11:16:15", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "35da02e904a464a828239f7552f321db13dc2724", "commit_訊息": "[Web]S00-20250224007 控制登入頁外顯AI產品名稱圖示及登入頁圖片(補)", "提交日期": "2025-03-25 09:45:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1dc03baf5faee9a6be20bc493d396c6c85ec9dc4", "commit_訊息": "[Web]S00-20250224007 控制登入頁外顯AI產品名稱圖示及登入頁圖片", "提交日期": "2025-03-25 09:06:28", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/Constants.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/aibpmlogo.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/aibpmlogo_cn.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/AiBPM_banner_default.jpg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/AiBPM_banner_default_zh_CN.jpg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/AiBPM_default.jpg", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/user_saved.jpg", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/user_uploaded.jpg", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 13}, {"commit_hash": "94d261b8b42127a43b96e112863aba3c1cdd983c", "commit_訊息": "[文件總結助手] 修正找经验无法上传文档的问题", "提交日期": "2025-03-25 09:04:10", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/CommonProgramModule/Report/dao/impl/WordReportMappingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1a48caa5476f43850abffaf506cd153fccdc585e", "commit_訊息": "[Web]C01-20250318007 修正監控流程點選此分類全部後轉成Excel報表異常問題", "提交日期": "2025-03-20 16:27:02", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eb9868e237b369fdab324a15a674345f08a1c0d0", "commit_訊息": "[內部]Q00-20250319001 將系統設定Athena整合待辦開關調整預設為false，避免未整合Athena時發起或簽核流程後會出現一堆報錯訊息", "提交日期": "2025-03-19 16:51:31", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b4674b656b696125e036963b456844b61309d048", "commit_訊息": "[SYSDT]S00-20250318001 Web系統管理系統郵件的帳號與密碼改為必填機制[補]", "提交日期": "2025-03-19 16:13:43", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "221d04660360c44694a5af0e8d3d829b93aa4821", "commit_訊息": "[SYSDT]S00-20250318001 Web系統管理系統郵件的帳號與密碼改為必填機制[補]", "提交日期": "2025-03-19 11:55:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "552219a4fdea0cb7f848ce0743de3b103274a16d", "commit_訊息": "[Web]C01-20250318002 修正RadioButton_txt/CheckBox_txt轉存表單未將輸入值存入資料庫問題", "提交日期": "2025-03-19 10:18:55", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7af6b90072ec8128c75ad03a0ffa6e4373b08eeb", "commit_訊息": "[內部]BPMVersionConverter改為BPMVersionHelper同時擴充共用方法(補2)", "提交日期": "2025-03-19 10:14:30", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/BPMVersionHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5d9d4a2e9d17826c70cc6d8fc6adb233d75eaba2", "commit_訊息": "[Web]C01-20250318001 修正發單簽核打開關閉行動端模擬，再點擊存儲表單或草稿，表單畫面異常的問題", "提交日期": "2025-03-19 08:58:03", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "db0d1effa1983eca730d76a06b2e0d34dcde43f7", "commit_訊息": "[內部]BPMVersionConverter改為BPMVersionHelper同時擴充共用方法(補)", "提交日期": "2025-03-18 16:21:40", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/BPMVersionHelper.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5ff1c3a01a238d4451930afc082ce7760aedabe1", "commit_訊息": "[內部]BPMVersionConverter改為BPMVersionHelper同時擴充共用方法", "提交日期": "2025-03-18 10:27:40", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/BPMVersionConverter.java", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/BPMVersionHelper.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "06c31d04616b1f2adad9978991244aca53f8cf44", "commit_訊息": "[內部]BPM外顯版本邏輯移至BPMVersionConverter，讓版更工具可以共用", "提交日期": "2025-03-17 11:27:23", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/BPMVersionConverter.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "44825e7725a7a8d9d580cecd51558482edb555b7", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-03-14 14:55:47", "作者": "周权", "檔案變更": [{"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/COSMOS/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0d78f2b9c5f29ce86c5f0ae3a0d4410a06dcb2f4", "commit_訊息": "[內部]S00-20250224003 顯示版號依據是否為AIGP變動", "提交日期": "2025-03-14 14:26:29", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "b772373ef85d858d3d898cc0afa901a77c6590a7", "commit_訊息": "[Web]S00-20250226002 数智员工模组增加介绍页面[补]", "提交日期": "2025-03-14 13:13:18", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fd1d5a125a5359fcb487de79e0377fd313031749", "commit_訊息": "[內部]SystemVariable快取增加bpm.aigp(DB、UI不可編輯)、bpm.version(不在DB、不可編輯)(補)", "提交日期": "2025-03-14 11:07:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/ServerInitialization.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/BpmInfoSingleton.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "bac45fac56a698de8d3da9c6fa3c011442e28b8b", "commit_訊息": "[內部]SystemVariable快取增加bpm.aigp(DB、UI不可編輯)、bpm.version(不在DB、不可編輯)", "提交日期": "2025-03-14 09:28:40", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/LoadSystemVariable.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "73df602d891b2532f17be03d046c1726fab1916f", "commit_訊息": "[Web]S00-20250226004 chatfile圖示更換成貓頭鷹 , chatfile視窗左上角名稱改為數智員工[补]", "提交日期": "2025-03-13 11:47:42", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a294619e902ae58ccfa6128cb2f9ac6b1d062c75", "commit_訊息": "[Web]S00-20250226002 数智员工模组增加介绍页面[补]", "提交日期": "2025-03-13 11:46:31", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "76635a95f2c6e008fa7f8a7216929a03aad14cb3", "commit_訊息": "[帳號安全管理]C01-20250310002 修正使用UserId+LDAP密碼會直接登入不會出現雙因素驗證的畫面", "提交日期": "2025-03-05 13:40:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d78fa0dbf6c642d102c1fba4b503be4ea57140d", "commit_訊息": "[Web]S00-20250226002 数智员工模组增加介绍页面[补]", "提交日期": "2025-03-12 11:43:11", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/SmartEmployeesDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c1f2607ddecc3e4fa6a4b621fa147f750630327", "commit_訊息": "[Web]S00-20250226002 数智员工模组增加介绍页面[补]", "提交日期": "2025-03-11 17:45:51", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5f019e49c17fbb2914933aabe0f62ae4268de0dc", "commit_訊息": "[Web]S00-20250224004 使用者登出登入記錄增加使用者登入失敗操作行為的記錄[补]", "提交日期": "2025-03-11 16:49:23", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "25f71d73c4b51dd442491e97ff7060c9e00c1c64", "commit_訊息": "[Web]S00-20250226004 chatfile圖示更換成貓頭鷹 , chatfile視窗左上角名稱改為數智員工[补]", "提交日期": "2025-03-11 15:33:56", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c648ac7fb31797d3abf3a770e71c0a5f4e20a34f", "commit_訊息": "[MPT]S00-20250224006首頁模組-圖表展示新增查詢SQL(登入失敗查詢、未啟用雙因素認證數量、密碼超時未修改)", "提交日期": "2025-03-11 10:58:49", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "10277ee7319944ca1da8724cd625b367ca313e8b", "commit_訊息": "[Web]S00-20250226002 数智员工模组增加介绍页面[补]", "提交日期": "2025-03-11 09:01:43", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "523d204d6d6ceb5b7dadd51753f0b80bb1c44111", "commit_訊息": "[Web]S00-20250226002 数智员工模组增加介绍页面[补]", "提交日期": "2025-03-10 13:28:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/8.1.1.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0fe823a91194743941091ad4b8557e0a465354e3", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2025-03-10 11:27:23", "作者": "kmin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "e2dd2a4595f278919ad98c21ff5c44169d3ad4ae", "commit_訊息": "[流程引擎]A00-20250307001 修正等待3秒invoke卡住在重組流程主旨邏輯", "提交日期": "2025-03-10 11:22:49", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7dccbd093037eb3b1e93e8fa3569fd7a6d477272", "commit_訊息": "[Web]S00-20250226002 数智员工模组增加介绍页面", "提交日期": "2025-03-10 11:18:15", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/SmartEmployeesDao.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/SmartEmployeesDaoImpl.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/domain/SmartEmployees.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/8.1.1.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "8f08774999f2c13d753806449e6c628a3eaae3f8", "commit_訊息": "[Web]C01-20250307002 修正佈景主題title文字顔色調整后不生效的問題", "提交日期": "2025-03-10 10:28:17", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b671f63d3fe420813dfef76438fe8b3cbfc7bab1", "commit_訊息": "[帳號安全管理]C01-20250304009 修正O<PERSON>h登入後顯示的語系是環境語系而不是登入頁選取的語系", "提交日期": "2025-03-05 13:40:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "31cb49d4d7c607c72f3c34415ae2d4b12cffd85a", "commit_訊息": "[BPM APP]C01-20250306002 修正額外輸入框前一個選項顯示值會變成實際值的問題", "提交日期": "2025-03-07 14:43:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainer.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7453ab813815f71e6239cba141f4a0b572a72dcf", "commit_訊息": "[帳號安全管理]C01-20250304008 修正驗證碼通知樣版使用的語系是使用者環境語系而不是登入畫面選擇的", "提交日期": "2025-03-06 16:44:33", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e8c3fb5959e8abb38f6e1e60f7760fb4ae019d31", "commit_訊息": "[帳號安全管理]C01-20250304008 修正缺少使用者語系的通知樣板導致雙因素驗證出現null提示的異常", "提交日期": "2025-03-06 16:44:23", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b96c69917df8fa1289fcfd324024c1e817b7279d", "commit_訊息": "[Web]C01-20250224002 修正簽核歷程多人關卡尚未有人接收時若關卡名稱要呈現「部門-職務」時無法跟人員一一對應", "提交日期": "2025-03-06 15:47:45", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "74ee75529ffbb287c48d7af2fe5c6d715a9e71c3", "commit_訊息": "[內部]Q00-20250306001 優化tActivityInstanceContainer is unknown type訊息印出更多有用的資訊。", "提交日期": "2025-03-06 15:00:41", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ActivityInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c3805968f26bef202857f779e07c3d839f142cc", "commit_訊息": "[PLM]C01-20250218003 修正hdnFilePath欄位值全形空白或2個空白拋單發起後變1個空白格問題", "提交日期": "2025-03-06 14:04:41", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "233631c0e318e1963a8e305014360c6b136a6450", "commit_訊息": "[內部]因應AiGP版號調整5.8.11.1改為8.1.1.1", "提交日期": "2025-03-05 16:51:12", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.11.1_DDL_DM8.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_MSSQL.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_Oracle.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_DM8.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_MSSQL.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_ORACLE.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_DM8.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_MSSQL.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_ORACLE.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 12}, {"commit_hash": "bd24010f3b1fe9563f6aaa0260de48a6361de802", "commit_訊息": "[數智員工]S00-20250224002 模組名稱:文件總結助手改為數智員工，相關多語系修改(補)", "提交日期": "2025-03-05 16:15:38", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "51c0a2fcc4ea0bab3aea3904a9f45d5bf9f30f69", "commit_訊息": "[數智員工]S00-20250224002 模組名稱:文件總結助手改為數智員工，相關多語系修改", "提交日期": "2025-03-05 16:05:03", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "99024f062bccbf2e675ccba13813e807f4369bb4", "commit_訊息": "[Web]Q00-20250305001 修正系統設定\"nana.performworkItem.html.character.filter\"=true,追蹤、待辦等主旨顯示會異常(補)", "提交日期": "2025-03-05 13:58:54", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "d653d9259325f3d0ea4c253ee694ee78b89fbf10", "commit_訊息": "[Web]Q00-20250305001 修正系統設定\"nana.performworkItem.html.character.filter\"=true,追蹤、待辦等主旨顯示會異常", "提交日期": "2025-03-05 13:40:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "d1b2511a593c71098792688e341e01e547e12ee0", "commit_訊息": "[Web]C01-20250303004 修正ListBox多選發起流程后，選項消失的問題(補)", "提交日期": "2025-03-05 10:33:55", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "08671daeced10267af005ce610876d80cf62708e", "commit_訊息": "[Web]C01-20250303003 修正CheckBox有勾選「最後一個選項額外產生輸入框」用FormUtil.getValue取不到值問題", "提交日期": "2025-03-05 08:42:40", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc05902feb479dda59e066a392bd4d734d72590f", "commit_訊息": "[Web]C01-20250303004 修正ListBox多選發起流程后，選項消失的問題", "提交日期": "2025-03-04 17:49:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ca7d3d183a0bd9fd55f7c06b4d0a2c7030dc6386", "commit_訊息": "[Web]C01-20250303005 修正表單在预览时，更換Title元件中image后，显示图片不正确的问题", "提交日期": "2025-03-04 16:34:30", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/TitleElement.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "f7ded032ae1ba640e3e6694ae7411412cf0c540f", "commit_訊息": "[Web]S00-20250226004 chatfile圖示更換成貓頭鷹 , chatfile視窗左上角名稱改為數智員工", "提交日期": "2025-03-04 11:34:57", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/css/bpm-style.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/AiBPM.png", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 7}, {"commit_hash": "436b2980d6aa2d2ac4a3a6f1b3136cdad3d70796", "commit_訊息": "[Web]S00-20250224005 模組名稱:雙因素認證模組為帳號安全管理模組[补]", "提交日期": "2025-02-28 13:50:18", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "e0fb147183c7af23a80e3257df2affcf2c70237b", "commit_訊息": "[资安]C01-20241218008 流程设计师新增失效选项(页面效果唯读，使用脚本样板或者F12修改值，不会改变数据库存储值)[补]", "提交日期": "2025-02-27 15:21:04", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ca144e30e9fc87e576855eca5c9a9f3cac2a3cd6", "commit_訊息": "[Web]S00-20250224004 使用者登出登入記錄增加使用者登入失敗操作行為的記錄[补]", "提交日期": "2025-02-27 15:17:39", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5221a9b3dc7f20b0ccde04a08e4c3b7118a2eb82", "commit_訊息": "[Web]C01-20250226001 修正部門主管登入BPM首頁會有錯誤彈窗的問題", "提交日期": "2025-02-27 11:14:51", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "50fda0b50ca39ab1fa50180fe33d46241176809a", "commit_訊息": "5.8.11.1更新", "提交日期": "2025-02-26 17:08:47", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f3b2994e09546df9196290de453b652d4df94325", "commit_訊息": "[Web]S00-20250224004 使用者登出登入記錄增加使用者登入失敗操作行為的記錄", "提交日期": "2025-02-26 10:21:10", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/license/UserLogInOutRecord.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "3b50d86af464544956a841aca1c36cc7e2a217ac", "commit_訊息": "[Web]S00-20250224005 模組名稱:雙因素認證模組為帳號安全管理模組", "提交日期": "2025-02-26 10:17:21", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/-59_InitDB.patch", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "81b11a39089637e7268034074955d139b664ecca", "commit_訊息": "[T100]C01-20250225001 修正Link分类元件造成拋單失敗，新增判斷略過Link元件(補)", "提交日期": "2025-02-26 09:35:33", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8c7eb43b37bbbdf9c1b92f5826ea9da62e59702d", "commit_訊息": "[T100]C01-20250225001 修正Link分类元件造成拋單失敗，新增判斷略過Link元件", "提交日期": "2025-02-26 09:09:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "525ed6869037ff00a1085814d19faebe183ea7fe", "commit_訊息": "Revert \"[E10]C01-20241226001 E10抽单调用撤銷流程接口，若bpm是在服务任务关卡，就先不允许撤銷\"", "提交日期": "2025-02-25 15:46:37", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d509a91bab19f085e252bc1a2089a0621b5b3699", "commit_訊息": "[BPM APP]C01-20250220003 修正從Line推播訊息上點擊查看詳情後因userId為null而出現錯誤畫面問題", "提交日期": "2025-02-24 15:27:57", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bdce8f7197ce2d91b521568076beea6d3329a88a", "commit_訊息": "[文件總結助手]Q00-20250224001 修正找經驗關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)", "提交日期": "2025-02-24 14:53:50", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "623e056b4e5a15d5c112457c40a5d5f6c7233113", "commit_訊息": "[Web]C01-20250221001 修正詳細流程圖核決關卡跳關異常問題", "提交日期": "2025-02-24 10:31:32", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3e71b9ad6b0157d4b40a392db3246951d1127941", "commit_訊息": "[Web]C01-20250221002 修正监控流程/追踪流程，按照建立时间排序第一次点击不会排序的问题", "提交日期": "2025-02-24 11:23:00", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3a6e27e857d6fcc39f4c6df73164cfd194150223", "commit_訊息": "[BPM APP]C01-20250212004 修正行動端開窗元件有設定組織名稱過濾條件時無法顯示非主部門的其餘部門資料[補]", "提交日期": "2025-02-21 10:56:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b4e74ee987fa40f53ae1cc617bd6efda5ce8e385", "commit_訊息": "[文件總結助手]Q00-20250220001 修正助閱讀關聯作業出現重覆流程的異常(因為加簽、核決等產生CustomProcessPackage造成)", "提交日期": "2025-02-17 16:14:46", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6fe8e0b8292e8df73e4ed2ae043ceefc37646364", "commit_訊息": "[Web]V00-20250219001 修正流程資料查詢在選擇表單時無法點選下一頁", "提交日期": "2025-02-20 15:16:02", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8bf7c2e3c0ad8b2be8bc6d04374ea6f5b4c1ad78", "commit_訊息": "[资安]C01-20241218008 流程设计师新增失效选项(页面效果唯读，使用脚本样板或者F12修改值，不会改变数据库存储值)", "提交日期": "2025-02-19 14:34:00", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessCellEditorRenderer.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessTableHeader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormValidateTableHeader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormValidateTableHeader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileToolDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_definition/enumTypes/FormFieldAccessType.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/Constants.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/BarcodeElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/ComplexElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/FormElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HandWritingElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HorizontalLineElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/LinkElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/TitleElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 23}, {"commit_hash": "aefb7d0e419115a04cc1bd5b44c5cffa50d97e82", "commit_訊息": "[MPT]A00-20250212001 修正公告申請單未結案前即重啟BPM服務導致圖片遺失問題", "提交日期": "2025-02-19 13:58:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/copyfiles/@mpt/default-form/MptAncApply.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6fff3636ed02dbe7f921fb658202ebaaa5c32dcc", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-18 15:15:52", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1d9ad5619cb2cc8ed4e99f15861b3b2fc5d54e90", "commit_訊息": "[Web] V00-*********** 資料選取註冊器選擇分頁筆數沒效果", "提交日期": "2025-02-17 17:46:15", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "b58f422c874ae600631283cd0c988245599ddf94", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-17 16:37:40", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "aa2db3c09213d4b683bd509493fbb6ca43444576", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-17 16:26:11", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0883bf9bb6cf956512ef3b2c861c2727e901e378", "commit_訊息": "[BPM APP]C01-20250212004 修正行動端開窗元件有設定組織名稱過濾條件時無法顯示非主部門的其餘部門資料", "提交日期": "2025-02-17 14:59:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/UserCacheSingletonMap.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e9eb4b7d2f4f689438fc5397e76e642ccd04836a", "commit_訊息": "[Web] V00-20250211004 資料選取註冊器新增時,代號只能輸入英文和數字判斷錯誤", "提交日期": "2025-02-17 13:54:13", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cab07b1082badbc6e525713fa5f4641b673ba4c9", "commit_訊息": "[Web] V00-20250212001 資料選取註冊器新增時，「名稱(預設值)」忘記填值時不會彈「XXX不可為空」", "提交日期": "2025-02-17 13:52:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5609ac89df0f89f02d6017f3ff9f0c81820d6e4a", "commit_訊息": "[Web] C01-20250213007 修正grid設置背景色列印無顔色", "提交日期": "2025-02-17 11:42:36", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82c644265219416103058b2bfd090a53af58408b", "commit_訊息": "[Web]C01-20250214002 修正雙因素登入成功時無法update重置登入失敗次數", "提交日期": "2025-02-17 10:42:51", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0f79433565704b42f6f9aa87d86ca308776737e7", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-14 15:10:50", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_ORACLE.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "82538ced270edb89195167917a0c4f1f7bfe4197", "commit_訊息": "[Web] C01-20250206008 調整助閲讀、找經驗分頁查詢", "提交日期": "2025-02-14 10:26:37", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileAssistedReadingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/ChatFileModule/dao/impl/ChatFileExperienceDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmCompanyMappingDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/digiwin/bpm/TrmModule/dao/impl/TrmInitiateProcessProfileDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "15ad170da6ef3ed697c41537dbf93b44468ef00d", "commit_訊息": "[Web] V00-20250211002 调整個資保護宣告簡體語系", "提交日期": "2025-02-14 09:57:42", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "06f3b35bc1e03eefa833b9d7b455c92b8fa461bb", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-13 11:08:54", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_ORACLE.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "8c284591f66adae059f7890c2743b2987015789e", "commit_訊息": "[Oauth]V00-20250212003 Oauth绑定頁面中公司商標修改", "提交日期": "2025-02-12 15:13:01", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/SSOCallBack.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "07227e83d0c56475508c744c6fe6c33bc9429c3d", "commit_訊息": "[BPM APP]C01-20250210001 修正行動端在複合式元件設置提示文字時不會顯示問題", "提交日期": "2025-02-12 14:13:39", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "891cfc73f58a1fef7dca74e01323c298a43b0057", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-12 09:30:53", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df004ec49f9011847b3245bd61f3a163b087ea94", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-11 17:20:33", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_ORACLE.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "31c341d272597c5e073fa0eadd7c54b3803cdfc0", "commit_訊息": "[資安] V00-20250211001 SonarQube安全性議題修正：Encryption algorithms should be used with secure mode and padding scheme", "提交日期": "2025-02-11 09:32:59", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/AthenaAESUtils.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cce54cca7d8a8a1dbd89ee6163b8f170b761e18b", "commit_訊息": "[Web]S00-20241230001 BPM登入頁相關公司商標修改(補)", "提交日期": "2025-02-10 17:11:28", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d071635eaf7c5f77aaa46bdf72c411e1e3a9dc4b", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-10 16:17:24", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_ORACLE.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "de7b6e82276b4b148774c1651a65852495ff0e71", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-08 17:41:59", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_ORACLE.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "f3b3ef236348db18d0af7c3e02a1de5e9fda6ac5", "commit_訊息": "Q00-20250208002 jquery-ui-1.11.4.js 另存檔案為jquery-ui-a.k.d.js,更新二支有引用此js的jsp檔(FormAppRWDDiagram.jsp、MobileRelativeDesigner.jsp)", "提交日期": "2025-02-08 16:08:36", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-ui-a.k.d.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "746c115e44abea238f70915496034c5d6041b454", "commit_訊息": "Revert \"Q00-20250208002 jquery-ui-1.11.4.js 另存檔案為jquery-ui-a.k.d.js ,更新二支有引用此js的jsp檔(FormAppRWDDiagram.jsp、MobileRelativeDesigner.jsp)\"", "提交日期": "2025-02-08 16:06:48", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-ui-a.k.d.js", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 3}, {"commit_hash": "a9f840b36986030ca6f7a6a9276ff20d0ee9da0b", "commit_訊息": "Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58", "提交日期": "2025-02-08 15:21:22", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0bd8b66de1f4299a1757d69343498966908bf644", "commit_訊息": "Q00-20250208002 jquery-ui-1.11.4.js 另存檔案為jquery-ui-a.k.d.js ,更新二支有引用此js的jsp檔(FormAppRWDDiagram.jsp、MobileRelativeDesigner.jsp)", "提交日期": "2025-02-08 15:20:46", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/jquery-ui-a.k.d.js", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "e74a428d06621932878575549320e3ca8aec34c1", "commit_訊息": "[费用报销] 费用报销模组回收[补]", "提交日期": "2025-02-07 10:42:47", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "f83294825313b6a137f40560d54a55da811dd957", "commit_訊息": "[费用报销] 费用报销模组回收", "提交日期": "2025-02-07 10:39:32", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "Release/copyfiles/@expense/form-default/ExpFAA.form", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/form-default/ExpFAB.form", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/form-default/ExpFAC.form", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/form-default/ExpFAD.form", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/COSMOS/EPM_ExpenseForm_CM.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/COSMOS/EPM_PrepaidVendorForm_CM.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/COSMOS/EPM_VendorRequestForm_CM.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/EPM_BudgetCreateForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/EPM_BudgetDivertForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/EPM_BudgetReviseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/EPM_ExpenseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/EPM_PrepaidExpenseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/EPM_PrepaidVendorForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/EPM_VendorRequestForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/EPM_ExpenseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/EPM_PrepaidExpenseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/EPM_PrepaidVendorForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/EPM_VendorRequestForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/EPM_BudgetCreateForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/EPM_BudgetDivertForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/EPM_BudgetReviseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/EPM_ExpenseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/EPM_PrepaidExpenseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/EPM_PrepaidVendorForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/EPM_VendorRequestForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/EPM_ExpenseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/EPM_PrepaidExpenseForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/EPM_PrepaidVendorForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/form-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/EPM_VendorRequestForm.form\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/COSMOS/EPM_ExpenseForm_CM.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/COSMOS/EPM_ExpenseForm_CM.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/COSMOS/EPM_PrepaidVendorForm_CM.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/COSMOS/EPM_PrepaidVendorForm_CM.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/COSMOS/EPM_VendorRequestForm_CM.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/COSMOS/EPM_VendorRequestForm_CM.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/EPM_ExpenseForm.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/EPM_ExpenseForm.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/EPM_PrepaidExpenseForm.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/EPM_PrepaidExpenseForm.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/EPM_PrepaidVendorForm.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/EPM_PrepaidVendorForm.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/EPM_VendorRequestForm.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/EPM_VendorRequestForm.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/Workflow/EPM_ExpenseForm.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/Workflow/EPM_ExpenseForm.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/Workflow/EPM_PrepaidExpenseForm.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/Workflow/EPM_PrepaidExpenseForm.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/Workflow/EPM_PrepaidVendorForm.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/Workflow/EPM_PrepaidVendorForm.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/Workflow/EPM_VendorRequestForm.jasper", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/copyfiles/@expense/iReportDoc/Workflow/EPM_VendorRequestForm.jrxml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/COSMOS/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/COSMOS/\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/COSMOS/\\345\\273\\240\\345\\225\\206\\351\\240\\220\\344\\273\\230\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/\\345\\223\\241\\345\\267\\245\\351\\240\\220\\346\\224\\257\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/T100/\\345\\273\\240\\345\\225\\206\\351\\240\\220\\344\\273\\230\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/\\345\\223\\241\\345\\267\\245\\351\\240\\220\\346\\224\\257\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/\\345\\273\\240\\345\\225\\206\\351\\240\\220\\344\\273\\230\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/\\351\\240\\220\\347\\256\\227\\345\\273\\272\\347\\253\\213\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/\\351\\240\\220\\347\\256\\227\\346\\214\\252\\347\\224\\250\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/TIPTOP/\\351\\240\\220\\347\\256\\227\\350\\252\\277\\346\\225\\264\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/\\345\\223\\241\\345\\267\\245\\351\\240\\220\\346\\224\\257\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/WORKFLOW/\\345\\273\\240\\345\\225\\206\\351\\240\\220\\344\\273\\230\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/\\344\\270\\200\\350\\210\\254\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/\\345\\223\\241\\345\\267\\245\\351\\240\\220\\346\\224\\257\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/\\345\\273\\240\\345\\225\\206\\351\\240\\220\\344\\273\\230\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/\\351\\240\\220\\347\\256\\227\\345\\273\\272\\347\\253\\213\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/\\351\\240\\220\\347\\256\\227\\346\\214\\252\\347\\224\\250\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\344\\272\\214\\346\\234\\237\\351\\226\\213\\347\\231\\274/\\351\\240\\220\\347\\256\\227\\350\\252\\277\\346\\225\\264\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\345\\223\\241\\345\\267\\245\\350\\262\\273\\347\\224\\250\\345\\240\\261\\351\\212\\267\\346\\265\\201\\347\\250\\213.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\345\\223\\241\\345\\267\\245\\351\\240\\220\\346\\224\\257\\347\\224\\263\\350\\253\\213.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\345\\226\\256.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\345\\273\\240\\345\\225\\206\\350\\253\\213\\346\\254\\276\\346\\265\\201\\347\\250\\213.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "\"Release/copyfiles/@expense/process-default/\\345\\273\\240\\345\\225\\206\\351\\240\\220\\344\\273\\230\\346\\265\\201\\347\\250\\213.bpmn\"", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DDL_ORACLE.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/Expense_5.8.11.1_DML_ORACLE.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/epm/ERP.hibernate.cfg.xml", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/wildfly/modules/NaNa/conf/epm/Expense.properties", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 90}, {"commit_hash": "14add4b95f87dbd3f75fa1a99ec77db836189c22", "commit_訊息": "[Athena]C01-*********** 更新智能工作台集成，调整单点登录跟待办推送逻辑，新增消息推送跟作业集成[補]", "提交日期": "2025-02-06 16:15:07", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/wildfly/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2e2d921ec3f09d76adf72d946bc58b80674473db", "commit_訊息": "[Athena]C01-*********** 更新智能工作台集成，调整单点登录跟待办推送逻辑，新增消息推送跟作业集成", "提交日期": "2024-04-12 10:39:41", "作者": "<PERSON><PERSON><PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/lib/JakartaCommons/commons-codec.jar", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AthenaEventBean.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/AthenaAESUtils.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/AthenaHttpUtil.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Cross.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/AthenaSSO.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/standalone/configuration/standalone-full.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 25}, {"commit_hash": "ee1c003cc222a794bf21dda01bab970d4091c1b9", "commit_訊息": "[<PERSON><PERSON><PERSON>]C01-20250123009 修正綁定平台頁面(SSOCallBack.jsp)無多語系", "提交日期": "2025-02-05 11:27:19", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/SSOCallBack.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "43ef21c3c88f0f15d13f40448d9a238dcad0cb6d", "commit_訊息": "[Oauth]C01-20250123010 修正解除綁定沒有選擇任何項目會出現異常：Argument 'pOID' cannot be null or empty string.，改成提示：請選擇要解除綁定的項目", "提交日期": "2025-02-04 14:30:01", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fe208df2c0c62ca8f88010adb4364933858841e1", "commit_訊息": "[Web]S00-20240814001 優化流程撤銷狀態簽核歷程記錄增加撤銷人員資訊", "提交日期": "2025-01-23 11:47:23", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29bde5e3c6ab18de2ae7a373a7bb433d7075b62f", "commit_訊息": "[BPM APP]C01-20250123007 修正行動端在取得常用流程模型定義清單時會發生連線異常問題", "提交日期": "2025-01-23 15:43:59", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fdb2ec087ac740264c6f0ff1ebb673f6b6bea320", "commit_訊息": "[Web]S00-20241230001 BPM登入頁相關公司商標修改(補)", "提交日期": "2025-01-21 14:49:25", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageSystemConfig-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "c6ab1f0977bf321148c60b4520f7b58c59ee2de9", "commit_訊息": "[Web]C01-20250116012 修正通知信連結點第二次不同連結時會出現異常訊息", "提交日期": "2025-01-20 16:38:50", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "179fb28d9d59a04f463ec2edb7ec5cecfa9e0f5b", "commit_訊息": "[Web]Q00-20250120001 調整單身加總欄位設定整數，依然顯示小數並計算異常的問題", "提交日期": "2025-01-20 10:20:06", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "600c815032cbe94fd38d77b7c75a92af0920fc00", "commit_訊息": "[BPM APP]C01-20250113001 修正整合釘釘時使用iOS手機查看PDF檔案因字型問題導致內容丟失問題", "提交日期": "2025-01-17 17:33:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/pdfJs/pdf-bpm.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0935c85a495fa78e1ad2020f75530b4eedb02f7e", "commit_訊息": "[Web]C01-20250116003 修正SubjectTitle导致主旨显示異常", "提交日期": "2025-01-17 14:25:09", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "bb6ce918f2adf6e17cb7554968827d9de7b94664", "commit_訊息": "[Web]C01-20250115002 修正關卡設定為\"允許輸入密碼\"時，繼續派送只有第一筆需要輸入密碼，第二筆之後就不用再輸入", "提交日期": "2025-01-16 17:23:04", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8507b9f61d9491644562e0009d9c284b4ce33af0", "commit_訊息": "[BPM APP]C01-20250116006 修正移動授權中間層的使用者維護作業切換分頁時會重複出現移動授權平台下拉選項問題", "提交日期": "2025-01-16 14:42:53", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "afb33b734602de1c34eab4854f86eb4b85a47710", "commit_訊息": "[Web]A00-20250114001 調整單身加總欄位設定小數點後1位四捨五入，計算錯誤的問題", "提交日期": "2025-01-15 14:08:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/BpmTable.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "dc31486ce1b016be86272eecef1ae94a177cf22c", "commit_訊息": "[表單設計師]C01-20250114002 修正Web表單設計師中腳本樣板的全域變數無法複製問題", "提交日期": "2025-01-15 11:57:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "63b91e2e66599d6754e8c4691205a391156cf88e", "commit_訊息": "[E10]C01-20250109002 E10表单同步若新增grid字段，表单只需新增字段，不用額外新增grid元件", "提交日期": "2025-01-13 15:48:18", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RWDFormMerge.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5227efb95b1117341fd55b327942059bdb33f8fe", "commit_訊息": "[流程引擎]C01-20250109003 修正核決關卡加簽2人以上關卡簡易流程預解析問題[補]", "提交日期": "2025-01-13 14:38:19", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "7dac5c4e1936cff2bb9e7e13f6e0598c6d6f43fb", "commit_訊息": "[流程引擎]C01-20250109003 修正核決關卡加簽2人以上關卡簡易流程預解析問題", "提交日期": "2025-01-13 11:04:24", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b78561268b2b1e115a42c907f419834f4b4eaaab", "commit_訊息": "[Web]S00-20241230001 BPM登入頁相關公司商標修改(補)", "提交日期": "2025-01-10 14:58:30", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "76dab32b00021e85b27d115894e0cb1eb361a055", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2025-01-10 14:54:18", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "4bd11f7bd5247c58c8dbbc8d345b651e5daab12b", "commit_訊息": "[Web]S00-20241230001 BPM登入頁相關公司商標修改(補)", "提交日期": "2025-01-10 14:54:00", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22be2ba5be9a60ada64ec93a248a49807c26b1e4", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2025-01-10 14:43:43", "作者": "kmin", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "b87152c887facacd2ff52a7ab369a6dceaa1a825", "commit_訊息": "[Web]S00-20250109002 列印表單時，是否要隱藏Input外框 true(隱藏)、false(不隱藏)。預設為false", "提交日期": "2025-01-10 14:43:00", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/configuration/PropertyKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "5bd8d3f1c8bcb4f83527cf7419f06104b0a88903", "commit_訊息": "[Web]S00-20241230001 BPM登入頁相關公司商標修改", "提交日期": "2025-01-10 14:27:35", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageSystemConfigAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/Constants.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/Login.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/cropped-logo_favicon.png", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/cropped-logo_favicon.png", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/default/newimages/v57image/logo.png", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/EFGP_banner_default.jpg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/EFGP_banner_default_zh_CN.jpg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/EFGP_logo_default.jpg", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/UserLogoImages/EFGP_logo_default_zh_CN.jpg", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 12}, {"commit_hash": "eed26d3c6b96f4ad67f4b0115c3a20a936f7e065", "commit_訊息": "[BPM APP]C01-20250107005 修正偶發關卡開啟待辦流程會發生取得表單資訊錯誤的問題", "提交日期": "2025-01-10 13:59:50", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1d067dd320fc89d424797b39068305f38807638c", "commit_訊息": "[派送關聯模組]Q00-20250109001 優化派送流程關聯，由後置流程取得前置流程附件資訊寫入後置表單log", "提交日期": "2025-01-09 13:37:37", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d8862a9f0782d5efc43cbd64ea6eca4ede62681", "commit_訊息": "[流程引擎]C01-20250102001 優化throw new JobExecutionException的部份，改為不立即重新執行", "提交日期": "2025-01-08 14:08:22", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/schedule/SystematicJob.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3e5b4cda9af163a07226b6342c7d92fddc2a8332", "commit_訊息": "[流程引擎]C01-20250103004 優化派送關卡異常呈現log不明確", "提交日期": "2025-01-08 11:46:41", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "0a0f5e2540a9e3011e1f16251ad7a9b3294f4f41", "commit_訊息": "[B2B]S00-20241231001_B2B模組新增獨立的「PDF浮水印屬性管理」功能供文件攜出使用(地)", "提交日期": "2025-01-08 09:25:52", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5cd5e604ff8032084b6f114e404f25d3b2ee3fff", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2025-01-07 17:31:32", "作者": "周权", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "0a0df44e914aef27302353b339ecb458b5107662", "commit_訊息": "[資安]Q00-20241227001 多選開窗查詢資安問題修正", "提交日期": "2025-01-07 17:27:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "310c61ac262eaf6fb2b69cf869511bf43b5be1e4", "commit_訊息": "[資安]Q00-20241226002 多選開窗查詢資安問題修正", "提交日期": "2025-01-07 17:27:19", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "cedf27f56a4c6c973372bb8a593118206baf357d", "commit_訊息": "[資安]Q00-20241226001 Sql Injection問題，调整多选开窗参数txtConditionValue", "提交日期": "2025-01-05 22:34:45", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "bc90618d7f0db88dce37b9ad67fd15295d7580e7", "commit_訊息": "[BPM APP]C01-20241231005 調整BPMAPP詳情頁連續簽核當workItemOID為空時直接返回待辦列表避免出現錯誤畫面", "提交日期": "2025-01-03 15:03:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a24cedede7d7eda62a360276c4818b4dbb02f715", "commit_訊息": "[BPM APP]C01-20241230002 調整企業微信組織同步機制避免因單筆資料異常導致整體同步失敗並增加相關Log資訊", "提交日期": "2025-01-02 16:25:03", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "10edcda109cd2d10aaa0203d988ca3c5a7f1faf3", "commit_訊息": "[Web]C01-20241227003 修正手機操作RWD畫面下無待辦事項時右上角工具列顯示待辦圖示的問題", "提交日期": "2024-12-31 16:11:36", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82f47b8bb31fdc798720a34930c471e3f350d1ea", "commit_訊息": "[BPM APP]C01-20241227004 修正當流程中在服務任務後接續核決關卡時會導致釘釘待辦創建重複兩筆的問題", "提交日期": "2024-12-31 13:55:17", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "075525d16310c37526d7b3e097752884d9b51ba9", "commit_訊息": "[流程引擎]C01-20241129002 增加寄送Mail連線重取機制，避免多人關卡漏信異常", "提交日期": "2024-12-03 17:24:14", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea5bb68429e8cd14930a8e56a277eea73b651b49", "commit_訊息": "[E10]C01-20241226001 E10抽单调用撤銷流程接口，若bpm是在服务任务关卡，就先不允许撤銷", "提交日期": "2024-12-30 10:05:30", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "5ef44d712fabdc7ca0b661e06b719353036a8777", "commit_訊息": "[流程引擎]C01-20241225003 修正禁止[發起者]作為活動處理者的代理人間接影響自動簽核判斷", "提交日期": "2024-12-27 13:59:14", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b091d85de12cf2f4e14c5f24993b903ce255404b", "commit_訊息": "[ISO]C01-20241219002 調整ISO屬性管理type欄位為NULL的資料為iso[補]", "提交日期": "2024-12-27 10:34:21", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "debcf6f1d30a36ac324edf5d258d4a252c7d98f3", "commit_訊息": "[MPT]C01-20241224003 調整在首頁追蹤流程中打開顯示流程當簽核歷程比較長時畫面會顯示不完整問題", "提交日期": "2024-12-26 13:51:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "57b266d07ba40e211bbc4e8f0f74f5b85f48b7b8", "commit_訊息": "[MPT]C01-20241224002 調整MPT公告申請單中沒有公告類型時會導致富文本畫面出不來問題", "提交日期": "2024-12-24 16:37:32", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "Release/copyfiles/@mpt/default-form/MptAncApply.form", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3c281a0cb34e4caac895033a10fcdd3f2a4cd68e", "commit_訊息": "[流程引擎]C01-20241223001 優化取不到WorkAssignment，增加每次執行緒sleep 0.3秒", "提交日期": "2024-12-24 14:01:07", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d4217ea19e5c52aa490e9541e48d7b519dfddba2", "commit_訊息": "[Web]C01-20241219003 修正追蹤流程點擊表單頁簽F12報錯找不到Resize方法", "提交日期": "2024-12-20 15:06:26", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ce49ead844f3cfab8c3793d76dd57f01775805ae", "commit_訊息": "[ISO]C01-20241219002 調整ISO屬性管理type欄位為NULL的資料為iso", "提交日期": "2024-12-20 14:37:19", "作者": "周权", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "1ca90821807af4445a4386f155e2462c22c10260", "commit_訊息": "[資安]Q00-20241217004 調整個人訊息頁欄位安全性問題", "提交日期": "2024-12-20 14:11:20", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "36176e9c963d882ff50c90ff9cec636d9896123d", "commit_訊息": "[文件总结助手] 修正找经验关联助手无chatfile授权显示，且新增有word授权才显示", "提交日期": "2024-12-20 10:09:39", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "29df590e0edf0991ec55c863f4106b6a302dd586", "commit_訊息": "Revert \"[資安]Q00-20241217001 避免XSS攻擊調整上傳檔案中'檔案描述'可輸入脚本的問題\"", "提交日期": "2024-12-19 17:06:16", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c8649201e3e09c31173bdebe1a56874387d49578", "commit_訊息": "[Web]C01-20241216003 修正searchFormDetail的網址hdnCurrentUserId值與實際用登入畫面帳號不一致導致異常", "提交日期": "2024-12-18 11:59:54", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1067b2405f6f3a90b128e14476bf37c146af94e4", "commit_訊息": "[Web]C01-20241218004 修正信件通知选择整张表单，栏位名称不显示是问题", "提交日期": "2024-12-19 14:03:14", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3d7cd2bce171d75afe612e60c4d3db17a6a82851", "commit_訊息": "[資安]Q00-20241217003 調整查詢欄位可以輸入查詢條件支持查詢的問題，防止SQL注入", "提交日期": "2024-12-19 10:43:41", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "feed170ac05e34f1076dd51005587c8f2d2f38ce", "commit_訊息": "[資安]Q00-20241217002 调整登入错误讯息[補]", "提交日期": "2024-12-19 10:23:42", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "c27c2bcf1b7fd8075ba91330278c113d122a7dd1", "commit_訊息": "[流程引擎]C01-20241008002 修正當流程已經有加簽過或是展開核決關卡後，再執行到客製sessionBean加簽關卡後，流程無法往下繼續派送的異常[補]", "提交日期": "2024-12-18 14:50:52", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "42f93a1b854b1435268015fe36b34275617d4a36", "commit_訊息": "[資安]Q00-20241217002 调整登入错误讯息", "提交日期": "2024-12-18 16:10:44", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "0137103b49970c59405d91de094f8590f617f1b8", "commit_訊息": "[資安]Q00-20241217001 避免XSS攻擊調整上傳檔案中'檔案描述'可輸入脚本的問題", "提交日期": "2024-12-18 10:29:27", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "72aafaa6c06070d358370fadc31d7cdfcd695602", "commit_訊息": "Merge remote-tracking branch 'origin/develop_v58' into develop_v58", "提交日期": "2024-12-17 15:40:36", "作者": "liu<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "58ab4f7d61540f50579e211fb224a81e1f8da27d", "commit_訊息": "[费用报销] 新增通过流程序号获取实例OID接口,费用报销管理作业requestUrl新增userId", "提交日期": "2024-12-17 15:40:14", "作者": "liu<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "35670e568741c874cb9e885b07a752eebb3d641a", "commit_訊息": "[內部]C01-20241217002 上傳檔案過大時，異常加入上傳者信息", "提交日期": "2024-12-17 15:37:29", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "62c917817e03711ba400e8a7e5e488be35d285da", "commit_訊息": "[流程引擎]C01-20241213002 調整WorkItem表 attachmentHits的欄位上限", "提交日期": "2024-12-17 13:52:47", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "7204cd996c2bf0cd606f9d8ae54edae66ccf2ebb", "commit_訊息": "[SYSDT]S00-20240905002 Web系統管理資料來源設定刪除優化為作廢機制[補]", "提交日期": "2024-12-17 12:02:39", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 6}, {"commit_hash": "ee6e3c254951a1865fcf689929203cd34382c922", "commit_訊息": "[Web]A00-20241212001 修正派送時關卡有勾選需要使用者指定需要發送的組織單位時畫面上沒有部門分類問題", "提交日期": "2024-12-13 17:04:25", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/InvokableOUListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "4f8c3c60cfe3171f501b428f8bad9162bdfcbc73", "commit_訊息": "[SYSDT]S00-20240905002 Web系統管理資料來源設定刪除優化為作廢機制[補]", "提交日期": "2024-12-12 17:14:32", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/SystemConfig.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/domain/form/DataAccessDefinition.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBSysCfgDAO.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.11.1_DDL_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_DM8.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.11.1_DML_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 16}, {"commit_hash": "752e2e0684569589c7462b4031089fe91cdff6af", "commit_訊息": "[PRODT]C01-20241209004 修正Web流程管理工具中活動參與者為策略分配時無法儲存定義問題[補]", "提交日期": "2024-12-12 16:24:37", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "60ea7428ade2e5d74cac0b94ef1368af4766a0e7", "commit_訊息": "[流程引擎]C01-20241204001 修正已轉派工作取回重派後解析主管異常問題", "提交日期": "2024-12-12 13:47:19", "作者": "kmin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ef6ea6674afc14df70fa58d1eea8ac1ba8403665", "commit_訊息": "[PRODT]S00-20241127001 組織管理工具(Web)將人員設定為離職時，LDAP帳號加上後綴「_U」(補)", "提交日期": "2024-12-12 10:29:54", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "Release/db/update/5.8.10.5_DML_DM8.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.10.5_DML_MSSQL.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}, {"檔案路徑": "Release/db/update/5.8.10.5_DML_Oracle.sql", "修改狀態": "重新命名", "狀態代碼": "R100"}], "變更檔案數量": 3}, {"commit_hash": "660622e598a7062bb9a736f210f20afd13440f4f", "commit_訊息": "[PRODT]S00-20241127001 組織管理工具(Web)將人員設定為離職時，LDAP帳號加上後綴「_U」", "提交日期": "2024-12-09 15:29:23", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "Release/db/update/5.8.10.5_DML_DM8.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.5_DML_MSSQL.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "Release/db/update/5.8.10.5_DML_Oracle.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 5}, {"commit_hash": "5dfd0e1650caa87c4935bafa911dfc3f1a319491", "commit_訊息": "[Web]C01-20241209005 增加发起流程资料选取注册器不存在的log讯息", "提交日期": "2024-12-11 10:46:15", "作者": "周权", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}