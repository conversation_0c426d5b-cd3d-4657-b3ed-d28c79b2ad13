{"比較資訊": {"專案ID": "BPM-ISO", "倉庫路徑": "D:\\IDEA_workspace\\BPM-ISO", "新分支": {"branch_name": "release_8.1.1.1", "date": "2025-03-06 13:45:18", "message": "[ISO]C01-20250305001 ISO文管首页树状作业，全文检索查询报错", "author": "周权"}, "舊分支": {"branch_name": "release_5.8.10.4", "date": "2024-12-06 13:46:11", "message": "[ISO]C01-20240820007 調整ISO新增單、變更單、作廢單JS(支援IMG簽核)(補)", "author": "lorenchang"}, "比較時間": "2025-07-18 11:45:14", "新增commit數量": 8, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "bb9831095e2b52ae0f91981389d57c0c2fcc484a", "commit_訊息": "[ISO]C01-20250305001 ISO文管首页树状作业，全文检索查询报错", "提交日期": "2025-03-06 13:45:18", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOHomePageByCategory.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9d6bcfa5f24de0840390eca23a495ddcafdc7f69", "commit_訊息": "[ISO]C01-20250227003 修正PDF閱讀畫面下載發佈檔會重覆下載2次", "提交日期": "2025-03-05 10:18:03", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/PDFWebView/web/BPMviewer.mjs", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cc65e2a51fe4525b24d3e46e569572bb11c5c70c", "commit_訊息": "[ISO]C01-20250224007 判斷機密等級新增防呆", "提交日期": "2025-02-25 13:26:03", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/AbsAccessRight.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "152a6743ae59efba6b48e6fb056e253de6adcb01", "commit_訊息": "[ISO]C01-20250220004 修正文件類別有不存在的群組會導致一般使用者打開文件類別管理出現異常訊息：build tree error", "提交日期": "2025-02-21 16:32:17", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOAuthorityDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8d009fa10c94ff576003a8168d91763efbb93134", "commit_訊息": "[ISO]C01-20250219002 修正評審規則定義觸發Revise流程名稱異常", "提交日期": "2025-02-21 08:31:04", "作者": "kmin", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOVettingRuleController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "67e4c0008b8726358bd0eb273d2378831548f0e6", "commit_訊息": "[B2B]S00-20241231001_B2B模組新增獨立的「PDF浮水印屬性管理」功能供文件攜出使用(地)", "提交日期": "2025-01-08 09:12:30", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/ISOWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/ModuleForm/UpdateDocumentInfo.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/ISOModule/isoPortability/ISOCloudWatermarkPattern.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreate.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOCreateManager.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOMod.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOPortability.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/WebContent/RWDFormJs/ISOPortabilityB2B.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.hbm.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/domain/ISOWatermarkPattern.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/restful/ISOWatermarkPatternController.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "ae0f540fc46b4a97c2f908eb9fcb590330dac1e8", "commit_訊息": "[ISO]C01-20241219002 修正浮水印開窗撈不到type為null的資料", "提交日期": "2024-12-20 14:27:05", "作者": "周权", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/dao/ISOWatermarkPatternDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "5c55ba4d86c2555c0e789f42928cab28a6d8464a", "commit_訊息": "[內部]增加ISO生失效排程預計作廢的Debug Log", "提交日期": "2024-12-11 17:39:20", "作者": "lorenchang", "檔案變更": [{"檔案路徑": "ISOModule/src/com/digiwin/bpm/ISOModule/services/ISODailyJobMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}