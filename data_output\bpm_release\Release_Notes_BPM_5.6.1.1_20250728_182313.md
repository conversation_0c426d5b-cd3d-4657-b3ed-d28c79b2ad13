# Release Notes - BPM

## 版本資訊
- **新版本**: 5.6.1.1
- **舊版本**: 5.6.0.3
- **生成時間**: 2025-07-28 18:23:13
- **新增 Commit 數量**: 229

## 變更摘要

### jerry1218 (19 commits)

- **2016-10-20 17:23:19**: T100整合-TrustGet服務預設type由4改為2
  - 變更檔案: 1 個
- **2016-10-18 16:26:13**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-18 16:24:48**: A00-20161017001 - T100整合ApproveLogGet服務,AutoAgent要擋掉
  - 變更檔案: 1 個
- **2016-10-17 10:51:46**: T100欄位同步時 , 以XML的Locale為主(BUG修正)
  - 變更檔案: 1 個
- **2016-10-05 16:42:54**: S00-20160930001 程式微調
  - 變更檔案: 1 個
- **2016-10-05 16:40:32**: S00-20160930001 加入設定開關
  - 變更檔案: 1 個
- **2016-10-05 16:30:24**: S00-20160930001 JBOSS啟動時,主動清除OJB_DLIST,OJB_DLIST_ENTRIES,OJB_DSET,OJB_DSET_ENTRIES這四個table - 程式修正 1.於NaNaEJB.properties增加開關nana.clear.ojbTable決定是否執行 2. 啟動JBOSS時清除ConnectedUserInfo
  - 變更檔案: 4 個
- **2016-09-30 17:20:00**: Q00-20160930003 修正後程式微調
  - 變更檔案: 1 個
- **2016-09-30 16:37:44**: Q00-20160930003 修正如果[使用舊客授權序號]且[由SecurityHandlerBean.register()進行登入]時 , 就算授權不足依然可以登入
  - 變更檔案: 1 個
- **2016-09-29 14:51:04**: 功能新增-JBOSS啟動時,由ContextManager主動清除OJB_DLIST,OJB_DLIST_ENTRIES,OJB_DSET,OJB_DSET_ENTRIES這四個table
  - 變更檔案: 1 個
- **2016-09-29 12:02:01**: (BUG修正 , AJAX及Action抓Globals.LOCALE_KEY)修改T100整合-表單欄位同步時 , 以設計師的Locale為主
  - 變更檔案: 2 個
- **2016-09-29 11:56:30**: (BUG修正)修改T100整合-表單欄位同步時 , 以設計師的Locale為主
  - 變更檔案: 3 個
- **2016-09-26 15:37:26**: 修正T100整合表單form檔 : 工單一般退料維護作業(asft323).form 客戶准入及變更作業(axmt800).form 會員卡種申請單(ammt320).form 實地盤點計畫維護作業(aint820).form
  - 變更檔案: 4 個
- **2016-09-26 15:36:29**: 新增整合表單form檔 : 工單維護作業(asft300).form 供應商績效評核定性評分單(apmt811).form 供應商績效評核綜合得分調整單(apmt814).form 門店資源協議申請作業(artt230).form 促銷談判申請(aprt310).form 專櫃合約異動申請(astt401).form 專櫃新商品引進維護作業(artt407).form 採購合約變更單維護作業(apmt490).form 採購預付單維護作業(aapt310).form 應付匯款開立作業(anmt460).form 應收帳款憑單(axrt300).form 轉帳傳票(aglt310).form 雜項待抵單維護作業(axrt341).form
  - 變更檔案: 13 個
- **2016-09-26 15:20:22**: Q00-20160926001 修正T100整合-待簽核事項中點擊連結至BPM中簽核會出現[主機連線異常,請重新登入]的錯誤
  - 變更檔案: 1 個
- **2016-09-23 17:48:07**: 修改T100整合-表單欄位同步時 , 以設計師的Locale為主
  - 變更檔案: 7 個
- **2016-09-21 11:04:41**: 修正組織內員工，如要從使用者基本資料中的「設定所屬單位」分頁，增加其「部門職務資料」在操作選取「所屬單位」(內有大量任職人員)項目後，要帶回於畫面上欄位時，處理時間過久。
  - 變更檔案: 1 個
- **2016-09-21 09:23:35**: 移除JSFilter
  - 變更檔案: 1 個
- **2016-09-19 15:38:12**: 組織設計師效能優化
  - 變更檔案: 11 個

### wayne (32 commits)

- **2016-10-20 12:01:08**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-20 11:59:56**: 修正模擬使用者進行簽核做人員切換的動作時會將上一個簽核的人員做登出(ESS)
  - 變更檔案: 1 個
- **2016-10-18 18:02:23**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-18 18:00:56**: 修正模擬使用者進行簽核做人員切換的動作時會將上一個簽核的人員做登出(ESS)
  - 變更檔案: 1 個
- **2016-10-14 17:06:45**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-14 17:05:45**: 重新產生新的patch(先建立5603版本，再update5611，再打包出來)
  - 變更檔案: 1 個
- **2016-10-14 13:55:14**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-14 13:54:06**: 將nextVersion_update....改檔名為5.6.1.1_update...
  - 變更檔案: 2 個
- **2016-10-12 14:26:49**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-12 14:26:10**: 自動安裝光碟無法執行，故重新調整
  - 變更檔案: 1 個
- **2016-10-12 11:37:05**: 加入 表單設計師 多語系
  - 變更檔案: 1 個
- **2016-10-12 10:44:06**: 將"formDesignerAccessor.error.duplicateFormId"調整為"formDesignerAccessor.error.duplicateFormID" (大小寫)
  - 變更檔案: 1 個
- **2016-10-11 17:03:58**: 將 alert('nav...') 字眼刪除
  - 變更檔案: 1 個
- **2016-10-11 16:30:26**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-11 16:28:27**: Q0020161007005  修正 ISO紙本申請 及 ISO紙本歸還申請 ajax 異常
  - 變更檔案: 1 個
- **2016-10-05 15:34:11**: 增加VIP User Table欄位
  - 變更檔案: 2 個
- **2016-09-29 14:03:22**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-29 14:01:08**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-29 13:57:20**: 增加VIPUser功能 - 補上多語系
  - 變更檔案: 1 個
- **2016-09-26 15:11:48**: 修正 EFGP 整合cross 時 mcloud 無法推播議題
  - 變更檔案: 2 個
- **2016-09-23 17:22:49**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2016-09-23 16:48:11**: [S]增加VIPUser功能
  - 變更檔案: 30 個
- **2016-09-22 15:42:32**: 修正多個使用者帳號取得BadgeNumber
  - 變更檔案: 1 個
- **2016-09-22 14:57:20**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-22 14:46:11**: ISO報表支援簡體中文
  - 變更檔案: 17 個
- **2016-09-22 14:20:07**: [Q]修正追蹤流程的取回重瓣按鈕異常
  - 變更檔案: 1 個
- **2016-09-22 14:08:11**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-22 14:00:43**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-20 15:51:34**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-14 15:53:36**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-14 15:22:07**: 20160914 調整sap無法寫入Grid Data問題
  - 變更檔案: 1 個
- **2016-09-14 11:01:15**: [Q]發起流程時因session覆蓋導致發起錯誤流程-防呆(從追蹤流程重發流程會報錯)
  - 變更檔案: 1 個

### pinchi_lin (5 commits)

- **2016-10-19 15:40:29**: V00-20161007001 修正安卓6.0以上手機於微信中點擊上傳附件沒有反應問題
  - 變更檔案: 3 個
- **2016-10-19 10:33:11**: 修正mobile新增SQL欄位存放位置
  - 變更檔案: 8 個
- **2016-10-07 16:46:20**: mobile的create_SQL加入新功能所需欄位指令
  - 變更檔案: 3 個
- **2016-10-06 11:47:32**: APP新UI：附件列表樣式修改-BUG修改
  - 變更檔案: 2 個
- **2016-10-05 17:32:37**: APP新UI：附件列表樣式修改
  - 變更檔案: 8 個

### Joe (29 commits)

- **2016-10-19 11:07:54**: 修正APP新UI部分多語系錯誤問題
  - 變更檔案: 2 個
- **2016-10-18 17:17:54**: APP新UI:自動登出功能
  - 變更檔案: 5 個
- **2016-10-18 15:56:17**: APP新UI 簽核ESS表單會跳出alert修正
  - 變更檔案: 2 個
- **2016-10-18 14:16:54**: APP新UI支援MCLOUD返回功能
  - 變更檔案: 11 個
- **2016-10-17 16:49:16**: APP 新UI 聯絡人頁面畫面修改
  - 變更檔案: 2 個
- **2016-10-17 14:44:43**: 新UI:若是local storage為空時會報can not read length of undefined修正
  - 變更檔案: 3 個
- **2016-10-17 09:52:33**: app新UI 修改MCLOUD沒先進工作首頁就直接進發起流程畫面導致沒有取到流程資料問題
  - 變更檔案: 1 個
- **2016-10-13 11:42:51**: MCLOUD 網址解析修改
  - 變更檔案: 2 個
- **2016-10-13 11:27:13**: MCLOUD 轉址修正
  - 變更檔案: 1 個
- **2016-10-12 13:52:27**: 修改MCLOUD導向到APP新UI
  - 變更檔案: 1 個
- **2016-10-07 16:09:46**: APP新UI 工作首頁:公告圖示先註解掉
  - 變更檔案: 1 個
- **2016-10-06 18:01:22**: app新ui 工作通知頁面多語系錯誤修正
  - 變更檔案: 1 個
- **2016-10-06 14:03:31**: APP新UI 工作通知:流程圖無法顯示bug修改
  - 變更檔案: 1 個
- **2016-10-06 13:43:28**: APP新UI header button 跑版修改
  - 變更檔案: 1 個
- **2016-10-04 17:28:27**: APP新UI:微信登入導頁錯誤修正
  - 變更檔案: 1 個
- **2016-10-04 14:55:48**: APP 新UI 公告功能修改
  - 變更檔案: 3 個
- **2016-10-04 09:53:51**: APP新UI 更改待辦:附件部分code
  - 變更檔案: 2 個
- **2016-09-26 16:14:36**: APP新UI:補上遺漏的多語系修改
  - 變更檔案: 1 個
- **2016-09-26 11:56:08**: APP新UI議題
  - 變更檔案: 16 個
- **2016-09-23 15:40:37**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-23 15:39:35**: APP新UI議題
  - 變更檔案: 15 個
- **2016-09-23 11:01:48**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-23 11:01:21**: 修正UserListReader SQL語法錯誤
  - 變更檔案: 1 個
- **2016-09-22 11:38:53**: APP新UI議題修正
  - 變更檔案: 1 個
- **2016-09-22 10:54:04**: APP新UI議題修正
  - 變更檔案: 5 個
- **2016-09-19 17:58:46**: APP新UI議題修改
  - 變更檔案: 2 個
- **2016-09-19 14:34:41**: APP新ui 新增撥打電話功能
  - 變更檔案: 4 個
- **2016-09-13 15:05:45**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-09-13 14:37:49**: BPM APP NEW UI 第一階段
  - 變更檔案: 97 個

### yylee1123 (36 commits)

- **2016-10-18 16:50:54**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-18 16:50:17**: 暫存表單的formDefinitionOID以-開頭，會導致IE無法另開視窗
  - 變更檔案: 1 個
- **2016-10-03 18:07:18**: [S]報表自定義上傳及關連設定增加新增時間
  - 變更檔案: 7 個
- **2016-10-03 16:13:35**: [Q00-20161003001]修正更新表單設計師後，舊表單無法列印表單的問題
  - 變更檔案: 1 個
- **2016-09-30 18:31:16**: [S]管理程式權限設定增加更新人員OID、時間
  - 變更檔案: 7 個
- **2016-09-30 11:39:24**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-30 11:38:18**: [S]在log記錄啟動流程測試模式的使用者、模擬對象
  - 變更檔案: 1 個
- **2016-09-29 17:08:03**: web表單設計師
  - 變更檔案: 3 個
- **2016-09-29 14:02:11**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-29 14:01:26**: [S]移除NaNaWeb.properties的nana.isoModule.enable，因命名與功能不符；開放管理程式權限設定的功能
  - 變更檔案: 5 個
- **2016-09-26 18:16:00**: [S]管理程式權限設定、模組程式維護、文管權限管理介面修改
  - 變更檔案: 20 個
- **2016-09-19 18:17:04**: [Q00-20160919001]修正重覆的@ejb.ejb-ref
  - 變更檔案: 2 個
- **2016-09-19 14:45:16**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-19 14:38:29**: [S]文件總管新增文件、ISO文件新增申請、ISO文件變更申請，若有設定多文件主機，會立即佈署文件
  - 變更檔案: 7 個
- **2016-09-12 13:03:41**: 修正ISO的updateSQL
  - 變更檔案: 1 個
- **2016-09-10 17:11:25**: web表單設計師
  - 變更檔案: 1 個
- **2016-09-10 15:27:56**: web表單設計師
  - 變更檔案: 6 個
- **2016-09-08 16:50:25**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
  - 變更檔案: 1 個
- **2016-09-08 16:46:50**: web表單設計師
  - 變更檔案: 8 個
- **2016-09-05 18:27:25**: web表單設計師
  - 變更檔案: 4 個
- **2016-09-02 10:45:29**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-09-02 10:38:06**: web表單設計師
  - 變更檔案: 1 個
- **2016-09-01 17:47:01**: 增加ISO紙本文件申請單身文件版號(displayVersion)
  - 變更檔案: 1 個
- **2016-09-01 16:26:46**: 修改ISOPaperRecord的docDisplayVersion預設值
  - 變更檔案: 1 個
- **2016-08-29 17:34:01**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-08-29 17:32:53**: web表單設計師
  - 變更檔案: 2 個
- **2016-08-29 10:59:18**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-08-29 10:56:55**: web表單設計師
  - 變更檔案: 3 個
- **2016-08-25 19:16:11**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-08-25 19:13:37**: web表單設計師
  - 變更檔案: 7 個
- **2016-08-25 14:14:40**: web表單設計師
  - 變更檔案: 20 個
- **2016-08-15 13:47:09**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-08-15 13:40:58**: web表單設計師
  - 變更檔案: 6 個
- **2016-08-11 09:46:02**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
  - 變更檔案: 3 個
- **2016-08-10 17:48:50**: web表單設計師
  - 變更檔案: 2 個
- **2016-08-10 17:48:50**: web表單設計師
  - 變更檔案: 2 個

### Gaspard (53 commits)

- **2016-10-16 15:40:03**: 修正從T100無法同步表單欄位
- **2016-10-16 15:40:03**: 修正從T100無法同步表單欄位
  - 變更檔案: 6 個
- **2016-10-14 11:35:40**: 修正在IE新建立一張表單並完成儲存後，於表單分類樹中會看到兩筆剛編輯的表單
  - 變更檔案: 1 個
- **2016-10-13 17:25:14**: 修正在修改表單分類名稱時，若修改前後的分類名稱相同會跳出錯誤提示
  - 變更檔案: 1 個
- **2016-10-13 17:14:48**: 修正在IE編輯表單分類時，修改完按下ENTER會自動開啟表單的問題
  - 變更檔案: 2 個
- **2016-10-13 16:39:01**: 修正取不到多語系的問題
  - 變更檔案: 1 個
- **2016-10-13 16:07:49**: Q00-20161012002 修正表單無法排序的問題
  - 變更檔案: 1 個
- **2016-10-11 16:10:27**: 流程效能監控多語系
  - 變更檔案: 2 個
- **2016-10-11 09:15:48**: 將舊的SWING表單設計師連結透過設定檔決定是否顯示
  - 變更檔案: 3 個
- **2016-10-06 16:08:28**: 加入5611的Create與UpdateSQL
  - 變更檔案: 4 個
- **2016-10-06 16:03:24**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
  - 變更檔案: 1 個
- **2016-10-06 16:03:02**: 流程效能追蹤紀錄器
  - 變更檔案: 33 個
- **2016-10-05 14:30:04**: WEB表單設計師新增欄位
  - 變更檔案: 4 個
- **2016-09-20 12:09:51**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-20 12:09:16**: 取得流程包裹效能優化
  - 變更檔案: 3 個
- **2016-09-08 10:12:07**: Web表單設計師
  - 變更檔案: 1 個
- **2016-09-07 17:19:53**: Web表單設計師
  - 變更檔案: 1 個
- **2016-09-06 17:53:51**: Web表單設計師
  - 變更檔案: 2 個
- **2016-09-06 15:57:51**: Web表單設計師
  - 變更檔案: 1 個
- **2016-09-06 15:17:08**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
  - 變更檔案: 1 個
- **2016-09-06 15:14:47**: Web表單設計師
  - 變更檔案: 1 個
- **2016-09-06 15:13:25**: Web表單設計師
  - 變更檔案: 11 個
- **2016-09-02 13:53:44**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-31 10:39:55**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-31 09:30:35**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-30 16:00:56**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-08-30 15:59:26**: Web表單設計師
  - 變更檔案: 4 個
- **2016-08-29 17:59:17**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-08-29 17:56:18**: Web表單設計師
  - 變更檔案: 2 個
- **2016-08-29 16:29:55**: Web表單設計師
  - 變更檔案: 2 個
- **2016-08-26 17:16:37**: Web表單設計師
  - 變更檔案: 4 個
- **2016-08-26 10:54:44**: Web表單設計師
  - 變更檔案: 4 個
- **2016-08-25 17:50:03**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-25 17:36:12**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-25 17:31:33**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-25 14:56:46**: Web表單設計師
  - 變更檔案: 3 個
- **2016-08-25 14:41:20**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **2016-08-25 14:31:45**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-25 14:25:12**: Web表單設計師
  - 變更檔案: 5 個
- **2016-08-24 16:38:13**: Web表單設計師
  - 變更檔案: 2 個
- **2016-08-24 15:21:45**: Web表單設計師
  - 變更檔案: 16 個
- **2016-08-23 17:33:56**: App版的SQL註冊器改寫
  - 變更檔案: 4 個
- **2016-08-23 17:32:27**: Web表單設計師
  - 變更檔案: 2 個
- **2016-08-22 17:12:00**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-22 16:38:06**: Web表單設計師
  - 變更檔案: 2 個
- **2016-08-19 17:44:22**: Web表單設計師
  - 變更檔案: 2 個
- **2016-08-19 16:36:30**: Web表單設計師
  - 變更檔案: 2 個
- **2016-08-19 14:34:53**: Web表單設計師
  - 變更檔案: 1 個
- **2016-08-19 10:24:00**: Web表單設計師
  - 變更檔案: 2 個
- **2016-08-18 17:31:40**: Web表單設計師
  - 變更檔案: 3 個
- **2016-08-18 13:57:30**: Web表單設計師
  - 變更檔案: 3 個
- **2016-08-18 13:48:35**: Web表單設計師
  - 變更檔案: 21 個
- **2016-08-11 13:42:06**: 測試Commit
  - 變更檔案: 1 個

### WenCheng (6 commits)

- **2016-10-14 14:21:40**: A00-20160921001 修正流程設計師其中的關卡屬性設置，無法儲存「是否允許退回重辦必填簽核意見」設定值。
  - 變更檔案: 2 個
- **2016-10-07 17:50:11**: Q00-20161007004 從T100產品執行「BPM表單設計師」開啟動作，其錯誤描述訊息內容已修正。
  - 變更檔案: 1 個
- **2016-10-07 17:48:41**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-07 17:47:48**: Q00-20161007002 安裝密碼註冊功能，修正查找出的註冊授權記錄消失問題。
  - 變更檔案: 1 個
- **2016-10-04 09:10:37**: 組織設計師效能優化
  - 變更檔案: 17 個
- **2016-09-23 14:13:17**: 組織設計師效能優化
  - 變更檔案: 21 個

### LALA (18 commits)

- **2016-10-06 11:37:47**: Q00-20160929001[內部]非整合crm不需要crmModal
  - 變更檔案: 1 個
- **2016-10-03 17:28:18**: S00-20160907001[內部]從文件總管開啟檔案時，預設為使用者wf主機對應的預設文件主機
  - 變更檔案: 3 個
- **2016-09-30 13:43:59**: Q00-20160929001[內部]下載檔案javaScript錯誤修正
  - 變更檔案: 2 個
- **2016-09-30 10:25:13**: [S00-20160727004][內部]統一轉派畫面的文字與順序，以避免客戶困惑
  - 變更檔案: 4 個
- **2016-09-30 09:58:29**: Q00-20160922002[內部]excel轉pdf時，頁籤過多會轉檔失敗
  - 變更檔案: 1 個
- **2016-09-30 09:56:30**: C01-20160919005[欣興]PDFViewer連續點擊PrintScreen可截圖
  - 變更檔案: 1 個
- **2016-09-30 09:53:25**: S00-20160907001[內部]從文件總管開啟檔案時，預設為使用者wf主機對應的預設文件主機
  - 變更檔案: 3 個
- **2016-09-30 09:41:50**: A00-20160910003[上品綜合工]簽核流設計師在沒有任何流程分類或流程時，有admin權限的使用者無法新增流程分類及流程
  - 變更檔案: 1 個
- **2016-09-29 14:29:05**: Q00-20160929001[內部]非整合crm不需要crmModal
  - 變更檔案: 2 個
- **2016-09-23 13:20:00**: A00-20160910003[上品綜合工]簽核流設計師在沒有任何流程分類或流程時，有admin權限的使用者無法新增流程分類及流程
  - 變更檔案: 1 個
- **2016-09-22 16:44:20**: S00-20160907001[內部]從文件總管開啟檔案時，預設為使用者wf主機對應的預設文件主機
  - 變更檔案: 3 個
- **2016-09-22 16:11:39**: C01-20160919005[欣興]PDFViewer連續點擊PrintScreen可截圖
  - 變更檔案: 1 個
- **2016-09-22 15:57:53**: Q00-20160922002[內部]excel轉pdf時，頁籤過多會轉檔失敗
  - 變更檔案: 1 個
- **2016-09-22 15:27:26**: [S00-20160727004][內部]統一轉派畫面的文字與順序，以避免客戶困惑
  - 變更檔案: 3 個
- **2016-09-12 15:32:45**: A00-20160823001[建邦]V6簽核流設計師的通知管理員，將原本V5有的變數加回變數清單
  - 變更檔案: 13 個
- **2016-09-12 14:58:40**: A00-20160910002[寶成]將表單設計師表預設表單Frames的容器長度調整為8000
  - 變更檔案: 1 個
- **2016-09-12 14:44:54**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-12 14:41:47**: C01-20160831001[宗盈]V6版本的簽核流程設計師，在條件運算式抓不到Input-Multi-Dialog
  - 變更檔案: 1 個

### loren (16 commits)

- **2016-10-05 17:29:18**: 移除多餘組織同步設定檔
  - 變更檔案: 1 個
- **2016-10-03 16:21:41**: 修正密碼政策的ForceChangePassword不設定時登入會出現異常的問題
  - 變更檔案: 3 個
- **2016-09-30 17:27:55**: 修改密碼政策
  - 變更檔案: 11 個
- **2016-09-21 17:48:26**: 合併分支develop_MergeRequest into develop
- **2016-09-21 17:30:17**: 合併分支VSS2Git_BPM into develop_MergeRequest
  - 變更檔案: 56 個
- **2016-09-20 16:10:14**: Merge remote-tracking branch 'origin/develop_5.6.1.1' into develop
- **2016-09-14 08:37:33**: RsrcBundle修正
  - 變更檔案: 2 個
- **2016-09-13 13:57:41**: 移除不需在Git控管的應用程式
  - 變更檔案: 7 個
- **2016-09-13 13:54:52**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-13 13:54:29**: 移除updateMCloud_MSSQL/Oralce.sql
  - 變更檔案: 2 個
- **2016-09-12 14:29:48**: T100組織同步增加可同步的項目(T100版本：v1.1.04以後)
  - 變更檔案: 19 個
- **2016-09-02 09:53:30**: Eclipse開發環境調整，讓設定新版本Eclipse或JDK時可以更方便
  - 變更檔案: 19 個
- **2016-09-02 09:43:25**: Eclipse開發環境調整，讓設定新版本Eclipse或JDK時可以更方便
  - 變更檔案: 19 個
- **2016-08-30 09:47:25**: Eclipse開發環境加入整個BPM目錄的Project，Resource Filters設定隱藏不顯示的目錄
  - 變更檔案: 2 個
- **2016-08-30 09:23:18**: Eclipse開發環境加入整個BPM目錄的Project，Resource Filters設定隱藏不顯示的目錄
  - 變更檔案: 2 個
- **2016-08-10 12:50:34**: 合併BPM(VSS)
  - 變更檔案: 1142 個

### jd (2 commits)

- **2016-10-03 16:34:11**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-10-03 16:29:25**: BPM公告系统
  - 變更檔案: 26 個

###  chr(38) ||]改回[&]|2016-09-29 15:06:04|jerry1218 (1 commits)

- ****: 修正nextVersion_updateSQL_SQLServer.sql錯誤 , [
  - 變更檔案: 1 個

### Noah.Jhuang (3 commits)

- **2016-09-23 10:28:28**: [內部] Q00-20160923001 2016.09.23 Modify 人員開窗報錯 by 駿緯.
  - 變更檔案: 1 個
- **2016-09-13 13:49:14**: Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **2016-09-13 13:47:39**: [立訊精密] C01-20160808003 2016.09.12 Modify SQL 效能優化 by 駿緯.
  - 變更檔案: 12 個

### Jack (7 commits)

- **2016-09-22 14:19:33**: APP新UI 議題修正
  - 變更檔案: 4 個
- **2016-09-22 14:02:08**: APP新UI 議題修正
  - 變更檔案: 6 個
- **2016-09-22 10:12:24**: APP 新UI議題修正
  - 變更檔案: 3 個
- **2016-09-22 10:01:31**: APP 新UI議題修正
  - 變更檔案: 2 個
- **2016-09-19 17:27:09**: Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
  - 變更檔案: 1 個
- **2016-09-19 17:24:55**: BPM APP 新UI議題修正
  - 變更檔案: 9 個
- **2016-09-19 10:48:27**: BPM APP新UI相關程式
  - 變更檔案: 1 個

### arielshih (1 commits)

- **2016-09-14 15:10:59**: 無議題編號
  - 變更檔案: 1 個

### Administrator (1 commits)

- **2016-09-10 17:00:01**: Merge branch 'release_5.6.0.3' into 'develop'

## 詳細變更記錄

### 1. T100整合-TrustGet服務預設type由4改為2
- **Commit ID**: `1880e462550e26daaa22f09cd21745a89b769c9f`
- **作者**: jerry1218
- **日期**: 2016-10-20 17:23:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`

### 2. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `cca00c7d61d13fa92e754f8d5155d8a6cd5f656d`
- **作者**: wayne
- **日期**: 2016-10-20 12:01:08
- **變更檔案數量**: 0

### 3. 修正模擬使用者進行簽核做人員切換的動作時會將上一個簽核的人員做登出(ESS)
- **Commit ID**: `7d62d03d84cb872ff3bce62b7a9097c973169eae`
- **作者**: wayne
- **日期**: 2016-10-20 11:59:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`

### 4. V00-20161007001 修正安卓6.0以上手機於微信中點擊上傳附件沒有反應問題
- **Commit ID**: `49d334433f6e68cb311947b3c9baaad5e3c0cf14`
- **作者**: pinchi_lin
- **日期**: 2016-10-19 15:40:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`

### 5. 修正APP新UI部分多語系錯誤問題
- **Commit ID**: `de0071a74c6613c6ff403dd1291c653dbdcc589c`
- **作者**: Joe
- **日期**: 2016-10-19 11:07:54
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 6. 修正mobile新增SQL欄位存放位置
- **Commit ID**: `79fae73d852a516d802b2cc7200d5534751e31b8`
- **作者**: pinchi_lin
- **日期**: 2016-10-19 10:33:11
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.1.1_updateSQL_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_SQLServer.sql`

### 7. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a06a77c3284593c11dfc7b961bae77b32400d02a`
- **作者**: wayne
- **日期**: 2016-10-18 18:02:23
- **變更檔案數量**: 0

### 8. 修正模擬使用者進行簽核做人員切換的動作時會將上一個簽核的人員做登出(ESS)
- **Commit ID**: `aa6b2f143c7de0a88932a5246ccfea762e703d48`
- **作者**: wayne
- **日期**: 2016-10-18 18:00:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`

### 9. APP新UI:自動登出功能
- **Commit ID**: `4fbee4cd44c2883842aaa8b50a7c0655c6aac008`
- **作者**: Joe
- **日期**: 2016-10-18 17:17:54
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 10. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `35aa11b14ddb6ca7e25c859bca5e440f7e99335e`
- **作者**: yylee1123
- **日期**: 2016-10-18 16:50:54
- **變更檔案數量**: 0

### 11. 暫存表單的formDefinitionOID以-開頭，會導致IE無法另開視窗
- **Commit ID**: `0e0da1ee47448abe60ae496cac3ab8b29e72eca9`
- **作者**: yylee1123
- **日期**: 2016-10-18 16:50:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`

### 12. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `aad038f131eb7191ea9415e4a3221dd9a0c791c9`
- **作者**: jerry1218
- **日期**: 2016-10-18 16:26:13
- **變更檔案數量**: 0

### 13. A00-20161017001 - T100整合ApproveLogGet服務,AutoAgent要擋掉
- **Commit ID**: `c7b8e54a8fd29edf510a7c055531605691e8078b`
- **作者**: jerry1218
- **日期**: 2016-10-18 16:24:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`

### 14. APP新UI 簽核ESS表單會跳出alert修正
- **Commit ID**: `d5db172c5f71cc1e61cfdcb80c05d8ca301dd0cd`
- **作者**: Joe
- **日期**: 2016-10-18 15:56:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`

### 15. APP新UI支援MCLOUD返回功能
- **Commit ID**: `8791dd1b4c5b64aeba35ccffb55e5d0a85429353`
- **作者**: Joe
- **日期**: 2016-10-18 14:16:54
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`

### 16. APP 新UI 聯絡人頁面畫面修改
- **Commit ID**: `fb9be36deea538619be197cc4cd209fcf7efd5d0`
- **作者**: Joe
- **日期**: 2016-10-17 16:49:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppContact.css`

### 17. 新UI:若是local storage為空時會報can not read length of undefined修正
- **Commit ID**: `13c5c6dc747d917abbe15a20cccc9eacfa3c672a`
- **作者**: Joe
- **日期**: 2016-10-17 14:44:43
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 18. T100欄位同步時 , 以XML的Locale為主(BUG修正)
- **Commit ID**: `f593b2aa4942e7a9ed9c23850853d3c244fc4a9e`
- **作者**: jerry1218
- **日期**: 2016-10-17 10:51:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopFormTransfer.java`

### 19. app新UI 修改MCLOUD沒先進工作首頁就直接進發起流程畫面導致沒有取到流程資料問題
- **Commit ID**: `b6fc1d6a699e8f38b6d51bc544a71cee0ead9ecc`
- **作者**: Joe
- **日期**: 2016-10-17 09:52:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 20. 修正從T100無法同步表單欄位
- **Commit ID**: `1cc63f99efe2206275d00368ed216707f0aae785`
- **作者**: Gaspard
- **日期**: 2016-10-16 15:40:03
- **變更檔案數量**: 0

### 21. 修正從T100無法同步表單欄位
- **Commit ID**: `237114854b20a5572e9b62999568f2db6924a452`
- **作者**: Gaspard
- **日期**: 2016-10-16 15:40:03
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopFormTransfer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/T100FormMerge.java`

### 22. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `c3f70cf20ece24d6338989c96580d24daaf9a3fd`
- **作者**: wayne
- **日期**: 2016-10-14 17:06:45
- **變更檔案數量**: 0

### 23. 重新產生新的patch(先建立5603版本，再update5611，再打包出來)
- **Commit ID**: `5c020f4124ab39f5c5900cd691439f78fffd78ea`
- **作者**: wayne
- **日期**: 2016-10-14 17:05:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 24. A00-20160921001 修正流程設計師其中的關卡屬性設置，無法儲存「是否允許退回重辦必填簽核意見」設定值。
- **Commit ID**: `c653de0a5a60e2aaf2ba0693d6fd6259ba2d0891`
- **作者**: WenCheng
- **日期**: 2016-10-14 14:21:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTableModel.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`

### 25. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `12d157d20ef39d0bdb8f729a4ddbbb7ed312b745`
- **作者**: wayne
- **日期**: 2016-10-14 13:55:14
- **變更檔案數量**: 0

### 26. 將nextVersion_update....改檔名為5.6.1.1_update...
- **Commit ID**: `c4ce7fd31be52dabe3f95527f19ef0c5bd1f7e13`
- **作者**: wayne
- **日期**: 2016-10-14 13:54:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📄 **重新命名**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 27. 修正在IE新建立一張表單並完成儲存後，於表單分類樹中會看到兩筆剛編輯的表單
- **Commit ID**: `af4cffd827f9b7dd028ca41bd0b2c9bbe9c20aee`
- **作者**: Gaspard
- **日期**: 2016-10-14 11:35:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`

### 28. 修正在修改表單分類名稱時，若修改前後的分類名稱相同會跳出錯誤提示
- **Commit ID**: `ed7e59aa46ed4519075fce88b681c5a0fb722320`
- **作者**: Gaspard
- **日期**: 2016-10-13 17:25:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 29. 修正在IE編輯表單分類時，修改完按下ENTER會自動開啟表單的問題
- **Commit ID**: `5df04b3e4c26798f6b047e866ce7528f4fc19b7b`
- **作者**: Gaspard
- **日期**: 2016-10-13 17:14:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 30. 修正取不到多語系的問題
- **Commit ID**: `f50eed3ab18128e872c1121741b0fcb8d98b9e93`
- **作者**: Gaspard
- **日期**: 2016-10-13 16:39:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/ProcessPerformanceMonitor.jsp`

### 31. Q00-20161012002 修正表單無法排序的問題
- **Commit ID**: `ab51353ac22e4b49258a576352c93bf5fb56989e`
- **作者**: Gaspard
- **日期**: 2016-10-13 16:07:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`

### 32. MCLOUD 網址解析修改
- **Commit ID**: `ed762bee9c05fee96b4c4f74823bfc7a696b4290`
- **作者**: Joe
- **日期**: 2016-10-13 11:42:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 33. MCLOUD 轉址修正
- **Commit ID**: `209d572397c07733681e729e0f65490bb8da3275`
- **作者**: Joe
- **日期**: 2016-10-13 11:27:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileKickStart.jsp`

### 34. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `19e5b019e753f4c9967dba5435ef93b1edeeef15`
- **作者**: wayne
- **日期**: 2016-10-12 14:26:49
- **變更檔案數量**: 0

### 35. 自動安裝光碟無法執行，故重新調整
- **Commit ID**: `125f50c024c18cc544038414faf35ec46c977390`
- **作者**: wayne
- **日期**: 2016-10-12 14:26:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 36. 修改MCLOUD導向到APP新UI
- **Commit ID**: `158e7a62f2d5d46bca276a43003ac550d8e752ff`
- **作者**: Joe
- **日期**: 2016-10-12 13:52:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 37. 加入 表單設計師 多語系
- **Commit ID**: `630ada769df66655712141282e7ea5027477c157`
- **作者**: wayne
- **日期**: 2016-10-12 11:37:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 38. 將"formDesignerAccessor.error.duplicateFormId"調整為"formDesignerAccessor.error.duplicateFormID" (大小寫)
- **Commit ID**: `6222f40191a22a9b452e34ee69da31d27e47c867`
- **作者**: wayne
- **日期**: 2016-10-12 10:44:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 39. 將 alert('nav...') 字眼刪除
- **Commit ID**: `eee44af7e87d48ff0760c7c41d290dcf23099f7b`
- **作者**: wayne
- **日期**: 2016-10-11 17:03:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 40. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `db85b728df1945dae85d8cae977e67ad1cdbf3bf`
- **作者**: wayne
- **日期**: 2016-10-11 16:30:26
- **變更檔案數量**: 0

### 41. Q0020161007005  修正 ISO紙本申請 及 ISO紙本歸還申請 ajax 異常
- **Commit ID**: `6b27e58e4227e81f1669aaed92e8648fb21d33ef`
- **作者**: wayne
- **日期**: 2016-10-11 16:28:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 42. 流程效能監控多語系
- **Commit ID**: `327d566945ffb57e0e2ce064d1e4968989229e0d`
- **作者**: Gaspard
- **日期**: 2016-10-11 16:10:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`

### 43. 將舊的SWING表單設計師連結透過設定檔決定是否顯示
- **Commit ID**: `64ccf63879d04375964ac31527eb2e8a9ceea56d`
- **作者**: Gaspard
- **日期**: 2016-10-11 09:15:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 44. Q00-20161007004 從T100產品執行「BPM表單設計師」開啟動作，其錯誤描述訊息內容已修正。
- **Commit ID**: `8405e3620b20d01184034f0a2abf91b589f09853`
- **作者**: WenCheng
- **日期**: 2016-10-07 17:50:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`

### 45. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b2c29996a425dcc19bb89c85be5398eb1b6788cf`
- **作者**: WenCheng
- **日期**: 2016-10-07 17:48:41
- **變更檔案數量**: 0

### 46. Q00-20161007002 安裝密碼註冊功能，修正查找出的註冊授權記錄消失問題。
- **Commit ID**: `4fbe78a73ec438de470647f8e38ca2b49d24232a`
- **作者**: WenCheng
- **日期**: 2016-10-07 17:47:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java`

### 47. mobile的create_SQL加入新功能所需欄位指令
- **Commit ID**: `77bb813857fc09d76ffa68821a6a1bf4ad035d1c`
- **作者**: pinchi_lin
- **日期**: 2016-10-07 16:46:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_SQLServer.sql`

### 48. APP新UI 工作首頁:公告圖示先註解掉
- **Commit ID**: `8c0afc6d5d6bcca270344faf08079a2125415829`
- **作者**: Joe
- **日期**: 2016-10-07 16:09:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`

### 49. app新ui 工作通知頁面多語系錯誤修正
- **Commit ID**: `8842df180b20a9be05a1ecc4d3f714488588c91c`
- **作者**: Joe
- **日期**: 2016-10-06 18:01:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`

### 50. 加入5611的Create與UpdateSQL
- **Commit ID**: `e92a6fbd57afedec2c17516e499b1c0b3d1f7470`
- **作者**: Gaspard
- **日期**: 2016-10-06 16:08:28
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 51. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `96b3e8124dd45d57c2ae426c6c068d00b70f4fe2`
- **作者**: Gaspard
- **日期**: 2016-10-06 16:03:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 52. 流程效能追蹤紀錄器
- **Commit ID**: `a277eca75c2217aca3e1451b3dc9da70971debd1`
- **作者**: Gaspard
- **日期**: 2016-10-06 16:03:02
- **變更檔案數量**: 33
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessPackageManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/audit_data/PerformanceData.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/audit_data/PerformanceRecord.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ProcessPerformanceMonitorAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ProcessPerformanceRecordUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-processPerformanceMonitor-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/InvokeProcessDiagram.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/PerformWorkItemDiagram.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/ProcessPerformanceMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/processPerformanceMonitor/invokeProcessDiagram.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/processPerformanceMonitor/performWorkItemDiagram.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/images/setting.png`

### 53. APP新UI 工作通知:流程圖無法顯示bug修改
- **Commit ID**: `a8709c606f5a04efff3275f80cf627e64e31afe5`
- **作者**: Joe
- **日期**: 2016-10-06 14:03:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 54. APP新UI header button 跑版修改
- **Commit ID**: `edca4a124ef41951b43b2ce07b7904823c6bc18e`
- **作者**: Joe
- **日期**: 2016-10-06 13:43:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 55. APP新UI：附件列表樣式修改-BUG修改
- **Commit ID**: `51173bbe435b61e7f0551496ee8cb871ef325e3f`
- **作者**: pinchi_lin
- **日期**: 2016-10-06 11:47:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`

### 56. Q00-20160929001[內部]非整合crm不需要crmModal
- **Commit ID**: `2fb3e5fc2d2488264b52b65b4f97fa3267acd713`
- **作者**: LALA
- **日期**: 2016-10-06 11:37:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 57. APP新UI：附件列表樣式修改
- **Commit ID**: `002404a7505dd8fc25fb5d651b5e0e7cb8c9b511`
- **作者**: pinchi_lin
- **日期**: 2016-10-05 17:32:37
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 58. 移除多餘組織同步設定檔
- **Commit ID**: `7366a54615aee503e4101dad02ff21c2abd6fc1c`
- **作者**: loren
- **日期**: 2016-10-05 17:29:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/NaNa/conf/syncorg/sync-def_forT100.xml`

### 59. S00-20160930001 程式微調
- **Commit ID**: `bffe2d30091a9771f2574bb4d05c463a55b05e68`
- **作者**: jerry1218
- **日期**: 2016-10-05 16:42:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaEJB.properties`

### 60. S00-20160930001 加入設定開關
- **Commit ID**: `2e1409cd3005b166deb7ac370ac604f769a0b7b6`
- **作者**: jerry1218
- **日期**: 2016-10-05 16:40:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaEJB.properties`

### 61. S00-20160930001 JBOSS啟動時,主動清除OJB_DLIST,OJB_DLIST_ENTRIES,OJB_DSET,OJB_DSET_ENTRIES這四個table - 程式修正 1.於NaNaEJB.properties增加開關nana.clear.ojbTable決定是否執行 2. 啟動JBOSS時清除ConnectedUserInfo
- **Commit ID**: `929ba8a3f9919f95c84cba3d74af3e0eda78a90d`
- **作者**: jerry1218
- **日期**: 2016-10-05 16:30:24
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/StartupDbInit.java`

### 62. 增加VIP User Table欄位
- **Commit ID**: `fdbfe0b4f9093d57e14c72f6c960d00acce210bf`
- **作者**: wayne
- **日期**: 2016-10-05 15:34:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`

### 63. WEB表單設計師新增欄位
- **Commit ID**: `fb5ee8307e942a117ab80ebdd1902aedc79bbe20`
- **作者**: Gaspard
- **日期**: 2016-10-05 14:30:04
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 64. 組織設計師效能優化
- **Commit ID**: `b483a0507527043858b2353dc7ea31003ed025e6`
- **作者**: WenCheng
- **日期**: 2016-10-04 09:10:37
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/ManagerSelectorDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/ManagerSelectorDialogController.java`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/ManagerSelectorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/ManagerSelectorTableController.java`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/ManagerSelectorUserPagingPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/mainframe/FunctionsPagingPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/ManagerSelectorDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/ManagerSelectorDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/ManagerSelectorDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/ManagerSelectorDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/ManagerSelectorDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`

### 65. 組織設計師效能優化
- **Commit ID**: `0465256c72709f6e1b1fa3eec4cbdeb179d20511`
- **作者**: WenCheng
- **日期**: 2016-09-23 14:13:17
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/UserCompanyEditor.java`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/mainframe/FunctionsPagingPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/mainframe/OrgMainFrame.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/mainframe/SearchPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/OrgTreeController.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/AbstractOrgUnitNode.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/DepartmentNode.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/org_tree/node/ProjectNode.java`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/images/paging/arrow_black_first.png`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/images/paging/arrow_black_last.png`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/images/paging/arrow_black_next.png`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/images/paging/arrow_black_pre.png`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/src/images/paging/arrow_black_refresh.png`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UnitFunctionListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 66. APP新UI:微信登入導頁錯誤修正
- **Commit ID**: `d571e084548b431e45eba1c877e4f94681f062ca`
- **作者**: Joe
- **日期**: 2016-10-04 17:28:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`

### 67. APP 新UI 公告功能修改
- **Commit ID**: `b1fc4adfa2cca263154557db22fcc0770f43b15e`
- **作者**: Joe
- **日期**: 2016-10-04 14:55:48
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/data/AnnouncementOADataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AnnouncementManageAccessor.java`

### 68. APP新UI 更改待辦:附件部分code
- **Commit ID**: `dd3040f98f8e109bf39ba61be3a3aa6aa0ec6191`
- **作者**: Joe
- **日期**: 2016-10-04 09:53:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`

### 69. [S]報表自定義上傳及關連設定增加新增時間
- **Commit ID**: `09ced066c512fd34e210fbce0c7f82e43598f49f`
- **作者**: yylee1123
- **日期**: 2016-10-03 18:07:18
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/report/ReportDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageCustomReportAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 70. S00-20160907001[內部]從文件總管開啟檔案時，預設為使用者wf主機對應的預設文件主機
- **Commit ID**: `175d9d8c367987004534e000d111e5e0a68ed858`
- **作者**: LALA
- **日期**: 2016-10-03 17:28:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`

### 71. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `2075ab5d3c38fbb5a44aa8f03da6381dcd79b491`
- **作者**: jd
- **日期**: 2016-10-03 16:34:11
- **變更檔案數量**: 0

### 72. BPM公告系统
- **Commit ID**: `fcdde785108c9cbfea157afa3b2452521c5528b6`
- **作者**: jd
- **日期**: 2016-10-03 16:29:25
- **變更檔案數量**: 26
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AnnouncementManageDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/announcement/Announcement.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/announcement/AnnouncementAttachment.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/announcement/AnnouncementEmergency.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/announcement/AnnouncementRecords.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/announcement/AnnouncementType.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementAttachmentDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementEmergencyDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementListDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementReadsDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/AnnouncementManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/AnnouncementManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/data/AnnouncementDataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/data/AnnouncementOADataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/search/AnnouncementAttachmentSearchKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/search/AnnouncementSearchKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/search/SearchKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AnnouncementManageAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomJsLib/Publisher.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`

### 73. 修正密碼政策的ForceChangePassword不設定時登入會出現異常的問題
- **Commit ID**: `63f0194bd49e1a286e501eb0d9e97630b2d7403e`
- **作者**: loren
- **日期**: 2016-10-03 16:21:41
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaEJB.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 74. [Q00-20161003001]修正更新表單設計師後，舊表單無法列印表單的問題
- **Commit ID**: `dd7e582c364efbde8c95b98712ef728ac1a2f0c9`
- **作者**: yylee1123
- **日期**: 2016-10-03 16:13:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 75. [S]管理程式權限設定增加更新人員OID、時間
- **Commit ID**: `78ff6bd5749089272570bdb9639a537213bf3fcb`
- **作者**: yylee1123
- **日期**: 2016-09-30 18:31:16
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramAccessRight.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 76. 修改密碼政策
- **Commit ID**: `4ea26fcae713a034b17a676919180ac065a667cc`
- **作者**: loren
- **日期**: 2016-09-30 17:27:55
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaEJB.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 77. Q00-20160930003 修正後程式微調
- **Commit ID**: `e8f6a8c8470dce7659b111d3ee8a03b542998504`
- **作者**: jerry1218
- **日期**: 2016-09-30 17:20:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 78. Q00-20160930003 修正如果[使用舊客授權序號]且[由SecurityHandlerBean.register()進行登入]時 , 就算授權不足依然可以登入
- **Commit ID**: `92115a812e005cdb6f97aaeb9e3b9da814440b3a`
- **作者**: jerry1218
- **日期**: 2016-09-30 16:37:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`

### 79. Q00-20160929001[內部]下載檔案javaScript錯誤修正
- **Commit ID**: `802e3b2c8faa58fca2175e2f252d0487c1c9af10`
- **作者**: LALA
- **日期**: 2016-09-30 13:43:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`

### 80. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `77f3d91a6ec2d6cc888a2f5e38f9e9cb8e44cffb`
- **作者**: yylee1123
- **日期**: 2016-09-30 11:39:24
- **變更檔案數量**: 0

### 81. [S]在log記錄啟動流程測試模式的使用者、模擬對象
- **Commit ID**: `7c65b6cbf3dbcdd9ddbdc3312b65e651afcc875c`
- **作者**: yylee1123
- **日期**: 2016-09-30 11:38:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`

### 82. [S00-20160727004][內部]統一轉派畫面的文字與順序，以避免客戶困惑
- **Commit ID**: `2fef4400cdf5ccb77bd8c6349723bf47e7ebc2e8`
- **作者**: LALA
- **日期**: 2016-09-30 10:25:13
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 83. Q00-20160922002[內部]excel轉pdf時，頁籤過多會轉檔失敗
- **Commit ID**: `ba663cd8d633fe57386c58721b4e67689e1339c4`
- **作者**: LALA
- **日期**: 2016-09-30 09:58:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java`

### 84. C01-20160919005[欣興]PDFViewer連續點擊PrintScreen可截圖
- **Commit ID**: `70dc97abcee7f370119bfb8ef5aeceb2621bda1f`
- **作者**: LALA
- **日期**: 2016-09-30 09:56:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/src/com/dsc/nana/user_interface/pdf/efgp_pdfViewer/controller/SecurityManager.java`

### 85. S00-20160907001[內部]從文件總管開啟檔案時，預設為使用者wf主機對應的預設文件主機
- **Commit ID**: `469b164d2f154e98d1f8a8b8ef227b6476db1ba6`
- **作者**: LALA
- **日期**: 2016-09-30 09:53:25
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`

### 86. A00-20160910003[上品綜合工]簽核流設計師在沒有任何流程分類或流程時，有admin權限的使用者無法新增流程分類及流程
- **Commit ID**: `c0e4fb87d31526624011881c77c906ded76fd526`
- **作者**: LALA
- **日期**: 2016-09-30 09:41:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`

### 87. web表單設計師
- **Commit ID**: `027be2fecdc35981f2b8351f1fdcab238d600041`
- **作者**: yylee1123
- **日期**: 2016-09-29 17:08:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/favorities/FavoriteMenu.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`

### 88. 修正nextVersion_updateSQL_SQLServer.sql錯誤 , [
- **Commit ID**: `42e1f3e0a463e0c591cb5494a0df1c19f20a5dc3`
- **作者**:  chr(38) ||]改回[&]|2016-09-29 15:06:04|jerry1218
- **日期**: 
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 89. 功能新增-JBOSS啟動時,由ContextManager主動清除OJB_DLIST,OJB_DLIST_ENTRIES,OJB_DSET,OJB_DSET_ENTRIES這四個table
- **Commit ID**: `e0a48ed127862b878e2fdab48aef3fd6ff2f1627`
- **作者**: jerry1218
- **日期**: 2016-09-29 14:51:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 90. Q00-20160929001[內部]非整合crm不需要crmModal
- **Commit ID**: `1b912d9bed14494b9af00d3a4f8a341ffbdd44d0`
- **作者**: LALA
- **日期**: 2016-09-29 14:29:05
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_ORACLE9i-2.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_SQLServer2005.sql`

### 91. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `c7b783084f86e7dc6847194c12d205e754e0be77`
- **作者**: wayne
- **日期**: 2016-09-29 14:03:22
- **變更檔案數量**: 0

### 92. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `5272baa4d9442e14b2ea24e50f7db92f2a698c22`
- **作者**: yylee1123
- **日期**: 2016-09-29 14:02:11
- **變更檔案數量**: 0

### 93. [S]移除NaNaWeb.properties的nana.isoModule.enable，因命名與功能不符；開放管理程式權限設定的功能
- **Commit ID**: `8d9ed2c6c0e800e585de05f8f398930ce8c082eb`
- **作者**: yylee1123
- **日期**: 2016-09-29 14:01:26
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/conf/NaNaWeb.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/VamIntegrationEFGP.java`

### 94. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a240c4ea8ed1a1b035aff09c03706675bc26774f`
- **作者**: wayne
- **日期**: 2016-09-29 14:01:08
- **變更檔案數量**: 0

### 95. 增加VIPUser功能 - 補上多語系
- **Commit ID**: `014ad2570a1aac2ffdcfb3c0a67d8aa369a70071`
- **作者**: wayne
- **日期**: 2016-09-29 13:57:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`

### 96. (BUG修正 , AJAX及Action抓Globals.LOCALE_KEY)修改T100整合-表單欄位同步時 , 以設計師的Locale為主
- **Commit ID**: `5a843e17dc1d9d19d65f0bbcff21913de7910444`
- **作者**: jerry1218
- **日期**: 2016-09-29 12:02:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 97. (BUG修正)修改T100整合-表單欄位同步時 , 以設計師的Locale為主
- **Commit ID**: `4d15d982f783122f7357d5e2acc2c4735aae80e8`
- **作者**: jerry1218
- **日期**: 2016-09-29 11:56:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`

### 98. [S]管理程式權限設定、模組程式維護、文管權限管理介面修改
- **Commit ID**: `6549120e00e4031f2f63e81497db1bce5f6af0db`
- **作者**: yylee1123
- **日期**: 2016-09-26 18:16:00
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/favorities/FavoriteMenu.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageISOAuthorityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/RoleAccessControl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/favorities/FavoritiesMenuViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageISOAuthority/SetISOAuthority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_SQLServer2005.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@iso/update/iso5.6.1.1_updateSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@iso/update/isoNextVersion_updateSQL.sql`

### 99. APP新UI:補上遺漏的多語系修改
- **Commit ID**: `e8f6568a5431dc8fbfdb68475d81e1a55cafc951`
- **作者**: Joe
- **日期**: 2016-09-26 16:14:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`

### 100. 修正T100整合表單form檔 : 工單一般退料維護作業(asft323).form 客戶准入及變更作業(axmt800).form 會員卡種申請單(ammt320).form 實地盤點計畫維護作業(aint820).form
- **Commit ID**: `d809d886bde8140677b4ac91f18bf17d8745d658`
- **作者**: jerry1218
- **日期**: 2016-09-26 15:37:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\256\242\346\210\266\345\207\206\345\205\245\345\217\212\350\256\212\346\233\264\344\275\234\346\245\255(axmt800).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\257\246\345\234\260\347\233\244\351\273\236\350\250\210\347\225\253\347\266\255\350\255\267\344\275\234\346\245\255(aint820).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\344\270\200\350\210\254\351\200\200\346\226\231\347\266\255\350\255\267\344\275\234\346\245\255(asft323).form"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\234\203\345\223\241\345\215\241\347\250\256\347\224\263\350\253\213\345\226\256(ammt320).form"`

### 101. 新增整合表單form檔 : 工單維護作業(asft300).form 供應商績效評核定性評分單(apmt811).form 供應商績效評核綜合得分調整單(apmt814).form 門店資源協議申請作業(artt230).form 促銷談判申請(aprt310).form 專櫃合約異動申請(astt401).form 專櫃新商品引進維護作業(artt407).form 採購合約變更單維護作業(apmt490).form 採購預付單維護作業(aapt310).form 應付匯款開立作業(anmt460).form 應收帳款憑單(axrt300).form 轉帳傳票(aglt310).form 雜項待抵單維護作業(axrt341).form
- **Commit ID**: `eddc05544eb2117a4d6f2cb970d6efcc961344cc`
- **作者**: jerry1218
- **日期**: 2016-09-26 15:36:29
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\347\270\276\346\225\210\350\251\225\346\240\270\345\256\232\346\200\247\350\251\225\345\210\206\345\226\256(apmt811).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\276\233\346\207\211\345\225\206\347\270\276\346\225\210\350\251\225\346\240\270\347\266\234\345\220\210\345\276\227\345\210\206\350\252\277\346\225\264\345\226\256(apmt814).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\344\277\203\351\212\267\350\253\207\345\210\244\347\224\263\350\253\213(aprt310).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\260\210\346\253\203\345\220\210\347\264\204\347\225\260\345\213\225\347\224\263\350\253\213(astt401).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\260\210\346\253\203\346\226\260\345\225\206\345\223\201\345\274\225\351\200\262\347\266\255\350\255\267\344\275\234\346\245\255(artt407).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\345\267\245\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(asft300).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\207\211\344\273\230\345\214\257\346\254\276\351\226\213\347\253\213\344\275\234\346\245\255(anmt460).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\207\211\346\224\266\345\270\263\346\254\276\346\206\221\345\226\256(axrt300).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\345\220\210\347\264\204\350\256\212\346\233\264\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(apmt490).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\346\216\241\350\263\274\351\240\220\344\273\230\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(aapt310).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\350\275\211\345\270\263\345\202\263\347\245\250(aglt310).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\226\200\345\272\227\350\263\207\346\272\220\345\215\224\350\255\260\347\224\263\350\253\213\344\275\234\346\245\255(artt230).form"`
  - ➕ **新增**: `"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/\351\233\234\351\240\205\345\276\205\346\212\265\345\226\256\347\266\255\350\255\267\344\275\234\346\245\255(axrt341).form"`

### 102. Q00-20160926001 修正T100整合-待簽核事項中點擊連結至BPM中簽核會出現[主機連線異常,請重新登入]的錯誤
- **Commit ID**: `3ddc743d590ee932f92e122d23bc8ef8abad767c`
- **作者**: jerry1218
- **日期**: 2016-09-26 15:20:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 103. 修正 EFGP 整合cross 時 mcloud 無法推播議題
- **Commit ID**: `d81c8b90264389b748f5eeb042fbcd124e3fbde0`
- **作者**: wayne
- **日期**: 2016-09-26 15:11:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/cross/CrossInvokeServiceXMLUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mcloud/McloudMgr.java`

### 104. APP新UI議題
- **Commit ID**: `7995e5508200c1f284154b356dc4a1f17b3a99bd`
- **作者**: Joe
- **日期**: 2016-09-26 11:56:08
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSettingLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 105. 修改T100整合-表單欄位同步時 , 以設計師的Locale為主
- **Commit ID**: `2f60b51493a1a297ed0136cb6e760d7127b2ad5e`
- **作者**: jerry1218
- **日期**: 2016-09-23 17:48:07
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/FormDefinitionManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/SystemController.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/util/NewTipTopFormMerge.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/util/NewTiptopFormTransfer.java`

### 106. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `8103e4cd2905498e358f0e236197934cd79f1363`
- **作者**: wayne
- **日期**: 2016-09-23 17:22:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 107. [S]增加VIPUser功能
- **Commit ID**: `dd9381a3eadfe77e6151ecc4e5e7dc40ddb00839`
- **作者**: wayne
- **日期**: 2016-09-23 16:48:11
- **變更檔案數量**: 30
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/favorities/FavoriteMenu.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/license/VipUsers.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/resource/EmployeeEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/VipUserCacheSingletonMap.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ConnectedUserInfoListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/OnlineUserAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/RoleAccessControl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/favorities/FavoritiesMenuViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/userProfile/ConnectedUserProfile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageOnlineUser-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/VipUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 108. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `2e8d163c251fa46728920baaf7cc7aee968f9e79`
- **作者**: Joe
- **日期**: 2016-09-23 15:40:37
- **變更檔案數量**: 0

### 109. APP新UI議題
- **Commit ID**: `876973c9a66df4d2477db1c4044162a0743a50b2`
- **作者**: Joe
- **日期**: 2016-09-23 15:39:35
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSettingLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 110. A00-20160910003[上品綜合工]簽核流設計師在沒有任何流程分類或流程時，有admin權限的使用者無法新增流程分類及流程
- **Commit ID**: `e18852991a075b1324c6209fea87d251d838212d`
- **作者**: LALA
- **日期**: 2016-09-23 13:20:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/tree/cmtree/CMTreeTableModel.java`

### 111. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `934a54bc22b61f2f51edd920890993cc0ece54db`
- **作者**: Joe
- **日期**: 2016-09-23 11:01:48
- **變更檔案數量**: 0

### 112. 修正UserListReader SQL語法錯誤
- **Commit ID**: `9c6b4f31665d12598f624258ab61f3bfc08d9440`
- **作者**: Joe
- **日期**: 2016-09-23 11:01:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 113. [內部] Q00-20160923001 2016.09.23 Modify 人員開窗報錯 by 駿緯.
- **Commit ID**: `d829f4042b183066fd8f99a42886059e9120aa6e`
- **作者**: Noah.Jhuang
- **日期**: 2016-09-23 10:28:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`

### 114. S00-20160907001[內部]從文件總管開啟檔案時，預設為使用者wf主機對應的預設文件主機
- **Commit ID**: `004eeb73c8632b299544537553ac1a5bf9bd1dac`
- **作者**: LALA
- **日期**: 2016-09-22 16:44:20
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`

### 115. C01-20160919005[欣興]PDFViewer連續點擊PrintScreen可截圖
- **Commit ID**: `a3a10583e4aea37e42a2efb4fe3f64902d7eb6eb`
- **作者**: LALA
- **日期**: 2016-09-22 16:11:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/src/com/dsc/nana/user_interface/pdf/efgp_pdfViewer/controller/SecurityManager.java`

### 116. Q00-20160922002[內部]excel轉pdf時，頁籤過多會轉檔失敗
- **Commit ID**: `a4e49b80b9d6714985c5b6db5d9dbbf2fc567edb`
- **作者**: LALA
- **日期**: 2016-09-22 15:57:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java`

### 117. 修正多個使用者帳號取得BadgeNumber
- **Commit ID**: `e78a578ac307ebb25ff45a28a63721e937fa53a8`
- **作者**: wayne
- **日期**: 2016-09-22 15:42:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`

### 118. [S00-20160727004][內部]統一轉派畫面的文字與順序，以避免客戶困惑
- **Commit ID**: `18d6f8a3922c0e51059059399c0ad71f2838007b`
- **作者**: LALA
- **日期**: 2016-09-22 15:27:26
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`

### 119. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `5a4529a66f4ba0a220d86666796c4b91afb0de22`
- **作者**: wayne
- **日期**: 2016-09-22 14:57:20
- **變更檔案數量**: 0

### 120. ISO報表支援簡體中文
- **Commit ID**: `1fc3d46361fd8d04fbd6fcbd4072445ab0fd78e2`
- **作者**: wayne
- **日期**: 2016-09-22 14:46:11
- **變更檔案數量**: 17
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/lib/Poi/poi-3.2-FINAL-20081019.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/Poi/poi-3.5-FINAL-20090928.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISOChangeFileListExcelServlet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISODocumentsListExcelServlet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISOEffectInvalidListExcelServlet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISOFileQueryListExcelServlet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISOListExcelServlet.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/create_excel/CreateISOReleaseDocListExcelServlet.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISODocumentsList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOEffectInvalidList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOReleaseDocList.jsp`

### 121. [Q]修正追蹤流程的取回重瓣按鈕異常
- **Commit ID**: `ca8f2a548b1da3e8641b2490f1d35f3e58cabffa`
- **作者**: wayne
- **日期**: 2016-09-22 14:20:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 122. APP新UI 議題修正
- **Commit ID**: `7b9b84e097031647504e77825b8fc2ddc075f744`
- **作者**: Jack
- **日期**: 2016-09-22 14:19:33
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 123. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `6156e730ac552e75c4deb23f238a32e8ccbd7ded`
- **作者**: wayne
- **日期**: 2016-09-22 14:08:11
- **變更檔案數量**: 0

### 124. APP新UI 議題修正
- **Commit ID**: `6ff45e144c8a8b183d767ec48c7fad24d4e2bad3`
- **作者**: Jack
- **日期**: 2016-09-22 14:02:08
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`

### 125. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `5c1ae4a07ec5c0e2344a0505bf6a2ba940b04536`
- **作者**: wayne
- **日期**: 2016-09-22 14:00:43
- **變更檔案數量**: 0

### 126. APP新UI議題修正
- **Commit ID**: `d57b0c0c26df76122da43b28122c02cb4c8ef5f4`
- **作者**: Joe
- **日期**: 2016-09-22 11:38:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`

### 127. APP新UI議題修正
- **Commit ID**: `dac571823b57cbaee5ee92c19cf052e9b405eb71`
- **作者**: Joe
- **日期**: 2016-09-22 10:54:04
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 128. APP 新UI議題修正
- **Commit ID**: `c7ab6276ff7bb312712465066415e26b3584ba7a`
- **作者**: Jack
- **日期**: 2016-09-22 10:12:24
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenu.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 129. APP 新UI議題修正
- **Commit ID**: `c95d371ddfa23cc824eed9fa228ca10fa8a40b80`
- **作者**: Jack
- **日期**: 2016-09-22 10:01:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/sign_.png`

### 130. 合併分支develop_MergeRequest into develop
- **Commit ID**: `8ec556b14d7825256cd227e59ddcb4099d5b0f48`
- **作者**: loren
- **日期**: 2016-09-21 17:48:26
- **變更檔案數量**: 0

### 131. 合併分支VSS2Git_BPM into develop_MergeRequest
- **Commit ID**: `fdd9bcac8777c35e77271e89235ef3e8d57f21b9`
- **作者**: loren
- **日期**: 2016-09-21 17:30:17
- **變更檔案數量**: 56
- **檔案變更詳細**:
  - 📄 **修改**: `.project`
  - 📄 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📄 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocCatergory.java`
  - 📄 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocCmItem.java`
  - 📄 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocTypeLevel.java`
  - 📄 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocument.java`
  - 📄 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISOFile.java`
  - 📄 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📄 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📄 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/RightPanel.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/hibernate/ISOPaperRecorDaoImpl.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryLocal.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudRejectableListReader.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudTraceProcessListReader.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📄 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`
  - 📄 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocTypeAction.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracingLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItemLib.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileKickStart.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BPMProcessTracing.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmTaskManage.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmWorkItem.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/Dialog.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/themecolor/main.css`

### 132. 修正組織內員工，如要從使用者基本資料中的「設定所屬單位」分頁，增加其「部門職務資料」在操作選取「所屬單位」(內有大量任職人員)項目後，要帶回於畫面上欄位時，處理時間過久。
- **Commit ID**: `3b449a8e76a0435562ae76df2d9f9f6fd480c8bc`
- **作者**: jerry1218
- **日期**: 2016-09-21 11:04:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/SearchDialog.java`

### 133. 移除JSFilter
- **Commit ID**: `9e51376ace1b70a37640b418593d76166b9da2bf`
- **作者**: jerry1218
- **日期**: 2016-09-21 09:23:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/JSFilter.java`

### 134. Merge remote-tracking branch 'origin/develop_5.6.1.1' into develop
- **Commit ID**: `5ce45aafb7d34a4880971a1bb15def7cbe0705d5`
- **作者**: loren
- **日期**: 2016-09-20 16:10:14
- **變更檔案數量**: 0

### 135. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `8ec75647bfae5dd750c5ab9b9b0e13f607f29126`
- **作者**: wayne
- **日期**: 2016-09-20 15:51:34
- **變更檔案數量**: 0

### 136. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `5858a15eb4a5aa194cf56a9afef874a049c7f70b`
- **作者**: Gaspard
- **日期**: 2016-09-20 12:09:51
- **變更檔案數量**: 0

### 137. 取得流程包裹效能優化
- **Commit ID**: `52e88e8983011260b6624b2516dd02b41e82db23`
- **作者**: Gaspard
- **日期**: 2016-09-20 12:09:16
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/ProPkgCacheSingletonMap.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`

### 138. [Q00-20160919001]修正重覆的@ejb.ejb-ref
- **Commit ID**: `2a15842fba6f8dc26875fe18bb650be129cc9cb8`
- **作者**: yylee1123
- **日期**: 2016-09-19 18:17:04
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 139. APP新UI議題修改
- **Commit ID**: `8774072f4ab7652fc31dcd86c6d556fc1889737f`
- **作者**: Joe
- **日期**: 2016-09-19 17:58:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`

### 140. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `ec84239f6f1f17a9617c9a480294b84d39e31b0c`
- **作者**: Jack
- **日期**: 2016-09-19 17:27:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`

### 141. BPM APP 新UI議題修正
- **Commit ID**: `ddacd4a9f2ccbe9df767029cf6b8eca6a9457dd7`
- **作者**: Jack
- **日期**: 2016-09-19 17:24:55
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/normal.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/sign.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 142. 組織設計師效能優化
- **Commit ID**: `60f0ace1d2bab64ad2a3eeccaead63497a81d9fb`
- **作者**: jerry1218
- **日期**: 2016-09-19 15:38:12
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/OrgDesigner.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/AddUserToUnitDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/OrgUnitEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/search/ManagerSelectorDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`

### 143. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `06d50da537f9d1e32b9048a9c9cd55cceaa1cd3c`
- **作者**: yylee1123
- **日期**: 2016-09-19 14:45:16
- **變更檔案數量**: 0

### 144. [S]文件總管新增文件、ISO文件新增申請、ISO文件變更申請，若有設定多文件主機，會立即佈署文件
- **Commit ID**: `76392c48bff4b7b2e655f4f2ed743bb3dfeaf2ec`
- **作者**: yylee1123
- **日期**: 2016-09-19 14:38:29
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISODocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/WorkflowServerManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/CreateDocumentAction.java`

### 145. APP新ui 新增撥打電話功能
- **Commit ID**: `d8d72a881ef69fcd05e6d84d9133d2e096a92879`
- **作者**: Joe
- **日期**: 2016-09-19 14:34:41
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForListDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileContactUserAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`

### 146. BPM APP新UI相關程式
- **Commit ID**: `636f8f99fe6d2f9c0cec448900142c7716db4d1e`
- **作者**: Jack
- **日期**: 2016-09-19 10:48:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppSetting.js`

### 147. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `a6e75e77eefe3cf607e5af2327c3999679713a49`
- **作者**: wayne
- **日期**: 2016-09-14 15:53:36
- **變更檔案數量**: 0

### 148. 20160914 調整sap無法寫入Grid Data問題
- **Commit ID**: `2e6cf283f6b9e68e0afa78da35523a0cf13063ab`
- **作者**: wayne
- **日期**: 2016-09-14 15:22:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`

### 149. 無議題編號
- **Commit ID**: `c972e365d23b0bc8894084d034f5c9c3dc8ace5e`
- **作者**: arielshih
- **日期**: 2016-09-14 15:10:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5602.xls`

### 150. [Q]發起流程時因session覆蓋導致發起錯誤流程-防呆(從追蹤流程重發流程會報錯)
- **Commit ID**: `0fc3adb04091da54c77a1e188eb70e890b43b4a9`
- **作者**: wayne
- **日期**: 2016-09-14 11:01:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 151. RsrcBundle修正
- **Commit ID**: `dbd618385b3d78125268fbb1d374f6f8ff535649`
- **作者**: loren
- **日期**: 2016-09-14 08:37:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5602.xls`

### 152. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `0893b7dc210660d9c4446ab82bfac7df9a6cdeb5`
- **作者**: Joe
- **日期**: 2016-09-13 15:05:45
- **變更檔案數量**: 0

### 153. BPM APP NEW UI 第一階段
- **Commit ID**: `0120c3016fb415e16da287622bb8a0cd5b1532f8`
- **作者**: Joe
- **日期**: 2016-09-13 14:37:49
- **變更檔案數量**: 97
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AnnouncementManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementAttachmentDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementEmergencyDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementListDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/announcement/AnnouncementTypeDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaIntSys.properties`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/AnnouncementManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/AnnouncementManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/AnnouncementManagerHome.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/AnnouncementManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/AnnouncementManagerLocalHome.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/announcement/data/AnnouncementOADataManageTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileCommonProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/search/AnnouncementAttachmentSearchKey.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/search/AnnouncementSearchKey.java`
  - ➕ **新增**: `3.Implementation/subproject/system/src/com/dsc/nana/util/search/SearchKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AnnouncementManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileContactUserAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/MobileProcessPackageVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContact.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContactLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNoticeLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSetting.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSettingLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDoLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenuLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppCommon.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppContact.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppNotice.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppToDo.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppWorkMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/Contact.cc`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/Notice.cc`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/ToDo.cc`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/WorkMenu.cc`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppContact.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/BpmAppWorkMenu.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/announcement/announce_default.jpg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/announcement/announce_default_slide.jpg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/announce.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/announce_dot.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/attach.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/cancel.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/dot.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/favorite.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/important.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/invoke_process.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/back.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/backto.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/backto_.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/contact.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/contact_.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/notice.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/notice.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/setting.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/setting_.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/todo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/todo_.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/todo_dot.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/work.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/menu/work_.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/newLogo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/newNoticeItem.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/newTaskManage.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/newTraceProcess.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/newWorkItem.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/notfavorite.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/process.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/running.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/save.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/search.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/sign.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/sign_click.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/sign_over.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/todo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/trace_process.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/common/unread.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/contact/default_info.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/contact/email.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/images/contact/phone.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/bpmApp/mobile-UI-common.css`

### 154. 移除不需在Git控管的應用程式
- **Commit ID**: `819979a502a74050ec2e4bd9e0d67daa78d53a6d`
- **作者**: loren
- **日期**: 2016-09-13 13:57:41
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/autocad-converter-setup/D2P.exe`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/autocad-converter-setup/DWG2PDF1.exe`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/pdf-converter-setup/EzjcomProfessionalInstaller.exe`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/pdf-converter-setup/Jbepprint.dll`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/pdf-converter-setup/Jbepproc.dll`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/pdf-converter-setup/PrinterTest.exe`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/copyfiles/@iso/pdf-converter-setup/easypdfsdk51_setup.msi`

### 155. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `b74e09ac9707415870b532f4077d6d4e459d0b75`
- **作者**: loren
- **日期**: 2016-09-13 13:54:52
- **變更檔案數量**: 0

### 156. 移除updateMCloud_MSSQL/Oralce.sql
- **Commit ID**: `7c6b17517cc87a3135709531075ffdb8c8d55f82`
- **作者**: loren
- **日期**: 2016-09-13 13:54:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mcloud/update/updateMCloud_MSSQL.sql`
  - ❌ **刪除**: `6.Deployment/DeploymentPlan/db/@mcloud/update/updateMCloud_Oracle.sql`

### 157. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `9bd7fea834969b04bddb805e840153169cd258a3`
- **作者**: Noah.Jhuang
- **日期**: 2016-09-13 13:49:14
- **變更檔案數量**: 0

### 158. [立訊精密] C01-20160808003 2016.09.12 Modify SQL 效能優化 by 駿緯.
- **Commit ID**: `3905d9aa29754b45306dc65f9182ee6857ad6fba`
- **作者**: Noah.Jhuang
- **日期**: 2016-09-13 13:47:39
- **變更檔案數量**: 12
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FavoritiesProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/NoticeWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PerformableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPkgCategoryListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/UserListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 159. A00-20160823001[建邦]V6簽核流設計師的通知管理員，將原本V5有的變數加回變數清單
- **Commit ID**: `1718cedd01bd217ce1807fcc1ebe0d4afdf2c7b5`
- **作者**: LALA
- **日期**: 2016-09-12 15:32:45
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/common/PatternEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/notification/NotificationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/processpackage/VariableNamesList.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationManager.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationManager_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationManager_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationManager_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/notification/NotificationManager_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_TW.properties`

### 160. A00-20160910002[寶成]將表單設計師表預設表單Frames的容器長度調整為8000
- **Commit ID**: `b8ad64f440e183bda18439d523a89fb24c251793`
- **作者**: LALA
- **日期**: 2016-09-12 14:58:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/RightPanel.java`

### 161. Merge branch 'develop' of http://************/BPM_Group/BPM.git into develop
- **Commit ID**: `7961ffc275d6d286543d7df6a97f40b9758f272f`
- **作者**: LALA
- **日期**: 2016-09-12 14:44:54
- **變更檔案數量**: 0

### 162. C01-20160831001[宗盈]V6版本的簽核流程設計師，在條件運算式抓不到Input-Multi-Dialog
- **Commit ID**: `99bb31941e074ad1aeff7ff0dd92c6b9d7ceefec`
- **作者**: LALA
- **日期**: 2016-09-12 14:41:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/chooser/FormElementFilter.java`

### 163. T100組織同步增加可同步的項目(T100版本：v1.1.04以後)
- **Commit ID**: `d10258daddd8ff9e0bd47dc79cbce1a3f10ef327`
- **作者**: loren
- **日期**: 2016-09-12 14:29:48
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - ❌ **刪除**: `3.Implementation/subproject/service/NaNa/conf/syncorg/SyncStep_config_forT100.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/syncorg/SyncTable.properties`
  - ❌ **刪除**: `3.Implementation/subproject/service/NaNa/conf/syncorg/sync-def_forT100.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/NewTipTopSyncOrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/AppProperties.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/cfg/SyncTableConstants.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/NewTiptop_Organization.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/NewTiptop_TitleDefinition.java`
  - ❌ **刪除**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/NewTiptop_User.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/T100_FuncLevel.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/T100_Organization.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/T100_PartJob.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/T100_TitleDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/T100_UnitLevel.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/domain/newtiptop/T100_User.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/etl/SyncOrgPhaseIBean.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@syncorg/create/T100_SyncTables_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@syncorg/create/T100_SyncTables_Oracle.sql`

### 164. 修正ISO的updateSQL
- **Commit ID**: `c54b1601f08535672f6b8d098deafa124b4fd931`
- **作者**: yylee1123
- **日期**: 2016-09-12 13:03:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/update/isoNextVersion_updateSQL.sql`

### 165. web表單設計師
- **Commit ID**: `39c6d18858511782f6b3244416e768f72c592100`
- **作者**: yylee1123
- **日期**: 2016-09-10 17:11:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/formExplorer.css`

### 166. Merge branch 'release_5.6.0.3' into 'develop'
- **Commit ID**: `c326805fa1303f50e70b0be0a191c319ffc23352`
- **作者**: Administrator
- **日期**: 2016-09-10 17:00:01
- **變更檔案數量**: 0

### 167. web表單設計師
- **Commit ID**: `98a142a8c7f5ab537e46495244db2b2b1c51876d`
- **作者**: yylee1123
- **日期**: 2016-09-10 15:27:56
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 168. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `5de031c38dd894c840221ef5456dbcc721fd54ee`
- **作者**: yylee1123
- **日期**: 2016-09-08 16:50:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 169. web表單設計師
- **Commit ID**: `72aa5a14026588e00e06573599d7d1f71a4f5696`
- **作者**: yylee1123
- **日期**: 2016-09-08 16:46:50
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`

### 170. Web表單設計師
- **Commit ID**: `ab6d89a5643817ac8341fd11aa397dbf3073390b`
- **作者**: Gaspard
- **日期**: 2016-09-08 10:12:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 171. Web表單設計師
- **Commit ID**: `2827efd0822a49673a70429b0a8f983b39aab2c9`
- **作者**: Gaspard
- **日期**: 2016-09-07 17:19:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 172. Web表單設計師
- **Commit ID**: `945d97791a893310706f4325734ae03aa6fe6302`
- **作者**: Gaspard
- **日期**: 2016-09-06 17:53:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 173. Web表單設計師
- **Commit ID**: `6eeed0761f1620774d9b6b0d56ec8257c5f4b658`
- **作者**: Gaspard
- **日期**: 2016-09-06 15:57:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js`

### 174. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `1ca90d855305739e97ce854534ebab6a94b3633c`
- **作者**: Gaspard
- **日期**: 2016-09-06 15:17:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 175. Web表單設計師
- **Commit ID**: `3c1df52e027b91bb18678e1ccb75ba6837b663e4`
- **作者**: Gaspard
- **日期**: 2016-09-06 15:14:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 176. Web表單設計師
- **Commit ID**: `28d6d574fd0e701499a9e19da93b8de251b4d730`
- **作者**: Gaspard
- **日期**: 2016-09-06 15:13:25
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/AppCustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/CustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 177. web表單設計師
- **Commit ID**: `9bf06c51e354286b67af81e71377d0c5a650fef4`
- **作者**: yylee1123
- **日期**: 2016-09-05 18:27:25
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`

### 178. Web表單設計師
- **Commit ID**: `2db3357d0de2ad42cc51e90e8fc86046f1a138f8`
- **作者**: Gaspard
- **日期**: 2016-09-02 13:53:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 179. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `5e0053477bdeb1ae55602ca32f8d1037dc4f9f85`
- **作者**: yylee1123
- **日期**: 2016-09-02 10:45:29
- **變更檔案數量**: 0

### 180. web表單設計師
- **Commit ID**: `cda04e715e38278ac89a8eb473d6a361045e1748`
- **作者**: yylee1123
- **日期**: 2016-09-02 10:38:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 181. Eclipse開發環境調整，讓設定新版本Eclipse或JDK時可以更方便
- **Commit ID**: `f4f762370d32f528efa1a0a030fe7f0c9ef79299`
- **作者**: loren
- **日期**: 2016-09-02 09:53:30
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/domain/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/dto/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/efgp-pdfViewer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/persistence/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/service/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/system/.settings/org.eclipse.jdt.core.prefs`
  - 📝 **修改**: `3.Implementation/subproject/webapp/.project`

### 182. Eclipse開發環境調整，讓設定新版本Eclipse或JDK時可以更方便
- **Commit ID**: `053856bfb4d679b829a832ce4c0b746974a03beb`
- **作者**: loren
- **日期**: 2016-09-02 09:43:25
- **變更檔案數量**: 19
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/domain/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/dto/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/efgp-pdfViewer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/persistence/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/service/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/.settings/org.eclipse.jdt.core.prefs`
  - ➕ **新增**: `3.Implementation/subproject/system/.settings/org.eclipse.jdt.core.prefs`
  - 📝 **修改**: `3.Implementation/subproject/webapp/.project`

### 183. 增加ISO紙本文件申請單身文件版號(displayVersion)
- **Commit ID**: `0b910ad3bb4d1189e8b9c97687d04ae21e0b6ad6`
- **作者**: yylee1123
- **日期**: 2016-09-01 17:47:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`

### 184. 修改ISOPaperRecord的docDisplayVersion預設值
- **Commit ID**: `6af685705a99cf120d640c490c35026bd83d285c`
- **作者**: yylee1123
- **日期**: 2016-09-01 16:26:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/ISOPaperRecord.java`

### 185. Web表單設計師
- **Commit ID**: `98ac23a7ee92d82bf1d1ed9d7e66d83daea475a8`
- **作者**: Gaspard
- **日期**: 2016-08-31 10:39:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 186. Web表單設計師
- **Commit ID**: `a83c7f43e669c75d06d1220fc96f57773f9990f1`
- **作者**: Gaspard
- **日期**: 2016-08-31 09:30:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 187. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `45cf807e22996d54098a87c20920c7e41ad7cc69`
- **作者**: Gaspard
- **日期**: 2016-08-30 16:00:56
- **變更檔案數量**: 0

### 188. Web表單設計師
- **Commit ID**: `fd5c11b1891d721cb7827ca1eb9d423694a6ed1c`
- **作者**: Gaspard
- **日期**: 2016-08-30 15:59:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 189. Eclipse開發環境加入整個BPM目錄的Project，Resource Filters設定隱藏不顯示的目錄
- **Commit ID**: `61bf06ed99a63536ee7fc099ebb4cee90fb8a440`
- **作者**: loren
- **日期**: 2016-08-30 09:47:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `.project`

### 190. Eclipse開發環境加入整個BPM目錄的Project，Resource Filters設定隱藏不顯示的目錄
- **Commit ID**: `c3b0dfd45ca8c3e5633815d5d37168cb2198713d`
- **作者**: loren
- **日期**: 2016-08-30 09:23:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `.project`

### 191. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `f73d92b3bb2e4056334ab1e61dc58e2a8b5800dd`
- **作者**: Gaspard
- **日期**: 2016-08-29 17:59:17
- **變更檔案數量**: 0

### 192. Web表單設計師
- **Commit ID**: `f865e7a691cd3d3cc345cb6222a26ba789c9bff4`
- **作者**: Gaspard
- **日期**: 2016-08-29 17:56:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 193. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `b24ae1ea84b8bd3132008148241950d85379aaab`
- **作者**: yylee1123
- **日期**: 2016-08-29 17:34:01
- **變更檔案數量**: 0

### 194. web表單設計師
- **Commit ID**: `f758788e908724e0c8a7ed247eb8a71dc14ce1ef`
- **作者**: yylee1123
- **日期**: 2016-08-29 17:32:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 195. Web表單設計師
- **Commit ID**: `7ae9464fa59ec1671a68f70a38505418ea56ba48`
- **作者**: Gaspard
- **日期**: 2016-08-29 16:29:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 196. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `fd0598270129d55fc78cd5cec7e4ac1ac8161e48`
- **作者**: yylee1123
- **日期**: 2016-08-29 10:59:18
- **變更檔案數量**: 0

### 197. web表單設計師
- **Commit ID**: `ffea52bba2534cdbf775193fc112131223cbd3a9`
- **作者**: yylee1123
- **日期**: 2016-08-29 10:56:55
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/JSONConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 198. Web表單設計師
- **Commit ID**: `546eb5133ffc383d1b216468defe3a29afbd86e8`
- **作者**: Gaspard
- **日期**: 2016-08-26 17:16:37
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/JspRegister.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 199. Web表單設計師
- **Commit ID**: `1696b0f143eb501be3aced40886fb0c8acecd74b`
- **作者**: Gaspard
- **日期**: 2016-08-26 10:54:44
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`

### 200. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `5cfcbcf955f387d94cb9677dd82f7f8473ed2487`
- **作者**: yylee1123
- **日期**: 2016-08-25 19:16:11
- **變更檔案數量**: 0

### 201. web表單設計師
- **Commit ID**: `33684b166c284daac661b1fb892ef522b1b4e2bf`
- **作者**: yylee1123
- **日期**: 2016-08-25 19:13:37
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rsrcBundleManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 202. Web表單設計師
- **Commit ID**: `387938298f327d8f0e59bbdf843e4443729ea46d`
- **作者**: Gaspard
- **日期**: 2016-08-25 17:50:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`

### 203. Web表單設計師
- **Commit ID**: `119c93b7a41c99c25288715859c5a166661499f6`
- **作者**: Gaspard
- **日期**: 2016-08-25 17:36:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 204. Web表單設計師
- **Commit ID**: `dc0c7b31cdd5b73be933884cd66040d854d287e9`
- **作者**: Gaspard
- **日期**: 2016-08-25 17:31:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`

### 205. Web表單設計師
- **Commit ID**: `5c36a624cc370144fe2e14c17ff30c7d7e0418a6`
- **作者**: Gaspard
- **日期**: 2016-08-25 14:56:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 206. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `e1ffbff6e7e6bef3bbfcd8f901b547d1f3861f7a`
- **作者**: Gaspard
- **日期**: 2016-08-25 14:41:20
- **變更檔案數量**: 0

### 207. Web表單設計師
- **Commit ID**: `29854ce2cbd6c8b0c04eb259b6030bfde52cab7a`
- **作者**: Gaspard
- **日期**: 2016-08-25 14:31:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 208. Web表單設計師
- **Commit ID**: `d0b17456bd15a07ea5d5cf11a2bb2118ad60da25`
- **作者**: Gaspard
- **日期**: 2016-08-25 14:25:12
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormCategoryListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`

### 209. web表單設計師
- **Commit ID**: `89f6ca36a1d3ff6c86fca75f7991fb11855dfc43`
- **作者**: yylee1123
- **日期**: 2016-08-25 14:14:40
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5611.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/formDesigner/rsrcBundleManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`

### 210. Web表單設計師
- **Commit ID**: `884c742e1315570ebacd704842db4351569b6da6`
- **作者**: Gaspard
- **日期**: 2016-08-24 16:38:13
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 211. Web表單設計師
- **Commit ID**: `ad157b198bb8a347e5889ae87b20535286ea0758`
- **作者**: Gaspard
- **日期**: 2016-08-24 15:21:45
- **變更檔案數量**: 16
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormValidateAccessCellEditorRenderer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormValidateAccessCellEditorRenderer.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`

### 212. App版的SQL註冊器改寫
- **Commit ID**: `3e5b471835d7b247640f072ae4319141967e1820`
- **作者**: Gaspard
- **日期**: 2016-08-23 17:33:56
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js`

### 213. Web表單設計師
- **Commit ID**: `e65cf85d4080002c6e80fbc09edab54fededa32f`
- **作者**: Gaspard
- **日期**: 2016-08-23 17:32:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 214. Web表單設計師
- **Commit ID**: `ca2154a9c47cc7ebdf9b70ad3885b3f3f83018bb`
- **作者**: Gaspard
- **日期**: 2016-08-22 17:12:00
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`

### 215. Web表單設計師
- **Commit ID**: `220214cca27b9fdacbe0e0fa8c7a522a12d0e51c`
- **作者**: Gaspard
- **日期**: 2016-08-22 16:38:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 216. Web表單設計師
- **Commit ID**: `3e7a86527f36a478fce3548858cbd392fe7bcb40`
- **作者**: Gaspard
- **日期**: 2016-08-19 17:44:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`

### 217. Web表單設計師
- **Commit ID**: `5e52fbb5c2a387527da8ab0b01d0e5a5883cf2f2`
- **作者**: Gaspard
- **日期**: 2016-08-19 16:36:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`

### 218. Web表單設計師
- **Commit ID**: `ecfe99ad2af15d3e0a675ca6a5cfedf3bbf7a6f9`
- **作者**: Gaspard
- **日期**: 2016-08-19 14:34:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`

### 219. Web表單設計師
- **Commit ID**: `8340fbdd6f90e24614a0cfe8b903c56b509d0fd8`
- **作者**: Gaspard
- **日期**: 2016-08-19 10:24:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`

### 220. Web表單設計師
- **Commit ID**: `bfcf47b965787496c641b410c368c8b40a1421e5`
- **作者**: Gaspard
- **日期**: 2016-08-18 17:31:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`

### 221. Web表單設計師
- **Commit ID**: `6a0cf983afacec94de2bae5ad45aaf0c37079a5b`
- **作者**: Gaspard
- **日期**: 2016-08-18 13:57:30
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`

### 222. Web表單設計師
- **Commit ID**: `135a492c1cb2c64a0e8ae7a48f0e8b8e6841821f`
- **作者**: Gaspard
- **日期**: 2016-08-18 13:48:35
- **變更檔案數量**: 21
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/CustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 223. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `2ca2ad4f943e0e1a494eeda937229229e378efba`
- **作者**: yylee1123
- **日期**: 2016-08-15 13:47:09
- **變更檔案數量**: 0

### 224. web表單設計師
- **Commit ID**: `951213723e83c9cf973c34cca37d4d299a075e03`
- **作者**: yylee1123
- **日期**: 2016-08-15 13:40:58
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 225. 測試Commit
- **Commit ID**: `9dcdf24d433d795d655bf2f46b89eeb7f8f97b1f`
- **作者**: Gaspard
- **日期**: 2016-08-11 13:42:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/ProcessDesignerApp.java`

### 226. Merge branch 'VSS2Git_BPM' of http://************/BPM_Group/BPM.git into VSS2Git_BPM
- **Commit ID**: `488b09c16fe6dbbe9088fc3835bd657d762ee2fc`
- **作者**: yylee1123
- **日期**: 2016-08-11 09:46:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📄 **修改**: `3.Implementation/subproject/business-delegate/.classpath`
  - 📄 **修改**: `3.Implementation/subproject/form-builder/.classpath`
  - 📄 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`

### 227. web表單設計師
- **Commit ID**: `7ef1f0e244d0a2da3274b35aed6d4459c60e8da6`
- **作者**: yylee1123
- **日期**: 2016-08-10 17:48:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`

### 228. web表單設計師
- **Commit ID**: `8ee9d31be0e9ba5a8024a79abcef555d8fcdfbfc`
- **作者**: yylee1123
- **日期**: 2016-08-10 17:48:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`

### 229. 合併BPM(VSS)
- **Commit ID**: `68b4cfe14dc5662c209664e802284981667be679`
- **作者**: loren
- **日期**: 2016-08-10 12:50:34
- **變更檔案數量**: 1142
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/domainhelper/DomainObjectFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/model/ProcessDefinitionMediaModel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/FormSelectDialog.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/common/ProcessIconCellEditorRenderer.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/common/ProcessIconEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileCellEditorRenderer.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormValidateAccessCellEditorRenderer.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/formaccess/FormValidateTableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/main/DesignerMainFrame.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/process/ProcessDefinitionMCERDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/processpackage/ProcessPackageMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/processpackage/ProcessPackageMCERTableModel.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/images/common/invoke.png`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/images/common/openfile.png`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormSelectDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormSelectDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormSelectDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormSelectDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/FormSelectDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/lib/Log4J/log4j.jar`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/bpm/user_interface/business_delegate/api/BpmServiceAPIDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessPackageManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProgramDefManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SapXmlManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/StatefulProcessDispatcherDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/FormDefinitionManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/ProcessPackageManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/ListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/AttachmentElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/BarcodeElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ClientDeviceType.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/CustomDataChooserDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DataAccessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DialogElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ElementStyle.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormSqlClause.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/InputElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/LinkElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ListElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/SelectElementDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/SqlAllowedForm.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/SqlAllowedJsp.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/SqlCommand.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TextElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TriggerElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ValidationRegexType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ValidationType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/IISOFormVariableNames.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/ISOPaperRecord.hbm.xml`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/ISOPaperRecord.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/comparator/CategoryComparator.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISOAccessRight.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocCatergory.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocCmItem.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocTypeLevel.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISODocument.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/iso/syncTable/SyncISOFile.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/WorkCalendar.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTag.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/newtiptop/model/NewTiptopXmlTagAttachment.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/QueryRunner.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/FormFieldAccessDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ProcessPackage.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/enumTypes/FormFieldAccessType.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/FormInstanceForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/UserProfileForManagerDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForMobilePerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/mobile/MobileOAuthClientUserDTO.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/lib/Json/json.jar`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/RunningEnvVariable.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/BarcodeElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ImageElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/LinkElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/OutputElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/ScriptEventTracker.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/AppDateTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputLabelTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputMultiTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppDialogInputTemplate.txt`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/AppGridTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/AppTimeTemplate.txt`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/src/resources/html/CustomDataChooserTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DateTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DialogInputLabelTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DialogInputMultiTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/DialogInputTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/GridTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/resources/html/TimeTemplate.txt`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/AppendGetValueAction.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/EditPhoneResponsiveStyleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/EditPhoneStyleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/EditScriptAction.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/ModelConverter.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/control/UpdateMobileScriptAction.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/model/AbstractSwingNode.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/EditMobileScriptDialog.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/FormPhoneView.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/RightPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/ToolBar.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/AbstractEditPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/AttachmentElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/BarcodePanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/CalendarChooser.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/DateElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/DialogInputElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/DisplayEditPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/ElementEditDialogMobile.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/HyperlinkElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/ImageElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/InputElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/ListElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/MultiZhEleEditPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/OutputElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/SelectElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/SerialNumberPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/TimeElementPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/TimeSetupPanel.java`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/src/com/dsc/nana/user_interface/apps/form_designer/view/mobile/TriggerElementPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/resource/properties/EditScriptDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/resource/properties/EditScriptDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/resource/properties/EditScriptDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/resource/properties/EditScriptDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/src/resource/properties/EditScriptDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/domainhelper/DomainObjectFactory.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/model/ProcessDefinitionMediaModel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/activity/FormSelectDialog.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/common/ProcessIconCellEditorRenderer.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/common/ProcessIconEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessControlEditor.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessMobileCellEditorRenderer.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormAccessMobileControlEditor.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormValidateAccessCellEditorRenderer.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/formaccess/FormValidateTableHeader.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/notification/NotificationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/processpackage/ProcessPackageMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/process_designer/view/processpackage/ProcessPackageMCERTableModel.java`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/images/common/invoke.png`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/src/images/common/openfile.png`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/activity/FormSelectDialog.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/activity/FormSelectDialog_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/activity/FormSelectDialog_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/activity/FormSelectDialog_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/activity/FormSelectDialog_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/formaccess/FormAccessControlEditor_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/resource/view/processpackage/ProcessPackageMCERTableModel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/NaNaEJB.properties`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/jakartaojb/repository_user.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/NaNa/conf/syncorg/sync-def.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/NaNa/conf/syncorg/sync-def_forT100.xml`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/bpm/services/api/BpmServiceAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocTypeMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/hibernate/ISOPaperRecorDaoImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/IFormDefinitionDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBFormDefDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcher.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessPackageManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PrsPKGMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ActivityEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/ProcessEventHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/exception/CannotParserParticipantException.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDefinitionManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerHome.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerLocalHome.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbstractPageListReader.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DraftListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FavoritiesProcessPkgListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormCategoryListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionSearchReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/GroupRejectableListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/GroupTraceProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudNoticeListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudRejectableListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudTraceProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/MOffice/McloudWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rsrcbundle/SysRsrcBundleManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mcloud/McloudMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileWeChatScheduleBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/AbstractNewTiptopMethod.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopUserImageSyncBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrAjax.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/SapXmlMgrInvoke.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/sap/util/sap/SapConnection.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrgMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/etl/SyncOrgEtl.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/TiptopManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF6Converter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5601.xls`
  - ➕ **新增**: `3.Implementation/subproject/webapp/NaNa/ResourceBundle/BPMRsrcBundle5602.xls`
  - 📝 **修改**: `3.Implementation/subproject/webapp/build.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/AdministratorFunctionAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/IntegratePortalURLEntranceAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageCustomReportAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReassignWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/UpdateVersionAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/app/ToolSuiteAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocTypeAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageSecurityLevelAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/sap/SapAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AppFormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileTracessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CommonAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ExtOrgAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/SapAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/TreeViewDataChooserAjax.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/BpmInvokeWorkItemVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/ISOPaperRecordVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/dataChooser/TreeViewDataChooserUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/FormDefinitionTransformer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/JSONConverter.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/formDesigner/T100FormMerge.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/module/ProgramViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/userProfile/UserProfileForManaging.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DocFileUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ISOFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobilePerformWorkItemTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileRESTTransferTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileUserAuthTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientPushTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileWeChatClientTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelationalFormViewer.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/TreeViewDataChooser.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileFileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ContextManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ExceptionMessageCreator.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/FormSqlClauseCacheHelper.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/JSFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/JSPFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/PLMIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/EFGPShareMethod.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/MobileCustomOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-formDesigner-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageDraft-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-manageModule-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/dwr-default.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-openWin-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/ExternalFormDesignerMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormLayoutGrid.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ReadDocument.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmApp.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppMenu.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppMenuLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracingLib.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPublicLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManageLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItemLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileKickStart.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SignImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/zTreeStyle/zTreeStyle-3.5-EFGP.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/AppModalDialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BPMProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmAppMenu.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BPMMobile/BpmWorkItem.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/.gitattributes`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/.npmignore`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/.travis.yml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/AUTHORS`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/CHANGELOG.md`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/CONTRIBUTING.md`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/LICENSE`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/README.md`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/comment/comment.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/comment/continuecomment.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/dialog/dialog.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/dialog/dialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/display/autorefresh.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/display/fullscreen.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/display/fullscreen.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/display/panel.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/display/placeholder.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/display/rulers.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/edit/closebrackets.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/edit/closetag.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/edit/continuelist.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/edit/matchbrackets.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/edit/matchtags.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/edit/trailingspace.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/fold/brace-fold.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/fold/comment-fold.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/fold/foldcode.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/fold/foldgutter.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/fold/foldgutter.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/fold/indent-fold.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/fold/markdown-fold.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/fold/xml-fold.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/hint/anyword-hint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/hint/css-hint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/hint/html-hint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/hint/javascript-hint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/hint/show-hint.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/hint/show-hint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/hint/sql-hint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/hint/xml-hint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/coffeescript-lint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/css-lint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/html-lint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/javascript-lint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/jshint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/json-lint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/lint.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/lint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/lint/yaml-lint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/merge/merge.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/merge/merge.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/mode/loadmode.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/mode/multiplex.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/mode/multiplex_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/mode/overlay.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/mode/simple.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/runmode/colorize.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/runmode/runmode-standalone.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/runmode/runmode.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/runmode/runmode.node.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/scroll/annotatescrollbar.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/scroll/scrollpastend.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/scroll/simplescrollbars.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/scroll/simplescrollbars.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/search/jump-to-line.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/search/match-highlighter.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/search/matchesonscrollbar.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/search/matchesonscrollbar.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/search/search.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/search/searchcursor.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/selection/active-line.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/selection/mark-selection.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/selection/selection-pointer.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/tern/tern.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/tern/tern.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/tern/worker.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/addon/wrap/hardwrap.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/bower.json`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/activeline.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/anywordhint.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/bidi.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/btree.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/buffers.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/changemode.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/closebrackets.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/closetag.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/complete.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/emacs.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/folding.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/fullscreen.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/hardwrap.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/html5complete.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/indentwrap.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/lint.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/loadmode.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/marker.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/markselection.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/matchhighlighter.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/matchtags.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/merge.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/multiplex.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/mustache.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/panel.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/placeholder.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/preview.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/requirejs.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/resize.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/rulers.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/runmode.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/search.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/simplemode.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/simplescrollbars.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/spanaffectswrapping_shim.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/sublime.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/tern.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/theme.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/trailingspace.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/variableheight.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/vim.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/visibletabs.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/widget.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/demo/xmlcomplete.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/activebookmark.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/compress.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/docs.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/internals.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/logo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/logo.svg`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/manual.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/realworld.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/releases.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/reporting.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/upgrade_v2.2.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/upgrade_v3.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/upgrade_v4.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/doc/yinyang.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/keymap/emacs.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/keymap/sublime.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/keymap/vim.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/lib/codemirror.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/lib/codemirror.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/apl/apl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/apl/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/asciiarmor/asciiarmor.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/asciiarmor/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/asn.1/asn.1.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/asn.1/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/asterisk/asterisk.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/asterisk/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/brainfuck/brainfuck.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/brainfuck/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/clike/clike.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/clike/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/clike/scala.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/clike/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/clojure/clojure.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/clojure/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/cmake/cmake.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/cmake/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/cobol/cobol.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/cobol/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/coffeescript/coffeescript.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/coffeescript/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/commonlisp/commonlisp.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/commonlisp/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/crystal/crystal.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/crystal/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/css.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/gss.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/gss_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/less.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/less_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/scss.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/scss_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/css/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/cypher/cypher.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/cypher/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/d/d.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/d/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dart/dart.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dart/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/diff/diff.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/diff/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/django/django.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/django/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dockerfile/dockerfile.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dockerfile/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dtd/dtd.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dtd/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dylan/dylan.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dylan/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/dylan/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ebnf/ebnf.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ebnf/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ecl/ecl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ecl/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/eiffel/eiffel.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/eiffel/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/elm/elm.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/elm/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/erlang/erlang.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/erlang/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/factor/factor.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/factor/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/fcl/fcl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/fcl/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/forth/forth.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/forth/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/fortran/fortran.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/fortran/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/gas/gas.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/gas/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/gfm/gfm.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/gfm/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/gfm/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/gherkin/gherkin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/gherkin/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/go/go.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/go/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/groovy/groovy.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/groovy/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haml/haml.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haml/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haml/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/handlebars/handlebars.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/handlebars/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haskell-literate/haskell-literate.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haskell-literate/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haskell/haskell.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haskell/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haxe/haxe.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/haxe/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/htmlembedded/htmlembedded.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/htmlembedded/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/htmlmixed/htmlmixed.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/htmlmixed/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/http/http.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/http/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/idl/idl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/idl/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/jade/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/jade/jade.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/javascript/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/javascript/javascript.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/javascript/json-ld.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/javascript/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/javascript/typescript.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/jinja2/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/jinja2/jinja2.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/jsx/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/jsx/jsx.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/jsx/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/julia/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/julia/julia.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/livescript/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/livescript/livescript.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/lua/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/lua/lua.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/markdown/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/markdown/markdown.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/markdown/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mathematica/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mathematica/mathematica.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/meta.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mirc/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mirc/mirc.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mllike/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mllike/mllike.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/modelica/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/modelica/modelica.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mscgen/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mscgen/mscgen.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mscgen/mscgen_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mscgen/msgenny_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mscgen/xu_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mumps/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/mumps/mumps.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/nginx/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/nginx/nginx.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/nsis/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/nsis/nsis.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ntriples/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ntriples/ntriples.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/octave/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/octave/octave.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/oz/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/oz/oz.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/pascal/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/pascal/pascal.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/pegjs/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/pegjs/pegjs.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/perl/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/perl/perl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/php/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/php/php.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/php/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/pig/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/pig/pig.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/powershell/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/powershell/powershell.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/powershell/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/properties/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/properties/properties.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/protobuf/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/protobuf/protobuf.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/puppet/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/puppet/puppet.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/python/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/python/python.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/q/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/q/q.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/r/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/r/r.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/rpm/changes/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/rpm/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/rpm/rpm.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/rst/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/rst/rst.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ruby/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ruby/ruby.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ruby/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/rust/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/rust/rust.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/rust/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/sass/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/sass/sass.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/scheme/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/scheme/scheme.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/shell/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/shell/shell.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/shell/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/sieve/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/sieve/sieve.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/slim/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/slim/slim.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/slim/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/smalltalk/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/smalltalk/smalltalk.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/smarty/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/smarty/smarty.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/solr/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/solr/solr.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/soy/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/soy/soy.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/sparql/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/sparql/sparql.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/spreadsheet/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/spreadsheet/spreadsheet.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/sql/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/sql/sql.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/stex/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/stex/stex.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/stex/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/stylus/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/stylus/stylus.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/swift/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/swift/swift.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tcl/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tcl/tcl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/textile/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/textile/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/textile/textile.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tiddlywiki/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tiddlywiki/tiddlywiki.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tiddlywiki/tiddlywiki.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tiki/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tiki/tiki.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tiki/tiki.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/toml/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/toml/toml.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tornado/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/tornado/tornado.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/troff/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/troff/troff.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ttcn-cfg/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ttcn-cfg/ttcn-cfg.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ttcn/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/ttcn/ttcn.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/turtle/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/turtle/turtle.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/twig/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/twig/twig.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/vb/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/vb/vb.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/vbscript/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/vbscript/vbscript.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/velocity/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/velocity/velocity.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/verilog/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/verilog/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/verilog/verilog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/vhdl/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/vhdl/vhdl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/vue/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/vue/vue.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/xml/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/xml/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/xml/xml.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/xquery/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/xquery/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/xquery/xquery.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/yaml-frontmatter/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/yaml-frontmatter/yaml-frontmatter.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/yaml/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/yaml/yaml.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/z80/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/mode/z80/z80.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/package.json`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/comment_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/doc_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/driver.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/emacs_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/index.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/lint.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/mode_test.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/mode_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/multi_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/phantom_driver.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/run.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/scroll_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/search_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/sql-hint-test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/sublime_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/test/vim_test.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/3024-day.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/3024-night.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/abcdef.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/ambiance-mobile.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/ambiance.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/base16-dark.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/base16-light.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/bespin.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/blackboard.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/cobalt.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/colorforth.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/dracula.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/eclipse.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/elegant.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/erlang-dark.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/hopscotch.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/icecoder.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/isotope.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/lesser-dark.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/liquibyte.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/material.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/mbo.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/mdn-like.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/midnight.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/monokai.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/neat.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/neo.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/night.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/paraiso-dark.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/paraiso-light.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/pastel-on-dark.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/railscasts.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/rubyblue.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/seti.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/solarized.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/the-matrix.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/tomorrow-night-bright.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/tomorrow-night-eighties.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/ttcn.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/twilight.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/vibrant-ink.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/xq-dark.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/xq-light.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/yeti.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CodeMirror-master/theme/zenburn.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/CustomDataChooser.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Dialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/BpmMobileLibrary.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolProcessTracing.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolTaskManage.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MVVM/MobileToolWorkItem.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BPMProcessTracing.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmAppMenu.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmTaskManage.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmWorkItem.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmWorkItemShell.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/BpmWorkPublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/cn.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/de.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/es.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/fr.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/it.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/nl.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/pt.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lang/ru.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/lib/aw.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/bg1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/bg2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/aqua/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/checkbox1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/checkbox2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/radio1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/radio2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/classic/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/mono/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_aqua-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_vista-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/_xp-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-bg1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-bg2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aqua-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-checkbox1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-checkbox2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-radio1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-radio2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/classic-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-g4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tabs1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tabs2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/vista-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/system/xp-tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g3.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/g4.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tabs1.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tabs2.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/vista/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/_tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/aw.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/button.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/checkbox.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/combo.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/grid.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/icons.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/radio.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/tabs.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ActiveWidgets264/styles/xp/tree.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/AppModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/Dialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ModalDialog.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/OpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ds-grid-aw.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/ds.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/dsMobile.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/Form/popup.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobileLibrary.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/System/BpmMobilePublic.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/System/knockout-3.2.0.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/System/knockout.mapping.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/System/utab.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/Map.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/MobileAppGrid.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/MobileProductOpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/index.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/plugin/snap.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileAppGrid.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/MobileProductOpenWin.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/OpenWin.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/ajaxSap/ajaxSap.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/colorpicker-master/i18n/jquery.ui.colorpicker-zh-CN.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/designerCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorer.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/formDesigner/shared-diagram.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/formDesigner/undoManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/formValidation.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-ui-1.8.9.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/css/frame.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/SaveDraft_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/SaveForm_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/disableDispatch_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/disableInvoke_zh_Hant_TW.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/dispatch_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/button_images/invoke_zh_Hant_TW.gif`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/default/images/gears.gif`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/default/images/index_images/portlet_logo.gif`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/formExplorer.css`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/bigbarcode.gif`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/explorerIcons.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/field.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/field_click.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/field_hover.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/menu.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/property.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/formDesigner/images/property_click.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/images/add.png`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/images/invoke.png`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/jquery.mobile-1.4.5.feng.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/jquery.mobile-1.4.5.feng_tm.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/jQueryMobile/themecolor/main.css`
  - 📝 **修改**: `3.Implementation/targets.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOCancel001.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOMod001.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISONew001.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperApply.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperRecover.form`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/form-default/ISOPaperWriteOff.form`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\344\275\234\345\273\242\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\346\226\260\345\242\236\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\347\264\231\346\234\254\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\346\226\207\344\273\266\350\256\212\346\233\264\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/bpmn/ISO\347\264\231\346\234\254\346\255\270\351\202\204(\346\262\226\351\212\267)\347\224\263\350\253\213.bpmn"`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOCancel001.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOMod001.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISONew001.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOPaperApply.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@iso/process-default/xpdl/ISOPaperWriteOff.process`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.6.0.1_updateSQL_Oracle_for5.5.6.2.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/nextVersion_updateSQL_SQLServer.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitISOData_SQLServer2005.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitNaNaISODB_Pure_ORACLE9i.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@iso/create/InitNaNaISODB_Pure_SQLServer2005.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@iso/update/isoNextVersion_updateSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/create/InitMobileDB_SQLServer.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/update/5.6.1.1_updateSQL_SQLServer.sql`

