# Release Notes - BPM

## 版本資訊
- **新版本**: hotfix_5.8.8.4_20230511
- **舊版本**: release_5.8.8.4
- **生成時間**: 2025-07-18 10:59:10
- **新增 Commit 數量**: 255

## 變更摘要

### way<PERSON><PERSON> (42 commits)

- **2023-05-04 17:44:03**: [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關[補]
  - 變更檔案: 1 個
- **2023-05-09 17:39:13**: [Web]Q00-20230509002 修正表單附件上傳後；若重新透過附件開窗上傳新的檔案時，原先上傳的附件無法下載的異常
  - 變更檔案: 1 個
- **2023-05-09 17:30:59**: [ISO]Q00-20230509001 修正ISO變更單於ModDocRequester關卡載入上一版附件後，點擊下載按鈕沒有反應
  - 變更檔案: 1 個
- **2023-03-27 17:07:58**: [Web]Q00-*********** 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱[補]
  - 變更檔案: 2 個
- **2023-03-27 13:54:51**: [Web]Q00-*********** 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱
  - 變更檔案: 5 個
- **2023-03-23 15:06:58**: [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析[補]
  - 變更檔案: 3 個
- **2023-03-02 15:25:23**: [流程引擎]Q00-20230302002 修正流程關係人部門設定為參考表單欄位，且表單欄位為DialogInput部門開窗時，發起流程會報錯
  - 變更檔案: 1 個
- **2023-03-23 10:51:02**: [Web]Q00-20230323001 調整使用者/流程處理/取回重辦，進入頁面後，選擇時間自訂的時間範圍說明由「流程發起時間」改為「工作完成的時間」
  - 變更檔案: 1 個
- **2023-03-21 11:03:12**: [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析
  - 變更檔案: 2 個
- **2023-03-06 17:36:59**: [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關
  - 變更檔案: 1 個
- **2023-03-01 11:58:51**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
  - 變更檔案: 1 個
- **2023-03-01 11:17:29**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
  - 變更檔案: 1 個
- **2023-03-01 11:17:29**: [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log
  - 變更檔案: 1 個
- **2023-02-23 14:56:22**: [內部]Q00-20230223003 流程引擎增加派送相關log
  - 變更檔案: 1 個
- **2023-02-22 15:49:01**: [流程引擎]Q00-20230222002 修正核決關卡設定與流程關卡處理者相同時自動簽核，且流程有兩個以上的核決關卡時，只有核決關卡展開的第一關有自動簽核，後續關卡皆未自動簽核
  - 變更檔案: 1 個
- **2023-02-21 15:02:58**: [Web]Q00-20230221001 修正當關卡有設定「必須上傳新附件」，若透過追蹤流程「重新發起新流程」時，卡控是否有上傳附件的功能失效
  - 變更檔案: 1 個
- **2023-02-16 17:53:47**: [流程引擎]Q00-20230216002 增加關卡判斷工作自動簽核跳關的log
  - 變更檔案: 1 個
- **2023-02-18 15:36:39**: [Web]A00-20230216001 修正核決關卡結束後被退回(取回)重新執行後，簡易流程圖的核決層級關卡名稱顯示錯誤
  - 變更檔案: 1 個
- **2023-02-15 10:59:36**: [流程引擎]Q00-20221020003 修正核決關卡參考自定義關卡，且自定義關卡沒有掛載任何表單時，導致流程無法往下繼續派送
  - 變更檔案: 1 個
- **2022-11-24 16:24:24**: [內部]Q00-20221124005 調整downloadImage的URL服務的ContentType為png
  - 變更檔案: 1 個
- **2023-02-07 10:30:37**: [流程設計師]Q00-20230207001 調整流程設計師簽入流程或匯入流程時，增加核決關卡的關卡Id校驗，避免因複製核決關卡功能導致流程定義異常而影響流程實例無法開啟
  - 變更檔案: 2 個
- **2022-11-23 14:32:29**: [在線閱覽]Q00-20221123002 調整通知關卡、追蹤流程(一般使用者)頁面，當表單附件為在線閱讀，且關卡的附件設置為fullcontrol時，需顯示下載附件的按鈕
  - 變更檔案: 2 個
- **2023-01-12 14:23:34**: [流程引擎]Q00-20230112002 修正Oracle資料庫，若流程有設計執行服務任務並將回傳值回寫至流程變數時，當回傳值為空字串時，服務任務會報無法更新STRINGWORKFLOWRUNTIMEVALUE為NULL的錯誤
  - 變更檔案: 6 個
- **2022-11-28 14:54:22**: [ISO]Q00-*********** 修正ISO變更單載入上一版附件時；當文件原始檔存在ISOSource+流水號時，無法載入上一版附件異常
  - 變更檔案: 6 個
- **2022-12-12 13:52:29**: [流程引擎]Q00-20221212001 調整ajax_ProcessAccessor.findFieldValueById取得表單內容方法，當流程同時掛載多表單時，偶發回傳找不到該欄位內容的錯誤
  - 變更檔案: 1 個
- **2022-12-02 12:05:10**: [內部]Q00-20221202001 調整Queue派送自動簽核關卡時，增加檢查WorkItem的處理者是否存在的防呆
  - 變更檔案: 1 個
- **2022-12-21 14:17:19**: [WorkFlow]Q00-20221221001 調整workflow整合WF熱鍵同步表單增加欄位Id的卡控判斷；當傳入的XML若表單元件沒有Id時，直接回傳因欄位沒有Id，所以同步失敗的xml
  - 變更檔案: 1 個
- **2022-12-12 17:43:31**: [流程引擎]Q00-20221212003 修正併簽流程；若其中一個分支直接退回到分支以前的關卡且流程設定被退回時逐關通知，其他分支執行中關卡也一併被關閉的異常
  - 變更檔案: 1 個
- **2022-12-08 16:37:51**: [流程引擎]Q00-20221208002 修正流程最後一個關卡為服務任務，且系統參數「traceprocess.view.workitem.with.first.activity」設定為false時，系統管理員透過追蹤流程進入流程時，會提示查不到此流程的資料
  - 變更檔案: 1 個
- **2022-12-01 14:06:46**: [流程引擎]Q00-20221201001 修正核決關卡的處理者若符合自動簽核時，核決關卡偶發無法繼續派送下去
  - 變更檔案: 1 個
- **2022-11-17 12:04:11**: [在線閱覽]Q00-*********** 修正在線閱覽開啟檔案的URL，當文件主機設置的WebAddress最後一碼為斜線時需過濾，避免開啟閱讀檔案後，點擊其他BPM功能會被導入登入頁
  - 變更檔案: 1 個
- **2022-11-14 14:31:49**: [WEB]Q00-20221114003 修正5884版本絕對位置表單下載附件異常，無法下載檔案
  - 變更檔案: 1 個
- **2022-11-09 17:27:22**: [流程引擎]Q00-20221109001 調整流程圖點選核決權限關卡，核決關卡改以關卡建立時間排序
  - 變更檔案: 1 個
- **2022-11-08 16:22:44**: [流程引擎]Q00-20221108003 修正流程引擎的加簽函式功能「addCustomParallelAndSerialActivity」，加簽出來的關卡的表單未依照「參考關卡」呈現對應的「表單元件顯示」狀態
  - 變更檔案: 1 個
- **2022-11-04 11:40:39**: [內部]Q00-20221104002 調整觸發自動簽核時間點的log
  - 變更檔案: 1 個
- **2022-11-02 18:07:03**: [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
  - 變更檔案: 1 個
- **2022-10-31 17:41:06**: [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
  - 變更檔案: 1 個
- **2022-10-31 16:23:44**: [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常
  - 變更檔案: 1 個
- **2022-10-28 15:21:40**: [流程引擎]Q00-20221028002 修正Oracle資料庫，若流程有設計執行服務任務並將回傳值回寫至流程變數時，服務任務會報錯的異常
  - 變更檔案: 1 個
- **2022-10-27 15:27:51**: [內部]Q00-*********** 調整PDF8Convert轉檔機制由synchronized改為多執行序執行，並增加debuglog
  - 變更檔案: 2 個
- **2022-10-25 17:29:45**: [流程引擎]Q00-20221025003 調整當核決關卡解析時；若解析人員在同一個組織下有多個兼職部門，且兼職部門的職務核決層級的level都相同時，則以該人員的主部門作為解析部門
  - 變更檔案: 1 個
- **2022-11-11 15:00:49**: [在線閱覽] Q00-20221111002 修正追蹤流程重發新流程，當第一關關卡有設定上傳附件不使用在線閱覽時，上傳附件仍會出現在線閱覽的選項
  - 變更檔案: 1 個

### 林致帆 (60 commits)

- **2023-04-18 16:37:44**: [流程引擎]Q00-20230418003 增加逾時關卡處理Log [補修正]
  - 變更檔案: 1 個
- **2023-04-18 15:39:32**: [流程引擎]Q00-20230418003 增加逾時關卡處理Log
  - 變更檔案: 1 個
- **2023-04-14 15:44:26**: [Web]Q00-20230414005 調整下載附件不該顯示This URL not have permission to download the file訊息
  - 變更檔案: 1 個
- **2023-04-14 09:26:59**: [表單設計師]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功 [補修正]
  - 變更檔案: 1 個
- **2023-04-06 16:13:14**: [流程引擎]Q00-20230406003 修正流程終點前若為閘道關卡，流程結案BamProInstData資料表的狀態還是進行中
  - 變更檔案: 1 個
- **2023-03-30 15:29:48**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 2 個
- **2023-03-30 10:46:26**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用 [補修正]
  - 變更檔案: 3 個
- **2023-03-28 18:05:14**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
  - 變更檔案: 5 個
- **2023-01-09 11:44:45**: [T100]S00-20221219001 T100支持在線閱覽功能 [補修正]
  - 變更檔案: 1 個
- **2022-12-27 11:21:32**: [T100]S00-20221219001 T100支持在線閱覽功能
  - 變更檔案: 2 個
- **2023-03-28 18:05:14**: [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
  - 變更檔案: 5 個
- **2023-03-25 14:43:14**: [流程引擎]Q00-20230325001 修正流程退回重瓣到有自動簽核之關卡會觸發自動簽核
  - 變更檔案: 1 個
- **2023-03-21 11:15:38**: [T100]Q00-*********** 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用
  - 變更檔案: 1 個
- **2023-03-15 10:29:37**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 1 個
- **2023-03-15 10:28:47**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 3 個
- **2023-03-14 16:31:22**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 3 個
- **2023-03-06 15:18:27**: [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊
  - 變更檔案: 2 個
- **2023-03-06 11:33:04**: [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功
  - 變更檔案: 3 個
- **2023-03-03 11:25:44**: [Web]Q00-20230303002 修正人員開窗選取帶有特殊字"𤧟"的人員派送後表單會重複多長好幾個"𤧟"字
  - 變更檔案: 1 個
- **2023-02-23 14:45:57**: [TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔
  - 變更檔案: 1 個
- **2023-01-30 16:04:15**: [Web]Q00-*********** 修正缺席紀錄資料過多，造成檢視簽核歷程時間過久
  - 變更檔案: 8 個
- **2023-02-17 11:33:44**: [E10]Q00-20230217002 修正子單身在Table模式下展開內容會無法完全顯示
  - 變更檔案: 1 個
- **2023-02-21 10:37:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
  - 變更檔案: 1 個
- **2023-02-13 18:35:53**: [WorkFlow]Q00-20230210001 修正從WorkFlow取得附件時因為URL為https導致取得失敗 [補修正]
  - 變更檔案: 3 個
- **2023-02-10 14:44:45**: [WorkFlow]Q00-20230210001 修正從WorkFlow取得附件時因為URL為https導致取得失敗
  - 變更檔案: 2 個
- **2023-02-21 10:37:32**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
  - 變更檔案: 1 個
- **2023-02-14 15:46:59**: [流程引擎]Q00-20230214001 修正流程在多人關卡且有設定撤銷事件進行撤銷，會造成DB LOCK
  - 變更檔案: 1 個
- **2023-01-09 11:46:58**: [WorkFlow]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
  - 變更檔案: 1 個
- **2022-12-06 15:14:27**: [Web]Q00-20221206001 修正BPM頁面在Chrome的網址列案Enter會導頁到錯誤頁面
  - 變更檔案: 1 個
- **2023-01-30 17:44:56**: [Web]Q00-20230130002 修正待辦清單有設定流程條件，使用者簽核完跳至下一個流程會找到非條件的流程
  - 變更檔案: 1 個
- **2023-01-13 18:43:32**: [雙因素模組]Q00-20230113004 修正取得資料庫寫法未增加釋放連線
  - 變更檔案: 1 個
- **2023-01-12 17:26:22**: [WorkFlow]Q00-20230112003 修正取簽核歷程未帶入FormId就導致歷程無法取得
  - 變更檔案: 2 個
- **2023-01-07 11:18:27**: [WorkFlow]Q00-20230105002 修正回傳WorkFlow處理者沒有被更新成功 [補修正]
  - 變更檔案: 1 個
- **2023-01-05 16:04:40**: [WorkFlow]Q00-20230105002 修正回傳WorkFlow處理者沒有被更新成功
  - 變更檔案: 1 個
- **2023-01-10 17:26:18**: [Web]Q00-20230110005 修正左側功能列點擊模擬使用者會重新刷新左側功能列
  - 變更檔案: 2 個
- **2023-01-06 10:44:17**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
  - 變更檔案: 3 個
- **2022-12-13 11:45:47**: [Web]Q00-20221213004 在Users資料被移除造成流程實例開啟異常的狀況下，優化異常訊息
  - 變更檔案: 2 個
- **2023-01-04 17:38:35**: [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯
  - 變更檔案: 2 個
- **2022-12-27 13:44:46**: [Web]Q00-20221227001 修正ESS流程圖開啟失敗
  - 變更檔案: 3 個
- **2022-11-25 14:27:04**: [流程引擎]Q00-20221125001 調整SystemConfig資料表改成二階快取
  - 變更檔案: 1 個
- **2022-12-26 13:59:17**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個
- **2022-12-26 11:59:05**: [流程引擎]Q00-20221221003 調整如果流程前兩關發起時間相同，會造成自動簽核為流程中有相同簽核者(不含發起者)就會沒有作用 [補修正]
  - 變更檔案: 1 個
- **2022-12-21 17:40:37**: [流程引擎]Q00-20221221003 調整如果流程前兩關發起時間相同，會造成自動簽核為流程中有相同簽核者(不含發起者)就會沒有作用
  - 變更檔案: 1 個
- **2022-12-19 14:35:56**: [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現 [補修正]
  - 變更檔案: 1 個
- **2022-12-15 10:33:31**: [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現 [補修正]
  - 變更檔案: 1 個
- **2022-12-15 10:31:31**: [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現
  - 變更檔案: 2 個
- **2022-12-09 17:09:53**: [流程引擎]Q00-20221209002 T100拋單若第一關與第二關的建立時間相同，自動簽核選擇與前一關相同簽核者就會無效
  - 變更檔案: 2 個
- **2022-12-05 14:41:15**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 1 個
- **2022-12-05 13:35:27**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
  - 變更檔案: 2 個
- **2022-12-01 18:19:35**: [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能
  - 變更檔案: 2 個
- **2022-11-21 18:02:16**: [流程引擎]Q00-20221121001 修正流程寄信內容是整張表單，且表單元件為浮點數且為空的狀況會派送失敗
  - 變更檔案: 1 個
- **2022-11-18 14:57:29**: [Web]Q00-20221118002 修正附件太多導致往下派送失敗
  - 變更檔案: 1 個
- **2022-11-17 09:23:07**: [流程引擎]Q00-20221117001 修正自動簽核在多人處理關卡上沒有效果
  - 變更檔案: 1 個
- **2022-11-16 14:15:06**: [Web]Q00-20221116002 修正個人資訊頁面載入，因為雙因素認證沒資料導致報錯
  - 變更檔案: 1 個
- **2022-11-14 10:42:08**: [WorkFlow]Q00-20221114001 修正附件URL帶有空格導致拋單敗
  - 變更檔案: 1 個
- **2022-11-03 17:41:40**: [流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信
  - 變更檔案: 3 個
- **2022-10-31 15:24:48**: [流程設計師]A00-20221026001 修正新增的預設關卡ID如果默認與已經存在的關卡ID一樣，儲存流程時不會異常導致開啟該流程直接報錯
  - 變更檔案: 1 個
- **2022-11-21 15:55:59**: [系統管理工具]A00-20221117001 修正儲存流程因為Application Server位址沒有填上PORT導致失敗
  - 變更檔案: 1 個
- **2022-11-21 15:48:40**: [Web]Q00-20221121002 修正關卡設定附近在線閱覽按鈕不顯示，在流程草稿上傳附件還是會顯示在線閱覽按鈕
  - 變更檔案: 1 個
- **2022-10-26 16:02:29**: [雙因素模組]Q00-20221026005 在未授權時，BPM首頁左側功能列會顯示雙因素模組功能
  - 變更檔案: 1 個

### yamiyeh10 (19 commits)

- **2023-05-05 18:20:02**: [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題
  - 變更檔案: 1 個
- **2022-12-12 16:26:24**: [Web]Q00-20221212002 修正在設定流程代理人時開啟流程出現很慢的問題
  - 變更檔案: 1 個
- **2023-03-27 17:47:54**: [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題[補]
  - 變更檔案: 1 個
- **2023-03-27 11:02:18**: [DT]A00-20230324001 修正系統權限管理員的可存取範圍不會根據編輯後的內容儲存問題
  - 變更檔案: 1 個
- **2023-03-14 11:23:36**: [流程設計師]Q00-20230314002 調整流程設計師在編輯範本內的變數清單中Runtime流程發起部門名稱多了一個姓字問題
  - 變更檔案: 2 個
- **2023-03-14 10:37:39**: [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題
  - 變更檔案: 1 個
- **2023-02-22 16:58:23**: [DT]C01-*********** 修正在系統權限管理員的可存取範圍權限無法選擇離職人員與失效部門問題
  - 變更檔案: 2 個
- **2022-12-20 14:00:18**: [BPM APP]Q00-*********** 修正移動端產品開窗選擇人員資訊會撈到離職人員問題
  - 變更檔案: 7 個
- **2022-11-09 13:23:45**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2022-12-20 14:00:18**: [BPM APP]Q00-*********** 修正移動端產品開窗選擇人員資訊會撈到離職人員問題
  - 變更檔案: 7 個
- **2023-02-21 15:44:41**: [WEB]Q00-*********** 修正在行動版的清單頁面上若主旨有<br>時無法正確換行問題
  - 變更檔案: 1 個
- **2023-02-18 12:40:38**: [表單設計師]Q00-20230218002 修正Title元件在插入LOGO後，Title文字會跟著滾輪上下移動的問題
  - 變更檔案: 1 個
- **2023-02-13 10:24:41**: [流程設計師]Q00-20230213007 修正在條件運算式中輸入超過整數最大值或最小值時執行上會因數值超過導致判斷走關卡異常問題
  - 變更檔案: 1 個
- **2023-01-04 11:18:21**: [WEB]Q00-20230104002 修正RWD表單在TextBox元件調整字體大小後使用iOS手機查看時欄位中的字不能完整呈現
  - 變更檔案: 1 個
- **2022-12-14 16:40:17**: [DT]C01-20221214005 修正Web化系統管理工具的系統郵件在Oracle環境下不存在帳號時會頁面異常問題
  - 變更檔案: 1 個
- **2022-11-17 16:01:45**: [DT]C01-*********** 修正Web化系統管理工具流程主機設定在編輯儲存後導致其他使用者登入BPM後顯示空白畫面問題
  - 變更檔案: 1 個
- **2022-11-15 14:02:09**: [Web]Q00-20221111005 修正員工代號有大寫時，使用iReport的列印功能會發生異常問題
  - 變更檔案: 1 個
- **2022-11-03 11:24:45**: [WEB]Q00-20221103001 使用者撤銷流程，理由填空白字串時不允許撤銷流程
  - 變更檔案: 1 個
- **2022-11-01 17:40:10**: [WEB]Q00-20221101005 修正在表單上設定運算規則時有參考單身加總的元件時不會自動觸發更新的問題
  - 變更檔案: 1 個

### cherryliao (19 commits)

- **2023-05-05 11:13:22**: [Web]Q00-20230504003 修正流程中附件檔名包含逗號時，檔案無法下載的問題
  - 變更檔案: 1 個
- **2023-04-14 10:47:52**: [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
  - 變更檔案: 1 個
- **2023-02-14 14:03:47**: [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題
  - 變更檔案: 2 個
- **2023-04-14 10:28:30**: [Web]Q00-20230414001 修正當用戶逾時閒置過久會彈出null訊息框的問題
  - 變更檔案: 2 個
- **2023-04-13 10:27:20**: [Web]Q00-20230413001 修正在表單腳本有使用addAttachment的方法時會無法取得附件描述的問題
  - 變更檔案: 1 個
- **2023-03-24 17:08:21**: [Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕
  - 變更檔案: 1 個
- **2023-03-20 10:38:43**: [Web]Q00-20230310001 調整倒數計時器功能的機制與提示訊息
  - 變更檔案: 2 個
- **2023-01-13 11:37:38**: [DT]Q00-20230113001 調整Web化組織管理工具在點擊部門後顯示的人員清單相關資訊異常的問題
  - 變更檔案: 1 個
- **2023-02-21 16:30:41**: [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題
  - 變更檔案: 1 個
- **2022-11-03 13:56:33**: [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
  - 變更檔案: 2 個
- **2023-01-12 16:08:15**: [Web]Q00-20230110004 修正Grid單頭與單身欄位不對齊的問題[補]
  - 變更檔案: 1 個
- **2023-01-10 13:50:02**: [Web]Q00-20230110004 修正Grid單頭與單身欄位不對齊的問題
  - 變更檔案: 2 個
- **2023-01-04 11:29:46**: [流程引擎]Q00-20230104003 修正寄送Mail在沒有CC收件人情況下不應副本給收件人的問題
  - 變更檔案: 1 個
- **2022-11-24 13:35:19**: [Web]Q00-20221124001 調整使用BPM外部URL連結跳轉畫面時表單內的表單名稱以多語系顯示
  - 變更檔案: 1 個
- **2022-12-20 16:47:16**: [DT]C01-20221201005 優化Web化資料使用權限管理頁面開啟緩慢問題
  - 變更檔案: 2 個
- **2022-11-18 14:25:40**: [Web]Q00-*********** 修正一般使用者簽核 T100 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊
  - 變更檔案: 5 個
- **2022-11-11 11:07:05**: [Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息
  - 變更檔案: 1 個
- **2022-10-26 11:14:26**: [Web]Q00-20221019001 修正響應式表單Grid元件設定凍結欄位時縮放瀏覽器時會出現跑版的問題
  - 變更檔案: 4 個
- **2022-11-16 14:27:02**: [Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題
  - 變更檔案: 1 個

### raven.917 (56 commits)

- **2023-05-02 16:41:11**: [Web] Q00-20230502001 相容56版window.print()列印簽核歷程
  - 變更檔案: 2 個
- **2023-04-18 11:14:02**: [Web] Q00-20230418001 修正 RadioButton & CheckBox 在列印表單時，被強制改成垂直式問題
  - 變更檔案: 1 個
- **2023-01-13 15:15:05**: [WEB]Q00-20221228002 修正SSO登入沒有轉換瀏覽器語系(補)
  - 變更檔案: 1 個
- **2023-01-05 11:57:14**: [WEB]Q00-20221228002 修正SSO登入沒有轉換瀏覽器語系
  - 變更檔案: 1 個
- **2023-04-13 17:02:10**: [Web] Q00-20230413002 修正通知信追蹤連結，流程圖開啟空白問題
  - 變更檔案: 1 個
- **2023-04-06 16:40:05**: [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題(補修正)
  - 變更檔案: 1 個
- **2023-04-06 16:28:22**: [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題
  - 變更檔案: 3 個
- **2023-03-23 15:28:14**: [Web] Q00-20230323002 調整部門主管首頁待辦處理量只會找得到在此部門內的使用者，監控流程圖及在途總處理量一併調整。
  - 變更檔案: 2 個
- **2023-03-13 15:37:27**: [Web] Q00-20230313002 修正SelectElement，Style屬性異常問題
  - 變更檔案: 1 個
- **2023-03-08 09:25:27**: [Web] Q00-20230308001 相容Grid,setAction點擊事件，支持點擊Row不帶回繫結欄位
  - 變更檔案: 1 個
- **2023-03-07 15:59:30**: [Web] Q00-20230307001 修正Admin操作員工工作轉派，撈取資料時新增防呆。
  - 變更檔案: 1 個
- **2023-02-23 10:18:01**: [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)
  - 變更檔案: 2 個
- **2022-12-23 15:26:45**: [組織同步]Q00-20221223003 同步新的主部門時，其他部門應自動變為兼職部門。
  - 變更檔案: 1 個
- **2023-02-23 10:18:01**: [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)
  - 變更檔案: 2 個
- **2023-02-21 15:53:39**: [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數
  - 變更檔案: 1 個
- **2022-11-25 15:25:38**: [組織設計師]Q00-20221125002調整復職時，移除通知SQL寫法。
  - 變更檔案: 1 個
- **2022-12-16 11:20:15**: [WEB]Q00-20221216001調整腳本「更改Grid欄位寬度」的提示訊息。
  - 變更檔案: 1 個
- **2023-02-16 10:41:10**: [組織同步] Q00-20230216001 調整ETL組織同步，使新寫出的暫存XML檔案轉為UTF-8
  - 變更檔案: 2 個
- **2023-02-15 10:44:12**: [WEB] V00-20230214004 修正設置浮點數無法過濾特殊字元符號問題
  - 變更檔案: 1 個
- **2023-02-14 11:40:10**: [WEB] Q00-20230207002 調整絕對定位表單設置必填，隱藏標籤提示異常問題
  - 變更檔案: 2 個
- **2023-02-08 20:06:25**: [WEB] V00-20221019001 修正監控流程設置「已撤銷」，無法匯出Excel
  - 變更檔案: 1 個
- **2023-02-14 16:34:58**: [WEB] A00-20230207001 調整水平線樣式變更時的邏輯，改為修改樣式後複製一個再刪除舊的
  - 變更檔案: 1 個
- **2022-12-28 18:04:44**: [WEB]S00-20220315002-在追蹤流程以及監控流程下，結案的流程會按照不同的結案結果呈現。
  - 變更檔案: 1 個
- **2022-12-13 10:23:46**: [WEB]Q00-20221213001 Update與Delete模組程式維護後，才需一併更新NavigatorMenu。
  - 變更檔案: 2 個
- **2023-02-03 12:19:44**: [WEB] Q00-20230203002 修正絕對定位表單，預覽列印下，RadioButton顯示異常
  - 變更檔案: 1 個
- **2023-01-04 23:48:42**: [WEB]Q00-20230104006修正列印模式下，絕對位置表單RadioButton顯示異常
  - 變更檔案: 2 個
- **2023-01-12 18:28:14**: [WEB] Q00-20230112001 修正T100拋單附件為URL時，不會計算點按次數。
  - 變更檔案: 2 個
- **2023-01-10 18:11:21**: [WEB]A00-20230110001 修正時間元件驗證異常錯誤。
  - 變更檔案: 1 個
- **2023-01-05 00:53:52**: [WEB]Q00-20221228004 修正舊版表單輸入法 yyyy/MM/dd 日期解析錯誤異常[補修正]
  - 變更檔案: 1 個
- **2022-12-29 10:53:58**: [WEB]Q00-20221228004 修正舊版表單輸入法 yy/MM/dd 日期解析錯誤異常[補修正]
  - 變更檔案: 1 個
- **2022-12-28 15:53:13**: [WEB]Q00-20221228004 修正舊版表單輸入法 yy/MM/dd 日期解析錯誤異常
  - 變更檔案: 1 個
- **2022-11-29 15:12:08**: [流程引擎]Q00-20221129001修正修正nchar欄位型態錯誤比對問題，導致轉存表單存空值。
  - 變更檔案: 1 個
- **2022-11-28 17:03:57**: [WEB]Q00-20221128006調整絕對定位表單RadioButton顏色更清楚。
  - 變更檔案: 3 個
- **2022-12-23 10:28:42**: [WEB]Q00-20221223001 流程資料查詢頁面無法下載附件
  - 變更檔案: 1 個
- **2022-10-25 15:06:58**: [Web]S00-20220711001Textbox元件設置整數及浮點數自動進位輸入值。
  - 變更檔案: 2 個
- **2022-11-23 09:01:05**: [WEB]Q00-20221123001 若tDialogType自定義開窗時，不應產生相應的Script語法。
  - 變更檔案: 1 個
- **2022-11-14 18:21:16**: [Web]Q00-20221114005絕對定位表單及RWD表單，統一可設定背景色設定。
  - 變更檔案: 4 個
- **2022-11-14 12:28:50**: [Web]Q00-20221114002修正表單設計師Barcode元件異常問題。
  - 變更檔案: 1 個
- **2022-11-08 15:25:39**: [Web]Q00-20221108001修正輸入元件設置必填後，沒勾選隱藏標籤原label標籤會出現undefined
  - 變更檔案: 1 個
- **2022-11-07 10:21:46**: [Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日(修正多語系)
  - 變更檔案: 1 個
- **2022-11-07 10:15:56**: [Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日
  - 變更檔案: 3 個
- **2022-11-04 17:32:12**: [Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。(補修正)
  - 變更檔案: 1 個
- **2022-11-04 17:06:47**: [Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。
  - 變更檔案: 4 個
- **2022-11-04 15:59:09**: [Web]Q00-20221104004 修正通知關卡指定離職人員時，離職交接人沒有作用。
  - 變更檔案: 1 個
- **2022-11-03 10:37:55**: [Tiptop]Q00-20221031002 修正log沒有辦法正常換日的問題，全部jar替換。
  - 變更檔案: 9 個
- **2022-11-01 16:41:19**: [WEB]Q00-20221028003 補修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)
  - 變更檔案: 1 個
- **2022-10-28 17:47:45**: [WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)
  - 變更檔案: 2 個
- **2022-10-28 17:26:12**: [WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>
  - 變更檔案: 1 個
- **2022-10-28 15:59:37**: [WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。(補修正)
  - 變更檔案: 1 個
- **2022-10-28 15:20:12**: [WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。
  - 變更檔案: 1 個
- **2022-10-26 16:01:07**: [Web]S00-20220510001新增運算規則可以選取到hidden元件。
  - 變更檔案: 1 個
- **2022-10-25 15:45:35**: [Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。
  - 變更檔案: 2 個
- **2022-10-25 15:25:10**: [Web]Q00-20221006004 上傳附件功能，優化使用者提示，且上傳過程不可點擊關閉按鈕。
  - 變更檔案: 1 個
- **2022-10-25 15:18:30**: [Web]V00-20221019001修正流程管理/監控流程 選擇「已撤銷」流程，匯出Excel發現多了「執行中的關卡」跟「目前處理者」的欄位。
  - 變更檔案: 1 個
- **2022-10-26 14:13:48**: [Web]Q00-20221026002 新增判斷二階快取應確認來源位置是否為本地端(localhost / 127.0.0.1)若是則不須額外清除。
  - 變更檔案: 1 個
- **2022-11-01 12:00:20**: [WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定
  - 變更檔案: 1 個

### 謝閔皓 (36 commits)

- **2023-02-08 20:01:08**: [Web]V00-20230208001 修正產品授權註冊新增 BPM 流程引擎時，未逾期的授權並未增加到總授權數裡
  - 變更檔案: 1 個
- **2023-03-03 11:56:46**: [Web]Q00-20230303001 調整 TextBox 元件進階設定中小數點後幾位的保存方式多語系，原本為實際值與四捨五入，將實際值調整為無條件捨去
  - 變更檔案: 3 個
- **2023-03-02 15:06:41**: [Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題
  - 變更檔案: 1 個
- **2023-02-18 13:36:07**: [Web]Q00-20230218001 調整讓 Grid 支援使用 <div>
  - 變更檔案: 1 個
- **2023-02-16 18:48:18**: [表單設計師]Q00-20230216003 新增表單設計師中的元件代號與元件名稱不支持 _lbl、_txt、_txt1、_txt2
  - 變更檔案: 3 個
- **2023-02-15 08:53:35**: [Web]Q00-20230215001 修正使用 https 且為 Safari 瀏覽器下載附件時，若檔名有中文會變成亂碼的問題
  - 變更檔案: 1 個
- **2023-01-30 15:11:00**: [Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題[補修正]
  - 變更檔案: 3 個
- **2023-01-18 09:06:30**: [Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題
  - 變更檔案: 1 個
- **2022-12-21 16:41:05**: [Web]Q00-20221221002 修正 TextArea 資料內容過多在列印表單時，會蓋到簽核意見的問題
  - 變更檔案: 1 個
- **2023-02-07 13:15:34**: [Web]A00-20230204001 修正追蹤流程中，查看已轉派工作且已處理的單據，點擊回到工作清單卻呈現工作通知清單的問題
  - 變更檔案: 1 個
- **2023-02-02 14:33:23**: [表單設計師]Q00-20230202001 修正表單設計師的 Time 元件，若在基本設定中的輸入框有設定預設值，儲存後再次簽出表單，該 Time 元件於畫面呈現時，其顯示值會有不正確的問題，且還有右下輸入框的預設值移動到提示文字的問題
  - 變更檔案: 2 個
- **2022-12-02 16:08:17**: [Web]Q00-20221202002 修正 Firefox 瀏覽器開啟絕對位置表單，使用列印表單的功能，畫面顯示異常的問題
  - 變更檔案: 1 個
- **2023-02-01 16:57:59**: [表單設計師]A00-20230131001 修正表單設計師的 Date 元件，若在基本設定中的輸入框有設定預設值，儲存後再次簽出表單，該 Date 元件於畫面呈現時，其顯示值會有不正確的問題
  - 變更檔案: 1 個
- **2023-01-31 08:53:44**: [TIPTOP]Q00-20230131001 修正 TIPTOP 差勤模組，若在 BPM 簽核時輸入＆ 符號，回傳給 TIPTOP 呈現時會有異常的問題
  - 變更檔案: 1 個
- **2023-01-30 16:57:12**: [TIPTOP]Q00-20230130001 修正回傳 TIPTOP 單據讀取簽名圖檔時，若環境為 Linux 且檔案名稱有英文大小寫，可能會造成檔案讀取失敗的問題
  - 變更檔案: 1 個
- **2023-01-16 11:01:34**: [Web]Q00-20230116001 上傳附件功能，優化使用者提示
  - 變更檔案: 2 個
- **2023-01-13 18:04:07**: [Web]Q00-20230113003 修正簽核意見內容有輸入換行，但通知信的簽核意見內容卻沒有換行的問題
  - 變更檔案: 1 個
- **2023-01-13 14:27:37**: [表單設計師]Q00-20230113002 隱藏 SubTab 元件右鍵的菜單，已確認 SubTab 元件與其頁籤在最初 ******* 版就不支援複製的功能
  - 變更檔案: 1 個
- **2023-01-12 17:49:56**: [Web]Q00-20230112004 修正 TextBox 元件的進階設定，若設定資料型態為浮點數且顯示千分位會導致顯示值異常的問題
  - 變更檔案: 1 個
- **2023-01-07 16:04:43**: [Web]A00-20230106001 修正 DialogInputLabel 元件，若在流程設計師中的表單存取控管設定權限為 Disable，於表單畫面中欄位還可修改的問題
  - 變更檔案: 1 個
- **2023-01-06 14:46:12**: [Web]Q00-20230106002 修正自行撰寫的查詢樣版有設定排序的功能，若沒有輸入查詢條件會無法排序的問題
  - 變更檔案: 1 個
- **2023-01-03 18:17:06**: [Web]Q00-20230103002 修正系統設定使用 LDAP 驗證，若使用者沒有設定 LDAP 驗證，從通知信連結進入 BPM 登入頁時，帳號欄位沒有自動帶入 UserId 的問題
  - 變更檔案: 1 個
- **2022-12-29 17:22:50**: [Web]Q00-20221228003 修正用任何語系登入，表單按鈕開窗用資料選取註冊器，Grid標籤跟模糊查詢標籤內容都只會顯示預設值[補修正]
  - 變更檔案: 2 個
- **2022-12-28 15:29:27**: [Web]Q00-20221228003 修正用任何語系登入，表單按鈕開窗用資料選取註冊器，Grid標籤跟模糊查詢標籤內容都只會顯示預設值
  - 變更檔案: 1 個
- **2022-12-26 10:17:44**: [表單設計師]Q00-20221226001 修正 TextBox 元件，若資料型態設定為浮點數且小數點後幾位，會導致沒有對齊的問題
  - 變更檔案: 1 個
- **2022-11-19 19:39:49**: [Web]V00-20221020001 修正切換頁面時，若讀取時間較長，會先呈現原畫面再跳轉的問題
  - 變更檔案: 2 個
- **2022-11-17 17:41:12**: [Web]Q00-*********** 修正一般使用者簽核 TIPTOP 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊
  - 變更檔案: 4 個
- **2022-11-16 17:52:27**: [Web]Q00-20221116003 修正 Checkbox、RadioButton 元件，若文字過多造成換行時，勾選按鈕會有偏移的問題
  - 變更檔案: 1 個
- **2022-11-15 15:04:02**: [Web]Q00-20221115002 修正流程設計師/流程模型/進階的主旨範本若有換行，會導致流程資料/流程資料查詢的查詢畫面無法顯示的問題
  - 變更檔案: 1 個
- **2022-11-10 11:40:18**: [Web]Q00-*********** 修正逾期授權的人數也算進總授權數裡
  - 變更檔案: 1 個
- **2022-11-06 16:37:01**: [表單設計師]Q00-20221106001 修正表單設計師中設置輔助格線的貼齊刻度無法暫存修改後的參數
  - 變更檔案: 2 個
- **2022-11-03 18:09:04**: [流程設計師]Q00-20221103003 修正流程定義/事件處理/流程完成/網頁應用程式，第一次點擊編輯時畫面空白的問題
  - 變更檔案: 1 個
- **2022-11-02 14:53:14**: [Web]Q00-*********** 修正checkbox設計時，若有勾選「最後一個選項額外產生輸入框」，表單中checkbox呈現與列印不一致的問題
  - 變更檔案: 1 個
- **2022-11-01 15:24:25**: [Web]Q00-20221101003 修正使用者若有離職作業維護，點選離職人員會跳到登入畫面的問題
  - 變更檔案: 1 個
- **2022-10-27 18:59:08**: [系統管理工具]A00-*********** 若管理員將有組織設計師權限的人員離職，並且移除組織及部門，會導致使用權限設定沒有畫面的問題
  - 變更檔案: 6 個
- **2022-10-26 09:06:05**: [Web]Q00-20221020004 修正 TextBox 元件進階功能的運算規則，若將已綁定的欄位值輸入後又刪除，會顯示 NaN 的問題
  - 變更檔案: 1 個

### 郭哲榮 (11 commits)

- **2023-04-13 18:31:55**: [MPT]A00-20230410001 修正從郵件連結進入BPM時，右上角沒有顯示首頁按鈕的問題
  - 變更檔案: 1 個
- **2023-03-23 11:42:15**: [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息[補]
  - 變更檔案: 2 個
- **2023-03-09 16:38:22**: [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息
  - 變更檔案: 1 個
- **2022-12-12 18:25:29**: [BPM APP]C01-20221130002 修正移動端絕對位置表單點擊附件按鈕沒反應與附件沒有顯示的問題
  - 變更檔案: 3 個
- **2022-12-29 20:43:36**: [BPM APP]C01-20221226004 修正絕對位置表單且行動版絕對位置在轉RWD表單時報錯的問題
  - 變更檔案: 2 個
- **2022-11-17 10:13:34**: [BPM APP]C01-20221109006 修正移動端Grid元件在不可新增但可編輯與刪除時能看到查看更多按鈕的問題
  - 變更檔案: 2 個
- **2022-11-16 14:40:46**: [BPM APP]C01-20221025006 修正企業微信未進入菜單前從推播進入表單畫面時空白問題
  - 變更檔案: 1 個
- **2022-11-15 18:20:46**: [BPM APP]C01-20220922002 修正移動端主旨與表單內容重疊跟取不到簽核歷程報錯問題
  - 變更檔案: 14 個
- **2022-11-11 17:34:54**: [BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題
  - 變更檔案: 6 個
- **2022-11-03 18:57:05**: [BPM APP]C01-20220927008 修正移動端Grid顯示畫面上按鈕重疊問題
  - 變更檔案: 1 個
- **2022-10-31 11:47:13**: [BPM APP]C01-20220921001 修正移動端在簽核後兩條流程的表單內容會串單問題
  - 變更檔案: 8 個

### kmin (7 commits)

- **2023-03-29 09:33:10**: Revert "[TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用"
  - 變更檔案: 5 個
- **2023-03-28 10:05:36**: Revert "[T100]Q00-*********** 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用"
  - 變更檔案: 1 個
- **2023-03-07 09:24:05**: [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功[補]
  - 變更檔案: 1 個
- **2023-02-23 11:16:23**: [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題[補]
  - 變更檔案: 1 個
- **2023-02-23 11:00:53**: Revert "[組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)"
  - 變更檔案: 2 個
- **2023-02-22 11:37:16**: Revert "[BPM APP]Q00-*********** 修正移動端產品開窗選擇人員資訊會撈到離職人員問題"
  - 變更檔案: 7 個
- **2023-02-21 14:29:19**: Revert "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]"
  - 變更檔案: 1 個

### pinchi_lin (5 commits)

- **2023-02-08 18:52:05**: [MPT]Q00-*********** 修正從授權中間層維護作業頁面點到首頁頁面會報NullPointerException問題
  - 變更檔案: 1 個
- **2023-02-21 18:56:35**: [DT]C01-20230217001 修正在維護流程關卡中加入新增的核決層級儲存簽入後就無法再開啟或簽出的問題
  - 變更檔案: 1 個
- **2023-02-20 12:36:32**: [DT]Q00-20221101001 修正Web組織管理工具中新增使用者的員工代號未卡控特殊字元檢查問題
  - 變更檔案: 1 個
- **2023-02-08 16:46:22**: [Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題[補]
  - 變更檔案: 3 個
- **2022-11-17 10:03:57**: [DT]C01-20221114005 修正Web化系統管理工具TIPTOP整合設定中對映索引修改後沒儲存問題
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關[補]
- **Commit ID**: `f553a1709a4384f23f25d47562e83d5274633d4b`
- **作者**: waynechang
- **日期**: 2023-05-04 17:44:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 2. [流程引擎]Q00-20230418003 增加逾時關卡處理Log [補修正]
- **Commit ID**: `805b8d13ec1c356e819f4ae6587fb5782abdec86`
- **作者**: 林致帆
- **日期**: 2023-04-18 16:37:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 3. [流程引擎]Q00-20230418003 增加逾時關卡處理Log
- **Commit ID**: `79abe5d42a00c37c2d96c02f109e155be8e9d781`
- **作者**: 林致帆
- **日期**: 2023-04-18 15:39:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 4. [Web]Q00-20230509002 修正表單附件上傳後；若重新透過附件開窗上傳新的檔案時，原先上傳的附件無法下載的異常
- **Commit ID**: `1f959bbedde6be20ca1a5fc787a7538b3bb7af2c`
- **作者**: waynechang
- **日期**: 2023-05-09 17:39:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`

### 5. [ISO]Q00-20230509001 修正ISO變更單於ModDocRequester關卡載入上一版附件後，點擊下載按鈕沒有反應
- **Commit ID**: `9fa2ab6791ec698aa088a865e15cedfde26d7ea4`
- **作者**: waynechang
- **日期**: 2023-05-09 17:30:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 6. [WEB]Q00-Q00-20230505001 修正重要流程在選擇流程的開窗時會出現重複資料問題
- **Commit ID**: `ced34c8134b237495ebc5829600dabdde7e030ba`
- **作者**: yamiyeh10
- **日期**: 2023-05-05 18:20:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 7. [Web]Q00-20221212002 修正在設定流程代理人時開啟流程出現很慢的問題
- **Commit ID**: `c2d95d671478ca64685bd390b0136c74b8a8bf02`
- **作者**: yamiyeh10
- **日期**: 2022-12-12 16:26:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 8. [Web]Q00-20230504003 修正流程中附件檔名包含逗號時，檔案無法下載的問題
- **Commit ID**: `780afa4bbc5d0fb96e64156cb1587a4ded15d01a`
- **作者**: cherryliao
- **日期**: 2023-05-05 11:13:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 9. [Web] Q00-20230502001 相容56版window.print()列印簽核歷程
- **Commit ID**: `60081cd29826da07b95e27ee0ed260e0e34bb741`
- **作者**: raven.917
- **日期**: 2023-05-02 16:41:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 10. [Web]V00-20230208001 修正產品授權註冊新增 BPM 流程引擎時，未逾期的授權並未增加到總授權數裡
- **Commit ID**: `a77d9daa2c81c7188f66d178d2db10b2120a4061`
- **作者**: 謝閔皓
- **日期**: 2023-02-08 20:01:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`

### 11. [Web]Q00-20230414005 調整下載附件不該顯示This URL not have permission to download the file訊息
- **Commit ID**: `117f08d14a69893bd04029af3c6ac258351f56ff`
- **作者**: 林致帆
- **日期**: 2023-04-14 15:44:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 12. [Web] Q00-20230418001 修正 RadioButton & CheckBox 在列印表單時，被強制改成垂直式問題
- **Commit ID**: `cf3e8d58a8d88ca9678b51c8a61d3a552d13492c`
- **作者**: raven.917
- **日期**: 2023-04-18 11:14:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap-3.3.5-print.css`

### 13. [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題[補]
- **Commit ID**: `7ef0788b314531b9c0e45b79a12445c54f0dc54b`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:47:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 14. [Web]Q00-20230208002 修正使用者發生逾時會卡在請關閉此瀏覽器訊息無法跳出問題
- **Commit ID**: `97a4ad84e174b5a83c60f4b01141848b4c10f8a5`
- **作者**: cherryliao
- **日期**: 2023-02-14 14:03:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 15. [Web]Q00-20230414001 修正當用戶逾時閒置過久會彈出null訊息框的問題
- **Commit ID**: `70542f4a771d7d3a62da2efe89f96eded6cc0df1`
- **作者**: cherryliao
- **日期**: 2023-04-14 10:28:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`

### 16. [表單設計師]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功 [補修正]
- **Commit ID**: `0076ed26b7a1ff771eead711d5845e20de531ba7`
- **作者**: 林致帆
- **日期**: 2023-04-14 09:26:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 17. [MPT]A00-20230410001 修正從郵件連結進入BPM時，右上角沒有顯示首頁按鈕的問題
- **Commit ID**: `45c583457304b7afc31260e2dfdfac3a94d96c20`
- **作者**: 郭哲榮
- **日期**: 2023-04-13 18:31:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 18. [WEB]Q00-20221228002 修正SSO登入沒有轉換瀏覽器語系(補)
- **Commit ID**: `98613d5909c568d20c6aa2fbd8d91ff16d07c8ff`
- **作者**: raven.917
- **日期**: 2023-01-13 15:15:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 19. [WEB]Q00-20221228002 修正SSO登入沒有轉換瀏覽器語系
- **Commit ID**: `681c148d697c766547844cc975b3639fa30b334b`
- **作者**: raven.917
- **日期**: 2023-01-05 11:57:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 20. [Web] Q00-20230413002 修正通知信追蹤連結，流程圖開啟空白問題
- **Commit ID**: `7db8e5997db2b219c6fa1920a7c5aedd71d693a1`
- **作者**: raven.917
- **日期**: 2023-04-13 17:02:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`

### 21. [Web]Q00-20230413001 修正在表單腳本有使用addAttachment的方法時會無法取得附件描述的問題
- **Commit ID**: `cc96a18a645322344e15b2cb978915e6ea5b2ac0`
- **作者**: cherryliao
- **日期**: 2023-04-13 10:27:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 22. [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題(補修正)
- **Commit ID**: `e9c74d2db74ecba9805c65f83b45b6fc3fca9c62`
- **作者**: raven.917
- **日期**: 2023-04-06 16:40:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 23. [web] Q00-20230406004 調整絕對定位表單RadioButton原生元件顏色過淺問題
- **Commit ID**: `49fa6a7a17021c4370d17ccb93cd041bff280c2b`
- **作者**: raven.917
- **日期**: 2023-04-06 16:28:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 24. [流程引擎]Q00-20230406003 修正流程終點前若為閘道關卡，流程結案BamProInstData資料表的狀態還是進行中
- **Commit ID**: `2c5d50d25d945ac3be00f743e9b6ae17d6a96ba3`
- **作者**: 林致帆
- **日期**: 2023-04-06 16:13:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 25. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `99d0b341137be890865463cfbb11962fa23c1fdf`
- **作者**: 林致帆
- **日期**: 2023-03-30 15:29:48
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`

### 26. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用 [補修正]
- **Commit ID**: `231b2e6380afd2f82e8c259fdc48d5d5f352d409`
- **作者**: 林致帆
- **日期**: 2023-03-30 10:46:26
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 27. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
- **Commit ID**: `cf3baff4fa7329b389fff54e20069a81a9efd5e2`
- **作者**: 林致帆
- **日期**: 2023-03-28 18:05:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 28. [T100]S00-20221219001 T100支持在線閱覽功能 [補修正]
- **Commit ID**: `e7a90d69ab9f37570ac881ab437ff15f707e5ca8`
- **作者**: 林致帆
- **日期**: 2023-01-09 11:44:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 29. [T100]S00-20221219001 T100支持在線閱覽功能
- **Commit ID**: `0194213796af2d7ea05e46f4fd97f35d20a28114`
- **作者**: 林致帆
- **日期**: 2022-12-27 11:21:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/InvokeT100Process.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 30. Revert "[TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用"
- **Commit ID**: `3fc560906da3fc691fff778950e21b63b8e8df72`
- **作者**: kmin
- **日期**: 2023-03-29 09:33:10
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - ❌ **刪除**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ❌ **刪除**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 31. [TIPTOP]Q00-20230328003 修正TIPTOP拋單使用在線閱讀功能，在附件為PDF類型無作用
- **Commit ID**: `eea437d390e45282c23de4ef8021b2e751d77954`
- **作者**: 林致帆
- **日期**: 2023-03-28 18:05:14
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.2_DML_Oracle.sql`

### 32. [MPT]Q00-*********** 修正從授權中間層維護作業頁面點到首頁頁面會報NullPointerException問題
- **Commit ID**: `cb334c442a7417989a38b187b633693b707a6c6d`
- **作者**: pinchi_lin
- **日期**: 2023-02-08 18:52:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`

### 33. Revert "[T100]Q00-*********** 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用"
- **Commit ID**: `9a09ff3571ef11fb24bdda4c12f4f80b141738c2`
- **作者**: kmin
- **日期**: 2023-03-28 10:05:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 34. [Web]Q00-*********** 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱[補]
- **Commit ID**: `adb7933f7d16f09cb4f4acca34ea79c181412041`
- **作者**: waynechang
- **日期**: 2023-03-27 17:07:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java`

### 35. [Web]Q00-*********** 修正5884以上版本；修改「模組程式維護」裡面的「模組名稱」或是「程式名稱」的多語系後，系統仍顯示修改前的名稱而未顯示修改後的名稱
- **Commit ID**: `8e76e45f87c951cd8c09866939ca1dd5e3607756`
- **作者**: waynechang
- **日期**: 2023-03-27 13:54:51
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/ProgramDefinitionDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/data_transfer/UserForSecurityDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ModuleDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`

### 36. [流程引擎]Q00-20230325001 修正流程退回重瓣到有自動簽核之關卡會觸發自動簽核
- **Commit ID**: `48e299fa37e33ee586d530972845a7b9b02d976e`
- **作者**: 林致帆
- **日期**: 2023-03-25 14:43:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

### 37. [Web]Q00-20230324002 優化上傳附件功能，防止重複點擊上傳按鈕
- **Commit ID**: `267cb1b623ea5a5a28b9c66913902fb6ca4f10ff`
- **作者**: cherryliao
- **日期**: 2023-03-24 17:08:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 38. [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題[補]
- **Commit ID**: `17b1e4623089783e4589940d75f1dac8c6cc80c3`
- **作者**: yamiyeh10
- **日期**: 2023-03-27 17:47:54
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessPackageManager.java`

### 39. [DT]A00-20230324001 修正系統權限管理員的可存取範圍不會根據編輯後的內容儲存問題
- **Commit ID**: `321895367bf458785f07f9335367cfb5818af958`
- **作者**: yamiyeh10
- **日期**: 2023-03-27 11:02:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/module/AuthorityManagerBean.java`

### 40. [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息[補]
- **Commit ID**: `f79afb53d6be7c5faed37f6414146724014caed6`
- **作者**: 郭哲榮
- **日期**: 2023-03-23 11:42:15
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 41. [Web] Q00-20230323002 調整部門主管首頁待辦處理量只會找得到在此部門內的使用者，監控流程圖及在途總處理量一併調整。
- **Commit ID**: `beec19b0e43f1b6d3ef4ab1df551b63e12810b97`
- **作者**: raven.917
- **日期**: 2023-03-23 15:28:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 42. [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析[補]
- **Commit ID**: `48988d217971e38da96e4abd145ff981c96f82cd`
- **作者**: waynechang
- **日期**: 2023-03-23 15:06:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherLocal.java`

### 43. [流程引擎]Q00-20230302002 修正流程關係人部門設定為參考表單欄位，且表單欄位為DialogInput部門開窗時，發起流程會報錯
- **Commit ID**: `41b225eeec689cab123aab45159de80751441d11`
- **作者**: waynechang
- **日期**: 2023-03-02 15:25:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ParticipantDefParserBean.java`

### 44. [Web]Q00-20230323001 調整使用者/流程處理/取回重辦，進入頁面後，選擇時間自訂的時間範圍說明由「流程發起時間」改為「工作完成的時間」
- **Commit ID**: `e77ad9b01a49c3c4974d3555ff63b98f90b4dcb3`
- **作者**: waynechang
- **日期**: 2023-03-23 10:51:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`

### 45. [Q00]S00-20230321002 調整核決層級邏輯，當使用者有多個核決層級，且當最高層級有複數時，找出距離參考部門最近的部門的職務做為流程解析
- **Commit ID**: `f9e998f7a35529ba9dd2a90421eaffab684748e1`
- **作者**: waynechang
- **日期**: 2023-03-21 11:03:12
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/organization/OrganizationUnit.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 46. [Web]Q00-20230310001 調整倒數計時器功能的機制與提示訊息
- **Commit ID**: `03845fdc5c1f03490f7b5d76cae90df0114c642e`
- **作者**: cherryliao
- **日期**: 2023-03-20 10:38:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 47. [T100]Q00-*********** 調整T100簽名圖檔同步功能需設定白名單IP設定才能正常使用
- **Commit ID**: `c924c26de35f791723d46d4fee86c3e1fffc55cd`
- **作者**: 林致帆
- **日期**: 2023-03-21 11:15:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 48. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `225465fe567b26628a0fb348646585268ab8730a`
- **作者**: 林致帆
- **日期**: 2023-03-15 10:29:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`

### 49. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `74bc22e1eb83a0b33895d01f66410fcd3ee57f4c`
- **作者**: 林致帆
- **日期**: 2023-03-15 10:28:47
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`

### 50. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `4f57c93d40f983dfc0b99cda3645347fe7eb46af`
- **作者**: 林致帆
- **日期**: 2023-03-14 16:31:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`

### 51. [流程設計師]Q00-20230314002 調整流程設計師在編輯範本內的變數清單中Runtime流程發起部門名稱多了一個姓字問題
- **Commit ID**: `88fb49955e106708d046bf7760ee8f9d5869fb4e`
- **作者**: yamiyeh10
- **日期**: 2023-03-14 11:23:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/processpackage/VariableNamesList_zh_TW.properties`

### 52. [流程設計師]Q00-20230314001 調整流程設計師執行還原動作後會導致連接線的條件無法編輯問題
- **Commit ID**: `0628bee111a4be4d2da49b733fba1b8b7d9eea8c`
- **作者**: yamiyeh10
- **日期**: 2023-03-14 10:37:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/ProcessPackageManager.java`

### 53. [Web] Q00-20230313002 修正SelectElement，Style屬性異常問題
- **Commit ID**: `35009b0c64ed969ac4b6d2819f9dc4a8c0cc02e5`
- **作者**: raven.917
- **日期**: 2023-03-13 15:37:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 54. [Web]Q00-20230303001 調整 TextBox 元件進階設定中小數點後幾位的保存方式多語系，原本為實際值與四捨五入，將實際值調整為無條件捨去
- **Commit ID**: `24a1cc696841ded904b727e33d3eea04fb3bd022`
- **作者**: 謝閔皓
- **日期**: 2023-03-03 11:56:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 55. [BPM APP]C01-20230306001 調整取互聯Token相關資訊失敗時開DEBUG層級才會顯示詳細訊息
- **Commit ID**: `afbda9fa6140cd50b7676a7d9c6f0821834ddaa6`
- **作者**: 郭哲榮
- **日期**: 2023-03-09 16:38:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileMPlatformClientTool.java`

### 56. [Web] Q00-20230308001 相容Grid,setAction點擊事件，支持點擊Row不帶回繫結欄位
- **Commit ID**: `2b1341749c276513281bf63f08fea936a80ea8d0`
- **作者**: raven.917
- **日期**: 2023-03-08 09:25:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 57. [Web] Q00-20230307001 修正Admin操作員工工作轉派，撈取資料時新增防呆。
- **Commit ID**: `42fe57ee09678efd42f92fc2a108b8c503d407a0`
- **作者**: raven.917
- **日期**: 2023-03-07 15:59:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java`

### 58. [流程引擎]Q00-20230306004 修正關卡設定自動簽核2與前一關相同簽核者則跳過，在流程同時有多分支並行簽核時；偶發會發生自動簽核判斷錯誤，而無法自動跳關
- **Commit ID**: `6c5715d049d825ab7dfa815e094840d9e7b839d8`
- **作者**: waynechang
- **日期**: 2023-03-06 17:36:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 59. [ESS]Q00-20230306003 修正同時整合ESS與其他ERP，發起非ESS流程log會印出ESS的流程資訊
- **Commit ID**: `ee7095cfdba84cbcfc814191b48e6805ccbc06c3`
- **作者**: 林致帆
- **日期**: 2023-03-06 15:18:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 60. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
- **Commit ID**: `e5c99ce76aaaf3d4d61b42629522819d416de89c`
- **作者**: waynechang
- **日期**: 2023-03-01 11:58:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 61. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log[補]
- **Commit ID**: `805684319808d0092d4e202cbc946ff732b2f3dc`
- **作者**: waynechang
- **日期**: 2023-03-01 11:17:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 62. [內部]Q00-20230301001 調整流程引擎在關卡加簽時增加相關log
- **Commit ID**: `2bb6595c38517a32a9cf7c783e38c776cd0ac6ed`
- **作者**: waynechang
- **日期**: 2023-03-01 11:17:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 63. [內部]Q00-20230223003 流程引擎增加派送相關log
- **Commit ID**: `038bba87db825619e6f4f882585d2d6df8b0c481`
- **作者**: waynechang
- **日期**: 2023-02-23 14:56:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 64. [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功[補]
- **Commit ID**: `91d630d77978677778e82e671ea8e9c82a0743af`
- **作者**: kmin
- **日期**: 2023-03-07 09:24:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`

### 65. [表單設計施]Q00-20230306002 增加防呆，修正匯入表單轉RWD時若元件ID異常，就不讓轉成功
- **Commit ID**: `fab538f9ed6309baafb98a8c129b7e711659044e`
- **作者**: 林致帆
- **日期**: 2023-03-06 11:33:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/FormDesignerAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 66. [Web]Q00-20230303002 修正人員開窗選取帶有特殊字"𤧟"的人員派送後表單會重複多長好幾個"𤧟"字
- **Commit ID**: `854cbb4e64b5b1c63f0e978cbedb790463825e18`
- **作者**: 林致帆
- **日期**: 2023-03-03 11:25:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/Dom4jUtil.java`

### 67. [Web]Q00-20230222004 修正 TextBox 元件的進階設定，若設定小數點後幾位且保存方式為實際值，實際值會完全顯示的問題
- **Commit ID**: `33ab276e2a3c3ff6bf18180178ea269e477585ea`
- **作者**: 謝閔皓
- **日期**: 2023-03-02 15:06:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 68. [TIPTOP]Q00-20230223002 修正拋單附件為一個以上時，cleanDocument接口無法刪除TIPTOP附件暫存檔
- **Commit ID**: `bd526c8937ae02078542ac991f35534ab361baab`
- **作者**: 林致帆
- **日期**: 2023-02-23 14:45:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 69. [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題[補]
- **Commit ID**: `892dc296ee386acccd53f3214470b6daf7b4368b`
- **作者**: kmin
- **日期**: 2023-02-23 11:16:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 70. [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)
- **Commit ID**: `6233e8531ce14c17ef87bf44d4e21dbfb2ebed58`
- **作者**: raven.917
- **日期**: 2023-02-23 10:18:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 71. [組織同步]Q00-20221223003 同步新的主部門時，其他部門應自動變為兼職部門。
- **Commit ID**: `0912c59ccd1d37d1bf8574508de1a618dde5ef04`
- **作者**: raven.917
- **日期**: 2022-12-23 15:26:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 72. Revert "[組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)"
- **Commit ID**: `73f1f3c353b5a089b47b758f1a78d53766e3d607`
- **作者**: kmin
- **日期**: 2023-02-23 11:00:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 73. [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數(補修正)
- **Commit ID**: `75d42ce4ead2d2304e4a58cdf35461470b5bc10c`
- **作者**: raven.917
- **日期**: 2023-02-23 10:18:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 74. [DT]C01-*********** 修正在系統權限管理員的可存取範圍權限無法選擇離職人員與失效部門問題
- **Commit ID**: `22cd5c5acf9741ce56604d7329a02ba829a4a0a6`
- **作者**: yamiyeh10
- **日期**: 2023-02-22 16:58:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 75. [DT]C01-20230217001 修正在維護流程關卡中加入新增的核決層級儲存簽入後就無法再開啟或簽出的問題
- **Commit ID**: `dc6cdb616fd7d6087eb54806b038c72268a85130`
- **作者**: pinchi_lin
- **日期**: 2023-02-21 18:56:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 76. [組織同步] Q00-20230221003 修正HRM助手更新User資料時，沒有取系統變數
- **Commit ID**: `dd4e03c3676c94348906e7df66227619efda37fa`
- **作者**: raven.917
- **日期**: 2023-02-21 15:53:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 77. [DT]Q00-20221101001 修正Web組織管理工具中新增使用者的員工代號未卡控特殊字元檢查問題
- **Commit ID**: `ff4e0c9c8366ca35cf9d3d45d134bee05610dbed`
- **作者**: pinchi_lin
- **日期**: 2023-02-20 12:36:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 78. [DT]Q00-20230113001 調整Web化組織管理工具在點擊部門後顯示的人員清單相關資訊異常的問題
- **Commit ID**: `e03bd0861ed32ea716cbcffced7b8d50ef4425cd`
- **作者**: cherryliao
- **日期**: 2023-01-13 11:37:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 79. [組織設計師]Q00-20221125002調整復職時，移除通知SQL寫法。
- **Commit ID**: `eeb09fd40eaaa1b1071101e19635796a90902477`
- **作者**: raven.917
- **日期**: 2022-11-25 15:25:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 80. [流程引擎]Q00-20230222002 修正核決關卡設定與流程關卡處理者相同時自動簽核，且流程有兩個以上的核決關卡時，只有核決關卡展開的第一關有自動簽核，後續關卡皆未自動簽核
- **Commit ID**: `307f0c680cdc7a4f5307af64eb85ba1c489368ab`
- **作者**: waynechang
- **日期**: 2023-02-22 15:49:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 81. [流程設計師]Q00-20230220003 修正簽核流程設計師應用程式管理員無法更新SessionBean的問題
- **Commit ID**: `b4175420cb3df7706424309818adb96eeb9bab38`
- **作者**: cherryliao
- **日期**: 2023-02-21 16:30:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/ApplicationManagerBean.java`

### 82. [Web]Q00-*********** 修正缺席紀錄資料過多，造成檢視簽核歷程時間過久
- **Commit ID**: `e6d2720ba547812d103797eca88b1b68895435a7`
- **作者**: 林致帆
- **日期**: 2023-01-30 16:04:15
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrgIntegrationLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPI.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerAPIBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 83. [BPM APP]Q00-*********** 修正移動端產品開窗選擇人員資訊會撈到離職人員問題
- **Commit ID**: `69a2ed9e4be242730d9d15bd71c821b10c46d747`
- **作者**: yamiyeh10
- **日期**: 2022-12-20 14:00:18
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 84. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `4911a752c569e3a2041d8a7b86af71c113cab328`
- **作者**: yamiyeh10
- **日期**: 2022-11-09 13:23:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 85. [內部]新增提供給組織設計工具Web化使用的SessionBean[補]
- **Commit ID**: `fb40dc88913e2cabf39ee64cbbd4a842e4e4692c`
- **作者**: cherryliao
- **日期**: 2022-11-03 13:56:33
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 86. Revert "[BPM APP]Q00-*********** 修正移動端產品開窗選擇人員資訊會撈到離職人員問題"
- **Commit ID**: `e5869a29a47f9de902943dda0cf55a6a7de7b941`
- **作者**: kmin
- **日期**: 2023-02-22 11:37:16
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 87. [BPM APP]Q00-*********** 修正移動端產品開窗選擇人員資訊會撈到離職人員問題
- **Commit ID**: `9395c57138716df39e8f8b6490e95d7ce6159fe9`
- **作者**: yamiyeh10
- **日期**: 2022-12-20 14:00:18
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/OrganizationManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/OrganizationManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/control/OrgDesignerManager.java`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 88. [WEB]Q00-*********** 修正在行動版的清單頁面上若主旨有<br>時無法正確換行問題
- **Commit ID**: `36db18980f991115b8228dc259b89b2b01bd2afe`
- **作者**: yamiyeh10
- **日期**: 2023-02-21 15:44:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 89. [Web]Q00-20230218001 調整讓 Grid 支援使用 <div>
- **Commit ID**: `d8e723a0b501918598e3f00a0cc4d158362ac5a5`
- **作者**: 謝閔皓
- **日期**: 2023-02-18 13:36:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 90. [E10]Q00-20230217002 修正子單身在Table模式下展開內容會無法完全顯示
- **Commit ID**: `6ce93b27005c6c21e18b924292715b333fbf4e11`
- **作者**: 林致帆
- **日期**: 2023-02-17 11:33:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 91. [WEB]Q00-20221216001調整腳本「更改Grid欄位寬度」的提示訊息。
- **Commit ID**: `0e4d323eb554ba72a9261b9ad06e0c8e8c0d4c81`
- **作者**: raven.917
- **日期**: 2022-12-16 11:20:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 92. [Web]Q00-20230221001 修正當關卡有設定「必須上傳新附件」，若透過追蹤流程「重新發起新流程」時，卡控是否有上傳附件的功能失效
- **Commit ID**: `839aabb5ece415db4f609884b89c433fdd0a016d`
- **作者**: waynechang
- **日期**: 2023-02-21 15:02:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 93. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
- **Commit ID**: `2ab74bb7d9edbd9cd336c3a3be8b1d14d25d99a0`
- **作者**: 林致帆
- **日期**: 2023-02-21 10:37:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 94. [WorkFlow]Q00-20230210001 修正從WorkFlow取得附件時因為URL為https導致取得失敗 [補修正]
- **Commit ID**: `26b908ca215211bab4bf813b3c06b352b5809d28`
- **作者**: 林致帆
- **日期**: 2023-02-13 18:35:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java`

### 95. [WorkFlow]Q00-20230210001 修正從WorkFlow取得附件時因為URL為https導致取得失敗
- **Commit ID**: `b5b2da5d76184c6f9596cc00c67e5c7bdec7c608`
- **作者**: 林致帆
- **日期**: 2023-02-10 14:44:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractTiptopMethod.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 96. Revert "[TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]"
- **Commit ID**: `03b3b981cc5aa33849792810bea4f7d59b1de702`
- **作者**: kmin
- **日期**: 2023-02-21 14:29:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 97. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
- **Commit ID**: `09eae3a215d71194fa7c71a4de8e1e3d4ccf3058`
- **作者**: 林致帆
- **日期**: 2023-02-21 10:37:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 98. [表單設計師]Q00-20230218002 修正Title元件在插入LOGO後，Title文字會跟著滾輪上下移動的問題
- **Commit ID**: `cf6fd1d663ba79cfee1d053986b3725f8d093446`
- **作者**: yamiyeh10
- **日期**: 2023-02-18 12:40:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 99. [流程引擎]Q00-20230216002 增加關卡判斷工作自動簽核跳關的log
- **Commit ID**: `84490b0c4f848054899e4fa099f0c855de7be551`
- **作者**: waynechang
- **日期**: 2023-02-16 17:53:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 100. [Web]A00-20230216001 修正核決關卡結束後被退回(取回)重新執行後，簡易流程圖的核決層級關卡名稱顯示錯誤
- **Commit ID**: `c3f5defa4650875c1bfc6737ccd063998b07a4e3`
- **作者**: waynechang
- **日期**: 2023-02-18 15:36:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 101. [表單設計師]Q00-20230216003 新增表單設計師中的元件代號與元件名稱不支持 _lbl、_txt、_txt1、_txt2
- **Commit ID**: `6fbc43d4bd574252094900f6570c34ecfacef18a`
- **作者**: 謝閔皓
- **日期**: 2023-02-16 18:48:18
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`

### 102. [組織同步] Q00-20230216001 調整ETL組織同步，使新寫出的暫存XML檔案轉為UTF-8
- **Commit ID**: `d0abf7cdfbd816686ab905a64766919a3b313aa1`
- **作者**: raven.917
- **日期**: 2023-02-16 10:41:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/lib/ETL/scriptella.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/etl/SyncOrgEtl.java`

### 103. [WEB] V00-20230214004 修正設置浮點數無法過濾特殊字元符號問題
- **Commit ID**: `02c1679bf37c91a027b386fdfe27b7ce1d1b9e54`
- **作者**: raven.917
- **日期**: 2023-02-15 10:44:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 104. [流程引擎]Q00-20221020003 修正核決關卡參考自定義關卡，且自定義關卡沒有掛載任何表單時，導致流程無法往下繼續派送
- **Commit ID**: `193f28fbfdb1ffcc49ff89e3a6a939c2f3733d62`
- **作者**: waynechang
- **日期**: 2023-02-15 10:59:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 105. [流程設計師]Q00-20230213007 修正在條件運算式中輸入超過整數最大值或最小值時執行上會因數值超過導致判斷走關卡異常問題
- **Commit ID**: `22afe6471a6ae07e06daed5140196e6145af4fad`
- **作者**: yamiyeh10
- **日期**: 2023-02-13 10:24:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/FormInstance.java`

### 106. [WEB] Q00-20230207002 調整絕對定位表單設置必填，隱藏標籤提示異常問題
- **Commit ID**: `27c8e0f96428ba5d0f0e7639210b4eafebc64497`
- **作者**: raven.917
- **日期**: 2023-02-14 11:40:10
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 107. [流程引擎]Q00-20230214001 修正流程在多人關卡且有設定撤銷事件進行撤銷，會造成DB LOCK
- **Commit ID**: `172017015b9a2cca6e678829606fb7c78645b24b`
- **作者**: 林致帆
- **日期**: 2023-02-14 15:46:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 108. [Web]Q00-20230215001 修正使用 https 且為 Safari 瀏覽器下載附件時，若檔名有中文會變成亂碼的問題
- **Commit ID**: `a813523ba9bd500ca6204d689b182993519e0521`
- **作者**: 謝閔皓
- **日期**: 2023-02-15 08:53:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 109. [WEB] V00-20221019001 修正監控流程設置「已撤銷」，無法匯出Excel
- **Commit ID**: `9a907b6f4a0f51671448bd4f52e188afa252cf1d`
- **作者**: raven.917
- **日期**: 2023-02-08 20:06:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 110. [內部]Q00-20221124005 調整downloadImage的URL服務的ContentType為png
- **Commit ID**: `6d262bd6466c30eab865e3dc418166eceb4a57a7`
- **作者**: waynechang
- **日期**: 2022-11-24 16:24:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 111. [WEB] A00-20230207001 調整水平線樣式變更時的邏輯，改為修改樣式後複製一個再刪除舊的
- **Commit ID**: `f151a585f11771be853b801619c077c5cd186e74`
- **作者**: raven.917
- **日期**: 2023-02-14 16:34:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 112. [Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題[補]
- **Commit ID**: `12fef97bbd5b3cd86dd82a057d93c05ec5bd04b3`
- **作者**: pinchi_lin
- **日期**: 2023-02-08 16:46:22
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-performWorkItem-config.xml`

### 113. [Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題[補修正]
- **Commit ID**: `7d8e5bc8403e5bcd126f880cb4f62dc0782c6f5d`
- **作者**: 謝閔皓
- **日期**: 2023-01-30 15:11:00
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 114. [Web]Q00-20221122001 修正流程追踪若被鑲嵌在首頁中，返回會有異常的問題
- **Commit ID**: `fd7783017c08153ef440058034cc5d138c3083a0`
- **作者**: 謝閔皓
- **日期**: 2023-01-18 09:06:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 115. [WEB]S00-20220315002-在追蹤流程以及監控流程下，結案的流程會按照不同的結案結果呈現。
- **Commit ID**: `9b9f33dba2aa76cf4c2cf0785fa70259126b6a83`
- **作者**: raven.917
- **日期**: 2022-12-28 18:04:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 116. [WorkFlow]Q00-20221014006 調整WorkFlow拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能 [補修正]
- **Commit ID**: `29c666251010e650ab9f89604dc8266b96641872`
- **作者**: 林致帆
- **日期**: 2023-01-09 11:46:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 117. [Web]Q00-20221221002 修正 TextArea 資料內容過多在列印表單時，會蓋到簽核意見的問題
- **Commit ID**: `747ab8f98ee60b45a6560729540a60986e51a898`
- **作者**: 謝閔皓
- **日期**: 2022-12-21 16:41:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bootstrap-3.3.5-print.css`

### 118. [Web]Q00-20221206001 修正BPM頁面在Chrome的網址列案Enter會導頁到錯誤頁面
- **Commit ID**: `bd94e7b292529763b1fdeb4a350e977d15deb6f1`
- **作者**: 林致帆
- **日期**: 2022-12-06 15:14:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/struts/action/ActionServlet.java`

### 119. [WEB]Q00-20221213001 Update與Delete模組程式維護後，才需一併更新NavigatorMenu。
- **Commit ID**: `3babaf9e38876ee9393805be654df8700b49b13c`
- **作者**: raven.917
- **日期**: 2022-12-13 10:23:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`

### 120. [Web]A00-20230204001 修正追蹤流程中，查看已轉派工作且已處理的單據，點擊回到工作清單卻呈現工作通知清單的問題
- **Commit ID**: `09b3037dff7f3ee18a4a1a936d8b31ece7a78b66`
- **作者**: 謝閔皓
- **日期**: 2023-02-07 13:15:34
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 121. [Web]Q00-20230130002 修正待辦清單有設定流程條件，使用者簽核完跳至下一個流程會找到非條件的流程
- **Commit ID**: `99d14b711cf9b012668d6c7d25e64340dc70eb1c`
- **作者**: 林致帆
- **日期**: 2023-01-30 17:44:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 122. [流程設計師]Q00-20230207001 調整流程設計師簽入流程或匯入流程時，增加核決關卡的關卡Id校驗，避免因複製核決關卡功能導致流程定義異常而影響流程實例無法開啟
- **Commit ID**: `2a5d544f89a010bf6204a1b972f326175532f8e8`
- **作者**: waynechang
- **日期**: 2023-02-07 10:30:37
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/controller/CMManager.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BpmUtil.java`

### 123. [表單設計師]Q00-20230202001 修正表單設計師的 Time 元件，若在基本設定中的輸入框有設定預設值，儲存後再次簽出表單，該 Time 元件於畫面呈現時，其顯示值會有不正確的問題，且還有右下輸入框的預設值移動到提示文字的問題
- **Commit ID**: `9f65661d2b93c81edab89481ddd708463f855820`
- **作者**: 謝閔皓
- **日期**: 2023-02-02 14:33:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 124. [WEB] Q00-20230203002 修正絕對定位表單，預覽列印下，RadioButton顯示異常
- **Commit ID**: `9a757d7542a8e7a55ec798a68ae83c70d3e17956`
- **作者**: raven.917
- **日期**: 2023-02-03 12:19:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 125. [Web]Q00-20221202002 修正 Firefox 瀏覽器開啟絕對位置表單，使用列印表單的功能，畫面顯示異常的問題
- **Commit ID**: `b0fddc8819ff1cd2e277239db6d283b2f9f94151`
- **作者**: 謝閔皓
- **日期**: 2022-12-02 16:08:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`

### 126. [WEB]Q00-20230104006修正列印模式下，絕對位置表單RadioButton顯示異常
- **Commit ID**: `1db60e8c5747cbfead975153ead8c1b9ab5a0824`
- **作者**: raven.917
- **日期**: 2023-01-04 23:48:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 127. [表單設計師]A00-20230131001 修正表單設計師的 Date 元件，若在基本設定中的輸入框有設定預設值，儲存後再次簽出表單，該 Date 元件於畫面呈現時，其顯示值會有不正確的問題
- **Commit ID**: `3a9836be51415c7744dcc2a366c342e1b445ad82`
- **作者**: 謝閔皓
- **日期**: 2023-02-01 16:57:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 128. [雙因素模組]Q00-20230113004 修正取得資料庫寫法未增加釋放連線
- **Commit ID**: `f758522c956823b65e64a5c6207580ef9077cb97`
- **作者**: 林致帆
- **日期**: 2023-01-13 18:43:32
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/TFAConfigManagerBean.java`

### 129. [TIPTOP]Q00-20230131001 修正 TIPTOP 差勤模組，若在 BPM 簽核時輸入＆ 符號，回傳給 TIPTOP 呈現時會有異常的問題
- **Commit ID**: `55a57de8e901b619348ebeb3341e514a84d2200c`
- **作者**: 謝閔皓
- **日期**: 2023-01-31 08:53:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/executor/CreateFormToTPExecutor.java`

### 130. [TIPTOP]Q00-20230130001 修正回傳 TIPTOP 單據讀取簽名圖檔時，若環境為 Linux 且檔案名稱有英文大小寫，可能會造成檔案讀取失敗的問題
- **Commit ID**: `35d92b0ee506454621d9415dc7056fdd06291024`
- **作者**: 謝閔皓
- **日期**: 2023-01-30 16:57:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 131. [Web]Q00-20230116001 上傳附件功能，優化使用者提示
- **Commit ID**: `5f0ac628352c619808379518cbd7d9f35632ca95`
- **作者**: 謝閔皓
- **日期**: 2023-01-16 11:01:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 132. [Web]Q00-20230113003 修正簽核意見內容有輸入換行，但通知信的簽核意見內容卻沒有換行的問題
- **Commit ID**: `68ba14e785d9924449acef8ef0afa71bfabf1bcc`
- **作者**: 謝閔皓
- **日期**: 2023-01-13 18:04:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 133. [表單設計師]Q00-20230113002 隱藏 SubTab 元件右鍵的菜單，已確認 SubTab 元件與其頁籤在最初 ******* 版就不支援複製的功能
- **Commit ID**: `ca54b83d144f34cea5bad0799331f35e037396da`
- **作者**: 謝閔皓
- **日期**: 2023-01-13 14:27:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 134. [WEB] Q00-20230112001 修正T100拋單附件為URL時，不會計算點按次數。
- **Commit ID**: `f5b3389014ef9b7bc282d37fe5f157f1e2061f13`
- **作者**: raven.917
- **日期**: 2023-01-12 18:28:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 135. [BPM APP]C01-20221130002 修正移動端絕對位置表單點擊附件按鈕沒反應與附件沒有顯示的問題
- **Commit ID**: `698732dfd433522d21de335446985f221803e89b`
- **作者**: 郭哲榮
- **日期**: 2022-12-12 18:25:29
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`

### 136. [在線閱覽]Q00-20221123002 調整通知關卡、追蹤流程(一般使用者)頁面，當表單附件為在線閱讀，且關卡的附件設置為fullcontrol時，需顯示下載附件的按鈕
- **Commit ID**: `6225e97ec9ee79929ce6ad7d601531e4e7d9ede9`
- **作者**: waynechang
- **日期**: 2022-11-23 14:32:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`

### 137. [Web]Q00-20230112004 修正 TextBox 元件的進階設定，若設定資料型態為浮點數且顯示千分位會導致顯示值異常的問題
- **Commit ID**: `13f7d5b5448bf8724944fe5ac7db36f47bffd238`
- **作者**: 謝閔皓
- **日期**: 2023-01-12 17:49:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`

### 138. [WorkFlow]Q00-20230112003 修正取簽核歷程未帶入FormId就導致歷程無法取得
- **Commit ID**: `fc1d73522b5f324cae6021d5a97dc371cbb23d28`
- **作者**: 林致帆
- **日期**: 2023-01-12 17:26:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBWFRequestRecordDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 139. [Web]Q00-20230110004 修正Grid單頭與單身欄位不對齊的問題[補]
- **Commit ID**: `86b916a4a1dafbf386d689f00674957253fc9b38`
- **作者**: cherryliao
- **日期**: 2023-01-12 16:08:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3_BPMcustomized.js`

### 140. [流程引擎]Q00-20230112002 修正Oracle資料庫，若流程有設計執行服務任務並將回傳值回寫至流程變數時，當回傳值為空字串時，服務任務會報無法更新STRINGWORKFLOWRUNTIMEVALUE為NULL的錯誤
- **Commit ID**: `855119b7c7cc33106e90b947069b2b50392a0a22`
- **作者**: waynechang
- **日期**: 2023-01-12 14:23:34
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/field_handler/database/String2StringNullAsEmptyStringConverter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/workflow_engine/StringWorkflowRuntimeValue.java`
  - ➕ **新增**: `Release/db/create/InitNaNaDB_MSSQL.sql`
  - ➕ **新增**: `Release/db/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.1_DDL_MSSQL.sql`
  - ➕ **新增**: `Release/db/update/5.8.9.1_DDL_Oracle.sql`

### 141. [WorkFlow]Q00-20230105002 修正回傳WorkFlow處理者沒有被更新成功 [補修正]
- **Commit ID**: `5c4e377c449bd5a7a02dabc54b42d894c37517c3`
- **作者**: 林致帆
- **日期**: 2023-01-07 11:18:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 142. [WorkFlow]Q00-20230105002 修正回傳WorkFlow處理者沒有被更新成功
- **Commit ID**: `5b2740203077b56452259445674fdaeb48ae9d6f`
- **作者**: 林致帆
- **日期**: 2023-01-05 16:04:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 143. [Web]A00-20230106001 修正 DialogInputLabel 元件，若在流程設計師中的表單存取控管設定權限為 Disable，於表單畫面中欄位還可修改的問題
- **Commit ID**: `929f915f240e9faedcfe1c1e2c834554d402a26e`
- **作者**: 謝閔皓
- **日期**: 2023-01-07 16:04:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 144. [Web]Q00-20230106002 修正自行撰寫的查詢樣版有設定排序的功能，若沒有輸入查詢條件會無法排序的問題
- **Commit ID**: `7efa6b89a11de7a524cd263d9b68467a070c9678`
- **作者**: 謝閔皓
- **日期**: 2023-01-06 14:46:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 145. [Web]Q00-20230110005 修正左側功能列點擊模擬使用者會重新刷新左側功能列
- **Commit ID**: `c9ae17f983aef0d00cd17ab827e4f63403f7b231`
- **作者**: 林致帆
- **日期**: 2023-01-10 17:26:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`

### 146. [WEB]A00-20230110001 修正時間元件驗證異常錯誤。
- **Commit ID**: `1226e5ae4969aa05f9f02f12fb3d15e3ebb03bd6`
- **作者**: raven.917
- **日期**: 2023-01-10 18:11:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 147. [Web]Q00-20230110004 修正Grid單頭與單身欄位不對齊的問題
- **Commit ID**: `cbee205326a851dc353c375684e341973b72427e`
- **作者**: cherryliao
- **日期**: 2023-01-10 13:50:02
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-1.18.3_BPMcustomized.js`

### 148. [ISO]Q00-*********** 修正ISO變更單載入上一版附件時；當文件原始檔存在ISOSource+流水號時，無法載入上一版附件異常
- **Commit ID**: `1ce470301bd329955688e50b850886844baec4e0`
- **作者**: waynechang
- **日期**: 2022-11-28 14:54:22
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/RemoteDocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 149. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯 [補修正]
- **Commit ID**: `26594eb7c1e4cf51be603fa2fe5fa232948772fa`
- **作者**: 林致帆
- **日期**: 2023-01-06 10:44:17
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactory.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 150. [Web]Q00-20221213004 在Users資料被移除造成流程實例開啟異常的狀況下，優化異常訊息
- **Commit ID**: `41badce6a38ea1a2c989dd3bc5bd32f56d747406`
- **作者**: 林致帆
- **日期**: 2022-12-13 11:45:47
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessTraceControllerBean.java`

### 151. [TIPTOP]Q00-*********** 修正TIPTOP開啟BPM簽核頁面登入其他使用者就報錯
- **Commit ID**: `4a63e6acb106a59f5974bfb620cefc5e515ba3b6`
- **作者**: 林致帆
- **日期**: 2023-01-04 17:38:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/AbstractMethodGetUrl.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 152. [流程引擎]Q00-20230104003 修正寄送Mail在沒有CC收件人情況下不應副本給收件人的問題
- **Commit ID**: `892c0210e9eed6e9b2d1d88462fb6e6050edbd49`
- **作者**: cherryliao
- **日期**: 2023-01-04 11:29:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/MailUtil.java`

### 153. [WEB]Q00-20221228004 修正舊版表單輸入法 yyyy/MM/dd 日期解析錯誤異常[補修正]
- **Commit ID**: `8f079f3d2d69f0f9c1948bee09570bc1e892960f`
- **作者**: raven.917
- **日期**: 2023-01-05 00:53:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 154. [WEB]Q00-20221228004 修正舊版表單輸入法 yy/MM/dd 日期解析錯誤異常[補修正]
- **Commit ID**: `d4e050e75debeb372f0fcdc7a0f2c600235a3b2e`
- **作者**: raven.917
- **日期**: 2022-12-29 10:53:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 155. [WEB]Q00-20221228004 修正舊版表單輸入法 yy/MM/dd 日期解析錯誤異常
- **Commit ID**: `1a2510b94bff54a111d9a2d8767f2a544e5d139e`
- **作者**: raven.917
- **日期**: 2022-12-28 15:53:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 156. [流程引擎]Q00-20221129001修正修正nchar欄位型態錯誤比對問題，導致轉存表單存空值。
- **Commit ID**: `b65815531c6fc4610af751ea73297a2cea217ce3`
- **作者**: raven.917
- **日期**: 2022-11-29 15:12:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/form/FormDataMigrateHandlerBean.java`

### 157. [WEB]Q00-20230104002 修正RWD表單在TextBox元件調整字體大小後使用iOS手機查看時欄位中的字不能完整呈現
- **Commit ID**: `560cd2674a347888adb5f7fcc355f3373c36982a`
- **作者**: yamiyeh10
- **日期**: 2023-01-04 11:18:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 158. [WEB]Q00-20221128006調整絕對定位表單RadioButton顏色更清楚。
- **Commit ID**: `12771055736249a2a53558f4cd05ec3e7ec76a16`
- **作者**: raven.917
- **日期**: 2022-11-28 17:03:57
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-form-component.css`

### 159. [Web]Q00-20230103002 修正系統設定使用 LDAP 驗證，若使用者沒有設定 LDAP 驗證，從通知信連結進入 BPM 登入頁時，帳號欄位沒有自動帶入 UserId 的問題
- **Commit ID**: `e32501b5067cf514fd857f54b866f46818571912`
- **作者**: 謝閔皓
- **日期**: 2023-01-03 18:17:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 160. [BPM APP]C01-20221226004 修正絕對位置表單且行動版絕對位置在轉RWD表單時報錯的問題
- **Commit ID**: `8ba8212622a5b29c65835d3da4b0f9d584ca50d6`
- **作者**: 郭哲榮
- **日期**: 2022-12-29 20:43:36
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/AbsoluteFormBluePrint.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 161. [Web]Q00-20221228003 修正用任何語系登入，表單按鈕開窗用資料選取註冊器，Grid標籤跟模糊查詢標籤內容都只會顯示預設值[補修正]
- **Commit ID**: `3c84629b940c57e58c8635dd7203861b8d35753a`
- **作者**: 謝閔皓
- **日期**: 2022-12-29 17:22:50
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/form/CustomDataChooserDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 162. [Web]Q00-20221228003 修正用任何語系登入，表單按鈕開窗用資料選取註冊器，Grid標籤跟模糊查詢標籤內容都只會顯示預設值
- **Commit ID**: `75e33120b2ddd88a39f371e9bbc6d26cea7c1df1`
- **作者**: 謝閔皓
- **日期**: 2022-12-28 15:29:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 163. [Web]Q00-20221227001 修正ESS流程圖開啟失敗
- **Commit ID**: `3cd637921fed01f69fe159a0c15fdc875b82396c`
- **作者**: 林致帆
- **日期**: 2022-12-27 13:44:46
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - ➕ **新增**: `Release/wildfly/modules/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 164. [流程引擎]Q00-20221212001 調整ajax_ProcessAccessor.findFieldValueById取得表單內容方法，當流程同時掛載多表單時，偶發回傳找不到該欄位內容的錯誤
- **Commit ID**: `e8f9587934da40c34f7b571cb1e3a0a0f0eb3e03`
- **作者**: waynechang
- **日期**: 2022-12-12 13:52:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 165. [流程引擎]Q00-20221125001 調整SystemConfig資料表改成二階快取
- **Commit ID**: `22e0102b0610f70d13de288cdbfa73b722e8cd34`
- **作者**: 林致帆
- **日期**: 2022-11-25 14:27:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/domain/SystemConfig.java`

### 166. [內部]Q00-20221202001 調整Queue派送自動簽核關卡時，增加檢查WorkItem的處理者是否存在的防呆
- **Commit ID**: `47a9474efbebb2d1c307c42deee2cabd8c9cdca5`
- **作者**: waynechang
- **日期**: 2022-12-02 12:05:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`

### 167. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `208a8254e2ab4cb45d4438c522f16753a83eca44`
- **作者**: 林致帆
- **日期**: 2022-12-26 13:59:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 168. [流程引擎]Q00-20221221003 調整如果流程前兩關發起時間相同，會造成自動簽核為流程中有相同簽核者(不含發起者)就會沒有作用 [補修正]
- **Commit ID**: `c7f86eb14d306d62e476a537c10367a915ab2c89`
- **作者**: 林致帆
- **日期**: 2022-12-26 11:59:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 169. [流程引擎]Q00-20221221003 調整如果流程前兩關發起時間相同，會造成自動簽核為流程中有相同簽核者(不含發起者)就會沒有作用
- **Commit ID**: `ddba51651f854fb3fdcaa82e3619f0f014fec89b`
- **作者**: 林致帆
- **日期**: 2022-12-21 17:40:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 170. [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現 [補修正]
- **Commit ID**: `eb4722b10705ed5dff1b7f8514a90e2550932958`
- **作者**: 林致帆
- **日期**: 2022-12-19 14:35:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 171. [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現 [補修正]
- **Commit ID**: `aba6e729c619223cdb663339164dfaebaa058fdd`
- **作者**: 林致帆
- **日期**: 2022-12-15 10:33:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 172. [Web]A00-20221212001 修正水平線元件在invisible的狀態下，上傳附件及列印表單都會出現
- **Commit ID**: `a9c1247075b03f9c88be983220045f4ec2bc16a8`
- **作者**: 林致帆
- **日期**: 2022-12-15 10:31:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 173. [WEB]Q00-20221223001 流程資料查詢頁面無法下載附件
- **Commit ID**: `911099b17043199a6f89ceca4e06d00b38afafe6`
- **作者**: raven.917
- **日期**: 2022-12-23 10:28:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 174. [Web]Q00-20221124001 調整使用BPM外部URL連結跳轉畫面時表單內的表單名稱以多語系顯示
- **Commit ID**: `8195cd30fe86fcfb97c48029c19a32a48ea31633`
- **作者**: cherryliao
- **日期**: 2022-11-24 13:35:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 175. [表單設計師]Q00-20221226001 修正 TextBox 元件，若資料型態設定為浮點數且小數點後幾位，會導致沒有對齊的問題
- **Commit ID**: `1e60869e318ed76308c6821116a99ab90214913d`
- **作者**: 謝閔皓
- **日期**: 2022-12-26 10:17:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 176. [WorkFlow]Q00-20221221001 調整workflow整合WF熱鍵同步表單增加欄位Id的卡控判斷；當傳入的XML若表單元件沒有Id時，直接回傳因欄位沒有Id，所以同步失敗的xml
- **Commit ID**: `185613903ceaf85af232615137b629577d434c7d`
- **作者**: waynechang
- **日期**: 2022-12-21 14:17:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/SystemIntegration.java`

### 177. [DT]C01-20221201005 優化Web化資料使用權限管理頁面開啟緩慢問題
- **Commit ID**: `4e08db87d83fc8461069275bbbfe293d27844978`
- **作者**: cherryliao
- **日期**: 2022-12-20 16:47:16
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java`

### 178. [DT]C01-20221214005 修正Web化系統管理工具的系統郵件在Oracle環境下不存在帳號時會頁面異常問題
- **Commit ID**: `e69be8fc0d82581c5b98b40b8703f4fac1374d7c`
- **作者**: yamiyeh10
- **日期**: 2022-12-14 16:40:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java`

### 179. [流程引擎]Q00-20221212003 修正併簽流程；若其中一個分支直接退回到分支以前的關卡且流程設定被退回時逐關通知，其他分支執行中關卡也一併被關閉的異常
- **Commit ID**: `f0d728475a9f7cd8431895c6247e959c36151c58`
- **作者**: waynechang
- **日期**: 2022-12-12 17:43:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 180. [Web]S00-20220711001Textbox元件設置整數及浮點數自動進位輸入值。
- **Commit ID**: `36839bfa8628b3f0ae6fa0efdf26640a4194121a`
- **作者**: raven.917
- **日期**: 2022-10-25 15:06:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormManager.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 181. [流程引擎]Q00-20221208002 修正流程最後一個關卡為服務任務，且系統參數「traceprocess.view.workitem.with.first.activity」設定為false時，系統管理員透過追蹤流程進入流程時，會提示查不到此流程的資料
- **Commit ID**: `8309c54d2bef01917e65a6db4978cd0b060b6010`
- **作者**: waynechang
- **日期**: 2022-12-08 16:37:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/RelevantDataViewer.java`

### 182. [流程引擎]Q00-20221209002 T100拋單若第一關與第二關的建立時間相同，自動簽核選擇與前一關相同簽核者就會無效
- **Commit ID**: `cab91544cc72a2e8fe178c036f8a241d4de5cdf9`
- **作者**: 林致帆
- **日期**: 2022-12-09 17:09:53
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/WorkItemTimeComparator.java`

### 183. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `21accc56c2b3759f827db319a33e41fdff837fa6`
- **作者**: 林致帆
- **日期**: 2022-12-05 14:41:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`

### 184. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能[補修正]
- **Commit ID**: `149a51b01a31f87c69b3c44dfeab84e1bfbdf106`
- **作者**: 林致帆
- **日期**: 2022-12-05 13:35:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 185. [TIPTOP]S00-20221123001 調整TIPTOP拋單傳附件時，如果關卡設置第一關為"上傳附件時允許修改是否使用在線閱讀"，就呈現在線閱讀功能
- **Commit ID**: `8ca0fbd9695e3ccb487b3ec8b577434c7e9e6d1c`
- **作者**: 林致帆
- **日期**: 2022-12-01 18:19:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 186. [流程引擎]Q00-20221201001 修正核決關卡的處理者若符合自動簽核時，核決關卡偶發無法繼續派送下去
- **Commit ID**: `14fcb152c150a53595f926590bc8f1380f74bf6f`
- **作者**: waynechang
- **日期**: 2022-12-01 14:06:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/comparator/ActInstTimeComparator.java`

### 187. [WEB]Q00-20221123001 若tDialogType自定義開窗時，不應產生相應的Script語法。
- **Commit ID**: `b3f2a7f56075a4bbcfba365bf42b1963cefe5538`
- **作者**: raven.917
- **日期**: 2022-11-23 09:01:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 188. [流程引擎]Q00-20221121001 修正流程寄信內容是整張表單，且表單元件為浮點數且為空的狀況會派送失敗
- **Commit ID**: `24ca6eb4cd593538f5111a31bb979d5713a49cbd`
- **作者**: 林致帆
- **日期**: 2022-11-21 18:02:16
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`

### 189. [Web]V00-20221020001 修正切換頁面時，若讀取時間較長，會先呈現原畫面再跳轉的問題
- **Commit ID**: `2a46c5a8ba4b041d20c5d3101b5be261e2549bb4`
- **作者**: 謝閔皓
- **日期**: 2022-11-19 19:39:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js`

### 190. [Web]Q00-20221118002 修正附件太多導致往下派送失敗
- **Commit ID**: `a64e321202c7b075c623cb1859541ceee7478961`
- **作者**: 林致帆
- **日期**: 2022-11-18 14:57:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 191. [Web]Q00-*********** 修正一般使用者簽核 T100 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊
- **Commit ID**: `2c55aa1fd3bff9285475b300011b3c11d9b864f9`
- **作者**: cherryliao
- **日期**: 2022-11-18 14:25:40
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysNewTiptopToolDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopTool.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/SysNewTiptopToolBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 192. [Web]Q00-*********** 修正一般使用者簽核 TIPTOP 單據時，點選退件表單資訊會顯示不同營運中心的表單資訊
- **Commit ID**: `a2a9ecf2fd9aa46548d535bf854365697186a2fa`
- **作者**: 謝閔皓
- **日期**: 2022-11-17 17:41:12
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/IPrsMappingKeyDAO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/dao/OJBPrsMappingKeyDAO.java`

### 193. [DT]C01-*********** 修正Web化系統管理工具流程主機設定在編輯儲存後導致其他使用者登入BPM後顯示空白畫面問題
- **Commit ID**: `f3b770bb2d3f6d91dae4437210050e52f997ae7e`
- **作者**: yamiyeh10
- **日期**: 2022-11-17 16:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/SystemConfigMgr.java`

### 194. [在線閱覽]Q00-*********** 修正在線閱覽開啟檔案的URL，當文件主機設置的WebAddress最後一碼為斜線時需過濾，避免開啟閱讀檔案後，點擊其他BPM功能會被導入登入頁
- **Commit ID**: `30a4f405b707ae4656df9ea6fcbc5f73ac99535f`
- **作者**: waynechang
- **日期**: 2022-11-17 12:04:11
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`

### 195. [BPM APP]C01-20221109006 修正移動端Grid元件在不可新增但可編輯與刪除時能看到查看更多按鈕的問題
- **Commit ID**: `fefd5c2cb34e9a17f16a64db8569b14b36cd8200`
- **作者**: 郭哲榮
- **日期**: 2022-11-17 10:13:34
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js`

### 196. [DT]C01-20221114005 修正Web化系統管理工具TIPTOP整合設定中對映索引修改後沒儲存問題
- **Commit ID**: `5d8d71445a2f10293cbd79762f1fb91f51dac800`
- **作者**: pinchi_lin
- **日期**: 2022-11-17 10:03:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/design_tool_web/TiptopSystemIntegrationMgr.java`

### 197. [流程引擎]Q00-20221117001 修正自動簽核在多人處理關卡上沒有效果
- **Commit ID**: `36bdee1880be4a0f4b3018fb8a959d490447b216`
- **作者**: 林致帆
- **日期**: 2022-11-17 09:23:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 198. [Web]Q00-20221116003 修正 Checkbox、RadioButton 元件，若文字過多造成換行時，勾選按鈕會有偏移的問題
- **Commit ID**: `b8f4b4453541a2a9b28979b7b816b7848e97cf17`
- **作者**: 謝閔皓
- **日期**: 2022-11-16 17:52:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`

### 199. [BPM APP]C01-20221025006 修正企業微信未進入菜單前從推播進入表單畫面時空白問題
- **Commit ID**: `ea0964c68f4dac4caac828eb364237b566d7ed28`
- **作者**: 郭哲榮
- **日期**: 2022-11-16 14:40:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`

### 200. [Web]Q00-20221116002 修正個人資訊頁面載入，因為雙因素認證沒資料導致報錯
- **Commit ID**: `b11c86b923037dc5c91666369313da06df79d16a`
- **作者**: 林致帆
- **日期**: 2022-11-16 14:15:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageUserProfileAction.java`

### 201. [BPM APP]C01-20220922002 修正移動端主旨與表單內容重疊跟取不到簽核歷程報錯問題
- **Commit ID**: `f39465ca13f15565f960fcbf8ee20b0ebd236b34`
- **作者**: 郭哲榮
- **日期**: 2022-11-15 18:20:46
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileResigend.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js`

### 202. [Web]Q00-20221115002 修正流程設計師/流程模型/進階的主旨範本若有換行，會導致流程資料/流程資料查詢的查詢畫面無法顯示的問題
- **Commit ID**: `551a8b4af4b76208a5aa93500949abdbd665f238`
- **作者**: 謝閔皓
- **日期**: 2022-11-15 15:04:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/searchFormData/FormInstResultForSearching.java`

### 203. [Web]Q00-20221111005 修正員工代號有大寫時，使用iReport的列印功能會發生異常問題
- **Commit ID**: `57c3456ff5bb5d2d46018050434aa93bc9450345`
- **作者**: yamiyeh10
- **日期**: 2022-11-15 14:02:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`

### 204. [Web]Q00-20221114005絕對定位表單及RWD表單，統一可設定背景色設定。
- **Commit ID**: `a0b09ba6b5e4a52297ff25fd545f3b47b112ee7d`
- **作者**: raven.917
- **日期**: 2022-11-14 18:21:16
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/LinkElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SelectElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`

### 205. [WEB]Q00-20221114003 修正5884版本絕對位置表單下載附件異常，無法下載檔案
- **Commit ID**: `7bd8e1c6be8a4de2ab27479bd1137fce1fda873d`
- **作者**: waynechang
- **日期**: 2022-11-14 14:31:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`

### 206. [Web]Q00-20221114002修正表單設計師Barcode元件異常問題。
- **Commit ID**: `cace94c2fe820f003d2fd9b5bedded1fce08eda4`
- **作者**: raven.917
- **日期**: 2022-11-14 12:28:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 207. [WorkFlow]Q00-20221114001 修正附件URL帶有空格導致拋單敗
- **Commit ID**: `c06a488ce947eef80574dd70859d14b452dac475`
- **作者**: 林致帆
- **日期**: 2022-11-14 10:42:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/util/TiptopUtil.java`

### 208. [BPM APP]C01-20221018001 修正移動端Grid元件因換行符號導致無法正常顯示Grid資料的問題
- **Commit ID**: `aa8dbbe7ef556ba95a7db183f5d4f03fa802a90d`
- **作者**: 郭哲榮
- **日期**: 2022-11-11 17:34:54
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/GridElementMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FormElementStyle.css`

### 209. [Web]Q00-20221111001 調整當使用者session過期時,撈取待辦、通知事項等總數出錯時不往前端拋訊息
- **Commit ID**: `5846a40d1a707fc319ab804069734d00d195d80b`
- **作者**: cherryliao
- **日期**: 2022-11-11 11:07:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 210. [Web]Q00-*********** 修正逾期授權的人數也算進總授權數裡
- **Commit ID**: `acbc5a2795816ada5acaabcb14fd54828d38114a`
- **作者**: 謝閔皓
- **日期**: 2022-11-10 11:40:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/dao/OJBLicenseRegDAO.java`

### 211. [流程引擎]Q00-20221109001 調整流程圖點選核決權限關卡，核決關卡改以關卡建立時間排序
- **Commit ID**: `7ada799a6b11a39b30de631e1c3386a6300accb4`
- **作者**: waynechang
- **日期**: 2022-11-09 17:27:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessTracer.java`

### 212. [流程引擎]Q00-20221108003 修正流程引擎的加簽函式功能「addCustomParallelAndSerialActivity」，加簽出來的關卡的表單未依照「參考關卡」呈現對應的「表單元件顯示」狀態
- **Commit ID**: `0ea3e8f32a1f88233795dfe16d4108978e43d4fc`
- **作者**: waynechang
- **日期**: 2022-11-08 16:22:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 213. [Web]Q00-20221108001修正輸入元件設置必填後，沒勾選隱藏標籤原label標籤會出現undefined
- **Commit ID**: `c63012432d0225424e4c3dbea899b7e44acb4ef2`
- **作者**: raven.917
- **日期**: 2022-11-08 15:25:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 214. [Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日(修正多語系)
- **Commit ID**: `ea49cc2c6bd451e4d6a33c4813cde057b00855c7`
- **作者**: raven.917
- **日期**: 2022-11-07 10:21:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 215. [Web]S00-20220818003 修正預設天數上限，最多不可設置超過180日
- **Commit ID**: `0b638db52c710f0de3d7cdacae7035fbe2febc41`
- **作者**: raven.917
- **日期**: 2022-11-07 10:15:56
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_Oracle_1.sql`

### 216. [表單設計師]Q00-20221106001 修正表單設計師中設置輔助格線的貼齊刻度無法暫存修改後的參數
- **Commit ID**: `f08d120064769a64acd928a602cb0a34b258564f`
- **作者**: 謝閔皓
- **日期**: 2022-11-06 16:37:01
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/form-builder.js`

### 217. [Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。(補修正)
- **Commit ID**: `f675ffb4677941cd5b8c95f1d495e3f30749d4f0`
- **作者**: raven.917
- **日期**: 2022-11-04 17:32:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`

### 218. [Web]S00-20220818003 追蹤流程預設區間出貨為30天 ， 開放給使用者可以設定區間天數，最多不可超過120日。
- **Commit ID**: `4f9afd5c868a813901192d7164c405a4c56bbe63`
- **作者**: raven.917
- **日期**: 2022-11-04 17:06:47
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.9.1_DML_Oracle_1.sql`

### 219. [Web]Q00-20221104004 修正通知關卡指定離職人員時，離職交接人沒有作用。
- **Commit ID**: `ddc8286c4f162d300aca7a9361b7fccd27177010`
- **作者**: raven.917
- **日期**: 2022-11-04 15:59:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 220. [內部]Q00-20221104002 調整觸發自動簽核時間點的log
- **Commit ID**: `33ab9934d8e1849cf0c30642339c42c55e854354`
- **作者**: waynechang
- **日期**: 2022-11-04 11:40:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`

### 221. [BPM APP]C01-20220927008 修正移動端Grid顯示畫面上按鈕重疊問題
- **Commit ID**: `a54cfacb2f7eaa1f2754a5e706331a2607d62cbb`
- **作者**: 郭哲榮
- **日期**: 2022-11-03 18:57:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/FixAbsoluteFormStyle.css`

### 222. [流程設計師]Q00-20221103003 修正流程定義/事件處理/流程完成/網頁應用程式，第一次點擊編輯時畫面空白的問題
- **Commit ID**: `a3d600a41013c5a14d84b3bc31779a85a6036c03`
- **作者**: 謝閔皓
- **日期**: 2022-11-03 18:09:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/application/FormalParametersCellEditorRenderer.java`

### 223. [流程引擎]A00-20221103001 修正流程繼續派送後或有通知關卡會重複寄信
- **Commit ID**: `bf5499db5c93ab7115b4605b5812a50007571a6a`
- **作者**: 林致帆
- **日期**: 2022-11-03 17:41:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 224. [WEB]Q00-20221103001 使用者撤銷流程，理由填空白字串時不允許撤銷流程
- **Commit ID**: `0c707a452bcc259eb0ecba0e1354f23348bee124`
- **作者**: yamiyeh10
- **日期**: 2022-11-03 11:24:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`

### 225. [Tiptop]Q00-20221031002 修正log沒有辦法正常換日的問題，全部jar替換。
- **Commit ID**: `cc4ac54b5a45e3fa3122c13f456bec43a23716b4`
- **作者**: raven.917
- **日期**: 2022-11-03 10:37:55
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/lib/CruiseControl/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/designer-common/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/org-designer-blink/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/lib/Log4J/log4j.jar`
  - 📝 **修改**: `3.Implementation/subproject/webapp/lib/Log4J/log4j.jar`

### 226. [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
- **Commit ID**: `6a4bb6bffd198b538d19fd0bf94357d3692ca3c9`
- **作者**: waynechang
- **日期**: 2022-11-02 18:07:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 227. [Web]Q00-*********** 修正checkbox設計時，若有勾選「最後一個選項額外產生輸入框」，表單中checkbox呈現與列印不一致的問題
- **Commit ID**: `d4d03c1e1ba992fdd7df784a6deba949bd8541a2`
- **作者**: 謝閔皓
- **日期**: 2022-11-02 14:53:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 228. [WEB]Q00-20221101005 修正在表單上設定運算規則時有參考單身加總的元件時不會自動觸發更新的問題
- **Commit ID**: `d9388ed93e5018361c80c7a40ba7ed2ef35178a1`
- **作者**: yamiyeh10
- **日期**: 2022-11-01 17:40:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 229. [WEB]Q00-20221028003 補修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)
- **Commit ID**: `6b159215c2fde8f7ed7c73b510678fc63a04da0f`
- **作者**: raven.917
- **日期**: 2022-11-01 16:41:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 230. [Web]Q00-20221101003 修正使用者若有離職作業維護，點選離職人員會跳到登入畫面的問題
- **Commit ID**: `246d1ec0f14148c933baa369488376d6c2ab6588`
- **作者**: 謝閔皓
- **日期**: 2022-11-01 15:24:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/UserProfile.java`

### 231. [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常[補]
- **Commit ID**: `c90a418e46a8f7dd33c955aeb9179ab2802445a5`
- **作者**: waynechang
- **日期**: 2022-10-31 17:41:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 232. [流程引擎]Q00-*********** 修正BPM5872以上版本，XPDL流程自動簽核功能失效異常
- **Commit ID**: `91fee61a0899a6dca7ad626d5dfe67c49cde6696`
- **作者**: waynechang
- **日期**: 2022-10-31 16:23:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 233. [流程設計師]A00-20221026001 修正新增的預設關卡ID如果默認與已經存在的關卡ID一樣，儲存流程時不會異常導致開啟該流程直接報錯
- **Commit ID**: `c81a4596e44c081a30e19479bb697f5a9eb68cea`
- **作者**: 林致帆
- **日期**: 2022-10-31 15:24:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/jDiagram/BPMNFactory.java`

### 234. [BPM APP]C01-20220921001 修正移動端在簽核後兩條流程的表單內容會串單問題
- **Commit ID**: `ac66e78c85cb8a29f55e41e77f0cdc8eb6611d70`
- **作者**: 郭哲榮
- **日期**: 2022-10-31 11:47:13
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 235. [WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>(補修正)
- **Commit ID**: `b44b02abc330cfaf9b1292a6b653a1b1167d109e`
- **作者**: raven.917
- **日期**: 2022-10-28 17:47:45
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/tiptop/MethodCreateForm.java`

### 236. [WEB]Q00-20221028003 修正Tiptop拋單單身含斷行符號會呈現<br/>
- **Commit ID**: `d7e4743d51b68d0ded7d90780745b9c0ca47fe28`
- **作者**: raven.917
- **日期**: 2022-10-28 17:26:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java`

### 237. [WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。(補修正)
- **Commit ID**: `0eb2d3d8ae2224ea746588a8fa77ac499cc0c0ec`
- **作者**: raven.917
- **日期**: 2022-10-28 15:59:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 238. [流程引擎]Q00-20221028002 修正Oracle資料庫，若流程有設計執行服務任務並將回傳值回寫至流程變數時，服務任務會報錯的異常
- **Commit ID**: `c334dcb4b409cce073207bcb31d09ac273e44ed0`
- **作者**: waynechang
- **日期**: 2022-10-28 15:21:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 239. [WEB]A00-***********修正新增關卡內-經常選取對象無法第二次選取進清單。
- **Commit ID**: `ab0485d9d9576cb5b04392e7164d5167b07ba8cb`
- **作者**: raven.917
- **日期**: 2022-10-28 15:20:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`

### 240. [系統管理工具]A00-*********** 若管理員將有組織設計師權限的人員離職，並且移除組織及部門，會導致使用權限設定沒有畫面的問題
- **Commit ID**: `dd219371af216d496a59f94bb90c04080f02dddd`
- **作者**: 謝閔皓
- **日期**: 2022-10-27 18:59:08
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/WizardAuthorityManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/client_delegate/WizardAuthorityManagerClientDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/controller/OrgWizardAuthorityScopeController.java`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/src/com/dsc/nana/user_interface/apps/adm/view/toolauth/OrgAuthConfPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/WizardAuthorityManagerBean.java`

### 241. [內部]Q00-*********** 調整PDF8Convert轉檔機制由synchronized改為多執行序執行，並增加debuglog
- **Commit ID**: `921f9374b88cad164defc3643fb434fc67bff34e`
- **作者**: waynechang
- **日期**: 2022-10-27 15:27:51
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDF8Converter.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/util/iso/PDFConverter.java`

### 242. [Web]S00-20220510001新增運算規則可以選取到hidden元件。
- **Commit ID**: `58a242d861255d407187cd1e525b9d352f1cce7d`
- **作者**: raven.917
- **日期**: 2022-10-26 16:01:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`

### 243. [Web]Q00-20221019001 修正響應式表單Grid元件設定凍結欄位時縮放瀏覽器時會出現跑版的問題
- **Commit ID**: `a835acea7ef36e5938d9c96ecd638af797d26ce2`
- **作者**: cherryliao
- **日期**: 2022-10-26 11:14:26
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - 📄 **重新命名**: `3.Implementation/subproject/webapp/web/js/bootstrap/bootstrapTable/bootstrap-table-fixed-columns-1.18.3.js`

### 244. [Web]Q00-20221020004 修正 TextBox 元件進階功能的運算規則，若將已綁定的欄位值輸入後又刪除，會顯示 NaN 的問題
- **Commit ID**: `c2b231bb1f3e6671896ede09063354a8106983d0`
- **作者**: 謝閔皓
- **日期**: 2022-10-26 09:06:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 245. [流程引擎]Q00-20221025003 調整當核決關卡解析時；若解析人員在同一個組織下有多個兼職部門，且兼職部門的職務核決層級的level都相同時，則以該人員的主部門作為解析部門
- **Commit ID**: `75386735ef4e4aa14c63749eea9a40ba21a9a2f3`
- **作者**: waynechang
- **日期**: 2022-10-25 17:29:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 246. [Web]S00-20220720003 修正輸入元件設置必填，隱藏標籤後提示為元件ID。
- **Commit ID**: `34e1a626e5cdac6361bcaf053f5d9613b9aeea03`
- **作者**: raven.917
- **日期**: 2022-10-25 15:45:35
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formValidation.js`

### 247. [Web]Q00-20221006004 上傳附件功能，優化使用者提示，且上傳過程不可點擊關閉按鈕。
- **Commit ID**: `fc3a5d88dae712787062c2018347e6d2d73e7034`
- **作者**: raven.917
- **日期**: 2022-10-25 15:25:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 248. [Web]V00-20221019001修正流程管理/監控流程 選擇「已撤銷」流程，匯出Excel發現多了「執行中的關卡」跟「目前處理者」的欄位。
- **Commit ID**: `927627b7b61f8622f56c59da50939a5a31b22e2f`
- **作者**: raven.917
- **日期**: 2022-10-25 15:18:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 249. [系統管理工具]A00-20221117001 修正儲存流程因為Application Server位址沒有填上PORT導致失敗
- **Commit ID**: `16e41849a9f1ffad6ccef7fe9cb2231eff7c9205`
- **作者**: 林致帆
- **日期**: 2022-11-21 15:55:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 250. [Web]Q00-20221026002 新增判斷二階快取應確認來源位置是否為本地端(localhost / 127.0.0.1)若是則不須額外清除。
- **Commit ID**: `304cbdb38e6e3103a55fc1b86794a25b5542a7ab`
- **作者**: raven.917
- **日期**: 2022-10-26 14:13:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java`

### 251. [Web]Q00-20221121002 修正關卡設定附近在線閱覽按鈕不顯示，在流程草稿上傳附件還是會顯示在線閱覽按鈕
- **Commit ID**: `d1c01f6a8a387e635780e90b0b984ba092c86cb2`
- **作者**: 林致帆
- **日期**: 2022-11-21 15:48:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`

### 252. [在線閱覽] Q00-20221111002 修正追蹤流程重發新流程，當第一關關卡有設定上傳附件不使用在線閱覽時，上傳附件仍會出現在線閱覽的選項
- **Commit ID**: `67df0bcb71552b497582364a54963bf575a51ed2`
- **作者**: waynechang
- **日期**: 2022-11-11 15:00:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/GetInvokedProcessDataAction.java`

### 253. [Web]Q00-20221116001 修正開啟流程草稿表單內容都被清空的問題
- **Commit ID**: `c3d835633c7eaa3dbb96f0764cb611bbb2eee4ff`
- **作者**: cherryliao
- **日期**: 2022-11-16 14:27:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 254. [WEB]Q00-20221101002 修正絕對定位表單SerialNumber元件CSS取到RWD設定
- **Commit ID**: `6b431c5cc12359ecca76a910f7c08f85231508e3`
- **作者**: raven.917
- **日期**: 2022-11-01 12:00:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/domain/form/builder/html/SerialNumberElement.java`

### 255. [雙因素模組]Q00-20221026005 在未授權時，BPM首頁左側功能列會顯示雙因素模組功能
- **Commit ID**: `4d35f4a1cd4ef39a9944bb61ef97566d601207b9`
- **作者**: 林致帆
- **日期**: 2022-10-26 16:02:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/cache/ProgramDefinitionLicenseCache.java`

