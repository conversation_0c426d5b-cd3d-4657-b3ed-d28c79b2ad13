{"比較資訊": {"專案ID": "BPM", "倉庫路徑": "D:\\IDEA_workspace\\BPM", "新分支": {"branch_name": "5.7.6.2_1", "date": "tag 5.7.6.2_1\nTagger: 張詠威 <<EMAIL>>\n\n20190815 1000 lastbuild2019-08-15 09:42:24", "message": "Q00-20190815001 修正/v1/process/invokeprocess發起流程的接口判斷表單數量異常", "author": "wayne<PERSON>"}, "舊分支": {"branch_name": "5.7.6.1", "date": "tag 5.7.6.1\nTagger: 張詠威 <<EMAIL>>\n\nlast build 108/06/26  10:302019-06-26 10:18:09", "message": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "author": "<PERSON>"}, "比較時間": "2025-07-28 17:56:24", "新增commit數量": 120, "比較方式": "多專案智能版本比較"}, "新增commit記錄": [{"commit_hash": "df2db71eeafa5c9ed76fbf10b3958fd6637d0d26", "commit_訊息": "Q00-20190815001 修正/v1/process/invokeprocess發起流程的接口判斷表單數量異常", "提交日期": "2019-08-15 09:42:24", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a0e8b560788119396d5b87704b3d00841468586a", "commit_訊息": "補修正<第二次>S00-20190528002 修正原本取得流程種類(XPDL/BPMN)的寫法在當前是核決關卡有異常", "提交日期": "2019-08-14 19:49:30", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "30f5b7e8dbae02517ec251f4e236fc741d71d9bc", "commit_訊息": "調整多餘空白與換行", "提交日期": "2019-08-14 16:23:29", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c9a14d1dbebfa468f68584c127ac4133956f2a00", "commit_訊息": "C01-20190627001 調整增加BPMN流程的詳細資訊顯示流程狀態", "提交日期": "2019-08-14 14:04:38", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "32e303a9191c3b7c763c27efe54bdc9b890f3ef0", "commit_訊息": "V00-20190807034 將右上角的其他設定、版本資訊設定為只有admin能使用", "提交日期": "2019-08-14 11:58:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b25a50b0aa22da46b52b1932525ee20727aceb47", "commit_訊息": "A00-20190813002 修正設定檔key=TodoWorkItemOrderBy的內容導致，點右上角代辦出現異常", "提交日期": "2019-08-14 11:40:38", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "8e3f714d33748810b87a0589fed6c67218153638", "commit_訊息": "修正移動授權中間層管理畫面", "提交日期": "2019-08-12 17:33:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "6d828a6e7b8db9769d68122e4ee18a9586ba779e", "commit_訊息": "Q00-20190812003 修正移動端儲存表單後選擇繼續留在表單畫面時未顯示遮罩導致可以再次被點擊", "提交日期": "2019-08-12 17:00:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c675e5b729b575b471cf829750c8fb2d958d2f11", "commit_訊息": "Q00-20190812002 修正IMG在儲存表單時原本為disable的元件會被打開可填寫", "提交日期": "2019-08-12 16:54:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d567c1ddde2b2d0443ad53b019a9c397abad4b6b", "commit_訊息": "修正移動的待辦與追蹤用listReader會有Connection沒釋放的問題", "提交日期": "2019-08-09 17:12:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "9fa4726e6e8182f1d5764afc8f8120c31a6ae2ca", "commit_訊息": "修正移動端在取得通知筆數會有Connection沒釋放問題", "提交日期": "2019-08-09 16:10:09", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileNoticeWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "606a2e10164256733ce4e068bb6537b51947b8e2", "commit_訊息": "調整移動授權中間層連線資訊維護頁面", "提交日期": "2019-08-08 21:03:43", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterComponentOAuth.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/Common.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 7}, {"commit_hash": "710b053d29058ab915373e66760e4eb09487026c", "commit_訊息": "C01-20190808005 二次修正企業微信2.8.10版本移動端的Grid顯示異常", "提交日期": "2019-08-08 16:34:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "13445c73732ec7469647f93179c9084983a41be7", "commit_訊息": "更新 e10流程範本 新增取得附件的Invoke關卡", "提交日期": "2019-08-08 16:30:41", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\\346\\265\\201\\347\\250\\213\\347\\257\\204\\346\\234\\254.bpmn\"", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "be90b79fc00be5246a6bb6aed5d0f12251f29eaa", "commit_訊息": "A00-20190719001 修正樹狀開窗如果部門名稱不是英文或數字 在IE會查不出部門內人員", "提交日期": "2019-08-08 15:51:31", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "b9330e4e65e85b4cd804efde77353b1d06cda3b6", "commit_訊息": "修正T100發單有附件時，會造成Connection持續被占住", "提交日期": "2019-08-08 14:08:05", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessCreate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3fe456da800b240de976d348846fdf2cde051797", "commit_訊息": "A00-20190805002 Checkbox語法調整", "提交日期": "2019-08-08 12:16:40", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a976fe3fe9efb513961418d50114cf408da1b94", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-08 11:31:53", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "e11ed8b64906388a50edf542a1370e72ab64241d", "commit_訊息": "A00-20190805002 依據交付提供語法調整", "提交日期": "2019-08-08 11:31:06", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ddfba3ada0874e2d9a3111b47abac8a1daae4580", "commit_訊息": "V00-20190807035 修正移動端客製開窗與產品開窗先勾選後查詢時已勾選人員的勾選未打勾", "提交日期": "2019-08-08 11:22:05", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "5091e868f263604fdebd36f8b77baf05db3c630e", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-08 11:08:57", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "9146c1563a967379e15c71a6476fe1db5d9f244b", "commit_訊息": "補修正<第二次>Q00-20190802002 修正前一筆修改的邏輯錯誤", "提交日期": "2019-08-08 11:08:12", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c56a779cfe49754e8018e8172172882bdb5829fa", "commit_訊息": "V00-20190807020 調整維護樣板，當回傳的status不等於200時，提示錯誤訊息", "提交日期": "2019-08-08 10:30:51", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_en_US.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_zh_CN.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/customModule/rescBunble/QueryTemplate_zh_TW.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "f67195db32c8eb56391f562bed283a5a160123e1", "commit_訊息": "V00-20190807007 修正入口平台整合設定的統計元件欄位中智能快簽多語系異常", "提交日期": "2019-08-08 10:09:44", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0e5040459076c812ad194e2161ca9daf54bcc92f", "commit_訊息": "Q00-20190807007 修正英文列印表單失效", "提交日期": "2019-08-08 09:43:17", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ea9081434cd530c723c3815242e74e2e0111b83a", "commit_訊息": "Q00-20190807002 修正移動端Grid元件資料展開畫面跑版", "提交日期": "2019-08-07 19:25:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "cebaf16d3ee36e05af8db80d880487528bc5cf99", "commit_訊息": "Q00-20190807003 修正客製開窗在資料為一行時圖式跑版", "提交日期": "2019-08-07 19:17:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d3e42926dc3f30c70994eb9579a815dcd5828102", "commit_訊息": "A00-20190807002 移除文件", "提交日期": "2019-08-07 19:15:05", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "\"6.Deployment/DeploymentPlan/copyfiles/@t100/form-default/form_\\346\\226\\274\\345\\205\\247\\351\\203\\250\\344\\270\\273\\346\\251\\237\\344\\270\\213\\350\\274\\211\\346\\234\\200\\346\\226\\260\\347\\211\\210.txt\"", "修改狀態": "刪除", "狀態代碼": "D"}], "變更檔案數量": 1}, {"commit_hash": "bf90d771d63e91f8f0c556c8c81cca9289795e0d", "commit_訊息": "調整移動授權中間層使用者維護頁面", "提交日期": "2019-08-07 18:51:16", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 4}, {"commit_hash": "50de7191cb9525566dfcc968d9b69ad350456f7f", "commit_訊息": "Q00-20190807006 修正行動版相對位置表單在grid或subTab元件新增後會無法拖曳欄位樣板進去問題", "提交日期": "2019-08-07 17:44:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "61443cdc250d57bc1a565c8661c32acd12d99ebf", "commit_訊息": "A00-20190806001 修正響應式表單中新增頁籤後點選確定但頁籤設定視窗不會關閉的問題", "提交日期": "2019-08-07 16:54:13", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d528056547d93dc650ed4ea3cf22972e683d6110", "commit_訊息": "Q00-20190807004 調整智慧待辦的排程log層級", "提交日期": "2019-08-07 16:44:35", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "05197706a56d8b6f57624535ac8dfcd29a74b84d", "commit_訊息": "Q00-20190807005 修正移動授權中間層使用者維護頁面", "提交日期": "2019-08-07 16:36:40", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "2d810496fea9d5e8003ffe2e184dbd8a11754614", "commit_訊息": "增加debug印出呼叫回寫T100時 T100所回傳的資訊", "提交日期": "2019-08-07 16:18:18", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/MethodProcessStatusUpdate.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "524ac66eb2cd955a8ab2c3435acc5af46474ab95", "commit_訊息": "Q00-20190807001 修正英文待辦事項標題未正常顯示", "提交日期": "2019-08-07 13:46:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "d55acdcbe79597dc851f8e592eccfceae52c2b22", "commit_訊息": "A00-20190403001補修正", "提交日期": "2019-08-07 09:37:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2d68411b95494640d87f46baa9af10f40f404bc7", "commit_訊息": "A00-20190403001 HR組織同步增加throws Exception", "提交日期": "2019-08-07 09:31:51", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/SyncOrg.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/syncorg/util/CheckIntegretyUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0b65f81f71748a9bc1581db332c745bf49554048", "commit_訊息": "A00-20190730006 取消舊版SyncISO文件同步方法", "提交日期": "2019-08-06 16:07:15", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISOSyncDocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "301c95077f7f99b5faad96e23a62270ed77b0be6", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-06 16:02:26", "作者": "wayne<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "a66a15f59793a7c74404c2cf0cc90625a874d804", "commit_訊息": "Q00-20190806004 調整FormInstance的configureFieldValue,判斷是否要一併調整txt屬性的值", "提交日期": "2019-08-06 16:01:55", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/FormInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "420f651654303ac3d620d91514dfdb41b772ba2f", "commit_訊息": "C01-20190802003 修正企業微信2.8.10版本使用iOS手機操作待辦與通知畫面時出現異常訊息", "提交日期": "2019-08-06 11:15:20", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileTool.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "75fffcab2eb2d8ed6f52b70b41fe517f71914b1d", "commit_訊息": "Q00-20190802002 修正Invoke呼叫restful時使用過期的token驗證  導致無法呼叫失敗", "提交日期": "2019-08-05 18:35:31", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/tool_agent/RestfulToolAgent.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e9cc9664bf2520e31373273f8a425758a9b5d24", "commit_訊息": "S00-*********** 修正formInstance 裡fieldValue的attachment Tag加入位置錯誤", "提交日期": "2019-08-05 17:57:05", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a2b8872651410dc82ef98ab1010326f1b6a9b978", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-05 17:28:40", "作者": "walter_wu", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "fb004cda6807ee846baaee3252489053eb60cc0e", "commit_訊息": "調整/v1/process/abort接口 撤銷流程失敗的回傳訊息 加上實際錯誤內容", "提交日期": "2019-08-05 17:28:09", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "20ad8b45c975ef6464ceefc9eed7c010d3cabc05", "commit_訊息": "A00-20190724002 調整", "提交日期": "2019-08-02 13:47:59", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "a9061e96dd9b918ba2a4a8d071d9b8afa1e21486", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-08-01 11:17:57", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "MM"}], "變更檔案數量": 1}, {"commit_hash": "f020dc3ff6a9b40a31d17590a1d16e2f6be938a3", "commit_訊息": "在地化-於BPM首頁強制顯示兩台主機的待辦項目ICON、標題列增加當前主機描述、移除判斷用戶設定僅顯示哪幾台主機ICON的功能", "提交日期": "2019-08-01 11:17:08", "作者": "gaspard.shih", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c74cde7e83a9856254daeca6d31a45fe66a810fb", "commit_訊息": "Q00-20190801001 公司私有雲增加支持Nutanix AHV 平台", "提交日期": "2019-08-01 09:17:04", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/util/GuardServiceUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "194546433902ade3575570a6bd976b5d3d54d995", "commit_訊息": "增加DML註解", "提交日期": "2019-07-31 19:08:26", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_MSSQL_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "cb1067127c526f8c3417e2ae0e470ff369cefbc0", "commit_訊息": "S00-*********** E10發單新增附件", "提交日期": "2019-07-31 19:06:36", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/DocManagerDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/sysintegration/ProcessMappingKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IDocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/DocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/E10/E10ManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/AttachmentsReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/DocumentBeanReq.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/request/InvokeProcessFormDataReq.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@app/NaNa/conf/jakartaojb/main/repository_user.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DDL_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DDL_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 17}, {"commit_hash": "648543587a43b2287ab28bd9355e00b717852af3", "commit_訊息": "Q00-20190718002 修正腳本樣板內容錯誤", "提交日期": "2019-07-31 14:58:52", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_MSSQL_1.sql", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.2_DML_Oracle_1.sql", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "68bb3136dca54386c438c1320131eb0a070e5774", "commit_訊息": "C01-20190730007 處理將簽合歷程轉為JSON時，因為通知關卡處理者為空導致解析錯誤", "提交日期": "2019-07-30 18:22:47", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9074fa8e64f7ccb85687528a566147c3d35e98e", "commit_訊息": "A00-20190708001 修正bpm-bootstrap-util.js出現錯誤時，表單script無作用", "提交日期": "2019-07-29 13:40:57", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/bpm-bootstrap-util.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6d0240382828e8357719bed2891955a6931aefc5", "commit_訊息": "A00-20190709002 A00-20190710004 修正FormUtil失效問題", "提交日期": "2019-07-29 11:09:34", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fc8a156bbd89f4a19b5f79045258c891fd971a62", "commit_訊息": "S00-20190528002 改善BPMN流程效能 加快代辦點單筆畫面載入速度 如果是BPMN流程忽略JGO畫圖", "提交日期": "2019-07-26 11:33:59", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "3ffd96850d2e92956aad18f98055f69870483101", "commit_訊息": "C01-*********** 發現gDialog_overlay會影響IE會導致絕對位置列印的排版跑掉", "提交日期": "2019-07-26 11:06:52", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintAllFormData.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "f9e645f174755ae72fe740b783bea77c31189928", "commit_訊息": "修正移動端查看ESS表單附件時會卡控表單名稱", "提交日期": "2019-07-25 19:02:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileFormHandlerTool.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "db7b5c1a3509301432219f5562ff6bb5c5b56ca0", "commit_訊息": "Q00-20190725008 修正移動授權中間層的連線資訊管理會有別名相同問題", "提交日期": "2019-07-25 18:11:30", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Adapter/ConfigManange/ComponentOAuth.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "67d462ad67d5575edb6cafdd3b63a343365d4736", "commit_訊息": "Q00-20190725001 修正IMG在關卡為多人處理且有設定工作執行率時上一關卡資訊會顯示異常問題", "提交日期": "2019-07-25 14:02:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2a6affa3dc8b3ebdff3a0c0857595800e9614b6d", "commit_訊息": "Q00-20190722004 調整預測關卡計算的時間機制", "提交日期": "2019-07-25 11:27:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "50c84dc28ee253445af4562ffe62c6434c0c0f55", "commit_訊息": "V00-20190717001 修改server cache同步邏輯-WorkflowServer AppServerAddress沒有值的就不同步", "提交日期": "2019-07-25 11:16:22", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/server_manager/ServerCacheManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9a7a1d0a488a82ceb7286ae70ceb5bba6bbdd006", "commit_訊息": "Q00-20190722002 調整撥打給上一關處理人的多語系", "提交日期": "2019-07-25 10:56:24", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "83df969a6ac05f01c0302385845c7d623392d182", "commit_訊息": "Q00-20190702002 修正RWD設計器轉換出的行動表單發生派送後無Grild資料問題", "提交日期": "2019-07-25 10:49:21", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9fb16294cf936befadd7fdb183a27a2cf1a59387", "commit_訊息": "補上hibernate.jar的classpath", "提交日期": "2019-07-25 09:32:17", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/.classpath", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c386fe0f70b6d741bec56fe92e30a0a992a15180", "commit_訊息": "Q00-20190718005 修正會辦流程在IMG快簽中仍會顯示終止流程選項的問題", "提交日期": "2019-07-24 17:24:27", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "eac1301c7b8a8a8cd8b02eb0128a507cfcb4dd26", "commit_訊息": "C01-20190712001 修正FormUtil設定Button元件樣式無效", "提交日期": "2019-07-24 17:05:35", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/FormUtil.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1f0afd80de42332c183e683c0d6c5a8515ccbf94", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-07-24 11:10:00", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "533f01ecbeac03c57232e86eac9b5eefb7b2ee6a", "commit_訊息": "A00-20190723003 指令錯誤更正", "提交日期": "2019-07-24 11:01:46", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DDL_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "c1050cfb5b111d24e6a607c8e1e9ae4affada739", "commit_訊息": "Q00-20190705001 修正移動端Grid內容不會清除", "提交日期": "2019-07-24 10:53:12", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileGridFormateRWD.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "954e0737321938026d8bcf5a34fd0febe3168cb7", "commit_訊息": "Q00-20190701002 修正智能待辦排程會因使用者無工作行事曆導致排成執行失敗的問題", "提交日期": "2019-07-23 18:34:03", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "0e6c7ff577e693081e3afd0ec4359fddd3c51811", "commit_訊息": "Q00-20190718006 調整智能學習管理中心頁面偏移值", "提交日期": "2019-07-23 16:31:23", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "26177f30e9482e74c181991896659ab3deb160e4", "commit_訊息": "Q00-20190703002 修正使用Android點選產品開窗時畫面會滑動", "提交日期": "2019-07-23 16:25:08", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileProductOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "d82919a48984ade36ae665d78a40070f53f902ad", "commit_訊息": "Q00-20190718004 修正關注按鈕在已結案流程不會顯示問題", "提交日期": "2019-07-23 13:55:19", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "fb0caa1e237d3bf935987104da2a02f6f4030b89", "commit_訊息": "Q00-20190704003 修正企業微信查看附件會保留上一次查看的位置", "提交日期": "2019-07-22 17:12:24", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "ee2abfc94b8fd53e0d2535a36e5242302454450e", "commit_訊息": "Q00-20190627003 修正企業微信從菜單進入待辦列表上方的篩選字樣顯示undefined", "提交日期": "2019-07-22 17:06:19", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileOpenViewPortAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "2c1ecd5614eceaa7ac76395585930bec323692bf", "commit_訊息": "Q00-20190627002 企業微信編輯我的最愛流程星號顯示機制", "提交日期": "2019-07-22 15:35:41", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListWorkMenu.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "6e17255b694b7e4ae4050bd272b85f0af95bf868", "commit_訊息": "A00-20190718004 修正多人處理關卡，每個人都要簽核，若其中有轉派記錄時，則須判斷是否為同一個處理者處理，避免造成轉派記錄錯誤", "提交日期": "2019-07-19 17:15:01", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ca58329c32ede6139dcd47719be9b8dcf5f5c7f1", "commit_訊息": "Q00-*********** 多新增delegate方法來接只取數量的方法", "提交日期": "2019-07-18 18:27:47", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/BAMServiceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 5}, {"commit_hash": "dcccdf1ab75b9f27739ec5312549850e561eb874", "commit_訊息": "Q00-*********** 還原被覆蓋的地方", "提交日期": "2019-07-18 18:12:22", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/conf/NaNaWeb.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "4afd74e53a517dd1e71ea2d5f7496cb5bb6227cf", "commit_訊息": "C01-20190716002 在地化server2修正智能示警漏了tIconDesc", "提交日期": "2019-07-18 18:09:46", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e672e9a646110f1e1caba2cd41c966a912a0e606", "commit_訊息": "C01-20190705001 移動端預解析關卡功能僅支援簽核流程設計師的流程", "提交日期": "2019-07-18 16:56:13", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessTraceMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "deec00d9041d68a12024b0a56e57548fbc11e124", "commit_訊息": "InUse Conn未釋放問題處理", "提交日期": "2019-07-18 16:46:50", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/orgAnalyze/OrgAnalyzeManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobilePlatformManageTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopSecurityManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/serviceRegister/ServiceRegisterBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 10}, {"commit_hash": "35eca86bc8d78f3b228c1774cb25d9a1ef43ad5b", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-07-18 16:06:31", "作者": "BPM", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "7af7a5f1e9333c2e298f64767b10fc2c750a0808", "commit_訊息": "Q00***********-在56發現待辦未結案率的統計圖會因資料量大而沒有回應，已調整成只取數量回來做計算。", "提交日期": "2019-07-18 15:47:37", "作者": "BPM", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobileDirectTraceableProcessListReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "c5a15d1ea3d35c3827d5ffd6d9cdc8e09c21c0ea", "commit_訊息": "Q0020190703004 企業微信開啟附件編碼為簡中時出現亂碼問題", "提交日期": "2019-07-18 15:20:22", "作者": "BPM", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/FileManager.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "031648b37392807db303188cbb0b69ddf0ac8162", "commit_訊息": "Q0020190703005檢視PDF檔案時會模糊不清晰問題(企業微信)", "提交日期": "2019-07-18 14:51:57", "作者": "BPM", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/pdfJs/pdf-bpm.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "df7606d6838156cdb7e62a75da6df9cccd32abf5", "commit_訊息": "Q00-*********** 增加 ISO舊表單能在V58轉呼叫V57站台的服務", "提交日期": "2019-07-18 14:38:56", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISORemoteDocManagerDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISORemoteListHandlerDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/iso_module/ISORemotePageListReaderDelegate.java", "修改狀態": "新增", "狀態代碼": "A"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/iso/ISODocManagerLocal.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IISODocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/IISOPageListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ISODocManagerImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/rmi/impl/ISOPageListReaderImpl.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 12}, {"commit_hash": "6deb3c79bf49607b89d87ec6e7914d77fef5ffee", "commit_訊息": "Q0020190718002 修正腳本樣版更新與法", "提交日期": "2019-07-18 14:34:18", "作者": "BPM", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/db/@base/update/5.7.6.1_DML_Oracle_1.sql", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e5f206202ebc1ca4ca9dfe4056c67c602092df79", "commit_訊息": "刪除多餘的system.out.println", "提交日期": "2019-07-18 09:47:19", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/newtiptop/NewTiptopManagerBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1abc8eb4dba7d83245076aef980f71ec24e90305", "commit_訊息": "Q00-20190718001 修正智能計算排程-計算流程模型權重會導致conn不會釋放", "提交日期": "2019-07-18 09:37:08", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/IntelligentLearningMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "468bfb7dfb5ed62d48c1c23d45ca06d837e49302", "commit_訊息": "Q00-20190717002 排除透過RMI呼叫時無法取得hibetnate的class", "提交日期": "2019-07-17 17:06:46", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/build.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/Hibernate/hibernate.jar", "修改狀態": "刪除", "狀態代碼": "D"}, {"檔案路徑": "3.Implementation/subproject/webapp/lib/Hibernate/hibernate3.jar", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 3}, {"commit_hash": "b2325e5cb5451729d788f72bc9a5be0efc45b09d", "commit_訊息": "Q00-20190717001 修正ISO自動編碼規則判斷邏輯", "提交日期": "2019-07-17 16:47:20", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocManager.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/ISODocSnGenerator.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/dao/iso/hibernate/SnRuleDaoImpl.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "95df42a15b4d86c8981db27fc6a8ae2dc9564fa8", "commit_訊息": "調整移動端支持電腦版企業微信與瀏覽器異常功能與畫面", "提交日期": "2019-07-17 11:25:38", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileResigend.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 11}, {"commit_hash": "0fb127ccd1e8476e997a522d6b31a1f9ea45ffc4", "commit_訊息": "修正移動端預覽表單畫面功能", "提交日期": "2019-07-16 18:34:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/AbstractFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderJSON.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilderMobile.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 8}, {"commit_hash": "93810034333d2c7c1f91efd58669f7e69b3cc27c", "commit_訊息": "S00-20190307001 新增在IMG中在連續簽核時保留列表所選篩選條件並且會取下筆而非最新流程", "提交日期": "2019-07-16 15:35:37", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictionKey.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SearchCondictions.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/mobile/MobilePerformableWorkItemListReader.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatformPushTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/thirdParty/MobileMPlatoromWorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/bean/dinwhale/WorkInfo.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleOperation.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhaleProcessMgr.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 13}, {"commit_hash": "cc69bf2fd12e2c777d4196e44ec950e323849ae8", "commit_訊息": "Q00-20190704001 補上漏改部分", "提交日期": "2019-07-15 20:35:35", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "fb335ec28c08fbba903101817288fe4e243e8786", "commit_訊息": "Q00-20190704001 修正IMG用詳情作連續簽核時，進入下一筆的表單畫面會一片空白問題", "提交日期": "2019-07-15 20:31:05", "作者": "pinchi_lin", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MobileAuthenticateTool.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "ae9c52156e83d8920450fad4aa117a37fec657cf", "commit_訊息": "調整移動端支持電腦版企業微信與瀏覽器異常功能與畫面", "提交日期": "2019-07-15 10:00:45", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/BpmAppListTracePerformed.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormInvoke.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileNotice.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTraceInvoked.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileTracePerform.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppTable.js", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/BpmFormatMaterialize.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/FixMaterializeCssExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/theme/bpmApp/v2/mobile-UI-commonExtruded.css", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 25}, {"commit_hash": "101f12404b069f310aad68b095035a96fe96cbb1", "commit_訊息": "A00-20190711002 調整獨立模組產生連結的url", "提交日期": "2019-07-12 10:56:45", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/CustomModuleAccessor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "82b6adb419fd7026b3d31fdc019022dd36f0c1a4", "commit_訊息": "C01-20190311004 修正人員任務排版錯亂", "提交日期": "2019-07-11 16:09:21", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "157ea298e6e115235fe36884810e380a37ef67ef", "commit_訊息": "A00-20190710003 修正人員任務進階設定的多語系", "提交日期": "2019-07-11 11:06:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_en_US.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_CN.properties", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_TW.properties", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "7e9e15cf1b07901cfd9cc669902a22efc182f5ac", "commit_訊息": "A00-20190709004 調整排程-關卡往下派送的服務(調整為一個小時前的關卡才允許自動往下派送，避免發生多筆同時處理同一個關卡)", "提交日期": "2019-07-11 09:43:27", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "1213869390cdca82848d7c2f0f9aaee8c11e9a5d", "commit_訊息": "Q00-20190710001 調整verifyaccesstokenForMDO", "提交日期": "2019-07-10 15:06:29", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Identity.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/IdentityMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "0474a9d0e6814034e3d671201e431f94761adcac", "commit_訊息": "V00-20190624012 修正IE瀏覽器-個人化首頁連結至待辦清單按鈕,按下後無法導頁問題", "提交日期": "2019-07-09 10:03:23", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "a49b921db1c0ce51fc7f96240acd689ada13d04a", "commit_訊息": "Q00-20190704004 調整外部模組透過url登入BPM的畫面", "提交日期": "2019-07-10 15:43:50", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/ExtraLogin.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "264765f21bd05376a8b012bf5c3098f43a4ae850", "commit_訊息": "V00-20190703003 修正系統管理工具測試寄信失敗問題", "提交日期": "2019-07-05 13:42:38", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/system/src/com/dsc/nana/util/MailUtil.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "3daa2892b3bee06b743d57eb119377af225a8bc6", "commit_訊息": "A00-20190703001 bpm-tools.exe英文拼字錯誤", "提交日期": "2019-07-05 10:29:06", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/view/dialog/ToolEntryLoginDialog.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "e1f96f7b1e447499d82529ba42406503c91fc63a", "commit_訊息": "C01-20190520001 修正查找問題時發現的標準差計算不正確", "提交日期": "2019-07-04 19:07:36", "作者": "walter_wu", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/bam/BamMgr.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "a01fe48128f3d0d70d2450fc31734eaefc169a6d", "commit_訊息": "C01-20190523003 修正\"流程中若有核決層級關卡，且退回的關卡設有自動簽核，當處理者重複時會觸發退回的關卡執行自動簽核\"的問題", "提交日期": "2019-07-04 16:05:18", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "22143437a27eb1653d3bd5281ef2e34eca5201b5", "commit_訊息": "Q00-20190703003 將文件總管的修改文件屬性的保存年限調整為保存日期。", "提交日期": "2019-07-03 16:43:38", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/iso_module/ManageDocumentAction.java", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WEB-INF/WMS/isoModule/struts-manageDocument-config.xml", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "3.Implementation/subproject/webapp/web/WMS/ManageDocument/EditDocument.jsp", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 3}, {"commit_hash": "78132277a5b1ae780bbd58115af93cd164ac225b", "commit_訊息": "C01-20190702004 修正移動端客製開窗使用模糊查詢時會顯示對資料庫執行SQL查詢指令失敗問題", "提交日期": "2019-07-03 14:20:32", "作者": "yamiyeh10", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "ef63e1172cb587961def4a96e2f5bab377c69e0e", "commit_訊息": "V00-20190626004 (第二次修正)修正組織設計師-修改員工資料-設定所屬單位-部門 , 完全沒選擇主部門也可儲存問題", "提交日期": "2019-07-01 11:11:54", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "791fdd5e43f911aab4eea852843139e1f4b03927", "commit_訊息": "V00-20190624004-修正TT整合設定無論成功失敗皆會顯示successful", "提交日期": "2019-07-01 10:51:58", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/sys-configure/src/com/dsc/nana/user_interface/apps/syscfg/view/tiptop/TiptopToolBar.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "482649be8b416b3111d470fc3e42e9d4fcd11135", "commit_訊息": "V00-20190626004 修正組織設計師-修改員工資料-設定所屬單位-部門 , 完全沒選擇主部門也可儲存問題", "提交日期": "2019-06-27 15:36:20", "作者": "jerry1218", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/org-designer-blink/src/com/dsc/nana/user_interface/apps/new_org_designer/view/dialog/EmployeeEditor.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "77ab3c05642cc9da835719ffc6af2272222e0ff6", "commit_訊息": "A00-20190513001 修正ISO排程dailyJob當制定單位設定成群組時會報錯", "提交日期": "2019-06-27 13:53:49", "作者": "wayne<PERSON>", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/biz/iso/bakjob/ISOAutoJobDaoWorker.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}, {"commit_hash": "9689d3935441f77229b47fff779d834a7c2ac2c8", "commit_訊息": "A00-20190626001 多語系縮減調整", "提交日期": "2019-06-26 19:19:16", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 2}, {"commit_hash": "e367214a7b65abdead02b94fffb6670c5fccf30f", "commit_訊息": "Merge branch 'develop_v57' of http://************/BPM_Group/BPM.git into develop_v57", "提交日期": "2019-06-26 14:55:43", "作者": "<PERSON>", "檔案變更": [], "變更檔案數量": 0}, {"commit_hash": "fb27b38538950e9b1d2303a6a099485108399908", "commit_訊息": "A00-20190626001 多語系縮減調整", "提交日期": "2019-06-26 14:55:11", "作者": "<PERSON>", "檔案變更": [{"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xls", "修改狀態": "修改", "狀態代碼": "M"}, {"檔案路徑": "6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle5762.xls", "修改狀態": "新增", "狀態代碼": "A"}], "變更檔案數量": 2}, {"commit_hash": "99a593324c426f517b3f959caa41503d5692e350", "commit_訊息": "C01-20190617004 修正使用者表單設計師查詢失效", "提交日期": "2019-06-26 14:44:50", "作者": "yanann_chen", "檔案變更": [{"檔案路徑": "3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionSearchReader.java", "修改狀態": "修改", "狀態代碼": "M"}], "變更檔案數量": 1}]}