# Release Notes - BPM

## 版本資訊
- **新版本**: release_*******
- **舊版本**: release_5.8.7.2
- **生成時間**: 2025-07-18 11:38:10
- **新增 Commit 數量**: 238

## 變更摘要

### lorenchang (8 commits)

- **2022-06-26 20:48:05**: [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
  - 變更檔案: 25 個
- **2022-04-06 18:24:28**: [內部]回收越南多語系
  - 變更檔案: 5 個
- **2022-04-06 15:36:53**: [內部]Merge *******_delivery_process分支多語系
  - 變更檔案: 1 個
- **2022-04-06 13:54:17**: Merge *******分支多語系
  - 變更檔案: 1 個
- **2022-03-31 16:06:17**: [內部]配合修正Dom4J XXE Injection漏洞進行相關調整
  - 變更檔案: 43 個
- **2022-03-31 16:05:21**: [資安]修正Dom4J XXE Injection漏洞
  - 變更檔案: 2 個
- **2022-03-28 16:25:37**: [流程引擎]提高58版Queue併發承載量
  - 變更檔案: 20 個
- **2022-03-28 14:08:51**: [內部]更新bpm-tool-entry設定，避免新版Eclipse編譯異常
  - 變更檔案: 1 個

### 林致帆 (59 commits)

- **2022-04-25 15:47:55**: [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務[補修正]
  - 變更檔案: 1 個
- **2022-04-22 17:45:14**: [內部]Q00-20220414001調整派送表單關聯設定作業SQL指令 [補修正]
  - 變更檔案: 2 個
- **2022-04-21 11:21:26**: [流程設計師]S00-20211105002 服務任務RESTful路徑新增#符號判別要呼叫哪台流程主機
  - 變更檔案: 1 個
- **2022-04-20 16:28:49**: [E10]Q00-20220420002調整標準出貨E10流程取得附件服務任務RESTful路徑改成/NaNaWeb/api/v1/process/attachinfo/documents/get
  - 變更檔案: 1 個
- **2022-04-19 09:20:07**: [Web]V00-20220415005 修正首頁的我的關注筆數與我的關注流程清單數量不一致
  - 變更檔案: 1 個
- **2022-04-18 15:04:06**: [Web]V00-20220415006修正 流程主旨帶有「\」符號，撤銷流程清單無法呈現
  - 變更檔案: 1 個
- **2022-04-15 17:58:59**: [Web]A00-20220414001修正表單設計器的搜尋表單欄位一直查詢導至InUseCount增長不會釋放
  - 變更檔案: 1 個
- **2022-04-15 13:37:42**: [Web]Q00-20220414002 修正一般使用者追蹤流程清單匯出Excel報表，簽核時間欄位沒有值
  - 變更檔案: 1 個
- **2022-04-15 11:39:27**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2022-04-15 11:39:14**: Revert "[內部]V00-20220415002 修正命名不符合BPM附件檔名的檔案還是能透過URL下載附件"
  - 變更檔案: 1 個
- **2022-04-15 11:09:09**: [內部]V00-20220415002 修正命名不符合BPM附件檔名的檔案還是能透過URL下載附件
  - 變更檔案: 1 個
- **2022-04-15 10:15:20**: [流程引擎]Q00-20220415001 修正因多餘附件移除邏輯改成流程結案處理，導至流程無法結案
  - 變更檔案: 1 個
- **2022-04-14 08:15:40**: [內部]Q00-20220414001調整派送表單關聯設定作業SQL指令
  - 變更檔案: 1 個
- **2022-04-07 17:05:41**: [資安]Q00-20220331001修正漏洞：透過下載附件URL替換檔案名稱就能下載任意使用者的附件[補修正]
  - 變更檔案: 6 個
- **2022-04-06 16:31:03**: [Web]Q00-20220406002 調整派送表單關聯設定作業PK長度過長問題
  - 變更檔案: 2 個
- **2022-02-23 18:00:27**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 2 個
- **2022-02-23 14:17:35**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 6 個
- **2022-02-21 10:48:40**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 7 個
- **2021-12-22 15:14:30**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 2 個
- **2021-12-09 19:12:50**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 1 個
- **2021-11-25 17:48:24**: [Web]S00-*********** 新增派送流程關聯設定作業 [補修正]
  - 變更檔案: 2 個
- **2021-11-25 17:11:57**: [Web]S00-*********** 新增派送流程關聯設定作業 [補修正]
  - 變更檔案: 10 個
- **2021-11-25 14:32:23**: [Web]S00-*********** 新增派送關聯配置作業
  - 變更檔案: 14 個
- **2022-03-31 10:52:07**: [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務
  - 變更檔案: 6 個
- **2022-01-25 11:51:39**: [ESS]S00-20211208003新增ESS外網主機IP設定
  - 變更檔案: 5 個
- **2022-04-01 17:29:20**: [資安]Q00-20220401002修正Spring漏洞反射型文件下载漏洞(CVE-2020-5421)
  - 變更檔案: 6 個
- **2022-04-01 15:52:58**: [資安]Q00-20220331001修正漏洞：透過下載附件URL替換檔案名稱就能下載任意使用者的附件
  - 變更檔案: 3 個
- **2022-03-31 16:41:01**: [資安]Q00-20220329003 修正jQuery版本過舊漏洞
  - 變更檔案: 351 個
- **2022-03-31 14:53:21**: [Web]Q00-20220330002 修正漏洞：一般使用者可登入後在瀏覽器開發者工具輸入goToURL語法進入產品授權註冊青單
  - 變更檔案: 1 個
- **2022-03-30 18:25:23**: [Web]Q00-20220330001修正透過修改參數hdnMethod:loginByQRCode, 不需要密碼便可直接登入BPM
  - 變更檔案: 5 個
- **2022-03-29 16:48:18**: [Web]Q00-20220329002 調整退回重辦流程清單以結案時間為主排序發起時間為副排序
  - 變更檔案: 1 個
- **2022-03-28 10:00:23**: [Web]Q00-20220328001 修正從首頁的待辦清單點選第二頁以後的任一流程，點擊繼續派送會報錯
  - 變更檔案: 1 個
- **2022-03-25 14:21:31**: [Web]Q00-20220325001 修正表單單身欄位對應錯誤
  - 變更檔案: 1 個
- **2022-03-23 17:01:14**: [WebService]Q00-*********** 移除MOfficeIntegrationEFGP WebService服務
  - 變更檔案: 2 個
- **2022-03-23 16:30:29**: [WebService]Q00-20220323002	修正BPM的WebService的SQLInjection漏洞
  - 變更檔案: 2 個
- **2022-03-22 15:15:25**: [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
  - 變更檔案: 1 個
- **2022-03-21 16:51:49**: [ESS]Q00-20220321003修正ESS流程經過取回or退回重辦在服務任務關卡前，撈取表單實例序號錯誤導致繼續派送報錯
  - 變更檔案: 1 個
- **2022-03-21 14:54:09**: [系統管理工具]S00-20220223003 新增資料來源設定，資料庫類型新增AZURE用的MSSQL
  - 變更檔案: 5 個
- **2022-03-21 10:51:48**: [Web]Q00-20220321001 修正絕對位置表單在第一關以外的關卡上傳附件按上傳後，開窗變空白
  - 變更檔案: 1 個
- **2022-03-18 11:56:49**: [系統管理工具]新增資料來源設定，資料庫類型新增PostgreSQL
  - 變更檔案: 4 個
- **2022-03-16 16:35:31**: [WorkFlowERP]S00-20220314001 新增WorkFlowERP表單
  - 變更檔案: 2 個
- **2022-03-16 16:27:39**: Revert "[WorkFlowERP]S00-20220314001 新增WorkFlowERP表單 PURI16,MOCI12"
  - 變更檔案: 2 個
- **2022-03-16 14:29:46**: [Web]S00-20220316002 修正漏洞downloadDocument的下載URL可任意下載其他路徑檔案
  - 變更檔案: 1 個
- **2022-03-16 11:19:36**: [Web]S00-20220316001 新增整合暫存管理供未註冊的BPM使用
  - 變更檔案: 1 個
- **2022-03-14 17:46:46**: [流程引擎]Q00-20220314003 修正流程關卡下一關卡為多人關卡處理且設定自動簽核為"與前一關相同簽核者，則跳過"，繼續派送會失敗
  - 變更檔案: 1 個
- **2022-03-14 17:32:56**: [WorkFlowERP]S00-20220314001 新增WorkFlowERP表單 PURI16,MOCI12
  - 變更檔案: 2 個
- **2022-03-10 15:45:19**: [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
  - 變更檔案: 2 個
- **2022-03-07 18:24:49**: [Web]Q00-20220307001 修正子單身過多造成表單開啟時間變長
  - 變更檔案: 1 個
- **2022-03-02 09:25:36**: [Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢[補修正]
  - 變更檔案: 1 個
- **2022-03-01 15:16:51**: Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **2022-03-01 15:16:38**: [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
  - 變更檔案: 2 個
- **2022-02-21 14:02:58**: [Web]Q00-*********** 修正離職交接人作業維護離職人員開窗增加離職人員可以選擇
  - 變更檔案: 1 個
- **2022-02-21 11:46:19**: [流程引擎]Q00-20220221001 修正流程派送到離職人員，離職人員沒有設置離職交接人作業維護就會派送失敗
  - 變更檔案: 2 個
- **2022-02-17 14:19:00**: [Web]Q00-20220217002 修正流程負責人在監控流程匯出Excel，處理者欄位內容應與系統管理員的處理者欄位內容一致
  - 變更檔案: 2 個
- **2022-02-16 18:22:42**: [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
  - 變更檔案: 2 個
- **2022-02-08 16:08:27**: [流程引擎]A00-20220127001修正流程退回重辦選擇"按照流程定義依序重新執行"，關卡會經過"服務任務"會導致主旨的退回重辦標籤沒有顯示
  - 變更檔案: 1 個
- **2022-01-26 10:41:27**: [Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單
  - 變更檔案: 1 個
- **2022-01-22 16:18:48**: [內部]Q00-20211115001新增GuardService連線成功的提示訊息
  - 變更檔案: 1 個
- **2022-01-21 11:39:58**: [Web]A00-20220120001修正IE開起流程用SQLcommand因為用replaceall函式導致報錯
  - 變更檔案: 1 個

### wayne (30 commits)

- **2022-04-22 17:18:19**: [內部]更新******* patch檔
  - 變更檔案: 1 個
- **2022-04-22 14:50:50**: [流程引擎]V00-20220420001 修正流程定義當核決關卡參考自定義關卡屬性時，流程派送到核決關卡時會無法正常派送
  - 變更檔案: 1 個
- **2022-04-21 15:38:11**: [Web]V00-20220421001 隱藏關注項目沒有分頁機制的筆數欄位
  - 變更檔案: 4 個
- **2022-04-20 11:08:26**: [內部] 調整「後置流程撤銷異常處理作業」改為「後置流程撤銷異常處理」
  - 變更檔案: 2 個
- **2022-04-18 16:47:57**: [Web]V00-20220415009 修正片語管理頁面內容新修刪後，片語輸入框不會清空
  - 變更檔案: 1 個
- **2022-04-18 16:28:58**: [內部]V00-20220414004 修正流程負責人-監控流程-點選「建立時間、流程結案時間」欄位排序，會出現請洽系統管理員
  - 變更檔案: 1 個
- **2022-04-18 16:20:17**: [內部]Q00-20220418001 調整「派送流程關聯」功能需納入序號卡控[補]
  - 變更檔案: 1 個
- **2022-04-18 11:46:58**: [內部]Q00-20220418001 調整「派送流程關聯」功能需納入序號卡控
  - 變更檔案: 4 個
- **2022-04-18 11:24:02**: [Web]S00-20220103001 產品授權註冊頁面增加顯示購買的模組類型[補]
  - 變更檔案: 3 個
- **2022-04-15 11:26:59**: [內部]V00-20220414003 修正一般使用者追蹤流程-已轉派的工作點選「全部」後，再點任一欄位上的排序會出現請洽系統管理員
  - 變更檔案: 1 個
- **2022-04-14 09:58:18**: [內部] 調整瀏覽器閒置過久，請重新登入的多語系
  - 變更檔案: 2 個
- **2022-04-12 14:44:21**: [內部]Q00-20220317002 增加服務接口「允許非流程處裡者，可以開啟追蹤流程表單畫面」[補]
  - 變更檔案: 3 個
- **2022-04-08 10:59:30**: [內部]更新******* patch檔
  - 變更檔案: 1 個
- **2022-04-08 10:35:29**: 更新越南語系
  - 變更檔案: 1 個
- **2022-04-01 14:17:04**: [組織設計工具]Q00-20220401001 修正組織設計師刪除職務核決層級定義；當該職務定義有被流程參考時，無法提示被引用的流程資訊
  - 變更檔案: 1 個
- **2022-03-31 13:51:04**: [Web]A00-20220330001 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系，但實際操作沒有呈現多語系的內容
  - 變更檔案: 1 個
- **2022-03-30 14:33:23**: [Web]S00-20220329001 登入機制優化，增加記錄密碼功能，Email連結自動跳轉[補]
  - 變更檔案: 2 個
- **2022-03-29 15:24:03**: [Web]S00-20220329001 登入機制優化，增加記錄密碼功能，Email連結自動跳轉
  - 變更檔案: 3 個
- **2022-03-25 16:56:17**: [Web]A00-20220325001 修正URL開啟追蹤TraceProcessForSearchForm畫面上的列印按鈕title無法正常顯示多語系
  - 變更檔案: 1 個
- **2022-03-25 16:30:15**: [流程引擎]Q00-20220325002 修正一般使用者-追蹤流程-已轉派的工作-匯出excel時，當資料庫為Oracle且匯出流程數量大於300時會報錯
  - 變更檔案: 1 個
- **2022-03-23 16:46:48**: [Web]A00-20220322001 修正流程負責人透過監控流程匯出excel時，當流程狀態為已完成、已撤銷、已中止時，「目前處理者」欄位仍有資料
  - 變更檔案: 1 個
- **2022-03-21 16:38:17**: [Web]Q00-20220321002 修正一般使用者-追蹤流程-已轉派的工作，使用「表單序號」當條件查詢無效
  - 變更檔案: 1 個
- **2022-03-21 16:28:59**: [Web]A00-20220317001 修正透過URL(TraceProcessForSearchForm)開啟追蹤流程頁面，且系統變數設置「追蹤頁面簽核歷程為top」時，簽核歷程無法展開顯示內容
  - 變更檔案: 4 個
- **2022-03-17 16:16:39**: [流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤[補]
  - 變更檔案: 1 個
- **2022-03-17 14:47:55**: [內部]Q00-20220317002 增加服務接口「允許非流程處裡者，可以開啟追蹤流程表單畫面」
  - 變更檔案: 2 個
- **2022-03-15 17:34:33**: [WEB]Q00-20220315004 修正離職作業維護-轉派離職人員的工作-勾選「全部轉派第一優先代理人」時，未將預設代理人帶回「接收者」欄位中
  - 變更檔案: 1 個
- **2022-03-15 14:10:49**: [WEB]A00-20220216001 修正追蹤流程-已轉派的工作，點進表單後再返回清單頁沒有保留原本的查詢條件
  - 變更檔案: 2 個
- **2022-03-15 14:01:56**: [WEB]Q00-20220315002 修正舊版表單InputElement若沒有textValue屬性時，儲存表單會發生錯誤
  - 變更檔案: 1 個
- **2022-03-11 16:49:37**: [流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤
  - 變更檔案: 6 個
- **2022-03-09 14:50:42**: [ISO]Q00-20220309001 修正ISO變更單，在ModDocRequester關卡載入附件時，若表單有選擇元件(RadioBox,ComboBox,CheckBox,ListBox)並將元件設定為invisible時，無法載入附件
  - 變更檔案: 1 個

### 王鵬程 (37 commits)

- **2022-04-22 16:50:44**: [Web]V00-20220422002 修正有取回重辦後，在行動版的待辦清單頁中該流程的主旨會無法正確顯示
  - 變更檔案: 1 個
- **2022-04-22 16:33:23**: [Web]V00-20220422001 修正行動版的待辦清單頁面中，有使用智能示警會無法正確顯示
  - 變更檔案: 1 個
- **2022-04-22 10:57:45**: [Web]S00-20211230002 調整自定義開窗當不需要模糊查詢欄位與欄位label時，就不顯示搜尋區塊
  - 變更檔案: 1 個
- **2022-04-20 18:24:03**: [Web]Q00-20220420003 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系但實際的標題未呈現多語系內容
  - 變更檔案: 1 個
- **2022-04-19 11:01:45**: [Web]Q00-20220419002 修正簽核意見內容很少只會顯示一行時的情況，在流程圖中該關卡與下個關卡之間不會有連接線
  - 變更檔案: 1 個
- **2022-04-18 17:29:48**: [Web]Q00-20220418002 修正administrator在跳過關卡時可填寫意見
  - 變更檔案: 1 個
- **2022-04-18 14:14:02**: [內部]V00-20220415012 修正使用者帳號中帶有^符號，從一個tool中再開啟其他tool會出現錯誤
  - 變更檔案: 1 個
- **2022-04-15 15:13:56**: [Web]V00-20220414001 修正流程有標示智能示警時，在行動版的追蹤(監控)、BPM首頁顯示會出現html文字
  - 變更檔案: 2 個
- **2022-04-13 14:39:06**: [Web]Q00-20220413003 修正DialogInputLabel有設提示文字，版更到5872後進入流程該元件未顯示提示訊息
  - 變更檔案: 1 個
- **2022-04-13 14:08:15**: [Web]Q00-20220413002 修正DialogInput有設提示文字，版更到5872後進入流程該元件未顯示提示訊息
  - 變更檔案: 1 個
- **2022-04-08 14:21:47**: [Web]Q00-20220408004 修正設定發起主旨為必填時，當只有按ENTER來換行也能發起流程
  - 變更檔案: 1 個
- **2022-01-26 11:26:40**: [Web]S00-20220103001 產品授權註冊頁面增加顯示購買的模組類型
  - 變更檔案: 5 個
- **2022-03-31 18:16:22**: [流程引擎]S00-*********** SQL註冊器內的設定可調用表單開窗增加查詢功[補]
  - 變更檔案: 1 個
- **2022-03-31 17:56:47**: [流程引擎]S00-*********** SQL註冊器內的設定可調用表單開窗增加查詢功能
  - 變更檔案: 6 個
- **2022-03-29 14:42:41**: [流程引擎]Q00-20220329001 修正模擬使用者進到BPM首頁再從下方進入待辦並簽核後，按回到清單頁會卡住
  - 變更檔案: 1 個
- **2022-03-25 17:45:39**: [系統管理工具]Q00-20220325003 修正使用者密碼帶有^符號，從一個tool中再開啟其他tool會出現錯誤
  - 變更檔案: 1 個
- **2022-03-24 17:04:15**: [表單設計師]Q00-20220324002 修正使用IE開啟有時間元件的RWD表單會出現錯誤
  - 變更檔案: 1 個
- **2022-03-24 15:59:40**: [表單設計師]Q00-*********** 調整表單設計器在發行表單的視窗中的截止有效日欄位改為非必填
  - 變更檔案: 3 個
- **2022-03-23 16:59:15**: [流程引擎]A00-*********** 修正SQL註冊器中語法有使用到order by 會導致報錯
  - 變更檔案: 1 個
- **2022-03-22 17:00:14**: [Web]Q00-*********** 修正行動版面下，從追蹤進入絕對表單的流程，沒有橫向scrollbar而無法看到完整表單
  - 變更檔案: 1 個
- **2022-03-21 11:42:46**: [Web]S00-20211013001 調整流程圖中能完整顯示出簽核意見內容[補]
  - 變更檔案: 1 個
- **2022-03-18 15:26:09**: [Web]Q00-20220317004 修正在IE開啟加簽頁面，確認按鈕無法點選
  - 變更檔案: 1 個
- **2022-03-16 14:34:18**: [表單設計師]Q00-20220316001 修正表單設計師DialogInputLabel有設提示文字，版到5872會導致提示文字出現在預設值
  - 變更檔案: 1 個
- **2022-03-15 16:19:59**: [表單設計師]Q00-20220315003 修正表單設計師DialogInput有設提示文字，版更到5872會導致提示文字出現在預設值
  - 變更檔案: 2 個
- **2022-03-09 11:48:44**: [Web]S00-20211013001 調整流程圖中能完整顯示出簽核意見內容
  - 變更檔案: 2 個
- **2022-03-08 15:44:47**: [流程引擎]A00-20220308001 修正一般使用者進入監控流程並點選『全部』，再到其他頁面後再回到監控會變一片空白
  - 變更檔案: 1 個
- **2022-03-04 17:59:14**: [Web]Q00-20220304001 修正查詢樣板使用6個$當作『請選擇』的內存值，在渲染後會被換成12個$
  - 變更檔案: 1 個
- **2022-03-01 17:16:43**: [流程引擎]A00-20220224001 修正條件式中各條件先做or後再做and，會無法派送到該條件後的關卡
  - 變更檔案: 1 個
- **2022-02-24 16:16:23**: [Web]Q00-20220224001 修正維護樣板作業從PC版切換到行動版時資料無法顯示
  - 變更檔案: 1 個
- **2022-02-24 14:47:46**: [Web]S00-20211117003 流程資料的頁面排序調整以發起時間大到小排序(DESC)
  - 變更檔案: 1 個
- **2022-02-18 17:28:24**: [Web]A00-20220216002 修正RWD表單當右下角有出現滑到頂部按鈕時，按鈕也會被列印出來
  - 變更檔案: 1 個
- **2022-02-18 11:40:13**: [Web]Q00-20220218002 修正維護樣板查詢欄位DropDown使用『請選擇』時，渲染到畫面上時該選項的順序沒出現在第一個
  - 變更檔案: 1 個
- **2022-02-17 14:10:05**: [Web]Q00-20220217001 修正使用32位元的Chrome進入BPM登入頁面會彈出BPM僅支援的瀏覽器資訊的警告框
  - 變更檔案: 1 個
- **2022-02-15 17:36:01**: [Web]Q00-20220215002 調整讓行動版與PC版一致讓Grid只支援(a、br、input、i、button)五種html標籤
  - 變更檔案: 1 個
- **2022-01-24 14:20:43**: [表單設計師]A00-20220120002 修正欄位樣板的三欄式和四欄式的最右邊欄位切分2欄後，再次編輯會變回切分1欄[補]
  - 變更檔案: 1 個
- **2022-01-21 17:58:43**: [表單設計師]A00-20220120002 修正欄位樣板的三欄式和四欄式的最右邊欄位切分2欄後，再次編輯會變回切分1欄
  - 變更檔案: 1 個
- **2022-01-20 18:23:35**: [Web]Q00-20220120003 流程代理人設定的選擇流程開窗，預設用流程代號做排序
  - 變更檔案: 1 個

### walter_wu (23 commits)

- **2022-04-21 18:41:08**: [Web]Q00-20220421003 優化iReport畫面開啟速度
  - 變更檔案: 4 個
- **2022-04-21 11:58:20**: [Web]Q00-20220421002 修正Radio&Checkbox在切換不同的Binding欄位內容時，還殘留前一次被勾選樣式的問題
  - 變更檔案: 1 個
- **2022-04-19 16:50:47**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
  - 變更檔案: 1 個
- **2022-04-19 10:45:07**: [Web]Q00-20220419001 修正如果切換讀取較久的頁面視覺上會跑一半又呈現原畫面再跳轉
  - 變更檔案: 1 個
- **2022-04-13 22:28:13**: S00-20211027003 新增記錄模擬使用者操作[補修正]
  - 變更檔案: 5 個
- **2022-04-13 21:32:11**: [流程引擎]S00-20211027003 新增記錄模擬使用者操作
  - 變更檔案: 15 個
- **2022-04-06 16:29:38**: [登入]S00-*********** 增加使用者登出登入紀錄[補修正]
  - 變更檔案: 2 個
- **2022-03-28 18:32:04**: [在線閱覽]Q00-20220328003 修正如果文件主機與流程主機不同台在線閱覽畫面打開後會看不到檔案內容
  - 變更檔案: 5 個
- **2022-03-09 23:39:16**: [登入]S00-*********** 增加使用者登出登入紀錄
  - 變更檔案: 20 個
- **2022-03-09 15:27:01**: [在線閱覽]S00-20210617002 關卡 新增在線閱讀選項
  - 變更檔案: 23 個
- **2022-02-08 17:23:31**: [在線閱覽]S00-20211112003 在線閱覽允許轉檔類型系統參數
  - 變更檔案: 7 個
- **2022-01-25 16:46:20**: [Web]S00-20210910001 移除攻略雲設定，轉檔異常處理作業只在出或移除，建patch時另外處理
  - 變更檔案: 2 個
- **2022-01-25 15:15:19**: [在線閱覽]S00-20211027002 在線閱覽開關
  - 變更檔案: 6 個
- **2021-10-25 18:47:29**: [流程引擎]S00-20210910003 退回重辦啟動代理人機制功能
  - 變更檔案: 4 個
- **2022-03-24 17:25:19**: [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出
  - 變更檔案: 1 個
- **2022-03-15 14:52:49**: [組織同步]A00-20220314001 修正組織同步完會蓋掉使用者設定的 使用者是否顯示待辦事項小視窗
  - 變更檔案: 1 個
- **2022-03-04 18:24:11**: [登入]Q00-20220304002 調整登入加密機制，避免後端session失效時取不到值登入失敗
  - 變更檔案: 6 個
- **2022-03-03 16:11:02**: [流程引擎]Q00-20220215001 修正偶發附件遺失問題[補修正]
  - 變更檔案: 1 個
- **2022-03-02 17:41:15**: [組織同步]A00-20220224002 修正組織同步完會蓋掉使用者設定的 簽核完畢後的行為
  - 變更檔案: 1 個
- **2022-03-01 16:23:22**: [Web]Q00-20220301001 修正SQLCommand沒有設定SQL卻佔用連線的問題
  - 變更檔案: 1 個
- **2022-02-17 22:58:33**: [Web]A00-20220214001 修正附件權限設定關卡在追蹤流程看不到的問題
  - 變更檔案: 1 個
- **2022-02-15 16:28:22**: [流程引擎]S00-20220210002 更新產品Base所有Mail.jar到與Wildfly15使用的1.6.2一致
  - 變更檔案: 8 個
- **2022-02-15 16:11:04**: [流程引擎]Q00-20220215001 修正偶發附件遺失問題
  - 變更檔案: 3 個

### yanann_chen (35 commits)

- **2022-04-20 12:05:30**: [Web]V00-20220415003 修正系統設定「解析HTML tag」時，員工工作轉派流程主旨顯示異常[補]
  - 變更檔案: 1 個
- **2022-04-20 12:03:44**: [Web]V00-20220415008 修正系統設定「解析HTML tag」時，刪除系統流程通知後，結果頁面上流程主旨顯示異常[補]
  - 變更檔案: 1 個
- **2022-04-20 11:51:29**: [Web]V00-20220415007 修正系統設定「解析HTML tag」時，工作取回重辦後，取回重辦結果頁面上流程主旨顯示異常[補]
  - 變更檔案: 1 個
- **2022-04-20 11:35:45**: [Web]V00-20220415004 修正系統設定「解析HTML tag」時，重啟服務清單主旨顯示異常[補]
  - 變更檔案: 1 個
- **2022-04-18 17:17:52**: [Web]V00-20220415008 修正系統設定「解析HTML tag」時，刪除系統流程通知後，結果頁面上流程主旨顯示異常
  - 變更檔案: 1 個
- **2022-04-18 16:52:09**: [Web]V00-20220415007 修正系統設定「解析HTML tag」時，工作取回重辦後，取回重辦結果頁面上流程主旨顯示異常
  - 變更檔案: 1 個
- **2022-04-18 15:52:22**: [Web]V00-20220415004 修正系統設定「解析HTML tag」時，重啟服務清單主旨顯示異常
  - 變更檔案: 2 個
- **2022-04-18 13:43:58**: [Web]V00-20220415003 修正系統設定「解析HTML tag」時，員工工作轉派流程主旨顯示異常
  - 變更檔案: 2 個
- **2022-04-11 18:34:44**: [流程引擎]Q00-20220411006 修正派送流程關聯設定中退回及取回重辦「不處理」設定無效問題
  - 變更檔案: 1 個
- **2022-04-11 16:40:27**: [流程引擎]Q00-20220406001 修正因日期元件缺少特定屬性造成流程無法正常往下派送的問題
  - 變更檔案: 1 個
- **2022-04-08 15:57:09**: [資安]Q00-20220408005 修正jQuery版本過舊漏洞
  - 變更檔案: 3 個
- **2022-04-08 13:39:45**: [內部]Q00-20220408001 優化5.8.2.1及5.8.3.1的update sql指令
  - 變更檔案: 3 個
- **2022-04-07 13:30:59**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 1 個
- **2022-04-06 11:31:39**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 2 個
- **2022-04-06 11:23:58**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 8 個
- **2022-03-31 16:24:13**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 6 個
- **2022-03-25 15:42:36**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 1 個
- **2022-03-23 15:00:06**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 1 個
- **2022-03-22 17:14:28**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 1 個
- **2022-03-22 14:17:04**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 23 個
- **2022-03-03 14:27:17**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 2 個
- **2022-03-02 13:42:31**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 2 個
- **2022-03-01 15:35:32**: [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
  - 變更檔案: 11 個
- **2021-12-06 13:51:36**: [流程引擎]S00-*********** 新增派送流程關聯設定作業
  - 變更檔案: 13 個
- **2022-01-18 17:01:11**: [流程引擎]Q00-*********** 調整Create Index指令，新增Index
  - 變更檔案: 2 個
- **2022-03-24 18:28:08**: [流程引擎]A00-20220323001 修正流程核決層級關卡之後的關卡若有設定自動簽核，流程無法往下派送的問題
  - 變更檔案: 1 個
- **2022-02-25 16:45:50**: [流程引擎]Q00-20220208004 修正「已轉派的工作」清單，在「全部」頁籤取得的資料筆數與「處理中」、「已處理」兩個頁籤相加的數量不符
  - 變更檔案: 1 個
- **2022-02-11 18:14:38**: [流程引擎]Q00-20220211001 調整簡易流程圖預先解析，當前核決層級關卡的工作處理者為原處理者的代理人時，以原處理者解析後續關卡
  - 變更檔案: 1 個
- **2022-02-10 17:51:10**: [ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態
  - 變更檔案: 1 個
- **2022-02-09 18:06:33**: [流程引擎]Q00-20220209001 調整追蹤流程接口邏輯，當追蹤流程url中未包含表單定義ID時，開放給流程處理者與系統管理員查看流程
  - 變更檔案: 1 個
- **2022-02-08 18:03:20**: [流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過
  - 變更檔案: 2 個
- **2022-02-08 17:06:17**: [Web]Q00-20220208002 單身加總欄位設定「顯示至小數點後第X位」且在其他欄位的運算規則中，單身加總數值改變後沒有觸發欄位運算
  - 變更檔案: 2 個
- **2022-02-08 14:49:14**: [流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程
  - 變更檔案: 1 個
- **2022-01-20 16:51:55**: [流程引擎]Q00-20220120001 修正「使用者有多個部門，在選擇發起部門後若發起流程失敗，回到表單頁面後無法再發起流程或儲存表單」問題
  - 變更檔案: 1 個
- **2022-01-20 16:50:48**: [流程引擎]A00-20211221001 修正「第一次發起流程因使用者填寫的表單資料有誤導致發起失敗，在使用者更正表單後仍無法發起流程」的問題
  - 變更檔案: 1 個

### cherryliao (14 commits)

- **2022-04-15 10:42:32**: [內部]Q00-20220414002 修正決關卡中核決條件之描述說明因刪除條件運算式導致清空或復原到某些操作前的值問題
  - 變更檔案: 2 個
- **2022-04-11 13:45:04**: [內部]Q00-20220411003 修正核決關卡中核決條件之描述說明會因核決條件於新增或編輯時回到預設的問題
  - 變更檔案: 1 個
- **2022-04-11 11:37:07**: [內部]Q00-20220411002 調整行動端查看ERP退件資訊開啟退件表單後點擊返回按鈕後畫面的問題
  - 變更檔案: 4 個
- **2021-12-15 18:06:35**: [流程引擎]S00-20210915002 新增BPM首頁預設撈取的時間區間是依系統變數設定
  - 變更檔案: 6 個
- **2021-12-07 16:47:37**: [流程引擎]S00-20210621004 新增待辦事項記錄簽核的Client IP
  - 變更檔案: 13 個
- **2021-11-29 11:38:42**: [流程引擎]S00-20210726001 新增流程草稿列表預設撈取的時間區間是依系統變數設定
  - 變更檔案: 9 個
- **2021-11-22 11:53:48**: [Web]S00-20210506012 新增核決關卡中核決條件之描述說明
  - 變更檔案: 20 個
- **2022-03-11 14:42:53**: [BPM APP]C01-*********** 移動授權中間層的使用者維護作業增加分頁功能[補]
  - 變更檔案: 3 個
- **2022-03-01 11:16:03**: [BPM APP]S00-20211027004新增移動端支援ERP流程終止或撤銷時，若單據修改重新送審後於移動表單可查看之前審批流程的功能[補]
  - 變更檔案: 9 個
- **2022-02-23 09:07:45**: [BPM APP]C01-*********** 移動授權中間層的使用者維護作業增加分頁功能
  - 變更檔案: 8 個
- **2022-02-15 11:35:12**: [BPM APP]S00-20211027004 新增移動端支援ERP流程終止或撤銷時，若單據修改重新送審後於移動表單可查看之前審批流程的功能
  - 變更檔案: 11 個
- **2022-01-24 10:52:30**: [表單設計師]S00-20210915004 新增Grid元件可設定新增、修改、删除按鈕的顏色屬性
  - 變更檔案: 9 個
- **2022-01-21 16:48:25**: [表單設計師]S00-20211117004 調整RWD表單複合元件開窗類型為自定義開窗時參與者型態增加部門/專案
  - 變更檔案: 2 個
- **2022-01-21 16:25:40**: [表單設計師]S00-20210811002 調整Web表單設計器日期時間元件比較欄位和TextBox數值欄位運算規則過濾元件本身代號
  - 變更檔案: 2 個

### 郭哲榮 (9 commits)

- **2022-04-13 12:20:07**: [BPM APP]Q00-20220314002 修正客製開窗為多選且無資料時返回按鈕顯示異常
  - 變更檔案: 1 個
- **2022-04-12 20:14:52**: [BPM APP]C01-20220308005修正Grid綁定的元件使用FormUtil.show方法時，在新增資料維護介面欄位分割顯示錯誤[補]
  - 變更檔案: 1 個
- **2022-04-11 18:28:52**: [內部]Q00-20220408003 調整移動端連續簽核參數化開關SQL無法重複執行問題
  - 變更檔案: 2 個
- **2022-04-01 13:17:46**: [BPM APP]S00-20211101001 新增移動端連續簽核系統參數化開關
  - 變更檔案: 2 個
- **2022-03-31 20:26:01**: [BPM APP]S00-20211101001 新增移動端連續簽核系統參數化開關
  - 變更檔案: 9 個
- **2022-03-14 18:56:49**: [BPM APP]C01-20220308005 修正Grid綁定的元件使用FormUtil.show方法時，在新增資料維護介面欄位分割顯示錯誤
  - 變更檔案: 1 個
- **2022-03-02 18:08:26**: [BPM App]C01-20220301003 修正E10表單在移動端操作日期元件時會無法帶入值與必填訊息顯示報錯的問題
  - 變更檔案: 1 個
- **2022-02-17 13:36:24**: [BPM APP]C01-20220211002 修正客製開窗在重組SQL指令的邏輯時，在錯誤的地方插入where條件
  - 變更檔案: 1 個
- **2022-02-08 13:37:02**: [BPM APP]C01-20220207001 修正企業微信的待辦推播若已被處理過導向追蹤已處理的表單畫面時缺少的多語系
  - 變更檔案: 1 個

### yamiyeh10 (9 commits)

- **2022-04-11 10:51:49**: [BPM APP]Q00-20211208001 修正企業微信使用者維護作業在MSSQL無法顯示多語系的問題
  - 變更檔案: 2 個
- **2022-04-08 15:11:19**: [MPT]S00-20211117001 調整公告申請單新增富文本功能
  - 變更檔案: 2 個
- **2022-03-31 16:44:03**: [BPM APP]C01-*********** 修正行動端HandWriting元件在script設置disable時無法正常使用問題
  - 變更檔案: 1 個
- **2022-03-16 13:37:57**: [BPM APP]C01-20220316003 修正行動端DialogInput元件有設定提示文字時會顯示_的問題
  - 變更檔案: 1 個
- **2022-03-14 18:00:12**: [BPM APP]C01-20220311003 修正行動端FormUtil的getValue方法異常問題
  - 變更檔案: 1 個
- **2022-03-10 10:28:12**: [BPM APP]C01-20220309001 修正Web端行動模擬發單功能於表單載入時不會呼叫到formCreate方法問題
  - 變更檔案: 1 個
- **2022-03-08 10:08:13**: [BPM APP]C01-20220224004 修正在取得IMG動態生成表單應用資料與取得綁訂使用者資料時會偶發statement close問題
  - 變更檔案: 1 個
- **2022-03-01 18:45:31**: [BPM APP]C01-20220301002 修正在沒有整合行動方案情況下開啟Web表單設計師任一表單時會發生錯誤的問題
  - 變更檔案: 1 個
- **2022-01-21 17:42:33**: [BPM APP]C01-20220118001 修正行動端表單多個Checkbox元件且相同元件名稱時簽核後勾選的選項值不會顯示問題
  - 變更檔案: 1 個

### pinchi_lin (8 commits)

- **2022-04-08 18:51:15**: [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]
  - 變更檔案: 1 個
- **2022-03-02 10:35:08**: [MPT]調整首頁模組相關上傳下載功能機制的上傳與下載接口有亂碼問題
  - 變更檔案: 1 個
- **2022-01-21 15:55:55**: [MPT]調整首頁模組相關上傳下載功能機制新增的檔案作廢接口
  - 變更檔案: 5 個
- **2021-12-24 15:59:43**: [MPT]調整首頁模組相關上傳下載功能機制新增的檔案下載接口
  - 變更檔案: 2 個
- **2021-12-10 11:52:06**: [MPT]調整首頁模組相關上傳下載功能機制新增的檔案上傳接口
  - 變更檔案: 2 個
- **2022-03-11 18:02:08**: [BPM APP]C01-20220311002 修正用平板登IMG時session會清空導致發單簽核有空指針異常問題
  - 變更檔案: 1 個
- **2022-02-10 14:00:07**: [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]
  - 變更檔案: 1 個
- **2022-01-22 11:40:35**: [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題
  - 變更檔案: 1 個

### waynechang (3 commits)

- **2022-02-16 11:10:55**: [Web]S00-20220120003 表單上傳附件支持多選上傳[補]
  - 變更檔案: 1 個
- **2022-02-15 17:44:24**: [Web]S00-20220120003 表單上傳附件支持多選上傳
  - 變更檔案: 6 個
- **2022-01-24 17:47:23**: [ISO]S00-20210507001 PDF浮水印屬性管理新增「圖片浮水印」功能(BCL8)
  - 變更檔案: 1 個

### ocean_yeh (3 commits)

- **2022-03-15 13:39:07**: [Web]Q00-20220223001表單無法派送，把元件applUserId刪除重拉就可以正常派送
  - 變更檔案: 1 個
- **2022-02-23 17:37:37**: [Web]Q00-20220223001 修正腳本樣板使用QRCode語法，儲存表單時QRCode消失
  - 變更檔案: 1 個
- **2022-02-17 15:45:28**: [Web]Q00-20220217004 TextBox有設定小數位時，使用FormUtil的寫法無法在formOpen時更換背景色
  - 變更檔案: 1 個

## 詳細變更記錄

### 1. [內部]增加Intellij IDEA支持(透過Maven)，打包版號固定為*******
- **Commit ID**: `16903d044a5b9d091d246fbfda970f479188ad0f`
- **作者**: lorenchang
- **日期**: 2022-06-26 20:48:05
- **變更檔案數量**: 25
- **檔案變更詳細**:
  - 📝 **修改**: `.gitignore`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/build-exe_maven.xml`
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/crm-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/designer-common/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/domain/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/dto/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-designer-blink/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/persistence/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/bpmToolEntrySimple.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/service/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-authority/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/WildFly/jboss-client.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/pom.xml`
  - ➕ **新增**: `3.Implementation/subproject/webapp/pom.xml`
  - ➕ **新增**: `pom.xml`

### 2. [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務[補修正]
- **Commit ID**: `4a485905fa02d6af9a71a752f762101de03b5953`
- **作者**: 林致帆
- **日期**: 2022-04-25 15:47:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`

### 3. [內部]Q00-20220414001調整派送表單關聯設定作業SQL指令 [補修正]
- **Commit ID**: `24e98182eefd2ca6f251881a5eef33973085295c`
- **作者**: 林致帆
- **日期**: 2022-04-22 17:45:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 4. [內部]更新******* patch檔
- **Commit ID**: `c06df60cef26b21f7214ae2b860eb601cfe8d2ef`
- **作者**: wayne
- **日期**: 2022-04-22 17:18:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 5. [Web]V00-20220422002 修正有取回重辦後，在行動版的待辦清單頁中該流程的主旨會無法正確顯示
- **Commit ID**: `30dcc37a005bdb7884f2d6887d6ba3a49f925167`
- **作者**: 王鵬程
- **日期**: 2022-04-22 16:50:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 6. [Web]V00-20220422001 修正行動版的待辦清單頁面中，有使用智能示警會無法正確顯示
- **Commit ID**: `23db93abf5bacd818900a0b62fba70274d50b345`
- **作者**: 王鵬程
- **日期**: 2022-04-22 16:33:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 7. [流程引擎]V00-20220420001 修正流程定義當核決關卡參考自定義關卡屬性時，流程派送到核決關卡時會無法正常派送
- **Commit ID**: `ea7174b86d34cadb9d6145987ab50e9bd41396a5`
- **作者**: wayne
- **日期**: 2022-04-22 14:50:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/Organization.java`

### 8. [Web]S00-20211230002 調整自定義開窗當不需要模糊查詢欄位與欄位label時，就不顯示搜尋區塊
- **Commit ID**: `15690cad83a83f4515fa56b1a11211a8e68d533b`
- **作者**: 王鵬程
- **日期**: 2022-04-22 10:57:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`

### 9. [Web]Q00-20220421003 優化iReport畫面開啟速度
- **Commit ID**: `9dc01090b26b20a29580be8c1ea0ffa33f8faca6`
- **作者**: walter_wu
- **日期**: 2022-04-21 18:41:08
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/report/ReportDefMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageCustomReportAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`

### 10. [Web]V00-20220421001 隱藏關注項目沒有分頁機制的筆數欄位
- **Commit ID**: `df41b5916fbde8a04b1cf6ce5a38c1aa5e30d9fe`
- **作者**: wayne
- **日期**: 2022-04-21 15:38:11
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`

### 11. [Web]Q00-20220421002 修正Radio&Checkbox在切換不同的Binding欄位內容時，還殘留前一次被勾選樣式的問題
- **Commit ID**: `1f5c9e22936de536b3d1139dfab6110f23faf049`
- **作者**: walter_wu
- **日期**: 2022-04-21 11:58:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 12. [流程設計師]S00-20211105002 服務任務RESTful路徑新增#符號判別要呼叫哪台流程主機
- **Commit ID**: `dd85b2dc40e85ded329e575dafc36423504a5ad2`
- **作者**: 林致帆
- **日期**: 2022-04-21 11:21:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/RestfulHelper.java`

### 13. [Web]Q00-20220420003 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系但實際的標題未呈現多語系內容
- **Commit ID**: `a2e939d1a04e38aed8a718414ca5381d5d0586c8`
- **作者**: 王鵬程
- **日期**: 2022-04-20 18:24:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 14. [E10]Q00-20220420002調整標準出貨E10流程取得附件服務任務RESTful路徑改成/NaNaWeb/api/v1/process/attachinfo/documents/get
- **Commit ID**: `6b0d6431dfbd2c0f9c5c4f81e2412c91e9ea9afb`
- **作者**: 林致帆
- **日期**: 2022-04-20 16:28:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `"6.Deployment/DeploymentPlan/copyfiles/@e10/process-default/e10\346\265\201\347\250\213\347\257\204\346\234\254.bpmn"`

### 15. [Web]V00-20220415003 修正系統設定「解析HTML tag」時，員工工作轉派流程主旨顯示異常[補]
- **Commit ID**: `043f5f5ee9e65e445a9396ccd7fd9982df984acc`
- **作者**: yanann_chen
- **日期**: 2022-04-20 12:05:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 16. [Web]V00-20220415008 修正系統設定「解析HTML tag」時，刪除系統流程通知後，結果頁面上流程主旨顯示異常[補]
- **Commit ID**: `07111c099967ab6d2fe3395564ed9a88bdc1b720`
- **作者**: yanann_chen
- **日期**: 2022-04-20 12:03:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`

### 17. [Web]V00-20220415007 修正系統設定「解析HTML tag」時，工作取回重辦後，取回重辦結果頁面上流程主旨顯示異常[補]
- **Commit ID**: `6f0100f50bdd9f0ddb304118173c12ae60501e17`
- **作者**: yanann_chen
- **日期**: 2022-04-20 11:51:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`

### 18. [Web]V00-20220415004 修正系統設定「解析HTML tag」時，重啟服務清單主旨顯示異常[補]
- **Commit ID**: `dfc4b33c3b52c4a7d41c8cf75c0ed9fe8a09e571`
- **作者**: yanann_chen
- **日期**: 2022-04-20 11:35:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`

### 19. [內部] 調整「後置流程撤銷異常處理作業」改為「後置流程撤銷異常處理」
- **Commit ID**: `e7c8a2f345b7aff89c2cfdf1176f9923b3ab2725`
- **作者**: wayne
- **日期**: 2022-04-20 11:08:26
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 20. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出[補修正]
- **Commit ID**: `5887386a736900de94429ecc15518a4205ba64da`
- **作者**: walter_wu
- **日期**: 2022-04-19 16:50:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 21. [Web]Q00-20220419002 修正簽核意見內容很少只會顯示一行時的情況，在流程圖中該關卡與下個關卡之間不會有連接線
- **Commit ID**: `ae653a80f831776d52a001bf267aab30eefb447f`
- **作者**: 王鵬程
- **日期**: 2022-04-19 11:01:45
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`

### 22. [Web]Q00-20220419001 修正如果切換讀取較久的頁面視覺上會跑一半又呈現原畫面再跳轉
- **Commit ID**: `5e2bd255bef8b19d8a6bfea41b077890794f495a`
- **作者**: walter_wu
- **日期**: 2022-04-19 10:45:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 23. [Web]V00-20220415005 修正首頁的我的關注筆數與我的關注流程清單數量不一致
- **Commit ID**: `6362bfa2106fa61e48f679a31636427bfb8324e0`
- **作者**: 林致帆
- **日期**: 2022-04-19 09:20:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`

### 24. [Web]Q00-20220418002 修正administrator在跳過關卡時可填寫意見
- **Commit ID**: `61b0a1f4e22b349efdeeec4788c06649f59d337b`
- **作者**: 王鵬程
- **日期**: 2022-04-18 17:29:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`

### 25. [Web]V00-20220415008 修正系統設定「解析HTML tag」時，刪除系統流程通知後，結果頁面上流程主旨顯示異常
- **Commit ID**: `7aa23d659bad65698056e177e49230e8c648e7f3`
- **作者**: yanann_chen
- **日期**: 2022-04-18 17:17:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`

### 26. [Web]V00-20220415007 修正系統設定「解析HTML tag」時，工作取回重辦後，取回重辦結果頁面上流程主旨顯示異常
- **Commit ID**: `678d1b60d49dd0619cfd801c8bd681a49d450433`
- **作者**: yanann_chen
- **日期**: 2022-04-18 16:52:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`

### 27. [Web]V00-20220415009 修正片語管理頁面內容新修刪後，片語輸入框不會清空
- **Commit ID**: `b295656f8bbd07ebcae38000bf458afcb65bc928`
- **作者**: wayne
- **日期**: 2022-04-18 16:47:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`

### 28. [內部]V00-20220414004 修正流程負責人-監控流程-點選「建立時間、流程結案時間」欄位排序，會出現請洽系統管理員
- **Commit ID**: `98e4ec36c4c5f7ef3627dae603977872121497cb`
- **作者**: wayne
- **日期**: 2022-04-18 16:28:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 29. [內部]Q00-20220418001 調整「派送流程關聯」功能需納入序號卡控[補]
- **Commit ID**: `24b4ebb32b14b4971218661540cb174d11a9bf9d`
- **作者**: wayne
- **日期**: 2022-04-18 16:20:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 30. [Web]V00-20220415004 修正系統設定「解析HTML tag」時，重啟服務清單主旨顯示異常
- **Commit ID**: `364e78864745cae9fa95d31f83b78f2e29716d4e`
- **作者**: yanann_chen
- **日期**: 2022-04-18 15:52:22
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/SuspendedInvokeActListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`

### 31. [Web]V00-20220415006修正 流程主旨帶有「\」符號，撤銷流程清單無法呈現
- **Commit ID**: `e9c2ca23f74fa46fa335ae6c586d3f786931da3d`
- **作者**: 林致帆
- **日期**: 2022-04-18 15:04:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AbortableProcessInstListReader.java`

### 32. [內部]V00-20220415012 修正使用者帳號中帶有^符號，從一個tool中再開啟其他tool會出現錯誤
- **Commit ID**: `d74fe922be205acf5e8e382713a9777edc5125b6`
- **作者**: 王鵬程
- **日期**: 2022-04-18 14:14:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java`

### 33. [Web]V00-20220415003 修正系統設定「解析HTML tag」時，員工工作轉派流程主旨顯示異常
- **Commit ID**: `35073186b0855c52b4e0f208f4c2fd3532af9eeb`
- **作者**: yanann_chen
- **日期**: 2022-04-18 13:43:58
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignableWorkItemListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`

### 34. [內部]Q00-20220418001 調整「派送流程關聯」功能需納入序號卡控
- **Commit ID**: `5c5fd7093a281ade93999f128c0593936e9f93ec`
- **作者**: wayne
- **日期**: 2022-04-18 11:46:58
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/module/ProgramDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 35. [Web]S00-20220103001 產品授權註冊頁面增加顯示購買的模組類型[補]
- **Commit ID**: `a877ebf1e38862e9ac7a17a057ff57482914fe58`
- **作者**: wayne
- **日期**: 2022-04-18 11:24:02
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/license/LicenseRegTO.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`

### 36. [Web]A00-20220414001修正表單設計器的搜尋表單欄位一直查詢導至InUseCount增長不會釋放
- **Commit ID**: `d6c2d91e8d1203fce332d3abec9bceee67b873e5`
- **作者**: 林致帆
- **日期**: 2022-04-15 17:58:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDefinitionSearchReader.java`

### 37. [Web]V00-20220414001 修正流程有標示智能示警時，在行動版的追蹤(監控)、BPM首頁顯示會出現html文字
- **Commit ID**: `8053dbd9acfb6770f3d1e85ce8f0f39954cd047e`
- **作者**: 王鵬程
- **日期**: 2022-04-15 15:13:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`

### 38. [Web]Q00-20220414002 修正一般使用者追蹤流程清單匯出Excel報表，簽核時間欄位沒有值
- **Commit ID**: `163b47f31093be89338ea830fd4f4bd84574cd02`
- **作者**: 林致帆
- **日期**: 2022-04-15 13:37:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 39. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `13896f5ce56ba83cab3755b0e85d2e48905f35f2`
- **作者**: 林致帆
- **日期**: 2022-04-15 11:39:27
- **變更檔案數量**: 0

### 40. Revert "[內部]V00-20220415002 修正命名不符合BPM附件檔名的檔案還是能透過URL下載附件"
- **Commit ID**: `7f6e73340b492ea969553a0b5c6074b8822a37ab`
- **作者**: 林致帆
- **日期**: 2022-04-15 11:39:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 41. [內部]V00-20220414003 修正一般使用者追蹤流程-已轉派的工作點選「全部」後，再點任一欄位上的排序會出現請洽系統管理員
- **Commit ID**: `d03ec4255bf50d783a583fba1102b2e356fd200c`
- **作者**: wayne
- **日期**: 2022-04-15 11:26:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 42. [內部]V00-20220415002 修正命名不符合BPM附件檔名的檔案還是能透過URL下載附件
- **Commit ID**: `4bf6aaf77f41ccde3d9a9e42318969517aff4008`
- **作者**: 林致帆
- **日期**: 2022-04-15 11:09:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 43. [內部]Q00-20220414002 修正決關卡中核決條件之描述說明因刪除條件運算式導致清空或復原到某些操作前的值問題
- **Commit ID**: `73b71f6da741f39b5f91e0ae384aa195f21a8816`
- **作者**: cherryliao
- **日期**: 2022-04-15 10:42:32
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditorPanel.java`

### 44. [流程引擎]Q00-20220415001 修正因多餘附件移除邏輯改成流程結案處理，導至流程無法結案
- **Commit ID**: `f75715d1771213511c50a877aaabd59f53a52a47`
- **作者**: 林致帆
- **日期**: 2022-04-15 10:15:20
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DefaultFileServiceImpl.java`

### 45. [內部] 調整瀏覽器閒置過久，請重新登入的多語系
- **Commit ID**: `c8977b85267897b10ffa7ce31e44d6d9d3daa65f`
- **作者**: wayne
- **日期**: 2022-04-14 09:58:18
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 46. [內部]Q00-20220414001調整派送表單關聯設定作業SQL指令
- **Commit ID**: `15de8d6f7b28d4dc01bfef13ca920ae37dcbf849`
- **作者**: 林致帆
- **日期**: 2022-04-14 08:15:40
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`

### 47. S00-20211027003 新增記錄模擬使用者操作[補修正]
- **Commit ID**: `8ba4ef64c4fcef35c577c0b23009581162711b71`
- **作者**: walter_wu
- **日期**: 2022-04-13 22:28:13
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 48. [流程引擎]S00-20211027003 新增記錄模擬使用者操作
- **Commit ID**: `975cfc8e30875624311bcf52535ee86ceb6e64a1`
- **作者**: walter_wu
- **日期**: 2022-04-13 21:32:11
- **變更檔案數量**: 15
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkItem.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/notification/TemplateTranslatorBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReexecuteActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/vo/WorkItemVo.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTracing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessWorkbox.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/ProcessWorkbox.java`

### 49. [Web]Q00-20220413003 修正DialogInputLabel有設提示文字，版更到5872後進入流程該元件未顯示提示訊息
- **Commit ID**: `082539c3e38c1b661f4201df76e2c18d8a098d7d`
- **作者**: 王鵬程
- **日期**: 2022-04-13 14:39:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 50. [Web]Q00-20220413002 修正DialogInput有設提示文字，版更到5872後進入流程該元件未顯示提示訊息
- **Commit ID**: `889026049211e243aab8f1e499d0a5f50b5e2d07`
- **作者**: 王鵬程
- **日期**: 2022-04-13 14:08:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 51. [BPM APP]Q00-20220314002 修正客製開窗為多選且無資料時返回按鈕顯示異常
- **Commit ID**: `bed308f7743a1f25bb0d00e47c54636ae63973db`
- **作者**: 郭哲榮
- **日期**: 2022-04-13 12:20:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/MobileCustomOpenWin.js`

### 52. [BPM APP]C01-20220308005修正Grid綁定的元件使用FormUtil.show方法時，在新增資料維護介面欄位分割顯示錯誤[補]
- **Commit ID**: `700c19ef188c40dae1b4ad28fda7a20d13706e51`
- **作者**: 郭哲榮
- **日期**: 2022-04-12 20:14:52
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 53. [內部]Q00-20220317002 增加服務接口「允許非流程處裡者，可以開啟追蹤流程表單畫面」[補]
- **Commit ID**: `76fd37637fea056e466e83c79209ba6718f52449`
- **作者**: wayne
- **日期**: 2022-04-12 14:44:21
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-traceProcess-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`

### 54. [流程引擎]Q00-20220411006 修正派送流程關聯設定中退回及取回重辦「不處理」設定無效問題
- **Commit ID**: `b264c7959db681158e5a29968a59b3a3f9257f69`
- **作者**: yanann_chen
- **日期**: 2022-04-11 18:34:44
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`

### 55. [內部]Q00-20220408003 調整移動端連續簽核參數化開關SQL無法重複執行問題
- **Commit ID**: `625bdbb20628ea055683d5b77063eefedee32a62`
- **作者**: 郭哲榮
- **日期**: 2022-04-11 18:28:52
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 56. [流程引擎]Q00-20220406001 修正因日期元件缺少特定屬性造成流程無法正常往下派送的問題
- **Commit ID**: `06f06d5062ad1ee242756236a98c5ea0d0f85c17`
- **作者**: yanann_chen
- **日期**: 2022-04-11 16:40:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`

### 57. [內部]Q00-20220411003 修正核決關卡中核決條件之描述說明會因核決條件於新增或編輯時回到預設的問題
- **Commit ID**: `015a00368b424935456ae1db2ceb69404954ba86`
- **作者**: cherryliao
- **日期**: 2022-04-11 13:45:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditorPanel.java`

### 58. [內部]Q00-20220411002 調整行動端查看ERP退件資訊開啟退件表單後點擊返回按鈕後畫面的問題
- **Commit ID**: `9a68d19b2a423f07b0f463f885d7178f93fb5059`
- **作者**: cherryliao
- **日期**: 2022-04-11 11:37:07
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileReturnRecordView.js`

### 59. [BPM APP]Q00-20211208001 修正企業微信使用者維護作業在MSSQL無法顯示多語系的問題
- **Commit ID**: `eced9fbb19449b9a2d645ab75c4d7f23baa8d037`
- **作者**: yamiyeh10
- **日期**: 2022-04-11 10:51:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@mobile/db/create/DDL_InitMobileDB_MSSQL.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@mobile/db/update/*******_mobile_DDL_MSSQL_1.sql`

### 60. [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]
- **Commit ID**: `6d602b77c469b4a9f2e39bc3a4709b2bf09af315`
- **作者**: pinchi_lin
- **日期**: 2022-04-08 18:51:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 61. [資安]Q00-20220408005 修正jQuery版本過舊漏洞
- **Commit ID**: `aa8d05e50f53067834130107b920c91558e70cea`
- **作者**: yanann_chen
- **日期**: 2022-04-08 15:57:09
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp`

### 62. [MPT]S00-20211117001 調整公告申請單新增富文本功能
- **Commit ID**: `34db332af1eaf7384cff7ab94f93e1fc04a7d3d5`
- **作者**: yamiyeh10
- **日期**: 2022-04-08 15:11:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/CustomCssLib/bpm-wangEditor.css`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomJsLib/wangEditor.min.js`

### 63. [Web]Q00-20220408004 修正設定發起主旨為必填時，當只有按ENTER來換行也能發起流程
- **Commit ID**: `f6c95d5efd6671f76b31c0e04a1ab187bc40b121`
- **作者**: 王鵬程
- **日期**: 2022-04-08 14:21:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 64. [內部]Q00-20220408001 優化5.8.2.1及5.8.3.1的update sql指令
- **Commit ID**: `591c1547997000267ef0fb14db44f81dd48ec260`
- **作者**: yanann_chen
- **日期**: 2022-04-08 13:39:45
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.2.1_DML_MSSQL_3.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.3.1_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/5.8.3.1_DML_Oracle_1.sql`

### 65. [內部]更新******* patch檔
- **Commit ID**: `dd7e1f3ee99ea1f26d81426346c544407b20fd72`
- **作者**: wayne
- **日期**: 2022-04-08 10:59:30
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/-59_InitDB.patch`

### 66. 更新越南語系
- **Commit ID**: `9522b4650dc4c82d483f49e53d7c40c121c3dbde`
- **作者**: wayne
- **日期**: 2022-04-08 10:35:29
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 67. [資安]Q00-20220331001修正漏洞：透過下載附件URL替換檔案名稱就能下載任意使用者的附件[補修正]
- **Commit ID**: `9a6b31d1a455b4a9973f38f306970777a9fef639`
- **作者**: 林致帆
- **日期**: 2022-04-07 17:05:41
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 68. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `9e74de3cda780abae9313e11f32f621d1472b49b`
- **作者**: yanann_chen
- **日期**: 2022-04-07 13:30:59
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 69. [內部]回收越南多語系
- **Commit ID**: `7126f6c4fa71c5d4f2aaae0555e444c76b1b03f0`
- **作者**: lorenchang
- **日期**: 2022-04-06 18:24:28
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/controller/CMManager_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/application/WSInvocationEditorController_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/process/ProcessDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 70. [Web]Q00-20220406002 調整派送表單關聯設定作業PK長度過長問題
- **Commit ID**: `513eff75a2c1e7aacb385617ad2f759ffd513eaf`
- **作者**: 林致帆
- **日期**: 2022-04-06 16:31:03
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 71. [登入]S00-*********** 增加使用者登出登入紀錄[補修正]
- **Commit ID**: `fd018de8f12d9e1b119f4d01a4a8683fac139773`
- **作者**: walter_wu
- **日期**: 2022-04-06 16:29:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 72. [內部]Merge *******_delivery_process分支多語系
- **Commit ID**: `17c6440c9aa06830b98b86aeb6d97500a7e5bbb6`
- **作者**: lorenchang
- **日期**: 2022-04-06 15:36:53
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 73. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `a9e9816ee40fdec3cd8a6547b89eeb5d97edc973`
- **作者**: yanann_chen
- **日期**: 2022-04-06 11:31:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`

### 74. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `5745d7a60cf6d9098f97a950a78bd657e195b1a3`
- **作者**: yanann_chen
- **日期**: 2022-04-06 11:23:58
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryBackProcessAbortFailed.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 75. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `8265e312a9484a3b14d99f237b4b06af5bc54b5c`
- **作者**: yanann_chen
- **日期**: 2022-03-31 16:24:13
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryInstanceManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/deliveryProcess/DeliveryProcessInstanceDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryBackProcessAbortFailed.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp`

### 76. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `cf8454029d1ab9812be917a122d399ca0cc9b1a8`
- **作者**: yanann_chen
- **日期**: 2022-03-25 15:42:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`

### 77. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `71278a8bc0055bd3c83b71c58cd22f41238bcc36`
- **作者**: yanann_chen
- **日期**: 2022-03-23 15:00:06
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`

### 78. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `99b54fdfecfb9dec481c9e361ced6f53d5b76255`
- **作者**: yanann_chen
- **日期**: 2022-03-22 17:14:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`

### 79. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `696973d37c8e723a5bcce0e848bc336c1268f804`
- **作者**: yanann_chen
- **日期**: 2022-03-22 14:17:04
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryInstanceManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/PerformWorkItemHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/ProcessAbortControllerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryInstanceManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortController.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/exception/BackProcessAbortFailException.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryBackProcessAbortFailed.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/DealDoneWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForTerminating.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessInstanceAbortFailed.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 80. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `9b881edbb2a87df24cc84c769dfa6cc36831c976`
- **作者**: yanann_chen
- **日期**: 2022-03-03 14:27:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`

### 81. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `999ff4c8eb9c870b154db3b14ccc5c55d5d9c94e`
- **作者**: yanann_chen
- **日期**: 2022-03-02 13:42:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`

### 82. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `d182791c7f8754026e395ddf52fb7593d9a01dfd`
- **作者**: yanann_chen
- **日期**: 2022-03-01 15:35:32
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryProcessHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessAbortControllerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 83. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `4b0448e7210ee5f1b29c89bdcf64ee0bc9420cb1`
- **作者**: 林致帆
- **日期**: 2022-02-23 18:00:27
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java`

### 84. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `7d02102cc5dbe1c4156c17d06547dcc56e7f364f`
- **作者**: 林致帆
- **日期**: 2022-02-23 14:17:35
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`

### 85. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `919449116e8baa53c6483b5beda9a43495a54e01`
- **作者**: 林致帆
- **日期**: 2022-02-21 10:48:40
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 86. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `0c257303a5a8ae987cf3ebbdc3673f407edecc84`
- **作者**: 林致帆
- **日期**: 2021-12-22 15:14:30
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`

### 87. [流程引擎]S00-*********** 新增派送流程關聯設定作業[補修正]
- **Commit ID**: `ee29b167b423852b04aee9d8e40780e164eb95e2`
- **作者**: 林致帆
- **日期**: 2021-12-09 19:12:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`

### 88. [流程引擎]S00-*********** 新增派送流程關聯設定作業
- **Commit ID**: `3db191217ff98b694683dbab1cdcee8421be5067`
- **作者**: yanann_chen
- **日期**: 2021-12-06 13:51:36
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryProcessHandlerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/RemoteObjectProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandler.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/DeliveryProcessHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Process.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ProcessMgr.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 89. [Web]S00-*********** 新增派送流程關聯設定作業 [補修正]
- **Commit ID**: `cf80b83ee0b9c8ccf0bcdf5464340ed4e1ebaa53`
- **作者**: 林致帆
- **日期**: 2021-11-25 17:48:24
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`

### 90. [Web]S00-*********** 新增派送流程關聯設定作業 [補修正]
- **Commit ID**: `ca4d2847c779f88010c35c295db4154149822cee`
- **作者**: 林致帆
- **日期**: 2021-11-25 17:11:57
- **變更檔案數量**: 10
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 91. [Web]S00-*********** 新增派送關聯配置作業
- **Commit ID**: `ed783358aff8853a082be90ce38b5adb70aefb15`
- **作者**: 林致帆
- **日期**: 2021-11-25 14:32:23
- **變更檔案數量**: 14
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/DeliveryManagerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/DeliveryProcessConfiguration.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManager.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerBean.java`
  - ➕ **新增**: `3.Implementation/subproject/service/src/com/dsc/nana/services/DeliveryManagerLocal.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/DeliveryConfiguration.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/DeliveryProcessConfigurationMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/MgrDelegateProvider.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormAccessor.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/DeliveryProcessConfiguration.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 92. Merge *******分支多語系
- **Commit ID**: `e90f6c307a4ad4f485b611ff94106ee275df8adb`
- **作者**: lorenchang
- **日期**: 2022-04-06 13:54:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 93. [BPM APP]S00-20211101001 新增移動端連續簽核系統參數化開關
- **Commit ID**: `7f13a0f3992123750b59dceddda9eabfd97c5646`
- **作者**: 郭哲榮
- **日期**: 2022-04-01 13:17:46
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 94. [WebService]S00-20220316003 新增白名單設定控管IP調用產品WebService服務
- **Commit ID**: `e83ff29fcc3cf4f7c66bc817318848372ac27539`
- **作者**: 林致帆
- **日期**: 2022-03-31 10:52:07
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/WebServiceFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 95. [在線閱覽]Q00-20220328003 修正如果文件主機與流程主機不同台在線閱覽畫面打開後會看不到檔案內容
- **Commit ID**: `2a1837783f8f7463f73178187ad322ea1eaa94f7`
- **作者**: walter_wu
- **日期**: 2022-03-28 18:32:04
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandlerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`

### 96. [登入]S00-*********** 增加使用者登出登入紀錄
- **Commit ID**: `3a7b7b513c414319ed1d9d043246f57ef7d584f6`
- **作者**: walter_wu
- **日期**: 2022-03-09 23:39:16
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SecurityHandlerDelegate.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/license/UserLogInOutRecord.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SecurityHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/System.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/SystemMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/SessionManager.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/web_agent/AdminAgent.java`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/UserLogInOutRecord.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 97. [在線閱覽]S00-20210617002 關卡 新增在線閱讀選項
- **Commit ID**: `e907bc5d9881b403f73db2e9082c6d328c6f4c36`
- **作者**: walter_wu
- **日期**: 2022-03-09 15:27:01
- **變更檔案數量**: 23
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTable.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/ActivityDefinitionMCERTableModel.java`
  - ➕ **新增**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/AllowChangeOnlineReadEditorRenderer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/activity/ActivityDefinitionMCERTableModel_zh_TW.properties`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/field_handler/database/AllowChangeOnlineReadType2IntFieldConversion.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/ActivityDefinition.java`
  - ➕ **新增**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/enumTypes/AllowChangeOnlineReadType.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessInstanceDTOFactoryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/instance/WorkItemForPerforming.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 98. [MPT]調整首頁模組相關上傳下載功能機制的上傳與下載接口有亂碼問題
- **Commit ID**: `3920f3810c3a242432132f045c48c8d60005b682`
- **作者**: pinchi_lin
- **日期**: 2022-03-02 10:35:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java`

### 99. [Web]S00-20220120003 表單上傳附件支持多選上傳[補]
- **Commit ID**: `37cb816f6ff17417c3bccb5b792cbed8b88bcd10`
- **作者**: waynechang
- **日期**: 2022-02-16 11:10:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`

### 100. [Web]S00-20220120003 表單上傳附件支持多選上傳
- **Commit ID**: `a40893d2ddd7243314a2b2244ab3158a1aa8fd52`
- **作者**: waynechang
- **日期**: 2022-02-15 17:44:24
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/MultiFormDocUploader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/web.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jqueryfileupload/jquery.fileupload.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jqueryfileupload/jquery.iframe-transport.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jqueryfileupload/vendor/jquery.ui.widget.js`

### 101. [在線閱覽]S00-20211112003 在線閱覽允許轉檔類型系統參數
- **Commit ID**: `0862754ba23f25f008db5913621c6d95b4528ef5`
- **作者**: walter_wu
- **日期**: 2022-02-08 17:23:31
- **變更檔案數量**: 7
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_Oracle_1.sql`

### 102. [Web]S00-20220103001 產品授權註冊頁面增加顯示購買的模組類型
- **Commit ID**: `6a9be0ce6a48214872c994e621d4a34efd625edc`
- **作者**: 王鵬程
- **日期**: 2022-01-26 11:26:40
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 103. [Web]S00-20210910001 移除攻略雲設定，轉檔異常處理作業只在出或移除，建patch時另外處理
- **Commit ID**: `3dd73c865ce35523080e42aa5569900a04fae83a`
- **作者**: walter_wu
- **日期**: 2022-01-25 16:46:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 104. [在線閱覽]S00-20211027002 在線閱覽開關
- **Commit ID**: `70060da42fc9355d8ed38e5ba95f76bc4a18ce24`
- **作者**: walter_wu
- **日期**: 2022-01-25 15:15:19
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/WmsAccessor.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@onlineRead/create/DML_InitOnlineReadDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@onlineRead/update/*******_onlineRead_DML_Oracle_1.sql`

### 105. [ESS]S00-20211208003新增ESS外網主機IP設定
- **Commit ID**: `c35b6c74413829937771d14ef5d6896fd7f5a991`
- **作者**: 林致帆
- **日期**: 2022-01-25 11:51:39
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/appform/helper/AppFormHelper.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 106. [MPT]調整首頁模組相關上傳下載功能機制新增的檔案作廢接口
- **Commit ID**: `f6db6e13d5d4d2857127d4e9f2c927b4f2c5310c`
- **作者**: pinchi_lin
- **日期**: 2022-01-21 15:55:55
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/doc_manager/LocalDocManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleMgr.java`

### 107. [流程引擎]Q00-*********** 調整Create Index指令，新增Index
- **Commit ID**: `2316e9b241211680a318a098901d1dcc1e63ae25`
- **作者**: yanann_chen
- **日期**: 2022-01-18 17:01:11
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/IndexNaNaDB_Oracle.sql`

### 108. [MPT]調整首頁模組相關上傳下載功能機制新增的檔案下載接口
- **Commit ID**: `ad50bf6ceb0b23495aa50e7504966e44540aa746`
- **作者**: pinchi_lin
- **日期**: 2021-12-24 15:59:43
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleMgr.java`

### 109. [流程引擎]S00-20210915002 新增BPM首頁預設撈取的時間區間是依系統變數設定
- **Commit ID**: `d0e9c15d773ddbb33e83a252f11c0ddba15abb5a`
- **作者**: cherryliao
- **日期**: 2021-12-15 18:06:35
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/Constants.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 110. [MPT]調整首頁模組相關上傳下載功能機制新增的檔案上傳接口
- **Commit ID**: `65a7cb2f7860f40a0426474119fa893529ebc146`
- **作者**: pinchi_lin
- **日期**: 2021-12-10 11:52:06
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/controller/Module.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/ModuleMgr.java`

### 111. [流程引擎]S00-20210621004 新增待辦事項記錄簽核的Client IP
- **Commit ID**: `639f833da29fbea8d8622870caebbe74ee6b07fc`
- **作者**: cherryliao
- **日期**: 2021-12-07 16:47:37
- **變更檔案數量**: 13
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/WorkItem.java`
  - 📝 **修改**: `3.Implementation/subproject/dto/src/com/dsc/nana/data_transfer/WorkItemForPerformDTO.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/PerformWorkItemHandlerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ReexecuteActivityAction.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 112. [流程引擎]S00-20210726001 新增流程草稿列表預設撈取的時間區間是依系統變數設定
- **Commit ID**: `c7826fea915ea1939a352f9a6535f499586a3566`
- **作者**: cherryliao
- **日期**: 2021-11-29 11:38:42
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/mvc/restful/manage/mobile/MobileDinWhale.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ManageDraftAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/Constants.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/BpmProcessProvider.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 113. [Web]S00-20210506012 新增核決關卡中核決條件之描述說明
- **Commit ID**: `bd0f982bac6e8c4bd6d7f109351894627ea4252f`
- **作者**: cherryliao
- **日期**: 2021-11-22 11:53:48
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/decision/ConditionCellEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/decision/DecisionConditionCellEditorRenderer.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/activity/decision/DecisionRuleListEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionDefinitionCellPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditor.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/com/dsc/bpm/user_interface/apps/process_designer/view/transition/ConditionExpressionEditorPanel.java`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_en_US.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_vi_VN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_zh_CN.properties`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/src/resource/view/transition/ConditionExpressionEditorPanel_zh_TW.properties`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_definition/DecisionCondition.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/definition/DecisionActivityDefForPreviewing.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_MSSQL.sql`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/db/@base/create/InitNaNaDB_Oracle.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DDL_Oracle_1.sql`

### 114. [流程引擎]S00-20210910003 退回重辦啟動代理人機制功能
- **Commit ID**: `53e76428c1e2f8c8ba1b3fa8b595a6be4d8bfe11`
- **作者**: walter_wu
- **日期**: 2021-10-25 18:47:29
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_MSSQL_1.sql`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/db/@base/update/*******_DML_Oracle_1.sql`

### 115. [資安]Q00-20220401002修正Spring漏洞反射型文件下载漏洞(CVE-2020-5421)
- **Commit ID**: `64a3571c6d4c5c0b28b37bfc823a6230c690b1d1`
- **作者**: 林致帆
- **日期**: 2022-04-01 17:29:20
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/Spring/spring-web-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Spring/spring-web-d.c.c0.RELEASE.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/metadata/jboss-deployment-structure.xml`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/lib/SpringFramework4/spring-web-4.3.7.RELEASE.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/SpringFramework4/spring-web-d.c.c0.RELEASE.jar`

### 116. [資安]Q00-20220331001修正漏洞：透過下載附件URL替換檔案名稱就能下載任意使用者的附件
- **Commit ID**: `e88ea52101b7ddd33846a26f75a24685bb01f693`
- **作者**: 林致帆
- **日期**: 2022-04-01 15:52:58
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 117. [組織設計工具]Q00-20220401001 修正組織設計師刪除職務核決層級定義；當該職務定義有被流程參考時，無法提示被引用的流程資訊
- **Commit ID**: `35fdbfbdb7747ebdde6ac29161772f9b86b998e0`
- **作者**: wayne
- **日期**: 2022-04-01 14:17:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`

### 118. [BPM APP]S00-20211101001 新增移動端連續簽核系統參數化開關
- **Commit ID**: `60f6920c2e506042def1e0c55267b33667568265`
- **作者**: 郭哲榮
- **日期**: 2022-03-31 20:26:01
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/SystemVariableCache.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/configuration/PropertyKey.java`
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/mobile/integration/SystemIntegrationConfig.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 119. [流程引擎]S00-*********** SQL註冊器內的設定可調用表單開窗增加查詢功[補]
- **Commit ID**: `7fd055991faefaeb66750027b3403ccc4742bb28`
- **作者**: 王鵬程
- **日期**: 2022-03-31 18:16:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`

### 120. [流程引擎]S00-*********** SQL註冊器內的設定可調用表單開窗增加查詢功能
- **Commit ID**: `d8769c9761312e09b46dd954423e39d9ca4bed8a`
- **作者**: 王鵬程
- **日期**: 2022-03-31 17:56:47
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/list_reader/PageListReaderDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/AllFormDefinitionListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacade.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/PageListReaderFacadeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/DataChooser.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/config.xml`

### 121. [BPM APP]C01-*********** 修正行動端HandWriting元件在script設置disable時無法正常使用問題
- **Commit ID**: `ba33bbd9bf2368b36c10f57f9870b79bd31cc3ff`
- **作者**: yamiyeh10
- **日期**: 2022-03-31 16:44:03
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/system/BpmAppHandWriting.js`

### 122. [資安]Q00-20220329003 修正jQuery版本過舊漏洞
- **Commit ID**: `e624b1dff5ac9402c5be3e333f6b53b695c60966`
- **作者**: 林致帆
- **日期**: 2022-03-31 16:41:01
- **變更檔案數量**: 351
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamProcessRecord.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/BamSetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/IWCIndicatorDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MaintainCuzDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/MultiLanguageSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CannotAccessWarnning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalFocusProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalOperationDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalPriority.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CriticalModule/ModuleForm/CriticalProcessDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/MaintainTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomModule/ModuleForm/QueryTemplateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapConnection.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapEditMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/SapMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/CustomOpenWin/ViewSapFormField.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxCommonTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxDBTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxExtOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxFormTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxOrgTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxProcessTest.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Ajax/AjaxService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/AttachmentExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/ButtonExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/CheckboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DateExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DialogExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/DropdownExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormOnMobileRWDExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/FormScriptExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/GridExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TextboxExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Form/TimeExample.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/Index.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Document/RESTful/RESTfulIndex.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ErrorPage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOClauseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ISOModule/ModuleForm/ISOReleaseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/ChildGridChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/CustomDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/JsonDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/MultipleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/SingleDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/OpenWin/TreeViewDataChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/PerformWorkFromMail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/VerifyPasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/AbortProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AbortProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AdministratorFunction/AdministratorFunction.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/AppFormManagement.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/EMSI01.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/AppFormModule/EMSProgram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/BusinessProcessMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/BusinessProcessMonitor/WrapProcessMonitorInfo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSet.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ColumnMask/ManageColumnMaskSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/CreateProcessDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/CreateProcessDocument/ProcessDocumentCreateResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/CompleteActivityRollingback.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/DealDoneWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DealDoneWorkItem/SetWorkItemCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/DesignerDownload/DesignerDownloadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/FavoritiesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/MenuFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FavoritiesMaintain/ProcessFavoritiesMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/AbsoluteFormBluePrint.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppAbsoluteDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormAppRWDDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormExplorer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/FormSqlClause.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRelativeDesigner.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/MobileRwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormScriptEditor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/InstallCertificate/InstallCertificate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CompleteUploadRsrcBundle.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/CreateSysLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/FormLanguageMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcBundleMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/LanguageMaintain/SysRsrcExcelMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/License/InstallPasswordRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ManageCustomReportMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCustomReport/ReportUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageCuzPattern/ManageCuzPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocCategory/ManageDocCategoryMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/AccessRightChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/BatchUploadMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/CreateDocument.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocClauseChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocLevelChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocServerChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/DocumentChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentForQuery.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/ManageDocumentMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/PDFUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SingleDocCategoryChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDocument/SnGenRuleChooser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageDraft/ManageDraftMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/CreateModuleDefinition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageModuleDefinitionMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/ManageProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/PersonalizeConfig.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetMultiLanguage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageModule/SetProgramAccessRight.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ManagePhraseMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManagePhrase/ViewPhrase2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOChangeFileList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISODocumentsList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOEffectInvalidList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOFileQueryList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageReport/ISOReleaseDocList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSysIntegration/SysIntegrationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/CompleteThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/LogoImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ManageSystemConfigMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageSystemConfig/ThemeMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserCurrentType/UserManageResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePasswordMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangePreferUser.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ChangeRelationship.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ManageUserProfileMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/SetupProcessSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageUserProfile/ShowSignImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/CompleteWfNotificationDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ManageWfNotification/ManageWfNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmApp.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContact.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListContactV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListNoticeV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListResigendV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListToDoV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTrace.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvoked.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTraceInvokedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformed.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListTracePerformedV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppListWorkMenuV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppNotice.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppSetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppToDo.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmAppWorkMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmPorcessTracing.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmTaskManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/BpmWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleUserCompleteImport.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/IntelligentLearningManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileBpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormInvokeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormNoticeLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormResigendLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTraceInvokedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTracePerformedLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFromReturnRecordView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileIntegrate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageDinWhale.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManagePlatform.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageUserMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileManageWeChat.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileOAuthRecive.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobilePhoneCall.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileRedirectModule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribe.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeForAdmin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileSubscribeResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTraceInvokedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileTracePerformedLib.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/NotRegisterApp.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/OnlineReadFileUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/PDFConvertFailList.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineRead/WatermarkPattern.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/OnlineUser/OnlineUserView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AdjustActivityOrder.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AppFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AttachmentHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseDispatchOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseInvokeOrgUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseMutilPrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChooseOrganizationUnit.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ChoosePrefechAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchProcessTerminating.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteBatchWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteReferProcessInvoking.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkItemSending.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/CompleteWorkRegetting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExcelImporter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ExpenseAccountItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormDocUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/FormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ForwardNotificationMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/InvokeReferProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/OnlySignImageUploader.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PersonalizeHome.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReassignWorkItemMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ReexecuteActivityMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/RwdFormPriniter.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/SetActivityContent.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraceReferProcess.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/TraditionInvokeProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/ViewReassignHistory.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WebHandler.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmPreviewAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/BpmSubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewAutoAgentActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewDecisionActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/PreviewParticipantActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/ProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PreviewProcess/SubProcessPreviewResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/InvokeProcessDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/PerformWorkItemDiagram.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ProcessPerformanceMonitor/ProcessPerformanceMonitor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/CompleteRedoInvoke.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/RedoInvoke/RedoInvokeMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportMaintain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ReportModule/ReportTemplate.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesAnalyzeProcessDef.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesModifyOrgData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesSearchOperation.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/CompleteFormDataSearching.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/ExportFormToDatabase.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetFormConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SearchFormData/SetProcessConditions.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainAnalyzeService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainCombinationService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/MaintainExternalService.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ServiceRegister/ServiceRegister.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Sysintegration/SysintegrationSetMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/AddSystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/SystemSchedule/SystemSchedule.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/NavigatorMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/OtherMenu.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AppFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/AssignNewAcceptor.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceSubTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceAllProcessImageSub.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmTraceDecisionActivityInst.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ChooseDefaultSubstitute.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteLeftEmployeeWorkReassigning.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessAborting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/CompleteProcessDeleting.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ReassignLeftEmployeeWorkMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormDefinitionViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/RwdFormViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SetProcessCondition.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/SubProcessTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceBpmnActivity.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessUserFocusMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TracePrsLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllClosedWorkItems.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/WebViewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/EnumerateWorkAssignee.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ValidateProcess/ValidateProcessMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/FormPreviewer.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/app/RwdFormPreviewer.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v1/plugin/jquery-a.h.c.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/plugin/jquery-a.h.c.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/MobileExtruded/plugin/jquery-a.h.c.min.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/NewTiptop/NewTiptop.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/PDFWebView/web/viewer.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/appform/jquery-a.d.b.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/jGrid/gridbox.html`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jGrid/jqgrid43/js/jquery-a.h.c.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jQueryMobile/jquery-a.h.c.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-a.d.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-a.g.a.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-a.g.a.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-a.h.c.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-a.j.a.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-a.k.a.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-a.k.c.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-c.b.a.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-ui-a.h.i.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jquery-ui-a.j.c.custom.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/jqueryUI/jquery-ui-a.k.d.min.js`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/zTreeJs/jquery-a.d.d.min.js`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@mcloud/openWin-Source/CustomOpenWin/MCloudMaintain.jsp`

### 123. [內部]配合修正Dom4J XXE Injection漏洞進行相關調整
- **Commit ID**: `fedbaa6f4f9ec078a43716b2ea34e98109e9604c`
- **作者**: lorenchang
- **日期**: 2022-03-31 16:06:17
- **變更檔案數量**: 43
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/build.xml`
  - 📄 **重新命名**: `3.Implementation/subproject/bpm-designer/lib/Dom4J/dom4j-2.1.1.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/bpm-tool-entry/lib/Dom4J/dom4j-1.6.1-changed_serialization.jar`
  - 📄 **重新命名**: `3.Implementation/subproject/domain/lib/Dom4J/dom4j-2.1.1.jar`
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/.classpath`
  - 📄 **重新命名**: `3.Implementation/subproject/business-delegate/lib/Dom4J/dom4j-2.1.1.jar`
  - 📝 **修改**: `3.Implementation/subproject/crm-configure/.classpath`
  - 📄 **重新命名**: `3.Implementation/subproject/crm-configure/lib/Dom4J/dom4j-2.1.1.jar`
  - 📝 **修改**: `3.Implementation/subproject/domain/.classpath`
  - ➕ **新增**: `3.Implementation/subproject/domain/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/efgp-pdfViewer/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/efgp-pdfViewer/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/efgp-pdfViewer/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/form-builder/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/form-builder/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/form-designer/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/form-designer/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/form-designer/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/form-importer/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/form-importer/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/form-importer/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/org-importer/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/org-importer/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/org-importer/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/.classpath`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/build.xml`
  - ❌ **刪除**: `3.Implementation/subproject/process-designer/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/process-designer/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/service/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/service/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/sys-configure/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/sys-configure/lib/Dom4J/dom4j-2.1.3.jar`
  - 📝 **修改**: `3.Implementation/subproject/system/.classpath`
  - ❌ **刪除**: `3.Implementation/subproject/system/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/system/lib/Dom4J/dom4j-2.1.3.jar`
  - ❌ **刪除**: `3.Implementation/subproject/webapp/lib/Dom4J/dom4j-2.1.1.jar`
  - ➕ **新增**: `3.Implementation/subproject/webapp/lib/Dom4J/dom4j-2.1.3.jar`

### 124. [資安]修正Dom4J XXE Injection漏洞
- **Commit ID**: `baa5e04da46e84b4bb510d5ea9431fc74362eef5`
- **作者**: lorenchang
- **日期**: 2022-03-31 16:05:21
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/modules/system/layers/base/org/dom4j/main/dom4j-2.1.3.jar`
  - ➕ **新增**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/modules/system/layers/base/org/dom4j/main/module.xml`

### 125. [Web]Q00-20220330002 修正漏洞：一般使用者可登入後在瀏覽器開發者工具輸入goToURL語法進入產品授權註冊青單
- **Commit ID**: `168e202cf131d89070626a70710a3b968b294c27`
- **作者**: 林致帆
- **日期**: 2022-03-31 14:53:21
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`

### 126. [Web]A00-20220330001 修正表單按鈕開窗使用SQL註冊器搭配資料選取來使用，開窗有設定多語系，但實際操作沒有呈現多語系的內容
- **Commit ID**: `80fc2b7fca3b3d73cf0f1401799f00dc8d7228c4`
- **作者**: wayne
- **日期**: 2022-03-31 13:51:04
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/TriggerElement.java`

### 127. [Web]Q00-20220330001修正透過修改參數hdnMethod:loginByQRCode, 不需要密碼便可直接登入BPM
- **Commit ID**: `253f4f1cf3f039249119cd6ee2bf4c1e2a65419f`
- **作者**: 林致帆
- **日期**: 2022-03-30 18:25:23
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/QRCodeLoginCache.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`

### 128. [Web]S00-20220329001 登入機制優化，增加記錄密碼功能，Email連結自動跳轉[補]
- **Commit ID**: `72385104aa502e1b164413850c71139927837203`
- **作者**: wayne
- **日期**: 2022-03-30 14:33:23
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 129. [Web]Q00-20220329002 調整退回重辦流程清單以結案時間為主排序發起時間為副排序
- **Commit ID**: `3dc1d1a3aa6f67d2c44cee3783b8c1ad8da965f5`
- **作者**: 林致帆
- **日期**: 2022-03-29 16:48:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReexecutableActInstListReader.java`

### 130. [Web]S00-20220329001 登入機制優化，增加記錄密碼功能，Email連結自動跳轉
- **Commit ID**: `0c8fa79b0705ca2b2597f09661f12b6384500d58`
- **作者**: wayne
- **日期**: 2022-03-29 15:24:03
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/ExtraLogin.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 131. [流程引擎]Q00-20220329001 修正模擬使用者進到BPM首頁再從下方進入待辦並簽核後，按回到清單頁會卡住
- **Commit ID**: `758a73fda3f0e4110a3ee613760edb2fca302a48`
- **作者**: 王鵬程
- **日期**: 2022-03-29 14:42:41
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ProcessTraceResult.jsp`

### 132. [流程引擎]提高58版Queue併發承載量
- **Commit ID**: `c70bf37cabb405ad32a1e52fe27d91b8d3ae1c19`
- **作者**: lorenchang
- **日期**: 2022-03-28 16:25:37
- **變更檔案數量**: 20
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocConvertWithFileHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/MessageHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/QueueHandlerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutoAgentPerformerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/AutomaticDeliveryBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/BatchNoticeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/DeleteClosedProcessInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/E10SendSignInfoBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/EventDispatcherBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/FinsihProInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/FormInstanceTransformerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/McloudPushInvokeBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/MobileMailerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/NoCmDocumentsBackgroundServiceBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/StartNextActInstBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/event/TiptopCleanDocumentBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/util/QueueHelper.java`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full.xml`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@wildfly/wildfly-15.0.0.Final/standalone/configuration/standalone-full_Oracle.xml`

### 133. [內部]更新bpm-tool-entry設定，避免新版Eclipse編譯異常
- **Commit ID**: `5e366b431316899b3a009e46d4209d35d4dfbe6b`
- **作者**: lorenchang
- **日期**: 2022-03-28 14:08:51
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - ➕ **新增**: `3.Implementation/subproject/bpm-tool-entry/.settings/org.eclipse.jdt.core.prefs`

### 134. [Web]Q00-20220328001 修正從首頁的待辦清單點選第二頁以後的任一流程，點擊繼續派送會報錯
- **Commit ID**: `18edfe630d1872eda04d6fc7b0216a1720295a89`
- **作者**: 林致帆
- **日期**: 2022-03-28 10:00:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 135. [系統管理工具]Q00-20220325003 修正使用者密碼帶有^符號，從一個tool中再開啟其他tool會出現錯誤
- **Commit ID**: `3ec826ea9ac32e6208ba1d7cd30f08b2fc738a8e`
- **作者**: 王鵬程
- **日期**: 2022-03-25 17:45:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/src/com/dsc/bpm/designer_main/util/ChooseDesigner.java`

### 136. [Web]A00-20220325001 修正URL開啟追蹤TraceProcessForSearchForm畫面上的列印按鈕title無法正常顯示多語系
- **Commit ID**: `e4e71609cf6419363e75b08bb9720bcb4a9acdae`
- **作者**: wayne
- **日期**: 2022-03-25 16:56:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewAllFormData.jsp`

### 137. [流程引擎]Q00-20220325002 修正一般使用者-追蹤流程-已轉派的工作-匯出excel時，當資料庫為Oracle且匯出流程數量大於300時會報錯
- **Commit ID**: `3d89780794465578cad8d7b86c8cae6f694ce62a`
- **作者**: wayne
- **日期**: 2022-03-25 16:30:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 138. [Web]Q00-20220325001 修正表單單身欄位對應錯誤
- **Commit ID**: `adc7d0aafec9834983565b5b23d7947da35c9533`
- **作者**: 林致帆
- **日期**: 2022-03-25 14:21:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 139. [流程引擎]A00-20220323001 修正流程核決層級關卡之後的關卡若有設定自動簽核，流程無法往下派送的問題
- **Commit ID**: `44f2b07d980e74e7b4e4b205400319cda37d6f96`
- **作者**: yanann_chen
- **日期**: 2022-03-24 18:28:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`

### 140. [Web]Q00-20220324003 修正網頁有縮小或是切換頁簽後切回來操作一段時間被登出
- **Commit ID**: `1ac4a09e68caf8f75e784df433fc883152625278`
- **作者**: walter_wu
- **日期**: 2022-03-24 17:25:19
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Templates/Template.jsp`

### 141. [表單設計師]Q00-20220324002 修正使用IE開啟有時間元件的RWD表單會出現錯誤
- **Commit ID**: `aa0827aec7bd99ebeeae9cc242e595dd79f749a9`
- **作者**: 王鵬程
- **日期**: 2022-03-24 17:04:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 142. [表單設計師]Q00-*********** 調整表單設計器在發行表單的視窗中的截止有效日欄位改為非必填
- **Commit ID**: `c014452b8335599b30e8f04da0cb5e403ede27e9`
- **作者**: 王鵬程
- **日期**: 2022-03-24 15:59:40
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/FormDefinitionManagerDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/FormDesignerAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/explorerActions.js`

### 143. [WebService]Q00-*********** 移除MOfficeIntegrationEFGP WebService服務
- **Commit ID**: `ca5aa04ba018f9d727226c9f73175654ad6399e7`
- **作者**: 林致帆
- **日期**: 2022-03-23 17:01:14
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/webservice/MOfficeIntegrationEFGP.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/server-config.wsdd`

### 144. [流程引擎]A00-*********** 修正SQL註冊器中語法有使用到order by 會導致報錯
- **Commit ID**: `415c1f91fd8b02dd2f02706c0964ff2b5eb60b26`
- **作者**: 王鵬程
- **日期**: 2022-03-23 16:59:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/DatabaseAccessor.java`

### 145. [Web]A00-20220322001 修正流程負責人透過監控流程匯出excel時，當流程狀態為已完成、已撤銷、已中止時，「目前處理者」欄位仍有資料
- **Commit ID**: `5924ad66a2ead87fe998ff68d8c03d455f23c5cf`
- **作者**: wayne
- **日期**: 2022-03-23 16:46:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`

### 146. [WebService]Q00-20220323002	修正BPM的WebService的SQLInjection漏洞
- **Commit ID**: `a7ee539e4e8e3c707b1d5cb76bc7d14ada14f2f3`
- **作者**: 林致帆
- **日期**: 2022-03-23 16:30:29
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/OrganizationManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/webservice/ProcessInstanceService.java`

### 147. [Web]Q00-*********** 修正行動版面下，從追蹤進入絕對表單的流程，沒有橫向scrollbar而無法看到完整表單
- **Commit ID**: `fd5eaddd778fa28578ad717c4a02078343166952`
- **作者**: 王鵬程
- **日期**: 2022-03-22 17:00:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkItem.jsp`

### 148. [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
- **Commit ID**: `19587aa0960eec9e78ed12d01750b0b4b178ea66`
- **作者**: 林致帆
- **日期**: 2022-03-22 15:15:25
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 149. [ESS]Q00-20220321003修正ESS流程經過取回or退回重辦在服務任務關卡前，撈取表單實例序號錯誤導致繼續派送報錯
- **Commit ID**: `e1c5820e31be4f7a7ae0db1816d2e6c59dbdfb15`
- **作者**: 林致帆
- **日期**: 2022-03-21 16:51:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 150. [Web]Q00-20220321002 修正一般使用者-追蹤流程-已轉派的工作，使用「表單序號」當條件查詢無效
- **Commit ID**: `ed73d34b94b970ac771b54a729312734c133c3f8`
- **作者**: wayne
- **日期**: 2022-03-21 16:38:17
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 151. [Web]A00-20220317001 修正透過URL(TraceProcessForSearchForm)開啟追蹤流程頁面，且系統變數設置「追蹤頁面簽核歷程為top」時，簽核歷程無法展開顯示內容
- **Commit ID**: `d46f7a446c1a47a97d96222c57886ef47095e9ac`
- **作者**: wayne
- **日期**: 2022-03-21 16:28:59
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/FormViewerJs.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/TraceProcessForSingleSearchForm.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/ViewWorkStep.jsp`

### 152. [系統管理工具]S00-20220223003 新增資料來源設定，資料庫類型新增AZURE用的MSSQL
- **Commit ID**: `0a8f9f852d174e1a0ef282f9c56cc772929f1abe`
- **作者**: 林致帆
- **日期**: 2022-03-21 14:54:09
- **變更檔案數量**: 5
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DataBaseType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ColumnsFinder.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/DatabasesFinder.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/TablesFinder.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/resources/DatabaseDriver.properties`

### 153. [Web]S00-20211013001 調整流程圖中能完整顯示出簽核意見內容[補]
- **Commit ID**: `d25cbb7c60cb0e4163e74d0b62d5b6fc5a80f2e9`
- **作者**: 王鵬程
- **日期**: 2022-03-21 11:42:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`

### 154. [Web]Q00-20220321001 修正絕對位置表單在第一關以外的關卡上傳附件按上傳後，開窗變空白
- **Commit ID**: `385d1c396c3ff231708d4d623af65a9650ec7558`
- **作者**: 林致帆
- **日期**: 2022-03-21 10:51:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 155. [Web]Q00-20220317004 修正在IE開啟加簽頁面，確認按鈕無法點選
- **Commit ID**: `0e43541a5cc9e020563cb83f70a9336374a479d5`
- **作者**: 王鵬程
- **日期**: 2022-03-18 15:26:09
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/AddCustomActivityMain.jsp`

### 156. [系統管理工具]新增資料來源設定，資料庫類型新增PostgreSQL
- **Commit ID**: `61689339591a95c30b3eb3408f04d7b78c030443`
- **作者**: 林致帆
- **日期**: 2022-03-18 11:56:49
- **變更檔案數量**: 4
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/DataBaseType.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/ColumnsFinder.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/DatabasesFinder.java`
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/jdbc/TablesFinder.java`

### 157. [流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤[補]
- **Commit ID**: `32095e8b3663303cd1ffac51a2d9c42dd6fa505b`
- **作者**: wayne
- **日期**: 2022-03-17 16:16:39
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`

### 158. [內部]Q00-20220317002 增加服務接口「允許非流程處裡者，可以開啟追蹤流程表單畫面」
- **Commit ID**: `408d6bd3368d58555b1d2f6fbf497048299109e2`
- **作者**: wayne
- **日期**: 2022-03-17 14:47:55
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/StringUtil.java`

### 159. [WorkFlowERP]S00-20220314001 新增WorkFlowERP表單
- **Commit ID**: `eddc65ae5660cc9692df2cce1fda2c8b9fe23bba`
- **作者**: 林致帆
- **日期**: 2022-03-16 16:35:31
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/conf/Process_Mapping.prsmapping`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip`

### 160. Revert "[WorkFlowERP]S00-20220314001 新增WorkFlowERP表單 PURI16,MOCI12"
- **Commit ID**: `86082a138eb0409b9594d3c8817913746cd5f845`
- **作者**: 林致帆
- **日期**: 2022-03-16 16:27:39
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/conf/Process_Mapping.prsmapping`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip`

### 161. [表單設計師]Q00-20220316001 修正表單設計師DialogInputLabel有設提示文字，版到5872會導致提示文字出現在預設值
- **Commit ID**: `55ca134d8d20ad0d67206bada2118fb81028414e`
- **作者**: 王鵬程
- **日期**: 2022-03-16 14:34:18
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 162. [Web]S00-20220316002 修正漏洞downloadDocument的下載URL可任意下載其他路徑檔案
- **Commit ID**: `c729deb737026d4015bbfc6f80d09a0e458ae1f7`
- **作者**: 林致帆
- **日期**: 2022-03-16 14:29:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 163. [BPM APP]C01-20220316003 修正行動端DialogInput元件有設定提示文字時會顯示_的問題
- **Commit ID**: `1c7169465d7f646890c62b75fb98150dc1449786`
- **作者**: yamiyeh10
- **日期**: 2022-03-16 13:37:57
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElementMobile.java`

### 164. [Web]S00-20220316001 新增整合暫存管理供未註冊的BPM使用
- **Commit ID**: `23d764e80485b1a163ae9da5e8e066820a852676`
- **作者**: 林致帆
- **日期**: 2022-03-16 11:19:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/program/ProgramDefManagerBean.java`

### 165. [WEB]Q00-20220315004 修正離職作業維護-轉派離職人員的工作-勾選「全部轉派第一優先代理人」時，未將預設代理人帶回「接收者」欄位中
- **Commit ID**: `1e902b38c5660a63b00667dc9ee8994565fca510`
- **作者**: wayne
- **日期**: 2022-03-15 17:34:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/ResignedEmployeesMaintain/ResignedEmployeesMaintainMain.jsp`

### 166. [表單設計師]Q00-20220315003 修正表單設計師DialogInput有設提示文字，版更到5872會導致提示文字出現在預設值
- **Commit ID**: `91409ce69ff060e7d9ba7ab068ba492dfebce8f7`
- **作者**: 王鵬程
- **日期**: 2022-03-15 16:19:59
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`

### 167. [組織同步]A00-20220314001 修正組織同步完會蓋掉使用者設定的 使用者是否顯示待辦事項小視窗
- **Commit ID**: `25cf223af9b2344d6e537905b8d59db90d625102`
- **作者**: walter_wu
- **日期**: 2022-03-15 14:52:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 168. [WEB]A00-20220216001 修正追蹤流程-已轉派的工作，點進表單後再返回清單頁沒有保留原本的查詢條件
- **Commit ID**: `c69a1e13a75274a4556c61e0f277662bec2901c7`
- **作者**: wayne
- **日期**: 2022-03-15 14:10:49
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/WorkItemDetail.jsp`

### 169. [WEB]Q00-20220315002 修正舊版表單InputElement若沒有textValue屬性時，儲存表單會發生錯誤
- **Commit ID**: `ab494c7edb04256b216352979764138fa4e1a030`
- **作者**: wayne
- **日期**: 2022-03-15 14:01:56
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/TextElementDefinition.java`

### 170. [Web]Q00-20220223001表單無法派送，把元件applUserId刪除重拉就可以正常派送
- **Commit ID**: `d64e32a5331906530a78e51f22bf342205781d84`
- **作者**: ocean_yeh
- **日期**: 2022-03-15 13:39:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DynamicScriptBuilder.java`

### 171. [BPM APP]C01-20220308005 修正Grid綁定的元件使用FormUtil.show方法時，在新增資料維護介面欄位分割顯示錯誤
- **Commit ID**: `09259a6676c558d458f0f9c4ba4c958c504c803e`
- **作者**: 郭哲榮
- **日期**: 2022-03-14 18:56:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 172. [BPM APP]C01-20220311003 修正行動端FormUtil的getValue方法異常問題
- **Commit ID**: `cd764fa0b6c93ba0c558186ce4ec71f5c85685bd`
- **作者**: yamiyeh10
- **日期**: 2022-03-14 18:00:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/form/FormUtil.js`

### 173. [流程引擎]Q00-20220314003 修正流程關卡下一關卡為多人關卡處理且設定自動簽核為"與前一關相同簽核者，則跳過"，繼續派送會失敗
- **Commit ID**: `d632eaabaa72136a5e8cbe81a85fbbf6c99a1a8c`
- **作者**: 林致帆
- **日期**: 2022-03-14 17:46:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 174. [WorkFlowERP]S00-20220314001 新增WorkFlowERP表單 PURI16,MOCI12
- **Commit ID**: `35e22d3b89e3312b96c51c99afc5e9bb6a7f38a1`
- **作者**: 林致帆
- **日期**: 2022-03-14 17:32:56
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/conf/Process_Mapping.prsmapping`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@workflow/form-default/form-default-workflow.zip`

### 175. [BPM APP]C01-20220311002 修正用平板登IMG時session會清空導致發單簽核有空指針異常問題
- **Commit ID**: `122c073c6f8bb8cf9c1dda10f138707e45df5ae0`
- **作者**: pinchi_lin
- **日期**: 2022-03-11 18:02:08
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AuthenticationHandler.java`

### 176. [流程引擎]Q00-*********** 調整BPM系統有開啟E10、TIPTOP、T100整合時，進入待辦開啟表單時，若單據非整合單據時，serverlog會有找不到整合流程資訊的錯誤
- **Commit ID**: `bbec7f6d0cb84e4e45f6095a0e4c11d119056bb9`
- **作者**: wayne
- **日期**: 2022-03-11 16:49:37
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/SysGateWayDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWay.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/SysGateWayMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`

### 177. [BPM APP]C01-*********** 移動授權中間層的使用者維護作業增加分頁功能[補]
- **Commit ID**: `1e5ceb8593af1fc8bd571a5fd5ee57148f83d2c0`
- **作者**: cherryliao
- **日期**: 2022-03-11 14:42:53
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterConfigManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/WechatManagePage.css`

### 178. [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
- **Commit ID**: `9710be65ebbeb2d34a46ea399b6e2764cb5037a4`
- **作者**: 林致帆
- **日期**: 2022-03-10 15:45:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 179. [BPM APP]C01-20220309001 修正Web端行動模擬發單功能於表單載入時不會呼叫到formCreate方法問題
- **Commit ID**: `150347685553ecd3e6145367e3651dbf66277772`
- **作者**: yamiyeh10
- **日期**: 2022-03-10 10:28:12
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/PerformWorkItem/PerformRequesterActivity.jsp`

### 180. [ISO]Q00-20220309001 修正ISO變更單，在ModDocRequester關卡載入附件時，若表單有選擇元件(RadioBox,ComboBox,CheckBox,ListBox)並將元件設定為invisible時，無法載入附件
- **Commit ID**: `9ac4abb021a939fd96fabb0ddd875fe0cbd93e47`
- **作者**: wayne
- **日期**: 2022-03-09 14:50:42
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/IsoModuleAccessor.java`

### 181. [Web]S00-20211013001 調整流程圖中能完整顯示出簽核意見內容
- **Commit ID**: `8fe55178247bd5e4becfd63868ccb285ef414163`
- **作者**: 王鵬程
- **日期**: 2022-03-09 11:48:44
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/BpmProcessInstanceTraceResult.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/css/bpm-style.css`

### 182. [流程引擎]A00-20220308001 修正一般使用者進入監控流程並點選『全部』，再到其他頁面後再回到監控會變一片空白
- **Commit ID**: `de69d831ac9a467b0a0a9b9e8558ddd162783228`
- **作者**: 王鵬程
- **日期**: 2022-03-08 15:44:47
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 183. [BPM APP]C01-20220224004 修正在取得IMG動態生成表單應用資料與取得綁訂使用者資料時會偶發statement close問題
- **Commit ID**: `c073465e728e60fcffb16856dab923d71a3dd0cd`
- **作者**: yamiyeh10
- **日期**: 2022-03-08 10:08:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/MobileMgr.java`

### 184. [Web]Q00-20220307001 修正子單身過多造成表單開啟時間變長
- **Commit ID**: `49d8c5a4f94800b98b6bf648b7ec13a29e43bf84`
- **作者**: 林致帆
- **日期**: 2022-03-07 18:24:49
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/SubGridTransfer.js`

### 185. [登入]Q00-20220304002 調整登入加密機制，避免後端session失效時取不到值登入失敗
- **Commit ID**: `f3235f7560cc609261ea2af61595cc4f12f449c5`
- **作者**: walter_wu
- **日期**: 2022-03-04 18:24:11
- **變更檔案數量**: 6
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/ValidateProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/ActionFilter.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/util/AesUtil.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/struts-common-config.xml`

### 186. [Web]Q00-20220304001 修正查詢樣板使用6個$當作『請選擇』的內存值，在渲染後會被換成12個$
- **Commit ID**: `123dfb344ec8b57a5ce1078ec1df6abfebcceeac`
- **作者**: 王鵬程
- **日期**: 2022-03-04 17:59:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 187. [流程引擎]Q00-20220215001 修正偶發附件遺失問題[補修正]
- **Commit ID**: `175bbe8619e58af38d081c5cc5680caf43dfc7c9`
- **作者**: walter_wu
- **日期**: 2022-03-03 16:11:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`

### 188. [BPM App]C01-20220301003 修正E10表單在移動端操作日期元件時會無法帶入值與必填訊息顯示報錯的問題
- **Commit ID**: `b8044bd283d43a0d014367d12f776044144ed5f9`
- **作者**: 郭哲榮
- **日期**: 2022-03-02 18:08:26
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/E10Form.js`

### 189. [組織同步]A00-20220224002 修正組織同步完會蓋掉使用者設定的 簽核完畢後的行為
- **Commit ID**: `9257448a5d5bc602e6a98f9aa16cb0a5b98dcf41`
- **作者**: walter_wu
- **日期**: 2022-03-02 17:41:15
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/organization/ImportOrganizationBean.java`

### 190. [Web]Q00-20220107002 修正一般使用者匯出Excel速度過慢[補修正]
- **Commit ID**: `ba91a9a3bd76730b7ccb43bf95a591500adac8ee`
- **作者**: 林致帆
- **日期**: 2022-03-02 09:25:36
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/DirectTraceableProcessListReader.java`

### 191. [BPM APP]C01-20220301002 修正在沒有整合行動方案情況下開啟Web表單設計師任一表單時會發生錯誤的問題
- **Commit ID**: `64ed6727c962b7dc2ec2767c353ba44116b49bf4`
- **作者**: yamiyeh10
- **日期**: 2022-03-01 18:45:31
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/SystemConfigManagerBean.java`

### 192. [流程引擎]A00-20220224001 修正條件式中各條件先做or後再做and，會無法派送到該條件後的關卡
- **Commit ID**: `633b5d3219feb3e4a664862f42511bffbfa0fccf`
- **作者**: 王鵬程
- **日期**: 2022-03-01 17:16:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/util/ConditionEvaluator.java`

### 193. [Web]Q00-20220301001 修正SQLCommand沒有設定SQL卻佔用連線的問題
- **Commit ID**: `5f6769c5c93831ea5ebaa082b5139d109b740114`
- **作者**: walter_wu
- **日期**: 2022-03-01 16:23:22
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/InputElement.java`

### 194. Merge branch 'develop_v58' of http://10.40.41.229/BPM_Group/BPM.git into develop_v58
- **Commit ID**: `e05217e4cb0e9ec4b736f910f63b1fcb6a1a6271`
- **作者**: 林致帆
- **日期**: 2022-03-01 15:16:51
- **變更檔案數量**: 0

### 195. [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
- **Commit ID**: `7f9a6a31bd47e63edda45347a583fd03814d3e6f`
- **作者**: 林致帆
- **日期**: 2022-03-01 15:16:38
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 196. [BPM APP]S00-20211027004新增移動端支援ERP流程終止或撤銷時，若單據修改重新送審後於移動表單可查看之前審批流程的功能[補]
- **Commit ID**: `2e67353e02cd7b98cc1fffe91f3e2d8f71319436`
- **作者**: cherryliao
- **日期**: 2022-03-01 11:16:03
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileDinWhaleAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/DinWhaleMobileFormTodoLibV2.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFromReturnRecordView.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/DinWhaleMobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileFormCommon.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileReturnRecordView.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`

### 197. [流程引擎]Q00-20220208004 修正「已轉派的工作」清單，在「全部」頁籤取得的資料筆數與「處理中」、「已處理」兩個頁籤相加的數量不符
- **Commit ID**: `aec4f0ff23eb1634482baa2dedd7419e0b4e6ed1`
- **作者**: yanann_chen
- **日期**: 2022-02-25 16:45:50
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ReassignedWorkItemListReader.java`

### 198. [Web]Q00-20220224001 修正維護樣板作業從PC版切換到行動版時資料無法顯示
- **Commit ID**: `e74ea12207c1115678a815ee3fcabea92138baa8`
- **作者**: 王鵬程
- **日期**: 2022-02-24 16:16:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 199. [Web]S00-20211117003 流程資料的頁面排序調整以發起時間大到小排序(DESC)
- **Commit ID**: `e85114d1dd0e1ceeb83edeeec271fb4fab41c5da`
- **作者**: 王鵬程
- **日期**: 2022-02-24 14:47:46
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/FormDataSearchListReader.java`

### 200. [Web]Q00-20220223001 修正腳本樣板使用QRCode語法，儲存表單時QRCode消失
- **Commit ID**: `ba8bcd4cce471874e66def2a5f89766aeb94ecdd`
- **作者**: ocean_yeh
- **日期**: 2022-02-23 17:37:37
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/bpm-qrcode.js`

### 201. [BPM APP]C01-*********** 移動授權中間層的使用者維護作業增加分頁功能
- **Commit ID**: `987652de76c48e4cb99cef539c747054fed718ff`
- **作者**: cherryliao
- **日期**: 2022-02-23 09:07:45
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/business-delegate/src/com/dsc/nana/user_interface/business_delegate/AdapterManageDelegate.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManager.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/AdapterManagerBean.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/mobile/access/AdapterMgr.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/AdapterManageAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterManage.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/AdapterUserManage.jsp`

### 202. [Web]Q00-*********** 修正離職交接人作業維護離職人員開窗增加離職人員可以選擇
- **Commit ID**: `d10a2f50b07dd8dbbc60209c7c93307efe8cb890`
- **作者**: 林致帆
- **日期**: 2022-02-21 14:02:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/BPMModule/ModuleForm/Resignation.jsp`

### 203. [流程引擎]Q00-20220221001 修正流程派送到離職人員，離職人員沒有設置離職交接人作業維護就會派送失敗
- **Commit ID**: `caaf1fdc19b424175beae6e299fb887108ce7ad1`
- **作者**: 林致帆
- **日期**: 2022-02-21 11:46:19
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/organization/User.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 204. [Web]A00-20220216002 修正RWD表單當右下角有出現滑到頂部按鈕時，按鈕也會被列印出來
- **Commit ID**: `42b2b04a397b60affde1b9e848a0a826134fb4a7`
- **作者**: 王鵬程
- **日期**: 2022-02-18 17:28:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/TraceProcess/PrintRwdAllFormData.jsp`

### 205. [Web]Q00-20220218002 修正維護樣板查詢欄位DropDown使用『請選擇』時，渲染到畫面上時該選項的順序沒出現在第一個
- **Commit ID**: `88e6b188ca9d361cb6a27271e3256c09e4fb051c`
- **作者**: 王鵬程
- **日期**: 2022-02-18 11:40:13
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/customModule/QueryTemplate.js`

### 206. [Web]A00-20220214001 修正附件權限設定關卡在追蹤流程看不到的問題
- **Commit ID**: `1741f47e9fa7ef6dd378de4f75e081695e989057`
- **作者**: walter_wu
- **日期**: 2022-02-17 22:58:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/AttachmentElement.java`

### 207. [Web]Q00-20220217004 TextBox有設定小數位時，使用FormUtil的寫法無法在formOpen時更換背景色
- **Commit ID**: `9b1b7f196ff340e06af713998584742bfd306e1c`
- **作者**: ocean_yeh
- **日期**: 2022-02-17 15:45:28
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/FormUtil.js`

### 208. [Web]Q00-20220217002 修正流程負責人在監控流程匯出Excel，處理者欄位內容應與系統管理員的處理者欄位內容一致
- **Commit ID**: `dfbcde1e4679da81151c59e8b4d07356e0feeaa0`
- **作者**: 林致帆
- **日期**: 2022-02-17 14:19:00
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/TraceableProcessInstListReader.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FileDownloader.java`

### 209. [Web]Q00-20220217001 修正使用32位元的Chrome進入BPM登入頁面會彈出BPM僅支援的瀏覽器資訊的警告框
- **Commit ID**: `3cbd2a7a794220574874ffc6e512cb9440542559`
- **作者**: 王鵬程
- **日期**: 2022-02-17 14:10:05
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/Login.jsp`

### 210. [BPM APP]C01-20220211002 修正客製開窗在重組SQL指令的邏輯時，在錯誤的地方插入where條件
- **Commit ID**: `d74d8ec4bcb27686ca6c78edfc566850721a2670`
- **作者**: 郭哲榮
- **日期**: 2022-02-17 13:36:24
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/MobileDatabaseAccessor.java`

### 211. [Web]Q00-20220118004修正表單時間元件有預設值不為時間內容時，E10表單回寫給E10會報錯[補修正]
- **Commit ID**: `26f825fb7903c03847467227b8c1e6eb8abef4ae`
- **作者**: 林致帆
- **日期**: 2022-02-16 18:22:42
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/DialogElement.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/HtmlFormBuilder.java`

### 212. [Web]Q00-20220215002 調整讓行動版與PC版一致讓Grid只支援(a、br、input、i、button)五種html標籤
- **Commit ID**: `a62a3972d0aef39c9e6732404c3762d063657cd1`
- **作者**: 王鵬程
- **日期**: 2022-02-15 17:36:01
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`

### 213. [流程引擎]S00-20220210002 更新產品Base所有Mail.jar到與Wildfly15使用的1.6.2一致
- **Commit ID**: `c6c47e678d797896a7f70640090b430080bc2e0d`
- **作者**: walter_wu
- **日期**: 2022-02-15 16:28:22
- **變更檔案數量**: 8
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/lib/mail.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-designer/lib/Mail/mail.jar`
  - 📝 **修改**: `3.Implementation/subproject/bpm-tool-entry/lib/Mail/mail.jar`
  - 📝 **修改**: `3.Implementation/subproject/crm-configure/lib/Mail/mail.jar`
  - 📝 **修改**: `3.Implementation/subproject/process-designer/lib/Mail/mail.jar`
  - 📝 **修改**: `3.Implementation/subproject/service/lib/JavaMail/mail.jar`
  - 📝 **修改**: `3.Implementation/subproject/sys-configure/lib/Mail/mail.jar`
  - 📝 **修改**: `3.Implementation/subproject/system/lib/Mail/mail.jar`

### 214. [流程引擎]Q00-20220215001 修正偶發附件遺失問題
- **Commit ID**: `6014e79609392a3d310ec67d5ac2f0356233368b`
- **作者**: walter_wu
- **日期**: 2022-02-15 16:11:04
- **變更檔案數量**: 3
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/doc_manager/DocManagerLocal.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/WorkflowEngineBean.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/FormHandler.java`

### 215. [BPM APP]S00-20211027004 新增移動端支援ERP流程終止或撤銷時，若單據修改重新送審後於移動表單可查看之前審批流程的功能
- **Commit ID**: `57a696470aca1a44490487484d16a455275c235f`
- **作者**: cherryliao
- **日期**: 2022-02-15 11:35:12
- **變更檔案數量**: 11
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/MobileWorkProcessAction.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/BpmMobileWorkItemAccessor.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTodoServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/tools/mobile/MobileTraceServiceTool.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WEB-INF/WMS/struts-mobileApplication-config.xml`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFormTodoLibV2.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/WMS/Mobile/MobileFromReturnRecordView.jsp`
  - ➕ **新增**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileReturnRecordView.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/Mobile/v2/MobileToDo.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/bpmApp/v3/FrameWorkStyle.css`
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 216. [流程引擎]Q00-20220211001 調整簡易流程圖預先解析，當前核決層級關卡的工作處理者為原處理者的代理人時，以原處理者解析後續關卡
- **Commit ID**: `4af29dfa4782aa2c846766c5ff101e708f76ca2b`
- **作者**: yanann_chen
- **日期**: 2022-02-11 18:14:38
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 217. [ESS]Q00-20220210001 調整BPM取得ESS流程當前存檔狀態的邏輯，若ESS流程狀態是03，則不可再更新此流程在BPM的狀態
- **Commit ID**: `d3a16e4f6893739a149d740eecac5833cdcbb048`
- **作者**: yanann_chen
- **日期**: 2022-02-10 17:51:10
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/sysintegration/appform/AppFormMgr.java`

### 218. [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題[補]
- **Commit ID**: `5dc782652ec5fd69aacc32295a043c61f0fa4e2b`
- **作者**: pinchi_lin
- **日期**: 2022-02-10 14:00:07
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 219. [流程引擎]Q00-20220209001 調整追蹤流程接口邏輯，當追蹤流程url中未包含表單定義ID時，開放給流程處理者與系統管理員查看流程
- **Commit ID**: `1b140e13de353f6369d5f6facf65c76c17adacc2`
- **作者**: yanann_chen
- **日期**: 2022-02-09 18:06:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/TraceProcessAction.java`

### 220. [流程引擎]Q00-20220208003 使用者取回(退回)重辦後，再次執行到有設定自動簽核的核決層級時，除了第一關以外，其餘關卡都會自動跳過
- **Commit ID**: `8081fac947c04007b0a88a2382921307e2ae0cab`
- **作者**: yanann_chen
- **日期**: 2022-02-08 18:03:20
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/workflow_engine/ProcessInstance.java`
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 221. [Web]Q00-20220208002 單身加總欄位設定「顯示至小數點後第X位」且在其他欄位的運算規則中，單身加總數值改變後沒有觸發欄位運算
- **Commit ID**: `9ccb1f4c4b4cc9cc4fe4c25f86d6c647b7b20ec7`
- **作者**: yanann_chen
- **日期**: 2022-02-08 17:06:17
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/BpmTable.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds-grid-aw.js`

### 222. [流程引擎]A00-20220127001修正流程退回重辦選擇"按照流程定義依序重新執行"，關卡會經過"服務任務"會導致主旨的退回重辦標籤沒有顯示
- **Commit ID**: `3eea83a580067309357e69bb2571317dd318285c`
- **作者**: 林致帆
- **日期**: 2022-02-08 16:08:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/ProcessDispatcherBean.java`

### 223. [流程引擎]Q00-20220208001 修正當前進行中的關卡有多個處理者時，流程圖預先解析會判定目前有多個執行中的關卡，不會繼續往下解析流程
- **Commit ID**: `30efd8539ae87a257289dbd7314571245a4e92dc`
- **作者**: yanann_chen
- **日期**: 2022-02-08 14:49:14
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/ajax_service/helper/ProcessAccessor.java`

### 224. [BPM APP]C01-20220207001 修正企業微信的待辦推播若已被處理過導向追蹤已處理的表單畫面時缺少的多語系
- **Commit ID**: `e5f7a95cf9dbe1fd008f055c10383f956bcb88b0`
- **作者**: 郭哲榮
- **日期**: 2022-02-08 13:37:02
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `6.Deployment/DeploymentPlan/copyfiles/@web/NaNa/ResourceBundle/BPMRsrcBundle.xlsx`

### 225. [Web]A00-20220121001修正從工作通知從郵件進入，點擊"回到工作清單"按紐會應該要回到工作通知清單而不是待辦清單
- **Commit ID**: `79b9995548d30cc44b63bd27761a0f70b0783639`
- **作者**: 林致帆
- **日期**: 2022-01-26 10:41:27
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformWorkItemAction.java`

### 226. [ISO]S00-20210507001 PDF浮水印屬性管理新增「圖片浮水印」功能(BCL8)
- **Commit ID**: `f477d505da8d3eca250e46c9c65ef3fbb1420005`
- **作者**: waynechang
- **日期**: 2022-01-24 17:47:23
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/system/src/com/dsc/nana/util/iso/PDF8Converter.java`

### 227. [表單設計師]A00-20220120002 修正欄位樣板的三欄式和四欄式的最右邊欄位切分2欄後，再次編輯會變回切分1欄[補]
- **Commit ID**: `b8873ac57f3746df086f55ba2839f40cf56636d9`
- **作者**: 王鵬程
- **日期**: 2022-01-24 14:20:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 228. [表單設計師]S00-20210915004 新增Grid元件可設定新增、修改、删除按鈕的顏色屬性
- **Commit ID**: `1a9ff5749ee2675c1c691510f8fef2475fa6d083`
- **作者**: cherryliao
- **日期**: 2022-01-24 10:52:30
- **變更檔案數量**: 9
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/domain/src/com/dsc/nana/domain/form/ListElementDefinition.java`
  - 📝 **修改**: `3.Implementation/subproject/form-builder/src/com/dsc/nana/domain/form/builder/html/GridElement.java`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMain.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/WMS/FormDesigner/RwdFormDesignerMainJS.jsp`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/node-model.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-node-factory.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/theme/formDesigner/form-designer.css`

### 229. [內部]Q00-20211115001新增GuardService連線成功的提示訊息
- **Commit ID**: `bcb82cc73eef1047913ad1de318598cd36d78aef`
- **作者**: 林致帆
- **日期**: 2022-01-22 16:18:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/LicenseModuleAction.java`

### 230. [MPT]C01-20220120009 修正首頁連結上bpmserver參數非固定導致首頁模組呼叫BPM接口偶發逾時問題
- **Commit ID**: `0c42ed39879332045a3586d09895c85c51d8d896`
- **作者**: pinchi_lin
- **日期**: 2022-01-22 11:40:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/SecurityLoginAction.java`

### 231. [表單設計師]A00-20220120002 修正欄位樣板的三欄式和四欄式的最右邊欄位切分2欄後，再次編輯會變回切分1欄
- **Commit ID**: `4c68ba9500c8c2ba3fbffadbdb1af47029ee8bb6`
- **作者**: 王鵬程
- **日期**: 2022-01-21 17:58:43
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-dialog.js`

### 232. [BPM APP]C01-20220118001 修正行動端表單多個Checkbox元件且相同元件名稱時簽核後勾選的選項值不會顯示問題
- **Commit ID**: `4fab4a2654835ced12813bb6ad8e6988830cc03e`
- **作者**: yamiyeh10
- **日期**: 2022-01-21 17:42:33
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/beans/mobile/ElementContainerSelect.java`

### 233. [表單設計師]S00-20211117004 調整RWD表單複合元件開窗類型為自定義開窗時參與者型態增加部門/專案
- **Commit ID**: `fa32f20fdc47b5d47e70736a3f57dfb08f0ce7a4`
- **作者**: cherryliao
- **日期**: 2022-01-21 16:48:25
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/util.js`

### 234. [表單設計師]S00-20210811002 調整Web表單設計器日期時間元件比較欄位和TextBox數值欄位運算規則過濾元件本身代號
- **Commit ID**: `4a509e421889f32ae8b90d8b14ab22db3e8cebe9`
- **作者**: cherryliao
- **日期**: 2022-01-21 16:25:40
- **變更檔案數量**: 2
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/dialog.js`
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/formDesigner/rwd-form-builder.js`

### 235. [Web]A00-20220120001修正IE開起流程用SQLcommand因為用replaceall函式導致報錯
- **Commit ID**: `787335e0f03f84c2c315d925ef3ec524adebc108`
- **作者**: 林致帆
- **日期**: 2022-01-21 11:39:58
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/web/js/ds.js`

### 236. [Web]Q00-20220120003 流程代理人設定的選擇流程開窗，預設用流程代號做排序
- **Commit ID**: `3d4b492528f6689d8c6bed291709d5421bd0e280`
- **作者**: 王鵬程
- **日期**: 2022-01-20 18:23:35
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/listreader/ProcessPackageListReader.java`

### 237. [流程引擎]Q00-20220120001 修正「使用者有多個部門，在選擇發起部門後若發起流程失敗，回到表單頁面後無法再發起流程或儲存表單」問題
- **Commit ID**: `607f4206c5382d7ad1317925224ff6bff9570dcc`
- **作者**: yanann_chen
- **日期**: 2022-01-20 16:51:55
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/webapp/src/com/dsc/nana/user_interface/web/actions/PerformRequesterActivityAction.java`

### 238. [流程引擎]A00-20211221001 修正「第一次發起流程因使用者填寫的表單資料有誤導致發起失敗，在使用者更正表單後仍無法發起流程」的問題
- **Commit ID**: `c75f2bd6cff7b9722809caddaad982ee620b18e0`
- **作者**: yanann_chen
- **日期**: 2022-01-20 16:50:48
- **變更檔案數量**: 1
- **檔案變更詳細**:
  - 📝 **修改**: `3.Implementation/subproject/service/src/com/dsc/nana/services/engine/StatefulProcessDispatcherBean.java`

